
ALTER TABLE `promo_activity` ADD COLUMN `EXTERNAL_ACTIVITY_ID` varchar(100) NULL COMMENT '外部系统活动id';

CREATE TABLE `marketing_group_user` (
	`ID` BIGINT ( 20 ) NOT NULL AUTO_INCREMENT COMMENT 'id',
	`TENANT_CODE` VARCHAR ( 32 ) NOT NULL COMMENT '租户编码',
	`DOMAIN_CODE` VARCHAR ( 32 ) NOT NULL COMMENT '域编码',
	`ACTIVITY_CODE` VARCHAR ( 32 ) NOT NULL COMMENT '活动编码',
	`MARKETING_GROUP_CODE` VARCHAR ( 32 ) NOT NULL COMMENT '拼团编码',
	`INVENTORY` int(9) NULL COMMENT '剩余库存',
	`USER_CODE` VARCHAR ( 32 ) NOT NULL COMMENT '用户编码',
	`PRODUCT_CODE` VARCHAR ( 32 ) NULL COMMENT '商品编码',
	`SKU_CODE` VARCHAR ( 32 ) NULL COMMENT 'SKU编码',
	`GROUP_STATUS` CHAR ( 2 ) NOT NULL DEFAULT '01' COMMENT '拼团状态 01进行中，02 拼团结束，03 取消拼团',
	`EFFECTIVE_TIME` VARCHAR ( 14 ) NULL COMMENT '截止有效时间yyyyMMddhhmmss（团长创单时间+时效段）',
	`CANCEL_TIME` VARCHAR ( 14 ) NOT NULL DEFAULT '0' COMMENT '取消订单时间yyyyMMddhhmmss',
	`HOUR` INT ( 9 ) NOT NULL COMMENT '有效小时',
	`TEAM_LEADER` TINYINT ( 1 ) NOT NULL DEFAULT '0' COMMENT '是否团长 1 是， 0 否',
	`CREATE_TIME` TIMESTAMP ( 0 ) NOT NULL DEFAULT CURRENT_TIMESTAMP ( 0 ) COMMENT '创建时间',
	`CREATE_USER` VARCHAR ( 32 ) NULL DEFAULT NULL COMMENT '创建者',
	`UPDATE_TIME` TIMESTAMP ( 0 ) NOT NULL DEFAULT CURRENT_TIMESTAMP ( 0 ) ON UPDATE CURRENT_TIMESTAMP ( 0 ) COMMENT '更新时间',
	`UPDATE_USER` VARCHAR ( 32 ) NULL DEFAULT NULL COMMENT '更新者',
	`LOGIC_DELETE` CHAR ( 1 ) NOT NULL DEFAULT '0' COMMENT '逻辑删除：0-未删除 1-已删除',
	PRIMARY KEY ( `ID` ) USING BTREE,
	UNIQUE INDEX `uniq_tenant_group_code` ( `TENANT_CODE`, `MARKETING_GROUP_CODE`, `USER_CODE`,`CANCEL_TIME`) USING BTREE
) COMMENT = '组团信息表';-- 订单表新增字段 MARKETING_GROUP_CODE


-- 拼团主表
CREATE TABLE `marketing_group_code` (
	`ID` BIGINT ( 20 ) NOT NULL AUTO_INCREMENT COMMENT 'id',
	`TENANT_CODE` VARCHAR ( 32 ) NOT NULL COMMENT '租户编码',
	`DOMAIN_CODE` VARCHAR ( 32 ) NOT NULL COMMENT '域编码',
	`ORG_CODE` VARCHAR ( 32 ) NOT NULL COMMENT '店铺编码',
	`ACTIVITY_CODE` VARCHAR ( 32 ) NOT NULL COMMENT '活动编码',
	`MARKETING_GROUP_CODE` VARCHAR ( 32 ) NOT NULL COMMENT '拼团编码',
	`INVENTORY` int(9) NULL COMMENT '该团剩余可参与人数',
	`GROUP_STATUS` char(2) NOT NULL DEFAULT '00' COMMENT '拼团状态，00 未开始 01进行中，02 拼团成功结束 03拼团失败',
	`CREATE_TIME` TIMESTAMP ( 0 ) NOT NULL DEFAULT CURRENT_TIMESTAMP ( 0 ) COMMENT '创建时间',
	`CREATE_USER` VARCHAR ( 32 ) NULL DEFAULT NULL COMMENT '创建者',
	`UPDATE_TIME` TIMESTAMP ( 0 ) NOT NULL DEFAULT CURRENT_TIMESTAMP ( 0 ) ON UPDATE CURRENT_TIMESTAMP ( 0 ) COMMENT '更新时间',
	`UPDATE_USER` VARCHAR ( 32 ) NULL DEFAULT NULL COMMENT '更新者',
	`LOGIC_DELETE` CHAR ( 1 ) NOT NULL DEFAULT '0' COMMENT '逻辑删除：0-未删除 1-已删除',
	PRIMARY KEY ( `ID` ) USING BTREE,
	UNIQUE INDEX `uniq_tenant_group_code` ( `TENANT_CODE`, `ACTIVITY_CODE`, `MARKETING_GROUP_CODE`) USING BTREE
) COMMENT = '参团注主题表';



ALTER TABLE `flash_sale_order` ADD COLUMN `MARKETING_GROUP_CODE` VARCHAR ( 32 ) NULL COMMENT '拼团编码';

-- 拼团--指定商品选择器
ALTER TABLE `marketing` ADD COLUMN `SELECT_PRODUCT_TYPE` CHAR ( 2 ) NOT NULL DEFAULT '01' COMMENT '指定商品类型 01-指定sku 02指定商品';

-- 分组编码
ALTER TABLE `marketing` ADD COLUMN `GROUP_CODE` VARCHAR ( 32 ) NULLCOMMENT '分组编码';

-- 索引优化 筛选优惠券是否可用
ALTER TABLE `promo_coupon_code_user` ADD INDEX `IDX_TENANT_COUPON_USER_CODE` ( `TENANT_CODE`, `USER_CODE` ) USING BTREE;

-- 团长价是否展示
ALTER TABLE `marketing_group` ADD COLUMN `SHOW_LEADER_PRICE` TINYINT ( 1 ) NOT NULL DEFAULT 0 COMMENT '0 不展示 1展示 default 0';

-- 删除多余参数
ALTER TABLE `marketing_group` DROP COLUMN `ORDER_COUNT`,DROP COLUMN `ALLOW_GROUP_FLAG`,DROP COLUMN `ORDER_COUNT_FLAG`;

-- 秒杀，拼团 预售排除会员
ALTER TABLE `flash_sale_qualification` ADD COLUMN `IS_EXCLUDE` CHAR ( 2 ) NULL COMMENT '02排除指定标签会员，其他情况标签指定会员（只针对会员标签字段）';
-- 抽奖排除会员
ALTER TABLE `lucky_draw_qualification` ADD COLUMN `IS_EXCLUDE` char(2) NULL COMMENT '02排除指定标签会员，其他情况标签指定会员（只针对会员标签字段）';



-- 数字和字母混合
ALTER TABLE `promo_coupon_release` ADD COLUMN `COUPON_RULE_TYPE` varchar(32) NULL COMMENT '券码生成规则：默认 01 数字，02数字字母混合';

-- 指定价格条件
ALTER TABLE `promo_activity` ADD COLUMN `PRICE_CONDITION` varchar(32) NULL DEFAULT 0 COMMENT '价格条件 0-销售价 1-市场价 默认销售价';


-- 营销spu支持维度
ALTER TABLE `flash_sale_order_detail` ADD COLUMN `PRODUCT_CODE` varchar(32) NULL COMMENT '商品编码';


-- 拼团添加封闭团的概念
ALTER TABLE `marketing_group` ADD COLUMN `CLOSE_FLAG` tinyint(1) NULL DEFAULT 0 COMMENT '是否封闭团，1是0不是，默认0';

-- 促销数据库
INSERT INTO `promo_group`(`TENANT_CODE`, `GROUP_CODE`, `GROUP_NAME`, `PRIORITY`, `TYPE`,  `LOGIC_DELETE`, `DOMAIN_CODE`) VALUES ('200005', '108', '单品特价促销', 1149, '01', '0', 'DC0005');


masterdata 数据库
INSERT INTO `t_masterdata_dd`(`DD_CODE`, `DD_VALUE`, `DD_TEXT`, `DD_TYPE`, `DD_ORDER`, `DD_REMARK`, `TENANT_CODE`, `STATE`) VALUES ('TENANT_GROUP_RELATION', '108', '单品特价促销', NULL, 999999, '', 'SYSTEM_DEFAULT', 1);

UPDATE `t_masterdata_dd_lang` SET `DD_TEXT` = '券失效或无对应的券活动.'  WHERE `TENANT_CODE` = 'SYSTEM_DEFAULT' and `DD_CODE` = 'ERROR_CODE_KEY' and  `DD_VALUE` = '10142988' and `LANG` = 'zh-CN';

UPDATE `t_masterdata_dd_lang` SET `DD_TEXT` = '商品 ###{0}### 不存在'  WHERE `TENANT_CODE` = 'SYSTEM_DEFAULT' and `DD_CODE` = 'ERROR_CODE_KEY' and  `DD_VALUE` = '10300201' and `LANG` = 'zh-CN';

UPDATE `t_masterdata_dd_lang` SET `DD_TEXT` = 'sku ###{0}### 不存在'  WHERE `TENANT_CODE` = 'SYSTEM_DEFAULT' and `DD_CODE` = 'ERROR_CODE_KEY' and  `DD_VALUE` = '10300202' and `LANG` = 'zh-CN';

INSERT INTO `t_masterdata_dd_lang`(`DD_CODE`, `DD_VALUE`, `LANG`, `DD_TEXT`, `TENANT_CODE`, `STATE`) VALUES ('ERROR_CODE_KEY', '10300213', 'zh-CN', '{0} 商品下架.', 'SYSTEM_DEFAULT', 1);

-- 分组初始化数据
INSERT INTO `t_masterdata_dd`(`DD_CODE`, `DD_VALUE`, `DD_TEXT`, `DD_TYPE`, `DD_ORDER`, `DD_REMARK`, `TENANT_CODE`, `STATE`, `UPDATE_TIME`, `UPDATE_USER`, `CREATE_TIME`, `CREATE_USER`) VALUES ('TENANT_GROUP_RELATION', '108', '单品特价促销', NULL, 999999, '', 'SYSTEM_DEFAULT', 1, '2023-10-31 10:22:36', NULL, '2023-10-31 10:22:36', NULL);



-- 如果存在执行更新语句
INSERT INTO `t_masterdata_dd`(`DD_CODE`, `DD_VALUE`, `DD_TEXT`, `DD_TYPE`, `DD_ORDER`, `DD_REMARK`, `TENANT_CODE`, `STATE`, `UPDATE_TIME`, `UPDATE_USER`, `CREATE_TIME`, `CREATE_USER`) VALUES ('TENANT_GROUP_RELATION', '301', '抽奖-大转盘', NULL, 999999, '', 'SYSTEM_DEFAULT', 1, '2023-08-17 10:30:56', NULL, '2023-08-17 10:30:56', NULL);
--  如果添加，存在则更新语句
UPDATE `t_masterdata_dd` SET `DD_TEXT` = '抽奖-大转盘'  WHERE `DD_CODE` = 'TENANT_GROUP_RELATION' and `DD_VALUE` = '301';

INSERT INTO `t_masterdata_dd`(`DD_CODE`, `DD_VALUE`, `DD_TEXT`, `DD_TYPE`, `DD_ORDER`, `DD_REMARK`, `TENANT_CODE`, `STATE`, `UPDATE_TIME`, `UPDATE_USER`, `CREATE_TIME`, `CREATE_USER`) VALUES ('TENANT_GROUP_RELATION', '302', '抽奖-砸金蛋', NULL, 999999, '', 'SYSTEM_DEFAULT', 1, '2023-08-17 10:30:56', NULL, '2023-08-17 10:30:56', NULL);

INSERT INTO `t_masterdata_dd`(`DD_CODE`, `DD_VALUE`, `DD_TEXT`, `DD_TYPE`, `DD_ORDER`, `DD_REMARK`, `TENANT_CODE`, `STATE`, `UPDATE_TIME`, `UPDATE_USER`, `CREATE_TIME`, `CREATE_USER`) VALUES ('TENANT_GROUP_RELATION', '303', '抽奖-九宫格', NULL, 999999, '', 'SYSTEM_DEFAULT', 1, '2023-08-17 10:30:56', NULL, '2023-08-17 10:30:56', NULL);

-- 更新禁止叠加数据值
UPDATE `t_masterdata_value` SET `VALUE_VALUE` = '["301","302","303","401","501","601","701"]' WHERE `TENANT_CODE` = 'SYSTEM_DEFAULT' and `VALUE_CODE` 'Disable_Overlay_Mutex_Array';


-- INSERT INTO `t_masterdata_dd_lang`(`DD_CODE`, `DD_VALUE`, `LANG`, `DD_TEXT`, `TENANT_CODE`, `STATE`) VALUES ('ERROR_CODE_KEY', '10142988', 'zh-CN', '券失效或无对应的券活动.', 'SYSTEM_DEFAULT', 1);

-- dev admin 配置
--
-- MENU_MARKTING
--
-- [{ "301": "大转盘", "302": "砸金蛋", "303": "九宫格","401":"打折券","501":"满减满折券","601":"多人拼团","701":"助力"}]
--
-- 配置 MENU_PROMOTION
-- [
--     {
--         "101": "打折促销",
--         "102": "买A优惠B",
--         "103": "订单满减满折",
--         "104": "一口价",
--         "105": "买多优惠",
--         "106": "满赠促销",
--         "107": "运费促销",
--         "108": "单品特价促销"
--     }
-- ]
