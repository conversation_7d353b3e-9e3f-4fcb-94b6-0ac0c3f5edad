<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gtech.promotion.dao.mapper.point.PointTransactionMapper">
  <resultMap id="BaseResultMap" type="com.gtech.promotion.dao.entity.point.PointTransactionEntity">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="domain_code" jdbcType="VARCHAR" property="domainCode" />
    <result column="tenant_code" jdbcType="VARCHAR" property="tenantCode" />
    <result column="transaction_sn" jdbcType="VARCHAR" property="transactionSn" />
    <result column="account_code" jdbcType="VARCHAR" property="accountCode" />
    <result column="account_type" jdbcType="BIT" property="accountType" />
    <result column="campaign_code" jdbcType="VARCHAR" property="campaignCode" />
    <result column="transaction_type" jdbcType="BIT" property="transactionType" />
    <result column="transaction_remarks" jdbcType="VARCHAR" property="transactionRemarks" />
    <result column="transaction_amount" jdbcType="INTEGER" property="transactionAmount" />
    <result column="transaction_date" jdbcType="BIGINT" property="transactionDate" />
    <result column="refer_transaction_sn" jdbcType="VARCHAR" property="referTransactionSn" />
    <result column="refer_order_number" jdbcType="VARCHAR" property="referOrderNumber" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="balance" jdbcType="INTEGER" property="balance" />
  </resultMap>
  <sql id="Base_Column_List">
    id, domain_code, tenant_code, transaction_sn, account_code, account_type, campaign_code, 
    transaction_type, transaction_remarks, transaction_amount, transaction_date, refer_transaction_sn, 
    refer_order_number, create_user, create_time, update_user, update_time , balance
  </sql>
  <sql id="point_transaction_query_condition">
    <trim prefix="WHERE" prefixOverrides="AND |OR ">
      <if test="domainCode != null and domainCode.trim().length() != 0">
        AND (domain_code = #{domainCode})
      </if>
      <if test="tenantCode != null and tenantCode.trim().length() != 0">
        AND (tenant_code = #{tenantCode})
      </if>
      <if test="transactionSn != null and transactionSn.trim().length() != 0">
        AND (transaction_sn = #{transactionSn})
      </if>
      <if test="accountCode != null and accountCode.trim().length() != 0">
        AND (account_code = #{accountCode})
      </if>
      <if test="accountType != null">
        AND (account_type = #{accountType})
      </if>
      <if test="campaignCode != null and campaignCode.trim().length() != 0">
        AND (campaign_code = #{campaignCode})
      </if>
      <if test="transactionType != null">
        AND (transaction_type = #{transactionType})
      </if>
      <if test="transactionRemarks != null and transactionRemarks.trim().length() != 0">
        AND (transaction_remarks = #{transactionRemarks})
      </if>
      <if test="transactionAmount != null">
        AND (transaction_amount = #{transactionAmount})
      </if>
      <if test="transactionDate != null">
        AND (transaction_date = #{transactionDate})
      </if>
      <if test="referTransactionSn != null and referTransactionSn.trim().length() != 0">
        AND (refer_transaction_sn = #{referTransactionSn})
      </if>
      <if test="referOrderNumber != null and referOrderNumber.trim().length() != 0">
        AND (refer_order_number = #{referOrderNumber})
      </if>
      <if test="createUser != null and createUser.trim().length() != 0">
        AND (create_user = #{createUser})
      </if>
      <if test="createTime != null">
        AND (create_time = #{createTime})
      </if>
      <if test="updateUser != null and updateUser.trim().length() != 0">
        AND (update_user = #{updateUser})
      </if>
      <if test="updateTime != null">
        AND (update_time = #{updateTime})
      </if>
    </trim>
  </sql>
  <select id="query" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
     from point_transaction 
    <include refid="point_transaction_query_condition" />
      order by CREATE_TIME desc
  </select>
  
    <select id="countEffectivePoint" resultType="Integer">
   	select sum(transaction_amount)  
   	from point_transaction 
   	where
   		tenant_code = #{tenantCode} 
   		and account_code = #{accountCode} 
   		and account_type = #{accountType} 
   		and transaction_type = 1
      <if test="campaignCode != null">
        AND campaign_code = #{campaignCode}
      </if>
   		and transaction_date &gt;= #{effectiveDate}

  </select>

  <select id="getCountPointTransactionCampaign" resultType="com.gtech.promotion.vo.result.point.PointAccountCampaignResult$CampaignBalance">
    SELECT
      campaign_code AS campaignCode,
      SUM(IF(transaction_type = 1,transaction_amount, 0))
          -
      SUM(IF(transaction_type = 2,transaction_amount, 0)) AS accountBalance
    FROM
      point_transaction
    WHERE tenant_code = #{tenantCode}
      AND account_code = #{accountCode}
      AND campaign_code IN
    <foreach item="item" index="index" collection="campaignCodes" open="(" separator="," close=")">
      #{item}
    </foreach>
    GROUP BY campaign_code
  </select>

  <select id="selectEndTime" resultMap="BaseResultMap">
    SELECT
    <include refid="Base_Column_List" />
    FROM
      point_transaction t
    WHERE
      id > #{maxId}
    and t.expiration &lt; #{nowTime}
    and t.transaction_type = 1
    and t.balance &gt; 0
    GROUP BY t.transaction_sn
    ORDER BY id ASC
      LIMIT #{limit}
  </select>

</mapper>