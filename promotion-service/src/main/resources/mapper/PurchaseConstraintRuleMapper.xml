<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.gtech.promotion.dao.mapper.purchaseconstraint.PurchaseConstraintRuleMapper" >
  <resultMap id="BaseResultMap" type="com.gtech.promotion.dao.entity.purchaseconstraint.PurchaseConstraintRuleEntity" >
    <!--
      WARNING - @mbggenerated
    -->
    <id column="ID" property="id" jdbcType="BIGINT" />
    <result column="TENANT_CODE" property="tenantCode" jdbcType="VARCHAR" />
    <result column="DOMAIN_CODE" property="domainCode" jdbcType="VARCHAR" />
    <result column="ORG_CODE" property="orgCode" jdbcType="VARCHAR" />
    <result column="PURCHASE_CONSTRAINT_CODE" property="purchaseConstraintCode" jdbcType="VARCHAR" />
    <result column="PURCHASE_CONSTRAINT_RULE_TYPE" property="purchaseConstraintRuleType" jdbcType="INTEGER" />
    <result column="PURCHASE_CONSTRAINT_VALUE" property="purchaseConstraintValue" jdbcType="VARCHAR" />
    <result column="PURCHASE_CONSTRAINT_RULE_TIME_TYPE" property="purchaseConstraintRuleTimeType" jdbcType="INTEGER" />
    <result column="PURCHASE_CONSTRAINT_RULE_TIME_VALUE" property="purchaseConstraintRuleTimeValue" jdbcType="VARCHAR" />
    <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP" />
    <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP" />
  </resultMap>
  <sql id="Base_Column_List" >
    <!--
      WARNING - @mbggenerated
    -->
    ID, TENANT_CODE,ORG_CODE, PURCHASE_CONSTRAINT_CODE, PURCHASE_CONSTRAINT_RULE_TYPE, PURCHASE_CONSTRAINT_VALUE,
    PURCHASE_CONSTRAINT_RULE_TIME_TYPE, PURCHASE_CONSTRAINT_RULE_TIME_VALUE, CREATE_TIME,
    UPDATE_TIME,DOMAIN_CODE
  </sql>
</mapper>