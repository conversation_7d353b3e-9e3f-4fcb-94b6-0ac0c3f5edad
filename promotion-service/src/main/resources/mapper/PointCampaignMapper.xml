<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gtech.promotion.dao.mapper.point.PointCampaignMapper">
  <resultMap id="BaseResultMap" type="com.gtech.promotion.dao.entity.point.PointCampaignEntity">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="domain_code" jdbcType="VARCHAR" property="domainCode" />
    <result column="tenant_code" jdbcType="VARCHAR" property="tenantCode" />
    <result column="campaign_code" jdbcType="VARCHAR" property="campaignCode" />
    <result column="program_name" jdbcType="VARCHAR" property="programName" />
    <result column="campaign_title" jdbcType="VARCHAR" property="campaignTitle" />
    <result column="campaign_title_language" jdbcType="CHAR" property="campaignTitleLanguage" />
    <result column="campaign_desc" jdbcType="VARCHAR" property="campaignDesc" />
    <result column="sponsor" jdbcType="VARCHAR" property="sponsor" />
    <result column="begin_date" jdbcType="DATE" property="beginDate" />
    <result column="end_date" jdbcType="DATE" property="endDate" />
    <result column="begin_time" jdbcType="VARCHAR" property="beginTime" />
    <result column="end_time" jdbcType="VARCHAR" property="endTime" />
    <result column="total_points" jdbcType="INTEGER" property="totalPoints" />
    <result column="remaining_points" jdbcType="INTEGER" property="remainingPoints" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, domain_code, tenant_code, campaign_code, program_name, campaign_title, campaign_title_language , campaign_desc, sponsor,
    begin_time, end_time, total_points, remaining_points, status, create_user, create_time,
    update_user, update_time
  </sql>
  <sql id="point_campaign_query_condition">
    <trim prefix="WHERE" prefixOverrides="AND |OR ">
      <if test="domainCode != null and domainCode.trim().length() != 0">
        AND (domain_code = #{domainCode})
      </if>
      <if test="tenantCode != null and tenantCode.trim().length() != 0">
        AND (tenant_code = #{tenantCode})
      </if>
      <if test="campaignCode != null and campaignCode.trim().length() != 0">
        AND (campaign_code = #{campaignCode})
      </if>
      <if test="campaignTitle != null and campaignTitle.trim().length() != 0">
        AND (campaign_title = #{campaignTitle})
      </if>
      <if test="campaignDesc != null and campaignDesc.trim().length() != 0">
        AND (campaign_desc = #{campaignDesc})
      </if>
      <if test="sponsor != null and sponsor.trim().length() != 0">
        AND (sponsor = #{sponsor})
      </if>
      <if test="beginTime != null">
        AND (begin_time = #{beginTime})
      </if>
      <if test="endTime != null">
        AND (end_time = #{endTime})
      </if>
      <if test="totalPoints != null">
        AND (total_points = #{totalPoints})
      </if>
      <if test="remainingPoints != null">
        AND (remaining_points = #{remainingPoints})
      </if>
      <if test="status != null">
        AND (status = #{status})
      </if>
      <if test="status != null and status == 1">
        AND (end_time &gt;= #{nowTime})
      </if>
      <if test="createUser != null and createUser.trim().length() != 0">
        AND (create_user = #{createUser})
      </if>
      <if test="createTime != null">
        AND (create_time = #{createTime})
      </if>
      <if test="updateUser != null and updateUser.trim().length() != 0">
        AND (update_user = #{updateUser})
      </if>
      <if test="updateTime != null">
        AND (update_time = #{updateTime})
      </if>
    </trim>
  </sql>
  <select id="query" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
     from point_campaign 
    <include refid="point_campaign_query_condition" />
      order by CREATE_TIME desc
  </select>
  
    <update id="updatePoint" parameterType="Map">
  	update point_campaign set 
  	remaining_points = remaining_points - #{transactionAmount}
  	where tenant_code = #{tenantCode} and campaign_code = #{campaignCode}
  	and remaining_points &gt;= #{transactionAmount} and status = 1 and end_time &gt;= #{expiration}
  	</update>
</mapper>