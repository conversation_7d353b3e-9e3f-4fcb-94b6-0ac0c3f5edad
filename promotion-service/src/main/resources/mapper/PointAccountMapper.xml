<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gtech.promotion.dao.mapper.point.PointAccountMapper">
  <resultMap id="BaseResultMap" type="com.gtech.promotion.dao.entity.point.PointAccountEntity">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="domain_code" jdbcType="VARCHAR" property="domainCode" />
    <result column="tenant_code" jdbcType="VARCHAR" property="tenantCode" />
    <result column="point_account_code" jdbcType="VARCHAR" property="pointAccountCode" />
    <result column="account_code" jdbcType="VARCHAR" property="accountCode" />
    <result column="campaign_code" jdbcType="VARCHAR" property="campaignCode" />
    <result column="account_type" jdbcType="BIT" property="accountType" />
    <result column="account_desc" jdbcType="VARCHAR" property="accountDesc" />
    <result column="account_balance" jdbcType="INTEGER" property="accountBalance" />
    <result column="status" jdbcType="BIT" property="status" />
    <result column="ext_params" jdbcType="CHAR" property="extParams" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, domain_code, tenant_code, account_code,campaign_code, point_account_code, account_type, account_desc, account_balance,
    status, ext_params, create_user, create_time, update_user, update_time
  </sql>
  <sql id="point_account_query_condition">
    <trim prefix="WHERE" prefixOverrides="AND |OR ">
      <if test="domainCode != null and domainCode.trim().length() != 0">
        AND (domain_code = #{domainCode})
      </if>
      <if test="tenantCode != null and tenantCode.trim().length() != 0">
        AND (tenant_code = #{tenantCode})
      </if>
      <if test="accountCode != null and accountCode.trim().length() != 0">
        AND (account_code = #{accountCode})
      </if>
      <if test="campaignCode != null and campaignCode.trim().length() != 0">
        AND (campaign_code = #{campaignCode})
      </if>
      <if test="pointAccountCode != null and pointAccountCode.trim().length() != 0">
        AND (point_account_code = #{pointAccountCode})
      </if>
      <if test="accountType != null">
        AND (account_type = #{accountType})
      </if>
      <if test="accountDesc != null and accountDesc.trim().length() != 0">
        AND (account_desc = #{accountDesc})
      </if>
      <if test="accountBalance != null">
        AND (account_balance = #{accountBalance})
      </if>
      <if test="status != null">
        AND (status = #{status})
      </if>
      <if test="extParams != null">
        AND (ext_params = #{extParams})
      </if>
      <if test="createUser != null and createUser.trim().length() != 0">
        AND (create_user = #{createUser})
      </if>
      <if test="createTime != null">
        AND (create_time = #{createTime})
      </if>
      <if test="updateUser != null and updateUser.trim().length() != 0">
        AND (update_user = #{updateUser})
      </if>
      <if test="updateTime != null">
        AND (update_time = #{updateTime})
      </if>
    </trim>
  </sql>
  <select id="query" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
     from point_account 
    <include refid="point_account_query_condition" />
      order by CREATE_TIME desc
  </select>
  
  <update id="updatePoint" parameterType="Map">
  	update point_account set 
  	<if test="transactionType == 1">
  		account_balance = account_balance + #{transactionAmount}
  		where tenant_code = #{tenantCode} 
  		and account_code = #{accountCode} 
  		and campaign_code = #{campaignCode}
  		and account_type = #{accountType}
  	</if>
  	<if test="transactionType == 2">
  		account_balance = account_balance - #{transactionAmount}
		where tenant_code = #{tenantCode} 
		and account_code = #{accountCode} 
		and campaign_code = #{campaignCode}
		and account_type = #{accountType}
		and account_balance &gt;= #{transactionAmount}
  	</if>
  	</update>
</mapper>