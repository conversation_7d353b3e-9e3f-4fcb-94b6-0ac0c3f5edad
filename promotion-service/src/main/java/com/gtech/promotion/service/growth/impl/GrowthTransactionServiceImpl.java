package com.gtech.promotion.service.growth.impl;

import java.util.Date;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;

import com.github.pagehelper.PageInfo;
import com.gtech.commons.redis.GTechCodeGenerator;
import com.gtech.commons.result.PageResult;
import com.gtech.commons.utils.BeanCopyUtils;
import com.gtech.promotion.checker.SystemChecker;
import com.gtech.promotion.dao.entity.growth.GrowthTransactionEntity;
import com.gtech.promotion.dao.mapper.growth.GrowthTransactionMapper;
import com.gtech.promotion.exception.PromotionException;
import com.gtech.promotion.service.BaseService;
import com.gtech.promotion.service.growth.GrowthTransactionService;
import com.gtech.promotion.utils.DateUtil;
import com.gtech.promotion.vo.param.growth.GrowthTransactionParam;
import com.gtech.promotion.vo.param.growth.query.GetGrowthTransactionParam;
import com.gtech.promotion.vo.result.growth.GrowthTransactionResult;

import lombok.extern.slf4j.Slf4j;
import tk.mybatis.mapper.util.StringUtil;
@Slf4j
@Service
public class GrowthTransactionServiceImpl extends BaseService implements GrowthTransactionService {

	private static final String SYS_CODE = "GrowthTransactionServiceImpl";

	private static final String TRANSACTION_SN = "transactionSn";


	@Autowired
	GrowthTransactionMapper growthTransactionMapper;

	@Autowired
	GTechCodeGenerator codeGenerator;

	@Override
	public String saveGrowthTransaction(GrowthTransactionParam growthTransactionVo) {

		// 1.检测
		String msg = checkGrowthTransaction(growthTransactionVo, false);

		if (StringUtils.isNotBlank(msg)) {
			throw new PromotionException(SYS_CODE + ".saveGrowthTransaction.checkGrowthTransaction", msg);
		}
		GrowthTransactionEntity growthTransaction = BeanCopyUtils.jsonCopyBean(growthTransactionVo, GrowthTransactionEntity.class);
		// 2.默认值
		setGrowthTransactionDefault(growthTransaction);
		// 3.保存
		saveGrowthTransactionModel(growthTransaction);

		return growthTransaction.getTransactionSn();
	}

	@Override
	public void updateGrowthTransaction(GrowthTransactionParam growthTransactionVo) {

		// 1.检测
		String msg = checkGrowthTransaction(growthTransactionVo, true);

		if (StringUtils.isNotBlank(msg)) {
			throw new PromotionException(SYS_CODE + ".updateGrowthTransaction.checkGrowthTransaction", msg);
		}
		// 2.获取MODEL
		GrowthTransactionEntity oldGrowthTransaction = getGrowthTransactionModelByCode(growthTransactionVo.getTenantCode(), growthTransactionVo.getTransactionSn());// NOSONAR
		if (null == oldGrowthTransaction) {
			throw new PromotionException(SystemChecker.NULL_VO.getCode(), SystemChecker.NULL_VO.getMessage());
		}
		try {
			BeanUtils.copyProperties(growthTransactionVo, oldGrowthTransaction);
		} catch (Exception e) {//NOSONAR
			throw new PromotionException(SYS_CODE + ".updateGrowthTransaction.copy", "copy exception");//NOSONAR
		}//NOSONAR
		// 3.默认值
		setGrowthTransactionUpdataDefault(oldGrowthTransaction);
		// 4.保存
		updateGrowthTransactionModel(oldGrowthTransaction);
	}

	@Override
	public PageResult<GrowthTransactionResult> queryGrowthTransactionPage(Map<String, Object> map) {
		// 分页查询
		setPage(map);
		map.put("order", true);
		List<GrowthTransactionEntity> growthTransactionList = queryGrowthTransactionModelPage(map);
		PageInfo<GrowthTransactionEntity> pageInfo = new PageInfo<>(growthTransactionList);
		List<GrowthTransactionResult> growthTransactionVoList = BeanCopyUtils.jsonCopyList(growthTransactionList, GrowthTransactionResult.class);
		return new PageResult<>(growthTransactionVoList, pageInfo.getTotal());
	}

	/**
	 * 获取成长值明细信息
	 * 
	 * @return GrowthTransaction
	 */
	private GrowthTransactionEntity getGrowthTransactionModelByCode(String tenantCode, String transactionSn) {
		try {
			GrowthTransactionEntity record = new GrowthTransactionEntity();
			record.setTenantCode(tenantCode);
			record.setTransactionSn(transactionSn);
			return growthTransactionMapper.selectOne(record);
		} catch (Exception e) {
			log.error(SYS_CODE + ".getGrowthTransactionModelByCode", e);
		}
		return null;
	}

	/**
	 * 检测成长值明细参数
	 * 
	 * @param growthTransactionVo
	 * @return
	 */
	private String checkGrowthTransaction(GrowthTransactionParam growthTransactionVo, boolean flag) {

		String msg = "";
		if (null == growthTransactionVo) {
			return "parameter is null";
		}
		if (flag && StringUtil.isEmpty(growthTransactionVo.getTransactionSn())) {
			msg += "【growthTransactionCode】";
		}
		if (StringUtil.isNotEmpty(msg)) {
			msg += " can not be empty .";
		}
		return msg;
	}

	/**
	 * 设置成长值明细新增默认值
	 * 
	 * @param growthTransaction
	 */
	private void setGrowthTransactionDefault(GrowthTransactionEntity growthTransaction) {

		if (null == growthTransaction)
			return;
		if (StringUtils.isBlank(growthTransaction.getTransactionSn())) {
			growthTransaction.setTransactionSn(codeGenerator.generateCode(growthTransaction.getTenantCode(), TRANSACTION_SN, "GT[D:yyyyMMddHHmmss][SM:%06d]", 1l));
		}
		growthTransaction.setTransactionDate(Long.parseLong(DateUtil.getDateString(new Date(), DateUtil.DATESTOREFORMAT)));
	}

	/**
	 * 设置成长值明细修改默认值
	 * 
	 * @param growthTransaction
	 */
	private void setGrowthTransactionUpdataDefault(GrowthTransactionEntity growthTransaction) {
		setGrowthTransactionDefault(growthTransaction);
		if (null == growthTransaction)//NOSONAR
			return;//NOSONAR
		growthTransaction.setUpdateTime(new Date());
	}

	/**
	 * 保存成长值明细对象
	 * 
	 * @param growthTransaction
	 * @throws PimException
	 */
	private void saveGrowthTransactionModel(GrowthTransactionEntity growthTransaction) {
		if (null == growthTransaction)//NOSONAR
			return;//NOSONAR
		try {
			growthTransactionMapper.insert(growthTransaction);
		} catch (DuplicateKeyException e) {
			throw new PromotionException(SystemChecker.DUPLICATE_KEY.getCode(), SystemChecker.DUPLICATE_KEY.getMessage());
		} catch (Exception e) {
			throw new PromotionException(SYS_CODE + ".saveGrowthTransactionModel.ex", e.getMessage());
		}

	}

	/**
	 * 更新成长值明细对象
	 * 
	 * @param growthTransaction
	 * @throws
	 */
	private void updateGrowthTransactionModel(GrowthTransactionEntity growthTransaction) {
		if (null == growthTransaction) {//NOSONAR
			return;//NOSONAR
		}//NOSONAR
		try {
			growthTransactionMapper.updateByPrimaryKeySelective(growthTransaction);
		} catch (DuplicateKeyException e) {//NOSONAR
			throw new PromotionException(SystemChecker.DUPLICATE_KEY.getCode(), SystemChecker.DUPLICATE_KEY.getMessage());//NOSONAR
		} catch (Exception e) {//NOSONAR
			throw new PromotionException(SYS_CODE + ".updateGrowthTransactionModel.ex", e.getMessage());//NOSONAR
		}//NOSONAR
	}

	/**
	 * 分页查询成长值明细
	 * 
	 * @param parammap
	 * @return GrowthTransaction
	 */
	private List<GrowthTransactionEntity> queryGrowthTransactionModelPage(Map<String, Object> parammap) {
		List<GrowthTransactionEntity> list = null;
		try {
			list = growthTransactionMapper.query(parammap);
            for (GrowthTransactionEntity entity : list) {
                GrowthTransactionEntity growthTransactionEntity = growthTransactionMapper.selectOne(entity);
                if (null!=growthTransactionEntity.getOrigin()){//NOSONAR
                    entity.setOrigin(growthTransactionEntity.getOrigin());//NOSONAR
                }//NOSONAR
            }
		} catch (Exception e) {//NOSONAR
			log.error(SYS_CODE + ".queryGrowthTransactionModel", e);//NOSONAR
		}//NOSONAR
		return list;
	}

	@Override
	public GrowthTransactionResult getGrowthTransaction(GetGrowthTransactionParam param) {
		GrowthTransactionEntity growthTransaction = getGrowthTransactionModelByCode(param.getTenantCode(), param.getTransactionSn());
		if (growthTransaction == null) {
			return null;
		}
		return BeanCopyUtils.jsonCopyBean(growthTransaction, GrowthTransactionResult.class);
	}
}
