package com.gtech.promotion.service.flashsale.impl;

import com.gtech.commons.utils.BeanCopyUtils;
import com.gtech.promotion.dao.entity.marketing.BaseEntity;
import com.gtech.promotion.dao.entity.marketing.flashsale.FlashSaleQualificationEntity;
import com.gtech.promotion.dao.mapper.marketing.flashsale.FlashSaleQualificationMapper;
import com.gtech.promotion.dao.model.marketing.flashsale.FlashSaleQualificationModel;
import com.gtech.promotion.service.flashsale.FlashSaleQualificationService;
import com.gtech.promotion.service.marketing.impl.BaseServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import java.util.List;

@Service
public class FlashSaleQualificationServiceImpl extends BaseServiceImpl<FlashSaleQualificationEntity, FlashSaleQualificationModel> implements FlashSaleQualificationService {

    public FlashSaleQualificationServiceImpl() {
        super(FlashSaleQualificationEntity.class, FlashSaleQualificationModel.class);
    }

    @Autowired
    private FlashSaleQualificationMapper qualificationMapper;

    @Override
    public List<FlashSaleQualificationModel> getQualificationsByActivityCodes(String tenantCode, List<String> activityCodes) {
        Example example = new Example(FlashSaleQualificationEntity.class);
        example.createCriteria().andEqualTo(BaseEntity.C_TENANT_CODE, tenantCode)
                .andIn(BaseEntity.C_ACTIVITY_CODE, activityCodes);
        return BeanCopyUtils.jsonCopyList(qualificationMapper.selectByCondition(example), FlashSaleQualificationModel.class);
    }
}
