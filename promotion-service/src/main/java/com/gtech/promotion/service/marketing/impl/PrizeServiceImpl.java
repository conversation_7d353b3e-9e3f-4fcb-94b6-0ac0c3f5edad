package com.gtech.promotion.service.marketing.impl;

import com.gtech.promotion.dao.entity.marketing.PrizeEntity;
import com.gtech.promotion.dao.mapper.marketing.PrizeMapper;
import com.gtech.promotion.dao.model.marketing.PrizeModel;
import com.gtech.promotion.service.marketing.PrizeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class PrizeServiceImpl extends BaseServiceImpl<PrizeEntity, PrizeModel> implements PrizeService {

    public PrizeServiceImpl() {
        super(PrizeEntity.class, PrizeModel.class);
    }

    @Autowired
    private PrizeMapper prizeMapper;

    @Override
    public int deductInventory(PrizeModel prizeModel) {
        return prizeMapper.deductInventory(prizeModel);
    }
}
