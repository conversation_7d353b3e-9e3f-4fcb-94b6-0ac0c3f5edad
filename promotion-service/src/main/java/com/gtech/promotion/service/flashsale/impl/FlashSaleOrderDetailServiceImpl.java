package com.gtech.promotion.service.flashsale.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.gtech.commons.utils.BeanCopyUtils;
import com.gtech.promotion.dao.entity.marketing.BaseEntity;
import com.gtech.promotion.dao.entity.marketing.flashsale.FlashSaleOrderDetailEntity;
import com.gtech.promotion.dao.entity.marketing.flashsale.FlashSaleOrderEntity;
import com.gtech.promotion.dao.mapper.marketing.flashsale.FlashSaleOrderDetailMapper;
import com.gtech.promotion.dao.model.marketing.flashsale.FlashSaleOrderDetailModel;
import com.gtech.promotion.service.flashsale.FlashSaleOrderDetailService;
import com.gtech.promotion.service.marketing.impl.BaseServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import java.util.Collections;
import java.util.List;

@Service
public class FlashSaleOrderDetailServiceImpl extends BaseServiceImpl<FlashSaleOrderDetailEntity, FlashSaleOrderDetailModel> implements FlashSaleOrderDetailService {

    public FlashSaleOrderDetailServiceImpl() {
        super(FlashSaleOrderDetailEntity.class, FlashSaleOrderDetailModel.class);
    }

    @Autowired
    private FlashSaleOrderDetailMapper orderDetailMapper;

    @Override
    public List<FlashSaleOrderDetailModel> findByOrderNo(String tenantCode, String orderNo) {
        FlashSaleOrderDetailEntity entity = new FlashSaleOrderDetailEntity();
        entity.setOrderId(orderNo);
        entity.setTenantCode(tenantCode);
        return BeanCopyUtils.jsonCopyList(orderDetailMapper.select(entity), FlashSaleOrderDetailModel.class);
    }

    @Override
    public List<FlashSaleOrderDetailModel> findByOrderNoList(String tenantCode, List<String> orderNo) {

        if (CollectionUtil.isEmpty(orderNo)){
            return Collections.emptyList();
        }
        Example example = new Example(FlashSaleOrderDetailEntity.class);
        example.orderBy("orderId").asc();
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo(BaseEntity.C_TENANT_CODE, tenantCode)
                .andIn(FlashSaleOrderEntity.ORDER_ID,orderNo);

        return BeanCopyUtils.jsonCopyList(orderDetailMapper.selectByCondition(example), FlashSaleOrderDetailModel.class);
    }

}
