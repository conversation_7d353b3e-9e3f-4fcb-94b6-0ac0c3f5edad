/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.service.activity;

import com.gtech.promotion.dao.entity.activity.TPromoActivityStatisticEntity;
import com.gtech.promotion.dto.in.activity.QueryActivityStatisticInDTO;
import com.gtech.promotion.dto.in.activity.QueryActivityStatisticSumInDTO;
import com.gtech.promotion.dto.in.activity.TPromoActivityStatisticInDTO;
import com.gtech.promotion.dto.out.activity.ActivityStatisticSumQueryOutDTO;

import java.util.List;

public interface TPromoActivityStatisticService {


    /**
     * 查询活动统计记录
     * @param paramDTO
     * @return
     */
    List<TPromoActivityStatisticEntity> queryActivityStatistic(QueryActivityStatisticInDTO paramDTO);

    /**
     * 批量创建活动统计
     * @param batchList
     */
    void createActivityStatisticBatch(List<TPromoActivityStatisticInDTO> batchList);

    /**
     * 统计促销活动数据
     * @param paramDTO
     * @return
     */
    ActivityStatisticSumQueryOutDTO queryActivityStatisticSum(QueryActivityStatisticSumInDTO paramDTO);
}
