package com.gtech.promotion.service.growth;

import java.util.Map;

import com.gtech.commons.result.PageResult;
import com.gtech.promotion.vo.param.growth.GrowthTransactionParam;
import com.gtech.promotion.vo.param.growth.query.GetGrowthTransactionParam;
import com.gtech.promotion.vo.result.growth.GrowthTransactionResult;

public interface GrowthTransactionService {

	/**
	 * 新增成长值流水
	 * 
	 * @param growthTransactionVo
	 * @return
	 */
	String saveGrowthTransaction(GrowthTransactionParam growthTransactionVo);

	/**
	 * 修改成长值流水
	 * 
	 * @param growthTransactionVo
	 */
	void updateGrowthTransaction(GrowthTransactionParam growthTransactionVo);

	/**
	 * 根据Code查询成长值流水
	 */
	GrowthTransactionResult getGrowthTransaction(GetGrowthTransactionParam param);

	/**
	 * 分页查询成长值流水
	 * 
	 * @param map
	 * @return queryResult<GrowthTransactionResult>
	 */
	PageResult<GrowthTransactionResult> queryGrowthTransactionPage(Map<String, Object> map);

}
