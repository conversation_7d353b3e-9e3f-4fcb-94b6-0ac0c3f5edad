package com.gtech.promotion.service.marketing;

import com.gtech.promotion.dao.entity.marketing.HelpRecordEntity;
import com.gtech.promotion.dao.model.marketing.HelpRecordModel;

import java.util.List;

public interface HelpRecordService extends BaseService<HelpRecordEntity, HelpRecordModel>{


    List<HelpRecordEntity> checkMemberHelpRecord(String sharingRecordCode, String memberCode);


    List<HelpRecordEntity> queryHelpRecord(HelpRecordModel helpRecordModel, List<String> sharingRecordCodeList);
}
