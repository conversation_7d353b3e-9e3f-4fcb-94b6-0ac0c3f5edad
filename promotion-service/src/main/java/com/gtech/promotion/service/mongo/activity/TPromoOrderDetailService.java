/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.service.mongo.activity;


import com.gtech.promotion.vo.mongo.TPromoOrderDetailVO;

import java.util.List;

/**
 * 促销订单明细
 * 
 */
public interface TPromoOrderDetailService {

    /**
     * 插入数据
     * 
     * @param orderDetailVOs 订单详情数据
     */
    int insertOrderDetailList(List<TPromoOrderDetailVO> orderDetailVOs);

    List<TPromoOrderDetailVO> queryOrderDetail(String promoOrderId);

}
