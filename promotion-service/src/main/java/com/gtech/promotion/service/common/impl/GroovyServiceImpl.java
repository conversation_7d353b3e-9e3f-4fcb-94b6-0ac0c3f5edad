package com.gtech.promotion.service.common.impl;

import com.gtech.commons.utils.BeanCopyUtils;
import com.gtech.promotion.dao.entity.activity.ActivityScriptEntity;
import com.gtech.promotion.dao.mapper.activity.ActivityScriptMapper;
import com.gtech.promotion.dao.model.activity.ActivityScriptModel;
import com.gtech.promotion.pojo.CacheGroovyScript;
import com.gtech.promotion.service.common.GroovyService;
import com.gtech.promotion.utils.EasyCacheUtil;
import groovy.lang.Binding;
import groovy.lang.GroovyShell;
import groovy.lang.Script;
import org.codehaus.groovy.control.CompilerConfiguration;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

@Service
public class GroovyServiceImpl implements GroovyService {

    private static final GroovyShell GROOVY_SHELL;

    private static final Map<String, CacheGroovyScript> SCRIPT_MAP = new ConcurrentHashMap<>();

    @Autowired
    private ActivityScriptMapper activityScriptMapper;

    static {
        CompilerConfiguration cfg = new CompilerConfiguration();
        GROOVY_SHELL = new GroovyShell(cfg);
    }

    @Override
    public boolean run(ActivityScriptModel activityScriptModel, Map<String, Object> paramMap) {
        if (null == activityScriptModel) {
            return true;
        }
        String key = activityScriptModel.getTenantCode() + ":" + activityScriptModel.getScriptCode();
        CacheGroovyScript cacheGroovyScript = SCRIPT_MAP.get(key);
        Script script;
        if (null == cacheGroovyScript || !cacheGroovyScript.getVersion().equals(activityScriptModel.getVersion())) {
            script = GROOVY_SHELL.parse(activityScriptModel.getScript());
            CacheGroovyScript groovyScript = new CacheGroovyScript();
            groovyScript.setVersion(activityScriptModel.getVersion());
            groovyScript.setScript(script);
            SCRIPT_MAP.put(key, groovyScript);
        } else {
            script = cacheGroovyScript.getScript();
        }
        Binding groovyBinding = new Binding(paramMap);
        script.setBinding(groovyBinding);
        return (Boolean) script.run();
    }

    @Override
    public ActivityScriptModel findByCode(String tenantCode, String scriptCode){
        String scriptKey  = tenantCode+":"+scriptCode;
        ActivityScriptEntity activityScriptEntity;
        activityScriptEntity = (ActivityScriptEntity) EasyCacheUtil.get(scriptKey);
        if (null != activityScriptEntity){
            return BeanCopyUtils.jsonCopyBean(activityScriptEntity, ActivityScriptModel.class);
        }
        activityScriptEntity = ActivityScriptEntity.builder().tenantCode(tenantCode).scriptCode(scriptCode).build();
        return BeanCopyUtils.jsonCopyBean(activityScriptMapper.selectOne(activityScriptEntity), ActivityScriptModel.class);
    }
}
