package com.gtech.promotion.service.flashsale;

import com.gtech.promotion.dao.entity.marketing.flashsale.FlashSaleStoreEntity;
import com.gtech.promotion.dao.model.marketing.flashsale.FlashSaleStoreModel;
import com.gtech.promotion.service.marketing.BaseService;

import java.util.List;

public interface FlashSaleStoreService extends BaseService<FlashSaleStoreEntity, FlashSaleStoreModel> {
    List<FlashSaleStoreModel> getStoresByActivityCodes(String tenantCode, List<String> activityCodes);
}
