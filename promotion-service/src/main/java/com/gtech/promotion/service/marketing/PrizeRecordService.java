package com.gtech.promotion.service.marketing;

import com.gtech.promotion.dao.entity.marketing.PrizeRecordEntity;
import com.gtech.promotion.dao.model.marketing.PrizeRecordModel;
import com.gtech.promotion.dao.model.marketing.QueryRecordByTicketsModel;

import java.util.List;

public interface PrizeRecordService extends BaseService<PrizeRecordEntity, PrizeRecordModel> {
    List<PrizeRecordModel> queryLuckyRecordList(QueryRecordByTicketsModel queryRecordByTicketsModel);
}
