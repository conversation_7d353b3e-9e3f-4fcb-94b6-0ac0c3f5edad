/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.service.impl.activity;

import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.gtech.commons.utils.BeanCopyUtils;
import com.gtech.commons.utils.DateUtil;
import com.gtech.promotion.dao.entity.activity.TPromoActivityStatisticEntity;
import com.gtech.promotion.dao.mapper.activity.TPromoActivityStatisticMapper;
import com.gtech.promotion.dto.in.activity.QueryActivityStatisticInDTO;
import com.gtech.promotion.dto.in.activity.QueryActivityStatisticSumInDTO;
import com.gtech.promotion.dto.in.activity.TPromoActivityStatisticInDTO;
import com.gtech.promotion.dto.out.activity.ActivityStatisticSumQueryOutDTO;
import com.gtech.promotion.service.activity.TPromoActivityStatisticService;

import tk.mybatis.mapper.entity.Example;

@Service
public class TPromoActivityStatisticServiceImpl implements TPromoActivityStatisticService {

    @Autowired
    private TPromoActivityStatisticMapper statisticMapper;


    @Override
    public List<TPromoActivityStatisticEntity> queryActivityStatistic(QueryActivityStatisticInDTO paramDTO) {
        Example example = new Example(TPromoActivityStatisticEntity.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("tenantCode", paramDTO.getTenantCode())
                .andEqualTo("activityCode", paramDTO.getActivityCode());

        if(StringUtils.isNotBlank(paramDTO.getStartTime()) && StringUtils.isNotBlank(paramDTO.getEndTime())){
            criteria.andBetween("createTime",  DateUtil.addDay(DateUtil.parseDate(paramDTO.getStartTime(), DateUtil.FORMAT_YYYYMMDD),1) ,DateUtil.addDay(DateUtil.parseDate(paramDTO.getEndTime(), DateUtil.FORMAT_YYYYMMDD),2));
        }

        return statisticMapper.selectByCondition(example);
    }

    @Override
    @Transactional
    public void createActivityStatisticBatch(List<TPromoActivityStatisticInDTO> batchList) {

        List<TPromoActivityStatisticEntity> convert = BeanCopyUtils.jsonCopyList(batchList, TPromoActivityStatisticEntity.class);

        for (TPromoActivityStatisticEntity statisticEntity : convert) {
            statisticMapper.insertSelective(statisticEntity);
        }
    }

    @Override
    public ActivityStatisticSumQueryOutDTO queryActivityStatisticSum(QueryActivityStatisticSumInDTO paramDTO) {

        return statisticMapper.queryActivityStatisticSum(paramDTO);
    }
}
