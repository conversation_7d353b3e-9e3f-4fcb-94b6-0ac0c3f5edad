package com.gtech.promotion.service.marketing.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.gtech.commons.utils.BeanCopyUtils;
import com.gtech.commons.utils.DateUtil;
import com.gtech.commons.utils.StringUtil;
import com.gtech.promotion.code.marketing.TeamLeaderEnum;
import com.gtech.promotion.code.marketing.UserGroupStatusEnum;
import com.gtech.promotion.dao.entity.marketing.BaseEntity;
import com.gtech.promotion.dao.entity.marketing.MarketingGroupUserEntity;
import com.gtech.promotion.dao.mapper.marketing.MarketingGroupUserMapper;
import com.gtech.promotion.dao.model.marketing.GroupUserCountDto;
import com.gtech.promotion.dao.model.marketing.MarketingGroupUserMode;
import com.gtech.promotion.service.marketing.MarketingGroupUserService;
import com.gtech.promotion.vo.param.marketing.MarketingGroupUserListParam;
import com.gtech.promotion.vo.param.marketing.MarketingGroupUserParam;
import com.gtech.promotion.vo.param.marketing.flashsale.FindGroupUserParam;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Description :
 * <AUTHOR> gaojie.li
 * @Date : 2023/8/7 14:04
 */
@Service
public class MarketingGroupUserServiceImpl extends BaseServiceImpl<MarketingGroupUserEntity, MarketingGroupUserMode> implements MarketingGroupUserService {

    @Autowired
    private MarketingGroupUserMapper marketingGroupUserMapper;

    public MarketingGroupUserServiceImpl() {
        super(MarketingGroupUserEntity.class, MarketingGroupUserMode.class);
    }


    @Override
    public MarketingGroupUserMode findGroupUserCode(FindGroupUserParam param) {

        Example example = new Example(MarketingGroupUserEntity.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo(BaseEntity.C_TENANT_CODE, param.getTenantCode())
                .andEqualTo(MarketingGroupUserEntity.C_MARKETING_GROUP_CODE, param.getMarketingGroupCode())
                .andEqualTo(MarketingGroupUserEntity.C_USER_CODE, param.getUserCode())
                .andIn(MarketingGroupUserEntity.C_GROUP_STATUS, Arrays.asList(UserGroupStatusEnum.PROCESSING.code(), UserGroupStatusEnum.PAID.code(), UserGroupStatusEnum.FINISH.code()));

        if (StringUtil.isNotEmpty(param.getSkuCode())){
            criteria.andEqualTo(MarketingGroupUserEntity.C_SKU_CODE, param.getSkuCode());
        }

        if (StringUtil.isNotEmpty(param.getProductCode())){
            criteria.andEqualTo(MarketingGroupUserEntity.C_PRODUCT_CODE, param.getProductCode());
        }

        List<MarketingGroupUserEntity> marketingGroupUserEntities = marketingGroupUserMapper.selectByCondition(example);
        if (CollectionUtils.isEmpty(marketingGroupUserEntities)){
            return null;
        }
        return BeanCopyUtils.jsonCopyBean(marketingGroupUserEntities.get(0), MarketingGroupUserMode.class);

    }

    @Override
    public MarketingGroupUserMode findGroupLeaderUserByMarketingGroupCode(String tenantCode, String marketingGroupCode) {

        String currentDateAsString = DateUtil.getCurrentDateAsString(DateUtil.FORMAT_YYYYMMDDHHMISS_14);
        Example example = new Example(MarketingGroupUserEntity.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo(BaseEntity.C_TENANT_CODE, tenantCode)
                .andEqualTo(MarketingGroupUserEntity.C_MARKETING_GROUP_CODE, marketingGroupCode)
                .andEqualTo(MarketingGroupUserEntity.C_TEAM_LEADER, TeamLeaderEnum.LEADER.code())
                .andGreaterThanOrEqualTo(MarketingGroupUserEntity.C_EFFECTIVE_TIME,currentDateAsString);

        List<MarketingGroupUserEntity> marketingGroupUserEntities = marketingGroupUserMapper.selectByCondition(example);
        if (CollectionUtils.isEmpty(marketingGroupUserEntities)){
            return null;
        }
        return BeanCopyUtils.jsonCopyBean(marketingGroupUserEntities.get(0), MarketingGroupUserMode.class);
    }

    @Override
    public MarketingGroupUserMode confirmGroupLeaderByMarketingGroupCode(String tenantCode, String marketingGroupCode) {

        Example example = new Example(MarketingGroupUserEntity.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo(BaseEntity.C_TENANT_CODE, tenantCode)
                .andEqualTo(MarketingGroupUserEntity.C_MARKETING_GROUP_CODE, marketingGroupCode)
                .andEqualTo(MarketingGroupUserEntity.C_TEAM_LEADER, TeamLeaderEnum.LEADER.code());

        List<MarketingGroupUserEntity> marketingGroupUserEntities = marketingGroupUserMapper.selectByCondition(example);
        if (CollectionUtils.isEmpty(marketingGroupUserEntities)){
            return null;
        }
        return BeanCopyUtils.jsonCopyBean(marketingGroupUserEntities.get(0), MarketingGroupUserMode.class);
    }

    @Override
    public int queryGroupUserListByMarketingGroupCode(String tenantCode, String activityCode,String marketingGroupCode) {

        Example example = new Example(MarketingGroupUserEntity.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo(BaseEntity.C_TENANT_CODE, tenantCode)
                .andEqualTo(MarketingGroupUserEntity.C_ACTIVITY_CODE, activityCode)
                .andEqualTo(MarketingGroupUserEntity.C_MARKETING_GROUP_CODE, marketingGroupCode)
                .andIn(MarketingGroupUserEntity.C_GROUP_STATUS,
                        Arrays.asList(UserGroupStatusEnum.PROCESSING.code(), UserGroupStatusEnum.PAID.code()))
        ;

        return marketingGroupUserMapper.selectCountByCondition(example);
    }

    @Override
    public int queryGroupMemberNoPay(String tenantCode, String activityCode, String marketingGroupCode) {

        Example example = new Example(MarketingGroupUserEntity.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo(BaseEntity.C_TENANT_CODE, tenantCode)
                .andEqualTo(MarketingGroupUserEntity.C_ACTIVITY_CODE, activityCode)
                .andEqualTo(MarketingGroupUserEntity.C_MARKETING_GROUP_CODE, marketingGroupCode)
                .andEqualTo(MarketingGroupUserEntity.C_GROUP_STATUS, UserGroupStatusEnum.PROCESSING.code());
        return marketingGroupUserMapper.selectCountByCondition(example);
    }

    @Override
    public List<MarketingGroupUserMode> queryGroupUserListByUserCode(String tenantCode, String activityCode, String userCode) {

        Example example = new Example(MarketingGroupUserEntity.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo(BaseEntity.C_TENANT_CODE, tenantCode)
                .andEqualTo(MarketingGroupUserEntity.C_ACTIVITY_CODE, activityCode)
                .andEqualTo(MarketingGroupUserEntity.C_USER_CODE, userCode);

        return BeanCopyUtils.jsonCopyList(marketingGroupUserMapper.selectByCondition(example), MarketingGroupUserMode.class);
    }

    @Override
    public List<MarketingGroupUserMode> queryPayGroupUserListByActivityCode(MarketingGroupUserParam param) {

        Example example = new Example(MarketingGroupUserEntity.class);

        Example.Criteria criteria = example.createCriteria();

        criteria.andEqualTo(BaseEntity.C_TENANT_CODE, param.getTenantCode())
                .andEqualTo(MarketingGroupUserEntity.C_ACTIVITY_CODE, param.getActivityCode())
                .andEqualTo(MarketingGroupUserEntity.C_GROUP_STATUS, UserGroupStatusEnum.PAID.code())
                .andEqualTo(MarketingGroupUserEntity.C_TEAM_LEADER,TeamLeaderEnum.LEADER.code());

        return getMarketingGroupUserModes(param, example, criteria);
    }

    public List<MarketingGroupUserMode> getMarketingGroupUserModes(MarketingGroupUserParam param, Example example, Example.Criteria criteria) {
        if (StringUtil.isNotEmpty(param.getUserCode())) {
            criteria.andEqualTo(MarketingGroupUserEntity.C_USER_CODE, param.getUserCode());
        }

        if (StringUtil.isNotEmpty(param.getSkuCode())) {
            criteria.andEqualTo(MarketingGroupUserEntity.C_SKU_CODE, param.getSkuCode());
        }

        if (StringUtil.isNotEmpty(param.getProductCode())) {
            criteria.andEqualTo(MarketingGroupUserEntity.C_PRODUCT_CODE, param.getProductCode());
        }
        PageHelper.startPage(1, 1000, false);
        List<MarketingGroupUserEntity> marketingGroupUserEntities = marketingGroupUserMapper.selectByCondition(example);
        return BeanCopyUtils.jsonCopyList(marketingGroupUserEntities, MarketingGroupUserMode.class);
    }

    @Override
    public List<MarketingGroupUserMode> queryGroupUserListByActivityCode(MarketingGroupUserParam param) {

        Example example = new Example(MarketingGroupUserEntity.class);

        Example.Criteria criteria = example.createCriteria();

        criteria.andEqualTo(BaseEntity.C_TENANT_CODE, param.getTenantCode())
                .andEqualTo(MarketingGroupUserEntity.C_ACTIVITY_CODE, param.getActivityCode())
                .andIn(MarketingGroupUserEntity.C_GROUP_STATUS, Arrays.asList(UserGroupStatusEnum.PAID.code(), UserGroupStatusEnum.PROCESSING.code(), UserGroupStatusEnum.FINISH.code()));
        return getMarketingGroupUserModes(param, example, criteria);
    }

    @Override
    public PageInfo<MarketingGroupUserMode> queryAllGroupUserListByCondition(MarketingGroupUserListParam param) {

        Example example = new Example(MarketingGroupUserEntity.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo(BaseEntity.C_TENANT_CODE, param.getTenantCode());

        if (StringUtil.isNotEmpty(param.getActivityCode())){
            criteria.andEqualTo(MarketingGroupUserEntity.C_ACTIVITY_CODE, param.getActivityCode());
        }

        if (StringUtil.isNotEmpty(param.getMarketingGroupCode())){
            criteria.andEqualTo(MarketingGroupUserEntity.C_MARKETING_GROUP_CODE, param.getMarketingGroupCode());
        }

        if (StringUtil.isNotEmpty(param.getUserCode())){
            criteria.andEqualTo(MarketingGroupUserEntity.C_USER_CODE, param.getUserCode());
        }


        if (StringUtil.isNotEmpty(param.getSkuCode())){
            criteria.andEqualTo(MarketingGroupUserEntity.C_SKU_CODE, param.getSkuCode());
        }

        if (StringUtil.isNotEmpty(param.getProductCode())){
            criteria.andEqualTo(MarketingGroupUserEntity.C_PRODUCT_CODE, param.getProductCode());
        }

        if (CollectionUtils.isNotEmpty(param.getGroupStatusList())){
            if (param.getGroupStatusList().contains(UserGroupStatusEnum.PROCESSING.code())){
                criteria .andNotEqualTo(MarketingGroupUserEntity.C_TEAM_LEADER,TeamLeaderEnum.LEADER.code());
            }
            criteria .andIn(MarketingGroupUserEntity.C_GROUP_STATUS,param.getGroupStatusList());
        }

        Page<MarketingGroupUserEntity> page = PageHelper.startPage(param.getPageNo(), param.getPageCount());
        List<MarketingGroupUserEntity> marketingGroupUserEntities = marketingGroupUserMapper.selectByCondition(example);
        List<MarketingGroupUserMode> userModes = BeanCopyUtils.jsonCopyList(marketingGroupUserEntities, MarketingGroupUserMode.class);
        PageInfo<MarketingGroupUserMode> pageInfo = new PageInfo<>();
        pageInfo.setList(userModes);
        pageInfo.setTotal(page.getTotal());

        return pageInfo;
    }

    @Override
    @Transactional
    public void updateGroupSuccessStatusByUserCode(String tenantCode, String marketingGroupCode, String userCode, UserGroupStatusEnum groupStatusEnum) {

        Example example = new Example(MarketingGroupUserEntity.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo(BaseEntity.C_TENANT_CODE, tenantCode)
                .andEqualTo(MarketingGroupUserEntity.C_MARKETING_GROUP_CODE, marketingGroupCode)
                .andEqualTo(MarketingGroupUserEntity.C_USER_CODE,userCode);
        MarketingGroupUserEntity entity = new MarketingGroupUserEntity();
        entity.setGroupStatus(groupStatusEnum.code());
        marketingGroupUserMapper.updateByConditionSelective(entity, example);

    }

    @Override
    public void updateGroupStatusByMarketingGroupCode(String tenantCode, String marketingGroupCode, UserGroupStatusEnum groupStatusEnum) {

        Example example = new Example(MarketingGroupUserEntity.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo(BaseEntity.C_TENANT_CODE, tenantCode)
                .andEqualTo(MarketingGroupUserEntity.C_MARKETING_GROUP_CODE, marketingGroupCode)
                .andIn(MarketingGroupUserEntity.C_GROUP_STATUS,
                        Arrays.asList(UserGroupStatusEnum.PROCESSING.code(), UserGroupStatusEnum.PAID.code()));
        MarketingGroupUserEntity entity = new MarketingGroupUserEntity();
        entity.setGroupStatus(groupStatusEnum.code());
        marketingGroupUserMapper.updateByConditionSelective(entity, example);
    }

    @Override
	public void updateGroupUserStatus(String tenantCode, String marketingGroupCode, String groupStatus, String oldGroupStatus) {
        Example example = new Example(MarketingGroupUserEntity.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo(BaseEntity.C_TENANT_CODE, tenantCode)
                .andEqualTo(MarketingGroupUserEntity.C_MARKETING_GROUP_CODE, marketingGroupCode)
				.andEqualTo(MarketingGroupUserEntity.C_GROUP_STATUS, oldGroupStatus);
        MarketingGroupUserEntity entity = new MarketingGroupUserEntity();
        entity.setGroupStatus(groupStatus);
        marketingGroupUserMapper.updateByConditionSelective(entity, example);
    }

    @Override
    public void updateGroupEffectiveTimeByMarketingGroupCode(String tenantCode, String marketingGroupCode, String userCode, String effectiveTime) {

        Example example = new Example(MarketingGroupUserEntity.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo(BaseEntity.C_TENANT_CODE, tenantCode)
                .andEqualTo(MarketingGroupUserEntity.C_MARKETING_GROUP_CODE, marketingGroupCode);
            criteria.andEqualTo(MarketingGroupUserEntity.C_USER_CODE,userCode);

        MarketingGroupUserEntity entity = new MarketingGroupUserEntity();
        entity.setEffectiveTime(effectiveTime);
        entity.setGroupStatus(UserGroupStatusEnum.PAID.code());
        marketingGroupUserMapper.updateByConditionSelective(entity, example);
    }

    @Override
    public void cancelGroupByMarketingGroupAndUserCode(String tenantCode, String marketingGroupCode, String userCode) {

        Example example = new Example(MarketingGroupUserEntity.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo(BaseEntity.C_TENANT_CODE, tenantCode)
                .andEqualTo(MarketingGroupUserEntity.C_MARKETING_GROUP_CODE, marketingGroupCode)
                .andEqualTo(MarketingGroupUserEntity.C_USER_CODE,userCode)
                .andEqualTo(MarketingGroupUserEntity.C_CANCEL_TIME,"0");

        MarketingGroupUserEntity entity = new MarketingGroupUserEntity();
        String currentDateAsString = DateUtil.getCurrentDateAsString(DateUtil.FORMAT_YYYYMMDDHHMISS_14);
        entity.setCancelTime(currentDateAsString);
        entity.setGroupStatus(UserGroupStatusEnum.CANCEL.code());
        marketingGroupUserMapper.updateByConditionSelective(entity,example);

    }

    @Override
    public boolean existLeaderByGroupCode(String tenantCode, String marketingGroupCode, String activityCode,String userCode) {
        Example example = new Example(MarketingGroupUserEntity.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo(BaseEntity.C_TENANT_CODE, tenantCode)
                .andEqualTo(MarketingGroupUserEntity.C_MARKETING_GROUP_CODE, marketingGroupCode);
        criteria.andEqualTo(MarketingGroupUserEntity.C_ACTIVITY_CODE,activityCode);
        List<MarketingGroupUserEntity> marketingGroupUserEntities = marketingGroupUserMapper.selectByCondition(example);

        if (CollectionUtils.isEmpty(marketingGroupUserEntities)){
            return true;
        }

        //团长存在，且用户编码是团长
        List<MarketingGroupUserEntity> collect = marketingGroupUserEntities.stream().filter(x -> x.getTeamLeader().equals(TeamLeaderEnum.LEADER.code())
                && x.getUserCode().equals(userCode)).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(collect)){
            return false;
        }else {
            return true;
        }
    }

    @Override
    public int checkGroupProductCode(String tenantCode, String activityCode, String marketingGroupCode,String productCode, String skuCode) {

        Example example = new Example(MarketingGroupUserEntity.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo(BaseEntity.C_TENANT_CODE, tenantCode)
                .andEqualTo(MarketingGroupUserEntity.C_ACTIVITY_CODE, activityCode)
                .andEqualTo(MarketingGroupUserEntity.C_MARKETING_GROUP_CODE, marketingGroupCode)
                .andEqualTo(MarketingGroupUserEntity.C_PRODUCT_CODE,productCode)
                .andEqualTo(MarketingGroupUserEntity.C_GROUP_STATUS, UserGroupStatusEnum.PAID.code());

        if (StringUtil.isNotEmpty(skuCode)){
            criteria.andEqualTo(MarketingGroupUserEntity.C_SKU_CODE,skuCode);
        }

        return marketingGroupUserMapper.selectCountByCondition(example);
    }


    @Override
    public int checkGroupLeaderProductCode(String tenantCode, String activityCode, String marketingGroupCode, String productCode,String skuCode) {

        Example example = new Example(MarketingGroupUserEntity.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo(BaseEntity.C_TENANT_CODE, tenantCode)
                .andEqualTo(MarketingGroupUserEntity.C_ACTIVITY_CODE, activityCode)
                .andEqualTo(MarketingGroupUserEntity.C_MARKETING_GROUP_CODE, marketingGroupCode)
                .andEqualTo(MarketingGroupUserEntity.C_PRODUCT_CODE,productCode)
                .andEqualTo(MarketingGroupUserEntity.C_TEAM_LEADER,TeamLeaderEnum.LEADER.code());

        if (StringUtil.isNotEmpty(skuCode)){
            criteria.andEqualTo(MarketingGroupUserEntity.C_SKU_CODE,skuCode);
        }
        return marketingGroupUserMapper.selectCountByCondition(example);
    }

    @Override
    public int countOfParticipants(GroupUserCountDto dto) {

        if (CollectionUtil.isEmpty(dto.getGroupCodeList())){
            return 0;
        }

        Example example = new Example(MarketingGroupUserEntity.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo(BaseEntity.C_TENANT_CODE, dto.getTenantCode())
                .andEqualTo(MarketingGroupUserEntity.C_ACTIVITY_CODE, dto.getActivityCode())
                .andIn(MarketingGroupUserEntity.C_MARKETING_GROUP_CODE, dto.getGroupCodeList())
                .andIn(MarketingGroupUserEntity.C_GROUP_STATUS, dto.getGroupStatusList())
                .andIn(MarketingGroupUserEntity.C_TEAM_LEADER,dto.getTeamLeaderList());

        return marketingGroupUserMapper.selectCountByCondition(example);
    }
}
