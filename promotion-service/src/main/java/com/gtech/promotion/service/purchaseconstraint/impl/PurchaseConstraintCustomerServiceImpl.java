package com.gtech.promotion.service.purchaseconstraint.impl;

import com.gtech.commons.utils.ConvertUtils;
import com.gtech.promotion.RedisClient;
import com.gtech.promotion.dao.entity.purchaseconstraint.PurchaseConstraintCustomer;
import com.gtech.promotion.dao.entity.purchaseconstraint.PurchaseConstraintDetail;
import com.gtech.promotion.dao.mapper.purchaseconstraint.PurchaseConstraintCustomerMapper;
import com.gtech.promotion.dao.model.purchaseconstraint.PcRuleCalculateModel;
import com.gtech.promotion.dao.model.purchaseconstraint.PurchaseConstraintCustomerListMode;
import com.gtech.promotion.feign.OrderClientConsumer;
import com.gtech.promotion.service.purchaseconstraint.PurchaseConstraintCustomerService;
import com.gtech.promotion.service.purchaseconstraint.PurchaseConstraintDetailService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

import static com.gtech.promotion.code.purchaseconstraint.PurchaseConstraintRuleTypeEnum.CUSTOMER_MAX_AMOUNT;

@Slf4j
@Service
public class PurchaseConstraintCustomerServiceImpl implements PurchaseConstraintCustomerService {
    @Autowired
    private RedisClient redisClient;
    @Autowired
    private PurchaseConstraintCustomerMapper purchaseConstraintCustomerMapper;

    @Autowired
    private OrderClientConsumer orderClientConsumer;
    @Autowired
    private PurchaseConstraintDetailService pcDetailService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void insert(PurchaseConstraintCustomer constraintCustomer) {
        purchaseConstraintCustomerMapper.insertSelective(constraintCustomer);
    }

    @Override
    public void insert(List<PurchaseConstraintCustomer> constraintCustomer) {
        if (CollectionUtils.isNotEmpty(constraintCustomer)) {
            Example example = new Example(PurchaseConstraintCustomer.class);
            example.createCriteria()
                    .andIn("customerCode", constraintCustomer.stream().map(PurchaseConstraintCustomer::getCustomerCode).collect(Collectors.toList()));
            List<PurchaseConstraintCustomer> purchaseConstraintCustomers = purchaseConstraintCustomerMapper.selectByCondition(example);
            if (CollectionUtils.isNotEmpty(purchaseConstraintCustomers)){
                List<String> customerCodes = purchaseConstraintCustomers.stream().map(PurchaseConstraintCustomer::getCustomerCode).collect(Collectors.toList());
                // 过滤已经存在的数据
                constraintCustomer  = constraintCustomer.stream().filter(x -> !customerCodes.contains(x.getCustomerCode())).collect(Collectors.toList());
            }
            if (CollectionUtils.isEmpty(constraintCustomer)){
                return;
            }
            purchaseConstraintCustomerMapper.insertList(constraintCustomer);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int increment(List<PurchaseConstraintCustomer> customers) {
        return purchaseConstraintCustomerMapper.increment(customers);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int decrement(PurchaseConstraintCustomer purchaseConstraintCustomer) {
        return purchaseConstraintCustomerMapper.decrement(purchaseConstraintCustomer);
    }

    @Override
    public List<PurchaseConstraintCustomer> list(PurchaseConstraintCustomerListMode mode) {
        return purchaseConstraintCustomerMapper.list(mode);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void decrement(PcRuleCalculateModel model, List<PurchaseConstraintDetail> list, Map<String, PcRuleCalculateModel.IncrementProduct> skuMap) {
        for (PurchaseConstraintDetail detail : list) {
            PcRuleCalculateModel.IncrementProduct incrementProduct = skuMap.get(detail.getSkuCode());
            List<String> customerCodes = Arrays.stream(detail.getCustomerCode().split(",")).collect(Collectors.toList());
            PurchaseConstraintCustomerListMode customerListMode = new PurchaseConstraintCustomerListMode();
            customerListMode.setTenantCode(model.getTenantCode());
            customerListMode.setCustomerCodes(customerCodes);
            List<PurchaseConstraintCustomer> customers = this.list(customerListMode);
            // 按照金额,数量分组;true:金额; false:数量;
            Map<Boolean, List<PurchaseConstraintCustomer>> collect = customers.stream()
                    .collect(Collectors.groupingBy(x -> CUSTOMER_MAX_AMOUNT.equalsCode(x.getPurchaseConstraintRuleType())));

            BigDecimal sellAmount = Optional.of(ConvertUtils.toBigDecimal(incrementProduct.getSellAmount(),BigDecimal.ZERO).multiply(ConvertUtils.toBigDecimal(BigDecimal.valueOf(incrementProduct.getSkuCount()),BigDecimal.ZERO))).orElse(BigDecimal.ZERO);
            Integer qty = Optional.ofNullable(incrementProduct.getSkuCount()).orElse(0);
            PurchaseConstraintDetail purchaseConstraintDetail = new PurchaseConstraintDetail();
            purchaseConstraintDetail.setCustomerCode(customerCodes.get(0));
            purchaseConstraintDetail.setTenantCode(model.getTenantCode());
            purchaseConstraintDetail.setOrderId(model.getOrderCode());
            purchaseConstraintDetail.setProductCode(incrementProduct.getProductCode());
            purchaseConstraintDetail.setSkuCode(incrementProduct.getSkuCode());
            purchaseConstraintDetail.setDetailQtyBack(qty);
            purchaseConstraintDetail.setDetailAmountBack(sellAmount);
            // 根据订单维度返还额度;如果修改行数0; 不进行额度返还
            if (pcDetailService.decrement(purchaseConstraintDetail) > 0) {
                collect.forEach((key, values) -> {
                    BigDecimal incrementValue = Boolean.TRUE.equals(key) ? sellAmount : new BigDecimal(String.valueOf(qty));
                    for (PurchaseConstraintCustomer customer : values) {
                        log.info("decrement#记录退还成功: {}", purchaseConstraintDetail.getCustomerCode());
                        PurchaseConstraintCustomer purchaseConstraintCustomer = new PurchaseConstraintCustomer();
                        purchaseConstraintCustomer.setTenantCode(model.getTenantCode());
                        purchaseConstraintCustomer.setCustomerCode(customer.getCustomerCode());
                        purchaseConstraintCustomer.setPurchaseConstraintValueUsed(incrementValue);
                        if (this.decrement(purchaseConstraintCustomer) > 0) {
                            log.info("decrement#额度返还成功: {}", purchaseConstraintDetail.getCustomerCode());
                            redisClient.delete(customer.getCustomerCode());
                        } else {
                            log.warn("decrement#额度返还失败: {}", purchaseConstraintDetail.getCustomerCode());
                        }
                    }
                });
            } else {
                log.warn("decrement#记录退还失败: {}", purchaseConstraintDetail.getCustomerCode());
            }
        }
    }



    @Override
    public int delete(List<PurchaseConstraintCustomer> customers) {
        if (CollectionUtils.isEmpty(customers)){
            return 0;
        }
        Example example = new Example(PurchaseConstraintCustomer.class);
        example.createCriteria()
                .andIn("userCode",customers.stream().map(PurchaseConstraintCustomer::getUserCode).collect(Collectors.toList()))
                .andIn("purchaseConstraintCode",customers.stream().map(PurchaseConstraintCustomer::getProductCode).collect(Collectors.toList()));
        return purchaseConstraintCustomerMapper.deleteByCondition(example);
    }


}
