/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.service.activity;

import com.gtech.promotion.dao.model.activity.ActivityFunctionParamRankModel;

import java.util.List;

/**
 * 函数层级服务
 *
 */
public interface ActivityFuncRankService {

    /**
     * 添加函数层级
     */
    String saveTPromoRuleFuncRank(String activityCode, String templateCode, int rank, String tenantCode);

    /**
     * 根据活动id获取层级列表
     */
    List<ActivityFunctionParamRankModel> getRankListByActivityCode(String activityCode);

    /**
     * 删除函数层级
     */
    int deleteByActivityCode(String activityCode);

    /**
     * 根据活动编码列表获取层级列表
     */
    List<ActivityFunctionParamRankModel> getRankListByActivityCodes(String tenantCode, List<String> activityCodes);
}
