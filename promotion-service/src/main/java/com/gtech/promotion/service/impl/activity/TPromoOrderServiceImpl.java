/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.service.impl.activity;

import com.gtech.commons.utils.BeanCopyUtils;
import com.gtech.commons.utils.DateUtil;
import com.gtech.commons.utils.StringUtil;
import com.gtech.promotion.code.activity.OrderStatusEnum;
import com.gtech.promotion.dao.entity.activity.TPromoOrderEntity;
import com.gtech.promotion.dao.mapper.activity.TPromoOrderMapper;
import com.gtech.promotion.dao.model.activity.TPromoOrderVO;
import com.gtech.promotion.dto.in.activity.ActivityTenantInDTO;
import com.gtech.promotion.dto.in.activity.CouponOrderStatisticInDTO;
import com.gtech.promotion.dto.in.activity.QueryActivityStatisticInDTO;
import com.gtech.promotion.service.activity.TPromoOrderService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;
import tk.mybatis.mapper.entity.Example.Criteria;

import java.util.Date;
import java.util.List;

/**
 * 促销订单
 * 
 */
@Service
public class TPromoOrderServiceImpl implements TPromoOrderService{

    @Autowired
    private TPromoOrderMapper orderMapper;

    private static final String UPDATE_TIME = "updateTime";

    private static final String ORDER_STATUS = "orderStatus";
    
    private static final String TENANT_CODE = "tenantCode";

    @Override
    @Transactional
    public String insertTPromoOrder(TPromoOrderVO order){

        TPromoOrderEntity entity = BeanCopyUtils.jsonCopyBean(order, TPromoOrderEntity.class);
        orderMapper.insertSelective(entity);
        return entity.getId().toString();
    }

    @Override
    public TPromoOrderVO queryOrderBySalesOrderNo(String tenantCode,String salesOrderNo){
        TPromoOrderEntity entity = new TPromoOrderEntity();
        entity.setTenantCode(tenantCode);
        entity.setOrderId(salesOrderNo);
        return BeanCopyUtils.jsonCopyBean(orderMapper.selectOne(entity), TPromoOrderVO.class);
    }

    @Override
    @Transactional
    public Integer updateOrderLogicDelete(String tenantCode,String salesOrderNo,String salesOrderStatus){
        Example example = new Example(TPromoOrderEntity.class);
        Criteria criteria = example.createCriteria();
        criteria.andEqualTo(TENANT_CODE, tenantCode).andEqualTo("orderId", salesOrderNo);
        TPromoOrderEntity record = new TPromoOrderEntity();
        record.setOrderStatus(salesOrderStatus);
        return orderMapper.updateByConditionSelective(record, example);
    }

    @Override
    public List<TPromoOrderEntity> queryPromoOrderYesterday(){

        Example example = new Example(TPromoOrderEntity.class);
        example.createCriteria().andEqualTo(ORDER_STATUS, OrderStatusEnum.PAID.code()).andGreaterThanOrEqualTo(UPDATE_TIME, DateUtil.addDay(DateUtil.parseDate(DateUtil.getCurrentDateAsString(DateUtil.FORMAT_YYYYMMDD), DateUtil.FORMAT_YYYYMMDD), -1))
                        .andLessThan(UPDATE_TIME, DateUtil.parseDate(DateUtil.getCurrentDateAsString(DateUtil.FORMAT_YYYYMMDD), DateUtil.FORMAT_YYYYMMDD));

        return orderMapper.selectByCondition(example);
    }

    @Override
    public List<TPromoOrderEntity> queryPromoOrderToday(QueryActivityStatisticInDTO paramDTO) {
        Example example = new Example(TPromoOrderEntity.class);

        example.createCriteria().andEqualTo(ORDER_STATUS, OrderStatusEnum.PAID.code()).andEqualTo(TENANT_CODE,paramDTO.getTenantCode())
                                .andGreaterThanOrEqualTo(UPDATE_TIME, DateUtil.parseDate(DateUtil.format(new Date(), DateUtil.FORMAT_YYYYMMDD), DateUtil.FORMAT_YYYYMMDD));

        return orderMapper.selectByCondition(example);
    }

    @Override
    public List<TPromoOrderEntity> queryPromoOrderByOrderIdList(CouponOrderStatisticInDTO paramDTO) {
        Example example = new Example(TPromoOrderEntity.class);
        example.createCriteria()
                .andIn("orderId",paramDTO.getOrderIdList());
        return orderMapper.selectByCondition(example);
    }

    @Override
    @Transactional
    public Integer updateOrderStatusById(TPromoOrderVO order){
        //根据主键修改属性不为null的值
        Example example = new Example(TPromoOrderEntity.class);
        example.createCriteria().andEqualTo(ORDER_STATUS, OrderStatusEnum.UNPAID.code()).andEqualTo("id", order.getId());
        TPromoOrderEntity entity = BeanCopyUtils.jsonCopyBean(order, TPromoOrderEntity.class);
        String salesOrderStatus = OrderStatusEnum.CANCELED.code();
        entity.setOrderStatus(salesOrderStatus);
        return orderMapper.updateByConditionSelective(entity, example);
    }

    @Override
    public long payOrderAmount(){
        Example example = new Example(TPromoOrderEntity.class);
        example.createCriteria().andEqualTo(ORDER_STATUS, OrderStatusEnum.PAID.code());
        return orderMapper.selectCountByCondition(example);
    }

    @Override
    public Integer getPayOrderAmount(ActivityTenantInDTO tenantInDTO){
          
        Example example = new Example(TPromoOrderEntity.class);
        Criteria criteria = example.createCriteria();
        criteria.andEqualTo(TENANT_CODE, tenantInDTO.getTenantCode()).andEqualTo(ORDER_STATUS, OrderStatusEnum.PAID.code());
        if (StringUtil.isNotBlank(tenantInDTO.getStartTime()) && StringUtil.isNotBlank(tenantInDTO.getEndTime())){
            criteria.andGreaterThanOrEqualTo(UPDATE_TIME, tenantInDTO.getStartTime()).andLessThanOrEqualTo(UPDATE_TIME, tenantInDTO.getEndTime());
        }
        return orderMapper.selectCountByCondition(example);
    }

}
