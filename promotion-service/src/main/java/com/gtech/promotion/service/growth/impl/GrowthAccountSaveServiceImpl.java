package com.gtech.promotion.service.growth.impl;

import com.gtech.commons.redis.GTechCodeGenerator;
import com.gtech.commons.utils.BeanCopyUtils;
import com.gtech.promotion.checker.SystemChecker;
import com.gtech.promotion.dao.entity.growth.GrowthAccountEntity;
import com.gtech.promotion.dao.mapper.growth.GrowthAccountMapper;
import com.gtech.promotion.exception.PromotionException;
import com.gtech.promotion.service.growth.GrowthAccountSaveService;
import com.gtech.promotion.service.growth.GrowthTransactionService;
import com.gtech.promotion.vo.param.growth.CreateGrowthAccountParam;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 * @Date 2021/1/15 10:11
 */
@Service
public class GrowthAccountSaveServiceImpl implements GrowthAccountSaveService {
    private static final String SYS_CODE = "GrowthAccountServiceImpl";
    private static final String GROWTH_ACCOUNT_CODE = "growthAccountCode";

    @Autowired
    GrowthAccountMapper growthAccountMapper;

    @Autowired
    GrowthTransactionService growthTransactionService;

    @Autowired
    GTechCodeGenerator codeGenerator;

    @Override
    @Transactional
    public String saveGrowthAccount(CreateGrowthAccountParam growthAccountVo) {

        GrowthAccountEntity growthAccount = BeanCopyUtils.jsonCopyBean(growthAccountVo, GrowthAccountEntity.class);

        if (null == growthAccount.getStatus()) {
            growthAccount.setStatus(0);
        }
        if (null == growthAccount.getAccountBalance()) {
            growthAccount.setAccountBalance(0);
        }
        if (StringUtils.isBlank(growthAccount.getGrowthAccountCode())) {//NOSONAR
            growthAccount.setGrowthAccountCode(codeGenerator.generateCode(growthAccount.getTenantCode(), GROWTH_ACCOUNT_CODE, "GA[D:yyyyMMddHHmmss][SM:%06d]", 1l));//NOSONAR
        }//NOSONAR

        try {
            growthAccountMapper.insert(growthAccount);
        } catch (DuplicateKeyException e) {
            throw new PromotionException(SystemChecker.DUPLICATE_KEY.getCode(), SystemChecker.DUPLICATE_KEY.getMessage());
        } catch (Exception e) {
            throw new PromotionException(SYS_CODE + ".savePointAccountModel.ex", e.getMessage());
        }

        return growthAccount.getGrowthAccountCode();
    }



}
