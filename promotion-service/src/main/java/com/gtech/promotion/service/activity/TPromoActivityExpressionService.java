 
/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.service.activity;

import com.gtech.promotion.dao.entity.activity.TPromoActivityExpressionEntity;

/**   
 * 互斥叠加优先级表达式服务类
 */
public interface TPromoActivityExpressionService{
    
    /**   
     * redis获取互斥叠加优先级表达式
     * @param tenantCode
     * @return  TPromoActivityExpressionEntity
     */
    public String getActivityExpression(String tenantCode);
    
    /**   
     * 获取互斥叠加优先级表达式
     * @param tenantCode
     * @return  TPromoActivityExpressionEntity
     */
    public TPromoActivityExpressionEntity selectExpression(String tenantCode);

    /**   
     * 添加互斥叠加优先级表达式
     * @param TPromoActivityExpressionEntity
     */
    public void addActivityExpression(String tenantCode,String expression);
    
    /**   
     * 修改叠加优先级表达式，根据主键
     * @param TPromoActivityExpressionEntity
     */
    public void updateActivityExpression(String tenantCode,String expression);

    /**   
     * 清空缓存将数据库的表达式加载到缓存中，或者更新新的表达式到数据库和缓存
     */
    public void addAllActivityExpression();

}
  
