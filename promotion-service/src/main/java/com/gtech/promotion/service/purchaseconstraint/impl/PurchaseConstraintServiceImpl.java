package com.gtech.promotion.service.purchaseconstraint.impl;

import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import com.gtech.commons.utils.BeanCopyUtils;
import com.gtech.promotion.code.activity.ActivityStatusEnum;
import com.gtech.promotion.code.purchaseconstraint.PurchaseConstraintStatusEnum;
import com.gtech.promotion.dao.entity.purchaseconstraint.PurchaseConstraintEntity;
import com.gtech.promotion.dao.mapper.purchaseconstraint.PurchaseConstraintMapper;
import com.gtech.promotion.dao.model.purchaseconstraint.PurchaseConstraintModel;
import com.gtech.promotion.service.purchaseconstraint.PurchaseConstraintService;
import com.gtech.promotion.vo.bean.PurchaseConstraintPriority;
import com.gtech.promotion.vo.param.purchaseconstraint.query.QueryPurchaseConstraintListParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;
import tk.mybatis.mapper.weekend.WeekendSqls;

import java.util.Date;
import java.util.List;

@Slf4j
@Service
public class PurchaseConstraintServiceImpl implements PurchaseConstraintService {

    @Autowired
    private PurchaseConstraintMapper pcMapper;

    @Override
    public PageInfo<PurchaseConstraintEntity> queryList(QueryPurchaseConstraintListParam param) {
        PageMethod.startPage(param.getPageNum(), param.getPageSize());
        return new PageInfo<>(pcMapper.queryList(param));
    }

    @Override
    public PurchaseConstraintModel getPurchaseConstraint(String tenantCode, String purchaseConstraintCode) {
        if (StringUtils.isBlank(tenantCode) || StringUtils.isBlank(purchaseConstraintCode)) {
            return null;
        }
        PurchaseConstraintEntity purchaseConstraintQuery = new PurchaseConstraintEntity();
        purchaseConstraintQuery.setTenantCode(tenantCode);
        purchaseConstraintQuery.setPurchaseConstraintCode(purchaseConstraintCode);
        return BeanCopyUtils.jsonCopyBean(pcMapper.selectOne(purchaseConstraintQuery)
                                            , PurchaseConstraintModel.class);
    }

    @Override
    public int updatePurchaseConstraintStatus(String tenantCode, String purchaseConstraintCode,
                                              String purchaseConstraintStatus, String operateUser) {
        PurchaseConstraintEntity entity = new PurchaseConstraintEntity();
        entity.setPurchaseConstraintStatus(purchaseConstraintStatus);
        entity.setUpdateUser(operateUser);
        if(ActivityStatusEnum.PENDING.code().equals(purchaseConstraintStatus)){
            entity.setCreateUser(operateUser);
        }
        if(ActivityStatusEnum.IN_AUDIT.code().equals(purchaseConstraintStatus)){
            entity.setAuditUser(operateUser);
        }
        Example example = new Example.Builder(PurchaseConstraintEntity.class)
                .where(WeekendSqls.<PurchaseConstraintEntity>custom()
                        .andEqualTo(PurchaseConstraintEntity::getTenantCode, tenantCode)
                        .andEqualTo(PurchaseConstraintEntity::getPurchaseConstraintCode, purchaseConstraintCode)
                ).build();
        return pcMapper.updateByConditionSelective(entity, example);
    }

    @Override
    public void insert(PurchaseConstraintModel purchaseConstraintModel) {
        PurchaseConstraintEntity purchaseConstraintEntity = BeanCopyUtils
                                                .jsonCopyBean(purchaseConstraintModel, PurchaseConstraintEntity.class);
        purchaseConstraintEntity.setCreateTime(new Date());
        purchaseConstraintEntity.setPurchaseConstraintStatus(PurchaseConstraintStatusEnum.PENDING.getCode());
        purchaseConstraintModel.setCreateTime(new Date());
        pcMapper.insertSelective(purchaseConstraintEntity);
    }

    @Override
    public int updatePurchaseConstraintById(PurchaseConstraintModel updatePurchaseConstraintModel) {
        PurchaseConstraintEntity purchaseConstraintEntityDb =
                                    pcMapper.selectByPrimaryKey(updatePurchaseConstraintModel.getId());
        PurchaseConstraintEntity purchaseConstraintEntity =
                            BeanCopyUtils.jsonCopyBean(updatePurchaseConstraintModel, PurchaseConstraintEntity.class);

        purchaseConstraintEntity.setPurchaseConstraintStatus(purchaseConstraintEntityDb.getPurchaseConstraintStatus());
        purchaseConstraintEntity.setCreateTime(purchaseConstraintEntityDb.getCreateTime());
        purchaseConstraintEntity.setCreateUser(purchaseConstraintEntityDb.getCreateUser());
        purchaseConstraintEntity.setAuditUser(purchaseConstraintEntityDb.getAuditUser());
        purchaseConstraintEntity.setPurchaseConstraintCode(purchaseConstraintEntityDb.getPurchaseConstraintCode());
        purchaseConstraintEntity.setTenantCode(purchaseConstraintEntityDb.getTenantCode());
        purchaseConstraintEntity.setDomainCode(purchaseConstraintEntityDb.getDomainCode());
        purchaseConstraintEntity.setOrgCode(purchaseConstraintEntityDb.getOrgCode());
        purchaseConstraintEntity.setPeriodType(purchaseConstraintEntityDb.getPeriodType());
        return pcMapper.updateByPrimaryKey(purchaseConstraintEntity);
    }

    @Override
    @Transactional
    public int updatePurchaseConstraintPriority(String tenantCode, List<PurchaseConstraintPriority> purchaseConstraintPriorityList) {
        int totalResult = 0;
        for(PurchaseConstraintPriority purchaseConstraintPriority : purchaseConstraintPriorityList) {
            int result = pcMapper.updatePurchaseConstraintPriority(tenantCode,
                                                                purchaseConstraintPriority.getPurchaseConstraintCode(),
                                                                purchaseConstraintPriority.getPurchaseConstraintPriority()
                                                                );
            totalResult = totalResult + result;
        }
        return totalResult;
    }

    @Override
    public List<PurchaseConstraintModel> queryEffectivePc(String tenantCode) {
        List<PurchaseConstraintEntity> pcEntityList = pcMapper.queryEffectivePc(tenantCode);
        return BeanCopyUtils.jsonCopyList(pcEntityList, PurchaseConstraintModel.class);
    }

    @Override
    public List<PurchaseConstraintModel> list(String tenantCode) {
        WeekendSqls<PurchaseConstraintEntity> custom = WeekendSqls.custom();
        custom.andEqualTo(PurchaseConstraintEntity::getPurchaseConstraintStatus, "04");
        custom.andEqualTo(PurchaseConstraintEntity::getTenantCode, tenantCode);

        Example example = new Example.Builder(PurchaseConstraintEntity.class)
                .where(custom).build();

        List<PurchaseConstraintEntity> purchaseConstraintEntityList = pcMapper.selectByExample(example);
        return BeanCopyUtils.jsonCopyList(purchaseConstraintEntityList, PurchaseConstraintModel.class);
    }
}
