package com.gtech.promotion.service.flashsale;

import com.gtech.promotion.dao.entity.marketing.flashsale.FlashSaleOrderEntity;
import com.gtech.promotion.dao.model.marketing.flashsale.FlashPreSaleOrderModel;
import com.gtech.promotion.dao.model.marketing.flashsale.FlashSaleOrderModel;
import com.gtech.promotion.service.marketing.BaseService;
import com.gtech.promotion.vo.result.flashpresale.ExportFlashPreSaleDto;

import java.util.List;

public interface FlashSaleOrderService extends BaseService<FlashSaleOrderEntity, FlashSaleOrderModel> {

    FlashSaleOrderModel findByOrderNo(String tenantCode, String orderNo);

    int updateStatus(String tenantCode, String orderNo, String orderStatus);

    /**
     * 查询团长是否支付
     * @param tenantCode
     * @param marketingGroupCode
     * @param activityCode
     * @param memberCode
     * @return  结果大于0 已支付
     */
    int checkLeaderPayOrder(String tenantCode, String activityCode,String marketingGroupCode,String memberCode);


    /**
     *
     * @param tenantCode
     * @param activityCode
     * @param marketingGroupCode
     * @return
     */
    List<FlashSaleOrderModel> queryOrderByMarketingGroupCode(String tenantCode, String activityCode,String marketingGroupCode);


    /***
     * 查询活动对应的订单
     * @param dto
     * @return
     */
    List<FlashPreSaleOrderModel> queryOrderByCondition(ExportFlashPreSaleDto dto);

    /**
     * 拼团活动对应的订单
     * @param tenantCode
     * @param activityCode
     * @param groupCodeList
     * @return
     */
    List<FlashPreSaleOrderModel> queryOrderByMarketingGroupCodeList(String tenantCode,String activityCode,List<String> groupCodeList);


}
