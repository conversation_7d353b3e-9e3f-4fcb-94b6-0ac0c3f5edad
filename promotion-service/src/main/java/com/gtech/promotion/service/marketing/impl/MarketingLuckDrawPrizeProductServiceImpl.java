package com.gtech.promotion.service.marketing.impl;

import com.gtech.promotion.code.marketing.LuckeyDrawPrizeProductStatusEnum;
import com.gtech.promotion.dao.entity.marketing.MarketingLuckDrawPrizeProductEntity;
import com.gtech.promotion.dao.mapper.marketing.MarketingLuckDrawPrizeProductMapper;
import com.gtech.promotion.dao.model.marketing.MarketingLuckDrawPrizeProductModel;
import com.gtech.promotion.service.marketing.MarketingLuckDrawPrizeProductService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import java.util.List;

@Service
public class MarketingLuckDrawPrizeProductServiceImpl extends BaseServiceImpl<MarketingLuckDrawPrizeProductEntity, MarketingLuckDrawPrizeProductModel> implements MarketingLuckDrawPrizeProductService {
    public MarketingLuckDrawPrizeProductServiceImpl() {
        super(MarketingLuckDrawPrizeProductEntity.class, MarketingLuckDrawPrizeProductModel.class);
    }
    @Autowired
    private MarketingLuckDrawPrizeProductMapper marketingLuckDrawPrizeProductMapper;





    @Override
    public Boolean checkLuckDrawPrizeProduct(String domainCode, String tenantCode, String userCode, String checkLuckDrawPrizeActivityCode, String productCode) {


        Example example = new Example(MarketingLuckDrawPrizeProductEntity.class);
        example .createCriteria()
                .andEqualTo("domainCode",domainCode)
                .andEqualTo("tenantCode",tenantCode)
                .andEqualTo("memberCode",userCode)
                .andEqualTo("activityCode",checkLuckDrawPrizeActivityCode)
                .andEqualTo("productCode",productCode)
                .andEqualTo("status", LuckeyDrawPrizeProductStatusEnum.UN_USED.code());


        List<MarketingLuckDrawPrizeProductEntity> marketingLuckDrawPrizeProductEntities = marketingLuckDrawPrizeProductMapper.selectByCondition(example);
        return marketingLuckDrawPrizeProductEntities.size() > 0;
    }

    @Override
    public Boolean lockPrizeProduct(String tenantCode, String activityCode,String memberCode, String productCode,String orderId) {
        Example example = new Example(MarketingLuckDrawPrizeProductEntity.class);
        example.createCriteria()
                .andEqualTo("tenantCode", tenantCode)
                .andEqualTo("activityCode", activityCode)
                .andEqualTo("productCode", productCode)
                .andEqualTo("memberCode", memberCode)
                .andEqualTo("status", LuckeyDrawPrizeProductStatusEnum.UN_USED.code());
        List<MarketingLuckDrawPrizeProductEntity> marketingLuckDrawPrizeProductEntities = marketingLuckDrawPrizeProductMapper.selectByExample(example);

        if (marketingLuckDrawPrizeProductEntities.isEmpty()) {
            return false;
        }
        MarketingLuckDrawPrizeProductEntity firstEntity = marketingLuckDrawPrizeProductEntities.get(0);
        Example updateExample = new Example(MarketingLuckDrawPrizeProductEntity.class);
        updateExample.createCriteria()
                .andEqualTo("luckyDrawPrizeProductCode", firstEntity.getLuckyDrawPrizeProductCode())
                .andEqualTo("memberCode", memberCode)
                .andEqualTo("status", LuckeyDrawPrizeProductStatusEnum.UN_USED.code());
        MarketingLuckDrawPrizeProductEntity updateEntity = new MarketingLuckDrawPrizeProductEntity();
        updateEntity.setStatus(LuckeyDrawPrizeProductStatusEnum.LOCK.code());
        updateEntity.setOrderId(orderId);
        return marketingLuckDrawPrizeProductMapper.updateByExampleSelective(updateEntity, updateExample) > 0;
    }

    @Override
    public Boolean usedPrizeProduct(String tenantCode, String activityCode,String memberCode, String orderId) {
        Example example = new Example(MarketingLuckDrawPrizeProductEntity.class);
        example.createCriteria()
                .andEqualTo("tenantCode", tenantCode)
                .andEqualTo("activityCode", activityCode)
                .andEqualTo("memberCode", memberCode)
                .andEqualTo("orderId", orderId)
                .andEqualTo("status", LuckeyDrawPrizeProductStatusEnum.LOCK.code());
        MarketingLuckDrawPrizeProductEntity prizeProduct = marketingLuckDrawPrizeProductMapper.selectOneByExample(example);

        if (null == prizeProduct) {
            return false;
        }

        Example updateExample = new Example(MarketingLuckDrawPrizeProductEntity.class);
        updateExample.createCriteria()
                .andEqualTo("luckyDrawPrizeProductCode", prizeProduct.getLuckyDrawPrizeProductCode())
                .andEqualTo("orderId", orderId)
                .andEqualTo("memberCode", memberCode)
                .andEqualTo("status", LuckeyDrawPrizeProductStatusEnum.LOCK.code());
        MarketingLuckDrawPrizeProductEntity updateEntity = new MarketingLuckDrawPrizeProductEntity();
        updateEntity.setStatus(LuckeyDrawPrizeProductStatusEnum.USED.code());
        return marketingLuckDrawPrizeProductMapper.updateByExampleSelective(updateEntity, updateExample) > 0;
    }

    @Override
    public Boolean cancelPrizeProduct(String tenantCode, String activityCode, String memberCode, String orderId) {

        Example example = new Example(MarketingLuckDrawPrizeProductEntity.class);
        example.createCriteria()
                .andEqualTo("tenantCode", tenantCode)
                .andEqualTo("activityCode", activityCode)
                .andEqualTo("memberCode", memberCode)
                .andEqualTo("orderId", orderId)
                .andEqualTo("status", LuckeyDrawPrizeProductStatusEnum.USED.code());
        MarketingLuckDrawPrizeProductEntity prizeProduct = marketingLuckDrawPrizeProductMapper.selectOneByExample(example);

        if (null == prizeProduct) {
            return false;
        }

        Example updateExample = new Example(MarketingLuckDrawPrizeProductEntity.class);
        updateExample.createCriteria()
                .andEqualTo("luckyDrawPrizeProductCode", prizeProduct.getLuckyDrawPrizeProductCode())
                .andEqualTo("orderId", orderId)
                .andEqualTo("memberCode", memberCode)
                .andEqualTo("status", LuckeyDrawPrizeProductStatusEnum.USED.code());
        MarketingLuckDrawPrizeProductEntity updateEntity = new MarketingLuckDrawPrizeProductEntity();
        updateEntity.setStatus(LuckeyDrawPrizeProductStatusEnum.UN_USED.code());
        return marketingLuckDrawPrizeProductMapper.updateByExampleSelective(updateEntity, updateExample) > 0;
    }


}
