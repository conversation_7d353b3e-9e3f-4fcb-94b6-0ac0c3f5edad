/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * <PERSON>ECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.service.impl.coupon;

import com.gtech.commons.utils.StringUtil;
import com.gtech.promotion.code.coupon.CouponStatusEnum;
import com.gtech.promotion.code.coupon.CouponTypeEnum;
import com.gtech.promotion.dto.in.activity.ManagementDataInDTO;
import com.gtech.promotion.service.coupon.SqlPublicMethodsService;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import tk.mybatis.mapper.entity.Example.Criteria;

import java.util.ArrayList;
import java.util.List;

/**   
 */
@Service
public class SqlPublicMethodsServiceImpl implements SqlPublicMethodsService{
    
    
    private static final String COUPON_CODE = "couponCode";
    private static final String TENANT_CODE = "tenantCode";
    private static final String STATUS = "status";
    private static final String RELEASE_CODE = "releaseCode";
    private static final String COUPON_TYPE = "couponType";
    
    @Override
    public void sqlSelectCondition(ManagementDataInDTO inDTO,Criteria criteria){
        criteria.andEqualTo(TENANT_CODE, inDTO.getTenantCode())

                .andNotEqualTo(COUPON_TYPE, CouponTypeEnum.PROMOTION_CODE.code()).andIn(RELEASE_CODE, inDTO.getReleaseCodes());
        
        if (StringUtil.isNotBlank(inDTO.getCouponCode())){
            criteria.andEqualTo(COUPON_CODE, inDTO.getCouponCode());
        }
        if (!CollectionUtils.isEmpty(inDTO.getCouponTypes())){
            criteria.andIn(COUPON_TYPE, inDTO.getCouponTypes());
        }
        if (!CollectionUtils.isEmpty(inDTO.getCouponStatus())){
            criteria.andIn(STATUS, inDTO.getCouponStatus());
        }
    }

    @Override
    public void sqlSelectCouponCodeCondition(ManagementDataInDTO inDTO,Criteria criteria){
        List<String> list = new ArrayList<>();
        if ("1".equals(inDTO.getIsUseStatus())){
            list.add(CouponStatusEnum.USED.code());
            list.add(CouponStatusEnum.LOCKED.code());
        }else {
            list.add(CouponStatusEnum.UN_GRANT.code());
            list.add(CouponStatusEnum.EXPIRE.code());
        }
        criteria.andEqualTo(TENANT_CODE, inDTO.getTenantCode()).andEqualTo(COUPON_TYPE, CouponTypeEnum.PROMOTION_CODE.code())
        .andIn(RELEASE_CODE, inDTO.getReleaseCodes()).andIn(STATUS, list);
        if (StringUtil.isNotBlank(inDTO.getCouponCode())){
            criteria.andEqualTo(COUPON_CODE, inDTO.getCouponCode());
        }
        
    }

}
  
