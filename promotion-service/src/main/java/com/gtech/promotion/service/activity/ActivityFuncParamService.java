/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.service.activity;

import com.gtech.promotion.dao.model.activity.FunctionParamModel;

import java.util.List;

/**
 * 函数参数服务
 */
public interface ActivityFuncParamService{

    /**
     * 保存函数参数
     */
    int saveTPromoRuleFuncParam(String activityCode, String templateCode, List<FunctionParamModel> tPromoRuleFuncParamVOs);

    /**
     * 根据层级id获取参数列表
     *
     * @param rankId 级别id
     * @return 层级列表
     */
    List<FunctionParamModel> getRuleFuncParamListByRankId(String rankId);
    
    /**   
     * 根据层级id 和 函数编码 获取对象
     * @param rankId
     * @param functionCode
     */
    FunctionParamModel getRuleFuncParamListByRankIdAndFunctionCode(String rankId,String functionCode);

    /**
     * 删除函数参数
     */
    int deleteRuleFuncParam111(String activityCode);

    List<FunctionParamModel> getRuleFuncParamListByRankIds(String tenantCode, List<String> rankIds);
}
