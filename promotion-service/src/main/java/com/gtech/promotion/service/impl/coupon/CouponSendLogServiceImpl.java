package com.gtech.promotion.service.impl.coupon;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.gtech.commons.utils.BeanCopyUtils;
import com.gtech.promotion.dao.entity.coupon.PromoCouponSendLogEntity;
import com.gtech.promotion.dao.mapper.coupon.PromoCouponSendLogMapper;
import com.gtech.promotion.dao.model.activity.PromoCouponSendDetailModel;
import com.gtech.promotion.dao.model.activity.PromoCouponSendLogModel;
import com.gtech.promotion.service.coupon.CouponSendDetailService;
import com.gtech.promotion.service.coupon.CouponSendLogService;
import com.gtech.promotion.utils.GenerateUtil;
import com.gtech.promotion.vo.param.coupon.QueryCouponSendLogParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;

import java.util.List;

@Slf4j
@Service
public class CouponSendLogServiceImpl implements CouponSendLogService {

    @Autowired
    private PromoCouponSendLogMapper promoCouponSendLogMapper;
    @Autowired
    private CouponSendDetailService couponSendDetailService;

    @Transactional
    @Override
    public int createCouponSendLog(PromoCouponSendLogModel promoCouponSendLogModel) {
        if(null == promoCouponSendLogModel){
            return 0;
        }
        String batchNo = String.valueOf(GenerateUtil.getGenerateID(3));
        promoCouponSendLogModel.setSendBatchNo(batchNo);
        PromoCouponSendLogEntity promoCouponSendLogEntity = BeanCopyUtils.jsonCopyBean(promoCouponSendLogModel, PromoCouponSendLogEntity.class);
        if(CollectionUtils.isNotEmpty(promoCouponSendLogModel.getCouponSendDetailModelList())){
            for(PromoCouponSendDetailModel promoCouponSendDetailModel : promoCouponSendLogModel.getCouponSendDetailModelList()){
                promoCouponSendDetailModel.setActivityCode(promoCouponSendLogModel.getActivityCode());
                promoCouponSendDetailModel.setBatchNo(batchNo);
                promoCouponSendDetailModel.setCreateUser(promoCouponSendLogModel.getCreateUser());
                promoCouponSendDetailModel.setUpdateUser(promoCouponSendLogModel.getUpdateUser());
                couponSendDetailService.createCouponSendDetail(promoCouponSendDetailModel);
            }
        }
        return promoCouponSendLogMapper.insertSelective(promoCouponSendLogEntity);
    }

    @Override
    public void updateCouponSendLog(PromoCouponSendLogModel promoCouponSendLogModel){
        PromoCouponSendLogEntity promoCouponSendLogEntity = new PromoCouponSendLogEntity();
        if(null != promoCouponSendLogModel.getStatus()){
            promoCouponSendLogEntity.setStatus(promoCouponSendLogModel.getStatus());
        }
        if(!StringUtils.isEmpty(promoCouponSendLogModel.getFailReason())){
            promoCouponSendLogEntity.setFailReason(promoCouponSendLogModel.getFailReason());
        }
        if(null != promoCouponSendLogModel.getCouponQty()){
            promoCouponSendLogEntity.setCouponQty(promoCouponSendLogModel.getCouponQty());
        }
        if(null != promoCouponSendLogModel.getUserQty()){
            promoCouponSendLogEntity.setUserQty(promoCouponSendLogModel.getUserQty());
        }
        Example example = new Example(PromoCouponSendLogEntity.class);
        example.createCriteria().andEqualTo("tenantCode", promoCouponSendLogModel.getTenantCode())
                .andEqualTo("activityCode",promoCouponSendLogModel.getActivityCode())
                .andEqualTo("sendBatchNo", promoCouponSendLogModel.getSendBatchNo());
        promoCouponSendLogMapper.updateByExampleSelective(promoCouponSendLogEntity, example);
    }


    @Override
    public PageInfo<PromoCouponSendLogEntity> queryCouponSendLogList(QueryCouponSendLogParam queryCouponSendLogParam) {
        Example example = new Example(PromoCouponSendLogEntity.class);
        example.setOrderByClause("id desc");
        Example.Criteria criteria = example.createCriteria();
        if(!StringUtils.isEmpty(queryCouponSendLogParam.getTenantCode())) {
            criteria.andEqualTo("tenantCode", queryCouponSendLogParam.getTenantCode());
        }
        if(!StringUtils.isEmpty(queryCouponSendLogParam.getActivityCode())){
            criteria.andEqualTo("activityCode",queryCouponSendLogParam.getActivityCode());
        }
        if(!StringUtils.isEmpty(queryCouponSendLogParam.getReleaseCode())){
            criteria.andEqualTo("couponBatchNo",queryCouponSendLogParam.getReleaseCode());
        }
        PageHelper.startPage(queryCouponSendLogParam.getPageNum(), queryCouponSendLogParam.getPageSize());
        List<PromoCouponSendLogEntity> couponSendLogEntityList = null;
        try {
            couponSendLogEntityList = promoCouponSendLogMapper.selectByExample(example);
        }catch (Exception e){
            log.error(e.getMessage(),e);
            log.error("queryCouponSendLogList error！{}",e.getMessage());
        }
        return new PageInfo<>(couponSendLogEntityList);
    }
}
