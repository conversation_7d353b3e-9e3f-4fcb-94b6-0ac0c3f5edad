package com.gtech.promotion.service.point.impl;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.github.pagehelper.PageInfo;
import com.gtech.basic.masterdata.client.MasterDataClient;
import com.gtech.basic.masterdata.response.JsonResult;
import com.gtech.commons.redis.GTechCodeGenerator;
import com.gtech.commons.result.PageResult;
import com.gtech.commons.utils.BeanCopyUtils;
import com.gtech.promotion.checker.SystemChecker;
import com.gtech.promotion.dao.entity.point.PointAccountEntity;
import com.gtech.promotion.dao.mapper.point.PointAccountMapper;
import com.gtech.promotion.dao.mapper.point.PointCampaignMapper;
import com.gtech.promotion.dao.mapper.point.PointTransactionMapper;
import com.gtech.promotion.exception.PromotionException;
import com.gtech.promotion.service.BaseService;
import com.gtech.promotion.service.point.PointAccountService;
import com.gtech.promotion.service.point.PointAccountTempService;
import com.gtech.promotion.service.point.PointCampaignService;
import com.gtech.promotion.service.point.PointTransactionService;
import com.gtech.promotion.utils.Constants.ErrorCodePrefix;
import com.gtech.promotion.utils.DateUtil;
import com.gtech.promotion.vo.param.point.CreatePointAccountParam;
import com.gtech.promotion.vo.param.point.PointTransactionParam;
import com.gtech.promotion.vo.param.point.UpdatePointAccountParam;
import com.gtech.promotion.vo.param.point.UpdatePointParam;
import com.gtech.promotion.vo.param.point.query.GetPointAccountCampaignParam;
import com.gtech.promotion.vo.param.point.query.GetPointAccountParam;
import com.gtech.promotion.vo.param.point.query.PointAccountUniqueParam.PointAccountStatusUniqueVo;
import com.gtech.promotion.vo.param.point.query.PointCampaignUniqueParam;
import com.gtech.promotion.vo.result.point.PointAccountCampaignResult;
import com.gtech.promotion.vo.result.point.PointAccountResult;
import com.gtech.promotion.vo.result.point.PointCampaignResult;

import lombok.extern.slf4j.Slf4j;
import tk.mybatis.mapper.entity.Example;
import tk.mybatis.mapper.entity.Example.Criteria;
import tk.mybatis.mapper.util.StringUtil;
@Slf4j
@Service
public class PointAccountServiceImpl extends BaseService implements PointAccountService {

	private static final String SYS_CODE = "PointAccountServiceImpl";

	private static final String POINT_ACCOUNT_CODE = "pointAccountCode";


	@Autowired
	PointCampaignService pointCampaignService;

	@Autowired
	PointAccountTempService pointAccountTempService;

	@Autowired
	PointAccountMapper pointAccountMapper;

	@Autowired
	PointCampaignMapper pointCampaignMapper;

	@Autowired
	PointTransactionService pointTransactionService;

	@Autowired
	PointTransactionMapper pointTransactionMapper;

	@Autowired
	GTechCodeGenerator codeGenerator;

	@Autowired
	MasterDataClient masterDataClient;

	@Override
    @Transactional
	public int updatePointAccountStatus(PointAccountStatusUniqueVo pointAccountStatusUniqueVo) {

		return updateStatusPointAccountModel(pointAccountStatusUniqueVo.getTenantCode(), pointAccountStatusUniqueVo.getPointAccountCode(),pointAccountStatusUniqueVo.getCampaignCode(), pointAccountStatusUniqueVo.getStatus(),
				pointAccountStatusUniqueVo.getOldStatus());
	}

    @Override
    @Transactional
    public int updatePointAccount(UpdatePointAccountParam param) {

        Example example = new Example(PointAccountEntity.class);

        Criteria criteria = example.createCriteria();

        criteria.andEqualTo(PointAccountEntity.C_DOMAIN_CODE, param.getDomainCode());
        criteria.andEqualTo(PointAccountEntity.C_TENANT_CODE, param.getTenantCode());

        criteria.andEqualTo(PointAccountEntity.C_ACCOUNT_TYPE, param.getAccountType());
        criteria.andEqualTo(PointAccountEntity.C_ACCOUNT_CODE, param.getAccountCode());

        PointAccountEntity record = BeanCopyUtils.jsonCopyBean(param, PointAccountEntity.class);
        record.setDomainCode(null);
        record.setTenantCode(null);
        record.setAccountType(null);
        record.setAccountCode(null);

        return this.pointAccountMapper.updateByExampleSelective(record, example);
    }

	@Override
	public PointAccountResult getPointAccount(GetPointAccountParam param) {

		PointAccountEntity pointAccount = getPointAccountModelByCode(param.getTenantCode(), param.getAccountType(), param.getAccountCode(),param.getCampaignCode());
		if (pointAccount == null) {
			return null;
		}
		PointAccountResult pointAccountResult = BeanCopyUtils.jsonCopyBean(pointAccount, PointAccountResult.class);
		getExpiringPoint(pointAccountResult, null);
		return pointAccountResult;
	}

	@Override
	public PageResult<PointAccountResult> queryPointAccountPage(Map<String, Object> map) {
		// 分页查询
		setPage(map);
		map.put("order", true);
		List<PointAccountEntity> pointAccountList = queryPointAccountModelPage(map);
		PageInfo<PointAccountEntity> pageInfo = new PageInfo<>(pointAccountList);
		List<PointAccountResult> pointAccountVoList = BeanCopyUtils.jsonCopyList(pageInfo.getList(), PointAccountResult.class);
		return new PageResult<>(pointAccountVoList, pageInfo.getTotal());
	}

    /**
     * 获取积分账户信息
     * 
     * @return PointAccount
     */
    private PointAccountEntity getPointAccountModelByCode(String tenantCode, Integer accountType, String accountCode,String campaignCode) {
        try {
            PointAccountEntity record = new PointAccountEntity();
            record.setTenantCode(tenantCode);
            record.setAccountType(accountType);
            record.setAccountCode(accountCode);
            record.setCampaignCode(campaignCode);
            return pointAccountMapper.selectOne(record);
        } catch (Exception e) {
            log.error(SYS_CODE + ".getPointAccountModelByCode", e);
        }
        return null;
    }

	/**
	 * 更新积分账户状态
	 *
	 * @param status
	 * @param oldStatus
	 */
	private int updateStatusPointAccountModel(String tenantCode, String pointAccountCode,String campaignCode, Integer status, Integer oldStatus) {

		if (StringUtils.isEmpty(pointAccountCode) || StringUtils.isEmpty(tenantCode))
			return 0;
		Map<String, Object> param = new HashMap<>();
		param.put("tenantCode", tenantCode);
		param.put(POINT_ACCOUNT_CODE, pointAccountCode);
		param.put("campaignCode",campaignCode);
		param.put("status", oldStatus);
		int i = 0;
		try {
			PointAccountEntity record = new PointAccountEntity();
			record.setStatus(status);
			Example example = new Example(PointAccountEntity.class);
			example.createCriteria().andAllEqualTo(param);
			i = pointAccountMapper.updateByExampleSelective(record, example);
		} catch (Exception e) {
			throw new PromotionException(SYS_CODE + ".updateStatusPointAccountModel.ex", e.getMessage());
		}
		if (i <= 0) {
			throw new PromotionException(SystemChecker.NULL_UPDATE.getCode(), SystemChecker.NULL_UPDATE.getMessage());
		}
		
		return i;
	}

	/**
	 * 分页查询积分账户
	 * 
	 * @param parammap
	 * @return PointAccount
	 */
	private List<PointAccountEntity> queryPointAccountModelPage(Map<String, Object> parammap) {
		List<PointAccountEntity> list = null;
		try {
			list = pointAccountMapper.query(parammap);
		} catch (Exception e) {
			log.error(SYS_CODE + ".queryPointAccountModel", e);
		}
		return list;
	}

	@SuppressWarnings("unchecked")
	@Override
	@Transactional
	public int updatePoint(UpdatePointParam pointChangeVo) {

		//判断有没有账户,如果没有自动添加一个账户


		GetPointAccountParam getPointAccountParam = BeanCopyUtils.jsonCopyBean(pointChangeVo, GetPointAccountParam.class);
		PointAccountResult pointAccount = getPointAccount(getPointAccountParam);

		if (null==pointAccount){
			CreatePointAccountParam createPointAccountParam = BeanCopyUtils.jsonCopyBean(pointChangeVo, CreatePointAccountParam.class);
			createPointAccountParam.setStatus(1);
			pointAccountTempService.savePointAccount(createPointAccountParam);
		}



		PointCampaignUniqueParam pointCampaignUniqueParam = BeanCopyUtils.jsonCopyBean(pointChangeVo, PointCampaignUniqueParam.class);

		PointCampaignResult pointCampaign = pointCampaignService.getPointCampaign(pointCampaignUniqueParam);


		//如果没有过期时间，那么过期时间就是积分池的过期时间
		if (StringUtils.isEmpty(pointChangeVo.getExpiration())){
			pointChangeVo.setExpiration(pointCampaign.getEndTime());
		}

		if (pointCampaign.getEndTime().compareTo(pointChangeVo.getExpiration())<0){
			throw new PromotionException(ErrorCodePrefix.POINT_TRANSACTION_CHECKER.getCodePrefix(), "Expiration GreaterThan The End Time Of The Points Pool");
		}

		// 更新积分池
		int count = pointCampaignService.updatePoint(pointChangeVo);
		if (count == 0) {
			throw new PromotionException(ErrorCodePrefix.POINT_CAMPAIGN_CHECKER.getCodePrefix(), "Insufficient remaining points in the pool or points plan disabled");
		}
		// 更新积分账户
		Map<String, Object> param = BeanCopyUtils.jsonCopyBean(pointChangeVo, Map.class);

		int countPoint = pointAccountMapper.updatePoint(param);
		if (countPoint == 0) {
			throw new PromotionException(ErrorCodePrefix.POINT_ACCOUNT_CHECKER.getCodePrefix(), "Insufficient remaining points");
		}
		// 新增积分流水
		PointTransactionParam pointTransactionVo = BeanCopyUtils.jsonCopyBean(pointChangeVo, PointTransactionParam.class);
		pointTransactionService.savePointTransaction(pointTransactionVo);

		return count;
	}

	@SuppressWarnings("unchecked")
	@Override
	@Transactional
	public int increaseOrDecreasePoint(UpdatePointParam updatePointParam) {
		// 更新积分账户
		Map<String, Object> param = BeanCopyUtils.jsonCopyBean(updatePointParam, Map.class);
		int countPoint = pointAccountMapper.updatePoint(param);
		if (countPoint == 0) {
			throw new PromotionException(ErrorCodePrefix.POINT_ACCOUNT_CHECKER.getCodePrefix(), "Insufficient remaining points");
		}
		// 新增积分流水
		PointTransactionParam pointTransactionVo = BeanCopyUtils.jsonCopyBean(updatePointParam, PointTransactionParam.class);
		pointTransactionService.savePointTransaction(pointTransactionVo);

		return countPoint;
	}

    @Override
    public PointAccountCampaignResult getPointAccountCampaign(GetPointAccountCampaignParam param) {
		PointAccountEntity pointAccount = getPointAccountModelByCode(param.getTenantCode(), param.getAccountType(), param.getAccountCode(),param.getAccountCode());
		if (pointAccount == null) {
			return null;
		}
		List<PointAccountCampaignResult.CampaignBalance> countPointTransactionCampaign = pointTransactionService.getCountPointTransactionCampaign(param.getTenantCode(), param.getAccountCode(), param.getCampaignCodes());
		PointAccountCampaignResult pointAccountCampaignResult = BeanCopyUtils.jsonCopyBean(pointAccount, PointAccountCampaignResult.class);
		pointAccountCampaignResult.setCampaignBalances(countPointTransactionCampaign);
		getExpiringPoint(pointAccountCampaignResult);
		return pointAccountCampaignResult;
    }

	private void getExpiringPoint(PointAccountCampaignResult pointAccountCampaignResult) {
		List<PointAccountCampaignResult.CampaignBalance> campaignBalances = pointAccountCampaignResult.getCampaignBalances();
		for (PointAccountCampaignResult.CampaignBalance campaignBalance : campaignBalances) {
			PointAccountResult pointAccountResult = BeanCopyUtils.jsonCopyBean(pointAccountCampaignResult, PointAccountResult.class);
			getExpiringPoint(pointAccountResult, campaignBalance.getCampaignCode());
			campaignBalance.setExpiringPoint(pointAccountResult.getExpiringPoint());
			if (null == pointAccountCampaignResult.getExpiringDays()){
				pointAccountCampaignResult.setExpiringDays(pointAccountResult.getExpiringDays());
			}
		}
	}

	@SuppressWarnings("unchecked")
	public void getExpiringPoint(PointAccountResult pointAccountResult, String campaignCode) {
		try {
			if (pointAccountResult == null)
				return;
			String expiringDate = calcExpiryDate(pointAccountResult.getTenantCode());
			Long expiringDays = DateUtil.getBetweenDays(DateUtil.getDateStr(DateUtil.DATESTOREFORMAT), expiringDate);
			if (expiringDays > 0) {
				Map<String, Object> param = BeanCopyUtils.jsonCopyBean(pointAccountResult, Map.class);
				param.put("campaignCode", campaignCode);
				Integer effectivePoint = countEffectivePoint(param, expiringDate);
				Integer accountBalance = pointAccountResult.getAccountBalance();
				if (effectivePoint != null && accountBalance != null && accountBalance > effectivePoint) {
					pointAccountResult.setExpiringPoint(accountBalance - effectivePoint);
				}
			}
			pointAccountResult.setExpiringDays(expiringDays);
		} catch (Exception e) {
			log.error(e.getMessage(), e);
		}
	}

	private Integer countEffectivePoint(Map<String, Object> param, String expiringDate) {
		Date expiring = DateUtil.isDate(expiringDate, DateUtil.DATESTOREFORMAT);
		String effectiveDate = DateUtil.getAfterCalendarType(DateUtil.getDateString(expiring, DateUtil.DATESHOWFORMAT), -1, 1, DateUtil.DATESTOREFORMAT);
		param.put("effectiveDate", Integer.parseInt(effectiveDate));
		return pointTransactionMapper.countEffectivePoint(param);
	}

	private String calcExpiryDate(String tenantCode) {
		Date nowDate = new Date();
		Integer year = DateUtil.getYear(nowDate);
		String expireTime = "";
		try {
			JsonResult<String> jsonResult = masterDataClient.getValueValue(tenantCode, "promotion_point_expire_time");
			expireTime = jsonResult.getData();
		} catch (Exception e) {
			log.error(e.getMessage());
		}
		if (StringUtil.isEmpty(expireTime)) {
			expireTime = "0101";// default expire time
		}
		log.info("tenantCode:{},expireTime:{}", tenantCode, expireTime);
		String expireDateStr = year + expireTime;
		long days = DateUtil.getBetweenDays(DateUtil.getDateStr(DateUtil.DATESTOREFORMAT), expireDateStr);
		if (days < 0) {
			expireDateStr = (year + 1) + expireTime;
		}
		return expireDateStr;
	}
}
