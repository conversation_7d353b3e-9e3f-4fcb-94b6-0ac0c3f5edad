package com.gtech.promotion.service.flashsale;

import com.gtech.promotion.dao.entity.marketing.flashsale.FlashSaleQualificationEntity;
import com.gtech.promotion.dao.model.marketing.flashsale.FlashSaleQualificationModel;
import com.gtech.promotion.service.marketing.BaseService;

import java.util.List;

public interface FlashSaleQualificationService extends BaseService<FlashSaleQualificationEntity, FlashSaleQualificationModel> {
    List<FlashSaleQualificationModel> getQualificationsByActivityCodes(String tenantCode, List<String> activityCodes);
}
