package com.gtech.promotion.service.activity;

import com.gtech.promotion.dao.model.activity.LuckyDrawRuleModel;
import com.gtech.promotion.dao.model.marketing.flashsale.FlashSaleProductModel;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021/1/26 9:56
 */
public interface LuckyDrawRuleService {

    /**
     * 创建抽奖规则
     * @param activityCode
     * @param ruleModels
     * @param tenantCode
     * @return
     */
    int createLuckyDrawRule(String activityCode, List<LuckyDrawRuleModel> ruleModels, String tenantCode);


    /**
     * 删除
     * @param activityCode
     * @return
     */
    int deleteLuckyDrawRuleByActivityCode(String activityCode);



    List<LuckyDrawRuleModel> queryLuckyDrawRulesByActivityCode(String tenantCode,String activityCode);
    List<LuckyDrawRuleModel> queryLuckyDrawRulesByActivityCode(String tenantCode,List<String> activityCodes);


    List<LuckyDrawRuleModel> queryLuckyDrawRulesByProductCode(String tenantCode,List<String> productCodes);

    List<FlashSaleProductModel> getLuckDrawProductsByActivityCodesAndProducts(String tenantCode, ArrayList<String> strings, List<String> skuCodeList);
}
