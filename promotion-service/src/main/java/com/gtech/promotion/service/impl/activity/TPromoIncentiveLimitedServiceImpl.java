/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.service.impl.activity;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import com.gtech.commons.utils.BeanCopyUtils;
import com.gtech.commons.utils.StringUtil;
import com.gtech.promotion.checker.activity.TPromoIncentiveLimitedChecker;
import com.gtech.promotion.code.activity.LimitationCodeEnum;
import com.gtech.promotion.dao.entity.activity.LuckyDrawIncentiveLimitedEntity;
import com.gtech.promotion.dao.entity.activity.TPromoIncentiveLimitedEntity;
import com.gtech.promotion.dao.mapper.activity.LuckyDrawIncentiveLimitedMapper;
import com.gtech.promotion.dao.mapper.activity.TPromoIncentiveLimitedMapper;
import com.gtech.promotion.dao.model.activity.TPromoIncentiveLimitedVO;
import com.gtech.promotion.exception.Check;
import com.gtech.promotion.service.activity.TPromoIncentiveLimitedService;

import tk.mybatis.mapper.entity.Example;

/**
 * <功能描述>
 */
@Service
public class TPromoIncentiveLimitedServiceImpl implements TPromoIncentiveLimitedService {

    @Autowired
    private TPromoIncentiveLimitedMapper limitedMappper;

    @Autowired
    private LuckyDrawIncentiveLimitedMapper luckyDrawIncentiveLimitedMapper;

    @Override
    @Transactional
    public Integer insertLimitedList111(String activityCode, List<TPromoIncentiveLimitedVO> limitedVOList, String tenantCode) {

        //存储limitionCode，验证唯一
        List<String> codeList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(limitedVOList)) {
            for (TPromoIncentiveLimitedVO limitedVO : limitedVOList) {
                String code = limitedVO.getLimitationCode();
                BigDecimal value = limitedVO.getLimitationValue();
                Check.check(value == null || value.compareTo(BigDecimal.ZERO) < 0, TPromoIncentiveLimitedChecker.NOT_NULL_LIMITATION_VALUE);
                Check.check(value.toString().length() > 16, TPromoIncentiveLimitedChecker.ERROR_LIMITED_SIZE);//NOSONAR
                Check.check(!LimitationCodeEnum.exist(code), TPromoIncentiveLimitedChecker.ERROR_LIMITED_CODE);
                Check.check(StringUtil.isBlank(code), TPromoIncentiveLimitedChecker.NOT_NULL_LIMITATION_CODE);
                Check.check(codeList.contains(code), TPromoIncentiveLimitedChecker.NOT_REPEAT_LIMITATION_CODE);
                codeList.add(code);
                //限制条件表
                limitedVO.setId(null);
                limitedVO.setActivityCode(activityCode);
                limitedVO.setTenantCode(tenantCode);

                limitedMappper.insertSelective(BeanCopyUtils.jsonCopyBean(limitedVO, TPromoIncentiveLimitedEntity.class));
            }
            return 1;
        }
        return 0;
    }

    @Override
    @Transactional
    public Integer insertLuckyDrawLimitedList(String activityCode, List<TPromoIncentiveLimitedVO> limitedVOList, String tenantCode) {
        //存储limitationCode，验证唯一
        List<String> codeList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(limitedVOList)) {
            for (TPromoIncentiveLimitedVO limitedVO : limitedVOList) {
                BigDecimal value = limitedVO.getLimitationValue();
                String code = limitedVO.getLimitationCode();
                Check.check(value == null || value.compareTo(BigDecimal.ZERO) < 0, TPromoIncentiveLimitedChecker.NOT_NULL_LIMITATION_VALUE);
                Check.check(value.toString().length() > 16, TPromoIncentiveLimitedChecker.ERROR_LIMITED_SIZE);//NOSONAR
                Check.check(StringUtil.isBlank(code), TPromoIncentiveLimitedChecker.NOT_NULL_LIMITATION_CODE);
                Check.check(!LimitationCodeEnum.exist(code), TPromoIncentiveLimitedChecker.ERROR_LIMITED_CODE);
                Check.check(codeList.contains(code), TPromoIncentiveLimitedChecker.NOT_REPEAT_LIMITATION_CODE);
                codeList.add(code);
                //限制条件表
                limitedVO.setId(null);
                limitedVO.setActivityCode(activityCode);
                limitedVO.setTenantCode(tenantCode);
                luckyDrawIncentiveLimitedMapper.insertSelective(BeanCopyUtils.jsonCopyBean(limitedVO, LuckyDrawIncentiveLimitedEntity.class));
            }
            return 1;
        }
        return 0;
    }

    @Override
    public List<TPromoIncentiveLimitedVO> getLimitedListByActivityCode(String activityCode) {

        TPromoIncentiveLimitedEntity entity = new TPromoIncentiveLimitedEntity();
        entity.setActivityCode(activityCode);
        return BeanCopyUtils.jsonCopyList(limitedMappper.select(entity), TPromoIncentiveLimitedVO.class);
    }

    @Override
    public List<TPromoIncentiveLimitedVO> getLuckyDrawLimitedListByActivityCode(String activityCode) {
        LuckyDrawIncentiveLimitedEntity entity = new LuckyDrawIncentiveLimitedEntity();
        entity.setActivityCode(activityCode);
        return BeanCopyUtils.jsonCopyList(luckyDrawIncentiveLimitedMapper.select(entity), TPromoIncentiveLimitedVO.class);
    }

    @Override
    @Transactional
    public Integer deleteLimitedByActivityCode(String activityCode) {

        TPromoIncentiveLimitedEntity entity = new TPromoIncentiveLimitedEntity();
        entity.setActivityCode(activityCode);
        return limitedMappper.delete(entity);
    }

    @Override
    @Transactional
    public Integer deleteLuckyDrawLimitedByActivityCode(String activityCode) {

        LuckyDrawIncentiveLimitedEntity entity = new LuckyDrawIncentiveLimitedEntity();
        entity.setActivityCode(activityCode);
        return luckyDrawIncentiveLimitedMapper.delete(entity);
    }

    @Override
    public List<TPromoIncentiveLimitedVO> getLimitedListByActivityCodes(String tenantCode, List<String> activityCodes) {
        Example example = new Example(TPromoIncentiveLimitedEntity.class);
        example.createCriteria().andEqualTo("tenantCode", tenantCode)
                .andIn("activityCode", activityCodes);

        return BeanCopyUtils.jsonCopyList(limitedMappper.selectByCondition(example), TPromoIncentiveLimitedVO.class);
    }

	@Override
	public Map<String, BigDecimal> getLimitedByActivityCodes(String tenantCode, List<String> activityCodes, String limitationCode) {
		Example example = new Example(TPromoIncentiveLimitedEntity.class);
        example.createCriteria().andEqualTo("tenantCode", tenantCode)
                .andIn("activityCode", activityCodes)
                .andEqualTo("limitationCode", limitationCode);
		return limitedMappper.selectByCondition(example).stream()
				.collect(Collectors.toMap(TPromoIncentiveLimitedEntity::getActivityCode, TPromoIncentiveLimitedEntity::getLimitationValue));
	}

}
