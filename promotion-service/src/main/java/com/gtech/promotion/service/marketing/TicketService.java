package com.gtech.promotion.service.marketing;

import com.gtech.commons.page.PageData;
import com.gtech.commons.page.PageParam;
import com.gtech.promotion.dao.entity.marketing.TicketEntity;
import com.gtech.promotion.dao.model.marketing.BaseModel;
import com.gtech.promotion.dao.model.marketing.TicketModel;
import com.gtech.promotion.domain.marketing.LuckyDrawMemberChanceDomain;
import com.gtech.promotion.vo.result.marketing.LuckyDrawMemberChanceResult;

import java.util.List;

public interface TicketService extends BaseService<TicketEntity, TicketModel> {
    List<TicketModel> selectListByMemberCode(BaseModel baseModel, String memberCode, String code);

    PageData<LuckyDrawMemberChanceResult> queryChanceList(LuckyDrawMemberChanceDomain domain);

    PageData<TicketModel> queryListByStatus(TicketModel ticketModel, PageParam pageParam);

    int updateByTicketCode(TicketModel ticketModel, String ticketCode);

    Integer selectCountByMemberCode(BaseModel baseModel, String memberCode, String beginTime, String endTime);

    int selectActivityByStatus(BaseModel ticketModel,String status);

    void updateFrozenStatus(LuckyDrawMemberChanceDomain domain);
}
