package com.gtech.promotion.service.marketing.impl;

import com.gtech.commons.utils.BeanCopyUtils;
import com.gtech.promotion.dao.entity.marketing.BaseEntity;
import com.gtech.promotion.dao.entity.marketing.MarketingLanguageEntity;
import com.gtech.promotion.dao.mapper.marketing.MarketingLanguageMapper;
import com.gtech.promotion.dao.model.marketing.MarketingLanguageModel;
import com.gtech.promotion.service.marketing.MarketingLanguageService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import java.util.List;

@Service
public class MarketingLanguageServiceImpl extends BaseServiceImpl<MarketingLanguageEntity, MarketingLanguageModel> implements MarketingLanguageService {

    public MarketingLanguageServiceImpl() {
        super(MarketingLanguageEntity.class, MarketingLanguageModel.class);
    }

    @Autowired
    private MarketingLanguageMapper languageMapper;

    @Override
    public MarketingLanguageModel findByLanguage(String activityCode, String language) {
        MarketingLanguageEntity entity = new MarketingLanguageEntity();
        entity.setActivityCode(activityCode);
        entity.setLanguage(language);
        MarketingLanguageEntity entity1 = languageMapper.selectOne(entity);
        return BeanCopyUtils.jsonCopyBean(entity1, MarketingLanguageModel.class);
    }

    @Override
    public List<MarketingLanguageModel> getLanguagesByActivityCodes(String tenantCode, List<String> activityCodes) {
        Example example = new Example(MarketingLanguageEntity.class);
        example.createCriteria().andEqualTo(BaseEntity.C_TENANT_CODE, tenantCode)
                .andIn(BaseEntity.C_ACTIVITY_CODE, activityCodes);
        return BeanCopyUtils.jsonCopyList(languageMapper.selectByCondition(example), MarketingLanguageModel.class);
    }
}
