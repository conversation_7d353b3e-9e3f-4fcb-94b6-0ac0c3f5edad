/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WA<PERSON><PERSON>NTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.service.activity;

import java.util.List;
import java.util.Map;

import com.gtech.promotion.dao.model.activity.TemplateModel;

/**
 * 规则模板服务
 *
 */
public interface TemplateService {

    /**
     * Query all template model info.
     */
    List<TemplateModel> queryTemplateAll();

    /**
     * Retrieve template model info by template code.
     */
    TemplateModel getTemplateByCode(String templateCode);

    /**
     * Retrieve template model info by template ID.
     */
    TemplateModel getTemplateById(String templateId);

    /**
     * Retrieve all template code and tag map.
     * 
     * @return <tagCode, [templateCode,templateCode,...]>
     */
    Map<String, String> findTemplateTagAll();
}
