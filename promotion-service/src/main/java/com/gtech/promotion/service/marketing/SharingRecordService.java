package com.gtech.promotion.service.marketing;

import com.gtech.promotion.dao.entity.marketing.SharingRecordEntity;
import com.gtech.promotion.dao.model.marketing.BoostSharingModel;
import com.gtech.promotion.dao.model.marketing.SharingRecordModel;
import com.gtech.promotion.dto.in.flashsale.HelpRecordDto;

import java.util.List;

public interface SharingRecordService extends BaseService<SharingRecordEntity, SharingRecordModel>{


    int updateSharingRecord(SharingRecordModel model);


    SharingRecordModel findBysharingRecordCode(String sharingRecordCode, String sharingMemberCode);

    List<SharingRecordEntity> querySharingRecord(SharingRecordModel model);

    int increaseTheNumberOfPeopleHelping(HelpRecordDto dto, BoostSharingModel boostSharingModel, SharingRecordModel sharingRecordModel);
}
