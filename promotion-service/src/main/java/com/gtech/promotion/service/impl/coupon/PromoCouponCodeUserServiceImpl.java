/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.service.impl.coupon;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Set;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.gtech.commons.code.LogicDeleteEnum;
import com.gtech.commons.code.LogicFlagEnum;
import com.gtech.commons.page.PageData;
import com.gtech.commons.utils.BeanCopyUtils;
import com.gtech.commons.utils.DateUtil;
import com.gtech.commons.utils.StringUtil;
import com.gtech.promotion.code.coupon.CouponFrozenStatusEnum;
import com.gtech.promotion.code.coupon.CouponStatusEnum;
import com.gtech.promotion.code.coupon.CouponTypeEnum;
import com.gtech.promotion.dao.entity.coupon.TPromoCouponCodeUserEntity;
import com.gtech.promotion.dao.entity.coupon.TPromoCouponInnerCodeEntity;
import com.gtech.promotion.dao.mapper.coupon.TPromoCouponCodeUserMapper;
import com.gtech.promotion.dao.model.coupon.CountCouponCodeModel;
import com.gtech.promotion.dao.model.coupon.QueryUserCouponVO;
import com.gtech.promotion.dao.model.coupon.TPromoCouponCodeUserVO;
import com.gtech.promotion.domain.coupon.CouponDomain;
import com.gtech.promotion.dto.in.activity.ActivityTenantInDTO;
import com.gtech.promotion.dto.in.activity.ManagementDataInDTO;
import com.gtech.promotion.dto.in.coupon.ExportCouponUserDto;
import com.gtech.promotion.dto.in.coupon.QueryCouponUsedInDTO;
import com.gtech.promotion.dto.in.coupon.TCouponCheckAndUseInDTO;
import com.gtech.promotion.page.RequestPage;
import com.gtech.promotion.service.coupon.PromoCouponCodeUserService;
import com.gtech.promotion.service.coupon.SqlPublicMethodsService;
import com.gtech.promotion.utils.Constants;
import com.gtech.promotion.utils.DateValidUtil;
import com.gtech.promotion.vo.result.coupon.ExportCouponUserDetailResult;
import com.gtech.promotion.vo.result.coupon.ExportCouponUserResult;

import tk.mybatis.mapper.entity.Example;
import tk.mybatis.mapper.entity.Example.Criteria;

/**
 * 用户优惠券接口
 */
@Service
public class PromoCouponCodeUserServiceImpl implements PromoCouponCodeUserService {

    @Autowired
    private TPromoCouponCodeUserMapper couponCodeUserDao;

    @Autowired
    private SqlPublicMethodsService sql;

    private static final String YESTERDAY = "yesterday";

    private static final String TODAY = "today";

    @Override
    @Transactional
    public int updateUsedRedIdByCodes(String tenantCode, List<String> couponCodes, String userCode, String usedRefId, CouponStatusEnum statusEnum) {

        if (!CollectionUtils.isEmpty(couponCodes)) {
            Example example = new Example(TPromoCouponCodeUserEntity.class);

            Example.Criteria criteria = example.createCriteria();

            criteria.andEqualTo(TPromoCouponCodeUserEntity.C_TENANT_CODE, tenantCode)
                .andEqualTo(TPromoCouponCodeUserEntity.C_USER_CODE, userCode);
            if (couponCodes.size() == 1) {
                criteria.andEqualTo(TPromoCouponCodeUserEntity.C_COUPON_CODE, couponCodes.get(0));
            } else {
                criteria.andIn(TPromoCouponCodeUserEntity.C_COUPON_CODE, couponCodes);
            }

            TPromoCouponCodeUserEntity entity = new TPromoCouponCodeUserEntity();
            entity.setUsedRefId(usedRefId);
            entity.setStatus(statusEnum.code());
            entity.setUsedTime(DateUtil.getCurrentDateAsString(DateUtil.FORMAT_YYYYMMDDHHMISS_14));
            return couponCodeUserDao.updateByConditionSelective(entity, example);
        }
        return 0;
    }

    @Override
    public List<TPromoCouponCodeUserVO> getCodeUserByUsedRefId(String usedRefId, String tenantCode) {

        Example example = new Example(TPromoCouponCodeUserEntity.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo(TPromoCouponCodeUserEntity.C_TENANT_CODE, tenantCode);
        criteria.andEqualTo(TPromoCouponCodeUserEntity.C_USED_REF_ID, usedRefId);
        List<TPromoCouponCodeUserEntity> list = couponCodeUserDao.selectByCondition(example);

        return BeanCopyUtils.jsonCopyList(list, TPromoCouponCodeUserVO.class);
    }

    @Override
    @Transactional
    public void clearUsedRefIdByUsedRefId(String usedRefId, String status) {

        Example example = new Example(TPromoCouponCodeUserEntity.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo(TPromoCouponCodeUserEntity.C_USED_REF_ID, usedRefId);
        TPromoCouponCodeUserEntity entity = new TPromoCouponCodeUserEntity();
        entity.setUsedRefId(Constants.DEFAULT_USED_REF_ID);
        entity.setUsedTime(null);//退单后将券使用时间也回退
        entity.setStatus(status);
        couponCodeUserDao.updateByConditionSelective(entity, example);
    }

    @Override
    public PageData<CouponDomain> getUserCouponByUserId(TPromoCouponCodeUserVO couponCodeUserVO, RequestPage requestPage,List<String> opsList,String beginTime,String endTime,Integer sort) {
        PageHelper.startPage(requestPage.getPageNo(), requestPage.getPageCount());
        Example example = new Example(TPromoCouponCodeUserEntity.class);
        Example.Criteria criteria = example.createCriteria();

        criteria
                .andEqualTo(TPromoCouponCodeUserEntity.C_TENANT_CODE, couponCodeUserVO.getTenantCode())
                .andEqualTo(TPromoCouponCodeUserEntity.C_USER_CODE, couponCodeUserVO.getUserCode())
                .andEqualTo(TPromoCouponCodeUserEntity.C_LOGIC_DELETE,0);
        String now = DateUtil.format(new Date(), DateUtil.FORMAT_YYYYMMDDHHMISS_14);
        if (StringUtil.isNotBlank(couponCodeUserVO.getStatus())) {

            if ("1".equals(couponCodeUserVO.getStatus())) { // 未使用,查询已发放状态，并且当前时间小于可用结束时间
                criteria.andEqualTo(TPromoCouponCodeUserEntity.C_STATUS, CouponStatusEnum.GRANTED.code());
                criteria.andGreaterThan(TPromoCouponCodeUserEntity.C_VALID_END_TIME, now);

            } else if ("2".equals(couponCodeUserVO.getStatus())) {// 已使用，查询已使用状态和已锁定状态
                List<String> statusList = new ArrayList<>(2);
                statusList.add(CouponStatusEnum.USED.code());
                statusList.add(CouponStatusEnum.LOCKED.code());
                criteria.andIn(TPromoCouponCodeUserEntity.C_STATUS, statusList);

            } else { // 已过期, 查询状态已过期  或者已冻结的券码且状态不能为锁定和使用
                Example.Criteria criteria1 = example.createCriteria();
                criteria.andNotIn(TPromoCouponCodeUserEntity.C_STATUS, Arrays.asList(CouponStatusEnum.USED.code(), CouponStatusEnum.LOCKED.code()));
                criteria1.andLessThanOrEqualTo(TPromoCouponCodeUserEntity.C_VALID_END_TIME, now);
                example.and(criteria1);
            }
        }
        if (StringUtils.isNotBlank(couponCodeUserVO.getTakeLabel())) {
            criteria.andEqualTo(TPromoCouponCodeUserEntity.C_TAKE_LABEL, couponCodeUserVO.getTakeLabel());
        }
        if (StringUtils.isNotBlank(beginTime)){
            criteria.andGreaterThanOrEqualTo(TPromoCouponCodeUserEntity.C_RECEIVED_TIME,beginTime);
        }
        if (StringUtils.isNotBlank(endTime)){
            criteria.andLessThanOrEqualTo(TPromoCouponCodeUserEntity.C_RECEIVED_TIME,endTime);
        }
        if (!CollectionUtils.isEmpty(opsList)){
            criteria.andIn(TPromoCouponCodeUserEntity.C_OPS_TYPE,opsList);
        }
        if (sort!=null && sort==1){
            example.orderBy(TPromoCouponCodeUserEntity.C_RECEIVED_TIME).desc();
        }
        example
            .orderBy(TPromoCouponCodeUserEntity.C_FROZEN_STATUS).asc()
            .orderBy(TPromoCouponCodeUserEntity.C_STATUS).asc()
            .orderBy(TPromoCouponCodeUserEntity.C_VALID_END_TIME).asc()
            .orderBy(TPromoCouponCodeUserEntity.C_ID).asc();

        List<TPromoCouponCodeUserEntity> list = couponCodeUserDao.selectByCondition(example);
        PageInfo<TPromoCouponCodeUserEntity> pageInfoEntity = new PageInfo<>(list);

        return new PageData<>(BeanCopyUtils.jsonCopyList(list, CouponDomain.class), pageInfoEntity.getTotal());
    }

    @Override
    public PageData<CouponDomain> queryUserCouponByUserId(TPromoCouponCodeUserVO couponCodeUserVO, QueryUserCouponVO queryUserCouponVO) {

        Example example = new Example(TPromoCouponCodeUserEntity.class);
        Example.Criteria criteria = example.createCriteria();

        criteria.andEqualTo(TPromoCouponCodeUserEntity.C_TENANT_CODE, couponCodeUserVO.getTenantCode())
                .andEqualTo(TPromoCouponCodeUserEntity.C_USER_CODE, couponCodeUserVO.getUserCode());

//        全部
//        排序逻辑：未使用优先展示（状态一致，按照到期时间倒序：先过期的先展示）；其次是已使用的；最后已过期的
//                其他状态
//        已使用：已使用和已锁定两个状态；优先展示已锁定的状态，其次是已使用；同状态下，按照使用和锁定时间正序排
//        已过期：按照过期时间倒序，新过期的优先展示
        setSql(criteria, couponCodeUserVO.getTakeLabel(), queryUserCouponVO.getBeginTime(), queryUserCouponVO.getEndTime(), queryUserCouponVO.getOpsList());
        if (queryUserCouponVO.getSort() != null && queryUserCouponVO.getSort() == 1) {
            example.orderBy(TPromoCouponCodeUserEntity.C_RECEIVED_TIME).desc();
        }

        if (StringUtil.isNotBlank(couponCodeUserVO.getStatus())) {
            // 未使用,查询已发放状态，并且当前时间小于可用结束时间
            if ("1".equals(couponCodeUserVO.getStatus())) {
                String now = DateUtil.format(new Date(), DateUtil.FORMAT_YYYYMMDDHHMISS_14);
                criteria.andEqualTo(TPromoCouponCodeUserEntity.C_STATUS, CouponStatusEnum.GRANTED.code())
                        .andEqualTo(TPromoCouponCodeUserEntity.C_FROZEN_STATUS,CouponFrozenStatusEnum.UN_FROZEN.code())
                        .andGreaterThan(TPromoCouponCodeUserEntity.C_VALID_END_TIME, now);
                example.orderBy(TPromoCouponCodeUserEntity.C_VALID_END_TIME).desc()
                        .orderBy(TPromoCouponCodeUserEntity.C_ID).asc();
            } else if ("2".equals(couponCodeUserVO.getStatus())) {
                // 已使用，查询已使用状态和已锁定状态
                List<String> statusList = new ArrayList<>(2);
                statusList.add(CouponStatusEnum.LOCKED.code());
                statusList.add(CouponStatusEnum.USED.code());
                criteria.andIn(TPromoCouponCodeUserEntity.C_STATUS, statusList);
                example.orderBy(TPromoCouponCodeUserEntity.C_STATUS).desc()
                        .orderBy(TPromoCouponCodeUserEntity.C_USED_TIME).asc()
                        .orderBy(TPromoCouponCodeUserEntity.C_ID).asc();
            } else {
                criteria.andEqualTo(TPromoCouponCodeUserEntity.C_STATUS, CouponStatusEnum.EXPIRE.code())
                        .andEqualTo(TPromoCouponCodeUserEntity.C_FROZEN_STATUS, CouponFrozenStatusEnum.UN_FROZEN.code());
                // 已过期 包含冻结的
                Example.Criteria criteria1 = example.createCriteria();
                criteria1.andEqualTo(TPromoCouponCodeUserEntity.C_TENANT_CODE, couponCodeUserVO.getTenantCode())
                        .andEqualTo(TPromoCouponCodeUserEntity.C_USER_CODE, couponCodeUserVO.getUserCode());
                setSql(criteria1, couponCodeUserVO.getTakeLabel(), queryUserCouponVO.getBeginTime(), queryUserCouponVO.getEndTime(), queryUserCouponVO.getOpsList());

                criteria1.andEqualTo(TPromoCouponCodeUserEntity.C_TENANT_CODE, couponCodeUserVO.getTenantCode())
                        .andEqualTo(TPromoCouponCodeUserEntity.C_USER_CODE, couponCodeUserVO.getUserCode());
                criteria1.andEqualTo(TPromoCouponCodeUserEntity.C_STATUS, CouponStatusEnum.GRANTED.code())
                        .andEqualTo(TPromoCouponCodeUserEntity.C_FROZEN_STATUS, CouponFrozenStatusEnum.FROZENED.code());
                example.or(criteria1);
                example.orderBy(TPromoCouponCodeUserEntity.C_VALID_END_TIME).desc()
                        .orderBy(TPromoCouponCodeUserEntity.C_STATUS).desc()
                        .orderBy(TPromoCouponCodeUserEntity.C_ID).asc();
            }
        } else {
            example.orderBy(TPromoCouponCodeUserEntity.C_STATUS).asc()
                    .orderBy(TPromoCouponCodeUserEntity.C_VALID_END_TIME).desc()
                    .orderBy(TPromoCouponCodeUserEntity.C_ID).asc();
        }

        PageHelper.startPage(queryUserCouponVO.getPageNo(), queryUserCouponVO.getPageCount());
        List<TPromoCouponCodeUserEntity> list = couponCodeUserDao.selectByCondition(example);
        PageInfo<TPromoCouponCodeUserEntity> pageInfoEntity = new PageInfo<>(list);

        return new PageData<>(BeanCopyUtils.jsonCopyList(list, CouponDomain.class), pageInfoEntity.getTotal());
    }

    public Criteria setSql(Example.Criteria criteria,String takeLabel,String beginTime,String endTime,List<String> opsList){

        if (!CollectionUtils.isEmpty(opsList)){
            criteria.andIn(TPromoCouponCodeUserEntity.C_OPS_TYPE,opsList);
        }

        if (StringUtils.isNotBlank(beginTime)){
            criteria.andGreaterThanOrEqualTo(TPromoCouponCodeUserEntity.C_RECEIVED_TIME,beginTime);
        }

        if (StringUtils.isNotBlank(takeLabel)) {
            criteria.andEqualTo(TPromoCouponCodeUserEntity.C_TAKE_LABEL, takeLabel);
        }

        if (StringUtils.isNotBlank(endTime)){
            criteria.andLessThanOrEqualTo(TPromoCouponCodeUserEntity.C_RECEIVED_TIME,endTime);
        }

        return criteria;


    }
    @Override
    public int updateStatusBatch(String tenantCode, List<String> couponCodes, CouponStatusEnum statusEnum) {

        TPromoCouponCodeUserEntity entity = new TPromoCouponCodeUserEntity();
        entity.setStatus(statusEnum.code());
        Example example = new Example(TPromoCouponCodeUserEntity.class);
        example.createCriteria().andEqualTo(TPromoCouponCodeUserEntity.C_TENANT_CODE, tenantCode)
                .andIn(TPromoCouponCodeUserEntity.C_COUPON_CODE, couponCodes);
        return couponCodeUserDao.updateByConditionSelective(entity, example);
    }

    @Override
    public TPromoCouponCodeUserVO getUserCouponInfo(String tenantCode, String couponCode, String userCode) {

        TPromoCouponCodeUserEntity entity = new TPromoCouponCodeUserEntity();
        entity.setTenantCode(tenantCode);
        entity.setCouponCode(couponCode);
        if (StringUtil.isNotBlank(userCode)) {
            entity.setUserCode(userCode);
        }
        return BeanCopyUtils.jsonCopyBean(couponCodeUserDao.selectOne(entity), TPromoCouponCodeUserVO.class);
    }

    @Override
    public int getUserCouponCountByActivityCode(String tenantCode, String activityCode, String userCode) {

        TPromoCouponCodeUserEntity entity = new TPromoCouponCodeUserEntity();

        entity.setTenantCode(tenantCode);
        entity.setActivityCode(activityCode);
        entity.setUserCode(userCode);

        return couponCodeUserDao.selectCount(entity);
    }

    @Override
    public int getUserUsedCouponCountByActivityCode(String tenantCode, String userCode, String activityCode) {

        Example example = new Example(TPromoCouponCodeUserEntity.class);
        example.createCriteria()
            .andEqualTo(TPromoCouponCodeUserEntity.C_TENANT_CODE, tenantCode)
                .andEqualTo(TPromoCouponCodeUserEntity.C_USER_CODE, userCode)
                .andEqualTo(TPromoCouponCodeUserEntity.C_ACTIVITY_CODE, activityCode)
            .andNotEqualTo(TPromoCouponCodeUserEntity.C_USED_REF_ID, Constants.DEFAULT_USED_REF_ID);
        return couponCodeUserDao.selectCountByCondition(example);
    }

    @Override
    @Transactional
    public int insert(List<TPromoCouponCodeUserVO> couponCodeUserList) {

        if (CollectionUtils.isEmpty(couponCodeUserList)) {
            return 0;
        }

        if (couponCodeUserList.size() == 1) {
            TPromoCouponCodeUserEntity entity = BeanCopyUtils.jsonCopyBean(couponCodeUserList.get(0), TPromoCouponCodeUserEntity.class);
            entity.setLogicDelete(LogicDeleteEnum.NORMAL.number());
            return couponCodeUserDao.insertSelective(entity);
        } else {
            List<TPromoCouponCodeUserEntity> entityList = new ArrayList<>(100);
            int result = 0;
            for (TPromoCouponCodeUserVO e : couponCodeUserList) {
                TPromoCouponCodeUserEntity entity = BeanCopyUtils.jsonCopyBean(e, TPromoCouponCodeUserEntity.class);
                entity.setLogicDelete(LogicFlagEnum.FALSE.number());
                Date now = DateUtil.now();
                entity.setCreateTime(now);
                entity.setUpdateTime(now);
                entityList.add(entity);
                if (entityList.size() == 100) {
                    result += this.couponCodeUserDao.insertList(entityList);
                    entityList.clear();
                }
            }
            if (!CollectionUtils.isEmpty(entityList)) {
                result += couponCodeUserDao.insertList(entityList);
            }
            return result;
        }
    }

    @Override
    @Transactional
    public int updatePromotionCode(String tenantCode, String promotionCodeOld, String promotionCodeNew) {

        TPromoCouponCodeUserEntity entity = new TPromoCouponCodeUserEntity();
        entity.setCouponCode(promotionCodeNew);
        Example example = new Example(TPromoCouponCodeUserEntity.class);
        example.createCriteria()
            .andEqualTo(TPromoCouponCodeUserEntity.C_TENANT_CODE, tenantCode)
            .andEqualTo(TPromoCouponCodeUserEntity.C_COUPON_CODE, promotionCodeOld);
        return couponCodeUserDao.updateByConditionSelective(entity, example);
    }

    @Override
    public int updateCouponEndTime(String tenantCode, String activityCode, String releaseCode, String endTime) {
        TPromoCouponCodeUserEntity entity = new TPromoCouponCodeUserEntity();
        entity.setValidEndTime(endTime);
        Example example = new Example(TPromoCouponCodeUserEntity.class);
        example.createCriteria()
                .andEqualTo(TPromoCouponCodeUserEntity.C_TENANT_CODE, tenantCode)
                .andEqualTo(TPromoCouponCodeUserEntity.C_ACTIVITY_CODE, activityCode)
                .andEqualTo(TPromoCouponCodeUserEntity.C_RELEASE_CODE, releaseCode);
        return couponCodeUserDao.updateByConditionSelective(entity, example);
    }

    @Override
    public int expireCouponCode() {
        TPromoCouponCodeUserEntity entity = new TPromoCouponCodeUserEntity();
        entity.setStatus(CouponStatusEnum.EXPIRE.code());
        String now = DateUtil.format(new Date(), DateUtil.FORMAT_YYYYMMDDHHMISS_14);

        Example example = new Example(TPromoCouponCodeUserEntity.class);
        example.createCriteria()
                .andEqualTo(TPromoCouponCodeUserEntity.C_STATUS, CouponStatusEnum.GRANTED.code())
                .andLessThanOrEqualTo(TPromoCouponCodeUserEntity.C_VALID_END_TIME, now);
        return couponCodeUserDao.updateByConditionSelective(entity, example);
    }

    @Override
    public int findCouponUserByActivityCode(String tenantCode, String activityCode) {
        Example example = new Example(TPromoCouponCodeUserEntity.class);
        example.createCriteria()
                .andEqualTo("tenantCode",tenantCode)
                .andEqualTo("activityCode",activityCode)
                .andNotEqualTo("usedRefId","DEFAULT");
        return couponCodeUserDao.selectCountByCondition(example);
    }

    @Override
    public List<ExportCouponUserResult> exportCouponUserCode(ExportCouponUserDto dto) {


        return couponCodeUserDao.exportCouponUserCode(dto);
    }

    @Override
    public List<ExportCouponUserDetailResult> exportCouponOrderCode(ExportCouponUserDto dto) {


        return couponCodeUserDao.exportCouponOrderCode(dto);
    }

    @Override
    @Transactional
    public int deleteCodeUserByUserId(String userId) {

        TPromoCouponCodeUserEntity entity = new TPromoCouponCodeUserEntity();
        entity.setId(Long.valueOf(userId));
        return couponCodeUserDao.deleteByPrimaryKey(entity);
    }

    @Override
    public int countByReleaseCode(String tenantCode, String activityCode, String releaseCode) {

        TPromoCouponCodeUserEntity entity = new TPromoCouponCodeUserEntity();

        entity.setTenantCode(tenantCode);
        entity.setActivityCode(activityCode);
        entity.setReleaseCode(releaseCode);

        return couponCodeUserDao.selectCount(entity);
    }

    @Override
    public List<TPromoCouponCodeUserVO> getMyCodesByActivityCodes(String userCode, String tenantCode, Set<String> activityCodes,List<String> opsTypeList) {

        Example example = new Example(TPromoCouponCodeUserEntity.class);
        String now = DateValidUtil.getNowFormatDate();
        Example.Criteria criteria = example.createCriteria();
        criteria
                .andEqualTo(TPromoCouponCodeUserEntity.C_TENANT_CODE, tenantCode)
                .andEqualTo(TPromoCouponCodeUserEntity.C_USER_CODE, userCode)
            .andIn(TPromoCouponCodeUserEntity.C_ACTIVITY_CODE, activityCodes)
            .andLessThanOrEqualTo(TPromoCouponCodeUserEntity.C_VALID_START_TIME, now)
            .andGreaterThanOrEqualTo(TPromoCouponCodeUserEntity.C_VALID_END_TIME, now)
            .andEqualTo(TPromoCouponCodeUserEntity.C_FROZEN_STATUS, CouponFrozenStatusEnum.UN_FROZEN.code())
            .andEqualTo(TPromoCouponCodeUserEntity.C_STATUS, CouponStatusEnum.GRANTED.code());
        if (!CollectionUtils.isEmpty(opsTypeList)){
            criteria.andIn(TPromoCouponCodeUserEntity.C_OPS_TYPE,opsTypeList);
        }

        return BeanCopyUtils.jsonCopyList(couponCodeUserDao.selectByCondition(example), TPromoCouponCodeUserVO.class);
    }

    @Override
    public PageInfo<TPromoCouponCodeUserVO> getUserCouponCode(String tenantCode, String activityCode, String status, String couponCode, RequestPage page) {

        TPromoCouponCodeUserEntity entity = new TPromoCouponCodeUserEntity();
        entity.setTenantCode(tenantCode);
        entity.setActivityCode(activityCode);
        if (StringUtil.isNotBlank(status)) {
            entity.setStatus(status);
        }
        if (StringUtil.isNotBlank(couponCode)) {
            entity.setCouponCode(couponCode);
        }
        PageHelper.startPage(page.getPageNo(), page.getPageCount());
        List<TPromoCouponCodeUserEntity> list = couponCodeUserDao.select(entity);
        List<TPromoCouponCodeUserVO> list2 = BeanCopyUtils.jsonCopyList(list, TPromoCouponCodeUserVO.class);
        PageInfo<TPromoCouponCodeUserEntity> pageInfo = new PageInfo<>(list);
        PageInfo<TPromoCouponCodeUserVO> pageInfo2 = new PageInfo<>();
        pageInfo2.setList(list2);
        pageInfo2.setTotal(pageInfo.getTotal());
        return pageInfo2;
    }

    /**
     * 更新通用优惠码的状态
     * 
     * @param usedRefId -- order number(Optional)
     */
    @Override
    @Transactional
    public int updateCouponStatus(String tenantCode, String activityCode, String couponCode, CouponStatusEnum statusEnum, String usedRefId) {

        TPromoCouponCodeUserEntity entity = new TPromoCouponCodeUserEntity();
        entity.setStatus(statusEnum.code());

        Example example = new Example(TPromoCouponCodeUserEntity.class);
        Criteria criteria = example.createCriteria();

        criteria.andEqualTo(TPromoCouponCodeUserEntity.C_TENANT_CODE, tenantCode);
        criteria.andEqualTo(TPromoCouponCodeUserEntity.C_ACTIVITY_CODE, activityCode);
        criteria.andEqualTo(TPromoCouponCodeUserEntity.C_COUPON_CODE, couponCode);

        if (StringUtils.isNotBlank(usedRefId)) {
            criteria.andEqualTo(TPromoCouponCodeUserEntity.C_USED_REF_ID, usedRefId);
        }
        if (CouponStatusEnum.EXPIRE.equals(statusEnum)) {
            criteria.andEqualTo(TPromoCouponCodeUserEntity.C_STATUS, CouponStatusEnum.GRANTED.code());
        }

        return couponCodeUserDao.updateByConditionSelective(entity, example);
    }

    /**
     * 根据activityCode & releaseCode, 更新对应的券的状态为过期状态
     * 
     * @param releaseCode -- Coupon release code. (Optional)
     */
    @Override
    @Transactional
    public int expireByActivityCode(String tenantCode, String activityCode, String releaseCode) {

        if (StringUtils.isBlank(tenantCode) || StringUtils.isBlank(activityCode)) {
            return 0;
        }

        TPromoCouponCodeUserEntity entity = new TPromoCouponCodeUserEntity();
        entity.setStatus(CouponStatusEnum.EXPIRE.code());

        Example example = new Example(TPromoCouponInnerCodeEntity.class);
        Criteria criteria = example.createCriteria();

        criteria.andEqualTo(TPromoCouponCodeUserEntity.C_TENANT_CODE, tenantCode);
        criteria.andEqualTo(TPromoCouponCodeUserEntity.C_ACTIVITY_CODE, activityCode);
        criteria.andEqualTo(TPromoCouponCodeUserEntity.C_STATUS, CouponStatusEnum.GRANTED.code());

        if (StringUtils.isNotBlank(releaseCode)) {
            criteria.andEqualTo(TPromoCouponCodeUserEntity.C_RELEASE_CODE, releaseCode);
        }

        return couponCodeUserDao.updateByConditionSelective(entity, example);
    }

    @Override
    @Transactional
    public int updateCouponUserFrozenStatus(String tenantCode, String activityCode) {

        TPromoCouponCodeUserEntity entity = new TPromoCouponCodeUserEntity();
        entity.setFrozenStatus(CouponFrozenStatusEnum.FROZENED.code());

        Example example = new Example(TPromoCouponCodeUserEntity.class);
        example.createCriteria()
            .andEqualTo(TPromoCouponCodeUserEntity.C_TENANT_CODE, tenantCode)
            .andEqualTo(TPromoCouponCodeUserEntity.C_ACTIVITY_CODE, activityCode);

        return couponCodeUserDao.updateByConditionSelective(entity, example);
    }

    @Override
    public int logicDelete(String tenantCode, String activityCode) {
        TPromoCouponCodeUserEntity entity = new TPromoCouponCodeUserEntity();
        entity.setFrozenStatus(CouponFrozenStatusEnum.FROZENED.code());
        entity.setLogicDelete(1);
        Example example = new Example(TPromoCouponCodeUserEntity.class);
        example.createCriteria()
                .andEqualTo(TPromoCouponCodeUserEntity.C_TENANT_CODE, tenantCode)
                .andEqualTo(TPromoCouponCodeUserEntity.C_ACTIVITY_CODE, activityCode);

        return couponCodeUserDao.updateByConditionSelective(entity, example);
    }

    @Override
    public List<CountCouponCodeModel> countCouponCode(String tenantCode, String activityCode, String releaseCode) {

        return couponCodeUserDao.countCouponCode(tenantCode, activityCode, releaseCode);
    }

    @Override
    public List<TPromoCouponCodeUserVO> getUserCouponInfos(String tenantCode, String activityCode, String couponCode, String status) {

        Example example = new Example(TPromoCouponCodeUserEntity.class);
        Criteria criteria = example.createCriteria();
        criteria
            .andEqualTo(TPromoCouponCodeUserEntity.C_TENANT_CODE, tenantCode)
            .andEqualTo(TPromoCouponCodeUserEntity.C_ACTIVITY_CODE, activityCode);
        if (StringUtil.isNotBlank(status)) {
            criteria.andEqualTo(TPromoCouponCodeUserEntity.C_STATUS, status);
        }
        if (StringUtil.isNotBlank(couponCode)) {
            criteria.andEqualTo(TPromoCouponCodeUserEntity.C_COUPON_CODE, couponCode);
        }
        List<TPromoCouponCodeUserEntity> list = couponCodeUserDao.selectByCondition(example);
        return BeanCopyUtils.jsonCopyList(list, TPromoCouponCodeUserVO.class);
    }

    @Override
    public List<TPromoCouponCodeUserVO> getCouponCodeUserVO(String activityCode, String tenantCode, String releaseCode) {

        Example example = new Example(TPromoCouponCodeUserEntity.class);
        Criteria criteria = example.createCriteria();
        criteria
            .andEqualTo(TPromoCouponCodeUserEntity.C_TENANT_CODE, tenantCode)
            .andEqualTo(TPromoCouponCodeUserEntity.C_ACTIVITY_CODE, activityCode)
            .andEqualTo(TPromoCouponCodeUserEntity.C_RELEASE_CODE, releaseCode);
        List<TPromoCouponCodeUserEntity> list = couponCodeUserDao.selectByCondition(example);
        return BeanCopyUtils.jsonCopyList(list, TPromoCouponCodeUserVO.class);
    }

    @Override
    @Transactional
    public int frozenCouponCodeUser(String tenantCode, String couponCode,Integer frozenStatus,Integer logicDelete) {

        TPromoCouponCodeUserEntity entity = new TPromoCouponCodeUserEntity();
        if (frozenStatus==2){
            entity.setFrozenStatus(CouponFrozenStatusEnum.FROZENED.code());
        }else {
            entity.setFrozenStatus(CouponFrozenStatusEnum.UN_FROZEN.code());
        }
        //如果logicDelete为1，则为发错券
        if (logicDelete!=null && logicDelete==1){
            entity.setLogicDelete(1);
        }
        Example example = new Example(TPromoCouponInnerCodeEntity.class);
        example.createCriteria()
            .andEqualTo(TPromoCouponCodeUserEntity.C_TENANT_CODE, tenantCode)
            .andEqualTo(TPromoCouponCodeUserEntity.C_COUPON_CODE, couponCode);
        return couponCodeUserDao.updateByConditionSelective(entity, example);
    }

    @Override
    public Integer getAllocateCouponCountYesterday111(String tenantCode, String activityCode) {

        JSONObject jsonObject = getJsonObject();

        Example example = new Example(TPromoCouponCodeUserEntity.class);

        example.createCriteria()
            .andEqualTo(TPromoCouponCodeUserEntity.C_TENANT_CODE, tenantCode)
            .andEqualTo(TPromoCouponCodeUserEntity.C_ACTIVITY_CODE, activityCode)
            .andGreaterThanOrEqualTo(TPromoCouponCodeUserEntity.C_RECEIVED_TIME, jsonObject.get(YESTERDAY))
            .andLessThan(TPromoCouponCodeUserEntity.C_RECEIVED_TIME, jsonObject.get(TODAY));

        return couponCodeUserDao.selectCountByCondition(example);
    }

    @Override
    public Integer getUseCouponCountYesterday111(String tenantCode, String activityCode) {

        JSONObject jsonObject = getJsonObject();

        Example example = new Example(TPromoCouponCodeUserEntity.class);
        example.createCriteria()
            .andEqualTo(TPromoCouponCodeUserEntity.C_TENANT_CODE, tenantCode)
            .andEqualTo(TPromoCouponCodeUserEntity.C_ACTIVITY_CODE, activityCode)
            .andEqualTo(TPromoCouponCodeUserEntity.C_STATUS, CouponStatusEnum.USED.code())
            .andGreaterThanOrEqualTo(TPromoCouponCodeUserEntity.C_UPDATE_TIME, DateUtil.parseDate((String) jsonObject.get(YESTERDAY), DateUtil.FORMAT_YYYYMMDDHHMISS_14))
            .andLessThan(TPromoCouponCodeUserEntity.C_UPDATE_TIME, DateUtil.parseDate((String) jsonObject.get(TODAY), DateUtil.FORMAT_YYYYMMDDHHMISS_14));

        return couponCodeUserDao.selectCountByCondition(example);
    }

    @Override
    public Integer getAllocateCouponCountToday111(String tenantCode, String activityCode) {

        JSONObject jsonObject = getJsonObject();

        Example example = new Example(TPromoCouponCodeUserEntity.class);

        example.createCriteria()
            .andEqualTo(TPromoCouponCodeUserEntity.C_TENANT_CODE, tenantCode)
            .andEqualTo(TPromoCouponCodeUserEntity.C_ACTIVITY_CODE, activityCode)
            .andGreaterThanOrEqualTo(TPromoCouponCodeUserEntity.C_RECEIVED_TIME, jsonObject.get(TODAY));

        return couponCodeUserDao.selectCountByCondition(example);
    }

    @Override
    public Integer getUseCouponCountToday111(String tenantCode, String activityCode) {

        JSONObject jsonObject = getJsonObject();

        Example example = new Example(TPromoCouponCodeUserEntity.class);
        example.createCriteria()
            .andEqualTo(TPromoCouponCodeUserEntity.C_TENANT_CODE, tenantCode)
            .andEqualTo(TPromoCouponCodeUserEntity.C_ACTIVITY_CODE, activityCode)
            .andEqualTo(TPromoCouponCodeUserEntity.C_STATUS, CouponStatusEnum.USED.code())
            .andGreaterThanOrEqualTo(TPromoCouponCodeUserEntity.C_UPDATE_TIME, DateUtil.parseDate((String) jsonObject.get(TODAY), DateUtil.FORMAT_YYYYMMDDHHMISS_14));

        return couponCodeUserDao.selectCountByCondition(example);
    }

    private JSONObject getJsonObject() {

        Calendar calendar = Calendar.getInstance();

        calendar.setTime(new Date());
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        String today = DateUtil.format(calendar.getTime(), DateUtil.FORMAT_YYYYMMDDHHMISS_14);
        calendar.add(Calendar.DATE, -1);
        String yesterday = DateUtil.format(calendar.getTime(), DateUtil.FORMAT_YYYYMMDDHHMISS_14);
        JSONObject jsonObject = new JSONObject();
        jsonObject.put(YESTERDAY, yesterday);
        jsonObject.put(TODAY, today);

        return jsonObject;
    }

    @Override
    public long usedCouponAmount(List<String> tenantCodes) {

        Example example = new Example(TPromoCouponCodeUserEntity.class);
        Criteria criteria = example.createCriteria();
        criteria
            .andEqualTo(TPromoCouponCodeUserEntity.C_COUPON_TYPE, CouponTypeEnum.PROMOTION_COUPON.code())
            .andEqualTo(TPromoCouponCodeUserEntity.C_STATUS, CouponStatusEnum.USED.code());
        if (!CollectionUtils.isEmpty(tenantCodes)) {
            criteria.andNotIn(TPromoCouponCodeUserEntity.C_TENANT_CODE, tenantCodes);
        }
        return couponCodeUserDao.selectCountByCondition(example);
    }

    @Override
    public long usedCouponAmount(String startTime, String endTime, List<String> tenantCodes) {

        Example example = new Example(TPromoCouponCodeUserEntity.class);
        Criteria criteria = example.createCriteria();
        criteria.andEqualTo(TPromoCouponCodeUserEntity.C_COUPON_TYPE, CouponTypeEnum.PROMOTION_COUPON.code())
            .andEqualTo(TPromoCouponCodeUserEntity.C_STATUS, CouponStatusEnum.USED.code());
        if (StringUtil.isNotBlank(startTime)) {
            criteria.andGreaterThanOrEqualTo(TPromoCouponCodeUserEntity.C_USED_TIME, startTime);
        }
        if (StringUtil.isNotBlank(endTime)) {
            criteria.andLessThanOrEqualTo(TPromoCouponCodeUserEntity.C_USED_TIME, endTime);
        }
        if (!CollectionUtils.isEmpty(tenantCodes)) {
            criteria.andNotIn(TPromoCouponCodeUserEntity.C_TENANT_CODE, tenantCodes);
        }
        return couponCodeUserDao.selectCountByCondition(example);
    }

    @Override
    public int getReceivedCouponAmount(ActivityTenantInDTO tenantInDTO) {

        Example example = new Example(TPromoCouponCodeUserEntity.class);
        Criteria createCriteria = example.createCriteria();
        createCriteria.andEqualTo(TPromoCouponCodeUserEntity.C_TENANT_CODE, tenantInDTO.getTenantCode());
        if (StringUtil.isNotBlank(tenantInDTO.getStartTime()) && StringUtil.isNotBlank(tenantInDTO.getEndTime())) {
            createCriteria
                .andGreaterThanOrEqualTo(TPromoCouponCodeUserEntity.C_RECEIVED_TIME, tenantInDTO.getStartTime())
                .andLessThanOrEqualTo(TPromoCouponCodeUserEntity.C_RECEIVED_TIME, tenantInDTO.getEndTime());
        }
        return couponCodeUserDao.selectCountByCondition(example);
    }

    @Override
    public int getUsedCouponAmount(ActivityTenantInDTO tenantInDTO) {

        Example example = new Example(TPromoCouponCodeUserEntity.class);
        Criteria createCriteria = example.createCriteria();
        createCriteria.andEqualTo(TPromoCouponCodeUserEntity.C_TENANT_CODE, tenantInDTO.getTenantCode())
            .andEqualTo(TPromoCouponCodeUserEntity.C_STATUS, CouponStatusEnum.USED.code());
        if (StringUtil.isNotBlank(tenantInDTO.getStartTime()) && StringUtil.isNotBlank(tenantInDTO.getEndTime())) {
            createCriteria.andGreaterThanOrEqualTo(TPromoCouponCodeUserEntity.C_USED_TIME, tenantInDTO.getStartTime())
                .andLessThanOrEqualTo(TPromoCouponCodeUserEntity.C_USED_TIME, tenantInDTO.getEndTime());
        }
        return couponCodeUserDao.selectCountByCondition(example);
    }

    @Override
    public TPromoCouponCodeUserVO getUserCouponCode(String tenantCode, String couponCode) {

        return BeanCopyUtils.jsonCopyBean(couponCodeUserDao.selectOne(TPromoCouponCodeUserEntity.builder().tenantCode(tenantCode).couponCode(couponCode).build()),
            TPromoCouponCodeUserVO.class);
    }

    private PageData<TPromoCouponCodeUserVO> getPageInfo(ManagementDataInDTO inDTO, Example example) {

        PageHelper.startPage(inDTO.getPageNo(), inDTO.getPageCount());
        List<TPromoCouponCodeUserEntity> list = couponCodeUserDao.selectByCondition(example);
        List<TPromoCouponCodeUserVO> vos = BeanCopyUtils.jsonCopyList(list, TPromoCouponCodeUserVO.class);
        PageInfo<TPromoCouponCodeUserEntity> pageInfo = new PageInfo<>(list);

        return new PageData<>(vos, pageInfo.getTotal());
    }

    @Override
    @Transactional
    public int updateUserByCouponCode(TCouponCheckAndUseInDTO checkAndUseInDTO) {

        String nowTime = DateUtil.format(new Date(), DateUtil.FORMAT_YYYYMMDDHHMISS_14);
        TPromoCouponCodeUserEntity entity = new TPromoCouponCodeUserEntity();
        entity.setUsedRefId(checkAndUseInDTO.getUsedRefId());
        entity.setStatus(CouponStatusEnum.USED.code());
        entity.setUsedTime(nowTime);
        Example example = new Example(TPromoCouponCodeUserEntity.class);
        example.createCriteria()
            .andEqualTo(TPromoCouponCodeUserEntity.C_TENANT_CODE, checkAndUseInDTO.getTenantCode())
            .andEqualTo(TPromoCouponCodeUserEntity.C_COUPON_CODE, checkAndUseInDTO.getCouponCode());
        return couponCodeUserDao.updateByConditionSelective(entity, example);
    }

    @Override
    public TPromoCouponCodeUserVO findUserByCouponCode(TCouponCheckAndUseInDTO checkAndUseInDTO) {

        return BeanCopyUtils.jsonCopyBean(couponCodeUserDao.selectOne(TPromoCouponCodeUserEntity.builder()
            .tenantCode(checkAndUseInDTO.getTenantCode())
            .userCode(checkAndUseInDTO.getUserCode())
            .couponCode(checkAndUseInDTO.getCouponCode()).build()),
            TPromoCouponCodeUserVO.class);
    }

    @Override
    public PageData<TPromoCouponCodeUserVO> queryManagementUserData(ManagementDataInDTO inDTO) {

        Example example = new Example(TPromoCouponCodeUserEntity.class);
        example.orderBy(TPromoCouponCodeUserEntity.C_ID).desc();
        Criteria criteria = example.createCriteria();
        sql.sqlSelectCondition(inDTO, criteria);
        criteriaCondition(inDTO, criteria);
        return getPageInfo(inDTO, example);
    }

    /**
     * 优惠码 用户表
     */
    @Override
    public PageData<TPromoCouponCodeUserVO> queryManagementCouponCodeData(ManagementDataInDTO inDTO) {

        Example example = new Example(TPromoCouponCodeUserEntity.class);
        example.orderBy(TPromoCouponCodeUserEntity.C_ID).desc();
        Criteria criteria = example.createCriteria();
        sql.sqlSelectCouponCodeCondition(inDTO, criteria);
        criteriaCondition(inDTO, criteria);

        return getPageInfo(inDTO, example);
    }

    private void criteriaCondition(ManagementDataInDTO inDTO, Criteria criteria) {

        if (StringUtil.isNotBlank(inDTO.getReceiveStartTime()) && StringUtil.isNotBlank(inDTO.getReceiveEndTime())) {
            criteria.andGreaterThanOrEqualTo(TPromoCouponCodeUserEntity.C_RECEIVED_TIME, inDTO.getReceiveStartTime())
                .andLessThanOrEqualTo(TPromoCouponCodeUserEntity.C_RECEIVED_TIME, inDTO.getReceiveEndTime());
        }
        if (StringUtil.isNotBlank(inDTO.getUsedStartTime()) && StringUtil.isNotBlank(inDTO.getUsedEndTime())) {
            criteria.andGreaterThanOrEqualTo(TPromoCouponCodeUserEntity.C_USED_TIME, inDTO.getUsedStartTime())
                .andLessThan(TPromoCouponCodeUserEntity.C_USED_TIME, inDTO.getUsedEndTime());
        }
        if (StringUtil.isNotBlank(inDTO.getUsedRefId())) {
            criteria.andEqualTo(TPromoCouponCodeUserEntity.C_USED_REF_ID, inDTO.getUsedRefId());
        }
        if (StringUtil.isNotBlank(inDTO.getUserCode())) {
            criteria.andEqualTo(TPromoCouponCodeUserEntity.C_USER_CODE, inDTO.getUserCode());
        }
    }

    @Override
    public PageData<TPromoCouponCodeUserVO> queryUsedCouponListService(QueryCouponUsedInDTO dto) {

        Example example = new Example(TPromoCouponCodeUserEntity.class);
        example.orderBy(TPromoCouponCodeUserEntity.C_ID).desc();
        Criteria criteria = example.createCriteria();
		criteria.andEqualTo(TPromoCouponCodeUserEntity.C_TENANT_CODE, dto.getTenantCode());

        if (StringUtil.isNotEmpty(dto.getUserCode())){
            criteria.andEqualTo(TPromoCouponCodeUserEntity.C_USER_CODE,dto.getUserCode());
        }

        if (StringUtil.isNotEmpty(dto.getCouponCode())){
            criteria.andEqualTo(TPromoCouponCodeUserEntity.C_COUPON_CODE,dto.getCouponCode());
        }

        if (StringUtil.isNotEmpty(dto.getBeginTime()) && StringUtil.isNotEmpty(dto.getEndTime())){
            //2020-02-21 16:40:58
            Date start = DateUtil.parseDate(dto.getBeginTime(), DateUtil.FORMAT_YYYYMMDDHHMISS);
            Date end = DateUtil.parseDate(dto.getEndTime(), DateUtil.FORMAT_YYYYMMDDHHMISS);
            criteria.andGreaterThanOrEqualTo(TPromoCouponCodeUserEntity.C_CREATE_TIME,start)
            .andLessThanOrEqualTo(TPromoCouponCodeUserEntity.C_CREATE_TIME,end);
        }

        if (StringUtil.isNotEmpty(dto.getUpdateBeginTime()) && StringUtil.isNotEmpty(dto.getUpdateEndTime())){
            //2020-02-21 16:40:58
            Date start = DateUtil.parseDate(dto.getUpdateBeginTime(), DateUtil.FORMAT_YYYYMMDDHHMISS);
            Date end = DateUtil.parseDate(dto.getUpdateEndTime(), DateUtil.FORMAT_YYYYMMDDHHMISS);
            criteria.andGreaterThanOrEqualTo(TPromoCouponCodeUserEntity.C_UPDATE_TIME,start)
                    .andLessThanOrEqualTo(TPromoCouponCodeUserEntity.C_UPDATE_TIME,end);
        }
        if (!CollectionUtils.isEmpty(dto.getStatus())){
            //01-未发放 02-已发放 03-已使用 04-已锁定 05-已过期
            criteria.andIn(TPromoCouponCodeUserEntity.C_STATUS,dto.getStatus());
        }
        Page<TPromoCouponCodeUserVO> page = PageHelper.startPage(dto.getPageNum(), dto.getPageSize());
        couponCodeUserDao.selectByCondition(example);
        return new PageData<>(BeanCopyUtils.jsonCopyList(page.getResult(),TPromoCouponCodeUserVO.class),page.getTotal());
    }

    @Override
    public void updateCouponUserByCode(List<TPromoCouponCodeUserVO> userVO) {

       couponCodeUserDao.updateBatchUserInnerCode(userVO);
    }

    @Override
    public List<TPromoCouponCodeUserVO> queryUserCouponInfo(String tenantCode, List<String> couponCode) {

        if (CollectionUtils.isEmpty(couponCode)){
            return Collections.emptyList();
        }
        Example example = new Example(TPromoCouponCodeUserEntity.class);
        example.orderBy(TPromoCouponCodeUserEntity.C_ID).desc();
        Criteria criteria = example.createCriteria();
        criteria.andEqualTo(TPromoCouponCodeUserEntity.C_TENANT_CODE, tenantCode)
        .andIn(TPromoCouponCodeUserEntity.C_COUPON_CODE,couponCode);

        return BeanCopyUtils.jsonCopyList(couponCodeUserDao.selectByCondition(example),TPromoCouponCodeUserVO.class);
    }

	public Integer queryCouponCountByCodeAndStataus(String tenantCode, String activityCode, List<String> statusList) {

		return couponCodeUserDao.queryCouponCountByCodeAndStataus(tenantCode, activityCode, statusList);
	}
}
