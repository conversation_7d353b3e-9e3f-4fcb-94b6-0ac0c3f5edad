/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.service.impl.activity;

import com.github.pagehelper.PageInfo;
import com.gtech.commons.utils.BeanCopyUtils;
import com.gtech.promotion.dao.entity.activity.ActivityProductDetailEntity;
import com.gtech.promotion.dao.mapper.activity.ActivityProductDetailMapper;
import com.gtech.promotion.dao.model.activity.TPromoActivityProductDetailVO;
import com.gtech.promotion.dto.cache.ProductSkuDetailDTO;
import com.gtech.promotion.service.activity.ActivityProductDetailService;
import com.gtech.promotion.vo.bean.ProductDetail;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;
import tk.mybatis.mapper.entity.Example.Criteria;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;

/**
 * 促销商品sku数据服务
 * 
 */
@Service
public class ActivityProductDetailServiceImpl implements ActivityProductDetailService {

    @Autowired
    private ActivityProductDetailMapper productDetailMapper;

    @Override
    @Transactional
    public Integer insertProductSku(List<TPromoActivityProductDetailVO> productDetailVO) {

        List<ActivityProductDetailEntity> convert = BeanCopyUtils.jsonCopyList(productDetailVO, ActivityProductDetailEntity.class);
        return productDetailMapper.insertList(convert);
    }

    @Override
    @Transactional
    public Integer insertProductDetail(TPromoActivityProductDetailVO productDetailVO) {

        return productDetailMapper.insertSelective(BeanCopyUtils.jsonCopyBean(productDetailVO, ActivityProductDetailEntity.class));
    }

    @Override
    public List<ProductSkuDetailDTO> getProductSkusByActivityCodes(String tenantCode, List<String> activityCodes) {
        Example example = new Example(ActivityProductDetailEntity.class);
        example.createCriteria().andEqualTo(ActivityProductDetailEntity.C_TENANT_CODE, tenantCode)
                .andIn(ActivityProductDetailEntity.C_ACTIVITY_CODE, activityCodes);
        return BeanCopyUtils.jsonCopyList(productDetailMapper.selectByCondition(example), ProductSkuDetailDTO.class);
    }

    @Override
    public List<ProductSkuDetailDTO> queryOneGroupByActivityCodes(String tenantCode, List<String> activityCodes) {

        if (CollectionUtils.isEmpty(activityCodes)){
            return new ArrayList<>();
        }
        return productDetailMapper.queryOneGroupByActivityCodes(tenantCode, activityCodes);
    }

    @Override
    public List<ProductSkuDetailDTO> queryListByActivityCodesAndProductCode(Set<String> activityCodes, String productCode) {
        if (CollectionUtils.isEmpty(activityCodes)) {
            return new ArrayList<>();
        }
        Example example = new Example(ActivityProductDetailEntity.class);
        example.orderBy(ActivityProductDetailEntity.C_ID).asc();
        example.createCriteria().andIn(ActivityProductDetailEntity.C_ACTIVITY_CODE, activityCodes)
            .andEqualTo(ActivityProductDetailEntity.C_PRODUCT_CODE, productCode);
        List<ActivityProductDetailEntity> selectByCondition = productDetailMapper.selectByCondition(example);
        return BeanCopyUtils.jsonCopyList(selectByCondition, ProductSkuDetailDTO.class);
    }

    @Override
    public List<ProductSkuDetailDTO> queryListByActivityCodesAndProductCodes(Set<String> activityCodes, List<String> productCodes) {
        if (CollectionUtils.isEmpty(activityCodes) || CollectionUtils.isEmpty(productCodes)) {
            return new ArrayList<>();
        }
        Example example = new Example(ActivityProductDetailEntity.class);
        //example.orderBy(ActivityProductDetailEntity.C_ID).asc();
        example.createCriteria().andIn(ActivityProductDetailEntity.C_ACTIVITY_CODE, activityCodes)
                .andIn(ActivityProductDetailEntity.C_PRODUCT_CODE, productCodes);
        List<ActivityProductDetailEntity> selectByCondition = productDetailMapper.selectByCondition(example);
        return BeanCopyUtils.jsonCopyList(selectByCondition, ProductSkuDetailDTO.class);
    }

    @Override
    public List<TPromoActivityProductDetailVO> queryProductSpuOrSku(String activityCode) {

        ActivityProductDetailEntity entity = new ActivityProductDetailEntity();

        entity.setActivityCode(activityCode);
        return BeanCopyUtils.jsonCopyList(productDetailMapper.select(entity), TPromoActivityProductDetailVO.class);

    }

    @Override
    @Transactional
    public Integer deleteProductDetails(String activityCode,Integer type) {

        ActivityProductDetailEntity detailEntity = new ActivityProductDetailEntity();
        detailEntity.setActivityCode(activityCode);
        if (null!=type) {
            detailEntity.setType(type);
        }
        int delete = productDetailMapper.delete(detailEntity);
        return Integer.valueOf(delete);
    }

    @Override
    public PageInfo<ProductDetail> queryProductDetails(String activityCode) {

        Example example = new Example(ActivityProductDetailEntity.class);
        example.orderBy(ActivityProductDetailEntity.C_ID).asc();
        example.createCriteria().andEqualTo(ActivityProductDetailEntity.C_ACTIVITY_CODE, activityCode);
        List<ActivityProductDetailEntity> selectByCondition = productDetailMapper.selectByCondition(example);
        List<ProductDetail> convert = BeanCopyUtils.jsonCopyList(selectByCondition, ProductDetail.class);
        return new PageInfo<>(convert);
    }

    @Override
    public List<ProductSkuDetailDTO> getProductSkus111(String activityCode) {

        Example example = new Example(ActivityProductDetailEntity.class);
        example.orderBy(ActivityProductDetailEntity.C_ID).asc();
        example.createCriteria().andEqualTo(ActivityProductDetailEntity.C_ACTIVITY_CODE, activityCode);
        return BeanCopyUtils.jsonCopyList(productDetailMapper.selectByCondition(example), ProductSkuDetailDTO.class);
    }

    @Override
    public PageInfo<ProductDetail> queryProductSkusGroup111(String activityCode, Integer seqNum) {

        List<ActivityProductDetailEntity> selectByCondition = productDetailMapper.selectProductSku111(activityCode, seqNum);
        PageInfo<ActivityProductDetailEntity> pageInfo = new PageInfo<>(selectByCondition);
        List<ProductDetail> convert = BeanCopyUtils.jsonCopyList(selectByCondition, ProductDetail.class);
        PageInfo<ProductDetail> productDetailVOs = new PageInfo<>(convert);
        productDetailVOs.setTotal(pageInfo.getTotal());
        return productDetailVOs;
    }

    @Override
    public List<ProductDetail> queryProductSpusIn111(String activityCode, List<String> spuCodes) {

        Example example = new Example(ActivityProductDetailEntity.class);

        example.createCriteria()
            .andIn(ActivityProductDetailEntity.C_PRODUCT_CODE, spuCodes)
            .andEqualTo(ActivityProductDetailEntity.C_ACTIVITY_CODE, activityCode);

        List<ActivityProductDetailEntity> selectByCondition = productDetailMapper.selectByCondition(example);
        return BeanCopyUtils.jsonCopyList(selectByCondition, ProductDetail.class);
    }

    @Override
    public List<Integer> getSeqNumCount111(String activityCode) {

        return productDetailMapper.getSeqNumCount111(activityCode);
    }

    @Override
    public List<ProductDetail> queryProductcombinSpusIn111(String activityCode, List<String> combineSpuCodes) {

        Example example = new Example(ActivityProductDetailEntity.class);
        Criteria criteria = example.createCriteria();
        criteria.andIn(ActivityProductDetailEntity.C_COMBINE_SKU_CODE, combineSpuCodes)
        .andEqualTo(ActivityProductDetailEntity.C_ACTIVITY_CODE, activityCode);
        List<ActivityProductDetailEntity> selectByCondition = productDetailMapper.selectByCondition(example);
        return BeanCopyUtils.jsonCopyList(selectByCondition, ProductDetail.class);
    }

    @Override
    @Transactional
    public Integer deleteProductSkuBySeqNum(String activityCode, Integer seqNum) {

        ActivityProductDetailEntity detailEntity = new ActivityProductDetailEntity();
        detailEntity.setActivityCode(activityCode);
        detailEntity.setSeqNum(seqNum);
        return productDetailMapper.delete(detailEntity);
    }

    @Override
    public List<TPromoActivityProductDetailVO> queryProductSkusBySeqNum111(String activityCode, Integer seqNum) {

        Example example = new Example(ActivityProductDetailEntity.class);
        example.orderBy(ActivityProductDetailEntity.C_ID).asc();
        example.createCriteria()
        .andEqualTo(ActivityProductDetailEntity.C_ACTIVITY_CODE, activityCode).andEqualTo("seqNum", seqNum);
        return BeanCopyUtils.jsonCopyList(productDetailMapper.selectByCondition(example), TPromoActivityProductDetailVO.class);
    }

    @Override
    public List<ProductDetail> getProductSpuSkus(String activityCode) {

        ActivityProductDetailEntity entity = new ActivityProductDetailEntity();
        entity.setActivityCode(activityCode);
        return BeanCopyUtils.jsonCopyList(productDetailMapper.select(entity), ProductDetail.class);
    }

    @Override
    public List<Integer> getSeqNumDistinct111(String activityCode) {

        return productDetailMapper.getSeqNumDistinct111(activityCode);
    }

    @Override
    public void copyProductSku111(String oldActivityCode, String newActivityCode) {

        productDetailMapper.copyProductSku111(oldActivityCode, newActivityCode);
    }

}
