package com.gtech.promotion.service.coupon;

import com.github.pagehelper.PageInfo;
import com.gtech.promotion.dao.entity.coupon.PromoCouponSendLogEntity;
import com.gtech.promotion.dao.model.activity.PromoCouponSendLogModel;
import com.gtech.promotion.vo.param.coupon.QueryCouponSendLogParam;

public interface CouponSendLogService {
    int createCouponSendLog(PromoCouponSendLogModel promoCouponSendLogModel);

    void updateCouponSendLog(PromoCouponSendLogModel promoCouponSendLogModel);

    PageInfo<PromoCouponSendLogEntity> queryCouponSendLogList(QueryCouponSendLogParam queryCouponSendLogParam);
}
