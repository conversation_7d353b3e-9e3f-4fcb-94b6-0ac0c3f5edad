package com.gtech.promotion.service.impl.activity;

import com.github.pagehelper.PageHelper;
import com.gtech.commons.utils.BeanCopyUtils;
import com.gtech.commons.utils.StringUtil;
import com.gtech.promotion.code.marketing.SwitchEnum;
import com.gtech.promotion.dao.entity.activity.PromoGroupEntity;
import com.gtech.promotion.dao.entity.activity.PromoGroupRelationEntity;
import com.gtech.promotion.dao.mapper.activity.ActivityMapper;
import com.gtech.promotion.dao.mapper.activity.PromoGroupMapper;
import com.gtech.promotion.dao.mapper.activity.PromoGroupRelationMapper;
import com.gtech.promotion.dao.mapper.marketing.MarketingMapper;
import com.gtech.promotion.dao.model.activity.GroupQueryVO;
import com.gtech.promotion.dao.model.activity.PromoGroupMode;
import com.gtech.promotion.dao.model.activity.PromoGroupVO;
import com.gtech.promotion.dto.in.activity.GroupPriorityVO;
import com.gtech.promotion.service.activity.PromotionGroupService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;

import java.util.Collections;
import java.util.Comparator;
import java.util.List;

/**
 * @Description :
 * <AUTHOR> gaojie.li
 * @Date : 2023/6/14 9:52
 */
@Service
public class PromotionGroupServiceImpl implements PromotionGroupService {

    private static final String TENANT_CODE = "tenantCode";

    private static final String GROUP_CODE = "groupCode";

	private static final String PRIORITY = "priority";

    private static final String GROUP_NAME = "groupName";

    public static final String LOGIC_DELETE = "logicDelete";
    public static final String GROUP_CODE1 = "groupCode";

    @Autowired
    private PromoGroupMapper promoGroupMapper;
    @Autowired
    private PromoGroupRelationMapper promoGroupRelationMapper;

    @Autowired
    private ActivityMapper activityMapper;
    @Autowired
    private MarketingMapper marketingMapper;

    @Override
    @Transactional
    public int insertActivityGroup(PromoGroupMode promoGroupMode) {

        PromoGroupEntity promoGroupEntity = BeanCopyUtils.jsonCopyBean(promoGroupMode, PromoGroupEntity.class);

        return promoGroupMapper.insertSelective(promoGroupEntity);
    }

    @Override
    public int insertActivityGroupList(List<PromoGroupMode> promoGroupModeList) {
        List<PromoGroupEntity> promoGroupEntity = BeanCopyUtils.jsonCopyList(promoGroupModeList, PromoGroupEntity.class);

        return promoGroupMapper.insertList(promoGroupEntity);
    }

    @Override
    @Transactional
    public int updateActivityGroup(PromoGroupMode promoGroupMode) {

        Example example = new Example(PromoGroupEntity.class);
        example.createCriteria().andEqualTo(TENANT_CODE, promoGroupMode.getTenantCode())
                .andEqualTo(GROUP_CODE,promoGroupMode.getGroupCode())
                .andEqualTo(LOGIC_DELETE, SwitchEnum.YES.code());

        PromoGroupEntity promoGroupEntity = BeanCopyUtils.jsonCopyBean(promoGroupMode, PromoGroupEntity.class);

        //存在其他地方调用， updateUser为空的情况
        if (StringUtil.isNotEmpty(promoGroupMode.getUpdateUser())){
            promoGroupEntity.setUpdateUser(promoGroupMode.getUpdateUser());
        }

        return promoGroupMapper.updateByConditionSelective(promoGroupEntity,example);
    }

    @Override
    @Transactional
    public int deleteActivityGroup(String tenantCode, String groupCode,String operatorUser) {
        Example example = new Example(PromoGroupEntity.class);
        example.createCriteria().andEqualTo(TENANT_CODE, tenantCode)
				.andEqualTo(GROUP_CODE, groupCode)
                .andEqualTo(LOGIC_DELETE,SwitchEnum.YES.code());
        PromoGroupEntity entity = new PromoGroupEntity();
        entity.setUpdateUser(operatorUser);
        entity.setLogicDelete(Integer.parseInt(SwitchEnum.NO.code()));

        return promoGroupMapper.updateByConditionSelective(entity,example);
    }

    @Override
    public PromoGroupVO getLastedGroup(String tenantCode){

        PageHelper.startPage(0, 1, false);
        Example example = new Example(PromoGroupEntity.class);
		example.orderBy(PRIORITY).desc();
		example.createCriteria().andEqualTo(TENANT_CODE, tenantCode);

        List<PromoGroupEntity> promoGroupEntities = promoGroupMapper.selectByCondition(example);

        if (CollectionUtils.isNotEmpty(promoGroupEntities)){
            PromoGroupEntity promoGroupEntity = promoGroupEntities.get(0);
            return BeanCopyUtils.jsonCopyBean(promoGroupEntity, PromoGroupVO.class);
        }

        return null;
    }

    @Override
    public List<PromoGroupMode> queryActivityGroupList(GroupQueryVO groupQueryVO) {

        Example example = new Example(PromoGroupEntity.class);
        Example.Criteria criteria = example.createCriteria()
                .andEqualTo(TENANT_CODE, groupQueryVO.getTenantCode())
                .andEqualTo(LOGIC_DELETE,groupQueryVO.getLogicDelete());

        if (StringUtil.isNotEmpty(groupQueryVO.getGroupCode())){
            criteria.andEqualTo(GROUP_CODE,groupQueryVO.getGroupCode());
        }
        if (StringUtil.isNotEmpty(groupQueryVO.getGroupName())){
            criteria.andLike(GROUP_NAME,groupQueryVO.getGroupName());
        }
        List<PromoGroupEntity> promoGroupEntities = promoGroupMapper.selectByCondition(example);

        return BeanCopyUtils.jsonCopyList(promoGroupEntities,PromoGroupMode.class);
    }

    @Override
    public List<PromoGroupVO> listActivityGroupByTenantCode(String tenantCode) {

        Example example = new Example(PromoGroupEntity.class);

        example.createCriteria()
                .andEqualTo(TENANT_CODE,tenantCode)
                .andEqualTo(LOGIC_DELETE,SwitchEnum.YES.code());
        List<PromoGroupEntity> promoGroupEntities = promoGroupMapper.selectByCondition(example);

        promoGroupEntities.sort(Comparator.comparing(PromoGroupEntity::getPriority));


        return BeanCopyUtils.jsonCopyList(promoGroupEntities,PromoGroupVO.class);
    }

    @Override
    public PromoGroupVO getGroupByGroupCode(String tenantCode, String groupCode) {

        PromoGroupEntity promoGroupEntity = new PromoGroupEntity();
        promoGroupEntity.setTenantCode(tenantCode);
        promoGroupEntity.setGroupCode(groupCode);

        return BeanCopyUtils.jsonCopyBean(promoGroupMapper.selectOne(promoGroupEntity),PromoGroupVO.class);
    }

    @Override
    @Transactional
    public int updateActivityGroupPriority(GroupPriorityVO priorityVO) {

        int size = 0;

        for (GroupPriorityVO.ActivityGroup group : priorityVO.getGroups()){
            PromoGroupMode promoGroupMode = BeanCopyUtils.jsonCopyBean(group, PromoGroupMode.class);
            promoGroupMode.setTenantCode(priorityVO.getTenantCode());
            promoGroupMode.setGroupCode(group.getGroupCode());
            promoGroupMode.setPriority(group.getPriority());
            updateActivityGroup(promoGroupMode);
            size++;
        }

        return size;

    }

    @Override
    public int getGroupCountGroupType(String tenantCode, String groupCode) {

        PromoGroupEntity promoGroupEntity = new PromoGroupEntity();
        promoGroupEntity.setTenantCode(tenantCode);
        promoGroupEntity.setGroupCode(groupCode);

        return promoGroupMapper.selectCount(promoGroupEntity);

    }

    @Override
    public List<PromoGroupVO>  queryGroupByGroupCode(String tenantCode, List<String> groupCode) {

        if (CollectionUtils.isEmpty(groupCode)){
            return Collections.emptyList();
        }

        Example example = new Example(PromoGroupEntity.class);
        example.createCriteria()
                .andEqualTo(TENANT_CODE,tenantCode)
                .andIn(GROUP_CODE1,groupCode);

        return BeanCopyUtils.jsonCopyList(promoGroupMapper.selectByCondition(example),PromoGroupVO.class);
    }

    @Override
    public void refreshActivityGroupData(String domainCode, String tenantCode) {

        //* update promo_activity set group_code = ops_type where tenant_code = '100016';
        //* update marketing set group_code = ops_type where tenant_code = '100016';
        activityMapper.initActivityGroupCode(domainCode,tenantCode);
        marketingMapper.initMarketingGroupCode(domainCode,tenantCode);

        Example example = new Example(PromoGroupEntity.class);
        example.createCriteria()
                .andEqualTo("domainCode",domainCode)
                .andEqualTo("tenantCode",tenantCode);
        promoGroupMapper.deleteByCondition(example);

        Example exampleReleation =  new Example(PromoGroupRelationEntity.class);
        exampleReleation.createCriteria()
                .andEqualTo("domainCode",domainCode)
                .andEqualTo("tenantCode",tenantCode);
        promoGroupRelationMapper.deleteByCondition(exampleReleation);
    }
}
