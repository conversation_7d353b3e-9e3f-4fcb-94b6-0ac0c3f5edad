package com.gtech.promotion.service.impl.coupon;

import com.gtech.commons.utils.BeanCopyUtils;
import com.gtech.promotion.dao.entity.coupon.PromoCouponSendDetailEntity;
import com.gtech.promotion.dao.mapper.coupon.PromoCouponSendDetailMapper;
import com.gtech.promotion.dao.model.activity.PromoCouponSendDetailModel;
import com.gtech.promotion.service.coupon.CouponSendDetailService;
import com.gtech.promotion.vo.param.coupon.QueryCouponSendDetailParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;

import java.util.ArrayList;
import java.util.List;

@Slf4j
@Service
public class CouponSendDetailServiceImpl implements CouponSendDetailService {

    @Autowired
    private PromoCouponSendDetailMapper promoCouponSendDetailMapper;

    @Override
    public int createCouponSendDetail(PromoCouponSendDetailModel promoCouponSendDetailModel) {
        if(null == promoCouponSendDetailModel){
            return 0;
        }

        PromoCouponSendDetailEntity promoCouponSendDetailEntity = BeanCopyUtils.jsonCopyBean(promoCouponSendDetailModel, PromoCouponSendDetailEntity.class);

        return promoCouponSendDetailMapper.insertSelective(promoCouponSendDetailEntity);
    }

    @Transactional
    @Override
    public void createCouponSendDetailList(List<PromoCouponSendDetailModel> couponSendDetailModelList) {
        if(CollectionUtils.isEmpty(couponSendDetailModelList)){
            return;
        }

        for(PromoCouponSendDetailModel detailModel: couponSendDetailModelList){
            this.createCouponSendDetail(detailModel);
        }
    }

    @Override
    public List<PromoCouponSendDetailEntity> queryCouponSendDetailList(QueryCouponSendDetailParam queryCouponSendDetailParam) {
        Example example = new Example(PromoCouponSendDetailEntity.class);
        example.setOrderByClause("id desc");
        Example.Criteria criteria = example.createCriteria();
        if(!StringUtils.isEmpty(queryCouponSendDetailParam.getTenantCode())) {
            criteria.andEqualTo("tenantCode", queryCouponSendDetailParam.getTenantCode());
        }
        if(!StringUtils.isEmpty(queryCouponSendDetailParam.getActivityCode())){
            criteria.andEqualTo("activityCode",queryCouponSendDetailParam.getActivityCode());
        }
        if(CollectionUtils.isNotEmpty(queryCouponSendDetailParam.getSendBatchNoList())){
            criteria.andIn("batchNo",queryCouponSendDetailParam.getSendBatchNoList());
        }
        List<PromoCouponSendDetailEntity> couponSendDetailEntityList = new ArrayList<>();
        try {
            couponSendDetailEntityList = promoCouponSendDetailMapper.selectByExample(example);
        }catch (Exception e){
            log.error(e.getMessage(),e);
            log.error("queryCouponSendDetailList error！{}",e.getMessage());
        }
        return couponSendDetailEntityList;
    }
}
