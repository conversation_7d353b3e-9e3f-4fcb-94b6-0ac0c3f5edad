/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.service.activity;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

import com.gtech.promotion.dao.model.activity.TPromoIncentiveLimitedVO;

/**
 * <功能描述>
 */
public interface TPromoIncentiveLimitedService {

    /**
     * 批量插入限制表对象
     */
    Integer insertLimitedList111(String activityCode, List<TPromoIncentiveLimitedVO> limitedList, String tenantCode);

    /**
     * 批量插入限制表对象
     */
    Integer insertLuckyDrawLimitedList(String activityCode, List<TPromoIncentiveLimitedVO> limitedList, String tenantCode);



    /**
     * 根据活动id获取限制条件列表
     */
    List<TPromoIncentiveLimitedVO> getLimitedListByActivityCode(String activityCode);

    /**
     * 根据活动id获取限制条件列表
     */
    List<TPromoIncentiveLimitedVO> getLuckyDrawLimitedListByActivityCode(String activityCode);

    /**
     * 删除
     */
    Integer deleteLimitedByActivityCode(String activityCode);

    /**
     * 删除
     */
    Integer deleteLuckyDrawLimitedByActivityCode(String activityCode);

    List<TPromoIncentiveLimitedVO> getLimitedListByActivityCodes(String tenantCode, List<String> activityCodes);

	Map<String, BigDecimal> getLimitedByActivityCodes(String tenantCode, List<String> activityCodes, String limitationCode);
}
