 
/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.service.activity;

import com.gtech.promotion.dao.model.activity.TPromoActivitySettingVO;

/**   
 * 活动设置项服务类
 */
public interface TPromoActivitySettingService{

    /**   
     * 查询一个活动配置项
     * @param tenantCode
     * @param settingType
     * @param settingCode
     */
    TPromoActivitySettingVO selectSetting(String tenantCode,Integer settingType,String settingCode);

    /**   
     * 添加一个活动配置项
     * @param tenantCode
     * @param settingType
     * @param settingCode
     * @param settingValue  
     */
    void addActivitySetting(String tenantCode,Integer settingType,String settingCode,String settingValue);
    
    /**   
     * 修改一个活动配置项
     * @param tenantCode
     * @param settingType
     * @param settingCode
     * @param settingValue  
     */
    void updateActivitySetting(String tenantCode,Integer settingType,String settingCode,String settingValue);

    /**   
     * 将数据库所有配置同步到redis 
     */
    void addAllActivitySetting();

    /**   
     * 删除该租户的一个配置项
     * @param tenantCode
     * @param settingType
     * @return  删除记录数
     */
    int deleteSetting(String tenantCode,Integer settingType);
}
  
