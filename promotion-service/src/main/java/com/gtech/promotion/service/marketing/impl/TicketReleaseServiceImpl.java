package com.gtech.promotion.service.marketing.impl;

import com.gtech.commons.utils.ConvertUtils;
import com.gtech.promotion.checker.coupon.CouponAllocateChecker;
import com.gtech.promotion.checker.marketing.MarketingChecker;
import com.gtech.promotion.dao.entity.marketing.TicketReleaseEntity;
import com.gtech.promotion.dao.mapper.marketing.TicketReleaseMapper;
import com.gtech.promotion.dao.model.marketing.BaseModel;
import com.gtech.promotion.dao.model.marketing.TicketReleaseModel;
import com.gtech.promotion.dto.in.marketing.TicketSendOutDto;
import com.gtech.promotion.exception.Check;
import com.gtech.promotion.helper.RedisLock;
import com.gtech.promotion.service.marketing.TicketReleaseService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

@Service
public class TicketReleaseServiceImpl extends BaseServiceImpl<TicketReleaseEntity, TicketReleaseModel> implements TicketReleaseService {

    @Autowired
    private TicketReleaseMapper releaseMapper;

    @Autowired
    private RedisLock redisLock;

    public TicketReleaseServiceImpl() {
        super(TicketReleaseEntity.class, TicketReleaseModel.class);
    }

    @Override
    @Transactional
    public List<TicketSendOutDto> deductInventory(BaseModel baseModel, int inventory) {
        List<TicketSendOutDto> ticketSendOutDtos = new ArrayList<>();

        if (inventory == 0) return ticketSendOutDtos;

        List<TicketReleaseModel> ticketReleaseModels = findListByActivityCode(baseModel.getActivityCode());
        Check.check(CollectionUtils.isEmpty(ticketReleaseModels), MarketingChecker.TICKET_NO_RELEASE);

        long totalCount = 0L;
        for (TicketReleaseModel ticketReleaseModel : ticketReleaseModels) {
            totalCount += ConvertUtils.toLong(ticketReleaseModel.getInventory(), 0l);
        }
        // 需要扣减的库存inventory 如果比所有批次剩余的库存总量多，则返回异常提示
        Check.check(inventory > totalCount, MarketingChecker.TICKET_NO_INVENTORY);

        long surplusInventory = inventory; // 剩余要扣减库存

        // 按批次扣减
        for (TicketReleaseModel ticketReleaseModel : ticketReleaseModels) {

            Long inventoryCurrRelease = ticketReleaseModel.getInventory(); // 批次剩余库存
            if(inventoryCurrRelease == null || inventoryCurrRelease <= 0) continue; // 库存为空则返回

            long deductInventory = surplusInventory; // 当前扣减库存 为 剩余要扣减库存
            if (deductInventory > inventoryCurrRelease){
                deductInventory = inventoryCurrRelease; // 当前扣减库存 比 批次剩余库存 大，则此次扣减 批次剩余库存
            }
            int row = releaseMapper.updateInventory(ticketReleaseModel.getTenantCode(), ticketReleaseModel.getActivityCode(), ticketReleaseModel.getReleaseCode(), deductInventory);
            if(row > 0) {
                // 扣减成功后，剩余要扣减库存 数量减去 当前扣减库存
                surplusInventory -= deductInventory;
                ticketSendOutDtos.add(new TicketSendOutDto(ticketReleaseModel.getReleaseCode(), deductInventory));
            }
        }

        // 如果 剩余要扣减库存 大于 0， 则程序抛异常，数据库回滚
        Check.check(surplusInventory > 0, CouponAllocateChecker.LOCK_FAIL);

        return ticketSendOutDtos;
    }

    @Override
    public int addUsed(BaseModel baseModel, String releaseCode) {
        return releaseMapper.addUsed(baseModel.getTenantCode(), baseModel.getActivityCode(), releaseCode);
    }

    @Override
    public int releaseSpecifiedQuantity(TicketReleaseModel ticketReleaseModel) {
        return releaseMapper.releaseSpecifiedQuantity(ticketReleaseModel.getTenantCode(), ticketReleaseModel.getActivityCode(), ticketReleaseModel.getReleaseCode(), ticketReleaseModel.getQuality());
    }
}
