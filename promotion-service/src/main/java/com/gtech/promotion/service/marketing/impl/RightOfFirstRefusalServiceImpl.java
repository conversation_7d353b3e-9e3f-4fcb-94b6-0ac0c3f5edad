package com.gtech.promotion.service.marketing.impl;

import com.gtech.commons.utils.StringUtil;
import com.gtech.promotion.code.marketing.RightOfFirstRefusalStatusEnum;
import com.gtech.promotion.dao.entity.marketing.RightOfFirstRefusalEntity;
import com.gtech.promotion.dao.mapper.marketing.RightOfFirstRefusalMapper;
import com.gtech.promotion.dao.model.marketing.RightOfFirstRefusalModel;
import com.gtech.promotion.dto.in.flashsale.WriteOffOfPreEmptiveRightsDto;
import com.gtech.promotion.service.marketing.RightOfFirstRefusalService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;

import java.util.List;

@Service
public class RightOfFirstRefusalServiceImpl extends BaseServiceImpl<RightOfFirstRefusalEntity, RightOfFirstRefusalModel> implements RightOfFirstRefusalService {

    public RightOfFirstRefusalServiceImpl() {
        super(RightOfFirstRefusalEntity.class, RightOfFirstRefusalModel.class);
    }

    @Autowired
    private RightOfFirstRefusalMapper rightOfFirstRefusalMapper;


    @Override
    public RightOfFirstRefusalEntity findRightOfFirstRefusalByMember(WriteOffOfPreEmptiveRightsDto dto) {

        Example example = new Example(RightOfFirstRefusalEntity.class);
        example.createCriteria()
                .andEqualTo("memberCode", dto.getMemberCode())
                .andEqualTo("rightOfFirstRefusalStatus", "01");
        return rightOfFirstRefusalMapper.selectOneByExample(example);
    }

    @Transactional
    @Override
    public int updateStatus(WriteOffOfPreEmptiveRightsDto dto) {
        Example example = new Example(RightOfFirstRefusalEntity.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("tenantCode", dto.getTenantCode());

        if (StringUtil.isNotBlank(dto.getOldStatus())) {
            criteria.andEqualTo("rightOfFirstRefusalStatus", dto.getOldStatus());
        }
        if (StringUtil.isNotBlank(dto.getDomainCode())) {
            criteria.andEqualTo("domainCode", dto.getDomainCode());
        }
        if (StringUtil.isNotBlank(dto.getMemberCode())) {
            criteria.andEqualTo("memberCode", dto.getMemberCode());
        }
        if (StringUtil.isNotBlank(dto.getActivityCode())) {
            criteria.andEqualTo("activityCode", dto.getActivityCode());
        }
        if (StringUtil.isNotBlank(dto.getOrgCode())) {
            criteria.andEqualTo("orgCode", dto.getOrgCode());
        }
        if (StringUtil.isNotBlank(dto.getOrderId()) && !dto.isOrderIDIsNull()){
            criteria.andEqualTo("orderId", dto.getOrderId());
        }

        RightOfFirstRefusalEntity rightOfFirstRefusalEntity = new RightOfFirstRefusalEntity();
        rightOfFirstRefusalEntity.setRightOfFirstRefusalStatus(dto.getNewStatus());

        if (RightOfFirstRefusalStatusEnum.LOCK.code().equals(dto.getNewStatus())){
            //如果有多条数据,则只更新一条
            rightOfFirstRefusalMapper.selectByCondition(example).stream().findFirst().ifPresent(entity -> {
                criteria.andEqualTo("rightOfFirstRefusalCode", entity.getRightOfFirstRefusalCode());
            });
            rightOfFirstRefusalEntity.setOrderId(dto.getOrderId());
        } else if (RightOfFirstRefusalStatusEnum.UN_USED.code().equals(dto.getNewStatus())) {
            rightOfFirstRefusalEntity.setOrderId("");
        }

        return rightOfFirstRefusalMapper.updateByConditionSelective(rightOfFirstRefusalEntity,example);
    }

    @Override
    public List<RightOfFirstRefusalEntity> queryRightOfFirstRefusalByMember(WriteOffOfPreEmptiveRightsDto dto) {
        Example example = new Example(RightOfFirstRefusalEntity.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("domainCode", dto.getDomainCode());
        criteria.andEqualTo("tenantCode", dto.getTenantCode());
        criteria.andEqualTo("orgCode", dto.getOrgCode());
        criteria.andEqualTo("memberCode", dto.getMemberCode());
        criteria.andEqualTo("rightOfFirstRefusalStatus", "01");
        if (StringUtil.isNotBlank(dto.getRightOfFirstRefusalProductCode())){
            criteria.andEqualTo("rightOfFirstRefusalProductCode", dto.getRightOfFirstRefusalProductCode());
        }
        if (StringUtil.isNotBlank(dto.getActivityCode())){
            criteria.andEqualTo("activityCode", dto.getActivityCode());
        }

        if (StringUtil.isNotBlank(dto.getRightOfFirstRefusalCode())){
            criteria.andEqualTo("rightOfFirstRefusalCode", dto.getRightOfFirstRefusalCode());
        }

        return rightOfFirstRefusalMapper.selectByCondition(example);
    }
}
