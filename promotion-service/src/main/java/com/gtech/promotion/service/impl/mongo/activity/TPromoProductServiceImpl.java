/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.service.impl.mongo.activity;

import com.alibaba.fastjson.JSONObject;
import com.gtech.commons.utils.BeanCopyUtils;
import com.gtech.commons.utils.StringUtil;
import com.gtech.promotion.dao.model.activity.TPromoActivityProductVO;
import com.gtech.promotion.dao.mongo.activity.ActivityProductEntity;
import com.gtech.promotion.service.mongo.activity.TPromoProductService;
import com.gtech.promotion.vo.bean.ProductScope;
import com.mongodb.client.result.DeleteResult;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.BasicQuery;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

/**
 * 促销商品基本服务
 * 
 */
@Service
public class TPromoProductServiceImpl implements TPromoProductService {

    @Autowired
    private MongoTemplate mongoTemplate;

    private static final String ACTIVITY_CODE = "activityCode";
    private static final String TENANT_CODE = "tenantCode";

    @Override
    @Transactional
    public Integer insertProducts(List<ProductScope> products, String activityCode, String tenantCode) {

        return this.doInsertProducts(products, activityCode, tenantCode);
    }
    
    private Integer doInsertProducts(List<ProductScope> products, String activityCode, String tenantCode) {
        
        if (CollectionUtils.isEmpty(products)) {
            return 0;
        }

        List<ActivityProductEntity> entityList = new ArrayList<>();
        for(ProductScope e : products) {
            
            ActivityProductEntity entity = BeanCopyUtils.jsonCopyBean(e, ActivityProductEntity.class);

            entity.setActivityCode(activityCode);
            entity.setTenantCode(tenantCode);
            entity.setAttributes(e.getAttributes());
            if (StringUtil.isEmpty(e.getAttrType())){
                entity.setAttrType("01");
            }
            entityList.add(entity);
        }

        mongoTemplate.insertAll(entityList);
        return products.size();
    }

    @Override
    @Transactional
    public Integer deleteProducts(String activityCode) {

        JSONObject jsonObject = new JSONObject();
        jsonObject.put(ACTIVITY_CODE, activityCode);
        Query query = new BasicQuery(jsonObject.toJSONString());
        DeleteResult remove = mongoTemplate.remove(query, ActivityProductEntity.class);
        return (int) remove.getDeletedCount();
    }

    @Override
    public List<ProductScope> getProducts(String activityCode) {

        JSONObject jsonObject = new JSONObject();
        jsonObject.put(ACTIVITY_CODE, activityCode);
        Query query = new BasicQuery(jsonObject.toJSONString());

        List<ActivityProductEntity> list = getActivityProductEntities(query);

        return BeanCopyUtils.jsonCopyList(list, ProductScope.class);
    }

    @Override
    public List<TPromoActivityProductVO> getPromoProduct(String activityCode) {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put(ACTIVITY_CODE, activityCode);
        Query query = new BasicQuery(jsonObject.toJSONString());

        List<ActivityProductEntity> list = getActivityProductEntities(query);
        return BeanCopyUtils.jsonCopyList(list, TPromoActivityProductVO.class);
    }

    public List<ActivityProductEntity> getActivityProductEntities(Query query) {
        List<ActivityProductEntity> list = mongoTemplate.find(query, ActivityProductEntity.class);

        for (ActivityProductEntity activityProductEntity : list) {
            if (CollectionUtils.isNotEmpty(activityProductEntity.getAttributes()) && StringUtil.isEmpty(activityProductEntity.getAttrType())) {
                activityProductEntity.setAttrType("01");
            }
        }
        return list;
    }

    @Override
    public List<TPromoActivityProductVO> getPromoProductByActivityCodes(String tenantCode, List<String> activityCodes) {

        Query query = new Query(Criteria.where(TENANT_CODE).is(tenantCode).and(ACTIVITY_CODE).in(activityCodes));
        List<ActivityProductEntity> list = mongoTemplate.find(query, ActivityProductEntity.class);

        return BeanCopyUtils.jsonCopyList(list, TPromoActivityProductVO.class);
    }

    @Override
    public void updateActivityId2ActivityCode(String tenantCode, String activityId, String activityCode) {

        JSONObject jsonObject = new JSONObject();
        jsonObject.put("activityId", activityId);
        Query query = new BasicQuery(jsonObject.toJSONString());
        List<ProductScope> findList = BeanCopyUtils.jsonCopyList(mongoTemplate.find(query, ActivityProductEntity.class), ProductScope.class);
        if (CollectionUtils.isNotEmpty(findList)) {
            this.doInsertProducts(findList, activityCode, tenantCode);
        }
    }

    @Override
    @Transactional
    public Integer deleteProductBySeqNum111(String activityCode, Integer seqNum) {

        JSONObject jsonObject = new JSONObject();
        jsonObject.put(ACTIVITY_CODE, activityCode);
        jsonObject.put("seqNum", seqNum);
        Query query = new BasicQuery(jsonObject.toJSONString());
        DeleteResult remove = mongoTemplate.remove(query, ActivityProductEntity.class);
        return (int) remove.getDeletedCount();
    }

}
