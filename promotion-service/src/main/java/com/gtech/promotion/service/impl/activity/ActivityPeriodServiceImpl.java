package com.gtech.promotion.service.impl.activity;

import com.gtech.commons.utils.BeanCopyUtils;
import com.gtech.promotion.dao.entity.activity.ActivityPeriodEntity;
import com.gtech.promotion.dao.mapper.activity.ActivityPeriodMapper;
import com.gtech.promotion.dao.model.activity.ActivityPeriodModel;
import com.gtech.promotion.service.activity.ActivityPeriodService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;

import java.util.List;

@Service
public class ActivityPeriodServiceImpl implements ActivityPeriodService {

    @Autowired
    private ActivityPeriodMapper activityPeriodMapper;

    @Override
    @Transactional
    public int createPeriod(ActivityPeriodModel activityPeriodModel) {
        return activityPeriodMapper.insertSelective(BeanCopyUtils.jsonCopyBean(activityPeriodModel, ActivityPeriodEntity.class));
    }

    @Override
    @Transactional
    public int deletePeriod(String tenantCode, String activityCode) {
        ActivityPeriodEntity activityPeriodEntity = new ActivityPeriodEntity();
        activityPeriodEntity.setTenantCode(tenantCode);
        activityPeriodEntity.setActivityCode(activityCode);
        return activityPeriodMapper.delete(activityPeriodEntity);
    }

    @Override
    public ActivityPeriodModel findPeriod(String tenantCode, String activityCode) {
        ActivityPeriodEntity activityPeriodEntity = new ActivityPeriodEntity();
        activityPeriodEntity.setTenantCode(tenantCode);
        activityPeriodEntity.setActivityCode(activityCode);
        return BeanCopyUtils.jsonCopyBean(activityPeriodMapper.selectOne(activityPeriodEntity), ActivityPeriodModel.class);
    }

    @Override
    public List<ActivityPeriodModel> queryPeriodByActivityCodes(String tenantCode, List<String> activityCodes) {
        Example example = new Example(ActivityPeriodEntity.class);
        example.createCriteria().andEqualTo(ActivityPeriodEntity.C_TENANT_CODE, tenantCode)
                .andIn(ActivityPeriodEntity.C_ACTIVITY_CODE, activityCodes);
        return BeanCopyUtils.jsonCopyList(activityPeriodMapper.selectByCondition(example), ActivityPeriodModel.class);
    }
}
