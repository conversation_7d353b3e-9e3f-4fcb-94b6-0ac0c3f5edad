/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.service.activity;

import java.util.List;

import com.gtech.promotion.dao.model.activity.ActivityLanguageModel;
import com.gtech.promotion.dao.model.activity.ActivityModel;

/**
 * 多语言活动表service
 */
public interface ActivityLanguageService {

    /**
     * Replace the multilingual attributes.
     */
    void replaceField(ActivityModel activityVO, String language);

    /**   
     * 创建一个多语言活动信息
     */
    void insertOne(ActivityLanguageModel activityLanguageModel);

    /**   
     * 根据租户编码和活动编码查询 活动的多语言信息列表
     */
    List<ActivityLanguageModel> queryActivityLanguages(String tenantCode, String activityCode);
    
    ActivityLanguageModel findActivityLanguage(String tenantCode, String activityCode, String language);

    /**   
     * 根据活动编码删除其多语言信息
     */
    void deleteByActivityCode(String activityCode);

	List<ActivityLanguageModel> queryActivityLanguagesByActivityCodes(String tenantCode, String language, List<String> activityCodes);
}
