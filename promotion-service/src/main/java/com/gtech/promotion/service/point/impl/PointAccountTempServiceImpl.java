package com.gtech.promotion.service.point.impl;

import com.gtech.commons.redis.GTechCodeGenerator;
import com.gtech.commons.utils.BeanCopyUtils;
import com.gtech.promotion.checker.SystemChecker;
import com.gtech.promotion.dao.entity.point.PointAccountEntity;
import com.gtech.promotion.dao.mapper.point.PointAccountMapper;
import com.gtech.promotion.exception.PromotionException;
import com.gtech.promotion.service.point.PointAccountTempService;
import com.gtech.promotion.vo.param.point.CreatePointAccountParam;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 * @Date 2021/1/6 16:49
 */
@Service
public class PointAccountTempServiceImpl implements PointAccountTempService {
    private static final String SYS_CODE = "PointAccountServiceImpl";

    private static final String POINT_ACCOUNT_CODE = "pointAccountCode";
    @Autowired
    PointAccountMapper pointAccountMapper;

    @Autowired
    GTechCodeGenerator codeGenerator;
    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public String savePointAccount(CreatePointAccountParam pointAccountVo) {

        PointAccountEntity pointAccount = BeanCopyUtils.jsonCopyBean(pointAccountVo, PointAccountEntity.class);

        if (null == pointAccount.getStatus()) {
            pointAccount.setStatus(0);
        }
        if (null == pointAccount.getAccountBalance()) {
            pointAccount.setAccountBalance(0);
        }
        if (StringUtils.isBlank(pointAccount.getPointAccountCode())) {
            pointAccount.setPointAccountCode(codeGenerator.generateCode(pointAccount.getTenantCode(), POINT_ACCOUNT_CODE, "PA[D:yyyyMMddHHmmss][SM:%06d]", 1l));
        }

        try {
            pointAccountMapper.insert(pointAccount);
        } catch (DuplicateKeyException e) {
            throw new PromotionException(SystemChecker.DUPLICATE_KEY.getCode(), SystemChecker.DUPLICATE_KEY.getMessage());
        } catch (Exception e) {
            throw new PromotionException(SYS_CODE + ".savePointAccountModel.ex", e.getMessage());
        }

        return pointAccount.getPointAccountCode();
    }
}
