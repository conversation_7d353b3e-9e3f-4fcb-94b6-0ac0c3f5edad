/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.service.activity;

import com.github.pagehelper.PageInfo;
import com.gtech.promotion.dao.model.activity.TPromoActivityStoreVO;
import com.gtech.promotion.page.RequestPage;

import java.util.List;

/**
 * 店铺服务类
 * 
 */
public interface ActivityStoreService {

    /**
     * 创建促销店铺
     *
     * @param storeVO 店铺VO
     */
    void createStore(TPromoActivityStoreVO storeVO);

    /**
     * 根据活动id获取店铺列表
     */
    List<TPromoActivityStoreVO> getStoresByActivityCode(String activityCode);

    /**
     * Delete by activity code.
     */
    void deleteByActivityCode(String activityCode);

    /**
     * 查找改租户下没有该店铺编码的活动
     */
    List<String> selectNotByCondition(String tenantCode, String orgCode);

    List<TPromoActivityStoreVO> getStoresByActivityCodes(String tenantCode, List<String> activityCodes);

    PageInfo<TPromoActivityStoreVO> selectByOrgCode(String tenantCode, String orgCode, RequestPage page);


}
