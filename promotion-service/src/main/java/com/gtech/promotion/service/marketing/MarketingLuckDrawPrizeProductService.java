package com.gtech.promotion.service.marketing;

import com.gtech.promotion.dao.entity.marketing.MarketingLuckDrawPrizeProductEntity;
import com.gtech.promotion.dao.model.marketing.MarketingLuckDrawPrizeProductModel;

public interface MarketingLuckDrawPrizeProductService  extends BaseService<MarketingLuckDrawPrizeProductEntity, MarketingLuckDrawPrizeProductModel>{
    Boolean checkLuckDrawPrizeProduct(String domainCode, String tenantCode, String userCode, String checkLuckDrawPrizeActivityCode, String productCode);

    Boolean lockPrizeProduct(String tenantCode, String activityCode, String memberCode,String productCode,String orderId);
    Boolean usedPrizeProduct(String tenantCode, String activityCode, String memberCode,String orderId);
    Boolean cancelPrizeProduct(String tenantCode, String activityCode, String memberCode,String orderId);
}
