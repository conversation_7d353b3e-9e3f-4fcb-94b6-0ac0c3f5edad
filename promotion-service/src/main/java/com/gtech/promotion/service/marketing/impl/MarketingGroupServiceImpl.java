package com.gtech.promotion.service.marketing.impl;

import com.google.common.collect.Lists;
import com.gtech.commons.utils.BeanCopyUtils;
import com.gtech.promotion.dao.entity.marketing.BaseEntity;
import com.gtech.promotion.dao.entity.marketing.MarketingGroupEntity;
import com.gtech.promotion.dao.mapper.marketing.MarketingGroupMapper;
import com.gtech.promotion.dao.model.marketing.MarketingGroupMode;
import com.gtech.promotion.service.marketing.MarketingGroupService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import tk.mybatis.mapper.entity.Example;

import java.util.Collections;
import java.util.List;
import java.util.Set;

/**
 * @Description :
 * <AUTHOR> gaojie.li
 * @Date : 2023/6/5 15:45
 */
@Service
public class MarketingGroupServiceImpl extends BaseServiceImpl<MarketingGroupEntity, MarketingGroupMode> implements MarketingGroupService {

    @Autowired
    private MarketingGroupMapper marketingGroupMapper;

    public MarketingGroupServiceImpl() {
        super(MarketingGroupEntity.class, MarketingGroupMode.class);
    }

    @Override
    public List<MarketingGroupEntity> selectMarketingGroupList(String tenantCode, Set<String> activityCodes) {
        if (CollectionUtils.isEmpty(activityCodes)){
            return Collections.emptyList();
        }
        Example example = new Example(MarketingGroupEntity.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo(BaseEntity.C_TENANT_CODE, tenantCode);
        if(!CollectionUtils.isEmpty(activityCodes)){
            criteria.andIn(BaseEntity.C_ACTIVITY_CODE, activityCodes);
        }
        return marketingGroupMapper.selectByExample(example);
    }

    @Override
    public MarketingGroupMode findMarketingGroupByActivityCode(String tenantCode, String activityCode) {

        MarketingGroupEntity entity = new MarketingGroupEntity();

        entity.setTenantCode(tenantCode);
        entity.setActivityCode(activityCode);

        return BeanCopyUtils.jsonCopyBean(marketingGroupMapper.selectOne(entity),MarketingGroupMode.class);
    }

    @Override
    public MarketingGroupMode findGroupByActivityCode(String tenantCode, String activityCode, Integer closeFlag) {
        MarketingGroupEntity entity = new MarketingGroupEntity();

        entity.setTenantCode(tenantCode);
        entity.setActivityCode(activityCode);

        if (null != closeFlag){
            entity.setCloseFlag(closeFlag);
        }

        return BeanCopyUtils.jsonCopyBean(marketingGroupMapper.selectOne(entity),MarketingGroupMode.class);
    }
}
