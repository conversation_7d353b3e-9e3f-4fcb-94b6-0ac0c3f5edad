package com.gtech.promotion.service.point.impl;

import java.util.Date;
import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.gtech.commons.page.PageData;
import com.gtech.commons.redis.GTechCodeGenerator;
import com.gtech.commons.utils.BeanCopyUtils;
import com.gtech.commons.utils.StringUtil;
import com.gtech.promotion.checker.SystemChecker;
import com.gtech.promotion.code.point.PointTransactionTypeEnum;
import com.gtech.promotion.dao.entity.point.PointCampaignEntity;
import com.gtech.promotion.dao.entity.point.PointTransactionEntity;
import com.gtech.promotion.dao.mapper.point.PointCampaignMapper;
import com.gtech.promotion.dao.mapper.point.PointTransactionMapper;
import com.gtech.promotion.dto.in.point.PointTransactionDto;
import com.gtech.promotion.exception.PromotionException;
import com.gtech.promotion.service.BaseService;
import com.gtech.promotion.service.point.PointTransactionService;
import com.gtech.promotion.utils.DateUtil;
import com.gtech.promotion.vo.param.point.PointTransactionParam;
import com.gtech.promotion.vo.param.point.query.GetPointTransactionParam;
import com.gtech.promotion.vo.result.point.PointAccountCampaignResult;
import com.gtech.promotion.vo.result.point.PointTransactionResult;

import lombok.extern.slf4j.Slf4j;
import tk.mybatis.mapper.entity.Example;
@Slf4j
@Service
public class PointTransactionServiceImpl extends BaseService implements PointTransactionService {

	private static final String SYS_CODE = "PointTransactionServiceImpl";

	private static final String TRANSACTION_SN = "transactionSn";


	@Autowired
	PointTransactionMapper pointTransactionMapper;

	@Autowired
	private PointCampaignMapper pointCampaignMapper;

	@Autowired
	GTechCodeGenerator codeGenerator;

	@Override
    @Transactional
	public String savePointTransaction(PointTransactionParam pointTransactionVo) {

		// 1.检测
		String msg = checkPointTransaction(pointTransactionVo, false);

		if (StringUtils.isNotBlank(msg)) {
			throw new PromotionException(SYS_CODE + ".savePointTransaction.checkPointTransaction", msg);
		}
		PointTransactionEntity pointTransaction = BeanCopyUtils.jsonCopyBean(pointTransactionVo, PointTransactionEntity.class);
		// 2.默认值
		setPointTransactionDefault(pointTransaction);

		// 3.扣减临时积分
		if (pointTransaction.getTransactionType()==PointTransactionTypeEnum.DEDUCT.number() && pointTransaction.getOperation()!=2){

			deductionOfTemporaryPoints(pointTransaction);
		}


		// 4.保存
		savePointTransactionModel(pointTransaction);

		return pointTransaction.getTransactionSn();
	}

	@Override
	public void updatePointTransaction(PointTransactionParam pointTransactionVo) {

		// 1.检测
		String msg = checkPointTransaction(pointTransactionVo, true);

		if (StringUtils.isNotBlank(msg)) {
			throw new PromotionException(SYS_CODE + ".updatePointTransaction.checkPointTransaction", msg);
		}
		// 2.获取MODEL
		PointTransactionEntity oldPointTransaction = getPointTransactionModelByCode(pointTransactionVo.getTenantCode(), pointTransactionVo.getTransactionSn());// NOSONAR
		if (null == oldPointTransaction) {
			throw new PromotionException(SystemChecker.NULL_VO.getCode(), SystemChecker.NULL_VO.getMessage());
		}
		try {
			BeanUtils.copyProperties(pointTransactionVo, oldPointTransaction);
		} catch (Exception e) {
			throw new PromotionException(SYS_CODE + ".updatePointTransaction.copy", "copy exception");
		}
		// 3.默认值
		setPointTransactionUpdataDefault(oldPointTransaction);
		// 4.保存
		updatePointTransactionModel(oldPointTransaction);
	}

	@Override
	public PageData<PointTransactionResult> queryPointTransactionPage(PointTransactionDto pointTransactionDto) {
		PageHelper.startPage(pointTransactionDto.getPageNum(), pointTransactionDto.getPageSize());

		Example example = new Example(PointTransactionEntity.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo("domainCode", pointTransactionDto.getDomainCode())
				.andEqualTo("tenantCode", pointTransactionDto.getTenantCode());

		if (StringUtil.isNotBlank(pointTransactionDto.getReferTransactionSn())){
			criteria.andEqualTo(PointTransactionEntity.C_TRANSACTION_SN,pointTransactionDto.getTransactionSn());
		}
		if (StringUtil.isNotBlank(pointTransactionDto.getAccountCode())){
			criteria.andEqualTo("accountCode", pointTransactionDto.getAccountCode());
		}
		if (pointTransactionDto.getAccountType()!=null){
			criteria.andEqualTo("accountType", pointTransactionDto.getAccountType());
		}
		if (StringUtil.isNotBlank(pointTransactionDto.getCampaignCode())){
			criteria.andEqualTo("campaignCode", pointTransactionDto.getCampaignCode());
		}
		if (pointTransactionDto.getTransactionType()!=null){
			criteria.andEqualTo("transactionType", pointTransactionDto.getTransactionType());
		}
		if (pointTransactionDto.getTransactionDate()!=null){
			criteria.andEqualTo("transactionDate", pointTransactionDto.getTransactionDate());
		}
		if (StringUtil.isNotBlank(pointTransactionDto.getTransactionSn())){
			criteria.andEqualTo("referTransactionSn", pointTransactionDto.getTransactionSn());
		}
		if (StringUtil.isNotBlank(pointTransactionDto.getReferOrderNumber())){
			criteria.andEqualTo("referOrderNumber", pointTransactionDto.getReferOrderNumber());
		}

		if (StringUtil.isNotBlank(pointTransactionDto.getBeginDateFrom())){
			criteria.andGreaterThanOrEqualTo(PointTransactionEntity.C_CREATE_TIME, pointTransactionDto.getBeginDateFrom());
		}
		if (StringUtil.isNotBlank(pointTransactionDto.getEndDateTo())){
			criteria.andLessThanOrEqualTo(PointTransactionEntity.C_CREATE_TIME, pointTransactionDto.getEndDateTo());
		}

		example.orderBy(PointTransactionEntity.C_CREATE_TIME).desc();
		Page<PointTransactionEntity> source = (Page<PointTransactionEntity>)pointTransactionMapper.selectByCondition(example);

		List<PointTransactionResult> dtos = BeanCopyUtils.jsonCopyList(source, PointTransactionResult.class);

		for (PointTransactionResult pointTransactionResult: dtos) {
			PointCampaignEntity entity = new PointCampaignEntity();
			entity.setTenantCode(pointTransactionResult.getTenantCode());
			entity.setCampaignCode(pointTransactionResult.getCampaignCode());
			PointCampaignEntity pointCampaignEntity = pointCampaignMapper.selectOne(entity);
			pointTransactionResult.setProgramName(pointCampaignEntity.getProgramName());
		}


		return new PageData<>(dtos, source.getTotal());
	}

	@Override
	public List<PointAccountCampaignResult.CampaignBalance> getCountPointTransactionCampaign(String tenantCode, String accountCode, List<String> campaignCodes) {
		return pointTransactionMapper.getCountPointTransactionCampaign(tenantCode, accountCode, campaignCodes);
	}

	/**
	 * 获取积分明细信息
	 * 
	 * @return PointTransaction
	 */
	private PointTransactionEntity getPointTransactionModelByCode(String tenantCode, String transactionSn) {
		try {
			PointTransactionEntity pointTransactionEntity = new PointTransactionEntity();
			pointTransactionEntity.setTenantCode(tenantCode);
			pointTransactionEntity.setTransactionSn(transactionSn);
			return pointTransactionMapper.selectOne(pointTransactionEntity);
		} catch (Exception e) {
			log.error(SYS_CODE + ".getPointTransactionModelByCode", e);
		}
		return null;
	}

	/**
	 * 检测积分明细参数
	 * 
	 * @param pointTransactionVo
	 * @return
	 */
	private String checkPointTransaction(PointTransactionParam pointTransactionVo, boolean flag) {

		String msg = "";
		if (null == pointTransactionVo) {
			return "parameter is null";
		}
		if (flag && StringUtil.isEmpty(pointTransactionVo.getTransactionSn())) {
			msg += "【pointTransactionCode】";
		}
		if (StringUtil.isNotEmpty(msg)) {
			msg += " can not be empty .";
		}
		return msg;
	}

	/**
	 * 设置积分明细新增默认值
	 * 
	 * @param pointTransaction
	 */
	private void setPointTransactionDefault(PointTransactionEntity pointTransaction) {

		if (null == pointTransaction)
			return;
		if (StringUtils.isBlank(pointTransaction.getTransactionSn())) {
			pointTransaction.setTransactionSn(codeGenerator.generateCode(pointTransaction.getTenantCode(), TRANSACTION_SN, "PT[D:yyyyMMddHHmmss][SM:%06d]", 1l));
		}


		if (pointTransaction.getTransactionType()==PointTransactionTypeEnum.INCREASE.number()){

			//如果不设置积分过期时间，默认为积分计划的过期时间
			if (null==pointTransaction.getExpiration()){

				PointCampaignEntity entity = BeanCopyUtils.jsonCopyBean(pointTransaction, PointCampaignEntity.class);

				PointCampaignEntity pointCampaign = pointCampaignMapper.selectOne(entity);

				com.gtech.commons.utils.DateUtil.parseDate(pointCampaign.getEndTime(), com.gtech.commons.utils.DateUtil.FORMAT_YYYYMMDDHHMISS_14);

				pointTransaction.setExpiration(pointCampaign.getEndTime());
			}


			//如果是给用户添加临时积分操作，积分要有余额
			pointTransaction.setBalance(pointTransaction.getTransactionAmount());
		}


		pointTransaction.setTransactionDate(Long.parseLong(DateUtil.getDateString(new Date(), DateUtil.DATESTOREFORMAT)));
	}

	/**
	 * 设置积分明细修改默认值
	 * 
	 * @param pointTransaction
	 */
	private void setPointTransactionUpdataDefault(PointTransactionEntity pointTransaction) {
		setPointTransactionDefault(pointTransaction);
		if (null == pointTransaction)
			return;
		pointTransaction.setUpdateTime(new Date());
	}

	/**
	 * 保存积分明细对象
	 * 
	 * @param pointTransaction
	 */
	private void savePointTransactionModel(PointTransactionEntity pointTransaction) {
		if (null == pointTransaction)
			return;
		try {
			pointTransactionMapper.insert(pointTransaction);
		} catch (DuplicateKeyException e) {
			throw new PromotionException(SystemChecker.DUPLICATE_KEY.getCode(), SystemChecker.DUPLICATE_KEY.getMessage());
		} catch (Exception e) {
			throw new PromotionException(SYS_CODE + ".savePointTransactionModel.ex", e.getMessage());
		}

	}

	/**
	 * 更新积分明细对象
	 * 
	 * @param pointTransaction
	 */
	private void updatePointTransactionModel(PointTransactionEntity pointTransaction) {
		if (null == pointTransaction)
			return;
		try {
			pointTransactionMapper.updateByPrimaryKeySelective(pointTransaction);
		} catch (DuplicateKeyException e) {
			throw new PromotionException(SystemChecker.DUPLICATE_KEY.getCode(), SystemChecker.DUPLICATE_KEY.getMessage());
		} catch (Exception e) {
			throw new PromotionException(SYS_CODE + ".updatePointTransactionModel.ex", e.getMessage());
		}
	}

	@Override
	public PointTransactionResult getPointTransaction(GetPointTransactionParam param) {

	    PointTransactionEntity pointTransaction = getPointTransactionModelByCode(param.getTenantCode(), param.getTransactionSn());
		if (pointTransaction == null) {
			return null;
		}
		return BeanCopyUtils.jsonCopyBean(pointTransaction, PointTransactionResult.class);
	}


	private void deductionOfTemporaryPoints(PointTransactionEntity pointTransaction){

		Example example = new Example(PointTransactionEntity.class);
		example.orderBy("expiration").orderBy(PointTransactionEntity.C_CREATE_TIME);
		Example.Criteria criteria = example.createCriteria();

		String nowTime = com.gtech.commons.utils.DateUtil.format(new Date(), DateUtil.DATETIMESTOREFORMAT);

		criteria.andGreaterThanOrEqualTo("expiration",nowTime);


		List<PointTransactionEntity> pointTransactionEntities = pointTransactionMapper.selectByExample(example);

		Integer over = pointTransaction.getTransactionAmount();


		for (PointTransactionEntity e:pointTransactionEntities){
			if (null==e.getBalance()){
				continue;
			}

			//如果临时积分-余额大于0
			if (over-e.getBalance()>=0){
				over-=e.getBalance();
				e.setBalance(0);
				pointTransactionMapper.updateByPrimaryKeySelective(e);
			}
			//如果临时积分-余额小于0
			else if(over-e.getBalance()<0){
				e.setBalance(e.getBalance()-over);
				over=0;
				pointTransactionMapper.updateByPrimaryKeySelective(e);
			}



		}



	}




	@Override
	public List<PointTransactionEntity> queryPointTransactionEndTime(Long maxId,int limit,String nowTime) {

		//查询所有积分流水   判断是否过期   如果过期并且还有余额，增加积分过期流水   扣除用户积分总额相应的积分
		return pointTransactionMapper.selectEndTime(maxId,limit,nowTime);


	}



}
