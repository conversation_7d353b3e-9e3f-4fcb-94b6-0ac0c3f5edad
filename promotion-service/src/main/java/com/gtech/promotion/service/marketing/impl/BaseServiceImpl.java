package com.gtech.promotion.service.marketing.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.gtech.commons.dao.mapper.GTechBaseMapper;
import com.gtech.commons.page.PageData;
import com.gtech.commons.page.PageParam;
import com.gtech.commons.utils.BeanCopyUtils;
import com.gtech.promotion.dao.entity.marketing.BaseEntity;
import com.gtech.promotion.service.marketing.BaseService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;

import java.util.List;

public abstract class BaseServiceImpl<E, M> implements BaseService<E, M> {

    @Autowired
    private GTechBaseMapper<E> mapper;

    private final Class<E> entityClass;
    private final Class<M> modelClass;

    public BaseServiceImpl(Class<E> entityClass, Class<M> modelClass){
        this.entityClass = entityClass;
        this.modelClass = modelClass;
    }

    @Override
    @Transactional
    public int insert(M model) {
        return mapper.insertSelective(BeanCopyUtils.jsonCopyBean(model, entityClass));
    }

    @Override
    @Transactional
    public int deleteByActivityCode(String activityCode) {
        Example example = new Example(entityClass);
        example.createCriteria().andEqualTo(BaseEntity.C_ACTIVITY_CODE, activityCode);
        return mapper.deleteByCondition(example);
    }

    @Override
    public M findByActivityCode(String activityCode) {
        Example example = new Example(entityClass);
        example.createCriteria().andEqualTo(BaseEntity.C_ACTIVITY_CODE, activityCode);
        List<E> entities = mapper.selectByCondition(example);
        if (CollectionUtils.isNotEmpty(entities)){
            return BeanCopyUtils.jsonCopyBean(entities.get(0), modelClass);
        }
        return null;
    }

    @Override
    public List<M> findListByActivityCode(String activityCode) {
        Example example = new Example(entityClass);
        example.createCriteria().andEqualTo(BaseEntity.C_ACTIVITY_CODE, activityCode);
        return BeanCopyUtils.jsonCopyList(mapper.selectByCondition(example), modelClass);
    }


    @Override
    public PageData<M> selectPageList(M model, PageParam pageParam) {
        PageHelper.startPage(pageParam.getPageNum(), pageParam.getPageSize());

        E entity = BeanCopyUtils.jsonCopyBean(model, entityClass);
        PageInfo<E> select = new PageInfo<>(mapper.select(entity));
        return new PageData<>(BeanCopyUtils.jsonCopyList(select.getList(), modelClass), select.getTotal());
    }

}
