package com.gtech.promotion.service.marketing.impl;

import com.gtech.commons.utils.BeanCopyUtils;
import com.gtech.commons.utils.StringUtil;
import com.gtech.promotion.dao.entity.marketing.SharingRecordEntity;
import com.gtech.promotion.dao.mapper.marketing.SharingRecordMapper;
import com.gtech.promotion.dao.model.marketing.BoostSharingModel;
import com.gtech.promotion.dao.model.marketing.SharingRecordModel;
import com.gtech.promotion.dto.in.flashsale.HelpRecordDto;
import com.gtech.promotion.service.marketing.SharingRecordService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;

import java.util.List;

@Service
public class SharingRecordServiceImpl extends BaseServiceImpl<SharingRecordEntity, SharingRecordModel> implements SharingRecordService {
    @Autowired
    private SharingRecordMapper sharingRecordMapper;


    public SharingRecordServiceImpl() {
        super(SharingRecordEntity.class, SharingRecordModel.class);
    }


    @Transactional
    @Override
    public int updateSharingRecord(SharingRecordModel model) {
        SharingRecordEntity sharingRecordEntity = new SharingRecordEntity();
        sharingRecordEntity.setActivityStatus(model.getActivityStatus());
        Example example = new Example(SharingRecordEntity.class);
        example.createCriteria()
                .andEqualTo("sharingRecordCode", model.getSharingRecordCode());
        return sharingRecordMapper.updateByExampleSelective(sharingRecordEntity, example);


    }

    @Override
    public SharingRecordModel findBysharingRecordCode(String sharingRecordCode, String sharingMemberCode) {

        SharingRecordEntity entity = new SharingRecordEntity();
        entity.setSharingRecordCode(sharingRecordCode);
        entity.setSharingMemberCode(sharingMemberCode);
        SharingRecordEntity resultEntity = sharingRecordMapper.selectOne(entity);
        return BeanCopyUtils.jsonCopyBean(resultEntity, SharingRecordModel.class);
    }

    @Override
    public List<SharingRecordEntity> querySharingRecord(SharingRecordModel model) {

        Example example = new Example(SharingRecordEntity.class);

        Example.Criteria criteria = example.createCriteria();

        criteria.andEqualTo("sharingMemberCode", model.getSharingMemberCode());
        criteria.andEqualTo("domainCode", model.getDomainCode());
        criteria.andEqualTo("tenantCode", model.getTenantCode());
        criteria.andEqualTo("orgCode", model.getOrgCode());
        if (StringUtil.isNotEmpty(model.getSharingRecordCode())) {
            criteria.andEqualTo("sharingRecordCode", model.getSharingRecordCode());
        }
        if (StringUtil.isNotEmpty(model.getActivityCode())) {
            criteria.andEqualTo("activityCode", model.getActivityCode());
        }
        if (StringUtil.isNotEmpty(model.getActivityStatus())) {
            criteria.andEqualTo("activityStatus", model.getActivityStatus());
        }
        example.orderBy("createTime").desc();
        return sharingRecordMapper.selectByCondition(example);
    }

    @Override
    public int increaseTheNumberOfPeopleHelping(HelpRecordDto dto, BoostSharingModel boostSharingModel, SharingRecordModel sharingRecordModel) {


        SharingRecordEntity sharingRecordEntity = new SharingRecordEntity();
        sharingRecordEntity.setSharingRecordCode(sharingRecordModel.getSharingRecordCode());
        return sharingRecordMapper.increaseTheNumberOfPeopleHelping(sharingRecordEntity);


    }
}
