/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * <PERSON>ECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.service.impl.coupon;

import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.gtech.commons.utils.BeanCopyUtils;
import com.gtech.commons.utils.StringUtil;
import com.gtech.promotion.dao.entity.coupon.TPromoCouponActivityEntity;
import com.gtech.promotion.dao.mapper.coupon.TPromoCouponActivityMapper;
import com.gtech.promotion.dao.model.activity.ActivityModel;
import com.gtech.promotion.dto.in.coupon.CouponActivityListInDTO;
import com.gtech.promotion.dto.out.coupon.CouponActivityListOutDTO;
import com.gtech.promotion.service.coupon.PromoCouponActivityService;

import tk.mybatis.mapper.entity.Example;
import tk.mybatis.mapper.entity.Example.Criteria;

@Service
public class PromoCouponActivityServiceImpl implements PromoCouponActivityService {

    @Autowired
    private TPromoCouponActivityMapper couponActivityMapper;

    @Override
    @Transactional
    public String createCouponActivity(ActivityModel couponActivityVO) {

        TPromoCouponActivityEntity promoCouponActivityEntity = BeanCopyUtils.jsonCopyBean(couponActivityVO, TPromoCouponActivityEntity.class);
        couponActivityMapper.insertSelective(promoCouponActivityEntity);
        return promoCouponActivityEntity.getId().toString();
    }

    @Override
    @Transactional
    public int updateCouponActivityByActivityCode(ActivityModel couponActivityVO) {

        TPromoCouponActivityEntity entity = BeanCopyUtils.jsonCopyBean(couponActivityVO, TPromoCouponActivityEntity.class);

        Example example = new Example(TPromoCouponActivityEntity.class);
        Criteria criteria = example.createCriteria();
        criteria.andEqualTo("activityCode", entity.getActivityCode());

        return couponActivityMapper.updateByCondition(entity, example);
    }

    @Override
    public PageInfo<CouponActivityListOutDTO> queryCouponActivityList(CouponActivityListInDTO couponActivityListInDTO) {

        PageHelper.startPage(couponActivityListInDTO.getPageNo(), couponActivityListInDTO.getPageCount());

        couponActivityListInDTO.setActivityStatus(this.dealInField(couponActivityListInDTO.getActivityStatus()));
        couponActivityListInDTO.setCouponType(this.dealInField(couponActivityListInDTO.getCouponType()));
        couponActivityListInDTO.setTemplateCodes(this.dealInField(couponActivityListInDTO.getTemplateCodes()));
        couponActivityListInDTO.setOpsType(this.dealInField(couponActivityListInDTO.getOpsType()));

        if (null != couponActivityListInDTO.getCouponCode() && !couponActivityListInDTO.getCouponCode().contains(",")){
            couponActivityListInDTO.setCouponCode(couponActivityListInDTO.getCouponCode()+",");
        }
        couponActivityListInDTO.setCouponCode(this.dealInField(couponActivityListInDTO.getCouponCode()));
        List<CouponActivityListOutDTO> promoCouponsDTOs;
        if (StringUtil.isNotBlank(couponActivityListInDTO.getChannelCode()) || !CollectionUtils.isEmpty(couponActivityListInDTO.getMemberTagList())) {
            promoCouponsDTOs = couponActivityMapper.queryCouponActivityListByQualification(couponActivityListInDTO);
        } else {
            promoCouponsDTOs = couponActivityMapper.queryCouponActivityList(couponActivityListInDTO);
        }
        return new PageInfo<>(promoCouponsDTOs);
    }

    // 组装：一个就是01  多个则是'01','02'
    private String dealInField(String values) {

        if (StringUtils.isBlank(values)) {
            return null;
        }
        if (values.indexOf(',') < 0) {
            return values;
        }

        return "'" + values.replaceAll(",", "', '") + "'";
    }

    /**
     * 根据促销活动ID查询券码活动
     */
    @Override
    public ActivityModel findCouponActivity(String tenantCode, String activityCode) {

        TPromoCouponActivityEntity entity = new TPromoCouponActivityEntity();

        entity.setTenantCode(tenantCode);
        entity.setActivityCode(activityCode);

        return BeanCopyUtils.jsonCopyBean(couponActivityMapper.selectOne(entity), ActivityModel.class);
    }

    @Override
    @Transactional
    public int deleteCouponActivity(String tenantCode, String activityCode) {

        TPromoCouponActivityEntity entity = new TPromoCouponActivityEntity();
        entity.setActivityCode(activityCode);
        entity.setTenantCode(tenantCode);
        return couponActivityMapper.delete(entity);
    }

    @Override
    @Transactional
    public int reserveCouponQuota(String tenantCode, String activityCode, int sum) {
        return couponActivityMapper.reserveCouponQuota(tenantCode, activityCode, sum);
    }

    @Override
    @Transactional
    public int returnCouponQuota(String tenantCode, String activityCode, int sum) {
        return couponActivityMapper.returnCouponQuota(tenantCode, activityCode, sum);
    }

    /**
     * 判断是否存在有效的活动
     *
     */
    @Override
    @Transactional(readOnly = true)
    public ActivityModel findEffectiveActivity(String tenantCode, String activityCode) {

        TPromoCouponActivityEntity entity = new TPromoCouponActivityEntity();

        entity.setActivityCode(activityCode);
        entity.setTenantCode(tenantCode);

        return BeanCopyUtils.jsonCopyBean(this.couponActivityMapper.selectOne(entity), ActivityModel.class);
    }

}
