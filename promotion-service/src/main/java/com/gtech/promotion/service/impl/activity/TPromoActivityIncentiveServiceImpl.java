/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.service.impl.activity;

import com.gtech.commons.code.LogicFlagEnum;
import com.gtech.commons.utils.BeanCopyUtils;
import com.gtech.commons.utils.StringUtil;
import com.gtech.promotion.dao.entity.activity.TPromoActivityIncentiveEntity;
import com.gtech.promotion.dao.mapper.activity.TPromoActivityIncentiveMapper;
import com.gtech.promotion.dao.model.activity.TPromoActivityIncentiveVO;
import com.gtech.promotion.dto.in.activity.ActivityTenantInDTO;
import com.gtech.promotion.service.activity.TPromoActivityIncentiveService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import tk.mybatis.mapper.entity.Example;

import java.util.List;

/**
 * 促销规则奖励
 * 
 */
@Service
public class TPromoActivityIncentiveServiceImpl implements TPromoActivityIncentiveService {

    @Autowired
    private TPromoActivityIncentiveMapper incentiveMapper;

    @Override
    @Transactional
    public Integer insertActivityIncentive(TPromoActivityIncentiveVO activityIncentiveVO) {

        return incentiveMapper.insertSelective(BeanCopyUtils.jsonCopyBean(activityIncentiveVO, TPromoActivityIncentiveEntity.class));
    }

    @Override
    public Long getUserActivityTimes111(String userCode, String activityCode, String incentiveType) {

        TPromoActivityIncentiveEntity entity = new TPromoActivityIncentiveEntity();
        entity.setActivityCode(activityCode);
        if (StringUtil.isNotBlank(userCode)) {
            entity.setUserCode(userCode);
        }
        if (StringUtil.isNotBlank(incentiveType)) {
            entity.setIncentiveType(incentiveType);
        }
        List<TPromoActivityIncentiveEntity> select = incentiveMapper.select(entity);
        long times = 0L;
        if (!CollectionUtils.isEmpty(select)) {
            times = select.stream().mapToInt(TPromoActivityIncentiveEntity::getIncentiveTimes).sum();
        }
        return times;
    }

    @Override
    @Transactional
    public Integer updateOrderIncentiveDeleteStatusByOrderId(Long orderId) {

        Example example = new Example(TPromoActivityIncentiveEntity.class);
        example.createCriteria()
            .andEqualTo(TPromoActivityIncentiveEntity.C_PROMO_ORDER_ID, orderId)
            .andEqualTo(TPromoActivityIncentiveEntity.C_LOGIC_DELETE, LogicFlagEnum.FALSE.code());
        TPromoActivityIncentiveEntity orderIncentive = new TPromoActivityIncentiveEntity();
        orderIncentive.setLogicDelete(LogicFlagEnum.TRUE.number());
        return incentiveMapper.updateByConditionSelective(orderIncentive, example);
    }

    @Override
    public List<TPromoActivityIncentiveEntity> getListByOrderId(String tenantCode, String promoOrderId) {

        TPromoActivityIncentiveEntity entity = new TPromoActivityIncentiveEntity();
        entity.setTenantCode(tenantCode);
        entity.setPromoOrderId(promoOrderId);
        return incentiveMapper.select(entity);
    }

    @Override
    public List<TPromoActivityIncentiveEntity> getListByOrderIds(List<Long> promoOrderIds) {

        Example example = new Example(TPromoActivityIncentiveEntity.class);

        example.createCriteria()
            .andIn(TPromoActivityIncentiveEntity.C_PROMO_ORDER_ID, promoOrderIds);

        return incentiveMapper.selectByCondition(example);
    }

    @Override
    public List<TPromoActivityIncentiveEntity> getListByOrderIds(List<Long> promoOrderIds, List<String> activityList) {
        Example example = new Example(TPromoActivityIncentiveEntity.class);

        example.createCriteria()
                .andIn(TPromoActivityIncentiveEntity.C_PROMO_ORDER_ID, promoOrderIds)
                .andIn(TPromoActivityIncentiveEntity.C_ACTIVITY_CODE, activityList);

        return incentiveMapper.selectByCondition(example);
    }

    @Override
    public double queryDiscountMoneyTotal() {

        return incentiveMapper.queryDiscountMoneyTotal();
    }

    @Override
    public List<TPromoActivityIncentiveEntity> getListByActivityCode(String activityCode) {

        Example example = new Example(TPromoActivityIncentiveEntity.class);
        example.createCriteria()
            .andEqualTo(TPromoActivityIncentiveEntity.C_ACTIVITY_CODE, activityCode);
        return incentiveMapper.selectByCondition(example);
    }

    @Override
    public double getPayOrderDiscountMoneyTotal(ActivityTenantInDTO tenantInDTO) {

        return incentiveMapper.getPayOrderMoneyTotalMapper(tenantInDTO);
    }

}
