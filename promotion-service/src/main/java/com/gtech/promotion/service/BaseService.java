package com.gtech.promotion.service;

import java.util.Map;

import com.github.pagehelper.PageHelper;

public abstract class BaseService {

	/**
	 * 设置分页查询pageBounds参数
	 * 
	 * @param map
	 * @return
	 */
	protected void setPage(Map<String, Object> map) {
		Integer pageNum = (Integer) map.get("pageNum");
		Integer pageSize = (Integer) map.get("pageSize");
		if (pageNum != null && pageSize != null) {
			PageHelper.startPage(pageNum, pageSize);
		}
	}

}
