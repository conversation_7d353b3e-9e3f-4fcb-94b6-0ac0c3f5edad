package com.gtech.promotion.service.marketing.impl;

import com.github.pagehelper.page.PageMethod;
import com.gtech.basic.idm.web.vo.param.QueryUserParam;
import com.gtech.basic.idm.web.vo.result.QueryUserResult;
import com.gtech.commons.result.Result;
import com.gtech.commons.utils.BeanCopyUtils;
import com.gtech.commons.utils.DateUtil;
import com.gtech.commons.utils.StringUtil;
import com.gtech.member.web.vo.param.QueryMemberParam;
import com.gtech.member.web.vo.result.QueryMemberListByConditionResult;
import com.gtech.promotion.code.marketing.BoostSharingEnum;
import com.gtech.promotion.code.marketing.BoostSharingTypeEnum;
import com.gtech.promotion.component.marketing.MarketingCacheComponent;
import com.gtech.promotion.dao.entity.marketing.BoostSharingEntity;
import com.gtech.promotion.dao.entity.marketing.HelpRecordEntity;
import com.gtech.promotion.dao.entity.marketing.SharingRecordEntity;
import com.gtech.promotion.dao.mapper.marketing.BoostSharingMapper;
import com.gtech.promotion.dao.mapper.marketing.HelpRecordMapper;
import com.gtech.promotion.dao.mapper.marketing.SharingRecordMapper;
import com.gtech.promotion.dao.model.marketing.BoostSharingModel;
import com.gtech.promotion.dao.model.marketing.MarketingModel;
import com.gtech.promotion.dao.model.marketing.flashsale.FlashSaleProductModel;
import com.gtech.promotion.dto.boostsharing.ExportBoostSharingDetailDto;
import com.gtech.promotion.dto.boostsharing.ExportBoostSharingTotalDto;
import com.gtech.promotion.feign.IdmFeignClient;
import com.gtech.promotion.feign.MemberFeignClient;
import com.gtech.promotion.service.marketing.BoostSharingService;
import com.gtech.promotion.service.marketing.MarketingService;
import com.gtech.promotion.vo.result.boostsharding.ExportBoostSharingDetailResult;
import com.gtech.promotion.vo.result.boostsharding.ExportBoostSharingTotalResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class BoostSharingServiceImpl extends BaseServiceImpl<BoostSharingEntity,BoostSharingModel> implements BoostSharingService {


    @Autowired
    private BoostSharingMapper boostSharingMapper;

    @Autowired
    private MarketingCacheComponent marketingCacheComponent;
    @Autowired
    private MarketingService marketingService;
    @Autowired
    private SharingRecordMapper sharingRecordMapper;

    @Autowired
    private HelpRecordMapper helpRecordMapper;

    @Autowired
    private MemberFeignClient memberFeignClient;


    @Autowired
    private IdmFeignClient idmFeignClient;

    public BoostSharingServiceImpl() {
        super(BoostSharingEntity.class, BoostSharingModel.class);
    }


    @Override
    public BoostSharingModel findBoostShardingInfo(String domainCode, String tenantCode, String orgCode, String activityCode) {

        Example example = new Example(BoostSharingEntity.class);

        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("domainCode",domainCode);
        criteria.andEqualTo("tenantCode",tenantCode);
        if (StringUtil.isNotBlank(orgCode)){
            criteria.andEqualTo("orgCode",orgCode);
        }
        criteria.andEqualTo("activityCode",activityCode);
        BoostSharingEntity boostSharingEntity = boostSharingMapper.selectOneByExample(example);
        return BeanCopyUtils.jsonCopyBean(boostSharingEntity,BoostSharingModel.class);
    }

    @Override
    public List<BoostSharingModel> queryBoostSharingByActivityList(String tenantCode, List<String> activityCodeList) {

        if (CollectionUtils.isEmpty(activityCodeList)){
            return Collections.emptyList();
        }
        Example example = new Example(BoostSharingEntity.class);

        Example.Criteria criteria = example.createCriteria();

        criteria.andEqualTo("tenantCode",tenantCode);

        criteria.andIn("activityCode",activityCodeList);

        List<BoostSharingEntity> boostSharingEntity = boostSharingMapper.selectByCondition(example);
        return BeanCopyUtils.jsonCopyList(boostSharingEntity,BoostSharingModel.class);
    }

    @Override
    public List<FlashSaleProductModel> getBoostSharingProductsByActivityCodesAndProducts(String tenantCode, ArrayList<String> strings, List<String> skuCodeList) {

        if (CollectionUtils.isEmpty(strings) || CollectionUtils.isEmpty(skuCodeList)){
            return Collections.emptyList();
        }

        Example example = new Example(BoostSharingEntity.class);
        example.createCriteria()
                .andEqualTo("tenantCode",tenantCode)
                .andIn("activityCode",strings)
                .andIn("rightOfFirstRefusalProductCode",skuCodeList);



        List<BoostSharingEntity> boostSharingEntities = boostSharingMapper.selectByCondition(example);

        //获取product信息
        List<FlashSaleProductModel> flashSaleProductModels = new ArrayList<>();
        for (BoostSharingEntity boostSharingEntity : boostSharingEntities) {
            FlashSaleProductModel flashSaleProductModel = new FlashSaleProductModel();
            flashSaleProductModel.setActivityCode(boostSharingEntity.getActivityCode());
            flashSaleProductModel.setProductCode(boostSharingEntity.getRightOfFirstRefusalProductCode());
            flashSaleProductModels.add(flashSaleProductModel);
        }

        return flashSaleProductModels;

    }

    @Override
    public ExportBoostSharingTotalResult exportBoostSharingTotal(ExportBoostSharingTotalDto dto) {


        BoostSharingEntity entity = new BoostSharingEntity();
        entity.setActivityCode(dto.getActivityCode());
        entity.setDomainCode(dto.getDomainCode());
        entity.setTenantCode(dto.getTenantCode());
        BoostSharingEntity boostSharingEntity = boostSharingMapper.selectOne(entity);
        if (null == boostSharingEntity){
            return new ExportBoostSharingTotalResult();
        }
        MarketingModel marketingCache = marketingCacheComponent.getMarketingCache(dto.getTenantCode(), dto.getActivityCode());
        if (marketingCache == null){
            marketingCache = marketingService.findByActivityCode(dto.getActivityCode());
        }

        Example sharingExample = new Example(BoostSharingEntity.class);
        sharingExample.createCriteria()
                .andEqualTo("domainCode",dto.getDomainCode())
                .andEqualTo("tenantCode",dto.getTenantCode())
                .andEqualTo("activityCode",dto.getActivityCode());
        List<SharingRecordEntity> sharingRecordEntities = sharingRecordMapper.selectByCondition(sharingExample);
        Example helpExample = new Example(HelpRecordEntity.class);
        helpExample.createCriteria()
                .andEqualTo("domainCode",dto.getDomainCode())
                .andEqualTo("tenantCode",dto.getTenantCode())
                .andEqualTo("activityCode",dto.getActivityCode())
                .andIn("sharingRecordCode",sharingRecordEntities.stream().map(SharingRecordEntity::getSharingRecordCode).collect(Collectors.toList()));

        List<HelpRecordEntity> helpRecordEntities = helpRecordMapper.selectByCondition(helpExample);



        /*活动名称
        活动时间
        活动类型
        数据统计时间
        当前发起活动人数
        发起活动成功数
        发起活动未成功数
        助力人数*/
        ExportBoostSharingTotalResult result = new ExportBoostSharingTotalResult();
        result.setActivityName(marketingCache.getActivityName());
        result.setActivityType(BoostSharingTypeEnum.getEnumByCode(boostSharingEntity.getBoostSharingType()).desc());
        if (StringUtil.isNotEmpty(marketingCache.getActivityBegin()) && StringUtil.isNotEmpty(marketingCache.getActivityEnd())){
            result.setActivityTime(DateUtil.format(DateUtil.parseDate(marketingCache.getActivityBegin(),DateUtil.FORMAT_YYYYMMDDHHMISS_14), DateUtil.FORMAT_YYYYMMDDHHMISS) + " - " + DateUtil.format(DateUtil.parseDate(marketingCache.getActivityEnd(),DateUtil.FORMAT_YYYYMMDDHHMISS_14), DateUtil.FORMAT_YYYYMMDDHHMISS));
        }
        result.setDataStatisticsTime(DateUtil.format(new Date(), DateUtil.FORMAT_YYYYMMDDHHMISS));
        result.setCurrentActivityNumber(String.valueOf(sharingRecordEntities.size()));
        result.setActivitySuccessNumber(String.valueOf(sharingRecordEntities.stream().filter(x ->x.getActivityStatus().equals(BoostSharingEnum.FINISH.code())).count()));
        result.setActivityUnsuccessfulNumber(String.valueOf(sharingRecordEntities.stream().filter(x ->!x.getActivityStatus().equals(BoostSharingEnum.FINISH.code())).count()));
        result.setBoostNumber(String.valueOf(helpRecordEntities.size()));

        return result;

    }

    @Override
    public List<ExportBoostSharingDetailResult> exportBoostSharingDetail(ExportBoostSharingDetailDto dto) {

        /*发起分享的ID
        发起分享的时间
        发起人会员账号
        发起人昵称
        发起人手机号
        助力时间
        助力人会员账号
        助力人昵称
        助力人手机号*/
        PageMethod.startPage(dto.getPageNum(), dto.getPageSize());
        Example sharingExample = new Example(SharingRecordEntity.class);
        sharingExample.createCriteria()
                .andEqualTo("domainCode",dto.getDomainCode())
                .andEqualTo("tenantCode",dto.getTenantCode())
                .andEqualTo("activityCode",dto.getActivityCode());
        List<SharingRecordEntity> sharingRecordEntities = sharingRecordMapper.selectByCondition(sharingExample);

        if (CollectionUtils.isEmpty(sharingRecordEntities)){
            return new ArrayList<>();
        }


        Example helpExample = new Example(HelpRecordEntity.class);
        helpExample.createCriteria()
                .andEqualTo("domainCode",dto.getDomainCode())
                .andEqualTo("tenantCode",dto.getTenantCode())
                .andEqualTo("activityCode",dto.getActivityCode())
                .andIn("sharingRecordCode",sharingRecordEntities.stream().map(SharingRecordEntity::getSharingRecordCode).collect(Collectors.toList()));
        List<HelpRecordEntity> helpRecordEntities = helpRecordMapper.selectByCondition(helpExample);

        if(CollectionUtils.isEmpty(helpRecordEntities)){
            return new ArrayList<>();
        }


        List<String> helpMemberCodeList = helpRecordEntities.stream().map(x -> x.getHelpMemberCode()).collect(Collectors.toList());
        List<String> sharingMemberCodeList = helpRecordEntities.stream().map(x -> x.getSharingMemberCode()).collect(Collectors.toList());

        List<String> memberCodeList = ListUtils.union(helpMemberCodeList, sharingMemberCodeList).stream().distinct().collect(Collectors.toList());
        QueryMemberParam queryMemberParam = new QueryMemberParam();
        queryMemberParam.setMemberCodes(memberCodeList);
        queryMemberParam.setDomainCode(dto.getDomainCode());
        queryMemberParam.setTenantCode(dto.getTenantCode());
        Result<List<QueryMemberListByConditionResult>> listResult = memberFeignClient.queryMemberListByMemberCode(queryMemberParam);
        Map<String, QueryMemberListByConditionResult> memberMap = new HashMap<>();
        Map<String, QueryUserResult> userMap = new HashMap<>();
        if (listResult.isSuccess()){
            List<QueryMemberListByConditionResult> data = listResult.getData();
            memberMap = data.stream().collect(Collectors.toMap(QueryMemberListByConditionResult::getMemberCode, x -> x));

            QueryUserParam param = new QueryUserParam();
            //memberCodeList转换为逗号分隔
            param.setUserCodes(String.join(",", memberCodeList));
            param.setDomainCode(dto.getDomainCode());
            param.setTenantCode(dto.getTenantCode());
            Result<List<QueryUserResult>> userList = idmFeignClient.queryUserList(param);
            if (userList.isSuccess()){
                userMap = userList.getData().stream().collect(Collectors.toMap(QueryUserResult::getUserCode, x -> x));
            }


        }


        Map<String, List<HelpRecordEntity>> helpRecordEntityMap = helpRecordEntities.stream().collect(Collectors.groupingBy(HelpRecordEntity::getSharingRecordCode));

        ArrayList<ExportBoostSharingDetailResult> exportBoostSharingDetailResults = new ArrayList<>();
        for (SharingRecordEntity sharingRecordEntity : sharingRecordEntities) {
            List<HelpRecordEntity> helpRecordEntitys = helpRecordEntityMap.get(sharingRecordEntity.getSharingRecordCode());
            ExportBoostSharingDetailResult result = new ExportBoostSharingDetailResult();


            //分享人信息
            result.setSharingRecordCode(sharingRecordEntity.getSharingRecordCode());
            result.setSharingTime(DateUtil.format(sharingRecordEntity.getCreateTime(),DateUtil.FORMAT_YYYYMMDDHHMISS));
            //result.setSharingMemberCode(sharingRecordEntity.getSharingMemberCode());
            if (userMap.containsKey(sharingRecordEntity.getSharingMemberCode())){
                result.setSharingMemberCode(userMap.get(sharingRecordEntity.getSharingMemberCode()).getAccount());
            }
            if (memberMap.containsKey(sharingRecordEntity.getSharingMemberCode())){
                QueryMemberListByConditionResult queryMemberListByConditionResult = memberMap.get(sharingRecordEntity.getSharingMemberCode());
                //result.setSharingMemberName(queryMemberListByConditionResult.getFirstName()+" "+queryMemberListByConditionResult.getLastName());
                if (StringUtil.isNotBlank(memberMap.get(sharingRecordEntity.getSharingMemberCode()).getFirstName())){
                    result.setSharingMemberName(memberMap.get(sharingRecordEntity.getSharingMemberCode()).getFirstName());
                }
                if (StringUtil.isNotBlank(memberMap.get(sharingRecordEntity.getSharingMemberCode()).getLastName())){
                    result.setSharingMemberName(result.getSharingMemberName()+" "+memberMap.get(sharingRecordEntity.getSharingMemberCode()).getLastName());
                }

                result.setSharingMemberPhone(queryMemberListByConditionResult.getMobile());
            }

            //如果没有助力人助力信息为空
            if (CollectionUtils.isEmpty(helpRecordEntitys)){
                exportBoostSharingDetailResults.add(result);
                continue;
            }

            //多个助力人信息
            for (HelpRecordEntity helpRecordEntity : helpRecordEntitys) {
                //助力人信息
                result.setBoostTime((DateUtil.format(helpRecordEntity.getCreateTime(),DateUtil.FORMAT_YYYYMMDDHHMISS)));
                //result.setBoostMemberCode(helpRecordEntity.getHelpMemberCode());
                if (userMap.containsKey(sharingRecordEntity.getSharingMemberCode())){
                    result.setBoostMemberCode(userMap.get(helpRecordEntity.getHelpMemberCode()).getAccount());
                }
                if (memberMap.containsKey(helpRecordEntity.getHelpMemberCode())){
                    QueryMemberListByConditionResult queryMemberListByConditionResult = memberMap.get(helpRecordEntity.getHelpMemberCode());
                    //result.setBoostMemberName(queryMemberListByConditionResult.getFirstName()+" "+queryMemberListByConditionResult.getLastName());

                    if (StringUtil.isNotBlank(memberMap.get(helpRecordEntity.getHelpMemberCode()).getFirstName())){
                        result.setBoostMemberName(memberMap.get(helpRecordEntity.getHelpMemberCode()).getFirstName());
                    }
                    if (StringUtil.isNotBlank(memberMap.get(helpRecordEntity.getHelpMemberCode()).getLastName())){
                        result.setBoostMemberName(result.getBoostMemberName()+" "+memberMap.get(helpRecordEntity.getHelpMemberCode()).getLastName());
                    }
                    result.setBoostMemberPhone(queryMemberListByConditionResult.getMobile());
                }
                ExportBoostSharingDetailResult exportBoostSharingDetailResult = BeanCopyUtils.jsonCopyBean(result, ExportBoostSharingDetailResult.class);
                exportBoostSharingDetailResults.add(exportBoostSharingDetailResult);

            }


        }
        return exportBoostSharingDetailResults;
    }
}
