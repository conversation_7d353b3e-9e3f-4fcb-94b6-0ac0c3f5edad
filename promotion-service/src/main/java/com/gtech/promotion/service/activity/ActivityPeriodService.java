package com.gtech.promotion.service.activity;

import com.gtech.promotion.dao.model.activity.ActivityPeriodModel;

import java.util.List;

public interface ActivityPeriodService {

    int createPeriod(ActivityPeriodModel activityPeriodModel);

    int deletePeriod(String tenantCode, String activityCode);

    ActivityPeriodModel findPeriod(String tenantCode, String activityCode);

    List<ActivityPeriodModel> queryPeriodByActivityCodes(String tenantCode, List<String> activityCodes);
}
