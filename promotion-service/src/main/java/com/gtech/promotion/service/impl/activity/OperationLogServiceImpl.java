package com.gtech.promotion.service.impl.activity;

import com.gtech.commons.redis.GTechCodeGenerator;
import com.gtech.commons.utils.BeanCopyUtils;
import com.gtech.commons.utils.CryptoUtils;
import com.gtech.promotion.dao.entity.activity.OperationLogContentEntity;
import com.gtech.promotion.dao.entity.activity.OperationLogEntity;
import com.gtech.promotion.dao.mapper.activity.OperationLogContentMapper;
import com.gtech.promotion.dao.mapper.activity.OperationLogMapper;
import com.gtech.promotion.dao.model.activity.OperationLogModel;
import com.gtech.promotion.dto.in.activity.QueryOperationLogsByActivityCodeDTO;
import com.gtech.promotion.dto.out.activity.QueryOperationLogsByActivityCodeOutDTO;
import com.gtech.promotion.service.activity.OperationLogService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;

import java.util.List;

@Service
public class OperationLogServiceImpl implements OperationLogService {

    private static final String OPERATION_CODE = "operationCode";

    @Autowired
    private OperationLogMapper operationLogMapper;

    @Autowired
    private OperationLogContentMapper contentMapper;

    @Autowired
    GTechCodeGenerator codeGenerator;

    private static final String MIX_SALT = "26a711f75b90055fe169fca73a0e0f03";

    @Override
    @Transactional
    public int insertLog(OperationLogModel operationLogModel, String jsonString) {
        OperationLogEntity operationLogEntity = BeanCopyUtils.jsonCopyBean(operationLogModel, OperationLogEntity.class);
        operationLogEntity.setOperationCode(codeGenerator.generateCode(operationLogModel.getTenantCode(), OPERATION_CODE, "OL[D:yyyyMMddHHmmss][SM:%06d]", 1l));
        operationLogEntity.setCreateFirstName(CryptoUtils.aesEncrypt(operationLogModel.getCreateFirstName(), MIX_SALT));
        operationLogEntity.setCreateLastName(CryptoUtils.aesEncrypt(operationLogModel.getCreateLastName(), MIX_SALT));
        operationLogMapper.insertSelective(operationLogEntity);

        OperationLogContentEntity entity = BeanCopyUtils.jsonCopyBean(operationLogEntity, OperationLogContentEntity.class);
        entity.setContent(jsonString);
        return contentMapper.insertSelective(entity);
    }

    @Override
    public List<QueryOperationLogsByActivityCodeOutDTO> queryOperationLogsByActivityCode(QueryOperationLogsByActivityCodeDTO dto) {
        Example example = new Example(OperationLogEntity.class);
        example.createCriteria().andEqualTo(OperationLogEntity.C_TENANT_CODE, dto.getTenantCode())
                .andEqualTo(OperationLogEntity.C_ACTIVITY_CODE, dto.getActivityCode());
        example.orderBy(OperationLogEntity.C_ID).desc();
        List<OperationLogEntity> source = operationLogMapper.selectByCondition(example);

        decrypt(source);
        return BeanCopyUtils.jsonCopyList(source, QueryOperationLogsByActivityCodeOutDTO.class);
    }

    private void decrypt(List<OperationLogEntity> entities) {
        for (OperationLogEntity entity : entities) {
            entity.setCreateLastName(CryptoUtils.aesDecrypt(entity.getCreateLastName(), MIX_SALT));
            entity.setCreateFirstName(CryptoUtils.aesDecrypt(entity.getCreateFirstName(), MIX_SALT));
        }
    }
}
