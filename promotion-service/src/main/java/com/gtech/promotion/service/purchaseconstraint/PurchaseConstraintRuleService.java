package com.gtech.promotion.service.purchaseconstraint;

import com.gtech.promotion.dao.model.purchaseconstraint.PurchaseConstraintRuleModel;

import java.util.List;

public interface PurchaseConstraintRuleService {

    int insertList(List<PurchaseConstraintRuleModel> purchaseConstraintRuleModelList);


    int deletePurchaseConstraintRule(String tenantCode, String purchaseConstraintCode);

    List<PurchaseConstraintRuleModel> queryPurchaseConstraintRuleByCode(String tenantCode, String purchaseConstraintCode);
}
