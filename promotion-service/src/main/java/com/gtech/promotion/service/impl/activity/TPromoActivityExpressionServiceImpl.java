/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.service.impl.activity;

import com.gtech.commons.utils.StringUtil;
import com.gtech.promotion.checker.activity.ActivityExpressionChecker;
import com.gtech.promotion.dao.entity.activity.TPromoActivityExpressionEntity;
import com.gtech.promotion.dao.mapper.activity.TPromoActivityExpressionMapper;
import com.gtech.promotion.exception.Check;
import com.gtech.promotion.helper.ActivityRedisHelpler;
import com.gtech.promotion.service.activity.TPromoActivityExpressionService;
import com.gtech.promotion.utils.Constants;
import com.gtech.promotion.utils.ExpressionHelper;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Set;

/**
 * 互斥叠加优先级表达式服务实现类
 * 
 */
@Service
public class TPromoActivityExpressionServiceImpl implements TPromoActivityExpressionService{

    @Autowired
    private TPromoActivityExpressionMapper expressionMapper;

    @Autowired
    private ActivityRedisHelpler redisService;

    @Autowired
    private StringRedisTemplate redisTemplate;

    @Override
    @Transactional
    public String getActivityExpression(String tenantCode) {

        String expression = redisService.getExpression(tenantCode);
        if (StringUtil.isBlank(expression) || ExpressionHelper.isExpiredVersion(expression)) {

            TPromoActivityExpressionEntity expressionEntity = selectExpression(tenantCode);
            if (null == expressionEntity) {//数据库为空走默认
                expression = ExpressionHelper.DEF_EXPROSSION;
                this.addActivityExpression(tenantCode, expression);
            } else if (ExpressionHelper.isExpiredVersion(expressionEntity.getExpression())) {
                expression = ExpressionHelper.DEF_EXPROSSION;
                this.updateActivityExpression(tenantCode, expression);
            } else {
                expression = expressionEntity.getExpression();
            }
            redisService.setExpression(tenantCode, expression);
        }

        return expression;
    }

    @Override
    public TPromoActivityExpressionEntity selectExpression(String tenantCode){

        TPromoActivityExpressionEntity entity = new TPromoActivityExpressionEntity();
        entity.setTenantCode(tenantCode);

        return expressionMapper.selectOne(entity);
    }

    @Override
    @Transactional
    public void addActivityExpression(String tenantCode,String expression){

        TPromoActivityExpressionEntity entity = new TPromoActivityExpressionEntity();
        entity.setTenantCode(tenantCode);
        entity.setExpression(expression);

        expressionMapper.insertSelective(entity);
        redisService.setExpression(tenantCode, expression);
    }

    @Override
    @Transactional
    public void updateActivityExpression(String tenantCode,String expression){

        TPromoActivityExpressionEntity entity = new TPromoActivityExpressionEntity();
        entity.setTenantCode(tenantCode);
        entity.setExpression(expression);

        TPromoActivityExpressionEntity expressionEntity = selectExpression(entity.getTenantCode());
        Check.check(expressionEntity == null, ActivityExpressionChecker.NOT_NULL);
        entity.setId(expressionEntity.getId());

        expressionMapper.updateByPrimaryKeySelective(entity);
        redisService.setExpression(tenantCode, expression);
    }

    @Override
    @Transactional
    public void addAllActivityExpression(){

        Set<String> keys = redisService.getAll(Constants.PROMOTION_ACTIVITY_EXPRESSION.concat("*"));
        redisTemplate.delete(keys);

        List<TPromoActivityExpressionEntity> selectAll = expressionMapper.selectAll();
        if (CollectionUtils.isEmpty(selectAll)){
            return;
        }

        for (TPromoActivityExpressionEntity entity : selectAll){
            redisService.setExpression(entity.getTenantCode(), entity.getExpression());
        }
    }

}
