package com.gtech.promotion.service.activity;

import com.gtech.promotion.dao.model.activity.ActivityGroupRelationVO;
import com.gtech.promotion.dao.model.activity.GroupRelationVO;
import com.gtech.promotion.dao.model.activity.QueryGroupRelationVO;

import java.util.List;

/**
 * @Description :
 * <AUTHOR> gaojie.li
 * @Date : 2023/7/12 10:34
 */
public interface PromotionGroupRelationService {


    /**
     * 分组关系创建
     * @param tenantCode
     * @param domainCode
     * @param relation
     * @return
     */
    int createGroupRelation(String tenantCode, String domainCode,GroupRelationVO relation);

    /**
     * 删除分组关系
     * @param tenantCode
     * @param domainCode
     * @param relation
     * @return
     */
    int deleteGroupRelation(String tenantCode, String domainCode,GroupRelationVO relation);

    /**
     * 分组对应的互斥关系
     * @param relationVO
     * @return
     */
    List<ActivityGroupRelationVO> queryListGroupRelationByGroupCodeA(QueryGroupRelationVO relationVO);


    /**
     * 分组A对应的互斥关系
     * @param tenantCode
     * @param groupCodeA
     * @return
     */
    List<ActivityGroupRelationVO> queryGroupRelationByGroupCodeA(String tenantCode, List<String> groupCodeA);

    /**
     * 根据编码删除分组之间的关系
     * @param tenantCode
     * @param domainCode
     * @param groupCode
     * @return
     */
    int deleteGroupRelationByGroupCode(String tenantCode, String domainCode,String groupCode);


    /**
     * 保存前清除所有关系数据
     * @param tenantCode
     * @param domainCode
     * @return
     */
    int deleteGroupRelationByTenantCode(String tenantCode, String domainCode);


    /**
     * 查询分组是否存在互斥关系
     * @param tenantCode
     * @param groupCodeA
     * @param groupCodeB
     * @return
     */
    int queryGroupRelationByGroupCodeAB(String tenantCode, String groupCodeA,String groupCodeB);



}
