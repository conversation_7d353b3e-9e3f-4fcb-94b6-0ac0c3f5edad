package com.gtech.promotion.service.flashsale;

import com.gtech.promotion.dao.entity.marketing.flashsale.FlashSaleOrderDetailEntity;
import com.gtech.promotion.dao.model.marketing.flashsale.FlashSaleOrderDetailModel;
import com.gtech.promotion.service.marketing.BaseService;

import java.util.List;

public interface FlashSaleOrderDetailService extends BaseService<FlashSaleOrderDetailEntity, FlashSaleOrderDetailModel> {

    List<FlashSaleOrderDetailModel> findByOrderNo(String tenantCode, String orderNo);


    List<FlashSaleOrderDetailModel> findByOrderNoList(String tenantCode, List<String> orderNo);


}
