/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.service.impl.activity;

import java.util.List;

import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.gtech.commons.utils.BeanCopyUtils;
import com.gtech.commons.utils.StringUtil;
import com.gtech.promotion.dao.entity.activity.TPromoActivityLanguageEntity;
import com.gtech.promotion.dao.mapper.activity.TPromoActivityLanguageMapper;
import com.gtech.promotion.dao.model.activity.ActivityLanguageModel;
import com.gtech.promotion.dao.model.activity.ActivityModel;
import com.gtech.promotion.service.activity.ActivityLanguageService;

import tk.mybatis.mapper.entity.Example;
import tk.mybatis.mapper.entity.Example.Criteria;

/**
 * 多语言活动表service实现
 */
@Service
public class TPromoActivityLanguageServiceImpl implements ActivityLanguageService {

    @Autowired
    private TPromoActivityLanguageMapper languageMapper;

    /**
     * Replace the multilingual attributes.
     */
    @Override
    public void replaceField(ActivityModel activityModel, String language) {

        if (StringUtil.isNotBlank(language) && null != activityModel) {
            ActivityLanguageModel activityLanguage = findActivityLanguage(activityModel.getTenantCode(), activityModel.getActivityCode(), language);
            if (activityLanguage != null) {
                activityModel.setLanguage(activityLanguage);
            }
        }
    }

    @Override
    public ActivityLanguageModel findActivityLanguage(String tenantCode, String activityCode, String language) {
        
        if (StringUtils.isBlank(tenantCode) || StringUtils.isBlank(activityCode) || StringUtils.isBlank(language)) {
            return null;
        }

        TPromoActivityLanguageEntity entity = new TPromoActivityLanguageEntity();

        entity.setActivityCode(activityCode);
        entity.setTenantCode(tenantCode);
        entity.setLanguage(language);

        return BeanCopyUtils.jsonCopyBean(languageMapper.selectOne(entity), ActivityLanguageModel.class);
    }

    @Override
    public List<ActivityLanguageModel> queryActivityLanguages(String tenantCode, String activityCode) {

        TPromoActivityLanguageEntity entity = new TPromoActivityLanguageEntity();
        entity.setActivityCode(activityCode);
        entity.setTenantCode(tenantCode);

        return BeanCopyUtils.jsonCopyList(languageMapper.select(entity), ActivityLanguageModel.class);
    }

    @Override
    @Transactional
    public void insertOne(ActivityLanguageModel activityLanguageModel) {

        languageMapper.insertSelective(BeanCopyUtils.jsonCopyBean(activityLanguageModel, TPromoActivityLanguageEntity.class));
    }

    @Override
    @Transactional
    public void deleteByActivityCode(String activityCode) {

        TPromoActivityLanguageEntity entity = new TPromoActivityLanguageEntity();
        entity.setActivityCode(activityCode);
        languageMapper.delete(entity);
    }

    @Override
	public List<ActivityLanguageModel> queryActivityLanguagesByActivityCodes(String tenantCode, String language, List<String> activityCodes) {
        Example example = new Example(TPromoActivityLanguageEntity.class);
		Criteria criteria = example.createCriteria();
		criteria.andEqualTo("tenantCode", tenantCode)
                .andIn("activityCode", activityCodes);
		if (StringUtil.isNotEmpty(language)) {
			criteria.andEqualTo("language", language);
		}
        return BeanCopyUtils.jsonCopyList(languageMapper.selectByCondition(example), ActivityLanguageModel.class);
    }

}
