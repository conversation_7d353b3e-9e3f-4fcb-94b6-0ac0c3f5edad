package com.gtech.promotion.service.marketing.impl;

import com.gtech.commons.utils.BeanCopyUtils;
import com.gtech.commons.utils.StringUtil;
import com.gtech.promotion.dao.entity.marketing.BaseEntity;
import com.gtech.promotion.dao.entity.marketing.PrizeRecordEntity;
import com.gtech.promotion.dao.mapper.marketing.PrizeRecordMapper;
import com.gtech.promotion.dao.model.marketing.PrizeRecordModel;
import com.gtech.promotion.dao.model.marketing.QueryRecordByTicketsModel;
import com.gtech.promotion.service.marketing.PrizeRecordService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import java.util.List;

@Service
public class PrizeRecordServiceImpl extends BaseServiceImpl<PrizeRecordEntity, PrizeRecordModel> implements PrizeRecordService {

    public PrizeRecordServiceImpl() {
        super(PrizeRecordEntity.class, PrizeRecordModel.class);
    }

    @Autowired
    private PrizeRecordMapper recordMapper;

    @Override
    public List<PrizeRecordModel> queryLuckyRecordList(QueryRecordByTicketsModel queryRecordByTicketsModel) {

        Example example = new Example(PrizeRecordEntity.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo(BaseEntity.C_DOMAIN_CODE, queryRecordByTicketsModel.getDomainCode())
            .andEqualTo(BaseEntity.C_TENANT_CODE, queryRecordByTicketsModel.getTenantCode())
            .andEqualTo(BaseEntity.C_ORG_CODE, queryRecordByTicketsModel.getOrgCode())
            .andEqualTo(BaseEntity.C_ACTIVITY_CODE, queryRecordByTicketsModel.getActivityCode())
            ;
        if (StringUtil.isNotBlank(queryRecordByTicketsModel.getMemberCode())) {
            criteria.andEqualTo(PrizeRecordEntity.C_MEMBER_CODE, queryRecordByTicketsModel.getMemberCode());
        }
        if (CollectionUtils.isNotEmpty(queryRecordByTicketsModel.getTicketCodes())) {
            criteria.andIn(PrizeRecordEntity.C_TICKET_CODE, queryRecordByTicketsModel.getTicketCodes());
        }

        return BeanCopyUtils.jsonCopyList(recordMapper.selectByCondition(example), PrizeRecordModel.class);
    }
}
