package com.gtech.promotion.service.growth.impl;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.github.pagehelper.PageInfo;
import com.gtech.commons.redis.GTechCodeGenerator;
import com.gtech.commons.result.PageResult;
import com.gtech.commons.utils.BeanCopyUtils;
import com.gtech.commons.utils.DateUtil;
import com.gtech.promotion.checker.SystemChecker;
import com.gtech.promotion.dao.entity.growth.GrowthAccountEntity;
import com.gtech.promotion.dao.entity.point.PointAccountEntity;
import com.gtech.promotion.dao.mapper.growth.GrowthAccountMapper;
import com.gtech.promotion.exception.PromotionException;
import com.gtech.promotion.service.BaseService;
import com.gtech.promotion.service.growth.GrowthAccountSaveService;
import com.gtech.promotion.service.growth.GrowthAccountService;
import com.gtech.promotion.service.growth.GrowthTransactionService;
import com.gtech.promotion.utils.Constants.ErrorCodePrefix;
import com.gtech.promotion.vo.param.growth.CreateGrowthAccountParam;
import com.gtech.promotion.vo.param.growth.GrowthTransactionParam;
import com.gtech.promotion.vo.param.growth.UpdateGrowthParam;
import com.gtech.promotion.vo.param.growth.query.GetGrowthAccountParam;
import com.gtech.promotion.vo.param.growth.query.GrowthAccountUniqueParam.GrowthAccountStatusUniqueVo;
import com.gtech.promotion.vo.result.growth.GrowthAccountResult;

import lombok.extern.slf4j.Slf4j;
import tk.mybatis.mapper.entity.Example;
import tk.mybatis.mapper.entity.Example.Criteria;
@Slf4j
@Service
public class GrowthAccountServiceImpl extends BaseService implements GrowthAccountService {

	private static final String SYS_CODE = "GrowthAccountServiceImpl";

	private static final String GROWTH_ACCOUNT_CODE = "growthAccountCode";


	@Autowired
	GrowthAccountMapper growthAccountMapper;

	@Autowired
	GrowthTransactionService growthTransactionService;

	@Autowired
	GTechCodeGenerator codeGenerator;

	@Autowired
	private GrowthAccountSaveService growthAccountSaveService;




	@Override
    @Transactional
	public int updateGrowthAccountStatus(GrowthAccountStatusUniqueVo growthAccountStatusUniqueVo) {

		return updateStatusGrowthAccountModel(growthAccountStatusUniqueVo.getTenantCode(), growthAccountStatusUniqueVo.getGrowthAccountCode(), growthAccountStatusUniqueVo.getStatus(),
				growthAccountStatusUniqueVo.getOldStatus());
	}

	@Override
    @Transactional
	public int updateGrowthAccount(CreateGrowthAccountParam param) {

        Example example = new Example(GrowthAccountEntity.class);

        Criteria criteria = example.createCriteria();

        criteria.andEqualTo(PointAccountEntity.C_DOMAIN_CODE, param.getDomainCode());
        criteria.andEqualTo(PointAccountEntity.C_TENANT_CODE, param.getTenantCode());

        criteria.andEqualTo(PointAccountEntity.C_ACCOUNT_TYPE, param.getAccountType());
        criteria.andEqualTo(PointAccountEntity.C_ACCOUNT_CODE, param.getAccountCode());

        GrowthAccountEntity record = BeanCopyUtils.jsonCopyBean(param, GrowthAccountEntity.class);
        record.setDomainCode(null);
        record.setTenantCode(null);
        record.setAccountType(null);
        record.setAccountCode(null);

        return this.growthAccountMapper.updateByExampleSelective(record, example);
	}

	@Override
	public GrowthAccountResult getGrowthAccount(GetGrowthAccountParam param) {
	    
	    GrowthAccountEntity growthAccount = this.growthAccountMapper.selectOne(GrowthAccountEntity.builder().domainCode(param.getDomainCode()).tenantCode(param.getTenantCode())
	        .accountType(param.getAccountType()).accountCode(param.getAccountCode()).build());

		return BeanCopyUtils.jsonCopyBean(growthAccount, GrowthAccountResult.class);
	}

	@Override
	public GrowthAccountResult getGrowthAccountByAccount(String tenantCode, String accountCode, Integer accountType) {

		GrowthAccountEntity record = new GrowthAccountEntity();
		record.setTenantCode(tenantCode);
		record.setAccountCode(accountCode);
		record.setAccountType(accountType);
		GrowthAccountEntity growthAccount = growthAccountMapper.selectOne(record);
		if (growthAccount == null) {//NOSONAR
			return null;//NOSONAR
		}//NOSONAR
		return BeanCopyUtils.jsonCopyBean(growthAccount, GrowthAccountResult.class);
	}

	@Override
	public PageResult<GrowthAccountResult> queryGrowthAccountPage(Map<String, Object> map) {
		// 分页查询
		setPage(map);
		map.put("order", true);
		List<GrowthAccountEntity> growthAccountList = queryGrowthAccountModelPage(map);
		PageInfo<GrowthAccountEntity> pageInfo = new PageInfo<>(growthAccountList);
		List<GrowthAccountResult> growthAccountVoList = BeanCopyUtils.jsonCopyList(pageInfo.getList(), GrowthAccountResult.class);
		return new PageResult<>(growthAccountVoList, pageInfo.getTotal());
	}

	/**
	 * 更新成长值状态
	 *
	 * @param status
	 * @param oldStatus
	 *
	 */
	private int updateStatusGrowthAccountModel(String tenantCode, String growthAccountCode, Integer status, Integer oldStatus) {

		if (StringUtils.isEmpty(growthAccountCode) || StringUtils.isEmpty(tenantCode))//NOSONAR
			return 0;//NOSONAR
		Map<String, Object> param = new HashMap<>();
		param.put("tenantCode", tenantCode);
		param.put(GROWTH_ACCOUNT_CODE, growthAccountCode);
		param.put("status", oldStatus);
		int i = 0;
		try {
			GrowthAccountEntity record = new GrowthAccountEntity();
			record.setStatus(status);
			Example example = new Example(GrowthAccountEntity.class);
			example.createCriteria().andAllEqualTo(param);
			i = growthAccountMapper.updateByExampleSelective(record, example);
		} catch (Exception e) {//NOSONAR
			throw new PromotionException(SYS_CODE + ".updateStatusGrowthAccountModel.ex", e.getMessage());//NOSONAR
		}//NOSONAR
		if (i <= 0) {//NOSONAR
			throw new PromotionException(SystemChecker.NULL_UPDATE.getCode(), SystemChecker.NULL_UPDATE.getMessage());//NOSONAR
		}//NOSONAR

		return i;
	}

	/**
	 * 分页查询成长值
	 * 
	 * @param parammap
	 * @return GrowthAccount
	 */
	private List<GrowthAccountEntity> queryGrowthAccountModelPage(Map<String, Object> parammap) {
		List<GrowthAccountEntity> list = null;
		try {
			list = growthAccountMapper.query(parammap);
		} catch (Exception e) {//NOSONAR
			log.error(SYS_CODE + ".queryGrowthAccountModel", e);//NOSONAR
		}//NOSONAR
		return list;
	}

	@SuppressWarnings("unchecked")
	@Override
	@Transactional
	public int updateGrowth(UpdateGrowthParam growthChangeVo) {
		// 更新成长值
		Map<String, Object> param = BeanCopyUtils.jsonCopyBean(growthChangeVo, Map.class);


		//查询是否有该账户，如果没有该账户，自动添加账户
		List<GrowthAccountEntity> query = growthAccountMapper.query(param);
		if (query.isEmpty()){//NOSONAR
			CreateGrowthAccountParam accountEntity = BeanCopyUtils.jsonCopyBean(growthChangeVo, CreateGrowthAccountParam.class);
			accountEntity.setValidBeginTime(new Date());
			accountEntity.setValidEndTime(DateUtil.addMonth(6));
			growthAccountSaveService.saveGrowthAccount(accountEntity);
		}

		//修改账户
		int count = growthAccountMapper.updateGrowth(param);
		if (count == 0) {//NOSONAR
			throw new PromotionException(ErrorCodePrefix.GROWTH_ACCOUNT_CHECKER.getCodePrefix(), "Insufficient remaining growth");//NOSONAR
		}//NOSONAR


		GrowthAccountEntity entity = BeanCopyUtils.jsonCopyBean(growthChangeVo,GrowthAccountEntity.class);
		GrowthAccountEntity growthAccountEntity = growthAccountMapper.selectOne(entity);


		// 新增积分流水
		GrowthTransactionParam growthTransactionVo = BeanCopyUtils.jsonCopyBean(growthChangeVo, GrowthTransactionParam.class);
		growthTransactionVo.setCreateTime(growthChangeVo.getTransactionCreateTime());
		growthTransactionVo.setCreateUser(growthChangeVo.getTransactionCreateUser());
		//前后账户余额
		growthTransactionVo.setBeforeBalance(growthAccountEntity.getAccountBalance());
		if (growthChangeVo.getTransactionType()==1){//NOSONAR
			growthTransactionVo.setAfterBalance(growthAccountEntity.getAccountBalance() + growthTransactionVo.getTransactionAmount());//NOSONAR
		}else if(growthChangeVo.getTransactionType()==2){//NOSONAR
			growthTransactionVo.setAfterBalance(growthAccountEntity.getAccountBalance() - growthTransactionVo.getTransactionAmount());//NOSONAR
		}
		growthTransactionService.saveGrowthTransaction(growthTransactionVo);

		return count;
	}
}
