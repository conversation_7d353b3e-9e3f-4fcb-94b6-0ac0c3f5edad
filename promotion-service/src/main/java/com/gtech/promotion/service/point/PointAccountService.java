package com.gtech.promotion.service.point;

import com.gtech.commons.result.PageResult;
import com.gtech.promotion.vo.param.point.UpdatePointAccountParam;
import com.gtech.promotion.vo.param.point.UpdatePointParam;
import com.gtech.promotion.vo.param.point.query.GetPointAccountCampaignParam;
import com.gtech.promotion.vo.param.point.query.GetPointAccountParam;
import com.gtech.promotion.vo.param.point.query.PointAccountUniqueParam.PointAccountStatusUniqueVo;
import com.gtech.promotion.vo.result.point.PointAccountCampaignResult;
import com.gtech.promotion.vo.result.point.PointAccountResult;

import java.util.Map;

public interface PointAccountService {

	/**
	 * 修改积分账户
	 * 
	 * @param pointAccountVo
	 */
	int updatePointAccount(UpdatePointAccountParam pointAccountVo);

	/**
	 * 修改积分账户状态
	 * 
	 * @param code
	 * @param status
	 * @param oldStatus
	 */
	int updatePointAccountStatus(PointAccountStatusUniqueVo pointAccountStatusUniqueVo);

	/**
	 * 根据Code查询积分账户
	 */
	PointAccountResult getPointAccount(GetPointAccountParam param);

	/**
	 * 分页查询积分账户
	 * 
	 * @param map
	 * @return queryResult<PointAccountResult>
	 */
	PageResult<PointAccountResult> queryPointAccountPage(Map<String, Object> map);

	/**
	 * 积分变更
	 * 
	 * @param pointChangeVo
	 */
	int updatePoint(UpdatePointParam pointChangeVo);

	/**
	 * 变更account积分
	 * @param param
	 * @return
	 */
	int increaseOrDecreasePoint(UpdatePointParam param);

    PointAccountCampaignResult getPointAccountCampaign(GetPointAccountCampaignParam param);
}
