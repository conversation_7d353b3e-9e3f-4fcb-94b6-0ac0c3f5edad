/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.service.impl.activity;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.shardingsphere.api.hint.HintManager;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.gtech.commons.utils.BeanCopyUtils;
import com.gtech.commons.utils.DateUtil;
import com.gtech.commons.utils.StringUtil;
import com.gtech.promotion.code.activity.ActivityStatusEnum;
import com.gtech.promotion.code.activity.ActivityTypeEnum;
import com.gtech.promotion.dao.entity.activity.ActivityEntity;
import com.gtech.promotion.dao.entity.marketing.MarketingEntity;
import com.gtech.promotion.dao.mapper.activity.ActivityMapper;
import com.gtech.promotion.dao.model.activity.ActivityModel;
import com.gtech.promotion.dto.in.activity.ActivityTimeDTO;
import com.gtech.promotion.dto.in.activity.GroupBindingActivityVO;
import com.gtech.promotion.dto.in.activity.GroupQueryListVO;
import com.gtech.promotion.dto.in.activity.TPromoActivityListInDTO;
import com.gtech.promotion.dto.in.activity.UpdateExternalActivityInDTO;
import com.gtech.promotion.dto.out.activity.GroupActivityVO;
import com.gtech.promotion.dto.out.activity.TPromoActivityOutDTO;
import com.gtech.promotion.page.RequestPage;
import com.gtech.promotion.service.activity.ActivityLanguageService;
import com.gtech.promotion.service.activity.ActivityService;
import com.gtech.promotion.service.activity.ActivityStoreService;
import com.gtech.promotion.vo.param.activity.QueryListParam;
import com.gtech.promotion.vo.param.activity.QueryPromoListByStoreParam;
import com.gtech.promotion.vo.result.activity.QueryListResult;

import tk.mybatis.mapper.entity.Example;
import tk.mybatis.mapper.entity.Example.Criteria;

@Service
public class ActivityServiceImpl implements ActivityService {

    public static final String ORG_CODE = "orgCode";
    @Autowired
    private ActivityMapper activityMapper;

    @Autowired
    private ActivityStoreService storeService;//店铺渠道

    @Autowired
    private ActivityLanguageService languageService;//店铺渠道


    private static final String CREATE_TIME = "createTime";

    private static final String ACTIVITY_BEGIN = "activityBegin";

    private static final String ACTIVITY_END = "activityEnd";

    private static final String WARM_BEGIN = "warmBegin";

    private static final String WARM_END = "warmEnd";

    private static final String ACTIVITY_STATUS = "activityStatus";

    private static final String ACTIVITY_TYPE = "activityType";

    @Override
    @Transactional
    public String createPromoActivity(ActivityModel tPromoActivity) {
        ActivityEntity activityEntity = BeanCopyUtils.jsonCopyBean(tPromoActivity, ActivityEntity.class);
        activityEntity.setPeriodType("00");
        activityEntity.setActivityStatus("01");
        activityMapper.insertSelective(activityEntity);

        return activityEntity.getId().toString();
    }

    @Override
    @Transactional
    public int updatePromoActivity(ActivityModel activityModel) {
        ActivityEntity activityEntity = BeanCopyUtils.jsonCopyBean(activityModel, ActivityEntity.class);
        return activityMapper.updateByPrimaryKeySelective(activityEntity);
    }

    /**
     * Update activity status by activityCode.
     */
    @Override
    @Transactional
    public int updateActivityStatus(String tenantCode, String activityCode, String activityStatus,String operator) {

        ActivityEntity entity = new ActivityEntity();
        entity.setActivityStatus(activityStatus);
        entity.setUpdateUser(operator);
        if(ActivityStatusEnum.PENDING.code().equals(activityStatus)){
            entity.setCreateUser(operator);
        }
        if(ActivityStatusEnum.IN_AUDIT.code().equals(activityStatus)){
            entity.setAuditUser(operator);
        }
        Example example = new Example(ActivityEntity.class);
        example.createCriteria()
            .andEqualTo(ActivityEntity.C_TENANT_CODE, tenantCode)
            .andEqualTo(ActivityEntity.C_ACTIVITY_CODE, activityCode);

        return activityMapper.updateByConditionSelective(entity, example);
    }

    /**
     * 查询促销活动
     * 
     * @param activityStatus -- Activity status. (Optional)
     */
    @Override
    public ActivityModel findActivity(String tenantCode, String activityCode, String activityStatus) {

        if (StringUtils.isBlank(tenantCode) || StringUtils.isBlank(activityCode)) {
            return null;
        }

        ActivityEntity entity = new ActivityEntity();

        entity.setTenantCode(tenantCode);
        entity.setActivityCode(activityCode);
        if (StringUtils.isNotBlank(activityStatus)) {
            entity.setActivityStatus(activityStatus);
        }

        return BeanCopyUtils.jsonCopyBean(activityMapper.selectOne(entity), ActivityModel.class);
    }

    /**
     * 查询Effective状态的促销活动(Readonly)
     */
    @Override
    @Transactional(readOnly = true)
    public ActivityModel findEffectiveActivity(String tenantCode, String activityCode) {

        if (StringUtils.isBlank(tenantCode) || StringUtils.isBlank(activityCode)) {
            return null;
        }

        ActivityEntity entity = new ActivityEntity();

        entity.setTenantCode(tenantCode);
        entity.setActivityCode(activityCode);
        entity.setActivityStatus(ActivityStatusEnum.EFFECTIVE.code());

        return BeanCopyUtils.jsonCopyBean(activityMapper.selectOne(entity), ActivityModel.class);
    }

    /**
     * 查询Effective状态的促销活动(Readonly)
     */
    @Override
    @Transactional(readOnly = true)
    public List<ActivityModel> queryEffectiveActivity(String tenantCode, List<String> activityCodes) {

        if (StringUtils.isBlank(tenantCode) || CollectionUtils.isEmpty(activityCodes)) {
            return new ArrayList<>();
        }

        Example example = new Example(ActivityEntity.class);

        Criteria criteria = example.createCriteria();

        criteria.andEqualTo(ActivityEntity.C_TENANT_CODE, tenantCode);
        criteria.andIn(ActivityEntity.C_ACTIVITY_CODE, activityCodes);
        criteria.andEqualTo(ActivityEntity.C_ACTIVITY_STATUS, ActivityStatusEnum.EFFECTIVE.code());

        return BeanCopyUtils.jsonCopyList(activityMapper.selectByCondition(example), ActivityModel.class);
    }

    /**
     * 根据活动Code列表 批量查询活动(含已经过期的活动)
     */
    @Override
    public List<ActivityModel> queryActivityByActivityCodes(String tenantCode, List<String> activityCodes) {

        Example example = new Example(ActivityEntity.class);
        Criteria criteria = example.createCriteria();
        criteria.andEqualTo(ActivityEntity.C_TENANT_CODE, tenantCode);
        criteria.andIn(ActivityEntity.C_ACTIVITY_CODE, activityCodes);
        return BeanCopyUtils.jsonCopyList(activityMapper.selectByCondition(example), ActivityModel.class);
    }

    @Override
	public PageInfo<ActivityModel> queryAllActivity(TPromoActivityListInDTO inDTO, RequestPage page, List<String> templateCodes) {
		// 查询条件
		Example example = new Example(ActivityEntity.class);
		// 去重
		example.setDistinct(true);
		switch (StringUtil.isBlank(inDTO.getOrderByType()) ? "01" : inDTO.getOrderByType()) {
		case "02":
			example.orderBy(CREATE_TIME).asc();
			break;
		case "03":
			example.orderBy(ACTIVITY_BEGIN).desc();
			break;
		case "04":
			example.orderBy(ACTIVITY_BEGIN).asc();
			break;
		case "05":
			example.orderBy(ACTIVITY_END).desc();
			break;
		case "06":
			example.orderBy(ACTIVITY_END).asc();
			break;
		default:
			example.orderBy(CREATE_TIME).desc();
			break;
		}
		Criteria criteria = example.createCriteria();
		criteria.andEqualTo(ActivityEntity.C_TENANT_CODE, inDTO.getTenantCode());
		//org protal登录
		if (StringUtils.isNotBlank(inDTO.getActivityOrgCode())) {
            criteria.andEqualTo(ORG_CODE, inDTO.getActivityOrgCode());
        }

		if (CollectionUtils.isNotEmpty(templateCodes)) {
			criteria.andIn("templateCode", templateCodes);
		}
		// 减少复杂度
        getCriteriaCondition(inDTO, criteria);

		if (StringUtil.isNotBlank(inDTO.getActivityStatus())) {
			List<String> activityStatusList = Arrays.asList(inDTO.getActivityStatus().split(","));
			inDTO.setActivityStatusList(activityStatusList);
			criteria.andIn(ACTIVITY_STATUS, activityStatusList);
		}
		if (StringUtil.isNotBlank(inDTO.getOrgCode()) && inDTO.getDefaultFlag()) {
			// 不为空 匹配店铺为default的活动 和 店铺对应的活动
			// 将店铺不匹配的活动 去掉，剩下的就是匹配的
			List<String> activityCodes = storeService.selectNotByCondition(inDTO.getTenantCode(), inDTO.getOrgCode());
			if (CollectionUtils.isNotEmpty(activityCodes)) {
				criteria.andNotIn(ActivityEntity.C_ACTIVITY_CODE, activityCodes);
			}
		}

        if (!inDTO.getDefaultFlag()){
            criteria.andNotEqualTo(ORG_CODE,"default");
        }
		// 分页
		List<ActivityEntity> selectByCondition = null;
		PageHelper.startPage(page.getPageNo(), page.getPageCount());
		if (StringUtil.isNotBlank(inDTO.getChannelCode())) {
			selectByCondition = activityMapper.selectByConditionAndChannel(inDTO);
		} else {
			selectByCondition = activityMapper.selectByCondition(example);
		}
        return getActivityModelPageInfo(selectByCondition, inDTO.getLanguage());
    }

    @Override
    public PageInfo<GroupActivityVO> queryActivityListUnderGroup(GroupQueryListVO vo, RequestPage page) {

        // 查询条件
        Example example = new Example(ActivityEntity.class);

        example.orderBy(CREATE_TIME).desc();

        Criteria criteria = example.createCriteria();
        criteria.andEqualTo(ActivityEntity.C_TENANT_CODE, vo.getTenantCode())
                .andIsNotNull(ActivityEntity.C_GROUP_CODE);

        if (StringUtil.isNotBlank(vo.getActivityName())) {
            criteria.andLike("activityName", "%" + vo.getActivityName() + "%");
        }

        if (StringUtil.isNotEmpty(vo.getGroupCode())){
            criteria.andEqualTo(ActivityEntity.C_GROUP_CODE, vo.getGroupCode());
        }

        if (StringUtils.isNotBlank(vo.getActivityOrgCode())) {
            criteria.andEqualTo(ORG_CODE, vo.getActivityOrgCode());
        }

        if (CollectionUtils.isNotEmpty(vo.getActivityStatus())) {
            criteria.andIn(ACTIVITY_STATUS, vo.getActivityStatus());
        }
        PageHelper.startPage(page.getPageNo(), page.getPageCount());
        List<ActivityEntity>  selectByCondition = activityMapper.selectByCondition(example);

        //分页数据转换
        PageInfo<ActivityModel> activityModelPageInfo = getActivityModelPageInfo(selectByCondition, vo.getLanguage());
        List<GroupActivityVO> groupActivityVOS = BeanCopyUtils.jsonCopyList(activityModelPageInfo.getList(), GroupActivityVO.class);
        PageInfo<GroupActivityVO> pageInfo = new PageInfo<>();
        pageInfo.setList(groupActivityVOS);
        pageInfo.setTotal(activityModelPageInfo.getTotal());
        return pageInfo;
    }

    public PageInfo<ActivityModel> getActivityModelPageInfo(List<ActivityEntity> activityEntities, String language) {
        PageInfo<ActivityEntity> pageInfo = new PageInfo<>(activityEntities);
        List<ActivityModel> list = BeanCopyUtils.jsonCopyList(activityEntities, ActivityModel.class);
        for (ActivityModel tPromoActivityVO : list) {
            String activityName = tPromoActivityVO.getActivityName();
            languageService.replaceField(tPromoActivityVO, language);
            //活动名称，不根据语言显示活动名称
            tPromoActivityVO.setActivityName(activityName);
        }
        PageInfo<ActivityModel> pageInfo2 = new PageInfo<>();
        pageInfo2.setList(list);
        pageInfo2.setTotal(pageInfo.getTotal());
        return pageInfo2;
    }

    public void getCriteriaCondition(TPromoActivityListInDTO inDTO, Criteria criteria) {

        if (StringUtils.isNotBlank(inDTO.getActivityType())) {
            criteria.andEqualTo(ACTIVITY_TYPE, inDTO.getActivityType());
        }

        if (StringUtil.isNotBlank(inDTO.getActivityName())) {
            criteria.andLike("activityName", "%" + inDTO.getActivityName() + "%");
        }
        if (StringUtil.isNotBlank(inDTO.getOpsType())) {
            List<String> opsTypeList = Arrays.asList(inDTO.getOpsType().split(","));
            inDTO.setOpsTypeList(opsTypeList);
            criteria.andIn("opsType", opsTypeList);
        }

        if (StringUtil.isNotBlank(inDTO.getSponsors())) {
            criteria.andLike("sponsors", inDTO.getSponsors() + "%");
        }
        if (StringUtil.isNotBlank(inDTO.getActivityCode())) {
            criteria.andEqualTo("activityCode", inDTO.getActivityCode());
        }
        // 活动开始时间段
        ActivityTimeDTO activityTimeDTO = BeanCopyUtils.jsonCopyBean(inDTO, ActivityTimeDTO.class);
        queryCriteriaTime(criteria, activityTimeDTO);
    }


    public void queryCriteriaTime(Criteria criteria, ActivityTimeDTO timeDTO) {
        // 活动开始时间段
        if (StringUtil.isNotBlank(timeDTO.getActivityBeginFrom())) {
            criteria.andGreaterThanOrEqualTo(ACTIVITY_BEGIN, timeDTO.getActivityBeginFrom());
        }
        if (StringUtil.isNotBlank(timeDTO.getActivityBeginTo())) {
            criteria.andLessThanOrEqualTo(ACTIVITY_BEGIN, timeDTO.getActivityBeginTo());
        }
        // 活动结束时间段
        if (StringUtil.isNotBlank(timeDTO.getActivityEndFrom())) {
            criteria.andGreaterThanOrEqualTo(ACTIVITY_END, timeDTO.getActivityEndFrom());
        }
        if (StringUtil.isNotBlank(timeDTO.getActivityEndTo())) {
            criteria.andLessThanOrEqualTo(ACTIVITY_END, timeDTO.getActivityEndTo());
        }
        // 创建时间段
        if (StringUtil.isNotBlank(timeDTO.getCreateTimeFrom())) {
            criteria.andGreaterThanOrEqualTo(CREATE_TIME, timeDTO.getCreateTimeFrom());
        }
        if (StringUtil.isNotBlank(timeDTO.getCreateTimeTo())) {
            criteria.andLessThanOrEqualTo(CREATE_TIME, timeDTO.getCreateTimeTo());
        }
        // 活动开始预热时间段
        if (!StringUtil.isEmpty(timeDTO.getWarmBeginFrom())) {
            criteria.andGreaterThanOrEqualTo(WARM_BEGIN, timeDTO.getWarmBeginFrom());
        }
        if (!StringUtil.isEmpty(timeDTO.getWarmBeginTo())) {
            criteria.andLessThanOrEqualTo(WARM_BEGIN, timeDTO.getWarmBeginTo());
        }
        // 活动预热结束时间段
        if (!StringUtil.isEmpty(timeDTO.getWarmEndFrom())) {
            criteria.andGreaterThanOrEqualTo(WARM_END, timeDTO.getWarmEndFrom());
        }
        if (!StringUtil.isEmpty(timeDTO.getWarmBeginTo())) {
            criteria.andLessThanOrEqualTo(WARM_END, timeDTO.getWarmBeginTo());
        }
    }

    @Override
    @Transactional
    public int endPromoActivity() {

        ActivityEntity entity = new ActivityEntity();
        entity.setActivityStatus("05");

        Example example = new Example(ActivityEntity.class);
        Criteria criteria = example.createCriteria();
        criteria.andLessThanOrEqualTo(ACTIVITY_END, DateUtil.getCurrentDateAsString(DateUtil.FORMAT_YYYYMMDDHHMISS_14));
        criteria.andEqualTo(ACTIVITY_STATUS, ActivityStatusEnum.EFFECTIVE.code());

        return activityMapper.updateByConditionSelective(entity, example);
    }

    @Override
    public List<ActivityModel> queryActivityByTenantCode(String tenantCode, ActivityTypeEnum activityType, RequestPage page, ActivityStatusEnum...status) {

        Example example = new Example(ActivityEntity.class);
        Criteria criteria = example.createCriteria();
        criteria.andEqualTo(ActivityEntity.C_TENANT_CODE, tenantCode);
        if (null != activityType) {
            criteria.andEqualTo(ACTIVITY_TYPE, activityType.code());
        }
        if (null != status && status.length > 0) {
            if (status.length == 1) {
                criteria.andEqualTo(ACTIVITY_STATUS, status[0].code());
            } else {
                List<String> codes = new ArrayList<>();
                for (ActivityStatusEnum activityStatusEnum : status) {
                    codes.add(activityStatusEnum.code());
                }
                criteria.andIn(ACTIVITY_STATUS, codes);
            }
        }
        if (null != page) {
            PageHelper.startPage(page.getPageNo(), page.getPageCount());
        }

        return BeanCopyUtils.jsonCopyList(activityMapper.selectByCondition(example), ActivityModel.class);//这个方法是存redis的 返回值不能转多语言
    }

    @Override
    public List<ActivityModel> queryEffectiveActivityByTenantCode(String tenantCode) {

        Example example = new Example(ActivityEntity.class);
        String now = DateUtil.getCurrentDateAsString(DateUtil.FORMAT_YYYYMMDDHHMISS_14);
        Criteria criteria = example.createCriteria();
        criteria.andEqualTo(ActivityEntity.C_TENANT_CODE, tenantCode)
                .andEqualTo(ActivityEntity.C_ACTIVITY_STATUS,ActivityStatusEnum.EFFECTIVE.code())
                .andGreaterThan(ActivityEntity.C_ACTIVITY_END,now)
                .andLessThan(ActivityEntity.C_ACTIVITY_BEGIN,now);
        return BeanCopyUtils.jsonCopyList(activityMapper.selectByCondition(example), ActivityModel.class);
    }

    @Override
    public ActivityModel findActivityByActivityCode(String tenantCode, String activityCode) {

        if (!HintManager.isMasterRouteOnly()) {
            HintManager.clear();
            HintManager hintManager = HintManager.getInstance();
            hintManager.setMasterRouteOnly();
        }
        ActivityEntity entity = new ActivityEntity();
        entity.setTenantCode(tenantCode);
        entity.setActivityCode(activityCode);
        ActivityEntity activityEntity = activityMapper.selectOne(entity);
        HintManager.clear();
        return BeanCopyUtils.jsonCopyBean(activityEntity, ActivityModel.class);
    }

    @Override
    @Transactional
    public int deleteActivity111(String tenantCode, String activityCode) {

        ActivityEntity entity = new ActivityEntity();
        entity.setActivityCode(activityCode);
        entity.setTenantCode(tenantCode);
        return activityMapper.delete(entity);
    }

    @Override
    public List<ActivityEntity> queryActivityByCodes(String tenantCode, List<String> codes) {

        Example example = new Example(ActivityEntity.class);

        example.createCriteria()
            .andEqualTo(ActivityEntity.C_TENANT_CODE, tenantCode)
            .andIn(ActivityEntity.C_ACTIVITY_CODE, codes);

        return activityMapper.selectByCondition(example);
    }

    @Override
    public List<ActivityEntity> queryActivityByTenantCodeAndStatusAndType(String tenantCode, Integer activityStatus, ActivityTypeEnum activityType) {

        Example example = new Example(ActivityEntity.class);
        Criteria criteria = example.createCriteria();
        criteria.andEqualTo(ActivityEntity.C_TENANT_CODE, tenantCode);
        if (null != activityType) {
            criteria.andEqualTo(ACTIVITY_TYPE, activityType.code());
        }
        if (null != activityStatus && activityStatus.equals(1)) {//查询沙箱数据
            List<String> statusList = new ArrayList<>();
            statusList.add(ActivityStatusEnum.PENDING.code());
            statusList.add(ActivityStatusEnum.IN_AUDIT.code());
            statusList.add(ActivityStatusEnum.REJECTED.code());
            statusList.add(ActivityStatusEnum.EFFECTIVE.code());
            statusList.add(ActivityStatusEnum.SUSPEND.code());
            criteria.andIn(ACTIVITY_STATUS, statusList);
        } else {
            criteria.andEqualTo(ACTIVITY_STATUS, ActivityStatusEnum.EFFECTIVE.code());
        }
        return activityMapper.selectByCondition(example);
    }

    @Override
    public long effectActivity() {

        ActivityEntity entity = new ActivityEntity();
        entity.setActivityStatus(ActivityStatusEnum.EFFECTIVE.code());
        return activityMapper.selectCount(entity);
    }

    @Override
    public long accumulativeTotal(List<String> tenantCodes) {

        Example example = new Example(ActivityEntity.class);
        Criteria criteria = example.createCriteria();
        if (!CollectionUtils.isEmpty(tenantCodes)) {
            criteria.andNotIn(ActivityEntity.C_TENANT_CODE, tenantCodes);
        }
        return activityMapper.selectCountByCondition(example);
    }

    @Override
    public long accumulativeTotal(String startTime, String endTime, List<String> tenantCodes) {

        Example example = new Example(ActivityEntity.class);
        Criteria criteria = example.createCriteria();
        if (StringUtil.isNotBlank(startTime)) {
            criteria.andGreaterThanOrEqualTo(CREATE_TIME, startTime);
        }
        if (StringUtil.isNotBlank(endTime)) {
            criteria.andLessThanOrEqualTo(CREATE_TIME, endTime);
        }
        if (!CollectionUtils.isEmpty(tenantCodes)) {
            criteria.andNotIn(ActivityEntity.C_TENANT_CODE, tenantCodes);
        }
        return activityMapper.selectCountByCondition(example);
    }

    @Override
    public long queryEffectActivityByTenantCode(String tenantCode) {

        ActivityEntity entity = new ActivityEntity();
        entity.setTenantCode(tenantCode);
        entity.setActivityStatus(ActivityStatusEnum.EFFECTIVE.code());
        return activityMapper.selectCount(entity);
    }

    @Override
    public List<ActivityModel> queryActivityByTenantCodeAndStatusAndTime(String tenantCode, String activityStatus, String activityType,String orgCode) {

        String currentDateAsString = DateUtil.getCurrentDateAsString(DateUtil.FORMAT_YYYYMMDDHHMISS_14);
        Example example = new Example(ActivityEntity.class);
        Criteria criteria = example.createCriteria();
        criteria.andEqualTo(ActivityEntity.C_TENANT_CODE, tenantCode);
        if(StringUtil.isNotBlank(orgCode)){
            criteria.andEqualTo(ORG_CODE, orgCode);
        }
        criteria.andEqualTo(ACTIVITY_STATUS, activityStatus);
        criteria.andEqualTo(ACTIVITY_TYPE, activityType);
        criteria.andLessThanOrEqualTo(ACTIVITY_BEGIN, currentDateAsString);
        criteria.andGreaterThan(ACTIVITY_END, currentDateAsString);
        example.orderBy(ACTIVITY_BEGIN).desc();
        return BeanCopyUtils.jsonCopyList(activityMapper.selectByCondition(example), ActivityModel.class);

    }

    @Override
    public PageInfo<TPromoActivityOutDTO> queryPromoListByStore(QueryPromoListByStoreParam param) {
        PageHelper.startPage(param.getPageNum(), param.getPageSize());
        List<TPromoActivityOutDTO> tPromoActivityOutDTOS = activityMapper.queryPromoListByStore(param.getTenantCode(), param.getOrgCode(), param.getActivityStatus(), param.getActivityType());
        return new PageInfo<>(tPromoActivityOutDTOS);
    }

    @Override
    public PageInfo<QueryListResult> queryList(QueryListParam param) {
        PageHelper.startPage(param.getPageNum(), param.getPageSize());
        return new PageInfo<>(activityMapper.queryList(param));
    }

    @Override
    public int expireActivity() {
        ActivityEntity entity = new ActivityEntity();
        entity.setActivityStatus(ActivityStatusEnum.CLOSURE.code());

        Example example = new Example(ActivityEntity.class);
        example.createCriteria().andEqualTo(MarketingEntity.ACTIVITY_STATUS, ActivityStatusEnum.EFFECTIVE.code())
                .andLessThanOrEqualTo(MarketingEntity.ACTIVITY_END, DateUtil.getCurrentDateAsString(DateUtil.FORMAT_YYYYMMDDHHMISS_14));
        return activityMapper.updateByConditionSelective(entity, example);
    }

    @Override
    public int queryPromotionCategoryCount(String tenantCode, String promotionCategory) {
        Example example = new Example(ActivityEntity.class);
        Criteria criteria = example.createCriteria();
        criteria.andEqualTo(ActivityEntity.C_TENANT_CODE, tenantCode)
                .andEqualTo(ActivityEntity.C_PROMOTION_CATEGORY,promotionCategory);
        return activityMapper.selectCountByCondition(example);
    }

    @Override
    @Transactional
    public int updatePromotionCategoryNull(String tenantCode, String promotionCategory) {
        return activityMapper.updatePromotionCategoryNull(tenantCode,promotionCategory);
    }

    @Override
    public String findActivityNewByActivityCode(String tenantCode, List<String> activityCodes) {

        Example example = new Example(ActivityEntity.class);
        example.orderBy(CREATE_TIME).desc();
        Criteria criteria = example.createCriteria();
        criteria.andEqualTo(ActivityEntity.C_TENANT_CODE, tenantCode)
                .andEqualTo(ActivityEntity.C_ACTIVITY_STATUS,ActivityStatusEnum.EFFECTIVE.code())
                .andIn(ActivityEntity.C_ACTIVITY_CODE,activityCodes);
        List<ActivityEntity> activityEntities = activityMapper.selectByCondition(example);
        if (CollectionUtils.isEmpty(activityEntities)){
            return null;
        }
        return activityEntities.get(0).getActivityCode();
    }

    @Override
    public List<ActivityModel> queryActivityByGroupCode(String tenantCode, String groupCode) {

        Example example = new Example(ActivityEntity.class);
        Criteria criteria = example.createCriteria();
        criteria.andEqualTo(ActivityEntity.C_TENANT_CODE, tenantCode)
                .andEqualTo(ActivityEntity.C_ACTIVITY_STATUS,ActivityStatusEnum.EFFECTIVE.code())
                .andEqualTo(ActivityEntity.C_GROUP_CODE,groupCode);
        List<ActivityEntity> activityEntities = activityMapper.selectByCondition(example);
        return BeanCopyUtils.jsonCopyList(activityEntities,ActivityModel.class);
    }

    @Override
    @Transactional
    public int bindingActivityToGroup(GroupBindingActivityVO vo) {

        Example example = new Example(ActivityEntity.class);
        Criteria criteria = example.createCriteria();
        criteria.andEqualTo(ActivityEntity.C_TENANT_CODE, vo.getTenantCode())
                .andEqualTo(ActivityEntity.C_DOMAIN_CODE,vo.getDomainCode())
                .andEqualTo(ActivityEntity.C_ACTIVITY_CODE,vo.getActivityCode());

        ActivityEntity activityEntity = new ActivityEntity();
		activityEntity.setGroupCode(vo.getGroupCode());

        return activityMapper.updateByConditionSelective(activityEntity,example);
    }

    @Override
    @Transactional
    public int updateExternalActivityId(UpdateExternalActivityInDTO dto) {

        Example example = new Example(ActivityEntity.class);
        Criteria criteria = example.createCriteria();
        criteria.andEqualTo(ActivityEntity.C_TENANT_CODE, dto.getTenantCode())
                .andEqualTo(ActivityEntity.C_DOMAIN_CODE,dto.getDomainCode())
                .andEqualTo(ActivityEntity.C_ACTIVITY_CODE,dto.getActivityCode());

        ActivityEntity activityEntity = new ActivityEntity();
        activityEntity.setExternalActivityId(dto.getExternalActivityId());
        activityEntity.setUpdateUser(dto.getOperateUser());

        return activityMapper.updateByConditionSelective(activityEntity,example);
    }

	@Override
	public List<ActivityEntity> queryActivityLimitTenant() {

		return activityMapper.queryActivityLimitTenant();
	}
}
