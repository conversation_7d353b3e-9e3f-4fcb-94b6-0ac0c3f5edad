/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.service.activity;

import com.gtech.promotion.dao.entity.activity.TPromoActivityIncentiveEntity;
import com.gtech.promotion.dao.model.activity.TPromoActivityIncentiveVO;
import com.gtech.promotion.dto.in.activity.ActivityTenantInDTO;

import java.util.List;

/**
 * 促销活动奖励
 */
public interface TPromoActivityIncentiveService {

    /**
     * 插入数据
     * 
     * @param activityIncentiveVO 促销规则奖励数据
     */
    Integer insertActivityIncentive(TPromoActivityIncentiveVO activityIncentiveVO);

    /**
     * 获取活动参与次数
     * 
     * @param userCode 用户编码(为空时,总次数)
     * @param activityId 活动id
     * @param incentiveType 促销类型
     */
    Long getUserActivityTimes111(String userId, String activityCode, String incentiveType);

    /**
     * 取消订单修，改订单相关活动奖励表数据 ， 逻辑删除状态改为1
     */
    Integer updateOrderIncentiveDeleteStatusByOrderId(Long orderId);

    /**
     * 根据订单id获取活动奖励
     */
    List<TPromoActivityIncentiveEntity> getListByOrderId(String tenantCode, String promoOrderId);

    /**
     * 根据订单id批量查询多条活动奖励
     */
    List<TPromoActivityIncentiveEntity> getListByOrderIds(List<Long> promoOrderIds);
    List<TPromoActivityIncentiveEntity> getListByOrderIds(List<Long> promoOrderIds,List<String> activityList);

    /**
     * 获取计算优惠总金额
     */
    double queryDiscountMoneyTotal();

    /**
     * 根据活动ID查询奖励信息
     */
    List<TPromoActivityIncentiveEntity> getListByActivityCode(String activityCode);

    /**
     * 订单支付优惠总金额
     */
    double getPayOrderDiscountMoneyTotal(ActivityTenantInDTO tenantInDTO);
}
