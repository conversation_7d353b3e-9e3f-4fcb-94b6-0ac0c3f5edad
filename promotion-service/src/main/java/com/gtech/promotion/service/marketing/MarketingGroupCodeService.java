package com.gtech.promotion.service.marketing;

import com.gtech.promotion.dao.entity.marketing.MarketingGroupCodeEntity;
import com.gtech.promotion.dao.model.marketing.MarketingGroupCodeMode;

import java.util.List;

/**
 * @Description :
 * <AUTHOR> gaojie.li
 * @Date : 2023/8/7 14:01
 */
public interface MarketingGroupCodeService extends BaseService<MarketingGroupCodeEntity, MarketingGroupCodeMode> {

    /**
     * 扣减
     * @param tenantCode
     * @param marketingGroupCode
     * @param activityCode
     * @return
     */
    int deductGroupInventory(String tenantCode, String activityCode, String marketingGroupCode);


    /**
     * 回滚添加
     * @param tenantCode
     * @param marketingGroupCode
     * @param activityCode
     * @return
     */
    int addGroupInventory(String tenantCode, String activityCode, String marketingGroupCode);


    /**
     * 拼团主表信息
     * @param tenantCode
     * @param activityCode
     * @param marketingGroupCode
     * @return
     */
    MarketingGroupCodeMode queryGroupByMarketingGroupCode(String tenantCode, String activityCode, String marketingGroupCode);


    /**
     * 更新主表拼团状态
     * @param tenantCode
     * @param activityCode
     * @param marketingGroupCode
     * @param groupStatus
     * @return
     */
    int updateMarketingCodeGroupStatus(String tenantCode, String activityCode, String marketingGroupCode,String groupStatus);


    /**
     * 查询拼团状态查询拼团主表
     * @param tenantCode
     * @param activityCode
     * @return
     */
    List<MarketingGroupCodeMode> queryGroupByActivityCode(String tenantCode, String activityCode,List<String> groupStatusList,String maxGroupCode);


}
