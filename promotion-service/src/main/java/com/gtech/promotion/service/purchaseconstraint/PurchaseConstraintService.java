package com.gtech.promotion.service.purchaseconstraint;

import com.github.pagehelper.PageInfo;
import com.gtech.promotion.dao.entity.purchaseconstraint.PurchaseConstraintEntity;
import com.gtech.promotion.dao.model.purchaseconstraint.PurchaseConstraintModel;
import com.gtech.promotion.vo.bean.PurchaseConstraintPriority;
import com.gtech.promotion.vo.param.purchaseconstraint.query.QueryPurchaseConstraintListParam;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

public interface PurchaseConstraintService {
    /**
     * 查询限购列表PurchaseConstraintService
     * @param param
     * @return
     */
    PageInfo<PurchaseConstraintEntity> queryList(QueryPurchaseConstraintListParam param);

    /**
     * 根据code获取限购信息
     * @param tenantCode
     * @param purchaseConstraintCode
     * @return
     */
    PurchaseConstraintModel getPurchaseConstraint(String tenantCode, String purchaseConstraintCode);

    /**
     * 修改限购状态
     * @param tenantCode
     * @param purchaseConstraintCode
     * @param purchaseConstraintStatus
     * @param operateUser
     * @return
     */
    int updatePurchaseConstraintStatus(String tenantCode, String purchaseConstraintCode, String purchaseConstraintStatus, String operateUser);

    /**
     * 保存限购
     * @param purchaseConstraintModel
     */
    void insert(PurchaseConstraintModel purchaseConstraintModel);

    /**
     * 根据Id修改限购, 覆盖更新, 值若为空会直接更新为空
     * @param updatePurchaseConstraintModel
     */
    int updatePurchaseConstraintById(PurchaseConstraintModel updatePurchaseConstraintModel);

    /**
     * 批量更新优先级
     * @param tenantCode
     * @param purchaseConstraintPriorityList
     * @return
     */
    int updatePurchaseConstraintPriority(String tenantCode, List<PurchaseConstraintPriority> purchaseConstraintPriorityList);

    /**
     * 查询有效的限购
     * 时间和状态都有效
     * @param tenantCode
     * @return
     */
    @Transactional(readOnly = true)
    List<PurchaseConstraintModel> queryEffectivePc(String tenantCode);

    List<PurchaseConstraintModel> list(String tenantCode);
}
