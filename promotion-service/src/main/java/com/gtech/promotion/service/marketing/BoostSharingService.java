package com.gtech.promotion.service.marketing;

import com.gtech.promotion.dao.entity.marketing.BoostSharingEntity;
import com.gtech.promotion.dao.model.marketing.BoostSharingModel;
import com.gtech.promotion.dao.model.marketing.flashsale.FlashSaleProductModel;
import com.gtech.promotion.dto.boostsharing.ExportBoostSharingDetailDto;
import com.gtech.promotion.dto.boostsharing.ExportBoostSharingTotalDto;
import com.gtech.promotion.vo.result.boostsharding.ExportBoostSharingDetailResult;
import com.gtech.promotion.vo.result.boostsharding.ExportBoostSharingTotalResult;

import java.util.ArrayList;
import java.util.List;

public interface BoostSharingService extends BaseService<BoostSharingEntity, BoostSharingModel> {


    BoostSharingModel findBoostShardingInfo(String domainCode,String tenantCode,String orgCode,String activityCode);


    List<BoostSharingModel> queryBoostSharingByActivityList(String tenantCode,List<String> activityCodeList);


    List<FlashSaleProductModel> getBoostSharingProductsByActivityCodesAndProducts(String tenantCode, ArrayList<String> strings, List<String> skuCodeList);

    ExportBoostSharingTotalResult exportBoostSharingTotal(ExportBoostSharingTotalDto dto);

    List<ExportBoostSharingDetailResult> exportBoostSharingDetail(ExportBoostSharingDetailDto dto);
}
