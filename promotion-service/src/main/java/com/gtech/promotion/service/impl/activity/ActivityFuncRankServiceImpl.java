/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.service.impl.activity;

import com.gtech.commons.utils.BeanCopyUtils;
import com.gtech.promotion.dao.entity.activity.ActivityFuncRankEntity;
import com.gtech.promotion.dao.mapper.activity.ActivityFuncRankMapper;
import com.gtech.promotion.dao.model.activity.ActivityFunctionParamRankModel;
import com.gtech.promotion.service.activity.ActivityFuncRankService;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;

import java.util.List;

@Service
public class ActivityFuncRankServiceImpl implements ActivityFuncRankService {

    @Autowired
    private ActivityFuncRankMapper activityFuncRankMapper;

    @Override
    @Transactional
    public String saveTPromoRuleFuncRank(String activityCode, String templateCode, int rank, String tenantCode) {

        ActivityFuncRankEntity funcRankEntity = new ActivityFuncRankEntity();
        funcRankEntity.setActivityCode(activityCode);
        funcRankEntity.setTemplateCode(templateCode);
        funcRankEntity.setRankParam(rank);
        funcRankEntity.setTenantCode(tenantCode);
        activityFuncRankMapper.insertSelective(funcRankEntity);

        return String.valueOf(funcRankEntity.getId());
    }

    @Override
    public List<ActivityFunctionParamRankModel> getRankListByActivityCode(String activityCode) {

        ActivityFuncRankEntity tPromoRuleFuncRankEntity = new ActivityFuncRankEntity();
        tPromoRuleFuncRankEntity.setActivityCode(activityCode);
        return BeanCopyUtils.jsonCopyList(activityFuncRankMapper.select(tPromoRuleFuncRankEntity), ActivityFunctionParamRankModel.class);
    }

    @Override
    @Transactional
    public int deleteByActivityCode(String activityCode) {

        ActivityFuncRankEntity tPromoRuleFuncRankEntity = new ActivityFuncRankEntity();
        tPromoRuleFuncRankEntity.setActivityCode(activityCode);
        return activityFuncRankMapper.delete(tPromoRuleFuncRankEntity);
    }

    @Override
    public List<ActivityFunctionParamRankModel> getRankListByActivityCodes(String tenantCode, List<String> activityCodes) {
        Example example = new Example(ActivityFuncRankEntity.class);
        example.createCriteria().andEqualTo(ActivityFuncRankEntity.C_TENANT_CODE, tenantCode)
                .andIn(ActivityFuncRankEntity.C_ACTIVITY_CODE, activityCodes);
        return BeanCopyUtils.jsonCopyList(activityFuncRankMapper.selectByCondition(example), ActivityFunctionParamRankModel.class);
    }

}
