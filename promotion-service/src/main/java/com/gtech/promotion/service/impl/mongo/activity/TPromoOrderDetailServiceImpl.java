/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.service.impl.mongo.activity;

import com.gtech.commons.utils.BeanCopyUtils;
import com.gtech.promotion.dao.mongo.activity.OrderDetailActivityEntity;
import com.gtech.promotion.dao.mongo.activity.OrderDetailEntity;
import com.gtech.promotion.service.mongo.activity.TPromoOrderDetailService;
import com.gtech.promotion.vo.mongo.TPromoOrderDetailVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

/**
 * 促销详情数据
 * 
 */
@Service
public class TPromoOrderDetailServiceImpl implements TPromoOrderDetailService{

    @Autowired
    private MongoTemplate mongoTemplate;

    @Override
    @Transactional
    public int insertOrderDetailList(List<TPromoOrderDetailVO> orderDetailVOs){
        List<OrderDetailEntity> param = new ArrayList<>(orderDetailVOs.size());
        orderDetailVOs.forEach(x -> {
            OrderDetailEntity convert = BeanCopyUtils.jsonCopyBean(x, OrderDetailEntity.class);
            convert.setActivities(BeanCopyUtils.jsonCopyList(x.getActivities(), OrderDetailActivityEntity.class));
            param.add(convert);
        });
        mongoTemplate.insertAll(param);
        return orderDetailVOs.size();
    }

    @Override
    public List<TPromoOrderDetailVO> queryOrderDetail(String promoOrderId) {
        Query query = new Query();
        Criteria criteriaDefinition = new Criteria();
        criteriaDefinition.and("promoOrderId").is(promoOrderId);
        query.addCriteria(criteriaDefinition);
        return BeanCopyUtils.jsonCopyList(mongoTemplate.find(query, OrderDetailEntity.class), TPromoOrderDetailVO.class);
    }

}
