/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.service.activity;

import com.gtech.promotion.dao.entity.activity.TPromoOrderEntity;
import com.gtech.promotion.dao.model.activity.TPromoOrderVO;
import com.gtech.promotion.dto.in.activity.ActivityTenantInDTO;
import com.gtech.promotion.dto.in.activity.CouponOrderStatisticInDTO;
import com.gtech.promotion.dto.in.activity.QueryActivityStatisticInDTO;

import java.util.List;

/**
 * 促销订单
 */
public interface TPromoOrderService{

    /**
     * 添加订单
     * 
     * @param order 订单数据
     */
    String insertTPromoOrder(TPromoOrderVO order);
    
    /**   
     * <方法描述> 查询订单
     * @param tenantCode 租户编码
     * @param salesOrderNo 订单编号
     */
    TPromoOrderVO queryOrderBySalesOrderNo(String tenantCode, String salesOrderNo);
    
    /**   
     * 根据主键修改订单状态（用于取消订单状态更改）
     * @param order 订单对象 TPromoOrderVO
     */
    Integer updateOrderStatusById(TPromoOrderVO order);

    /**   
     * <方法描述> 更新订单状态
     * @param tenantCode
     * @param salesOrderNo
     * @param salesOrderStatus
     */
    Integer updateOrderLogicDelete(String tenantCode, String salesOrderNo, String salesOrderStatus);

    /**
     * @description  查询昨天产生的促销订单数
     */
    List<TPromoOrderEntity> queryPromoOrderYesterday();

    /**
     * @Description 查询今天产生的促销订单数
     * @return java.util.List<com.gtech.promotion.entity.activity.TPromoOrderEntity>
     **/
    List<TPromoOrderEntity> queryPromoOrderToday(QueryActivityStatisticInDTO paramDTO);
    List<TPromoOrderEntity> queryPromoOrderByOrderIdList(CouponOrderStatisticInDTO paramDTO);

    /**   
     * 订单支付总数
     */
    long payOrderAmount();
    
    /**   
     * 订单支付总数
     * @param tenantInDTO
     */
    Integer getPayOrderAmount(ActivityTenantInDTO tenantInDTO);
}
