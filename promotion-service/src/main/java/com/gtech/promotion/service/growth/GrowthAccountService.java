package com.gtech.promotion.service.growth;

import com.gtech.commons.result.PageResult;
import com.gtech.promotion.vo.param.growth.CreateGrowthAccountParam;
import com.gtech.promotion.vo.param.growth.UpdateGrowthParam;
import com.gtech.promotion.vo.param.growth.query.GetGrowthAccountParam;
import com.gtech.promotion.vo.param.growth.query.GrowthAccountUniqueParam.GrowthAccountStatusUniqueVo;
import com.gtech.promotion.vo.result.growth.GrowthAccountResult;

import java.util.Map;

public interface GrowthAccountService {



	/**
	 * 修改成长值账户
	 * 
	 * @param growthAccountVo
	 */
	int updateGrowthAccount(CreateGrowthAccountParam growthAccountVo);

	/**
	 * 修改成长值账户状态
	 * 
	 * @param growthAccountStatusUniqueVo
	 */
	int updateGrowthAccountStatus(GrowthAccountStatusUniqueVo growthAccountStatusUniqueVo);

	/**
	 * 根据Code查询成长值账户
	 */
	GrowthAccountResult getGrowthAccount(GetGrowthAccountParam param);

	/**
	 * 根据账户信息查询成长值账户
	 * 
	 * @param accountCode
	 * @return
	 */
	GrowthAccountResult getGrowthAccountByAccount(String tenantCode, String accountCode, Integer accountType);

	/**
	 * 分页查询成长值账户
	 * 
	 * @param map
	 * @return queryResult<GrowthAccountResult>
	 */
	PageResult<GrowthAccountResult> queryGrowthAccountPage(Map<String, Object> map);

	/**
	 * 成长值 积分变更
	 * 
	 * @param growthChangeVo
	 */
	int updateGrowth(UpdateGrowthParam growthChangeVo);
}
