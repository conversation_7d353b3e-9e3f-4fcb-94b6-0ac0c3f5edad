/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.service.impl.activity;

import com.gtech.commons.utils.BeanCopyUtils;
import com.gtech.commons.utils.StringUtil;
import com.gtech.promotion.checker.activity.TPromoRuleFuncParamChecker;
import com.gtech.promotion.checker.activity.TPromoTemplateChecker;
import com.gtech.promotion.dao.entity.activity.ActivityFuncParamEntity;
import com.gtech.promotion.dao.mapper.activity.ActivityFuncParamMapper;
import com.gtech.promotion.dao.model.activity.ActivityFunctionParamRankModel;
import com.gtech.promotion.dao.model.activity.FunctionParamModel;
import com.gtech.promotion.dao.model.activity.TemplateModel;
import com.gtech.promotion.exception.Check;
import com.gtech.promotion.exception.PromotionException;
import com.gtech.promotion.service.activity.ActivityFuncParamService;
import com.gtech.promotion.service.activity.ActivityFuncRankService;
import com.gtech.promotion.service.activity.TemplateService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;

import java.util.List;

@Slf4j
@Service
public class ActivityFuncParamServiceImpl implements ActivityFuncParamService {

    @Autowired
    private ActivityFuncParamMapper activityFuncParamMapper;

    @Autowired
    private ActivityFuncRankService activityFuncRankService;

    @Autowired
    private TemplateService templateService;

    @Override
    @Transactional
    public int saveTPromoRuleFuncParam(String activityCode, String templateCode, List<FunctionParamModel> paramVOs) {

        Check.check(paramVOs == null, TPromoRuleFuncParamChecker.NOT_NULL);
        Check.check(StringUtil.isBlank(templateCode), TPromoTemplateChecker.NOT_NULL_TEMPLATE_CODE);
        
        TemplateModel template = templateService.getTemplateByCode(templateCode);
        Check.check(template == null, TPromoTemplateChecker.ILLEGAL_TEMPLATE_CODE);
        
        int insertCount = 0;//插入参数记录数
        int rankParam = 0;//层级
        String rankId = "";//层级id
        for (FunctionParamModel temp : paramVOs) {
            //如果当前层级没有被添加过，添加当前层级(此处是因为一个层级有4个TPromoActivityFuncParamVO对象)
            if (rankParam != temp.getRankParam()) {
                rankParam = temp.getRankParam();
                rankId = activityFuncRankService.saveTPromoRuleFuncRank(activityCode, template.getTemplateCode(), rankParam, temp.getTenantCode());
            }
            //添加模板函数参数值
            temp.setRankId(rankId);
            try {
                insertCount += activityFuncParamMapper.insertSelective(BeanCopyUtils.jsonCopyBean(temp, ActivityFuncParamEntity.class));
            } catch (DuplicateKeyException e) {
                log.error("模板函数参数重复", e);
                throw new PromotionException(TPromoRuleFuncParamChecker.DUPLICATE_RANKPARAM);
            }
        }
        return insertCount == paramVOs.size() ? 1 : 0;
    }

    @Override
    public List<FunctionParamModel> getRuleFuncParamListByRankId(String rankId){

        ActivityFuncParamEntity entity = new ActivityFuncParamEntity();
        entity.setRankId(rankId);
        return BeanCopyUtils.jsonCopyList(activityFuncParamMapper.select(entity), FunctionParamModel.class);
    }

    @Override
    @Transactional
    public int deleteRuleFuncParam111(String activityCode) {

        List<ActivityFunctionParamRankModel> rankList = activityFuncRankService.getRankListByActivityCode(activityCode);
        for (int i = 0; i < rankList.size(); i++) {
            ActivityFunctionParamRankModel rank = rankList.get(i);
            ActivityFuncParamEntity entity = new ActivityFuncParamEntity();
            entity.setRankId(rank.getId());
            activityFuncParamMapper.delete(entity);
        }
        return activityFuncRankService.deleteByActivityCode(activityCode);
    }

    @Override
    public List<FunctionParamModel> getRuleFuncParamListByRankIds(String tenantCode, List<String> rankIds) {
        Example example = new Example(ActivityFuncParamEntity.class);
        example.createCriteria().andEqualTo(ActivityFuncParamEntity.C_TENANT_CODE, tenantCode)
                .andIn(ActivityFuncParamEntity.C_RANK_ID, rankIds);
        return BeanCopyUtils.jsonCopyList(activityFuncParamMapper.selectByCondition(example), FunctionParamModel.class);
    }

    @Override
    public FunctionParamModel getRuleFuncParamListByRankIdAndFunctionCode(String rankId,String functionCode){
        ActivityFuncParamEntity entity = new ActivityFuncParamEntity();
        entity.setRankId(rankId);
        entity.setFunctionCode(functionCode);
        ActivityFuncParamEntity selectOne = activityFuncParamMapper.selectOne(entity);
        return BeanCopyUtils.jsonCopyBean(selectOne, FunctionParamModel.class);
    }


}
