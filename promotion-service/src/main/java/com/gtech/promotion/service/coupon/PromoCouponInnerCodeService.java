/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.service.coupon;

import java.util.Date;
import java.util.List;

import com.github.pagehelper.PageInfo;
import com.gtech.commons.page.PageData;
import com.gtech.promotion.code.coupon.CouponStatusEnum;
import com.gtech.promotion.dao.model.coupon.CountCouponCodeModel;
import com.gtech.promotion.dao.model.coupon.CouponInnerRelationVO;
import com.gtech.promotion.dao.model.coupon.PromoPassVO;
import com.gtech.promotion.dao.model.coupon.TPromoCouponInnerCodeVO;
import com.gtech.promotion.dto.in.activity.CouponQuantityDTO;
import com.gtech.promotion.dto.in.activity.ManagementDataInDTO;
import com.gtech.promotion.dto.in.coupon.ExportCouponInDTO;
import com.gtech.promotion.dto.in.coupon.ExportCouponRelationDto;
import com.gtech.promotion.dto.in.coupon.TCouponListQueryDTO;
import com.gtech.promotion.dto.out.coupon.CouponInfoImportOutDTO;
import com.gtech.promotion.dto.out.coupon.ExportCouponOutDTO;
import com.gtech.promotion.dto.out.coupon.ManagementDataOutDTO;
import com.gtech.promotion.page.RequestPage;

/**
 * 券内码表service接口
 */
public interface PromoCouponInnerCodeService {

    /**
     * 插入一批券到数据库中
     */
    int insertPromoCouponInnerCode(List<TPromoCouponInnerCodeVO> innerCodeVOs);

    /**
     * 查询优惠券
     */
    TPromoCouponInnerCodeVO findCouponByCouponCode(String tenantCode, String couponCode);

    TPromoCouponInnerCodeVO findCouponByCouponCodeOrPassword(String tenantCode, String couponCode);


    /**
     * 分页查询券信息
     */
    PageInfo<TPromoCouponInnerCodeVO> selectCouponList(String tenantCode, String activityCode, String couponStatus, Date createTimeStart, Date createTimeEnd, RequestPage page,
                    String couponCode);

    /**
     * 查询券码
     */
    PageInfo<TPromoCouponInnerCodeVO> selectCouponCode(TPromoCouponInnerCodeVO innerCodeVO, String couponStatus, Date createTimeStart,
                    Date createTimeEnd, RequestPage page);

    /**
     * 将券的状态改为冻结状态
     */
    int updateCouponInnerFrozenStatus(String tenantCode, String activityCode);

    /**
     * @param innerCodeVO
     */
    int updateInnerCoudeById(TPromoCouponInnerCodeVO innerCodeVO);

    /**
     * 根据券码获取券码信息
     */
    List<TPromoCouponInnerCodeVO> getCouponInnerCodeByCodes(String tenantCode, List<String> couponCodes);

    /**
     * 批量更新优惠券状态
     * 
     * @param couponCodes 券码集合
     * @param statusEnum 新的状态
     * @return 更新记录数
     */
    int updateStatusBatch(String tenantCode, List<String> couponCodes, CouponStatusEnum statusEnum);

    /**
     * 根据activityCode & releaseCode, 更新对应的券的状态为过期状态
     * 
     * @param releaseCode -- Coupon release code. (Optional)
     */
    int expireByActivityCode(String tenantCode, String activityCode, String releaseCode);

    /**
     * 更新通用优惠码的状态
     * 
     * @param -- order number(Optional)
     */
    int updateCouponStatus(String tenantCode, String activityCode, String couponCode, CouponStatusEnum statusEnum);

    /**
     * 更新券码状态为已发放
     */
    int updateCouponToGrantedState(String tenantCode, List<String> couponCodes, String takeLabel,Integer frozenStatus);

    /**
     * 已锁定
     */
    int updateCouponToLockedState(String tenantCode, String couponCode, String takeLabel);

    /**
     * 根据投放id获取券码列表
     */
    List<TPromoCouponInnerCodeVO> queryInnerCouponByReleaseCode(String tenantCode, String activityCode, String releaseCode);

    /**
     * Get coupon information by activity code.
     */
    TPromoCouponInnerCodeVO getCouponByActivityCode(String tenantCode, String activityCode);

    /**
     * 根据 couponStatus 分类统计指定的 activityCode & releaseCode 的券数量, releaseCode为空时统计 activityCode 对应的数据
     */
    List<CountCouponCodeModel> countCouponCode(String tenantCode, String activityCode, String releaseCode);

    /**
     * 匿名券领取数量确定
     * @param tenantCode
     * @param activityCode
     * @param releaseCode
     * @return
     */
    int countAnonymityCouponCode(String tenantCode, String activityCode, String releaseCode);


    /**
     * shopkeeper查询或导出券码
     * 
     * @param tCouponListQueryDTO
     */
    List<CouponInfoImportOutDTO> selectCouponCodeList(TCouponListQueryDTO tCouponListQueryDTO, Date createTimeStart, Date createTimeEnd);

    /**
     * 冻结券码
     */
    int frozenInnerCode(String tenantCode, String couponCode,Integer frozenStatus,Integer logicDelete);


    /**
     * 查询券码
     */
    int frozenFindInnerCode(String tenantCode, String couponCode);

    /**
     * 获取可以领取的券
     */
    List<String> queryHeaderCouponCodes(String tenantCode, String activityCode, String releaseCode, int count);

    int getFrozenAndUnGrantCodeCount111(String releaseCode);

    /**
     * 券码统一管理
     */
    PageData<TPromoCouponInnerCodeVO> queryManagementData(ManagementDataInDTO inDTO);

    PageData<ManagementDataOutDTO> queryCouponActivityListService(ManagementDataInDTO managementDataInDTO);

    /**
     * 优惠码查询
     */
    PageData<TPromoCouponInnerCodeVO> findCouponCodeData(ManagementDataInDTO inDTO);

    /**
     * 根据优惠码更新优惠码
     */
    int updateInnerCodeByCode(String tenantCode, String promotionCodeOld, String promotionCodeNew);

    /**
     * 删除
     */
    int deleteCouponInnerCode111(String tenantCode, String activityCode);

    List<ExportCouponOutDTO> exportCoupon(ExportCouponInDTO exportCouponInDTO);

    int updateInnerCouponEndTime(String tenantCode, String activityCode, String releaseCode, String endTime);

    int expireCouponCode();

    int logicDelete(String tenantCode, String activityCode);


    Integer getCouponQuantity(CouponQuantityDTO couponQuantityDTO);

    void updateBatchInnerCodeValidTime(List<TPromoCouponInnerCodeVO> vos);

    List<PromoPassVO> findCouponCodeByPassword(String tenantCode, String promoPassword);

	void updateBatchInnerCodeByCouponCodes(List<TPromoCouponInnerCodeVO> vos);

    /**
     * 单个券查询券活动
     * @param tenantCode
     * @param couponCode
     * @return
     */
    TPromoCouponInnerCodeVO findActivityCodeByCouponCode(String tenantCode, String couponCode);

    /**
     * 批量查询券
     * @param tenantCode
     * @param couponCodes
     * @return
     */
    List<TPromoCouponInnerCodeVO> queryActivityByCouponCodes(String tenantCode, List<String> couponCodes);

    /**
     *  导出券码以及相关信息
     * @param dto
     * @return
     */
    List<CouponInnerRelationVO> exportCouponRelationInfo(ExportCouponRelationDto dto);

}
