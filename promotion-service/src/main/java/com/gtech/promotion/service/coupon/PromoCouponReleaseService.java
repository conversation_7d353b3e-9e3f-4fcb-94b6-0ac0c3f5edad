/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.service.coupon;

import com.github.pagehelper.PageInfo;
import com.gtech.promotion.dao.model.coupon.CouponReleaseModel;
import com.gtech.promotion.dao.model.coupon.ReleaseCouponVO;
import com.gtech.promotion.domain.coupon.CouponReleaseDomain;
import com.gtech.promotion.domain.coupon.CouponReleaseInventoryDomain;
import com.gtech.promotion.page.RequestPage;

import java.util.List;

/**
 * 投放券码service接口
 */
public interface PromoCouponReleaseService {

    /**
     * 插入投放券记录
     */
    String createCouponRelease(CouponReleaseModel promoCouponReleaseVO);

    /**
     * 根据releaseCode更新投放信息
     */
    int updateCouponReleaseByReleaseCode(CouponReleaseDomain couponReleaseDomain);

    /**
     * 更新活动id更新投放状态
     */
    int stopCouponRelease(String activityCode);

    /**
     * 根据releaseCode查询投放详情
     */
    CouponReleaseDomain findCouponReleaseByReleaseCode(String tenantCode, String releaseCode);

    /**
     * 根据条件查询出所有投放记录
     */
    PageInfo<CouponReleaseDomain> queryReleases(CouponReleaseDomain releaseVO, RequestPage page);

    /**
     * 根据活动ID查询所有投放对象
     */
    List<CouponReleaseModel> queryCouponRelease(String activityCode);

    /**
     * 根据券码活动Id查询投放券码总量
     */
    Long queryReleaseCount111(String activityCode);

    /**
     * 查询所有未投放的预约投放记录(定时投放专用)
     */
    List<CouponReleaseModel> queryReleasesNotReleased();

    /**
     * 优惠码删除券活动对应的投放
     */
    int deleteCouponRelease(String tenantCode, String activityCode);

    /**
     * 根据活动id查询可领券的所有投放
     * 
     * @param releaseCode -- Coupon activity release code. (Optional)
     */
    List<CouponReleaseDomain> queryCanReceiveReleases(String tenantCode, String activityCode, String releaseCode);
    List<CouponReleaseDomain> queryCanReceiveReleasesByTime(String tenantCode, String activityCode,  String receiveStartTime, String receiveEndTime);
    /**
	 * 根据活动CODE集合查询所有投放
	 * 
	 * @param tenantCode
	 * @param activityCodes
	 * @param inventoryFlag 可投放券>0
	 * @return
	 */
	List<CouponReleaseDomain> queryReleasesByActivityCodes(String tenantCode, List<String> activityCodes, Boolean inventoryFlag);

    /**
     * 扣减投放批次一定数量的库存
     * 
     * @param inventory 扣减数量
     */
    List<CouponReleaseInventoryDomain> deductInventory(String tenantCode, String activityCode, List<CouponReleaseDomain> canReleaseDomains, int inventory);

    /**
     * 根据租户编码 和时间段查询活动id集合
     */
    List<CouponReleaseModel> queryCouponReleaseActivityCode(String tenantCode, String startTime, String endTime, String activityCode);

    /**
     * 根据活动id和投放id 查询该券是否在可领取时间段
     */
    CouponReleaseModel findCanReceiveRelease(String activityCode, String releaseCode);

    /**
     * 根据活动id和投放id查询该券 用于判断券是否在可用时间段
     */
    CouponReleaseModel findCanUseRelease(String activityCode, String releaseCode);


    /**
     * 查询符合发送的券
     * @param tenantCode
     * @param activityCode
     * @param releaseCode
     * @param sendTotal
     * @return
     */
    List<ReleaseCouponVO> queryReleaseCouponRecord(String tenantCode, String activityCode,String releaseCode,Integer sendTotal ,Integer receiveType);

    /**
     * 根据指定编码查询投放批次
     * @param tenantCode
     * @param activityCode
     * @param releaseList
     * @return
     */
    List<CouponReleaseDomain> queryReleaseByCondition(String tenantCode, String activityCode, List<String> releaseList);


    List<String> queryActivityCodeByReceiveTime(String tenantCode, String receiveStartTime, String receiveEndTime);


    int releaseSpecifiedQuantity(CouponReleaseDomain releaseSpecifiedQuantityDomain);


    /**
     * 批量查询投放详情
     * @param tenantCode
     * @param releaseCodes
     * @return
     */
    List<CouponReleaseDomain> queryCouponReleaseByReleaseCodes(String tenantCode, List<String> releaseCodes);
}
