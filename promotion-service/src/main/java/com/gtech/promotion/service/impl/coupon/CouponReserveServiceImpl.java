package com.gtech.promotion.service.impl.coupon;

import com.gtech.commons.utils.BeanCopyUtils;
import com.gtech.promotion.dao.entity.coupon.CouponReserveEntity;
import com.gtech.promotion.dao.mapper.coupon.CouponReserveMapper;
import com.gtech.promotion.dto.in.coupon.ReserveCouponDto;
import com.gtech.promotion.service.coupon.CouponReserveService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;

@Service
public class CouponReserveServiceImpl implements CouponReserveService {

    @Autowired
    private CouponReserveMapper couponReserveMapper;

    @Override
    @Transactional
    public int addReserve(ReserveCouponDto reserveCouponDto) {
        CouponReserveEntity entity = BeanCopyUtils.jsonCopyBean(reserveCouponDto, CouponReserveEntity.class);
        return couponReserveMapper.insertSelective(entity);
    }

    @Override
    public ReserveCouponDto getReserve(ReserveCouponDto dto) {
        CouponReserveEntity entity = BeanCopyUtils.jsonCopyBean(dto, CouponReserveEntity.class);
        return BeanCopyUtils.jsonCopyBean(couponReserveMapper.selectOne(entity), ReserveCouponDto.class);
    }

    @Override
    @Transactional
    public int updateReserve(ReserveCouponDto reserve) {
        CouponReserveEntity entity = new CouponReserveEntity();
        entity.setStatus(reserve.getStatus());

        Example example = new Example(CouponReserveEntity.class);
        example.createCriteria().andEqualTo(CouponReserveEntity.C_TENANT_CODE, reserve.getTenantCode())
                .andEqualTo(CouponReserveEntity.C_ORDER_NO, reserve.getOrderNo())
                .andEqualTo(CouponReserveEntity.C_ACTIVITY_CODE, reserve.getActivityCode());
        return couponReserveMapper.updateByConditionSelective(entity, example);
    }
}
