/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.service.activity;

import com.github.pagehelper.PageInfo;
import com.gtech.promotion.dao.model.activity.TPromoActivityProductDetailVO;
import com.gtech.promotion.dto.cache.ProductSkuDetailDTO;
import com.gtech.promotion.vo.bean.ProductDetail;

import java.util.List;
import java.util.Set;

/**
 * 促销商品sku
 * 
 */
public interface ActivityProductDetailService {

    /**
     * 插入商品sku数据
     */
    Integer insertProductSku(List<TPromoActivityProductDetailVO> productDetailVO);

    /**
     * 删除商品sku
     */
    Integer deleteProductDetails(String activityCode, Integer type);

    /**
     * 活动商品sku信息
     */
    PageInfo<ProductDetail> queryProductDetails(String activityCode);

    /**
     * 活动商品sku信息
     */
    List<ProductSkuDetailDTO> getProductSkus111(String activityCode);

    /**
     * 活动商品sku信息
     */
    List<TPromoActivityProductDetailVO> queryProductSkusBySeqNum111(String activityCode, Integer seqNum);

    /**
     * 根据seqNum删除商品sku
     */
    Integer deleteProductSkuBySeqNum(String activityCode, Integer seqNum);

    List<Integer> getSeqNumDistinct111(String activityCode);

    /**
     * 复制活动商品sku信息
     */
    void copyProductSku111(String oldActivityCode, String newActivityCode);

    /**
     * 根据spu和套装编码分组查询
     */
    PageInfo<ProductDetail> queryProductSkusGroup111(String activityCode, Integer seqNum);

    List<ProductDetail> queryProductSpusIn111(String activityCode, List<String> spuCodes);

    List<ProductDetail> queryProductcombinSpusIn111(String activityCode, List<String> combineSpuCodes);

    /**
     * 根据活动id查询商品池数量
     */
    List<Integer> getSeqNumCount111(String activityCode);

    /**
     * 查询该活动下所有商品明细信息
     */
    List<ProductDetail> getProductSpuSkus(String activityCode);

    /**
     * 插入一条商品数据
     */
    Integer insertProductDetail(TPromoActivityProductDetailVO productDetailVO);

    List<ProductSkuDetailDTO> getProductSkusByActivityCodes(String tenantCode, List<String> activityCodes);

    List<ProductSkuDetailDTO> queryOneGroupByActivityCodes(String tenantCode, List<String> activityCodes);

    List<ProductSkuDetailDTO> queryListByActivityCodesAndProductCode(Set<String> activityCodes, String productCode);

    List<ProductSkuDetailDTO> queryListByActivityCodesAndProductCodes(Set<String> keySet, List<String> productCodes);


    /**
     * 查询该活动下所有商品明细信息
     */
    List<TPromoActivityProductDetailVO> queryProductSpuOrSku(String activityCode);
}
