package com.gtech.promotion.service.marketing;

import com.gtech.promotion.dao.entity.marketing.MarketingGroupEntity;
import com.gtech.promotion.dao.model.marketing.MarketingGroupMode;

import java.util.List;
import java.util.Set;

public interface MarketingGroupService extends BaseService<MarketingGroupEntity, MarketingGroupMode> {

    List<MarketingGroupEntity> selectMarketingGroupList(String tenantCode, Set<String> activityCodes);


    /**
     * 查询拼团信息
     * @param tenantCode
     * @param activityCode
     * @return
     */
    MarketingGroupMode findMarketingGroupByActivityCode(String tenantCode,String activityCode);


    /**
     * 是否允许查询封闭团,不传查询所有
     * @param tenantCode
     * @param activityCode
     * @param closeFlag
     * @return
     */
    MarketingGroupMode findGroupByActivityCode(String tenantCode,String activityCode,Integer closeFlag);

}
