package com.gtech.promotion.service.marketing;

import com.gtech.commons.page.PageData;
import com.gtech.commons.page.PageParam;

import java.util.List;

public interface BaseService<E, M> { // NOSONAR

    int insert(M m);

    int deleteByActivityCode(String activityCode);

    M findByActivityCode(String activityCode);

    List<M> findListByActivityCode(String activityCode);

    PageData<M> selectPageList(M m, PageParam param);
}
