package com.gtech.promotion.service.flashsale.impl;

import com.github.pagehelper.PageHelper;
import com.gtech.commons.utils.BeanCopyUtils;
import com.gtech.commons.utils.DateUtil;
import com.gtech.commons.utils.StringUtil;
import com.gtech.promotion.code.activity.OrderStatusEnum;
import com.gtech.promotion.dao.entity.marketing.BaseEntity;
import com.gtech.promotion.dao.entity.marketing.flashsale.FlashSaleOrderEntity;
import com.gtech.promotion.dao.mapper.marketing.flashsale.FlashSaleOrderMapper;
import com.gtech.promotion.dao.model.marketing.flashsale.FlashPreSaleOrderModel;
import com.gtech.promotion.dao.model.marketing.flashsale.FlashSaleOrderModel;
import com.gtech.promotion.service.flashsale.FlashSaleOrderService;
import com.gtech.promotion.service.marketing.impl.BaseServiceImpl;
import com.gtech.promotion.vo.result.flashpresale.ExportFlashPreSaleDto;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

@Service
public class FlashSaleOrderServiceImpl extends BaseServiceImpl<FlashSaleOrderEntity, FlashSaleOrderModel> implements FlashSaleOrderService {

    public FlashSaleOrderServiceImpl() {
        super(FlashSaleOrderEntity.class, FlashSaleOrderModel.class);
    }

    @Autowired
    private FlashSaleOrderMapper orderMapper;

    @Override
    public FlashSaleOrderModel findByOrderNo(String tenantCode, String orderNo) {
        FlashSaleOrderEntity entity = new FlashSaleOrderEntity();
        entity.setOrderId(orderNo);
        entity.setTenantCode(tenantCode);
        return BeanCopyUtils.jsonCopyBean(orderMapper.selectOne(entity), FlashSaleOrderModel.class);
    }

    @Override
    @Transactional
    public int updateStatus(String tenantCode, String orderNo, String orderStatus) {
        FlashSaleOrderEntity entity = new FlashSaleOrderEntity();
        entity.setOrderStatus(orderStatus);

        Example example = new Example(FlashSaleOrderEntity.class);
        example.createCriteria().andEqualTo(BaseEntity.C_TENANT_CODE, tenantCode)
                    .andEqualTo(FlashSaleOrderEntity.ORDER_ID, orderNo);
        return orderMapper.updateByConditionSelective(entity, example);
    }

    @Override
    public int checkLeaderPayOrder(String tenantCode, String activityCode,String marketingGroupCode, String memberCode) {
        Example example = new Example(FlashSaleOrderEntity.class);
        example.createCriteria().andEqualTo(BaseEntity.C_TENANT_CODE, tenantCode)
                .andEqualTo(FlashSaleOrderEntity.C_ACTIVITY_CODE,activityCode)
                .andEqualTo(FlashSaleOrderEntity.MARKETING_GROUP_CODE, marketingGroupCode)
                .andEqualTo(FlashSaleOrderEntity.MEMBER_CODE, memberCode)
                .andEqualTo(FlashSaleOrderEntity.ORDER_STATUS, OrderStatusEnum.PAID.code());
        return orderMapper.selectCountByCondition(example);
    }

    @Override
    public List<FlashSaleOrderModel> queryOrderByMarketingGroupCode(String tenantCode, String activityCode, String marketingGroupCode) {

        Example example = new Example(FlashSaleOrderEntity.class);
        example.createCriteria().andEqualTo(BaseEntity.C_TENANT_CODE, tenantCode)
                .andEqualTo(FlashSaleOrderEntity.C_ACTIVITY_CODE,activityCode)
                .andEqualTo(FlashSaleOrderEntity.MARKETING_GROUP_CODE, marketingGroupCode);

        List<FlashSaleOrderEntity> flashSaleOrderEntities = orderMapper.selectByCondition(example);

        return BeanCopyUtils.jsonCopyList(flashSaleOrderEntities,FlashSaleOrderModel.class);
    }

    @Override
    public List<FlashPreSaleOrderModel> queryOrderByCondition(ExportFlashPreSaleDto dto) {

        Example example = new Example(FlashSaleOrderEntity.class);
        example.orderBy("orderId").asc();
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo(BaseEntity.C_TENANT_CODE, dto.getTenantCode())
                .andEqualTo(FlashSaleOrderEntity.C_ACTIVITY_CODE, dto.getActivityCode());

        if (StringUtil.isNotEmpty(dto.getMaxOrderCode())){
            criteria.andGreaterThan(FlashSaleOrderEntity.ORDER_ID, dto.getMaxOrderCode());

        }


        if (StringUtil.isNotEmpty(dto.getOrderStatus())){
            criteria.andEqualTo(FlashSaleOrderEntity.ORDER_STATUS, dto.getOrderStatus());
        }

        PageHelper.startPage(1,1000,false);
        List<FlashSaleOrderEntity> flashSaleOrderEntities = orderMapper.selectByCondition(example);

        List<FlashPreSaleOrderModel> modelList = new ArrayList<>();

        for (FlashSaleOrderEntity flashSaleOrderEntity : flashSaleOrderEntities) {

            FlashPreSaleOrderModel flashPreSaleOrderModel = BeanCopyUtils.jsonCopyBean(flashSaleOrderEntity, FlashPreSaleOrderModel.class);
            flashPreSaleOrderModel.setDate(DateUtil.format(flashSaleOrderEntity.getCreateTime(),DateUtil.FORMAT_YYYYMMDD));
            modelList.add(flashPreSaleOrderModel);

        }


        return modelList;

    }

    @Override
    public List<FlashPreSaleOrderModel> queryOrderByMarketingGroupCodeList(String tenantCode, String activityCode, List<String> groupCodeList) {

        if (CollectionUtils.isEmpty(groupCodeList)){
            return Collections.emptyList();
        }

        Example example = new Example(FlashSaleOrderEntity.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo(BaseEntity.C_TENANT_CODE, tenantCode)
                .andEqualTo(FlashSaleOrderEntity.C_ACTIVITY_CODE, activityCode)
                .andIn(FlashSaleOrderEntity.MARKETING_GROUP_CODE, groupCodeList);

        return BeanCopyUtils.jsonCopyList(orderMapper.selectByCondition(example),FlashPreSaleOrderModel.class);
    }
}
