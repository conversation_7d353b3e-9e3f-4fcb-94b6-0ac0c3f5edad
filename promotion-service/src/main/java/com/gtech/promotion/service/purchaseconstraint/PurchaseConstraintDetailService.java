package com.gtech.promotion.service.purchaseconstraint;

import com.gtech.promotion.dao.entity.purchaseconstraint.PurchaseConstraintDetail;
import com.gtech.promotion.dao.model.purchaseconstraint.PurchaseConstraintDetailListModel;

import java.util.List;

public interface PurchaseConstraintDetailService {
    void insert(PurchaseConstraintDetail purchaseConstraintDetail);

    void insert(List<PurchaseConstraintDetail> purchaseConstraintDetail);

    List<PurchaseConstraintDetail> list(PurchaseConstraintDetailListModel model);


    int decrement(PurchaseConstraintDetail detail);

    int delete(List<PurchaseConstraintDetail> detailInserts);
}
