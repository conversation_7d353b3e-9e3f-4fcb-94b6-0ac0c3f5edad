/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * <PERSON>ECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.service.impl.activity;

import com.gtech.commons.utils.BeanCopyUtils;
import com.gtech.promotion.dao.entity.activity.GiveawayEntity;
import com.gtech.promotion.dao.mapper.activity.GiveawayMapper;
import com.gtech.promotion.dao.model.activity.GiveawayVO;
import com.gtech.promotion.service.activity.GiveawayService;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;

import java.util.List;

@Service
public class GiveawayServiceImpl implements GiveawayService {

    @Autowired
    private GiveawayMapper giveawayMapper;

    @Override
    @Transactional
    public void createGiveaway(GiveawayVO giftVO) {

        GiveawayEntity convert = BeanCopyUtils.jsonCopyBean(giftVO, GiveawayEntity.class);
        giveawayMapper.insertSelective(convert);
    }

    @Override
    public List<GiveawayVO> getGiftListByActivityCode(String tenantCode, String activityCode) {

        Example example = new Example(GiveawayEntity.class);
        example.createCriteria()
                .andEqualTo(GiveawayEntity.C_TENANT_CODE, tenantCode)
                .andEqualTo(GiveawayEntity.C_ACTIVITY_CODE, activityCode);
        example.orderBy(GiveawayEntity.C_ID);
        List<GiveawayEntity> select = giveawayMapper.selectByCondition(example);
        return BeanCopyUtils.jsonCopyList(select, GiveawayVO.class);
    }

    @Override
    @Transactional
    public void deleteGiveaway(String tenantCode, String activityCode) {

        GiveawayEntity entity = new GiveawayEntity();
        entity.setTenantCode(tenantCode);
        entity.setActivityCode(activityCode);
        giveawayMapper.delete(entity);
    }

    @Override
    public List<GiveawayVO> getGiftListByActivityCodes(String tenantCode, List<String> activityCodes) {
        Example example = new Example(GiveawayEntity.class);
        example.createCriteria()
                .andEqualTo(GiveawayEntity.C_TENANT_CODE, tenantCode)
                .andIn(GiveawayEntity.C_ACTIVITY_CODE, activityCodes);
        example.orderBy(GiveawayEntity.C_ID);
        List<GiveawayEntity> select = giveawayMapper.selectByCondition(example);
        return BeanCopyUtils.jsonCopyList(select, GiveawayVO.class);
    }

}
