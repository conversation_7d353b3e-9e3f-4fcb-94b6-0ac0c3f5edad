package com.gtech.promotion.service.activity;

import com.gtech.promotion.dao.model.activity.OperationLogModel;
import com.gtech.promotion.dto.in.activity.QueryOperationLogsByActivityCodeDTO;
import com.gtech.promotion.dto.out.activity.QueryOperationLogsByActivityCodeOutDTO;

import java.util.List;

public interface OperationLogService {

    int insertLog(OperationLogModel operationLogModel, String jsonString);

    List<QueryOperationLogsByActivityCodeOutDTO> queryOperationLogsByActivityCode(QueryOperationLogsByActivityCodeDTO dto);
}
