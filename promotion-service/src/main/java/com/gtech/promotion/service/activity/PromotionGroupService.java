package com.gtech.promotion.service.activity;

import com.gtech.promotion.dao.model.activity.GroupQueryVO;
import com.gtech.promotion.dao.model.activity.PromoGroupMode;
import com.gtech.promotion.dao.model.activity.PromoGroupVO;
import com.gtech.promotion.dto.in.activity.GroupPriorityVO;

import java.util.List;

/**
 * @Description :
 * <AUTHOR> gaojie.li
 * @Date : 2023/6/14 9:52
 */
public interface PromotionGroupService {

    /**
     * 创建活动分组
     * @return 1:成功
     */
    int insertActivityGroup(PromoGroupMode promoGroupMode);
    int insertActivityGroupList(List<PromoGroupMode> promoGroupModeList);


    /**
     * 更新分组
     * @param promoGroupMode
     * @return
     */
    int updateActivityGroup(PromoGroupMode promoGroupMode);

    /**
     * 删除分组
     * @param tenantCode
     * @param groupCode
     * @param operatorUser
     * @return
     */
    int deleteActivityGroup(String tenantCode,String groupCode,String operatorUser);

    /**
     * 查询最新创建的分组
     * @param tenantCode 租户编码
     * @return 最新创建的分组
     */
    PromoGroupVO getLastedGroup(String tenantCode);

    /**
     * 查询分组列表
     * @param groupQueryVO
     * @return
     */
    List<PromoGroupMode> queryActivityGroupList(GroupQueryVO groupQueryVO);

    /***
     * 查询分组
     * @param tenantCode
     * @return
     */
    List<PromoGroupVO>  listActivityGroupByTenantCode(String tenantCode);


    /**
     * 根据分组编码查询分组
     * @param tenantCode
     * @param groupCode
     * @return
     */
    PromoGroupVO getGroupByGroupCode(String tenantCode,String groupCode);

    /**
     * 设置分组优先级
     * @param priorityInBO
     * @return
     */
    int updateActivityGroupPriority(GroupPriorityVO priorityInBO);


    /**
     * 根据分组编码查询是否存在
     * @param tenantCode
     * @param groupCode
     * @return > 0 存在
     */
    int getGroupCountGroupType(String tenantCode,String groupCode);



    /**
     * 批量编码查询分组
     * @param tenantCode
     * @param groupCode
     * @return
     */
    List<PromoGroupVO> queryGroupByGroupCode(String tenantCode,List<String> groupCode);


    void refreshActivityGroupData(String domainCode, String tenantCode);
}
