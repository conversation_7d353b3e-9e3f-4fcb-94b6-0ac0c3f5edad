package com.gtech.promotion.domain.marketing;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2021/2/25 14:24
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class QueryMemberLuckyDrawFrequencyDomain implements Serializable {

    private static final long serialVersionUID = -8179683046811056009L;
    private String domainCode;

    private String tenantCode;

    private String orgCode;

    private String activityCode;

    private String memberCode;
}
