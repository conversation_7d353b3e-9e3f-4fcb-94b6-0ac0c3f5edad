/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * <PERSON>ECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.domain.coupon;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

import com.gtech.commons.utils.DateUtil;
import com.gtech.promotion.code.coupon.CouponStatusEnum;
import com.gtech.promotion.domain.StoreInfoDomain;
import com.gtech.promotion.vo.bean.ActivityPeriod;
import com.gtech.promotion.vo.param.activity.CustomCondition;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * CouponDomain
 *
 * <AUTHOR>
 * @Date 2020-02-25
 */
@Getter
@Setter
@ToString
public class CouponDomain implements Serializable {

    private static final long serialVersionUID = 960993679281971244L;

    // Tenant code.
    private String tenantCode;

    // Activity code.
    private String activityCode;

    // Activity name.
    private String activityName;

    // Activity label
    private String activityLabel;

    // Coupon activity description.
    private String activityDesc;

    // Activity remark
    private String activityRemark;

    // Activity status: 01-待提交 02-审核中 03-已拒绝 04-已生效 05-已结束 06-暂停中 07-已终止
    private String activityStatus;

    // Coupon code.
    private String couponCode;

    // Coupon type: 01-优惠券 02-匿名券 03-优惠码
    private String couponType;

    // 券OPS类型：201- 202- 203- 204-
    private String opsType;

    // Coupon face value.
    private BigDecimal faceValue;

    // Coupon face value unit: 01-金额 02-折扣
    private String faceUnit;

    // Coupon use condition value.
    private BigDecimal conditionValue;

    // Coupon use condition value unit: 01-金额 02-数量
    private String conditionUnit;

    // Promotion activity page url.
    private String activityUrl;

    // Coupon status: 01-未发放 02-已发放 03-已使用 04-已锁定 05-已过期
    private String status;

    // Coupon frozen status: 01-未冻结 02-已冻结
    private String frozenStatus;

    // Coupon received time.
    private String receivedTime;

    // Coupon used time.
    private String usedTime;

    // Coupon valid start time.
    private String validStartTime;

    // Coupon valid end time.
    private String validEndTime;

    // Take coupon label: 01-免费领取 02-活动赠券 03-促销赠券 04-积分换购 05-有价券 06-新人券 07-生日券 08-补偿券 09-推送券 10-匿名券 11-其他
    private String takeLabel;

    private String couponSource;

    // Coupon reference order no
    private String usedRefId;

    // Incentive type: 01-减金额 02-打折扣 03-单件固定金额 04-组合固定金额 05-包邮 06-送赠品 07-买A送A 08-买A送B 12-买A减价B 13-买A打折B
    private String rewardType;

    // Coupon store list.
    private List<StoreInfoDomain> stores;

    @ApiModelProperty(value = "Activity period.")
    private ActivityPeriod activityPeriod;

	@ApiModelProperty(value = "Custom conditions.")
	private List<CustomCondition> customConditions;
    /**
     * 判断是否需要做过期处理
     */
    public boolean isNeedToDoExpire() {

        return (CouponStatusEnum.UN_GRANT.equalsCode(this.status) || CouponStatusEnum.GRANTED.equalsCode(this.status))
                        && DateUtil.parseDate(this.validEndTime, DateUtil.FORMAT_YYYYMMDDHHMISS_14).getTime() <= System.currentTimeMillis();
    }

}
