package com.gtech.promotion.domain.marketing;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class TicketSendDomain implements Serializable {

    private static final long serialVersionUID = 4956567569028961178L;

    private String domainCode;

    private String tenantCode;

    private String orgCode;

    private String activityCode;

    private Integer quality;

    private String frozenStatus;

    private List<String> memberCodes;

    private String rightOfFirstRefusalSourceCode;
}
