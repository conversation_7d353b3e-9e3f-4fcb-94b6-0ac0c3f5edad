package com.gtech.promotion.domain.marketing;

import com.gtech.promotion.vo.bean.ProductCodeAndQuantity;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2021/1/28 10:15
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class JudgeQualificationDomain {

    String domainCode;

    String tenantCode;

    String memberCode;

    String orgCode;

    BigDecimal amount;

    List<String> productCodes;

    List<ProductCodeAndQuantity> productCodeAndQuantity;

    BigDecimal quantity;

    String frozenStatus;

    private Map<String, List<String>> qualifications;





}
