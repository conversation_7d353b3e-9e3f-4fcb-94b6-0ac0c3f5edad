package com.gtech.promotion.domain.marketing.flashsale;

import com.gtech.promotion.domain.marketing.MarketingDomain;
import com.gtech.promotion.vo.bean.marketing.MarketingGroup;
import com.gtech.promotion.vo.bean.marketing.boostsharing.BoostSharing;
import com.gtech.promotion.vo.bean.marketing.flashsale.FlashSaleProduct;
import com.gtech.promotion.vo.bean.marketing.flashsale.FlashSaleQualification;
import com.gtech.promotion.vo.bean.marketing.flashsale.FlashSaleStore;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
public class FlashSaleDomain extends MarketingDomain {

    private String importNo;

    private String preSalePayType;

    private String shippingTime;



    private List<FlashSaleQualification> flashSaleQualifications;

    private List<FlashSaleStore> flashSaleStores;

    private List<FlashSaleProduct> products;

    private MarketingGroup marketingGroup;

    private BoostSharing boostSharing;


}
