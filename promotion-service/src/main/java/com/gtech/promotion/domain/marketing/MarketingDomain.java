package com.gtech.promotion.domain.marketing;

import com.gtech.promotion.code.marketing.ActivityTypeEnum;
import com.gtech.promotion.vo.bean.*;
import com.gtech.promotion.vo.bean.marketing.MarketingLanguage;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MarketingDomain {

    private String selectProductType;

    private String domainCode;

    private String tenantCode;

    private String orgCode;

    private String groupCode;

    private String activityType;

    private String opsType;

    private String activityCode;

    private String activityName;

    private String activityBegin;

    private String activityEnd;

    private String activityStatus;

    private String activityUrl;

    private String sponsors;
    private String backgroundImage;
    private String ribbonImage;
    private String ribbonPosition;
    private String ribbonText;
    private String coolDown;
    private String warmBegin;
    private String warmEnd;

    private String operateUser;
    private String operateLastName;

    private String operateFirstName;
    private List<MarketingLanguage> marketingLanguages;

    private List<Qualification> qualifications;

    private List<IncentiveLimited> incentiveLimiteds;

    private List<LuckyDrawRule> luckyDrawRules;

    private ActivityPeriod activityPeriod;

    private String incentiveLimitedFlag;

    private String luckyDrawRuleFlag;

    private List<ExtImage> extImages;
    public void ofInfoNull(){
        setActivityBegin(null);
        setActivityEnd(null);
        if (!ActivityTypeEnum.GROUP.equalsCode(activityType)){
            setActivityType(null);
        }
        setActivityUrl(null);
        setWarmBegin(null);
        setWarmEnd(null);
        setCoolDown(null);
        setOpsType(null);
        setSponsors(null);
        setActivityPeriod(null);
    }
}
