package com.gtech.promotion.domain.coupon;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.Date;

@Getter
@Setter
@ToString
public class CouponReleaseDomain {

    // Tenant code.
    private String tenantCode;

    // Activity code.
    private String activityCode;

    // Coupon type: 01-优惠券 02-匿名券 03-优惠码
    private String couponType;

    // Coupon release code.
    private String releaseCode;

    // Coupon release status: 01-Waiting released 02-Already released 03-Imported 04-Cancel released
    private String releaseStatus;

    // Release coupon quantity: releaseQuantity fixed 1 while couponType=03
    private Integer releaseQuantity;

    // Release coupon inventory
    private Integer inventory;

    // User received coupon quantity.
    private Integer receivedQuantity;

    // User used coupon quantity.
    private Integer usedTotal;

    // User locked coupon quantity.
    private Integer locked;

    // Coupon release source: 01-System generated 02-Imported
    private String releaseSource;

    // Receive coupon start time: yyyyMMddhhmmss
    private String receiveStartTime;

    // Receive coupon end time: yyyyMMddhhmmss
    private String receiveEndTime;

    // Validity days from received date.
    private Integer validDays;

    // Validity date begin: yyyyMMddhhmmss
    private String validStartTime;

    // Validity date end: yyyyMMddhhmmss
    private String validEndTime;

    // Appointment time of coupon release: yyyyMMddhhmmss, required while releaseType=02
    private String releaseTime;

    // Coupon release type: 01-immediately, 02-appointment
    private String releaseType;

    private String couponCodePrefix;

	private Integer couponCodeLength;

    // 时间同步活动时间 0-不同步 1-同步
    private String timeSameActivity;

    private String receiveTimeSameActivity;


    private String couponRuleType;

    private String promotionType;

    private String remark;
    private String createUser;

    private Date createTime;


}
