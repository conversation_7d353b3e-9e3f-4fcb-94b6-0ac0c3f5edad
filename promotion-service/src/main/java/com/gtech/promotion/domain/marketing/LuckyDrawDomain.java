package com.gtech.promotion.domain.marketing;

import com.gtech.promotion.vo.bean.IncentiveLimited;
import com.gtech.promotion.vo.bean.LuckyDrawRule;
import com.gtech.promotion.vo.bean.Qualification;
import com.gtech.promotion.vo.bean.marketing.MarketingPrize;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
public class LuckyDrawDomain extends MarketingDomain {

    private List<MarketingPrize> marketingPrizes;

    private List<Qualification> qualifications;

    private List<IncentiveLimited> incentiveLimiteds;

    private List<LuckyDrawRule> luckyDrawRules;
}
