/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * <PERSON>ECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.callable;

import com.gtech.promotion.calc.CalcExecuter;
import com.gtech.promotion.calc.model.CalcShoppingCart;
import com.gtech.promotion.component.activity.ActivityCacheDomain;
import com.gtech.promotion.component.activity.ShoppingCartDomain;
import com.gtech.promotion.dto.cache.ActivityCacheDTO;
import com.gtech.promotion.dto.in.activity.ShoppingCartDTO;
import com.gtech.promotion.dto.out.activity.ShoppingCartOutDTO;
import lombok.extern.slf4j.Slf4j;

import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * 自动选优惠券的任务
 */
@Slf4j
public class CouponChooseCallable implements PromotionCallable<List<ShoppingCartOutDTO>> {

    private ShoppingCartDomain shoppingCartDomain;
    private ActivityCacheDomain activityCacheDomain;
    private ShoppingCartDTO shoppingCart;
    private CalcExecuter calcExecuter;

    public CouponChooseCallable(ShoppingCartDomain shoppingCartDomain, ActivityCacheDomain activityCacheDomain, ShoppingCartDTO shoppingCart, CalcExecuter calcExecuter) {
        this.shoppingCartDomain = shoppingCartDomain;
        this.activityCacheDomain = activityCacheDomain;
        this.shoppingCart = shoppingCart;
        this.calcExecuter = calcExecuter;
    }

    @Override
    public List<ShoppingCartOutDTO> call() throws Exception {

        return this.chooseCouponsByShoppingCart();
    }

    public List<ShoppingCartOutDTO> chooseCouponsByShoppingCart() {
        // 计算
        try {
            Map<String, ActivityCacheDTO> activityCacheMap = activityCacheDomain.getActivityCacheMap(shoppingCart.getTenantCode(), shoppingCart.getLanguage(), 2, null);
            shoppingCart = shoppingCartDomain.queryActivity(shoppingCart, activityCacheMap,false);
            return calcExecuter.calc(new CalcShoppingCart(shoppingCart), activityCacheMap);
        } catch (Exception e) {
            log.error("CouponChooseCallable.chooseCouponsByShoppingCart::{}", shoppingCart.getCouponCodes(), e);
            return Collections.emptyList();
        }
    }

}
