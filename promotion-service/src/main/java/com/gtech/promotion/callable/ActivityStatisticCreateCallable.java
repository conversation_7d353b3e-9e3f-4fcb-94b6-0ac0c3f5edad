/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.callable;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.util.CollectionUtils;

import com.gtech.commons.utils.DateUtil;
import com.gtech.promotion.code.activity.ActivityTypeEnum;
import com.gtech.promotion.dao.entity.activity.ActivityEntity;
import com.gtech.promotion.dao.entity.activity.TPromoActivityIncentiveEntity;
import com.gtech.promotion.dao.entity.activity.TPromoOrderEntity;
import com.gtech.promotion.dao.mongo.activity.OrderDetailEntity;
import com.gtech.promotion.dto.in.activity.TPromoActivityStatisticInDTO;
import com.gtech.promotion.service.activity.ActivityService;
import com.gtech.promotion.service.activity.TPromoActivityIncentiveService;
import com.gtech.promotion.service.activity.TPromoActivityStatisticService;
import com.gtech.promotion.service.activity.TPromoOrderService;
import com.gtech.promotion.service.coupon.PromoCouponCodeUserService;

import lombok.extern.slf4j.Slf4j;

@Slf4j
public class ActivityStatisticCreateCallable implements PromotionCallable<String> {

    private TPromoOrderService orderService;

    private TPromoActivityIncentiveService incentiveService;

    private ActivityService activityService;

    private MongoTemplate mongoTemplate;

    private TPromoActivityStatisticService statisticService;

    private PromoCouponCodeUserService couponCodeUserService;

    public ActivityStatisticCreateCallable(TPromoOrderService orderService, TPromoActivityIncentiveService incentiveService, ActivityService activityService,
                    MongoTemplate mongoTemplate, TPromoActivityStatisticService statisticService, PromoCouponCodeUserService couponCodeUserService) {
        this.orderService = orderService;
        this.incentiveService = incentiveService;
        this.activityService = activityService;
        this.mongoTemplate = mongoTemplate;
        this.statisticService = statisticService;
        this.couponCodeUserService = couponCodeUserService;
    }

    @Override
    public String call() {

        //总维度:产生已付款的活动订单  单表查询
        List<TPromoOrderEntity> list = orderService.queryPromoOrderYesterday();

        if (CollectionUtils.isEmpty(list)) {
            return "昨天无有效订单";

        }
        //所有租铺的活动奖励
        List<TPromoActivityIncentiveEntity> incentiveList = incentiveService.getListByOrderIds(list.stream().map(TPromoOrderEntity::getId).collect(Collectors.toList()));

        String success = "各租铺活动统计数据成功";
        if (CollectionUtils.isEmpty(incentiveList)) {
            return success;
        }

        //根据租铺分组
        Map<String, List<TPromoActivityIncentiveEntity>> map = groupByTenantCode(incentiveList);

        //循环遍历map  根据租铺顺序执行统计操作
        Iterator<Map.Entry<String, List<TPromoActivityIncentiveEntity>>> iterator = map.entrySet().iterator();

        try {

            List<TPromoActivityStatisticInDTO> batchList = new ArrayList<>();

            while (iterator.hasNext()) {
                //操作此租铺的奖励记录
                Map.Entry<String, List<TPromoActivityIncentiveEntity>> next = iterator.next();

                //查询此租铺下活动信息
                List<String> activityCodes = next.getValue().stream().map(TPromoActivityIncentiveEntity::getActivityCode).distinct().collect(Collectors.toList());

                List<ActivityEntity> activityEntities = activityService.queryActivityByCodes(next.getKey(), activityCodes);

                // 将activityCode 和 activity绑定
                HashMap<String, ActivityEntity> hashMap = new HashMap<>();
                for (ActivityEntity entity : activityEntities) {
                    hashMap.put(entity.getActivityCode(), entity);
                }

                setBatchList(batchList, next, activityCodes, hashMap);
            }

            statisticService.createActivityStatisticBatch(batchList);

        } catch (Exception e) {
            log.error("创建活动统计数据失败信息:{}", e);
            return "";
        }

        return success;
    }

    private void setBatchList(List<TPromoActivityStatisticInDTO> batchList, Map.Entry<String, List<TPromoActivityIncentiveEntity>> next,
                    List<String> activityCodes, HashMap<String, ActivityEntity> hashMap) {

        for (String activityCode : activityCodes) {

            //优惠金额
            BigDecimal incentiveAmount = new BigDecimal(0);
            // 活动参与的人数
            Set<String> userCodes = new HashSet<>();
            // 活动参与次数
            List<String> promoOrderIds = new ArrayList<>();
            // 活动下商品总价
            BigDecimal productPrice = new BigDecimal(0);
            // 订单在至少一个活动下的总减免金额
            BigDecimal reduceAmount = new BigDecimal(0);

            for (TPromoActivityIncentiveEntity incentiveEntity : next.getValue()) {
                if (incentiveEntity.getActivityCode().equals(activityCode)) {

                    userCodes.add(incentiveEntity.getUserCode());
                    promoOrderIds.add(incentiveEntity.getPromoOrderId());
                    
                    incentiveAmount = incentiveAmount.add(incentiveEntity.getIncentiveAmount());

                    // 该活动下该订单在当前活动下的减免金额   sum:该活动的所有减免金额
                    reduceAmount = setReduceAmount(next, reduceAmount, incentiveEntity);
                    productPrice = setProductPrice(next, productPrice, incentiveEntity);
                }
            }

            // 该活动一天总的领取券的数量
            Integer allocateCouponCount = null;
            Integer useCouponCount = null;

            if (hashMap.get(activityCode).getActivityType().equals(ActivityTypeEnum.COUPON.code())) {
                allocateCouponCount = couponCodeUserService.getAllocateCouponCountYesterday111(next.getKey(), activityCode);
                useCouponCount = couponCodeUserService.getUseCouponCountYesterday111(next.getKey(), activityCode);
            }

            batchList.add(TPromoActivityStatisticInDTO.builder()
                .tenantCode(next.getKey())
                .activityCode(activityCode)
                .orderCount(promoOrderIds.size())
                .memberCount(userCodes.size())
                .reduceAmount(incentiveAmount)
                .orderAmount(productPrice.subtract(reduceAmount))
                .statisticDate(DateUtil.format(DateUtil.addDay(DateUtil.now(), -1), DateUtil.FORMAT_YYYYMMDD))
                .allocateCouponCount(allocateCouponCount)
                .useCouponCount(useCouponCount)
                .build());
        }
    }

    private BigDecimal setProductPrice(Map.Entry<String, List<TPromoActivityIncentiveEntity>> next, BigDecimal productPrice, TPromoActivityIncentiveEntity incentiveEntity) {

        Query query = new Query();
        Criteria criteria = Criteria.where("promoOrderId").is(incentiveEntity.getPromoOrderId()).and("tenantCode").is(next.getKey());
        query.addCriteria(criteria);

        List<OrderDetailEntity> entityList = mongoTemplate.find(query, OrderDetailEntity.class);
        for (OrderDetailEntity detailVO : entityList) {
            productPrice = productPrice.add(detailVO.getProductAmount());
        }
        return productPrice;
    }

    private BigDecimal setReduceAmount(Map.Entry<String, List<TPromoActivityIncentiveEntity>> next, BigDecimal reduceAmount, TPromoActivityIncentiveEntity incentiveEntity) {

        List<BigDecimal> collect = next.getValue().stream().filter(x -> x.getPromoOrderId().equals(incentiveEntity.getPromoOrderId())).map(TPromoActivityIncentiveEntity::getIncentiveAmount)
            .collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(collect)) {
            for (BigDecimal bigDecimal : collect) {
                reduceAmount = reduceAmount.add(bigDecimal);
            }
        }
        return reduceAmount;
    }

    private Map<String, List<TPromoActivityIncentiveEntity>> groupByTenantCode(List<TPromoActivityIncentiveEntity> incentiveList) {

        Map<String, List<TPromoActivityIncentiveEntity>> map = new HashMap<>();
        for (TPromoActivityIncentiveEntity incentive : incentiveList) {
            if (map.containsKey(incentive.getTenantCode())) {
                map.get(incentive.getTenantCode()).add(incentive);
            } else {
                List<TPromoActivityIncentiveEntity> entityList = new ArrayList<>();
                entityList.add(incentive);
                map.put(incentive.getTenantCode(), entityList);
            }
        }
        return map;
    }
}
