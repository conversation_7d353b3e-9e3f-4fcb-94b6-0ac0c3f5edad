/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WA<PERSON>RANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.callable;

import com.gtech.promotion.checker.SystemChecker;
import com.gtech.promotion.exception.PromotionException;
import lombok.Setter;

import java.util.Collection;
import java.util.List;
import java.util.concurrent.*;

/**
 *
 */
public class ThreadPoolUtil{

    private ThreadPoolUtil() {
        throw new PromotionException(SystemChecker.CAN_NOT_INSTANCE_ERROR);
    }

    @Setter
    private static ExecutorService executorService ;
    private static ScheduledExecutorService scheduledExecutorService ;

    public static void createExecutor(int maxThread){
        executorService = new ThreadPoolExecutor(10,maxThread,3000L
                ,TimeUnit.MILLISECONDS,new SynchronousQueue<>(), new ThreadPoolExecutor.CallerRunsPolicy());
    }

    public static void createScheduleExecutor(){
        scheduledExecutorService = Executors.newScheduledThreadPool(1);
    }

    /**
     * 设置定时任务
     * @param callable 任务
     * @param delay 延时多久
     * @param unit 单位
     * @return 异步执行结果
     */
    public static <V> ScheduledFuture<V> schedule(PromotionCallable<V> callable,long delay, TimeUnit unit){
        try {
            return scheduledExecutorService.schedule(callable, delay, unit);
        } catch (RejectedExecutionException | NullPointerException e){
            throw new PromotionException(SystemChecker.SCHEDULE_ERROR);
        }

    }

    /**
     * 给线程池提交任务
     * 
     * @param collection 任务集合
     * @param <V> 任务附带对象
     * @return 任务执行结果
     */
    public static <V> List<Future<V>> pushTasks(Collection<? extends PromotionCallable<V>> collection) {

        try {
            return executorService.invokeAll(collection, 3000L, TimeUnit.MILLISECONDS);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            throw new PromotionException(SystemChecker.CALC_ERROR);
        }
    }

    public static <V> Future<V> pushTask(PromotionCallable<V> callable){
        return executorService.submit(callable);
    }

    /**
     * 关闭线程池
     */
    public static void shutdownThreadPool(){
        executorService.shutdown();
    }
    
}
  
