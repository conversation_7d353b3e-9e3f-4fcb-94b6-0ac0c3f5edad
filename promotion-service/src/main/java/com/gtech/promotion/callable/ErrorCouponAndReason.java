/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.callable;

import com.gtech.commons.exception.ErrorCode;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * ErrorCouponAndReason
 */
@Setter
@Getter
@ToString
public class ErrorCouponAndReason {

    private String errorCoupon;
    private ErrorCode failedReason;

    public ErrorCouponAndReason(String couponCode, ErrorCode errorCode) {

        this.errorCoupon = couponCode;
        this.failedReason = errorCode;
    }

    public String getFalseReason() {
        
        return null == failedReason ? null : failedReason.getMessage();
    }

    public String getFalseCode() {
        
        return null == failedReason ? null : failedReason.getCode();
    }
}
