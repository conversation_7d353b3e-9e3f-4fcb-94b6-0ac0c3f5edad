/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.vo.mongo;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

import com.gtech.promotion.vo.bean.ProductAttribute;

import lombok.Data;

/**
 * 订单的mongo数据
 */
@Data
public class TPromoOrderDetailVO implements Serializable{

    private static final long serialVersionUID = -1905107179991687190L;

    // 促销订单ID
    private String promoOrderId;

    // 租户编码
    private String tenantCode;

    // 品类编码：0000-全部品类
    private List<String> categoryCodes;

    // 品牌编码：0000-全部品牌
    private String brandCode;

    // 商品编码：0000-不限制
    private String productCode;

    // sku编码：0000-不限制
    private String skuCode;
    
    // 套装sku编码：0000-不限制
    private String combineSkuCode;

    // Product attribute list.
    private List<ProductAttribute> attributes;

    // 商品原单价
    private BigDecimal productPrice;

    // 商品原总价
    private BigDecimal productAmount;

    // 购买数量
    private Integer quantity;

    // Promotion activity list.
    private List<TPromoOrderDetailActivityVO> activities;

}
