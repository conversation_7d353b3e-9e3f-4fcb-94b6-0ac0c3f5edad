/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.vo.mongo;

import com.gtech.promotion.vo.bean.Giveaway;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**   
 * <功能描述>
 */
@Data
public class TPromoOrderDetailActivityVO implements Serializable{

    private static final long serialVersionUID = 7760126748693129671L;

    private String activityCode;

    private String couponCode;

    private String promoScope;//规则范围 01：单品 ，02：商品范围 ， 03：订单

    private String activityName;

    private String activityLabel;

    private String activityType;

    // 促销前金额
    private BigDecimal amountBefore;

    // 促销后金额
    private BigDecimal amountAfter;

    private String giveawayLimitMax;//赠品赠送最大限制数量

    private List<Giveaway> giveaways;//赠品列表

}