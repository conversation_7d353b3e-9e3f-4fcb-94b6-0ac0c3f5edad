/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.utils;

import com.gtech.commons.utils.StringUtil;
import com.gtech.promotion.code.activity.LangTypeEnum;
import com.gtech.promotion.dto.out.coupon.CouponInfoImportOutDTO;
import com.gtech.promotion.dto.out.coupon.ManagementDataOutDTO;

import java.io.IOException;
import java.io.OutputStream;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.Map;

/**
 * 导出优惠券代码
 */
public class ExportCouponUtil{
	private ExportCouponUtil() {
		throw new IllegalStateException("Utility class");
	}
    private static final String ENTER = "\n";
    private static final String TAB = "\t";
    private static final String COMMA = ",";
    private static final  String COUPON_STATUS = "couponStatus";
                  
    private static final  String COUPON_FROZEN_STATUS = "couponFrozenStatus";
                  
    private static final  String COUPON_TYPE = "couponType";
                  
    private static final  String COUPON_TAKE_LABEL = "couponTakeLabel";
    public static void exportCouponInfoHeader(OutputStream outputStream, String lang) throws IOException {
        StringBuilder sb = new StringBuilder();
        if(StringUtil.isBlank(lang)||(!LangTypeEnum.HK.equalsCode(lang)&&!LangTypeEnum.US.equalsCode(lang))) {//默认中文
            sb.append("券码编号").append(TAB).append(COMMA)
            .append("券码状态").append(TAB).append(COMMA)
            .append("券码名称").append(TAB).append(COMMA)
            .append("券码描述").append(TAB).append(COMMA)
            .append("创建时间").append(TAB).append(COMMA)
            .append("可领取开始时间").append(TAB).append(COMMA)
            .append("可领取结束时间").append(TAB).append(COMMA)
            .append("可用开始时间").append(TAB).append(COMMA)
            .append("可用结束时间").append(TAB).append(COMMA)
            .append("可用時長（日）").append(TAB).append(COMMA)
            .append("领取时间").append(TAB).append(COMMA)
            .append("会员编码").append(TAB).append(COMMA)
            .append("订单编号").append(TAB).append(COMMA)
            .append("使用时间").append(TAB).append(COMMA)
            .append("冻结状态").append(ENTER);
        }else if(LangTypeEnum.HK.equalsCode(lang)) {
            sb.append("券碼編號").append(TAB).append(COMMA)
            .append("券碼狀態").append(TAB).append(COMMA)
            .append("券碼名稱").append(TAB).append(COMMA)
            .append("券碼描述").append(TAB).append(COMMA)
            .append("創建時間").append(TAB).append(COMMA)
            .append("可領取開始時間 ").append(TAB).append(COMMA)
            .append("可領取結束時間").append(TAB).append(COMMA)
            .append("可用開始時間").append(TAB).append(COMMA)
            .append("可用結束時間").append(TAB).append(COMMA)
            .append("可用時長（日）").append(TAB).append(COMMA)
            .append("領取時間").append(TAB).append(COMMA)
            .append("會員編碼").append(TAB).append(COMMA)
            .append("訂單編號").append(TAB).append(COMMA)
            .append("使用時間").append(TAB).append(COMMA)
            .append("凍結狀態").append(ENTER);
        }else if(LangTypeEnum.US.equalsCode(lang)) {
            sb.append("Coupon Number").append(TAB).append(COMMA)
            .append("Status").append(TAB).append(COMMA)
            .append("Coupon Name").append(TAB).append(COMMA)
            .append("Coupon Description").append(TAB).append(COMMA)
            .append("Create Time").append(TAB).append(COMMA)
            .append("Coupon Claiming Start Time ").append(TAB).append(COMMA)
            .append("Coupon Claiming  End Time").append(TAB).append(COMMA)
            .append("Coupon Valid Start Time").append(TAB).append(COMMA)
            .append("Coupon Valid End Time").append(TAB).append(COMMA)
            .append("Valid Period Duration (Days)").append(TAB).append(COMMA)
            .append("Coupon Claiming Time").append(TAB).append(COMMA)
            .append("Member ID").append(TAB).append(COMMA)
            .append("Order ID").append(TAB).append(COMMA)
            .append("Redeemed Time").append(TAB).append(COMMA)
            .append("Frozen").append(ENTER);
        }
        
   
        outputStream.write(new byte[] { (byte) 0xEF, (byte) 0xBB,(byte) 0xBF});
        outputStream.write(sb.toString().getBytes(StandardCharsets.UTF_8));
        outputStream.flush();
    }

    public static void exportCouponInfoContent(List<CouponInfoImportOutDTO> list, OutputStream outputStream) throws IOException {
        StringBuilder sb = new StringBuilder();
        for (CouponInfoImportOutDTO couponInfoDTO : list) {
            sb.append(couponInfoDTO.getCouponCode()).append(TAB).append(COMMA)
                .append(couponInfoDTO.getCouponStatus()).append(TAB).append(COMMA)
                .append(couponInfoDTO.getCouponName()).append(TAB).append(COMMA)
                .append(couponInfoDTO.getCouponDesc()).append(TAB).append(COMMA)
                .append(couponInfoDTO.getCreateTime()).append(TAB).append(COMMA)
                .append(couponInfoDTO.getReceiveStartTime()).append(TAB).append(COMMA)
                .append(couponInfoDTO.getReceiveEndTime()).append(TAB).append(COMMA)
                .append(couponInfoDTO.getValidBegin()).append(TAB).append(COMMA)
                .append(couponInfoDTO.getValidEnd()).append(TAB).append(COMMA)
                .append(null == couponInfoDTO.getValidDays() ? "" : String.valueOf(couponInfoDTO.getValidDays())).append(TAB).append(COMMA)
                .append(StringUtil.isBlank(couponInfoDTO.getReceiveTime()) ? "" : couponInfoDTO.getReceiveTime()).append(TAB).append(COMMA)
                .append(StringUtil.isBlank(couponInfoDTO.getUserCode()) ? "" : couponInfoDTO.getUserCode()).append(TAB).append(COMMA)
                .append(StringUtil.isBlank(couponInfoDTO.getUsedRefId()) || "DEFAULT".equals(couponInfoDTO.getUsedRefId()) ? "" : couponInfoDTO.getUsedRefId()).append(TAB).append(COMMA)
                .append(StringUtil.isBlank(couponInfoDTO.getUsedTime()) ? "" : couponInfoDTO.getUsedTime()).append(TAB).append(COMMA)
                .append(couponInfoDTO.getFrozenStatus()).append(ENTER);
        }
        outputStream.write(sb.toString().getBytes(StandardCharsets.UTF_8));
        outputStream.flush();
    }
    
    public static void exportManagementCouponInfoHeader(OutputStream outputStream) throws IOException {
        
        StringBuilder sb = new StringBuilder();

        sb.append("Activity Number").append(TAB).append(COMMA)
        .append("Activity Name").append(TAB).append(COMMA)
        .append("Coupon Number").append(TAB).append(COMMA)
        .append("Coupon Type").append(TAB).append(COMMA)
        .append("Status").append(TAB).append(COMMA)
        .append("Coupon Issue Time").append(TAB).append(COMMA)
        .append("Coupon Claiming Time").append(TAB).append(COMMA)
        .append("Redeemed Time").append(TAB).append(COMMA)
        .append("Member ID").append(TAB).append(COMMA)
        .append("Order ID").append(TAB).append(COMMA)
        .append("Receive tag").append(TAB).append(COMMA)
        .append("Frozen").append(ENTER);
        
        outputStream.write(new byte[] { (byte) 0xEF, (byte) 0xBB,(byte) 0xBF});
        outputStream.write(sb.toString().getBytes(StandardCharsets.UTF_8));
        outputStream.flush();
    }
    
    public static void exportManagementCouponInfoContent(List<ManagementDataOutDTO> list, OutputStream outputStream,Map<String, Map<String, String>> hashMap) throws IOException {
        StringBuilder sb = new StringBuilder();
        for (ManagementDataOutDTO couponInfoDTO : list) {
                sb.append(couponInfoDTO.getActivityCode()).append(TAB).append(COMMA)
                .append(couponInfoDTO.getActivityName()).append(TAB).append(COMMA)
                .append(couponInfoDTO.getCouponCode()).append(TAB).append(COMMA)
                .append(hashMap.get(COUPON_TYPE).get(couponInfoDTO.getCouponType())).append(TAB).append(COMMA)
                .append(hashMap.get(COUPON_STATUS).get(couponInfoDTO.getStatus())).append(TAB).append(COMMA)
                .append(couponInfoDTO.getReleaseTime()).append(TAB).append(COMMA)
                .append(StringUtil.isBlank(couponInfoDTO.getReceiveTime()) ? "" : couponInfoDTO.getReceiveTime()).append(TAB).append(COMMA)
                .append(StringUtil.isBlank(couponInfoDTO.getUsedTime()) ? "" : couponInfoDTO.getUsedTime()).append(TAB).append(COMMA)
                .append(StringUtil.isBlank(couponInfoDTO.getUserCode()) ? "" : couponInfoDTO.getUserCode()).append(TAB).append(COMMA)
                .append(StringUtil.isBlank(couponInfoDTO.getUsedRefId()) || "DEFAULT".equals(couponInfoDTO.getUsedRefId()) ? "" : couponInfoDTO.getUsedRefId()).append(TAB).append(COMMA)
                .append(StringUtil.isBlank(couponInfoDTO.getTakeLabel()) ? "" : hashMap.get(COUPON_TAKE_LABEL).get(couponInfoDTO.getTakeLabel())).append(TAB).append(COMMA)
                .append(hashMap.get(COUPON_FROZEN_STATUS).get(couponInfoDTO.getFrozenStatus())).append(ENTER);
        }
        outputStream.write(sb.toString().getBytes(StandardCharsets.UTF_8));
        outputStream.flush();
    }
}
