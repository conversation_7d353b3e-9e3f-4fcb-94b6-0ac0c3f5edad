/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.utils;

import com.gtech.commons.utils.CheckUtils;
import com.gtech.commons.utils.StringUtil;
import com.gtech.promotion.exception.ErrorCodes;
import com.gtech.promotion.vo.bean.ProductAttribute;
import com.gtech.promotion.vo.bean.ProductCodes;
import org.springframework.util.CollectionUtils;

import java.util.Iterator;

/**
 * 商品查活动商品信息校验工具类
 */
public class ActivityProductCheckUtil{
	private ActivityProductCheckUtil() {
		throw new IllegalStateException("Utility class");
	}
    /**
     * 商品属性校验：购物车sku编码不能也为空
     * 
     * @param product
     */
    public static void checkProduct(ProductCodes product){
        checkProductCombine(product);
        if (StringUtil.isBlank(product.getCombineSkuCode())){
            CheckUtils.isNotBlank(product.getSkuCode(), ErrorCodes.PARAM_EMPTY, "skuCode");
        }
    }
    
    public static void checkProductCombine(ProductCodes product){
        if (StringUtil.isBlank(product.getCombineSkuCode())){
            //根据商品匹配活动 属性编码和值只要有一个为空，则不合法，删掉，不抛错
            if (!CollectionUtils.isEmpty(product.getAttributes())){
                Iterator<ProductAttribute> iterator = product.getAttributes().iterator();
                while (iterator.hasNext()){
                    ProductAttribute attributelDTO = iterator.next();
                    if (StringUtil.isBlank(attributelDTO.getAttributeCode()) || StringUtil.isBlank(attributelDTO.getAttributeValues())){
                        iterator.remove();
                    }
                }
            }
        }else{
            product.setAttributes(null);
            product.setCategoryCodes(null);
            product.setBrandCode(null);
            product.setProductCode(null);
            product.setSkuCode(null);
        }
    }

}
