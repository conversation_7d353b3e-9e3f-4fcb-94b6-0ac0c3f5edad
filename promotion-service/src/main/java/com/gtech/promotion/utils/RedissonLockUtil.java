package com.gtech.promotion.utils;

import com.gtech.promotion.checker.SystemChecker;
import com.gtech.promotion.exception.PromotionException;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.concurrent.TimeUnit;

@Component
@Slf4j
public class RedissonLockUtil {
    private final Long DEFAULT_WAIT_TIME = 5000L;

    @Autowired
    private RedissonClient redissonClient;


    /**
     * 加锁后运行
     *
     * @param lockName 锁名称
     * @param waitTime 等待时间，超过之后取锁失败
     * @param runnable Runnable
     */
    @SneakyThrows
    public void runWithLock(String lockName, Long waitTime,Long leaseTime, Runnable runnable) {
        RLock lock = redissonClient.getLock(lockName);
        if (waitTime == null) {
            waitTime = DEFAULT_WAIT_TIME;
        }

        boolean lockOn = lock.tryLock(waitTime, leaseTime,TimeUnit.MILLISECONDS);
        log.info("runWithLock:{} lockOn:{}", lockName,lockOn);
        if (lockOn) {
            try {
                runnable.run();
            } finally {
                log.info("runWithLock:{} unlock",lockName);
                lock.unlock();
            }
        } else {
            throw new PromotionException(SystemChecker.LOCK_FAIL);
        }
    }


}
