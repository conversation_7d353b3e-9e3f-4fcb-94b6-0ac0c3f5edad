/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.feign;

import com.alibaba.fastjson.JSONObject;
import com.gtech.promotion.feign.bean.ProductCodeRequest;
import com.gtech.promotion.feign.bean.SkuCodeQueryRequest;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@FeignClient(value = "PIM", url = "${titan.gateway.url:}", contextId = "PimFeignClient")
public interface PimFeignClient {

    @PostMapping(value = "/pim/sku/querySkuPage",consumes = MediaType.APPLICATION_JSON_VALUE)
    public JSONObject querySkuPage(@RequestBody SkuCodeQueryRequest param);


    @PostMapping(value = "pim/sku/querySkuWithProductList",consumes = MediaType.APPLICATION_JSON_VALUE)
    JSONObject querySkuWithProductList(@RequestBody ProductCodeRequest param);

}
