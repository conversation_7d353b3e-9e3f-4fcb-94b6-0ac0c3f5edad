package com.gtech.promotion.feign;

import com.alibaba.fastjson.JSONObject;
import com.gtech.basic.masterdata.response.JsonResult;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * @Description :
 * <AUTHOR> gaojie.li
 * @Date : 2021/7/27 10:50
 */
@FeignClient(value = "MASTERDATA", url = "${titan.gateway.url:}")
public interface MasterDataFeignClient {

    @PostMapping(value = "/masterdata/value/getValueValue")
    JsonResult<String> getValueValue(@RequestParam("tenantCode") String tenantCode, @RequestParam("valueCode") String valueCode);

    @PostMapping(value = "/masterdata/dd/queryPages")
    JSONObject queryDdPages(@RequestBody JSONObject request);



}

