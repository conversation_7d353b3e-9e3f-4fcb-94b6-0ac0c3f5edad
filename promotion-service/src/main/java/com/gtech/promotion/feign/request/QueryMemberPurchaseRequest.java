package com.gtech.promotion.feign.request;

import lombok.Data;

import java.util.Date;
import java.util.List;


@Data
public class QueryMemberPurchaseRequest {

    /**
     * 租户号
     */
    private String tenantCode;

    /**
     * 店铺Org
     */
    private String orgCode;

    /**
     * 会员编码
     */
    private String memberCode;

    /**
     * 订单创建时间-开始时间.
     */
    private Date createTimeStart;

    /**
     * 订单创建时间-结束时间.
     */
    private Date createTimeEnd;

    /**
     * 商品编码
     */
    private List<String> productCodes;

    /**
     * sku编码
     */
    private List<String> skuCodes;

    /**
     * 过滤不统计的商品编码
     */
    private List<String> excludeProductCodes;

    /**
     * 过滤不统计的SKU编码, 最多支持100个
     */
    private List<String> excludeSkuCodes;
}
