package com.gtech.promotion.feign.enums;


import com.google.common.collect.Maps;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Map;

/**
 * t_order_order表， order_status字段
 * 21:等待付款
 * 61:买家取消
 * 62:超时自动取消
 */
@AllArgsConstructor
@Getter
public enum OmsOrderStatusEnum {
    /**
     * 21:等待付款
     */
    WAITING_PAYMENT("21"),
    /**
     * 61:买家取消
     */
    BUYER_CANCEL("61"),
    /**
     * 62:超时自动取消
     */
    AUTO_CANCEL("62"),

    ;
    private final String code;


    private static final Map<String, OmsOrderStatusEnum> OMS_ORDER_STATUS_MAP = Maps.newHashMap();
    static {
        Arrays.stream(values()).forEach(val -> OMS_ORDER_STATUS_MAP.put(val.getCode(), val));
    }

    public static OmsOrderStatusEnum getByCode(String code) {
        return OMS_ORDER_STATUS_MAP.get(code);
    }
}
