package com.gtech.promotion.feign.request;

import lombok.Data;

import java.io.Serializable;

/**
 * tenantCode entity
 */
@Data
public class TenantCodeIn implements Serializable {

    private static final long serialVersionUID = -9021151643007451082L;

    private String tenantCode;

    private String domainCode;

    private String channelCode;

    private String orgCode;
    /**
     * 内部等同于orgCode
     */
    private String shopCode;

    private String createUser;


}
