/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.feign;

import com.gtech.basic.idm.web.vo.param.QueryOpUserAccountByCodesParam;
import com.gtech.basic.idm.web.vo.param.QueryUserParam;
import com.gtech.basic.idm.web.vo.result.QueryAccountListResult;
import com.gtech.basic.idm.web.vo.result.QueryOpUserAccountListResult;
import com.gtech.basic.idm.web.vo.result.QueryUserResult;
import com.gtech.commons.result.Result;
import com.gtech.promotion.feign.bean.QueryUserByAccountRequest;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

@FeignClient(value = "IDM", url = "${titan.gateway.url:}")
public interface IdmFeignClient {

    @PostMapping(value = "/idm/opUserAccount/queryUserAccountByCodes",consumes = MediaType.APPLICATION_JSON_VALUE)
    public Result<List<QueryOpUserAccountListResult>> queryOpUserAccountByCodes(@RequestBody QueryOpUserAccountByCodesParam param);

    @PostMapping(value = "/idm/userAccount/queryUserList",consumes = MediaType.APPLICATION_JSON_VALUE)
    public Result<List<QueryUserResult>> queryUserList(@RequestBody QueryUserParam param);

    @PostMapping(value = "/idm/userAccount/queryUserCodeByAccountList",consumes = MediaType.APPLICATION_JSON_VALUE)
    public Result<List<QueryAccountListResult>> queryUserCodeByAccountList(@RequestBody QueryUserByAccountRequest param);
}
