package com.gtech.promotion.feign.enums;


import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.List;

/**
 * 监听的状态
 */
@AllArgsConstructor
@Getter
public enum MonitorStatusEnum {
    /**
     * t_order_order表， order_status字段
     * 21:等待付款
     * 61:买家取消
     * 62:超时自动取消
     */
    ORDER_STATUS(Lists.newArrayList("21", "61", "62")),
    /**
     * t_order_return表
     * 41: Received
     * 51: Processing
     * 61: Complete
     */
    ORDER_RETURN_STATUS(Lists.newArrayList("41", "51", "61")),
    /**
     * 取消订单： 41
     * 退货：11
     * 已发货仅退款：51
     */
    ORDER_RETURN_MODE(Lists.newArrayList("41", "11", "51")),
    ;
    private final List<String> code;

}
