package com.gtech.promotion.feign.enums;


import lombok.AllArgsConstructor;
import lombok.Getter;


/**
 * t_order_return表 mode
 *
 * 取消订单： 41
 * 退货：11
 * 已发货仅退款：51
 */
@AllArgsConstructor
@Getter
public enum OmsOrderReturnModeEnum {
    /**
     * 取消订单： 41
     */
    ORDER_CANCEL("41"),
    /**
     * 退货：11
     */
    ORDER_RETURN("11"),
    /**
     * 已发货仅退款：51
     */
    ONLY_REFUND("51"),

    ;
    private final String code;

}
