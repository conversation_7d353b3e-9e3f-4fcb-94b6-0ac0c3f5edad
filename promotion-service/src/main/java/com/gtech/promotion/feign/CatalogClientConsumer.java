package com.gtech.promotion.feign;



import com.gtech.pim.client.CatalogClient;
import com.gtech.pim.request.CatalogQueryProductRequest;
import com.gtech.pim.request.PageBean;
import com.gtech.pim.response.CatalogProductBaseResponse;
import com.gtech.pim.response.PageResult;
import com.gtech.pim.response.Result;
import com.gtech.promotion.checker.purchaseconstraint.PurchaseConstraintChecker;
import com.gtech.promotion.exception.PromotionException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Component
public class CatalogClientConsumer {
    @Autowired
    private CatalogClient catalogClient;

    public PageResult<CatalogProductBaseResponse> searchProduct(CatalogQueryProductRequest request) {
        Result<PageResult<CatalogProductBaseResponse>> pageResultResult = catalogClient.searchProducts(request);
        if (pageResultResult == null || !Boolean.TRUE.equals(pageResultResult.isSuccess())) {
            throw new PromotionException(PurchaseConstraintChecker.PIM_ERROR);
        }
        return Optional.ofNullable(pageResultResult.getData()).orElse(new PageResult<>());
    }

    public List<CatalogProductBaseResponse> searchProductAll(CatalogQueryProductRequest request) {

        PageBean pageBean = new PageBean();
        pageBean.setPageNum(1);
        pageBean.setPageSize(500);
        request.setPageBean(pageBean);

        PageResult<CatalogProductBaseResponse> pageResult = this.searchProduct(request);
        List<CatalogProductBaseResponse> result = new ArrayList<>(pageResult.getList());
        if (pageResult.getTotal() > pageBean.getPageSize()) {
            long l = pageResult.getTotal() / pageBean.getPageSize();
            // 从第二页开始
            for (int i = 0; i < l; i++) {
                pageBean.setPageNum(pageBean.getPageNum() + 1);
                result.addAll(this.searchProduct(request).getList());
            }
        }
        return result;
    }
}
