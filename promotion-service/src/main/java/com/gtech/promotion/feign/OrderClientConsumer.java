package com.gtech.promotion.feign;

import com.gtech.ecomm.common.result.JsonResult;
import com.gtech.ecomm.order.vo.in.order.OrderCommonIn;
import com.gtech.ecomm.order.vo.out.order.OrderOut;
import com.gtech.promotion.checker.purchaseconstraint.PurchaseConstraintChecker;
import com.gtech.promotion.exception.PromotionException;
import com.gtech.promotion.feign.request.CustomOrderSkuStatisticsQuery;
import com.gtech.promotion.feign.request.QueryMemberPurchaseRequest;
import com.gtech.promotion.feign.response.CustomOrderSkuStatisticsQueryOut;
import com.gtech.promotion.feign.response.MemberPurchaseStatisticsResp;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Slf4j
@Component
public class OrderClientConsumer {
    @Autowired
    private OrderFeignClient orderFeignClient;


    public List<MemberPurchaseStatisticsResp> queryMemberPurchaseStatistics(QueryMemberPurchaseRequest request) {
        JsonResult<List<MemberPurchaseStatisticsResp>> jsonResult = orderFeignClient.queryMemberPurchaseStatistics(request);
        if (jsonResult == null || !Boolean.TRUE.equals(jsonResult.getSuccess())) {
            throw new PromotionException(PurchaseConstraintChecker.OMS_ERROR);
        }
        return Optional.ofNullable(jsonResult.getData())
                .orElse(new ArrayList<>());
    }

    public List<CustomOrderSkuStatisticsQueryOut> queryCustomOrderSkuStatistics(CustomOrderSkuStatisticsQuery customOrderSkuStatisticsQuery) {
        JsonResult<List<CustomOrderSkuStatisticsQueryOut>> jsonResult = orderFeignClient.queryCustomOrderSkuStatistics(customOrderSkuStatisticsQuery);
        if (jsonResult == null || !Boolean.TRUE.equals(jsonResult.getSuccess())) {
            throw new PromotionException(PurchaseConstraintChecker.OMS_ERROR);
        }
        return Optional.ofNullable(jsonResult.getData())
                .orElse(new ArrayList<>());
    }

}
