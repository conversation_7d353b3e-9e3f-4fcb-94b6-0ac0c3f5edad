package com.gtech.promotion.feign.response;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;


@Data
public class CustomOrderSkuStatisticsQueryOut {
    /**
     * 订单号
     */
    private String orderCode;
    /**
     * 订单状态
     */
    private String orderStatus;

    private List<OrderSkuStatisticsQueryItem> statisticsItems;


    @Data
    public static class OrderSkuStatisticsQueryItem {
        private String orderItemCode;

        private String productCode;

        private String skuCode;
        /**
         * 总购买数量
         */
        private int count;
        /**
         * 退货完成数量
         */
        private int returnCount;
        /**
         * 取消的数量
         */
        private int cancelCount;

        /**
         * 总实付金额（折后）
         */
        private BigDecimal amount;
        /**
         * 退货完成金额
         */
        private BigDecimal returnAmount;
        /**
         * 取消的金额
         */
        private BigDecimal cancelAmount;

    }
}
