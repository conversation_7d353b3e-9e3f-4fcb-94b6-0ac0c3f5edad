package com.gtech.promotion.feign;

import com.gtech.member.client.MemberClient;
import com.gtech.member.response.Result;
import com.gtech.member.web.vo.param.QueryTagListParam;
import com.gtech.member.web.vo.result.TagListResult;
import com.gtech.promotion.checker.purchaseconstraint.PurchaseConstraintChecker;
import com.gtech.promotion.exception.PromotionException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Slf4j
@Component
public class MemberClientConsumer {
    @Autowired
    private MemberClient memberClient;


    public List<TagListResult> queryListByMemberCode(QueryTagListParam param) {
        Result<List<TagListResult>> result = memberClient.queryListByMemberCode(param);
        if (null == result || !Boolean.TRUE.equals(result.isSuccess())) {
            throw new PromotionException(PurchaseConstraintChecker.MEMBER_ERROR);
        }
        return Optional.ofNullable(result.getData()).orElse(new ArrayList<>());
    }


}
