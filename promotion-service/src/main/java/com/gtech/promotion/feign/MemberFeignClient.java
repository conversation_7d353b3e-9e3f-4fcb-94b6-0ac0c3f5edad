package com.gtech.promotion.feign;

import com.gtech.commons.result.PageResult;
import com.gtech.commons.result.Result;
import com.gtech.member.web.vo.param.GetMemberProfileParam;
import com.gtech.member.web.vo.param.QueryMemberCodesParam;
import com.gtech.member.web.vo.param.QueryMemberListByConditionParam;
import com.gtech.member.web.vo.param.QueryMemberParam;
import com.gtech.member.web.vo.param.TagQueryMemberParam;
import com.gtech.member.web.vo.result.GetMemberProfileResult;
import com.gtech.member.web.vo.result.QueryMemberCodesResult;
import com.gtech.member.web.vo.result.QueryMemberListByConditionResult;
import com.gtech.member.web.vo.result.TagMemberListResult;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

@FeignClient(value = "MEMBER", url = "${titan.gateway.url:}")
public interface MemberFeignClient {

    @PostMapping(value = "/member/tag/member/list", consumes = MediaType.APPLICATION_JSON_VALUE)
    PageResult<TagMemberListResult> queryMemberCodesByTagCodes(@RequestBody TagQueryMemberParam param);

    @PostMapping(value = "/member/member/queryMemberCodes", consumes = MediaType.APPLICATION_JSON_VALUE)
    PageResult<QueryMemberCodesResult> queryMemberCodesByMobiles(@RequestBody QueryMemberCodesParam param);

    @PostMapping(value = "/member/member/getMemberInfo", consumes = MediaType.APPLICATION_JSON_VALUE)
    Result<GetMemberProfileResult> getMemberInfo(@RequestBody GetMemberProfileParam param);
    @PostMapping(value = "/member/member/getMemberProfile", consumes = MediaType.APPLICATION_JSON_VALUE)
    Result<GetMemberProfileResult> getMemberProfile(@RequestBody GetMemberProfileParam param);


    @PostMapping(value = "/member/member/queryMemberListByCondition", consumes = MediaType.APPLICATION_JSON_VALUE)
    PageResult<QueryMemberListByConditionResult> queryMemberListByCondition(@RequestBody QueryMemberListByConditionParam param);

    @PostMapping(value = "/member/member/queryMemberListByMemberCode", consumes = MediaType.APPLICATION_JSON_VALUE)
    Result<List<QueryMemberListByConditionResult>> queryMemberListByMemberCode(@RequestBody QueryMemberParam param);

}
