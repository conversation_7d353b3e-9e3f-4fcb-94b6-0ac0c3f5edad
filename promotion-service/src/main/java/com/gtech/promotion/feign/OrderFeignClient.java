package com.gtech.promotion.feign;

import com.gtech.ecomm.common.page.ResponsePage;
import com.gtech.ecomm.common.result.JsonResult;
import com.gtech.ecomm.order.vo.in.order.OrderCommonIn;
import com.gtech.ecomm.order.vo.in.order.OrderQueryIn;
import com.gtech.ecomm.order.vo.out.order.OrderOut;
import com.gtech.ecomm.order.vo.out.order.OrderQueryOut;
import com.gtech.ecomm.order.vo.out.orderreturn.OrderReturnOut;
import com.gtech.promotion.feign.request.CustomOrderSkuStatisticsQuery;
import com.gtech.promotion.feign.request.OrderReturnCommonIn;
import com.gtech.promotion.feign.request.QueryMemberPurchaseRequest;
import com.gtech.promotion.feign.response.CustomOrderSkuStatisticsQueryOut;
import com.gtech.promotion.feign.response.MemberPurchaseStatisticsResp;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

@FeignClient(value = "ORDER", url = "${titan.gateway.url:}")
public interface OrderFeignClient {

    @PostMapping(value = "/order/statistic/queryMemberPurchaseStatistics", consumes = MediaType.APPLICATION_JSON_VALUE)
    JsonResult<List<MemberPurchaseStatisticsResp>> queryMemberPurchaseStatistics(@RequestBody QueryMemberPurchaseRequest request);

    @PostMapping(value = "/order/custom/queryCustomOrderSkuStatistics", consumes = MediaType.APPLICATION_JSON_VALUE)
    JsonResult<List<CustomOrderSkuStatisticsQueryOut>> queryCustomOrderSkuStatistics(@RequestBody CustomOrderSkuStatisticsQuery customOrderSkuStatisticsQuery);

    @PostMapping("/order/get")
    JsonResult<OrderReturnOut> get(@RequestBody OrderReturnCommonIn orderReturnCommonIn);

    @PostMapping("/order/queryOrderDetail")
    JsonResult<OrderOut> queryOrderDetail(@RequestBody OrderCommonIn orderCommonIn);


    @PostMapping("/order/queryOrder")
    JsonResult<ResponsePage<OrderQueryOut>> queryOrderList(@RequestBody OrderQueryIn orderQueryIn);


}
