/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * <PERSON>ECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.dto.in.activity;

import com.gtech.promotion.api.ApiConstants;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.List;

/**
 * 提交锁定订单dto
 *
 */
@Data
@EqualsAndHashCode(callSuper=true)
@ToString(callSuper = true)
@ApiModel("OrderCommit")
public class OrderCommitDTO extends ShoppingCartDTO {

    private static final long serialVersionUID = -8774266367544684215L;

    @ApiModelProperty(value="Total discount amount (including promoRewardPostage)",required=true)
    private BigDecimal promoDeductedAmount;//

    @ApiModelProperty(value="赠品列表")
    private List<OrderGiveawayDTO> promoGiveaways;//

    @ApiModelProperty(value="订单号，提交订单时使用",required=true)
    private String orderId;//
    
    @ApiModelProperty(value=ApiConstants.FREE_POSTAGE,required=true)
    private Integer freePostage;//

    private BigDecimal promoRewardPostage;

}
