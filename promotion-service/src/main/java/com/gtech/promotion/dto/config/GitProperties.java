/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WA<PERSON><PERSON>NTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.dto.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.PropertySource;

import lombok.Data;

/**
 * git-commit-id-plugin configuration
 *
 * <AUTHOR>
 * @Date 2019年9月23日下午5:24:58
 */
@Data
@Configuration
@PropertySource(value="classpath:git.properties", ignoreResourceNotFound=true)
@ConfigurationProperties(prefix = "git")
public class GitProperties {

    private String branch;

    @Value("${git.build.time:None}")
    private String buildTime;

    @Value("${git.build.version:None}")
    private String buildVersion;

    @Value("${git.closest.tag.name:None}")
    private String tagName;

    @Value("${git.commit.id.full:None}")
    private String commitIdFull;

    @Value("${git.commit.id.abbrev:None}")
    private String commitIdAbbrev;

    @Value("${git.commit.message.full:None}")
    private String commitMessage;

    @Value("${git.commit.time:None}")
    private String commitTime;

    @Value("${git.commit.user.name:None}")
    private String commitUserName;

    @Value("${git.commit.user.email:None}")
    private String commitUserEmail;

}
