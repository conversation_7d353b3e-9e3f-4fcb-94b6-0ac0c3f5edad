package com.gtech.promotion.dto.in.coupon;

import com.gtech.commons.page.PageParam;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2021/3/29 13:31
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ExportCouponUserDto extends PageParam implements Serializable {
    private static final long serialVersionUID = 1925235709486312968L;


    private String id;

    private String domainCode;

    private String tenantCode;

    private String activityCode;

    private String maxMemberCode;

}
