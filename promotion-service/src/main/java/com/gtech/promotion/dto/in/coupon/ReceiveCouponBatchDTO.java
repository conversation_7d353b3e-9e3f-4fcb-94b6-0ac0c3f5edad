/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.dto.in.coupon;

import java.io.Serializable;
import java.util.List;

import com.gtech.promotion.vo.bean.Qualification;

import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 批量领取优惠券接口的入参
 */
@Getter
@Setter
@ToString
// com.gtech.promotion.vo.param.coupon.SendCouponToUserListParam
public class ReceiveCouponBatchDTO implements Serializable {

    private static final long serialVersionUID = 8269856175579483732L;

    // Tenant code
    private String tenantCode;

    private String domainCode;
    
    // Coupon activity code
    private String activityCode;

    // Coupon activity release code.
    private String releaseCode;

    // Take coupon label: 01-免费领取 02-活动赠券 03-促销赠券 04-积分换购 05-有价券 06-新人券 07-生日券 08-补偿券 09-推送券 10-匿名券 11-其他
    private String takeLabel;
    
    // Receive count for per member.
    private int receiveCount;
    
    // Member user information list.
    private List<UserAndLevel> userList;

    private String userCodes;

    private String userMobiles;

    private String userAccounts;

	private String userTags;

    private String isAllUser = "0";

    private String operatorCode;

    private String errorMessage;

    @Data
    public static class UserAndLevel implements Serializable{

        private static final long serialVersionUID = 4622564783386355449L;

        // Member user code.
        private String userCode;

        // Member level code.
        private List<Qualification> qualifications;
        
    }
}
