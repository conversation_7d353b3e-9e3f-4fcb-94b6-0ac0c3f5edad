/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.dto.out.coupon;

import com.gtech.promotion.api.ApiConstants;
import com.gtech.promotion.vo.bean.ActivityPeriod;
import com.gtech.promotion.vo.bean.Giveaway;
import com.gtech.promotion.vo.bean.Qualification;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 查询券活动详情-对外出参
 */
@Data
@ApiModel("查询券活动详情-对外出参")
// com.gtech.promotion.vo.result.coupon.FindCouponDetailResult
public class CouponInfoOutDTO implements Serializable{

    private static final long serialVersionUID = -5941628936219812796L;

    private String activityCode;

    private String activityName;

    @ApiModelProperty("活动描述")
    private String activityDesc;

    @ApiModelProperty("活动备注")
    private String activityRemark;
    
    @ApiModelProperty("活动标签")
    private String activityLabel;

    @ApiModelProperty(ApiConstants.COUPON_TYPE)
    private String couponType;

    @ApiModelProperty("优惠券面值")
    private BigDecimal faceValue;

    @ApiModelProperty(ApiConstants.FACE_UNIT)
    private String faceUnit;
    
    //条件值单位 01：金额  02：件
    @ApiModelProperty(ApiConstants.CONDITION_UNIT)
    private String conditionUnit;
    
    @ApiModelProperty("条件值")
    private BigDecimal conditionValue;

    @ApiModelProperty("活动页url")
    private String activityUrl;

    @ApiModelProperty("每人限领、限用")
    private Integer userLimitMax;

    //奖励类型：减金额(01)、打折扣(02)、单件固定金额(03)、组合固定金额(04)、包邮(05)、送赠品(06)、买A送A(07)、买A送B(08)
    @ApiModelProperty(value = ApiConstants.REWARD_TYPE)
    private String rewardType;

    @ApiModelProperty("最早可领取开始时间")
    private String startReceiveTime;

    @ApiModelProperty("最晚可领取结束时间")
    private String endReceiveTime;

    @ApiModelProperty("领取状态：1：可领取；2：未开始；3：已结束；4：已领完；5：已被限领；6：会员等级不符合要求；7：活动已关闭")
    private int receiveStatus;

    @ApiModelProperty(ApiConstants.PRODUCT_SELECTION_TYPE)
    private String productSelectionType;

    @ApiModelProperty("赠品列表")
    private List<Giveaway> giveaways;

    @ApiModelProperty("Qualification list")
    private List<Qualification> qualifications;
    
    @ApiModelProperty("券活动的券可用时间")
    private List<CouponReleaseTimeOutDTO> couponValidTime;

    @ApiModelProperty(value = "Activity warm-up begin time.")
    private String warmBegin;

    @ApiModelProperty(value = "Activity warm-up end time.")
    private String warmEnd;

    @ApiModelProperty(value = "Activity cool down time.")
    private String coolDown;

    @ApiModelProperty(value = "Activity background image.")
    private String backgroundImage;

    @ApiModelProperty(value = "Activity period.")
    private ActivityPeriod activityPeriod;
}
