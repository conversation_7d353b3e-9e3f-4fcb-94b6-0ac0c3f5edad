/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.dto.in.activity;

import com.gtech.promotion.vo.bean.ProductCodes;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;
import java.util.Map;

/**
 * 根据商品查询促销列表(入参)
 * 商户编码和商品信息
 * 
 */
@Getter
@Setter
@ToString
// com.gtech.promotion.vo.param.coupon.QueryCouponActivityListByProductParam
public class TenantProductDTO extends ProductCodes {

    private static final long serialVersionUID = -1047022621585656143L;

    // Tenant code.
    private String tenantCode;
    private String domainCode;

    // Organization code list. 
    private List<String> orgCodes;

    // Qualification list.
    private Map<String, List<String>> qualifications;

    // Channel code.
    private String channelCode;

    // Language id.
    private String language;
}
