package com.gtech.promotion.dto.in.coupon;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ReserveCouponDto implements Serializable {

    private static final long serialVersionUID = -1829072055835255761L;
    private String tenantCode;

    // Activity code
    private String activityCode;

    // 状态 1:锁定库存 2:解锁库存 3：已发券
    private Integer status;

    private Integer quota;

    private String orderNo;

    private String memberCode;
}
