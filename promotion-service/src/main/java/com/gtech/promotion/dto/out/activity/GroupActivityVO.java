package com.gtech.promotion.dto.out.activity;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;

/**
 * @Description :
 * <AUTHOR> gaojie.li
 * @Date : 2023/7/16 10:09
 */
@Getter
@Setter
@ToString
public class GroupActivityVO implements Serializable {



    private String tenantCode;

    private String domainCode;

    private String groupCode;

    private String activityCode;

    private String orgCode;

    private String activityOrgCode;


    private String activityName;

    private String groupName;

    private String activityBegin;

    // Activity end time. (yyyyMMddHHmmss)
    private String activityEnd;

    private String activityStatus;

    private Integer priority;

}
