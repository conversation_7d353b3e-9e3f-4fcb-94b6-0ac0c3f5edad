package com.gtech.promotion.dto.out.coupon;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.Date;

/**
 * MemberTag所有生效的优惠券活动列表出参
 */
@Data
@ToString
@ApiModel("MemberTagCouponActivityOutDTO")
public class MemberTagCouponActivityOutDTO implements Serializable{

    private static final long serialVersionUID = -4469636996025014315L;

    // Coupon type: 01-优惠券 02-匿名券 03-优惠码
    @ApiModelProperty("Coupon type: 01-discount coupon 02-anonymity coupon 03-discount code")
    private String couponType;

    // Activity total quantity: 0 means unlimited, fixed 1 while couponType='03'
    @ApiModelProperty("Activity total quantity: 0 means unlimited, fixed 1 while couponType='03'")
    private String totalQuantity;

    // Already released quantity.
    @ApiModelProperty("Already released quantity.")
    private String releaseQuantity;

    // Single user limited max. 0 means unlimited
    @ApiModelProperty("Single user limited max. 0 means unlimited")
    private int userLimitMax;

    // Activity code.
    @ApiModelProperty("Activity code.")
    private String activityCode;

    // Activity name.
    @ApiModelProperty("Activity name.")
    private String activityName;

    // Activity description.
    @ApiModelProperty("Activity description.")
    private String activityDesc;

    // Activity remark.
    @ApiModelProperty("Activity remark.")
    private String activityRemark;

    // Activity label.
    @ApiModelProperty("Activity label.")
    private String activityLabel;

    // Product selection type: 01-Selection, 02-Invert Selection
    @ApiModelProperty("Product selection type: 01-Selection, 02-Invert Selection")
    private String productSelectionType;

    // Activity start time. (yyyyMMddHHmmss)
    @ApiModelProperty("Activity start time. (yyyyMMddHHmmss)")
    private String activityBegin;

    // Activity end time. (yyyyMMddHHmmss)
    @ApiModelProperty("Activity end time. (yyyyMMddHHmmss)")
    private String activityEnd;

    // Activity status.
    @ApiModelProperty("Activity status.")
    private String activityStatus;

    //Activity create user.
    @ApiModelProperty("Activity create user.")
    private String createUser;

    // Activity create time.
    @ApiModelProperty("Activity create time.")
    private Date createTime;

}
