/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.dto.in.coupon;

import java.io.Serializable;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**   
 * 
 */
@Getter
@Setter
@ToString
// 查询会员某一张优惠券券码信息入参
// com.gtech.promotion.vo.param.coupon.FindUserCouponParam
public class TCouponQueryOrApplyDTO implements Serializable{
	
    private static final long serialVersionUID = -2871907340762002142L;

    @ApiModelProperty(name = "tenantCode",value = "租户编号", required = true)
	private String tenantCode;

    @ApiModelProperty(value = "Language id")
    private String language;

    @ApiModelProperty(name = "couponCode",value = "优惠券编码", required = true)
	private String couponCode;
	
    @ApiModelProperty(name = "userCode",value = "会员编号", required = true)
	private String userCode;

    // Store organization code.
    @ApiModelProperty(value = "Store organization code.", required = false)
    private String orgCode;
    
    @ApiModelProperty(value="渠道编码",required=false)
    private String channelCode;

}
