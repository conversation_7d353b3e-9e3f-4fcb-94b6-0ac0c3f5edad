/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.dto.in.activity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;

/**   
 * 导出促销SKU数据入参
 */
@Data
@ToString
@ApiModel("ExportSkuExcelIn")
public class ExportSkuExcelInDto implements Serializable{

    private static final long serialVersionUID = 2394790196532065875L;

    @ApiModelProperty(value = "租户编码",required = true)
    private String tenantCode;

    @ApiModelProperty(value = "Language id")
    private String language;

    @ApiModelProperty(value = "Activity code.",required = true)
    private String activityCode;

    @ApiModelProperty(value = "商品池序号",required = true)
    private Integer seqNum;
    
    @ApiModelProperty(value = "是否带商品价格标识：0-不带；1-带",required = true)
    private String withPrice;
    
}
  
