package com.gtech.promotion.dto.in.purchaseconstraint;

import com.gtech.promotion.vo.bean.ProductAttribute;
import lombok.Data;
import lombok.ToString;
import org.apache.commons.collections4.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

@Data
@ToString
public class CalAvailableQtyProductInDto {
    private List<String> categories;// 此list为categoryPath 格式A1>B1>C 如果以后需要时 新增List<String> categoryCodes
    private List<String> categoryCodes;
    private List<String> categoryNames;
    private String brandCode;
    private String brandName;
    private List<ProductAttribute> attributes; // sku属性
    private String productCode;
    private String productName;
    private String skuCode;
    private String skuName;
    private String mainProductNo;
    private List<ProductAttribute> spuAttributes; // spu属性，用于核算促销价时，处理通过spu属性标记的活动
    private String productTag;

    public List<ProductAttribute> getAttributes() {
        List<ProductAttribute> list = new ArrayList<>();
        if (!CollectionUtils.isEmpty(this.attributes)) {
            list.addAll(this.attributes);
        }
        if (!CollectionUtils.isEmpty(this.spuAttributes)) {
            list.addAll(this.spuAttributes);
        }
        return list;
    }
}
