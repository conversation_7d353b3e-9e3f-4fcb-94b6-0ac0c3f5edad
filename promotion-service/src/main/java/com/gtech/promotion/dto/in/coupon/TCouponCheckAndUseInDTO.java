/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.dto.in.coupon;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**   
 * 核销券码
 */
@ApiModel("TCouponCheckAndUseIn")
@Data
public class TCouponCheckAndUseInDTO implements Serializable{

    private static final long serialVersionUID = -3083267446103472224L;
    
    @ApiModelProperty(name = "tenantCode",value = "系统租户编码", required = true)
    private String tenantCode;
    
    @ApiModelProperty(name = "userCode",value = "会员编码", required = true)
    private String userCode;
    
    @ApiModelProperty(name = "couponCodes",value = "券码编号，多个以逗号隔开", required = true)
    private String couponCodes;
    
    @ApiModelProperty(name = "usedRefId",value = "订单编号", required = true)
    private String usedRefId;
    
    @ApiModelProperty(hidden = true)
    private String couponCode;
    
    

}
  
