/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * <PERSON>ECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.dto.out.coupon;

import com.gtech.promotion.api.ApiConstants;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;

/**   
 */
@Data
@ToString
@ApiModel("CouponInfoQueryOut")
public class CouponInfoQueryOutDTO implements Serializable{

    /**
     * serialVersionUID:序列化
     */
    private static final long serialVersionUID = -1141601317217722678L;

    @ApiModelProperty("优惠券编码")
    private String couponCode;
    
    @ApiModelProperty("会员编码")
    private String userCode;
    
    @ApiModelProperty("券码领取时间")
    private String receivedTime;
    
    @ApiModelProperty("券码使用时间")
    private String usedTime;
    
    @ApiModelProperty("券码可用结束时间")
    private String validEndTime;
    
    @ApiModelProperty("订单编号")
    private String usedRefId;
    
    @ApiModelProperty(ApiConstants.FROZEN_STATUS)
    private String frozenStatus;
    
    @ApiModelProperty(ApiConstants.COUPON_STATUS)
    private String couponStatus;

    @ApiModelProperty("创建时间，字符串格式")
    private String createTime;

    @ApiModelProperty(ApiConstants.TAKE_LABEL)
    private String takeLabel;
    
    
    
}
