package com.gtech.promotion.dto.in.coupon;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class ExportCouponInDTO implements Serializable {

    private static final long serialVersionUID = -8676441150311568802L;

    private String domainCode;

    private String tenantCode;

    private String activityCode;

    private String maxId;

    private int size;
}
