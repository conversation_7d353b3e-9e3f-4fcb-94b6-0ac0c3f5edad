/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.dto.out.product;

import com.gtech.commons.exception.Exceptions;
import com.gtech.promotion.exception.ErrorCodes;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;

/**
 * 促销商品明细
 */
@Data
@ToString
// com.gtech.promotion.vo.bean.ProductDetail
public class ProductDetailInDTO implements Serializable{

    private static final long serialVersionUID = -4947230289917036853L;
    
    @ApiModelProperty(value = "商品池序号",example="1", required = true)
    private Integer seqNum;
    
    @ApiModelProperty(value = "商品编码")
    private String productCode;

    @ApiModelProperty(value = "商品名称")
    private String spuName;

    @ApiModelProperty(value = "sku编码")
    private String skuCode;

    @ApiModelProperty(value = "sku名称")
    private String skuName;

    @ApiModelProperty(value = "org编码")
    private String orgCode;

    @ApiModelProperty(value = "org名称")
    private String orgName;

    @ApiModelProperty(value = "促销价格")
    private String promoPrice;

    @ApiModelProperty(value = "Product number")
    private String productNumber;

    @ApiModelProperty(value = "Product url")
    private String productUrl;

    @ApiModelProperty(value = "Sku number")
    private String skuNumber;

    @ApiModelProperty(value = "Sku url")
    private String skuUrl;
    @ApiModelProperty(value = "Price type. 1 -special price ; 2 -discount ; default 1")
    private String priceType;

    @ApiModelProperty(value = "Sku url")
    private String priceDiscount;

    /**
     * 是否是全商品 
     */
    public boolean isEmptyProductDetails(){

        return (StringUtils.isEmpty(this.skuCode) && StringUtils.isEmpty(this.productCode));
    }

    public void assertNotEmpty(){

        if (StringUtils.isEmpty(this.skuCode) && StringUtils.isEmpty(this.productCode)) {
            throw Exceptions.fail(ErrorCodes.PARAM_LEAST_ONE, "skuCode or productCode");
        }
    }
}
