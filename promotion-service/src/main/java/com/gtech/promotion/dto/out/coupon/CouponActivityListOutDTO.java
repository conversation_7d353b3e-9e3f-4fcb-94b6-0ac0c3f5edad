/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such
 * Confidential Information and shall use it only in accordance with the terms of the license agreement
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WA<PERSON>RANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.dto.out.coupon;

import com.gtech.promotion.dto.out.activity.TPromoActivityListOutDTO;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * CouponActivityListOutDTO
 */

@Getter
@Setter
@ToString
public class CouponActivityListOutDTO extends TPromoActivityListOutDTO{

    private static final long serialVersionUID = -4469636996025011815L;


    // Activity total quantity: 0 means unlimited, fixed 1 while couponType='03'
    private String totalQuantity;

    // Already released quantity.
    private String releaseQuantity;

    // Single user limited max. 0 means unlimited
    private Integer userLimitMax;

    private Integer userLimitMaxDay;

    private String groupCode;

    private String couponCode;

    private String userCode;

    // 可用开始时间(yyyyMMddhhmmss)
    private String validStartTime;

    // 可用结束时间(yyyyMMddhhmmss)
    private String validEndTime;

    // 券类型：01-优惠券 02-匿名券 03-优惠码
    private String couponType;

    // 状态：01-未发放 02-已发放 03-已使用 04-已锁定
    private String status;

    // 冻结状态：01-未冻结 02-已冻结
    private String frozenStatus;


    // 领券时间
    private String receivedTime;

    // 使用时业务编号
    private String usedRefId;

    // 使用时间(yyyyMMddHHmmss)
    private String usedTime;

    // 使用时间(yyyyMMddHHmmss)
    private String releaseTime;

}
