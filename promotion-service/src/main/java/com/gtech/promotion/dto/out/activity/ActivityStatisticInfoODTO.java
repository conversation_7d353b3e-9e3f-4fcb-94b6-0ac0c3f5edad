/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.dto.out.activity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * ActivityStatisticInfoODTO
 **/
@Data
@ApiModel(value = "活动每日数据统计记录")
public class ActivityStatisticInfoODTO implements Serializable {

    // serialVersionUID
    private static final long serialVersionUID = -2277772140418000914L;

    @ApiModelProperty(value = "租铺编号")
    private String tenantCode;

    @ApiModelProperty(value = "活动编号")
    private String activityCode;

    @ApiModelProperty(value = "订单参与数")
    private Integer orderCount;

    @ApiModelProperty(value = "会员参与数")
    private Integer memberCount;

    @ApiModelProperty(value = "(券活动)领取券的数量")
    private Integer allocateCouponCount;

    @ApiModelProperty(value = "(券活动)使用券的数量")
    private Integer useCouponCount;

    @ApiModelProperty(value = "活动减免总金额")
    private BigDecimal reduceAmount;

    @ApiModelProperty(value = "订单优惠后总金额")
    private BigDecimal orderAmount;

    @ApiModelProperty(value = "数据统计日期")
    private String statisticDate;
}
