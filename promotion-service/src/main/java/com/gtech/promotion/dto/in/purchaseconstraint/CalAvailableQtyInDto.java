package com.gtech.promotion.dto.in.purchaseconstraint;

import com.gtech.promotion.vo.bean.ProductAttribute;
import lombok.Data;
import lombok.ToString;

import java.util.List;
import java.util.Map;

@Data
@ToString
public class CalAvailableQtyInDto {
    private String tenantCode;

    private String domainCode;

    private String orgCode;

    private String memberCode;

    private Map<String, List<String>> qualifications;

    private Integer touristFlag;

    private CalAvailableQtyProductInDto calAvailableQtyProduct;
    private List<CalAvailableQtyProductInDto> calAvailableQtyProductList;
    private Map<String, CalAvailableQtyProductInDto> skuProductMap;

    private Boolean checkTime;

    private Boolean ignoreProductScopeFilter;

    private Map<String, List<ProductAttribute>> skuAttributeMap;
    private Map<String, String> customMap;
}
