/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.dto.in.activity;

import java.io.Serializable;

import com.gtech.commons.utils.StringUtil;
import com.gtech.promotion.checker.activity.ActivityStatisticChecker;
import com.gtech.promotion.exception.Check;
import com.gtech.promotion.utils.DateValidUtil;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

/**
 * 开始结束时间入参对象
 */
@Data
@ToString
@ApiModel("StartAndEndTimeIn")
public class StartAndEndTimeInDTO implements Serializable{

    private static final long serialVersionUID = -2019875977730638045L;

    @ApiModelProperty(name = "startTime",value = "开始时间,例如开始时间20190305000000",required = false)
    private String startTime;

    @ApiModelProperty(name = "endTime",value = "结束时间，结束时间20190305235959",required = false)
    private String endTime;
    
    @ApiModelProperty(value = "排除掉的租户编码，用逗号隔开",required = false)
    private String tenantCodes;

    public void checkTime(){
        if (StringUtil.isNotBlank(startTime)){
            Check.check(!DateValidUtil.isValidDate(startTime), ActivityStatisticChecker.NO_TIME_FORTMAT);
        }
        if (StringUtil.isNotBlank(endTime)){
            Check.check(!DateValidUtil.isValidDate(endTime), ActivityStatisticChecker.NO_TIME_FORTMAT);
        }
        if (StringUtil.isNotBlank(startTime) && StringUtil.isNotBlank(endTime)){
            Check.check(startTime.compareTo(endTime) > 0, ActivityStatisticChecker.START_TIME_LOWER_END_TIME);
        }
    }

}
