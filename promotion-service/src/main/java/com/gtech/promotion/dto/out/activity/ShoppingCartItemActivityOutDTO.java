/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.dto.out.activity;

import com.gtech.promotion.api.ApiConstants;
import com.gtech.promotion.vo.bean.Giveaway;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 购物车商品参加的活动
 *
 */
@Data
@ToString
@ApiModel("ShoppingCartItemActivityOut")
public class ShoppingCartItemActivityOutDTO implements Serializable{

    private static final long serialVersionUID = -2753703550400480955L;
    
    @ApiModelProperty(value = "活动编码")
    private String activityCode;

    @ApiModelProperty(value = "商品活动上指定价格")
    private BigDecimal promoPrice;

    @ApiModelProperty(value = "金额类型条件")
    private String priceCondition;

    @ApiModelProperty(value = "是否满足活动")
    private boolean effectiveFlag;

    @ApiModelProperty(value = "券码")
    private String couponCode;

    @ApiModelProperty(value = ApiConstants.PROMO_SCOPE)
    private String promoScope;//

    @ApiModelProperty(value = "活动名称")
    private String activityName;

    @ApiModelProperty(value = "促销分类")
    private String promotionCategory;

    // 开始时间：yyyyMMddhhmmss
    @ApiModelProperty(value = "活动开始时间")
    private String activityBegin;

    // 结束时间：yyyyMMddhhmmss
    @ApiModelProperty(value = "活动结束时间")
    private String activityEnd;

    // 活动描述
    @ApiModelProperty(value = "活动描述")
    private String activityDesc;

    // 活动备注
    @ApiModelProperty(value = "活动备注")
    private String activityRemark;

    @ApiModelProperty(value = "活动标签")
    private String activityLabel;

    @ApiModelProperty(value = ApiConstants.ACTIVITY_TYPE)
    private String activityType;

    @ApiModelProperty(value = "参加活动计算前的商品项总价")
    private BigDecimal beforeAmount;//

    @ApiModelProperty(value = "参加活动计算后的商品项总价")
    private BigDecimal afterAmount;//
    
    @ApiModelProperty(value = "赠品赠送最大限制数量")
    private String giveawayLimitMax;//

    @ApiModelProperty(value = "赠品列表")
    private List<Giveaway> giveaways;//

    @ApiModelProperty(value = "单人限购次数", hidden = true)
    private transient String userLimitation;//

    @ApiModelProperty(value = "活动类型")
    private String opsType;

}
