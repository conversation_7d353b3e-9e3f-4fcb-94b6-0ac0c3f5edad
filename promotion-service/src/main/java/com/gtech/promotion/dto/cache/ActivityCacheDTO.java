/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.dto.cache;

import com.alibaba.fastjson.annotation.JSONField;
import com.gtech.promotion.dao.model.activity.ActivityFunctionParamRankModel;
import com.gtech.promotion.dao.model.activity.ActivityLanguageModel;
import com.gtech.promotion.dao.model.activity.ActivityModel;
import com.gtech.promotion.dao.model.activity.ActivityPeriodModel;
import com.gtech.promotion.dao.model.activity.FunctionParamModel;
import com.gtech.promotion.dao.model.activity.QualificationModel;
import com.gtech.promotion.dao.model.activity.TPromoActivityProductVO;
import com.gtech.promotion.dao.model.activity.TPromoActivityStoreVO;
import com.gtech.promotion.dao.model.activity.TPromoIncentiveLimitedVO;
import com.gtech.promotion.dao.model.activity.TemplateFunctionModel;
import com.gtech.promotion.dao.model.activity.TemplateModel;
import com.gtech.promotion.dao.model.marketing.BoostSharingModel;
import com.gtech.promotion.dao.model.marketing.MarketingGroupMode;
import com.gtech.promotion.vo.bean.Giveaway;
import com.gtech.promotion.vo.result.activity.ActivityGroupCache;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;
import java.util.Map;

/**
 * 缓存数据
 * 
 */
@Getter
@Setter
@ToString
public class ActivityCacheDTO extends BaseCacheDTO implements CustomConditionAware {

    public static final String LAST_VERSION = "20200310";

    private MarketingGroupMode marketingGroupMode;
    private BoostSharingModel boostSharingModel;

    // Cache version
    private String version;

    @JSONField(serialize = false,deserialize = false)
    public boolean isLastVersion() {

        return LAST_VERSION.equals(version);
    }

	private List<ActivityGroupCache> groupCacheList;

    private String seqNum;

    // 活动基本信息
    private ActivityModel activityModel;

    //模板信息
    private TemplateModel promoTemplate;

    private ActivityPeriodModel periodModel;

    // 资格信息
    private List<QualificationModel> qualificationModels;

    //函数层级列表
    private List<ActivityFunctionParamRankModel> promoFuncRanks;

    //函数参数列表
    private List<FunctionParamModel> promoFuncParams;

    //函数列表
    private List<TemplateFunctionModel> promoTemplateFunctions;

    //渠道对象列表
    private List<TPromoActivityStoreVO> promoChannels;

    // 赠品对象列表
    private List<Giveaway> giveaways;

    //对应的商品对象列表
    private List<TPromoActivityProductVO> promoProducts;

    // map key：productCode value:ProductSkuDetailDTO
    private Map<String, List<ProductSkuDetailDTO>> promoProductDetails;

    //套装商品skuCode列表
    private List<ProductSkuDetailDTO> promoProductCombines;

    //奖励限制列表
    private List<TPromoIncentiveLimitedVO> incentiveLimiteds;

    // 多语言列表: <language, ActivityLanguageVO>
    private Map<String, ActivityLanguageModel> languageMap;

    @Override
    public String getCustomCondition() {
        return activityModel.getCustomCondition();
    }

    @Override
    public String getCustomRule() {
        return null;
    }
}
