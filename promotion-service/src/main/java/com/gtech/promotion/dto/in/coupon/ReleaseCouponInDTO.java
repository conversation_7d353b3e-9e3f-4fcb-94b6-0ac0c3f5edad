/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.dto.in.coupon;

import com.gtech.promotion.api.ApiConstants;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;


/**
 * 投放券码入参
 */
@Data
@ToString
@ApiModel("ReleaseCouponIn")
// com.gtech.promotion.vo.param.coupon.ReleaseCouponParam
public class ReleaseCouponInDTO implements Serializable {

    private static final long serialVersionUID = 6689695900547578339L;

    @ApiModelProperty(value = "租户编号",required = true)
    private String tenantCode;

    @ApiModelProperty(value = " 活动编码",required = true)
    private String activityCode;

    @ApiModelProperty(name = "status，参数不传 默认为 02 ",value = "券状态 01 未领取， 02 领取 03 已使用 04 已锁定 05 过期")
    private String status;

    @ApiModelProperty(name = "Check flag time",value = "true 校验时间，false 不校验")
    private boolean checkFlagTime= true;

    @ApiModelProperty(hidden = true)
    private String couponType;

    @ApiModelProperty(hidden = true)
    private String releaseSource;

    @ApiModelProperty(value = "领取开始时间：yyyyMMddhhmmss",required = true)
    private String receiveStart;

    @ApiModelProperty(value = "领取结束时间：yyyyMMddhhmmss",required = true)
    private String receiveEnd;

    @ApiModelProperty(value = "有效期天数（从领取之日开始计算）",required = false)
    private Integer validDays;

    @ApiModelProperty(value = "可用开始时间：yyyyMMddhhmmss",required = false)
    private String validStart;

    @ApiModelProperty(value = " 可用结束时间：yyyyMMddhhmmss",required = false)
    private String validEnd;

    @ApiModelProperty(value = "投放时间：yyyyMMddhhmmss,当是预约投放类型的时候，必填",required = false)
    private String releaseTime;

    @ApiModelProperty(value = ApiConstants.RELEASE_TYPE,required = true)
    private String releaseType;

    @ApiModelProperty(value = "券投放数量：优惠券类型为优惠码时，固定促销优惠码，投放数量固定为1",required = true)
    private Integer releaseQuantity;
    
    private Integer inventory;

    private List<String> couponCodes;

    private String couponCodePrefix;

    private String couponRuleType;


    private Integer couponCodeLength;

    // 时间同步活动时间 0-不同步 1-同步
    private String timeSameActivity;

    private String receiveTimeSameActivity;


    @ApiModelProperty(name = "promotionType",value = "The promotion type")
    private String promotionType;

    @ApiModelProperty(name = "remark",value = "The remark")
    private String remark;


    //ua领取券使用
    @ApiModelProperty(value = ApiConstants.TAKE_LABEL,hidden = true)
    private String takeLabel;

    //ua领取券使用
    @ApiModelProperty(value = "user codes",hidden = true)
    private String userCode;


    private String createUser;

}
