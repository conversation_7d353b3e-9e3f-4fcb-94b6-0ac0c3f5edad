
package com.gtech.promotion.dto.in.coupon;

import java.io.Serializable;
import java.util.List;

import com.gtech.promotion.page.RequestPage;

import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**   
 * 商城可查询所有店铺下的优惠券活动信息入参参数
 */
@ApiModel("MemberTagCouponActivityIn")
@Data
@EqualsAndHashCode(callSuper = true)
public class MemberTagCouponActivityInDTO extends RequestPage implements Serializable {

    private static final long serialVersionUID = 6798266147881904014L;

    private String domainCode;

    private String tenantCode;

    private List<String> memberTagList;

}
