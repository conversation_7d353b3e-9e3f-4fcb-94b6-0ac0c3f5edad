/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.dto.in.activity;

import java.io.Serializable;

import org.springframework.web.multipart.MultipartFile;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

/**
 * <功能描述>
 * 
 */
@Data
@ToString
@ApiModel("ProductFile")
public class ProductFileDTO implements Serializable{

    private static final long serialVersionUID = -8981123327502050563L;

    @ApiModelProperty(name = "file",value = "商品文件",required = true)
    private transient MultipartFile file;

    @ApiModelProperty(value = "租户编码",required = true)
    private String tenantCode;

    @ApiModelProperty(value = "文件上传秘钥：首次上传可为空,多个商品池后续上传不可为空",required = false)
    private String skuToken;

    @ApiModelProperty(value = "商品池序号",required = true)
    private Long seqNum;
    
    @ApiModelProperty(value = "是否带商品价格标识：0-不带；1-带",required = true)
    private String withPrice;

}
