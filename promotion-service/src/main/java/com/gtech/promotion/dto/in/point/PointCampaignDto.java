package com.gtech.promotion.dto.in.point;

import com.alibaba.fastjson.JSON;
import com.gtech.commons.utils.BeanCopyUtils;
import com.gtech.commons.utils.StringUtil;
import com.gtech.promotion.vo.bean.CampaignTitleLanguage;
import lombok.*;

import java.io.Serializable;
import java.util.Collections;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2020/12/31 14:41
 */
@Getter
@Setter
@ToString
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PointCampaignDto implements Serializable {
    private static final long serialVersionUID = -2672669724502079428L;

    /**
     * Tenant code.
     */
    private String tenantCode;

    /**
     * campaign code
     */
    private String campaignCode;


    /**
     * program name
     */
    private String programName;

    /**
     * campaign title
     */
    private String campaignTitle;

    /**
     * campaign desc
     */
    private String campaignDesc;

    /**
     * Campaingn sponsor information.
     */
    private String sponsor;

    /**
     * Point campaign begin time.
     */
    private String beginTime;

    /**
     * Point campaign end time.
     */
    private String endTime;

    /**
     * Activity total points.
     */
    private Integer totalPoints;

    /**
     * Activity remaining points.
     */
    private Integer remainingPoints;

    /**
     * campaignstatus.( 0-inactive 1-active)
     */
    private Integer status;


    /**
     * create user.
     */
    private String createUser;

    /**
     * create time
     */
    private Date createTime;

    /**
     * campaignTitleLanguage
     */
    private String campaignTitleLanguage;

    private List<CampaignTitleLanguage> campaignTitleLanguages;




    public List<CampaignTitleLanguage> getLanguages(){

        if (StringUtil.isNotBlank(campaignTitleLanguage)){
            campaignTitleLanguages = BeanCopyUtils.jsonCopyList(JSON.parseArray(campaignTitleLanguage), CampaignTitleLanguage.class);
        }else{
            campaignTitleLanguages = Collections.emptyList();
        }
        return campaignTitleLanguages;
    }



}
