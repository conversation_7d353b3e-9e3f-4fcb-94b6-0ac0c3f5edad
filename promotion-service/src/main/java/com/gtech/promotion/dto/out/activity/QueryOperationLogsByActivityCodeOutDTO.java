package com.gtech.promotion.dto.out.activity;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class QueryOperationLogsByActivityCodeOutDTO {

    private String activityCode;

    private String tenantCode;

    private String operationCode;

    private String operationType;

    private String createUser;

    private String createLastName;

    private String createFirstName;

    @JSONField(format="yyyyMMddHHmmss")
    private Date createTime;
}
