/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.dto.in.activity;

import java.io.Serializable;
import java.util.List;

import com.gtech.promotion.api.ApiConstants;
import com.gtech.promotion.page.RequestPage;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 统一券码数据管理
 * 
 */
@Getter
@Setter
@ToString
@ApiModel("ManagementDataIn")
public class ManagementDataInDTO extends RequestPage implements Serializable{

    private static final long serialVersionUID = -1206718866974463177L;

    @ApiModelProperty(name = "tenantCode",value = "租户编码",required = true,example = "88000011")
    private String tenantCode;

    @ApiModelProperty(value = "Language id")
    private String language;

    @ApiModelProperty(name = "activityCode",value = "活动编号",required = false,example = "20200690898932117670")
    private String activityCode;

    @ApiModelProperty(name = "couponCode",value = "券码编号",required = false,example = "611666644000134071")
    private String couponCode;

    @ApiModelProperty(name = "releaseStartTime",value = "投放开始时间",required = false,example = "20181213140606")
    private String releaseStartTime;

    @ApiModelProperty(name = "releaseEndTime",value = "投放结束时间",required = false,example = "20181213140606")
    private String releaseEndTime;

    @ApiModelProperty(name = "receiveStartTime",value = "领取开始时间",required = false,example = "20181213140606")
    private String receiveStartTime;

    @ApiModelProperty(name = "receiveEndTime",value = "领取结束时间",required = false,example = "20181213140606")
    private String receiveEndTime;

    @ApiModelProperty(name = "usedStartTime",value = "使用开始时间",required = false,example = "20181213140606")
    private String usedStartTime;

    @ApiModelProperty(name = "usedEndTime",value = "使用结束时间",required = false,example = "20181213140606")
    private String usedEndTime;

    @ApiModelProperty(name = "userCode",value = "会员编号",required = false,example = "18042000000058")
    private String userCode;

    @ApiModelProperty(name = "status",value = ApiConstants.COUPON_STATUS,example = "01",required = false)
    private String status;

    @ApiModelProperty(name = "couponType",value = ApiConstants.COUPON_TYPE + ",多个以逗号隔开",example = "01,02",required = false)
    private String couponType;

    @ApiModelProperty(name = "usedRefId",value = "订单编号",required = false,example = "20180426110666")
    private String usedRefId;

    @ApiModelProperty(name = "releaseCodes",hidden = true)
    private List<String> releaseCodes;

    //可以废除
    @ApiModelProperty(name = "couponTypes",hidden = true)
    private List<String> couponTypes;

    //可以废除
    @ApiModelProperty(name = "couponStatus",hidden = true)
    private List<String> couponStatus;

    @ApiModelProperty(name = "Type",hidden = true)
    private String type;

    @ApiModelProperty(name = "isUseStatus",hidden = true)
    private String isUseStatus;

}
