/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * <PERSON>ECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.dto.out.coupon;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

import com.gtech.commons.exception.ErrorCode;
import com.gtech.promotion.api.ApiConstants;
import com.gtech.promotion.vo.bean.ActivityPeriod;
import com.gtech.promotion.vo.param.activity.CustomCondition;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


/**   
 */
@Data
@ApiModel("筛选优惠券出参")
// com.gtech.promotion.vo.result.coupon.FilterCouponsByCartResult
public class CouponInfoDTO implements Serializable {
	
	private static final long serialVersionUID = 2027083643323885019L;

	@ApiModelProperty(value = "活动id",hidden = true)
    private transient String activityId;

    @ApiModelProperty("优惠券编码")
    private String couponCode;

    @ApiModelProperty("优惠口令")
    private String promoPassword;

    @ApiModelProperty(ApiConstants.COUPON_TYPE)
    private String couponType;

    @ApiModelProperty(ApiConstants.COUPON_STATUS)
    private String couponStatus;
    
    @ApiModelProperty("可用开始时间")
    private String validBegin;

    @ApiModelProperty(" 可用结束时间")
    private String validEnd;

    @ApiModelProperty(" 优惠券是否满足条件")
    private Boolean isReward;

    // Activity code
    private String activityCode;
    
    // Activity name
    private String activityName;
    
    // Activity label
    private String activityLabel;

    // Activity description
    private String activityDesc;

    // Activity short description
    private String activityShortDesc;

    @ApiModelProperty("每人限领")
    private Integer userlimitMax;

    @ApiModelProperty("券面值")
    private BigDecimal faceValue;
    
    @ApiModelProperty(ApiConstants.FACE_UNIT)
    private String faceUnit;
    
    @ApiModelProperty(ApiConstants.CONDITION_UNIT)
    private String conditionUnit;
    
    @ApiModelProperty("条件值")
    private BigDecimal conditionValue;

    @ApiModelProperty(ApiConstants.REWARD_TYPE)
    private String rewardType;

    @ApiModelProperty(value = "Activity period.")
    private ActivityPeriod activityPeriod;

	@ApiModelProperty(value = "Custom conditions.")
	private List<CustomCondition> customConditions;

    // Promotion match failed reason
    private ErrorCode failedReason;
    public String getFalseReason() {
        
        return null == failedReason ? null : failedReason.getMessage();
    }
    public String getFalseCode() {
        
        return null == failedReason ? null : failedReason.getCode();
    }

}
