/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.dto.in.activity;

import com.gtech.promotion.api.ApiConstants;
import com.gtech.promotion.checker.activity.TPromoOrderChecker;
import com.gtech.promotion.exception.Check;
import com.gtech.promotion.vo.bean.ProductCodes;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 购物车促销活动查询与计算的商品项参数实体
 *
 */
@Getter
@Setter
@ToString(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
public class ShoppingCartItem extends ProductCodes {

    private static final long serialVersionUID = -6590497614422112446L;

    @ApiModelProperty(value="商品原单价",required=true)
    private BigDecimal productPrice;

    @ApiModelProperty(value="市场价")
    private BigDecimal productListPrice;

    @ApiModelProperty(value="购买数量",required=true)
    private Integer quantity;

    @ApiModelProperty(value=ApiConstants.SELECT_FLAG,required=true)
    private String selectionFlag;

    @ApiModelProperty(hidden=true)
    private String orgCode;

    // "商品原总价",required = true)
    @ApiModelProperty(hidden=true)
    private transient BigDecimal productAmount;

    // "商品促销总价",required = true)
    @ApiModelProperty(hidden=true)
    private transient BigDecimal productPromoAmount;
    
    //该单品活动对其设置的特价0411
    //k-activityCode, v-price
    @ApiModelProperty(hidden=true)
    private transient Map<String, BigDecimal> activity0411Price;
    
    // 当前使用的活动
    @ApiModelProperty(hidden=true)
    @Getter
    private transient List<ShoppingCartActivity> usedActivitys;
    
    public void setUsedActivitys(List<ShoppingCartActivity> usedActivitys) {
        this.usedActivitys = usedActivitys;
    }

    public BigDecimal getProductAmount(){
        Check.check(null ==productPrice || null == quantity, TPromoOrderChecker.ERROR_PRICE_QUANTITY);
        return productAmount = this.productPrice.multiply(new BigDecimal(this.quantity));//NOSONAR
    }

    @Override
    public String toString() {
        return "ShoppingCartItem{" +
                "productPrice=" + productPrice +
                ", quantity=" + quantity +
                ", selectionFlag='" + selectionFlag + '\'' +
                ", orgCode='" + orgCode + '\'' +
                ", productAmount=" + productAmount +
                ", productPromoAmount=" + productPromoAmount +
                ", activity0411Price=" + activity0411Price +
                ", usedActivitys=" + usedActivitys +
                '}';
    }
}
