package com.gtech.promotion.dto.in.coupon;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class ChooseCouponByCartCouponInfoDTO implements Comparable{

    private String couponCode;
    private String validEndTime;

    @Override
    public int compareTo(Object o) {
        if (o instanceof ChooseCouponByCartCouponInfoDTO) {
            return validEndTime.compareTo(((ChooseCouponByCartCouponInfoDTO) o).getValidEndTime());
        }
        return 1;
    }
}
