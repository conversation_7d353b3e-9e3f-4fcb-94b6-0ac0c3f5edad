/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.dto.in.coupon;

import com.gtech.promotion.domain.activity.ActivityDomain;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**   
 * 创建优惠券活动入参
 */
@Getter
@Setter
@ToString
// @See CreateCouponActivityParam
public class CreateCouponActivityDTO extends ActivityDomain {

	private static final long serialVersionUID = -8811997698553792015L;

	// Coupon type: 01-General coupon 02-Anonymous coupon 03-Single fixed promotion code.
    private String couponType;
    
	// Single user limited max. 0 means unlimited
    private Integer userLimitMax;

    // Single user limited max. 0 means unlimited
    private Integer userLimitMaxDay;

	// Activity total quantity: 0 means unlimited
    private Integer totalQuantity;

	// Single promotional code while couponType=03
    private String promotionCode;

    private String promoPassword;

    private String couponRuleType;

    // Activity code.
    private transient String activityCode;
}
