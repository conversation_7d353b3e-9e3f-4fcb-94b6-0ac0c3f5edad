/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.dto.out.activity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;

/**
 * 
 */

@Data
@ToString
@ApiModel("ActivityDataAnalyzeOut")
public class ActivityDataAnalyzeOutDto implements Serializable{

    private static final long serialVersionUID = 1084187764428858816L;

    @ApiModelProperty(value = "累计创建活动总数含券活动",example = "100000")
    private Long activityTotal;

    @ApiModelProperty(value = "已使用的优惠券总量",example = "100000")
    private Long usedCouponTotal;

    @ApiModelProperty(value = "累计创建活动总数含券活动(某时间段内)",example = "10000")
    private Long activityTotalInTime;

    @ApiModelProperty(value = "已使用的优惠券总量(某时间段内)",example = "10000")
    private Long usedCouponTotalInTime;

}
