/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.dto.out.coupon;

import java.io.Serializable;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**   
 */
@Getter
@Setter
@ToString
public class CouponInfoImportOutDTO implements Serializable{

    private static final long serialVersionUID = -5622712657834202349L;

    // 优惠券编码
    private String couponCode;

    // 优惠券状态
    private String couponStatus;

    // 创建时间，字符串格式
    private String createTime;

    // 领取标签 01-免费领取、02-活动赠券、03-促销赠券、04-积分换购、05-有价券、06-新人券、07-生日券、08-补偿券、09-推送券、10-其他；
    private String takeLabel;
    
    private String couponDesc;
    
    private String couponName;

    private String validBegin;

    private String validEnd;
    
    private Integer validDays;

    // 领取开始时间(yyyyMMddhhmmss)
    private String receiveStartTime;

    // 领取结束时间(yyyyMMddhhmmss)
    private String receiveEndTime;
    
    private String receiveTime;
    
    private String usedTime;
    
    private String usedRefId;
    
    private String userCode;

    private transient String releaseCode;
    
    private String frozenStatus;

}
