/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * <PERSON>ECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.dto.out.product;

import com.gtech.promotion.vo.bean.ProductAttribute;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;

/**
 * 商品信息出参对象
 */
@Data
@ToString
@ApiModel("ProductOut")
public class ProductOutDTO implements Serializable{

    private static final long serialVersionUID = -5059123202236414239L;

    @ApiModelProperty(value = "Product category code.")
    private String categoryCode;

    @ApiModelProperty(value = "Product brand code.")
    private String brandCode;

    @ApiModelProperty(value = "Product attribute list.")
    private List<ProductAttribute> attributes;

    @ApiModelProperty("商品池序号")
    private Integer seqNum;

    @ApiModelProperty(value = "org code.",required = true)
    private String orgCode;

}
