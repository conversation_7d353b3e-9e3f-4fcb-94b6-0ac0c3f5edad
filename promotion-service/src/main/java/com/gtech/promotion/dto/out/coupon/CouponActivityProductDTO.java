/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.dto.out.coupon;

import com.gtech.promotion.api.ApiConstants;
import com.gtech.promotion.dto.out.activity.ActivityInfoDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.List;


/**   
 * 
 */
@Data
@ToString(callSuper=true)
@EqualsAndHashCode(callSuper=true)
@ApiModel("CouponActivityProduct")
public class CouponActivityProductDTO extends ActivityInfoDTO{

    private static final long serialVersionUID = -3834410732116752787L;

    @ApiModelProperty("faceValue:优惠券面值")
    private BigDecimal faceValue;

    @ApiModelProperty(ApiConstants.FACE_UNIT)
    private String faceUnit;
    
    @ApiModelProperty(" 赠品赠送最大限制数量    ")
    private String giftLimitMax;

    @ApiModelProperty(ApiConstants.COUPON_TYPE)
    private String couponType;

    @ApiModelProperty("每人限领")
    private Integer userLimitMax;

    @ApiModelProperty("奖励类型")
    private String rewardType;

    //条件值单位 01：元  02：件
    @ApiModelProperty(ApiConstants.CONDITION_UNIT)
    private String conditionUnit;

    @ApiModelProperty("条件值")
    private BigDecimal conditionValue;

    @ApiModelProperty("券活动的券可用时间")
    private List<CouponReleaseTimeOutDTO> couponValidTime;
    @ApiModelProperty("外部活动id")
    private String externalActivityId;
}
