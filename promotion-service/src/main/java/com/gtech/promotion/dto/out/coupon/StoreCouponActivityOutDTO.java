/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.dto.out.coupon;

import com.gtech.promotion.api.ApiConstants;
import com.gtech.promotion.vo.bean.ActivityPeriod;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 店铺下所有生效的优惠券活动列表出参
 */
@Data
@ToString
@ApiModel("店铺下所有生效的优惠券活动列表出参")
public class StoreCouponActivityOutDTO implements Serializable{

    private static final long serialVersionUID = 6132455069952360408L;

    @ApiModelProperty("活动名称")
    private String activityName;
    
    @ApiModelProperty("活动编码")
    private String activityCode;

    @ApiModelProperty("活动标签")
    private String activityLabel;

    @ApiModelProperty("活动描述")
    private String activityDesc;

    @ApiModelProperty("活动备注")
    private String activityRemark;

    @ApiModelProperty("优惠券面值")
    private BigDecimal faceValue;

    @ApiModelProperty(ApiConstants.FACE_UNIT)
    private String faceUnit;

    // conditionUnit:条件值单位 01：金额  02：数量   
    @ApiModelProperty(ApiConstants.CONDITION_UNIT)
    private String conditionUnit;

    @ApiModelProperty("条件值")
    private BigDecimal conditionValue;

    @ApiModelProperty("活动页url")
    private String activityUrl;

    // rewardType:奖励类型：减金额(01)、打折扣(02)、单件固定金额(03)、组合固定金额(04)、包邮(05)、送赠品(06)、买A送A(07)、买A送B(08)    
    @ApiModelProperty(value = ApiConstants.REWARD_TYPE)
    private String rewardType;

    @ApiModelProperty("最早可领取开始时间")
    private String startReceiveTime;

    @ApiModelProperty("最晚可领取结束时间")
    private String endReceiveTime;

    @ApiModelProperty(value = ApiConstants.RECEIVE_STATUS_STORE)
    private int receiveStatus;

    @ApiModelProperty("每人限领、限用")
    private Integer userLimitMax;

    @ApiModelProperty(value = "Activity warm-up begin time.")
    private String warmBegin;

    @ApiModelProperty(value = "Activity warm-up end time.")
    private String warmEnd;

    @ApiModelProperty(value = "Activity cool down time.")
    private String coolDown;

    @ApiModelProperty(value = "Activity background image.")
    private String backgroundImage;

    @ApiModelProperty(value = "Activity period.")
    private ActivityPeriod activityPeriod;
}
