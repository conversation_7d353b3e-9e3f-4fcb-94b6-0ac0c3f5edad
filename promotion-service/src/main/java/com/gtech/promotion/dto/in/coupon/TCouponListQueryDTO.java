/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * <PERSON>ECH MAKES NO REPRESENTATIONS OR WA<PERSON><PERSON>NTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.dto.in.coupon;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;

/**   
 */
@Getter
@Setter
@ToString
@ApiModel("TCouponListQuery")
public class TCouponListQueryDTO implements Serializable {
	
    private static final long serialVersionUID = -4870898936387208776L;

    @ApiModelProperty(value = "Tenant code.", required = true)
	private String tenantCode;

    @ApiModelProperty(value = "Language ID.")
    private String language;

    @ApiModelProperty(value = "Activity code.", required = true)
	private String activityCode;
	
    @ApiModelProperty(value = "券码创建开始时间", required = false)
	private String createTimeStart;
	
    @ApiModelProperty(value = "券码创建结束时间", required = false)
	private String createTimeEnd;
	
    @ApiModelProperty(value = "券码状态", required = false)
	private String couponStatus;
	
    @ApiModelProperty(value = "券码编码", required = false)
    private String couponCode;
    
    @ApiModelProperty(value = "投放编码", required = false)
    private String releaseCode;
    
    @ApiModelProperty(value = "页数 ", required = false)
	private Integer pageNo;

    @ApiModelProperty(value = "每页显示条数", required = false)
	private Integer pageCount;
    
    @ApiModelProperty(value = "Store orgnization code.", required = false)
    private String orgCode;
    
    @ApiModelProperty(value="渠道编码",required=false)
    private String channelCode;
}
  
