package com.gtech.promotion.dto.in.coupon;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @Description :
 * <AUTHOR> gaojie.li
 * @Date : 2023/12/22 13:57
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ExportCouponRelationDto implements Serializable {


    private static final long serialVersionUID = 4191460021713663882L;

    private String maxCouponCode;

    private String domainCode;

    private String tenantCode;

    private String activityCode;

    private String releaseCode;



}
