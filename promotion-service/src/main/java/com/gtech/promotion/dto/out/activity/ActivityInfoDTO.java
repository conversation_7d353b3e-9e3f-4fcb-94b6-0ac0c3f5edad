/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.dto.out.activity;

import com.gtech.promotion.api.ApiConstants;
import com.gtech.promotion.vo.bean.ActivityPeriod;
import com.gtech.promotion.vo.bean.Giveaway;
import com.gtech.promotion.vo.bean.Qualification;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 根据商品查询促销列表出参实体(出参)
 * 
 */
@Data
@ToString
@ApiModel("ActivityInfo")
public class ActivityInfoDTO implements Serializable{

    private static final long serialVersionUID = -7626002447833164385L;

    @ApiModelProperty(hidden = true)
    private transient String id;//

    @ApiModelProperty(ApiConstants.ACTIVITY_TYPE)
    private String activityType;//

    @ApiModelProperty("活动编码")
    private String activityCode;//

    @ApiModelProperty(value = "Group code.")
    private String groupCode;

    @ApiModelProperty(value = "Activity status.")
    private String activityStatus;

    @ApiModelProperty("活动名称")
    private String activityName;//

    @ApiModelProperty("活动描述")
    private String activityDesc;//
    
    @ApiModelProperty("活动备注")
    private String activityRemark;//

    @ApiModelProperty("活动标签")
    private String activityLabel;//
    
    @ApiModelProperty(ApiConstants.ACTIVITY_SORT)
    private String activitySort;//

    @ApiModelProperty("开始时间")
    private String activityBegin;//

    @ApiModelProperty("结束时间")
    private String activityEnd;//

    @ApiModelProperty(value = "Activity warm-up begin time.")
    private String warmBegin;

    @ApiModelProperty(value = "Activity cool down time.")
    private String coolDown;

    @ApiModelProperty(value = "ops create Activity type")
    private String opsType;

   @ApiModelProperty(value = "Create time.")
    private Date createTime;

    @ApiModelProperty("Qualification list")
    private List<Qualification> qualifications;//
    
    @ApiModelProperty(value = ApiConstants.PROMO_SCOPE)
    private String promoScope;//

    private String customCondition;

//    @ApiModelProperty("")
//    private String chanCodes;//渠道编码（多个用逗号隔开）

    @ApiModelProperty("预热结束时间")
    private String warmEnd;//

    @ApiModelProperty("活动URL")
    private String activityUrl;//

    @ApiModelProperty(ApiConstants.PRODUCT_SELECTION_TYPE)
    private String productSelectionType;

    @ApiModelProperty("赠品赠送最大限制数量")
    private String giftLimitMax;//
    
    @ApiModelProperty("赠品列表")
    private List<Giveaway> giveaways;

    @ApiModelProperty("商品池序号")
    private String seqNum;

    @ApiModelProperty(value = "Activity background image.")
    private String backgroundImage;

    @ApiModelProperty(value = "Ribbon image.")
    private String ribbonImage;

    @ApiModelProperty(value = "Position of Ribbon image .")
    private String ribbonPosition;

    @ApiModelProperty(value = "Ribbon text.")
    private String ribbonText;

    @ApiModelProperty(value = "Activity period.")
    private ActivityPeriod activityPeriod;


    @ApiModelProperty(value = "Priority.")
    private Integer priority;
}
