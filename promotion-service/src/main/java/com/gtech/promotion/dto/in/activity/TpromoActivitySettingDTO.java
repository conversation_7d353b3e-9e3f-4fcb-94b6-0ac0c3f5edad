/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.dto.in.activity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;

/**
 * 活动设置项入参实体
 */
@Data
@ToString
@ApiModel("TpromoActivitySetting")
public class TpromoActivitySettingDTO implements Serializable{

    private static final long serialVersionUID = -1047022621585656143L;

    @ApiModelProperty(value = "租户编码",required = true)
    private String tenantCode;

    @ApiModelProperty(value = "设置类型：0-重新加载数据库中的所有配置；1-模板的商品价格排序",required = true)
    private Integer settingType;

    @ApiModelProperty(value = "可设置项:settingType为1时，settingCode代表活动类型标签(参照活动列表接口tagCode解释)；settingType为2时，settingCode可以为空或者任意值",required = true)
    private String settingCode;
    
    @ApiModelProperty(value = "可设置值：settingType为1时，settingValue=1从大到小，settingValue=2从小到大（默认第和买送的活动是升序,其他降序）",required = true)
    private String settingValue;

}
