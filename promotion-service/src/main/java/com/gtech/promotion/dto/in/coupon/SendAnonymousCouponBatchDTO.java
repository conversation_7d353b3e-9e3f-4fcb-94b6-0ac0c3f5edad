/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.dto.in.coupon;

import com.gtech.promotion.vo.bean.Qualification;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;

/**
 * 批量领取优惠券接口的入参
 */
@Getter
@Setter
@ToString
public class SendAnonymousCouponBatchDTO implements Serializable {

    private static final long serialVersionUID = -6883155437525674016L;

    @ApiModelProperty(value = "Domain code.",example = "D00001")
    private String domainCode;

    @ApiModelProperty(value = "Tenant code")
    private String tenantCode;

    @ApiModelProperty(value = "Coupon activity code.")
    private String activityCode;

    @ApiModelProperty(value = "How many coupon codes are obtained this time.")
    private Integer sendTotal;

    @ApiModelProperty(value = "Language id")
    private String language;

    @ApiModelProperty(value="Channel code.")
    private String channelCode;

    @ApiModelProperty(value = "Store organization code.")
    private String orgCode;

    @ApiModelProperty(value = "Coupon activity release code.")
    private String releaseCode;

    private Integer receiveType;


    @Data
    public static class UserAndLevel implements Serializable{


        private static final long serialVersionUID = 3033117872726519598L;

        // Member level code.
        private List<Qualification> qualifications;
        
    }
}
