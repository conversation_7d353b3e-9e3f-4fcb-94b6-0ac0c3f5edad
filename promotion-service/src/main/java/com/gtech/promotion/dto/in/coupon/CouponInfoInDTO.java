/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.dto.in.coupon;

import com.gtech.promotion.vo.bean.Qualification;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 查询券活动详情-对外入参
 */
@Data
@ApiModel("CouponInfoIn")
// com.gtech.promotion.vo.param.coupon.FindCouponDetailParam
public class CouponInfoInDTO implements Serializable{

    private static final long serialVersionUID = -3147398547464028560L;

    @ApiModelProperty(name = "tenantCode",value = "系统租户编码", required = true)
    private String tenantCode;

    @ApiModelProperty(value = "Language id")
    private String language;

    @ApiModelProperty(name = "activityCode",value = "活动编码", required = true)
    private String activityCode;

    @ApiModelProperty(name = "userCode",value = "会员编码", required = false)
    private String userCode;

    @ApiModelProperty(name = "qualifications",value = "Qualification list",required = false)
    private List<Qualification> qualifications;
    
    @ApiModelProperty(value="Store organization code. (00-unlimited)",required=false)
    private String orgCode;
    
    @ApiModelProperty(value="渠道编码",required=false)
    private String channelCode;
}
