/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.dto.in.coupon;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 优惠券券码的缓存类
 */
@Data
public class CouponCodeCacheDTO implements Serializable {

    private static final long serialVersionUID = -1098944289511825150L;

    // Tenant code
    private String tenantCode;

    // Activity code
    private String activityCode;

    // 券活动类型：01：优惠券 02:匿名券 03：优惠码
    private String couponType;

    // 会员编号
    private transient String userCode;

    // Coupon release code.
    private String releaseCode;
    
    // 券码
    private String couponCode;

    // 单用户领券上限
    private Integer userLimitMax;

    // 领取开始时间(yyyyMMddhhmmss)
    private String receiveStartTime;

    // 领取结束时间(yyyyMMddhhmmss)
    private String receiveEndTime;

    // 可用开始时间
    private String validStartTime;
 
    // 可用结束时间
    private String validEndTime;
    
    // 面值    
    private BigDecimal faceValue;
    
    // 面值单位
    private String faceUnit;
    
    // 活动结束时间
    private String activityEndTime;
    
    // 有效时长.    
    private Integer validDays;

    // 领取标签 01-免费领取、02-活动赠券、03-促销赠券、04-积分换购、05-有价券、06-新人券、07-生日券、08-补偿券、09-推送券、10-其他；
    private transient String takeLabel;

    // 解决匿名券再使用时（提交订单锁定资源的时候），先领取后锁定在同一个事务中不能修改promo_coupon_code_user表中订单号的问题
    private transient String orderNo;
}

