/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.dto.out.activity;

import com.gtech.basic.idm.web.vo.result.QueryOpUserAccountListResult;
import com.gtech.basic.idm.web.vo.result.QueryUserResult;
import com.gtech.promotion.dao.model.activity.ActivityLanguageModel;
import com.gtech.promotion.dao.model.activity.ActivityModel;
import com.gtech.promotion.vo.bean.ActivityStore;
import com.gtech.promotion.vo.bean.ExtImage;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Getter
@Setter
@ToString
public class TPromoActivityListOutDTO implements Serializable {

    private static final long serialVersionUID = 5570477485515736705L;

    private String orgCode;

    // Activity type：01-Activity 02-CoupUon
    private String activityType;

    private String groupName;

    private String externalActivityId;

    // Activity code.
    private String activityCode;

    // Activity name.
    private String activityName;

    private String promotionCategory;

    // Activity description.
    private String activityDesc;

    // Activity remark.
    private String activityRemark;

    // Activity label.
    private String activityLabel;

    // Activity tag codes. Separated by commas. 01-单品、02-满减、03-满折、04-特价、05-包邮、06-赠品、07-捆绑、08-满送、10-买A优惠B
    private String tagCode;

    // Product selection type: 01-Selection, 02-Invert Selection
    private String productSelectionType;

    // Activity channel & store list.
    private List<ActivityStore> stores;

    // Activity start time. (yyyyMMddHHmmss)
    private String activityBegin;

    // Activity end time. (yyyyMMddHHmmss)
    private String activityEnd;

    // Activity warm start time. (yyyyMMddHHmmss)
    private String warmBegin;

    // Activity warm end time. (yyyyMMddHHmmss)
    private String warmEnd;

    // Activity status.
    private String activityStatus;

    // Template code.
    private String templateCode;

    private String createUser;
    private String createUserFirstName;
    private String createUserLastName;

    // Activity create time
    private Date createTime;

    private String updateUser;

    // Activity sponsors
    private String sponsors;

    private String opsType;

    private Integer priority;

    private String needAudit;

    private String needDifferentOperator;

    private String auditUser;

    private String commitUser;
    private List<ExtImage> extImages;

    //构造函数
    public TPromoActivityListOutDTO() {
    }

    /**
     * Creates a new instance of TPromoActivityListOutDTO.
     */
    public TPromoActivityListOutDTO(ActivityModel tPromoActivityVO, String tagCode, List<ActivityStore> stores, List<QueryOpUserAccountListResult> accountList, List<QueryUserResult> userAccountList,String groupName) {

        this.groupName = groupName;
        this.promotionCategory = tPromoActivityVO.getPromotionCategory();
        this.orgCode = tPromoActivityVO.getOrgCode();
        this.activityType = tPromoActivityVO.getActivityType();
        this.externalActivityId = tPromoActivityVO.getExternalActivityId();
        this.activityCode = tPromoActivityVO.getActivityCode();
        this.activityName = tPromoActivityVO.getActivityName();
        this.activityLabel = tPromoActivityVO.getActivityLabel();
        this.createTime = tPromoActivityVO.getCreateTime();
        this.activityDesc = tPromoActivityVO.getActivityDesc();
        this.activityRemark = tPromoActivityVO.getActivityRemark();
        this.activityBegin = tPromoActivityVO.getActivityBegin();
        this.activityEnd = tPromoActivityVO.getActivityEnd();
        this.warmBegin = tPromoActivityVO.getWarmBegin();
        this.warmEnd = tPromoActivityVO.getWarmEnd();
        this.activityStatus = tPromoActivityVO.getActivityStatus();
        this.productSelectionType = tPromoActivityVO.getProductSelectionType();
        this.tagCode = tagCode;
        this.stores = stores;
        this.sponsors = tPromoActivityVO.getSponsors();
        this.opsType = tPromoActivityVO.getOpsType();
        this.priority = tPromoActivityVO.getPriority();
        this.createUser = tPromoActivityVO.getCreateUser();
        this.auditUser = tPromoActivityVO.getAuditUser();
        this.extImages = tPromoActivityVO.getExtImages();
        if (CollectionUtils.isNotEmpty(accountList)){
            for (QueryOpUserAccountListResult queryOpUserAccountListResult : accountList) {
                if (queryOpUserAccountListResult.getUserCode().equals(tPromoActivityVO.getCreateUser())) {
                    createUserFirstName = queryOpUserAccountListResult.getFirstName();
                    createUserLastName = queryOpUserAccountListResult.getLastName();
                    break;
                }
            }
        }
        if (CollectionUtils.isNotEmpty(userAccountList)){
        	for (QueryUserResult queryUserResult : userAccountList) {
        		if (queryUserResult.getUserCode().equals(tPromoActivityVO.getCreateUser())) {
        			createUserFirstName = queryUserResult.getFirstName();
        			createUserLastName = queryUserResult.getLastName();
        			break;
        		}
        	}
        }
    }

    public void setLanguage(ActivityLanguageModel activityLanguage) {

        if (null == activityLanguage) {
            return;
        }

        if (StringUtils.isNotBlank(activityLanguage.getActivityLabel())) {
            this.setActivityLabel(activityLanguage.getActivityLabel());
        }

        if (StringUtils.isNotBlank(activityLanguage.getActivityName())) {
            this.setActivityName(activityLanguage.getActivityName());
        }

        if (StringUtils.isNotBlank(activityLanguage.getActivityDesc())) {
            this.setActivityDesc(activityLanguage.getActivityDesc());
        }

        if (StringUtils.isNotBlank(activityLanguage.getActivityRemark())) {
            this.setActivityRemark(activityLanguage.getActivityRemark());
        }
    }

}
