/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WA<PERSON>RANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.dto.in.activity;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**   
 * 更新活动状态入参对象
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UpdateExternalActivityInDTO {

    @ApiModelProperty(value="Tenant code",required=true)
    private String tenantCode;

    @ApiModelProperty(value="Activity code",required=true)
    private String activityCode;

    @ApiModelProperty(value="Domain code",required=true)
    private String domainCode;


    @ApiModelProperty(value = "User code of the operator.", example="U00001", required = true)
    private String operateUser;


    @ApiModelProperty(value = "External activity id",example="U00001", required = true)
    private String externalActivityId;


}
  
