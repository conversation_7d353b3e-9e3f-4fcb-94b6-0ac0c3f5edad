/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.dto.in.flashsale;

import com.google.common.base.Splitter;
import com.gtech.commons.utils.BeanCopyUtils;
import com.gtech.commons.utils.StringUtil;
import com.gtech.promotion.api.ApiConstants;
import com.gtech.promotion.callable.ErrorCouponAndReason;
import com.gtech.promotion.code.marketing.ActivityTypeEnum;
import com.gtech.promotion.dao.model.coupon.TPromoCouponCodeUserVO;
import com.gtech.promotion.dto.in.activity.ShoppingCartItem;
import com.gtech.promotion.vo.bean.ShoppingCartStore;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.*;

/**
 * 购物车查询计算的参数实体 属性有”租户信息“和”订单活动列表“以及“商品列表，商品里面包含其所属活动” 等
 */
@Getter
@Setter
@ToString
public class FlashSaleShoppingCartDto implements Serializable{

    private static final long serialVersionUID = -8669146458890802091L;

    // Tenant code.
    private String domainCode;

    // Tenant code.
    private String tenantCode;

    private String activityCode;

    private String orderNo;

    private String marketingGroupCode;

    // Language id.
    private String language;

    @ApiModelProperty(value = "activity type", example = "04")
    private String activityType = ActivityTypeEnum.FLASH_SALE.code();

    // Shopping cart store list.
    private List<ShoppingCartStore> cartStoreList;
    public void setCartStoreList(List<ShoppingCartStore> cartStoreList) {

        if (CollectionUtils.isEmpty(cartStoreList)) {
            this.cartStoreList = null;
            return;
        }
        this.cartStoreList = cartStoreList;

        if (null == this.promoProducts) {
            this.promoProducts = new ArrayList<>();
            for(ShoppingCartStore cs : cartStoreList) {
                this.promoProducts.addAll(BeanCopyUtils.jsonCopyList(cs.getCartItemList(), ShoppingCartItem.class));
            }
        }
    }

    public List<String> getOrgCodes() {

        if (CollectionUtils.isEmpty(cartStoreList)) {
            return Collections.emptyList();
        }

        List<String> orgCodes = new ArrayList<>();
        for(ShoppingCartStore cs : this.cartStoreList) {
            orgCodes.add(cs.getOrgCode());
        }

        return orgCodes;
    }

    @ApiModelProperty(value = "渠道编码",required = false)
    private Integer channelCode;

    @ApiModelProperty(value = "会员编码",required = true)
    private String userCode;

    @ApiModelProperty(value = "邮费")
    private BigDecimal postage;

    @ApiModelProperty(value = ApiConstants.QUALIFICATIONS)
    private Map<String, List<String>> qualifications;

    @ApiModelProperty(value = "已使用的券编码（多个用逗号隔开）",required = false)
    private String couponCodes;

    // Promotion time.(yyyyMMddHHmmss)
    private String promotionTime;

    private List<ShoppingCartItem> promoProducts;
    public List<ShoppingCartItem> getPromoProducts() {
        return this.promoProducts;
    }

    // 为筛选优惠券专门添加的字段
    @ApiModelProperty(hidden = true)
    private transient List<ErrorCouponAndReason> errorCouponAndReasons;

    public void addErrorCouponAndReasons(ErrorCouponAndReason errorCouponAndReason){
        if (CollectionUtils.isEmpty(errorCouponAndReasons)){
            errorCouponAndReasons = new ArrayList<>();
        }
        errorCouponAndReasons.add(errorCouponAndReason);
    }

    private transient String activityExpr;

    private Set<String> highPrioriyActivities;
    
    public void autoHighPrioriyActivities(Map<String, TPromoCouponCodeUserVO> couponCodeMap) {

        if (StringUtil.isBlank(this.getCouponCodes()) || MapUtils.isEmpty(couponCodeMap)){
            return;
        }

        for(String cc : Splitter.on(",").trimResults().splitToList(this.getCouponCodes())) {
            TPromoCouponCodeUserVO couponCodeUser = couponCodeMap.get(cc);
            if (null != couponCodeUser) {
                if (null == highPrioriyActivities) {
                    highPrioriyActivities = new HashSet<>();
                }
                highPrioriyActivities.add(couponCodeUser.getActivityCode());
            }
        }
    }

    public boolean isHighPrioriyActivity(String activityCode) {

        if (CollectionUtils.isEmpty(highPrioriyActivities)) {
            return false;
        }

        return highPrioriyActivities.contains(activityCode);
    }
}
