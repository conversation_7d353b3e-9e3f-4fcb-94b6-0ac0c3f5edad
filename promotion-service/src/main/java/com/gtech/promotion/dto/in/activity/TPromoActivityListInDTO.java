/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WA<PERSON>RANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.dto.in.activity;

import java.io.Serializable;
import java.util.List;

import com.gtech.promotion.api.ApiConstants;
import com.gtech.promotion.page.RequestPage;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString
public class TPromoActivityListInDTO extends RequestPage implements Serializable {

    private static final long serialVersionUID = -7246261991941438343L;

    // Domain code, required = true
    private String domainCode;

    // Tenant code, required = true
    private String tenantCode;

    private String orgCode;

    @ApiModelProperty(value = "Language id")
    private String language;

    @ApiModelProperty(value = "Activity code")
    private String activityCode;

    @ApiModelProperty(value = "Activity type：01-Activity 02-Coupon")
    private String activityType;

    @ApiModelProperty(value = "Activity tag codes. Separated by commas. 01-单品、02-满减、03-满折、04-特价、05-包邮、06-赠品、07-捆绑、08-满送、10-买A优惠B 11-prodict discount -1-promotion code")
    private String tagCode;

    @ApiModelProperty(value = "Activity name")
    private String activityName;

    @ApiModelProperty(value = "Activity begin time from")
    private String activityBeginFrom;

    @ApiModelProperty(value = "Activity begin time to")
    private String activityBeginTo;

    @ApiModelProperty(value = "Activity end time from")
    private String activityEndFrom;

    @ApiModelProperty(value = "Activity end time to")
    private String activityEndTo;

    @ApiModelProperty(value = "Activity create time from")
    private String createTimeFrom;

    @ApiModelProperty(value = "Activity create time to")
    private String createTimeTo;

    @ApiModelProperty(value = "Activity status: split by comma")
    private String activityStatus;

    @ApiModelProperty(value = ApiConstants.ORDERBY_TYPE)
    private String orderByType;

    // Channel code.
    private String channelCode;

    @ApiModelProperty(value = "Activity sponsors")
    private String sponsors;

    @ApiModelProperty(value = "ops create Activity type")
    private String opsType;

    @ApiModelProperty(value = "activity org code")
    private String activityOrgCode;

    private List<String> activityStatusList;
    private List<String> opsTypeList;
    private List<String> memberTagList;

    private String warmBeginFrom;

    private String warmBeginTo;

    private String warmEndFrom;

    private String warmEndTo;
	private Boolean defaultFlag = true;




}
