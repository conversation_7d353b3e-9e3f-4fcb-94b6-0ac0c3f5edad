/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.dto.out.activity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;

/**   
 * 促销租户数据统计出参实体
 */
@Data
@ToString
@ApiModel("ActivityTenantOut")
public class ActivityTenantOutDTO implements Serializable{

    private static final long serialVersionUID = -5483551227443367592L;
    
    @ApiModelProperty(value = "券领取总数",  example = "888888")
    private int receivedTotal;
    
    @ApiModelProperty(value = "券使用总数", example = "888888")
    private int usedTotal;
    
    @ApiModelProperty(value = "订单支付总数", example = "888888")
    private int orderTotal;
    
    @ApiModelProperty(value = "奖励总金额", example = "888888")
    private Double incentiveMoneyTotal;

}
  
