package com.gtech.promotion.dto.in.purchaseconstraint;

import com.gtech.promotion.vo.bean.ProductAttribute;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 限购检查请求商品信息
 */
@Data
@Accessors(chain = true)
public class CheckPurchaseConstraintProductDto implements Serializable {
    private List<String> categories;// 此list为categoryPath 格式A1>B1>C 如果以后需要时 新增List<String> categoryCodes
    private List<String> categoryCodes;
    private List<String> categoryNames;
    private String brandCode;
    private String brandName;
    private List<ProductAttribute> attributes; // sku属性
    private String productCode;
    private String productName;
    private String skuCode;
    private String skuName;
    private BigDecimal promotionPrice; // 此商品行折后单价
    private BigDecimal promotionAmount; // 此商品行折后总价
    private Integer quantity;
    private Integer selected;
    private String mainProductNo;
    private List<ProductAttribute> spuAttributes; // spu属性，用于核算促销价时，处理通过spu属性标记的活动
    private String productTag;
    /**
     * 高级价格限购标识，默认0，非高级价格限购
     */
    private Integer priceSetting;
}
