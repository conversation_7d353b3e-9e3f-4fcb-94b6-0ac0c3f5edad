/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.dto.out.coupon;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;

/**   
 * 查询根据租户编码查询券活动返回dto
 */
@Data
@ToString
@ApiModel("CouponActivity")
public class CouponActivityDTO implements Serializable{

    
    private static final long serialVersionUID = -7638177984554786571L;

    @ApiModelProperty("活动id")
    private String id;
   
    @ApiModelProperty("活动名称   ")
    private String activityName;
    
    @ApiModelProperty("活动编码  ")
    private String activityCode;
    
    @ApiModelProperty(" 活动开始 ")
    private String activityBegin;
    
    @ApiModelProperty("活动结束")
    private String activityEnd;
}
  
