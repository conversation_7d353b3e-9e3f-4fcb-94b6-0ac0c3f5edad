package com.gtech.promotion.mq;

import com.alibaba.fastjson.JSON;
import com.alibaba.rocketmq.common.message.Message;
import com.gtech.basic.filecloud.api.model.ImportFileMessage;
import com.gtech.promotion.pojo.ExportBusinessEnums;
import com.gtech.promotion.pojo.MqEnums;
import com.gtech.promotion.service.common.ExcelService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import tk.mybatis.mapper.util.StringUtil;

@Slf4j
@Component(value = "MQConsumerBusiness")
public class MQConsumerBusiness {

	@Autowired
	private ExcelService excelService;

	public void businessImpl(MqEnums mqEnums, String json) {
		switch (mqEnums) {
			case EXCEL_REQUEST_MQ:
				importExcel(json);
				break;
			case EXCEL_RESULT_MQ:
			default:
				break;
		}
	}

	@SuppressWarnings("unchecked")
	private void importExcel(String json) {
		try {
			if (StringUtil.isEmpty(json)) {
				return;
			}
			ImportFileMessage<String> importFileMessage = JSON.parseObject(json, ImportFileMessage.class);
			String category = importFileMessage.getSubcategory();// 以sub区分excel的sheet
			ExportBusinessEnums exportBusinessEnums = ExportBusinessEnums.getByCode(category);
			log.info("subcategory:{}", category);
			if (exportBusinessEnums == null) {
				log.warn("importExcel faild, subcategory:[{}] does not exist", category);
				return;
			}
			ImportFileMessage<ImportFileMessage.Result> resultMessage = null;

			if (exportBusinessEnums == ExportBusinessEnums.SKU_IMPORT_FLASH_SALE) {//NOSONAR
				resultMessage = excelService.importSku(importFileMessage);
			}
			sendExcelResultMessage(resultMessage);
		} catch (Exception e) {
			log.error(e.getMessage(), e);
		}
	}

	public void sendExcelResultMessage(Object object) {
		if (object == null) {
			log.info("sendResultMessage is null");
			return;
		}
		log.info("start send message.");
		Message message = new Message(MqEnums.EXCEL_RESULT_MQ.getTopicName(), JSON.toJSONString(object).getBytes());
		try {
			MQProducerFactory.getInstance().getProducer(MqEnums.EXCEL_RESULT_MQ.getTopicName()).send(message);
			log.error("send message success");
		} catch (Exception e) {
			log.error("send message error");
			log.error(e.getMessage(), e);
		}
		log.info("end send message.");
	}
}
