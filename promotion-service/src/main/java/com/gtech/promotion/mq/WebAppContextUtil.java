package com.gtech.promotion.mq;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

/**
 * bean 集合
 *
 * 
 */
@Component
public class WebAppContextUtil implements ApplicationContextAware {

    private static ApplicationContext applicationContext;


	@Autowired
    @Override
	public void setApplicationContext(ApplicationContext applicationContext) {
        storeApplicationContext(applicationContext);
    }


    private static void storeApplicationContext(ApplicationContext appCtx){
        WebAppContextUtil.applicationContext = appCtx;
    }

    public static Object getBean(String beanName) {
        return applicationContext.getBean(beanName);
    }

    public static boolean containsBean(String beanName) {
        return applicationContext.containsBean(beanName);
    }

}
