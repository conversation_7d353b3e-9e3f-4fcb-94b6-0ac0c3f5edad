package com.gtech.promotion.mq;

import com.alibaba.rocketmq.client.exception.MQClientException;
import com.alibaba.rocketmq.client.producer.DefaultMQProducer;
import com.gtech.promotion.dto.config.MQConfigurations;
import com.gtech.promotion.pojo.MqEnums;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.Map;
@Slf4j
public class MQProducerFactory {

	private static MQProducerFactory instance;

	private static Map<String, DefaultMQProducer> producerMap = new HashMap<>(16);

	public static MQProducerFactory getInstance() {
		if (instance == null) {//NOSONAR
			synchronized (MQProducerFactory.class) {//NOSONAR
				if (instance == null) {//NOSONAR
					instance = new MQProducerFactory();
				}
			}
		}
		return instance;
	}

	public void addProducer(String groupName, DefaultMQProducer producer) {
		producerMap.put(groupName, producer);
	}

	public DefaultMQProducer getProducer(String topicName) {
		MqEnums mqEnums = MqEnums.getByCode(topicName);
		if (producerMap.get(topicName) == null) {//NOSONAR
			synchronized (producerMap) {
				if (producerMap.get(topicName) == null) {
					addProducer(topicName, createMQProducer(mqEnums));
				}
			}
		}
		return producerMap.get(topicName);
	}

	private DefaultMQProducer createMQProducer(MqEnums mqEnums) {

		DefaultMQProducer producer = new DefaultMQProducer(mqEnums.getProducerGroupName());

		MQConfigurations mqConfigurations = (MQConfigurations) WebAppContextUtil.getBean("MQConfigurations");
		producer.setNamesrvAddr(mqConfigurations.getNamesrvAddr());
		if (mqConfigurations.getMaxMessageSize() != null) {
			producer.setMaxMessageSize(mqConfigurations.getMaxMessageSize());
		}
		if (mqConfigurations.getSendMsgTimeout() != null) {
			producer.setSendMsgTimeout(mqConfigurations.getSendMsgTimeout());
		}
		// 如果发送消息失败，设置重试次数，默认为2次
		if (mqConfigurations.getRetryTimesWhenSendFailed() != null) {
			producer.setRetryTimesWhenSendFailed(mqConfigurations.getRetryTimesWhenSendFailed());
		}

		try {
			producer.start();

			log.info("producer is start !!! groupName:{},namesrvAddr:{}", mqEnums.getProducerGroupName(), mqConfigurations.getNamesrvAddr());
		} catch (MQClientException e) {//NOSONAR
			log.error(String.format("producer is error %s", e.getMessage()));//NOSONAR
		}
		return producer;
	}

}
