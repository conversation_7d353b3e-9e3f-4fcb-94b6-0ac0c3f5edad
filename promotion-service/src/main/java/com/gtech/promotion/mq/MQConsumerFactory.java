package com.gtech.promotion.mq;

import java.util.HashMap;
import java.util.Map;

import org.springframework.util.CollectionUtils;

import com.alibaba.rocketmq.client.consumer.DefaultMQPushConsumer;
import com.alibaba.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import com.alibaba.rocketmq.client.consumer.listener.MessageListenerConcurrently;
import com.alibaba.rocketmq.client.exception.MQClientException;
import com.alibaba.rocketmq.common.consumer.ConsumeFromWhere;
import com.alibaba.rocketmq.common.message.MessageExt;
import com.gtech.promotion.dto.config.MQConfigurations;
import com.gtech.promotion.pojo.MqEnums;

import lombok.extern.slf4j.Slf4j;
@Slf4j
public class MQConsumerFactory {

	private static MQConsumerFactory instance;

	private static Map<String, DefaultMQPushConsumer> consumerMap = new HashMap<>(16);

	public static MQConsumerFactory getInstance() {
		if (instance == null) {
			synchronized (MQConsumerFactory.class) {//NOSONAR
				if (instance == null) {
					instance = new MQConsumerFactory();
				}
			}
		}
		return instance;
	}

	public void getConsumer() {
		getConsumer(MqEnums.EXCEL_REQUEST_MQ);
	}

	public void addConsumer(String topicName, DefaultMQPushConsumer consumer) {
		consumerMap.put(topicName, consumer);
	}//NOSONAR

	public DefaultMQPushConsumer getConsumer(MqEnums mqEnums) {

		if (consumerMap.get(mqEnums.getTopicName()) == null) {
			synchronized (consumerMap) {
				if (consumerMap.get(mqEnums.getTopicName()) == null) {
					addConsumer(mqEnums.getTopicName(), createMQConsumer(mqEnums));
				}
			}
		}
		return consumerMap.get(mqEnums.getTopicName());
	}

	private DefaultMQPushConsumer createMQConsumer(MqEnums mqEnums) {

		MQConfigurations mqConfigurations = (MQConfigurations) WebAppContextUtil.getBean("MQConfigurations");
		DefaultMQPushConsumer consumer = new DefaultMQPushConsumer(mqEnums.getConsumerGroupName());
		consumer.setNamesrvAddr(mqConfigurations.getNamesrvAddr());
		consumer.setConsumeThreadMin(mqConfigurations.getConsumeThreadMin());
		consumer.setConsumeThreadMax(mqConfigurations.getConsumeThreadMax());
		consumer.registerMessageListener((MessageListenerConcurrently) (msgs, context) -> {
			if (CollectionUtils.isEmpty(msgs)) {
				log.info("接受到的消息为空，不处理，直接返回成功");
				return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
			}
			MessageExt messageExt = msgs.get(0);
			// 判断该消息是否重复消费（RocketMQ不保证消息不重复，如果你的业务需要保证严格的不重复消息，需要你自己在业务端去重）
			int reconsume = messageExt.getReconsumeTimes();
			if (reconsume > 0) {
				// 消息已经重试了3次，如果不需要再次消费，则返回成功
				return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
			}

			MQConsumerBusiness consumerBusiness = (MQConsumerBusiness) WebAppContextUtil.getBean("MQConsumerBusiness");
			String json = new String(messageExt.getBody());
			log.info("接受到的消息成功");
			// 处理业务
			consumerBusiness.businessImpl(mqEnums, json);
			// 如果没有return success ，consumer会重新消费该消息，直到return success
			return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
		});
		/**
		 * 设置Consumer第一次启动是从队列头部开始消费还是队列尾部开始消费 如果非第一次启动，那么按照上次消费的位置继续消费
		 */
		consumer.setConsumeFromWhere(ConsumeFromWhere.CONSUME_FROM_LAST_OFFSET);
		/**
		 * 设置一次消费消息的条数，默认为1条
		 */
		consumer.setConsumeMessageBatchMaxSize(mqConfigurations.getConsumeMessageBatchMaxSize());
		try {
			consumer.subscribe(mqEnums.getTopicName(), "*");
			consumer.start();
			log.info("consumer is start !!! groupName:{},namesrvAddr:{}", mqEnums.getConsumerGroupName(), mqConfigurations.getNamesrvAddr());
		} catch (MQClientException e) {
			log.error("consumer is start !!! groupName:{},namesrvAddr:{}", mqEnums.getConsumerGroupName(), mqConfigurations.getNamesrvAddr(), e);
		}
		return consumer;
	}

}
