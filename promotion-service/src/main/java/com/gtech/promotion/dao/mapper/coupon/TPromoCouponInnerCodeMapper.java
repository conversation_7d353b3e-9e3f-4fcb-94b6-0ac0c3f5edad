/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.dao.mapper.coupon;

import java.util.Date;
import java.util.List;

import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import com.gtech.commons.dao.mapper.GTechBaseMapper;
import com.gtech.promotion.dao.entity.coupon.TPromoCouponInnerCodeEntity;
import com.gtech.promotion.dao.model.coupon.CountCouponCodeModel;
import com.gtech.promotion.dao.model.coupon.CouponInnerRelationVO;
import com.gtech.promotion.dao.model.coupon.TPromoCouponInnerCodeVO;
import com.gtech.promotion.dto.in.activity.CouponQuantityDTO;
import com.gtech.promotion.dto.in.activity.ManagementDataInDTO;
import com.gtech.promotion.dto.in.coupon.ExportCouponRelationDto;
import com.gtech.promotion.dto.in.coupon.TCouponListQueryDTO;
import com.gtech.promotion.dto.out.coupon.CouponInfoImportOutDTO;
import com.gtech.promotion.dto.out.coupon.ManagementDataOutDTO;

/**   
 * 券码
 */
//@Mapper
public interface TPromoCouponInnerCodeMapper extends GTechBaseMapper<TPromoCouponInnerCodeEntity> {

    @Select("<script> SELECT COUNT(status) as couponCount, release_code as releaseCode, status as couponStatus"
            + " FROM promo_coupon_inner_code WHERE tenant_code = #{tenantCode} and activity_code = #{activityCode} "
            + "<if test='releaseCode != null'> and release_code = #{releaseCode} </if>"
            + " GROUP BY release_code, status </script>")
    List<CountCouponCodeModel> countCouponCode(@Param("tenantCode") String tenantCode, @Param("activityCode") String activityCode, @Param("releaseCode") String releaseCode);

    @Select("<script> SELECT " +
            "    date_format(t1.CREATE_TIME, '%Y%m%d%H%i%s') AS createTime," +
            "    t1.COUPON_CODE AS couponCode," +
            "    t1.RELEASE_CODE AS releaseCode," +
            "    t1.STATUS AS couponStatus," +
            "    t1.RECEIVE_START_TIME AS receiveStartTime," +
            "    t1.RECEIVE_END_TIME AS receiveEndTime," +
            "    t2.USED_REF_ID AS usedRefId," +
            "    t2.USER_CODE AS userCode," +
            "    t2.USED_TIME AS usedTime," +
            "    t1.FROZEN_STATUS as frozenStatus" +
            " FROM " +
            "    promo_coupon_inner_code t1" +
            "        LEFT JOIN" +
            "    promo_coupon_code_user t2 ON t1.COUPON_CODE = t2.COUPON_CODE" +
            "        AND t2.TENANT_CODE = #{tCouponListQueryDTO.tenantCode}" +
            "        AND t2.ACTIVITY_CODE = #{tCouponListQueryDTO.activityCode}" +
            " WHERE " +
            "    t1.TENANT_CODE = #{tCouponListQueryDTO.tenantCode}" +
            "        AND t1.ACTIVITY_CODE = #{tCouponListQueryDTO.activityCode}" +
            "  <if test=\"createTimeStart != null   \"> " +
            "        and t1.CREATE_TIME &gt;= #{createTimeStart}" +
            " </if> " +
            "  <if test=\"createTimeEnd != null \"> " +
            "        and t1.CREATE_TIME &lt;= #{createTimeEnd}" +
            " </if> " +
            "  <if test=\"tCouponListQueryDTO.couponCode != null and tCouponListQueryDTO.couponCode != ''  \"> " +
            "        and t1.COUPON_CODE = #{tCouponListQueryDTO.couponCode}" +
            " </if> " +
            "  <if test=\"tCouponListQueryDTO.couponStatus != null and tCouponListQueryDTO.couponStatus != ''  \"> " +
            "        and t1.STATUS = #{tCouponListQueryDTO.couponStatus}" +
            " </if> " +
            " </script>")
    List<CouponInfoImportOutDTO> selectCouponCodeList(@Param("tCouponListQueryDTO") TCouponListQueryDTO tCouponListQueryDTO,
                                                      @Param("createTimeStart") Date createTimeStart, @Param("createTimeEnd") Date createTimeEnd);

    @Select("SELECT COUPON_CODE FROM promo_coupon_inner_code WHERE tenant_code = #{tenantCode} and activity_code = #{activityCode} and RELEASE_CODE = #{releaseCode}"
            + " AND FROZEN_STATUS = '01' AND STATUS = '01' "
            + " AND RECEIVE_START_TIME <= #{now1} AND RECEIVE_END_TIME > #{now2} LIMIT #{count} ")
    List<String> queryHeaderCouponCodes(@Param("tenantCode") String tenantCode, @Param("activityCode") String activityCode, @Param("releaseCode") String releaseCode,
                                        @Param("now1") String now1, @Param("now2") String now2, @Param("count") int count);

    @Select("<script> SELECT " +
            "    i.COUPON_TYPE as couponType," +
            "    i.COUPON_CODE as couponCode," +
            "    IFNULL(u.STATUS,i.STATUS) as status," +
            "    i.FROZEN_STATUS as frozenStatus," +
            "    b.ACTIVITY_CODE AS activityCode," +
            "    b.ACTIVITY_NAME AS activityName," +
            "    r.RELEASE_TIME  as releaseTime," +
            "    u.USED_REF_ID   as usedRefId," +
            "    u.RECEIVED_TIME as receiveTime," +
            "    u.USED_TIME     as usedTime," +
            "    u.TAKE_LABEL    as takeLabel," +
            "    u.USER_CODE     as userCode" +
            " FROM" +
            "    promo_coupon_inner_code i" +
            "    join promo_activity b on b.ACTIVITY_CODE = i.ACTIVITY_CODE " +
            "    left join promo_coupon_code_user u on u.COUPON_CODE = i.COUPON_CODE AND u.TENANT_CODE = #{tenantCode} AND u.COUPON_TYPE != '03'" +
            "    left join promo_coupon_release r on r.RELEASE_CODE = i.RELEASE_CODE AND r.TENANT_CODE = #{tenantCode} " +
            "    left join promo_activity a on a.ACTIVITY_CODE = i.ACTIVITY_CODE" +

            " WHERE " +
            " i.TENANT_CODE = #{tenantCode} AND i.COUPON_TYPE != '03' " +

            " <if test=\"activityCode != null and activityCode!='' \">" +
            "        AND a.ACTIVITY_CODE = #{activityCode}" +
            " </if>" +

            " <if test=\"couponTypes != null \">" +
            "        AND i.COUPON_TYPE in " +
            "        <foreach item=\"item\" index=\"index\" collection=\"couponTypes\" open=\"(\" separator=\",\" close=\")\"> #{item} </foreach>" +
            " </if>" +

            " <if test=\"couponCode != null and couponCode !=''\">" +
            "        AND i.COUPON_CODE = #{couponCode}" +
            " </if>" +

            " <if test=\"status != null and status !=''\">" +
            "        AND u.STATUS = #{status}" +
            " </if>" +

            " <if test=\"userCode != null and userCode !=''\">" +
            "      AND u.USER_CODE = #{userCode}" +
            " </if>" +

            "  <if test=\"usedRefId != null and usedRefId!='' \">" +
            "      AND u.USED_REF_ID = #{usedRefId}" +
            "  </if>" +

            "  <if test=\"receiveEndTime != null and receiveEndTime !='' \">" +
            "      AND u.RECEIVED_TIME &lt;= #{receiveEndTime}" +
            "  </if>" +

            "  <if test=\"usedStartTime != null and usedStartTime !='' \">" +
            "      AND u.USED_TIME &gt;= #{usedStartTime}" +
            "  </if>" +

            "  <if test=\"usedEndTime != null and usedEndTime !='' \">" +
            "      AND u.USED_TIME &lt;= #{usedEndTime}" +
            "  </if>" +

            "   <if test=\"releaseStartTime != null and releaseStartTime !='' \">" +
            "           AND r.RELEASE_TIME &gt;= #{releaseStartTime}" +
            "  </if>" +

            "  <if test=\"releaseEndTime != null and releaseEndTime !='' \">" +
            "           AND r.RELEASE_TIME &lt;= #{releaseEndTime}" +
            "  </if>" +

            "  <if test=\"receiveStartTime != null and receiveStartTime !='' \">" +
            "           AND u.RECEIVED_TIME &gt;= #{receiveStartTime}" +
            "  </if>" +
            "  ORDER BY  i.ID DESC" +
            "</script>")
    List<ManagementDataOutDTO> queryCouponActivityList(ManagementDataInDTO managementDataInDTO);


    @Select("<script> select count(*) from promo_coupon_inner_code c where c.TENANT_CODE = #{tenantCode} and c.ACTIVITY_CODE = #{activityCode} and c.RELEASE_CODE = #{releaseCode} and c.`STATUS` = #{status} and c.FROZEN_STATUS = #{frozenStatus}</script>")
    Integer getCouponQuantity(CouponQuantityDTO couponQuantityDTO);


    @Update("<script>  " +
            "<foreach item=\"item\" index=\"index\" collection=\"vos\" open=\"\" close=\"\" separator=\";\" >" +

            "update promo_coupon_inner_code set VALID_START_TIME= #{item.validStartTime}, VALID_END_TIME= #{item.validEndTime}" +

            "  <if test=\"item.status  != null and item.status !='' \">" +
            "     , STATUS = #{item.status}" +
            "  </if>" +

            " where COUPON_CODE = #{item.couponCode} and TENANT_CODE = #{item.tenantCode}  " +
            "</foreach>" +
            "</script>")
    void updateBatchInnerCodeValidTime(@Param("vos") List<TPromoCouponInnerCodeVO> vos);

    @Update("<script>  " +
            "<foreach item=\"item\" index=\"index\" collection=\"vos\" open=\"\" close=\"\" separator=\";\" >" +

            "update promo_coupon_inner_code set STATUS = #{item.status} " +

            "  <if test=\"item.validStartTime  != null and item.validStartTime !='' \">" +
            "     , VALID_START_TIME= #{item.validStartTime}" +
            "  </if>" +

            "  <if test=\"item.validEndTime  != null and item.validEndTime !='' \">" +
            "     , VALID_END_TIME= #{item.validEndTime} " +
            "  </if>" +

            " where COUPON_CODE = #{item.couponCode} and TENANT_CODE = #{item.tenantCode}  " +
            "</foreach>" +

            "</script>")
    void updateBatchInnerCode(@Param("vos") List<TPromoCouponInnerCodeVO> vos);


    @Select("<script>" +
            "SELECT  " +
            "  TENANT_CODE  tenantCode,  " +
            "  ACTIVITY_CODE activityCode,  " +
            "  RELEASE_CODE releaseCode," +
            "  COUPON_CODE couponCode," +
            "  COUPON_CODE maxCouponCode," +

            "  COUPON_TYPE couponType," +
            "  STATUS status," +
            "  RECEIVE_START_TIME receiveStartTime," +
            "  RECEIVE_END_TIME receiveEndTime," +
            "  VALID_START_TIME validStartTime," +
            "  VALID_END_TIME validEndTime " +
            "   FROM  " +
            "  promo_coupon_inner_code   " +
            "WHERE  " +
            "  TENANT_CODE = #{tenantCode}   " +
            "  AND ACTIVITY_CODE = #{activityCode}  " +

            " <if test=\"releaseCode != null and releaseCode != ''\"> " +
            "  AND RELEASE_CODE = #{releaseCode} " +
            " </if> " +

            " <if test=\"maxCouponCode != null and maxCouponCode != ''\"> " +
            "  AND COUPON_CODE &gt; #{maxCouponCode} " +
            " </if> " +

            " ORDER BY  COUPON_CODE asc LIMIT 1000" +
            "</script>")
    List<CouponInnerRelationVO> exportCouponRelationInfo(ExportCouponRelationDto dto);

}
