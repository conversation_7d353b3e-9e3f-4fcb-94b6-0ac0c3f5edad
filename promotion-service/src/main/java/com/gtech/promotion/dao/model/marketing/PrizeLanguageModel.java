package com.gtech.promotion.dao.model.marketing;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
public class PrizeLanguageModel extends BaseModel {

    // 奖品编号（系统生成，全局唯一）
    private String prizeNo;
    private String prizeName;
    private String language;

}
