package com.gtech.promotion.dao.model.marketing;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Description :
 * <AUTHOR> gaojie.li
 * @Date : 2023/6/5 15:35
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class MarketingGroupMode extends BaseModel {

    private Integer showLeaderPrice;

    /**
     * 时效字段
     */
    private Integer effectiveHour;

    /***
     * 成团人数
     */
    private Integer groupSize;

    /**
     * 允许自动成团
     * 默认开， 0开 1关闭
     */
    private Integer autoGroupFlag;


    private String createUser;

    private String updateUser;

    /**
     * 团长福利
     */
    private String leaderBenefits;

    private Integer closeFlag;



}
