package com.gtech.promotion.dao.entity.marketing;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import javax.persistence.Table;

@EqualsAndHashCode(callSuper = true)
@Table(name = "MARKETING_PRIZE_RECORD")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class PrizeRecordEntity extends BaseEntity {

    public static final String C_MEMBER_CODE = "memberCode";
    public static final String C_TICKET_CODE = "ticketCode";

    private String activityCode;
    // 奖品编号（系统生成，全局唯一）
    private String prizeNo;
    private String memberCode;
    // 领取状态，01-未领取 02-已领取 03-领取失败
    private String status;
    private String ticketCode;
}
