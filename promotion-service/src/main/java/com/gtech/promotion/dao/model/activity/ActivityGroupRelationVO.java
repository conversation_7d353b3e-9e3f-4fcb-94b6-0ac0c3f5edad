package com.gtech.promotion.dao.model.activity;

import lombok.*;

import java.io.Serializable;

/**
 * @Description :
 * <AUTHOR> gaojie.li
 * @Date : 2023/7/12 14:07
 */
@NoArgsConstructor
@AllArgsConstructor
@Getter
@Setter
@ToString
public class ActivityGroupRelationVO implements Serializable {

    private String tenantCode;

    private String groupCodeA;

    private String groupCodeB;
    /**
     * 分组关系 1:叠加 2：互斥
     */
    private Integer relation;
}
