package com.gtech.promotion.dao.entity.marketing.flashsale;

import com.gtech.promotion.dao.entity.marketing.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import javax.persistence.Table;

@EqualsAndHashCode(callSuper = true)
@Table(name = "flash_sale_qualification")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class FlashSaleQualificationEntity extends BaseEntity {

    private String activityCode;
    private String qualificationCode;
    private String qualificationValue;
    private String qualificationValueName;
    private String relation;

    //根据标签指定排除会员 02
    private String isExclude;
}
