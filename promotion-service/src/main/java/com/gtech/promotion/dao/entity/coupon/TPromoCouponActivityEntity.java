/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * <PERSON>ECH MAKES NO REPRESENTATIONS OR WA<PERSON><PERSON>NTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.dao.entity.coupon;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.util.Date;

/**
 * 促销活动券表
 * 
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "promo_coupon_activity")
public class TPromoCouponActivityEntity {

    public static final String C_ID = "id";
    public static final String C_TENANT_CODE = "tenantCode";
    public static final String C_ACTIVITY_CODE = "activityCode";

    // ID
    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    // Tenant code
    @Column(name = "TENANT_CODE")
    private String tenantCode;

    // Org Code
    @Column(name = "ORG_CODE")
    private String orgCode;

    // Activity code
    @Column(name = "ACTIVITY_CODE")
    private String activityCode;

    // 券活动类型：01：优惠券 02:匿名券 (待定)03：优惠码
    @Column(name = "COUPON_TYPE")
    private String couponType;

    // 券投放总数量：0-不限制
    @Column(name = "TOTAL_QUANTITY")
    private Integer totalQuantity;

    @Column(name = "RESERVE_INVENTORY")
    private Integer reserveInventory = 0;

    // 单用户领券上限
    @Column(name = "USER_LIMIT_MAX")
    private Integer userLimitMax;

    // 单用户单日领券上限
    @Column(name = "USER_LIMIT_MAX_DAY")
    private Integer userLimitMaxDay;

    // Create user code.
    @Column(name = "create_user")
    private String createUser;

    // Create time.
    @Column(name = "create_time")
    private Date createTime;

    // Lastest update user.
    @Column(name = "update_user")
    private String updateUser;

    // Lastest update time.
    @Column(name = "update_time")
    private Date updateTime;

    // Logic delete. (0-normal 1-deleted)
    @Column(name = "logic_delete")
    private Integer logicDelete;

}