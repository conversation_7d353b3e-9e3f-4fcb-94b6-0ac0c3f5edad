package com.gtech.promotion.dao.model.marketing;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.Date;

@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
public class TicketReleaseModel extends BaseModel {

    private String releaseCode;
    private Long quality;
    private Long inventory;
    private Long used;
    private String createUser;
    @JSONField(format = "yyyyMMddHHmmss")
    private Date createTime;
}
