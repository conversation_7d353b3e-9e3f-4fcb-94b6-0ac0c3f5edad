/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * <PERSON>ECH MAKES NO REPRESENTATIONS OR WA<PERSON><PERSON>NTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.dao.entity.coupon;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 促销券（内）码表
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "promo_coupon_inner_code")
public class TPromoCouponInnerCodeEntity {

    public static final String C_ID = "id";
    public static final String C_TENANT_CODE = "tenantCode";
    public static final String C_ACTIVITY_CODE = "activityCode";
    public static final String C_COUPON_CODE = "couponCode";
    public static final String C_RELEASE_CODE = "releaseCode";
    public static final String C_STATUS = "status";
    public static final String C_CREATE_TIME = "createTime";
    public static final String C_FROZEN_STATUS = "frozenStatus";
    public static final String C_RECEIVE_START_TIME = "receiveStartTime";
    public static final String C_RECEIVE_END_TIME = "receiveEndTime";
    public static final String C_COUPON_TYPE = "couponType";
    public static final String C_VALID_START_TIME = "validStartTime";
    public static final String C_VALID_END_TIME = "validEndTime";
    public static final String C_PROMO_PW = "promoPassword";


    // ID
    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    // Tenant code
    @Column(name = "TENANT_CODE")
    private String tenantCode;

    // Activity code
    @Column(name = "ACTIVITY_CODE")
    private String activityCode;

    // Coupon release code.
    @Column(name = "RELEASE_CODE")
    private String releaseCode;

    // 券码
    @Column(name = "COUPON_CODE")
    private String couponCode;

    @Column(name = "PROMO_PASSWORD")
    private String promoPassword;


    // 券类型：01-优惠券 02-匿名券 03-优惠码
    @Column(name = "COUPON_TYPE")
    private String couponType;

    // 状态：01-未发放 02-已发放 03-已使用 04-已锁定
    @Column(name = "STATUS")
    private String status;

    // 冻结状态：01-未冻结 02-已冻结
    @Column(name = "FROZEN_STATUS")
    private String frozenStatus;

    // 券面值
    @Column(name = "FACE_VALUE")
    private BigDecimal faceValue;

    // 券面值单位01：金额 02：折扣
    @Column(name = "FACE_UNIT")
    private String faceUnit;

    // 领取标签 01-免费领取、02-活动赠券、03-促销赠券、04-积分换购、05-有价券、06-新人券、07-生日券、08-补偿券、09-推送券、10-其他；
    @Column(name = "TAKE_LABEL")
    private String takeLabel;

    // 领取开始时间(yyyyMMddhhmmss)
    @Column(name = "RECEIVE_START_TIME")
    private String receiveStartTime;

    // 领取结束时间(yyyyMMddhhmmss)
    @Column(name = "RECEIVE_END_TIME")
    private String receiveEndTime;

    // Create user code.
    @Column(name = "create_user")
    private String createUser;

    // Create time.
    @Column(name = "create_time")
    private Date createTime;

    // Lastest update user.
    @Column(name = "update_user")
    private String updateUser;

    // Lastest update time.
    @Column(name = "update_time")
    private Date updateTime;

    // Logic delete. (0-normal 1-deleted)
    @Column(name = "logic_delete")
    private Integer logicDelete;

    // 可用开始时间(yyyyMMddhhmmss)
    @Column(name = "VALID_START_TIME")
    private String validStartTime;

    // 可用结束时间(yyyyMMddhhmmss)
    @Column(name = "VALID_END_TIME")
    private String validEndTime;
}