package com.gtech.promotion.dao.model.activity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * @Description :
 * <AUTHOR> gaojie.li
 * @Date : 2023/6/14 10:10
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class PromoGroupVO implements Comparable<PromoGroupVO>,Serializable {

    private static final long serialVersionUID = 2909708666165253149L;

    private String tenantCode;

    private String domainCode;

	private String groupCode;

    private String groupName;

    private Long priority;

    private String type;

    private String logicDelete;

    private Date createTime;
    private String extParam;

    @Override
    public int compareTo(PromoGroupVO o) {
        if(o.getPriority()>this.getPriority()){
            return 1;
        }else if (o.getPriority().equals(this.getPriority())){
            return 0;
        }else{
            return -1;
        }
    }
}
