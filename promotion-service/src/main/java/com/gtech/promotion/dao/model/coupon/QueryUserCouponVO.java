package com.gtech.promotion.dao.model.coupon;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @Description :
 * <AUTHOR> gaojie.li
 * @Date : 2023/7/25 18:20
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class QueryUserCouponVO {

   private List<String> opsList;
    private String beginTime;
    private String endTime;
    private Integer sort;

    private Integer pageNo;

    private Integer pageCount;

}
