package com.gtech.promotion.dao.entity.marketing;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import javax.persistence.Table;

/**
 * @Description :
 * <AUTHOR> gaojie.li
 * @Date : 2023/6/5 15:35
 */
@EqualsAndHashCode(callSuper = true)
@Table(name = "marketing_group")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class MarketingGroupEntity extends BaseEntity {

    /**
     * 活动编码
     */
    private String activityCode;

    /**
     * 时效字段
     */
    private Integer effectiveHour;


    /**
     * 展示团长价 0 no 1 yes
     */
    private Integer showLeaderPrice;

    /***
     * 成团人数
     */
    private Integer groupSize;

    /**
     * 允许自动成团
     * 默认开， 0开 1关闭
     */
    private Integer autoGroupFlag;


    /**
     * 团长福利，可参加的优惠券活动
     */
    private String leaderBenefits;


    /**
     * 是否封闭团，1是0不是，默认0"
     */
    private Integer closeFlag;

}
