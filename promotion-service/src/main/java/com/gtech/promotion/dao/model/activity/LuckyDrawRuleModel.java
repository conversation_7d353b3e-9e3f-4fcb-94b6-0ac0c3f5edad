package com.gtech.promotion.dao.model.activity;

import lombok.*;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Date 2021/1/26 9:59
 */
@Getter
@Setter
@ToString
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LuckyDrawRuleModel implements Serializable {


    private static final long serialVersionUID = 1893187813208474065L;
    private String admissionRules;

    private String productCode;

    private String activityCode;

    private String productName;

    private BigDecimal buyingQty;

    private Integer earnTicket;

    private Integer maxGivingPerUser;

}
