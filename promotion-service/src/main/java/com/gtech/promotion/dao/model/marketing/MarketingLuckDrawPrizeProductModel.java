package com.gtech.promotion.dao.model.marketing;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class MarketingLuckDrawPrizeProductModel {



    private String domainCode;
    private String tenantCode;
    private String orgCode;
    private String activityCode;
    private String luckyDrawPrizeProductCode;
    private String memberCode;
    private String productCode;
    private String status;
    private String orderId;

}
