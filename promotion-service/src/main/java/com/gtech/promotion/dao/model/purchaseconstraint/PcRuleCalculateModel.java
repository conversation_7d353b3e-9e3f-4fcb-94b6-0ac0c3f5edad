package com.gtech.promotion.dao.model.purchaseconstraint;

import com.gtech.promotion.checker.purchaseconstraint.PurchaseConstraintChecker;
import com.gtech.promotion.dto.cache.PurchaseConstraintCacheDTO;
import com.gtech.promotion.exception.PromotionException;
import com.gtech.promotion.vo.bean.ProductAttribute;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

@Data
public class PcRuleCalculateModel implements Serializable {


    private String tenantCode;

    private String userCode;

    private String orgCode;

    /**
     *
     * true: 正向流程
     * false: 逆向流程 事务回滚
     */
    private Boolean forward;
    /**
     * 计算类型
     * @see com.gtech.promotion.code.purchaseconstraint.PcRuleCalculateTypeEnum
     */
    private String type;
    /**
     * 订单号
     */
    private String orderCode;
    /**
     * 订单状态
     */
    private String orderStatus;
    /**
     * 退货单号
     */
    private String returnCode;
    /**
     * 订单日期
     */
    private Date orderDate;
    /**
     * 增量商品集合
     */
    private List<IncrementProduct> incrementProducts;
    /**
     * 资格
     */
    private Map<String, List<String>> qualifications;

    /**
     * 所有限购集合
     */
    private List<PurchaseConstraintCacheDTO> pcCacheDTOs;

    public void valid() {
        if (StringUtils.isBlank(this.getTenantCode())) {
            throw new PromotionException(PurchaseConstraintChecker.NOT_NULL_TENANT_CODE);
        }
        if (StringUtils.isBlank(userCode)) {
            throw new PromotionException(PurchaseConstraintChecker.USER_CODE_NOT_EMPTY);
        }
        if (this.forward == null) {
            throw new PromotionException(PurchaseConstraintChecker.FORWARD_NOT_EMPTY);
        }
        if (StringUtils.isBlank(this.type)) {
            throw new PromotionException(PurchaseConstraintChecker.TYPE_NOT_EMPTY);
        }
        if (CollectionUtils.isEmpty(incrementProducts)) {
            throw new PromotionException(PurchaseConstraintChecker.INCREMENT_PRODUCTS_NOT_EMPTY);
        }
    }

    @Data
    public static class IncrementProduct implements Serializable {
        // 商品编码
        private String productCode;
        // sku编码
        private String skuCode;
        // sku折扣总价
        private BigDecimal sellAmount;
        // sku总数
        private Integer skuCount;
        // Product category code list.
        private List<String> categoryCodes;
        // Product brand code.
        private String brandCode;
        // Product attribute information list.
        private List<ProductAttribute> attributes;
        // Product tag code
        private String productTag;
        //Product attribute information list.
        private List<ProductAttribute> spuAttributes;
        // 命中的限购编码; 这个限购编码可能只有订单维度,没有累计规则
        private String hitPcCode;
        private List<String> hitPcCodes;

        /**
         * 高级价格限购标识，默认0，非高级价格限购
         */
        private Integer priceSetting;
    }

}
