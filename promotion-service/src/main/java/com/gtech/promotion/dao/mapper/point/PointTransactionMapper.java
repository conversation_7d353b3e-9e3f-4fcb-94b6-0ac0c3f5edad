package com.gtech.promotion.dao.mapper.point;

import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import com.gtech.commons.dao.mapper.GTechBaseMapper;
import com.gtech.promotion.dao.entity.point.PointTransactionEntity;
import com.gtech.promotion.vo.result.point.PointAccountCampaignResult;
import com.gtech.promotion.vo.result.point.PointTransactionResult;

import tk.mybatis.mapper.common.Mapper;

@Repository
public interface PointTransactionMapper
		extends Mapper<PointTransactionEntity>, GTechBaseMapper<PointTransactionEntity> {

	PointTransactionResult queryMemberTransaction(String tenantCode, String transactionSn);

	List<PointTransactionEntity> query(Map<String, Object> parameters);

	Integer countEffectivePoint(Map<String, Object> param);

	List<PointAccountCampaignResult.CampaignBalance> getCountPointTransactionCampaign(
			@Param("tenantCode") String tenantCode, @Param("accountCode") String accountCode,
			@Param("campaignCodes") List<String> campaignCodes);

	List<PointTransactionEntity> selectEndTime(@Param("maxId") Long maxId, @Param("limit") int limit,
			@Param("nowTime") String nowTime);

}