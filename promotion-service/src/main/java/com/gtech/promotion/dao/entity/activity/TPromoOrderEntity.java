/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * <PERSON>ECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.dao.entity.activity;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**   
 * <功能描述>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "promo_order")
public class TPromoOrderEntity {

    // ID
    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    // Tenant code
    @Column(name = "TENANT_CODE")
    private String tenantCode;

    // 促销订单编号
    @Column(name = "PROMO_ORDER_NO")
    private String promoOrderNo;

    // 促销订单序号（初始序号为1，自然递增）
    @Column(name = "PROMO_ORDER_SEQ")
    private Integer promoOrderSeq;

    // 促销订单时间: yyyyMMddhhmmss
    @Column(name = "PROMO_ORDER_TIME")
    private String promoOrderTime;

    // 销售订单编号(促销计算用业务单号)
    @Column(name = "ORDER_ID")
    private String orderId;

    // 销售订单状态：01-未付款 02-已付款 03-已取消
    @Column(name = "ORDER_STATUS")
    private String orderStatus;

    // Store organization code.
    @Column(name = "ORG_CODE")
    private String orgCode;

    // 会员编码
    @Column(name = "USER_CODE")
    private String userCode;

    // Create user code.
    @Column(name="create_user")
    private String createUser;

    // Create time.
    @Column(name="create_time")
    private Date createTime;

    // Lastest update user.
    @Column(name="update_user")
    private String updateUser;

    // Lastest update time.
    @Column(name="update_time")
    private Date updateTime;

    // Logic delete. (0-normal 1-deleted)
    @Column(name="logic_delete")
    private Integer logicDelete;

}