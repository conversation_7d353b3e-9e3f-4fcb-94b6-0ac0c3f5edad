package com.gtech.promotion.dao.model.marketing;

import com.alibaba.fastjson.annotation.JSONField;
import com.gtech.commons.utils.DateUtil;
import com.gtech.promotion.code.activity.ActivityStatusEnum;
import com.gtech.promotion.dao.model.activity.ActivityPeriodModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CacheMarketingModel extends BaseModel {

    private String activityType;
    private String opsType;
    private String groupCode;
    private String activityName;
    private String activityLabel;
    private String activityDesc;
    private String activityEnd;
    private String activityShortDesc;
    private String activityBegin;
    private String ribbonPosition;
    // 活动状态：01-待提交 02-审核中 03-已拒绝 04-已生效 05-已结束 06-暂停中 07-已终止
    private String activityStatus;
    private String backgroundImage;
    private String ribbonImage;
    private String activityUrl;
    private String ribbonText;
    private String sponsors;
    private String coolDown;
    private String warmBegin;
    private String warmEnd;
    private String preSalePayType;
    private String shippingTime;
    private String importNo;
    private Date createTime;
    private ActivityPeriodModel activityPeriod;

    //sku 01 ,spu 02. default 01
    private String selectProductType;

    private List<MarketingLanguageModel> languages;

    /**
     * 判断是否需要做过期处理
     */
    @JSONField(serialize = false)
    public boolean isNeedToDoExpire() {

        return ActivityStatusEnum.EFFECTIVE.equalsCode(this.activityStatus)
                && DateUtil.parseDate(this.activityEnd, DateUtil.FORMAT_YYYYMMDDHHMISS_14).getTime() <= System.currentTimeMillis();
    }

}
