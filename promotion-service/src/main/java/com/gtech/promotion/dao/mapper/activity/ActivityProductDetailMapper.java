/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.dao.mapper.activity;

import com.gtech.commons.dao.mapper.GTechBaseMapper;
import com.gtech.promotion.dao.entity.activity.ActivityProductDetailEntity;
import com.gtech.promotion.dto.cache.ProductSkuDetailDTO;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

//@Mapper
public interface ActivityProductDetailMapper extends GTechBaseMapper<ActivityProductDetailEntity> {

    @Select("SELECT DISTINCT SEQ_NUM FROM promo_activity_product_detail WHERE ACTIVITY_CODE = #{activityCode}")
    List<Integer> getSeqNumDistinct111(@Param("activityCode") String activityCode);

    @Select("INSERT INTO promo_activity_product_detail (ACTIVITY_CODE, TENANT_CODE, PRODUCT_CODE, SPU_NAME, SKU_CODE, SKU_NAME, ORG_CODE, ORG_NAME, COMBINE_SKU_CODE, COMBINE_SKU_NAME,"
                    + " PROMO_PRICE, SEQ_NUM, UPDATE_USER, UPDATE_TIME, CREATE_USER, CREATE_TIME, IMPORT_TYPE, LOGIC_DELETE)"
                    + " SELECT #{newActivityCode}, TENANT_CODE, PRODUCT_CODE, SPU_NAME, SKU_CODE, SKU_NAME, ORG_CODE, ORG_NAME, COMBINE_SKU_CODE, COMBINE_SKU_NAME, PROMO_PRICE, SEQ_NUM, UPDATE_USER,"
                    + " current_timestamp(), CREATE_USER, current_timestamp(), IMPORT_TYPE, LOGIC_DELETE "
                    + " FROM promo_activity_product_detail WHERE ACTIVITY_CODE = #{oldActivityCode}")
    Integer copyProductSku111(@Param("oldActivityCode") String oldActivityCode, @Param("newActivityCode") String newActivityCode);

    @Select("<script> SELECT PRODUCT_CODE, COMBINE_SKU_CODE FROM promo_activity_product_detail where ACTIVITY_CODE = #{activityCode}"
                    + " <if test=\"seqNum != null\"> and SEQ_NUM = #{seqNum} </if> "
                    + " group by COMBINE_SKU_CODE, PRODUCT_CODE</script>")
    List<ActivityProductDetailEntity> selectProductSku111(@Param("activityCode") String activityCode, @Param("seqNum") Integer seqNum);

    @Select("<script> SELECT SEQ_NUM FROM promo_activity_product_detail where ACTIVITY_CODE = #{activityCode} group by SEQ_NUM </script>")
    List<Integer> getSeqNumCount111(@Param("activityCode") String activityCode);

    @Select("<script> " +
            "select a.* from promo_activity_product_detail a,(select max(id) id from promo_activity_product_detail where TENANT_CODE = #{tenantCode} and ACTIVITY_CODE in " +
            " <foreach collection=\"activityCodes\" item=\"activityCode\" index=\"index\" open=\"(\" close=\")\" separator=\",\">\n" +
            "    #{activityCode}\n" +
            "</foreach>" +
            " group by ACTIVITY_CODE) b\n" +
            "where a.TENANT_CODE =  #{tenantCode} and a.id = b.id" +
            "</script>")
    List<ProductSkuDetailDTO> queryOneGroupByActivityCodes(@Param("tenantCode") String tenantCode, @Param("activityCodes") List<String> activityCodes);
}