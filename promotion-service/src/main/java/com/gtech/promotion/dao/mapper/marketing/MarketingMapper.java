package com.gtech.promotion.dao.mapper.marketing;

import com.gtech.commons.dao.mapper.GTechBaseMapper;
import com.gtech.promotion.dao.entity.marketing.MarketingEntity;
import com.gtech.promotion.vo.param.marketing.flashsale.FlashSaleQueryListParam;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

//@Mapper
public interface MarketingMapper extends GTechBaseMapper<MarketingEntity> {

    @Select("<script>" +
            "SELECT id, " +
            " ACTIVITY_CODE AS activityCode, " +
            " ACTIVITY_TYPE AS activityType, " +
            " OPS_TYPE AS opsType, " +
            " ACTIVITY_NAME AS activityName, " +
            " ACTIVITY_BEGIN AS activityBegin, " +
            " ACTIVITY_END AS activityEnd, " +
            " ACTIVITY_STATUS AS activityStatus, " +
            " ACTIVITY_URL AS activityUrl, " +
            " sponsors, " +
            " BACKGROUND_IMAGE AS backgroundImage, " +
            " RIBBON_IMAGE AS ribbonImage, " +
            " RIBBON_POSITION AS ribbonPosition, " +
            " RIBBON_TEXT AS ribbonText, " +
            " COOL_DOWN AS coolDown, " +
            " WARM_BEGIN AS warmBegin, " +
            " WARM_END AS warmEnd  " +
            "FROM " +
            " `marketing` t1  " +
            "WHERE " +
            " t1.DOMAIN_CODE = #{domainCode} " +
            " and t1.TENANT_CODE = #{tenantCode} " +
            " and t1.ACTIVITY_TYPE = '04' " +
            " <if test=\"startTime != null and startTime != ''\"> and t1.ACTIVITY_BEGIN &gt;= #{startTime} </if> " +
            " <if test=\"endTime != null and endTime != ''\"> and t1.ACTIVITY_END &lt;= #{endTime} </if> " +
            " <if test=\"activityStatus != null and activityStatus != ''\"> and t1.ACTIVITY_STATUS = #{activityStatus} </if> " +
            " <if test=\"activityStatus != null and activityStatus = '04'\"> and t1.ACTIVITY_END &gt; DATE_FORMAT(CURRENT_TIMESTAMP, '%Y%m%d%H%i%s') </if> " +
            " <if test=\"sponsors != null and sponsors != ''\"> and t1.ACTIVITY_END = #{sponsors} </if> " +
            " and " +
            " NOT EXISTS ( SELECT id FROM flash_sale_store t2 WHERE t2.ACTIVITY_CODE = t1.ACTIVITY_CODE ) UNION " +
            "SELECT id, " +
            " ACTIVITY_CODE AS activityCode, " +
            " ACTIVITY_TYPE AS activityType, " +
            " OPS_TYPE AS opsType, " +
            " ACTIVITY_NAME AS activityName, " +
            " ACTIVITY_BEGIN AS activityBegin, " +
            " ACTIVITY_END AS activityEnd, " +
            " ACTIVITY_STATUS AS activityStatus, " +
            " ACTIVITY_URL AS activityUrl, " +
            " sponsors, " +
            " BACKGROUND_IMAGE AS backgroundImage, " +
            " RIBBON_IMAGE AS ribbonImage, " +
            " RIBBON_POSITION AS ribbonPosition, " +
            " RIBBON_TEXT AS ribbonText, " +
            " COOL_DOWN AS coolDown, " +
            " WARM_BEGIN AS warmBegin, " +
            " WARM_END AS warmEnd  " +
            "FROM " +
            " `marketing` t1  " +
            "WHERE " +
            " t1.DOMAIN_CODE = #{domainCode} " +
            " and t1.TENANT_CODE = #{tenantCode} " +
            " and t1.ACTIVITY_TYPE = '04' " +
            " <if test=\"startTime != null and startTime != ''\"> and t1.ACTIVITY_BEGIN &gt;= #{startTime} </if> " +
            " <if test=\"endTime != null and endTime != ''\"> and t1.ACTIVITY_END &lt;= #{endTime} </if> " +
            " <if test=\"activityStatus != null and activityStatus != ''\"> and t1.ACTIVITY_STATUS = #{activityStatus} </if> " +
            " <if test=\"activityStatus != null and activityStatus = '04'\"> and t1.ACTIVITY_END &gt; DATE_FORMAT(CURRENT_TIMESTAMP, '%Y%m%d%H%i%s') </if> " +
            " <if test=\"sponsors != null and sponsors != ''\"> and t1.ACTIVITY_END = #{sponsors} </if> " +
            " and " +
            " EXISTS ( " +
            " SELECT " +
            "  id  " +
            " FROM " +
            "  flash_sale_store t3  " +
            " WHERE " +
            " t3.ACTIVITY_CODE = t1.ACTIVITY_CODE  " +
            " AND t3.ORG_CODE = #{orgCode})" +
            " order by id desc " +
            "</script>")
    List<MarketingEntity> selectFlashSaleByStore(FlashSaleQueryListParam param);


    @Update("<script>" +
            " update  marketing set GROUP_CODE = OPS_TYPE where TENANT_CODE = #{tenantCode}  AND DOMAIN_CODE= #{domainCode}" +
            "</script>")
    void initMarketingGroupCode(@Param("domainCode")String domainCode, @Param("tenantCode") String tenantCode);
}
