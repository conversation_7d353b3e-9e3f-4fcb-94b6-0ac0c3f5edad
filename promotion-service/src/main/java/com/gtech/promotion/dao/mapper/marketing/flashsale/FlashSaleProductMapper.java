package com.gtech.promotion.dao.mapper.marketing.flashsale;

import com.gtech.commons.dao.mapper.GTechBaseMapper;
import com.gtech.promotion.dao.entity.marketing.flashsale.FlashSaleProductEntity;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;
import tk.mybatis.mapper.common.ExampleMapper;

//@Mapper
public interface FlashSaleProductMapper extends GTechBaseMapper<FlashSaleProductEntity>, ExampleMapper<FlashSaleProductEntity> {
    @Update(" update flash_sale_product set SKU_INVENTORY = SKU_INVENTORY - #{inventory}"
            + " where ACTIVITY_CODE = #{activityCode} and SKU_CODE = #{skuCode} and SKU_INVENTORY >= #{inventory}")
    int dealInventory(@Param("activityCode") String activityCode, @Param("skuCode") String skuCode, @Param("inventory") Integer promoQuantity);

    @Update(" update flash_sale_product set SKU_INVENTORY = SKU_INVENTORY - #{inventory}"
            + " where ACTIVITY_CODE = #{activityCode} and PRODUCT_CODE = #{productCode} and SKU_INVENTORY >= #{inventory}")
    int dealInventoryBySpu(@Param("activityCode") String activityCode, @Param("productCode") String spuCode, @Param("inventory") Integer promoQuantity);
}
