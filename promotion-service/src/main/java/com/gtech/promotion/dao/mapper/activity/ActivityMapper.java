/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * <PERSON>ECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.dao.mapper.activity;

import java.util.List;

import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import com.gtech.commons.dao.mapper.GTechBaseMapper;
import com.gtech.promotion.dao.entity.activity.ActivityEntity;
import com.gtech.promotion.dto.in.activity.TPromoActivityListInDTO;
import com.gtech.promotion.dto.out.activity.TPromoActivityOutDTO;
import com.gtech.promotion.vo.param.activity.QueryListParam;
import com.gtech.promotion.vo.result.activity.QueryListResult;

/**   
 * 活动DAO类
 */
//@Mapper
public interface ActivityMapper extends GTechBaseMapper<ActivityEntity> {

    @Select("<script> " +
            "SELECT " +
            "  t1.`ACTIVITY_CODE`, t1.`ACTIVITY_BEGIN`, t1.`ACTIVITY_DESC`, t1.`ACTIVITY_END`, t1.`ACTIVITY_STATUS`, t1.`ACTIVITY_LABEL`, t1.`ACTIVITY_NAME`, t1.`ACTIVITY_REMARK`, " +
            "  t1.`ACTIVITY_TYPE`, t1.`ACTIVITY_URL`, t1.`BACKGROUND_IMAGE`, t1.`COOL_DOWN`, t1.`OPS_TYPE`,t1.`PRODUCT_SELECTION_TYPE`, t1.`SPONSORS`, t1.`WARM_BEGIN`, t1.RIBBON_IMAGE, t1.RIBBON_POSITION, t1.RIBBON_TEXT " +
            " FROM " +
            "  `promo_activity` t1 " +
            " WHERE t1.ACTIVITY_STATUS = #{activityStatus} AND t1.`TENANT_CODE` = #{tenantCode} " +
            " <if test=\"activityType != null and activityType != ''\"> and t1.ACTIVITY_TYPE = #{activityType} </if> " +
            " AND (T1.STORE_TYPE = '00' OR EXISTS " +
            "  (SELECT " +
            "    id " +
            "  FROM" +
            "    `promo_activity_store` t2 " +
            "  WHERE t2.`ACTIVITY_CODE` = t1.`ACTIVITY_CODE` " +
            "    AND t2.`TENANT_CODE` = t1.`TENANT_CODE` " +
            "    AND t2.`TENANT_CODE` = #{tenantCode} " +
            "    AND t2.`ORG_CODE` = #{orgCode}))" +
            "  order by t1.id desc " +
            "</script> ")
    List<TPromoActivityOutDTO> queryPromoListByStore(@Param("tenantCode")String tenantCode, @Param("orgCode")String orgCode, @Param("activityStatus")String activityStatus, @Param("activityType")String activityType);

    @Select("<script>" +
            "SELECT" +
            " t1.CREATE_TIME," +
            " t1.ACTIVITY_CODE," +
            " t1.ACTIVITY_BEGIN," +
            " t1.ACTIVITY_END," +
            " t1.ACTIVITY_STATUS," +
            " t1.ACTIVITY_NAME," +
            " t1.ACTIVITY_TYPE," +
            " t1.OPS_TYPE, " +
            " t1.SPONSORS, t1.RIBBON_IMAGE, t1.RIBBON_POSITION, t1.RIBBON_TEXT " +
            "FROM " +
            " promo_activity t1 " +
            "WHERE " +
            " t1.ACTIVITY_STATUS = '04' " +
            "AND t1.TENANT_CODE = #{tenantCode} " +
            " <if test=\"activityTypes != null\"> " +
            "   and t1.ACTIVITY_TYPE in " +
            "   <foreach collection=\"activityTypes\" item=\"activityType\" index=\"index\" open=\"(\" close=\")\" separator=\",\"> " +
            "        #{activityType} " +
            "   </foreach>" +
            " </if> " +
            " <if test=\"activityCode != null and activityCode != ''\"> " +
            "   and t1.ACTIVITY_CODE = #{activityCode} " +
            " </if> " +
            " <if test=\"orgCode != null and orgCode != ''\"> " +
            "   and t1.ORG_CODE = #{orgCode} " +
            " </if> " +
            " <if test=\"activityName != null and activityName != ''\"> " +
            "   and t1.ACTIVITY_NAME like concat(concat('%', #{activityName}), '%') " +
            " </if> " +

            " <if test=\"warmBeginFrom != null and warmBeginFrom != ''\"> AND t1.WARM_BEGIN &gt;= #{warmBeginFrom} </if>  " +
            " <if test=\"warmBeginTo != null and warmBeginTo != ''\"> AND t1.WARM_BEGIN &lt;= #{warmBeginTo} </if> " +
            " <if test=\"warmEndFrom != null and warmEndFrom != ''\"> AND t1.WARM_END &gt;= #{warmEndFrom} </if> " +
            " <if test=\"warmEndTo != null and warmEndTo != ''\"> AND t1.WARM_END &lt;= #{warmEndTo} </if> " +

            "UNION " +
            " SELECT " +
            "  t2.CREATE_TIME, " +
            "  t2.ACTIVITY_CODE, " +
            "  t2.ACTIVITY_BEGIN, " +
            "  t2.ACTIVITY_END, " +
            "  t2.ACTIVITY_STATUS, " +
            "  t2.ACTIVITY_NAME, " +
            "  t2.ACTIVITY_TYPE, " +
            "  t2.OPS_TYPE, " +
            "  t2.SPONSORS, t2.RIBBON_IMAGE, t2.RIBBON_POSITION, t2.RIBBON_TEXT " +
            " FROM" +
            "  marketing t2 " +
            " WHERE" +
            "  t2.ACTIVITY_STATUS = '04' " +
            " AND t2.TENANT_CODE = #{tenantCode} " +
            " <if test=\"activityTypes != null\"> " +
            "   and t2.ACTIVITY_TYPE in " +
            "   <foreach collection=\"activityTypes\" item=\"activityType\" index=\"index\" open=\"(\" close=\")\" separator=\",\"> " +
            "        #{activityType} " +
            "   </foreach>" +
            " </if> " +
            " <if test=\"activityCode != null and activityCode != ''\"> " +
            "   and t2.ACTIVITY_CODE = #{activityCode} " +
            " </if> " +
            " <if test=\"orgCode != null and orgCode != ''\"> " +
            "   and t2.ORG_CODE = #{orgCode} " +
            " </if> " +
            " <if test=\"activityName != null and activityName != ''\"> " +
            "   and t2.ACTIVITY_NAME like concat(concat('%', #{activityName}), '%') " +
            " </if> " +
            " <if test=\"warmBeginFrom != null and warmBeginFrom != ''\"> AND t2.WARM_BEGIN &gt;= #{warmBeginFrom} </if>  " +
            " <if test=\"warmBeginTo != null and warmBeginTo != ''\"> AND t2.WARM_BEGIN &lt;= #{warmBeginTo} </if> " +
            " <if test=\"warmEndFrom != null and warmEndFrom != ''\"> AND t2.WARM_END &gt;= #{warmEndFrom} </if> " +
            " <if test=\"warmEndTo != null and warmEndTo != ''\"> AND t2.WARM_END &lt;= #{warmEndTo} </if> " +
            " ORDER BY CREATE_TIME DESC " +
            "</script>")
    List<QueryListResult> queryList(QueryListParam param);

    @Select("<script> " +
            "SELECT " +
            " ID as id, " +
            " DOMAIN_CODE as domainCode, " +
            " TENANT_CODE as tenantCode, " +
            " ACTIVITY_CODE as activityCode, " +
            " PROMOTION_CATEGORY as promotionCategory, " +
            " ACTIVITY_TYPE as activityType, " +
            " ACTIVITY_LABEL as activityLabel, " +
            " ACTIVITY_NAME as activityName, " +
            " SPONSORS as sponsors, " +
            " TEMPLATE_CODE as templateCode, " +
            " INCENTIVE_LIMITED_FLAG AS incentiveLimitedFlag, " +
            " ACTIVITY_BEGIN AS activityBegin, " +
            " ACTIVITY_END AS activityEnd, " +
            " PERIOD_TYPE AS periodType, " +
            " PRODUCT_SELECTION_TYPE AS productSelectionType, " +
            " ITEM_SCOPE_TYPE AS itemScopeType, " +
            " STORE_TYPE AS storeType, " +
            " ACTIVITY_STATUS AS activityStatus, " +
            " ACTIVITY_URL AS activityUrl, " +
            " WARM_BEGIN AS warmBegin, " +
            " WARM_END AS warmEnd, " +
            " ACTIVITY_DESC AS activityDesc, " +
            " ACTIVITY_EXPR AS activityExpr, " +
            " UPDATE_USER AS updateUser, " +
            " UPDATE_TIME AS updateTime, " +
            " CREATE_USER AS createUser, " +
            " CREATE_TIME AS createTime, " +
            " LOGIC_DELETE AS logicDelete, " +
            " AUDIT_USER AS auditUser, " +
            " ACTIVITY_REMARK AS activityRemark, " +
            " priority, " +
            " SHOW_FLAG AS showFlag, " +
            " OPS_TYPE AS opsType, " +
            " COOL_DOWN AS coolDown, " +
            " BACKGROUND_IMAGE AS backgroundImage, " +
            " PRODUCT_CONDITION AS productCondition, " +
            " EXT_IMAGES AS extImages " +
            "FROM " +
            " promo_activity t1  " +
            "WHERE " +
            " t1.DOMAIN_CODE = #{domainCode}  " +
            " AND t1.TENANT_CODE = #{tenantCode}  " +
            "  <if test=\"orgCode != null and orgCode != ''\"> AND t1.ORG_CODE = #{orgCode} </if> " +
            "  <if test=\"activityType != null and activityType != ''\"> AND t1.ACTIVITY_TYPE = #{activityType} </if> " +
            " <if test=\"activityName != null and activityName != ''\"> AND t1.ACTIVITY_NAME LIKE concat('%', #{activityName}, '%')  </if> " +
            " <if test=\"opsType != null and opsType != ''\"> " +
            "   AND T1.OPS_TYPE IN " +
            " <foreach item=\"item\" index=\"index\" collection=\"opsTypeList\" open=\"(\" separator=\",\" close=\")\"> #{item} </foreach> " +
            " </if> " +
            " <if test=\"activityBeginFrom != null and activityBeginFrom != ''\"> AND t1.ACTIVITY_BEGIN &gt;= #{activityBeginFrom} </if>  " +
            " <if test=\"activityBeginTo != null and activityBeginTo != ''\"> AND t1.ACTIVITY_BEGIN &lt;= #{activityBeginTo} </if> " +
            " <if test=\"activityEndFrom != null and activityEndFrom != ''\"> AND t1.ACTIVITY_END &gt;= #{activityEndFrom} </if> " +
            " <if test=\"activityEndTo != null and activityEndTo != ''\"> AND t1.ACTIVITY_END &lt;= #{activityEndTo} </if> " +

            " <if test=\"warmBeginFrom != null and warmBeginFrom != ''\"> AND t1.WARM_BEGIN &gt;= #{warmBeginFrom} </if>  " +
            " <if test=\"warmBeginTo != null and warmBeginTo != ''\"> AND t1.WARM_BEGIN &lt;= #{warmBeginTo} </if> " +
            " <if test=\"warmEndFrom != null and warmEndFrom != ''\"> AND t1.WARM_END &gt;= #{warmEndFrom} </if> " +
            " <if test=\"warmEndTo != null and warmEndTo != ''\"> AND t1.WARM_END &lt;= #{warmEndTo} </if> " +

            " <if test=\"createTimeFrom != null and createTimeFrom != ''\"> AND t1.CREATE_TIME &gt;= #{createTimeFrom} </if> " +
            " <if test=\"createTimeTo != null and createTimeTo != ''\"> AND t1.CREATE_TIME &lt;= #{createTimeTo} </if> " +
            " <if test=\"activityCode != null and activityCode != ''\"> AND t1.ACTIVITY_CODE = #{activityCode} </if> " +
            " <if test=\"sponsors != null and sponsors != ''\"> AND t1.SPONSORS like concat(#{sponsors}, '%') </if> " +
            " <if test=\"activityStatus != null and activityStatus != ''\"> " +
            " AND t1.ACTIVITY_STATUS IN " +
            " <foreach item=\"item\" index=\"index\" collection=\"activityStatusList\" open=\"(\" separator=\",\" close=\")\"> #{item} </foreach> " +
            " </if> " +
            " AND EXISTS ( SELECT id FROM promo_qualification t2 WHERE t2.ACTIVITY_CODE = t1.ACTIVITY_CODE AND t2.QUALIFICATION_CODE = 'channelCode' AND t2.QUALIFICATION_VALUE = #{channelCode} ) " +
            " UNION " +
            "SELECT " +
            " ID as id, " +
            " DOMAIN_CODE as domainCode, " +
            " TENANT_CODE as tenantCode, " +
            " ACTIVITY_CODE as activityCode, " +
            " ACTIVITY_TYPE as activityType, " +
            " ACTIVITY_LABEL as activityLabel, " +
            " ACTIVITY_NAME as activityName, " +
            " SPONSORS as sponsors, " +
            " TEMPLATE_CODE as templateCode, " +
            " INCENTIVE_LIMITED_FLAG AS incentiveLimitedFlag, " +
            " ACTIVITY_BEGIN AS activityBegin, " +
            " ACTIVITY_END AS activityEnd, " +
            " PERIOD_TYPE AS periodType, " +
            " PRODUCT_SELECTION_TYPE AS productSelectionType, " +
            " ITEM_SCOPE_TYPE AS itemScopeType, " +
            " STORE_TYPE AS storeType, " +
            " ACTIVITY_STATUS AS activityStatus, " +
            " ACTIVITY_URL AS activityUrl, " +
            " WARM_BEGIN AS warmBegin, " +
            " WARM_END AS warmEnd, " +
            " ACTIVITY_DESC AS activityDesc, " +
            " ACTIVITY_EXPR AS activityExpr, " +
            " UPDATE_USER AS updateUser, " +
            " UPDATE_TIME AS updateTime, " +
            " CREATE_USER AS createUser, " +
            " CREATE_TIME AS createTime, " +
            " LOGIC_DELETE AS logicDelete, " +
            " AUDIT_USER AS auditUser, " +
            " ACTIVITY_REMARK AS activityRemark, " +
            " priority, " +
            " SHOW_FLAG AS showFlag, " +
            " OPS_TYPE AS opsType, " +
            " COOL_DOWN AS coolDown, " +
            " BACKGROUND_IMAGE AS backgroundImage, " +
            " PRODUCT_CONDITION AS productCondition " +
            "FROM " +
            " promo_activity t1  " +
            "WHERE " +
            " t1.DOMAIN_CODE = #{domainCode}  " +
            " AND t1.TENANT_CODE = #{tenantCode}  " +
            "  <if test=\"activityType != null and activityType != ''\"> AND t1.ACTIVITY_TYPE = #{activityType} </if> " +
            " <if test=\"activityName != null and activityName != ''\"> AND t1.ACTIVITY_NAME LIKE concat('%', #{activityName}, '%')  </if> " +
            " <if test=\"opsType != null and opsType != ''\"> " +
            "   AND T1.OPS_TYPE IN " +
            " <foreach item=\"item\" index=\"index\" collection=\"opsTypeList\" open=\"(\" separator=\",\" close=\")\"> #{item} </foreach> " +
            " </if> " +
            " <if test=\"activityBeginFrom != null and activityBeginFrom != ''\"> AND t1.ACTIVITY_BEGIN &gt;= #{activityBeginFrom} </if>  " +
            " <if test=\"activityBeginTo != null and activityBeginTo != ''\"> AND t1.ACTIVITY_BEGIN &lt;= #{activityBeginTo} </if> " +
            " <if test=\"activityEndFrom != null and activityEndFrom != ''\"> AND t1.ACTIVITY_END &gt;= #{activityEndFrom} </if> " +
            " <if test=\"activityEndTo != null and activityEndTo != ''\"> AND t1.ACTIVITY_END &lt;= #{activityEndTo} </if> " +

            " <if test=\"warmBeginFrom != null and warmBeginFrom != ''\"> AND t1.WARM_BEGIN &gt;= #{warmBeginFrom} </if>  " +
            " <if test=\"warmBeginTo != null and warmBeginTo != ''\"> AND t1.WARM_BEGIN &lt;= #{warmBeginTo} </if> " +
            " <if test=\"warmEndFrom != null and warmEndFrom != ''\"> AND t1.WARM_END &gt;= #{warmEndFrom} </if> " +
            " <if test=\"warmEndTo != null and warmEndTo != ''\"> AND t1.WARM_END &lt;= #{warmEndTo} </if> " +

            " <if test=\"createTimeFrom != null and createTimeFrom != ''\"> AND t1.CREATE_TIME &gt;= #{createTimeFrom} </if> " +
            " <if test=\"createTimeTo != null and createTimeTo != ''\"> AND t1.CREATE_TIME &lt;= #{createTimeTo} </if> " +
            " <if test=\"activityCode != null and activityCode != ''\"> AND t1.ACTIVITY_CODE = #{activityCode} </if> " +
            " <if test=\"sponsors != null and sponsors != ''\"> AND t1.SPONSORS like concat(#{sponsors}, '%') </if> " +
            " <if test=\"activityStatus != null and activityStatus != ''\"> " +
            " AND t1.ACTIVITY_STATUS IN " +
            " <foreach item=\"item\" index=\"index\" collection=\"activityStatusList\" open=\"(\" separator=\",\" close=\")\"> #{item} </foreach> " +
            " </if> " +
            " AND NOT EXISTS ( SELECT id FROM promo_qualification t2 WHERE t2.ACTIVITY_CODE = t1.ACTIVITY_CODE AND t2.QUALIFICATION_CODE = 'channelCode' )  " +
            "ORDER BY " +
            " createTime DESC" +
            "</script>")
    List<ActivityEntity> selectByConditionAndChannel(TPromoActivityListInDTO inDTO);


    @Update("<script>" +
            " update  promo_activity set PROMOTION_CATEGORY = NULL,PROMOTION_CATEGORY_NAME = NULL where TENANT_CODE = #{tenantCode}  AND PROMOTION_CATEGORY= #{promotionCategory}" +
            "</script>")
    int updatePromotionCategoryNull(@Param("tenantCode") String tenantCode,@Param("promotionCategory")String promotionCategory);

    @Update("<script>" +
            " update  promo_activity set GROUP_CODE = OPS_TYPE where TENANT_CODE = #{tenantCode}  AND DOMAIN_CODE= #{domainCode}" +
            "</script>")
    void initActivityGroupCode(@Param("domainCode")String domainCode, @Param("tenantCode") String tenantCode);

	@Select("select tenant_code, activity_code from promo_activity where ACTIVITY_STATUS = '04' and ACTIVITY_TYPE = '02' and INCENTIVE_LIMITED_FLAG = '01'")
	List<ActivityEntity> queryActivityLimitTenant();
}