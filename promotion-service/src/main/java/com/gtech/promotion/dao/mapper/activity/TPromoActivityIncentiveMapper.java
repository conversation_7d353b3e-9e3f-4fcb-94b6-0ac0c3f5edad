/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WA<PERSON>RANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.dao.mapper.activity;

import com.gtech.commons.dao.mapper.GTechBaseMapper;
import com.gtech.promotion.dao.entity.activity.TPromoActivityIncentiveEntity;
import com.gtech.promotion.dto.in.activity.ActivityTenantInDTO;
import org.apache.ibatis.annotations.Select;

//@Mapper
public interface TPromoActivityIncentiveMapper extends GTechBaseMapper<TPromoActivityIncentiveEntity>{
    
    
    @Select("<script> SELECT "  +
                   "SUM(i.INCENTIVE_AMOUNT),"+
                    "    i.INCENTIVE_AMOUNT as incentiveAmount" +
                    "    FROM" +
                    "    promo_activity_incentive i,"+
                    "    promo_order o "+
                    " WHERE  o.ID = i.PROMO_ORDER_ID" +
                    " AND o.ORDER_STATUS = '02'"+
                    "</script>")
    double queryDiscountMoneyTotal();
    
    @Select("<script> SELECT "  +
                     "    ifNULL(SUM(i.INCENTIVE_AMOUNT),'0.0'),"+
                     "    i.INCENTIVE_AMOUNT as incentiveMoneyTotal" +
                     "    FROM" +
                     "    promo_activity_incentive i,"+
                     "    promo_order o "+
                     " WHERE  o.ID = i.PROMO_ORDER_ID" +
                     " AND o.ORDER_STATUS = '02'"+
                     "AND o.TENANT_CODE = #{tenantCode}"+
            "       <if test=\"startTime != null and startTime !='' and endTime != null and endTime !='' \"> " + 
            "        AND i.UPDATE_TIME &gt;= #{startTime}" +
            "        AND i.UPDATE_TIME &lt;= #{endTime}" +
            "       </if> " + 
            "</script>")
     double getPayOrderMoneyTotalMapper(ActivityTenantInDTO tenantInDTO);
    
}