package com.gtech.promotion.dao.entity.activity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2021/1/25 17:58
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "lucky_draw_incentive_limited")
public class LuckyDrawIncentiveLimitedEntity {

    // ID
    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    // Tenant code
    @Column(name = "TENANT_CODE")
    private String tenantCode;

    // Activity code
    @Column(name = "ACTIVITY_CODE")
    private String activityCode;

    // 限制条件编码：参考【促销奖励限制条件表】
    @Column(name = "LIMITATION_CODE")
    private String limitationCode;

    // 限制条件值
    @Column(name = "LIMITATION_VALUE")
    private BigDecimal limitationValue;

    // Create user code.
    @Column(name = "create_user")
    private String createUser;

    // Create time.
    @Column(name = "create_time")
    private Date createTime;

    // Lastest update user.
    @Column(name = "update_user")
    private String updateUser;

    // Lastest update time.
    @Column(name = "update_time")
    private Date updateTime;

    // Logic delete. (0-normal 1-deleted)
    @Column(name = "logic_delete")
    private Integer logicDelete;

}
