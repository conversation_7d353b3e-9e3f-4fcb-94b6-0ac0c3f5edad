/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * <PERSON>ECH MAKES NO REPRESENTATIONS OR WA<PERSON>RANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.dao.entity.activity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.util.Date;

/**
 * 促销活动函数参数表
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "promo_activity_func_param")
public class ActivityFuncParamEntity {

    public static final String C_TENANT_CODE = "tenantCode";
    public static final String C_RANK_ID = "rankId";

    // ID
    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    // Tenant code
    @Column(name = "TENANT_CODE")
    private String tenantCode;

    // 函数层级ID
    @Column(name = "RANK_ID")
    private String rankId;

    // 函数类型：01-促销范围 02-促销条件 03-条件参数 04-促销奖励
    @Column(name = "FUNCTION_TYPE")
    private String functionType;

    // 函数编码
    @Column(name = "FUNCTION_CODE")
    private String functionCode;

    // 函数名称
    @Column(name = "FUNCTION_NAME")
    private String functionName;

    // 参数类型: 01-空 02-数值 03-JSON
    @Column(name = "PARAM_TYPE")
    private String paramType;

    // 参数值
    @Column(name = "PARAM_VALUE")
    private String paramValue;

    // 参数单位: 01-元 02-件 03-折
    @Column(name = "PARAM_UNIT")
    private String paramUnit;

    // Create user code.
    @Column(name="create_user")
    private String createUser;

    // Create time.
    @Column(name="create_time")
    private Date createTime;

    // Lastest update user.
    @Column(name="update_user")
    private String updateUser;

    // Lastest update time.
    @Column(name="update_time")
    private Date updateTime;

}