package com.gtech.promotion.dao.model.marketing;

import com.gtech.promotion.vo.bean.ExtDescription;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
public class MarketingLanguageModel extends BaseModel {

    private String activityName;
    private String activityLabel;
    private String activityDesc;
    private String activityShortDesc;
    private String language;

    private List<ExtDescription> extDescriptions;

}
