/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.dao.mapper.activity;

import com.gtech.commons.dao.mapper.GTechBaseMapper;
import com.gtech.promotion.dao.entity.activity.TPromoActivityStoreEntity;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

//@Mapper
public interface TPromoActivityStoreMapper extends GTechBaseMapper<TPromoActivityStoreEntity> {

    // TODO: 当门店活动过多时，可能导致查询结果太大 // NOSONAR
    @Select("SELECT DISTINCT ACTIVITY_CODE from promo_activity_store "
                    + " WHERE (TENANT_CODE = #{TENANT_CODE} AND ACTIVITY_CODE not in "
                    + " (SELECT distinct ACTIVITY_CODE FROM promo_activity_store WHERE (TENANT_CODE = #{TENANT_CODE} AND ORG_CODE = #{ORG_CODE} )))")
    List<String> selectNotByCondition(@Param("TENANT_CODE") String tenantCode, @Param("ORG_CODE") String orgCode);
}
