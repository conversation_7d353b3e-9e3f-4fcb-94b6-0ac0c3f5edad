/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.dao.model.coupon;

import java.io.Serializable;
import java.util.List;

import org.apache.commons.collections4.CollectionUtils;

import com.gtech.promotion.code.coupon.CouponStatusEnum;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * CountCouponCodeModel
 *
 * <AUTHOR>
 * @Date 2020-02-19
 */
@Getter
@Setter
@ToString
public class CountCouponCodeModel implements Serializable {

    private static final long serialVersionUID = 6438781589128243338L;

    // Coupon activity code
    private String activityCode;

    // Coupon release code
    private String releaseCode;

    // Coupon status
    private String couponStatus;

    // Coupon count group by status
    private int couponCount;

    public static int countByStatus(List<CountCouponCodeModel> modelList, CouponStatusEnum status) {

        if (CollectionUtils.isEmpty(modelList)) {
            return 0;
        }

        int sum = 0;
        for (CountCouponCodeModel m : modelList) {
            if (status.equalsCode(m.getCouponStatus())) {
                sum += m.getCouponCount();
            }
        }

        return sum;
    }
}
