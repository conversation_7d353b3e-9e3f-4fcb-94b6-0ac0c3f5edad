package com.gtech.promotion.dao.entity.marketing;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import javax.persistence.Table;

@EqualsAndHashCode(callSuper = true)
@Table(name = "MARKETING_PRIZE_LANGUAGE")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class PrizeLanguageEntity extends BaseEntity {

    private String activityCode;
    // 奖品编号（系统生成，全局唯一）
    private String prizeNo;
    private String prizeName;
    private String language;

}
