/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * <PERSON>ECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.dao.entity.activity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 促销活动奖励表
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "promo_activity_incentive")
public class TPromoActivityIncentiveEntity {
    
    public static final String C_ACTIVITY_CODE = "activityCode";
    public static final String C_PROMO_ORDER_ID = "promoOrderId";
    public static final String C_LOGIC_DELETE = "logicDelete";

    // ID
    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    // Tenant code
    @Column(name = "TENANT_CODE")
    private String tenantCode;

    // Activity code
    @Column(name = "ACTIVITY_CODE")
    private String activityCode;

    // 促销订单ID
    @Column(name = "PROMO_ORDER_ID")
    private String promoOrderId;

    // 奖励类型：01-直降 02-折扣 03-赠品
    @Column(name = "INCENTIVE_TYPE")
    private String incentiveType;

    // 奖励金额
    @Column(name = "INCENTIVE_AMOUNT")
    private BigDecimal incentiveAmount;

    //  运费减免金额
    private BigDecimal incentivePostage;

    // 奖励次数
    @Column(name = "INCENTIVE_TIMES")
    private Integer incentiveTimes;

    // 会员编码
    @Column(name = "USER_CODE")
    private String userCode;

    // Create user code.
    @Column(name="create_user")
    private String createUser;

    // Create time.
    @Column(name="create_time")
    private Date createTime;

    // Lastest update user.
    @Column(name="update_user")
    private String updateUser;

    // Lastest update time.
    @Column(name="update_time")
    private Date updateTime;

    // Logic delete. (0-normal 1-deleted)
    @Column(name="logic_delete")
    private Integer logicDelete;

}