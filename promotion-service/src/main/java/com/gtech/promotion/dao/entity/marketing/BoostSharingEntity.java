package com.gtech.promotion.dao.entity.marketing;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import javax.persistence.Table;

@EqualsAndHashCode(callSuper = true)
@Table(name = "marketing_boost_sharing")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class BoostSharingEntity extends BaseEntity {


    @Column(name="activity_code")
    private String activityCode;

    @Column(name="boost_sharing_type")
    private String boostSharingType;

    @Column(name="attract_new_customers")
    private String attractNewCustomers;

    @Column(name="number_of_boost_sharing")
    private String numberOfBoostSharing;

    /**-----------------分享人奖励----------------------*/
    @Column(name="share_to_get_coupon_activity_code")
    private String shareToGetCouponActivityCode;
    @Column(name="share_to_get_coupon_activity_name")
    private String shareToGetCouponActivityName;
    @Column(name="right_of_first_refusal_product_code")
    private String rightOfFirstRefusalProductCode;
    @Column(name="right_of_first_refusal_product_no")
    private String rightOfFirstRefusalProductNo;
    @Column(name="right_of_first_refusal_product_name")
    private String rightOfFirstRefusalProductName;
    @Column(name="right_of_first_refusal_start_time")
    private String rightOfFirstRefusalStartTime;
    @Column(name="right_of_first_refusal_end_time")
    private String rightOfFirstRefusalEndTime;
    @Column(name="lucky_draw_activity_code")
    private String luckyDrawActivityCode;
    @Column(name="lucky_draw_activity_name")
    private String luckyDrawActivityName;
    /**-----------------分享人奖励----------------------*/


    /**-----------------助力人奖励----------------------*/
    @Column(name="help_to_get_coupon_activity_code")
    private String helpToGetCouponActivityCode;
    @Column(name="help_to_get_coupon_activity_name")
    private String helpToGetCouponActivityName;
    /**-----------------助力人奖励----------------------*/


    @Column(name="redirect_link")
    private String redirectLink;
    @Column(name="event_page_image")
    private String eventPageImage;
    @Column(name="event_back_ground_page_image")
    private String eventBackGroundPageImage;
    @Column(name="event_page_link")
    private String eventPageLink;
    @Column(name="mini_program_image")
    private String miniProgramImage;
    @Column(name="mini_program_share_copy")
    private String miniProgramShareCopy;
    @Column(name="poster_image")
    private String posterImage;
    @Column(name="popup_image")
    private String popupImage;
    @Column(name="popup_share_copy")
    private String popupShareCopy;




}
