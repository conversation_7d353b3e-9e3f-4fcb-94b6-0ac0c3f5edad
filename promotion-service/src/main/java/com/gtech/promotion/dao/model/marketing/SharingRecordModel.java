package com.gtech.promotion.dao.model.marketing;


import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class SharingRecordModel {

    private String activityCode;
    private String domainCode;
    private String tenantCode;
    private String orgCode;
    private String sharingRecordCode;
    private String sharingMemberCode;
    private String activityStatus;
    private String numberOfBoostSharing;
    private String numberOfPeopleWhoHaveHelped;

    private Date updateTime;




}
