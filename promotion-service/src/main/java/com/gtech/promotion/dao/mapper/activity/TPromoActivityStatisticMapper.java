/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * <PERSON>ECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.dao.mapper.activity;

import com.gtech.commons.dao.mapper.GTechBaseMapper;
import com.gtech.promotion.dao.entity.activity.TPromoActivityStatisticEntity;
import com.gtech.promotion.dto.in.activity.QueryActivityStatisticSumInDTO;
import com.gtech.promotion.dto.out.activity.ActivityStatisticSumQueryOutDTO;
import org.apache.ibatis.annotations.Select;

//@Mapper
public interface TPromoActivityStatisticMapper extends GTechBaseMapper<TPromoActivityStatisticEntity> {

    @Select("SELECT SUM(ORDER_COUNT) AS OrderCount, SUM(MEMBER_COUNT) AS memberCount, SUM(ORDER_AMOUNT) AS orderAmount, SUM(REDUCE_AMOUNT) AS reduceAmount " +
            "FROM promo_activity_statistic WHERE TENANT_CODE = #{tenantCode} AND ACTIVITY_CODE = #{activityCode} ")
    ActivityStatisticSumQueryOutDTO queryActivityStatisticSum(QueryActivityStatisticSumInDTO paramDTO);

}
