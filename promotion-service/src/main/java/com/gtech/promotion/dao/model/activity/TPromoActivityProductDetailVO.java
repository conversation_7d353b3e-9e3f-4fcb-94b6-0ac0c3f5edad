/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.dao.model.activity;

import lombok.*;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 商品sku
 */
@Getter
@Setter
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class TPromoActivityProductDetailVO {

    // Tenant code
    private String tenantCode;

    // Activity code
    private String activityCode;

    // 商品范围层级id:默认1
    private Integer seqNum;
    
    // 商品编码：0000-不限制
    private String productCode;

    // 商品名称
    private String spuName;

    // SKU编码
    private String skuCode;

    // SKU名称
    private String skuName;

    private Integer type;

    private String orgCode;

    private String orgName;

    // 组合商品sku编码
    private String combineSkuCode;
    
    // 组合商品sku名称
    private String combineSkuName;

    // 促销价格
    private BigDecimal promoPrice;

    // ID
    private String id;

    // 创建时间
    private Date createTime;

    // 更新时间
    private Date updateTime;

    // 逻辑删除: 0-未删除 1-已删除
    private String logicDelete;

    //商品扩展参数
    private String extendParam;


    private String priceType;

    private String priceDiscount;


}