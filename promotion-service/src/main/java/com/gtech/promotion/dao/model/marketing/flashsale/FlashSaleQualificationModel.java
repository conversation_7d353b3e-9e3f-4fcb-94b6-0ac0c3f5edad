package com.gtech.promotion.dao.model.marketing.flashsale;

import com.gtech.promotion.dao.model.marketing.BaseModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
public class FlashSaleQualificationModel extends BaseModel {

    private String qualificationCode;
    private String qualificationValue;
    private String qualificationValueName;
    private String relation;

    private String isExclude;

}
