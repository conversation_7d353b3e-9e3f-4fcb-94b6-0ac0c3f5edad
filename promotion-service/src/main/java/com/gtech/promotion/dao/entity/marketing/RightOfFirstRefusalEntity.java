package com.gtech.promotion.dao.entity.marketing;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;

@Table(name = "marketing_right_of_first_refusal")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class RightOfFirstRefusalEntity {

    @Id
    @Column(name = "id")
    private Long id;
    @Column(name = "domain_code")
    private String domainCode;
    @Column(name = "tenant_code")
    private String tenantCode;
    @Column(name = "org_code")
    private String orgCode;
    @Column(name = "activity_code")
    private String activityCode;
    @Column(name = "member_code")
    private String memberCode;
    @Column(name = "right_of_first_refusal_code")
    private String rightOfFirstRefusalCode;

    @Column(name = "right_of_first_refusal_product_code")
    private String rightOfFirstRefusalProductCode;


    @Column(name = "right_of_first_refusal_status")
    private String rightOfFirstRefusalStatus;

    @Column(name = "order_id")
    private String orderId;







}
