package com.gtech.promotion.dao.model.activity;

import lombok.Data;

import java.io.Serializable;

/**
 * @Description :
 * <AUTHOR> gaojie.li
 * @Date : 2023/12/20 14:04
 */
@Data
public class ActivityGroupPriorityVO implements Serializable {

    private static final long serialVersionUID = -4911810572384813973L;

    // Activity code
    private String activityCode;

    //分组优先级
    private Long groupPriority;

    //分组编码
    private String groupCode;

    //活动优先级
    private Integer priority;
}
