package com.gtech.promotion.dao.entity.activity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.util.Date;

/**
 * @Description :
 * <AUTHOR> gaojie.li
 * @Date : 2023/6/13 11:33
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "promo_group")
public class PromoGroupEntity {

    @Id
    @Column(name = "ID")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 租户编码
     */
    @Column(name = "TENANT_CODE")
    private String tenantCode;

    @Column(name = "DOMAIN_CODE")
    private String domainCode;
    /**
     * 分组编码
     */
    @Column(name = "GROUP_CODE")
    private String groupCode;

    /**
     * 活动分组名称
     */
    @Column(name = "GROUP_NAME")
    private String groupName;

    /**
     * 优先级，数字越小约优先
     */
    @Column(name = "PRIORITY")
    private Long priority;

    /**
     * 分组类型 01:默认分组 02：自定义分组
     */
    @Column(name = "TYPE")
    private String type;

    @Column(name="create_user")
    private String createUser;

    // Create time.
    @Column(name="create_time")
    private Date createTime;

    // Lastest update user.
    @Column(name="update_user")
    private String updateUser;

    // Lastest update time.
    @Column(name="update_time")
    private Date updateTime;

    @Column(name="LOGIC_DELETE")
    private Integer logicDelete;

    @Column(name="EXT_PARAM")
    private String extParam;
}
