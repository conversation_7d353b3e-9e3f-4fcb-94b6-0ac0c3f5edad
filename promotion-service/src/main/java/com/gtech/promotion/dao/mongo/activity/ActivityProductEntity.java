/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * <PERSON>ECH MAKES NO REPRESENTATIONS OR WA<PERSON>RANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.dao.mongo.activity;

import com.gtech.promotion.code.PromotionConstants;
import com.gtech.promotion.vo.bean.ProductAttribute;
import lombok.*;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import java.io.Serializable;
import java.util.List;

/**
 * ActivityProductEntity
 * 
 */
@Getter
@Setter
@ToString
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Document(collection = "promo_activity_product")
public class ActivityProductEntity implements Serializable {

    private static final long serialVersionUID = -8802496801890053932L;

    @Field("_id")
    private String id;

    // 租户编码
    private String tenantCode;

    // 活动ID
    private String activityCode;

    // 商品范围层级id:默认
    @Builder.Default
    private Integer seqNum = 1;

    // 品类编码：0000-全部品类
    @Builder.Default
    private String categoryCode = PromotionConstants.UNLIMITED;

    // 品类名称
    private String categoryName;

    // 品牌编码：0000-全部品牌
    @Builder.Default
    private String brandCode = PromotionConstants.UNLIMITED;

    // 品牌名称
    private String brandName;

    // Product attribute-values define
    private List<ProductAttribute> attributes;

    private String orgCode;

    ///01 sku属性  02spu属性
    @Builder.Default
    private String attrType = PromotionConstants.ATTERIBUTE_TYPE;


    // SPU属性
    private List<ProductAttribute> spuAttributes;

    private String productTag;
}