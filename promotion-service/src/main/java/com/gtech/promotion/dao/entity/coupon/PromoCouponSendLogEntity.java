/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * <PERSON>ECH MAKES NO REPRESENTATIONS OR WA<PERSON><PERSON>NTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.dao.entity.coupon;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.util.Date;


@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "promo_coupon_send_log")
public class PromoCouponSendLogEntity {

    // ID
    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    // Tenant code
    @Column(name = "TENANT_CODE")
    private String tenantCode;

    // Activity code
    @Column(name = "ACTIVITY_CODE")
    private String activityCode;

    @Column(name = "SEND_BATCH_NO")
    private String sendBatchNo;

    @Column(name = "COUPON_BATCH_NO")
    private String couponBatchNo;

    @Column(name = "coupon_qty")
    private Integer couponQty;

    @Column(name = "user_qty")
    private Integer userQty;

    @Column(name = "status")
    private Integer status;

    @Column(name = "fail_reason")
    private String failReason;

    // Create user code.
    @Column(name = "create_user")
    private String createUser;

    // Create time.
    @Column(name = "create_time")
    private Date createTime;

    // Lastest update user.
    @Column(name = "update_user")
    private String updateUser;

    // Lastest update time.
    @Column(name = "update_time")
    private Date updateTime;

    // Logic delete. (0-normal 1-deleted)
    @Column(name = "logic_delete")
    private Integer logicDelete;

}