/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.dao.model.coupon;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.Date;


/**   
 * <功能描述>
 */
@Getter
@Setter
@ToString
public class TPromoCouponInnerCodeVO {
    
    private String id;
    private String createUser;

    // Tenant code
    private String tenantCode;

    // Activity code
    private String activityCode;

	// 券投放ID
	private String releaseCode;


	// 券码
	private String couponCode;

	// 券类型：01-优惠券 02-匿名券 03-优惠码
	private String couponType;

	// 状态：01-未发放 02-已发放 03-已使用 04-已锁定 05-已过期
	private String status;

	// 冻结状态：01-未冻结 02-已冻结
	private String frozenStatus;

	// 券面值 
	private BigDecimal faceValue;
	
	// 券面值单位01：金额 02：折扣    
	private String faceUnit;

	// 领取标签 01-免费领取、02-活动赠券、03-促销赠券、04-积分换购、05-有价券、06-新人券、07-生日券、08-补偿券、09-推送券、10-其他；
	private String takeLabel;

	// 领取开始时间(yyyyMMddhhmmss)
	private String receiveStartTime;

	// 领取结束时间(yyyyMMddhhmmss)
	private String receiveEndTime;

	private Date createTime;
	
	private String logicDelete;

	// 可用开始时间(yyyyMMddhhmmss)
	private String validStartTime;

	// 可用结束时间(yyyyMMddhhmmss)
	private String validEndTime;

	private String promoPassword;

}
