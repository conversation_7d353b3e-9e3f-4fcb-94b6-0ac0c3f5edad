package com.gtech.promotion.dao.entity.activity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2021/1/25 18:01
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "lucky_draw_rules")
public class LuckyDrawRulesEntity {

    // ID
    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    // Tenant code
    @Column(name = "TENANT_CODE")
    private String tenantCode;

    // Activity code
    @Column(name = "ACTIVITY_CODE")
    private String activityCode;

    //01-无规则 02-根据商品数量 03-根据商品金额
    @Column(name = "ADMISSION_RULES")
    private String admissionRules;

    //SPU
    @Column(name = "PRODUCT_CODE")
    private String productCode;

    //SPU Name
    @Column(name = "PRODUCT_NAME")
    private String productName;

    //价格或数量
    @Column(name = "BUYING_QTY")
    private BigDecimal buyingQty;

    //奖券数量
    @Column(name = "EARN_TICKET")
    private Integer earnTicket;

    //用户可获得最大数量
    @Column(name = "MAX_GIVING_PER_USER")
    private Integer maxGivingPerUser;

    // Create user code.
    @Column(name = "create_user")
    private String createUser;

    // Create time.
    @Column(name = "create_time")
    private Date createTime;

    // Lastest update user.
    @Column(name = "update_user")
    private String updateUser;

    // Lastest update time.
    @Column(name = "update_time")
    private Date updateTime;

    // Logic delete. (0-normal 1-deleted)
    @Column(name = "logic_delete")
    private Integer logicDelete;


}
