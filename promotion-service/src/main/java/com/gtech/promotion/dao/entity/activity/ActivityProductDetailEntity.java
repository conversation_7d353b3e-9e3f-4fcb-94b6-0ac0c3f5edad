/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * <PERSON>ECH MAKES NO REPRESENTATIONS OR WA<PERSON><PERSON>NTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.dao.entity.activity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 商品sku
 * 
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "promo_activity_product_detail")
public class ActivityProductDetailEntity {

    public static final String C_ID = "id";
    public static final String C_ACTIVITY_CODE = "activityCode";
    public static final String C_TENANT_CODE = "tenantCode";
    public static final String C_PRODUCT_CODE = "productCode";
    public static final String C_COMBINE_SKU_CODE = "combineSkuCode";

    // ID
    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    // Tenant code
    @Column(name = "TENANT_CODE")
    private String tenantCode;

    // Activity code
    @Column(name = "ACTIVITY_CODE")
    private String activityCode;

    // 商品范围层级id:默认1
    @Column(name = "SEQ_NUM")
    private Integer seqNum;

    // 商品编码：0000-不限制
    @Column(name = "PRODUCT_CODE")
    private String productCode;

    // 商品名称
    @Column(name = "SPU_NAME")
    private String spuName;

    // SKU编码
    @Column(name = "SKU_CODE")
    private String skuCode;

    // SKU名称
    @Column(name = "SKU_NAME")
    private String skuName;

    //活动商品黑白名单，1-白  2-黑
    @Column(name = "type")
    private Integer type;

    // ORG编码
    @Column(name = "ORG_CODE")
    private String orgCode;

    // ORG名称
    @Column(name = "ORG_NAME")
    private String orgName;

    // SKU编码
    @Column(name = "COMBINE_SKU_CODE")
    private String combineSkuCode;

    // SKU名称
    @Column(name = "COMBINE_SKU_NAME")
    private String combineSkuName;

    // 促销价格
    @Column(name = "PROMO_PRICE")
    private BigDecimal promoPrice;

    // Create user code.
    @Column(name="create_user")
    private String createUser;

    // Create time.
    @Column(name="create_time")
    private Date createTime;

    // Lastest update user.
    @Column(name="update_user")
    private String updateUser;

    // Lastest update time.
    @Column(name="update_time")
    private Date updateTime;

    // Logic delete. (0-normal 1-deleted)
    @Column(name="logic_delete")
    private Integer logicDelete;

    //商品扩展参数
    @Column(name="EXTEND_PARAM")
    private String extendParam;


    @Column(name="PRICE_TYPE")
    private String priceType;

    @Column(name="PRICE_DISCOUNT")
    private String priceDiscount;


}