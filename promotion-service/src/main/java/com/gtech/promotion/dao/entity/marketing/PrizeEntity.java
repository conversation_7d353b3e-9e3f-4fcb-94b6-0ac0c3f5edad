package com.gtech.promotion.dao.entity.marketing;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import javax.persistence.Table;
import java.math.BigDecimal;

@EqualsAndHashCode(callSuper = true)
@Table(name = "MARKETING_PRIZE")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class PrizeEntity extends BaseEntity {

    private String activityCode;
    // 奖品编号（系统生成，全局唯一）
    private String prizeNo;
    private String prizeCode;
    private String prizeName;
    private String prizeImage;
    private Integer prizeOrder;
    private Integer prizeQuota;
    private Integer prizeInventory;
    private Integer prizeNum;
    // 奖品类型，01-券
    private String prizeType;

    private Integer numberOfTimesWon;
    private BigDecimal prizeProbability;

}
