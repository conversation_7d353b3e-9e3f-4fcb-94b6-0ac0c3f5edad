/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.dao.model.activity;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**   
 * 订单vo
 */
@Getter
@Setter
@ToString
public class TPromoOrderVO {
    
    private String id;

    // Tenant code
    private String tenantCode;

    // 促销订单编号
    private String promoOrderNo;

    // 促销订单序号（初始序号为1，自然递增）
    private Integer promoOrderSeq;

    // 促销订单时间: yyyyMMddhhmmss
    private String promoOrderTime;

    // 销售订单编号(促销计算用业务单号)
    private String orderId;

    // 销售订单状态：01-未付款 02-已付款 03-已取消
    private String orderStatus;

    // Store organization code.
    private String orgCode;

    // 会员ID
    private String userCode;

}