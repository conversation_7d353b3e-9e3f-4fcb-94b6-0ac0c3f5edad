package com.gtech.promotion.dao.model.marketing.flashsale;

import com.gtech.promotion.dao.model.marketing.BoostSharingModel;
import com.gtech.promotion.dao.model.marketing.CacheMarketingModel;
import com.gtech.promotion.dao.model.marketing.MarketingGroupMode;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CacheFlashSaleModel extends CacheMarketingModel implements Serializable {

    private static final long serialVersionUID = 5380856357738169650L;

    private List<FlashSaleProductModel> products; // NOSONAR
    private List<FlashSaleQualificationModel> qualifications; // NOSONAR
    private List<FlashSaleStoreModel> stores; // NOSONAR

    //拼团
    private MarketingGroupMode marketingGroupMode;

    private BoostSharingModel boostSharingModel;
}
