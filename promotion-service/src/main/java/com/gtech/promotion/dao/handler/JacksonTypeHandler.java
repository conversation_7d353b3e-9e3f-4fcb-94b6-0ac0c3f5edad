package com.gtech.promotion.dao.handler;

import cn.hutool.core.lang.Assert;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.MappedJdbcTypes;
import org.apache.ibatis.type.MappedTypes;

import java.io.IOException;

@Slf4j
@MappedTypes({Object.class})
@MappedJdbcTypes(JdbcType.VARCHAR)
public class JacksonTypeHandler extends AbstractJsonTypeHandler<Object> {
    private static ObjectMapper objectMapper = new ObjectMapper();
    private final Class<?> type;

    public JacksonTypeHandler(Class<?> type) {
        Assert.notNull(type, () -> new IllegalArgumentException("JacksonTypeHandler.type not null"));
        this.type = type;
    }

    public static void setObjectMapper(ObjectMapper objectMapper) {
        Assert.notNull(objectMapper, () -> new IllegalArgumentException("setObjectMapper.objectMapper not null"));
        JacksonTypeHandler.objectMapper = objectMapper;
    }

    @SneakyThrows
    @Override
    protected Object parse(String json) {
        try {
            return objectMapper.readValue(json, type);
        } catch (IOException e) {
            return type.newInstance();
        }
    }

    @SneakyThrows
    @Override
    protected String toJson(Object obj) {
        return objectMapper.writeValueAsString(obj);
    }
}