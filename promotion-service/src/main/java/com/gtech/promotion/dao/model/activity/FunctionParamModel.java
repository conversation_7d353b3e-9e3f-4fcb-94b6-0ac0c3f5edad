/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.dao.model.activity;

import com.gtech.commons.utils.CheckUtils;
import com.gtech.commons.utils.EnumUtil;
import com.gtech.promotion.checker.activity.TPromoActivityChecker;
import com.gtech.promotion.code.activity.FuncTypeEnum;
import com.gtech.promotion.code.activity.FunctionParamTypeEnum;
import com.gtech.promotion.code.activity.FunctionParamUnitEnum;
import com.gtech.promotion.exception.Check;
import com.gtech.promotion.exception.ErrorCodes;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;

/**
 * FunctionParamModel
 * 
 */
@Getter
@Setter
@ToString
public class FunctionParamModel implements Serializable {

    private static final long serialVersionUID = -8968511306296239201L;

    private String id;

    // Tenant code
    private String tenantCode;

    // 函数层级ID
    private String rankId;

    // 函数类型：01-促销范围 02-促销条件 03-条件参数 04-促销奖励
    private String functionType;

    // 函数编码
    private String functionCode;

    // 函数名称
    private String functionName;

    // 参数类型: 01-空 02-数值 03-JSON
    private String paramType;

    // 参数值
    private String paramValue;

    // 参数单位: 01-金额 02-件 03-折
    private String paramUnit;

    // 层级参数
    private Integer rankParam;

    public void validate() {

        CheckUtils.isNotNull(this.rankParam, ErrorCodes.PARAM_EMPTY, "rankParam");

        Check.check(this.rankParam > 99 || this.rankParam < 1, TPromoActivityChecker.RANK);

        CheckUtils.isNotNull(this.functionCode, ErrorCodes.PARAM_EMPTY, "functionCode");

        CheckUtils.isNotNull(this.functionType, ErrorCodes.PARAM_EMPTY, "functionType");
        CheckUtils.isTrue(EnumUtil.exist(FuncTypeEnum.class, this.functionType), ErrorCodes.PARAM_ERROR, "functionType");

        CheckUtils.isNotNull(this.paramType, ErrorCodes.PARAM_EMPTY, "paramType");
        CheckUtils.isTrue(EnumUtil.exist(FunctionParamTypeEnum.class, this.paramType), ErrorCodes.PARAM_ERROR, "paramType");

        if (FunctionParamTypeEnum.NUMBER.equalsCode(this.paramType)) {

            CheckUtils.isNotNull(this.paramUnit, ErrorCodes.PARAM_EMPTY, "paramUnit");
            CheckUtils.isTrue(EnumUtil.exist(FunctionParamUnitEnum.class, this.paramUnit), ErrorCodes.PARAM_ERROR, "paramUnit");

            CheckUtils.isNotNull(this.paramValue, ErrorCodes.PARAM_EMPTY, "paramValue");
        }
    }
}