package com.gtech.promotion.dao.entity.point;

import lombok.*;

import javax.persistence.*;
import java.util.Date;

@Table(name = "point_account")
@Getter
@Setter
@ToString
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PointAccountEntity {

    public static final String C_DOMAIN_CODE = "domainCode";
    public static final String C_TENANT_CODE = "tenantCode";
    public static final String C_POINT_ACCOUNT_CODE = "pointAccountCode";
    public static final String C_ACCOUNT_CODE = "accountCode";
    public static final String C_ACCOUNT_TYPE = "accountType";
    public static final String C_CAMPAIGN_CODE = "campaignCode";

    // Primary key.
	@Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
	private Long id;

	// Domain code.
	@Column(name = "domain_code")
	private String domainCode;

	// Tenant code.
	@Column(name = "tenant_code")
	private String tenantCode;

	// Point account code.
	@Column(name = "point_account_code")
	private String pointAccountCode;

	// account code. (UserCode or OrgCode)
	@Column(name = "account_code")
	private String accountCode;

	//campaign code
	@Column(name = "campaign_code")
	private String campaignCode;

	// Point account type. (1-User 2-Organization)
	@Column(name = "account_type")
	private Integer accountType;

	// Point account description.
	@Column(name = "account_desc")
	private String accountDesc;

	/**
	 * Account balance
	 */
	@Column(name = "account_balance")
	private Integer accountBalance;

	/**
	 * Point account status.(0-Inactive 1-Active)
	 */
	private Integer status;

	/**
	 * Extends parameters. (JSON String)
	 */
	@Column(name = "ext_params")
	private String extParams;

	/**
	 * Create user code
	 */
	@Column(name = "create_user")
	private String createUser;

	/**
	 * Create time.
	 */
	@Column(name = "create_time")
	private Date createTime;

	/**
	 * Lastest update user.
	 */
	@Column(name = "update_user")
	private String updateUser;

	/**
	 * Lastest update time.
	 */
	@Column(name = "update_time")
	private Date updateTime;
}