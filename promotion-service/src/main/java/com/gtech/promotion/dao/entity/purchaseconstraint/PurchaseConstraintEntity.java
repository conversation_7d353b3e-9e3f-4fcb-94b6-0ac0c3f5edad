package com.gtech.promotion.dao.entity.purchaseconstraint;

import lombok.Data;

import javax.persistence.*;
import java.util.Date;

@Data
@Table(name = "promo_purchase_constraint")
public class PurchaseConstraintEntity {

    /**
     * 主键
     */
    @Id
    @Column(name = "ID")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    // DOMAIN code
    @Column(name = "DOMAIN_CODE")
    private String domainCode;

    /**
     * 租户号
     */
    @Column(name = "TENANT_CODE")
    private String tenantCode;

    /**
     * 组织编号
     */
    @Column(name = "ORG_CODE")
    private String orgCode;

    /**
     * 限购Code
     */
    @Column(name = "PURCHASE_CONSTRAINT_CODE")
    private String purchaseConstraintCode;

    /**
     * 限购名称
     */
    @Column(name = "PURCHASE_CONSTRAINT_NAME")
    private String purchaseConstraintName;

    /**
     * 限购开始时间
     */
    @Column(name = "PURCHASE_CONSTRAINT_START_TIME")
    private Date purchaseConstraintStartTime;

    /**
     * 限购结束时间
     */
    @Column(name = "PURCHASE_CONSTRAINT_END_TIME")
    private Date purchaseConstraintEndTime;

    /**
     * 状态
     * @see com.gtech.promotion.code.purchaseconstraint.PurchaseConstraintStatusEnum
     */
    @Column(name = "PURCHASE_CONSTRAINT_STATUS")
    private String purchaseConstraintStatus;

    /**
     * 创建时间
     */
    @Column(name = "CREATE_TIME")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name = "UPDATE_TIME")
    private Date updateTime;

    /**
     * 创建人
     */
    @Column(name = "CREATE_USER")
    private String createUser;

    /**
     * 更新人
     */
    @Column(name = "UPDATE_USER")
    private String updateUser;

    /**
     * 周期类型：00-全时段 01-自定义
     */
    @Column(name = "PERIOD_TYPE")
    private String periodType;

    /**
     * 商品范围正反选：01-正选；02-反选
     */
    @Column(name = "PRODUCT_SELECTION_TYPE")
    private String productSelectionType;

    /**
     * Product item scope type: 1-All scope 2-By spu in scope
     */
    @Column(name = "ITEM_SCOPE_TYPE")
    private Integer itemScopeType;

    /**
     * 店铺范围：00-全店铺 01-自定义
     */
    @Column(name = "STORE_TYPE")
    private String storeType;

    /**
     * 审核人
     */
    @Column(name = "AUDIT_USER")
    private String auditUser;

    /**
     * 是否优先购买  0 否 1 是
     */
    @Column(name = "FIRST_REFUSAL")
    private Integer firstRefusal;

    /**
     * 描述
     */
    @Column(name = "DESCRIPTION")
    private String description;

    /**
     * 优先级
     */
    @Column(name = "PRIORITY")
    private Integer priority;

    /**
     * 高级价格限购标识，默认0，非高级价格限购
     */
    @Column(name = "PRICE_SETTING")
    private Integer priceSetting;

    @Column(name = "CUSTOM_CONDITION")
    private String customCondition;
    @Column(name = "CUSTOM_RULE")
    private String customRule;
}