package com.gtech.promotion.dao.model.marketing;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Description :
 * <AUTHOR> gaojie.li
 * @Date : 2023/6/5 15:35
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class MarketingGroupUserMode extends BaseModel {

    /**
     * 拼团业务编码
     */
    private String marketingGroupCode;

    /**
     * 会员编码
     */
    private String userCode;

    /**
     * 有效小时
     */
    private Integer effectiveHour;

    /**
     * 有效截止时间
     */
    private String effectiveTime;

    /**
     * 拼团状态 01进行中，02 拼团成功，03 取消 04 已支付
     */
    private String groupStatus;

    /**
     * 是否团长 1 是， 0 否
     */
    private String teamLeader;

    private String cancelTime;

    private String productCode;

    private String skuCode;



}
