package com.gtech.promotion.dao.mapper.marketing;

import com.gtech.commons.dao.mapper.GTechBaseMapper;
import com.gtech.promotion.dao.entity.marketing.MarketingGroupUserEntity;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;

/**
 * @Description :
 * <AUTHOR> gaojie.li
 * @Date : 2023/8/7 14:05
 */
public interface MarketingGroupUserMapper extends GTechBaseMapper<MarketingGroupUserEntity> {



    @Update(" update marketing_group_user set INVENTORY = INVENTORY - 1"
            + " where TENANT_CODE = #{tenantCode} and ACTIVITY_CODE = #{activityCode} and MARKETING_GROUP_CODE = #{marketingGroupCode} and TEAM_LEADER = 1 and INVENTORY > 0 ")
    int deductGroupInventory(@Param("tenantCode") String tenantCode, @Param("activityCode") String activityCode, @Param("marketingGroupCode") String marketingGroupCode);

}
