package com.gtech.promotion.dao.mapper.marketing;

import com.gtech.commons.dao.mapper.GTechBaseMapper;
import com.gtech.promotion.dao.entity.marketing.MarketingGroupCodeEntity;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;

/**
 * @Description :
 * <AUTHOR> gaojie.li
 * @Date : 2023/8/7 14:05
 */
public interface MarketingGroupCodeMapper extends GTechBaseMapper<MarketingGroupCodeEntity> {



    @Update(" update marketing_group_code set INVENTORY = INVENTORY - 1"
            + " where TENANT_CODE = #{tenantCode} and ACTIVITY_CODE = #{activityCode} and MARKETING_GROUP_CODE = #{marketingGroupCode} and INVENTORY > 0 ")
    int deductGroupInventory(@Param("tenantCode") String tenantCode, @Param("activityCode") String activityCode, @Param("marketingGroupCode") String marketingGroupCode);

    @Update(" update marketing_group_code set INVENTORY = INVENTORY + 1"
            + " where TENANT_CODE = #{tenantCode} and ACTIVITY_CODE = #{activityCode} and MARKETING_GROUP_CODE = #{marketingGroupCode} and INVENTORY >= 0 ")
    int addGroupInventory(@Param("tenantCode") String tenantCode, @Param("activityCode") String activityCode, @Param("marketingGroupCode") String marketingGroupCode);

}
