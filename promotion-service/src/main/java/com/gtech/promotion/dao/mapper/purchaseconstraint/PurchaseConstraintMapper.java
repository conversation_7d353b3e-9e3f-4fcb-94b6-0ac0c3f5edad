package com.gtech.promotion.dao.mapper.purchaseconstraint;

import com.gtech.commons.dao.mapper.GTechBaseMapper;
import com.gtech.promotion.dao.entity.purchaseconstraint.PurchaseConstraintEntity;
import com.gtech.promotion.vo.param.purchaseconstraint.query.QueryPurchaseConstraintListParam;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.example.SelectByExampleMapper;

import java.util.List;

public interface PurchaseConstraintMapper extends GTechBaseMapper<PurchaseConstraintEntity>, SelectByExampleMapper<PurchaseConstraintEntity> {
    List<PurchaseConstraintEntity> queryList(QueryPurchaseConstraintListParam paramDto);

    int updatePurchaseConstraintPriority(@Param("tenantCode") String tenantCode,
                                         @Param("purchaseConstraintCode") String purchaseConstraintCode,
                                         @Param("purchaseConstraintPriority") Integer purchaseConstraintPriority);

    List<PurchaseConstraintEntity> queryEffectivePc(@Param("tenantCode") String tenantCode);
}