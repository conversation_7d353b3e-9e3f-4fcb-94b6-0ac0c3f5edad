/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.dao.model.activity;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.annotation.JSONField;
import com.gtech.commons.utils.DateUtil;
import com.gtech.promotion.calc.TemplateEnum;
import com.gtech.promotion.code.PromotionConstants;
import com.gtech.promotion.code.activity.ActivityStatusEnum;
import com.gtech.promotion.code.activity.ActivityTypeEnum;
import com.gtech.promotion.code.activity.ItemScopeTypeEnum;
import com.gtech.promotion.vo.bean.ExtImage;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 */
@Getter
@Setter
@ToString
public class ActivityModel implements Serializable {

    private static final long serialVersionUID = -5353769145991935754L;


    // Activity id
    private Long id;

    private String domainCode;

    private String externalActivityId;


    // Tenant code
    private String tenantCode;

    // Activity code
    private String activityCode;

    private String groupCode;
    // 活动名称
    private String activityName;

    private String promotionCategory;

    private String promotionCategoryName;

    // Org code
    private String orgCode;
    private String priceCondition;
    private String customCondition;

    // 活动类型: 01-活动 02-券
    private String activityType;
    public boolean isCouponActivity() {

        return ActivityTypeEnum.COUPON.equalsCode(activityType);
    }

    // 活动标签
    private String activityLabel;


    // 活动描述
    private String activityDesc;
    
    //扩展字段：是否对外显示：1显示；2不显示（1.21版本新加）
    private Integer showFlag;

    // 活动备注
    private String activityRemark;

    // 活动公式
    private String activityExpr;

    // Activity priority
    private Integer priority;

    // Template code
    private String templateCode;
    @JSONField(serialize = false)
    public String getTagCode() {
        return TemplateEnum.code2TagCode(this.templateCode);
    }
    @JSONField(serialize = false)
    public String getRewardType() {
        return this.templateCode.substring(this.templateCode.length() - 2);
    }

    // 奖励限制标志：0-无限制 1-有限制
    private String incentiveLimitedFlag;

    // 预热开始时间：yyyyMMddhhmmss
    private String warmBegin;

    // 预热结束时间：yyyyMMddhhmmss
    private String warmEnd;

    // 开始时间：yyyyMMddhhmmss
    private String activityBegin;

    // 结束时间：yyyyMMddhhmmss
    private String activityEnd;

    // 周期类型：00-全时段 01-自定义
    private String periodType;

    // 商品正反选 01-正选 02-反选
    private String productSelectionType;

    // Product item scope type: 1-All scope 2-By spu in scope
    @Setter
    private Integer itemScopeType;
    public Integer getItemScopeType() {
        return null == this.itemScopeType ? ItemScopeTypeEnum.ALL_SCOPE.number() : this.itemScopeType;
    }

    // 店铺范围：00-全店铺 01-自定义
    private String storeType;

    private String activityUrl;

    // Activity status: 01-待提交 02-审核中 03-已拒绝 04-已生效 05-已结束 06-暂停中 07-已终止
    private String activityStatus;

    // Activity sponsors
    private String sponsors;

    private String opsType;

    // Activity exclusive scope identification key. (Default exclusive by order.)
    private String exclusionKey;

    private String createUser;

    private Date createTime;

    private String updateUser;

    ////////////////////////////////////////////////////////////////////////////////////////////////
    // For coupon activity
    // Total quantity for activity: 0-unlimited, fixed 1 when couponType=03
    private Integer totalQuantity;

    // Single user max limited
    private Integer userLimitMax;

    // Single user max limited day
    private Integer userLimitMaxDay;
    
    // Coupon type: 01-优惠券 02-匿名券 03-优惠码   
    private String couponType;

    // Coupon code.
    private String couponCode;

    private Integer reserveInventory;

    private String coolDown;

    private String backgroundImage;

    // 商品条件：01-买A优惠B，B单价要低于A
    private String productCondition;

    private String ribbonImage;
    private String ribbonPosition;
    private String ribbonText;

    private String auditUser;

    private Integer logicDelete;

    // For calculate
    @JSONField(serialize = false,deserialize = false)
    private transient List<String> couponCodes = new ArrayList<>();

    private JSONObject extendParams;

    private List<ExtImage> extImages;

    private Boolean highPrioriy = false;



    public void setCouponInfo(ActivityModel activityModel) {

        if (null == activityModel) {
            return;
        }
        this.reserveInventory = activityModel.reserveInventory;
        this.totalQuantity = activityModel.totalQuantity;
        this.userLimitMax = activityModel.userLimitMax;
        this.couponType = activityModel.couponType;
        this.couponCode = activityModel.couponCode;
    }

    public Integer getPriority() {

        return priority == null ? PromotionConstants.DEF_PRIORITY : priority;
    }

    /**
     * 是否有效活动(验证有效期、活动状态)
     */
    public boolean isValid(Date date) {

        //添加包含预热时间生效活动
        return ActivityStatusEnum.EFFECTIVE.equalsCode(this.activityStatus) && (null != this.warmBegin ||
                DateUtil.parseDate(this.activityBegin, DateUtil.FORMAT_YYYYMMDDHHMISS_14).getTime() <= date.getTime()
                && DateUtil.parseDate(this.activityEnd, DateUtil.FORMAT_YYYYMMDDHHMISS_14).getTime() > date.getTime())
                ;
    }

    /**
     * 判断是否需要做过期处理
     */
    public boolean isNeedToDoExpire() {

        return ActivityStatusEnum.EFFECTIVE.equalsCode(this.activityStatus) 
                        && DateUtil.parseDate(this.activityEnd, DateUtil.FORMAT_YYYYMMDDHHMISS_14).getTime() <= System.currentTimeMillis();
    }

    public void setLanguage(ActivityLanguageModel activityLanguage) {

        if (null == activityLanguage) {
            return;
        }

        if (StringUtils.isNotBlank(activityLanguage.getActivityLabel())) {
            this.setActivityLabel(activityLanguage.getActivityLabel());
        }

        if (StringUtils.isNotBlank(activityLanguage.getActivityName())) {
            this.setActivityName(activityLanguage.getActivityName());
        }

        if (StringUtils.isNotBlank(activityLanguage.getActivityDesc())) {
            this.setActivityDesc(activityLanguage.getActivityDesc());
        }

        if (StringUtils.isNotBlank(activityLanguage.getActivityRemark())) {
            this.setActivityRemark(activityLanguage.getActivityRemark());
        }
    }
}
