/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.dao.mapper.coupon;

import com.gtech.commons.dao.mapper.GTechBaseMapper;
import com.gtech.promotion.dao.entity.coupon.TPromoCouponCodeUserEntity;
import com.gtech.promotion.dao.model.coupon.CountCouponCodeModel;
import com.gtech.promotion.dao.model.coupon.TPromoCouponCodeUserVO;
import com.gtech.promotion.dto.in.coupon.ExportCouponUserDto;
import com.gtech.promotion.vo.result.coupon.ExportCouponUserDetailResult;
import com.gtech.promotion.vo.result.coupon.ExportCouponUserResult;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
 * 会员优惠券相关mappper
 */
//@Mapper
public interface TPromoCouponCodeUserMapper extends GTechBaseMapper<TPromoCouponCodeUserEntity> {

    @Select("<script> SELECT COUNT(status) as couponCount, release_code as releaseCode, status as couponStatus" 
          + " FROM promo_coupon_code_user WHERE tenant_code = #{tenantCode} and activity_code = #{activityCode} "
          + "<if test='releaseCode != null'> and release_code = #{releaseCode} </if>"
          + " GROUP BY release_code, status </script>")
    List<CountCouponCodeModel> countCouponCode(@Param("tenantCode") String tenantCode, @Param("activityCode") String activityCode, @Param("releaseCode") String releaseCode);

    @Select("<script>" +
            "SELECT  " +
            "  USER_CODE id,  " +
            "  USER_CODE userCode,  " +
            "CASE  " +
            "      " +
            "    WHEN COUPON_TYPE = '01' THEN  " +
            "    COUNT( CASE `STATUS` WHEN '02' THEN `STATUS` END ) ELSE 'N/A'   " +
            "  END unusedQty,  " +
            "  COUNT( CASE `STATUS` WHEN '03' THEN `STATUS` WHEN '04' THEN `STATUS` END ) usedQty,  " +
            "  COUNT( `STATUS` ) totalQty   " +
            "FROM  " +
            "  promo_coupon_code_user   " +
            "WHERE  " +
            "  TENANT_CODE = #{tenantCode}   " +
            "  AND ACTIVITY_CODE = #{activityCode}  " +
            " <if test=\"id != null and id != ''\"> " +
            "  AND USER_CODE &gt; ${id} " +
            " </if> "+
            " GROUP BY  " +
            "  USER_CODE" +
            " ORDER BY  " +
            "  USER_CODE" +
            "</script>")
    List<ExportCouponUserResult> exportCouponUserCode(ExportCouponUserDto dto);//Operation SuiteOPS-2548 Promotion-券发放给N个会员，导出Execl表格，第一页只显示一条会员的数据



    @Select("<script>" +
            "SELECT  " +
            "   id," +
            "  USER_CODE userCode,  " +
            "  USED_REF_ID orderCode,  " +
            "  COUPON_CODE,  " +
            "CASE  " +
            "    `STATUS`   " +
            "    WHEN '01' THEN  " +
            "    'unused'   " +
            "    WHEN '02' THEN  " +
            "    'unused'   " +
            "    WHEN '03' THEN  " +
            "    'used'   " +
            "    WHEN '04' THEN  " +
            "    'locked'   " +
            "    WHEN '05' THEN  " +
            "    'expired'   " +
            "  END `STATUS`   " +
            "FROM  " +
            "  promo_coupon_code_user   " +
            "WHERE  " +
            "  TENANT_CODE = #{tenantCode}   " +
            "  AND ACTIVITY_CODE = #{activityCode}   " +
            " <if test=\"id != null and id != ''\"> " +
            "  AND id &gt; ${id} " +
            " </if> "+

            "ORDER BY  " +
            "  id" +
            "</script>")
    List<ExportCouponUserDetailResult> exportCouponOrderCode(ExportCouponUserDto dto);


    @Update("<script>  " +
            "<foreach item=\"item\" index=\"index\" collection=\"vos\" open=\"\" close=\"\" separator=\";\" >" +

            "update promo_coupon_code_user set STATUS = #{item.status} " +

            "  <if test=\"item.validStartTime  != null and item.validStartTime !='' \">" +
            "     , VALID_START_TIME= #{item.validStartTime}" +
            "  </if>"+

            "  <if test=\"item.validEndTime  != null and item.validEndTime !='' \">" +
            "     , VALID_END_TIME= #{item.validEndTime} " +
            "  </if>"+

            " where COUPON_CODE = #{item.couponCode} and TENANT_CODE = #{item.tenantCode}  " +
            "</foreach>" +

            "</script>")
    void updateBatchUserInnerCode(@Param("vos") List<TPromoCouponCodeUserVO> vos);

    
    @Select("<script>SELECT"
    		+ "	count(*) as count"
    		+ " FROM"
    		+ "	promo_coupon_code_user "
    		+ " WHERE"
    		+ " tenant_code = #{tenantCode}"
			+ "	AND ACTIVITY_CODE = #{activitCode}"
    		+ " AND STATUS IN "
    		+ " <foreach item=\"status\" index=\"index\" collection=\"statusList\" open=\"(\" separator=\",\" close=\")\"> #{status} </foreach>"
			+ " </script>")
	int queryCouponCountByCodeAndStataus(@Param("tenantCode") String tenantCode, @Param("activitCode") String activitCode,
			@Param("statusList") List<String> statusList);
}
