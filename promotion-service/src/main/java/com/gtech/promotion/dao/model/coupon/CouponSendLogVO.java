package com.gtech.promotion.dao.model.coupon;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
public class CouponSendLogVO implements Serializable {
    private static final long serialVersionUID = -5353769145991935754L;

    private Long id;

    private String tenantCode;

    private String activityCode;

    private String sendBatchNo;

    private String couponBatchNo;

    private Integer couponQty;

    private Integer userQty;

    private Integer status;

    private String failReason;

    private String createUser;

    private Date createTime;

    private String updateUser;

    private Date updateTime;

    private Integer failCount;

    private List<CouponSendDetailVO> couponSendDetailList;
}
