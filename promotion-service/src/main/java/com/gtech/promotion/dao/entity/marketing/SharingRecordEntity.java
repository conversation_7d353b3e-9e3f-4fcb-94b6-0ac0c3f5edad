package com.gtech.promotion.dao.entity.marketing;


import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

@Table(name = "marketing_sharing_record")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class SharingRecordEntity {

    @Id
    @Column(name = "id")
    private Long id;
    @Column(name = "activity_code")
    private String activityCode;
    @Column(name = "domain_code")
    private String domainCode;
    @Column(name = "tenant_code")
    private String tenantCode;
    @Column(name = "org_code")
    private String orgCode;
    @Column(name = "sharing_record_code")
    private String sharingRecordCode;
    @Column(name = "sharing_member_code")
    private String sharingMemberCode;
    @Column(name = "activity_status")
    private String activityStatus;
    @Column(name = "number_of_boost_sharing")
    private String numberOfBoostSharing;
    @Column(name = "number_of_people_who_have_helped")
    private String numberOfPeopleWhoHaveHelped;

    @Column(name="update_time")
    private Date updateTime;

    @Column(name="create_time")
    private Date createTime;


}
