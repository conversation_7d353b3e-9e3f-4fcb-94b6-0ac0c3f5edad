package com.gtech.promotion.dao.mapper.marketing;

import com.gtech.commons.dao.mapper.GTechBaseMapper;
import com.gtech.promotion.dao.entity.marketing.TicketEntity;
import com.gtech.promotion.domain.marketing.LuckyDrawMemberChanceDomain;
import com.gtech.promotion.vo.result.marketing.LuckyDrawMemberChanceResult;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

//@Mapper
public interface TicketMapper extends GTechBaseMapper<TicketEntity> {

    @Select("<script>" +
            "select " +
            "  T1.activity_code as activityCode," +
            "  COUNT(T1.activity_code) AS times," +
            "  (" +
            " SELECT COUNT(1) FROM `marketing_ticket` T1,  " +
            " `marketing` T2  WHERE T1.FROZEN_STATUS = '02' " +
            " AND T1.status = '01' " +
            " AND T1.`TENANT_CODE` = #{tenantCode} " +
            " AND T1.`DOMAIN_CODE` = #{domainCode} " +
            " <if test=\"activityCode != null and activityCode != '' \">" +
            "  AND T1.`ACTIVITY_CODE` = #{activityCode} " +
            " </if> " +
            " AND T1.`MEMBER_CODE` = #{memberCode} " +
            " AND T2.`TENANT_CODE` = #{tenantCode} " +
            " AND T2.`DOMAIN_CODE` = #{domainCode} " +
            " AND T1.`ACTIVITY_CODE` = T2.`ACTIVITY_CODE` " +
            " AND T2.`ACTIVITY_END` &gt; #{currDate} " +
            " AND T2.`ACTIVITY_STATUS` = '04' " +
            " AND T1.`LOGIC_DELETE` = '0' " +
            ") frozenCount," +
            "  ( case" +
            "      when " +
            "      (select count(activity_name) from marketing_language where activity_code = t1.`ACTIVITY_CODE` and TENANT_CODE = T1.`TENANT_CODE` and language = #{language}) = 0 " +
            "      then T2.ACTIVITY_NAME " +
            "      else " +
            "      (SELECT activity_name FROM marketing_language WHERE activity_code = t1.`ACTIVITY_CODE` and TENANT_CODE = T1.`TENANT_CODE` AND LANGUAGE = #{language}) " +
            "  end ) as activityName," +
            "  T2.`ACTIVITY_BEGIN` AS activityBegin," +
            "  T2.`ACTIVITY_END` AS activityEnd," +
            "  T2.`sponsors` " +
            "from" +
            "  `marketing_ticket` T1," +
            "  `marketing` T2 " +
            "where T1.status = '01' " +
            "  AND T1.`TENANT_CODE` = #{tenantCode} " +
            "  AND T1.`DOMAIN_CODE` = #{domainCode} " +
            //"  AND T1.`ORG_CODE` = #{orgCode} " +
            " <if test=\"activityCode != null and activityCode != '' \">" +
            "  AND T1.`ACTIVITY_CODE` = #{activityCode} " +
            " </if> " +
            "  AND T1.`MEMBER_CODE` = #{memberCode} " +
            "  AND T2.`TENANT_CODE` = #{tenantCode} " +
            "  AND T2.`DOMAIN_CODE` = #{domainCode} " +
            "  AND T1.`ACTIVITY_CODE` = T2.`ACTIVITY_CODE` " +
            "  AND T2.`ACTIVITY_END` &gt; #{currDate} " +
            "  AND T2.`ACTIVITY_STATUS` = '04' " +
            "  AND T1.`LOGIC_DELETE` = '0' " +
            "GROUP BY T1.activity_code " +
            "order by T1.id desc " +
            "</script>")
    List<LuckyDrawMemberChanceResult> queryChanceList(LuckyDrawMemberChanceDomain domain);



    @Select("<script>" +
            "SELECT " +
            "COUNT(1) AS count " +
            "FROM " +
            "marketing_ticket " +
            "WHERE " +
            "ACTIVITY_CODE = #{activityCode} " +
            "AND MEMBER_CODE = #{memberCode} " +
            "AND DOMAIN_CODE = #{domainCode} " +
            "AND TENANT_CODE = #{tenantCode} " +
            "AND `STATUS` != '01' " +
            "AND `LOGIC_DELETE` = '0' " +
            " <if test=\"beginTime != null and beginTime != '' \">" +
            "AND USE_TIME &gt;= #{beginTime} " +
            " </if> " +
            " <if test=\"endTime != null and endTime != '' \">" +
            "AND USE_TIME &lt;= #{endTime} "+
            " </if> " +
            "</script>")
    Integer selectCountByMemberCode(@Param("activityCode") String activityCode,@Param("domainCode") String domainCode,@Param("tenantCode") String tenantCode, @Param("memberCode") String memberCode,@Param("beginTime")  String beginTime,@Param("endTime")  String endTime);
}
