package com.gtech.promotion.dao.entity.purchaseconstraint;

import lombok.Data;

import java.util.Date;
import javax.persistence.*;

@Data
@Table(name = "promo_purchase_constraint_rule")
public class PurchaseConstraintRuleEntity {
    @Id
    @Column(name = "ID")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 租户编码
     */
    @Column(name = "TENANT_CODE")
    private String tenantCode;

    /**
     * 系统域编码，系统拥有者编码
     */
    @Column(name = "DOMAIN_CODE")
    private String domainCode;

    /**
     * 组织编号
     */
    @Column(name = "ORG_CODE")
    private String orgCode;

    /**
     * 限购条件编码
     */
    @Column(name = "PURCHASE_CONSTRAINT_CODE")
    private String purchaseConstraintCode;

    /**
     * 限购条件类型
     * 0:CUSTOMER_MAX_QTY_PER_PRODUCT,1:CUSTOMER_MAX_QTY_ALL_PRODUCTS,2:CUSTOMER_MAX_AMOUNT,3:ORDER_MAX_QTY_PER_SKU,
     * 4:ORDER_MAX_QUANTITY,5:ORDER_MAX_AMOUNT_PER_SKU,6:ORDER_MAX_AMOUNT
     */
    @Column(name = "PURCHASE_CONSTRAINT_RULE_TYPE")
    private Integer purchaseConstraintRuleType;

    /**
     * 限购条件值 可能是金额也可能是数量
     */
    @Column(name = "PURCHASE_CONSTRAINT_VALUE")
    private String purchaseConstraintValue;

    /**
     * 限购条件统计时间类型  1: YEARLY, 2:MONTHLY, 3:WEEKLY
     */
    @Column(name = "PURCHASE_CONSTRAINT_RULE_TIME_TYPE")
    private Integer purchaseConstraintRuleTimeType;

    /**
     * 限购条件统计时间值 YEARLY:MMdd, MONTHLY: dd, WEEKLY:0-6
     */
    @Column(name = "PURCHASE_CONSTRAINT_RULE_TIME_VALUE")
    private String purchaseConstraintRuleTimeValue;

    @Column(name = "CREATE_TIME")
    private Date createTime;

    @Column(name = "UPDATE_TIME")
    private Date updateTime;

}