package com.gtech.promotion.dao.model.marketing;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.Date;

@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
public class PrizeRecordModel extends BaseModel {

    // 奖品编号（系统生成，全局唯一）
    private String prizeNo;
    private String memberCode;
    // 领取状态，01-未领取 02-已领取 03-领取失败
    private String status;
    private String ticketCode;
    private Date createTime;
}
