package com.gtech.promotion.dao.model.marketing.flashsale;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * @Description :
 * <AUTHOR> gaojie.li
 * @Date : 2023/12/29 10:52
 */
@Data
public class OrderOutMode implements Serializable {


    private String orderCode;

    private BigDecimal amount;

    private String payStatus;


    private List<OrderProductOutMode> orderProductOutList;

}
