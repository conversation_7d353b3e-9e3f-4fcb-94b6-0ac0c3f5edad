/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.dao.model.activity;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import com.gtech.promotion.code.PromotionConstants;
import com.gtech.promotion.vo.bean.ProductAttribute;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * <功能描述> 活动商品实体
 * 
 */
@Getter
@Setter
@ToString
public class TPromoActivityProductVO implements Serializable {

    private static final long serialVersionUID = -2084701821891186799L;

    // Activity code
    private String activityCode;

    // 商品范围层级id:默认1
    private Integer seqNum = 1;

    // 品类编码：0000-全部品类
    private String categoryCode = PromotionConstants.UNLIMITED;
    private String categoryName = PromotionConstants.UNLIMITED_DESC;

    // 品牌编码：0000-全部品牌
    private String brandCode = PromotionConstants.UNLIMITED;
    private String brandName = PromotionConstants.UNLIMITED_DESC;

    private List<ProductAttribute> attributes;
	// SPU属性
	private List<ProductAttribute> spuAttributes;
    private String attrType;

    private String orgCode;

	private String productTag;


    public void setAttributes(List<ProductAttribute> attributes) {

        if (CollectionUtils.isEmpty(attributes)) {
            this.attributes = null;
        } else {
            for(ProductAttribute a : attributes) {
                addAttribute(a);
            }
        }
    }
    public void addAttribute(ProductAttribute attribute) {
        
        if (null == attribute || this.isUnlimited(attribute.getAttributeCode())) {
            return;
        }
        if (null == this.attributes) {
            this.attributes = new ArrayList<>();
        }
        this.attributes.add(attribute);
    }

	public void setSpuAttributes(List<ProductAttribute> spuAttributes) {

		if (CollectionUtils.isEmpty(spuAttributes)) {
			this.spuAttributes = null;
		} else {
			for (ProductAttribute a : spuAttributes) {
				addSpuAttribute(a);
			}
		}
	}

	public void addSpuAttribute(ProductAttribute attribute) {

		if (null == attribute || this.isUnlimited(attribute.getAttributeCode())) {
			return;
		}
		if (null == this.spuAttributes) {
			this.spuAttributes = new ArrayList<>();
		}
		this.spuAttributes.add(attribute);
	}

    private boolean isUnlimited(String code) {

        return StringUtils.isBlank(code) || PromotionConstants.UNLIMITED.equals(code);
    }

    public static boolean moreThanOneSeq(List<TPromoActivityProductVO> list) {
        
        if (CollectionUtils.isEmpty(list)) {
            return false;
        }

        for(TPromoActivityProductVO e : list) {
            if (e.seqNum > 1) {
                return true;
            }
        }

        return false;
    }
}