package com.gtech.promotion.dao.entity.marketing;

import com.gtech.promotion.dao.handler.ExtDescriptionTypeHandler;
import com.gtech.promotion.vo.bean.ExtDescription;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import tk.mybatis.mapper.annotation.ColumnType;

import javax.persistence.Column;
import javax.persistence.Table;
import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Table(name = "MARKETING_LANGUAGE")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class MarketingLanguageEntity extends BaseEntity {

    public static final String C_LANGUAGE = "language";

    private String activityCode;
    private String activityName;
    private String activityLabel;
    private String activityDesc;
    private String activityShortDesc;
    private String language;

    @Column(name = "EXT_DESCRIPTIONS")
    @ColumnType(typeHandler = ExtDescriptionTypeHandler.class)
    private List<ExtDescription> extDescriptions;

}
