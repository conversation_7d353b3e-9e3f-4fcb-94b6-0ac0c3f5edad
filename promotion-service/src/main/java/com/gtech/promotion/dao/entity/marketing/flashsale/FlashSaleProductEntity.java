package com.gtech.promotion.dao.entity.marketing.flashsale;

import com.gtech.promotion.dao.entity.marketing.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import javax.persistence.Table;
import java.math.BigDecimal;

@EqualsAndHashCode(callSuper = true)
@Table(name = "flash_sale_product")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class FlashSaleProductEntity extends BaseEntity {

    public static final String SKU_CODE = "skuCode";
    public static final String SPU_CODE = "productCode";
    private String activityCode;
    private String skuCode;
    private String skuName;
    private Integer skuQuota;
    private Integer skuInventory;
    private Integer maxPerUser;
    private BigDecimal listPrice;
    private BigDecimal salePrice;
    private BigDecimal flashPrice;
    //
    private BigDecimal groupLeaderPrice;
    private Integer limitFlag;
    private String productCode;
    private String spuName;

    private String extendParam;
    private Integer maxPerUserFlag;



}
