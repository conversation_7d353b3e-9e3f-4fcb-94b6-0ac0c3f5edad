package com.gtech.promotion.dao.model.activity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * @Description :
 * <AUTHOR> gaojie.li
 * @Date : 2023/6/14 9:48
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class PromoGroupMode {

        /**
     * 租户编码
     */
    private String tenantCode;

    private String domainCode;

    /**
     * 分组编码
     */
    private String groupCode;

    /**
     * 活动分组名称
     */
    private String groupName;

    /**
     * 优先级，数字越小约优先
     */
    private Long priority;

    /**
     * 分组类型 01:默认分组 02：自定义分组
     */
    private String type;

    private String createUser;

    // Create time.
    private Date createTime;

    // Lastest update user.
    private String updateUser;

    // Lastest update time.
    private Date updateTime;

    private String logicDelete;
    private String extParam;

}
