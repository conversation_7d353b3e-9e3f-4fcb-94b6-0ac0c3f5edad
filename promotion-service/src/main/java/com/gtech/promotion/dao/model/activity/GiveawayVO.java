/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.dao.model.activity;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;

/**
 * 赠品数据
 */
@Getter
@Setter
@ToString
public class GiveawayVO implements Serializable {
    
    private static final long serialVersionUID = 3466526908754782510L;

    private String id;

    // Tenant code
    private String tenantCode;

    // Activity code
    private String activityCode;

    // SKU编码
    private String giveawayCode;

    // SKU名称
    private String giveawayName;

    // 赠送数量
    private int giveawayNum;

    // Giveaway type. 1:sku 2:coupon 4:营销-抽奖次数
    private int giveawayType;

    private String opsType;

    private Integer rankParam;

    /**
     * 赠品序号
     */
    private Integer giveawaySort;

}