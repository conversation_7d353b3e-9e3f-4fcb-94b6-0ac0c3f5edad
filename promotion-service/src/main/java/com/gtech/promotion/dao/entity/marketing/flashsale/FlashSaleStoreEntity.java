package com.gtech.promotion.dao.entity.marketing.flashsale;

import com.gtech.promotion.dao.entity.marketing.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import javax.persistence.Table;

@EqualsAndHashCode(callSuper = true)
@Table(name = "flash_sale_store")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class FlashSaleStoreEntity extends BaseEntity {

    private String activityCode;
    private String storeName;
}
