/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * <PERSON>ECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.dao.model.coupon;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.gtech.commons.utils.DateUtil;
import com.gtech.promotion.domain.coupon.CouponReleaseDomain;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.Date;


/**   
 * 
 */
@Getter
@Setter
@ToString
public class TPromoCouponCodeUserVO {
    
    private String id;
    private String createUser;
    private String logicDelete;

    // Tenant code
    private String tenantCode;//2

    // Activity code
    private String activityCode;


    // 优惠口令
    private String promoPassword;

    // Coupon release code.
    private String releaseCode;
  
    // 券类型 ：01：优惠券 02:匿名券 03：优惠码
    private String couponType;

    // 券OPS类型：201- 202- 203- 204-
    private String opsType;
    
    // 券状态 01-未发放 02-已发放 03-已使用 04-已锁定 05-已过期
    private String status;//3
 
    // 券冻结状态 01-未冻结 02-已冻结
    private String frozenStatus;

    // 券码编号
    private String couponCode;

    // 用户编号
    private String userCode;//1
    
    // 券面值 01：金额 02：折扣
    private BigDecimal faceValue;
    
    // 券面值单位 01：金额 02：折扣
    private String faceUnit;
    
    // 领取标签:01-免费领取、02-活动赠券、03-促销赠券、04-积分换购、05-有价券、06-新人券、07-生日券、08-补偿券、09-推送券、10-匿名券 11-其他；
    private String takeLabel;//4
    private String couponSource;

    // 可用开始时间(yyyyMMddhhmmss)
    private String validStartTime;

    // 可用结束时间(yyyyMMddhhmmss)
    private String validEndTime;

    // 领券时间
    private String receivedTime;

    // 使用时业务编号
    private String usedRefId;

    // 使用时间(yyyyMMddHHmmss)
    private String usedTime;

    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    public void setValidTime(CouponReleaseDomain releaseDomain, String activityEnd) {
        long now = Long.parseLong(DateUtil.format(new Date(), DateUtil.FORMAT_YYYYMMDDHHMISS_14));
        if (null != releaseDomain.getValidDays()){
            setValidStartTime(String.valueOf(now));
            String endDate = DateUtil.format(DateUtil.addDay(releaseDomain.getValidDays()), DateUtil.FORMAT_YYYYMMDDHHMISS_14);
            if (Long.parseLong(endDate) > Long.parseLong(activityEnd)){
                //可用结束时间不能超过活动结束时间
                setValidEndTime(activityEnd);
            }else{
                setValidEndTime(endDate);
            }
        }else{
            setValidStartTime(releaseDomain.getValidStartTime());
            setValidEndTime(releaseDomain.getValidEndTime());
        }
    }
}
