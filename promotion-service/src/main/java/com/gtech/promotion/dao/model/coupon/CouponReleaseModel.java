/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.dao.model.coupon;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.apache.commons.collections4.CollectionUtils;

import java.util.List;

/**   
 * 
 */
@Getter
@Setter
@ToString
public class CouponReleaseModel {

    // Tenant code
    private String tenantCode;

    // Activity code
    private String activityCode;

    // Coupon type: 01-优惠券 02-匿名券 03-优惠码
    private String couponType;

    // Coupon release code
    private String releaseCode;

    // Coupon release status: 01-Waiting released 02-Already released 03-Imported 04-Cancel released
    private String releaseStatus;

    // Release coupon quantity: releaseQuantity fixed 1 while couponType=03
    private Integer releaseQuantity;

    // Remaining available stock.
    private Integer inventory;

    // Coupon release source: 01-System generate 02-Imported
    private String releaseSource;

    // Coupon release type: 01-immediately, 02-appointment
    private String releaseType;

    // Appointment time of coupon release: yyyyMMddhhmmss, required while releaseType=02
    private String releaseTime;

    // Receive coupon start time: yyyyMMddhhmmss
    private String receiveStartTime;

    // Receive coupon end time: yyyyMMddhhmmss
    private String receiveEndTime;

    // Validity date begin: yyyyMMddhhmmss
    private String validStartTime;

    // Validity date end: yyyyMMddhhmmss
    private String validEndTime;

    // Validity days from received date.
    private Integer validDays;

    private String couponCodePrefix;

    private String couponRuleType;


    private Integer couponCodeLength;

    private String timeSameActivity;

    private String receiveTimeSameActivity;

    private String promotionType;

    private String remark;
    private String createUser;

    public static int countReleaseQuantity(List<CouponReleaseModel> list) {

        if (CollectionUtils.isEmpty(list)) {
            return 0;
        }

        int count = 0;
        for (CouponReleaseModel e : list) {
            count += e.getReleaseQuantity();
        }

        return count;
    }

    public static int countInventory(List<CouponReleaseModel> list) {

        if (CollectionUtils.isEmpty(list)) {
            return 0;
        }

        int count = 0;
        for (CouponReleaseModel e : list) {
            count += e.getInventory();
        }

        return count;
    }
}
