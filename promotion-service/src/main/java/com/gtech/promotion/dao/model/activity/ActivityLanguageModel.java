/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.dao.model.activity;

import com.gtech.promotion.vo.bean.ExtDescription;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;
/**   
 * 多语言信息对象
 */
@Getter
@Setter
@ToString
public class ActivityLanguageModel implements Serializable {

    private static final long serialVersionUID = -2576016528315611000L;

    private Long id;

    // Tenant code.
    private String tenantCode;

    // Activity code.
    private String activityCode;

    // Activity type: 01-活动 02-券
    private String activityType;

    // Activity label
    private String activityLabel;

    // Activity name.
    private String activityName;

    // Activity description
    private String activityDesc;

    // Activity remark
    private String activityRemark;

    // Language identity
    private String language;


    private List<ExtDescription> extDescriptions;

}
