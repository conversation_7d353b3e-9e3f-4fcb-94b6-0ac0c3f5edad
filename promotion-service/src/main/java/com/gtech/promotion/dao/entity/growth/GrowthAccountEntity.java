package com.gtech.promotion.dao.entity.growth;

import lombok.*;

import javax.persistence.*;
import java.util.Date;

@Table(name = "growth_account")
@Getter
@Setter
@ToString
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GrowthAccountEntity {

    public static final String C_DOMAIN_CODE = "domainCode";
    public static final String C_TENANT_CODE = "tenantCode";
    public static final String C_GROWTH_ACCOUNT_CODE = "growthAccountCode";
    public static final String C_ACCOUNT_CODE = "accountCode";
    public static final String C_ACCOUNT_TYPE = "accountType";

    // Primary key.
	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	private Long id;

	// Domain code.
	@Column(name = "domain_code")
	private String domainCode;

	// Tenant code.
	@Column(name = "tenant_code")
	private String tenantCode;

	// Growth account code.
	@Column(name = "growth_account_code")
	private String growthAccountCode;

	// Account code. (UserCode or OrgCode)
	@Column(name = "account_code")
	private String accountCode;

	// Growth account type. (1-User 2-Organization)
	@Column(name = "account_type")
	private Integer accountType;

	// Growth account description.
	@Column(name = "account_desc")
	private String accountDesc;

	// Latest account points.
	@Column(name = "account_balance")
	private Integer accountBalance;

    // Account grade.
    @Column(name = "account_grade")
    private Integer accountGrade;

	// Growth account status.(0-Inactive 1-Active)
	private Integer status;

	// Extends parameters. (JSON String)
	@Column(name = "ext_params")
	private String extParams;

	// Create user code
	@Column(name = "create_user")
	private String createUser;

	// Create time.
	@Column(name = "create_time")
	private Date createTime;

	// Lastest update user.
	@Column(name = "update_user")
	private String updateUser;

	// Lastest update time.
	@Column(name = "update_time")
	private Date updateTime;

	// Valid begin time.
	@Column(name = "valid_begin_time")
	private Date validBeginTime;

	// Valid end time.
	@Column(name = "valid_end_time")
	private Date validEndTime;
}