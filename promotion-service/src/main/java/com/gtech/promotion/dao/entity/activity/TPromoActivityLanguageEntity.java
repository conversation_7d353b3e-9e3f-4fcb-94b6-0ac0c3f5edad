/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * <PERSON>ECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.dao.entity.activity;

import com.gtech.promotion.dao.handler.ExtDescriptionTypeHandler;
import com.gtech.promotion.vo.bean.ExtDescription;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import tk.mybatis.mapper.annotation.ColumnType;

import javax.persistence.*;
import java.util.Date;
import java.util.List;

/**
 * 活动基础数据
 * 
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "promo_activity_language")
public class TPromoActivityLanguageEntity {

    // ID
    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    // Tenant code
    @Column(name = "TENANT_CODE")
    private String tenantCode;

    // Activity code
    @Column(name = "ACTIVITY_CODE")
    private String activityCode;

    // Activity label
    @Column(name = "ACTIVITY_LABEL")
    private String activityLabel;

    // Activity name
    @Column(name = "ACTIVITY_NAME")
    private String activityName;

    // Activity description
    @Column(name = "ACTIVITY_DESC")
    private String activityDesc;
    
    // Activity remark
    @Column(name = "ACTIVITY_REMARK")
    private String activityRemark;

    // Language id. (en-US/id-ID/zh-CN/...)
    @Column(name = "LANG")
    private String language;

    // Create time.
    @Column(name="create_time")
    private Date createTime;

    // Lastest update time.
    @Column(name="update_time")
    private Date updateTime;

    @Column(name = "EXT_DESCRIPTIONS")
    @ColumnType(typeHandler = ExtDescriptionTypeHandler.class)
    private List<ExtDescription> extDescriptions;


}