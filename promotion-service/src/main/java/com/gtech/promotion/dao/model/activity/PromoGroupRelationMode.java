package com.gtech.promotion.dao.model.activity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * @Description :
 * <AUTHOR> gaojie.li
 * @Date : 2023/6/14 9:48
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class PromoGroupRelationMode {

    /**
     * 租户编码
     */
    private String tenantCode;

    /**
     * 分组编码
     */
    private String groupCodeA;

    /**
     * 分组编码
     */
    private String groupCodeB;


    private String createUser;

    // Create time.
    private Date createTime;

    // Lasted update user.
    private String updateUser;

    // Lasted update time.
    private Date updateTime;

    private String logicDelete;

}
