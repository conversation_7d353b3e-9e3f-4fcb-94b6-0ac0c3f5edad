package com.gtech.promotion.dao.entity.marketing.flashsale;

import com.gtech.promotion.dao.entity.marketing.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import javax.persistence.Table;

@EqualsAndHashCode(callSuper = true)
@Table(name = "flash_sale_order_detail")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class FlashSaleOrderDetailEntity extends BaseEntity {
    public static final String ORDER_ID = "orderId";

    private String productCode;

    private String activityCode;
    private String orderId;
    private String skuCode;
    private Integer skuQuality;
}
