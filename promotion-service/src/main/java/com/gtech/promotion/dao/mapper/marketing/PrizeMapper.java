package com.gtech.promotion.dao.mapper.marketing;

import com.gtech.commons.dao.mapper.GTechBaseMapper;
import com.gtech.promotion.dao.entity.marketing.PrizeEntity;
import com.gtech.promotion.dao.model.marketing.PrizeModel;
import org.apache.ibatis.annotations.Update;

//@Mapper
public interface PrizeMapper extends GTechBaseMapper<PrizeEntity> {

    @Update(" update MARKETING_PRIZE set PRIZE_INVENTORY = PRIZE_INVENTORY - 1"
            + " where TENANT_CODE = #{tenantCode} and ACTIVITY_CODE = #{activityCode} and PRIZE_NO = #{prizeNo} and PRIZE_INVENTORY >= 1")
    int deductInventory(PrizeModel prizeModel);
}
