package com.gtech.promotion.dao.entity.marketing;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import javax.persistence.Table;

/**
 * @Description :
 * <AUTHOR> gaojie.li
 * @Date : 2023/10/17 16:52
 */
@EqualsAndHashCode(callSuper = true)
@Table(name = "marketing_group_code")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class MarketingGroupCodeEntity extends BaseEntity{

    public static final String C_MARKETING_GROUP_CODE = "marketingGroupCode";

    public static final String C_MARKETING_GROUP_STATUS = "groupStatus";


    @Column(name="ACTIVITY_CODE")
    private String activityCode;
    /**
     * 拼团业务编码
     */
    @Column(name="MARKETING_GROUP_CODE")
    private String marketingGroupCode;


    @Column(name = "INVENTORY")
    private Integer inventory;

    /**
     * 主表拼团状态 00 未开始 01 进行中，02 拼团成功 03 拼团失败
     */
    @Column(name="GROUP_STATUS")
    private String groupStatus;

    /***
     * 有效截止时间
     */
    @Column(name="EFFECTIVE_TIME")
    private String effectiveTime;




}
