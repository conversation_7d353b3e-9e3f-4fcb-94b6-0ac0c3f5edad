package com.gtech.promotion.dao.entity.point;

import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

@Table(name = "point_transaction")
@Data
public class PointTransactionEntity {
    public static final String C_TRANSACTION_SN = "transactionSn";
    public static final String C_CREATE_TIME = "createTime";
    /**
     * Primary key.
     */
    @Id
    private Long id;

    /**
     * Domain code.
     */
    @Column(name = "domain_code")
    private String domainCode;

    /**
     * Tenant code.
     */
    @Column(name = "tenant_code")
    private String tenantCode;

    /**
     * Transaction serial number.
     */
    @Column(name = "transaction_sn")
    private String transactionSn;

    /**
     * Point account code. (UserCode or OrgCode)
     */
	@Column(name = "account_code")
	private String accountCode;

    /**
     * Point account type. (1-User 2-Organization)
     */
    @Column(name = "account_type")
	private Integer accountType;

    /**
     * Point campaign code.
     */
    @Column(name = "campaign_code")
    private String campaignCode;

    /**
     * Point transaction type. (1-Increase points 2-Deduct points)
     */
    @Column(name = "transaction_type")
	private Integer transactionType;

    /**
     * Point transaction remarks.
     */
    @Column(name = "transaction_remarks")
    private String transactionRemarks;

    /**
     * Point spent/earned in the transaction.
     */
    @Column(name = "transaction_amount")
    private Integer transactionAmount;

    /**
     * Transaction date. (yyyyMMddHHmmss)
     */
    @Column(name = "transaction_date")
    private Long transactionDate;

    /**
     * Refer transaction serial number.
     */
    @Column(name = "refer_transaction_sn")
    private String referTransactionSn;

    /**
     * Refer order number.
     */
    @Column(name = "refer_order_number")
    private String referOrderNumber;

    /**
     * Create user code
     */
    @Column(name = "create_user")
    private String createUser;

    /**
     * Create time.
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * Lastest update user.
     */
    @Column(name = "update_user")
    private String updateUser;

    /**
     * Lastest update time.
     */
    @Column(name = "update_time")
    private Date updateTime;

    /**
     *
     */
    @Column(name = "expiration")
    private String expiration;

    /**
     * Lastest update time.
     */
    @Column(name = "balance")
    private Integer balance;

    /**
     *
     */
    @Column(name = "operation")
    private Integer operation;
}