/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.dao.model.activity;

import java.io.Serializable;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 活动渠道
 * 
 */
@Getter
@Setter
@ToString
public class TPromoActivityStoreVO implements Serializable {

    private static final long serialVersionUID = -7743912391855195734L;

    private String id;

    // Tenant code
    private String tenantCode;

    // Activity code
    private String activityCode;

    // 渠道编码
    private String channelCode;

    // 渠道名称
    private String channelName;

    // 店铺编码
    private String orgCode;

    // 店铺名称
    private String storeName;
    
    // 活动链接
    private String url;

}