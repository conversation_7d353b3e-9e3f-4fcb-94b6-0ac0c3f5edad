package com.gtech.promotion.dao.mapper.point;

import java.util.List;
import java.util.Map;

import org.springframework.stereotype.Repository;

import com.gtech.promotion.dao.entity.point.PointAccountEntity;

import tk.mybatis.mapper.common.Mapper;

@Repository
public interface PointAccountMapper extends Mapper<PointAccountEntity> {
    List<PointAccountEntity> query(Map<String, Object> parameters);

	int updatePoint(Map<String, Object> param);

}