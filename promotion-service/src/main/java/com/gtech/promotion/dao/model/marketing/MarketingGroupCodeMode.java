package com.gtech.promotion.dao.model.marketing;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Description :
 * <AUTHOR> gaojie.li
 * @Date : 2023/6/5 15:35
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class MarketingGroupCodeMode extends BaseModel {

    private String activityCode;

    /**
     * 拼团业务编码
     */
    private String marketingGroupCode;


    private int inventory;

    /**
     * 有效截止时间
     */
    private String effectiveTime;

    /**
     * 主表拼团状态 01 进行中，02 拼团成功 03 拼团失败
     */
    private String groupStatus;

    // Create time.
    private String date;

    private String updateDate;


}
