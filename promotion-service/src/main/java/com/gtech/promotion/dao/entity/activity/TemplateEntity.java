/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * <PERSON>ECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.dao.entity.activity;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**   
 * <功能描述>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "promo_template")
public class TemplateEntity {

    // ID
    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    // Template code
    @Column(name = "TEMPLATE_CODE")
    private String templateCode;

    // Template name
    @Column(name = "TEMPLATE_NAME")
    private String templateName;

    // Template tag code
    @Column(name = "TAG_CODE")
    private String tagCode;

    // Template description
    @Column(name = "TEMPLATE_DESC")
    private String templateDesc;

    // Template language: 参考Java的Locale枚举，默认：zh-cn
    @Column(name = "LOCALE")
    private String locale;

    // Create user code.
    @Column(name="create_user")
    private String createUser;

    // Create time.
    @Column(name="create_time")
    private Date createTime;

    // Lastest update user.
    @Column(name="update_user")
    private String updateUser;

    // Lastest update time.
    @Column(name="update_time")
    private Date updateTime;

}