package com.gtech.promotion.dao.model.marketing.flashsale;

import com.gtech.promotion.dao.model.marketing.BaseModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
public class FlashSaleOrderDetailModel extends BaseModel {

    private String productCode;
    private String orderId;
    private String skuCode;
    private Integer skuQuality;
}
