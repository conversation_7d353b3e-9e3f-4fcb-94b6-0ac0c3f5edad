package com.gtech.promotion.dao.model.marketing;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
public class TicketModel extends BaseModel {

    private String releaseCode;
    private String ticketCode;
    private List<String> ticketCodeList;
    private String memberCode;
    // 状态：01-已发放 02-已使用（未中奖） 03-已中奖
    private String status;
    private String useTime;
    private String luckyTime;
    private String frozenStatus;
    private String order;
}
