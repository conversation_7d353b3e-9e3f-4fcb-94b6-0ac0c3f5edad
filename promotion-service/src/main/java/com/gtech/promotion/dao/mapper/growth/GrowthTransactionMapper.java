package com.gtech.promotion.dao.mapper.growth;

import java.util.List;
import java.util.Map;

import org.springframework.stereotype.Repository;

import com.gtech.promotion.dao.entity.growth.GrowthTransactionEntity;

import tk.mybatis.mapper.common.Mapper;

@Repository
public interface GrowthTransactionMapper extends Mapper<GrowthTransactionEntity> {
    List<GrowthTransactionEntity> query(Map<String, Object> parameters);

}