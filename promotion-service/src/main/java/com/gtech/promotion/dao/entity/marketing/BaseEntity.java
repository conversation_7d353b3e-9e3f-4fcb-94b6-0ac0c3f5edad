package com.gtech.promotion.dao.entity.marketing;

import lombok.Data;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import java.util.Date;

@Data
public class BaseEntity {

    public static final String C_ID = "id";
    public static final String C_TENANT_CODE = "tenantCode";
    public static final String C_DOMAIN_CODE = "domainCode";
    public static final String C_ORG_CODE = "orgCode";
    public static final String C_ACTIVITY_CODE = "activityCode";
    public static final String C_SKU_CODE = "skuCode";
    public static final String C_LOGIC_DELETE = "logicDelete";

    // ID
    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    // Domain code.
    @Column(name = "domain_code")
    private String domainCode;

    // Tenant code
    @Column(name = "TENANT_CODE")
    private String tenantCode;

    // org code.
    @Column(name = "org_code")
    private String orgCode;

    // Create user code.
    @Column(name="create_user")
    private String createUser;

    // Create time.
    @Column(name="create_time")
    private Date createTime;

    // Lastest update user.
    @Column(name="update_user")
    private String updateUser;

    // Lastest update time.
    @Column(name="update_time")
    private Date updateTime;

    // Logic delete. (0-normal 1-deleted)
    @Column(name="logic_delete")
    private Integer logicDelete;
}
