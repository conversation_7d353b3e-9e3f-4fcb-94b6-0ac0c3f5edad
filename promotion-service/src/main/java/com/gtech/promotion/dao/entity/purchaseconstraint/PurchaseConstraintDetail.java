package com.gtech.promotion.dao.entity.purchaseconstraint;

import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
@Table(name = "promo_purchase_constraint_detail")
public class PurchaseConstraintDetail implements Serializable {
    private static final long serialVersionUID = 556879924027132531L;
    @Id
    @Column(name = "ID")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    /**
     * 租户编码
     */
    private String tenantCode;
    /**
     * 条件限购唯一码,多个以逗号分割
     */
    private String customerCode;
    /**
     * 限购条件编码
     */
    private String purchaseConstraintCode;
    /**
     * 限购订单号
     */
    private String orderId;
    /**
     * 限购商品编码
     */
    private String productCode;
    /**
     * 限购SKU编码
     */
    private String skuCode;
    /**
     * 占用限购金额
     */
    private BigDecimal detailAmount;
    /**
     * 占用限购数量
     */
    private Integer detailQty;
    /**
     * 限购回退金额
     */
    private BigDecimal detailAmountBack;
    /**
     * 限购回退数量
     */
    private Integer detailQtyBack;

    private Date createTime;

    private Date updateTime;

}

