/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * <PERSON>ECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.component.impl.feign;

import java.util.HashMap;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.gtech.commons.utils.StringUtil;
import com.gtech.promotion.component.config.feign.BasicClient;
import com.gtech.promotion.component.feign.BasicClientDomain;

@Service
public class BasicClientDomainImpl implements BasicClientDomain{

    @Autowired
    private BasicClient basicClient;

    @Override
    public Map<String, String> getLanguages(String parameter){
        JSONObject obj = new JSONObject();
        obj.put("ddCode", parameter);
        JSONObject result = basicClient.getLanguages(obj, new HashMap<>());
        //得到所需对象
        Map<String, String> map = new HashMap<>();
        JSONArray array = result.getJSONArray("data");
        if (array != null && !array.isEmpty()){
            for (int i = 0; i < array.size(); i++){
                JSONObject jsonObject = array.getJSONObject(i);
                if (StringUtil.isNotBlank(jsonObject.getString("text")) && StringUtil.isNotBlank(jsonObject.getString("value"))){
                    map.put(jsonObject.getString("value"), jsonObject.getString("text"));
                }
            }
        }
        return map;
    }

}
