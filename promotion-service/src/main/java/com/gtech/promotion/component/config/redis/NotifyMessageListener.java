/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * <PERSON>ECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.component.config.redis;

import com.gtech.promotion.code.coupon.CouponStatusEnum;
import com.gtech.promotion.code.purchaseconstraint.PurchaseConstraintStatusEnum;
import com.gtech.promotion.component.activity.ActivityExpireComponentDomain;
import com.gtech.promotion.component.coupon.CouponReleaseComponent;
import com.gtech.promotion.component.marketing.MarketingGroupComponent;
import com.gtech.promotion.component.marketing.MarketingCacheComponent;
import com.gtech.promotion.service.coupon.PromoCouponCodeUserService;
import com.gtech.promotion.service.coupon.PromoCouponInnerCodeService;
import com.gtech.promotion.service.purchaseconstraint.PurchaseConstraintService;
import com.gtech.promotion.utils.Constants;
import com.gtech.promotion.utils.MarketingConstants;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.connection.Message;
import org.springframework.data.redis.connection.MessageListener;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.util.concurrent.TimeUnit;

/**
 * redis键过期消息监听
 */
@Service
@Slf4j
public class NotifyMessageListener implements MessageListener{

    public static final String COUPONCODE = "COUPONCODE";
    private final Object lock = new Object();

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    @Autowired
    private PromoCouponInnerCodeService innerCodeService;

    @Autowired
    private PromoCouponCodeUserService couponUserService;

    @Autowired
    private CouponReleaseComponent couponReleaseComponent;

    @Autowired
    private ActivityExpireComponentDomain activityExpireComponentDomain;

    @Autowired
    private MarketingCacheComponent marketingCacheComponent;

    @Autowired
    private PurchaseConstraintService purchaseConstraintService;


    @Autowired
    private MarketingGroupComponent marketingGroupComponent;

    private static final String ACTIVITY_CODE = "ACTIVITYCODE";
    private static final String TENANTCODE = "TENANTCODE";

    @Override
    public void onMessage(Message message,byte[] pattern){
        byte[] body = message.getBody();
        String itemValue = new String(body);

        if (isNotify(itemValue)){
            // 分布式锁，同一条消息全局只能由一个节点处理
            String lockKey = Constants.PROMOTION_TASK_LOCK_KEY + itemValue;
            Boolean aBoolean = stringRedisTemplate.opsForValue().setIfAbsent(lockKey, itemValue, 10000L, TimeUnit.MILLISECONDS);
            if (null != aBoolean && aBoolean){
                synchronized (lock){
                    log.info("redis 键过期：itemValue:{}", itemValue);
                    try {
                        if (itemValue.startsWith(Constants.PROMOTION_ACTIVITY_CACHE)){// 活动到期
                            activityExpire(itemValue);
                        }else if (itemValue.startsWith(Constants.PROMOTION_COUPON_USER)){// 领取后的券码可用时间到期
                            //PROMOTION_COUPON_USER:TENANTCODE=666666:ACTIVITYID=231:USERCODE=60:COUPONCODE=614066675008707014
                            canUseExpire(itemValue);
                        }else if (itemValue.startsWith(Constants.PROMOTION_COUPON_RELEASE_TIME)){
                            couponReleaseComponent.couponReleaseTimer();
                        }else if (itemValue.startsWith(MarketingConstants.MARKETING_ACTIVITY_CACHE)){
                            marketingActivityExpire(itemValue);
                        }else if(itemValue.startsWith(Constants.PURCHASE_CONSTRAINT_CACHE_KEY_PREFIX)){
                            purchaseConstraintExpire(itemValue);
                        }else if (itemValue.startsWith(MarketingConstants.MARKETING_GROUP_USER_CACHE)){
                            marketingGroupComponent.marketingGroupUserExpire(itemValue);
                        }
                    } finally {
                        stringRedisTemplate.delete(lockKey);
                    }
                }
            }
        }
    }

    /**
     * 处理限购过期
     * @param itemValue String  PURCHASE_CONSTRAINT_KEY:tenantCode:purchaseConstraintCode
     */
    private void purchaseConstraintExpire(String itemValue) {
        String[] items = itemValue.split(Constants.REDIS_KEY_SEPARATOR);
        if(items.length == 3){
            String tenantCode = items[1];
            String purchaseConstraintCode = items[2];
            purchaseConstraintService.updatePurchaseConstraintStatus(tenantCode,
                    purchaseConstraintCode,
                    PurchaseConstraintStatusEnum.CLOSURE.getCode(),
                    Constants.SYSTEM_USER
            );
        }
    }

    private void canUseExpire(String itemValue) {
        // 修改未发放和已发放的券码状态为已过期
        String[] items = itemValue.split(":");
        String couponCode = "";
        String activityCode = "";
        String tenantCode = "";
        for (String item : items){
            if (item.split(Constants.REGEX)[0].equals(COUPONCODE)){
                couponCode = item.split(Constants.REGEX)[1];
            }
            if (item.split(Constants.REGEX)[0].equals(ACTIVITY_CODE)){
                activityCode = item.split(Constants.REGEX)[1];
            }
            if (item.split(Constants.REGEX)[0].equals(TENANTCODE)){
                tenantCode = item.split(Constants.REGEX)[1];
            }
        }
        innerCodeService.updateCouponStatus(tenantCode, activityCode, couponCode, CouponStatusEnum.EXPIRE);
        couponUserService.updateCouponStatus(tenantCode, activityCode, couponCode, CouponStatusEnum.EXPIRE, null);
    }

    private void activityExpire(String itemValue) {
        // 关闭活动，只修改活动表状态   PROMOTION_ACTIVITY_CACHE:TENANTCODE=5551:ACTIVITYTYPE=01:ACTIVITYID=74:STATUS=04
        String[] items = itemValue.split(":");
        String activityCode = "";
        String tenantCode = "";
        for (String item : items){
            if (item.split(Constants.REGEX)[0].equals(TENANTCODE)){
                tenantCode = item.split(Constants.REGEX)[1];
            }
            if (item.split(Constants.REGEX)[0].equals(ACTIVITY_CODE)){
                activityCode = item.split(Constants.REGEX)[1];
            }
        }
        this.activityExpireComponentDomain.expireActivity(tenantCode, activityCode);
    }

    private void marketingActivityExpire(String itemValue) {
        // 关闭活动，只修改活动表状态   MARKETING_ACTIVITY_CACHE:TENANT_CODE=100001:ACTIVITY_CODE=1020070900006720:STATUS=04
        String[] items = itemValue.split(":");
        String activityCode = "";
        String tenantCode = "";
        for (String item : items){
            if (item.split(Constants.REGEX)[0].equals("TENANT_CODE")){
                tenantCode = item.split(Constants.REGEX)[1];
            }
            if (item.split(Constants.REGEX)[0].equals("ACTIVITY_CODE")){
                activityCode = item.split(Constants.REGEX)[1];
            }
        }
        log.info("开始处理营销活动过期事件,item:{},tenantCode:{},activityCode:{}", itemValue, tenantCode, activityCode);
        marketingCacheComponent.expire(tenantCode, activityCode);
    }

    private boolean isNotify(String itemValue) {
        return itemValue.startsWith(Constants.PROMOTION_ACTIVITY_CACHE)
                || itemValue.startsWith(Constants.PROMOTION_COUPON_CODE_CACHE)
                || itemValue.startsWith(Constants.PROMOTION_COUPON_USER)
                || itemValue.startsWith(Constants.PROMOTION_COUPON_RELEASE_TIME)
                || itemValue.startsWith(MarketingConstants.MARKETING_ACTIVITY_CACHE)
                || itemValue.startsWith(Constants.PURCHASE_CONSTRAINT_CACHE_KEY_PREFIX)
                || itemValue.startsWith(MarketingConstants.MARKETING_GROUP_USER_CACHE)
                ;
    }
}
