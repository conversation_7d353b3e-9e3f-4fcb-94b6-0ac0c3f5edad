package com.gtech.promotion.component.purchaseconstraint;


import cn.hutool.core.map.MapUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.github.pagehelper.PageInfo;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import com.google.common.collect.Lists;
import com.gtech.basic.idm.web.vo.result.QueryOpUserAccountListResult;
import com.gtech.basic.idm.web.vo.result.QueryUserResult;
import com.gtech.commons.redis.GTechCodeGenerator;
import com.gtech.commons.result.PageResult;
import com.gtech.commons.result.Result;
import com.gtech.commons.utils.BeanCopyUtils;
import com.gtech.commons.utils.DateUtil;
import com.gtech.commons.utils.StringUtil;
import com.gtech.promotion.RedisClient;
import com.gtech.promotion.checker.SystemChecker;
import com.gtech.promotion.checker.purchaseconstraint.PurchaseConstraintChecker;
import com.gtech.promotion.checker.purchaseconstraint.PurchaseConstraintRuleChecker;
import com.gtech.promotion.code.PromotionConstants;
import com.gtech.promotion.code.activity.*;
import com.gtech.promotion.code.purchaseconstraint.PcRuleCalculateTypeEnum;
import com.gtech.promotion.code.purchaseconstraint.PurchaseConstraintRuleTypeEnum;
import com.gtech.promotion.code.purchaseconstraint.PurchaseConstraintStatusEnum;
import com.gtech.promotion.component.activity.ActivityCacheDomain;
import com.gtech.promotion.component.activity.ActivityComponentDomain;
import com.gtech.promotion.component.activity.ProductDomain;
import com.gtech.promotion.component.feign.IdmFeignClientComponent;
import com.gtech.promotion.component.feign.MasterDataFeignClientComponent;
import com.gtech.promotion.component.purchaseconstraint.dto.PcRuleCacheValuePair;
import com.gtech.promotion.component.purchaseconstraint.dto.PcRuleCheckResult;
import com.gtech.promotion.dao.entity.purchaseconstraint.PurchaseConstraintEntity;
import com.gtech.promotion.dao.model.activity.*;
import com.gtech.promotion.dao.model.purchaseconstraint.PcRuleCalculateModel;
import com.gtech.promotion.dao.model.purchaseconstraint.PurchaseConstraintModel;
import com.gtech.promotion.dao.model.purchaseconstraint.PurchaseConstraintRuleModel;
import com.gtech.promotion.domain.purchaseconstraint.PurchaseConstraintDomain;
import com.gtech.promotion.dto.cache.PurchaseConstraintCacheDTO;
import com.gtech.promotion.dto.in.purchaseconstraint.*;
import com.gtech.promotion.dto.out.purchaseconstraint.FindPurchaseConstraintOutDto;
import com.gtech.promotion.exception.Check;
import com.gtech.promotion.exception.PromotionException;
import com.gtech.promotion.feign.MasterDataFeignClient;
import com.gtech.promotion.helper.CustomerConditionHelper;
import com.gtech.promotion.helper.QualificationFilter;
import com.gtech.promotion.service.activity.*;
import com.gtech.promotion.service.mongo.activity.TPromoProductService;
import com.gtech.promotion.service.purchaseconstraint.PurchaseConstraintRuleService;
import com.gtech.promotion.service.purchaseconstraint.PurchaseConstraintService;
import com.gtech.promotion.utils.Constants;
import com.gtech.promotion.utils.CronUtil;
import com.gtech.promotion.vo.bean.*;
import com.gtech.promotion.vo.param.activity.CustomCondition;
import com.gtech.promotion.vo.param.purchaseconstraint.query.QueryPurchaseConstraintListParam;
import com.gtech.promotion.vo.result.purchaseconstraint.*;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.text.MessageFormat;
import java.util.*;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;


@Slf4j
@Component
public class PurchaseConstraintComponent {
    @Autowired
    private RedissonClient redissonClient;
    @Autowired
    private RedisClient redisClient;

    @Autowired
    private PcRuleComponent pcRuleComponent;

    @Autowired
    private PurchaseConstraintService pcService;

    @Autowired
    private OperationLogService operationLogService;

    @Autowired
    private QualificationService qualificationService;

    @Autowired
    private TPromoProductService productService;

    @Autowired
    private ActivityProductDetailService activityProductDetailService;

    @Autowired
    private ActivityPeriodService activityPeriodService;

    @Autowired
    private PurchaseConstraintRuleService purchaseConstraintRuleService;

    @Autowired
    private ActivityStoreService activityStoreService;

    @Autowired
    private PurchaseConstraintCacheComponent pcCacheComponent;


    @Autowired
    private ActivityComponentDomain activityComponentDomain;

    @Autowired
    private ActivityCacheDomain activityCacheDomain;


    @Autowired
    private ProductDomain productDomain;

    @Autowired
    private  GTechCodeGenerator codeGenerator;

    @Autowired
    private IdmFeignClientComponent idmFeignClientComponent;

    @Autowired
    private MasterDataFeignClientComponent masterDataFeignClientComponent;

    @Autowired
    private MasterDataFeignClient masterDataFeignClient;

    @Autowired
    private CustomerConditionHelper customerConditionHelper;

    private static final String PURCHASE_CONSTRAINT_CODE = "purchaseConstraintCode";



    /**
     * 创建限购
     * @param purchaseConstraintDomain
     * @return
     */
    @Transactional
    public String createPurchaseConstraint(PurchaseConstraintDomain purchaseConstraintDomain) {
        log.info("purchaseConstraintDomain : {}", JSON.toJSONString(purchaseConstraintDomain));
        // 保存限购主表
        PurchaseConstraintModel purchaseConstraintModel = BeanCopyUtils.jsonCopyBean(purchaseConstraintDomain, PurchaseConstraintModel.class);
        String purchaseConstraintCode = codeGenerator.generateCode(purchaseConstraintModel.getTenantCode(),
                PURCHASE_CONSTRAINT_CODE, "PCC[D:yyyyMMddHHmmss][SM:%06d]", 1L);
        purchaseConstraintModel.setPurchaseConstraintCode(purchaseConstraintCode);
        purchaseConstraintModel.setCreateUser(purchaseConstraintDomain.getOperateUser());
        List<CustomCondition> customConditions = purchaseConstraintDomain.getCustomConditionsList();
        if (CollectionUtils.isNotEmpty(customConditions)) {
            String toJSONString = JSON.toJSONString(customConditions);
            purchaseConstraintModel.setCustomCondition(toJSONString);
        }

        List<CustomCondition> customRules = purchaseConstraintDomain.getCustomRulesList();
        if (CollectionUtils.isNotEmpty(customRules)) {
            String toJSONString = JSON.toJSONString(customRules);
            purchaseConstraintModel.setCustomRule(toJSONString);
        }

        pcService.insert(purchaseConstraintModel);

        // 创建限购周期
        createPurchaseConstraintPeriod(purchaseConstraintDomain, purchaseConstraintCode);


        // 创建选择的店铺
        createActivityStore(purchaseConstraintDomain, purchaseConstraintCode);

        // 保存人员条件
        createActivityQualification(purchaseConstraintDomain, purchaseConstraintCode);

        // Product Scopes
        if (CollectionUtils.isNotEmpty(purchaseConstraintDomain.getProducts())) {
            productService.insertProducts(purchaseConstraintDomain.getProducts(), purchaseConstraintCode, purchaseConstraintDomain.getTenantCode());
        }

        // Product Details
        if (CollectionUtils.isNotEmpty(purchaseConstraintDomain.getProductDetails())) {
            List<TPromoActivityProductDetailVO> tPromoActivityProductDetailVOS = activityComponentDomain.getActivityProductDetailVOS(
                    purchaseConstraintDomain.getProductDetails(), purchaseConstraintDomain.getTenantCode(), purchaseConstraintCode, 1);
            activityProductDetailService.insertProductSku(tPromoActivityProductDetailVOS);
        }

        // 商品黑名单
        if (CollectionUtils.isNotEmpty(purchaseConstraintDomain.getProductDetailBlackList())) {
            // Product Details
            List<TPromoActivityProductDetailVO> tPromoActivityProductDetailVOS = activityComponentDomain.getActivityProductDetailVOS(
                    purchaseConstraintDomain.getProductDetailBlackList(), purchaseConstraintDomain.getTenantCode(), purchaseConstraintCode, 2);
            activityProductDetailService.insertProductSku(tPromoActivityProductDetailVOS);
        }

        // 创建限购规则
        createPurchaseConstraintRules(purchaseConstraintDomain.getPurchaseConstraintRules(),
                                        purchaseConstraintDomain.getDomainCode(),
                                        purchaseConstraintDomain.getTenantCode(),
                                        purchaseConstraintDomain.getOrgCode(),
                                        purchaseConstraintCode);

        // 记录日志
        purchaseConstraintDomain.setProductDetails(new ArrayList<>());
        purchaseConstraintDomain.setProducts(new ArrayList<>());
        operationLogService.insertLog(
                OperationLogModel.builder().tenantCode(purchaseConstraintDomain.getTenantCode())
                        .activityCode(purchaseConstraintCode)
                        .operationType(OperationTypeEnum.CREATION.code())
                        .createFirstName(purchaseConstraintDomain.getOperateFirstName())
                        .createLastName(purchaseConstraintDomain.getOperateLastName())
                        .createUser(purchaseConstraintDomain.getOperateUser()).build(),
                JSON.toJSONString(purchaseConstraintDomain));

        return purchaseConstraintCode;
    }


    /**
     * 创建活动店铺
     * @param purchaseConstraintDomain
     * @param purchaseConstraintCode
     */
    private void createActivityStore(PurchaseConstraintDomain purchaseConstraintDomain, String purchaseConstraintCode) {
        if (StoreParamTypeEnum.STORE_CUSTOM.equalsCode(purchaseConstraintDomain.getStoreType())) {
            List<ActivityStore> channelStores = purchaseConstraintDomain.getChannelStores();
            List<TPromoActivityStoreVO> listChans = BeanCopyUtils.jsonCopyList(channelStores,
                    TPromoActivityStoreVO.class);
            for (int i = 0; i < listChans.size(); i++) {
                listChans.get(i).setTenantCode(purchaseConstraintDomain.getTenantCode());
                listChans.get(i).setActivityCode(purchaseConstraintCode);
                activityStoreService.createStore(listChans.get(i));
            }
        }
    }

    /**
     * 创建限购规则
     * @param purchaseConstraintRules
     * @param domainCode
     * @param tenantCode
     * @param orgCode
     * @param purchaseConstraintCode
     */
    private void createPurchaseConstraintRules(List<PurchaseConstraintRule> purchaseConstraintRules,
                                               String domainCode,
                                               String tenantCode,
                                               String orgCode,
                                               String purchaseConstraintCode) {
        if(CollectionUtils.isNotEmpty(purchaseConstraintRules)){
            List<PurchaseConstraintRuleModel> purchaseConstraintRuleModels = new ArrayList<>();
            for(PurchaseConstraintRule purchaseConstraintRule : purchaseConstraintRules){
                PurchaseConstraintRuleModel purchaseConstraintRuleModel = BeanCopyUtils.jsonCopyBean(purchaseConstraintRule, PurchaseConstraintRuleModel.class);
                purchaseConstraintRuleModel.setDomainCode(domainCode);
                purchaseConstraintRuleModel.setTenantCode(tenantCode);
                purchaseConstraintRuleModel.setOrgCode(orgCode);
                purchaseConstraintRuleModel.setPurchaseConstraintCode(purchaseConstraintCode);
                purchaseConstraintRuleModel.setCreateTime(new Date());
                purchaseConstraintRuleModels.add(purchaseConstraintRuleModel);
            }
            purchaseConstraintRuleService.insertList(purchaseConstraintRuleModels);
        }
    }


    /**
     * 创建限购周期
     * @param purchaseConstraintDomain
     * @param activityCode
     */
    private void createPurchaseConstraintPeriod(PurchaseConstraintDomain purchaseConstraintDomain, String activityCode) {
        if (null != purchaseConstraintDomain.getActivityPeriod()) {
            ActivityPeriodModel activityPeriodModel = BeanCopyUtils.jsonCopyBean(purchaseConstraintDomain.getActivityPeriod(),
                    ActivityPeriodModel.class);
            activityPeriodModel.setDomainCode(purchaseConstraintDomain.getDomainCode());
            activityPeriodModel.setTenantCode(purchaseConstraintDomain.getTenantCode());
            activityPeriodModel.setActivityCode(activityCode);
            activityPeriodModel.setCreateUser(purchaseConstraintDomain.getOperateUser());
            activityPeriodService.createPeriod(activityPeriodModel);
        }
    }

    /**
     * 创建人员条件
     * @param purchaseConstraintDomain
     * @param purchaseConstraintCode
     */
    private void createActivityQualification(PurchaseConstraintDomain purchaseConstraintDomain, String purchaseConstraintCode) {

        List<Qualification> qualifications = purchaseConstraintDomain.getQualifications();

        if (CollectionUtils.isNotEmpty(qualifications)) {
            List<QualificationModel> qualificationModels = new ArrayList<>();
            for (Qualification qualification : qualifications) {
                for (int i = 0; i < qualification.getQualificationValue().size(); i++) {
                    String s = qualification.getQualificationValue().get(i);
                    QualificationModel qualificationModel = new QualificationModel();
                    qualificationModel.setDomainCode(purchaseConstraintDomain.getDomainCode());
                    qualificationModel.setTenantCode(purchaseConstraintDomain.getTenantCode());
                    qualificationModel.setActivityCode(purchaseConstraintCode);
                    qualificationModel.setQualificationCode(qualification.getQualificationCode());
                    qualificationModel.setQualificationValue(s);
                    qualificationModel.setIsExclude(qualification.getIsExclude());
                    if (CollectionUtils.isNotEmpty(qualification.getQualificationValueName())
                            && qualification.getQualificationValueName().size() > i) {
                        qualificationModel.setQualificationValueName(qualification.getQualificationValueName().get(i));
                    }
                    qualificationModels.add(qualificationModel);
                }
            }
            qualificationService.createQualifications(qualificationModels);
        }
    }

    /**
     * 查询限购列表
     * @param param
     * @return
     */
    public PageResult<QueryPurchaseConstraintListResult> queryPurchaseConstraintList(QueryPurchaseConstraintListParam param) {
        PageInfo<PurchaseConstraintEntity> purchaseConstraintEntityPageInfo = pcService.queryList(param);
        List<QueryPurchaseConstraintListResult> queryListResult =
                BeanCopyUtils.jsonCopyList(purchaseConstraintEntityPageInfo.getList(), QueryPurchaseConstraintListResult.class);
        List<String> createUserCodeList = queryListResult.stream()
                                    .map(QueryPurchaseConstraintListResult::getCreateUser).collect(Collectors.toList());
        List<QueryOpUserAccountListResult> opUserAccountListResults =
                                            idmFeignClientComponent.queryOpUserAccountList(createUserCodeList);
        Map<String, QueryOpUserAccountListResult> opUserMap = opUserAccountListResults.stream()
                    .collect(Collectors.toMap(QueryOpUserAccountListResult::getUserCode, Function.identity()));
        List<QueryUserResult> orgUserList = idmFeignClientComponent
                                    .queryUserResults(param.getDomainCode(), param.getTenantCode(), createUserCodeList);
        Map<String, QueryUserResult> orgUserMap = orgUserList.stream()
                .collect(Collectors.toMap(QueryUserResult::getUserCode, Function.identity()));
        queryListResult.forEach(purchaseConstraint -> {
            QueryOpUserAccountListResult opUser = opUserMap.get(purchaseConstraint.getCreateUser());
            if(null != opUser){
                purchaseConstraint.setCreateUserFirstName(opUser.getFirstName());
                purchaseConstraint.setCreateUserLastName(opUser.getLastName());
            }else {
                QueryUserResult orgUser = orgUserMap.get(purchaseConstraint.getCreateUser());
                if(null != orgUser){
                    purchaseConstraint.setCreateUserFirstName(orgUser.getFirstName());
                    purchaseConstraint.setCreateUserLastName(orgUser.getLastName());
                }
            }
        });
        return PageResult.ok(queryListResult, purchaseConstraintEntityPageInfo.getTotal());
    }

    /**
     * 更新限购状态
     * @param updatePurchaseConstraintStatusInDTO
     */
    @Transactional
    public void updatePurchaseConstraintStatus(UpdatePurchaseConstraintStatusInDTO updatePurchaseConstraintStatusInDTO) {
        String tenantCode = updatePurchaseConstraintStatusInDTO.getTenantCode();
        String purchaseConstraintCode = updatePurchaseConstraintStatusInDTO.getPurchaseConstraintCode();
        String purchaseConstraintStatus = updatePurchaseConstraintStatusInDTO.getPurchaseConstraintStatus();
        PurchaseConstraintModel purchaseConstraintModel =
                pcService.getPurchaseConstraint(tenantCode, purchaseConstraintCode);
        Check.check(purchaseConstraintModel == null, PurchaseConstraintChecker.NOT_EXIST_PURCHASE_CONSTRAINT);
        if(null != purchaseConstraintModel.getPurchaseConstraintEndTime()) {
            Check.check(ActivityStatusEnum.EFFECTIVE.equalsCode(purchaseConstraintStatus)
                            && purchaseConstraintModel.getPurchaseConstraintEndTime().getTime() <= System.currentTimeMillis(),
                    PurchaseConstraintChecker.END_TIME_NOW);
        }
        String auditConfig = activityComponentDomain.getActivityAuditConfig(tenantCode); //限购三层审核与活动三层审核共用一个开关
        if (StringUtil.isEmpty(auditConfig) || Constants.NEED_AUDIT_NO.equals(activityComponentDomain.isNeedAudit(auditConfig))) {
            Check.check(ActivityStatusEnum.IN_AUDIT.equalsCode(purchaseConstraintStatus),
                    PurchaseConstraintChecker.TENANT_NOT_NEED_AUDIT);
        }
        String needDifferent = activityComponentDomain.isAuditDifferentOperator(auditConfig);
        if (ActivityStatusEnum.IN_AUDIT.code().equals(purchaseConstraintStatus)
                && Constants.NEED_DIFFERENT_YES.equals(needDifferent)) {
            Check.check(
                    activityComponentDomain.isContainOperator(purchaseConstraintModel.getCreateUser(),
                                                    null,
                                                            updatePurchaseConstraintStatusInDTO.getOperateUser()),
                    PurchaseConstraintChecker.CREATE_AUDIT_NEED_DIFFERENT);
        }
        if (ActivityStatusEnum.EFFECTIVE.code().equals(purchaseConstraintStatus)
                && Constants.NEED_DIFFERENT_YES.equals(needDifferent)) {
            Check.check(
                    activityComponentDomain.isContainOperator(purchaseConstraintModel.getCreateUser(), purchaseConstraintModel.getAuditUser(),
                            updatePurchaseConstraintStatusInDTO.getOperateUser()),
                    PurchaseConstraintChecker.CREATE_AUDIT_COMMIT_NEED_DIFFERENT);
        }

        pcService.updatePurchaseConstraintStatus(tenantCode, purchaseConstraintCode, purchaseConstraintStatus,
                updatePurchaseConstraintStatusInDTO.getOperateUser());

        String operationType;
        if (ActivityStatusEnum.EFFECTIVE.code().equals(purchaseConstraintStatus)) {
            operationType = OperationTypeEnum.ACTIVATION.code();
        } else if (ActivityStatusEnum.IN_AUDIT.code().equals(purchaseConstraintStatus)) {
            operationType = OperationTypeEnum.APPROVAL.code();
        } else {
            operationType = ActivityStatusEnum.END.code().equals(purchaseConstraintStatus) ?
                                        OperationTypeEnum.TERMINATION.code() : OperationTypeEnum.COMPLETION.code();
        }

        operationLogService.insertLog(
                OperationLogModel.builder().tenantCode(tenantCode).activityCode(purchaseConstraintCode)
                        .operationType(operationType)
                        .createLastName(updatePurchaseConstraintStatusInDTO.getOperateLastName())
                        .createFirstName(updatePurchaseConstraintStatusInDTO.getOperateFirstName())
                        .createUser(updatePurchaseConstraintStatusInDTO.getOperateUser()).build(),
                JSON.toJSONString(updatePurchaseConstraintStatusInDTO));
    }

    /**
     * 获取限购详情
     * @param findPurchaseConstraintInDto
     * @return
     */
    public FindPurchaseConstraintOutDto findPurchaseConstraint(FindPurchaseConstraintInDto findPurchaseConstraintInDto) {
        String tenantCode = findPurchaseConstraintInDto.getTenantCode();
        String purchaseConstraintCode = findPurchaseConstraintInDto.getPurchaseConstraintCode();
        String orgCode = findPurchaseConstraintInDto.getOrgCode();
        PurchaseConstraintModel purchaseConstraintModel = pcService.getPurchaseConstraint(tenantCode , purchaseConstraintCode);
        Check.check(purchaseConstraintModel == null, PurchaseConstraintChecker.NOT_EXIST_PURCHASE_CONSTRAINT);

        FindPurchaseConstraintOutDto findPurchaseConstraintOutDto =
                                BeanCopyUtils.jsonCopyBean(purchaseConstraintModel, FindPurchaseConstraintOutDto.class);

        // 渠道
        List<ActivityStore> channelStores = new ArrayList<>();
        if (StoreParamTypeEnum.STORE_CUSTOM.equalsCode(findPurchaseConstraintOutDto.getStoreType())) {
            List<TPromoActivityStoreVO> stores = activityStoreService.getStoresByActivityCode(purchaseConstraintCode);
            if (StringUtil.isNotBlank(orgCode) && Boolean.TRUE.equals(!activityComponentDomain.matchStore(orgCode, stores, false))) {// 入参为空，跳过排除，直接返回所有店铺
                return null;
            }
            channelStores = BeanCopyUtils.jsonCopyList(stores, ActivityStore.class);
        }
        findPurchaseConstraintOutDto.setChannelStores(channelStores);

        // 资格信息
        List<QualificationModel> qualificationModels = qualificationService.queryQualifications(tenantCode,
                purchaseConstraintCode);
        List<Qualification> qualifications = QualificationModel.convert(qualificationModels);
        findPurchaseConstraintOutDto.setQualifications(qualifications);

        // 商品信息
        List<ProductScope> products = new ArrayList<>();
        activityComponentDomain.setProductScope(purchaseConstraintCode, products);
        findPurchaseConstraintOutDto.setProducts(products);

        // 时间周期
        ActivityPeriodModel period = activityPeriodService.findPeriod(tenantCode, purchaseConstraintCode);
        findPurchaseConstraintOutDto.setActivityPeriod(BeanCopyUtils.jsonCopyBean(period, ActivityPeriod.class));

        // 获取所有商品详情
        List<ProductDetail> allProductDetails = new ArrayList<>();
        activityComponentDomain.queryProductDetail(purchaseConstraintCode, allProductDetails);
        // 商品白名单
        List<ProductDetail> productDetailWhiteList = new ArrayList<>();
        // 商品黑名单
        List<ProductDetail> productDetailBlackList = new ArrayList<>();
        //从所有商品详情中过滤出白名单和黑名单
        activityComponentDomain.productDetailList(allProductDetails, productDetailWhiteList, productDetailBlackList);

        findPurchaseConstraintOutDto.setProductDetails(productDetailWhiteList);
        findPurchaseConstraintOutDto.setProductDetailBlackList(productDetailBlackList);

        List<PurchaseConstraintRuleModel> purchaseConstraintRuleList =  purchaseConstraintRuleService
                                                .queryPurchaseConstraintRuleByCode(tenantCode, purchaseConstraintCode);

        findPurchaseConstraintOutDto.setPurchaseConstraintRuleList(
                                BeanCopyUtils.jsonCopyList(purchaseConstraintRuleList, PurchaseConstraintRule.class));

        return findPurchaseConstraintOutDto;
    }

    /**
     * 修改限购
     * @param updatePurchaseConstraintInDto
     * @return
     */
    public int updatePurchaseConstraint(UpdatePurchaseConstraintInDto updatePurchaseConstraintInDto) {
        String tenantCode = updatePurchaseConstraintInDto.getTenantCode();
        String purchaseConstraintCode = updatePurchaseConstraintInDto.getPurchaseConstraintCode();
        PurchaseConstraintModel purchaseConstraintModel = pcService
                .getPurchaseConstraint(tenantCode , purchaseConstraintCode);
        Check.check(purchaseConstraintModel == null, PurchaseConstraintChecker.NOT_EXIST_PURCHASE_CONSTRAINT);

        PurchaseConstraintModel updatePurchaseConstraintModel = BeanCopyUtils.jsonCopyBean(updatePurchaseConstraintInDto, PurchaseConstraintModel.class);
        updatePurchaseConstraintModel.setId(purchaseConstraintModel.getId());
        List<CustomCondition> customConditions = updatePurchaseConstraintInDto.getCustomConditionsList();
        if (CollectionUtils.isNotEmpty(customConditions)) {
            String toJSONString = JSON.toJSONString(customConditions);
            updatePurchaseConstraintModel.setCustomCondition(toJSONString);
        }
        List<CustomCondition> customRules = updatePurchaseConstraintInDto.getCustomRulesList();
        if (CollectionUtils.isNotEmpty(customRules)) {
            String toJSONString = JSON.toJSONString(customRules);
            updatePurchaseConstraintModel.setCustomRule(toJSONString);
        }

        int updateCount = pcService.updatePurchaseConstraintById(updatePurchaseConstraintModel);

        if(updateCount > 0) {
            // 限购周期 先删除再创建
            activityPeriodService.deletePeriod(tenantCode, purchaseConstraintCode);
            createPurchaseConstraintPeriod(updatePurchaseConstraintInDto, purchaseConstraintCode);

            // 资格 先删除 再创建
            qualificationService.deleteQualifications(tenantCode, purchaseConstraintCode);
            createActivityQualification(updatePurchaseConstraintInDto, purchaseConstraintCode);

            // 活动渠道 先删除 创建
            activityStoreService.deleteByActivityCode(purchaseConstraintCode);
            createActivityStore(updatePurchaseConstraintInDto, purchaseConstraintCode);

            // 活动商品详细spu-sku列表先删除再创建，商品选择方式：1：文件上传；2：json数据
            activityProductDetailService.deleteProductDetails(purchaseConstraintCode, null);
            activityComponentDomain.deleteProductDetail(updatePurchaseConstraintInDto.getProductDetails(), purchaseConstraintCode);
            if (!CollectionUtils.isEmpty(updatePurchaseConstraintInDto.getProductDetails())) {
                List<TPromoActivityProductDetailVO> tPromoActivityProductDetailVOS = activityComponentDomain.getActivityProductDetailVOS(
                        updatePurchaseConstraintInDto.getProductDetails(), tenantCode, purchaseConstraintCode, 1);
                activityProductDetailService.insertProductSku(tPromoActivityProductDetailVOS);
            }
            if (!CollectionUtils.isEmpty(updatePurchaseConstraintInDto.getProductDetailBlackList())) {
                List<TPromoActivityProductDetailVO> tPromoActivityProductDetailVOS = activityComponentDomain.getActivityProductDetailVOS(
                        updatePurchaseConstraintInDto.getProductDetailBlackList(), tenantCode, purchaseConstraintCode, 2);
                activityProductDetailService.insertProductSku(tPromoActivityProductDetailVOS);
            }

            // 限购规则  先删除再创建
            purchaseConstraintRuleService.deletePurchaseConstraintRule(tenantCode, purchaseConstraintCode);
            createPurchaseConstraintRules(updatePurchaseConstraintInDto.getPurchaseConstraintRules(),
                                            updatePurchaseConstraintInDto.getDomainCode(),
                                            updatePurchaseConstraintInDto.getTenantCode(),
                                            updatePurchaseConstraintInDto.getOrgCode(),
                                            purchaseConstraintCode);

            dealProducts(updatePurchaseConstraintInDto, purchaseConstraintCode);

            // 商品条件，字段容易超长
            updatePurchaseConstraintInDto.setProductDetails(new ArrayList<>());
            updatePurchaseConstraintInDto.setProducts(new ArrayList<>());
            operationLogService.insertLog(OperationLogModel.builder().tenantCode(tenantCode).activityCode(purchaseConstraintCode)
                    .operationType(OperationTypeEnum.EDITION.code()).createLastName(updatePurchaseConstraintInDto.getOperateLastName())
                    .createFirstName(updatePurchaseConstraintInDto.getOperateFirstName()).createUser(updatePurchaseConstraintInDto.getOperateUser())
                    .build(), JSON.toJSONString(updatePurchaseConstraintInDto));
        }
        return updateCount;
    }

    /**
     * 处理商品 插入新的商品，删除老的商品
     * 该方法要确保放到最后，防止mongo里的商品数据被删除或更新后，由于下面流程异常导致数据不能被回滚
     * @param purchaseConstraintInDto
     * @param purchaseConstraintCode
     */
    private void dealProducts(UpdatePurchaseConstraintInDto purchaseConstraintInDto, String purchaseConstraintCode) {

        List<ProductScope> productsOld = this.productService.getProducts(purchaseConstraintCode);
        this.productService.deleteProducts(purchaseConstraintCode);

        try {
            // 要修改的活动商品是 非全商品时 添加商品表数据
            List<ProductScope> products = purchaseConstraintInDto.getProducts();
            // 如果是sku上传的,但是商品传了数据(将商品全部删除)
            if (CollectionUtils.isNotEmpty(products)) {
                productService.insertProducts(products, purchaseConstraintCode, purchaseConstraintInDto.getTenantCode());
            }
            // token为空时,无需保存sku数据，不为空时 表示上传文件或者删除之前的sku
            if (StringUtils.isNotBlank(purchaseConstraintInDto.getSkuToken())) {
                productDomain.createProductSku(purchaseConstraintCode, purchaseConstraintInDto.getTenantCode(),
                        purchaseConstraintInDto.getSkuToken());
            }
        } catch (Exception e) {
            if (CollectionUtils.isNotEmpty(productsOld)) {
                productService.insertProducts(productsOld, purchaseConstraintCode, purchaseConstraintInDto.getTenantCode());
            }
            throw e;
        }
    }

    /**
     * 修改限购优先级
     * @param updatePurchaseConstraintInDto
     * @return
     */
    @Transactional
    public int updatePurchaseConstraintPriority(UpdatePurchaseConstraintPriorityInDto updatePurchaseConstraintInDto) {
        String tenantCode = updatePurchaseConstraintInDto.getTenantCode();
        List<PurchaseConstraintPriority> purchaseConstraintPriorities =
                                            updatePurchaseConstraintInDto.getPurchaseConstraintPriorityList();


        int result = pcService.updatePurchaseConstraintPriority(tenantCode,
                                                    updatePurchaseConstraintInDto.getPurchaseConstraintPriorityList());

        Check.check(result != purchaseConstraintPriorities.size(),
                                PurchaseConstraintChecker.PURCHASE_CONSTRAINT_PRIORITY_UPDATE_NOT_MATCH);

        operationLogService.insertLog(
                OperationLogModel.builder().tenantCode(tenantCode).activityCode("all")
                        .operationType(OperationTypeEnum.PRIORITY.code())
                        .createLastName(updatePurchaseConstraintInDto.getOperateLastName())
                        .createFirstName(updatePurchaseConstraintInDto.getOperateFirstName())
                        .createUser(updatePurchaseConstraintInDto.getOperateUser()).build(),
                JSON.toJSONString(updatePurchaseConstraintInDto));

        return result;
    }


    /**
     * 过期终止限购
     * @param tenantCode
     * @param purchaseConstraintCode
     */
    public void expirePurchaseConstraint(String tenantCode, String purchaseConstraintCode) {
        Check.check(StringUtil.isBlank(tenantCode), PurchaseConstraintChecker.NOT_NULL_TENANT_CODE);
        Check.check(StringUtil.isBlank(purchaseConstraintCode), PurchaseConstraintChecker.NOT_NULL_PURCHASE_CONSTRAINT_CODE);

        // 更新数据库状态
        pcService.updatePurchaseConstraintStatus(tenantCode,
                                                                    purchaseConstraintCode,
                                                                    PurchaseConstraintStatusEnum.CLOSURE.getCode(),
                                                                    Constants.SYSTEM_USER
                                                                    );
        // 删除缓存
        pcCacheComponent.delPurchaseConstraintCache(tenantCode, purchaseConstraintCode);
    }
    /**
     * 是否生效的限购
     * @param purchaseConstraintModel
     * @param date
     * @return
     */
    public boolean isEffectivePurchase(PurchaseConstraintModel purchaseConstraintModel, Date date) {
        if(null == purchaseConstraintModel){
            return false;
        }
        if (purchaseConstraintModel.isNeedToDoExpire()) {
            expirePurchaseConstraint(purchaseConstraintModel.getTenantCode(), purchaseConstraintModel.getPurchaseConstraintCode());
        }
        return purchaseConstraintModel.isValid(date);
    }

    private static final String DEFAULT_ORDER_MAX_QTY_PER_SKU = "DEFAULT_ORDER_MAX_QTY_PER_SKU";
    private static final String DEFAULT_ORDER_MAX_AMOUNT = "DEFAULT_ORDER_MAX_AMOUNT";

    /**
     * 校验创建的订单是否符合限购
     *
     * 不支持多店铺下单校验
     *
     * @return 符合则不返回任何信息，不符合则抛出异常
     */
    @SneakyThrows
    public CheckPurchaseConstraintResult checkPurchaseConstraint(CheckPurchaseConstraintInDto pcInDto){
        String tenantCode = pcInDto.getTenantCode();
        List<PurchaseConstraintModel> pcModelList = pcService.list(tenantCode);
        List<PurchaseConstraintCacheDTO> pcCacheDTOList = pcCacheComponent.queryValidPcFromCache(pcModelList);
        for (PurchaseConstraintCacheDTO pc : pcCacheDTOList) {
            List<PurchaseConstraintRule> pcRuleList = pc.getPurchaseConstraintRuleList();
            // 移除非指定校验规则
            pcRuleList.removeIf(pcRule -> CollectionUtils.isNotEmpty(pcInDto.getPcRuleTypeList())
                    && !pcInDto.getPcRuleTypeList().contains(pcRule.getPurchaseConstraintRuleType()));
        }

        final Map<String, List<String>> qualifications = pcInDto.getQualifications();
        // 通用限购规则（时间、人员、店铺、商品）过滤后的限购集合
        List<PurchaseConstraintCacheDTO> validPcCacheDtoList = pcCacheDTOList.stream()
                .filter(pcCacheDTO -> checkCommonPurchaseConstraint(pcCacheDTO, qualifications, Collections.singletonList(pcInDto.getOrgCode()), true))
                .collect(Collectors.toList());

        validPcCacheDtoList = this.filterPurchaseConstraintCacheByCustomCondition(pcInDto.getTenantCode(), validPcCacheDtoList, pcInDto.getCustomMap());

        // 获取默认限购规则
        PurchaseConstraintCacheDTO defaultPcCacheDTO = this.getDefaultPcCacheDTO(pcInDto, tenantCode);


        if (CollectionUtils.isEmpty(validPcCacheDtoList)) {
            if (defaultPcCacheDTO != null) {
                validPcCacheDtoList.add(defaultPcCacheDTO);
            } else {
                return CheckPurchaseConstraintResult.builder()
                        .canBuy(Boolean.TRUE)
                        .hitPurchaseConstraint(Boolean.FALSE)
                        .build();
            }
        } else if (defaultPcCacheDTO != null) {
            //修改为追加一条默认限购规则
            validPcCacheDtoList.add(defaultPcCacheDTO);
        }

        /*else if (defaultPcCacheDTO != null) {
            for (PurchaseConstraintCacheDTO pcCacheDTO : validPcCacheDtoList) {
                List<PurchaseConstraintRule> pcRuleList = pcCacheDTO.getPurchaseConstraintRuleList();
                List<PurchaseConstraintRule> defaultPcRuleList = defaultPcCacheDTO.getPurchaseConstraintRuleList();
                if (CollectionUtils.isEmpty(pcRuleList)) {
                    pcCacheDTO.setPurchaseConstraintRuleList(defaultPcRuleList);
                } else {
                    // 当前限购规则配置
                    Map<Integer, PurchaseConstraintRule> pcRuleTypeMap = pcRuleList.stream().collect(Collectors.toMap(PurchaseConstraintRule::getPurchaseConstraintRuleType, a -> a));
                    for (PurchaseConstraintRule defaultPcRule : defaultPcRuleList) {
                        PurchaseConstraintRule purchaseConstraintRule = pcRuleTypeMap.get(defaultPcRule.getPurchaseConstraintRuleType());
                        // 当前限购没有与默认配置相同规则,故而追加一条默认限购规则
                        if (purchaseConstraintRule == null) {
                            pcRuleList.add(defaultPcRule);
                        }
                    }
                }
            }
        }*/


        //4. 检查购物车或订单里的商品
        List<CheckPurchaseConstraintProductDto>  shoppingCartItemList = pcInDto.getCheckPurchaseConstraintProducts();

        // 购物车商品限购规则映射 key = pcCode
        Map<String, List<CheckPurchaseConstraintProductDto>> cartPcMap = new HashMap<>();

        Map<String, List<PurchaseConstraintCacheDTO>> skuHitPcMap = new HashMap<>();

        boolean hitPurchaseConstraint = Boolean.FALSE; // 是否命中限购
        List<PurchaseConstraintCacheDTO> hitRules = new ArrayList<>(); // 命中的限购规则

        //去除掉selected为0的商品
        shoppingCartItemList = shoppingCartItemList.stream().filter(item -> Objects.equals(item.getSelected(), ProductSelectedEnum.SELECT.code())).collect(Collectors.toList());
        // key 为skuCode， value为sku对应的商品信息
        Map<String, CheckPurchaseConstraintProductDto> skuProductMap = shoppingCartItemList.stream()
                .collect(Collectors.toMap(CheckPurchaseConstraintProductDto::getSkuCode, Function.identity()));

        // 当前订单中的商品参与的限购集合。key为skuCode, value为sku参与的限购列表
        Map<String, List<PurchaseConstraintCacheDTO>> skuPcListMap = new HashMap<>();
        for(CheckPurchaseConstraintProductDto shoppingCartItem : shoppingCartItemList ){
            String productCode = shoppingCartItem.getProductCode();
            String skuCode = shoppingCartItem.getSkuCode();
            ProductCodes productDTO = ProductCodes.builder()
                    .categoryCodes(shoppingCartItem.getCategoryCodes())
                    .brandCode(shoppingCartItem.getBrandCode())
                    .productCode(productCode)
                    .skuCode(skuCode)
                    .attributes(shoppingCartItem.getAttributes())
                    .productTag(shoppingCartItem.getProductTag())
                    .spuAttributes(shoppingCartItem.getSpuAttributes())
                    .build();
            // 匹配当前商品的限购
            List<PurchaseConstraintCacheDTO> currPurchaseConstraintList = this.filterPurchaseConstraintByProduct(validPcCacheDtoList, productDTO);
            if (defaultPcCacheDTO != null){
                currPurchaseConstraintList.add(defaultPcCacheDTO);
            }
            if(CollectionUtils.isNotEmpty(currPurchaseConstraintList)) {
                hitPurchaseConstraint = Boolean.TRUE;
                skuPcListMap.put(skuCode, currPurchaseConstraintList);
            }
        }

        if(MapUtil.isEmpty(skuPcListMap)){ // 当前订单中没有商品参与限购
            return CheckPurchaseConstraintResult.builder()
                    .canBuy(Boolean.TRUE)
                    .hitPurchaseConstraint(Boolean.FALSE)
                    .build();
        }


        Map<String, Map<Integer, String>> productRuleTypeMap = new HashMap<>();
        for(CheckPurchaseConstraintProductDto shoppingCartItem : shoppingCartItemList ){
            HashMap<Integer, String> ruleTypeMap = new HashMap<>();
            String productCode = shoppingCartItem.getProductCode();
            String skuCode = shoppingCartItem.getSkuCode();
            String mainProductNo = shoppingCartItem.getMainProductNo();

            // 匹配当前商品的限购
            List<PurchaseConstraintCacheDTO> currpcList = skuPcListMap.get(skuCode);
            if(CollectionUtils.isNotEmpty(currpcList)) {

                //判断如果商品没有高级价格,去除高级价格限购
                if (null == shoppingCartItem.getPriceSetting() || shoppingCartItem.getPriceSetting() == 0){
                    currpcList = currpcList.stream()
                            .filter(pc -> pc.getPriceSetting() == 0).collect(Collectors.toList());
                    //更新skuPcListMap
                    skuPcListMap.put(skuCode,currpcList);
                    //如果没有高级价格限购,则取默认限购
                    if (CollectionUtils.isEmpty(currpcList) && defaultPcCacheDTO != null){
                        skuPcListMap.put(skuCode, Lists.newArrayList(defaultPcCacheDTO));
                        currpcList = skuPcListMap.get(skuCode);
                    }else if(MapUtil.isEmpty(skuPcListMap)){ // 当前订单中没有商品参与限购
                        return CheckPurchaseConstraintResult.builder()
                                .canBuy(Boolean.TRUE)
                                .hitPurchaseConstraint(Boolean.FALSE)
                                .build();
                    }

                }

                //1.根据限购优先级排序

                List<PurchaseConstraintCacheDTO> currpcListSortByPriority = currpcList.stream()
                        .sorted((pc1, pc2) -> {
                            if (pc1.getPriority().equals(pc2.getPriority())) {
                                return Long.compare(pc2.getCreateTime().getTime(), pc1.getCreateTime().getTime());
                            }
                            return Integer.compare(pc1.getPriority(), pc2.getPriority());
                        })
                        .collect(Collectors.toList());



                //2.根据每一个限购的RuleType,获取对应的限购code



                currpcListSortByPriority.forEach(currPcCache -> {
                    String purchaseConstraintCode = currPcCache.getPurchaseConstraintCode();
                    List<PurchaseConstraintRule> purchaseConstraintRuleList = currPcCache.getPurchaseConstraintRuleList();
                    for (PurchaseConstraintRule purchaseConstraintRule : purchaseConstraintRuleList) {
                        //如果不存在，则添加
                        ruleTypeMap.putIfAbsent(purchaseConstraintRule.getPurchaseConstraintRuleType(), purchaseConstraintCode);
                    }
                    if (FirstRefusalEnum.YES.getCode().equals(currPcCache.getFirstRefusal())){
                        ruleTypeMap.put(PurchaseConstraintRuleTypeEnum.FIRST_REFUSAL.getCode(), purchaseConstraintCode);
                    }
                    if (StringUtil.isNotEmpty(currPcCache.getCustomRule())){
                        ruleTypeMap.put(PurchaseConstraintRuleTypeEnum.CUSTOM_RULES.getCode(), purchaseConstraintCode);
                    }
                });


                productRuleTypeMap.put(skuCode,ruleTypeMap);

                //3.合并限购活动为一个list
                //获取currpcListSortByPriority中限购code存在rulemap中的限购
                List<PurchaseConstraintCacheDTO> purchaseConstraintCacheDTOList = currpcListSortByPriority.stream()
                       .filter(pc -> ruleTypeMap.containsValue(pc.getPurchaseConstraintCode()))
                       .collect(Collectors.toList());

                //4.计算限购
                for (PurchaseConstraintCacheDTO currPcCache : purchaseConstraintCacheDTOList) {


                    // 排序返回优先级值最小的那个，若优先级一样，取时间最大的那个

                    // 叠加限购,需要将多个限购规则的限购数量叠加合并

                    String hitPurchaseConstraintCode = currPcCache.getPurchaseConstraintCode();


                    // 开启优先购买 判断优先购买权的类型是否满足叠加之后的限购
                    if (FirstRefusalEnum.YES.getCode().equals(currPcCache.getFirstRefusal())
                        && ruleTypeMap.containsKey(PurchaseConstraintRuleTypeEnum.FIRST_REFUSAL.getCode())
                        && ruleTypeMap.get(PurchaseConstraintRuleTypeEnum.FIRST_REFUSAL.getCode()).equals(hitPurchaseConstraintCode)
                    ) {

                        boolean checkQualificationResult = checkQualification(currPcCache, qualifications);
                        if (!checkQualificationResult) { //不符合优先购买，不能购买
                            List<Qualification> hitQualificationList = currPcCache.getQualifications(); //触发不能购买的优先限购人群
                            //限购目前只处理滤会员标签类型
                            Qualification tagQualification = hitQualificationList.stream().filter(hitQualification ->
                                            hitQualification.getQualificationCode().equalsIgnoreCase(Constants.MEMBER_TAG_CODE))
                                    .findFirst().orElse(new Qualification());
                            String errorMessageVal = String.join(Constants.COMMA_SEPARATOR, tagQualification.getQualificationValueName());

                            return CheckPurchaseConstraintResult.builder()
                                    .canBuy(Boolean.FALSE)
                                    .hitPurchaseConstraint(Boolean.TRUE)
                                    .products(Collections.singletonList(CheckPurchaseConstraintProductResult.builder()
                                            .mainProductNo(mainProductNo)
                                            .productCode(productCode)
                                            .skuCode(skuCode)
                                            .build()))
                                    .errorCode(PurchaseConstraintRuleChecker.FIRST_REFUSAL_VALID_FAIL.getCode())
                                    .errorMessage(MessageFormat.format(
                                            PurchaseConstraintRuleChecker.FIRST_REFUSAL_VALID_FAIL.getMessage(),
                                            errorMessageVal))
                                    .purchaseConstraintCode(currPcCache.getPurchaseConstraintCode())
                                    .purchaseConstraintRule(PurchaseConstraintRule.builder()
                                            .purchaseConstraintRuleType(PurchaseConstraintRuleTypeEnum.FIRST_REFUSAL.getCode())
                                            .purchaseConstraintValue(errorMessageVal)
                                            .build()
                                    )
                                    .build();

                        }
                    }

                    List<PurchaseConstraintRule> pcRuleList = currPcCache.getPurchaseConstraintRuleList();


                    if (CollectionUtils.isNotEmpty(pcRuleList)) {
                        for (PurchaseConstraintRule pcRule : pcRuleList) {
                            PurchaseConstraintRuleTypeEnum pcRuleTypeEnum = PurchaseConstraintRuleTypeEnum.valueOfCode(pcRule.getPurchaseConstraintRuleType());
                            switch (pcRuleTypeEnum) {
                                case ORDER_MAX_AMOUNT: {
                                    if (null != ruleTypeMap && ( !ruleTypeMap.containsKey(PurchaseConstraintRuleTypeEnum.ORDER_MAX_AMOUNT.getCode())
                                    || !ruleTypeMap.get(PurchaseConstraintRuleTypeEnum.ORDER_MAX_AMOUNT.getCode()).equals(hitPurchaseConstraintCode)
                                    )){
                                        break;
                                    }

                                    List<CheckPurchaseConstraintProductResult> pcProductResult = new ArrayList<>();
                                    BigDecimal totalAmt = getTotalAmt(hitPurchaseConstraintCode, skuPcListMap, skuProductMap, pcProductResult);
                                    if (totalAmt.compareTo(new BigDecimal(pcRule.getPurchaseConstraintValue())) > 0) {
                                        return CheckPurchaseConstraintResult.builder()
                                                .canBuy(Boolean.FALSE)
                                                .hitPurchaseConstraint(Boolean.TRUE)
                                                .products(pcProductResult)
                                                .errorCode(PurchaseConstraintRuleChecker.ORDER_MAX_AMOUNT_VALID_FAIL.getCode())
                                                .errorMessage(MessageFormat.format(
                                                        PurchaseConstraintRuleChecker.ORDER_MAX_AMOUNT_VALID_FAIL.getMessage(),
                                                        pcRule.getPurchaseConstraintValue()))
                                                .purchaseConstraintCode(currPcCache.getPurchaseConstraintCode())
                                                .purchaseConstraintRule(pcRule)
                                                .build();
                                    }
                                }
                                break;
                                case ORDER_MAX_QUANTITY: {

                                    if (null != ruleTypeMap && ( !ruleTypeMap.containsKey(PurchaseConstraintRuleTypeEnum.ORDER_MAX_QUANTITY.getCode())
                                            || !ruleTypeMap.get(PurchaseConstraintRuleTypeEnum.ORDER_MAX_QUANTITY.getCode()).equals(hitPurchaseConstraintCode)
                                    )){
                                        break;
                                    }

                                    List<CheckPurchaseConstraintProductResult> pcProductResult = new ArrayList<>();
                                    Integer totalQty = getTotalQuantity(hitPurchaseConstraintCode, skuPcListMap, skuProductMap, pcProductResult);
                                    if (totalQty > Integer.parseInt(pcRule.getPurchaseConstraintValue())) {
                                        return CheckPurchaseConstraintResult.builder()
                                                .canBuy(Boolean.FALSE)
                                                .hitPurchaseConstraint(Boolean.TRUE)
                                                .products(pcProductResult)
                                                .errorCode(PurchaseConstraintRuleChecker.ORDER_MAX_QTY_VALID_FAIL.getCode())
                                                .errorMessage(MessageFormat.format(
                                                        PurchaseConstraintRuleChecker.ORDER_MAX_QTY_VALID_FAIL.getMessage(),
                                                        pcRule.getPurchaseConstraintValue()))
                                                .purchaseConstraintCode(currPcCache.getPurchaseConstraintCode())
                                                .purchaseConstraintRule(pcRule)
                                                .build();
                                    }
                                }
                                break;
                                case ORDER_MAX_QTY_PER_SKU:

                                    if ( null != ruleTypeMap && (!ruleTypeMap.containsKey(PurchaseConstraintRuleTypeEnum.ORDER_MAX_QTY_PER_SKU.getCode())
                                            || !ruleTypeMap.get(PurchaseConstraintRuleTypeEnum.ORDER_MAX_QTY_PER_SKU.getCode()).equals(hitPurchaseConstraintCode)
                                    )){
                                        break;
                                    }

                                    if (shoppingCartItem.getQuantity() > Integer.parseInt(pcRule.getPurchaseConstraintValue())) {
                                        return CheckPurchaseConstraintResult.builder()
                                                .canBuy(Boolean.FALSE)
                                                .hitPurchaseConstraint(Boolean.TRUE)
                                                .products(Collections.singletonList(CheckPurchaseConstraintProductResult.builder()
                                                        .mainProductNo(mainProductNo)
                                                        .productCode(productCode)
                                                        .skuCode(skuCode)
                                                        .build()))
                                                .errorCode(PurchaseConstraintRuleChecker.ORDER_MAX_QTY_PER_SKU_VALID_FAIL.getCode())
                                                .errorMessage(MessageFormat.format(
                                                        PurchaseConstraintRuleChecker.ORDER_MAX_QTY_PER_SKU_VALID_FAIL.getMessage(),
                                                        pcRule.getPurchaseConstraintValue()))
                                                .purchaseConstraintCode(currPcCache.getPurchaseConstraintCode())
                                                .purchaseConstraintRule(pcRule)
                                                .build();
                                    }

                                    break;
                                case ORDER_MAX_AMOUNT_PER_SKU:
                                    if (null != ruleTypeMap && ( !ruleTypeMap.containsKey(PurchaseConstraintRuleTypeEnum.ORDER_MAX_AMOUNT_PER_SKU.getCode())
                                            || !ruleTypeMap.get(PurchaseConstraintRuleTypeEnum.ORDER_MAX_AMOUNT_PER_SKU.getCode()).equals(hitPurchaseConstraintCode)
                                    )){
                                        break;
                                    }


                                    if (shoppingCartItem.getPromotionAmount()
                                            .compareTo(new BigDecimal(pcRule.getPurchaseConstraintValue())) > 0) {
                                        return CheckPurchaseConstraintResult.builder()
                                                .canBuy(Boolean.FALSE)
                                                .hitPurchaseConstraint(Boolean.TRUE)
                                                .products(Collections.singletonList(CheckPurchaseConstraintProductResult.builder()
                                                        .mainProductNo(mainProductNo)
                                                        .productCode(productCode)
                                                        .skuCode(skuCode)
                                                        .build()))
                                                .errorCode(PurchaseConstraintRuleChecker.ORDER_MAX_AMOUNT_PER_SKU_VALID_FAIL.getCode())
                                                .errorMessage(MessageFormat.format(PurchaseConstraintRuleChecker.ORDER_MAX_AMOUNT_PER_SKU_VALID_FAIL.getMessage(),
                                                        pcRule.getPurchaseConstraintValue()))
                                                .purchaseConstraintCode(currPcCache.getPurchaseConstraintCode())
                                                .purchaseConstraintRule(pcRule)
                                                .build();
                                    }
                                    break;
                                default:
                                    break;
                            }
                        }
                    }


                    if (StringUtil.isNotEmpty(currPcCache.getCustomRule())
                            && ruleTypeMap.containsKey(PurchaseConstraintRuleTypeEnum.CUSTOM_RULES.getCode())
                            && ruleTypeMap.get(PurchaseConstraintRuleTypeEnum.CUSTOM_RULES.getCode()).equals(hitPurchaseConstraintCode)
                    ) {

                        //TODO 判断是否满足自定义规则
                        HashMap<String, PurchaseConstraintCacheDTO> pcDtoMap = new HashMap<>();
                        pcDtoMap.put(currPcCache.getPurchaseConstraintCode(),currPcCache);
                        Map<String, PurchaseConstraintCacheDTO> stringPurchaseConstraintCacheDTOMap = customerConditionHelper.filterCacheByCustomCondition(tenantCode, pcDtoMap, pcInDto.getCustomMap(),CustomerConditionHelper.RULE);
                        if (MapUtil.isEmpty(stringPurchaseConstraintCacheDTOMap)) {


                            String errorMessageVal = "自定义条件值";

                            return CheckPurchaseConstraintResult.builder()
                                    .canBuy(Boolean.FALSE)
                                    .hitPurchaseConstraint(Boolean.TRUE)
                                    .products(Collections.singletonList(CheckPurchaseConstraintProductResult.builder()
                                            .mainProductNo(mainProductNo)
                                            .productCode(productCode)
                                            .skuCode(skuCode)
                                            .build()))
                                    .errorCode(PurchaseConstraintRuleChecker.CUSTOMER_RULE_VALID_FAIL.getCode())
                                    .errorMessage(MessageFormat.format(
                                            PurchaseConstraintRuleChecker.CUSTOMER_RULE_VALID_FAIL.getMessage(),
                                            errorMessageVal))
                                    .purchaseConstraintCode(currPcCache.getPurchaseConstraintCode())
                                    .purchaseConstraintRule(PurchaseConstraintRule.builder()
                                            .purchaseConstraintRuleType(PurchaseConstraintRuleTypeEnum.CUSTOM_RULES.getCode())
                                            .purchaseConstraintValue(errorMessageVal)
                                            .build()
                                    )
                                    .build();

                        }
                    }



                    //
                    List<CheckPurchaseConstraintProductDto> pcCartItem = cartPcMap.get(currPcCache.getPurchaseConstraintCode());
                    if (CollectionUtils.isNotEmpty(pcCartItem)) {
                        pcCartItem.add(shoppingCartItem);
                    } else {
                        cartPcMap.put(currPcCache.getPurchaseConstraintCode(), Lists.newArrayList(shoppingCartItem));
                    }
                    //如果存在此sku,则添加到skuHitPcMap的对应value中,如果不存在,则新建一个list,并添加到skuHitPcMap中
                    if (skuHitPcMap.containsKey(skuCode)) {
                        skuHitPcMap.get(skuCode).add(currPcCache);
                    } else {
                        skuHitPcMap.put(skuCode, Lists.newArrayList(currPcCache));
                    }

                    // 商品满足限购
                    hitRules.add(currPcCache);
                }


            }
        }

        AtomicReference<CheckPurchaseConstraintResult> checkPcResult = new AtomicReference<>();
        if (Boolean.TRUE.equals(pcInDto.getUseIncrement())) {
            log.info("checkPurchaseConstraint: 使用增量,添加锁");
            String lockName = String.format(PcRuleComponent.PROMOTION_PC_INCREMENT_LOCK_KEY, pcInDto.getTenantCode(), pcInDto.getMemberCode());
            RLock lock = redissonClient.getLock(lockName);
            boolean lockOn = lock.tryLock(5000L, 20000L,TimeUnit.MILLISECONDS);
            log.info("runWithLock:{} lockOn:{}", lockName,lockOn);
            if (lockOn) {
                try {
                    checkPcResult.set(checkRollupRules(pcInDto, skuPcListMap, skuProductMap, pcCacheDTOList,skuHitPcMap,productRuleTypeMap));
                } finally {
                    log.info("runWithLock:{} unlock",lockName);
                    lock.unlock();
                }
            } else {
                throw new PromotionException(SystemChecker.LOCK_FAIL);
            }
        } else {
            log.info("checkPurchaseConstraint: 未使用增量");
            checkPcResult.set(checkRollupRules(pcInDto, skuPcListMap, skuProductMap, validPcCacheDtoList, skuHitPcMap, productRuleTypeMap));
        }
        // 检查累计规则
        if (checkPcResult.get() != null) {
            return checkPcResult.get();
        }

        List<CheckPurchaseConstraintResult.CheckPurchaseConstraintHitResult> checkPurchaseConstraintHitResults = hitRules.stream()
                .map(m -> BeanCopyUtils.jsonCopyBean(m, CheckPurchaseConstraintResult.CheckPurchaseConstraintHitResult.class))
                .collect(Collectors.toList());
        return CheckPurchaseConstraintResult.builder()
                .canBuy(Boolean.TRUE)
                .hitPurchaseConstraint(hitPurchaseConstraint)
                .checkPurchaseConstraintHitResults(checkPurchaseConstraintHitResults)
                .build();
    }


    public List<PurchaseConstraintCacheDTO> filterPurchaseConstraintCacheByCustomCondition(String tenantCode, List<PurchaseConstraintCacheDTO> activityList, Map<String, String> customMap) {
        List<PurchaseConstraintCacheDTO> collect = activityList.stream().filter(x -> StringUtil.isNotEmpty(x.getCustomCondition())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(collect)) {
            return activityList;
        }
        Map<String, PurchaseConstraintCacheDTO> map = activityList.stream().collect(Collectors.toMap(FindPurchaseConstraintOutDto::getPurchaseConstraintCode, x->x));
        Map<String, PurchaseConstraintCacheDTO> cacheDTOMap = customerConditionHelper.filterCacheByCustomCondition(tenantCode, map, customMap, CustomerConditionHelper.CONDITION);
        return new ArrayList<>(cacheDTOMap.values());
    }






    public static final LoadingCache<String, String> LOCAL_CATCH = CacheBuilder.newBuilder()
            .maximumSize(100)
            // 固定2分钟后失效
            .expireAfterWrite(2,TimeUnit.MINUTES)
            .removalListener(removalNotification -> log.info("DEFAULT_ORDER_RULE local catch remove... key ===> " + removalNotification.getKey()))
            .recordStats()
            .build(new CacheLoader<String, String>() {
                // 处理缓存键不存在缓存值时的处理逻辑
                @Override
                public String load(String key) {
                    return null;
                }
            });

    @SneakyThrows
    private PurchaseConstraintCacheDTO getDefaultPcCacheDTO(CheckPurchaseConstraintInDto pcInDto, String tenantCode) {

        List<PurchaseConstraintRule> defaultPcRuleList = new ArrayList<>();
        PurchaseConstraintRule purchaseConstraintRule;

        String defaultOrderMaxQtyPerSku = getString(tenantCode,DEFAULT_ORDER_MAX_QTY_PER_SKU);
        String defaultOrderMaxAmount = getString(tenantCode, DEFAULT_ORDER_MAX_AMOUNT);

        defaultOrderMaxQtyPerSku = DEFAULT_ORDER_MAX_QTY_PER_SKU.equals(defaultOrderMaxQtyPerSku) ? null : defaultOrderMaxQtyPerSku;
        defaultOrderMaxAmount = DEFAULT_ORDER_MAX_AMOUNT.equals(defaultOrderMaxAmount) ? null : defaultOrderMaxAmount;

        if (StringUtils.isAllBlank(defaultOrderMaxQtyPerSku, defaultOrderMaxAmount)) {
            // 没有配置默认的限购
            return null;
        }
        for (PurchaseConstraintRuleTypeEnum value : PurchaseConstraintRuleTypeEnum.values()) {
            purchaseConstraintRule = new PurchaseConstraintRule();
            purchaseConstraintRule.setPurchaseConstraintRuleType(value.getCode());
            switch (value) {
                case CUSTOMER_MAX_QTY_PER_PRODUCT:
                case CUSTOMER_MAX_QTY_ALL_PRODUCTS:
                case CUSTOMER_MAX_AMOUNT:
                case ORDER_MAX_QUANTITY:
                case ORDER_MAX_AMOUNT_PER_SKU:
                case FIRST_REFUSAL:
                    break;
                case ORDER_MAX_QTY_PER_SKU:
                    purchaseConstraintRule.setPurchaseConstraintValue(defaultOrderMaxQtyPerSku);
                    break;
                case ORDER_MAX_AMOUNT:
                    purchaseConstraintRule.setPurchaseConstraintValue(defaultOrderMaxAmount);
                    break;
            }
            if (StringUtils.isNotBlank(purchaseConstraintRule.getPurchaseConstraintValue())) {
                defaultPcRuleList.add(purchaseConstraintRule);
            }
        }
        PurchaseConstraintCacheDTO defaultPcCacheDTO = new PurchaseConstraintCacheDTO();
        defaultPcCacheDTO.setDomainCode(pcInDto.getDomainCode());
        defaultPcCacheDTO.setTenantCode(pcInDto.getTenantCode());
        defaultPcCacheDTO.setOrgCode(pcInDto.getOrgCode());
        defaultPcCacheDTO.setPurchaseConstraintName("default purchase constrain");
        defaultPcCacheDTO.setPurchaseConstraintCode("default purchase constrain");
        defaultPcCacheDTO.setPurchaseConstraintStatus(PurchaseConstraintStatusEnum.EFFECTIVE.getCode());
        defaultPcCacheDTO.setPurchaseConstraintRuleList(defaultPcRuleList);
        defaultPcCacheDTO.setDescription("默认限购");
        defaultPcCacheDTO.setPriceSetting(0);
        defaultPcCacheDTO.setPriority(9999999);
        return defaultPcCacheDTO;
    }

    private String getString(String tenantCode,String systemValue) {
        String defaultOrderMaxQtyPerSkuKey = tenantCode + systemValue;
        String defaultOrderMaxQtyPerSku = LOCAL_CATCH.getIfPresent(defaultOrderMaxQtyPerSkuKey);
        if (StringUtils.isBlank(defaultOrderMaxQtyPerSku)) {
            defaultOrderMaxQtyPerSku = masterDataFeignClientComponent.masterDataValue(tenantCode, systemValue);
            String value = StringUtil.isNotBlank(defaultOrderMaxQtyPerSku) ? defaultOrderMaxQtyPerSku : systemValue;
            LOCAL_CATCH.put(defaultOrderMaxQtyPerSkuKey, value);
        }
        return defaultOrderMaxQtyPerSku;
    }

    /**
     * 检查累计规则
     */
    public CheckPurchaseConstraintResult checkRollupRules(CheckPurchaseConstraintInDto pcInDto,
                                                           Map<String, List<PurchaseConstraintCacheDTO>> skuPcListMap,
                                                           Map<String, CheckPurchaseConstraintProductDto> skuProductMap,
                                                           List<PurchaseConstraintCacheDTO> pcCacheDTOList,
                                                           Map<String, List<PurchaseConstraintCacheDTO>> skuHitPcMap, Map<String, Map<Integer, String>> ruleTypeMap) {

        Map<String, CheckPurchaseConstraintProductDto> skuMap = pcInDto.getCheckPurchaseConstraintProducts().stream()
                .collect(Collectors.toMap(CheckPurchaseConstraintProductDto::getSkuCode, a -> a, (a, b) -> a));

        // 增量累计规则缓存 在校验累计规则之前,需要加载future.get方法
        List<CheckPurchaseConstraintProductDto> shoppingCartItemList = pcInDto.getCheckPurchaseConstraintProducts();
        List<PcRuleCalculateModel.IncrementProduct> productIncrementList = shoppingCartItemList.stream()
                .map(m -> {
                    CheckPurchaseConstraintProductDto checkPurchaseConstraintProductDto = skuMap.get(m.getSkuCode());
                    PcRuleCalculateModel.IncrementProduct productIncrement = new PcRuleCalculateModel.IncrementProduct();
                    productIncrement.setProductCode(m.getProductCode());
                    productIncrement.setSkuCode(m.getSkuCode());
                    productIncrement.setSellAmount(m.getPromotionAmount());
                    productIncrement.setSkuCount(m.getQuantity());
                    productIncrement.setCategoryCodes(checkPurchaseConstraintProductDto.getCategoryCodes());
                    productIncrement.setBrandCode(checkPurchaseConstraintProductDto.getBrandCode());
                    productIncrement.setAttributes(checkPurchaseConstraintProductDto.getAttributes());
                    productIncrement.setProductTag(checkPurchaseConstraintProductDto.getProductTag());
                    productIncrement.setSpuAttributes(checkPurchaseConstraintProductDto.getSpuAttributes());
                    productIncrement.setHitPcCodes(null == skuHitPcMap.get(m.getSkuCode()) ? null : skuHitPcMap.get(m.getSkuCode()).stream().map(FindPurchaseConstraintOutDto::getPurchaseConstraintCode).collect(Collectors.toList()));
                    productIncrement.setPriceSetting(m.getPriceSetting());
                    return productIncrement;
                }).collect(Collectors.toList());

        PcRuleCalculateModel request = new PcRuleCalculateModel();

        request.setTenantCode(pcInDto.getTenantCode());
        request.setUserCode(pcInDto.getMemberCode());
        request.setOrgCode(pcInDto.getOrgCode());
        request.setForward(true);
        if (Boolean.TRUE.equals(pcInDto.getUseIncrement())) {
            request.setType(PcRuleCalculateTypeEnum.INCREMENT.getCode());
        } else {
            request.setType(PcRuleCalculateTypeEnum.CHECK.getCode());
        }
        request.setOrderCode(pcInDto.getOrderCode());
        request.setQualifications(pcInDto.getQualifications());
        request.setIncrementProducts(productIncrementList);
        request.setPcCacheDTOs(pcCacheDTOList);
        Result<PcRuleCheckResult> pcRuleIncrementResult = pcRuleComponent.pcRuleIncrement(request,ruleTypeMap);
        if (pcRuleIncrementResult != null) {
            PcRuleCheckResult data = pcRuleIncrementResult.getData();
            List<CheckPurchaseConstraintProductResult> pcProductResult = skuPcListMap.entrySet()
                    .stream()
                    .filter(pcEntry -> pcEntry.getValue()
                            .stream()
                            .anyMatch(x -> Objects.equals(x.getPurchaseConstraintCode(), data.getPcCode())))
                    .map(pcEntry -> {
                        CheckPurchaseConstraintProductDto checkPcProductDto = skuProductMap.get(pcEntry.getKey());
                        return CheckPurchaseConstraintProductResult
                                .builder()
                                .productCode(checkPcProductDto.getProductCode())
                                .skuCode(checkPcProductDto.getSkuCode())
                                .mainProductNo(checkPcProductDto.getMainProductNo()).build();
                    })
                    .collect(Collectors.toList());
            CheckPurchaseConstraintResult checkPurchaseConstraintResult = new CheckPurchaseConstraintResult();
            checkPurchaseConstraintResult.setPurchaseConstraintCode(data.getPcCode());
            checkPurchaseConstraintResult.setProducts(pcProductResult);
            checkPurchaseConstraintResult.setCanBuy(Boolean.FALSE);
            checkPurchaseConstraintResult.setHitPurchaseConstraint(Boolean.TRUE);
            checkPurchaseConstraintResult.setPurchaseConstraintRule(data.getPcRule());
            checkPurchaseConstraintResult.setErrorMessage(pcRuleIncrementResult.getMessage());
            checkPurchaseConstraintResult.setErrorCode(pcRuleIncrementResult.getCode());
            return checkPurchaseConstraintResult;
        }
        return null;
    }

    /**
     * 获取订单中命中限购的所有商品总金额
     * @param hitPurchaseConstraintCode
     * @param skuPcListMap
     * @param skuProductMap
     * @param pcProductResult
     * @return
     */
    private BigDecimal getTotalAmt(String hitPurchaseConstraintCode,
                                   Map<String, List<PurchaseConstraintCacheDTO>> skuPcListMap,
                                   Map<String, CheckPurchaseConstraintProductDto> skuProductMap,
                                   List<CheckPurchaseConstraintProductResult> pcProductResult) {
        BigDecimal totalAmt = BigDecimal.ZERO;
        for(Map.Entry<String, List<PurchaseConstraintCacheDTO>> pcEntry : skuPcListMap.entrySet()){
            if(pcEntry.getValue()
                    .stream()
                    .map(PurchaseConstraintCacheDTO::getPurchaseConstraintCode)
                    .collect(Collectors.toList()).contains(hitPurchaseConstraintCode)){

                CheckPurchaseConstraintProductDto checkPurchaseConstraintProductDto = skuProductMap.get(pcEntry.getKey());
                totalAmt = totalAmt.add(checkPurchaseConstraintProductDto.getPromotionAmount());
                pcProductResult.add(CheckPurchaseConstraintProductResult
                                        .builder()
                                        .productCode(checkPurchaseConstraintProductDto.getProductCode())
                                        .skuCode(checkPurchaseConstraintProductDto.getSkuCode())
                                        .mainProductNo(checkPurchaseConstraintProductDto.getMainProductNo()).build());
            }
        }
        return totalAmt;
    }

    /**
     * 获取订单中命中限购的所有商品总数量
     * @param hitPurchaseConstraintCode
     * @param skuPcListMap
     * @param skuProductMap
     * @param pcProductResult
     * @return
     */
    private Integer getTotalQuantity(String hitPurchaseConstraintCode,
                                   Map<String, List<PurchaseConstraintCacheDTO>> skuPcListMap,
                                   Map<String, CheckPurchaseConstraintProductDto> skuProductMap,
                                   List<CheckPurchaseConstraintProductResult> pcProductResult) {
        Integer totalQuantity = 0;
        for(Map.Entry<String, List<PurchaseConstraintCacheDTO>> pcEntry : skuPcListMap.entrySet()){
            if(pcEntry.getValue()
                    .stream()
                    .map(PurchaseConstraintCacheDTO::getPurchaseConstraintCode)
                    .collect(Collectors.toList()).contains(hitPurchaseConstraintCode)){
                CheckPurchaseConstraintProductDto checkPurchaseConstraintProductDto = skuProductMap.get(pcEntry.getKey());
                totalQuantity = totalQuantity + checkPurchaseConstraintProductDto.getQuantity();
                pcProductResult.add(CheckPurchaseConstraintProductResult
                        .builder()
                        .productCode(checkPurchaseConstraintProductDto.getProductCode())
                        .skuCode(checkPurchaseConstraintProductDto.getSkuCode())
                        .mainProductNo(checkPurchaseConstraintProductDto.getMainProductNo()).build());
            }
        }
        return totalQuantity;
    }


    /**
     * 商品选择器筛选限购信息
     * @param caches List<PurchaseConstraintCacheDTO> 待筛选限购信息
     * @param product ProductCodes 商品范围
     * @return
     */
    public List<PurchaseConstraintCacheDTO> filterPurchaseConstraintByProduct(List<PurchaseConstraintCacheDTO> caches,
                                                                              ProductCodes product) {

        //3.商品断言
        //3.1 品类
        List<String> categoryCodes = CollectionUtils.isEmpty(product.getCategoryCodes()) ? new ArrayList<>() : product.getCategoryCodes();//购物车某个商品的分类
        Predicate<TPromoActivityProductVO> cgPredicateAll = x -> PromotionConstants.UNLIMITED.equals(x.getCategoryCode());//正反选都适用
        Predicate<TPromoActivityProductVO> cgPredicate = x -> Arrays.stream(x.getCategoryCode().split(","))
                .anyMatch(y -> categoryCodes.stream().anyMatch(z -> z.concat(">").endsWith(y.concat(">"))));//反选.negate()
        //3.2 品牌
        Predicate<TPromoActivityProductVO> bdPredicateAll = x -> PromotionConstants.UNLIMITED.equals(x.getBrandCode());//正反选都适用
        Predicate<TPromoActivityProductVO> bdPredicate = x -> Arrays.stream(x.getBrandCode().split(",")).anyMatch(y -> y.equals(product.getBrandCode()));//反选.negate()

        //3.3 正选Attribute断言：为空则匹配3个AttributeCode全是0000的活动，不为空则要大于等于活动的标签
        Predicate<TPromoActivityProductVO> attributePredicate = activityCacheDomain.attributeAllPredicate().or(activityCacheDomain.attributePredicate(product));
        //3.3.1 反选Attribute断言：为空则匹配3个AttributeCode全是0000的活动，不为空则要大于等于活动的标签
        Predicate<TPromoActivityProductVO> attributePredicate1 = activityCacheDomain.attributeAllPredicate().or(activityCacheDomain.attributeNegatePredicate(product));

        //3.4 正选商品 spuCode相等 && 库中sku数据为空||(库中sku不为空&&(库中sku数据是0000||库中sku和参数sku相等)) -- 使用白名单和和黑名单替代

        //3.5 套装商品 -- 没人知道什么样的商品不处理

        // 3.6 商品标签
        Predicate<TPromoActivityProductVO> tagPredicateAll = x -> {
            return activityCacheDomain.isUnlimited(x.getProductTag());
        };// 正反选都适用

        List<String> currProductTagList = new ArrayList<>(); // 当前商品拥有的商品标签集合
        if(StringUtil.isNotBlank(product.getProductTag())) {
            currProductTagList.addAll(Arrays.asList(product.getProductTag().split(Constants.COMMA_SEPARATOR)));
        }
        Predicate<TPromoActivityProductVO> tagPredicate = x -> {
            return Arrays.stream(x.getProductTag().split(Constants.COMMA_SEPARATOR))
                .anyMatch(currProductTagList::contains);
        };// 反选.negate()

        List<PurchaseConstraintCacheDTO> newCache = new ArrayList<>();//符合的限购

        if (CollectionUtils.isEmpty(caches)) {
            return newCache;
        }

        for (PurchaseConstraintCacheDTO activityCacheDTO : caches) {
            List<ProductDetail> blackProductDetails = activityCacheDTO.getProductDetailBlackList();

            if(CollectionUtils.isNotEmpty(activityCacheDTO.getProductDetailBlackList())){ //黑名单直接过滤
                List<String> blackSkuCodeList = blackProductDetails
                                                        .stream()
                                                        .map(ProductDetail::getSkuCode)
                                                        .collect(Collectors.toList());
                if(blackSkuCodeList.contains(product.getSkuCode())){
                    continue;
                }
            }

            //正反选商品
            boolean selection = ProductSelectionEnum.SELECT.equalsCode(activityCacheDTO.getProductSelectionType());
            Predicate<TPromoActivityProductVO> newCgPredicate = selection ? cgPredicateAll.or(cgPredicate) : cgPredicateAll.or(cgPredicate.negate());// 品类
            Predicate<TPromoActivityProductVO> newBdPredicate = selection ? bdPredicateAll.or(bdPredicate) : bdPredicateAll.or(bdPredicate.negate());// 品牌
            Predicate<TPromoActivityProductVO> newAttributePredicate = selection ? attributePredicate : attributePredicate1;//标签属性
            Predicate<TPromoActivityProductVO> newTagPredicate = selection ? tagPredicateAll.or(tagPredicate) : tagPredicateAll.or(tagPredicate.negate());// 商品标签

            List<ProductScope> productScopeList = activityCacheDTO.getProducts();

            //-------------指定范围 productType==01
            if (!CollectionUtils.isEmpty(productScopeList)) {//如果入参套装编码有值，就不判读商品属性。限购只有一个商品范围
                for (ProductScope  productScope : productScopeList) {
                    TPromoActivityProductVO tPromoActivityProductVO = BeanCopyUtils.jsonCopyBean(productScope, TPromoActivityProductVO.class);
                    //指定范围的数据对比：品类、品牌and标签属性
                    boolean categoryCodeTest = newCgPredicate.test(tPromoActivityProductVO);
                    boolean brandCodeTest = newBdPredicate.test(tPromoActivityProductVO);
                    boolean attributeTest = newAttributePredicate.test(tPromoActivityProductVO);
                    boolean productTagTest = newTagPredicate.test(tPromoActivityProductVO);
                    if (categoryCodeTest && brandCodeTest && attributeTest && productTagTest) {
                        newCache.add(activityCacheDTO);
                    }
                }
            }

            //-------------指定商品spu、sku productType==02
            //包含该spu,说明当前spu下的数据可能符合sku
            List<ProductDetail> whiteProductDetails = activityCacheDTO.getProductDetails();
            if(!CollectionUtils.isEmpty(whiteProductDetails)){
                List<String> skuCodeList = whiteProductDetails
                                                    .stream()
                                                    .map(ProductDetail::getSkuCode)
                                                    .collect(Collectors.toList());

                if((selection && skuCodeList.contains(product.getSkuCode())) // 正选是白名单
                        || (!selection && !skuCodeList.contains(product.getSkuCode()))){ // 反选是黑名单
                    newCache.add(activityCacheDTO);
                }
            }
        }
        return newCache;
    }


    /**
     * 判断当前限购的通用规则是否是有效的限购
     * 1. 时间
     * 2. 店铺
     * 3. 人员
     * @param pcCacheDTO
     * @param qualifications
     * @param orgCodes
     * @param checkTime 兼容限购缓存; 如果重复周期 是跳日的, 没有限购的某一天也需要进行缓存增量加减
     * @return
     */
    @SneakyThrows
    public boolean checkCommonPurchaseConstraint(PurchaseConstraintCacheDTO pcCacheDTO,
                                                  Map<String, List<String>> qualifications,
                                                  List<String> orgCodes,
                                                  Boolean checkTime) {

        //1. 时间 checkTime null/true 进行check
        if (!Boolean.FALSE.equals(checkTime)
                && (Boolean.FALSE.equals(checkPeriod(pcCacheDTO)))){
                return Boolean.FALSE;

        }


        //2. 店铺
        if(Boolean.FALSE.equals(checkOrgCodes(pcCacheDTO, orgCodes))){
            return Boolean.FALSE;
        }


        //3. 人员资格，未开启优先购买的情况
        if (FirstRefusalEnum.NO.getCode().equals(pcCacheDTO.getFirstRefusal())
                && Boolean.TRUE.equals(!checkQualification(pcCacheDTO, qualifications))) {
            return Boolean.FALSE;
        }

        return Boolean.TRUE;
    }


    /**
     * 非人员资格限购
     * @param pcCacheDTO
     * @param orgCodes
     * @param checkTime
     * @return
     */
    @SneakyThrows
    public boolean checkCommonPurchaseConstraintNoQualifications(PurchaseConstraintCacheDTO pcCacheDTO,
                                                 List<String> orgCodes,
                                                 Boolean checkTime) {

        //1. 时间 checkTime null/true 进行check
        if (!Boolean.FALSE.equals(checkTime)
                && (Boolean.FALSE.equals(checkPeriod(pcCacheDTO)))){
            return Boolean.FALSE;

        }


        //2. 店铺
        if(Boolean.FALSE.equals(checkOrgCodes(pcCacheDTO, orgCodes))){
            return Boolean.FALSE;
        }

        return Boolean.TRUE;
    }


    /**
     * 检查店铺
     * @param pcCacheDTO
     * @param orgCodes
     * @return
     */
    public Boolean checkOrgCodes(PurchaseConstraintCacheDTO pcCacheDTO, List<String> orgCodes) {
        try {

            List<ActivityStore> channelStores = pcCacheDTO.getChannelStores();
            if (CollectionUtils.isEmpty(channelStores)) {
                return Boolean.TRUE;
            }

            for (ActivityStore channelStore : channelStores) {
                if (orgCodes.contains(channelStore.getOrgCode())) {
                    return Boolean.TRUE;
                }
            }
            return Boolean.FALSE;
        } catch (Exception e) {
            log.error("checkOrgCodes error", e);
            return false;
        }

    }

    /**
     * 校验人员资格
     * @param pcCacheDTO
     * @param qualifications
     * @return
     */
    private Boolean checkQualification(PurchaseConstraintCacheDTO pcCacheDTO, Map<String, List<String>> qualifications) {
        try {

            List<Qualification> pcQualificationList = pcCacheDTO.getQualifications();
            if (CollectionUtils.isNotEmpty(pcQualificationList)) {
                List<QualificationModel> qualificationModels = QualificationModel.convertToModel(pcQualificationList);
                QualificationFilter qualificationFilter = new QualificationFilter(qualificationModels);
                return qualificationFilter.filter(QualificationModel.convertToModel(qualifications));
            }
            return Boolean.TRUE;
        } catch (Exception e) {
            log.error("checkQualification error", e);
            return false;
        }
    }

    /**
     * 校验时间
     * @param purchaseConstraintCacheDTO
     * @return
     */
    private Boolean checkPeriod(PurchaseConstraintCacheDTO purchaseConstraintCacheDTO) {

        try {
            ActivityPeriod activityPeriod = purchaseConstraintCacheDTO.getActivityPeriod();
            if (null != activityPeriod) {
                Date purchaseConstraintStartTime = purchaseConstraintCacheDTO.getPurchaseConstraintStartTime();
                Date purchaseConstraintEndTime = purchaseConstraintCacheDTO.getPurchaseConstraintEndTime();
                String purchaseConstraintStartTimeStr;
                String purchaseConstraintEndTimeStr;
                String promotionTime = DateUtil.format(new Date(), DateUtil.FORMAT_YYYYMMDDHHMISS_14);
                if (null == purchaseConstraintEndTime) { // 若限购结束时间为空则将结束时间设置为当前时间的后10天，为了用于临时计算
                    purchaseConstraintEndTimeStr = DateUtil.format(DateUtil.addDay(BigDecimal.TEN.intValue()), DateUtil.FORMAT_YYYYMMDDHHMISS_14);
                } else {
                    purchaseConstraintEndTimeStr = DateUtil.format(purchaseConstraintEndTime, DateUtil.FORMAT_YYYYMMDDHHMISS_14);
                }

                if (null == purchaseConstraintStartTime) { // 若限购开始时间为空则将结束时间设置为当前时间的前10天，为了用于临时计算
                    purchaseConstraintStartTimeStr = DateUtil.format(DateUtil.addDay(-BigDecimal.TEN.intValue()), DateUtil.FORMAT_YYYYMMDDHHMISS_14);
                } else {
                    purchaseConstraintStartTimeStr = DateUtil.format(purchaseConstraintStartTime, DateUtil.FORMAT_YYYYMMDDHHMISS_14);
                }

                return CronUtil.checkDate(activityPeriod.getBeginPeriod(),
                        activityPeriod.getEndPeriod(),
                        activityPeriod.getIntervalWeek(),
                        purchaseConstraintStartTimeStr,
                        purchaseConstraintEndTimeStr,
                        promotionTime);
            }
            return Boolean.TRUE;
        } catch (Exception e) {
            log.error("checkPeriod error", e);
            return false;
        }
    }

    /**
     * 核算同一个spu的所有sku可购买数量
     * @param calAvailableQtyInDto
     * @return
     */
    public List<CalAvailableQtyResult> calAvailableQty(CalAvailableQtyInDto calAvailableQtyInDto) {
        List<CalAvailableQtyResult> resultList = new CopyOnWriteArrayList<>();
        Map<String, List<ProductAttribute>> skuAttributeMap = calAvailableQtyInDto.getSkuAttributeMap();
        List<PurchaseConstraintCacheDTO> purchaseConstraintCacheDTOList = pcCacheComponent.queryValidPcFromCache(pcService.queryEffectivePc(calAvailableQtyInDto.getTenantCode()));
        //productList不为空不需要查询
        List<PurchaseConstraintCacheDTO> currPurchaseConstraintList =  getPurchaseConstraintList(calAvailableQtyInDto, purchaseConstraintCacheDTOList);

        if(Boolean.TRUE.equals(calAvailableQtyInDto.getIgnoreProductScopeFilter())){
            skuAttributeMap.entrySet().parallelStream().forEach(entry -> {
                CalAvailableQtyInDto calAvailableQtyInDtoTemp = BeanCopyUtils.jsonCopyBean(calAvailableQtyInDto, CalAvailableQtyInDto.class);
                CalAvailableQtyProductInDto calAvailableQtyProduct = new CalAvailableQtyProductInDto();
                if (null != calAvailableQtyInDtoTemp.getSkuProductMap()){
                    calAvailableQtyProduct = calAvailableQtyInDtoTemp.getSkuProductMap().get(entry.getKey());
                }else {
                    calAvailableQtyProduct = calAvailableQtyInDtoTemp.getCalAvailableQtyProduct();
                }
                calAvailableQtyProduct.setSkuCode(entry.getKey());
                calAvailableQtyProduct.setAttributes(entry.getValue());
                calAvailableQtyInDtoTemp.setCalAvailableQtyProduct(calAvailableQtyProduct);

                ProductCodes productDTO = ProductCodes.builder()
                        .categoryCodes(calAvailableQtyProduct.getCategoryCodes())
                        .brandCode(calAvailableQtyProduct.getBrandCode())
                        .productCode(calAvailableQtyProduct.getProductCode())
                        .skuCode(calAvailableQtyProduct.getSkuCode())
                        .attributes(calAvailableQtyProduct.getAttributes())
                        .spuAttributes(calAvailableQtyProduct.getSpuAttributes())
                        .productTag(calAvailableQtyProduct.getProductTag())
                        .build();
                // 匹配当前商品范围的限购
                List<PurchaseConstraintCacheDTO> finalPurchaseConstraintList = filterPurchaseConstraintByProduct(currPurchaseConstraintList, productDTO);
                resultList.add(getCalAvailableQtyResults(calAvailableQtyInDtoTemp, finalPurchaseConstraintList));
            });
        }else if(CollectionUtils.isNotEmpty(calAvailableQtyInDto.getCalAvailableQtyProductList())){
            calAvailableQtyInDto.getCalAvailableQtyProductList().parallelStream().forEach(entry->{
                List<PurchaseConstraintCacheDTO> purchaseConstraintCacheDTOS = new ArrayList<>(purchaseConstraintCacheDTOList);
                CalAvailableQtyInDto calProduct = BeanCopyUtils.jsonCopyBean(calAvailableQtyInDto, CalAvailableQtyInDto.class);
                calProduct.setCalAvailableQtyProduct(entry);
                calProduct.setCalAvailableQtyProductList(null);
                List<PurchaseConstraintCacheDTO> currPurchaseConstraintListByProduct =  getPurchaseConstraintList(calProduct, purchaseConstraintCacheDTOS);
                resultList.add(getCalAvailableQtyResults(calProduct, currPurchaseConstraintListByProduct));
            });
        }else {
            resultList.add(getCalAvailableQtyResults(calAvailableQtyInDto, currPurchaseConstraintList));
        }
        //转换类型
        convertCustomList(resultList);
        return resultList;
    }

    private void convertCustomList(List<CalAvailableQtyResult> resultList) {
        resultList.stream()
                .map(CalAvailableQtyResult::getPurchaseConstraint)
                .filter(Objects::nonNull)
                .forEach(purchaseConstraint -> {
                    Optional.ofNullable(purchaseConstraint.getCustomCondition())
                            .filter(StringUtils::isNotEmpty)
                            .ifPresent(customConditionJsonStr ->
                                    purchaseConstraint.setCustomConditionsList(
                                            JSONArray.parseArray(customConditionJsonStr, CustomCondition.class)
                                    )
                            );

                    Optional.ofNullable(purchaseConstraint.getCustomRule())
                            .filter(StringUtils::isNotEmpty)
                            .ifPresent(customRuleJsonStr ->
                                    purchaseConstraint.setCustomRulesList(
                                            JSONArray.parseArray(customRuleJsonStr, CustomCondition.class)
                                    )
                            );
                });
    }

    /**
     * 核算单个sku可购买数量
     * @param calAvailableQtyInDto
     * @param currPurchaseConstraintList
     * @return
     */
    @SneakyThrows
    public CalAvailableQtyResult getCalAvailableQtyResults(CalAvailableQtyInDto calAvailableQtyInDto,
                                                           List<PurchaseConstraintCacheDTO> currPurchaseConstraintList) {

        if(CollectionUtils.isEmpty(currPurchaseConstraintList)){
            return CalAvailableQtyResult.builder()
                    .hitPurchaseConstraint(Boolean.FALSE)
                    .skuCode(calAvailableQtyInDto.getCalAvailableQtyProduct().getSkuCode())
                    .spuCode(calAvailableQtyInDto.getCalAvailableQtyProduct().getProductCode())
                    .availableQty(CalAvailableQtyResult.UN_LIMIT)
                    .firstRefusalValidFail(Boolean.FALSE)
                    .build();
        }else {
            // 排序返回优先级值最小的那个
            PurchaseConstraintCacheDTO currPcCache = currPurchaseConstraintList
                    .stream()
                    .sorted((pc1, pc2) -> {
                        if (pc1.getPriority().equals(pc2.getPriority())) {
                            return Long.compare(pc2.getCreateTime().getTime(), pc1.getCreateTime().getTime());
                        }
                        return Integer.compare(pc1.getPriority(), pc2.getPriority());
                    })
                    .collect(Collectors.toList())
                    .get(0);

            // 开启优先购买
            if (FirstRefusalEnum.YES.getCode().equals(currPcCache.getFirstRefusal())) {
                boolean checkQualificationResult = checkQualification(currPcCache, calAvailableQtyInDto.getQualifications());
                if (!checkQualificationResult) { //不符合优先购买，不能购买
                    return CalAvailableQtyResult.builder()
                            .hitPurchaseConstraint(Boolean.TRUE)
                            .firstRefusalValidFail(Boolean.TRUE)
                            .availableQty(CalAvailableQtyResult.NO_QTY)
                            .skuCode(calAvailableQtyInDto.getCalAvailableQtyProduct().getSkuCode())
                            .spuCode(calAvailableQtyInDto.getCalAvailableQtyProduct().getProductCode())
                            .purchaseConstraint(BeanCopyUtils.jsonCopyBean(currPcCache, FindPurchaseConstraintResult.class))
                            .build();
                }
            }

            List<PurchaseConstraintRule> pcRules = currPcCache.getPurchaseConstraintRuleList();
            //有限购，没有限购规则
            if(CollectionUtils.isEmpty(pcRules)){
                return CalAvailableQtyResult.builder()
                        .hitPurchaseConstraint(Boolean.TRUE)
                        .firstRefusalValidFail(Boolean.FALSE)
                        .availableQty(CalAvailableQtyResult.UN_LIMIT)
                        .skuCode(calAvailableQtyInDto.getCalAvailableQtyProduct().getSkuCode())
                        .spuCode(calAvailableQtyInDto.getCalAvailableQtyProduct().getProductCode())
                        .purchaseConstraint(BeanCopyUtils.jsonCopyBean(currPcCache, FindPurchaseConstraintResult.class))
                        .build();
            }

            int availableQty = CalAvailableQtyResult.UN_LIMIT; // 符合限购的可购买数量

            if (StringUtil.isEmpty(calAvailableQtyInDto.getMemberCode())) {
                return CalAvailableQtyResult.builder()
                        .skuCode(calAvailableQtyInDto.getCalAvailableQtyProduct().getSkuCode())
                        .spuCode(calAvailableQtyInDto.getCalAvailableQtyProduct().getProductCode())
                        .availableQty(availableQty)
                        .firstRefusalValidFail(Boolean.FALSE)
                        .hitPurchaseConstraint(Boolean.TRUE)
                        .purchaseConstraint(BeanCopyUtils.jsonCopyBean(currPcCache, FindPurchaseConstraintResult.class))
                        .build();
            }

            for(PurchaseConstraintRule pcRule : pcRules){
                PurchaseConstraintRuleTypeEnum purchaseConstraintRuleTypeEnum =
                        PurchaseConstraintRuleTypeEnum.valueOfCode(pcRule.getPurchaseConstraintRuleType());
                switch (purchaseConstraintRuleTypeEnum){
                    case ORDER_MAX_QTY_PER_SKU:
                        int orderMaxQtyPerSku = Integer.parseInt(pcRule.getPurchaseConstraintValue());
                        if(CalAvailableQtyResult.UN_LIMIT.equals(availableQty)
                                || orderMaxQtyPerSku < availableQty){
                            availableQty = orderMaxQtyPerSku;
                        }
                        break;
                    case ORDER_MAX_QUANTITY:
                        int orderMaxQuantity = Integer.parseInt(pcRule.getPurchaseConstraintValue());
                        if(CalAvailableQtyResult.UN_LIMIT.equals(availableQty)
                                || orderMaxQuantity < availableQty){
                            availableQty = orderMaxQuantity;
                        }
                        break;
                    case CUSTOMER_MAX_QTY_PER_PRODUCT:
                        String pcRuleKey = PcRuleCacheValuePair.builder()
                                .tenantCode(calAvailableQtyInDto.getTenantCode())
                                .pcCode(currPcCache.getPurchaseConstraintCode())
                                .userCode(calAvailableQtyInDto.getMemberCode())
                                .productCode(calAvailableQtyInDto.getCalAvailableQtyProduct().getProductCode())
                                .build()
                                .getPcRuleKey(pcRule.getPurchaseConstraintRuleType());

                        String string = redisClient.getString(pcRuleKey);
                        long customerBuyQty = Long.parseLong(Optional.ofNullable(string).orElse("0"));
                        long pcCustomerMaxQtyPerProduct = Long.parseLong(pcRule.getPurchaseConstraintValue());
                        if (0 != customerBuyQty) {
                            long customerAvailableQty = pcCustomerMaxQtyPerProduct - customerBuyQty;
                            customerAvailableQty = customerAvailableQty < 0 ? 0 : customerAvailableQty;
                            if (CalAvailableQtyResult.UN_LIMIT.equals(availableQty)
                                    || (int) customerAvailableQty < availableQty) {
                                availableQty = (int) customerAvailableQty;
                            }
                        } else {
                            if (CalAvailableQtyResult.UN_LIMIT.equals(availableQty)
                                    || (int) pcCustomerMaxQtyPerProduct < availableQty) {
                                availableQty = (int) pcCustomerMaxQtyPerProduct;
                            }
                        }
                        break;
                    case CUSTOMER_MAX_QTY_ALL_PRODUCTS:

                        String pcRuleAllProductKey = PcRuleCacheValuePair.builder()
                                .tenantCode(calAvailableQtyInDto.getTenantCode())
                                .pcCode(currPcCache.getPurchaseConstraintCode())
                                .userCode(calAvailableQtyInDto.getMemberCode())
                                .build()
                                .getPcRuleKey(pcRule.getPurchaseConstraintRuleType());

                        String allProductQty = redisClient.getString(pcRuleAllProductKey);

                        long allProductBuyCount = Long.parseLong(Optional.ofNullable(allProductQty).orElse("0"));

                        long maxQtyAllProduct = Long.parseLong(pcRule.getPurchaseConstraintValue());
                        if(allProductBuyCount > 0){
                            long customerAvailableQty = maxQtyAllProduct - allProductBuyCount;
                            customerAvailableQty = customerAvailableQty < 0 ? 0 : customerAvailableQty;
                            if(CalAvailableQtyResult.UN_LIMIT.equals(availableQty)
                                    || (int) customerAvailableQty < availableQty){
                                availableQty = (int) customerAvailableQty;
                            }
                        }else {
                            if(CalAvailableQtyResult.UN_LIMIT.equals(availableQty)
                                    || (int) maxQtyAllProduct < availableQty){
                                availableQty = (int) maxQtyAllProduct;
                            }
                        }
                        break;
                    default:break;
                }
            }

            return CalAvailableQtyResult.builder()
                    .skuCode(calAvailableQtyInDto.getCalAvailableQtyProduct().getSkuCode())
                    .spuCode(calAvailableQtyInDto.getCalAvailableQtyProduct().getProductCode())
                    .availableQty(availableQty)
                    .firstRefusalValidFail(Boolean.FALSE)
                    .hitPurchaseConstraint(Boolean.TRUE)
                    .purchaseConstraint(BeanCopyUtils.jsonCopyBean(currPcCache, FindPurchaseConstraintResult.class))
                    .build();

        }
    }


    public List<PurchaseConstraintCacheDTO> getPurchaseConstraintList(CalAvailableQtyInDto calAvailableQtyInDto,List<PurchaseConstraintCacheDTO> pcCacheDTOList) {

        if(CollectionUtils.isEmpty(pcCacheDTOList)){
            return pcCacheDTOList;
        }

        // 通用限购规则（时间、人员、店铺、商品）过滤后的限购集合
        List<PurchaseConstraintCacheDTO> finalPurchaseConstraintCacheList = new ArrayList<>();

        if (StringUtil.isNotEmpty(calAvailableQtyInDto.getMemberCode())){
            finalPurchaseConstraintCacheList =  pcCacheDTOList.stream()
                    .filter(f -> checkCommonPurchaseConstraint(f, calAvailableQtyInDto.getQualifications(), Collections.singletonList(calAvailableQtyInDto.getOrgCode()),calAvailableQtyInDto.getCheckTime()))
                    .collect(Collectors.toList());
        }else if (StringUtil.isEmpty(calAvailableQtyInDto.getMemberCode())){
            finalPurchaseConstraintCacheList =  pcCacheDTOList.stream()
                    .filter(f -> checkCommonPurchaseConstraintNoQualifications(f,  Collections.singletonList(calAvailableQtyInDto.getOrgCode()),calAvailableQtyInDto.getCheckTime()))
                    .collect(Collectors.toList());
        }



        if(CollectionUtils.isEmpty(finalPurchaseConstraintCacheList)){
            return finalPurchaseConstraintCacheList;
        }

        //自定义条件过滤
        finalPurchaseConstraintCacheList = this.filterPurchaseConstraintCacheByCustomCondition(calAvailableQtyInDto.getTenantCode(), finalPurchaseConstraintCacheList, calAvailableQtyInDto.getCustomMap());



        if(Boolean.TRUE.equals(calAvailableQtyInDto.getIgnoreProductScopeFilter())){// pdp里一个spu多个sku的暂时不处理
            return finalPurchaseConstraintCacheList;
        } else if (CollectionUtils.isNotEmpty(calAvailableQtyInDto.getCalAvailableQtyProductList())) {

            List<CalAvailableQtyProductInDto> calAvailableQtyProductList = calAvailableQtyInDto.getCalAvailableQtyProductList();
            List<PurchaseConstraintCacheDTO> puList = new ArrayList<>();
            for (CalAvailableQtyProductInDto calAvailableQtyProduct : calAvailableQtyProductList) {
                ProductCodes productDTO = ProductCodes.builder()
                        .categoryCodes(calAvailableQtyProduct.getCategoryCodes())
                        .brandCode(calAvailableQtyProduct.getBrandCode())
                        .productCode(calAvailableQtyProduct.getProductCode())
                        .skuCode(calAvailableQtyProduct.getSkuCode())
                        .attributes(calAvailableQtyProduct.getAttributes())
                        .spuAttributes(calAvailableQtyProduct.getSpuAttributes())
                        .productTag(calAvailableQtyProduct.getProductTag())
                        .build();

                // 匹配当前商品的限购
                List<PurchaseConstraintCacheDTO> purchaseConstraintCacheList = filterPurchaseConstraintByProduct(finalPurchaseConstraintCacheList, productDTO);
                puList.addAll(purchaseConstraintCacheList);
            }
            return puList;

        } else {
            CalAvailableQtyProductInDto calAvailableQtyProduct = calAvailableQtyInDto.getCalAvailableQtyProduct();

            ProductCodes productDTO = ProductCodes.builder()
                    .categoryCodes(calAvailableQtyProduct.getCategoryCodes())
                    .brandCode(calAvailableQtyProduct.getBrandCode())
                    .productCode(calAvailableQtyProduct.getProductCode())
                    .skuCode(calAvailableQtyProduct.getSkuCode())
                    .attributes(calAvailableQtyProduct.getAttributes())
                    .spuAttributes(calAvailableQtyProduct.getSpuAttributes())
                    .productTag(calAvailableQtyProduct.getProductTag())
                    .build();


            // 匹配当前商品的限购
            return filterPurchaseConstraintByProduct(finalPurchaseConstraintCacheList, productDTO);
        }
    }
}
