package com.gtech.promotion.component.flashsale;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.collections4.map.HashedMap;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.time.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.gtech.commons.exception.ErrorCode;
import com.gtech.commons.exception.Exceptions;
import com.gtech.commons.page.PageData;
import com.gtech.commons.page.PageParam;
import com.gtech.commons.redis.GTechCodeGenerator;
import com.gtech.commons.redis.GTechRedisTemplate;
import com.gtech.commons.result.PageResult;
import com.gtech.commons.result.Result;
import com.gtech.commons.utils.BeanCopyUtils;
import com.gtech.commons.utils.CheckUtils;
import com.gtech.commons.utils.DateUtil;
import com.gtech.commons.utils.StringUtil;
import com.gtech.ecomm.common.page.ResponsePage;
import com.gtech.ecomm.common.result.JsonResult;
import com.gtech.ecomm.order.vo.in.order.OrderQueryIn;
import com.gtech.ecomm.order.vo.out.order.OrderQueryOut;
import com.gtech.promotion.calc.CalcConstants;
import com.gtech.promotion.calc.CalcExecuter;
import com.gtech.promotion.calc.model.CalcShoppingCart;
import com.gtech.promotion.checker.activity.TPromoOrderChecker;
import com.gtech.promotion.checker.coupon.CouponErrorChecker;
import com.gtech.promotion.checker.flashsale.FlashSaleChecker;
import com.gtech.promotion.code.CodeHelper;
import com.gtech.promotion.code.activity.ActivityStatusEnum;
import com.gtech.promotion.code.activity.ActivityStoreEnum;
import com.gtech.promotion.code.activity.IncentiveLimitedFlagEnum;
import com.gtech.promotion.code.activity.LangTypeStandardEnum;
import com.gtech.promotion.code.activity.LimitationCodeEnum;
import com.gtech.promotion.code.activity.OperationTypeEnum;
import com.gtech.promotion.code.activity.OrderStatusEnum;
import com.gtech.promotion.code.activity.SelectorProductTypeEnum;
import com.gtech.promotion.code.marketing.ActivityTypeEnum;
import com.gtech.promotion.code.marketing.ActivityTypeProductTableEnum;
import com.gtech.promotion.code.marketing.LeaderPriceEnum;
import com.gtech.promotion.code.marketing.MarketingGroupStatusEnum;
import com.gtech.promotion.code.marketing.PrizeTypeEnum;
import com.gtech.promotion.code.marketing.SelectFlagEnum;
import com.gtech.promotion.code.marketing.SwitchEnum;
import com.gtech.promotion.code.marketing.TeamLeaderEnum;
import com.gtech.promotion.code.marketing.UserGroupStatusEnum;
import com.gtech.promotion.component.activity.ActivityCacheDomain;
import com.gtech.promotion.component.activity.ActivityComponentDomain;
import com.gtech.promotion.component.activity.PromoGroupDomain;
import com.gtech.promotion.component.activity.PromoGroupRelationDomain;
import com.gtech.promotion.component.activity.ShoppingCartDomain;
import com.gtech.promotion.component.marketing.MarketingCacheComponent;
import com.gtech.promotion.component.marketing.MarketingGroupComponent;
import com.gtech.promotion.dao.entity.marketing.MarketingGroupEntity;
import com.gtech.promotion.dao.model.activity.ActivityPeriodModel;
import com.gtech.promotion.dao.model.activity.LuckyDrawRuleModel;
import com.gtech.promotion.dao.model.activity.OperationLogModel;
import com.gtech.promotion.dao.model.activity.QualificationModel;
import com.gtech.promotion.dao.model.activity.TPromoIncentiveLimitedVO;
import com.gtech.promotion.dao.model.marketing.BoostSharingModel;
import com.gtech.promotion.dao.model.marketing.GroupUserCountDto;
import com.gtech.promotion.dao.model.marketing.MarketingGroupCodeMode;
import com.gtech.promotion.dao.model.marketing.MarketingGroupMode;
import com.gtech.promotion.dao.model.marketing.MarketingGroupUserMode;
import com.gtech.promotion.dao.model.marketing.MarketingLanguageModel;
import com.gtech.promotion.dao.model.marketing.MarketingModel;
import com.gtech.promotion.dao.model.marketing.PrizeModel;
import com.gtech.promotion.dao.model.marketing.flashsale.CacheFlashSaleModel;
import com.gtech.promotion.dao.model.marketing.flashsale.FlashPreSaleOrderModel;
import com.gtech.promotion.dao.model.marketing.flashsale.FlashSaleOrderDetailModel;
import com.gtech.promotion.dao.model.marketing.flashsale.FlashSaleOrderModel;
import com.gtech.promotion.dao.model.marketing.flashsale.FlashSaleProductModel;
import com.gtech.promotion.dao.model.marketing.flashsale.FlashSaleQualificationModel;
import com.gtech.promotion.dao.model.marketing.flashsale.FlashSaleStoreModel;
import com.gtech.promotion.dao.model.marketing.flashsale.OrderOutMode;
import com.gtech.promotion.dao.model.marketing.flashsale.OrderProductOutMode;
import com.gtech.promotion.domain.marketing.flashsale.FlashSaleDomain;
import com.gtech.promotion.dto.cache.ActivityCacheDTO;
import com.gtech.promotion.dto.cache.ProductSkuDetailDTO;
import com.gtech.promotion.dto.in.activity.ShoppingCartDTO;
import com.gtech.promotion.dto.in.activity.SingleProductDTO;
import com.gtech.promotion.dto.in.flashsale.FlashSaleShoppingCartDto;
import com.gtech.promotion.dto.out.activity.ShoppingCartOutDTO;
import com.gtech.promotion.dto.out.marketing.FlashSaleActivityInfoDTO;
import com.gtech.promotion.dto.out.marketing.FlashSaleActivityPriceDTO;
import com.gtech.promotion.dto.out.marketing.FlashSaleSkuActivityPriceDTO;
import com.gtech.promotion.exception.Check;
import com.gtech.promotion.exception.ErrorCodes;
import com.gtech.promotion.feign.OrderFeignClient;
import com.gtech.promotion.feign.PimFeignClient;
import com.gtech.promotion.feign.bean.ProductCodeRequest;
import com.gtech.promotion.helper.ActivityRedisHelpler;
import com.gtech.promotion.helper.RedisOpsHelper;
import com.gtech.promotion.service.activity.ActivityPeriodService;
import com.gtech.promotion.service.activity.ActivityProductDetailService;
import com.gtech.promotion.service.activity.LuckyDrawRuleService;
import com.gtech.promotion.service.activity.OperationLogService;
import com.gtech.promotion.service.activity.TPromoIncentiveLimitedService;
import com.gtech.promotion.service.flashsale.FlashSaleOrderDetailService;
import com.gtech.promotion.service.flashsale.FlashSaleOrderService;
import com.gtech.promotion.service.flashsale.FlashSaleProductService;
import com.gtech.promotion.service.flashsale.FlashSaleQualificationService;
import com.gtech.promotion.service.flashsale.FlashSaleStoreService;
import com.gtech.promotion.service.marketing.BoostSharingService;
import com.gtech.promotion.service.marketing.MarketingGroupCodeService;
import com.gtech.promotion.service.marketing.MarketingGroupService;
import com.gtech.promotion.service.marketing.MarketingGroupUserService;
import com.gtech.promotion.service.marketing.MarketingLanguageService;
import com.gtech.promotion.service.marketing.MarketingLuckDrawPrizeProductService;
import com.gtech.promotion.service.marketing.MarketingService;
import com.gtech.promotion.service.marketing.PrizeService;
import com.gtech.promotion.utils.ActivityFilterUtil;
import com.gtech.promotion.utils.ActivityProductCheckUtil;
import com.gtech.promotion.utils.Constants;
import com.gtech.promotion.utils.FlashSaleConstants;
import com.gtech.promotion.utils.MarketingConstants;
import com.gtech.promotion.utils.MarketingFilterUtil;
import com.gtech.promotion.vo.bean.ActivityPeriod;
import com.gtech.promotion.vo.bean.IncentiveLimited;
import com.gtech.promotion.vo.bean.ProductCodes;
import com.gtech.promotion.vo.bean.Qualification;
import com.gtech.promotion.vo.bean.ShoppingCartItem;
import com.gtech.promotion.vo.bean.ShoppingCartStore;
import com.gtech.promotion.vo.bean.marketing.MarketingLanguage;
import com.gtech.promotion.vo.bean.marketing.flashsale.FlashSaleProduct;
import com.gtech.promotion.vo.bean.marketing.flashsale.FlashSaleQualification;
import com.gtech.promotion.vo.bean.marketing.flashsale.FlashSaleStore;
import com.gtech.promotion.vo.param.activity.CalcShoppingCartParam;
import com.gtech.promotion.vo.param.activity.QueryMarketingActivityListByProductListParam;
import com.gtech.promotion.vo.param.activity.QueryPromotionOrMarketingNoFilterParam;
import com.gtech.promotion.vo.param.marketing.flashsale.FlashSaleCreateOrderParam;
import com.gtech.promotion.vo.param.marketing.flashsale.FlashSaleQueryListParam;
import com.gtech.promotion.vo.param.marketing.flashsale.SkuParam;
import com.gtech.promotion.vo.result.activity.ActivityGroupCache;
import com.gtech.promotion.vo.result.activity.CalcShoppingCartResult;
import com.gtech.promotion.vo.result.activity.QueryActivityListByProductResult;
import com.gtech.promotion.vo.result.flashpresale.ExportFlashPreProductResult;
import com.gtech.promotion.vo.result.flashpresale.ExportFlashPreSaleDto;
import com.gtech.promotion.vo.result.flashpresale.ExportFlashPreSaleResult;
import com.gtech.promotion.vo.result.flashpresale.ExportGroupDetailResult;
import com.gtech.promotion.vo.result.flashpresale.ExportGroupDto;
import com.gtech.promotion.vo.result.flashsale.ActivityLanguageResult;
import com.gtech.promotion.vo.result.flashsale.BoostSharingResult;
import com.gtech.promotion.vo.result.flashsale.FlashSaleFindResult;
import com.gtech.promotion.vo.result.flashsale.FlashSaleOrderCalaResult;
import com.gtech.promotion.vo.result.flashsale.FlashSaleProductResult;
import com.gtech.promotion.vo.result.flashsale.FlashSaleQueryListResult;
import com.gtech.promotion.vo.result.flashsale.FlashSaleQueryProductListResult;
import com.gtech.promotion.vo.result.flashsale.MarketingGroupResult;
import com.gtech.promotion.vo.result.flashsale.MarketingGroupUserResult;
import com.gtech.promotion.vo.result.flashsale.MarketingLanguageResult;
import com.gtech.promotion.vo.result.flashsale.QueryFlashSaleListByProductResult;
import com.gtech.promotion.vo.result.flashsale.QueryMarketingActivityListByProductListResult;
import com.gtech.promotion.vo.result.flashsale.QueryMarketingActivityListByProductListResult.ProductPrice;
import com.gtech.promotion.vo.result.flashsale.QueryPromotionOrMarketingNoFilterResult;
import com.gtech.promotion.vo.result.marketing.MarketingQueryResult;

import cn.hutool.core.collection.CollectionUtil;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component
public class FlashSaleComponent {

    public static final int SKU_INVENTORY = 10000000;
    public static final int ZERO = 0;

    @Autowired
    private GTechCodeGenerator gTechCodeGenerator;

    @Autowired
    private MarketingService marketingService;

    @Autowired
    private MarketingLanguageService languageService;

    @Autowired
    private FlashSaleStoreService flashSaleStoreService;
    @Autowired
    private ActivityPeriodService activityPeriodService;

    @Autowired
    private FlashSaleQualificationService flashSaleQualificationService;

    @Autowired
    private FlashSaleProductService flashSaleProductService;

    @Autowired
    private FlashSaleOrderService flashSaleOrderService;

    @Autowired
    private FlashSaleOrderDetailService flashSaleOrderDetailService;

    @Autowired
    private RedisOpsHelper redisOpsHelper;

    @Autowired
    private GTechRedisTemplate redisTemplate;

    @Autowired
    private OperationLogService operationLogService;
    @Autowired
    private MarketingCacheComponent marketingCacheComponent;
    @Autowired
    private ActivityComponentDomain activityComponentDomain;

    @Autowired
    private MarketingGroupService marketingGroupService;

    @Autowired
    private MarketingGroupUserService marketingGroupUserService;

    @Autowired
    private ActivityCacheDomain activityCacheDomain;

    @Autowired
    private ShoppingCartDomain shoppingCartDomain;

    @Autowired
    private CalcExecuter calcExecuter;

    @Autowired
    private PromoGroupRelationDomain promoGroupRelationDomain;

    @Autowired
    private TPromoIncentiveLimitedService limitedService;

    @Autowired
    private ActivityRedisHelpler activityRedisHelpler;

    @Autowired
    private BoostSharingService boostSharingService;

    @Autowired
    private MarketingGroupComponent marketingGroupComponent;


    @Autowired
    private PromoGroupDomain promoGroupDomain;


    @Autowired
    private MarketingGroupCodeService marketingGroupCodeService;

    @Autowired
    private LuckyDrawRuleService luckyDrawRuleService;

    @Autowired
    private ActivityProductDetailService productDetailService;


    @Autowired
    private PimFeignClient pimFeignClient;

    @Autowired
    private OrderFeignClient orderFeignClient;

    @Autowired
    private MarketingLuckDrawPrizeProductService marketingLuckDrawPrizeProductService;

    @Autowired
    private PrizeService prizeService;


    /***
     *
     * @param shoppingCart 入参
     * @param
     * @return
     */
    public List<FlashSaleOrderCalaResult> handlerShoppingCart(FlashSaleShoppingCartDto shoppingCart) {
        SingleProductDTO product = new SingleProductDTO();
        List<FlashSaleOrderCalaResult> list = new ArrayList<>();
        FlashSaleOrderCalaResult calcShoppingCartResult = null;
        String tenantCode = shoppingCart.getTenantCode();
        Map<String, CacheFlashSaleModel> flashSaleCacheMap = marketingCacheComponent.getFlashSaleCacheMap(tenantCode, shoppingCart.getLanguage(), shoppingCart.getActivityType());
        Check.check(flashSaleCacheMap.isEmpty(), FlashSaleChecker.SKU_NOT_MATCH_ACTIVITY);

        String activityCode = shoppingCart.getActivityCode();

        MarketingModel byActivityCode = marketingService.findByActivityCode(activityCode);

        Check.check(null == byActivityCode, FlashSaleChecker.SKU_NOT_MATCH_ACTIVITY);

        //获取sku或者spu商品
        List<String> skuOrSpuCodes = new ArrayList<>();
        String selectProductType = byActivityCode.getSelectProductType();
        List<FlashSaleProductModel> products;
        if (SelectorProductTypeEnum.SELECT_SKU.code().equals(selectProductType)){
            addSkuCodesByStore(shoppingCart, skuOrSpuCodes);
            products= this.getProductsByActivityCodesAndProducts(tenantCode, flashSaleCacheMap.keySet(), skuOrSpuCodes);
        }else if (SelectorProductTypeEnum.SELECT_SPU.code().equals(selectProductType)){
            addSpuCodesByStore(shoppingCart, skuOrSpuCodes);
            products = this.getProductsByActivityCodesAndProductsBySpu(tenantCode, flashSaleCacheMap.keySet(), skuOrSpuCodes);
            if (ActivityTypeEnum.LUCKY_DRAW.code().equals(shoppingCart.getActivityType())){
                List<PrizeModel> listByActivityCode = prizeService.findListByActivityCode(shoppingCart.getActivityCode());
                listByActivityCode.stream().filter(x->x.getPrizeType().equals(PrizeTypeEnum.PRODUCT.code())).forEach(x->{
                    FlashSaleProductModel flashSaleProductModel = new FlashSaleProductModel();
                    flashSaleProductModel.setProductCode(x.getPrizeCode());
                    flashSaleProductModel.setActivityCode(shoppingCart.getActivityCode());
                    flashSaleProductModel.setFlashPrice(BigDecimal.ZERO);
                    products.add(flashSaleProductModel);
                });
            }
        } else {
            products = null;
        }
        String marketingGroupCode = shoppingCart.getMarketingGroupCode();

        //是否团长，true是
        boolean leaderFlag = false;

        //查询拼团信息
        MarketingGroupMode marketingGroup = null;
        if (ActivityTypeEnum.GROUP.code().equals(shoppingCart.getActivityType())){
            marketingGroup = marketingGroupService.findByActivityCode(activityCode);
            leaderFlag = marketingGroupUserService.existLeaderByGroupCode(tenantCode, marketingGroupCode, activityCode, shoppingCart.getUserCode());
        }

        List<ShoppingCartStore> cartStoreList = shoppingCart.getCartStoreList();
        for (ShoppingCartStore shoppingCartStore : cartStoreList) {
            List<ShoppingCartItem> cartItemList = shoppingCartStore.getCartItemList();
            boolean effectiveFlag = false;
            for (ShoppingCartItem item : cartItemList) {
                product.setPrice(item.getProductPrice());
                List<String> orgCodes = new ArrayList<>();
                orgCodes.add(shoppingCartStore.getOrgCode());
                product.setOrgCodes(CodeHelper.getOrgCodes(orgCodes));
                product.setQualifications(shoppingCart.getQualifications());
                product.setTenantCode(tenantCode);
                product.setLanguage(shoppingCart.getLanguage());
                product.setAttributes(item.getAttributes());
                product.setBrandCode(item.getBrandCode());
                product.setCategoryCodes(item.getCategoryCodes());
                product.setProductCode(item.getProductCode());
                product.setSkuCode(item.getSkuCode());
                product.setCombineSkuCode(item.getCombineSkuCode());
                ActivityProductCheckUtil.checkProductCombine(product);

                flashSaleCacheMap = MarketingFilterUtil.filterActivityByOrgCodes(flashSaleCacheMap, product.getOrgCodes());

                //拼团参团人员不校验资格只针对团长
                if (leaderFlag){
                    flashSaleCacheMap = MarketingFilterUtil.filterActivityByQualifications(flashSaleCacheMap, product.getQualifications());
                }
                flashSaleCacheMap = MarketingFilterUtil.filterActivityByTime(flashSaleCacheMap, null);
                flashSaleCacheMap = marketingCacheComponent.filterActivityByProduct(flashSaleCacheMap, product, products,byActivityCode.getSelectProductType());

                flashSaleCacheMap = getStringCacheFlashSaleModelMap(flashSaleCacheMap, activityCode);

                CacheFlashSaleModel cacheFlashSaleModel = flashSaleCacheMap.get(activityCode);

                Optional<FlashSaleProductModel> first;
                if (SelectorProductTypeEnum.SELECT_SPU.code().equals(selectProductType)){
                    first = cacheFlashSaleModel.getProducts().stream().filter(x -> x.getProductCode().equals(product.getProductCode())).findFirst();
                }else {
                    first = cacheFlashSaleModel.getProducts().stream().filter(x -> x.getSkuCode().equals(product.getSkuCode())).findFirst();
                }


                if (first.isPresent()) {
                    FlashSaleProductModel productModel = first.get();
                    FlashSaleProductModel currentProductModel;
                    if (SelectorProductTypeEnum.SELECT_SPU.code().equals(selectProductType)){
                        currentProductModel = flashSaleProductService.findByActivityAndProduct(activityCode, productModel.getProductCode());
                        if (ActivityTypeEnum.LUCKY_DRAW.code().equals(shoppingCart.getActivityType())){
                            FlashSaleProductModel flashSaleProductModel = new FlashSaleProductModel();
                            flashSaleProductModel.setProductCode(productModel.getProductCode());
                            flashSaleProductModel.setFlashPrice(product.getPrice());
                            currentProductModel= flashSaleProductModel;
                        }
                    }else {
                        currentProductModel= flashSaleProductService.findByActivityAndSku(activityCode, productModel.getSkuCode());
                    }


                    String skuOrSpu = productModel.getSkuCode();
                    if (SelectorProductTypeEnum.SELECT_SPU.code().equals(byActivityCode.getSelectProductType())){
                        skuOrSpu = productModel.getProductCode();
                    }
                    boolean flag = true;
                    //针对抽奖做特殊处理 不检查参与次数
                    if (!ActivityTypeEnum.LUCKY_DRAW.code().equals(shoppingCart.getActivityType())){
                        flag = this.checkQuota(activityCode, skuOrSpu, item.getQuantity(), shoppingCart.getOrderNo(), currentProductModel);
                    }

                    MarketingGroupUserResult marketingGroupUserResult = null;
                    if (flag) {
                        if (ActivityTypeEnum.GROUP.code().equals(shoppingCart.getActivityType())) {
                            marketingGroupUserResult = new MarketingGroupUserResult();

                            //拼团信息填充
                            if (null != marketingGroup) {
                                Integer closeFlag = marketingGroup.getCloseFlag();
                                marketingGroupUserResult.setGroupSize(marketingGroup.getGroupSize());
                                marketingGroupUserResult.setCloseFlag(closeFlag);
                                marketingGroupUserResult.setEffectiveHour(marketingGroup.getEffectiveHour());
                                marketingGroupUserResult.setAutoGroupFlag(marketingGroup.getAutoGroupFlag());
                                marketingGroupUserResult.setShowLeaderPrice(marketingGroup.getShowLeaderPrice());

                            }
                            marketingGroupUserResult.setSelectProductType(selectProductType);
                            marketingGroupUserResult.setUserCode(shoppingCart.getUserCode());
                            marketingGroupUserResult.setGroupStatus(UserGroupStatusEnum.PROCESSING.code());
                            marketingGroupUserResult.setMarketingGroupCode(marketingGroupCode);
                            marketingGroupUserResult.setActivityCode(shoppingCart.getActivityCode());
                            marketingGroupUserResult.setProductCode(currentProductModel.getProductCode());
                            marketingGroupUserResult.setSkuCode(currentProductModel.getSkuCode());
                            marketingGroupUserResult.setOrgCode(currentProductModel.getOrgCode());
                            //拼团信息填充，用于计算以及保存
                            setMarketingGroupUserInformation(shoppingCart, marketingGroup, productModel, currentProductModel, marketingGroupUserResult,
                                    marketingGroupCode,leaderFlag,cacheFlashSaleModel.getActivityEnd());
                        }

                        List<FlashSaleOrderCalaResult.ShoppingCartItem> shoppingCartItemList;
                        BigDecimal promoRewardAmount = (item.getProductPrice().subtract(productModel.getFlashPrice())).multiply(new BigDecimal(item.getQuantity()));
                        if (ActivityTypeEnum.LUCKY_DRAW.code().equals(shoppingCart.getActivityType())){
                            promoRewardAmount = item.getProductPrice().multiply(new BigDecimal(item.getQuantity()));
                        }

                        if (null == calcShoppingCartResult) {
                            calcShoppingCartResult = new FlashSaleOrderCalaResult();
                            calcShoppingCartResult.setActivityCode(shoppingCart.getActivityCode());
                            calcShoppingCartResult.setActivityEnd(byActivityCode.getActivityEnd());
                            calcShoppingCartResult.setActivityBegin(byActivityCode.getActivityBegin());
                            calcShoppingCartResult.setActivityLabel(cacheFlashSaleModel.getActivityLabel());
                            calcShoppingCartResult.setActivityName(cacheFlashSaleModel.getActivityName());
                            List<MarketingLanguageModel> languages = cacheFlashSaleModel.getLanguages();
                            for (MarketingLanguageModel language : languages) {
                                if (language.getLanguage().equalsIgnoreCase(LangTypeStandardEnum.CN.code())){
                                    String activityLabel = language.getActivityLabel();
                                    calcShoppingCartResult.setActivityLabel(activityLabel);
                                    calcShoppingCartResult.setActivityName(language.getActivityName());
                                    if (StringUtil.isEmpty(language.getActivityName())){
                                        calcShoppingCartResult.setActivityName(cacheFlashSaleModel.getActivityName());
                                    }
                                }
                            }
                            List<MarketingLanguageResult> marketingLanguageResults = makeLanguageResult(languages);
                            calcShoppingCartResult.setLanguageResults(marketingLanguageResults);

                            calcShoppingCartResult.setActivityType(shoppingCart.getActivityType());
                            calcShoppingCartResult.setActivityUrl(cacheFlashSaleModel.getActivityUrl());
                            calcShoppingCartResult.setUserLimitation(String.valueOf(productModel.getMaxPerUser()));

                            if (SelectFlagEnum.SELECT_FLAG_YES.code().equals(item.getSelectionFlag())) {
                                effectiveFlag = true;
                                calcShoppingCartResult.setPromoRewardAmount(promoRewardAmount);
                                if (promoRewardAmount.compareTo(BigDecimal.ZERO) <= 0){
                                    calcShoppingCartResult.setPromoRewardAmount(BigDecimal.ZERO);

                                }
                            } else {
                                calcShoppingCartResult.setPromoRewardAmount(BigDecimal.ZERO);
                            }
                            calcShoppingCartResult.setActivityPeriod(BeanCopyUtils.jsonCopyBean(cacheFlashSaleModel.getActivityPeriod(), ActivityPeriod.class));
                            calcShoppingCartResult.setPreSalePayType(cacheFlashSaleModel.getPreSalePayType());
                            calcShoppingCartResult.setShippingTime(cacheFlashSaleModel.getShippingTime());
                            calcShoppingCartResult.setImportNo(cacheFlashSaleModel.getImportNo());
                            shoppingCartItemList = new ArrayList<>();

                        } else {

                            if (SelectFlagEnum.SELECT_FLAG_YES.code().equals(item.getSelectionFlag())) {
                                effectiveFlag = true;
                                calcShoppingCartResult.setPromoRewardAmount(calcShoppingCartResult.getPromoRewardAmount().add(promoRewardAmount));
                            }
                            shoppingCartItemList = calcShoppingCartResult.getShoppingCartItems();
                        }
                        //拼团信息填充
                        if (null != marketingGroupUserResult) {
                            calcShoppingCartResult.setGroupUser(marketingGroupUserResult);
                        }

                        FlashSaleOrderCalaResult.ShoppingCartItem shoppingCartItem1 = new FlashSaleOrderCalaResult.ShoppingCartItem();
                        shoppingCartItem1.setQuantity(item.getQuantity());
                        shoppingCartItem1.setProductPrice(item.getProductPrice());
                        shoppingCartItem1.setSkuCode(item.getSkuCode());
                        shoppingCartItem1.setProductCode(item.getProductCode());
                        shoppingCartItem1.setCombineSkuCode(item.getCombineSkuCode());

                        if (SelectFlagEnum.SELECT_FLAG_YES.code().equals(item.getSelectionFlag())) {
                            shoppingCartItem1.setPromoAmount(productModel.getFlashPrice().multiply(new BigDecimal(item.getQuantity())));
                            shoppingCartItem1.setPromoQuantity(item.getQuantity());

                            //检查是否是抽奖商品
                            if (ActivityTypeEnum.LUCKY_DRAW.code().equals(shoppingCart.getActivityType())) {
                                //检查是否有抽奖奖品
                                Boolean aBoolean = marketingLuckDrawPrizeProductService.checkLuckDrawPrizeProduct(shoppingCart.getDomainCode(), shoppingCart.getTenantCode(), shoppingCart.getUserCode(), shoppingCart.getActivityCode(), productModel.getProductCode());
                                Check.check(!aBoolean, TPromoOrderChecker.NO_PRIZE_PRODUCT);
                                //暂时只支持抽奖商品单个购买
                                shoppingCartItem1.setPromoQuantity(1);
                                shoppingCartItem1.setPromoAmount(BigDecimal.ZERO);

                            }

                        } else {
                            shoppingCartItem1.setPromoQuantity(ZERO);
                            shoppingCartItem1.setPromoAmount(BigDecimal.ZERO);
                        }
                        shoppingCartItem1.setMaxPerUser(currentProductModel.getMaxPerUser());
                        shoppingCartItem1.setSkuInventory(currentProductModel.getSkuInventory());

                        shoppingCartItem1.setFlashPrice(currentProductModel.getFlashPrice());
                        if (!ActivityTypeEnum.LUCKY_DRAW.code().equals(shoppingCart.getActivityType())) {
                            shoppingCartItem1.setMemberInventory(this.buildMemberInventory(activityCode, productModel.getSkuCode(), shoppingCart.getUserCode(), currentProductModel));
                        }
                        shoppingCartItemList.add(shoppingCartItem1);
                        calcShoppingCartResult.setEffectiveFlag(effectiveFlag);
                        calcShoppingCartResult.setShoppingCartItems(shoppingCartItemList);
                    }
                }
            }
        }
        addCalcShoppingCartResult(list, calcShoppingCartResult);

        //查询当前活动的分组
        //查询分组之间的关系,拿到所有互斥的活动
        //查询当前所有活动,剔除互斥的活动
        //交给促销购物车计算
        promotionActivityCalculation(list, shoppingCart,flashSaleCacheMap.get(shoppingCart.getActivityCode()));

        return list;
    }

    //拼团信息，用于计算以及保存
    public void setMarketingGroupUserInformation(FlashSaleShoppingCartDto shoppingCart, MarketingGroupMode marketingGroup,
                                                 FlashSaleProductModel productModel, FlashSaleProductModel currentProductModel,
                                                 MarketingGroupUserResult marketingGroupUserResult, String cartMarketingGroupCode,boolean leaderFlag,String activityEnd) {
        if (leaderFlag) {
            //团长
            Integer effectiveHour = marketingGroup.getEffectiveHour();
            Date date = DateUtils.addHours(new Date(), effectiveHour);
            String effectiveTime = DateUtil.format(date,DateUtil.FORMAT_YYYYMMDDHHMISS_14);
            if (effectiveTime.compareTo(activityEnd) > 0){
                effectiveTime = activityEnd;
            }
            marketingGroupUserResult.setEffectiveTime(effectiveTime);


            marketingGroupUserResult.setMarketingGroupCode(cartMarketingGroupCode);
            marketingGroupUserResult.setTeamLeader(TeamLeaderEnum.LEADER.code());
            marketingGroupUserResult.setLeaderBenefits(marketingGroup.getLeaderBenefits());
            //计算拼团团长价格 必须符合团长计算条件
            if (marketingGroup != null && LeaderPriceEnum.LEADER_PRICE.code().equals(String.valueOf(marketingGroup.getShowLeaderPrice()))) {
                productModel.setFlashPrice(currentProductModel.getGroupLeaderPrice());
                currentProductModel.setFlashPrice(currentProductModel.getGroupLeaderPrice());
            }

        }else {
            //团员
            marketingGroupUserResult.setTeamLeader(TeamLeaderEnum.MEMBER.code());
            MarketingGroupUserMode leader = marketingGroupUserService.findGroupLeaderUserByMarketingGroupCode(shoppingCart.getTenantCode(), cartMarketingGroupCode);
            if (null != leader){
                marketingGroupUserResult.setEffectiveTime(leader.getEffectiveTime());
                marketingGroupUserResult.setLeaderUserCode(leader.getUserCode());
            }else {
                Check.check(true, TPromoOrderChecker.NO_LEADER_MARKETING_GROUP_CODE);
            }
        }
    }


    public void promotionActivityCalculation(List<FlashSaleOrderCalaResult> list, FlashSaleShoppingCartDto flashShoppingCart, CacheFlashSaleModel cacheFlashSaleModel) {

        List<CalcShoppingCartResult> resultList = processShoppingCart(flashShoppingCart,cacheFlashSaleModel,list);

        if (CollectionUtils.isEmpty(resultList)) {
            return;
        }
        updateFlashSaleOrderResults(list, resultList);

    }


    public List<CalcShoppingCartResult> processShoppingCart(FlashSaleShoppingCartDto flashShoppingCart, CacheFlashSaleModel cacheFlashSaleModel, List<FlashSaleOrderCalaResult> list) {
        if (StringUtil.isEmpty(cacheFlashSaleModel.getGroupCode())){
            return Collections.emptyList();
        }



        CalcShoppingCartParam param = createCalcShoppingCartParam(flashShoppingCart,list);
        param.validate();
        log.info("购物车入参：{}", JSONObject.toJSONString(param));
        ShoppingCartDTO shoppingCart = BeanCopyUtils.jsonCopyBean(param, ShoppingCartDTO.class);
        shoppingCart.setUserCode(param.getMemberCode());

        Map<String, ActivityCacheDTO> ruleCacheMap = fetchRuleCacheMap(shoppingCart, param);
        if (shouldReturnEarly(ruleCacheMap, shoppingCart)) {
            return Collections.emptyList();
        }

        List<ActivityGroupCache> groupCacheList = promoGroupRelationDomain.getTenantGroupCache(flashShoppingCart.getTenantCode());
        List<ActivityGroupCache> flashMutuallyExclusiveGroup = groupCacheList.stream().filter(x -> x.getGroupCode().equals(cacheFlashSaleModel.getGroupCode())).collect(Collectors.toList());

        List<String> relationList = flashMutuallyExclusiveGroup.stream().filter(x->CollectionUtils.isNotEmpty(x.getRelationList())).flatMap(x -> x.getRelationList().stream()).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(relationList)){
            return processCartActivities(shoppingCart, ruleCacheMap);
        }

        if (CollectionUtils.isNotEmpty(flashMutuallyExclusiveGroup)) {
            //过滤掉ruleCacheMap的Value中存在在flashMutuallyExclusiveGroup的活动
            ruleCacheMap = ruleCacheMap.entrySet().stream().filter(x -> {
                ActivityCacheDTO activityCacheDTO = x.getValue();
                String groupCode = activityCacheDTO.getActivityModel().getGroupCode();
                return !relationList.contains(groupCode);
            }).collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
        }

        return processCartActivities(shoppingCart, ruleCacheMap);
    }

    public CalcShoppingCartParam createCalcShoppingCartParam(FlashSaleShoppingCartDto flashShoppingCart, List<FlashSaleOrderCalaResult> list) {
        CalcShoppingCartParam param = new CalcShoppingCartParam();
        param.setDomainCode(flashShoppingCart.getDomainCode());
        param.setTenantCode(flashShoppingCart.getTenantCode());
        param.setLanguage(flashShoppingCart.getLanguage());
        param.setChannelCode(flashShoppingCart.getChannelCode());
        param.setMemberCode(flashShoppingCart.getUserCode());
        param.setQualifications(flashShoppingCart.getQualifications());
        param.setCouponCodes(flashShoppingCart.getCouponCodes());
        param.setPromotionTime(flashShoppingCart.getPromotionTime());
        param.setCartStoreList(flashShoppingCart.getCartStoreList());
        param.setPostage(flashShoppingCart.getPostage());
        param.setCouponCodes(flashShoppingCart.getCouponCodes());

        list.forEach(flashSaleOrderCalaResult->{
            flashSaleOrderCalaResult.getShoppingCartItems().forEach(ShoppingCartItem->{

                //如果是秒杀活动，需要将购物车中的商品价格改为秒杀价格
                if (flashSaleOrderCalaResult.getActivityType().equals(ActivityTypeEnum.FLASH_SALE.code())
                || flashSaleOrderCalaResult.getActivityType().equals(ActivityTypeEnum.GROUP.code())
                || flashSaleOrderCalaResult.getActivityType().equals(ActivityTypeEnum.PRE_SALE.code())

                ){
                    param.getCartStoreList().stream().forEach(cartStore->{
                        cartStore.getCartItemList().stream().forEach(cartItem->{
                            if (cartItem.getSkuCode().equals(ShoppingCartItem.getSkuCode())){
                                cartItem.setProductPrice(ShoppingCartItem.getFlashPrice());
                            }
                        });
                    });
                }
                //其他营销暂不处理

            });
        });



        return param;
    }

    public Map<String, ActivityCacheDTO> fetchRuleCacheMap(ShoppingCartDTO shoppingCart, CalcShoppingCartParam param) {
        Map<String, ActivityCacheDTO> cacheMap = activityCacheDomain.getActivityCacheMap(shoppingCart.getTenantCode(), param.getLanguage(), 2, null);
        return activityCacheDomain.filterActivityByCustomCondition(shoppingCart.getTenantCode(), cacheMap, param.getCustomConditionMap());
    }

    public boolean shouldReturnEarly(Map<String, ActivityCacheDTO> ruleCacheMap, ShoppingCartDTO shoppingCart) {
        if (MapUtils.isEmpty(ruleCacheMap)) {
            if (StringUtil.isNotBlank(shoppingCart.getCouponCodes())) {
                Check.check(true, CouponErrorChecker.NO_EFFECTIVE);
            }
            return true;
        }
        return false;
    }

    public List<CalcShoppingCartResult> processCartActivities(ShoppingCartDTO shoppingCart, Map<String, ActivityCacheDTO> ruleCacheMap) {
        shoppingCart = shoppingCartDomain.queryActivity(shoppingCart, ruleCacheMap,false);


        List<ShoppingCartOutDTO> scoDtoList = calcExecuter.calc(new CalcShoppingCart(shoppingCart), ruleCacheMap);
        if (CollectionUtils.isEmpty(scoDtoList)) {
            log.info("scoDtoList is null");
            Result.ok();
            return Collections.emptyList();
        }

        return convertToCalcShoppingCartResultList(scoDtoList);
    }

    public List<CalcShoppingCartResult> convertToCalcShoppingCartResultList(List<ShoppingCartOutDTO> scoDtoList) {
        List<CalcShoppingCartResult> resultList = new ArrayList<>();
        for(ShoppingCartOutDTO sco : scoDtoList) {
            CalcShoppingCartResult scResult = BeanCopyUtils.jsonCopyBean(sco, CalcShoppingCartResult.class);
            if (sco.getFailedReason() != null) {
                scResult.setFalseReason(sco.getFailedReason().getMessage());
                scResult.setFalseCode(sco.getFailedReason().getCode());
            }
            resultList.add(scResult);
        }
        return resultList;
    }



    public void updateFlashSaleOrderResults(List<FlashSaleOrderCalaResult> list, List<CalcShoppingCartResult> resultList) {
        for (FlashSaleOrderCalaResult flashSaleOrderCalaResult : list) {
            BigDecimal promoRewardAmount = BigDecimal.ZERO;
            List<FlashSaleOrderCalaResult.ShoppingCartItem> shoppingCartItems = flashSaleOrderCalaResult.getShoppingCartItems();

            for (FlashSaleOrderCalaResult.ShoppingCartItem shoppingCartItem : shoppingCartItems) {
                promoRewardAmount = updateShoppingCartItem(shoppingCartItem, resultList, promoRewardAmount);
            }

            flashSaleOrderCalaResult.setShoppingCartItems(shoppingCartItems);
            if (flashSaleOrderCalaResult.getPromoRewardAmount() != null){
                flashSaleOrderCalaResult.setPromoRewardAmount(promoRewardAmount.add(flashSaleOrderCalaResult.getPromoRewardAmount()));
            }else {
                flashSaleOrderCalaResult.setPromoRewardAmount(promoRewardAmount);
            }
        }
    }

    public BigDecimal updateShoppingCartItem(FlashSaleOrderCalaResult.ShoppingCartItem shoppingCartItem, List<CalcShoppingCartResult> resultList, BigDecimal currentPromoRewardAmount) {
        HashMap<String, List<CalcShoppingCartResult.ShoppingCartItemActivity>> skuActivitys = new HashMap<>();

        for (CalcShoppingCartResult calcShoppingCartResult : resultList) {
            for (CalcShoppingCartResult.ShoppingCartItem promoShoppingCartItem : calcShoppingCartResult.getShoppingCartItems()) {
                skuActivitys.put(promoShoppingCartItem.getSkuCode(), promoShoppingCartItem.getShoppingCartItemActivitys());

                BigDecimal rewardAmount = promoShoppingCartItem.getProductPrice().subtract(promoShoppingCartItem.getPromoAmount());
                currentPromoRewardAmount = currentPromoRewardAmount.add(rewardAmount);

                shoppingCartItem.setPromoAmount(shoppingCartItem.getPromoAmount().subtract(rewardAmount));
            }
        }
        shoppingCartItem.setPromoShoppingCartItemActivitys(skuActivitys.get(shoppingCartItem.getSkuCode()));

        return currentPromoRewardAmount;
    }



    public Map<String, CacheFlashSaleModel> getStringCacheFlashSaleModelMap(Map<String, CacheFlashSaleModel> flashSaleCacheMap, String activityCode) {
        Check.check(!flashSaleCacheMap.containsKey(activityCode), FlashSaleChecker.SKU_NOT_MATCH_ACTIVITY);
        Map<String, CacheFlashSaleModel> tempMap = new HashedMap<>();
        tempMap.put(activityCode, flashSaleCacheMap.get(activityCode));
        flashSaleCacheMap = tempMap;
        return flashSaleCacheMap;
    }
    private void addCalcShoppingCartResult(List<FlashSaleOrderCalaResult> list, FlashSaleOrderCalaResult calcShoppingCartResult) {
        if (calcShoppingCartResult != null) {
            list.add(calcShoppingCartResult);
        }
    }

    private void addSkuCodesByStore(FlashSaleShoppingCartDto shoppingCart, List<String> skuCodes) {
        for (ShoppingCartStore shoppingCartStore : shoppingCart.getCartStoreList()) {
            for (ShoppingCartItem shoppingCartItem : shoppingCartStore.getCartItemList()) {
                skuCodes.add(shoppingCartItem.getSkuCode());
            }
        }
    }

    private void addSpuCodesByStore(FlashSaleShoppingCartDto shoppingCart, List<String> spuCodes) {
        for (ShoppingCartStore shoppingCartStore : shoppingCart.getCartStoreList()) {
            for (ShoppingCartItem shoppingCartItem : shoppingCartStore.getCartItemList()) {
                spuCodes.add(shoppingCartItem.getProductCode());
            }
        }
    }


    @Transactional
    public String createFlashSale(FlashSaleDomain flashSaleDomain) {
        MarketingModel marketingModel = BeanCopyUtils.jsonCopyBean(flashSaleDomain, MarketingModel.class);
        String activityCode = gTechCodeGenerator.generateCode(MarketingConstants.APP_KEY, flashSaleDomain.getActivityType(), flashSaleDomain.getActivityType()+FlashSaleConstants.FLASH_CODE_TEMPLATE_EXP, 106L);
        marketingModel.setActivityCode(activityCode);
        marketingModel.setCreateUser(flashSaleDomain.getOperateUser());

        String tenantCode = flashSaleDomain.getTenantCode();
        String opsType = marketingModel.getOpsType();
        boolean groupFlag = false;

        //这里检查是否有分组记录,如果没有分组则新建分组,并且默认与所有促销和营销互斥
        if (StringUtil.isNotBlank(opsType)){
            String groupCode = promoGroupDomain.queryGroupCode(flashSaleDomain.getDomainCode(), tenantCode, opsType);
            marketingModel.setGroupCode(groupCode);
        }

        /*if(!ActivityTypeEnum.BOOST_SHARDING.code().equals(flashSaleDomain.getActivityType())){
            //是否开启分组
            groupFlag = promoGroupDomain.checkGroupEnabledByTenantCode(tenantCode, opsType);
        }


        if (groupFlag){
            //opsType 作为分组编码 直接关联活动
            marketingModel.setGroupCode(opsType);
        }*/


        int insert = marketingService.insert(marketingModel);
        if (insert == 1){
            createOtherInfo(flashSaleDomain, activityCode);
        }


        operationLogService.insertLog(OperationLogModel.builder()
                .operationType(OperationTypeEnum.CREATION.code())
                .createUser(flashSaleDomain.getOperateUser()).tenantCode(tenantCode)
                .createLastName(flashSaleDomain.getOperateLastName()).createFirstName(flashSaleDomain.getOperateFirstName())
                .activityCode(activityCode).build(), JSONObject.toJSONString(flashSaleDomain));
        return activityCode;
    }

    private void createOtherInfo(FlashSaleDomain flashSaleDomain, String activityCode) {
        List<FlashSaleStore> flashSaleStores = flashSaleDomain.getFlashSaleStores();

        ActivityPeriod activityPeriod = flashSaleDomain.getActivityPeriod();
        if (null != activityPeriod){
            ActivityPeriodModel activityPeriodModel = BeanCopyUtils.jsonCopyBean(activityPeriod, ActivityPeriodModel.class);
            activityPeriodModel.setDomainCode(flashSaleDomain.getDomainCode());
            activityPeriodModel.setTenantCode(flashSaleDomain.getTenantCode());
            activityPeriodModel.setActivityCode(activityCode);
            activityPeriodService.createPeriod(activityPeriodModel);
        }

        insertFlashSaleStore(flashSaleDomain, activityCode, flashSaleStores);

        List<FlashSaleQualification> flashSaleQualifications = flashSaleDomain.getFlashSaleQualifications();
        insertQualification(flashSaleDomain, activityCode, flashSaleQualifications);

        List<MarketingLanguage> marketingLanguages = flashSaleDomain.getMarketingLanguages();
        insertLanguage(flashSaleDomain, activityCode, marketingLanguages);
        if (StringUtil.isNotBlank(flashSaleDomain.getImportNo())){
            flashSaleProductService.updateActivityCodeAndOrgCode(flashSaleDomain.getImportNo(), flashSaleDomain.getOrgCode(), activityCode);
        }else {
            List<FlashSaleProductModel> model = BeanCopyUtils.jsonCopyList(flashSaleDomain.getProducts(), FlashSaleProductModel.class);
            for (FlashSaleProductModel flashSaleProductModel : model) {

                flashSaleProductModel.setActivityCode(activityCode);
                flashSaleProductModel.setDomainCode(flashSaleDomain.getDomainCode());
                flashSaleProductModel.setTenantCode(flashSaleDomain.getTenantCode());
                flashSaleProductModel.setOrgCode(flashSaleDomain.getOrgCode());
                if (SwitchEnum.YES.code().equals(String.valueOf(flashSaleProductModel.getLimitFlag()))){
                    flashSaleProductModel.setSkuQuota(Integer.valueOf(SwitchEnum.YES.code()));
                    flashSaleProductModel.setSkuInventory(SKU_INVENTORY);

                }else {
                    flashSaleProductModel.setSkuInventory(flashSaleProductModel.getSkuQuota());
                }

                Integer maxPerUserFlag = flashSaleProductModel.getMaxPerUserFlag();
                if (SwitchEnum.YES.code().equals(String.valueOf(maxPerUserFlag))){
                    flashSaleProductModel.setMaxPerUser(SKU_INVENTORY);
                }else {
                    flashSaleProductModel.setMaxPerUser(flashSaleProductModel.getMaxPerUser());
                }

                flashSaleProductService.insert(flashSaleProductModel);
            }


        }

        createMarketingGroup(flashSaleDomain, activityCode);
        createBoostSharing(flashSaleDomain, activityCode);

        if (IncentiveLimitedFlagEnum.YES.code().equals(flashSaleDomain.getIncentiveLimitedFlag())) {
            List<IncentiveLimited> incentiveLimitedList = flashSaleDomain.getIncentiveLimiteds();
            List<TPromoIncentiveLimitedVO> list = BeanCopyUtils.jsonCopyList(incentiveLimitedList, TPromoIncentiveLimitedVO.class);
            limitedService.insertLimitedList111(activityCode, list, flashSaleDomain.getTenantCode());

        }
    }

    private void insertLanguage(FlashSaleDomain flashSaleDomain, String activityCode, List<MarketingLanguage> marketingLanguages) {
        for (MarketingLanguage marketingLanguage : marketingLanguages) {
            MarketingLanguageModel model = BeanCopyUtils.jsonCopyBean(marketingLanguage, MarketingLanguageModel.class);
            model.setActivityCode(activityCode);
            model.setDomainCode(flashSaleDomain.getDomainCode());
            model.setTenantCode(flashSaleDomain.getTenantCode());
            model.setOrgCode(flashSaleDomain.getOrgCode());
            languageService.insert(model);
        }
    }

    private void insertQualification(FlashSaleDomain flashSaleDomain, String activityCode, List<FlashSaleQualification> flashSaleQualifications) {
        if (CollectionUtils.isNotEmpty(flashSaleQualifications)){
            for (FlashSaleQualification flashSaleQualification : flashSaleQualifications) {
                for (int i = 0; i < flashSaleQualification.getQualificationValue().size(); i++) {
                    FlashSaleQualificationModel qualificationModel = new FlashSaleQualificationModel();
                    qualificationModel.setActivityCode(activityCode);
                    qualificationModel.setDomainCode(flashSaleDomain.getDomainCode());
                    qualificationModel.setTenantCode(flashSaleDomain.getTenantCode());
                    qualificationModel.setOrgCode(flashSaleDomain.getOrgCode());
                    qualificationModel.setActivityCode(activityCode);
                    qualificationModel.setQualificationCode(flashSaleQualification.getQualificationCode());
                    qualificationModel.setQualificationValue(flashSaleQualification.getQualificationValue().get(i));
                    qualificationModel.setIsExclude(flashSaleQualification.getIsExclude());
                    if (CollectionUtils.isNotEmpty(flashSaleQualification.getQualificationValueName()) && flashSaleQualification.getQualificationValueName().size() > i) {
                        qualificationModel.setQualificationValueName(flashSaleQualification.getQualificationValueName().get(i));
                    }
                    flashSaleQualificationService.insert(qualificationModel);
                }
            }
        }
    }

    private void insertFlashSaleStore(FlashSaleDomain flashSaleDomain, String activityCode, List<FlashSaleStore> flashSaleStores) {
        if (CollectionUtils.isNotEmpty(flashSaleStores)){
            List<FlashSaleStoreModel> model = BeanCopyUtils.jsonCopyList(flashSaleStores, FlashSaleStoreModel.class);
            for (FlashSaleStoreModel flashSaleStoreModel : model) {
                flashSaleStoreModel.setDomainCode(flashSaleDomain.getDomainCode());
                flashSaleStoreModel.setTenantCode(flashSaleDomain.getTenantCode());
                flashSaleStoreModel.setActivityCode(activityCode);
                flashSaleStoreService.insert(flashSaleStoreModel);
            }
        }
    }

    @Transactional
    public int updateFlashSale(FlashSaleDomain flashSaleDomain) {
        String activityCode = flashSaleDomain.getActivityCode();
        MarketingModel model = marketingService.findByActivityCode(activityCode);
        Check.check(null == model, FlashSaleChecker.ACTIVITY_NOT_EXIST);
        languageService.deleteByActivityCode(activityCode);
        flashSaleQualificationService.deleteByActivityCode(activityCode);
        flashSaleStoreService.deleteByActivityCode(activityCode);
        marketingGroupService.deleteByActivityCode(activityCode);
        boostSharingService.deleteByActivityCode(activityCode);
        limitedService.deleteLimitedByActivityCode(activityCode);
        boolean notNeedUpdate = ActivityStatusEnum.EFFECTIVE.equalsCode(model.getActivityStatus()) && !model.isNeedToDoExpire();
        if (notNeedUpdate){
            flashSaleDomain.setProducts(new ArrayList<>());
            flashSaleDomain.setActivityStatus(ActivityStatusEnum.EFFECTIVE.code());
            flashSaleDomain.ofInfoNull();
            flashSaleDomain.setOrgCode(model.getOrgCode());
        }else {
            activityPeriodService.deletePeriod(flashSaleDomain.getTenantCode(), activityCode);
            flashSaleProductService.deleteByActivityCode(activityCode);
        }
        MarketingModel marketingModel = BeanCopyUtils.jsonCopyBean(flashSaleDomain, MarketingModel.class);
        marketingModel.setUpdateUser(flashSaleDomain.getOperateUser());
        int update = marketingService.updateByActivityCode(marketingModel);

        if (update == 1){
            createOtherInfo(flashSaleDomain, flashSaleDomain.getActivityCode());

        }
        operationLogService.insertLog(OperationLogModel.builder()
                .operationType(OperationTypeEnum.EDITION.code())
                .createUser(flashSaleDomain.getOperateUser()).tenantCode(flashSaleDomain.getTenantCode())
                .createLastName(flashSaleDomain.getOperateLastName()).createFirstName(flashSaleDomain.getOperateFirstName())
                .activityCode(activityCode).build(), JSONObject.toJSONString(flashSaleDomain));
        if (notNeedUpdate) {
            marketingCacheComponent.updateCacheByStatus(model, ActivityStatusEnum.EFFECTIVE.code());
        }
        return update;
    }

    public void createMarketingGroup(FlashSaleDomain flashSaleDomain, String activityCode) {
        if (null != flashSaleDomain.getMarketingGroup()) {
            CheckUtils.isTrue(flashSaleDomain.getActivityType().equals(ActivityTypeEnum.GROUP.code()), ErrorCodes.ACTIVITY_TYPE_GROUP);
            MarketingGroupMode marketingGroupMode = BeanCopyUtils.jsonCopyBean(flashSaleDomain.getMarketingGroup(), MarketingGroupMode.class);
            marketingGroupMode.setDomainCode(flashSaleDomain.getDomainCode());
            marketingGroupMode.setTenantCode(flashSaleDomain.getTenantCode());
            marketingGroupMode.setActivityCode(activityCode);
            marketingGroupMode.setOrgCode(flashSaleDomain.getOrgCode());;
            marketingGroupMode.setCreateUser(flashSaleDomain.getOperateUser());
            marketingGroupMode.setUpdateUser(flashSaleDomain.getOperateUser());
            marketingGroupService.insert(marketingGroupMode);
        }
    }

    public void createBoostSharing(FlashSaleDomain flashSaleDomain, String activityCode) {
        if (null == flashSaleDomain.getBoostSharing()){
            return;
        }
        BoostSharingModel boostSharingModel = BeanCopyUtils.jsonCopyBean(flashSaleDomain.getBoostSharing(), BoostSharingModel.class);
        boostSharingModel.setDomainCode(flashSaleDomain.getDomainCode());
        boostSharingModel.setTenantCode(flashSaleDomain.getTenantCode());
        boostSharingModel.setOrgCode(flashSaleDomain.getOrgCode());
        boostSharingModel.setActivityCode(activityCode);
        boostSharingModel.setCreateUser(flashSaleDomain.getOperateUser());
        boostSharingModel.setUpdateUser(flashSaleDomain.getOperateUser());
        boostSharingService.insert(boostSharingModel);
    }


    public FlashSaleFindResult findFlashSale(String activityCode) {
        MarketingModel marketingModel = marketingService.findByActivityCode(activityCode);
        Check.check(null == marketingModel, FlashSaleChecker.ACTIVITY_NOT_EXIST);
        List<MarketingLanguageModel> languageModels = languageService.findListByActivityCode(activityCode);
        List<FlashSaleQualificationModel> qualificationModels = flashSaleQualificationService.findListByActivityCode(activityCode);
        List<FlashSaleStoreModel> storeModels = flashSaleStoreService.findListByActivityCode(activityCode);
        List<FlashSaleProductModel> productModels = flashSaleProductService.findListByActivityCode(activityCode);

        MarketingGroupMode marketingGroupMode = marketingGroupService.findByActivityCode(activityCode);
        BoostSharingModel boostSharingModel = boostSharingService.findByActivityCode(activityCode);

        FlashSaleFindResult result = BeanCopyUtils.jsonCopyBean(marketingModel, FlashSaleFindResult.class);
        result.setMarketingLanguages(BeanCopyUtils.jsonCopyList(languageModels, MarketingLanguage.class));
        result.setFlashSaleQualifications(BeanCopyUtils.jsonCopyList(QualificationModel.convert(BeanCopyUtils.jsonCopyList(qualificationModels, QualificationModel.class)), FlashSaleQualification.class));
        result.setFlashSaleStores(BeanCopyUtils.jsonCopyList(storeModels, FlashSaleStore.class));

        if (IncentiveLimitedFlagEnum.YES.code().equals(marketingModel.getIncentiveLimitedFlag())){
            List<TPromoIncentiveLimitedVO> list = limitedService.getLimitedListByActivityCode(activityCode);
            result.setIncentiveLimiteds(BeanCopyUtils.jsonCopyList(list, IncentiveLimited.class));
        }

        List<FlashSaleProductModel> collect = new ArrayList<>();

        if (CollectionUtils.isNotEmpty(productModels)){
            String productCode = productModels.get(0).getProductCode();
            if (StringUtil.isNotEmpty(productCode)){
                List<FlashSaleProductModel> collect1 = productModels.stream().sorted(Comparator.comparing(FlashSaleProductModel::getProductCode)).collect(Collectors.toList());
                collect.addAll(collect1);
            }else {
                collect.addAll(productModels);
            }
        }
        //数量不限时用于回显
        productModels.stream().forEach(x -> {
                    Integer limitFlag = x.getLimitFlag();
                    if (null != limitFlag && limitFlag.equals(Integer.parseInt(SwitchEnum.YES.code()))) {
                        x.setSkuQuota(null);
                    }
                    Integer maxPerUserFlag = x.getMaxPerUserFlag();
                    if (maxPerUserFlag != null && maxPerUserFlag.equals(Integer.parseInt(SwitchEnum.YES.code()))) {
                        x.setMaxPerUser(null);
                    }
                }
        );
        result.setProducts(BeanCopyUtils.jsonCopyList(collect, FlashSaleProduct.class));
        result.setMarketingGroup(BeanCopyUtils.jsonCopyBean(marketingGroupMode, MarketingGroupResult.class));
        result.setBoostSharing(BeanCopyUtils.jsonCopyBean(boostSharingModel, BoostSharingResult.class));
        result.setActivityPeriod(BeanCopyUtils.jsonCopyBean(activityPeriodService.findPeriod(marketingModel.getTenantCode(), activityCode), ActivityPeriod.class));
        result.setAuditUser(marketingModel.getAuditUser());
        String auditConfig = activityComponentDomain.getActivityAuditConfig(marketingModel.getTenantCode());
        setFlashSaleAuditConfig(result, auditConfig);
        return result;
    }

    public void setFlashSaleAuditConfig(FlashSaleFindResult flashSaleFindResult, String auditConfig){
        flashSaleFindResult.setNeedAudit(activityComponentDomain.isNeedAudit(auditConfig));
        flashSaleFindResult.setNeedDifferentOperator(activityComponentDomain.isAuditDifferentOperator(auditConfig));
    }

    public FlashSaleSkuActivityPriceDTO activitySkuPrice(SingleProductDTO product, Map<String, CacheFlashSaleModel> activityCacheMap) {

        //单品对象(出参)
        FlashSaleSkuActivityPriceDTO skuActivity = new FlashSaleSkuActivityPriceDTO();
        skuActivity.setSkuCode(product.getSkuCode());
        skuActivity.setProductCode(product.getProductCode());
        skuActivity.setCombineSkuCode(product.getCombineSkuCode());

        if (MapUtils.isEmpty(activityCacheMap)) {
            return skuActivity;
        }

        ArrayList<String> list = new ArrayList<>(activityCacheMap.keySet());
        Collections.sort(list);
        String activityCode = list.get(list.size() - 1);
        CacheFlashSaleModel cacheFlashSaleModel = activityCacheMap.get(activityCode);
        //单品内单个活动对象
        FlashSaleActivityPriceDTO activityPriceDTO = new FlashSaleActivityPriceDTO();
        activityPriceDTO.setActivityCode(cacheFlashSaleModel.getActivityCode());
        activityPriceDTO.setActivityLabel(cacheFlashSaleModel.getActivityLabel());
        activityPriceDTO.setActivityName(cacheFlashSaleModel.getActivityName());
        activityPriceDTO.setActivityStartTime(cacheFlashSaleModel.getActivityBegin());
        activityPriceDTO.setActivityEndTime(cacheFlashSaleModel.getActivityEnd());
        activityPriceDTO.setCoolDown(cacheFlashSaleModel.getCoolDown());
        activityPriceDTO.setWarmBegin(cacheFlashSaleModel.getWarmBegin());
        activityPriceDTO.setOpsType(cacheFlashSaleModel.getOpsType());
        activityPriceDTO.setRibbonPosition(cacheFlashSaleModel.getRibbonPosition());
        activityPriceDTO.setRibbonImage(cacheFlashSaleModel.getRibbonImage());
        activityPriceDTO.setRibbonText(cacheFlashSaleModel.getRibbonText());
        activityPriceDTO.setActivityPeriod(BeanCopyUtils.jsonCopyBean(cacheFlashSaleModel.getActivityPeriod(), ActivityPeriod.class));
        activityPriceDTO.setSponsors(cacheFlashSaleModel.getSponsors());
        activityPriceDTO.setQualifications(QualificationModel.convert(BeanCopyUtils.jsonCopyList(cacheFlashSaleModel.getQualifications(), QualificationModel.class)));
        activityPriceDTO.setBackgroundImage(cacheFlashSaleModel.getBackgroundImage());
        List<FlashSaleProductModel> products = cacheFlashSaleModel.getProducts();
        for (FlashSaleProductModel flashSaleProductModel : products) {
            if (flashSaleProductModel.getSkuCode().equals(product.getSkuCode())){
                FlashSaleProductModel byActivityAndSku = flashSaleProductService.findByActivityAndSku(cacheFlashSaleModel.getActivityCode(), product.getSkuCode());
                activityPriceDTO.setPromotionPrice(byActivityAndSku.getFlashPrice());
                activityPriceDTO.setInventory(byActivityAndSku.getSkuInventory());
                activityPriceDTO.setMaxPerUser(byActivityAndSku.getMaxPerUser());
                activityPriceDTO.setSkuCode(product.getSkuCode());
                break;
            }
        }
        ArrayList<FlashSaleActivityPriceDTO> arrayList = new ArrayList<>();
        FlashSaleActivityPriceDTO activityPriceDTO1 = new FlashSaleActivityPriceDTO();
        BeanCopyUtils.copyProperties(activityPriceDTO, activityPriceDTO1);//冗余一个，不复制的化 出参显示会有问题
        arrayList.add(activityPriceDTO1);
        skuActivity.setActivity(arrayList);

        return skuActivity;
    }



    public List<FlashSaleSkuActivityPriceDTO> activitySkuPriceList(SingleProductDTO product, Map<String, CacheFlashSaleModel> activityCacheMap) {
        List<FlashSaleSkuActivityPriceDTO> activityPriceDTOList = new ArrayList<>();
        if (MapUtils.isEmpty(activityCacheMap)) {
            return activityPriceDTOList;
        }
        if(CollectionUtils.isEmpty(product.getSkuList())){
            return activityPriceDTOList;
        }

        List<SkuParam> skuParamList = product.getSkuList();
        List<String> skuCodeList = skuParamList.stream().map(SkuParam::getSkuCode).collect(Collectors.toList());


        ArrayList<String> list = new ArrayList<>(activityCacheMap.keySet());
        Collections.sort(list);
        String activityCode = list.get(list.size() - 1);
        CacheFlashSaleModel cacheFlashSaleModel = activityCacheMap.get(activityCode);
        List<FlashSaleProductModel> flashSaleProductModelList = flashSaleProductService.findByActivityAndSkuList(cacheFlashSaleModel.getActivityCode(), skuCodeList);
        Map<String,FlashSaleProductModel> flashSaleProductModelMap = flashSaleProductModelList.stream().collect(Collectors.toMap(FlashSaleProductModel::getSkuCode, m->m,(key1,key2)->key2));
        for(FlashSaleProductModel skuParam : flashSaleProductModelList){
            //单品对象(出参)
            FlashSaleSkuActivityPriceDTO skuActivity = new FlashSaleSkuActivityPriceDTO();
            skuActivity.setSkuCode(skuParam.getSkuCode());
            skuActivity.setProductCode(product.getProductCode());

            //单品内单个活动对象
            FlashSaleActivityPriceDTO activityPriceDTO = new FlashSaleActivityPriceDTO();
            activityPriceDTO.setActivityName(cacheFlashSaleModel.getActivityName());
            activityPriceDTO.setActivityCode(cacheFlashSaleModel.getActivityCode());
            activityPriceDTO.setActivityEndTime(cacheFlashSaleModel.getActivityEnd());
            activityPriceDTO.setActivityLabel(cacheFlashSaleModel.getActivityLabel());
            activityPriceDTO.setWarmBegin(cacheFlashSaleModel.getWarmBegin());
            activityPriceDTO.setCoolDown(cacheFlashSaleModel.getCoolDown());
            activityPriceDTO.setOpsType(cacheFlashSaleModel.getOpsType());
            activityPriceDTO.setRibbonImage(cacheFlashSaleModel.getRibbonImage());
            activityPriceDTO.setRibbonPosition(cacheFlashSaleModel.getRibbonPosition());
            activityPriceDTO.setRibbonText(cacheFlashSaleModel.getRibbonText());
            activityPriceDTO.setActivityStartTime(cacheFlashSaleModel.getActivityBegin());
            activityPriceDTO.setSponsors(cacheFlashSaleModel.getSponsors());
            activityPriceDTO.setActivityPeriod(BeanCopyUtils.jsonCopyBean(cacheFlashSaleModel.getActivityPeriod(), ActivityPeriod.class));
            activityPriceDTO.setBackgroundImage(cacheFlashSaleModel.getBackgroundImage());
            activityPriceDTO.setQualifications(QualificationModel.convert(BeanCopyUtils.jsonCopyList(cacheFlashSaleModel.getQualifications(), QualificationModel.class)));
            activityPriceDTO.setSkuCode(skuParam.getSkuCode());
            FlashSaleProductModel currentModel = flashSaleProductModelMap.get(skuParam.getSkuCode());
            if(null != currentModel){
                activityPriceDTO.setPromotionPrice(currentModel.getFlashPrice());
                activityPriceDTO.setInventory(currentModel.getSkuInventory());
                activityPriceDTO.setMaxPerUser(currentModel.getMaxPerUser());
            }

            ArrayList<FlashSaleActivityPriceDTO> arrayList = new ArrayList<>();
            FlashSaleActivityPriceDTO activityPriceDTO1 = new FlashSaleActivityPriceDTO();
            BeanCopyUtils.copyProperties(activityPriceDTO, activityPriceDTO1);//冗余一个，不复制的化 出参显示会有问题
            arrayList.add(activityPriceDTO1);
            skuActivity.setActivity(arrayList);
            activityPriceDTOList.add(skuActivity);
        }

        return activityPriceDTOList;
    }

    public List<FlashSaleActivityInfoDTO> getActivitiesByProduct(Map<String, CacheFlashSaleModel> activityCacheMap, ProductCodes product) {

        //返回对象集合
        List<FlashSaleActivityInfoDTO> activities = new ArrayList<>();
        if (MapUtils.isEmpty(activityCacheMap)){
            return activities;
        }
        List<String> activityCodes = new ArrayList<>();
        Map<String, List<Qualification>> activityQualificationMap = new HashMap<>();
        activityCacheMap.forEach((x,y)->{
            String activityCode = y.getActivityCode();
            activityCodes.add(activityCode);
            List<Qualification> qualification = QualificationModel.convert(BeanCopyUtils.jsonCopyList(y.getQualifications(), QualificationModel.class));
            activityQualificationMap.put(activityCode,qualification);
        });
        String skuCode = product.getSkuCode();
        List<FlashSaleProductModel> flashSaleProductModels = flashSaleProductService.queryProductsByActivityCodesAndSkuCode(activityCodes, skuCode);
        Map<String, FlashSaleProductModel> skuMap = new HashMap<>();
        for (FlashSaleProductModel flashSaleProductModel : flashSaleProductModels) {
            skuMap.put(flashSaleProductModel.getActivityCode()+":"+flashSaleProductModel.getSkuCode(),flashSaleProductModel);
        }
        activityCacheMap.forEach((x,y)->{
            FlashSaleActivityInfoDTO activity = BeanCopyUtils.jsonCopyBean(y, FlashSaleActivityInfoDTO.class);
            if (y.getMarketingGroupMode()!=null){
                BeanCopyUtils.copyProperties(y.getMarketingGroupMode(),activity);
            }
            String activityCode = y.getActivityCode();
            activity.setQualifications(activityQualificationMap.get(activityCode));
            FlashSaleProductModel byActivityAndSku = skuMap.get(activityCode + ":" + skuCode);
            if (null != byActivityAndSku) {
                activity.setFlashPrice(byActivityAndSku.getFlashPrice());
                activity.setGroupLeaderPrice(byActivityAndSku.getGroupLeaderPrice());
                activity.setSkuInventory(byActivityAndSku.getSkuInventory());
                activity.setMaxPerUser(byActivityAndSku.getMaxPerUser());
                activities.add(activity);
            }
        });
        return activities;
    }

    public FlashSaleQueryProductListResult queryProductsByActivityCode(String language, String activityCode, PageParam page) {
        MarketingModel marketingModel = this.marketingService.findByActivityCode(activityCode);
        if (marketingModel == null || !marketingModel.getActivityStatus().equals(ActivityStatusEnum.EFFECTIVE.code()) || marketingModel.isNeedToDoExpire()) {
            throw Exceptions.fail(ErrorCodes.VALIDATE_ACTIVITY_EXIST, activityCode);
        }

        FlashSaleQueryProductListResult result = BeanCopyUtils.jsonCopyBean(marketingModel, FlashSaleQueryProductListResult.class);
        dealLanguage(language, activityCode, result);
        // 商品详情处理
        FlashSaleProductModel productModel = new FlashSaleProductModel();
        productModel.setActivityCode(activityCode);
        PageData<FlashSaleProductModel> flashSaleProductModelPageData = flashSaleProductService.selectPageList(productModel, page);
        result.setSpuSkuTotal(flashSaleProductModelPageData.getTotal());
        result.setProductDetils(BeanCopyUtils.jsonCopyList(flashSaleProductModelPageData.getList(), FlashSaleProductResult.class));
        result.setActivityPeriod(BeanCopyUtils.jsonCopyBean(activityPeriodService.findPeriod(marketingModel.getTenantCode(), activityCode), ActivityPeriod.class));
        //组装出参
        return result;
    }

    private void dealLanguage(String language, String activityCode, ActivityLanguageResult result) {
        if (StringUtil.isNotBlank(language)){
            List<MarketingLanguageModel> languages = languageService.findListByActivityCode(activityCode);
            foreachMarketingLanguageToSetValue(language, result, languages);
        }
    }

    private void foreachMarketingLanguageToSetValue(String language, ActivityLanguageResult result, List<MarketingLanguageModel> languages) {
        if (CollectionUtils.isNotEmpty(languages)){
            for (MarketingLanguageModel marketingLanguageModel : languages) {
                if (marketingLanguageModel.getLanguage().equals(language)){
                    setActivityValue(result, marketingLanguageModel);
                    break;
                }
            }
        }
    }

    private void setActivityValue(ActivityLanguageResult result, MarketingLanguageModel marketingLanguageModel) {
        if (StringUtil.isNotBlank(marketingLanguageModel.getActivityDesc())){
            result.setActivityDesc(marketingLanguageModel.getActivityDesc());
        }
        if (StringUtil.isNotBlank(marketingLanguageModel.getActivityName())){
            result.setActivityName(marketingLanguageModel.getActivityName());
        }
        if (StringUtil.isNotBlank(marketingLanguageModel.getActivityShortDesc())){
            result.setActivityShortDesc(marketingLanguageModel.getActivityShortDesc());
        }
        if (StringUtil.isNotBlank(marketingLanguageModel.getActivityLabel())){
            result.setActivityLabel(marketingLanguageModel.getActivityLabel());
        }
    }

    //每单每sku限制
    public boolean checkQuota(String activityCode, String skuOrSpuCode, Integer quality, String orderId,FlashSaleProductModel currentProductModel) {
        boolean b = null != currentProductModel && currentProductModel.getSkuInventory() >= quality;
        if (b){
            BigDecimal remainingValue = redisOpsHelper.getLimitedValue(LimitationCodeEnum.SKU_TOTAL_COUNT_GROUP, currentProductModel.getTenantCode(), activityCode + "-" + skuOrSpuCode, orderId, skuOrSpuCode);
            if (null == remainingValue) {
                remainingValue = new BigDecimal(currentProductModel.getMaxPerUser());
            }

            if (remainingValue.compareTo(new BigDecimal(quality)) < 0) {
                b = false;
            }
        }
        return b;
    }

//
//    //活动限制每人单数
//    public boolean checkPerOrderCount(String tenantCode,String activityCode, Integer quality, String memberCode) {
//
//        boolean flag = true;
//
//        BigDecimal remainingValue = redisOpsHelper.getLimitedValue("0466", tenantCode, activityCode, memberCode, "");
//
//        if (null == remainingValue) {
//            return true;
//        }
//        if (remainingValue.compareTo(new BigDecimal(quality)) < 0) {
//            flag = false;
//        }
//        return flag;
//    }

    @Transactional
    public void commitOrder(FlashSaleCreateOrderParam orderCommitDTO, List<FlashSaleOrderCalaResult> list) {

        //没有活动可参加并且有扣减金额
        Check.check(CollectionUtils.isEmpty(list), TPromoOrderChecker.TENANT_CODE_NO_ACTIVITY);
        Check.check(!list.get(0).getEffectiveFlag(), TPromoOrderChecker.TENANT_CODE_NO_ACTIVITY);
        //校验减免金额
        log.info("活动重新校验后的减免金额：{}， 接口传入的减免金额：{} ", list.get(0).getPromoRewardAmount(), orderCommitDTO.getPromoDeductedAmount());
        CheckUtils.isTrue(list.get(0).getPromoRewardAmount().setScale(CalcConstants.LAST_PRECISION, CalcConstants.LAST_ROUND).compareTo(
                orderCommitDTO.getPromoDeductedAmount().setScale(CalcConstants.LAST_PRECISION, CalcConstants.LAST_ROUND)) == 0,
                ErrorCodes.CREATE_ORDER_DEDUCTED_AMOUNT_ERROR, list.get(0).getPromoRewardAmount());

        //保存数据总订单数据
        FlashSaleOrderModel flashSaleOrderModel = new FlashSaleOrderModel();
        flashSaleOrderModel.setDomainCode(orderCommitDTO.getDomainCode());
        flashSaleOrderModel.setTenantCode(orderCommitDTO.getTenantCode());
        flashSaleOrderModel.setMemberCode(orderCommitDTO.getMemberCode());
        flashSaleOrderModel.setActivityCode(list.get(0).getActivityCode());
        flashSaleOrderModel.setOrgCode(orderCommitDTO.getCartStoreList().get(0).getOrgCode());
        flashSaleOrderModel.setOrderId(orderCommitDTO.getOrderNo());
        flashSaleOrderModel.setOrderStatus(OrderStatusEnum.UNPAID.code());
        flashSaleOrderModel.setPromoAmount(orderCommitDTO.getPromoDeductedAmount());

        MarketingGroupUserResult groupUser = list.get(0).getGroupUser();

        if (null != groupUser) {
            String teamLeader = groupUser.getTeamLeader();
            String marketingGroupCode = groupUser.getMarketingGroupCode();

            //校验是否存在进行中的拼团
            String leaderUserCode = groupUser.getLeaderUserCode();
            String selectProductType = groupUser.getSelectProductType();

            String productCode = groupUser.getProductCode();
            String skuCode = null;
            if (SelectorProductTypeEnum.SELECT_SKU.code().equals(selectProductType)) {
                skuCode = groupUser.getSkuCode();
            }
            //校验团员以及团长是否符合参团
            checkJoinGroupCondition(orderCommitDTO, list, groupUser, teamLeader, marketingGroupCode, leaderUserCode, productCode, skuCode);
            String currentDateAsString = DateUtil.getCurrentDateAsString(DateUtil.FORMAT_YYYYMMDDHHMISS_14);
            String effectiveTime = groupUser.getEffectiveTime();
            if (StringUtil.isNotEmpty(effectiveTime)) {
                Check.check(currentDateAsString.compareTo(effectiveTime) >= 0, TPromoOrderChecker.NO_MARKETING_EFFECTIVE_TIME);
            }

            flashSaleOrderModel.setMarketingGroupCode(groupUser.getMarketingGroupCode());

        }

        //保存数据,重复时会报错
        flashSaleOrderService.insert(flashSaleOrderModel);

        //校验并锁定资源
        MarketingModel byActivityCode = marketingService.findByActivityCode(list.get(0).getActivityCode());
        final long timeout = DateUtil.parseDate(byActivityCode.getActivityEnd(), DateUtil.FORMAT_YYYYMMDDHHMISS_14).getTime() - System.currentTimeMillis();

        String productCode = null;
        String skuCode = null;
        for (FlashSaleOrderCalaResult.ShoppingCartItem shoppingCartItem : list.get(0).getShoppingCartItems()) {

            productCode = shoppingCartItem.getProductCode();
            skuCode = shoppingCartItem.getSkuCode();

            FlashSaleOrderDetailModel detailModel = BeanCopyUtils.jsonCopyBean(flashSaleOrderModel, FlashSaleOrderDetailModel.class);
            detailModel.setSkuCode(skuCode);
            detailModel.setProductCode(productCode);
            detailModel.setSkuQuality(shoppingCartItem.getPromoQuantity());
            flashSaleOrderDetailService.insert(detailModel);

            if (!byActivityCode.getActivityType().equals(ActivityTypeEnum.LUCKY_DRAW.code())){
                int i = 0;
                if(SelectorProductTypeEnum.SELECT_SPU.code().equals(byActivityCode.getSelectProductType())){
                    i = flashSaleProductService.dealInventoryBySpu(list.get(0).getActivityCode(), shoppingCartItem.getProductCode(), shoppingCartItem.getPromoQuantity());

                }else if (SelectorProductTypeEnum.SELECT_SKU.code().equals(byActivityCode.getSelectProductType())){
                    i = flashSaleProductService.dealInventory(list.get(0).getActivityCode(), shoppingCartItem.getSkuCode(), shoppingCartItem.getPromoQuantity());
                }
                throwExceptionIfLessZero(i);
            }

            //预售，秒杀等逻辑，除拼团之外
            if (byActivityCode.getActivityType().equals(ActivityTypeEnum.PRE_SALE.code()) ||
                    byActivityCode.getActivityType().equals(ActivityTypeEnum.FLASH_SALE.code())){

                lockNumberOfParticipations(orderCommitDTO, list, flashSaleOrderModel, timeout, shoppingCartItem);
            }else if (byActivityCode.getActivityType().equals(ActivityTypeEnum.LUCKY_DRAW.code())){
                Boolean lockStatus = marketingLuckDrawPrizeProductService.lockPrizeProduct(orderCommitDTO.getTenantCode(), byActivityCode.getActivityCode(),orderCommitDTO.getMemberCode(), shoppingCartItem.getProductCode(),orderCommitDTO.getOrderNo());
                Check.check(!lockStatus, TPromoOrderChecker.NO_PRIZE_PRODUCT);
            }

        }

        //组团
        if (null != groupUser) {

            //资源限制
            activityIncentiveLimited(orderCommitDTO, timeout);
            MarketingGroupUserMode marketingGroupUserMode = new MarketingGroupUserMode();
            marketingGroupUserMode.setEffectiveHour(groupUser.getEffectiveHour());
            marketingGroupUserMode.setMarketingGroupCode(groupUser.getMarketingGroupCode());
            marketingGroupUserMode.setUserCode(groupUser.getUserCode());
            marketingGroupUserMode.setTenantCode(orderCommitDTO.getTenantCode());
            marketingGroupUserMode.setDomainCode(orderCommitDTO.getDomainCode());
            marketingGroupUserMode.setActivityCode(groupUser.getActivityCode());
            marketingGroupUserMode.setEffectiveTime(groupUser.getEffectiveTime());
            marketingGroupUserMode.setTeamLeader(groupUser.getTeamLeader());
            marketingGroupUserMode.setProductCode(productCode);
            marketingGroupUserMode.setSkuCode(skuCode);
            marketingGroupUserService.insert(marketingGroupUserMode);

            if (groupUser.getTeamLeader().equals(TeamLeaderEnum.LEADER.code())){
                MarketingGroupCodeMode codeMode = new MarketingGroupCodeMode();
                codeMode.setTenantCode(orderCommitDTO.getTenantCode());
                codeMode.setDomainCode(orderCommitDTO.getDomainCode());
                codeMode.setActivityCode(groupUser.getActivityCode());
                codeMode.setInventory(groupUser.getGroupSize());
                codeMode.setGroupStatus(MarketingGroupStatusEnum.GROUP_NO_START.code());
                codeMode.setMarketingGroupCode(groupUser.getMarketingGroupCode());
                codeMode.setOrgCode(groupUser.getOrgCode());
                codeMode.setEffectiveTime(groupUser.getEffectiveTime());
                //参团主表
                marketingGroupCodeService.insert(codeMode);

                marketingGroupCodeService.deductGroupInventory(orderCommitDTO.getTenantCode(), groupUser.getActivityCode(), groupUser.getMarketingGroupCode());

                //设置缓存
                activityRedisHelpler.setMarketingGroupCode(orderCommitDTO.getTenantCode(), groupUser.getActivityCode(), groupUser.getMarketingGroupCode(), groupUser.getEffectiveTime(), JSONObject.toJSONString(groupUser));
            }
        }
    }

    private void lockNumberOfParticipations(FlashSaleCreateOrderParam orderCommitDTO, List<FlashSaleOrderCalaResult> list, FlashSaleOrderModel flashSaleOrderModel, long timeout, FlashSaleOrderCalaResult.ShoppingCartItem shoppingCartItem) {
        RedisOpsHelper.IncentiveLimitedParam param = RedisOpsHelper.IncentiveLimitedParam.builder()
                .limitedCode(LimitationCodeEnum.USER_TOTAL_COUNT_FLASH_SALE.code())
                .tenantCode(orderCommitDTO.getTenantCode())
                .activityCode(flashSaleOrderModel.getActivityCode() + "-" + shoppingCartItem.getSkuCode())
                .userCode(orderCommitDTO.getMemberCode())
                .totalValue(new BigDecimal(list.get(0).getUserLimitation()))
                .build();
        setIncentiveValue(shoppingCartItem, param);
        List<RedisOpsHelper.IncentiveLimitedParam> ilParamList = new ArrayList<>();
        throwException(timeout, param, ilParamList);
    }

    public void checkJoinGroupCondition(FlashSaleCreateOrderParam orderCommitDTO, List<FlashSaleOrderCalaResult> list, MarketingGroupUserResult groupUser, String teamLeader, String marketingGroupCode, String leaderUserCode, String productCode, String skuCode) {
        if (TeamLeaderEnum.MEMBER.code().equals(teamLeader)) {
            //团长
            int i = flashSaleOrderService.checkLeaderPayOrder(orderCommitDTO.getTenantCode(), list.get(0).getActivityCode(), marketingGroupCode, leaderUserCode);
            Check.check(i == 0, TPromoOrderChecker.NO_LEADER_NO_PAY);

            int i1 = marketingGroupUserService.checkGroupProductCode(orderCommitDTO.getTenantCode(), list.get(0).getActivityCode(), marketingGroupCode, productCode,skuCode);
            Check.check(i1 == 0, TPromoOrderChecker.NO_ORDER_GROUP_LEADER);

            //校验剩余拼团库存
            int inventory = marketingGroupCodeService.deductGroupInventory(orderCommitDTO.getTenantCode(), groupUser.getActivityCode(), groupUser.getMarketingGroupCode());
            Check.check(inventory == 0, TPromoOrderChecker.QUANTITY_GROUP_SIZE);
        }else {
            int i1 = marketingGroupUserService.checkGroupLeaderProductCode(orderCommitDTO.getTenantCode(), list.get(0).getActivityCode(), marketingGroupCode, productCode,skuCode);
            Check.check(i1 == 1, TPromoOrderChecker.ONCE_GROUP);
        }
    }


    public void activityIncentiveLimited(FlashSaleCreateOrderParam orderCommitDTO, long timeout) {
        List<RedisOpsHelper.IncentiveLimitedParam> ilParamList = new ArrayList<>();
        //查询活动的限制条件
        List<TPromoIncentiveLimitedVO> limitedList = limitedService.getLimitedListByActivityCode(orderCommitDTO.getActivityCode());
        limitedList.forEach(x->{
            if (!LimitationCodeEnum.isTimes(x.getLimitationCode())) {
                return;
            }
            RedisOpsHelper.IncentiveLimitedParam limitedParam = RedisOpsHelper.IncentiveLimitedParam.builder()
                    .limitedCode(x.getLimitationCode())
                    .tenantCode(orderCommitDTO.getTenantCode())
                    .activityCode(x.getActivityCode())
                    .userCode(orderCommitDTO.getMemberCode())
                    .totalValue(x.getLimitationValue())
                    .build();

            //校验是否存在限制code并填充值
            if (limitedParam.isTimesValue()){
                limitedParam.setIncentiveValue(new BigDecimal(1));
            }
            //锁定资源
            checkAndLock(ilParamList, limitedParam, timeout, x.getLimitationCode());
        });
    }


    private void checkAndLock(List<RedisOpsHelper.IncentiveLimitedParam> ilParamList, RedisOpsHelper.IncentiveLimitedParam limitedParam, long timeout, String limitationCode) {
        ErrorCode errorCode = null;

        if (LimitationCodeEnum.USER_TOTAL_COUNT.equalsCode(limitationCode)) {
            errorCode = ErrorCodes.ACTIVITY_LIMIT_USER_TOTAL_COUNT;
        } else if (LimitationCodeEnum.ACTIVITY_TOTAL_COUNT.equalsCode(limitationCode)) {
            errorCode = ErrorCodes.ACTIVITY_LIMIT_ACTIVITY_TOTAL_COUNT;
        } else if (LimitationCodeEnum.USER_DAY_COUNT.equalsCode(limitationCode)) {
            errorCode = ErrorCodes.ACTIVITY_LIMIT_ACTIVITY_TOTAL_COUNT;
        } else if (LimitationCodeEnum.ACTIVITY_DAY_ORDER_COUNT.equalsCode(limitationCode)) {
            errorCode = ErrorCodes.ACTIVITY_LIMIT_ACTIVITY_TOTAL_COUNT;
        }

        if (errorCode != null && !this.redisOpsHelper.lockRedisData(ilParamList, limitedParam, timeout, "")) {
            throw Exceptions.fail(errorCode);
        }
    }


    public void throwExceptionIfLessZero(int i) {
        if (i <= 0){
            throw Exceptions.fail(ErrorCodes.FLASH_SALE_INVENTORY_NOT_ENOUGH);
        }
    }

    public void throwException(long timeout, RedisOpsHelper.IncentiveLimitedParam param, List<RedisOpsHelper.IncentiveLimitedParam> ilParamList) {
        if (!this.redisOpsHelper.lockRedisData(ilParamList, param, timeout, "")) {

            if (LimitationCodeEnum.USER_TOTAL_COUNT_ORDER_GROUP.code().equals(param.getLimitedCode())){
                throw Exceptions.fail(ErrorCodes.LOCK_PROMOTION_RESOURCES_FAILED_USER_ORDER_COUNT);
            }
            throw Exceptions.fail(ErrorCodes.LOCK_PROMOTION_RESOURCES_FAILED_USER_TOTAL_COUNT);
        }
    }

    public void setIncentiveValue(FlashSaleOrderCalaResult.ShoppingCartItem shoppingCartItem, RedisOpsHelper.IncentiveLimitedParam param) {
        if (param.isTimesValue()) {
            param.setIncentiveValue(new BigDecimal(shoppingCartItem.getPromoQuantity()));
        }
    }

    @Transactional
    public int confirmOrder(String tenantCode, String orderNo) {

        FlashSaleOrderModel byOrderNo = flashSaleOrderService.findByOrderNo(tenantCode, orderNo);
        if (Objects.isNull(byOrderNo) || OrderStatusEnum.PAID.code().equals(byOrderNo.getOrderStatus())){
            return 1;
        }
        Check.check(OrderStatusEnum.CANCELED.equalsCode(byOrderNo.getOrderStatus()), TPromoOrderChecker.ERROR_CANCEL_ORDER_NO);
        //修改订单状态  改成已支付
        String orderStatus = OrderStatusEnum.PAID.code();

        int i = flashSaleOrderService.updateStatus(tenantCode, orderNo, orderStatus);

        String marketingGroupCode = byOrderNo.getMarketingGroupCode();
        //支付拼团业务处理
        updateMarketingGroupUserStatus(tenantCode, byOrderNo, marketingGroupCode);

        //确认订单，如果是抽奖活动，核销奖品
        MarketingModel byActivityCode = marketingService.findByActivityCode(byOrderNo.getActivityCode());
        if (byActivityCode.getActivityType().equals(ActivityTypeEnum.LUCKY_DRAW.code())){
            Boolean aBoolean = marketingLuckDrawPrizeProductService.usedPrizeProduct(tenantCode, byOrderNo.getActivityCode(),byOrderNo.getMemberCode(), orderNo);
            Check.check(!aBoolean, TPromoOrderChecker.NO_PRIZE_PRODUCT);
        }

        return i;
    }

    public void updateMarketingGroupUserStatus(String tenantCode, FlashSaleOrderModel byOrderNo, String marketingGroupCode) {

        if (StringUtil.isNotEmpty(marketingGroupCode)){

            String activityCode = byOrderNo.getActivityCode();
            MarketingGroupMode groupMode = marketingGroupService.findMarketingGroupByActivityCode(tenantCode, activityCode);

            //团长是否存在
            MarketingGroupUserMode leader = marketingGroupUserService.confirmGroupLeaderByMarketingGroupCode(tenantCode, marketingGroupCode);
            Check.check(null ==leader, TPromoOrderChecker.NO_ORDER_GROUP_LEADER);

            MarketingModel byActivityCode = marketingService.findByActivityCode(activityCode);

            MarketingGroupCodeMode marketingGroupCodeMode = marketingGroupCodeService.queryGroupByMarketingGroupCode(tenantCode, activityCode, marketingGroupCode);

            //团长
            if (leader.getUserCode().equals(byOrderNo.getMemberCode())){
                updateLeaderGroupInformation(tenantCode, byOrderNo, marketingGroupCode, activityCode, groupMode,byActivityCode,marketingGroupCodeMode);
            }else {
                //团员情况处理
                //状态变更支付
                Check.check(null ==marketingGroupCodeMode, TPromoOrderChecker.NO_ORDER_GROUP_LEADER);

                updateUserGroupStatus(tenantCode, byOrderNo,marketingGroupCodeMode.getInventory(),groupMode,byActivityCode,marketingGroupCodeMode);
            }
        }
    }

    //变更团长相关信息
    public void updateLeaderGroupInformation(String tenantCode, FlashSaleOrderModel byOrderNo, String marketingGroupCode, String activityCode,
                                             MarketingGroupMode groupMode, MarketingModel byActivityCode, MarketingGroupCodeMode marketingGroupCodeMode) {
        String memberCode = byOrderNo.getMemberCode();
        String currentTime = DateUtil.format(new Date(), DateUtil.FORMAT_YYYYMMDDHHMISS_14);


        String effectiveTime = marketingGroupCodeMode.getEffectiveTime();
        String groupStatus = marketingGroupCodeMode.getGroupStatus();
        //如果这个时间小于活动结束时间,则使用活动结束时间
        if (effectiveTime.compareTo(byActivityCode.getActivityEnd()) > 0) {
            effectiveTime = byActivityCode.getActivityEnd();
        }
        String activityStatus = byActivityCode.getActivityStatus();
        //终止业务
        if (ActivityStatusEnum.END.code().equals(activityStatus)) {
            //允许自动成团
            if (groupMode.getAutoGroupFlag().intValue() == 0) {
                //拼团未开始，则只有团长一人
                if (groupStatus.equals(MarketingGroupStatusEnum.GROUP_NO_START.code())) {
                    marketingGroupCodeService.updateMarketingCodeGroupStatus(tenantCode, activityCode, marketingGroupCode, MarketingGroupStatusEnum.GROUP_SUCCESS.code());
                    //团长更新
					marketingGroupUserService.updateGroupUserStatus(tenantCode, marketingGroupCode, UserGroupStatusEnum.FINISH.code(),
							UserGroupStatusEnum.PROCESSING.code());
                    //mq消息推送
					marketingGroupComponent.sendMessageToOms(marketingGroupCodeMode, "1");

                }
            }
        } else {
            //正常业务
            //允许自动成团
            if (groupMode.getAutoGroupFlag().intValue() == 0) {
                //过期处理
                if (currentTime.compareTo(effectiveTime) > 0) {
                    //拼团未开始，则只有团长一人
                    marketingGroupCodeService.updateMarketingCodeGroupStatus(tenantCode, activityCode, marketingGroupCode, MarketingGroupStatusEnum.GROUP_SUCCESS.code());
                    //团长更新
					marketingGroupUserService.updateGroupUserStatus(tenantCode, marketingGroupCode, UserGroupStatusEnum.FINISH.code(),
							UserGroupStatusEnum.PROCESSING.code());
                } else {
                    //未过期，业务处理
                    marketingGroupCodeService.updateMarketingCodeGroupStatus(tenantCode, activityCode, marketingGroupCode, MarketingGroupStatusEnum.GROUP_PROCESSING.code());
                    marketingGroupUserService.updateGroupEffectiveTimeByMarketingGroupCode(tenantCode, marketingGroupCode, byOrderNo.getMemberCode(), marketingGroupCodeMode.getEffectiveTime());
                }

            } else {
                //不允许自动成团
                if (currentTime.compareTo(effectiveTime)> 0) {
                    marketingGroupCodeService.updateMarketingCodeGroupStatus(tenantCode, activityCode, marketingGroupCode, MarketingGroupStatusEnum.GROUP_FAIL.code());
                    //用户取消拼团
					marketingGroupUserService.updateGroupUserStatus(tenantCode, marketingGroupCode, UserGroupStatusEnum.CANCEL.code(),
							UserGroupStatusEnum.PROCESSING.code());

                } else {
                    //时间未过期，正常业务支付
                    marketingGroupCodeService.updateMarketingCodeGroupStatus(tenantCode, activityCode, marketingGroupCode, MarketingGroupStatusEnum.GROUP_PROCESSING.code());
                    //参团人员信息表
                    marketingGroupUserService.updateGroupEffectiveTimeByMarketingGroupCode(tenantCode, marketingGroupCode, memberCode, effectiveTime);
                }
            }
        }
    }

    //对应状态变更
    public void updateUserGroupStatus(String tenantCode, FlashSaleOrderModel byOrderNo, int i1,MarketingGroupMode groupMode,MarketingModel byActivityCode,MarketingGroupCodeMode marketingGroupCodeMode) {

        String groupStatus = marketingGroupCodeMode.getGroupStatus();

        int inventory = marketingGroupCodeMode.getInventory();
        String activityStatus = byActivityCode.getActivityStatus();
        String activityCode = marketingGroupCodeMode.getActivityCode();
        String marketingGroupCode = marketingGroupCodeMode.getMarketingGroupCode();
        String effectiveTime = marketingGroupCodeMode.getEffectiveTime();
        //终止业务
        if (ActivityStatusEnum.END.code().equals(activityStatus)) {
            //允许自动成团
            if (groupMode.getAutoGroupFlag().intValue() == 0) {
                //拼团未开始，则只有团长一人
                if (groupStatus.equals(MarketingGroupStatusEnum.GROUP_NO_START.code())) {
                    marketingGroupCodeService.updateMarketingCodeGroupStatus(tenantCode, activityCode, marketingGroupCode, MarketingGroupStatusEnum.GROUP_SUCCESS.code());
                    //团长更新
					marketingGroupUserService.updateGroupUserStatus(tenantCode, marketingGroupCode, UserGroupStatusEnum.FINISH.code(),
							UserGroupStatusEnum.PROCESSING.code());

                    //mq消息推送
					marketingGroupComponent.sendMessageToOms(marketingGroupCodeMode, "1");

                } else if (groupStatus.equals(MarketingGroupStatusEnum.GROUP_SUCCESS.code())) {

                    //更新用户表已支付,mq消息之前推送过不需要再次推送
                    marketingGroupUserService.updateGroupEffectiveTimeByMarketingGroupCode(tenantCode, marketingGroupCode, byOrderNo.getMemberCode(), marketingGroupCodeMode.getEffectiveTime());
					marketingGroupUserService.updateGroupUserStatus(tenantCode, marketingGroupCode, UserGroupStatusEnum.FINISH.code(),
							UserGroupStatusEnum.PAID.code());
                }

            } else {
                if (groupStatus.equals(MarketingGroupStatusEnum.GROUP_PROCESSING.code())) {
                    //满成团人数
                    if (inventory == 0) {
                        //未过期，业务处理
                        int i = marketingGroupUserService.queryGroupMemberNoPay(tenantCode, activityCode, marketingGroupCode);
                        if (i > 1) {
                            //更新用户表已支付
                            marketingGroupUserService.updateGroupEffectiveTimeByMarketingGroupCode(tenantCode, marketingGroupCode, byOrderNo.getMemberCode(), marketingGroupCodeMode.getEffectiveTime());
                        } else {
                            //主题表更新拼团成功
                            marketingGroupCodeService.updateMarketingCodeGroupStatus(tenantCode, activityCode, marketingGroupCode, MarketingGroupStatusEnum.GROUP_SUCCESS.code());
                            //参团人员信息表 将已支付的以及剩余最后一个进行中的一起更新为拼团成功
                            marketingGroupUserService.updateGroupStatusByMarketingGroupCode(tenantCode, marketingGroupCode, UserGroupStatusEnum.FINISH);

                            //mq消息推送
							marketingGroupComponent.sendMessageToOms(marketingGroupCodeMode, "1");
                        }
                    }
                }
            }
        }else {


            String currentTime = DateUtil.format(new Date(), DateUtil.FORMAT_YYYYMMDDHHMISS_14);

            if (currentTime.compareTo(effectiveTime) > 0) {

                if (groupMode.getAutoGroupFlag().intValue() == 0) {
                    //更新用户表已支付,mq消息之前推送过不需要再次推送
                    marketingGroupUserService.updateGroupEffectiveTimeByMarketingGroupCode(tenantCode, marketingGroupCode, byOrderNo.getMemberCode(), marketingGroupCodeMode.getEffectiveTime());
					marketingGroupUserService.updateGroupUserStatus(tenantCode, marketingGroupCode, UserGroupStatusEnum.FINISH.code(),
							UserGroupStatusEnum.PAID.code());
                } else {
                    //满成团人数
                    if (inventory == 0) {
                        //未过期，业务处理
                        int i = marketingGroupUserService.queryGroupMemberNoPay(tenantCode, activityCode, marketingGroupCode);
                        if (i > 1) {
                            //更新用户表已支付
                            marketingGroupUserService.updateGroupEffectiveTimeByMarketingGroupCode(tenantCode, marketingGroupCode, byOrderNo.getMemberCode(), marketingGroupCodeMode.getEffectiveTime());
                        } else {
                            //主题表更新拼团成功
                            marketingGroupCodeService.updateMarketingCodeGroupStatus(tenantCode, activityCode, marketingGroupCode, MarketingGroupStatusEnum.GROUP_SUCCESS.code());
                            //参团人员信息表 将已支付的以及剩余最后一个进行中的一起更新为拼团成功
                            marketingGroupUserService.updateGroupStatusByMarketingGroupCode(tenantCode, marketingGroupCode, UserGroupStatusEnum.FINISH);
                            //mq消息推送
							marketingGroupComponent.sendMessageToOms(marketingGroupCodeMode, "1");
                        }
                    }
                }

            } else {
                //拼团满员后,更改拼团状态
                //支付成功
                marketingGroupUserService.updateGroupSuccessStatusByUserCode(tenantCode, marketingGroupCode,byOrderNo.getMemberCode(), UserGroupStatusEnum.PAID);
                //团员不存在未支付情况
                int i = marketingGroupUserService.queryGroupMemberNoPay(tenantCode, activityCode, marketingGroupCode);

                if (i1 == 0 && 0 == i) {
                    marketingGroupUserService.updateGroupStatusByMarketingGroupCode(tenantCode, marketingGroupCode, UserGroupStatusEnum.FINISH);
                    marketingGroupCodeService.updateMarketingCodeGroupStatus(tenantCode,activityCode,marketingGroupCode,MarketingGroupStatusEnum.GROUP_SUCCESS.code());

                    //模糊删除该拼团下所有消息记录 即该团团长消息记录
                    String matchKey = activityRedisHelpler.queryFuzzyMatchingMarketingGroupUserKey(tenantCode, byOrderNo.getActivityCode(), marketingGroupCode);
                    activityRedisHelpler.deleteFuzzyMatch(matchKey);
                    //mq消息推送
					marketingGroupComponent.sendMessageToOms(marketingGroupCodeMode, "1");
                }
            }
        }
    }

    @Transactional
    public int cancelOrder(String tenantCode, String orderNo) {
        FlashSaleOrderModel byOrderNo = flashSaleOrderService.findByOrderNo(tenantCode, orderNo);
        if (Objects.isNull(byOrderNo) || OrderStatusEnum.CANCELED.code().equals(byOrderNo.getOrderStatus())){
            return 1;
        }
        Check.check(OrderStatusEnum.PAID.equalsCode(byOrderNo.getOrderStatus()), TPromoOrderChecker.ERROR_PAID_ORDER_NO);

        List<FlashSaleOrderDetailModel> detailsByOrderNo = flashSaleOrderDetailService.findByOrderNo(tenantCode, orderNo);

        MarketingModel byActivityCode = marketingService.findByActivityCode(byOrderNo.getActivityCode());

        for (FlashSaleOrderDetailModel detailModel : detailsByOrderNo) {

            String code = SelectorProductTypeEnum.SELECT_SPU.code();
            String selectProductType = byActivityCode.getSelectProductType();

            //spu或者sku资源回滚
            rollBackDealInventory(byOrderNo, detailModel, code, selectProductType);

            try {
                if (StringUtil.isNotEmpty(byOrderNo.getMarketingGroupCode())) {
                    //拼团取消业务逻辑，以及资源释放
                    rollBackMarketingGroupResource(tenantCode, byOrderNo, detailModel,byActivityCode);
                }else if(byActivityCode.getActivityType().equals(ActivityTypeEnum.LUCKY_DRAW.code())){

                    //抽奖取消业务逻辑，以及资源释放
                    rollBackMarketingLuckyDrawResource(tenantCode, byOrderNo, detailModel,byActivityCode);
                    
                } else {
            	    //预售 秒杀资源释放
                    rollBackMarketingResource(tenantCode, byOrderNo, detailModel);
                }

            }catch (Exception e){
                log.error("releaseUserMax error!");
                log.error(e.getMessage(), e);
            }
        }
        return flashSaleOrderService.updateStatus(tenantCode, orderNo, OrderStatusEnum.CANCELED.code());
    }

    private void rollBackMarketingLuckyDrawResource(String tenantCode, FlashSaleOrderModel byOrderNo, FlashSaleOrderDetailModel detailModel, MarketingModel byActivityCode) {

        marketingLuckDrawPrizeProductService.cancelPrizeProduct(tenantCode, byOrderNo.getActivityCode(),byOrderNo.getMemberCode(), byOrderNo.getOrderId());


    }

    public void rollBackDealInventory(FlashSaleOrderModel byOrderNo, FlashSaleOrderDetailModel detailModel, String code, String selectProductType) {
        if (code.equals(selectProductType)){
            flashSaleProductService.dealInventoryBySpu(byOrderNo.getActivityCode(), detailModel.getProductCode(), -detailModel.getSkuQuality());

        }else {
            flashSaleProductService.dealInventory(byOrderNo.getActivityCode(), detailModel.getSkuCode(), -detailModel.getSkuQuality());
        }
    }

    public void rollBackMarketingResource(String tenantCode, FlashSaleOrderModel byOrderNo, FlashSaleOrderDetailModel detailModel) {
        String key1 = redisOpsHelper.buildIncentiveKey(LimitationCodeEnum.USER_TOTAL_COUNT_FLASH_SALE.code(), tenantCode, byOrderNo.getActivityCode() + "-" + detailModel.getSkuCode(), "", "");
        String userLimitKey1 = redisOpsHelper.buildIncentiveKey(LimitationCodeEnum.USER_TOTAL_COUNT_FLASH_SALE.code(), tenantCode, byOrderNo.getActivityCode() + "-" + detailModel.getSkuCode(), byOrderNo.getMemberCode(), detailModel.getSkuCode());
        if(Boolean.TRUE.equals(redisTemplate.opsHashHasKey(Constants.APP_KEY, key1, userLimitKey1))) {
            redisTemplate.opsHashIncrement(Constants.APP_KEY, key1, userLimitKey1,detailModel.getSkuQuality());
        }
    }

    public void rollBackMarketingGroupResource(String tenantCode, FlashSaleOrderModel byOrderNo, FlashSaleOrderDetailModel detailModel,MarketingModel byActivityCode) {

        List<TPromoIncentiveLimitedVO> limitedList = limitedService.getLimitedListByActivityCode(detailModel.getActivityCode());

        //用户sku限制
        String activityCode = byOrderNo.getActivityCode();
        String key = redisOpsHelper.buildIncentiveKey(LimitationCodeEnum.USER_TOTAL_COUNT_ORDER_GROUP.code(), tenantCode, activityCode, "", "");
        String userLimitKey = redisOpsHelper.buildIncentiveKey(LimitationCodeEnum.USER_TOTAL_COUNT_ORDER_GROUP.code(), tenantCode, activityCode, byOrderNo.getMemberCode(), "");
        if (Boolean.TRUE.equals(redisTemplate.opsHashHasKey(Constants.APP_KEY, key, userLimitKey))) {
            redisTemplate.opsHashIncrement(Constants.APP_KEY, key, userLimitKey, detailModel.getSkuQuality());
        }

        //指定拼团资源回滚
        String marketingGroupCode = byOrderNo.getMarketingGroupCode();
        marketingGroupCodeService.addGroupInventory(tenantCode, activityCode, marketingGroupCode);

        //拼团状态未开始的状态，取消拼团，状态变更拼团失败，其他状态则不需要关注
        MarketingGroupCodeMode marketingGroupCodeMode = marketingGroupCodeService.queryGroupByMarketingGroupCode(tenantCode, activityCode, marketingGroupCode);
        String groupStatus = marketingGroupCodeMode.getGroupStatus();
        if (groupStatus.equals(MarketingGroupStatusEnum.GROUP_NO_START.code())) {
            marketingGroupCodeService.updateMarketingCodeGroupStatus(tenantCode, activityCode, marketingGroupCode, MarketingGroupStatusEnum.GROUP_FAIL.code());
        }


        limitedList.forEach(x -> {
            String activityKey = redisOpsHelper.buildIncentiveKey(x.getLimitationCode(), tenantCode, activityCode, "", "");
            String userActivityLimitKey = redisOpsHelper.buildIncentiveKey(x.getLimitationCode(), tenantCode, activityCode, byOrderNo.getMemberCode(), "");
            if (LimitationCodeEnum.USER_TOTAL_COUNT.equalsCode(x.getLimitationCode())
                    || LimitationCodeEnum.USER_DAY_COUNT.equalsCode(x.getLimitationCode())
                    || LimitationCodeEnum.USER_WEEK_COUNT.equalsCode(x.getLimitationCode())
                    || LimitationCodeEnum.USER_MONTH_COUNT.equalsCode(x.getLimitationCode())
                    || LimitationCodeEnum.USER_TOTAL_AMOUNT.equalsCode(x.getLimitationCode())
                    || LimitationCodeEnum.USER_TOTAL_COUNT_FLASH_SALE.equalsCode(x.getLimitationCode())//秒杀
                    || LimitationCodeEnum.USER_TOTAL_COUNT_ORDER_GROUP.equalsCode(x.getLimitationCode())//拼团
                    || LimitationCodeEnum.USER_DAY_ORDER_COUNT.equalsCode(x.getLimitationCode())
                    || LimitationCodeEnum.USER_SKU_COUNT.equalsCode(x.getLimitationCode())
                    || LimitationCodeEnum.SKU_COUNT.equalsCode(x.getLimitationCode())) {
                if (Boolean.TRUE.equals(redisTemplate.opsHashHasKey(Constants.APP_KEY, activityKey, userActivityLimitKey))) {
                    redisTemplate.opsHashIncrement(Constants.APP_KEY, activityKey, userActivityLimitKey, 1);
                }
            } else {
                redisTemplate.opsValueIncrement(Constants.APP_KEY, userActivityLimitKey, 1);
            }
        });

        MarketingGroupMode marketingGroupMode = marketingGroupService.findMarketingGroupByActivityCode(tenantCode, activityCode);
        String effectiveTime = DateUtil.format(new Date(), DateUtil.FORMAT_YYYYMMDDHHMISS_14);

        if ((byActivityCode.getActivityStatus().equals(ActivityStatusEnum.END.code()) && marketingGroupMode.getAutoGroupFlag().intValue() == 1)
                || effectiveTime.compareTo(marketingGroupCodeMode.getEffectiveTime()) > 0) {

            int i = marketingGroupUserService.queryGroupMemberNoPay(tenantCode, activityCode, marketingGroupCode);

            if (i == 1) {
                marketingGroupCodeService.updateMarketingCodeGroupStatus(tenantCode, activityCode, marketingGroupCode, MarketingGroupStatusEnum.GROUP_FAIL.code());
                //统一取消拼团 已支付的
                marketingGroupUserService.updateGroupStatusByMarketingGroupCode(tenantCode, marketingGroupCode, UserGroupStatusEnum.CANCEL);

                //mq消息推送
				marketingGroupComponent.sendMessageToOms(marketingGroupCodeMode, "0");

            }
        }

        //取消拼团更新状态并记录取消时间
        marketingGroupUserService.cancelGroupByMarketingGroupAndUserCode(tenantCode, marketingGroupCode, byOrderNo.getMemberCode());

    }


    public PageResult<FlashSaleQueryListResult> queryActivityList(FlashSaleQueryListParam param) {
        PageData<MarketingModel> marketingModelPageData = marketingService.queryMarketingFlashSaleList(param);
        List<FlashSaleQueryListResult> result = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(marketingModelPageData.getList())){
            List<String> activityCodes = new ArrayList<>();
            marketingModelPageData.getList().forEach(x-> activityCodes.add(x.getActivityCode()));
            List<ActivityPeriodModel> periodModels = activityPeriodService.queryPeriodByActivityCodes(param.getTenantCode(), activityCodes);
            Map<String, List<ActivityPeriodModel>> collect = periodModels.stream().collect(Collectors.groupingBy(ActivityPeriodModel::getActivityCode));
            for (MarketingModel marketingModel : marketingModelPageData.getList()) {
                FlashSaleQueryListResult queryListResult = BeanCopyUtils.jsonCopyBean(marketingModel, FlashSaleQueryListResult.class);
                queryListResult.setActivityPeriod(BeanCopyUtils.jsonCopyBean(CollectionUtils.isNotEmpty(collect.get(marketingModel.getActivityCode())) ? collect.get(marketingModel.getActivityCode()).get(0) : null, ActivityPeriod.class));
                dealLanguage(param.getLanguage(), marketingModel.getActivityCode(), queryListResult);
                result.add(queryListResult);
            }
        }
        return PageResult.ok(result, marketingModelPageData.getTotal());
    }

    public List<FlashSaleProductModel> getProductsByActivityCodesAndProducts(String tenantCode, Set<String> activityCodeSet, List<String> skuCodeList) {
        return flashSaleProductService.getProductsByActivityCodesAndProducts(tenantCode, new ArrayList<>(activityCodeSet), skuCodeList);
    }

    public List<FlashSaleProductModel> getBoostSharingProductsByActivityCodesAndProducts(String tenantCode, Set<String> activityCodeSet, List<String> skuCodeList) {
        return boostSharingService.getBoostSharingProductsByActivityCodesAndProducts(tenantCode, new ArrayList<>(activityCodeSet), skuCodeList);
    }
    public List<FlashSaleProductModel> getLuckDrawProductsByActivityCodesAndProducts(String tenantCode, Set<String> activityCodeSet, List<String> skuCodeList) {
        return luckyDrawRuleService.getLuckDrawProductsByActivityCodesAndProducts(tenantCode, new ArrayList<>(activityCodeSet), skuCodeList);
    }
    public List<FlashSaleProductModel> getProductsByActivityCodesAndProductsBySpu(String tenantCode, Set<String> activityCodeSet, List<String> spuCodeList) {
        return flashSaleProductService.getProductsByActivityCodesAndProductsBySpu(tenantCode, new ArrayList<>(activityCodeSet), spuCodeList);
    }



    public int buildMemberInventory(String activityCode, String skuCode, String memberCode,FlashSaleProductModel currentProductModel){
        if(currentProductModel.getSkuInventory() == 0){
            return 0;
        }
        BigDecimal remainingValue = redisOpsHelper.getLimitedValue("0411", currentProductModel.getTenantCode(), activityCode + "-" + skuCode, memberCode, "");
        if (null == remainingValue) {
//            return 0;
			remainingValue = new BigDecimal(currentProductModel.getMaxPerUser());
        }

        return remainingValue.intValue();
    }

    public void buildQueryMemberInventory(String tenantCode,String memberCode,String skuCode,List<QueryFlashSaleListByProductResult> flashSaleListByProductList){
        if(!StringUtil.isEmpty(memberCode))
            flashSaleListByProductList.forEach(flashSale->{
                FlashSaleProductModel currentProductModel = new FlashSaleProductModel();
                currentProductModel.setTenantCode(tenantCode);
                currentProductModel.setMaxPerUser(flashSale.getMaxPerUser());
                currentProductModel.setSkuInventory(flashSale.getSkuInventory());
                flashSale.setMemberInventory(buildMemberInventory(flashSale.getActivityCode(),skuCode,memberCode,currentProductModel));
            });
    }


    public List<QueryMarketingActivityListByProductListResult> queryMarketingActivityListByProductList(QueryMarketingActivityListByProductListParam param){

        //该商户已审核成功的活动
        Map<String, CacheFlashSaleModel> activityCacheMap = marketingCacheComponent.getFlashSaleCacheMapByActivityTypeList(param.getTenantCode(), param.getLanguage(), param.getActivityTypeList());

        // 过滤符合的活动
//        activityCacheMap = MarketingFilterUtil.filterActivityByTime(activityCacheMap, null,param.getWarmBeginFrom(),param.getWarmBeginTo());

        // 过滤符合的活动,预热时间段
        activityCacheMap = MarketingFilterUtil.filterActivityByWarmTime(activityCacheMap,null,param.getWarmBeginFrom());


        activityCacheMap = MarketingFilterUtil.filterActivityByQualifications(activityCacheMap, param.getQualifications());
        activityCacheMap = MarketingFilterUtil.filterActivityByActivityTypeList(activityCacheMap, param.getActivityTypeList());

        List<QueryMarketingActivityListByProductListResult> list = new ArrayList<>();
        List<QueryMarketingActivityListByProductListParam.Product> productList = param.getProductList();
        productList.stream().collect(Collectors.groupingBy(product -> {
            if (StringUtil.isNotEmpty(product.getProductCode())){
                return product.getProductCode();
            }else {
                return "default";
            }
        })).forEach((k, v)->{
            QueryMarketingActivityListByProductListResult result = new QueryMarketingActivityListByProductListResult();
            if (StringUtil.isNotEmpty(k)){
                result.setProductCode(k);
            }
            for (QueryMarketingActivityListByProductListParam.Product product : v) {
                QueryMarketingActivityListByProductListResult.SkuActivityList skuActivity = new QueryMarketingActivityListByProductListResult.SkuActivityList();
                //如果为空新增,否则追加
                if (result.getSkuList() == null){
                    result.setSkuList(Lists.newArrayList());
                }

                if (StringUtil.isNotEmpty(product.getSkuCode())){
                    skuActivity.setSkuCode(product.getSkuCode());
                    result.getSkuList().add(skuActivity);
                }
            }
            list.add(result);
        });

        filterActiveSpuOrSku(param, activityCacheMap,list);


        return list;

    }

    public void filterActiveSpuOrSku(QueryMarketingActivityListByProductListParam param, Map<String, CacheFlashSaleModel> activityCacheMap,  List<QueryMarketingActivityListByProductListResult> list) {
        if (activityCacheMap.size() == 0){
            return;
        }

        //针对抽奖特殊处理,转为SPU
        activityCacheMap.values().stream().filter(x -> x.getActivityType().equals(ActivityTypeEnum.LUCKY_DRAW.code())).forEach(x -> {
            x.setSelectProductType(SelectorProductTypeEnum.SELECT_SPU.code());
        });


        //前置数据处理
        List<String> skuList = param.getProductList().stream().filter(x -> StringUtil.isNotEmpty(x.getSkuCode())).map(QueryMarketingActivityListByProductListParam.Product::getSkuCode).collect(Collectors.toList());
        List<String> spuList = param.getProductList().stream().filter(x -> StringUtil.isNotEmpty(x.getProductCode())).map(QueryMarketingActivityListByProductListParam.Product::getProductCode).collect(Collectors.toList());
        List<FlashSaleProductModel> productsBySku = CollectionUtils.isEmpty(skuList) ? Lists.newArrayList() : this.getProductsByActivityCodesAndProducts(param.getTenantCode(), activityCacheMap.keySet(), skuList);
        List<FlashSaleProductModel> productsByBoostSharing =  CollectionUtils.isEmpty(spuList) ? Lists.newArrayList() :  this.getBoostSharingProductsByActivityCodesAndProducts(param.getTenantCode(), activityCacheMap.keySet(), spuList);
        List<FlashSaleProductModel> luckDrawProducts =  CollectionUtils.isEmpty(spuList) ? Lists.newArrayList() :  this.getLuckDrawProductsByActivityCodesAndProducts(param.getTenantCode(), activityCacheMap.keySet(), spuList);
        List<FlashSaleProductModel> productsBySpu =  CollectionUtils.isEmpty(spuList) ? Lists.newArrayList() :  this.getProductsByActivityCodesAndProductsBySpu(param.getTenantCode(), activityCacheMap.keySet(), spuList);




        List<MarketingGroupEntity> marketingGroupModels = marketingGroupService.selectMarketingGroupList(param.getTenantCode(),
                activityCacheMap.values().stream().filter(x -> x.getActivityType().equals(ActivityTypeEnum.GROUP.code())).map(CacheFlashSaleModel::getActivityCode).collect(Collectors.toSet()));
        List<BoostSharingModel> boostSharingModels = boostSharingService.queryBoostSharingByActivityList(param.getTenantCode(),
                activityCacheMap.values().stream().filter(x -> x.getActivityType().equals(ActivityTypeEnum.BOOST_SHARDING.code())).map(CacheFlashSaleModel::getActivityCode).collect(Collectors.toList()));

        for (QueryMarketingActivityListByProductListParam.Product product : param.getProductList()) {
            Map<String, CacheFlashSaleModel> copyMap = new HashedMap<>();
            copyMap.putAll(activityCacheMap);
            ProductCodes productCodes = makeProductCodes(product);
            List<String> orgCodes;
            if (StringUtils.isNotBlank(product.getOrgCodes())) {
                orgCodes = Arrays.asList(product.getOrgCodes().split(","));
            } else {
                orgCodes = Collections.singletonList(ActivityStoreEnum.ALL.code());
            }
            ActivityProductCheckUtil.checkProductCombine(productCodes);
            copyMap = MarketingFilterUtil.filterActivityByOrgCodes(copyMap, orgCodes);


            productsBySpu.addAll(productsByBoostSharing);
            productsBySpu.addAll(luckDrawProducts);
            for (QueryMarketingActivityListByProductListResult productListResult : list) {
                if (CollectionUtils.isNotEmpty(productsBySpu) && CollectionUtils.isNotEmpty(spuList)){
                    List<FlashSaleProductModel> productFlashSale = productsBySpu.stream().filter(x -> x.getProductCode().equals(productListResult.getProductCode())).collect(Collectors.toList());
                    Map<String, CacheFlashSaleModel> spuMap = marketingCacheComponent.filterActivityByProduct(copyMap, productCodes, productFlashSale,SelectorProductTypeEnum.SELECT_SPU.code());
                    List<CacheFlashSaleModel> spuMarketing = new ArrayList<>(spuMap.values());
                    spuMarketing = getGroupMarketing(param.getTenantCode(), spuMarketing, marketingGroupModels);
                    spuMarketing = getBoostSharing(param.getTenantCode(), spuMarketing, boostSharingModels);

                    List<QueryMarketingActivityListByProductListResult.MarketingActivity> marketingActivityList = new ArrayList<>();
                    for (CacheFlashSaleModel model : spuMarketing) {
                        String selectProductType = model.getSelectProductType();
                        if (SelectorProductTypeEnum.SELECT_SPU.code().equals(selectProductType)){
                            QueryMarketingActivityListByProductListResult.MarketingActivity marketingActivity = BeanCopyUtils.jsonCopyBean(model, QueryMarketingActivityListByProductListResult.MarketingActivity.class);
                            //设置价格，从products中获取sku相同的价格，如果为空，则为空
                            Optional<QueryMarketingActivityListByProductListResult.ProductPrice> first = marketingActivity.getProducts().stream()
                                    .filter(x -> x.getProductCode().equals(productListResult.getProductCode())).findFirst();
                            marketingActivity.setFlashPrice(first.map(QueryMarketingActivityListByProductListResult.ProductPrice::getFlashPrice).orElse(BigDecimal.ZERO));
                            List<MarketingLanguageModel> languages = model.getLanguages();
                            marketingActivity.setLanguageResults(makeLanguageResult(languages));
                            for (MarketingLanguageModel language : languages) {
                                if (StringUtil.isNotEmpty(param.getLanguage())) {
                                    if (language.getLanguage().equalsIgnoreCase(param.getLanguage())&& StringUtil.isNotBlank(language.getActivityName())) {
                                        marketingActivity.setActivityLabel(language.getActivityLabel());
                                        marketingActivity.setActivityName(language.getActivityName());
                                    }
                                } else {
                                    if (language.getLanguage().equalsIgnoreCase(LangTypeStandardEnum.CN.code())&& StringUtil.isNotBlank(language.getActivityName())) {
                                        marketingActivity.setActivityLabel(language.getActivityLabel());
                                        marketingActivity.setActivityName(language.getActivityName());
                                    }
                                }
                            }
							marketingActivity.setProducts(makeProductResult(model.getProducts()));
							marketingActivity.setMarketingGroup(copyMarketingGroup(model.getMarketingGroupMode()));
                            marketingActivity.setBoostSharing(BeanCopyUtils.jsonCopyBean(model.getBoostSharingModel(), BoostSharingResult.class));
                            marketingActivityList.add(marketingActivity);
                        }
                    }
                    if(productListResult.getProductCode().equals(product.getProductCode())){
                        productListResult.setMarketingActivityList(marketingActivityList);
                    }
                }

                for (QueryMarketingActivityListByProductListResult.SkuActivityList skuActivityList : productListResult.getSkuList()) {
                    if (CollectionUtils.isNotEmpty(productsBySku) && CollectionUtils.isNotEmpty(skuList)) {

                        List<FlashSaleProductModel> skuFlashSale = productsBySku.stream().filter(x -> x.getSkuCode().equals(skuActivityList.getSkuCode())).collect(Collectors.toList());
                        Map<String, CacheFlashSaleModel> skuMap = marketingCacheComponent.filterActivityByProduct(copyMap, productCodes, skuFlashSale, SelectorProductTypeEnum.SELECT_SKU.code());
                        List<CacheFlashSaleModel> skuMarketing = new ArrayList<>(skuMap.values());
                        skuMarketing = getGroupMarketing(param.getTenantCode(), skuMarketing, marketingGroupModels);
                        skuMarketing = getBoostSharing(param.getTenantCode(), skuMarketing, boostSharingModels);

                        List<QueryMarketingActivityListByProductListResult.MarketingActivity> marketingActivityList = new ArrayList<>();
                        for (CacheFlashSaleModel model : skuMarketing) {
                            String selectProductType = model.getSelectProductType();
                            if (SelectorProductTypeEnum.SELECT_SKU.code().equals(selectProductType)) {
                                QueryMarketingActivityListByProductListResult.MarketingActivity marketingActivity = BeanCopyUtils.jsonCopyBean(model, QueryMarketingActivityListByProductListResult.MarketingActivity.class);
                                //设置价格，从products中获取sku相同的价格，如果为空，则为空
                                Optional<QueryMarketingActivityListByProductListResult.ProductPrice> first = marketingActivity.getProducts().stream()
                                        .filter(x -> x.getSkuCode().equals(skuActivityList.getSkuCode())).findFirst();
                                marketingActivity.setFlashPrice(first.map(QueryMarketingActivityListByProductListResult.ProductPrice::getFlashPrice).orElse(BigDecimal.ZERO));
                                List<MarketingLanguageModel> languages = model.getLanguages();
                                marketingActivity.setLanguageResults(makeLanguageResult(languages));
                                for (MarketingLanguageModel language : languages) {
                                    if (StringUtil.isNotEmpty(param.getLanguage())) {
                                        if (language.getLanguage().equalsIgnoreCase(param.getLanguage()) && StringUtil.isNotBlank(language.getActivityName())) {
                                            marketingActivity.setActivityLabel(language.getActivityLabel());
                                            marketingActivity.setActivityName(language.getActivityName());
                                        }
                                    } else {
                                        if (language.getLanguage().equalsIgnoreCase(LangTypeStandardEnum.CN.code())&& StringUtil.isNotBlank(language.getActivityName())) {
                                            marketingActivity.setActivityLabel(language.getActivityLabel());
                                            marketingActivity.setActivityName(language.getActivityName());
                                        }
                                    }
                                }
								marketingActivity.setProducts(makeProductResult(model.getProducts()));
								marketingActivity.setMarketingGroup(copyMarketingGroup(model.getMarketingGroupMode()));
                                marketingActivity.setBoostSharing(BeanCopyUtils.jsonCopyBean(model.getBoostSharingModel(), BoostSharingResult.class));
                                marketingActivityList.add(marketingActivity);
                            }
                        }
                        skuActivityList.setMarketingActivityList(marketingActivityList);
                    }
                }
            }
        }
    }

	private List<ProductPrice> makeProductResult(List<FlashSaleProductModel> products) {
		if (CollectionUtils.isEmpty(products)) {
			return Collections.emptyList();
		}
		
		List<ProductPrice> priceList = new ArrayList<>();
		for (FlashSaleProductModel model : products) {
			ProductPrice productPrice = new ProductPrice();
			productPrice.setSkuCode(model.getSkuCode());
			productPrice.setSkuName(model.getSkuName());
			productPrice.setSkuQuota(model.getSkuQuota());
			productPrice.setSkuInventory(model.getSkuInventory());
			productPrice.setMaxPerUser(model.getMaxPerUser());
			productPrice.setListPrice(model.getListPrice());
			productPrice.setSalePrice(model.getSalePrice());
			productPrice.setFlashPrice(model.getFlashPrice());
			productPrice.setGroupLeaderPrice(model.getGroupLeaderPrice());
			productPrice.setLimitFlag(model.getLimitFlag());
			productPrice.setProductCode(model.getProductCode());
			productPrice.setSpuName(model.getSpuName());
			productPrice.setExtendParam(model.getExtendParam());
			productPrice.setMaxPerUserFlag(model.getMaxPerUserFlag());
			priceList.add(productPrice);
		}
		return priceList;
	}

	private MarketingGroupResult copyMarketingGroup(MarketingGroupMode model) {
		if (model == null) {
			return null;
		}
		MarketingGroupResult marketingGroupResult = new MarketingGroupResult();
		marketingGroupResult.setAutoGroupFlag(model.getAutoGroupFlag());
		marketingGroupResult.setCloseFlag(model.getCloseFlag());
		marketingGroupResult.setEffectiveHour(model.getEffectiveHour());
		marketingGroupResult.setGroupSize(model.getGroupSize());
		marketingGroupResult.setLeaderBenefits(model.getLeaderBenefits());
		marketingGroupResult.setShowLeaderPrice(model.getShowLeaderPrice());
		return marketingGroupResult;
	}

	private List<MarketingLanguageResult> makeLanguageResult(List<MarketingLanguageModel> languages) {
		if (CollectionUtils.isEmpty(languages)) {
			return Collections.emptyList();
		}
		List<MarketingLanguageResult> list = new ArrayList<>();
		for (MarketingLanguageModel model : languages) {
			MarketingLanguageResult marketingLanguageResult = new MarketingLanguageResult();
			list.add(marketingLanguageResult);
			marketingLanguageResult.setActivityDesc(model.getActivityDesc());
			marketingLanguageResult.setActivityLabel(model.getActivityLabel());
			marketingLanguageResult.setActivityName(model.getActivityName());
			marketingLanguageResult.setActivityShortDesc(model.getActivityShortDesc());
			marketingLanguageResult.setLanguage(model.getLanguage());
		}
		return list;
	}

	private ProductCodes makeProductCodes(QueryMarketingActivityListByProductListParam.Product product) {
		ProductCodes productCodes = new ProductCodes();
		productCodes.setAttributes(product.getAttributes());
		productCodes.setBrandCode(product.getBrandCode());
		productCodes.setCategoryCodes(product.getCategoryCodes());
		productCodes.setCombineSkuCode(product.getCombineSkuCode());
		productCodes.setProductCode(product.getProductCode());
		productCodes.setProductTag(product.getProductTag());
		productCodes.setSkuCode(product.getSkuCode());
		productCodes.setSpuAttributes(product.getSpuAttributes());
		return productCodes;
	}

    public List<CacheFlashSaleModel> getGroupMarketing(String tenantCode, List<CacheFlashSaleModel> models, List<MarketingGroupEntity> marketingGroupEntities){
        if (CollectionUtils.isEmpty(marketingGroupEntities)){
            return models;
        }
        //给models中对应的活动添加拼团信息
        models.forEach(x->{
            List<MarketingGroupEntity> collect = marketingGroupEntities.stream().filter(y -> y.getActivityCode().equals(x.getActivityCode())).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(collect)){
				MarketingGroupEntity marketingGroupEntity = collect.get(0);
				MarketingGroupMode marketingGroupMode = new MarketingGroupMode();
				marketingGroupMode.setShowLeaderPrice(marketingGroupEntity.getShowLeaderPrice());
				marketingGroupMode.setEffectiveHour(marketingGroupEntity.getEffectiveHour());
				marketingGroupMode.setGroupSize(marketingGroupEntity.getGroupSize());
				marketingGroupMode.setAutoGroupFlag(marketingGroupEntity.getAutoGroupFlag());
				marketingGroupMode.setCreateUser(marketingGroupEntity.getCreateUser());
				marketingGroupMode.setUpdateUser(marketingGroupEntity.getUpdateUser());
				marketingGroupMode.setLeaderBenefits(marketingGroupEntity.getLeaderBenefits());
				marketingGroupMode.setCloseFlag(marketingGroupEntity.getCloseFlag());
				marketingGroupMode.setDomainCode(marketingGroupEntity.getDomainCode());
				marketingGroupMode.setTenantCode(marketingGroupEntity.getTenantCode());
				marketingGroupMode.setOrgCode(marketingGroupEntity.getOrgCode());
				marketingGroupMode.setActivityCode(marketingGroupEntity.getActivityCode());
				x.setMarketingGroupMode(marketingGroupMode);
            }
        });
        return models;
    }


    public List<CacheFlashSaleModel> getBoostSharing(String tenantCode, List<CacheFlashSaleModel> models, List<BoostSharingModel> boostSharingModels){
        if (CollectionUtils.isEmpty(boostSharingModels)){
            return models;
        }
        //给models中对应的活动添加拼团信息
        models.forEach(x->{
            List<BoostSharingModel> collect = boostSharingModels.stream().filter(Objects::nonNull).filter(y -> y.getActivityCode().equals(x.getActivityCode())).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(collect)){
				x.setBoostSharingModel(collect.get(0));
            }
        });
        return models;
    }


    public List<QueryPromotionOrMarketingNoFilterResult> queryPromotionOrMarketingNoFilter(QueryPromotionOrMarketingNoFilterParam param) {

        List<QueryPromotionOrMarketingNoFilterParam.Product> productList = param.getProductList();
        HashMap<String, HashMap<String, List<String>>> activitySpuOrSkuMap = new HashMap<>();

        //该商户已审核成功的活动
        Map<String, CacheFlashSaleModel> marketingCacheMap = marketingCacheComponent.getFlashSaleCacheMapByActivityTypeList(param.getTenantCode(), param.getLanguage(), param.getActivityTypeList());
        Map<String, ActivityCacheDTO> promotionCacheMap = activityCacheDomain.getActivityCacheMap(param.getTenantCode(), param.getLanguage(), 2, null);

        marketingCacheMap = MarketingFilterUtil.filterActivityByOrgCodes(marketingCacheMap, param.getProductList().stream().map(QueryPromotionOrMarketingNoFilterParam.Product::getOrgCode).collect(Collectors.toList()));
        promotionCacheMap = ActivityFilterUtil.filterActivityByOrgCodes(promotionCacheMap, param.getProductList().stream().map(QueryPromotionOrMarketingNoFilterParam.Product::getOrgCode).collect(Collectors.toList()));

        //查询营销
        queryMarketingNoFilter(param, productList, activitySpuOrSkuMap,marketingCacheMap);
        //查询促销
        queryPromotionNoFilter(param, productList, activitySpuOrSkuMap,promotionCacheMap);

        //组装返回值
        return assemblyReturnValue(productList, activitySpuOrSkuMap, marketingCacheMap, promotionCacheMap);
    }

    public List<QueryPromotionOrMarketingNoFilterResult> assemblyReturnValue(List<QueryPromotionOrMarketingNoFilterParam.Product> productList, Map<String, HashMap<String, List<String>>> activitySpuOrSkuMap, Map<String, CacheFlashSaleModel> marketingCacheMap, Map<String, ActivityCacheDTO> promotionCacheMap) {
        //去重
        activitySpuOrSkuMap.forEach((k, v) ->
            v.forEach((k1, v1) ->
                v.put(k1, v1.stream().distinct().collect(Collectors.toList()))
            )
        );

        ArrayList<QueryPromotionOrMarketingNoFilterResult> results = new ArrayList<>();
        productList.forEach(product -> {
            QueryPromotionOrMarketingNoFilterResult result = new QueryPromotionOrMarketingNoFilterResult();
            result.setProductCode(product.getProductCode());
            result.setSkuCode(product.getSkuCode());

            //获取这个sku所有的营销活动
            ArrayList<String> marketingCode = new ArrayList<>(marketingCacheMap.keySet());
            marketingCode.forEach(code -> {
                if (this.isContainsSpuOrSku(activitySpuOrSkuMap, code, SelectorProductTypeEnum.SELECT_SPU, product.getProductCode())
                        || this.isContainsSpuOrSku(activitySpuOrSkuMap, code, SelectorProductTypeEnum.SELECT_SKU, product.getSkuCode())) {
                    this.resultSetMarketingActivity(marketingCacheMap, result, code);
                }
            });

            //获取这个sku所有的促销活动
            ArrayList<String> promotionCode = new ArrayList<>(promotionCacheMap.keySet());
            promotionCode.forEach(code -> {
                if (this.isContainsSpuOrSku(activitySpuOrSkuMap, code, SelectorProductTypeEnum.SELECT_SPU, product.getProductCode())
                   || this.isContainsSpuOrSku(activitySpuOrSkuMap, code, SelectorProductTypeEnum.SELECT_SKU, product.getSkuCode())) {
                    this.resultSetPromotionActivity(promotionCacheMap, result, code);
                }
            });
            results.add(result);
        });
        return results;
    }

    public boolean isContainsSpuOrSku(Map<String, HashMap<String, List<String>>> activitySpuOrSkuMap, String code, SelectorProductTypeEnum selectSpu, String product) {
        return activitySpuOrSkuMap.containsKey(code)
                && activitySpuOrSkuMap.get(code).containsKey(selectSpu.code())
                && activitySpuOrSkuMap.get(code).get(selectSpu.code()).contains(product);
    }

    public void resultSetMarketingActivity(Map<String, CacheFlashSaleModel> marketingCacheMap, QueryPromotionOrMarketingNoFilterResult result, String code) {
        MarketingQueryResult marketingQueryResult = BeanCopyUtils.jsonCopyBean(marketingCacheMap.get(code), MarketingQueryResult.class);
        if (CollectionUtils.isEmpty(result.getMarketingQueryResults())) {
            result.setMarketingQueryResults(Lists.newArrayList(marketingQueryResult));
        }else if (!result.getMarketingQueryResults().contains(marketingQueryResult)) {
            result.getMarketingQueryResults().add(marketingQueryResult);
        }
    }


    public void resultSetPromotionActivity(Map<String, ActivityCacheDTO> promotionCacheMap, QueryPromotionOrMarketingNoFilterResult result, String code) {
        QueryActivityListByProductResult promotionQueryResult = BeanCopyUtils.jsonCopyBean(promotionCacheMap.get(code).getActivityModel(), QueryActivityListByProductResult.class);
        //如果不存在就新增,存在就追加
        if (CollectionUtils.isEmpty(result.getActivityListResults())) {
            result.setActivityListResults(Lists.newArrayList(promotionQueryResult));
        } else if (!result.getActivityListResults().contains(promotionQueryResult)) {
            result.getActivityListResults().add(promotionQueryResult);
        }
    }


    public void queryPromotionNoFilter(QueryPromotionOrMarketingNoFilterParam param, List<QueryPromotionOrMarketingNoFilterParam.Product> productList, HashMap<String, HashMap<String, List<String>>> activitySpuOrSkuMap,Map<String, ActivityCacheDTO> activityCacheMap){


        //1.根据活动类型区分,查询不同的表
        List<String> productCodes = param.getProductList().stream().map(QueryPromotionOrMarketingNoFilterParam.Product::getProductCode).collect(Collectors.toList());
        List<ProductSkuDetailDTO> productSkuDetailDTOS = productDetailService
                .queryListByActivityCodesAndProductCodes(activityCacheMap.keySet(), productCodes);

        for (QueryPromotionOrMarketingNoFilterParam.Product product : param.getProductList()) {
            ProductCodes productDTO = new ProductCodes();
            productDTO.setCombineSkuCode(product.getCombineSkuCode());
            productDTO.setAttributes(product.getAttributes());
            productDTO.setCategoryCodes(product.getCategoryCodes());
            productDTO.setBrandCode(product.getBrandCode());
            String productCode = product.getProductCode();
            String skuCode = product.getSkuCode();
            productDTO.setProductCode(productCode);
            productDTO.setSkuCode(skuCode);
            // 商品校验
            Map<String, ActivityCacheDTO> stringActivityCacheDTOMap = activityCacheDomain.filterActivityByProduct(activityCacheMap, productDTO,
                    productSkuDetailDTOS);
            for (ActivityCacheDTO cacheDTO : stringActivityCacheDTOMap.values()) {

                HashMap<String, List<String>> activityMap = activitySpuOrSkuMap.computeIfAbsent(cacheDTO.getActivityModel().getActivityCode(), (key) -> new HashMap<>());
                List<String> spuList = activityMap.computeIfAbsent(SelectorProductTypeEnum.SELECT_SKU.code(), (key) -> new ArrayList<>());
                spuList.add(productDTO.getSkuCode());




            }
        }
    }



    public void queryMarketingNoFilter(QueryPromotionOrMarketingNoFilterParam param, List<QueryPromotionOrMarketingNoFilterParam.Product> productList, HashMap<String, HashMap<String, List<String>>> activitySpuOrSkuMap,Map<String, CacheFlashSaleModel> activityCacheMap) {

        //1.根据活动类型区分,查询不同的表
        Map<String, List<CacheFlashSaleModel>> byActivityType = activityCacheMap.values().stream().collect(Collectors.groupingBy(CacheFlashSaleModel::getActivityType, Collectors.toList()));


        /*List<CacheFlashSaleModel> luckyDraw = byActivityType.get(ActivityTypeEnum.LUCKY_DRAW.code())
        List<CacheFlashSaleModel> flashSale = byActivityType.get(ActivityTypeEnum.FLASH_SALE.code())
        List<CacheFlashSaleModel> preSale = byActivityType.get(ActivityTypeEnum.PRE_SALE.code())
        List<CacheFlashSaleModel> group = byActivityType.get(ActivityTypeEnum.GROUP.code())
        List<CacheFlashSaleModel> boostSharding = byActivityType.get(ActivityTypeEnum.BOOST_SHARDING.code())*/


        //抽奖: lucky_draw_rules 仅有product类型
        //
        //预售: flash_sale_product  product或者skuCode
        //
        //秒杀: flash_sale_product  仅有sku类型
        //
        //拼团: flash_sale_product  product或者skuCode
        //
        //分享助力: marketing_boost_sharing    仅有product类型


        //activityCacheMap去除 属性selectPruductType为空的数据
        activityCacheMap = activityCacheMap.entrySet().stream().filter(x -> StringUtil.isNotEmpty(x.getValue().getSelectProductType())).collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));


        //<活动Code,<商品类型指定Spu或者Sku,SpuCode或者SkuCode列表>>
        Map<String, CacheFlashSaleModel> finalActivityCacheMap = activityCacheMap;
        byActivityType.forEach((k, v)->{

            switch (Objects.requireNonNull(ActivityTypeProductTableEnum.getTableName(k))){
                case "lucky_draw_rules":
                    //抽奖: lucky_draw_rules 仅有product类型
                    List<LuckyDrawRuleModel> luckyDrawRuleModels = luckyDrawRuleService.queryLuckyDrawRulesByActivityCode(param.getTenantCode(), v.stream().map(CacheFlashSaleModel::getActivityCode).collect(Collectors.toList()));
                    List<LuckyDrawRuleModel> drawRuleModelsBySpu = luckyDrawRuleModels.stream().filter(x -> param.getProductList().stream().map(y -> y.getProductCode()).collect(Collectors.toList()).contains(x.getProductCode())).collect(Collectors.toList());
                    for (LuckyDrawRuleModel luckyDrawRuleModel : drawRuleModelsBySpu) {
                        //如果已经存在,则追加,否则新增
                        HashMap<String, List<String>> activityMap = activitySpuOrSkuMap.computeIfAbsent(luckyDrawRuleModel.getActivityCode(), (key) -> new HashMap<>());
                        List<String> spuList = activityMap.computeIfAbsent(SelectorProductTypeEnum.SELECT_SPU.code(), (key) -> new ArrayList<>());
                        spuList.add(luckyDrawRuleModel.getProductCode());
                    }
                    break;
                case "flash_sale_product":
                    //预售: flash_sale_product  product或者skuCode
                    //秒杀: flash_sale_product  仅有sku类型
                    //拼团: flash_sale_product  product或者skuCode
                    //2.根据活动商品类型区分,查询不同的维度
                    Map<String, List<CacheFlashSaleModel>> bySelectProductType = finalActivityCacheMap.values().stream().collect(Collectors.groupingBy(CacheFlashSaleModel::getSelectProductType, Collectors.toList()));
                    List<CacheFlashSaleModel> bySpu = bySelectProductType.get(SelectorProductTypeEnum.SELECT_SPU.code());
                    List<CacheFlashSaleModel> bySku = bySelectProductType.get(SelectorProductTypeEnum.SELECT_SKU.code());
                    if (CollectionUtils.isNotEmpty(bySpu)){
                        List<FlashSaleProductModel> productsByActivityCodesAndProductsBySpu = flashSaleProductService.getProductsByActivityCodesAndProductsBySpu(param.getTenantCode(),
                                bySpu.stream().map(CacheFlashSaleModel::getActivityCode).collect(Collectors.toList()),
                                productList.stream().filter(x -> StringUtil.isNotEmpty(x.getProductCode())).map(QueryPromotionOrMarketingNoFilterParam.Product::getProductCode).collect(Collectors.toList()));
                        //Spu
                        for (FlashSaleProductModel flashSaleProductModel : productsByActivityCodesAndProductsBySpu) {
                            //如果已经存在,则追加,否则新增
                            HashMap<String, List<String>> activityMap = activitySpuOrSkuMap.computeIfAbsent(flashSaleProductModel.getActivityCode(), (key) -> new HashMap<>());
                            List<String> spuList = activityMap.computeIfAbsent(SelectorProductTypeEnum.SELECT_SPU.code(), (key) -> new ArrayList<>());
                            spuList.add(flashSaleProductModel.getProductCode());
                        }

                    }
                    if (CollectionUtils.isNotEmpty(bySku)){
                        List<FlashSaleProductModel> productsByActivityCodesAndProductsBySku = flashSaleProductService.getProductsByActivityCodesAndProducts(param.getTenantCode(),
                                bySku.stream().map(CacheFlashSaleModel::getActivityCode).collect(Collectors.toList()),
                                productList.stream().filter(x -> StringUtil.isNotEmpty(x.getProductCode())).map(QueryPromotionOrMarketingNoFilterParam.Product::getSkuCode).collect(Collectors.toList()));
                        //Sku
                        for (FlashSaleProductModel flashSaleProductModel : productsByActivityCodesAndProductsBySku) {
                            //如果已经存在,则追加,否则新增
                            HashMap<String, List<String>> activityMap = activitySpuOrSkuMap.computeIfAbsent(flashSaleProductModel.getActivityCode(), (key) -> new HashMap<>());
                            List<String> spuList = activityMap.computeIfAbsent(SelectorProductTypeEnum.SELECT_SKU.code(), (key) -> new ArrayList<>());
                            spuList.add(flashSaleProductModel.getSkuCode());
                        }
                    }
                    break;
                case "marketing_boost_sharing":
                    //分享助力: marketing_boost_sharing    仅有product类型
                    List<FlashSaleProductModel> boostSharingProductsByActivityCodesAndProducts = boostSharingService.getBoostSharingProductsByActivityCodesAndProducts(param.getTenantCode(),
                            (ArrayList<String>) v.stream().map(CacheFlashSaleModel::getActivityCode).collect(Collectors.toList()),
                            productList.stream().map(QueryPromotionOrMarketingNoFilterParam.Product::getProductCode).collect(Collectors.toList()));

                    //Spu
                    for (FlashSaleProductModel flashSaleProductModel : boostSharingProductsByActivityCodesAndProducts) {
                        //如果已经存在,则追加,否则新增
                        HashMap<String, List<String>> activityMap = activitySpuOrSkuMap.computeIfAbsent(flashSaleProductModel.getActivityCode(), (key) -> new HashMap<>());
                        List<String> spuList = activityMap.computeIfAbsent(SelectorProductTypeEnum.SELECT_SPU.code(), (key) -> new ArrayList<>());
                        spuList.add(flashSaleProductModel.getProductCode());
                    }

                    break;
                default:
                    break;
            }
        });


        log.info("activitySpuOrSkuMap:{}",JSON.toJSONString(activitySpuOrSkuMap));
    }


    public List<ExportFlashPreSaleResult> exportFlashOrPreSaleDetail(ExportFlashPreSaleDto dto) {

        List<FlashPreSaleOrderModel> flashSaleOrderModels = flashSaleOrderService.queryOrderByCondition(dto);

        String maxOrderCode = null;
        if (!CollectionUtil.isEmpty(flashSaleOrderModels)){
            maxOrderCode = flashSaleOrderModels.get(flashSaleOrderModels.size() - 1).getOrderId();
        }


        Map<String, List<FlashPreSaleOrderModel>> dateListMap = flashSaleOrderModels.stream().collect(Collectors.groupingBy(FlashPreSaleOrderModel::getDate));

        Iterator<String> iterator = dateListMap.keySet().iterator();

        List<ExportFlashPreSaleResult> results = new ArrayList<>();
        while (iterator.hasNext()){

            ExportFlashPreSaleResult result = new ExportFlashPreSaleResult();
            String next = iterator.next();

            List<FlashPreSaleOrderModel> modelList = dateListMap.get(next);
            List<String> orderList = modelList.stream().map(FlashPreSaleOrderModel::getOrderId).collect(Collectors.toList());
            long cancelOrderCount = modelList.stream().filter(x -> x.getOrderStatus().equals(OrderStatusEnum.CANCELED.code())).count();

            List<OrderOutMode> orderOutModes = getOrderOutModes(orderList, dto.getTenantCode());

            long count = 0L;
            BigDecimal orderAmount = BigDecimal.ZERO;
            BigDecimal orderPayAmount = BigDecimal.ZERO;
            for (OrderOutMode orderOutMode : orderOutModes) {
                orderAmount = orderAmount.add(orderOutMode.getAmount());
                if ("1".equals(orderOutMode.getPayStatus())){
                    orderPayAmount = orderPayAmount.add(orderOutMode.getAmount());
                    count++;
                }
            }

            result.setOfCancelledOrders(cancelOrderCount);
            result.setOfPaidOrders(count);
            result.setAmountOfPaidOrders(orderPayAmount.doubleValue());
            result.setTotalOfOrders(modelList.size());
            result.setDate(next);
            result.setTotalAmount(orderAmount.doubleValue());
            result.setMaxOrderCode(maxOrderCode);
            results.add(result);
        }

        return results;
    }

    public List<ExportFlashPreProductResult> exportFlashOrPreSaleProductDetail(ExportFlashPreSaleDto dto) {
        dto.setOrderStatus(OrderStatusEnum.PAID.code());

        List<FlashPreSaleOrderModel> flashSaleOrderModels = flashSaleOrderService.queryOrderByCondition(dto);

        String maxOrderCode = null;
        if (!CollectionUtil.isEmpty(flashSaleOrderModels)){
            maxOrderCode = flashSaleOrderModels.get(flashSaleOrderModels.size() - 1).getOrderId();
        }

        Map<String, List<FlashPreSaleOrderModel>> dateListMap = flashSaleOrderModels.stream()
                .collect(Collectors.groupingBy(FlashPreSaleOrderModel::getDate));

        Iterator<String> iterator = dateListMap.keySet().iterator();

        List<ExportFlashPreProductResult> results = new ArrayList<>();
        while (iterator.hasNext()) {

            String next = iterator.next();

            List<FlashPreSaleOrderModel> modelList = dateListMap.get(next);

            List<String> collect = modelList.stream().map(FlashPreSaleOrderModel::getOrderId).collect(Collectors.toList());

            List<FlashSaleOrderDetailModel> productList = flashSaleOrderDetailService.findByOrderNoList(dto.getTenantCode(), collect);

            Map<String, List<FlashSaleOrderDetailModel>> productMap = productList.stream().filter(x -> null != x.getProductCode()).collect(Collectors.groupingBy(FlashSaleOrderDetailModel::getProductCode));

            List<String> productCodeList = productList.stream().map(FlashSaleOrderDetailModel::getProductCode).collect(Collectors.toList());
            ProductCodeRequest request = new ProductCodeRequest();

            request.setTenantCode(dto.getTenantCode());
            request.setProductCodeList(productCodeList);

            JSONObject jsonObject = pimFeignClient.querySkuWithProductList(request);

            JSONArray data = jsonObject.getJSONArray("data");

            for (Object datum : data) {

                JSONObject jsonObject1 = JSONObject.parseObject(JSON.toJSONString(datum));
               if (null == jsonObject1){
                   continue;
               }
                ExportFlashPreProductResult result = new ExportFlashPreProductResult();

                result.setMaxOrderCode(maxOrderCode);
                String productName = jsonObject1.getString("productName");
                String productNo = jsonObject1.getString("productNo");
                String productCode = jsonObject1.getString("productCode");

                result.setProductName(productName);
                result.setProductNumber(productNo);

                List<FlashSaleOrderDetailModel> detailModelList = productMap.get(productCode);
                List<String> collect1 = detailModelList.stream().map(FlashSaleOrderDetailModel::getOrderId).distinct().collect(Collectors.toList());

                List<OrderOutMode> orderOutModes = getOrderOutModes(collect1, dto.getTenantCode());

                long count = 0L;
                BigDecimal orderAmount = BigDecimal.ZERO;
                BigDecimal orderPayAmount = BigDecimal.ZERO;

                Integer payProductCount = 0;

                BigDecimal payProductAmount = BigDecimal.ZERO;

                for (OrderOutMode orderOutMode : orderOutModes) {
                    orderAmount = orderAmount.add(orderOutMode.getAmount());
                    if ("1".equals(orderOutMode.getPayStatus())){
                        orderPayAmount = orderPayAmount.add(orderOutMode.getAmount());
                        count++;
                        List<OrderProductOutMode> orderProductOutList = orderOutMode.getOrderProductOutList();
                        for (OrderProductOutMode orderProductOutMode : orderProductOutList) {
                            payProductCount += orderProductOutMode.getCount();
                            payProductAmount = payProductAmount.add(orderProductOutMode.getAmount());
                        }
                    }
                }

                result.setOfPaidProducts(payProductCount);

                result.setOfPaidOrders(count);
                result.setAmountOfPaidProducts(payProductAmount.doubleValue());
                result.setTimeOfStatistics(next);
                results.add(result);

            }

        }
        List<ExportFlashPreProductResult> collect = results.stream().sorted(Comparator.comparing(ExportFlashPreProductResult::getTimeOfStatistics)).collect(Collectors.toList());
        return collect;
    }

    public List<OrderOutMode> getOrderOutModes(List<String> orderList, String tenantCode) {

        OrderQueryIn orderQueryIn = new OrderQueryIn();
        orderQueryIn.setTenantCode(tenantCode);
        orderQueryIn.setOrderCodes(orderList);
        orderQueryIn.setIsOmniChannel("0");
        orderQueryIn.setPageSize(orderList.size());
        JsonResult<ResponsePage<OrderQueryOut>> orderQueryOutJsonResult = orderFeignClient.queryOrderList(orderQueryIn);
        List<OrderOutMode> orderOutModes = new ArrayList<>();
        List<OrderQueryOut> data2 = orderQueryOutJsonResult.getData().getList();
        if (CollectionUtil.isNotEmpty(data2)) {
            List<OrderOutMode> outModes = BeanCopyUtils.jsonCopyList(data2, OrderOutMode.class);
            orderOutModes.addAll(outModes);
        }
        return orderOutModes;


    }

    public List<ExportGroupDetailResult> exportGroupData(ExportGroupDto dto) {

        List<String> groupStatusList = Arrays.asList(MarketingGroupStatusEnum.GROUP_NO_START.code(),
                MarketingGroupStatusEnum.GROUP_PROCESSING.code(),
                MarketingGroupStatusEnum.GROUP_SUCCESS.code(),
                MarketingGroupStatusEnum.GROUP_FAIL.code());

        List<MarketingGroupCodeMode> groupCodeModeList = marketingGroupCodeService.queryGroupByActivityCode(dto.getTenantCode(), dto.getActivityCode(),groupStatusList,dto.getMaxGroupCode());


        Map<String, List<MarketingGroupCodeMode>> dateGroupMap = groupCodeModeList.stream().collect(Collectors.groupingBy(MarketingGroupCodeMode::getDate));
        Iterator<String> iterator = dateGroupMap.keySet().iterator();

        List<ExportGroupDetailResult> resultList = new ArrayList<>();

        while (iterator.hasNext()){

            ExportGroupDetailResult result = new ExportGroupDetailResult();
            String next = iterator.next();
            result.setDate(next);

            List<MarketingGroupCodeMode> list = dateGroupMap.get(next);

            List<String> collect = list.stream().map(MarketingGroupCodeMode::getMarketingGroupCode).collect(Collectors.toList());
            List<MarketingGroupCodeMode> processing = list.stream().filter(x -> x.getGroupStatus().equals(MarketingGroupStatusEnum.GROUP_PROCESSING.code())).collect(Collectors.toList());
            List<MarketingGroupCodeMode> success = list.stream().filter(x -> x.getGroupStatus().equals(MarketingGroupStatusEnum.GROUP_SUCCESS.code())).collect(Collectors.toList());
            List<MarketingGroupCodeMode> fail = list.stream().filter(x -> x.getGroupStatus().equals(MarketingGroupStatusEnum.GROUP_FAIL.code())).collect(Collectors.toList());

            Integer size = processing.size() + success.size();

            GroupUserCountDto userCountDto = new GroupUserCountDto();

            userCountDto.setTenantCode(dto.getTenantCode());
            userCountDto.setActivityCode(dto.getActivityCode());
            userCountDto.setGroupCodeList(collect);
            userCountDto.setTeamLeaderList(Arrays.asList(TeamLeaderEnum.MEMBER.code()));
            userCountDto.setGroupStatusList(Arrays.asList(UserGroupStatusEnum.PROCESSING.code(),UserGroupStatusEnum.FINISH.code()));

            int ofParticipants = marketingGroupUserService.countOfParticipants(userCountDto);

            result.setOfInitiators(size);
            result.setOfNewGroups(size);
            result.setOfSuccessfulGroups(success.size());
            result.setOfFailingGroups(fail.size());
            result.setOfParticipants(ofParticipants);

            List<FlashPreSaleOrderModel> modelList = flashSaleOrderService.queryOrderByMarketingGroupCodeList(dto.getTenantCode(), dto.getActivityCode(), collect);

            List<String> orderCodeList = modelList.stream().map(FlashPreSaleOrderModel::getOrderId).collect(Collectors.toList());

            List<OrderOutMode> orderOutModes = getOrderOutModes(orderCodeList, dto.getTenantCode());

            Integer count = 0;
            BigDecimal orderAmount = BigDecimal.ZERO;
            BigDecimal orderPayAmount = BigDecimal.ZERO;

            for (OrderOutMode orderOutMode : orderOutModes) {
                orderAmount = orderAmount.add(orderOutMode.getAmount());
                orderPayAmount = orderPayAmount.add(orderOutMode.getAmount());
                count++;

            }
            result.setTotalOfOrders(count);
            result.setTotalAmountOfOrders(orderPayAmount);
            result.setMaxGroupCode(groupCodeModeList.get(groupCodeModeList.size() -1).getMarketingGroupCode());
            resultList.add(result);
        }

        return resultList;

    }
}
