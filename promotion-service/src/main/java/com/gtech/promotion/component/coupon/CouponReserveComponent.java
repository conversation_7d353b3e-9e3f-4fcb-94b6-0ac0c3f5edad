package com.gtech.promotion.component.coupon;

import com.gtech.commons.exception.Exceptions;
import com.gtech.commons.utils.BeanCopyUtils;
import com.gtech.commons.utils.CheckUtils;
import com.gtech.promotion.checker.activity.TPromoOrderChecker;
import com.gtech.promotion.checker.coupon.CouponActivityChecker;
import com.gtech.promotion.code.activity.ActivityTagCodeEnum;
import com.gtech.promotion.code.activity.CouponReverseStatusEnum;
import com.gtech.promotion.code.activity.GiveawayTypeEnum;
import com.gtech.promotion.code.coupon.TakeLabelEnum;
import com.gtech.promotion.dao.entity.activity.TPromoActivityIncentiveEntity;
import com.gtech.promotion.dao.model.activity.ActivityModel;
import com.gtech.promotion.dao.model.activity.GiveawayVO;
import com.gtech.promotion.dao.model.activity.TPromoOrderVO;
import com.gtech.promotion.domain.coupon.CouponReleaseDomain;
import com.gtech.promotion.dto.in.coupon.ReserveCouponDto;
import com.gtech.promotion.dto.in.coupon.ReserveCouponQuotaDto;
import com.gtech.promotion.exception.Check;
import com.gtech.promotion.exception.ErrorCodes;
import com.gtech.promotion.service.activity.GiveawayService;
import com.gtech.promotion.service.activity.TPromoActivityIncentiveService;
import com.gtech.promotion.service.activity.TPromoOrderService;
import com.gtech.promotion.service.coupon.CouponReserveService;
import com.gtech.promotion.service.coupon.PromoCouponActivityService;
import com.gtech.promotion.service.coupon.PromoCouponReleaseService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Slf4j
@Component
public class CouponReserveComponent {

    @Autowired
    private TPromoOrderService orderService;

    @Autowired
    private TPromoActivityIncentiveService incentiveService;

    @Autowired
    private GiveawayService giveawayService;

    @Autowired
    private PromoCouponReleaseService couponReleaseService;

    @Autowired
    private PromoCouponActivityService couponActivityService;

    @Autowired
    private CouponReserveService couponReserveService;

    @Autowired
    private CouponInnerCodeComponent couponInnerCodeDomain;

    @Autowired
    private CouponActivityComponent couponActivityDomain;

    @Transactional
    public void reserveCouponQuota(ReserveCouponQuotaDto reserveCouponQuotaDto) {
        String tenantCode = reserveCouponQuotaDto.getTenantCode();
        TPromoOrderVO tPromoOrderVO = orderService.queryOrderBySalesOrderNo(tenantCode, reserveCouponQuotaDto.getOrderNo());
        Check.check(null == tPromoOrderVO, TPromoOrderChecker.NULL_ORDER);

        List<TPromoActivityIncentiveEntity> listByOrderId = incentiveService.getListByOrderId(tenantCode, tPromoOrderVO.getId());

        for (TPromoActivityIncentiveEntity tPromoActivityIncentiveEntity : listByOrderId) {
            if (ActivityTagCodeEnum.GIVEAWAY.code().equals(tPromoActivityIncentiveEntity.getIncentiveType())){
                String activityCode = tPromoActivityIncentiveEntity.getActivityCode();
                List<GiveawayVO> giveawayVOS = giveawayService.getGiftListByActivityCode(tenantCode, activityCode);
                for (GiveawayVO giveawayVO : giveawayVOS) {
                    if (Integer.valueOf(GiveawayTypeEnum.COUPON.code()).equals(giveawayVO.getGiveawayType())){
                        int sum = giveawayVO.getGiveawayNum() * tPromoActivityIncentiveEntity.getIncentiveTimes();
                        if (sum > 0) {
                            checkReserveInventory(reserveCouponQuotaDto, tenantCode, tPromoOrderVO, giveawayVO, sum);
                        }
                    }
                }
            }
        }
    }

    public void checkReserveInventory(ReserveCouponQuotaDto reserveCouponQuotaDto, String tenantCode, TPromoOrderVO tPromoOrderVO, GiveawayVO giveawayVO, int sum) {
        ActivityModel couponActivity = couponActivityService.findCouponActivity(tenantCode, giveawayVO.getGiveawayCode());
        Check.check(null == couponActivity, CouponActivityChecker.ACTIVITY_NOT_EXIST);
        List<CouponReleaseDomain> couponRelease = couponReleaseService.queryCanReceiveReleases(tenantCode, giveawayVO.getGiveawayCode(), null);
        int inventory = 0;
        for (CouponReleaseDomain couponReleaseDomain : couponRelease) {
            inventory += couponReleaseDomain.getInventory();
        }
        if (sum + couponActivity.getReserveInventory() > inventory){
            throw Exceptions.fail(ErrorCodes.COUPON_INVENTORY_NOT_ENOUGH);
        }else{
            couponReserveService.addReserve(ReserveCouponDto.builder()
                    .activityCode(giveawayVO.getGiveawayCode()).tenantCode(tenantCode)
                    .memberCode(tPromoOrderVO.getUserCode())
                    .orderNo(reserveCouponQuotaDto.getOrderNo())
                    .quota(sum).status(Integer.valueOf(CouponReverseStatusEnum.REVERSE.code())).build());
            couponActivityService.reserveCouponQuota(tenantCode, giveawayVO.getGiveawayCode(), sum);
        }
    }

    @Transactional
    public void returnCouponQuota(ReserveCouponQuotaDto reserveCouponQuotaDto) {
        ReserveCouponDto dto = BeanCopyUtils.jsonCopyBean(reserveCouponQuotaDto, ReserveCouponDto.class);
        ReserveCouponDto reserve = couponReserveService.getReserve(dto);
        if (null != reserve && reserve.getStatus().equals(Integer.valueOf(CouponReverseStatusEnum.REVERSE.code()))){
            reserve.setStatus(Integer.valueOf(CouponReverseStatusEnum.RETURN.code()));
            couponReserveService.updateReserve(reserve);
            couponActivityService.returnCouponQuota(reserve.getTenantCode(), reserve.getActivityCode(), reserve.getQuota());
        }
    }

    @Transactional
    public void releaseCouponQuota(ReserveCouponQuotaDto reserveCouponQuotaDto) {
        ReserveCouponDto dto = BeanCopyUtils.jsonCopyBean(reserveCouponQuotaDto, ReserveCouponDto.class);
        ReserveCouponDto reserve = couponReserveService.getReserve(dto);
        if (null != reserve && reserve.getStatus().equals(Integer.valueOf(CouponReverseStatusEnum.REVERSE.code()))){
            couponActivityService.returnCouponQuota(reserve.getTenantCode(), reserve.getActivityCode(), reserve.getQuota());
            reserve.setStatus(Integer.valueOf(CouponReverseStatusEnum.SEND.code()));
            couponReserveService.updateReserve(reserve);

            ActivityModel activityModel = couponActivityDomain.findValidActivity(reserveCouponQuotaDto.getTenantCode(), reserve.getActivityCode(), null, new Date());
            CheckUtils.isNotNull(activityModel, ErrorCodes.VALIDATE_COUPON_ACTIVITY_EXIST);

            // 获取所有可领券的投放
            List<CouponReleaseDomain> couponRelease = couponReleaseService.queryCanReceiveReleases(reserveCouponQuotaDto.getTenantCode(), reserve.getActivityCode(), null);
            if (CollectionUtils.isEmpty(couponRelease)) {
                throw Exceptions.fail(ErrorCodes.COUPON_INVENTORY_NOT_ENOUGH);
            }

            List<String> members = new ArrayList<>();
            members.add(reserve.getMemberCode());
            couponInnerCodeDomain.allocateCoupon(members, reserve.getQuota(), TakeLabelEnum.GIVEAWAY_PROMOTION.code(), activityModel, couponRelease,1,dto.getOrderNo());
        }
    }
}
