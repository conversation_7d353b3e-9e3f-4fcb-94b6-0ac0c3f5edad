/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * <PERSON>ECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.component.coupon;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.gtech.basic.idm.web.vo.result.QueryUserResult;
import com.gtech.commons.exception.Exceptions;
import com.gtech.commons.page.PageData;
import com.gtech.commons.result.Result;
import com.gtech.commons.utils.BeanCopyUtils;
import com.gtech.commons.utils.CheckUtils;
import com.gtech.commons.utils.DateUtil;
import com.gtech.commons.utils.StringUtil;
import com.gtech.promotion.checker.activity.TPromoActivityChecker;
import com.gtech.promotion.checker.coupon.CouponActivityChecker;
import com.gtech.promotion.checker.coupon.CouponAllocateChecker;
import com.gtech.promotion.checker.coupon.CouponQueryChecker;
import com.gtech.promotion.checker.coupon.CouponQueryListChecker;
import com.gtech.promotion.code.coupon.*;
import com.gtech.promotion.component.activity.ActivityComponentDomain;
import com.gtech.promotion.component.activity.ActivityStoreDomain;
import com.gtech.promotion.component.activity.FunctionParamDomain;
import com.gtech.promotion.dao.model.activity.ActivityFunctionParamRankModel;
import com.gtech.promotion.dao.model.activity.ActivityModel;
import com.gtech.promotion.dao.model.activity.FunctionParamModel;
import com.gtech.promotion.dao.model.activity.TemplateModel;
import com.gtech.promotion.dao.model.coupon.CouponInnerRelationVO;
import com.gtech.promotion.dao.model.coupon.CouponReleaseModel;
import com.gtech.promotion.dao.model.coupon.TPromoCouponCodeUserVO;
import com.gtech.promotion.dao.model.coupon.TPromoCouponInnerCodeVO;
import com.gtech.promotion.domain.coupon.CouponDomain;
import com.gtech.promotion.domain.coupon.CouponReleaseDomain;
import com.gtech.promotion.domain.coupon.CouponReleaseInventoryDomain;
import com.gtech.promotion.dto.in.activity.ManagementDataInDTO;
import com.gtech.promotion.dto.in.coupon.*;
import com.gtech.promotion.dto.in.coupon.BindingCouponInDTO.UserAndCoupon;
import com.gtech.promotion.dto.out.coupon.CouponInfoImportOutDTO;
import com.gtech.promotion.dto.out.coupon.CouponInfoQueryOutDTO;
import com.gtech.promotion.dto.out.coupon.ManagementDataOutDTO;
import com.gtech.promotion.exception.Check;
import com.gtech.promotion.exception.ErrorCodes;
import com.gtech.promotion.exception.PromotionException;
import com.gtech.promotion.feign.MessageFeignClient;
import com.gtech.promotion.feign.bean.EngineSendRequest;
import com.gtech.promotion.helper.ActivityRedisHelpler;
import com.gtech.promotion.helper.CouponUtils;
import com.gtech.promotion.helper.RedisLock;
import com.gtech.promotion.page.RequestPage;
import com.gtech.promotion.service.activity.*;
import com.gtech.promotion.service.coupon.PromoCouponActivityService;
import com.gtech.promotion.service.coupon.PromoCouponCodeUserService;
import com.gtech.promotion.service.coupon.PromoCouponInnerCodeService;
import com.gtech.promotion.service.coupon.PromoCouponReleaseService;
import com.gtech.promotion.utils.Constants;
import com.gtech.promotion.utils.ExportCouponUtil;
import com.gtech.promotion.vo.bean.ActivityPeriod;
import com.gtech.promotion.vo.result.coupon.ExportCouponRelationResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.io.IOException;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

@Slf4j
@Component
public class CouponInnerCodeComponent {

    @Autowired
    private PromoCouponInnerCodeService promoCouponInnerCodeService;

    @Autowired
    private PromoCouponCodeUserService promoCouponCodeUserService;

    @Autowired
    private PromoCouponActivityService promoCouponActivityService;

    @Autowired
    private PromoCouponReleaseService promoCouponReleaseService;

    @Autowired
    private ActivityService activityService;

    @Autowired
    private ActivityComponentDomain activityDomain;

    @Autowired
    private CouponActivityComponent couponActivityDomain;

    @Autowired
    private ActivityRedisHelpler redisService;

    @Autowired
    private TemplateService templateService;

    @Autowired
    private ActivityFuncParamService promoActivityFuncParamService;

    @Autowired
    private ActivityFuncRankService promoActivityFuncRankService;

    @Autowired
    private ActivityStoreDomain activityStoreDomain;

    @Autowired
    private FunctionParamDomain functionParamDomain;

    @Autowired
    private CouponInnerCodeComponent couponInnerCodeDomain;
    @Autowired
    private ActivityPeriodService activityPeriodService;

    @Autowired
    private RedisLock redisLock;

    @Autowired
    private CouponCodeUserComponent couponCodeUserComponent;

    @Value("${export.coupon.pageCount:1000}")
    private int pageCount;

    private static final String START_TIME = "createTimeStart";

    private static final String END_TIME = "createTimeEnd";

    @Transactional
    public CouponDomain getCouponInfo(TCouponQueryOrApplyDTO paramDto) {

        TPromoCouponInnerCodeVO couponInnerCode = promoCouponInnerCodeService.findCouponByCouponCode(paramDto.getTenantCode(), paramDto.getCouponCode());
        CheckUtils.isNotNull(couponInnerCode, ErrorCodes.VALIDATE_COUPON_CODE_EXIST);

        TPromoCouponCodeUserVO couponCodeUser;
        if (CouponTypeEnum.ANONYMITY_COUPON.equalsCode(couponInnerCode.getCouponType())) {
            couponCodeUser = BeanCopyUtils.jsonCopyBean(couponInnerCode, TPromoCouponCodeUserVO.class);
            couponCodeUser.setValidEndTime(couponInnerCode.getReceiveEndTime());
            couponCodeUser.setValidStartTime(couponInnerCode.getReceiveStartTime());
        } else {
            couponCodeUser = promoCouponCodeUserService.getUserCouponInfo(paramDto.getTenantCode(), paramDto.getCouponCode(), paramDto.getUserCode());
            CheckUtils.isNotNull(couponCodeUser, ErrorCodes.VALIDATE_COUPON_CODE_EXIST);
        }

        ActivityModel couponActivityModel = couponActivityDomain.findValidActivity(couponInnerCode.getTenantCode(), couponInnerCode.getActivityCode(),
            paramDto.getLanguage(), new Date());
        CheckUtils.isNotNull(couponActivityModel, ErrorCodes.VALIDATE_COUPON_ACTIVITY_EXIST);
        CheckUtils.isTrue(this.activityDomain.isEffectiveActivity(couponActivityModel, new Date()), ErrorCodes.VALIDATE_COUPON_ACTIVITY_EXIST);
        CheckUtils.isTrue(!CouponTypeEnum.PROMOTION_CODE.equalsCode(couponActivityModel.getCouponType()), ErrorCodes.VALIDATE_COUPON_ACTIVITY_EXIST);

        if (StringUtil.isNotBlank(couponCodeUser.getFaceUnit()) && FaceUnitEnum.DISCOUNT.equalsCode(couponCodeUser.getFaceUnit())) {
            ConditionAndFace findConditionAndFace = functionParamDomain.findConditionAndFaceByActivityCode(couponActivityModel.getActivityCode());
            couponCodeUser.setFaceValue(findConditionAndFace.getFaceValue());
        }

        CouponDomain couponInfoFindOutDTO = BeanCopyUtils.jsonCopyBean(couponCodeUser, CouponDomain.class);
        //校验入参
        Check.check(null == couponInfoFindOutDTO, CouponQueryChecker.NOT_EXIST_COUPON);
        couponInfoFindOutDTO.setActivityRemark(couponActivityModel.getActivityRemark());
        couponInfoFindOutDTO.setActivityName(couponActivityModel.getActivityName());
        couponInfoFindOutDTO.setActivityLabel(couponActivityModel.getActivityLabel());
        couponInfoFindOutDTO.setActivityDesc(couponActivityModel.getActivityDesc());
        couponInfoFindOutDTO.setActivityPeriod(BeanCopyUtils.jsonCopyBean(activityPeriodService.findPeriod(couponActivityModel.getTenantCode(), couponActivityModel.getActivityCode()), ActivityPeriod.class));

        //过滤店铺
        Check.check(!activityStoreDomain.checkStore111(paramDto.getOrgCode(), couponInfoFindOutDTO.getActivityCode()), CouponActivityChecker.ACTIVITY_STORE_NOT_EXIST);

        TemplateModel template = templateService.getTemplateByCode(couponActivityModel.getTemplateCode());
        couponInfoFindOutDTO.setRewardType(template.getTemplateCode().substring(template.getTemplateCode().length() - 2));
        List<ActivityFunctionParamRankModel> rankList = promoActivityFuncRankService.getRankListByActivityCode(couponActivityModel.getActivityCode());
        if (!CollectionUtils.isEmpty(rankList)){
            ActivityFunctionParamRankModel rankVO = rankList.get(0);
            //函数参数层级
            String templateCode = template.getTemplateCode().substring(8, 12);
            //填充条件 
            FunctionParamModel param = promoActivityFuncParamService.getRuleFuncParamListByRankIdAndFunctionCode(rankVO.getId(), templateCode);
           if (StringUtil.isNotEmpty(param.getParamValue()) && StringUtil.isNotEmpty(param.getParamUnit())){
               couponInfoFindOutDTO.setConditionUnit(param.getParamUnit());
               couponInfoFindOutDTO.setConditionValue(new BigDecimal(param.getParamValue()));
           }
        }
        return couponInfoFindOutDTO;

    }

    /**
     * 领取优惠券
     * 
     * @return Map<"userCode", "couponCode,...,couponCode">
     */
    @Transactional
    public Map<String, String> allocateCoupon(List<String> userCodes, int receiveCount, String takeLabel, ActivityModel couponActivity,
                                              List<CouponReleaseDomain> releaseDomains, Integer frozenStatus, String orderNo){

        if (receiveCount < 1) {
            receiveCount = 1;
        }
        int totalCount = receiveCount * userCodes.size();

        String[] allocates = new String[totalCount];
        getAllocate(userCodes, receiveCount, allocates);
        int allocatesBegin = 0;

        List<CouponReleaseInventoryDomain> couponReleaseDomains = this.handleInventory(releaseDomains, totalCount, couponActivity.getReserveInventory());

        Map<String, String> mapResult = new HashMap<>();
        for (CouponReleaseInventoryDomain releaseDomain : couponReleaseDomains) {

            List<String> couponCodes = new ArrayList<>();
            boolean isCouponNeedCreate = false;
            if (CouponReleaseSourceEnum.SYSTEM_CREATE.equalsCode(releaseDomain.getReleaseSource())) {
                // 优惠券领取需要生成券码
                for (int i = 0; i < releaseDomain.getSendInventory(); i++) {

                    if (null == releaseDomain.getCouponRuleType() || CouponRuleTypeEnum.DIGIT.code().equals(releaseDomain.getCouponRuleType())){
                        couponCodes.add(CouponUtils.createCoupons(releaseDomain.getTenantCode(), releaseDomain.getReleaseCode(),
                                releaseDomain.getCouponCodePrefix(), releaseDomain.getCouponCodeLength()));
                    }else {
                        couponCodes.add(CouponUtils.generateRandomCouponCode(releaseDomain.getCouponCodePrefix(), releaseDomain.getCouponCodeLength()));
                    }

                }
                isCouponNeedCreate = true;
            } else {
                couponCodes = promoCouponInnerCodeService.queryHeaderCouponCodes(releaseDomain.getTenantCode(), releaseDomain.getActivityCode(),
                    releaseDomain.getReleaseCode(), totalCount);
            }
            if (CollectionUtils.isEmpty(couponCodes) || couponCodes.size() < releaseDomain.getSendInventory()) {
                throw Exceptions.fail(ErrorCodes.COUPON_INVENTORY_NOT_ENOUGH);
            }
            totalCount -= couponCodes.size();

            // 创建领券记录对象
            TPromoCouponCodeUserVO couponCodeUser = this.builderCouponToUser(couponActivity, releaseDomain);

            if (isCouponNeedCreate) {
                // 添加优惠券innerCode记录
                List<TPromoCouponInnerCodeVO> list = new ArrayList<>();
                getInnerCodes(takeLabel, frozenStatus, releaseDomain, couponCodes, couponCodeUser, list);
                promoCouponInnerCodeService.insertPromoCouponInnerCode(list);
            } else if (!CouponTypeEnum.PROMOTION_CODE.equalsCode(releaseDomain.getCouponType())) {
                // 修改券码状态
                promoCouponInnerCodeService.updateCouponToGrantedState(couponActivity.getTenantCode(), couponCodes, takeLabel,frozenStatus);
            }

            // 添加领券记录
            List<TPromoCouponCodeUserVO> ccuModelList = new ArrayList<>();
            TPromoCouponCodeUserVO couponCodeUserVO=new TPromoCouponCodeUserVO();
            couponCodeUserVO.setTakeLabel(takeLabel);
            couponCodeUserVO.setCouponSource(orderNo);
            if(frozenStatus!=null) {
            	couponCodeUserVO.setFrozenStatus(String.valueOf(frozenStatus));
            }
            for (; allocatesBegin < allocates.length; allocatesBegin++) {
                if (getResultByUserVo(couponCodeUserVO, couponActivity,  allocates[allocatesBegin], mapResult, couponCodes, couponCodeUser, ccuModelList))
                    break;
            }
            promoCouponCodeUserService.insert(ccuModelList);

            // 限领缓存
            //limitCouponUserCode(userCodes, receiveCount, couponActivity, releaseDomain)
        }

        CouponUtils.clearCouponSet();
        CouponUtils.clearCouponDigitSet();

//        if (!TakeLabelEnum.PURCHASE.equalsCode(takeLabel)) { // NOSONAR
//            this.sendMessage(couponActivity.getDomainCode(), couponActivity.getTenantCode(), couponActivity.getActivityName(), userCodes); // NOSONAR
//        }// NOSONAR

        return mapResult;
    }

    public void getAllocate(List<String> userCodes, int receiveCount, String[] allocates) {
        for (int i = 0; i < userCodes.size(); i++) {
            for (int j = receiveCount * i; j < receiveCount * (i + 1); j++) {
                allocates[j] = userCodes.get(i);
            }
        }
    }

    public void getInnerCodes(String takeLabel, Integer frozenStatus, CouponReleaseInventoryDomain releaseDomain, List<String> couponCodes, TPromoCouponCodeUserVO couponCodeUser, List<TPromoCouponInnerCodeVO> list) {
        for (String couponCode : couponCodes) {
            TPromoCouponInnerCodeVO innerCode = BeanCopyUtils.jsonCopyBean(couponCodeUser, TPromoCouponInnerCodeVO.class);
            innerCode.setReceiveStartTime(releaseDomain.getReceiveStartTime());
            innerCode.setReceiveEndTime(releaseDomain.getReceiveEndTime());
            innerCode.setTakeLabel(takeLabel);
            innerCode.setCouponCode(couponCode);
            if (frozenStatus!=null && frozenStatus == 2){
                innerCode.setFrozenStatus(CouponFrozenStatusEnum.FROZENED.code());
            }
            list.add(innerCode);
        }
    }

    public boolean getResultByUserVo(TPromoCouponCodeUserVO couponCodeUserVO, ActivityModel couponActivity,  String allocate, Map<String, String> mapResult, List<String> couponCodes, TPromoCouponCodeUserVO couponCodeUser, List<TPromoCouponCodeUserVO> ccuModelList) {
        String s = allocate;
        TPromoCouponCodeUserVO ccuModel = BeanCopyUtils.jsonCopyBean(couponCodeUser, TPromoCouponCodeUserVO.class);
        ccuModel.setUserCode(s);
        ccuModel.setOpsType(couponActivity.getOpsType());
        ccuModel.setTakeLabel(couponCodeUserVO.getTakeLabel());
        ccuModel.setCouponSource(couponCodeUserVO.getCouponSource());

        if (StringUtils.isNotBlank(couponCodeUserVO.getFrozenStatus()) && Integer.valueOf(couponCodeUserVO.getFrozenStatus()) == 2){
            ccuModel.setFrozenStatus(CouponFrozenStatusEnum.FROZENED.code());
        }
        if (couponCodes.isEmpty()){
            return true;
        }
        ccuModel.setCouponCode(couponCodes.remove(0));
        ccuModelList.add(ccuModel);

        putMapResult(mapResult, s, ccuModel);
        return false;
    }

    public void putMapResult(Map<String, String> mapResult, String s, TPromoCouponCodeUserVO ccuModel) {
        if (mapResult.containsKey(s)){
            mapResult.put(s, mapResult.get(s) + "," + ccuModel.getCouponCode());
        }else {
            mapResult.put(s, ccuModel.getCouponCode());
        }
    }

    public void limitCouponUserCode(List<String> userCodes, int receiveCount, ActivityModel couponActivity, CouponReleaseInventoryDomain releaseDomain) {
        for(String s : userCodes) {
            for (int i = 0; i < receiveCount; i++) {
                this.limitCouponCodeUser(couponActivity.getTenantCode(), s, releaseDomain.getActivityCode(), couponActivity.getActivityEnd(), couponActivity.getUserLimitMax());
            }
        }
    }

    @Autowired
    private MessageFeignClient messageFeignClient;

    /**
     * sendMessage to user
     * @Date 2020-05-22
     */
    private void sendMessage(String domainCode, String tenantCode, String activityName, List<String> userCodes) {// NOSONAR
        
        CompletableFuture.supplyAsync(() -> {

            for(String userCode : userCodes) {
                JSONObject param = new JSONObject();
                param.put("aliases", Arrays.asList(userCode));
                param.put("type","4");
                param.put("couponName", activityName);
                param.put("userCode", userCode);
                try {
                    messageFeignClient.sendMessage(EngineSendRequest.builder()
                        .domainCode(StringUtil.isBlank(domainCode) ? "DEFAULT" : domainCode).tenantCode(tenantCode).eventCode("EV004").param(param).build());
                } catch (Exception e) {
                    log.error("messageFeignClient.sendMessage failed.", e);
                }
            }
            return true;
        });
    }

    private List<CouponReleaseInventoryDomain> handleInventory(List<CouponReleaseDomain> releaseDomains, int receiveCount, int quota) {
        
        if (CollectionUtils.isEmpty(releaseDomains)) {
            throw Exceptions.fail(ErrorCodes.COUPON_INVENTORY_NOT_ENOUGH);
        }

        String tenantCode = releaseDomains.get(0).getTenantCode();
        String activityCode = releaseDomains.get(0).getActivityCode();

        checkInventory(releaseDomains, receiveCount, quota);
        List<CouponReleaseInventoryDomain> releaseInventoryDomains;
        String lockName = Constants.PROMOTION_COUPON_CODE_LOCK_KEY + ":" + tenantCode + ":" + activityCode;
        String key = "";
        if (releaseDomains.size() > 1){ // 多个投放批次，添加分布式锁
            key = redisLock.tryLockAndRetry(lockName, 1000L, 3);
            Check.check(StringUtil.isBlank(key), CouponAllocateChecker.LOCK_FAIL);
            try {
                releaseDomains = promoCouponReleaseService.queryCanReceiveReleases(tenantCode, activityCode, null);
                checkInventory(releaseDomains, receiveCount, quota);
                releaseInventoryDomains = promoCouponReleaseService.deductInventory(tenantCode, activityCode, releaseDomains, receiveCount);
            } finally {
                redisLock.unlock(lockName, key);
            }
        }else {
            releaseInventoryDomains = promoCouponReleaseService.deductInventory(tenantCode, activityCode, releaseDomains, receiveCount);
        }
        if (CollectionUtils.isEmpty(releaseInventoryDomains)) {
            throw Exceptions.fail(ErrorCodes.COUPON_INVENTORY_NOT_ENOUGH);
        }
        return releaseInventoryDomains;
    }

    private List<CouponReleaseDomain> checkInventory(List<CouponReleaseDomain> releaseDomains, int receiveCount, int quota) {
        List<CouponReleaseDomain> couponReleaseDomains = new ArrayList<>();
        int inventory = 0;
        boolean enough = false;
        for (CouponReleaseDomain releaseDomain : releaseDomains){
            // 寻找还有库存的couponRelease
            if (releaseDomain.getInventory() >= 0){
                couponReleaseDomains.add(releaseDomain);
                inventory += releaseDomain.getInventory();
                if (inventory >= receiveCount + quota){
                    enough = true;
                    break;
                }
            }
        }
        if (!enough) {
            throw Exceptions.fail(ErrorCodes.COUPON_INVENTORY_NOT_ENOUGH);
        }
        return couponReleaseDomains;
    }

    public TPromoCouponCodeUserVO builderCouponToUser(ActivityModel vo, CouponReleaseDomain canReceiveReleaseVO) {

        long now = Long.parseLong(DateUtil.format(DateUtil.now(), DateUtil.FORMAT_YYYYMMDDHHMISS_14));
        // 添加领券记录
        TPromoCouponCodeUserVO couponCodeUserVO = new TPromoCouponCodeUserVO();
        couponCodeUserVO.setReceivedTime(String.valueOf(now));
        couponCodeUserVO.setTenantCode(canReceiveReleaseVO.getTenantCode());
        couponCodeUserVO.setActivityCode(canReceiveReleaseVO.getActivityCode());
        couponCodeUserVO.setCouponType(canReceiveReleaseVO.getCouponType());
        ConditionAndFace conditionAndFace = functionParamDomain.findConditionAndFaceByActivityCode(canReceiveReleaseVO.getActivityCode());
        String templateCode = vo.getTemplateCode();
        String substring = templateCode.substring(templateCode.length() - 2);
        if (FaceUnitEnum.DISCOUNT.equalsCode(substring)) {
            couponCodeUserVO.setFaceValue(conditionAndFace.getFaceValue().divide(new BigDecimal(10)));
            couponCodeUserVO.setFaceUnit(conditionAndFace.getFaceUnit());
        } else {
            couponCodeUserVO.setFaceValue(conditionAndFace.getFaceValue());
            couponCodeUserVO.setFaceUnit(conditionAndFace.getFaceUnit());
        }

        couponCodeUserVO.setFrozenStatus(CouponFrozenStatusEnum.UN_FROZEN.code());
        couponCodeUserVO.setReleaseCode(canReceiveReleaseVO.getReleaseCode());
        couponCodeUserVO.setUsedRefId(Constants.DEFAULT_USED_REF_ID);
        couponCodeUserVO.setStatus(CouponStatusEnum.GRANTED.code());

        // 设置可用时间段
        if (null != canReceiveReleaseVO.getValidDays()) { // 可用时长
            String endDate = DateUtil.format(DateUtil.addDay(canReceiveReleaseVO.getValidDays()), DateUtil.FORMAT_YYYYMMDDHHMISS_14);
            couponCodeUserVO.setValidStartTime(String.valueOf(now));
            if (Long.parseLong(endDate) > Long.parseLong(vo.getActivityEnd())) {// 可用结束时间不能超过活动结束时间
                couponCodeUserVO.setValidEndTime(vo.getActivityEnd());
            } else {
                couponCodeUserVO.setValidEndTime(endDate);
            }
        } else {
            couponCodeUserVO.setValidStartTime(canReceiveReleaseVO.getValidStartTime());
            couponCodeUserVO.setValidEndTime(canReceiveReleaseVO.getValidEndTime());
        }
        return couponCodeUserVO;
    }

    /**
     * 分页查询某一活动下的优惠券列表
     */
    @Transactional
    public PageInfo<CouponInfoQueryOutDTO> queryCouponListInfo(TCouponListQueryDTO queryDTO, RequestPage page){
        Map<String, Date> map = validateTime(queryDTO);
        Date createTimeStart = map.get(START_TIME);
        Date createTimeEnd = map.get(END_TIME);
        ActivityModel couponActivity = promoCouponActivityService.findCouponActivity(queryDTO.getTenantCode(), queryDTO.getActivityCode());
        Check.check(null == couponActivity, CouponActivityChecker.ACTIVITY_NOT_EXIST);
        //过滤店铺
        Check.check(!activityStoreDomain.checkStore111(queryDTO.getOrgCode(), queryDTO.getActivityCode()), CouponActivityChecker.ACTIVITY_STORE_NOT_EXIST);

        //投放编码不为空时
        String releaseCode = queryDTO.getReleaseCode();
        TPromoCouponInnerCodeVO innerCodeVO=new TPromoCouponInnerCodeVO();
        innerCodeVO.setTenantCode(queryDTO.getTenantCode());
        innerCodeVO.setActivityCode(queryDTO.getActivityCode());
        innerCodeVO.setCouponCode( queryDTO.getCouponCode());
        innerCodeVO.setReleaseCode(releaseCode);
        PageInfo<TPromoCouponInnerCodeVO> couponList = promoCouponInnerCodeService.selectCouponCode(innerCodeVO, queryDTO.getCouponStatus(), createTimeStart, createTimeEnd, page);
        List<CouponInfoQueryOutDTO> listCouponInfoDTO = new ArrayList<>();

        PageInfo<TPromoCouponCodeUserVO> pageInfoCode = new PageInfo<>();
        //填充用户编码信息  优惠券 匿名券
        if (!CouponTypeEnum.PROMOTION_CODE.code().equals(couponActivity.getCouponType())){
            for (TPromoCouponInnerCodeVO vo : couponList.getList()){
                CouponInfoQueryOutDTO dto = new CouponInfoQueryOutDTO();
                BeanUtils.copyProperties(vo, dto);
                dto.setCouponStatus(vo.getStatus());
                dto.setCreateTime(DateUtil.format(vo.getCreateTime(), DateUtil.FORMAT_YYYYMMDDHHMISS_14));
                TPromoCouponCodeUserVO couponInfo = promoCouponCodeUserService.getUserCouponInfo(vo.getTenantCode(), vo.getCouponCode(), null);
                if (!Objects.isNull(couponInfo)){
                    dto.setUserCode(couponInfo.getUserCode());
                    dto.setReceivedTime(couponInfo.getReceivedTime());
                    dto.setUsedRefId(couponInfo.getUsedRefId());
                    dto.setUsedTime(couponInfo.getUsedTime());
                    dto.setValidEndTime(couponInfo.getValidEndTime());
                }else{
                    dto.setUsedRefId("DEFAULT");
                }
                listCouponInfoDTO.add(dto);
            }
        }else{
            //通用优惠码
            pageInfoCode = promoCouponCodeUserService.getUserCouponCode(queryDTO.getTenantCode(), queryDTO.getActivityCode(), 
                queryDTO.getCouponStatus(), queryDTO.getCouponCode(), page);
            List<TPromoCouponCodeUserVO> list = pageInfoCode.getList();
            if (!CollectionUtils.isEmpty(list)){
                for (TPromoCouponCodeUserVO tPromoCouponCodeUserVO : list){
                    CouponInfoQueryOutDTO dto = new CouponInfoQueryOutDTO();
                    BeanUtils.copyProperties(tPromoCouponCodeUserVO, dto);
                    dto.setCouponStatus(tPromoCouponCodeUserVO.getStatus());
                    dto.setCreateTime(DateUtil.format(tPromoCouponCodeUserVO.getCreateTime(), DateUtil.FORMAT_YYYYMMDDHHMISS_14));
                    dto.setUserCode(tPromoCouponCodeUserVO.getUserCode());
                    dto.setReceivedTime(tPromoCouponCodeUserVO.getReceivedTime());
                    dto.setUsedRefId(tPromoCouponCodeUserVO.getUsedRefId());
                    dto.setUsedTime(tPromoCouponCodeUserVO.getUsedTime());
                    dto.setValidEndTime(tPromoCouponCodeUserVO.getValidEndTime());
                    listCouponInfoDTO.add(dto);
                }
            }
        }
        PageInfo<CouponInfoQueryOutDTO> pageInfo = new PageInfo<>(listCouponInfoDTO);
        if (!CouponTypeEnum.PROMOTION_CODE.code().equals(couponActivity.getCouponType())){
            pageInfo.setTotal(couponList.getTotal());
        }else{
            pageInfo.setTotal(pageInfoCode.getTotal());
        }

        return pageInfo;
    }

    /**
     * 查询要导出的匿名券
     */
    @Transactional
    public void exportCouponInfos(TCouponListQueryDTO tCouponListQueryDTO,OutputStream outputStream,Map<String, String> couponStatus,Map<String, String> frozenStatus) throws IOException{
        // 查询活动
        ActivityModel activity = activityService.findActivityByActivityCode(tCouponListQueryDTO.getTenantCode(), tCouponListQueryDTO.getActivityCode());
        Check.check(null == activity, CouponActivityChecker.ACTIVITY_NOT_EXIST);
        // 查询所有投放
        List<CouponReleaseModel> couponReleases = promoCouponReleaseService.queryCouponRelease(tCouponListQueryDTO.getActivityCode());
        exportCodeInfoPage(tCouponListQueryDTO, couponReleases, activity, outputStream, couponStatus, frozenStatus);
    }

    private void exportCodeInfoPage(TCouponListQueryDTO tCouponListQueryDTO,List<CouponReleaseModel> couponReleases,ActivityModel activity,OutputStream outputStream,Map<String, String> couponStatus,Map<String, String> frozenStatus)
                    throws IOException{
        Map<String, Date> map = validateTime(tCouponListQueryDTO);
        Date createTimeStart = map.get(START_TIME);
        Date createTimeEnd = map.get(END_TIME);
        RequestPage page = new RequestPage(1, pageCount);
        while (true){
            PageHelper.startPage(page.getPageNo(), page.getPageCount());
            List<CouponInfoImportOutDTO> listCouponInfoDTO = promoCouponInnerCodeService.selectCouponCodeList(tCouponListQueryDTO, createTimeStart, createTimeEnd);
            if (CollectionUtils.isEmpty(listCouponInfoDTO)){
                break;
            }
            page.setPageNo(page.getPageNo() + 1);
            listCouponInfoDTO.forEach(x -> {
                x.setCouponName(activity.getActivityName());
                x.setCouponDesc(activity.getActivityDesc());
                x.setFrozenStatus(frozenStatus.get(x.getFrozenStatus()));
                x.setCouponStatus(couponStatus.get(x.getCouponStatus()));
                if (Constants.DEFAULT_USED_REF_ID.equals(x.getUsedRefId())){
                    x.setUsedRefId(null);
                }
                couponReleases.forEach(y -> {
                    if (y.getReleaseCode().equals(x.getReleaseCode())){
                        x.setValidDays(y.getValidDays());
                        x.setValidBegin(y.getValidStartTime());
                        x.setValidEnd(y.getValidEndTime());
                    }
                });
            });
            ExportCouponUtil.exportCouponInfoContent(listCouponInfoDTO, outputStream);
            listCouponInfoDTO.clear();
        }
    }

    public void exportManagementCodeInfos(ManagementDataInDTO dataInDTO,OutputStream outputStream,Map<String, Map<String, String>> hashMap) throws IOException{
        dataInDTO.setPageNo(1);
        while (true){
            PageData<ManagementDataOutDTO> pageInfo = promoCouponInnerCodeService.queryCouponActivityListService(dataInDTO);
            if (CollectionUtils.isEmpty(pageInfo.getList())){
                break;
            }
            dataInDTO.setPageNo(dataInDTO.getPageNo() + 1);
            ExportCouponUtil.exportManagementCouponInfoContent(pageInfo.getList(), outputStream, hashMap);
            pageInfo.getList().clear();
        }
    }

    /**
     * 导出通用优惠码
     */
    public void exportDiscountCodeInfos(TCouponListQueryDTO tCouponListQueryDTO,OutputStream outputStream,Map<String, String> couponStatus,Map<String, String> frozenStatus) throws IOException{

        Map<String, Date> map = validateTime(tCouponListQueryDTO);
        Date createTimeStart = map.get(START_TIME);
        Date createTimeEnd = map.get(END_TIME);

        // 查询活动
        ActivityModel activity = activityService.findActivityByActivityCode(tCouponListQueryDTO.getTenantCode(), tCouponListQueryDTO.getActivityCode());
        Check.check(null == activity, CouponActivityChecker.ACTIVITY_NOT_EXIST);

        PageInfo<TPromoCouponInnerCodeVO> couponList = promoCouponInnerCodeService.selectCouponList(tCouponListQueryDTO.getTenantCode(), tCouponListQueryDTO.getActivityCode(),
            null, createTimeStart, createTimeEnd, null, tCouponListQueryDTO.getCouponCode());

        if (!CollectionUtils.isEmpty(couponList.getList())){
            TPromoCouponInnerCodeVO vo = couponList.getList().get(0);
            exportDiscountCodeInfoPage(tCouponListQueryDTO, vo, activity, outputStream, couponStatus, frozenStatus);
        }
    }

    private void exportDiscountCodeInfoPage(TCouponListQueryDTO tCouponListQueryDTO,TPromoCouponInnerCodeVO vo,ActivityModel activity,OutputStream outputStream,
                    Map<String, String> couponStatus,Map<String, String> frozenStatus) throws IOException{

        RequestPage page = new RequestPage(1, pageCount);
        List<CouponInfoImportOutDTO> listCouponInfoDTO = new ArrayList<>(pageCount);
        while (true){
            PageHelper.startPage(page.getPageNo(), page.getPageCount());
            List<TPromoCouponCodeUserVO> infoList = promoCouponCodeUserService.getUserCouponInfos(tCouponListQueryDTO.getTenantCode(),
                tCouponListQueryDTO.getActivityCode(), tCouponListQueryDTO.getCouponCode(), tCouponListQueryDTO.getCouponStatus());
            if (CollectionUtils.isEmpty(infoList)){
                break;
            }
            page.setPageNo(page.getPageNo() + 1);
            for (TPromoCouponCodeUserVO userVO : infoList){
                CouponInfoImportOutDTO dto = new CouponInfoImportOutDTO();
                BeanUtils.copyProperties(vo, dto);
                dto.setUsedRefId(userVO.getUsedRefId());
                dto.setUsedTime(userVO.getUsedTime());
                dto.setReceiveTime(userVO.getReceivedTime());
                dto.setUserCode(userVO.getUserCode());
                dto.setValidBegin(userVO.getValidStartTime());
                dto.setValidEnd(userVO.getValidEndTime());
                dto.setValidDays(null);
                dto.setFrozenStatus(frozenStatus.get(dto.getFrozenStatus()));
                dto.setCouponStatus(couponStatus.get(dto.getCouponStatus()));
                dto.setCouponName(activity.getActivityName());
                dto.setCouponDesc(activity.getActivityDesc());
                dto.setCreateTime(DateUtil.format(vo.getCreateTime(), DateUtil.FORMAT_YYYYMMDDHHMISS_14));
                listCouponInfoDTO.add(dto);
            }
            ExportCouponUtil.exportCouponInfoContent(listCouponInfoDTO, outputStream);
            listCouponInfoDTO.clear();
        }
    }

    public Map<String, Date> validateTime(TCouponListQueryDTO tCouponListQueryDTO){
        Map<String, Date> map = new HashMap<>();
        if (StringUtils.isNotEmpty(tCouponListQueryDTO.getCreateTimeStart())){
            try{
                Date createTimeStart = DateUtil.parseDate(tCouponListQueryDTO.getCreateTimeStart(), DateUtil.FORMAT_YYYYMMDDHHMISS_14);
                map.put(START_TIME, createTimeStart);
            }catch (Exception e){
                throw new PromotionException(CouponQueryListChecker.EXCEPTION_STARTTIME.getCode(), CouponQueryListChecker.EXCEPTION_STARTTIME.getMessage());
            }
        }
        if (StringUtils.isNotEmpty(tCouponListQueryDTO.getCreateTimeEnd())){
            try{
                Date createTimeEnd = DateUtil.parseDate(tCouponListQueryDTO.getCreateTimeEnd(), DateUtil.FORMAT_YYYYMMDDHHMISS_14);
                map.put(END_TIME, createTimeEnd);
            }catch (Exception e){
                throw new PromotionException(CouponQueryListChecker.EXCEPTION_ENDTIME.getCode(), CouponQueryListChecker.EXCEPTION_ENDTIME.getMessage());
            }
        }
        return map;
    }

    /**
     * 核销优惠券
     */
    @Transactional
    public int useCoupon(TCouponUseInDTO tCouponUseInDTO){
        //根据租户编号和订单编号查找对应的券
        List<TPromoCouponCodeUserVO> codeUser = promoCouponCodeUserService.getCodeUserByUsedRefId(tCouponUseInDTO.getUsedRefId(), tCouponUseInDTO.getTenantCode());
        if (!CollectionUtils.isEmpty(codeUser)){
            for (TPromoCouponCodeUserVO vo : codeUser){
                List<String> couponCodes = new ArrayList<>(1);
                if (!CouponTypeEnum.PROMOTION_CODE.code().equals(vo.getCouponType())){
                    couponCodes.add(vo.getCouponCode());
                    promoCouponInnerCodeService.updateStatusBatch(tCouponUseInDTO.getTenantCode(), couponCodes, CouponStatusEnum.USED);
                    promoCouponCodeUserService.updateStatusBatch(tCouponUseInDTO.getTenantCode(), couponCodes, CouponStatusEnum.USED);
                }else{
                    promoCouponCodeUserService.updateCouponStatus(vo.getTenantCode(), vo.getActivityCode(), vo.getCouponCode(), CouponStatusEnum.USED, vo.getUsedRefId());
                }
            }
            return 1;
        }
        return 0;
    }

    @Transactional
    public String receiveCouponCodeByOrder(TPromoCouponInnerCodeVO innerCodeVO,String orderId,String takeLabel,String userCode){
        Check.check(null == innerCodeVO, CouponAllocateChecker.NOT_EXISTS_OUTERCOUPON);
        Check.check(innerCodeVO.getFrozenStatus().equals(CouponFrozenStatusEnum.FROZENED.code()), CouponAllocateChecker.FROZENED);
        String couponCode = "";

        ActivityModel couponActivity = this.couponActivityDomain.findValidActivity(innerCodeVO.getTenantCode(), innerCodeVO.getActivityCode(), null, new Date());
        CheckUtils.isNotNull(couponActivity, ErrorCodes.VALIDATE_COUPON_ACTIVITY_EXIST);

        CouponReleaseDomain release = promoCouponReleaseService.findCouponReleaseByReleaseCode(innerCodeVO.getTenantCode(), innerCodeVO.getReleaseCode());
        long now = Long.parseLong(DateUtil.format(new Date(), DateUtil.FORMAT_YYYYMMDDHHMISS_14));
        Check.check(now > Long.parseLong(release.getReceiveEndTime()), CouponAllocateChecker.COUPON_EXPIRED);
        // 添加领券记录
        TPromoCouponCodeUserVO couponCodeUserVO = new TPromoCouponCodeUserVO();
        BeanUtils.copyProperties(innerCodeVO, couponCodeUserVO);
        couponCodeUserVO.setId(null);
        couponCode = innerCodeVO.getCouponCode();
        // 匿名券锁定
        if (innerCodeVO.getCouponType().equals(CouponTypeEnum.ANONYMITY_COUPON.code())){
            promoCouponInnerCodeService.updateCouponToLockedState(innerCodeVO.getTenantCode(), couponCode, takeLabel);
        }
        couponCodeUserVO.setReceivedTime(String.valueOf(now));
        couponCodeUserVO.setTakeLabel(takeLabel);
        couponCodeUserVO.setUserCode(userCode);
        couponCodeUserVO.setUsedTime(String.valueOf(now));
        couponCodeUserVO.setStatus(CouponStatusEnum.LOCKED.code());
        couponCodeUserVO.setUsedRefId(orderId);
        couponCodeUserVO.setOpsType(couponActivity.getOpsType());

        // 设置可用时间段
        if (null != release.getValidDays()){ // 可用时长
            couponCodeUserVO.setValidStartTime(String.valueOf(now));
            String endDate = DateUtil.format(DateUtil.addDay(release.getValidDays()), DateUtil.FORMAT_YYYYMMDDHHMISS_14);
            if (Long.parseLong(endDate) > Long.parseLong(couponActivity.getActivityEnd())){// 可用结束时间不能超过活动结束时间
                couponCodeUserVO.setValidEndTime(couponActivity.getActivityEnd());
            }else{
                couponCodeUserVO.setValidEndTime(endDate);
            }
        }else{
            couponCodeUserVO.setValidStartTime(release.getValidStartTime());
            couponCodeUserVO.setValidEndTime(release.getValidEndTime());
        }
        promoCouponCodeUserService.insert(Arrays.asList(couponCodeUserVO));

        this.limitCouponCodeUser(innerCodeVO.getTenantCode(), userCode, innerCodeVO.getActivityCode(), couponActivity.getActivityEnd(), couponActivity.getUserLimitMax());
        if (innerCodeVO.getCouponType().equals(CouponTypeEnum.PROMOTION_CODE.code())){
            limitCouponRelease(innerCodeVO.getTenantCode(), innerCodeVO.getReleaseCode(), innerCodeVO.getActivityCode(), release.getReceiveEndTime(),
                couponActivity.getTotalQuantity());
        }
        return couponCode;
    }

    //用户限制领取优惠券
    private void limitCouponCodeUser(String tenantCode, String userCode, String activityCode, String activityEnd, Integer userLimitMax) {

        // 每人限领
        Long limitKeyExpireTime = null;
        if (null != userLimitMax && userLimitMax > 0) {
            int count = redisService.getCouponUserCount111(tenantCode, activityCode, userCode);
            if (count > 0) {
                Check.check(count >= userLimitMax, CouponAllocateChecker.ERROR_LIMIT, String.valueOf(userLimitMax));
            } else {
                long endTime = DateUtil.parseDate(activityEnd, DateUtil.FORMAT_YYYYMMDDHHMISS_14).getTime();
                limitKeyExpireTime = endTime - System.currentTimeMillis();
            }
            redisService.addCouponUserCount111(tenantCode, activityCode, userCode, limitKeyExpireTime);
        }
    }

    private void limitCouponRelease(String tenantCode, String releaseCode, String activityCode, String receiveEndTime, int releaseLimitMax) {

        // 总限制次数
        Long limitKeyExpireTime = null;
        if (releaseLimitMax > 0) {
            int count = redisService.getCouponReleaseLimit(tenantCode, activityCode, releaseCode);
            if (count > 0) {
                // 此异常多会在通用优惠码的场景出现
                Check.check(count >= releaseLimitMax, CouponActivityChecker.USED_LIMIT);
            } else {
                long endTime = DateUtil.parseDate(receiveEndTime, DateUtil.FORMAT_YYYYMMDDHHMISS_14).getTime();
                limitKeyExpireTime = endTime - System.currentTimeMillis();
            }
            redisService.addCouponReleaseLimit(tenantCode, activityCode, releaseCode, limitKeyExpireTime);
        }
    }

    public PageData<ManagementDataOutDTO> queryManagementUserCouponCodeData(ManagementDataInDTO dataInDTO,ActivityModel activityModel){

        PageData<TPromoCouponCodeUserVO> pageInfo = promoCouponCodeUserService.queryManagementUserData(dataInDTO);
        List<ManagementDataOutDTO> infos = new ArrayList<>();
        List<TPromoCouponCodeUserVO> list = pageInfo.getList();
        for (TPromoCouponCodeUserVO vo : list){
            if (null != vo){
                ManagementDataOutDTO outDTO2 = new ManagementDataOutDTO();
                setUserAttributeValue(outDTO2, vo);
                setActivityNameAndActivityCode(vo.getTenantCode(), vo.getActivityCode(), activityModel, outDTO2);
                setInnerAttributeValue(vo.getTenantCode(), vo.getCouponCode(), vo.getCouponType(), vo.getFrozenStatus(), vo.getStatus(), vo.getReleaseCode(), outDTO2);
                infos.add(outDTO2);
            }

        }
        return getPageInfo(infos, pageInfo);
    }


    /**
     * 优惠码
     */
    public PageData<ManagementDataOutDTO> managementCouponCode(ManagementDataInDTO dataInDTO,ActivityModel activityModel){

        List<ManagementDataOutDTO> infos = new ArrayList<>();
        if ("1".equals(dataInDTO.getIsUseStatus())){
            PageData<TPromoCouponCodeUserVO> pageInfo = promoCouponCodeUserService.queryManagementCouponCodeData(dataInDTO);
            if (!pageInfo.getList().isEmpty()){
                List<TPromoCouponCodeUserVO> list = pageInfo.getList();
                for (TPromoCouponCodeUserVO vo : list){
                    ManagementDataOutDTO outDTO2 = new ManagementDataOutDTO();
                    setActivityNameAndActivityCode(vo.getTenantCode(), vo.getActivityCode(), activityModel, outDTO2);
                    setInnerAttributeValue(vo.getTenantCode(), vo.getCouponCode(), vo.getCouponType(), vo.getFrozenStatus(), vo.getStatus(), vo.getReleaseCode(), outDTO2);
                    setUserAttributeValue(outDTO2, vo);
                    infos.add(outDTO2);
                }
                return getPageInfo(infos, pageInfo);
            }
            return setEmpty(infos);

        }else{
            if (StringUtil.isNotBlank(dataInDTO.getReceiveStartTime()) || StringUtil.isNotBlank(dataInDTO.getUsedStartTime()) 
                            || StringUtil.isNotBlank(dataInDTO.getUsedRefId()) || StringUtil.isNotBlank(dataInDTO.getUserCode())){
                return setEmpty(infos);
            }
            PageData<TPromoCouponInnerCodeVO> list = promoCouponInnerCodeService.findCouponCodeData(dataInDTO);
            List<TPromoCouponInnerCodeVO> pageList = list.getList();
            if (!CollectionUtils.isEmpty(pageList)){
                for (TPromoCouponInnerCodeVO vo : pageList){
                    ManagementDataOutDTO dto = new ManagementDataOutDTO();
                    setActivityNameAndActivityCode(vo.getTenantCode(), vo.getActivityCode(), activityModel, dto);
                    setInnerAttributeValue(vo.getTenantCode(), vo.getCouponCode(), vo.getCouponType(), vo.getFrozenStatus(), vo.getStatus(), vo.getReleaseCode(), dto);
                    infos.add(dto);
                }
            }
            PageInfo<ManagementDataOutDTO> pageInfo2 = new PageInfo<>();
            pageInfo2.setList(infos);
            pageInfo2.setTotal(list.getTotal());
            return new PageData<>(pageInfo2.getList(), pageInfo2.getTotal());
        }
    }

    private PageData<ManagementDataOutDTO> setEmpty(List<ManagementDataOutDTO> infos){
        PageInfo<ManagementDataOutDTO> pageInfo2 = new PageInfo<>();
        pageInfo2.setList(infos);
        pageInfo2.setTotal(0);
        return new PageData<>(pageInfo2.getList(), pageInfo2.getTotal());
    }

    public PageData<ManagementDataOutDTO> getPageInfo(ManagementDataInDTO dataInDTO){

        ActivityModel tPromoActivityVO = getActivity(dataInDTO);
        if (null == tPromoActivityVO){
            return new PageData<>();
        }
        if (StringUtil.isNotBlank(dataInDTO.getReleaseStartTime()) || StringUtil.isNotBlank(tPromoActivityVO.getActivityCode())){
            List<String> releaseCodes = getReleaseCodes(dataInDTO, tPromoActivityVO);
            if (CollectionUtils.isEmpty(releaseCodes)){
                return new PageData<>();
            }
            dataInDTO.setReleaseCodes(releaseCodes);
        }
        setManageMentStatusAndCouponType(dataInDTO);

        if (CouponTypeEnum.PROMOTION_CODE.code().equals(dataInDTO.getType())){
            return managementCouponCode(dataInDTO, tPromoActivityVO);
        }else{

            return managementMethods(dataInDTO, tPromoActivityVO);
        }
    }

    public PageData<ManagementDataOutDTO> queryManagementInnerCouponCodeData(ManagementDataInDTO dataInDTO,ActivityModel tPromoActivityVO){
        return setManagementAttributValue(dataInDTO, tPromoActivityVO);
    }

    private PageData<ManagementDataOutDTO> setManagementAttributValue(ManagementDataInDTO dataInDTO,ActivityModel activityModel){

        PageData<TPromoCouponInnerCodeVO> pageInfo = promoCouponInnerCodeService.queryManagementData(dataInDTO);
        List<ManagementDataOutDTO> infos = new ArrayList<>();
        List<TPromoCouponInnerCodeVO> list = pageInfo.getList();
        for (TPromoCouponInnerCodeVO vo : list){
            ManagementDataOutDTO outDTO2 = new ManagementDataOutDTO();
            //设置活动属性值
            setActivityNameAndActivityCode(vo.getTenantCode(), vo.getActivityCode(), activityModel, outDTO2);
            if (!CouponStatusEnum.UN_GRANT.code().equals(vo.getStatus())){
                TPromoCouponCodeUserVO userVO = promoCouponCodeUserService.getUserCouponCode(vo.getTenantCode(), vo.getCouponCode());
                if (null != userVO){
                    setUserAttributeValue(outDTO2, userVO);
                }
            }
            setInnerAttributeValue(vo.getTenantCode(), vo.getCouponCode(), vo.getCouponType(), vo.getFrozenStatus(), vo.getStatus(), vo.getReleaseCode(), outDTO2);
            infos.add(outDTO2);
        }
        PageInfo<ManagementDataOutDTO> pageInfo2 = new PageInfo<>(infos);
        pageInfo2.setList(infos);
        pageInfo2.setTotal(pageInfo.getTotal());
        return new PageData<>(pageInfo2.getList(), pageInfo2.getTotal());
    }

    private void setInnerAttributeValue(String tenantCode, String couponCode, String couponType, String frozenStatus, String status, String releaseCode, ManagementDataOutDTO outDTO2) {

        outDTO2.setCouponCode(couponCode);
        outDTO2.setCouponType(couponType);
        outDTO2.setFrozenStatus(frozenStatus);
        outDTO2.setStatus(status);
        CouponReleaseDomain releaseVO = promoCouponReleaseService.findCouponReleaseByReleaseCode(tenantCode, releaseCode);
        outDTO2.setReleaseTime(releaseVO.getReleaseTime());
    }

    private void setUserAttributeValue(ManagementDataOutDTO outDTO2,TPromoCouponCodeUserVO userVO){
        if (null != userVO){
            outDTO2.setReceiveTime(userVO.getReceivedTime());
            outDTO2.setTakeLabel(userVO.getTakeLabel());
            outDTO2.setUsedRefId(userVO.getUsedRefId());
            outDTO2.setUserCode(userVO.getUserCode());
            outDTO2.setUsedTime(userVO.getUsedTime());
        }
    }

    private void setActivityNameAndActivityCode(String tenantCode, String activityCode, ActivityModel activityModel, ManagementDataOutDTO outDTO2) {

        if (null == activityModel || StringUtil.isBlank(activityModel.getActivityCode())) {
            activityModel = activityService.findActivityByActivityCode(tenantCode, activityCode);
            if (null != activityModel) {
                outDTO2.setActivityCode(activityModel.getActivityCode());
                outDTO2.setActivityName(activityModel.getActivityName());
            }
        } else {
            outDTO2.setActivityCode(activityModel.getActivityCode());
            outDTO2.setActivityName(activityModel.getActivityName());
        }
    }

    private void setManageMentStatusAndCouponType(ManagementDataInDTO dataInDTO){
        List<String> types = new ArrayList<>();
        if (StringUtil.isNotBlank(dataInDTO.getCouponType())){
            String[] couponTypes = dataInDTO.getCouponType().split(",");
            if (couponTypes != null && couponTypes.length > 0){
                for (int i = 0; i < couponTypes.length; i++){
                    String type = couponTypes[i];
                    Check.check(!CouponTypeEnum.exist(type), CouponActivityChecker.NOT_COUPON_TYPE_EXIST);
                    types.add(type);
                }
            }
        }
        List<String> status = new ArrayList<>();
        if (StringUtil.isNotBlank(dataInDTO.getStatus())){
            String[] couponStatus = dataInDTO.getStatus().split(",");
            if (couponStatus != null && couponStatus.length > 0){
                for (int i = 0; i < couponStatus.length; i++){
                    String statusType = couponStatus[i];
                    boolean exist = CouponStatusEnum.exist(statusType);
                    Check.check(!exist, CouponActivityChecker.ERROR_STATUS);
                    status.add(statusType);
                }
            }
        }
        dataInDTO.setCouponStatus(status);
        dataInDTO.setCouponTypes(types);
    }

    private List<String> getReleaseCodes(ManagementDataInDTO dataInDTO, ActivityModel tPromoActivityVO) {

        String activityCode = "";
        List<String> releaseCodes = new ArrayList<>();
        if (null != tPromoActivityVO) {
            activityCode = tPromoActivityVO.getActivityCode();
        }

        List<CouponReleaseModel> releaseVOs = promoCouponReleaseService.queryCouponReleaseActivityCode(dataInDTO.getTenantCode(),
            dataInDTO.getReleaseStartTime(), dataInDTO.getReleaseEndTime(), activityCode);

        releaseVOs.forEach(x -> releaseCodes.add(x.getReleaseCode()));
        return releaseCodes;
    }

    private ActivityModel getActivity(ManagementDataInDTO dataInDTO){
        ActivityModel tPromoActivityVO = new ActivityModel();
        if (StringUtil.isNotBlank(dataInDTO.getActivityCode())){
            tPromoActivityVO = activityService.findActivity(dataInDTO.getTenantCode(), dataInDTO.getActivityCode(), null);
        }
        return tPromoActivityVO;
    }

    public PageData<ManagementDataOutDTO> managementMethods(ManagementDataInDTO dataInDTO,ActivityModel tPromoActivityVO){
        //用户表逻辑
        if (StringUtil.isNotBlank(dataInDTO.getUserCode()) || StringUtil.isNotBlank(dataInDTO.getUsedRefId()) || StringUtil.isNotBlank(dataInDTO.getReceiveStartTime()) || StringUtil.isNotBlank(dataInDTO.getUsedStartTime())){
            return couponInnerCodeDomain.queryManagementUserCouponCodeData(dataInDTO, tPromoActivityVO);
        }
        return couponInnerCodeDomain.queryManagementInnerCouponCodeData(dataInDTO, tPromoActivityVO);
    }

    private PageData<ManagementDataOutDTO> getPageInfo(List<ManagementDataOutDTO> infos,PageData<TPromoCouponCodeUserVO> pageInfo){
        PageInfo<ManagementDataOutDTO> pageInfo2 = new PageInfo<>(infos);
        pageInfo2.setList(infos);
        pageInfo2.setTotal(pageInfo.getTotal());
        return new PageData<>(pageInfo2.getList(), pageInfo2.getTotal());
    }

    @Transactional
    public int bindingUserAndCoupon(BindingCouponInDTO bindingCouponInDTO){
        ActivityModel activityVO = activityService.findActivityByActivityCode(bindingCouponInDTO.getTenantCode(), bindingCouponInDTO.getActivityCode());
        Check.check(null == activityVO, TPromoActivityChecker.NOT_NULL);
        Check.check(activityVO.getActivityBegin().compareTo(bindingCouponInDTO.getValidStartTime())> 0, CouponAllocateChecker.START_TIME);
        Check.check(activityVO.getActivityEnd().compareTo(bindingCouponInDTO.getValidEndTime())< 0, CouponAllocateChecker.END_TIME);

        ActivityModel couponActivity = promoCouponActivityService.findEffectiveActivity(bindingCouponInDTO.getTenantCode(), bindingCouponInDTO.getActivityCode());
        getLimitUserKey(bindingCouponInDTO, activityVO, couponActivity);
        List<CouponReleaseModel> releaseVOs = promoCouponReleaseService.queryCouponRelease(activityVO.getActivityCode());
        Check.check(CollectionUtils.isEmpty(releaseVOs), CouponAllocateChecker.NO_RELEASE);
        Optional<CouponReleaseModel> optional = releaseVOs.stream().max(Comparator.comparing(CouponReleaseModel::getReleaseCode));
        CouponReleaseModel releaseVO = null;
        if (optional.isPresent()){
          releaseVO = optional.get();
        }
        Check.check(null == releaseVO, CouponAllocateChecker.NO_RELEASE);
        Check.check(CouponTypeEnum.PROMOTION_CODE.code().equals(releaseVO.getCouponType()), CouponAllocateChecker.NOT_DISCOUNT_CODE);//NOSONAR
        List<UserAndCoupon> userCoupons = bindingCouponInDTO.getUserCoupons();   
        ConditionAndFace conditionAndFace = functionParamDomain.findConditionAndFaceByActivityCode(activityVO.getActivityCode());

        String templateCode = activityVO.getTemplateCode();
        String substring = templateCode.substring(templateCode.length() - 2);
        List<TPromoCouponInnerCodeVO> innerCodeVOs = new ArrayList<>();
        for (UserAndCoupon userAndCoupon : userCoupons){
            Check.check(StringUtil.isBlank(userAndCoupon.getCouponCode()), CouponAllocateChecker.NOT_NULL_COUPON_CODE);
            //添加数据
            TPromoCouponInnerCodeVO vo = new TPromoCouponInnerCodeVO();
            if (CouponTypeEnum.ANONYMITY_COUPON.code().equals(releaseVO.getCouponType())){
                Check.check(StringUtil.isNotBlank(userAndCoupon.getUserCode()), CouponAllocateChecker.USER_NULL);
                vo.setStatus(CouponStatusEnum.UN_GRANT.code());

            }else {
                Check.check(StringUtil.isBlank(userAndCoupon.getUserCode()), CouponAllocateChecker.NOT_USER_NULL);
                //用户表
                TPromoCouponCodeUserVO userVO = new TPromoCouponCodeUserVO();
                userVO.setActivityCode(activityVO.getActivityCode());
                userVO.setCouponCode(userAndCoupon.getCouponCode());
                userVO.setCouponType(releaseVO.getCouponType());
                if (FaceUnitEnum.DISCOUNT.code().equals(substring)){
                    userVO.setFaceUnit(conditionAndFace.getFaceUnit());
                    userVO.setFaceValue(conditionAndFace.getFaceValue().divide(new BigDecimal(10)));
                }else{
                    userVO.setFaceValue(conditionAndFace.getFaceValue());
                    userVO.setFaceUnit(conditionAndFace.getFaceUnit());
                }
                userVO.setFrozenStatus(CouponFrozenStatusEnum.UN_FROZEN.code());
                userVO.setReceivedTime(DateUtil.format(DateUtil.now(), DateUtil.FORMAT_YYYYMMDDHHMISS_14));
                userVO.setValidStartTime(bindingCouponInDTO.getValidStartTime());
                userVO.setValidEndTime(bindingCouponInDTO.getValidEndTime());
                userVO.setReleaseCode(releaseVO.getReleaseCode());
                userVO.setStatus(CouponStatusEnum.GRANTED.code());
                userVO.setTakeLabel(TakeLabelEnum.OTHER.code());
                userVO.setTenantCode(activityVO.getTenantCode());
                userVO.setUserCode(userAndCoupon.getUserCode());
                userVO.setOpsType(activityVO.getOpsType());
                promoCouponCodeUserService.insert(Arrays.asList(userVO));
                vo.setStatus(CouponStatusEnum.GRANTED.code());

            }
            vo.setActivityCode(activityVO.getActivityCode());
            vo.setCouponCode(userAndCoupon.getCouponCode());
            vo.setCouponType(releaseVO.getCouponType());
            if (FaceUnitEnum.DISCOUNT.code().equals(substring)){
                vo.setFaceValue(conditionAndFace.getFaceValue().divide(new BigDecimal(10)));
                vo.setFaceUnit(conditionAndFace.getFaceUnit());
            }else{
                vo.setFaceValue(conditionAndFace.getFaceValue());
                vo.setFaceUnit(conditionAndFace.getFaceUnit());
            }
            vo.setFrozenStatus(CouponFrozenStatusEnum.UN_FROZEN.code());
            vo.setReceiveEndTime(releaseVO.getReceiveEndTime());
            vo.setReceiveStartTime(releaseVO.getReceiveStartTime());
            vo.setReleaseCode(releaseVO.getReleaseCode());
            vo.setTakeLabel(TakeLabelEnum.OTHER.code());
            vo.setTenantCode(activityVO.getTenantCode());
            innerCodeVOs.add(vo);
        }
        promoCouponInnerCodeService.insertPromoCouponInnerCode(innerCodeVOs);
        return innerCodeVOs.size();
    }

    public void getLimitUserKey(BindingCouponInDTO bindingCouponInDTO, ActivityModel activityVO, ActivityModel couponActivity) {
        if (null != couponActivity && null !=couponActivity.getUserLimitMax() && couponActivity.getUserLimitMax().intValue() >=1){
            // 限领缓存
            for(UserAndCoupon user : bindingCouponInDTO.getUserCoupons()) {
                for (int i = 0; i < bindingCouponInDTO.getUserCoupons().size(); i++) {
                    this.limitCouponCodeUser(activityVO.getTenantCode(), user.getUserCode(), activityVO.getActivityCode(), activityVO.getActivityEnd(), couponActivity.getUserLimitMax());
                }
            }
        }
    }

    public List<TPromoCouponInnerCodeVO> findActivityCodeByCouponCode(String tenantCode, String couponCode){

        String[] split = couponCode.split(",");
        List<String> list = Arrays.asList(split);

        return promoCouponInnerCodeService.queryActivityByCouponCodes(tenantCode, list);
    }

    public List<TPromoCouponCodeUserVO> queryActivityByCouponCodes(String tenantCode, List<String> couponList){

        return promoCouponCodeUserService.queryUserCouponInfo(tenantCode, couponList);
    }

    public List<ExportCouponRelationResult> exportCouponRelationInfo(ExportCouponRelationDto dto){

        List<CouponInnerRelationVO> couponInnerRelationVOS = promoCouponInnerCodeService.exportCouponRelationInfo(dto);

        List<String> releaseCodeList = couponInnerRelationVOS.stream().map(CouponInnerRelationVO::getReleaseCode).distinct().collect(Collectors.toList());

        List<CouponReleaseDomain> releaseDomains = promoCouponReleaseService.queryReleaseByCondition(dto.getTenantCode(), dto.getActivityCode(), releaseCodeList);

        Map<String, Integer> releaseValidDayMap = releaseDomains.stream().filter(x -> null != x.getValidDays())
                .collect(Collectors.toMap(CouponReleaseDomain::getReleaseCode, CouponReleaseDomain::getValidDays));

        //处理couponCode 对应的相关信息
        List<String> couponCodeList = couponInnerRelationVOS.stream().map(CouponInnerRelationVO::getCouponCode).collect(Collectors.toList());
        List<TPromoCouponCodeUserVO> userVOS = promoCouponCodeUserService.queryUserCouponInfo(dto.getTenantCode(), couponCodeList);

        List<String> userCodeList = userVOS.stream().map(TPromoCouponCodeUserVO::getUserCode).distinct().collect(Collectors.toList());
        Map<String, String> couponUserMap = userVOS.stream().collect(Collectors.toMap(TPromoCouponCodeUserVO::getCouponCode, TPromoCouponCodeUserVO::getUserCode));

        Map<String, String> userAccountMap = new HashMap<>();

        if(!CollectionUtils.isEmpty(userCodeList)) {
            //查询用户账号
            Result<List<QueryUserResult>> queryUserResult = couponCodeUserComponent.queryUserList(userCodeList.stream().map(String::valueOf).collect(Collectors.joining(",")),
                    dto.getTenantCode(), dto.getDomainCode());
            log.info("queryUserResult:{}", JSON.toJSONString(queryUserResult));
            if (Boolean.TRUE.equals(queryUserResult.isSuccess()) && !CollectionUtils.isEmpty(queryUserResult.getData())) {
                queryUserResult.getData().forEach(user -> userAccountMap.put(user.getUserCode(), user.getAccount()));
            }
        }

        Map<String, String> couponOrderMap = userVOS.stream().collect(Collectors.toMap(TPromoCouponCodeUserVO::getCouponCode, TPromoCouponCodeUserVO::getUsedRefId));

        List<ExportCouponRelationResult> results = new ArrayList<>();

        for (CouponInnerRelationVO vo : couponInnerRelationVOS) {
            ExportCouponRelationResult result = BeanCopyUtils.jsonCopyBean(vo, ExportCouponRelationResult.class);

            Integer validDay = releaseValidDayMap.get(vo.getReleaseCode());
            result.setValidDays(validDay);
            result.setStatus(CouponStatusEnum.getByCode(vo.getStatus()).desc());
            String orderCode = couponOrderMap.get(vo.getCouponCode());
            result.setOrderCode("default".equalsIgnoreCase(orderCode)? null:orderCode);

            String userCode = couponUserMap.get(vo.getCouponCode());
            if (StringUtil.isNotEmpty(userCode)){
                String account = userAccountMap.get(userCode);
                result.setAccount(account);
                result.setUserCode(userCode);
            }

            results.add(result);
        }

        return results;
    }


}
