package com.gtech.promotion.component.activity;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Set;
import java.util.TreeMap;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.gtech.basic.idm.web.vo.result.QueryOpUserAccountListResult;
import com.gtech.basic.idm.web.vo.result.QueryUserResult;
import com.gtech.basic.masterdata.client.MasterDataClient;
import com.gtech.basic.masterdata.response.JsonResult;
import com.gtech.commons.code.LogicDeleteEnum;
import com.gtech.commons.exception.Exceptions;
import com.gtech.commons.result.Result;
import com.gtech.commons.utils.BeanCopyUtils;
import com.gtech.commons.utils.CheckUtils;
import com.gtech.commons.utils.DateUtil;
import com.gtech.commons.utils.StringUtil;
import com.gtech.promotion.calc.CalcConstants;
import com.gtech.promotion.calc.function.FunctionEnum;
import com.gtech.promotion.checker.activity.GiveawayChecker;
import com.gtech.promotion.checker.activity.TPromoActivityChecker;
import com.gtech.promotion.checker.activity.TPromoIncentiveLimitedChecker;
import com.gtech.promotion.checker.activity.TPromoProductChecker;
import com.gtech.promotion.checker.activity.TPromoRuleFuncParamChecker;
import com.gtech.promotion.checker.activity.TPromoTemplateChecker;
import com.gtech.promotion.code.PromotionConstants;
import com.gtech.promotion.code.activity.ActivityStatusEnum;
import com.gtech.promotion.code.activity.ActivityTagCodeEnum;
import com.gtech.promotion.code.activity.ActivityTypeEnum;
import com.gtech.promotion.code.activity.FuncTypeEnum;
import com.gtech.promotion.code.activity.FunctionParamTypeEnum;
import com.gtech.promotion.code.activity.GiveawayTypeEnum;
import com.gtech.promotion.code.activity.IncentiveLimitedFlagEnum;
import com.gtech.promotion.code.activity.OperationTypeEnum;
import com.gtech.promotion.code.activity.ProductAttributeTypeEnum;
import com.gtech.promotion.code.activity.QualificationCodeEnum;
import com.gtech.promotion.code.activity.StoreParamTypeEnum;
import com.gtech.promotion.code.activity.StoreTypeEnum;
import com.gtech.promotion.code.coupon.CouponTypeEnum;
import com.gtech.promotion.code.coupon.ReleaseTimeSameActivityEnum;
import com.gtech.promotion.component.coupon.CouponActivityComponent;
import com.gtech.promotion.component.feign.IdmFeignClientComponent;
import com.gtech.promotion.dao.model.activity.ActivityFunctionParamRankModel;
import com.gtech.promotion.dao.model.activity.ActivityGroupPriorityVO;
import com.gtech.promotion.dao.model.activity.ActivityLanguageModel;
import com.gtech.promotion.dao.model.activity.ActivityModel;
import com.gtech.promotion.dao.model.activity.ActivityPeriodModel;
import com.gtech.promotion.dao.model.activity.FunctionParamModel;
import com.gtech.promotion.dao.model.activity.GiveawayVO;
import com.gtech.promotion.dao.model.activity.OperationLogModel;
import com.gtech.promotion.dao.model.activity.PromoGroupVO;
import com.gtech.promotion.dao.model.activity.QualificationModel;
import com.gtech.promotion.dao.model.activity.TPromoActivityProductDetailVO;
import com.gtech.promotion.dao.model.activity.TPromoActivityProductVO;
import com.gtech.promotion.dao.model.activity.TPromoActivityStoreVO;
import com.gtech.promotion.dao.model.activity.TPromoIncentiveLimitedVO;
import com.gtech.promotion.dao.model.activity.TemplateFunctionModel;
import com.gtech.promotion.dao.model.activity.TemplateModel;
import com.gtech.promotion.dao.model.coupon.CouponReleaseModel;
import com.gtech.promotion.dao.model.coupon.TPromoCouponInnerCodeVO;
import com.gtech.promotion.dao.model.marketing.MarketingModel;
import com.gtech.promotion.domain.activity.ActivityDomain;
import com.gtech.promotion.domain.coupon.CouponReleaseDomain;
import com.gtech.promotion.dto.cache.ActivityCacheDTO;
import com.gtech.promotion.dto.cache.ProductSkuDetailDTO;
import com.gtech.promotion.dto.in.activity.ActivityExtendInDTO;
import com.gtech.promotion.dto.in.activity.ActivityUpdateDTO;
import com.gtech.promotion.dto.in.activity.SingleProductDTO;
import com.gtech.promotion.dto.in.activity.TPromoActivityListInDTO;
import com.gtech.promotion.dto.in.activity.TPromoActivityListMallInDTO;
import com.gtech.promotion.dto.in.activity.UpdateActivityStatusInDTO;
import com.gtech.promotion.dto.in.activity.UpdateExternalActivityInDTO;
import com.gtech.promotion.dto.out.activity.ActivityPriceDTO;
import com.gtech.promotion.dto.out.activity.SkuActivityPriceDTO;
import com.gtech.promotion.dto.out.activity.TPromoActivityListMallOutDTO;
import com.gtech.promotion.dto.out.activity.TPromoActivityListOutDTO;
import com.gtech.promotion.dto.out.activity.TPromoActivityOutDTO;
import com.gtech.promotion.dto.out.product.ProductDetailInDTO;
import com.gtech.promotion.exception.Check;
import com.gtech.promotion.exception.ErrorCodes;
import com.gtech.promotion.exception.PromotionException;
import com.gtech.promotion.helper.CodeHelper;
import com.gtech.promotion.helper.FunctionHelper;
import com.gtech.promotion.helper.QualificationFilter;
import com.gtech.promotion.helper.RedisOpsHelper;
import com.gtech.promotion.helper.TemplateHelper;
import com.gtech.promotion.page.RequestPage;
import com.gtech.promotion.service.activity.ActivityFuncParamService;
import com.gtech.promotion.service.activity.ActivityFuncRankService;
import com.gtech.promotion.service.activity.ActivityLanguageService;
import com.gtech.promotion.service.activity.ActivityPeriodService;
import com.gtech.promotion.service.activity.ActivityProductDetailService;
import com.gtech.promotion.service.activity.ActivityService;
import com.gtech.promotion.service.activity.ActivityStoreService;
import com.gtech.promotion.service.activity.GiveawayService;
import com.gtech.promotion.service.activity.OperationLogService;
import com.gtech.promotion.service.activity.PromotionGroupService;
import com.gtech.promotion.service.activity.QualificationService;
import com.gtech.promotion.service.activity.TPromoIncentiveLimitedService;
import com.gtech.promotion.service.activity.TemplateService;
import com.gtech.promotion.service.coupon.PromoCouponActivityService;
import com.gtech.promotion.service.coupon.PromoCouponCodeUserService;
import com.gtech.promotion.service.coupon.PromoCouponInnerCodeService;
import com.gtech.promotion.service.coupon.PromoCouponReleaseService;
import com.gtech.promotion.service.mongo.activity.TPromoProductService;
import com.gtech.promotion.utils.ActivityFilterUtil;
import com.gtech.promotion.utils.Constants;
import com.gtech.promotion.vo.bean.ActivityLanguage;
import com.gtech.promotion.vo.bean.ActivityPeriod;
import com.gtech.promotion.vo.bean.ActivityStore;
import com.gtech.promotion.vo.bean.FunctionParam;
import com.gtech.promotion.vo.bean.Giveaway;
import com.gtech.promotion.vo.bean.IncentiveLimited;
import com.gtech.promotion.vo.bean.ProductAttribute;
import com.gtech.promotion.vo.bean.ProductCodes;
import com.gtech.promotion.vo.bean.ProductDetail;
import com.gtech.promotion.vo.bean.ProductScope;
import com.gtech.promotion.vo.bean.Qualification;
import com.gtech.promotion.vo.param.activity.CustomCondition;
import com.gtech.promotion.vo.param.activity.QueryAfterDiscountItemParam;
import com.gtech.promotion.vo.param.activity.QueryAfterDiscountPriceParam;
import com.gtech.promotion.vo.param.activity.QueryListParam;
import com.gtech.promotion.vo.param.activity.QueryPromoListByStoreParam;
import com.gtech.promotion.vo.param.activity.QueryPromotionCategoryParam;
import com.gtech.promotion.vo.result.activity.DiscountPriceItemResult;
import com.gtech.promotion.vo.result.activity.DiscountPriceResult;
import com.gtech.promotion.vo.result.activity.QueryListResult;

import cn.hutool.core.map.MapUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * <功能描述>
 * 
 */
@Component
@Slf4j
public class ActivityComponentDomain {

	public static final int INT = 0;

	public static final String TENANT_GROUP_RELATION = "TENANT_GROUP_RELATION";

	@Autowired
	private TemplateService templateService;
	@Autowired
	private GiveawayService giveawayService;
	@Autowired
	private PromoCouponActivityService couponActivityService;
	@Autowired
	private PromoCouponReleaseService couponReleaseService;
	@Autowired
	private ActivityStoreService activityStoreService;
	@Autowired
	private TPromoProductService productService;
	@Autowired
	private ActivityService activityService;
	@Autowired
	private ActivityProductDetailService activityProductDetailService;
	@Autowired
	private TPromoIncentiveLimitedService incentiveLimitedService;
	@Autowired
	private PromoCouponInnerCodeService couponInnerCodeService;
	@Autowired
	private PromoCouponCodeUserService couponCodeUserService;
	@Autowired
	private ActivityFuncParamService activityFuncParamService;
	@Autowired
	private ActivityFuncRankService activityFuncRankService;
	@Autowired
	private QualificationService qualificationService;
	@Autowired
	private TemplateDomain templateDomain;
	@Autowired
	private ActivityCacheDomain activityCacheDomain;
	@Autowired
	private ProductDomain productDomain;
	@Autowired
	private ActivityStoreService storeService;// 店铺渠道
	@Autowired
	private ActivityPeriodService activityPeriodService;
	@Autowired
	private ActivityLanguageService languageService;// 语言
	@Autowired
	private ActivityExpireComponentDomain activityExpireComponentDomain;
	@Autowired
	private CouponActivityComponent couponActivityDomain;
	@Autowired
	private StringRedisTemplate redisTemplate;
	@Autowired
	private TemplateHelper templateHelper;
	@Autowired
	private OperationLogService operationLogService;
	@Autowired
	private RedisOpsHelper incentiveLimitedHelper;
	@Autowired
	private IdmFeignClientComponent idmFeignClientComponent;
	@Autowired
	private ActivityPriceComponentDomain activityPriceComponentDomain;
	@Autowired
	private com.gtech.promotion.service.marketing.MarketingService marketingService;

	@Autowired
	MasterDataClient masterDataClient;

	@Autowired
	private ActivityProductDetailService productDetailService;

	@Autowired
	private PromotionGroupService promotionGroupService;


	@Autowired
	private PromoGroupDomain promoGroupDomain;


	/**
	 * @return activityCode
	 */
	@Transactional
	public String createPromoActivity(ActivityDomain activityDomain) {

		ActivityModel activityModel = BeanCopyUtils.jsonCopyBean(activityDomain, ActivityModel.class);
		String tenantCode = activityDomain.getTenantCode();


		//这里检查是否有分组记录,如果没有分组则新建分组,并且默认与所有促销和营销互斥
		if (StringUtil.isNotBlank(activityDomain.getOpsType())){
			String groupCode = promoGroupDomain.queryGroupCode(activityDomain.getDomainCode(), tenantCode, activityDomain.getOpsType());
			activityModel.setGroupCode(groupCode);
		}


		/*boolean groupFlag = promoGroupDomain.checkGroupEnabledByTenantCode(tenantCode, activityDomain.getOpsType());

		if (groupFlag){
			//opsType 作为分组编码 直接关联活动
			activityModel.setGroupCode(activityDomain.getOpsType());
		}*/

		activityModel.setCreateUser(activityDomain.getCreateUser());
		// 根据模板编码 获取 模板id 放入活动对象TEMPLATE
		TemplateModel template = templateService.getTemplateByCode(activityDomain.getTemplateCode());
		Check.check(template == null, TPromoTemplateChecker.ILLEGAL_TEMPLATE_CODE);
		List<FunctionParamModel> funcList = BeanCopyUtils.jsonCopyList(activityDomain.getFuncParams(),
				FunctionParamModel.class);
		List<Giveaway> giveaways = activityDomain.getGiveaways();
		// 校验和排序函数列表
		funcList = sortFuncParams(funcList, template.getTemplateCode(), tenantCode, giveaways);
		// 生成活动编码
		String activityCode = CodeHelper.newActivityCode(template.getTagCode(),
				activityModel.getActivityType().substring(1, 2));
		activityModel.setActivityCode(activityCode);

		activityModel.setSponsors(activityDomain.getSponsors());
		activityModel.setTemplateCode(template.getTemplateCode());

		// 多语言信息处理
		this.languageDeal(activityModel, activityDomain.getActivityLanguages());
		if (StringUtil.isNotBlank(activityDomain.getActivityName())) {
			activityModel.setActivityName(activityDomain.getActivityName());
		}

		List<CustomCondition> customConditions = activityDomain.getCustomConditions();
		if (CollectionUtils.isNotEmpty(customConditions)) {
			String toJSONString = JSON.toJSONString(customConditions);
			activityModel.setCustomCondition(toJSONString);
		}
		this.activityService.createPromoActivity(activityModel);

		// 创建活动对应的资格
		createActivityQualification(activityDomain, tenantCode, activityCode);
		// 赠品创建
		if (FuncTypeEnum.IncentiveEnum.GIVEAWAY.equalsCode(activityDomain.getTemplateCode().substring(12, 16))) {
			checkGiveaway(giveaways, funcList, tenantCode);
			createGiveaway(tenantCode, giveaways, activityCode);
		}

		if (CollectionUtils.isNotEmpty(activityDomain.getProducts())) {
			// Product Scopes
			productService.insertProducts(activityDomain.getProducts(), activityCode, activityDomain.getTenantCode());
		}
		if (CollectionUtils.isNotEmpty(activityDomain.getProductDetails())) {

			// Product Details
			List<TPromoActivityProductDetailVO> tPromoActivityProductDetailVOS = getActivityProductDetailVOS(
					activityDomain.getProductDetails(), tenantCode, activityCode, 1);
			activityProductDetailService.insertProductSku(tPromoActivityProductDetailVOS);
		}
		// 商品黑名单
		if (CollectionUtils.isNotEmpty(activityDomain.getProductDetailBlackList())) {
			// Product Details
			List<TPromoActivityProductDetailVO> tPromoActivityProductDetailVOS = getActivityProductDetailVOS(
					activityDomain.getProductDetailBlackList(), tenantCode, activityCode, 2);
			activityProductDetailService.insertProductSku(tPromoActivityProductDetailVOS);
		}
		if (StringUtil.isNotBlank(activityDomain.getSkuToken())) {
			checkFileType(activityDomain);
		}
		createActivityPeriod(activityDomain, activityCode);
		// token为空时,无需保存sku数据,已不用
		productDomain.createProductSku(activityCode, activityDomain.getTenantCode(), activityDomain.getSkuToken());

		// 模板函数创建
		activityFuncParamService.saveTPromoRuleFuncParam(activityCode, template.getTemplateCode(), funcList);
		// 活动限制列表创建
		this.createIncentiveLimit(activityDomain, tenantCode, activityCode);
		// 活动渠道店铺 创建 自定义时
		this.createActivityStore(activityDomain, activityModel, tenantCode, activityCode);

		if (!activityModel.isCouponActivity()) {
			// 商品条件，字段容易超长
			activityDomain.setProductDetails(new ArrayList<>());
			activityDomain.setProducts(new ArrayList<>());
			operationLogService.insertLog(
					OperationLogModel.builder().tenantCode(tenantCode).activityCode(activityCode)
							.operationType(OperationTypeEnum.CREATION.code())
							.createFirstName(activityDomain.getOperateFirstName())
							.createLastName(activityDomain.getOperateLastName())
							.createUser(activityModel.getCreateUser()).build(),
					JSONObject.toJSONString(activityDomain));
		}
		return activityCode;
	}


	public void checkGiveaway(List<Giveaway> giveaways, List<FunctionParamModel> funcList, String tenantCode) {
		Check.check(CollectionUtils.isEmpty(giveaways), GiveawayChecker.NULL_LIST);
		List<Integer> giveawayRankList = giveaways.stream().map(Giveaway::getRankParam).collect(Collectors.toList());
		Boolean isExistRank = true;
		for (FunctionParamModel functionParamModel : funcList) {
			if (!giveawayRankList.contains(functionParamModel.getRankParam())) {
				isExistRank = false;
			}
		}
		Check.check(Boolean.FALSE.equals(isExistRank), GiveawayChecker.NULL_RANK_LIST);

		Map<Integer, List<String>> giveawayMap = new HashMap<>();
		for (Giveaway giveaway : giveaways) {
			Integer type = giveaway.getGiveawayType();
			if (CollectionUtils.isEmpty(giveawayMap.get(type))) {
				List<String> codeList = new ArrayList<>();
				giveawayMap.put(type, codeList);
			}
			giveawayMap.get(type).add(giveaway.getGiveawayCode());
		}

		Map<String, List<String>> errorMap = new HashMap<>();
		for (Map.Entry<Integer, List<String>> entry : giveawayMap.entrySet()) {
			String type = String.valueOf(entry.getKey());
			List<String> codeList = entry.getValue();
			if (GiveawayTypeEnum.COUPON.code().equals(type) && !CollectionUtils.isEmpty(codeList)) {
				List<ActivityModel> activityModels = activityService.queryActivityByActivityCodes(tenantCode, codeList);
				if (!CollectionUtils.isEmpty(activityModels)) {
					List<String> errorCodeList = activityModels.stream()
							.filter(p -> !p.getActivityStatus().equals(ActivityStatusEnum.EFFECTIVE.code()))
							.map(ActivityModel::getActivityCode).collect(Collectors.toList());
					if (!CollectionUtils.isEmpty(errorCodeList)) {
						errorMap.put(type, errorCodeList);
					}
				}
			} else if (GiveawayTypeEnum.LUCKY_DRAW.code().equals(type) && !CollectionUtils.isEmpty(codeList)) {
				List<MarketingModel> marketModels = marketingService.getMarketingByActivityCodeList(tenantCode,
						codeList);
				if (!CollectionUtils.isEmpty(marketModels)) {
					List<String> errorCodeList = marketModels.stream()
							.filter(p -> !p.getActivityStatus().equals(ActivityStatusEnum.EFFECTIVE.code()))
							.map(MarketingModel::getActivityCode).collect(Collectors.toList());
					if (!CollectionUtils.isEmpty(errorCodeList)) {
						errorMap.put(type, errorCodeList);
					}
				}
			}
		}
		if (errorMap.size() > 0) {
			throw new PromotionException(GiveawayChecker.ACTIVITY_STATUS_NOT_MATCH.getCode(),
					JSON.toJSONString(errorMap));
		}
	}

	private void createActivityPeriod(ActivityDomain activityDomain, String activityCode) {
		if (null != activityDomain.getActivityPeriod()) {
			ActivityPeriodModel activityPeriodModel = BeanCopyUtils.jsonCopyBean(activityDomain.getActivityPeriod(),
					ActivityPeriodModel.class);
			activityPeriodModel.setDomainCode(activityDomain.getDomainCode());
			activityPeriodModel.setTenantCode(activityDomain.getTenantCode());
			activityPeriodModel.setActivityCode(activityCode);
			activityPeriodModel.setCreateUser(activityDomain.getCreateUser());
			activityPeriodService.createPeriod(activityPeriodModel);
		}
	}

	public List<TPromoActivityProductDetailVO> getActivityProductDetailVOS(List<ProductDetailInDTO> list,
			String tenantCode, String activityCode, Integer type) {
		List<TPromoActivityProductDetailVO> tPromoActivityProductDetailVOS = new ArrayList<>();

		for (ProductDetailInDTO dto : list) {
			TPromoActivityProductDetailVO detailVO = BeanCopyUtils.jsonCopyBean(dto, TPromoActivityProductDetailVO.class);

			detailVO.setActivityCode(activityCode);
			detailVO.setTenantCode(tenantCode);
			detailVO.setLogicDelete(LogicDeleteEnum.NORMAL.code());
			detailVO.setType(type);
			String productUrl = dto.getProductUrl();
			String productNumber = dto.getProductNumber();
			String skuNumber = dto.getSkuNumber();
			String skuUrl = dto.getSkuUrl();

			JSONObject jsonObject = new JSONObject();
			jsonObject.put("productUrl",productUrl);
			jsonObject.put("productNumber",productNumber);
			jsonObject.put("skuNumber",skuNumber);
			jsonObject.put("skuUrl",skuUrl);
			detailVO.setExtendParam(jsonObject.toJSONString());

			tPromoActivityProductDetailVOS.add(detailVO);
		}
		return tPromoActivityProductDetailVOS;
	}

	public void checkFileType(ActivityDomain activityDomain) {

		String funcCode0411 = FuncTypeEnum.IncentiveEnum.EACH_FIXED_MONEY.code();
		String skuToken = activityDomain.getSkuToken();
		String withPrice = skuToken.substring(skuToken.length() - 1, skuToken.length());// 最后一位是withPrice
		Check.check("0".equals(withPrice) && funcCode0411.equals(activityDomain.getTemplateCode().substring(12, 16)),
				TPromoProductChecker.ERROR_ACTIVITY_TYPE_EACH_PRICE);// 0411的必须是1
		Check.check("1".equals(withPrice) && !funcCode0411.equals(activityDomain.getTemplateCode().substring(12, 16)),
				TPromoProductChecker.ERROR_ACTIVITY_TYPE_EACH_PRICE);// 非0411的不能是1
	}

	private void languageDeal(ActivityModel activitymodel, List<ActivityLanguage> activityLanguages) {

		languageService.deleteByActivityCode(activitymodel.getActivityCode());

		List<ActivityLanguageModel> languageModels = BeanCopyUtils.jsonCopyList(activityLanguages,
				ActivityLanguageModel.class);
		languageModels.forEach(x -> {
			x.setTenantCode(activitymodel.getTenantCode());
			x.setActivityCode(activitymodel.getActivityCode());
			languageService.insertOne(x);
		});

		activitymodel.setLanguage(languageModels.get(0));
	}

	public List<ActivityModel> queryValidActivities(String tenantCode, List<String> activityCodes, Date date) {

		if (StringUtils.isBlank(tenantCode) || CollectionUtils.isEmpty(activityCodes)) {
			return new ArrayList<>();
		}

		if (null == date) {
			date = new Date();
		}

		List<ActivityModel> resultList = new ArrayList<>();
		List<ActivityModel> activityModels = this.activityService.queryEffectiveActivity(tenantCode, activityCodes);
		for (ActivityModel e : activityModels) {
			activityExpireComponentDomain.expireActivity(e);
			if (e.isValid(date)) {
				resultList.add(e);
			}
		}

		return resultList;
	}

	public ActivityModel findActivityByActivityCode(String tenantCode, String language, String activityCode) {

		ActivityModel activityModel = this.activityService.findActivity(tenantCode, activityCode, null);
		Check.check(null == activityModel, TPromoActivityChecker.NOT_NULL);

		this.languageService.replaceField(activityModel, language);
		return activityModel;
	}

	public ActivityModel findValidActivity(String tenantCode, String activityCode, String language, Date date) {

		if (StringUtils.isBlank(tenantCode) || StringUtils.isBlank(activityCode)) {
			return null;
		}

		if (null == date) {
			date = new Date();
		}

		ActivityModel activityModel = this.activityService.findEffectiveActivity(tenantCode, activityCode);
		if (null == activityModel) {
			return null;
		}

		// 多语言
		if (StringUtils.isNotBlank(language)) {
			ActivityLanguageModel activityLanguage = languageService.findActivityLanguage(tenantCode, activityCode,
					language);
			if (null != activityLanguage) {
				activityModel.setLanguage(activityLanguage);
			}
		}

		activityExpireComponentDomain.expireActivity(activityModel);

		return activityModel.isValid(date) ? activityModel : null;
	}

	/**
	 * 判断活动是否有效的活动
	 */
	public boolean isEffectiveActivity(ActivityModel activityModel, Date date) {

		if (activityModel.isNeedToDoExpire()) {
			activityExpireComponentDomain.expireActivity(activityModel);
		}
		return activityModel.isValid(date);
	}

	/**
	 * 为活动加载多语言配置
	 */
	public void loadActivityLanguage(ActivityModel activityModel, String language) {

		if (null == activityModel || StringUtils.isBlank(language)) {
			return;
		}

		// 多语言
		ActivityLanguageModel activityLanguage = languageService.findActivityLanguage(activityModel.getTenantCode(),
				activityModel.getActivityCode(), language);
		if (null != activityLanguage) {
			activityModel.setLanguage(activityLanguage);
		}
	}

	/**
	 * <方法简单描述>
	 */
	@Transactional
	public int updatePromoActivity(ActivityUpdateDTO updateActivity) {

		String tenantCode = updateActivity.getTenantCode();
		String activityCode = updateActivity.getActivityCode();
		ActivityModel activity = activityService.findActivity(tenantCode, activityCode, null);
		Check.check(activity == null, TPromoActivityChecker.NULL_ACTIVITY_ENTITY);
		boolean needUpdate = true;
		if (ActivityStatusEnum.EFFECTIVE.equalsCode(activity.getActivityStatus()) && !activity.isNeedToDoExpire()) {

			updateActivity.setWarmBegin(null);
			updateActivity.setWarmEnd(null);
			updateActivity.setCoolDown(null);
			updateActivity.setIncentiveLimitedFlag(null);
			updateActivity.setTemplateCode(activity.getTemplateCode());
			updateActivity.setSponsors(null);
			updateActivity.setOpsType(null);
			updateActivity.setActivityUrl(null);
			updateActivity.setIncentiveLimiteds(null);
			updateActivity.setFuncParams(null);
			updateActivity.setActivityPeriod(null);
			needUpdate = false;
		}
		ActivityModel activitymodel = BeanCopyUtils.jsonCopyBean(updateActivity, ActivityModel.class);
		activitymodel.setId(activity.getId());
		List<Giveaway> giveaways = updateActivity.getGiveaways();
		TemplateModel template = templateService.getTemplateByCode(updateActivity.getTemplateCode());
		List<FunctionParamModel> funcList = BeanCopyUtils.jsonCopyList(updateActivity.getFuncParams(),
				FunctionParamModel.class);

		// 赠品先删除 再创建
		if (ActivityTagCodeEnum.GIVEAWAY.equalsCode(template.getTagCode())) {
			checkGiveaway(giveaways, funcList, tenantCode);
		}
		activitymodel.setTemplateCode(template.getTemplateCode());

		// 根据模板编码 获取 模板id 放入活动对象
		if (needUpdate) {
			Check.check(null == template, TPromoTemplateChecker.ILLEGAL_TEMPLATE_CODE);
			// 校验和排序函数列表
			funcList = sortFuncParams(funcList, template.getTemplateCode(), tenantCode, giveaways);
			// 活动限制列表先删除 创建
			incentiveLimitedService.deleteLimitedByActivityCode(activityCode);
			createIncentiveLimit(updateActivity, tenantCode, activityCode);

			// 活动参数列表先删除 创建
			activityFuncParamService.deleteRuleFuncParam111(activityCode);
			activityFuncParamService.saveTPromoRuleFuncParam(activityCode, template.getTemplateCode(), funcList);

			activityPeriodService.deletePeriod(tenantCode, activityCode);
			createActivityPeriod(updateActivity, activityCode);

		}

		// 填充自定义条件
		List<CustomCondition> customConditions = updateActivity.getCustomConditions();
		if (CollectionUtils.isNotEmpty(customConditions)) {
			String toJSONString = JSON.toJSONString(customConditions);
			activitymodel.setCustomCondition(toJSONString);
		} else {
			activitymodel.setCustomCondition(new JSONArray().toJSONString());
		}
		// 多语言信息处理 先删除 再创建
		this.languageDeal(activitymodel, updateActivity.getActivityLanguages());

		if (StringUtil.isNotBlank(updateActivity.getActivityName())) {
			activitymodel.setActivityName(updateActivity.getActivityName());
		}
		activityService.updatePromoActivity(activitymodel);

		// 资格先删除 再创建
		qualificationService.deleteQualifications(tenantCode, activityCode);
		createActivityQualification(updateActivity, tenantCode, activityCode);

		// 赠品先删除 再创建
		if (ActivityTagCodeEnum.GIVEAWAY.equalsCode(template.getTagCode())) {
			checkGiveaway(giveaways, funcList, tenantCode);

			giveawayService.deleteGiveaway(tenantCode, activityCode);
			createGiveaway(tenantCode, giveaways, activityCode);
		}

		// 活动渠道先删除 创建
		activityStoreService.deleteByActivityCode(activityCode);
		createActivityStore(updateActivity, activitymodel, tenantCode, activityCode);

		// 活动商品详细spu-sku列表先删除再创建，商品选择方式：1：文件上传；2：json数据
		activityProductDetailService.deleteProductDetails(activityCode, null);
		deleteProductDetail(updateActivity.getProductDetails(), activityCode);
		if (!CollectionUtils.isEmpty(updateActivity.getProductDetails())) {
			List<TPromoActivityProductDetailVO> tPromoActivityProductDetailVOS = getActivityProductDetailVOS(
					updateActivity.getProductDetails(), tenantCode, activityCode, 1);
			activityProductDetailService.insertProductSku(tPromoActivityProductDetailVOS);
		}
		if (!CollectionUtils.isEmpty(updateActivity.getProductDetailBlackList())) {
			List<TPromoActivityProductDetailVO> tPromoActivityProductDetailVOS = getActivityProductDetailVOS(
					updateActivity.getProductDetailBlackList(), tenantCode, activityCode, 2);
			activityProductDetailService.insertProductSku(tPromoActivityProductDetailVOS);
		}
		dealProducts(updateActivity, activityCode);

		if (ActivityTypeEnum.isActivity(updateActivity.getActivityType())) {
			// 商品条件，字段容易超长
			updateActivity.setProductDetails(new ArrayList<>());
			updateActivity.setProducts(new ArrayList<>());
			operationLogService.insertLog(OperationLogModel.builder().tenantCode(tenantCode).activityCode(activityCode)
					.operationType(OperationTypeEnum.EDITION.code()).createLastName(updateActivity.getOperateLastName())
					.createFirstName(updateActivity.getOperateFirstName()).createUser(updateActivity.getUpdateUser())
					.build(), JSONObject.toJSONString(updateActivity));

		}
		if (!needUpdate) {
			activityCacheDomain.updateCacheByActivityStatus(tenantCode, updateActivity.getActivityType(), activityCode,
					ActivityStatusEnum.EFFECTIVE.code(), ActivityStatusEnum.EFFECTIVE.code());
			return 11;
		}
		return 1;

	}

	/**
	 * 根据suqNum删除商品详细
	 */
	public void deleteProductDetail(List<ProductDetailInDTO> productDetails, String activityCode) {

		if (!CollectionUtils.isEmpty(productDetails)) {
			HashSet<Integer> set = new HashSet<>();
			for (ProductDetailInDTO productDetailInDTO : productDetails) {
				if (!set.contains(productDetailInDTO.getSeqNum())) {
					activityProductDetailService.deleteProductSkuBySeqNum(activityCode, productDetailInDTO.getSeqNum());
				}
				set.add(productDetailInDTO.getSeqNum());
			}
		}
	}

	private void dealProducts(ActivityUpdateDTO updateActivity, String activityCode) {

		List<ProductScope> productsOld = this.productService.getProducts(activityCode);
		this.productService.deleteProducts(activityCode);

		try {
			// 要修改的活动商品是 非全商品时 添加商品表数据
			List<ProductScope> products = updateActivity.getProducts();
			// 如果是sku上传的,但是商品传了数据(将商品全部删除)
			if (CollectionUtils.isNotEmpty(products)) {
				productService.insertProducts(products, activityCode, updateActivity.getTenantCode());
			}
			if (StringUtil.isNotBlank(updateActivity.getSkuToken())) {
				checkFileType(updateActivity);
			}
			// token为空时,无需保存sku数据，不为空时 表示上传文件或者删除之前的sku
			if (StringUtils.isNotBlank(updateActivity.getSkuToken())) {
				productDomain.createProductSku(activityCode, updateActivity.getTenantCode(),
						updateActivity.getSkuToken());
			}
			activityCacheDomain.putCache(updateActivity.getTenantCode(), activityCode);
		} catch (Exception e) {
			if (CollectionUtils.isNotEmpty(productsOld)) {
				productService.insertProducts(productsOld, activityCode, updateActivity.getTenantCode());
			}
			throw e;
		}
	}

	private void createGiveaway(String tenantCode, List<Giveaway> giveaways, String activityCode) {

		Set<String> countSet = new HashSet<>();// 计数器
		for (int i = 0; i < giveaways.size(); i++) {
			Giveaway giveaway = giveaways.get(i);
			giveaway.validate();
			GiveawayVO giveawayVO = BeanCopyUtils.jsonCopyBean(giveaway, GiveawayVO.class);
			giveawayVO.setActivityCode(activityCode);
			giveawayVO.setTenantCode(tenantCode);
			countSet.add(giveawayVO.getRankParam() + ":" + giveawayVO.getGiveawayCode());
			Check.check(countSet.size() < (i + 1), GiveawayChecker.DUPLICATE_SKU_CODE);// skucode重复
			giveawayService.createGiveaway(giveawayVO);
		}
	}

	private void createActivityStore(ActivityDomain activityDomain, ActivityModel promoActivityVO, String tenantCode,
			String activityCode) {

		if (StoreParamTypeEnum.STORE_CUSTOM.equalsCode(promoActivityVO.getStoreType())) {
			List<ActivityStore> channelStores = activityDomain.getChannelStores();
			List<TPromoActivityStoreVO> listChans = BeanCopyUtils.jsonCopyList(channelStores,
					TPromoActivityStoreVO.class);
			for (int i = 0; i < listChans.size(); i++) {
				listChans.get(i).setTenantCode(tenantCode);
				listChans.get(i).setActivityCode(activityCode);
				activityStoreService.createStore(listChans.get(i));
			}
		}
	}

	private void createIncentiveLimit(ActivityDomain activityDomain, String tenantCode, String activityCode) {

		if (IncentiveLimitedFlagEnum.YES.equalsCode(activityDomain.getIncentiveLimitedFlag())) {
			List<IncentiveLimited> limiteds = activityDomain.getIncentiveLimiteds();
			Check.check(CollectionUtils.isEmpty(limiteds), TPromoIncentiveLimitedChecker.NOT_NULL_LIMITATION_LIST);
			List<TPromoIncentiveLimitedVO> list = BeanCopyUtils.jsonCopyList(limiteds, TPromoIncentiveLimitedVO.class);
			incentiveLimitedService.insertLimitedList111(activityCode, list, tenantCode);
		}
	}

	private void createActivityQualification(ActivityDomain activityDomain, String tenantCode, String activityCode) {

		List<Qualification> qualifications = activityDomain.getQualifications();
		// 资格创建
		if (CollectionUtils.isNotEmpty(qualifications)) {
			List<QualificationModel> qualificationModels = new ArrayList<>();
			for (Qualification qualification : qualifications) {
				for (int i = 0; i < qualification.getQualificationValue().size(); i++) {
					QualificationModel qualificationModel = new QualificationModel();
					String s = qualification.getQualificationValue().get(i);
					qualificationModel.setDomainCode(activityDomain.getDomainCode());
					qualificationModel.setTenantCode(tenantCode);
					qualificationModel.setIsExclude(qualification.getIsExclude());
					qualificationModel.setActivityCode(activityCode);
					qualificationModel.setQualificationValue(s);
					qualificationModel.setQualificationCode(qualification.getQualificationCode());
					if (CollectionUtils.isNotEmpty(qualification.getQualificationValueName())
							&& qualification.getQualificationValueName().size() > i) {
						qualificationModel.setQualificationValueName(qualification.getQualificationValueName().get(i));
					}
					qualificationModels.add(qualificationModel);
				}
			}
			qualificationService.createQualifications(qualificationModels);
		}
	}

	/**
	 * 校验和排序函数列表
	 * 
	 * @param funcParams   函数列表
	 * @param templateCode 模板id
	 * @param tenantCode   租户编码
	 * @param giveaways
	 * @return List<TPromoActivityFuncParamVO> 根据03活动条件 排序层级的函数列表
	 */
	private List<FunctionParamModel> sortFuncParams(List<FunctionParamModel> funcParams, String templateCode,
			String tenantCode, List<Giveaway> giveaways) {

		Check.check(CollectionUtils.isEmpty(funcParams), TPromoRuleFuncParamChecker.NOT_NULL);
		Check.check(funcParams.size() % 4 != 0, TPromoRuleFuncParamChecker.ERROR_FUNCPARAM);// 模板函数列表必须是4的倍数

		TreeMap<String, FunctionParamModel> treeMap = new TreeMap<>();// 将函数模板按照03的paramVlaue排序 k:paramValue
																		// v:TPromoActivityFuncParamVO
		ArrayList<Integer> rankParams = new ArrayList<>();

		for (FunctionParamModel paramModel : funcParams) {
			TemplateFunctionModel templateFunction = this.checkFunction(paramModel, templateCode, tenantCode,
					rankParams, funcParams);
			// ParamType=01 空
			if (paramModel.getParamType().equals(FunctionParamTypeEnum.NULL.code())) {
				checkParamType01(treeMap, paramModel);

			} else if (FunctionParamTypeEnum.NUMBER.equalsCode(paramModel.getParamType())) {
				// ParamType=02 数值
				String paramValue = paramModel.getParamValue();
				String paramUnit = paramModel.getParamUnit();

				Check.check(FuncTypeEnum.SCOPE.equalsCode(paramModel.getFunctionType()),
						TPromoRuleFuncParamChecker.NOT_NULL_PARAM_VALUE);// 促销范围
				Check.check(FuncTypeEnum.CONDITION.equalsCode(paramModel.getFunctionType()),
						TPromoRuleFuncParamChecker.NOT_NULL_PARAM_VALUE);// 促销条件

				TemplateHelper.validateFunctionParam(paramModel.getFunctionCode(), paramUnit, paramValue);

				sortFuncParamsCheck(giveaways, treeMap, paramModel, paramValue);
			}
			paramModel.setFunctionName(templateFunction.getFunctionName());
		}

		List<FunctionParamModel> list = new ArrayList<>();
		// 根据03的条件值排序层级
		int c = 1;
		for (Entry<String, FunctionParamModel> entry : treeMap.entrySet()) {
			Integer value = entry.getValue().getRankParam();
			Check.check(Collections.frequency(rankParams, value) != 4, TPromoRuleFuncParamChecker.ERROR_RANKPARAM);
			for (int i = 0; i < funcParams.size(); i++) {
				if (funcParams.get(i).getRankParam().equals(value)) {
					FunctionParamModel paramVO = new FunctionParamModel();
					BeanUtils.copyProperties(funcParams.get(i), paramVO);
					paramVO.setRankParam(c);
					list.add(paramVO);
				}
			}
			c++;
		}
		return list;
	}

	private void sortFuncParamsCheck(List<Giveaway> giveaways, TreeMap<String, FunctionParamModel> treeMap,
			FunctionParamModel paramModel, String paramValue) {
		if (FuncTypeEnum.PARAM.equalsCode(paramModel.getFunctionType())) {
			// 条件值排序层级
			treeMap.put(String.format("%020f", Double.valueOf(paramValue)), paramModel);
		}
		if (FuncTypeEnum.IncentiveEnum.GIVEAWAY.equalsCode(paramModel.getFunctionCode())) {
			// 礼品赠送活动需要检测礼品信息
			Check.check(giveaways == null, GiveawayChecker.NULL_LIST);
		}
	}

	private TemplateFunctionModel checkFunction(FunctionParamModel paramModel, String templateCode, String tenantCode,
			ArrayList<Integer> rankParams, List<FunctionParamModel> funcParams) {

		Check.check(null == paramModel, TPromoRuleFuncParamChecker.NOT_NULL);

		FunctionHelper.validateFunctionCode(templateCode, paramModel.getFunctionCode());
		paramModel.validate();

		paramModel.setTenantCode(tenantCode);
		rankParams.add(paramModel.getRankParam());
		// 无条件、每满、各、捆绑都不能有多个层级,只有满 和 第 可以有多个层级 //020x
		Check.check(
				FuncTypeEnum.CONDITION.equalsCode(paramModel.getFunctionType())
						&& !FunctionEnum.canRank(paramModel.getFunctionCode()) && funcParams.size() > 4,
				TPromoRuleFuncParamChecker.ILLEGAL_EACH_FULL);
		// 校验函数参数
		TemplateFunctionModel templateFunction = FunctionHelper.getTemplateFunctionByCode(paramModel.getFunctionCode());
		if (null == templateFunction) {
			throw Exceptions.fail(ErrorCodes.PARAM_ERROR, "functionCode");
		}
		CheckUtils.isTrue(paramModel.getFunctionType().equals(templateFunction.getFunctionType()),
				ErrorCodes.PARAM_ERROR, "functionType");

		return templateFunction;
	}

	private void checkParamType01(TreeMap<String, FunctionParamModel> treeMap, FunctionParamModel temp) {

		// 参数类型为数值时参数值不能为空
		Check.check(FunctionEnum.F0302.equalsCode(temp.getFunctionCode()),
				TPromoRuleFuncParamChecker.NOT_NULL_PARAM_VALUE);
		Check.check(FunctionEnum.F0303.equalsCode(temp.getFunctionCode()),
				TPromoRuleFuncParamChecker.NOT_NULL_PARAM_VALUE);
		Check.check(
				FuncTypeEnum.INCENTIVE.equalsCode(temp.getFunctionType())
						&& !FunctionEnum.F0405.equalsCode(temp.getFunctionCode())
						&& !FunctionEnum.F0411.equalsCode(temp.getFunctionCode())
						&& !FunctionEnum.F0412.equalsCode(temp.getFunctionCode())
						&& !FunctionEnum.F0413.equalsCode(temp.getFunctionCode())
						&& !FunctionEnum.F0414.equalsCode(temp.getFunctionCode()),
				TPromoRuleFuncParamChecker.NOT_NULL_PARAM_VALUE);
		temp.setParamValue(null);
		temp.setParamUnit(null);
		if (temp.getFunctionType().equals(FuncTypeEnum.PARAM.code())) {
			treeMap.put("1", temp);
		}
	}

	public TPromoActivityOutDTO findActivityDetail(String tenantCode, String activityCode, String orgCode) {
		List<IncentiveLimited> promoLimiteds = new ArrayList<>();
		List<ActivityStore> channelStores = new ArrayList<>();
		List<FunctionParam> paramList = new ArrayList<>();

		ActivityModel activityModel = activityService.findActivity(tenantCode, activityCode, null);
		if (null == activityModel) {
			throw Exceptions.fail(ErrorCodes.VALIDATE_ACTIVITY_EXIST, activityCode);
		}

		// 活动函数层级
		List<ActivityFunctionParamRankModel> rankVOs = activityFuncRankService.getRankListByActivityCode(activityCode);
		setRank(paramList, rankVOs);

		// 限制信息填充
		promoLimiteds = getIncentiveLimited(activityCode, promoLimiteds, activityModel);

		// 渠道
		if (StoreParamTypeEnum.STORE_CUSTOM.equalsCode(activityModel.getStoreType())) {
			List<TPromoActivityStoreVO> stores = activityStoreService.getStoresByActivityCode(activityCode);
			if (StringUtil.isNotBlank(orgCode) && !matchStore(orgCode, stores, false)) {// 入参为空，跳过排除，直接返回所有店铺
				throw Exceptions.fail(ErrorCodes.NO_ORG_CODE_ACTIVITY_EXIST, orgCode);
			}
			channelStores = BeanCopyUtils.jsonCopyList(stores, ActivityStore.class);
		}

		// 资格信息
		List<QualificationModel> qualificationModels = qualificationService.queryQualifications(tenantCode,
				activityCode);
		List<Qualification> qualifications = QualificationModel.convert(qualificationModels);

		// 赠品
		List<GiveawayVO> giftVOs = giveawayService.getGiftListByActivityCode(tenantCode, activityCode);
		List<Giveaway> giveaways = BeanCopyUtils.jsonCopyList(giftVOs, Giveaway.class);
		addMissingGiveaway(giveaways, rankVOs);

		// 商品信息
		List<ProductScope> products = new ArrayList<>();
		setProductScope(activityCode, products);

		ActivityPeriodModel period = activityPeriodService.findPeriod(tenantCode, activityCode);

		// 获取所有商品详情
		List<ProductDetail> productDetilsList = new ArrayList<>();
		queryProductDetail(activityCode, productDetilsList);

		// 商品白名单
		List<ProductDetail> productDetils = new ArrayList<>();
		// 商品黑名单
		List<ProductDetail> productDetilBlackList = new ArrayList<>();

		productDetailList(productDetilsList, productDetils, productDetilBlackList);
		// 多语言信息
		List<ActivityLanguageModel> languageVOs = languageService.queryActivityLanguages(tenantCode, activityCode)
				.stream().sorted(Comparator.comparing(ActivityLanguageModel::getId)).collect(Collectors.toList());
		List<ActivityLanguage> languages = BeanCopyUtils.jsonCopyList(languageVOs, ActivityLanguage.class);

		String promotionCode = null;
		String promoPassword = null;
		String couponType = null;
		Integer userLimitMax = null;
		Integer userLimitMaxDay = null;
		if (ActivityTypeEnum.isCoupon(activityModel.getActivityType())) {
			ActivityModel couponActivity = couponActivityService.findCouponActivity(tenantCode, activityCode);
			userLimitMax = couponActivity.getUserLimitMax();
			userLimitMaxDay = couponActivity.getUserLimitMaxDay();
			couponType = couponActivity.getCouponType();
			if (couponType.equals(CouponTypeEnum.PROMOTION_CODE.code())) {
				List<TPromoCouponInnerCodeVO> tPromoCouponInnerCodeVOS = couponInnerCodeService
						.queryInnerCouponByReleaseCode(tenantCode, activityCode, null);
				promotionCode = tPromoCouponInnerCodeVOS.get(0).getCouponCode();
				promoPassword = tPromoCouponInnerCodeVOS.get(0).getPromoPassword();
			}
		}

		String auditConfig = getActivityAuditConfig(tenantCode);
		ActivityPeriod activityPeriod = BeanCopyUtils.jsonCopyBean(period, ActivityPeriod.class);
		TPromoActivityOutDTO promoActivityOutDTO = TPromoActivityOutDTO.builder()
				.orgCode(activityModel.getOrgCode())
				.activityCode(activityModel.getActivityCode()).backgroundImage(activityModel.getBackgroundImage())
				.activityUrl(activityModel.getActivityUrl())
				.externalActivityId(activityModel.getExternalActivityId())
				.incentiveLimitedFlag(activityModel.getIncentiveLimitedFlag())
				.itemScopeType(String.valueOf(activityModel.getItemScopeType()))
				.promotionCategoryName(activityModel.getPromotionCategoryName())
				.activityStatus(activityModel.getActivityStatus())
				.warmEnd(activityModel.getWarmEnd())
				.periodType(activityModel.getPeriodType())
				.tagCode(activityModel.getTagCode())
				.sponsors(activityModel.getSponsors())
				.priority(activityModel.getPriority())
				.showFlag(activityModel.getShowFlag())
				.templateCode(activityModel.getTemplateCode())
				.customCondition(activityModel.getCustomCondition())
				.createTime(activityModel.getCreateTime())
				.productCondition(activityModel.getProductCondition())
				.activityBegin(activityModel.getActivityBegin())
				.activityDesc(activityModel.getActivityDesc())
				.activityEnd(activityModel.getActivityEnd())
				.activityLabel(activityModel.getActivityLabel())
				.activityName(activityModel.getActivityName())
				.activityPeriod(activityPeriod)
				.channelStores(channelStores)
				.activityRemark(activityModel.getActivityRemark())
				.incentiveLimiteds(promoLimiteds)
				.couponType(couponType).activityLanguages(languages)
				.storeType(activityModel.getStoreType())
				.giveaways(giveaways).qualifications(qualifications)
				.products(products).productDetils(productDetils)
				.ribbonImage(activityModel.getRibbonImage())
				.ribbonPosition(activityModel.getRibbonPosition())
				.ribbonText(activityModel.getRibbonText())
				.productSelectionType(activityModel.getProductSelectionType())
				.coolDown(activityModel.getCoolDown())
				.opsType(activityModel.getOpsType())
				.warmBegin(activityModel.getWarmBegin())
				.promotionCode(promotionCode)
				.promoPassword(promoPassword)
				.funcParams(paramList)
				.productDetilBlackList(productDetilBlackList).auditUser(activityModel.getAuditUser())
				.userLimitMax(userLimitMax).userLimitMaxDay(userLimitMaxDay)
				.promotionCategory(activityModel.getPromotionCategory())
				.priceCondition(activityModel.getPriceCondition())
				.activityType(activityModel.getActivityType())
				.extendParams(activityModel.getExtendParams())
				.extImages(activityModel.getExtImages())
				.build();
		setAuditConfig(promoActivityOutDTO, auditConfig);
		setCommitUser(promoActivityOutDTO, activityModel);
		return promoActivityOutDTO;
	}

	//获取商品详情，并转换 extendParam json数据
	public void queryProductDetail(String activityCode, List<ProductDetail> productDetilsList) {
		List<TPromoActivityProductDetailVO> tPromoActivityProductDetailVOS = activityProductDetailService.queryProductSpuOrSku(activityCode);

		for (TPromoActivityProductDetailVO tPromoActivityProductDetailVO : tPromoActivityProductDetailVOS) {
			ProductDetail productDetail = BeanCopyUtils.jsonCopyBean(tPromoActivityProductDetailVO, ProductDetail.class);
			String extendParam = tPromoActivityProductDetailVO.getExtendParam();
			if (StringUtil.isNotEmpty(extendParam)) {
				JSONObject jsonObject = JSONObject.parseObject(extendParam);
				String productNumber = jsonObject.getString("productNumber");
				String productUrl = jsonObject.getString("productUrl");
				String skuNumber = jsonObject.getString("skuNumber");
				String skuUrl = jsonObject.getString("skuUrl");
				productDetail.setProductNumber(productNumber);
				productDetail.setProductUrl(productUrl);
				productDetail.setSkuNumber(skuNumber);
				productDetail.setSkuUrl(skuUrl);
			}
			productDetilsList.add(productDetail);
		}
	}

	//商品根据条件选项， 旧数据转变，spu属性出参替换
	public void setProductScope(String activityCode, List<ProductScope> products) {
		List<ProductScope> list = productService.getProducts(activityCode);
		products.addAll(list);
		for (ProductScope productScope : list) {
			if (ProductAttributeTypeEnum.SPU.code().equals(productScope.getAttrType())){
				List<ProductAttribute> attributes = productScope.getAttributes();
				productScope.setSpuAttributes(attributes);
				productScope.setAttributes(new ArrayList<>());
			}
		}
	}

	public void setCommitUser(TPromoActivityOutDTO promoActivityOutDTO, ActivityModel activityModel) {
		String activityStatus = promoActivityOutDTO.getActivityStatus();
		if (ActivityStatusEnum.CLOSURE.code().equals(activityStatus)
				|| ActivityStatusEnum.EFFECTIVE.code().equals(activityStatus)
				|| ActivityStatusEnum.END.code().equals(activityStatus)) {
			promoActivityOutDTO.setCommitUser(activityModel.getUpdateUser());
		}
	}

	public void setAuditConfig(TPromoActivityOutDTO promoActivityOutDTO, String auditConfig) {
		promoActivityOutDTO.setNeedAudit(isNeedAudit(auditConfig));
		promoActivityOutDTO.setNeedDifferentOperator(isAuditDifferentOperator(auditConfig));
	}

	public String isNeedAudit(String auditConfig) {
		if (StringUtil.isEmpty(auditConfig)) {
			return Constants.NEED_AUDIT_NO;
		}
		String[] configArray = auditConfig.split(",");
		if (configArray.length > 0) {
			return configArray[0];
		}
		return Constants.NEED_AUDIT_NO;
	}

	public String isAuditDifferentOperator(String auditConfig) {
		if (StringUtil.isEmpty(auditConfig)) {
			return Constants.NEED_DIFFERENT_NO;
		}
		String[] configArray = auditConfig.split(",");
		if (configArray.length == 1) {
			return Constants.NEED_DIFFERENT_NO;
		} else if (configArray.length == 2) {
			return configArray[1];
		}
		return Constants.NEED_DIFFERENT_NO;
	}

	public String getActivityAuditConfig(String tenantCode) {
		// log.info("start getActivityAuditConfig!tenantCode:{}", tenantCode);
		JsonResult<String> jsonResult = masterDataClient.getValueValue(tenantCode, Constants.ACTIVITY_AUDIT_CONFIG);
		if (null == jsonResult || StringUtil.isEmpty(jsonResult.getData())) {
			jsonResult = masterDataClient.getValueValue(Constants.SYSTEM_DEFAULT, Constants.ACTIVITY_AUDIT_CONFIG);
			if (null == jsonResult || StringUtil.isEmpty(jsonResult.getData())) {
				return null;
			}
		}
		// log.info("data:{}", jsonResult.getData());
		return jsonResult.getData();
	}

	public void addMissingGiveaway(List<Giveaway> giveawayList,
			List<ActivityFunctionParamRankModel> rankListByActivityCodes) {
		if (CollectionUtils.isEmpty(giveawayList)) {
			return;
		}
		Boolean isTriggerDefault = false;
		Set<Integer> rankSet = new HashSet<>();
		for (Giveaway giveaway : giveawayList) {
			if (null == giveaway.getRankParam()) {
				giveaway.setRankParam(1);
				isTriggerDefault = true;
			}
			rankSet.add(giveaway.getRankParam());
		}

		if (CollectionUtils.isEmpty(rankListByActivityCodes) || Boolean.FALSE.equals(isTriggerDefault)) {
			return;
		}

		List<Giveaway> addGiveawayList = new ArrayList<>();
		for (ActivityFunctionParamRankModel activityFunctionParamRankModel : rankListByActivityCodes) {
			if (!rankSet.contains(activityFunctionParamRankModel.getRankParam())) {
				for (Giveaway giveaway : giveawayList) {
					Giveaway giveawayCopy = BeanCopyUtils.jsonCopyBean(giveaway, Giveaway.class);
					giveawayCopy.setRankParam(activityFunctionParamRankModel.getRankParam());
					addGiveawayList.add(giveawayCopy);
				}
				rankSet.add(activityFunctionParamRankModel.getRankParam());
			}
		}
		giveawayList.addAll(addGiveawayList);
	}

	public List<IncentiveLimited> getIncentiveLimited(String activityCode, List<IncentiveLimited> promoLimiteds,
			ActivityModel activityModel) {
		if (IncentiveLimitedFlagEnum.YES.equalsCode(activityModel.getIncentiveLimitedFlag())) {
			List<TPromoIncentiveLimitedVO> limitedVOs = incentiveLimitedService
					.getLimitedListByActivityCode(activityCode);
			promoLimiteds = BeanCopyUtils.jsonCopyList(limitedVOs, IncentiveLimited.class);
		}
		return promoLimiteds;
	}

	public void productDetailList(List<ProductDetail> productDetilsList, List<ProductDetail> productDetils,
			List<ProductDetail> productDetilBlackList) {
		if (CollectionUtils.isNotEmpty(productDetilsList)) {
			for (ProductDetail productDetail : productDetilsList) {
				if (null != productDetail.getType() && 2 == productDetail.getType()) {
					productDetilBlackList.add(productDetail);
				} else {
					productDetils.add(productDetail);
				}
			}
		} else {
			productDetils.addAll(productDetilsList);
		}
	}

	public void setRank(List<FunctionParam> paramList, List<ActivityFunctionParamRankModel> rankVOs) {
		for (ActivityFunctionParamRankModel rankVO : rankVOs) {
			// 活动函数参数填充信息
			List<FunctionParamModel> params = activityFuncParamService.getRuleFuncParamListByRankId(rankVO.getId());
			for (FunctionParamModel param : params) {
				param.setRankParam(rankVO.getRankParam());
			}
			paramList.addAll(BeanCopyUtils.jsonCopyList(params, FunctionParam.class));
		}
	}

	/**
	 * 判断是否能匹配上任意一个orgCode
	 * 
	 * @param orgCode -- Store organization code.
	 * @param stores  店鋪集合
	 * @param flag    默认false
	 * @return Boolean ture：能匹配上
	 */
	public Boolean matchStore(String orgCode, List<TPromoActivityStoreVO> stores, Boolean flag) {

		for (TPromoActivityStoreVO storeVO : stores) {
			if (orgCode.equals(storeVO.getOrgCode())) {
				flag = true;
				break;
			}
		}
		return flag;
	}

	public PageInfo<TPromoActivityListOutDTO> queryAllActivity(TPromoActivityListInDTO inDTO, RequestPage page) {

		Map<String, String> tagMap = templateService.findTemplateTagAll();
		List<String> templeCodes = templetCodeDeal(inDTO.getTagCode(), tagMap);
		PageInfo<ActivityModel> list = activityService.queryAllActivity(inDTO, page, templeCodes);
		List<TPromoActivityListOutDTO> activityListOutDTOs = new ArrayList<>(page.getPageCount());
		PageInfo<TPromoActivityListOutDTO> tPromoActivityListOutDTOs = new PageInfo<>(activityListOutDTOs);
		tPromoActivityListOutDTOs.setTotal(list.getTotal());
		List<QueryOpUserAccountListResult> accountList = idmFeignClientComponent.getIdmOpUserAccount(list.getList());
		List<QueryUserResult> userAccountList = idmFeignClientComponent.queryIdmUserList(inDTO.getDomainCode(),
				inDTO.getTenantCode(), list.getList());
		String auditConfig = getActivityAuditConfig(inDTO.getTenantCode());
		String needAudit = isNeedAudit(auditConfig);
		String needDifferentOperator = isAuditDifferentOperator(auditConfig);

		List<String> collect = list.getList().stream().map(ActivityModel::getGroupCode).collect(Collectors.toList());
		List<PromoGroupVO> promoGroupVO = promotionGroupService.queryGroupByGroupCode(inDTO.getTenantCode(), collect);
		Map<String, String> collect1 = promoGroupVO.stream().collect(Collectors.toMap(PromoGroupVO::getGroupCode, PromoGroupVO::getGroupName));

		// 循环活动列表
		list.getList().forEach(x -> {
			List<TPromoActivityStoreVO> stores = storeService.getStoresByActivityCode(x.getActivityCode());
			List<ActivityStore> storeDTOs = BeanCopyUtils.jsonCopyList(stores, ActivityStore.class);
			String tagCode = "";
			for (Map.Entry<String, String> entry : tagMap.entrySet()) {
				if (entry.getValue().indexOf(x.getTemplateCode()) >= 0) {
					tagCode = entry.getKey();
				}
			}
			String groupName = null;
			if (StringUtil.isNotEmpty(x.getGroupCode())){
				groupName = collect1.get(x.getGroupCode());
			}
			TPromoActivityListOutDTO listOutDTO = new TPromoActivityListOutDTO(x, tagCode, storeDTOs, accountList,
					userAccountList, groupName);
			listOutDTO.setNeedAudit(needAudit);
			listOutDTO.setNeedDifferentOperator(needDifferentOperator);
			setPromoActivityListOutCommitUser(listOutDTO);
			listOutDTO.setTemplateCode(x.getTemplateCode());
			tPromoActivityListOutDTOs.getList().add(listOutDTO);
		});
		return tPromoActivityListOutDTOs;
	}

	public void setPromoActivityListOutCommitUser(TPromoActivityListOutDTO promoActivityListOutDTO) {
		String activityStatus = promoActivityListOutDTO.getActivityStatus();
		if (ActivityStatusEnum.CLOSURE.code().equals(activityStatus)
				|| ActivityStatusEnum.EFFECTIVE.code().equals(activityStatus)
				|| ActivityStatusEnum.END.code().equals(activityStatus)) {
			promoActivityListOutDTO.setCommitUser(promoActivityListOutDTO.getUpdateUser());
		}
	}

	/**
	 * 根据活动分类标签组装模板id
	 */
	private List<String> templetCodeDeal(String tagCodes, Map<String, String> tagMap) {

		if (StringUtil.isBlank(tagCodes)) {
			return new ArrayList<>();
		}

		List<String> templetCodes = new ArrayList<>();
		for (String tagCode : tagCodes.split(",")) {
			String templateCode = tagMap.get(tagCode);
			if (StringUtils.isNotBlank(templateCode)) {
				templetCodes.addAll(Arrays.asList(templateCode.split(",")));
			}
		}

		return templetCodes;
	}

	public SkuActivityPriceDTO activitySkuPrice(SingleProductDTO product,
			Map<String, ActivityCacheDTO> activityCacheMap) {

		// 单品无条件活动模板id
		String singletemplateCodes = templateDomain.findTemplateTagByTagId(ActivityTagCodeEnum.SINGLE.code());
		// 单品对象(出参)
		SkuActivityPriceDTO skuActivity = new SkuActivityPriceDTO();
		skuActivity.setSkuCode(product.getSkuCode());
		skuActivity.setProductCode(product.getProductCode());
		skuActivity.setCombineSkuCode(product.getCombineSkuCode());

		Map<String, ActivityCacheDTO> activityMap = ActivityFilterUtil.filterActivityByActivityType(activityCacheMap,
				ActivityTypeEnum.ACTIVITY);
		activityMap = ActivityFilterUtil.filterActivityByTime(activityMap, null);
		List<ProductSkuDetailDTO> productSkuDetailDTOS = activityProductDetailService
				.queryListByActivityCodesAndProductCode(activityMap.keySet(), product.getProductCode());
		activityMap = activityCacheDomain.filterActivityByProduct(activityMap, product, productSkuDetailDTOS);
		if (MapUtils.isEmpty(activityMap)) {
			return skuActivity;
		}
		boolean groupFlag = false;
		if (MapUtil.isNotEmpty(activityMap)){

			ActivityCacheDTO activityCacheDTO = activityMap.values().stream().collect(Collectors.toList()).get(0);
			String opsType = activityCacheDTO.getActivityModel().getOpsType();

			groupFlag = promoGroupDomain.checkGroupEnabledByTenantCode(product.getTenantCode(), opsType);
		}

		List<ActivityGroupPriorityVO> priorityVOS = new ArrayList<>();

		//获取所有单品活动
		TreeMap<String, ActivityCacheDTO> treeMap = new TreeMap<>();
		Iterator<Entry<String, ActivityCacheDTO>> iterator = activityMap.entrySet().iterator();
		while (iterator.hasNext()) {
			ActivityCacheDTO calcActivity = iterator.next().getValue();
			ActivityModel activityVO = calcActivity.getActivityModel();
			if (singletemplateCodes.indexOf(activityVO.getTemplateCode() + "") >= 0) {// 单品无条件活动
				treeMap.put(activityVO.getActivityCode(), calcActivity);
				if (groupFlag){
					ActivityGroupPriorityVO activityGroupPriorityVO = new ActivityGroupPriorityVO();
					activityGroupPriorityVO.setGroupCode(activityVO.getGroupCode());
					activityGroupPriorityVO.setPriority(activityVO.getPriority());
					PromoGroupVO promoGroupVO = promotionGroupService.getGroupByGroupCode(activityVO.getTenantCode(), activityVO.getGroupCode());
					activityGroupPriorityVO.setGroupPriority(promoGroupVO.getPriority());
					activityGroupPriorityVO.setActivityCode(activityVO.getActivityCode());
					priorityVOS.add(activityGroupPriorityVO);
				}
			}
		}

		String activityCode = null;
		//分组优先级，活动优先级排序
		if (CollectionUtils.isNotEmpty(priorityVOS)){
			List<ActivityGroupPriorityVO> collect = priorityVOS.stream().sorted(Comparator.comparing(ActivityGroupPriorityVO::getGroupPriority))
					.sorted(Comparator.comparing(ActivityGroupPriorityVO::getPriority)).collect(Collectors.toList());

			activityCode = collect.get(0).getActivityCode();
		}

		// 保留精度
		if (MapUtils.isNotEmpty(treeMap)) {

			Iterator<Entry<String, ActivityCacheDTO>> iterator1 = treeMap.entrySet().iterator();

			if (groupFlag){
				while (iterator1.hasNext()) {
					Entry<String, ActivityCacheDTO> next1 = iterator1.next();
					String key = next1.getKey();
					if (key.equals(activityCode)) {
						ActivityCacheDTO firstActivity = next1.getValue();
						setActivityProductPrice(product, skuActivity, activityMap, firstActivity);
						break;
					}
				}
			}else {
				ActivityCacheDTO firstActivity = treeMap.lastEntry().getValue();
				setActivityProductPrice(product, skuActivity, activityMap, firstActivity);
			}
		}

		return skuActivity;
	}

	public void setActivityProductPrice(SingleProductDTO product, SkuActivityPriceDTO skuActivity, Map<String, ActivityCacheDTO> activityMap, ActivityCacheDTO firstActivity) {
		ActivityModel promoActivity = firstActivity.getActivityModel();
		// 单品内单个活动对象
		ActivityPriceDTO activityPriceDTO = new ActivityPriceDTO();
		activityPriceDTO.setActivityCode(promoActivity.getActivityCode());
		activityPriceDTO.setActivityName(promoActivity.getActivityName());
		activityPriceDTO.setActivityLabel(promoActivity.getActivityLabel());
		activityPriceDTO.setActivityStartTime(promoActivity.getActivityBegin());
		activityPriceDTO.setActivityEndTime(promoActivity.getActivityEnd());
		activityPriceDTO.setWarmBegin(promoActivity.getWarmBegin());
		activityPriceDTO.setCoolDown(promoActivity.getCoolDown());
		activityPriceDTO.setOpsType(promoActivity.getOpsType());
		activityPriceDTO.setRibbonImage(promoActivity.getRibbonImage());
		activityPriceDTO.setRibbonPosition(promoActivity.getRibbonPosition());
		activityPriceDTO.setRibbonText(promoActivity.getRibbonText());
		activityPriceDTO.setActivityPeriod(
				BeanCopyUtils.jsonCopyBean(firstActivity.getPeriodModel(), ActivityPeriod.class));
		activityPriceDTO.setQualifications(qualificationDeal(firstActivity));
		activityPriceDTO.setSeqNum("0".equals(firstActivity.getSeqNum()) ? "1" : firstActivity.getSeqNum());
		ActivityCacheDTO activityCacheDTO = activityMap.get(promoActivity.getActivityCode());
		List<FunctionParamModel> funcParamList = activityCacheDTO.getPromoFuncParams();
		int precision = activityPriceComponentDomain.getTenantPrecision(promoActivity.getTenantCode());
		BigDecimal powerPrecision = activityPriceComponentDomain.getTenantPowerPrecision(promoActivity.getTenantCode());
		setPromotionPrice(product, precision, powerPrecision, activityPriceDTO, activityCacheDTO, funcParamList);
		ArrayList<ActivityPriceDTO> arrayList = new ArrayList<>();
		ActivityPriceDTO activityPriceDTO1 = new ActivityPriceDTO();
		BeanCopyUtils.copyProperties(activityPriceDTO, activityPriceDTO1);// 冗余一个，不复制的化 出参显示会有问题
		arrayList.add(activityPriceDTO1);
		skuActivity.setActivity(arrayList);
	}

	private void setPromotionPrice(SingleProductDTO product, int precision, BigDecimal powerPrecision, ActivityPriceDTO activityPriceDTO,
			ActivityCacheDTO activityCacheDTO, List<FunctionParamModel> funcParamList) {

		boolean flag = false;
		for (FunctionParamModel funcParam : funcParamList) {
			if (FuncTypeEnum.IncentiveEnum.REDUCE_AMOUNT.equalsCode(funcParam.getFunctionCode())) {// 减金额
				activityPriceDTO
						.setPromotionPrice(product.getPrice().subtract(new BigDecimal(funcParam.getParamValue()))
								.max(BigDecimal.ZERO).setScale(precision, CalcConstants.DEFAULT_ROUND));
				flag = true;
			}
			if (FuncTypeEnum.IncentiveEnum.DISCOUNT.equalsCode(funcParam.getFunctionCode())) {// 打折扣
				activityPriceDTO
						.setPromotionPrice(product.getPrice().multiply(new BigDecimal(funcParam.getParamValue()))
								.setScale(precision, getRoundingModeDiscount(precision)));
				flag = true;
			}
			if (FuncTypeEnum.IncentiveEnum.FIXED_MONEY.equalsCode(funcParam.getFunctionCode())) {// 单件固定金额
				activityPriceDTO.setPromotionPrice(
						new BigDecimal(funcParam.getParamValue()).setScale(precision, CalcConstants.DEFAULT_ROUND));
				flag = true;
			}
			if (setPromotionEachFixedMoneyPrice(product, precision, activityPriceDTO, activityCacheDTO, funcParam)) {
				flag = true;
			}
			if (powerPrecision != null && activityPriceDTO.getPromotionPrice() != null) {
				activityPriceDTO.setPromotionPrice(
						activityPriceDTO.getPromotionPrice().divide(powerPrecision).setScale(0, CalcConstants.ROUND_DOWN).multiply(powerPrecision));
			}
			if (flag) {
				break;
			}
		}
	}

	private boolean setPromotionEachFixedMoneyPrice(SingleProductDTO product, int precision,
			ActivityPriceDTO activityPriceDTO, ActivityCacheDTO activityCacheDTO, FunctionParamModel funcParam) {

		if (!FuncTypeEnum.IncentiveEnum.EACH_FIXED_MONEY.equalsCode(funcParam.getFunctionCode())) {// 每件不同特价
			return false;
		}
		Set<String> activityCodes = new HashSet<>();
		activityCodes.add(activityCacheDTO.getActivityModel().getActivityCode());
		List<ProductSkuDetailDTO> productSkuDetailDTOS = activityProductDetailService
				.queryListByActivityCodesAndProductCode(activityCodes, product.getProductCode());
		if (CollectionUtils.isNotEmpty(productSkuDetailDTOS)) {// 前面已经过滤了商品，这里应该是有该商品的
			for (ProductSkuDetailDTO x : productSkuDetailDTOS) {
				// 活动的skuCode是空或者all的直接符合，不为空的要和入参的skucode相等
				if (StringUtil.isBlank(x.getSkuCode()) || PromotionConstants.UNLIMITED.equals(x.getSkuCode())
						|| x.getSkuCode().equals(product.getSkuCode())) {
					activityPriceDTO
							.setPromotionPrice(x.getPromoPrice().setScale(precision, CalcConstants.DEFAULT_ROUND));
					break;
				}
			}
		}
		return true;
	}

	// 会员标签处理
	private List<Qualification> qualificationDeal(ActivityCacheDTO firstActivity) {
		return QualificationModel.convert(firstActivity.getQualificationModels());
	}

	/**
	 * 获取保留小数的方式
	 * 
	 * @return precision=2四折五入；其他向上取整
	 */
	public RoundingMode getRoundingModeDiscount(int precision) {

		if (precision == 2) {
			return RoundingMode.HALF_UP;
		} else {
			return RoundingMode.UP;
		}
	}

	@Transactional
	public void deleteActivityCorrelation(String tenantCode, String activityCode) {

		ActivityModel promoActivityVO = activityService.findActivity(tenantCode, activityCode, null);
		Check.check(promoActivityVO == null, TPromoActivityChecker.NULL_ACTIVITY_ENTITY);
		Check.check(
				!ActivityStatusEnum.PENDING.equalsCode(promoActivityVO.getActivityStatus())
						&& !ActivityStatusEnum.REJECTED.equalsCode(promoActivityVO.getActivityStatus()),
				TPromoActivityChecker.ERROR_DELETESTATUS);
		int info = activityService.deleteActivity111(tenantCode, activityCode);
		Check.check(info != 1, TPromoActivityChecker.ERROR_DELETE);
		couponActivityService.deleteCouponActivity(tenantCode, activityCode);
		couponInnerCodeService.deleteCouponInnerCode111(tenantCode, activityCode);
		couponReleaseService.deleteCouponRelease(tenantCode, activityCode);
		activityFuncParamService.deleteRuleFuncParam111(activityCode);
		languageService.deleteByActivityCode(activityCode);
		qualificationService.deleteQualifications(tenantCode, activityCode);
		giveawayService.deleteGiveaway(tenantCode, activityCode);
		incentiveLimitedService.deleteLimitedByActivityCode(activityCode);
		activityStoreService.deleteByActivityCode(activityCode);
		activityProductDetailService.deleteProductDetails(activityCode, null);

		List<ProductScope> productsOld = productService.getProducts(activityCode);
		productService.deleteProducts(activityCode);
		try {
			activityCacheDomain.delCache(tenantCode, promoActivityVO.getActivityType(), activityCode,
					promoActivityVO.getActivityStatus());
		} catch (Exception e) {
			// 非全商品时 添加商品表数据
			if (CollectionUtils.isNotEmpty(productsOld)) {
				productService.insertProducts(productsOld, activityCode, promoActivityVO.getTenantCode());
			}
			throw e;
		}
	}

	public List<TPromoActivityListMallOutDTO> queryMallAllActivity(TPromoActivityListMallInDTO inDTO) {

		List<TPromoActivityListMallOutDTO> outs = new ArrayList<>();
		List<ActivityModel> list = activityService.queryActivityByTenantCodeAndStatusAndTime(inDTO.getTenantCode(),
				ActivityStatusEnum.EFFECTIVE.code(), ActivityTypeEnum.ACTIVITY.code(), inDTO.getOrgCode());

		Iterator<ActivityModel> iterator = list.iterator();
		while (iterator.hasNext()) {
			ActivityModel activity = iterator.next();
			String activityCode = activity.getActivityCode();
			languageService.replaceField(activity, inDTO.getLanguage());
			boolean flag = true;

			List<TPromoActivityStoreVO> stores = activityStoreService.getStoresByActivityCode(activityCode);
			flag = storesMatch(inDTO, activity, flag, stores);

			List<QualificationModel> qualificationModels = qualificationService
					.queryQualifications(inDTO.getTenantCode(), activityCode);
			QualificationFilter qualificationFilter = new QualificationFilter(qualificationModels);
			if (flag && CollectionUtils.isNotEmpty(inDTO.getMemberTagCodes())) {
				List<QualificationModel> models = new ArrayList<>();
				for (String memberTagCode : inDTO.getMemberTagCodes()) {
					QualificationModel model = new QualificationModel();
					model.setQualificationCode(QualificationCodeEnum.MEMBER_TAG.code());
					model.setQualificationValue(memberTagCode);
					models.add(model);
				}
				flag = qualificationFilter.filter(models);
			}

			if (!flag) {
				iterator.remove();
			} else {
				TPromoActivityListMallOutDTO mallOutDTO = new TPromoActivityListMallOutDTO(activity,
						templateHelper.templateCode2TagCode(activity.getTemplateCode()),
						BeanCopyUtils.jsonCopyList(stores, ActivityStore.class),
						QualificationModel.convert(qualificationModels));
				outs.add(mallOutDTO);
			}
		}

		return outs;
	}

	@Transactional
	public void updateExternalActivityId(UpdateExternalActivityInDTO dto){

		activityService.updateExternalActivityId(dto);
	}

	@Transactional
	public int updateActivityStatus(UpdateActivityStatusInDTO updateActivityStatusInDTO) {
		String tenantCode = updateActivityStatusInDTO.getTenantCode();
		String activityCode = updateActivityStatusInDTO.getActivityCode();
		String activityStatus = updateActivityStatusInDTO.getActivityStatus();
		ActivityModel activityVO = activityService.findActivity(tenantCode, activityCode, null);
		Check.check(activityVO == null, TPromoActivityChecker.NULL_ACTIVITY_ENTITY);
		Check.check(ActivityStatusEnum.EFFECTIVE.equalsCode(activityStatus)
				&& DateUtil.parseDate(activityVO.getActivityEnd(), DateUtil.FORMAT_YYYYMMDDHHMISS_14)
						.getTime() <= System.currentTimeMillis(),
				TPromoActivityChecker.END_TIME_NOW);
		String auditConfig = this.getActivityAuditConfig(tenantCode);
		if (StringUtil.isEmpty(auditConfig) || Constants.NEED_AUDIT_NO.equals(isNeedAudit(auditConfig))) {
			Check.check(ActivityStatusEnum.IN_AUDIT.equalsCode(activityStatus),
					TPromoActivityChecker.TENANT_NOT_NEED_AUDIT);
		}
		String needDifferent = isAuditDifferentOperator(auditConfig);
		if (ActivityStatusEnum.IN_AUDIT.code().equals(activityStatus)
				&& Constants.NEED_DIFFERENT_YES.equals(needDifferent)) {
			Check.check(isContainOperator(activityVO.getCreateUser(), null, updateActivityStatusInDTO.getOperateUser()),
					TPromoActivityChecker.CREATE_AUDIT_NEED_DIFFERENT);
		}
		if (ActivityStatusEnum.EFFECTIVE.code().equals(activityStatus)
				&& Constants.NEED_DIFFERENT_YES.equals(needDifferent)) {
			Check.check(
					isContainOperator(activityVO.getCreateUser(), activityVO.getAuditUser(),
							updateActivityStatusInDTO.getOperateUser()),
					TPromoActivityChecker.CREATE_AUDIT_COMMIT_NEED_DIFFERENT);
		}

		int info = activityService.updateActivityStatus(tenantCode, activityCode, activityStatus,
				updateActivityStatusInDTO.getOperateUser());

		String operationType;
		if (ActivityStatusEnum.EFFECTIVE.code().equals(activityStatus)) {
			operationType = OperationTypeEnum.ACTIVATION.code();
		} else if (ActivityStatusEnum.IN_AUDIT.code().equals(activityStatus)) {
			operationType = OperationTypeEnum.APPROVAL.code();
		} else {
			operationType = ActivityStatusEnum.END.code().equals(activityStatus) ? OperationTypeEnum.TERMINATION.code()
					: OperationTypeEnum.COMPLETION.code();
		}

		operationLogService.insertLog(
				OperationLogModel.builder().tenantCode(tenantCode).activityCode(activityCode)
						.operationType(operationType).createLastName(updateActivityStatusInDTO.getOperateLastName())
						.createFirstName(updateActivityStatusInDTO.getOperateFirstName())
						.createUser(updateActivityStatusInDTO.getOperateUser()).build(),
				JSONObject.toJSONString(updateActivityStatusInDTO));

		activityCacheDomain.updateCacheByActivityStatus(tenantCode, activityVO.getActivityType(), activityCode,
				activityVO.getActivityStatus(), activityStatus);
		if (info == 1 && ActivityStatusEnum.END.equalsCode(activityStatus)
				&& ActivityTypeEnum.COUPON.equalsCode(activityVO.getActivityType())) {
			// 中止券活动的时候，删除redis中所有券码缓存
			couponActivityDomain.closeCoupon(tenantCode, activityCode);
			redisTemplate.delete(com.gtech.promotion.utils.Constants.PROMOTION_COUPON_CODE_CACHE + ":" + tenantCode
					+ ":" + activityCode);
		}
		return 1;
	}

	public Boolean isContainOperator(String createOp, String auditOp, String currentOp) {
		Set<String> opSet = new HashSet<>();
		if (!StringUtil.isEmpty(createOp)) {
			opSet.add(createOp);
		}
		if (!StringUtil.isEmpty(auditOp)) {
			opSet.add(auditOp);
		}
		return opSet.contains(currentOp);
	}

	private boolean storesMatch(TPromoActivityListMallInDTO inDTO, ActivityModel activity, boolean flag,
			List<TPromoActivityStoreVO> stores) {

		if ((StringUtil.isNotBlank(inDTO.getOrgCode()) || StringUtil.isNotBlank(inDTO.getChannelCode()))
				&& (StoreTypeEnum.CUSTOM.equalsCode(activity.getStoreType()) && !CollectionUtils.isEmpty(stores))) {
			for (TPromoActivityStoreVO storeVO : stores) {
				flag = false;
				if (inDTO.getOrgCode().equals(storeVO.getOrgCode())
						|| inDTO.getChannelCode().equals(storeVO.getChannelCode() + "")) {
					flag = true;
					break;
				}
			}
		}
		return flag;
	}

	@Transactional
	public int activityExtend(ActivityExtendInDTO dto) {
		ActivityModel activity = activityService.findActivityByActivityCode(dto.getTenantCode(), dto.getActivityCode());
		Check.check(
				activity.isNeedToDoExpire() || activity.getActivityStatus().equals(ActivityStatusEnum.END.code())
						|| activity.getActivityStatus().equals(ActivityStatusEnum.CLOSURE.code()),
				TPromoActivityChecker.ACTIVITY_END);
		long activityEnd = DateUtil.parseDate(activity.getActivityEnd(), DateUtil.FORMAT_YYYYMMDDHHMISS_14).getTime();
		long endTime = DateUtil.parseDate(dto.getEndTime(), DateUtil.FORMAT_YYYYMMDDHHMISS_14).getTime();
		Check.check(activityEnd >= endTime, TPromoActivityChecker.END_TIME_BEFORE_ACTIVITY_END_TIME);

		activity.setActivityEnd(dto.getEndTime());
		int update = activityService.updatePromoActivity(activity);

		List<TPromoIncentiveLimitedVO> limitedListByActivityCode = incentiveLimitedService
				.getLimitedListByActivityCode(dto.getActivityCode());
		for (TPromoIncentiveLimitedVO tPromoIncentiveLimitedVO : limitedListByActivityCode) {
			incentiveLimitedHelper.extendKey(tPromoIncentiveLimitedVO, endTime - System.currentTimeMillis());
		}
		if (activity.isCouponActivity()) {
			ActivityModel couponActivity = couponActivityService.findCouponActivity(dto.getTenantCode(),
					dto.getActivityCode());
			if (CouponTypeEnum.PROMOTION_CODE.equalsCode(couponActivity.getCouponType())) {
				List<CouponReleaseModel> list = couponReleaseService.queryCouponRelease(dto.getActivityCode());
				CouponReleaseModel couponReleaseModel = list.get(0);
				extendCoupon(dto, couponReleaseModel);
			} else {
				List<CouponReleaseModel> list = couponReleaseService.queryCouponRelease(dto.getActivityCode());
				for (CouponReleaseModel couponReleaseModel : list) {
					extendCoupon(dto, couponReleaseModel);
				}
			}
		}
		operationLogService.insertLog(OperationLogModel.builder().activityCode(activity.getActivityCode())
				.tenantCode(activity.getTenantCode()).createUser(dto.getOperateUser())
				.createLastName(dto.getOperateLastName()).createFirstName(dto.getOperateFirstName())
				.operationType(OperationTypeEnum.EXTENSION.code()).build(), JSONObject.toJSONString(dto));

		if (activity.isValid(new Date())) {
			activityCacheDomain.extendActivityCacheTime(activity);
		}
		return update;
	}

	private void extendCoupon(ActivityExtendInDTO dto, CouponReleaseModel couponReleaseModel) {
		CouponReleaseDomain couponReleaseDomain = new CouponReleaseDomain();
		couponReleaseDomain.setTenantCode(couponReleaseModel.getTenantCode());
		couponReleaseDomain.setActivityCode(couponReleaseModel.getActivityCode());
		couponReleaseDomain.setReleaseCode(couponReleaseModel.getReleaseCode());
		Boolean sameValidTime = false;
		Boolean sameReceiveTime = false;
		if (ReleaseTimeSameActivityEnum.SAME.getCode().equals(couponReleaseModel.getTimeSameActivity())) {
			couponReleaseDomain.setValidEndTime(dto.getEndTime());
			sameValidTime = true;
		}
		if (ReleaseTimeSameActivityEnum.SAME.getCode().equals(couponReleaseModel.getReceiveTimeSameActivity())) {
			couponReleaseDomain.setReceiveEndTime(dto.getEndTime());
			sameReceiveTime = true;
		}
		if (Boolean.TRUE.equals(sameValidTime) && Boolean.TRUE.equals(sameReceiveTime)) {
			couponReleaseService.updateCouponReleaseByReleaseCode(couponReleaseDomain);
			couponInnerCodeService.updateInnerCouponEndTime(dto.getTenantCode(), dto.getActivityCode(),
					couponReleaseModel.getReleaseCode(), dto.getEndTime());
			couponCodeUserService.updateCouponEndTime(dto.getTenantCode(), dto.getActivityCode(),
					couponReleaseModel.getReleaseCode(), dto.getEndTime());
		}
	}

	public PageInfo<TPromoActivityOutDTO> queryPromoListByStore(QueryPromoListByStoreParam param) {
		PageInfo<TPromoActivityOutDTO> pageInfo = activityService.queryPromoListByStore(param);
		if (CollectionUtils.isNotEmpty(pageInfo.getList())) {
			List<String> activityCodes = pageInfo.getList().stream().map(TPromoActivityOutDTO::getActivityCode)
					.collect(Collectors.toList());
			String language = param.getLanguage();
			String tenantCode = param.getTenantCode();
			// 赠品对象列表
			List<GiveawayVO> giveawayVOS = giveawayService.getGiftListByActivityCodes(tenantCode, activityCodes);
			Map<String, List<GiveawayVO>> giveawayMap = giveawayVOS.stream()
					.collect(Collectors.groupingBy(GiveawayVO::getActivityCode));

			// 商品对象列表
			List<TPromoActivityProductVO> promoProducts = productService.getPromoProductByActivityCodes(tenantCode,
					activityCodes);
			Map<String, List<TPromoActivityProductVO>> productMap = promoProducts.stream()
					.collect(Collectors.groupingBy(TPromoActivityProductVO::getActivityCode));
			// 多语言列表
			List<ActivityLanguageModel> languages = languageService.queryActivityLanguagesByActivityCodes(tenantCode, null,
					activityCodes);
			Map<String, List<ActivityLanguageModel>> languagesMap = languages.stream()
					.collect(Collectors.groupingBy(ActivityLanguageModel::getActivityCode));
			// SKU和套装商品列表
			List<ProductSkuDetailDTO> promoProductDetails = activityProductDetailService
					.getProductSkusByActivityCodes(tenantCode, activityCodes);
			Map<String, List<ProductSkuDetailDTO>> skuDetailMap = promoProductDetails.stream()
					.collect(Collectors.groupingBy(ProductSkuDetailDTO::getActivityCode));
			List<ActivityPeriodModel> periodModels = activityPeriodService.queryPeriodByActivityCodes(tenantCode,
					activityCodes);
			Map<String, List<ActivityPeriodModel>> periodMap = periodModels.stream()
					.collect(Collectors.groupingBy(ActivityPeriodModel::getActivityCode));
			for (TPromoActivityOutDTO activityOutDTO : pageInfo.getList()) {
				activityOutDTO.setGiveaways(
						BeanCopyUtils.jsonCopyList(giveawayMap.get(activityOutDTO.getActivityCode()), Giveaway.class));
				activityOutDTO.setProductDetils(BeanCopyUtils
						.jsonCopyList(skuDetailMap.get(activityOutDTO.getActivityCode()), ProductDetail.class));
				activityOutDTO.setProducts(BeanCopyUtils.jsonCopyList(productMap.get(activityOutDTO.getActivityCode()),
						ProductScope.class));
				activityOutDTO.setActivityPeriod(BeanCopyUtils
						.jsonCopyBean(periodMap.get(activityOutDTO.getActivityCode()), ActivityPeriod.class));
				if (CollectionUtils.isNotEmpty(languagesMap.get(activityOutDTO.getActivityCode()))) {
					activityLanguage(language, languagesMap, activityOutDTO);
				}
			}
		}
		return pageInfo;
	}

	public void activityLanguage(String language, Map<String, List<ActivityLanguageModel>> languagesMap,
			TPromoActivityOutDTO activityOutDTO) {
		for (ActivityLanguageModel activityLanguageModel : languagesMap.get(activityOutDTO.getActivityCode())) {
			if (language.equals(activityLanguageModel.getLanguage())) {
				if (StringUtils.isNotBlank(activityLanguageModel.getActivityLabel())) {
					activityOutDTO.setActivityLabel(activityLanguageModel.getActivityLabel());
				}
				if (StringUtils.isNotBlank(activityLanguageModel.getActivityName())) {
					activityOutDTO.setActivityName(activityLanguageModel.getActivityName());
				}
				if (StringUtils.isNotBlank(activityLanguageModel.getActivityDesc())) {
					activityOutDTO.setActivityDesc(activityLanguageModel.getActivityDesc());
				}
				if (StringUtils.isNotBlank(activityLanguageModel.getActivityRemark())) {
					activityOutDTO.setActivityRemark(activityLanguageModel.getActivityRemark());
				}
				break;
			}
		}
	}

	public PageInfo<QueryListResult> queryList(QueryListParam param) {
		return activityService.queryList(param);
	}

	@Transactional
	public int updateActivityProduct(ActivityUpdateDTO updateActivity) {
		String tenantCode = updateActivity.getTenantCode();
		String activityCode = updateActivity.getActivityCode();
		ActivityModel activity = activityService.findActivity(tenantCode, activityCode, null);
		Check.check(activity == null, TPromoActivityChecker.NULL_ACTIVITY_ENTITY);
		Check.check(!ActivityStatusEnum.EFFECTIVE.code().equals(activity.getActivityStatus()),
				TPromoActivityChecker.ACTIVITY_NOT_EFFECTIVE);

		ActivityModel activitymodel = BeanCopyUtils.jsonCopyBean(updateActivity, ActivityModel.class);
		activitymodel.setId(activity.getId());
		// 根据模板编码 获取 模板id 放入活动对象
		TemplateModel template = templateService.getTemplateByCode(activity.getTemplateCode());
		Check.check(null == template, TPromoTemplateChecker.ILLEGAL_TEMPLATE_CODE);

		activitymodel.setTemplateCode(template.getTemplateCode());
		activityService.updatePromoActivity(activitymodel);

		// 活动商品详细spu-sku列表先删除再创建，商品选择方式：1：文件上传；2：json数据
		activityProductDetailService.deleteProductDetails(activityCode, 1);
		List<ProductDetailInDTO> productDetails = updateActivity.getProductDetails();
		deleteProductDetail(productDetails, activityCode);
		if (!CollectionUtils.isEmpty(productDetails)) {
			for (ProductDetailInDTO productDetailInDTO : productDetails) {
				TPromoActivityProductDetailVO detailVO = BeanCopyUtils.jsonCopyBean(productDetailInDTO,
						TPromoActivityProductDetailVO.class);
				detailVO.setActivityCode(activityCode);
				detailVO.setTenantCode(tenantCode);
				detailVO.setType(1);
				activityProductDetailService.insertProductDetail(detailVO);
			}
		}
		// 黑名单
		List<ProductDetailInDTO> productDetailBlackList = updateActivity.getProductDetailBlackList();
		insertProducts(tenantCode, activityCode, productDetailBlackList, 2);
		dealProducts(updateActivity, activityCode);

		// 商品条件，字段容易超长
		updateActivity.setProductDetails(new ArrayList<>());
		updateActivity.setProducts(new ArrayList<>());
		operationLogService.insertLog(OperationLogModel.builder().tenantCode(tenantCode).activityCode(activityCode)
				.operationType(OperationTypeEnum.EDITION_PRODUCT.code())
				.createLastName(updateActivity.getOperateLastName())
				.createFirstName(updateActivity.getOperateFirstName()).createUser(updateActivity.getUpdateUser())
				.build(), JSONObject.toJSONString(updateActivity));

		return 1;
	}

	private void insertProducts(String tenantCode, String activityCode, List<ProductDetailInDTO> list, Integer type) {
		if (CollectionUtils.isNotEmpty(list)) {
			activityProductDetailService.deleteProductDetails(activityCode, type);
			for (ProductDetailInDTO productDetailInDTO : list) {
				TPromoActivityProductDetailVO detailVO = BeanCopyUtils.jsonCopyBean(productDetailInDTO,
						TPromoActivityProductDetailVO.class);
				detailVO.setActivityCode(activityCode);
				detailVO.setTenantCode(tenantCode);
				detailVO.setType(type);
				activityProductDetailService.insertProductDetail(detailVO);
			}
		}
	}

	public int expireActivity() {
		return activityService.expireActivity();
	}

	@Transactional
	public int updateActivityProductDetailBlackList(String tenantCode, List<ProductDetail> productDetailBlackList) {
		// 查询已经生效的活动
		List<ActivityModel> list = activityService.queryEffectiveActivityByTenantCode(tenantCode);
		if (CollectionUtils.isEmpty(list)) {
			throw Exceptions.fail(ErrorCodes.VALIDATE_ACTIVITY_EXIST);
		}
		List<TPromoActivityProductDetailVO> tPromoActivityProductDetailVOS = BeanCopyUtils
				.jsonCopyList(productDetailBlackList, TPromoActivityProductDetailVO.class);
		for (ActivityModel activityModel : list) {
			for (TPromoActivityProductDetailVO tPromoActivityProductDetailVO : tPromoActivityProductDetailVOS) {
				tPromoActivityProductDetailVO.setActivityCode(activityModel.getActivityCode());
				tPromoActivityProductDetailVO.setLogicDelete(LogicDeleteEnum.NORMAL.code());
				tPromoActivityProductDetailVO.setTenantCode(activityModel.getTenantCode());
			}
			activityProductDetailService.insertProductSku(tPromoActivityProductDetailVOS);
		}
		return 1;
	}

	public Result<DiscountPriceResult> queryAfterDiscountPrice(QueryAfterDiscountPriceParam param) {
		DiscountPriceResult discountPriceResult = new DiscountPriceResult();
		Map<String, ActivityCacheDTO> ruleCacheMap = activityCacheDomain.getActivityCacheMap(param.getTenantCode(),
				param.getLanguage(), 2, null);

		// 遍历购物车所有sku，绑定活动
		List<String> productCodes = new ArrayList<>();
		for (QueryAfterDiscountItemParam shoppingCartItem : param.getItemList()) {
			productCodes.add(shoppingCartItem.getProductCode());
		}
		List<ProductSkuDetailDTO> productSkuDetailDTOS = productDetailService
				.queryListByActivityCodesAndProductCodes(ruleCacheMap.keySet(), productCodes);

		Map<String, List<ProductSkuDetailDTO>> blackProductMap = productSkuDetailDTOS.stream()
				.filter(p -> 2 == p.getType()).collect(Collectors.groupingBy(ProductSkuDetailDTO::getActivityCode));
		// 设置小数点
		int precision = activityPriceComponentDomain.getTenantPrecision(param.getTenantCode());
		BigDecimal powerPrecision = activityPriceComponentDomain.getTenantPowerPrecision(param.getTenantCode());
		Map<String, BigDecimal> skuPriceMap = new HashMap<>();
		Map<String, ActivityCacheDTO> skuActivityMap = new HashMap<>();
		for (QueryAfterDiscountItemParam shoppingCartItem : param.getItemList()) {
			BigDecimal skuSalePrice = shoppingCartItem.getSalePrice();
			String skuCode = shoppingCartItem.getSkuCode();
			DiscountPriceItemResult discountPriceItemResult = new DiscountPriceItemResult();
			discountPriceItemResult.setDiscountPrice(skuSalePrice);
			Map<String, ActivityCacheDTO> filterMap = new HashMap<>();
			filterMap.putAll(ruleCacheMap);
			ProductCodes productDTO = new ProductCodes();
			productDTO.setCategoryCodes(shoppingCartItem.getCategoryCodes());
			productDTO.setBrandCode(shoppingCartItem.getBrandCode());
			String productCode = shoppingCartItem.getProductCode();
			productDTO.setProductCode(productCode);
			productDTO.setSkuCode(skuCode);
//            productDTO.setCombineSkuCode(shoppingCartItem.getCombineSkuCode())
			productDTO.setAttributes(shoppingCartItem.getAttributes());
			productDTO.setProductTag(shoppingCartItem.getProductTag());

			filterMap = activityCacheDomain.filterBlackProduct(filterMap, productDTO, blackProductMap);
			filterMap = activityCacheDomain.filterActivityByProduct(filterMap, productDTO, productSkuDetailDTOS);
			Iterator<Map.Entry<String, ActivityCacheDTO>> entries = filterMap.entrySet().iterator();
			while (entries.hasNext()) {
				Map.Entry<String, ActivityCacheDTO> entry = entries.next();
				String key = entry.getKey();
				ActivityCacheDTO activityCacheDTO = entry.getValue();
				TemplateModel promoTemplate = activityCacheDTO.getPromoTemplate();
				List<FunctionParamModel> promoFuncParams = activityCacheDTO.getPromoFuncParams();
				FunctionParamModel functionParamModel = null;
				if (!CollectionUtils.isEmpty(promoFuncParams) && promoFuncParams.size() == 4) {
					functionParamModel = promoFuncParams.get(3);
				} else {
					continue;
				}

				BigDecimal afterAmount = null;
				BigDecimal zero = BigDecimal.ZERO;
				if (null != promoTemplate && "0101020103010402".equals(promoTemplate.getTemplateCode())
						&& null != functionParamModel) {
					log.info("进入折扣逻辑！skuCode:{},activityCode：{}", shoppingCartItem.getSkuCode(), key);
					String paramValue = functionParamModel.getParamValue();
					BigDecimal incentiveAmount = skuSalePrice
							.multiply(new BigDecimal(1).subtract(new BigDecimal(paramValue)))
							.setScale(precision, CalcConstants.ROUND_DOWN);
					afterAmount = skuSalePrice.subtract(incentiveAmount).setScale(precision, CalcConstants.ROUND_DOWN);
					if (powerPrecision != null) {
						afterAmount = afterAmount.divide(powerPrecision).setScale(0, CalcConstants.ROUND_DOWN).multiply(powerPrecision);
					}
				} else if (null != promoTemplate && "0101020103010401".equals(promoTemplate.getTemplateCode())
						&& null != functionParamModel) {
					log.info("进入减金额逻辑！skuCode:{},activityCode：{}", shoppingCartItem.getSkuCode(), key);
					String paramValue = functionParamModel.getParamValue();
					afterAmount = skuSalePrice.subtract(new BigDecimal(paramValue));
					if (afterAmount.compareTo(zero) <= 0) {
						afterAmount = zero;
					} else {
						afterAmount = afterAmount.setScale(precision, CalcConstants.ROUND_DOWN);
					}
					if (powerPrecision != null) {
						afterAmount = afterAmount.divide(powerPrecision).setScale(0, CalcConstants.ROUND_DOWN).multiply(powerPrecision);
					}
				}

				if (null != skuPriceMap.get(skuCode) && null != afterAmount) {
					BigDecimal skuPrice = skuPriceMap.get(skuCode);
					if (afterAmount.compareTo(skuPrice) < 0) {
						skuPriceMap.put(skuCode, afterAmount);
						skuActivityMap.put(skuCode, activityCacheDTO);
					}
				} else if (null == skuPriceMap.get(skuCode) && null != afterAmount) {
					skuPriceMap.put(skuCode, afterAmount);
					skuActivityMap.put(skuCode, activityCacheDTO);
				}
			}
		}

		List<DiscountPriceItemResult> items = new ArrayList<>();
		for (Map.Entry<String, BigDecimal> entry : skuPriceMap.entrySet()) {
			String skuCode = entry.getKey();
			ActivityCacheDTO activityCacheDTO = skuActivityMap.get(skuCode);
			DiscountPriceItemResult discountPriceItemResult = new DiscountPriceItemResult();
			discountPriceItemResult.setSkuCode(skuCode);
			discountPriceItemResult.setDiscountPrice(entry.getValue());
			if (null != activityCacheDTO && null != activityCacheDTO.getActivityModel()) {
				discountPriceItemResult.setActivityCode(activityCacheDTO.getActivityModel().getActivityCode());
			}
			items.add(discountPriceItemResult);
		}
		discountPriceResult.setDiscountPriceItemList(items);
		return Result.ok(discountPriceResult);
	}

	public int existPromotionCategory(QueryPromotionCategoryParam param){
		return activityService.queryPromotionCategoryCount(param.getTenantCode(),param.getPromotionCategory());
	}

	public int updatePromotionCategoryNull(QueryPromotionCategoryParam param){
		return activityService.updatePromotionCategoryNull(param.getTenantCode(),param.getPromotionCategory());
	}
}
