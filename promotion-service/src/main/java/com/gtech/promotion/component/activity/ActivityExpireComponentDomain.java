/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * <PERSON>ECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.component.activity;

import com.gtech.promotion.code.activity.ActivityStatusEnum;
import com.gtech.promotion.code.activity.OperationTypeEnum;
import com.gtech.promotion.dao.model.activity.ActivityModel;
import com.gtech.promotion.dao.model.activity.OperationLogModel;
import com.gtech.promotion.helper.ActivityRedisHelpler;
import com.gtech.promotion.service.activity.ActivityService;
import com.gtech.promotion.service.activity.OperationLogService;
import com.gtech.promotion.service.coupon.PromoCouponCodeUserService;
import com.gtech.promotion.service.coupon.PromoCouponInnerCodeService;
import com.gtech.promotion.service.coupon.PromoCouponReleaseService;
import com.gtech.promotion.utils.Constants;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * ActivityExpireComponent
 *
 * <AUTHOR>
 * @Date 2020-03-11
 */
@Slf4j
@Component
public class ActivityExpireComponentDomain {

    private static final String SYSTEM_AUTO_COMPLETION = "SYSTEM AUTO COMPLETION";
    private static final String SYSTEM = "SYSTEM";
    @Autowired
    private ActivityService activityService;

    @Autowired
    private StringRedisTemplate redisTemplate;

    @Autowired
    private ActivityRedisHelpler activityRedisHelper;

    @Autowired
    private PromoCouponReleaseService couponReleaseService;

    @Autowired
    private PromoCouponInnerCodeService couponInnerCodeService;

    @Autowired
    private PromoCouponCodeUserService couponCodeUserService;

    @Autowired
    private OperationLogService operationLogService;

    /**
     * Deal with expired activity list.
     */
    @Transactional(propagation=Propagation.REQUIRES_NEW)
    public <T extends ActivityModel> int expireActivity(List<T> activityModels) {

        int expire = 0;

        if (CollectionUtils.isNotEmpty(activityModels)) {
            for(ActivityModel e : activityModels) {
                expire += this.expireActivity(e);
            }
        }

        return expire;
    }

    /**
     * Deal with expired activity.
     */
    @Transactional(propagation=Propagation.REQUIRES_NEW)
    public <T extends ActivityModel> int expireActivity(T activityModel) {

        return this.doExpireActivity(activityModel);
    }

    @Transactional(propagation=Propagation.REQUIRES_NEW)
    public void expireActivity(String tenantCode, String activityCode) {
        try {
            this.doExpireActivity(tenantCode, activityCode);
        } catch (Exception e){
            log.error("expireActivity error", e);
        }
    }
    
    private <T extends ActivityModel> int doExpireActivity(T activityModel) {
        
        if (activityModel.isNeedToDoExpire()) {
            if (1 != this.doExpireActivity(activityModel.getTenantCode(), activityModel.getActivityCode())) {
                return 0;
            }
            activityModel.setActivityStatus(ActivityStatusEnum.CLOSURE.code());
            return 1;
        }
        return 0;
    }

    private int doExpireActivity(String tenantCode, String activityCode) {
        ActivityModel activityByActivityCode = activityService.findActivityByActivityCode(tenantCode, activityCode);
        if (null != activityByActivityCode && activityByActivityCode.isNeedToDoExpire()) {
            this.activityService.updateActivityStatus(tenantCode, activityCode, ActivityStatusEnum.CLOSURE.code(),SYSTEM);

            operationLogService.insertLog(OperationLogModel.builder()
                    .tenantCode(tenantCode).activityCode(activityCode).createUser(SYSTEM)
                    .createLastName(SYSTEM)
                    .operationType(OperationTypeEnum.COMPLETION.code()).build(), SYSTEM_AUTO_COMPLETION);
            this.activityRedisHelper.removeActivityCacheLevelTwo(tenantCode);

            if (activityCode.startsWith("2")) {
                this.couponInnerCodeService.expireByActivityCode(tenantCode, activityCode, null);
                this.couponCodeUserService.expireByActivityCode(tenantCode, activityCode, null);

                this.couponReleaseService.stopCouponRelease(activityCode);
                this.redisTemplate.delete(Constants.PROMOTION_COUPON_CODE_CACHE + ":" + tenantCode + ":" + activityCode);
            }
            return 1;
        }
        return 0;
    }

}
