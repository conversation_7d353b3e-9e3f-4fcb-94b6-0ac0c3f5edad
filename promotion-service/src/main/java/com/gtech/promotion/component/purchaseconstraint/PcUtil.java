package com.gtech.promotion.component.purchaseconstraint;

import cn.hutool.core.date.DateUtil;
import com.gtech.promotion.code.purchaseconstraint.PurchaseConstraintRuleTimeTypeEnum;
import lombok.experimental.UtilityClass;

import java.util.Date;

@UtilityClass
public class PcUtil {

    /**
     * 指定截止日期, 设置过期时间
     *
     * @param date
     * @return
     */
    public Long getExpired(Date date) {
        if (date == null) {
            return null;
        }
        return date.getTime() - System.currentTimeMillis();
    }
    public Long getExpired(PurchaseConstraintRuleTimeTypeEnum timeType, String timeValue) {
        Date date = PcUtil.getStatisticsEnd(timeType, timeValue);
        if (date == null) {
            return null;
        }
        return date.getTime() - System.currentTimeMillis();
    }

    private static final String YYYY_MM_DD = "yyyyMMdd";

    public Date getStatisticsBegin(Integer timeType, String timeValue) {
        return getStatisticsBegin(PurchaseConstraintRuleTimeTypeEnum.getByCode(timeType), timeValue);
    }

    /**
     * 获取当前统计周期开始时间
     */
    public Date getStatisticsBegin(PurchaseConstraintRuleTimeTypeEnum timeType, String timeValue) {
        if (timeType == null) {
            return null;
        }
        Date now = new Date();
        Date result = null;
        String year = String.valueOf(DateUtil.year(now));
        String month = String.format("%02d", DateUtil.month(now) + 1);

        switch (timeType) {
            case YEARLY:
                result = DateUtil.parse(year + timeValue, YYYY_MM_DD);
                if (result.compareTo(now) > 0) {
                    result = DateUtil.offsetMonth(result, -12);
                }
                break;
            case MONTHLY:
                result = DateUtil.parse(year + month + timeValue, YYYY_MM_DD);
                if (result.compareTo(now) > 0) {
                    result = DateUtil.offsetMonth(result, -1);
                }
                break;
            // 周日,周一,周二,周三,周四,周五,周六(0,1,2,3,4,5,6)
            case WEEKLY:
                // 指定周几  当前周几
                int dayOfWeekNow = DateUtil.dayOfWeek(now) - 1;
                // 指定周几 > 当前周几; 取上周
                if (Integer.parseInt(timeValue) - dayOfWeekNow > 0) {
                    // 日期向左偏移
                    int offset = Integer.parseInt(timeValue) - dayOfWeekNow - 7;
                    result = DateUtil.offsetDay(now, offset);
                }
                // 指定周几 < 当前周几; 取本周
                else if (Integer.parseInt(timeValue) - dayOfWeekNow < 0) {
                    // 日期向左偏移
                    int offset = dayOfWeekNow - Integer.parseInt(timeValue);
                    result = DateUtil.offsetDay(now, -offset);
                } else {
                    result = now;
                }
                break;
        }
        return DateUtil.beginOfDay(result);
    }

    public Date getStatisticsEnd(Integer timeType, String timeValue) {
        return getStatisticsEnd(PurchaseConstraintRuleTimeTypeEnum.getByCode(timeType), timeValue);
    }

    /**
     * 获取当前统计周期结束时间
     */
    public Date getStatisticsEnd(PurchaseConstraintRuleTimeTypeEnum timeType, String timeValue) {
        if (timeType == null) {
            return null;
        }
        Date now = new Date();
        Date result = null;
        String year = String.valueOf(DateUtil.year(now));
        String month = String.format("%02d", DateUtil.month(now) + 1);
        switch (timeType) {
            case YEARLY:
                result = DateUtil.parse(year + timeValue, YYYY_MM_DD);
                if (result.compareTo(now) > 0) {
                    result = DateUtil.offsetMonth(result, -12);
                }
                // 右移12月
                result = DateUtil.endOfDay(DateUtil.offsetMonth(result, 12));
                break;
            case MONTHLY:
                result = DateUtil.parse(year + month + timeValue, YYYY_MM_DD);
                if (result.compareTo(now) > 0) {
                    result = DateUtil.offsetMonth(result, -1);
                }
                // 右移1月
                result = DateUtil.endOfDay(DateUtil.offsetMonth(result, 1));
                break;
            // 周日,周一,周二,周三,周四,周五,周六(0,1,2,3,4,5,6)
            case WEEKLY:
                // 指定周几  当前周几
                int dayOfWeekNow = DateUtil.dayOfWeek(now) - 1;
                // 指定周几 > 当前周几; 取上周
                if (Integer.parseInt(timeValue) - dayOfWeekNow > 0) {
                    // 日期向左偏移
                    int offset = Integer.parseInt(timeValue) - dayOfWeekNow - 7;
                    result = DateUtil.offsetDay(now, offset);
                }
                // 指定周几 < 当前周几; 取本周
                else if (Integer.parseInt(timeValue) - dayOfWeekNow < 0) {
                    // 日期向右偏移
                    int offset = dayOfWeekNow - Integer.parseInt(timeValue);
                    result = DateUtil.offsetDay(now, -offset);
                } else {
                    result = now;
                }
                // 右移1周
                result = DateUtil.endOfDay(DateUtil.offsetWeek(result, 1));
                break;
        }
        // 结束日期,向左偏移1天
        return DateUtil.offsetDay(result, -1);
    }

}
