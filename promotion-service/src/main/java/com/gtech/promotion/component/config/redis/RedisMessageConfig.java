/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.component.config.redis;

import org.springframework.boot.autoconfigure.AutoConfigureAfter;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.connection.MessageListener;
import org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.listener.ChannelTopic;
import org.springframework.data.redis.listener.RedisMessageListenerContainer;

import com.gtech.promotion.RedisConfig;

/**
 * <功能描述>
 */
@Configuration
@AutoConfigureAfter(RedisConfig.class)
public class RedisMessageConfig {

    @Bean
    public RedisMessageListenerContainer redisMessageListenerContainer(StringRedisTemplate stringRedisTemplate, MessageListener notifyMessageListener){
        RedisMessageListenerContainer container = new RedisMessageListenerContainer();
        LettuceConnectionFactory connectionFactory = (LettuceConnectionFactory)stringRedisTemplate.getConnectionFactory();
        int database = connectionFactory.getDatabase();
        // 设置Redis的连接工厂
        container.setConnectionFactory(stringRedisTemplate.getConnectionFactory());
        // 设置监听的Topic
        ChannelTopic channelTopic = new ChannelTopic("__keyevent@"+database+"__:expired");
        // 设置监听器
        container.addMessageListener(notifyMessageListener, channelTopic);
        return container;
    }

}
