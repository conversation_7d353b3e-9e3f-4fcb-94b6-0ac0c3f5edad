package com.gtech.promotion.component.marketing;

import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.page.PageMethod;
import com.gtech.basic.idm.web.vo.param.QueryUserParam;
import com.gtech.basic.idm.web.vo.result.QueryUserResult;
import com.gtech.commons.exception.ErrorCode;
import com.gtech.commons.exception.Exceptions;
import com.gtech.commons.page.PageData;
import com.gtech.commons.redis.GTechCodeGenerator;
import com.gtech.commons.redis.GTechRedisTemplate;
import com.gtech.commons.result.Result;
import com.gtech.commons.utils.BeanCopyUtils;
import com.gtech.commons.utils.ConvertUtils;
import com.gtech.commons.utils.DateUtil;
import com.gtech.commons.utils.StringUtil;
import com.gtech.member.web.vo.param.QueryMemberParam;
import com.gtech.member.web.vo.result.QueryMemberListByConditionResult;
import com.gtech.promotion.checker.marketing.MarketingChecker;
import com.gtech.promotion.code.activity.LimitationCodeEnum;
import com.gtech.promotion.code.marketing.LuckeyDrawPrizeProductStatusEnum;
import com.gtech.promotion.code.marketing.PrizeTypeEnum;
import com.gtech.promotion.code.marketing.RecordStatusEnum;
import com.gtech.promotion.code.marketing.TicketStatusEnum;
import com.gtech.promotion.component.boostsharing.BoostSharingComponent;
import com.gtech.promotion.dao.model.activity.ActivityPeriodModel;
import com.gtech.promotion.dao.model.activity.LuckyDrawRuleModel;
import com.gtech.promotion.dao.model.activity.QualificationModel;
import com.gtech.promotion.dao.model.activity.TPromoIncentiveLimitedVO;
import com.gtech.promotion.dao.model.marketing.*;
import com.gtech.promotion.domain.marketing.*;
import com.gtech.promotion.dto.in.flashsale.IssuanceOfPreEmptiveRightsDto;
import com.gtech.promotion.dto.in.marketing.ExportLuckyDrawDto;
import com.gtech.promotion.exception.Check;
import com.gtech.promotion.exception.ErrorCodes;
import com.gtech.promotion.feign.IdmFeignClient;
import com.gtech.promotion.feign.MemberFeignClient;
import com.gtech.promotion.helper.RedisOpsHelper;
import com.gtech.promotion.helper.marketing.AbstractDraw;
import com.gtech.promotion.helper.marketing.Prize;
import com.gtech.promotion.service.activity.ActivityPeriodService;
import com.gtech.promotion.service.activity.LuckyDrawRuleService;
import com.gtech.promotion.service.activity.QualificationService;
import com.gtech.promotion.service.activity.TPromoIncentiveLimitedService;
import com.gtech.promotion.service.marketing.*;
import com.gtech.promotion.utils.*;
import com.gtech.promotion.vo.bean.ActivityPeriod;
import com.gtech.promotion.vo.bean.IncentiveLimited;
import com.gtech.promotion.vo.result.coupon.ActivityParticipateInResult;
import com.gtech.promotion.vo.result.marketing.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Component
public class LuckyDrawComponent {

    @Autowired
    private MarketingService marketingService;

    @Autowired
    private TicketComponent ticketComponent;

    @Autowired
    private MarketingLanguageService languageService;

    @Autowired
    private QualificationService qualificationService;

    @Autowired
    private LuckyDrawRuleService luckyDrawRuleService;

    @Autowired
    private PrizeService prizeService;

    @Autowired
    private PrizeLanguageService prizeLanguageService;

    @Autowired
    private TPromoIncentiveLimitedService incentiveLimitedService;

    @Autowired
    private ActivityPeriodService activityPeriodService;

    @Autowired
    private TicketReleaseService ticketReleaseService;

    @Autowired
    private RedisOpsHelper incentiveLimitedHelper;

    @Autowired
    private TicketService ticketService;

    @Autowired
    private PrizeRecordService recordService;

    @Autowired
    private BoostSharingComponent boostSharingComponent;


    @Autowired
    private MemberFeignClient memberFeignClient;


    @Autowired
    private GTechRedisTemplate gTechRedisTemplate;

    @Autowired
    private MarketingLuckDrawPrizeProductService marketingLuckDrawPrizeProduct;

    @Autowired
    private IdmFeignClient idmFeignClient;

    @Autowired
    private GTechCodeGenerator gTechCodeGenerator;

    public static final String APP_KEY =  Constants.APP_KEY;

    public LuckyDrawDetailResult getLuckyDrawDetail(LuckyDrawDetailDomain luckyDrawDetailDomain) {
        BaseModel baseModel = BeanCopyUtils.jsonCopyBean(luckyDrawDetailDomain, BaseModel.class);
        MarketingModel marketingModel = marketingService.findByActivityCode(baseModel.getActivityCode());
        Check.check(null == marketingModel, MarketingChecker.ACTIVITY_NOT_EXIST);

        LuckyDrawDetailResult result = BeanCopyUtils.jsonCopyBean(marketingModel, LuckyDrawDetailResult.class);
        MarketingLanguageModel languageModel = languageService.findByLanguage(baseModel.getActivityCode(), luckyDrawDetailDomain.getLanguage());
        if (null != languageModel){
            result.setActivityLabel(languageModel.getActivityLabel());
            result.setActivityName(languageModel.getActivityName());
            result.setActivityDesc(languageModel.getActivityDesc());
            result.setActivityShortDesc(languageModel.getActivityShortDesc());
        }
        List<TPromoIncentiveLimitedVO> limitedVOS = incentiveLimitedService.getLuckyDrawLimitedListByActivityCode(luckyDrawDetailDomain.getActivityCode());
        if (!CollectionUtils.isEmpty(limitedVOS)){
            List<IncentiveLimited> incentiveLimiteds = BeanCopyUtils.jsonCopyList(limitedVOS, IncentiveLimited.class);
            result.setIncentiveLimiteds(incentiveLimiteds);
        }
        ActivityPeriodModel period = activityPeriodService.findPeriod(baseModel.getTenantCode(), baseModel.getActivityCode());
        result.setActivityPeriod(BeanCopyUtils.jsonCopyBean(period, ActivityPeriod.class));

        List<PrizeModel> prizeModels = prizeService.findListByActivityCode(baseModel.getActivityCode());
        List<LuckyDrawPrizeResult> prizes = BeanCopyUtils.jsonCopyList(prizeModels, LuckyDrawPrizeResult.class);
        List<PrizeLanguageModel> prizeLanguageModels = prizeLanguageService.findListByActivityCode(baseModel.getActivityCode());
        Map<String, List<PrizeLanguageModel>> collect = prizeLanguageModels.stream().collect(Collectors.groupingBy(PrizeLanguageModel::getPrizeNo));
        for (LuckyDrawPrizeResult prize : prizes) {
            if (collect.containsKey(prize.getPrizeNo())){
                Optional<PrizeLanguageModel> first = collect.get(prize.getPrizeNo()).stream().filter(x -> x.getLanguage().equals(luckyDrawDetailDomain.getLanguage())).findFirst();
                first.ifPresent(prizeLanguageModel -> prize.setPrizeName(prizeLanguageModel.getPrizeName()));
            }
        }
        result.setPrizes(prizes);
        return result;
    }

    @Transactional
    public LuckyDrawResult getLuckyDrawResult(LuckyDrawDoDomain luckyDrawDoDomain) {
        BaseModel baseModel = BeanCopyUtils.jsonCopyBean(luckyDrawDoDomain, BaseModel.class);
        MarketingModel marketingModel = marketingService.findByActivityCode(baseModel.getActivityCode());
        Check.check(null == marketingModel, MarketingChecker.ACTIVITY_NOT_EXIST);
        //marketingModel开始结束时间抛出活动未开始活动已结束异常
        Check.check(DateUtil.now().before(DateUtil.parseDate(marketingModel.getActivityBegin(),DateUtil.FORMAT_YYYYMMDDHHMISS_14)), MarketingChecker.ACTIVITY_NOT_START);
        Check.check(DateUtil.now().after(DateUtil.parseDate(marketingModel.getActivityEnd(),DateUtil.FORMAT_YYYYMMDDHHMISS_14)), MarketingChecker.ACTIVITY_END);

        //Check.check(!ActivityStatusEnum.EFFECTIVE.equalsCode(marketingModel.getActivityStatus()), MarketingChecker.ACTIVITY_NOT_EFFECTIVE);
        ActivityPeriodModel period = activityPeriodService.findPeriod(baseModel.getTenantCode(), baseModel.getActivityCode());
        Check.check(null != period && !CronUtil.checkDate(period.getBeginPeriod(), period.getEndPeriod(), period.getIntervalWeek(), marketingModel.getActivityBegin(), marketingModel.getActivityEnd(), DateUtil.getCurrentDateAsString(DateUtil.FORMAT_YYYYMMDDHHMISS_14)), MarketingChecker.ACTIVITY_NOT_EFFECTIVE);
        List<TicketModel> ticketModels = ticketService.selectListByMemberCode(baseModel, luckyDrawDoDomain.getMemberCode(), TicketStatusEnum.NO_USE.code());
        Integer quality = luckyDrawDoDomain.getQuality();

        Check.check(CollectionUtils.isEmpty(ticketModels) || ticketModels.size() < quality, MarketingChecker.TICKET_NOT_ENOUGH);
        LuckyDrawResult result = new LuckyDrawResult();
        List<LuckyDrawResult.Prize> prizes = new ArrayList<>();
        //校验活动资格限制（每天限制次数  每周限制次数  每月限制次数  活动总限制次数）
        Check.check(!this.limitation(luckyDrawDoDomain.getMemberCode(),luckyDrawDoDomain.getTenantCode(),luckyDrawDoDomain.getActivityCode(),luckyDrawDoDomain.getQuality(),marketingModel.getActivityEnd()),MarketingChecker.PARTICIPATION_LIMIT_REACHED);
        for (TicketModel ticketModelTemp : ticketModels) {
            if (quality <= 0){
                break;
            }
            ticketReleaseService.addUsed(baseModel, ticketModelTemp.getReleaseCode());
            List<PrizeModel> prizeModels = getLanguagePrizeList(baseModel, luckyDrawDoDomain.getLanguage());

            TicketModel ticketModel = new TicketModel();
            // 抽奖算法
            Prize lottery = AbstractDraw.getDraw().lottery(BeanCopyUtils.jsonCopyList(prizeModels, Prize.class));
            LuckyDrawResult.Prize prize;
            String currentTime = DateUtil.getCurrentDateAsString(DateUtil.FORMAT_YYYYMMDDHHMISS_14);
            if (null == lottery){ // 未中奖
                prize = new LuckyDrawResult.Prize();
                prize.setResultCode(LuckyDrawResult.NO_LUCKY);
                ticketModel.setStatus(TicketStatusEnum.USED.code());
            }else if (PrizeTypeEnum.DEFAULT.code().equals(lottery.getPrizeType())){// 中谢谢惠顾奖
                PrizeModel prizeModel = prizeModels.stream().filter(x -> x.getPrizeNo().equals(lottery.getPrizeNo())).findFirst().orElse(null);
                prize = BeanCopyUtils.jsonCopyBean(prizeModel, LuckyDrawResult.Prize.class);
                prize.setResultCode(LuckyDrawResult.NO_LUCKY);
                ticketModel.setStatus(TicketStatusEnum.USED.code());
            }else if (lottery.isNoInventory()){ // 奖品没有库存
                prize = new LuckyDrawResult.Prize();
                prize.setResultCode(LuckyDrawResult.NO_INVENTORY);
                ticketModel.setStatus(TicketStatusEnum.USED.code());
            }else {
                PrizeModel prizeModel = prizeModels.stream().filter(x -> x.getPrizeNo().equals(lottery.getPrizeNo())).findFirst().orElse(null);
                //限制最大中奖次数
                if (null != prizeModel &&  null != prizeModel.getNumberOfTimesWon()){
                    String key = "MAXIMUM_NUMBER_OF_PRIZES_WON_IN_THE_LOTTERY"+":"+luckyDrawDoDomain.getActivityCode()+":"+prizeModel.getPrizeCode()+":"+luckyDrawDoDomain.getMemberCode();
                    Long aLong = gTechRedisTemplate.opsValueIncrement(APP_KEY, key, 1L);
                    if (aLong > prizeModel.getNumberOfTimesWon()){
                        prize = new LuckyDrawResult.Prize();
                        prize.setResultCode(LuckyDrawResult.NO_LUCKY);
                        ticketModel.setStatus(TicketStatusEnum.USED.code());
                        prizes.add(prize);
                        ticketModel.setUseTime(currentTime);
                        ticketService.updateByTicketCode(ticketModel, ticketModelTemp.getTicketCode());
                        quality --;
                        continue;
                    }
                }
                int i = prizeService.deductInventory(prizeModel);
                if(i == 0) {
                    prize = new LuckyDrawResult.Prize();
                    prize.setResultCode(LuckyDrawResult.NO_INVENTORY);
                    ticketModel.setStatus(TicketStatusEnum.USED.code());
                }else{
                    PrizeRecordModel prizeRecordModel = BeanCopyUtils.jsonCopyBean(prizeModel, PrizeRecordModel.class);
                    prizeRecordModel.setMemberCode(luckyDrawDoDomain.getMemberCode());
                    prizeRecordModel.setStatus(RecordStatusEnum.NO_RECEIVE.code());
                    prizeRecordModel.setTicketCode(ticketModelTemp.getTicketCode());
                    recordService.insert(prizeRecordModel);

                    ticketModel.setStatus(TicketStatusEnum.LUCKY.code());
                    ticketModel.setLuckyTime(currentTime);

                    if (PrizeTypeEnum.RIGHT_OF_FIRST_REFUSAL.code().equals(Objects.requireNonNull(prizeModel).getPrizeType())){
                        distributionOfRightOfFirstRefusal(prizeModel,luckyDrawDoDomain,ticketModelTemp.getTicketCode());
                    }else if (PrizeTypeEnum.PRODUCT.code().equals(Objects.requireNonNull(prizeModel).getPrizeType())){
                        distributionOfProduct(prizeModel,luckyDrawDoDomain);
                    }
                    prize = BeanCopyUtils.jsonCopyBean(prizeModel, LuckyDrawResult.Prize.class);
                    prize.setResultCode(LuckyDrawResult.LUCKY);
                }
            }
            prizes.add(prize);

            ticketModel.setUseTime(currentTime);
            ticketService.updateByTicketCode(ticketModel, ticketModelTemp.getTicketCode());
            quality --;
        }

        result.setPrizes(prizes);
        return result;
    }


    public void distributionOfRightOfFirstRefusal(PrizeModel prizeModel, LuckyDrawDoDomain luckyDrawDoDomain,String ticketCode){
        IssuanceOfPreEmptiveRightsDto dto = new IssuanceOfPreEmptiveRightsDto();
        dto.setDomainCode(luckyDrawDoDomain.getDomainCode());
        dto.setOrgCode(luckyDrawDoDomain.getOrgCode());
        dto.setTenantCode(luckyDrawDoDomain.getTenantCode());
        dto.setMemberCode(luckyDrawDoDomain.getMemberCode());
        dto.setRightOfFirstRefusalCode(prizeModel.getPrizeNo());
        dto.setSharingRecordCode(ticketCode);
        boostSharingComponent.issuanceOfPreEmptiveRights(dto);
    }


    //给用户发送商品核销资格,待下单时检查并且核销
    public void distributionOfProduct(PrizeModel prizeModel, LuckyDrawDoDomain luckyDrawDoDomain){
        MarketingLuckDrawPrizeProductModel model = new MarketingLuckDrawPrizeProductModel();
        model.setDomainCode(luckyDrawDoDomain.getDomainCode());
        model.setLuckyDrawPrizeProductCode(gTechCodeGenerator.generateCode(MarketingConstants.APP_KEY, "LDP", "03"+ FlashSaleConstants.FLASH_CODE_TEMPLATE_EXP, 106L));
        model.setOrgCode(luckyDrawDoDomain.getOrgCode());
        model.setTenantCode(luckyDrawDoDomain.getTenantCode());
        model.setMemberCode(luckyDrawDoDomain.getMemberCode());
        model.setProductCode(prizeModel.getPrizeCode());
        model.setActivityCode(prizeModel.getActivityCode());
        model.setStatus(LuckeyDrawPrizeProductStatusEnum.UN_USED.code());
        marketingLuckDrawPrizeProduct.insert(model);
    }




    public PageData<LuckyDrawMemberChanceResult> queryChanceList(LuckyDrawMemberChanceDomain domain) {
        domain.setCurrDate(DateUtil.getCurrentDateAsString(DateUtil.FORMAT_YYYYMMDDHHMISS_14));
        return ticketService.queryChanceList(domain);
    }

    public PageData<LuckyDrawMemberRecordResult> queryLuckyRecordList(LuckyDrawMemberRecordDomain domain) {
        TicketModel ticketModel = BeanCopyUtils.jsonCopyBean(domain, TicketModel.class);
        PageData<TicketModel> ticketModelPageData = ticketService.queryListByStatus(ticketModel, domain);
        List<LuckyDrawMemberRecordResult> recordResults = BeanCopyUtils.jsonCopyList(ticketModelPageData.getList(), LuckyDrawMemberRecordResult.class);

        if (!CollectionUtils.isEmpty(recordResults)){
            List<PrizeModel> languagePrizeList = getLanguagePrizeList(ticketModel, domain.getLanguage());
            QueryRecordByTicketsModel queryRecordByTicketsModel = BeanCopyUtils.jsonCopyBean(ticketModel, QueryRecordByTicketsModel.class);
            List<PrizeRecordModel> prizeRecordModels = recordService.queryLuckyRecordList(queryRecordByTicketsModel);
            for (LuckyDrawMemberRecordResult recordResult : recordResults) {
                if (recordResult.getStatus().equals(TicketStatusEnum.LUCKY.code())){
                    recordResult.setPrizes(getPrizes(languagePrizeList, prizeRecordModels, recordResult.getTicketCode()));
                }else {
                    recordResult.setPrizes(Collections.emptyList());
                }
            }
        }
        return new PageData<>(recordResults, ticketModelPageData.getTotal());
    }

    private List<LuckyDrawMemberRecordResult.Prize> getPrizes(List<PrizeModel> languagePrizeList, List<PrizeRecordModel> prizeRecordModels, String ticketCode) {
        List<LuckyDrawMemberRecordResult.Prize> prizes = new ArrayList<>();
        for (PrizeRecordModel prizeRecordModel : prizeRecordModels) {
            if (prizeRecordModel.getTicketCode().equals(ticketCode)){
                for (PrizeModel prizeModel : languagePrizeList) {
                    if (prizeModel.getPrizeNo().equals(prizeRecordModel.getPrizeNo())){
                        prizes.add(BeanCopyUtils.jsonCopyBean(prizeModel, LuckyDrawMemberRecordResult.Prize.class));
                    }
                }
            }
        }
        return prizes;
    }

    private List<PrizeModel> getLanguagePrizeList(BaseModel baseModel, String language){
        List<PrizeModel> prizeModels = prizeService.findListByActivityCode(baseModel.getActivityCode());
        List<PrizeLanguageModel> prizeLanguageModels = prizeLanguageService.findListByActivityCode(baseModel.getActivityCode());
        Map<String, List<PrizeLanguageModel>> collect = prizeLanguageModels.stream().collect(Collectors.groupingBy(PrizeLanguageModel::getPrizeNo));
        for (PrizeModel prize : prizeModels) {
            if (collect.containsKey(prize.getPrizeNo())){
                Optional<PrizeLanguageModel> first = collect.get(prize.getPrizeNo()).stream().filter(x -> x.getLanguage().equals(language)).findFirst();
                first.ifPresent(prizeLanguageModel -> prize.setPrizeName(prizeLanguageModel.getPrizeName()));
            }
        }
        return prizeModels;
    }



	public List<ActivityParticipateInResult> judgeQualification(JudgeQualificationDomain domain) {
		// 查询所有符合spu的所有活动规则
		List<LuckyDrawRuleModel> luckyDrawRuleModels = luckyDrawRuleService
				.queryLuckyDrawRulesByProductCode(domain.getTenantCode(), domain.getProductCodes());
		// 没有限制不发券
		if (CollectionUtils.isEmpty(luckyDrawRuleModels)) {
			log.error("校验资格 无规则:{{}}", JSONObject.toJSONString(domain));
			return Collections.emptyList();
		}
		// 所有活动code
		ArrayList<String> activityCodes = new ArrayList<>();
		for (LuckyDrawRuleModel mo : luckyDrawRuleModels) {
			activityCodes.add(mo.getActivityCode());
		}
		// 所有正在进行的活动marketing
		List<MarketingModel> marketing = marketingService.getMarketingByActivityCode(domain.getTenantCode(),
				activityCodes, "04");
		if (CollectionUtils.isEmpty(marketing)) {
			return Collections.emptyList();
		}

		List<ActivityParticipateInResult> result = new ArrayList<>();
		// 根据用户条件过滤活动
		for (MarketingModel market : marketing) {
			// 规则
			// 根据活动查询出所有用户条件
			List<QualificationModel> qualificationModels = qualificationService
					.queryLuckyDrawQualificationsByActivityCodes(domain.getTenantCode(), market.getActivityCode());
			// 过滤规则
			// 用户条件过滤不通过 下一个
			if (!ActivityFilterUtil.filterActivityByQualifications(qualificationModels, domain.getQualifications())) {
				continue;
			}
			for (LuckyDrawRuleModel luckyDrawRuleModel : luckyDrawRuleModels) {
				if (market.getActivityCode().equals(luckyDrawRuleModel.getActivityCode())) {
					buildjudgeQualificationResult(domain, market, luckyDrawRuleModel, result);
				}
			}
		}
		return result;
	}

	private void buildjudgeQualificationResult(JudgeQualificationDomain domain, MarketingModel market,
			LuckyDrawRuleModel luckyDrawRuleModel, List<ActivityParticipateInResult> result) {
		// 所有数量限制的集合key: redis自增的key value: 值(出现异常时用于数据回滚).
		List<RedisOpsHelper.IncentiveLimitedParam> paramList = new ArrayList<>();
		// Redis超时时间
		long timeout = DateUtil.parseDate(market.getActivityEnd(), DateUtil.FORMAT_YYYYMMDDHHMISS_14).getTime()
				- System.currentTimeMillis();
		if (luckyDrawRuleModel.getBuyingQty().compareTo(ConvertUtils.toBigDecimal(domain.getAmount())) > 0) {
			return;
		}

		BigDecimal min = new BigDecimal(luckyDrawRuleModel.getMaxGivingPerUser());
		// 参数数量/限制数量*票数 = 要送多少票 和最大票数限制取最小
		// 参数金额/限制金额*票数 = 要送多少票 和最大票数限制取最小
		min = min.min(domain.getAmount().divide(luckyDrawRuleModel.getBuyingQty(), 0, BigDecimal.ROUND_DOWN)
				.multiply(ConvertUtils.toBigDecimal(luckyDrawRuleModel.getEarnTicket())));

		// 过滤扣减活动规则限制
		RedisOpsHelper.IncentiveLimitedParam param = RedisOpsHelper.IncentiveLimitedParam.builder()
				.limitedCode(LimitationCodeEnum.USER_SKU_COUNT.code()).tenantCode(domain.getTenantCode())
				.activityCode(market.getActivityCode()).userCode(domain.getMemberCode())
				.totalValue(ConvertUtils.toBigDecimal(luckyDrawRuleModel.getMaxGivingPerUser()))
				.incentiveValue(min.setScale(0, BigDecimal.ROUND_DOWN)).build();
		// l 返回的是扣减的票数
		long l = incentiveLimitedHelper.lockRedisDataSpu(paramList, param, timeout,
				luckyDrawRuleModel.getProductCode());
		log.info("用户：" + domain.getMemberCode(), "--活动 ：" + market.getActivityCode());
		if (l > 0) {
			int i = 0;
			try {
				// 发券
				TicketSendDomain ticketSendDomain = new TicketSendDomain();
				ticketSendDomain.setOrgCode(domain.getOrgCode());
				ticketSendDomain.setDomainCode(domain.getDomainCode());
				ticketSendDomain.setQuality((int) l);
				ticketSendDomain.setOrgCode(domain.getOrgCode());
				ticketSendDomain.setActivityCode(market.getActivityCode());
				ticketSendDomain.setTenantCode(domain.getTenantCode());
				if (StringUtil.isNotBlank(domain.getFrozenStatus())) {
					ticketSendDomain.setFrozenStatus(domain.getFrozenStatus());
				}
				List<String> memberCodes = new ArrayList<>();
				memberCodes.add(domain.getMemberCode());
				ticketSendDomain.setMemberCodes(memberCodes);
				log.info("发券参数:{{}}" + JSONObject.toJSONString(ticketSendDomain));
				List<TicketSendResult> ticketSendResults = ticketComponent.sendTicket(ticketSendDomain);
				i = ticketSendResults.get(0).getTicketCodes().size();
			} catch (Exception e) {
				// 回滚
				log.info("发券异常:{{}}" + e.toString());
				incentiveLimitedHelper.rollBackRedisData(paramList);
			}
			ActivityParticipateInResult inResult = BeanCopyUtils.jsonCopyBean(market,
					ActivityParticipateInResult.class);
			inResult.setEarnTicket(i);
			result.add(inResult);
		}

	}
    

    public List<ActivityParticipateInResult> eligibleActivities(JudgeQualificationDomain domain){
        //查询所有符合spu的所有活动规则
        List<LuckyDrawRuleModel> luckyDrawRuleModels = luckyDrawRuleService.queryLuckyDrawRulesByProductCode(domain.getTenantCode(), domain.getProductCodes());
        //没有限制不发券
        if (CollectionUtils.isEmpty(luckyDrawRuleModels)){
            log.error("校验资格 无规则 跳过:{{}}", JSONObject.toJSONString(domain));
            return new ArrayList<>();
        }
        //所有活动code
        ArrayList<String> activityCodes = new ArrayList<>();
        for (LuckyDrawRuleModel mo : luckyDrawRuleModels) {
            activityCodes.add(mo.getActivityCode());
        }

        //所有正在进行的活动marketing
        List<MarketingModel> marketing = marketingService.getMarketingByActivityCode(domain.getTenantCode(), activityCodes, "04");
        if (CollectionUtils.isEmpty(marketing)){
            return new ArrayList<>();
        }
        List<ActivityParticipateInResult> result = new ArrayList<>();
        //根据用户条件过滤活动
        for (MarketingModel market : marketing) {
            for (LuckyDrawRuleModel luckyDrawRuleModel : luckyDrawRuleModels) {
                if (market.getActivityCode().equals(luckyDrawRuleModel.getActivityCode())) {
                    //规则
                    //根据活动查询出所有用户条件
                    List<QualificationModel> qualificationModels = qualificationService.queryLuckyDrawQualificationsByActivityCodes(domain.getTenantCode(), market.getActivityCode());
                    //过滤规则
                    //用户条件过滤不通过 下一个
                    if (!ActivityFilterUtil.filterActivityByQualifications(qualificationModels, domain.getQualifications()) ||
                            luckyDrawRuleModel.getBuyingQty().compareTo(ConvertUtils.toBigDecimal(domain.getAmount())) > 0){
                        continue;
                    }

                    BigDecimal min = new BigDecimal(luckyDrawRuleModel.getMaxGivingPerUser());
                    //参数数量/限制数量*票数 = 要送多少票  和最大票数限制取最小
                    //参数金额/限制金额*票数 = 要送多少票  和最大票数限制取最小
                    min = min.min(domain.getAmount().divide(luckyDrawRuleModel.getBuyingQty()).multiply(ConvertUtils.toBigDecimal(luckyDrawRuleModel.getEarnTicket())));

                    BigDecimal incentiveValue = min.setScale(0, BigDecimal.ROUND_DOWN);

                    ActivityParticipateInResult inResult = BeanCopyUtils.jsonCopyBean(market, ActivityParticipateInResult.class);
                    inResult.setEarnTicket(incentiveValue.intValue());
                    result.add(inResult);
                }
            }
        }

        return result;
    }



    private boolean limitation(String memberCode,String tenantCode,String activityCode,Integer quality,String end){



        //所有数量限制的集合key: redis自增的key value: 值(出现异常时用于数据回滚).
        List<RedisOpsHelper.IncentiveLimitedParam> paramList = new ArrayList<>();
        List<TPromoIncentiveLimitedVO> luckyDrawLimitedListByActivityCode = incentiveLimitedService.getLuckyDrawLimitedListByActivityCode(activityCode);
        //活动限制
        for (TPromoIncentiveLimitedVO incentiveLimitedVO : luckyDrawLimitedListByActivityCode) {
            //过滤规则
            ErrorCode errorCode = null;

            if (LimitationCodeEnum.USER_TOTAL_COUNT.equalsCode(incentiveLimitedVO.getLimitationCode())) {
                errorCode = ErrorCodes.ACTIVITY_LIMIT_USER_TOTAL_COUNT;
            } else if (LimitationCodeEnum.ACTIVITY_TOTAL_COUNT.equalsCode(incentiveLimitedVO.getLimitationCode())) {
                errorCode = ErrorCodes.ACTIVITY_LIMIT_ACTIVITY_TOTAL_COUNT;
            } else if (LimitationCodeEnum.USER_DAY_COUNT.equalsCode(incentiveLimitedVO.getLimitationCode())) {
                errorCode = ErrorCodes.ACTIVITY_LIMIT_ACTIVITY_DAY_TOTAL_COUNT;
            } else if (LimitationCodeEnum.USER_WEEK_COUNT.equalsCode(incentiveLimitedVO.getLimitationCode())) {
                errorCode = ErrorCodes.ACTIVITY_LIMIT_ACTIVITY_WEEK_TOTAL_COUNT;
            }else if (LimitationCodeEnum.USER_MONTH_COUNT.equalsCode(incentiveLimitedVO.getLimitationCode())) {
                errorCode = ErrorCodes.ACTIVITY_LIMIT_ACTIVITY_MONTH_TOTAL_COUNT;
            }

            // Redis超时时间
            long timeout = DateUtil.parseDate(end, DateUtil.FORMAT_YYYYMMDDHHMISS_14).getTime() - System.currentTimeMillis();

            //过滤扣减活动规则限制
            RedisOpsHelper.IncentiveLimitedParam param = RedisOpsHelper.IncentiveLimitedParam.builder()
                    .limitedCode(incentiveLimitedVO.getLimitationCode())
                    .tenantCode(tenantCode)
                    .activityCode(activityCode)
                    .userCode(memberCode)
                    .totalValue(incentiveLimitedVO.getLimitationValue())
                    //每次参与活动扣减1
                    .incentiveValue(ConvertUtils.toBigDecimal(quality))
                    .build();
            boolean b = incentiveLimitedHelper.lockRedisData(paramList, param, timeout, "");
            if (!b){
                throw Exceptions.fail(errorCode);
            }
        }
        return true;
    }


    public QueryMemberLuckyDrawFrequencyResult queryMemberLuckyDrawFrequency(QueryMemberLuckyDrawFrequencyDomain domain) {

        BaseModel baseModel = BeanCopyUtils.jsonCopyBean(domain, BaseModel.class);


        QueryMemberLuckyDrawFrequencyResult result = new QueryMemberLuckyDrawFrequencyResult();


        List<TPromoIncentiveLimitedVO> luckyDrawLimitedList = incentiveLimitedService.getLuckyDrawLimitedListByActivityCode(domain.getActivityCode());


        if (CollectionUtils.isEmpty(luckyDrawLimitedList)){
            return result;
        }

        //01-活动限制总次数  11-每人限制总次数  13-每人限制单日次数  16-每人限制单周次数  17-每人限制单月次数
        Date now = DateUtil.parseDate(DateUtil.format(new Date(), DateUtil.FORMAT_DEFAULT), DateUtil.FORMAT_DEFAULT);
        for (TPromoIncentiveLimitedVO limitedVO : luckyDrawLimitedList) {
            if (limitedVO.getLimitationCode().equals("13")){
                String beginTime = DateUtil.format(DateUtil.getStartOfDay(now), DateUtil.FORMAT_YYYYMMDDHHMISS_14);
                String endTime = DateUtil.format(DateUtil.getEndOfDay(now), DateUtil.FORMAT_YYYYMMDDHHMISS_14);
                Integer day = ticketService.selectCountByMemberCode(baseModel, domain.getMemberCode(), beginTime, endTime);
                result.setDay(day);
            }else if (limitedVO.getLimitationCode().equals("16")){
                String beginTime = DateUtil.format(getFirstDayOfWeek(now), DateUtil.FORMAT_YYYYMMDDHHMISS_14);
                String endTime = DateUtil.format(getLastDayOfWeek(DateUtil.getEndOfDay(now)), DateUtil.FORMAT_YYYYMMDDHHMISS_14);
                Integer week = ticketService.selectCountByMemberCode(baseModel, domain.getMemberCode(), beginTime, endTime);
                result.setWeek(week);
            }else if (limitedVO.getLimitationCode().equals("17")){
                String beginTime = DateUtil.format(DateUtil.getStartDayOfMonth(now), DateUtil.FORMAT_YYYYMMDDHHMISS_14);
                String endTime = DateUtil.format(getCurrentMonthLastDay(DateUtil.getEndOfDay(now)), DateUtil.FORMAT_YYYYMMDDHHMISS_14);
                Integer month = ticketService.selectCountByMemberCode(baseModel, domain.getMemberCode(), beginTime, endTime);
                result.setMonth(month);
            }else if (limitedVO.getLimitationCode().equals("11")){
                Integer total = ticketService.selectCountByMemberCode(baseModel, domain.getMemberCode(),"","");
                result.setTotal(total);
            }else if (limitedVO.getLimitationCode().equals("01")){
                result.setActivityTotal(limitedVO.getLimitationValue().intValue()-ticketService.selectActivityByStatus(baseModel,"02")-ticketService.selectActivityByStatus(baseModel,"03"));
            }

        }

        return result;

    }





    //当前周的第一天
    private Date getFirstDayOfWeek(Date date) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        cal.set(Calendar.DAY_OF_WEEK, 2);
        return cal.getTime();
    }

    //当前周的最后一天
    private Date getLastDayOfWeek(Date date) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        cal.set(Calendar.DAY_OF_WEEK, 2);
        cal.set(Calendar.DATE, cal.get(Calendar.DATE) + 6);
        return cal.getTime();
    }

    //当前月的最后一天
    private Date getCurrentMonthLastDay(Date date) {
        Calendar cale = Calendar.getInstance();
        cale.setTime(date);
        cale.add(Calendar.MONTH, 1);
        cale.set(Calendar.DAY_OF_MONTH, 0);
        return cale.getTime();
    }


    public void updateFrozenStatus(LuckyDrawMemberChanceDomain domain) {
        ticketService.updateFrozenStatus(domain);
    }

    public List<ExportLuckyDrawResult> exportLuckyDraw(ExportLuckyDrawDto dto) {
        //先查询抽奖券,再根据抽奖券查询中奖记录
        PageMethod.startPage(dto.getPageNum(), dto.getPageSize());
        TicketModel ticketModel = BeanCopyUtils.jsonCopyBean(dto, TicketModel.class);
        ticketModel.setOrder("id");
        PageData<TicketModel> ticketModelPageData = ticketService.queryListByStatus(ticketModel, dto);
        if (CollectionUtils.isEmpty(ticketModelPageData.getList())){
            return Collections.emptyList();
        }
        List<TicketModel> ticketModels = ticketModelPageData.getList();
        List<String> ticketList = ticketModels.stream().map(TicketModel::getTicketCode).collect(Collectors.toList());

        QueryRecordByTicketsModel model = BeanCopyUtils.jsonCopyBean(dto, QueryRecordByTicketsModel.class);
        model.setTicketCodes(ticketList);
        List<PrizeRecordModel> prizeRecordModels = recordService.queryLuckyRecordList(model);


        List<String> prizeMemberCode = ticketModels.stream().map(TicketModel::getMemberCode).collect(Collectors.toList());
        QueryMemberParam queryMemberParam = new QueryMemberParam();
        queryMemberParam.setTenantCode(dto.getTenantCode());
        queryMemberParam.setMemberCodes(prizeMemberCode);
        queryMemberParam.setDomainCode(dto.getDomainCode());
        Result<List<QueryMemberListByConditionResult>> listResult =
                memberFeignClient.queryMemberListByMemberCode(queryMemberParam);
        Map<String, QueryUserResult> userMap = new HashMap<>();
        Map<String, QueryMemberListByConditionResult> memberMap = new HashMap<>();
        if (listResult.isSuccess()){

            memberMap = listResult.getData().stream().collect(Collectors.toMap(QueryMemberListByConditionResult::getMemberCode, x -> x, (k1, k2) -> k1));

            QueryUserParam param = new QueryUserParam();
            //memberCodeList转换为逗号分隔
            param.setUserCodes(String.join(",", prizeMemberCode));
            param.setDomainCode(dto.getDomainCode());
            param.setTenantCode(dto.getTenantCode());
            Result<List<QueryUserResult>> userList = idmFeignClient.queryUserList(param);
            if (userList.isSuccess()){
                userMap = userList.getData().stream().collect(Collectors.toMap(QueryUserResult::getUserCode, x -> x));
            }

        }


        List<PrizeModel> languagePrizeList = getLanguagePrizeList(ticketModel, dto.getLanguage());
        Map<String, PrizeModel> prizeMapByNo = languagePrizeList.stream().collect(Collectors.toMap(PrizeModel::getPrizeNo, x -> x));

        ArrayList<ExportLuckyDrawResult> results = new ArrayList<>();

        Map<String, PrizeRecordModel> recordByTicket = prizeRecordModels.stream().collect(Collectors.toMap(PrizeRecordModel::getTicketCode, x -> x, (a, b) -> a));


        for (TicketModel ticket : ticketModelPageData.getList()) {
            ExportLuckyDrawResult exportLuckyDrawResult = new ExportLuckyDrawResult();
            exportLuckyDrawResult.setDrawTime(DateUtil.format(DateUtil.parseDate(ticket.getUseTime(),DateUtil.FORMAT_YYYYMMDDHHMISS_14),DateUtil.FORMAT_YYYYMMDDHHMISS));
            //exportLuckyDrawResult.setMemberCode(ticket.getMemberCode());
            if (userMap.containsKey(ticket.getMemberCode())){
                exportLuckyDrawResult.setMemberCode(userMap.get(ticket.getMemberCode()).getAccount());
            }
            //避免名称为null
            if (null != memberMap.get(ticket.getMemberCode()) && StringUtil.isNotBlank(memberMap.get(ticket.getMemberCode()).getFirstName())){
                exportLuckyDrawResult.setMemberName(memberMap.get(ticket.getMemberCode()).getFirstName());
            }
            if ( null!= memberMap.get(ticket.getMemberCode()) && StringUtil.isNotBlank(memberMap.get(ticket.getMemberCode()).getLastName())){
                exportLuckyDrawResult.setMemberName(exportLuckyDrawResult.getMemberName()+" "+memberMap.get(ticket.getMemberCode()).getLastName());
            }
            if ( null!= memberMap.get(ticket.getMemberCode()) && StringUtil.isNotBlank(memberMap.get(ticket.getMemberCode()).getMobile())){
                exportLuckyDrawResult.setMemberMobile(memberMap.get(ticket.getMemberCode()).getMobile());
            }

            results.add(exportLuckyDrawResult);
            if (ticket.getStatus().equals(TicketStatusEnum.LUCKY.code())){
                exportLuckyDrawResult.setDrawResult(prizeMapByNo.get(recordByTicket.get(ticket.getTicketCode()).getPrizeNo()).getPrizeName());
            }else if (ticket.getStatus().equals(TicketStatusEnum.USED.code())){
                exportLuckyDrawResult.setDrawResult("未中奖");
            }
        }
        return results;
    }

    public List<ExportLuckyDrawResultTotal> exportLuckyDrawTotal(ExportLuckyDrawDto exportLuckyDrawDto) {

        List<PrizeModel> byActivityCode = prizeService.findListByActivityCode(exportLuckyDrawDto.getActivityCode());

        if (CollectionUtils.isEmpty(byActivityCode)){
            return Collections.emptyList();
        }


        ArrayList<ExportLuckyDrawResultTotal> exportLuckyDrawResultTotals = new ArrayList<>();

        for (PrizeModel prizeModel : byActivityCode) {
            ExportLuckyDrawResultTotal total = new ExportLuckyDrawResultTotal();
            if (exportLuckyDrawDto.getLanguage().equals("zh_CN")) {
                total.setPrize(Objects.requireNonNull(PrizeTypeEnum.getEnumByCode(prizeModel.getPrizeType())).language());
            } else {
                total.setPrize(Objects.requireNonNull(PrizeTypeEnum.getEnumByCode(prizeModel.getPrizeType())).desc());
            }
            if (prizeModel.getPrizeType().equals(PrizeTypeEnum.PRODUCT.code()) || prizeModel.getPrizeType().equals(PrizeTypeEnum.COUPON.code())) {
                total.setProductName(prizeModel.getPrizeName());
            }
            total.setPrizeCode(prizeModel.getPrizeCode());
            total.setPrizeNum(prizeModel.getPrizeQuota());
            total.setPrizeInventory(prizeModel.getPrizeInventory());
            total.setPrizeDrawn(prizeModel.getPrizeQuota() - prizeModel.getPrizeInventory());
            exportLuckyDrawResultTotals.add(total);
        }

        return exportLuckyDrawResultTotals;


    }
}
