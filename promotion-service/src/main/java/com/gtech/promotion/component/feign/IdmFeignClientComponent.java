package com.gtech.promotion.component.feign;

import com.gtech.basic.idm.web.vo.param.QueryOpUserAccountByCodesParam;
import com.gtech.basic.idm.web.vo.param.QueryUserParam;
import com.gtech.basic.idm.web.vo.result.QueryOpUserAccountListResult;
import com.gtech.basic.idm.web.vo.result.QueryUserResult;
import com.gtech.commons.result.Result;
import com.gtech.promotion.dao.model.activity.ActivityModel;
import com.gtech.promotion.feign.IdmFeignClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

@Component
public class IdmFeignClientComponent {

    @Autowired
    private IdmFeignClient idmFeignClient;

    public List<QueryOpUserAccountListResult> getIdmOpUserAccount(List<ActivityModel> list) {

        List<String> userCodes = getUserCodes(list);
        return queryOpUserAccountList(userCodes);
    }

    public List<QueryOpUserAccountListResult> queryOpUserAccountList(List<String> userCodes) {
        if(CollectionUtils.isEmpty(userCodes)) {
        	return Collections.emptyList();
        }

        QueryOpUserAccountByCodesParam queryOpUserAccountByCodesParam = new QueryOpUserAccountByCodesParam();
        queryOpUserAccountByCodesParam.setUserCodes(userCodes);
        Result<List<QueryOpUserAccountListResult>> listResult = idmFeignClient.queryOpUserAccountByCodes(queryOpUserAccountByCodesParam);
        return listResult.getData();
    }

    public List<String> getUserCodes(List<ActivityModel> list) {
        List<String> userCodes = new ArrayList<>();
        list.forEach(x -> {
            if (null != x.getCreateUser() && !userCodes.contains(x.getCreateUser())) {
                userCodes.add(x.getCreateUser());
            }
        });
        return userCodes;
    }

    public List<QueryUserResult> queryIdmUserList(String domainCode,String tenantCode,List<ActivityModel> list) {
        List<String> userCodes = getUserCodes(list);
        return queryUserResults(domainCode, tenantCode, userCodes);
    }

    public List<QueryUserResult> queryUserResults(String domainCode, String tenantCode, List<String> userCodes) {
        if(CollectionUtils.isEmpty(userCodes)) {
           	return Collections.emptyList();
           }
        QueryUserParam queryUserParam = new QueryUserParam();
        queryUserParam.setDomainCode(domainCode);
        queryUserParam.setTenantCode(tenantCode);
        queryUserParam.setUserCodes(String.join(",", userCodes));
        Result<List<QueryUserResult>> listResult = idmFeignClient.queryUserList(queryUserParam);
        return listResult.getData();
    }
}
