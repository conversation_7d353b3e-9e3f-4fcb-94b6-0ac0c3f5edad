/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 * <p>
 * This software is the confidential and proprietary information of GTech. You shall not disclose such
 * Confidential Information and shall use it only in accordance with the terms of the license agreement
 * you entered into with GTech.
 * <p>
 * <PERSON><PERSON><PERSON> MAKES NO REPRESENTATIONS OR WAR<PERSON>NTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.component.activity;

import static com.gtech.promotion.code.activity.ActivityExtendParamsEnum.GIVEAWAY_POOL_ENABLE;
import static com.gtech.promotion.code.activity.ActivityExtendParamsEnum.GIVEAWAY_RULES;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.function.Predicate;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.collections4.map.HashedMap;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.gtech.basic.masterdata.response.JsonResult;
import com.gtech.commons.utils.BeanCopyUtils;
import com.gtech.commons.utils.CryptoUtils;
import com.gtech.commons.utils.StringUtil;
import com.gtech.member.common.utils.RandomUtil;
import com.gtech.promotion.checker.activity.TPromoProductChecker;
import com.gtech.promotion.code.PromotionConstants;
import com.gtech.promotion.code.activity.ActivityStatusEnum;
import com.gtech.promotion.code.activity.ActivityTypeEnum;
import com.gtech.promotion.code.activity.GiveawayMethodEnum;
import com.gtech.promotion.code.activity.GiveawayPoolEnableEnum;
import com.gtech.promotion.code.activity.GiveawaySortRuleEnum;
import com.gtech.promotion.code.activity.PriceConditionEnum;
import com.gtech.promotion.code.activity.ProductSelectionEnum;
import com.gtech.promotion.code.activity.ProductTypeEnum;
import com.gtech.promotion.code.activity.SeqNumEnum;
import com.gtech.promotion.code.activity.TurnOnAndOffEnum;
import com.gtech.promotion.code.coupon.CouponStatusEnum;
import com.gtech.promotion.code.marketing.BoostSharingTypeEnum;
import com.gtech.promotion.dao.entity.activity.ActivityEntity;
import com.gtech.promotion.dao.entity.marketing.RightOfFirstRefusalEntity;
import com.gtech.promotion.dao.model.activity.ActivityFunctionParamRankModel;
import com.gtech.promotion.dao.model.activity.ActivityLanguageModel;
import com.gtech.promotion.dao.model.activity.ActivityModel;
import com.gtech.promotion.dao.model.activity.ActivityPeriodModel;
import com.gtech.promotion.dao.model.activity.FunctionParamModel;
import com.gtech.promotion.dao.model.activity.GiveawayVO;
import com.gtech.promotion.dao.model.activity.QualificationModel;
import com.gtech.promotion.dao.model.activity.TPromoActivityProductVO;
import com.gtech.promotion.dao.model.activity.TPromoActivityStoreVO;
import com.gtech.promotion.dao.model.activity.TPromoIncentiveLimitedVO;
import com.gtech.promotion.dao.model.activity.TemplateFunctionModel;
import com.gtech.promotion.dao.model.activity.TemplateModel;
import com.gtech.promotion.dao.model.marketing.BoostSharingModel;
import com.gtech.promotion.dao.model.marketing.MarketingGroupMode;
import com.gtech.promotion.dto.cache.ActivityCacheDTO;
import com.gtech.promotion.dto.cache.ProductSkuDetailDTO;
import com.gtech.promotion.dto.in.activity.ShoppingCartDTO;
import com.gtech.promotion.dto.in.flashsale.WriteOffOfPreEmptiveRightsDto;
import com.gtech.promotion.exception.Check;
import com.gtech.promotion.exception.PromotionException;
import com.gtech.promotion.feign.MasterDataFeignClient;
import com.gtech.promotion.helper.ActivityRedisHelpler;
import com.gtech.promotion.helper.FunctionHelper;
import com.gtech.promotion.service.activity.ActivityFuncParamService;
import com.gtech.promotion.service.activity.ActivityFuncRankService;
import com.gtech.promotion.service.activity.ActivityLanguageService;
import com.gtech.promotion.service.activity.ActivityPeriodService;
import com.gtech.promotion.service.activity.ActivityProductDetailService;
import com.gtech.promotion.service.activity.ActivityService;
import com.gtech.promotion.service.activity.ActivityStoreService;
import com.gtech.promotion.service.activity.GiveawayService;
import com.gtech.promotion.service.activity.QualificationService;
import com.gtech.promotion.service.activity.TPromoIncentiveLimitedService;
import com.gtech.promotion.service.activity.TemplateService;
import com.gtech.promotion.service.coupon.PromoCouponActivityService;
import com.gtech.promotion.service.coupon.PromoCouponCodeUserService;
import com.gtech.promotion.service.marketing.BoostSharingService;
import com.gtech.promotion.service.marketing.MarketingGroupService;
import com.gtech.promotion.service.marketing.RightOfFirstRefusalService;
import com.gtech.promotion.service.mongo.activity.TPromoProductService;
import com.gtech.promotion.utils.Constants;
import com.gtech.promotion.vo.bean.Giveaway;
import com.gtech.promotion.vo.bean.ProductAttribute;
import com.gtech.promotion.vo.bean.ProductCodes;
import com.gtech.promotion.vo.bean.ProductDetail;
import com.gtech.promotion.vo.param.activity.CustomCondition;
import com.gtech.promotion.vo.param.activity.MallProductConditionParam;
import com.gtech.promotion.vo.result.activity.ActivityGroupCache;

import lombok.extern.slf4j.Slf4j;

/**
 * 缓存数据
 *
 */
@Slf4j
@Service
public class ActivityCacheDomain {


    public static final String TENANT_CUSTOM_CONDITION = "TENANT_CUSTOM_CONDITION";
    public static final String PRODUCT_RELATION_ALL = "0000";
    @Autowired
    private ActivityService activityService;

    @Autowired
    private TemplateService templateService;

    @Autowired
    private ActivityPeriodService activityPeriodService;
    @Autowired
    private ActivityFuncRankService activityFuncRankService;

    @Autowired
    private ActivityFuncParamService activityFuncParamService;

    @Autowired
    private GiveawayService giveawayService;

    @Autowired
    private TPromoProductService productService;

    @Autowired
    private ActivityStoreService tPromoStoreService;

    @Autowired
    private ActivityProductDetailService productDetailService;

    @Autowired
    private TPromoIncentiveLimitedService incentiveLimitedService;

    @Autowired
    private PromoCouponActivityService couponActivityService;

    @Autowired
    private QualificationService qualificationService;

    @Autowired
    private ActivityLanguageService activityLanguageService;

    @Autowired
    private StringRedisTemplate redisTemplate;

    @Autowired
    private ActivityRedisHelpler redisService;

    @Autowired
    private ActivityComponentDomain activityComponentDomain;

    @Autowired
	private PromoGroupRelationDomain promoGroupRelationDomain;

    @Autowired
    private MasterDataFeignClient masterDataFeignClient;

    @Autowired
    private MarketingGroupService marketingGroupService;
    @Autowired
    private BoostSharingService boostSharingService;

    @Autowired
    private RightOfFirstRefusalService rightOfFirstRefusalService;
	@Autowired
	private PromoCouponCodeUserService couponCodeUserService;

    private static final String TENANT_CODE = ":TENANTCODE=";
    private static final String ACTIVITY_TYPE = ":ACTIVITYTYPE=";
    private static final String ACTIVITY_CODE = ":ACTIVITYCODE=";
    private static final String STATUS = ":STATUS=";
    private static final AtomicBoolean lockActivityOk = new AtomicBoolean();



    /**
     * 存入缓存
     */
    public void putCache(ActivityCacheDTO activityCache) {

        if (null != activityCache) {
            String type = activityCache.getActivityModel().getActivityStatus();
            //不符合的状态不存库
            if (type.equals(ActivityStatusEnum.END.code()) || type.equals(ActivityStatusEnum.CLOSURE.code())) {
                return;
            }
            redisService.setActivityCache(activityCache);
            redisService.removeActivityCacheLevelTwo(activityCache.getActivityModel().getTenantCode());
        }
    }

    /**
     * 删除缓存
     */
    public void delCache(String tenantCode, String activityType, String activityCode, String activityStatus) {

        redisService.removeActivityCache(tenantCode, activityType, activityCode, activityStatus);
        redisService.removeActivityCacheLevelTwo(tenantCode);
    }

    /**
     * 存入缓存
     */
    public void putCache(String tenantCode, String activityCode) {

        ActivityModel activity = this.activityService.findActivityByActivityCode(tenantCode, activityCode);
        if (this.activityComponentDomain.isEffectiveActivity(activity, new Date())) {
            this.putCache(this.getActivityCacheDTO(activity));
        }
    }

    /**
     * 修改活动状态使用的方法
     *
     * @param activityStatus 活动未修改前状态
     * @param targetStatus 目标状态
     */
    public void updateCacheByActivityStatus(String tenantCode, String activityType, String activityCode, String activityStatus, String targetStatus) {

        delCache(tenantCode, activityType, activityCode, activityStatus);
        if (!ActivityStatusEnum.END.equalsCode(targetStatus)) {
            setActivityCache(tenantCode, activityCode, targetStatus);
            return;
        }
        if (ActivityStatusEnum.END.equalsCode(activityStatus) && !ActivityStatusEnum.END.equalsCode(targetStatus) && !ActivityStatusEnum.CLOSURE.equalsCode(targetStatus)) {
            setActivityCache(tenantCode, activityCode, targetStatus);
        }
    }

    private void setActivityCache(String tenantCode, String activityCode, String targetStatus) {

        ActivityModel activity = activityService.findActivity(tenantCode, activityCode, null);
        ActivityCacheDTO cacheDTO = getActivityCacheDTO(activity);
        cacheDTO.getActivityModel().setActivityStatus(targetStatus);
        redisService.removeActivityCache(tenantCode, activity.getActivityType(), activityCode, targetStatus);
        redisService.setActivityCache(cacheDTO);
        redisService.removeActivityCacheLevelTwo(tenantCode);

    }

    private Map<String, String> localCacheMap = new HashMap<>();

    public void extendActivityCacheTime(ActivityModel activityModel) {
        setActivityCache(activityModel.getTenantCode(), activityModel.getActivityCode(), ActivityStatusEnum.EFFECTIVE.code());
    }

    /**
     * 获取缓存
     *
     * @param activityStatus 1：已创建的活动，用户购物车沙盒测试 2：已审核的活动，用于生产环境
     * @param activityType 活动类型(null时为所有类型)
     * @return Map k-id v-ActivityCacheDTO
     */
    public Map<String, ActivityCacheDTO> getActivityCacheMap(String tenantCode, String language, Integer activityStatus, ActivityTypeEnum activityType) {

        Map<String, ActivityCacheDTO> caches = new HashMap<>();
        String key = getCacheKey(tenantCode, activityStatus, activityType);
        Date date = new Date();

        String levelTwoCache = null;

		List<ActivityGroupCache> groupCacheList = promoGroupRelationDomain.getTenantGroupCache(tenantCode);
        // redis缓存
        try {
            // 本地缓存
			levelTwoCache = redisService.getActivityCacheLevelTwo(tenantCode, key);

            if (StringUtil.isNotBlank(levelTwoCache)) {
                String localCacheMapElement = localCacheMap.get(key);
                if (StringUtil.isNotBlank(localCacheMapElement) && levelTwoCache.equals(CryptoUtils.md5Encrypt(localCacheMapElement))) {
					// log.info("直接走本地缓存，{}，{}", tenantCode, levelTwoCache);
					return dealActivityCacheMap(groupCacheList, language, date,
							JSON.parseObject(localCacheMapElement, new TypeReference<Map<String, ActivityCacheDTO>>() {
                    }));
                }
                localCacheMap.remove(key);
            }
			// log.info("本地缓存已过期，{}", tenantCode);
            // 如果本地缓存没有，就走redis缓存
            caches = getRedis(tenantCode, language, activityStatus, activityType);
        } catch (Exception e) {
			// log.warn("ActivityCacheDomain#getActivityCacheMap", e);
            caches = getActivityCacheFromMysql(tenantCode, language, activityStatus, activityType);
        }

        if (MapUtils.isEmpty(caches)) {
            return caches;
        } else {
			caches = dealActivityCacheMap(groupCacheList, language, date, caches);
            //添加默认层级赠品
            Iterator<Entry<String, ActivityCacheDTO>> iterator = caches.entrySet().iterator();
            while (iterator.hasNext()) {
                Map.Entry<String, ActivityCacheDTO> activityCacheEntry = iterator.next();
                ActivityCacheDTO activityCacheDTO = activityCacheEntry.getValue();
                addMissingGiveaway(activityCacheDTO, activityCacheDTO.getPromoFuncRanks());
            }
        }
        try {
			// log.info("存放本地缓存，{}", tenantCode);
            String tempStr = JSON.toJSONString(caches);
            localCacheMap.put(key, tempStr);
            redisService.setActivityCacheLevelTwo(tenantCode, key, CryptoUtils.md5Encrypt(tempStr));
        } catch (Exception e) {
            log.error("redis设置二级缓存失败：", e);
        }
        return caches;
    }


    public void flushActivityCache(String tenantCode){
        String language = "en-US";
        Map<String, ActivityCacheDTO> caches = new HashMap<>();
        String key = getCacheKey(tenantCode, 2, null);
        Date date = new Date();

        String levelTwoCache = null;

        List<ActivityGroupCache> groupCacheList = promoGroupRelationDomain.getTenantGroupCache(tenantCode);

        // 本地缓存
        levelTwoCache = redisService.getActivityCacheLevelTwo(tenantCode, key);

        if (StringUtil.isNotBlank(levelTwoCache)) {
            localCacheMap.remove(key);
        }
        log.info("刷新本地缓存，{}", tenantCode);

        caches = getActivityCacheFromMysql(tenantCode, language, 2, null);


        caches = dealActivityCacheMap(groupCacheList, language, date, caches);
        //添加默认层级赠品
        Iterator<Entry<String, ActivityCacheDTO>> iterator = caches.entrySet().iterator();
        while (iterator.hasNext()) {
            Map.Entry<String, ActivityCacheDTO> activityCacheEntry = iterator.next();
            ActivityCacheDTO activityCacheDTO = activityCacheEntry.getValue();
            addMissingGiveaway(activityCacheDTO, activityCacheDTO.getPromoFuncRanks());
        }

        try {
            log.info("存放本地缓存，{}", tenantCode);
            String tempStr = JSON.toJSONString(caches);
            localCacheMap.put(key, tempStr);
            redisService.setActivityCacheLevelTwo(tenantCode, key, CryptoUtils.md5Encrypt(tempStr));
        } catch (Exception e) {
            log.error("redis设置二级缓存失败：", e);
        }


    }

    /**
     * @param language
     * @param date
     * @return
     * <AUTHOR>
     * @Date 2020-02-25
     */
	private Map<String, ActivityCacheDTO> dealActivityCacheMap(List<ActivityGroupCache> groupCacheList, String language, Date date,
			Map<String, ActivityCacheDTO> cacheMap) {

        if (MapUtils.isEmpty(cacheMap)) {
            return new HashMap<>();
        }
        Map<String, ActivityCacheDTO> resultMap = new HashMap<>();
        for (Map.Entry<String, ActivityCacheDTO> entry : cacheMap.entrySet()) {

            if (!entry.getValue().isLastVersion()) {
                throw new PromotionException("Version updated.");
            }
			entry.getValue().setGroupCacheList(groupCacheList);
            if (this.activityComponentDomain.isEffectiveActivity(entry.getValue().getActivityModel(), date)) {
                this.languageDeal(entry.getValue(), language);
                resultMap.put(entry.getKey(), entry.getValue());
            }
        }
        return resultMap;
    }

    private Map<String, ActivityCacheDTO> getRedis(String tenantCode, String language, Integer activityStatus, ActivityTypeEnum activityType) {

        Map<String, ActivityCacheDTO> caches = new HashMap<>();

        Set<String> keys = new HashSet<>();
        List<ActivityEntity> activityEntities = activityService.queryActivityByTenantCodeAndStatusAndType(tenantCode, activityStatus, activityType);
        activityEntitys(keys, activityEntities);

        List<String> values = null;
        if (CollectionUtils.isNotEmpty(keys)) {
            values = getActivityByKeys(keys);
        }

        if (CollectionUtils.isEmpty(values) || values.stream().anyMatch(StringUtil::isBlank)) {
            if (lockActivityOk.compareAndSet(false, true)) {
                try {
                    log.info("redis中没有，走db，{}", tenantCode);
                    return this.getActivityCacheFromMysql(tenantCode, language, activityStatus, activityType);
                } finally {
                    lockActivityOk.set(false);
                }
            }
        } else {
            for (String string : values) {
                ActivityCacheDTO cacheDTO = JSON.parseObject(string, ActivityCacheDTO.class);
                if (null != cacheDTO && checkActivityIsNull(caches, keys, cacheDTO)) {
                    return this.getActivityCacheFromMysql(tenantCode, language, activityStatus, activityType);
                }
            }
        }
        return caches;
    }

    private boolean checkActivityIsNull(Map<String, ActivityCacheDTO> caches, Set<String> keys, ActivityCacheDTO cacheDTO) {
        if (!cacheDTO.isLastVersion()) {
            throw new PromotionException("Version updated.");
        }

        if (cacheDTO.getActivityModel() != null && StringUtils.isBlank(cacheDTO.getActivityModel().getTemplateCode())) {
            redisTemplate.delete(keys);
            return true;
        }

        caches.put(cacheDTO.getActivityModel().getActivityCode(), cacheDTO);
        return false;
    }

    private List<String> getActivityByKeys(Set<String> keys) {
        List<String> values;//获取活动
        values = redisTemplate.opsForValue().multiGet(keys);
        return values;
    }

    private void activityEntitys(Set<String> keys, List<ActivityEntity> activityEntities) {
        if (CollectionUtils.isNotEmpty(activityEntities)) {
            activityEntities.forEach(x -> {
                String redisKey = Constants.PROMOTION_ACTIVITY_CACHE + TENANT_CODE + x.getTenantCode() + ACTIVITY_TYPE +
                        x.getActivityType() + ACTIVITY_CODE + x.getActivityCode() + STATUS + x.getActivityStatus();
                log.info("redis中的key，{}", redisKey);
                keys.add(redisKey);
            });
        }
    }

    /**
     * Get cache key
     */
    private String getCacheKey(String tenantCode, Integer activityStatus, ActivityTypeEnum activityType) {

        String cacheKey = "";
        if (null != activityStatus && activityStatus.equals(2)) {//查询正式数据
            if (null == activityType) {//查询04生效中的券和活动
                cacheKey = Constants.PROMOTION_ACTIVITY_CACHE + TENANT_CODE + tenantCode + ":*:STATUS=" + ActivityStatusEnum.EFFECTIVE.code();
            } else {//根据activityType查询04生效中的券或者活动
                cacheKey = Constants.PROMOTION_ACTIVITY_CACHE + TENANT_CODE + tenantCode + ACTIVITY_TYPE + activityType.code() + "*:STATUS=" + ActivityStatusEnum.EFFECTIVE.code();
            }
        } else if (null != activityStatus && activityStatus.equals(1)) {//沙箱数据测试
            if (null == activityType) {//所有状态的券和活动
                cacheKey = Constants.PROMOTION_ACTIVITY_CACHE + TENANT_CODE + tenantCode + ":*";
            } else {//所有状态的券或者活动
                cacheKey = Constants.PROMOTION_ACTIVITY_CACHE + TENANT_CODE + tenantCode + ACTIVITY_TYPE + activityType.code() + "*";
            }
        }
        return cacheKey;
    }

    public Map<String, ActivityCacheDTO> getActivityCacheFromMysql(String tenantCode, String language, Integer activityStatus, ActivityTypeEnum activityType) {

        Map<String, ActivityCacheDTO> caches = new HashMap<>();

        List<ActivityModel> activities = getActivityList(tenantCode, activityType, 0);
        if (CollectionUtils.isEmpty(activities)) {
            return caches;
        }

        List<ActivityCacheDTO> activityCacheDTOS = getActivityCacheDTOs(activities);
        for (ActivityCacheDTO activityCacheDTO : activityCacheDTOS) {

            this.languageDeal(activityCacheDTO, language);

            if (activityStatus == 2) {
                if (ActivityStatusEnum.EFFECTIVE.equalsCode(activityCacheDTO.getActivityModel().getActivityStatus())) {
                    caches.put(activityCacheDTO.getActivityModel().getActivityCode(), activityCacheDTO);
                }
            } else {
                caches.put(activityCacheDTO.getActivityModel().getActivityCode(), activityCacheDTO);
            }
            //所有活动存入redis
            putCache(activityCacheDTO);
        }

        return caches;
    }

    private List<ActivityCacheDTO> getActivityCacheDTOs(List<ActivityModel> activities) {
        List<ActivityCacheDTO> activityCacheDTOS = new ArrayList<>();
        String tenantCode = activities.get(0).getTenantCode();
        List<String> activityCodes = activities.stream().map(ActivityModel::getActivityCode).collect(Collectors.toList());
        //根据规则主体获取规则模板
        List<TemplateModel> templateModels = templateService.queryTemplateAll();
        Map<String, List<TemplateModel>> collect = templateModels.stream().collect(Collectors.groupingBy(TemplateModel::getTemplateCode));

        //规则函数层级列表
        List<ActivityFunctionParamRankModel> rankListByActivityCodes = activityFuncRankService.getRankListByActivityCodes(tenantCode, activityCodes);
        Map<String, List<ActivityFunctionParamRankModel>> promoFuncRanks = rankListByActivityCodes.stream().collect(Collectors.groupingBy(ActivityFunctionParamRankModel::getActivityCode));
        List<String> rankIds = rankListByActivityCodes.stream().map(ActivityFunctionParamRankModel::getId).collect(Collectors.toList());
        //规则函数参数列表
        List<FunctionParamModel> temps = activityFuncParamService.getRuleFuncParamListByRankIds(tenantCode, rankIds);

        //渠道对象列表
        List<TPromoActivityStoreVO> promoChannels = tPromoStoreService.getStoresByActivityCodes(tenantCode, activityCodes);
        Map<String, List<TPromoActivityStoreVO>> stringListMap = promoChannels.stream().collect(Collectors.groupingBy(TPromoActivityStoreVO::getActivityCode));

        //资格
        List<QualificationModel> qualificationModels = qualificationService.queryQualificationsByActivityCodes(tenantCode, activityCodes);
        Map<String, List<QualificationModel>> qualificationMap = qualificationModels.stream().collect(Collectors.groupingBy(QualificationModel::getActivityCode));
        // 周期
        List<ActivityPeriodModel> periodModels = activityPeriodService.queryPeriodByActivityCodes(tenantCode, activityCodes);
        Map<String, List<ActivityPeriodModel>> periodMap = periodModels.stream().collect(Collectors.groupingBy(ActivityPeriodModel::getActivityCode));

        //商品对象列表
        List<TPromoActivityProductVO> promoProducts = productService.getPromoProductByActivityCodes(tenantCode, activityCodes);
        Map<String, List<TPromoActivityProductVO>> productMap = promoProducts.stream().collect(Collectors.groupingBy(TPromoActivityProductVO::getActivityCode));

        //赠品对象列表
        List<GiveawayVO> giveawayVOS = giveawayService.getGiftListByActivityCodes(tenantCode, activityCodes);

        Map<String, List<GiveawayVO>> giveawayMap = giveawayVOS.stream().collect(Collectors.groupingBy(GiveawayVO::getActivityCode));

        //奖励限制列表
        List<TPromoIncentiveLimitedVO> incentiveLimitedList = incentiveLimitedService.getLimitedListByActivityCodes(tenantCode, activityCodes);
        Map<String, List<TPromoIncentiveLimitedVO>> limitedMap = incentiveLimitedList.stream().collect(Collectors.groupingBy(TPromoIncentiveLimitedVO::getActivityCode));

        //多语言列表
		List<ActivityLanguageModel> languages = activityLanguageService.queryActivityLanguagesByActivityCodes(tenantCode, null, activityCodes);
        Map<String, List<ActivityLanguageModel>> languagesMap = languages.stream().collect(Collectors.groupingBy(ActivityLanguageModel::getActivityCode));

        //SKU和套装商品列表// NOSONAR
        List<ProductSkuDetailDTO> promoProductDetails = productDetailService.queryOneGroupByActivityCodes(tenantCode, activityCodes);// NOSONAR
        Map<String, List<ProductSkuDetailDTO>> skuDetailMap = promoProductDetails.stream().collect(Collectors.groupingBy(ProductSkuDetailDTO::getActivityCode));// NOSONAR


        for (ActivityModel activity : activities) {
            ActivityCacheDTO ac = new ActivityCacheDTO();
            String activityCode = activity.getActivityCode();

            ac.setVersion(ActivityCacheDTO.LAST_VERSION);
            setCouponInfo(activity, activityCode);
            ac.setActivityModel(activity);

            TemplateModel promoTemplate = collect.get(activity.getTemplateCode()).get(0);
            ac.setPromoTemplate(promoTemplate);
            ac.setQualificationModels(getListOrEmpty(qualificationMap.get(activityCode)));
            ac.setPromoFuncRanks(getListOrEmpty(promoFuncRanks.get(activityCode)));
            List<FunctionParamModel> promoFuncParams = new ArrayList<>();
            //函数
            functionParam(promoFuncRanks, temps, activityCode, promoFuncParams);
            ac.setPromoFuncParams(promoFuncParams);

            ac.setPromoTemplateFunctions(FunctionHelper.queryTemplateFunctionList(activity.getTemplateCode()));
            ac.setPromoChannels(getListOrEmpty(stringListMap.get(activityCode)));

            List<Giveaway> giveaways = BeanCopyUtils.jsonCopyList(getListOrEmpty(giveawayMap.get(activityCode)), Giveaway.class);
            //规则函数列表
            JSONObject extendParams = Optional.ofNullable(activity.getExtendParams()).orElse(new JSONObject());
            // set默认值 做到版本兼容
            extendParams.computeIfAbsent(GIVEAWAY_POOL_ENABLE.getCode(), k -> GiveawayPoolEnableEnum.DISABLE.getCode());
            Map<Integer, List<Giveaway>> giveawayRankMap = giveaways.stream()
                    .collect(Collectors.groupingBy(Giveaway::getRankParam));
            extendParams.computeIfAbsent(GIVEAWAY_RULES.getCode(), k -> giveawayRankMap.keySet().stream()
                    .map(rankParam -> {
                        ActivityEntity.GiveawayRule defaultRule = new ActivityEntity.GiveawayRule();
                        defaultRule.setGiveawayMethod(GiveawayMethodEnum.ALL.getCode());
                        defaultRule.setGiveawayChooseQty(giveaways.size());
                        defaultRule.setRankParam(rankParam);
                        defaultRule.setGiveawaySortRule(GiveawaySortRuleEnum.ORDERLY.getCode());
                        return defaultRule;
                    }).collect(Collectors.toList()));

            List<ActivityEntity.GiveawayRule> giveawayRules = BeanCopyUtils.jsonCopyList(extendParams.get(GIVEAWAY_RULES.getCode()), ActivityEntity.GiveawayRule.class);
            Map<Integer, ActivityEntity.GiveawayRule> giveawayRuleMap = giveawayRules.stream()
                    .collect(Collectors.toMap(ActivityEntity.GiveawayRule::getRankParam, a -> a, (a, b) -> a));

            for (Entry<Integer, ActivityEntity.GiveawayRule> entry : giveawayRuleMap.entrySet()) {
                List<Giveaway> giveawaysByRank = giveawayRankMap.get(entry.getKey());
                ActivityEntity.GiveawayRule giveawayRule = entry.getValue();
                // 启用赠品池
                if (GiveawayPoolEnableEnum.ENABLE.equalsCode(String.valueOf(extendParams.get(GIVEAWAY_POOL_ENABLE.getCode())))
                        // 随机赠
                        && (GiveawaySortRuleEnum.RANDOM.equalsCode(giveawayRule.getGiveawaySortRule()))) {
                    // 设置随机序号
                    giveawaysByRank.forEach(x -> x.setGiveawaySort(Integer.parseInt(RandomUtil.getRandomNumberStr(9))));
                }
            }


            ac.setGiveaways(giveaways);
            ac.setPromoProducts(getListOrEmpty(productMap.get(activityCode)));
            ac.setPeriodModel(CollectionUtils.isEmpty(periodMap.get(activityCode)) ? null : periodMap.get(activityCode).get(0));

            Map<String, List<ProductSkuDetailDTO>> productSkuDetailsMap = new HashMap<>();// NOSONAR
            List<ProductSkuDetailDTO> promoProductCombine = skuAndCombineDeal(getListOrEmpty(skuDetailMap.get(activityCode)), productSkuDetailsMap); // NOSONAR
            ac.setPromoProductDetails(productSkuDetailsMap);// NOSONAR
            ac.setPromoProductCombines(promoProductCombine);// NOSONAR
            ac.setIncentiveLimiteds(getListOrEmpty(limitedMap.get(activityCode)));

            Map<String, ActivityLanguageModel> languageMap = new HashMap<>();
            if (CollectionUtils.isNotEmpty(languagesMap.get(activityCode))) {
                for (ActivityLanguageModel al : languagesMap.get(activityCode)) {
                    languageMap.put(al.getLanguage(), al);
                }
            }
            ac.setLanguageMap(languageMap);
            activityCacheDTOS.add(ac);
        }

        return activityCacheDTOS;
    }


    public void addMissingGiveaway(ActivityCacheDTO activityCacheDTO, List<ActivityFunctionParamRankModel> rankListByActivityCodes) {
        List<Giveaway> giveawayList = activityCacheDTO.getGiveaways();
        if (CollectionUtils.isEmpty(giveawayList)) {
            return;
        }
        Boolean isTriggerDefault = false;
        Set<Integer> rankSet = new HashSet<>();
        for (Giveaway giveaway : giveawayList) {
            if (null == giveaway.getRankParam()) {
                giveaway.setRankParam(1);
                isTriggerDefault = true;
            }
            rankSet.add(giveaway.getRankParam());
        }

        if (CollectionUtils.isEmpty(rankListByActivityCodes) || Boolean.FALSE.equals(isTriggerDefault)) {
            return;
        }

        List<Giveaway> addGiveawayList = new ArrayList<>();
        for (ActivityFunctionParamRankModel activityFunctionParamRankModel : rankListByActivityCodes) {
            if (!rankSet.contains(activityFunctionParamRankModel.getRankParam())) {
                for (Giveaway giveaway : giveawayList) {
                    Giveaway giveawayCopy = BeanCopyUtils.jsonCopyBean(giveaway, Giveaway.class);
                    giveawayCopy.setRankParam(activityFunctionParamRankModel.getRankParam());
                    addGiveawayList.add(giveawayCopy);
                }
                rankSet.add(activityFunctionParamRankModel.getRankParam());
            }
        }
        giveawayList.addAll(addGiveawayList);
    }


    private void setCouponInfo(ActivityModel activity, String activityCode) {
        if (ActivityTypeEnum.COUPON.equalsCode(activity.getActivityType())) {
            // Coupon activity
            activity.setCouponInfo(couponActivityService.findCouponActivity(activity.getTenantCode(), activityCode));
        }
    }

    private void functionParam(Map<String, List<ActivityFunctionParamRankModel>> promoFuncRanks, List<FunctionParamModel> temps, String activityCode, List<FunctionParamModel> promoFuncParams) {
        for (FunctionParamModel temp : temps) {
            for (ActivityFunctionParamRankModel activityFunctionParamRankModel : getListOrEmpty(promoFuncRanks.get(activityCode))) {
                if (temp.getRankId().equals(activityFunctionParamRankModel.getId())) {
                    promoFuncParams.add(temp);
                }
            }
        }
    }

    private <T> List<T> getListOrEmpty(List<T> list) {
        return CollectionUtils.isEmpty(list) ? new ArrayList<>() : list;
    }

    /**
     * 根请求据head匹配对应语言
     */
    private void languageDeal(ActivityCacheDTO cacheDTO, String language) {

        if (StringUtils.isBlank(language) || null == cacheDTO) {
            return;
        }

        Map<String, ActivityLanguageModel> languageMap = cacheDTO.getLanguageMap();
        if (MapUtils.isEmpty(languageMap)) {
            return;
        }

        cacheDTO.getActivityModel().setLanguage(languageMap.get(language));
    }

    public ActivityCacheDTO getActivityCacheDTO(ActivityModel activity) {

        setCouponInfo(activity, activity.getActivityCode());

        //根据规则主体获取规则模板
        TemplateModel promoTemplate = templateService.getTemplateByCode(activity.getTemplateCode());
        //规则函数层级列表
        List<ActivityFunctionParamRankModel> promoFuncRanks = activityFuncRankService.getRankListByActivityCode(activity.getActivityCode());
        //规则函数参数列表
        List<FunctionParamModel> promoFuncParams = new ArrayList<>(promoFuncRanks.size() * 4);
        if (!CollectionUtils.isEmpty(promoFuncRanks)) {
            for (ActivityFunctionParamRankModel tPromoRuleFuncRankVO : promoFuncRanks) {
                List<FunctionParamModel> temps = activityFuncParamService.getRuleFuncParamListByRankId(tPromoRuleFuncRankVO.getId());
                promoFuncParams.addAll(temps);
            }
        }

        //规则函数列表
        List<TemplateFunctionModel> functionModels = FunctionHelper.queryTemplateFunctionList(promoTemplate.getTemplateCode());
        //渠道对象列表
        List<TPromoActivityStoreVO> promoChannels = tPromoStoreService.getStoresByActivityCode(activity.getActivityCode());

        //多语言列表
        List<ActivityLanguageModel> languages = activityLanguageService.queryActivityLanguages(activity.getTenantCode(), activity.getActivityCode());
        Map<String, ActivityLanguageModel> languageMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(languages)) {
            for (ActivityLanguageModel al : languages) {
                languageMap.put(al.getLanguage(), al);
            }
        }

        //SKU和套装商品列表// NOSONAR
        List<String> activityCodes = new ArrayList<>();
        activityCodes.add(activity.getActivityCode());
        List<ProductSkuDetailDTO> promoProductDetails = productDetailService.queryOneGroupByActivityCodes(activity.getTenantCode(), activityCodes); // NOSONAR
        Map<String, List<ProductSkuDetailDTO>> productSkuDetailsMap = new HashMap<>(); // NOSONAR
        List<ProductSkuDetailDTO> promoProductCombine = skuAndCombineDeal(promoProductDetails, productSkuDetailsMap);// NOSONAR

        //赠品对象列表
        List<GiveawayVO> giveawayVOS = giveawayService.getGiftListByActivityCode(activity.getTenantCode(), activity.getActivityCode());
        //商品对象列表
        List<TPromoActivityProductVO> promoProducts = productService.getPromoProduct(activity.getActivityCode());
        //奖励限制列表
        List<TPromoIncentiveLimitedVO> incentiveLimiteds = incentiveLimitedService.getLimitedListByActivityCode(activity.getActivityCode());

        //会员
        List<QualificationModel> qualificationModels = qualificationService.queryQualifications(activity.getTenantCode(), activity.getActivityCode());
        // 周期
        ActivityPeriodModel period = activityPeriodService.findPeriod(activity.getTenantCode(), activity.getActivityCode());


        ActivityCacheDTO ac = new ActivityCacheDTO();

        ac.setVersion(ActivityCacheDTO.LAST_VERSION);
        ac.setActivityModel(activity);
        ac.setPromoTemplate(promoTemplate);
        ac.setQualificationModels(qualificationModels);
        ac.setPromoFuncRanks(promoFuncRanks);
        ac.setPromoFuncParams(promoFuncParams);
        ac.setPromoTemplateFunctions(functionModels);
        ac.setPromoChannels(promoChannels);
        ac.setGiveaways(BeanCopyUtils.jsonCopyList(giveawayVOS, Giveaway.class));
        ac.setPromoProducts(promoProducts);
        ac.setPromoProductDetails(productSkuDetailsMap);// NOSONAR
        ac.setPromoProductCombines(promoProductCombine);// NOSONAR
        ac.setIncentiveLimiteds(incentiveLimiteds);
        ac.setLanguageMap(languageMap);
        ac.setPeriodModel(period);
        if (activity.getActivityType().equals(com.gtech.promotion.code.marketing.ActivityTypeEnum.GROUP.code())){
            MarketingGroupMode marketingGroupMode = marketingGroupService.findByActivityCode(activity.getActivityCode());
            ac.setMarketingGroupMode(marketingGroupMode);
        }
        if (activity.getActivityType().equals(com.gtech.promotion.code.marketing.ActivityTypeEnum.BOOST_SHARDING.code())){
            BoostSharingModel boostSharingModel = boostSharingService.findByActivityCode(activity.getActivityCode());
            ac.setBoostSharingModel(boostSharingModel);
        }

        return ac;
    }

    private List<ProductSkuDetailDTO> skuAndCombineDeal(List<ProductSkuDetailDTO> promoProductDetails, Map<String, List<ProductSkuDetailDTO>> productSkuDetailsMap) {

        List<ProductSkuDetailDTO> promoProductCombine = new ArrayList<>();
        if (!CollectionUtils.isEmpty(promoProductDetails)) {
            for (ProductSkuDetailDTO productSkuDetailDTO : promoProductDetails) {
                if (StringUtil.isNotBlank(productSkuDetailDTO.getCombineSkuCode())) {
                    promoProductCombine.add(productSkuDetailDTO);
                } else if (StringUtil.isNotBlank(productSkuDetailDTO.getProductCode())) {
                    String productCode = productSkuDetailDTO.getProductCode();
                    List<ProductSkuDetailDTO> list = new ArrayList<>();
                    if (productSkuDetailsMap.containsKey(productCode)) {
                        list = productSkuDetailsMap.get(productCode);
                        list.add(productSkuDetailDTO);
                        productSkuDetailsMap.put(productCode, list);
                    } else {
                        list.add(productSkuDetailDTO);
                        productSkuDetailsMap.put(productCode, list);
                    }
                }
            }
        }
        return promoProductCombine;
    }

    /**
     * 获得不同类型的活动
     *
     * @param tenantCode
     * @param activityType
     * @param activityStatus
     *            0-生效中状态的活动；1-所有状态的活动
     */
    private List<ActivityModel> getActivityList(String tenantCode, ActivityTypeEnum activityType, Integer activityStatus) {

        if (null != activityStatus && activityStatus.equals(1)) {
            return activityService.queryActivityByTenantCode(tenantCode, activityType, null, ActivityStatusEnum.PENDING, ActivityStatusEnum.IN_AUDIT, ActivityStatusEnum.REJECTED,
                    ActivityStatusEnum.EFFECTIVE, ActivityStatusEnum.SUSPEND);
        } else {
            return activityService.queryActivityByTenantCode(tenantCode, activityType, null, ActivityStatusEnum.EFFECTIVE);
        }
    }

    public Map<String, ActivityCacheDTO> filterBlackProduct(Map<String, ActivityCacheDTO> activityMap, ProductCodes product, Map<String, List<ProductSkuDetailDTO>> blackProductMap) {
        Map<String, ActivityCacheDTO> newCaches = new HashMap<>();
        String productCode = product.getProductCode();
        for (Entry<String, ActivityCacheDTO> e : activityMap.entrySet()) {
            ActivityCacheDTO activity = e.getValue();
            String activityCode = activity.getActivityModel().getActivityCode();
            List<ProductSkuDetailDTO> productSkuDetailDTOS = blackProductMap.get(activityCode);
            if (org.springframework.util.CollectionUtils.isEmpty(productSkuDetailDTOS)) {
                newCaches.put(e.getKey(), e.getValue());
            } else {
                Set<String> productCodeSet = productSkuDetailDTOS.stream().map(ProductSkuDetailDTO::getProductCode).collect(Collectors.toSet());
                if (!productCodeSet.contains(productCode)) {
                    newCaches.put(e.getKey(), e.getValue());
                }
            }
        }
        return newCaches;
    }

    /**
     * 根据商品信息获得对应符合条件的数据(商品过滤,时间过滤)
     * 参数请自行校验
     *
     * @param product -- Product condition.
     * @param caches 缓存
     * @return 符合条件的活动
     */
    public Map<String, ActivityCacheDTO> filterActivityByProduct(Map<String, ActivityCacheDTO> caches, ProductCodes product, List<ProductSkuDetailDTO> productSkuDetailDTOS) {

        //3.商品断言
        //3.1 品类
        List<String> categoryCodes = CollectionUtils.isEmpty(product.getCategoryCodes()) ? new ArrayList<>() : product.getCategoryCodes();//购物车某个商品的分类
        Predicate<TPromoActivityProductVO> cgPredicateAll = x -> PromotionConstants.UNLIMITED.equals(x.getCategoryCode());//正反选都适用
        Predicate<TPromoActivityProductVO> cgPredicate = x -> Arrays.asList(x.getCategoryCode().split(",")).stream()
                .anyMatch(y -> categoryCodes.stream().anyMatch(z -> z.concat(">").startsWith(y.concat(">"))));//反选.negate()
        //3.2 品牌
        Predicate<TPromoActivityProductVO> bdPredicateAll = x -> PromotionConstants.UNLIMITED.equals(x.getBrandCode());//正反选都适用
        Predicate<TPromoActivityProductVO> bdPredicate = x -> Arrays.asList(x.getBrandCode().split(",")).stream().anyMatch(y -> y.equals(product.getBrandCode()));//反选.negate()

        //3.3 正选Attribute断言：为空则匹配3个AttributeCode全是0000的活动，不为空则要大于等于活动的标签
        Predicate<TPromoActivityProductVO> attributePredicate = attributeAllPredicate().or(attributePredicate(product));
        //3.3.1 反选Attribute断言：为空则匹配3个AttributeCode全是0000的活动，不为空则要大于等于活动的标签
        Predicate<TPromoActivityProductVO> attributePredicate1 = attributeAllPredicate().or(attributeNegatePredicate(product));

        //3.4 正选商品 spuCode相等 && 库中sku数据为空||(库中sku不为空&&(库中sku数据是0000||库中sku和参数sku相等))
        Predicate<ProductSkuDetailDTO> skuPredicate = x -> x.getProductCode().equals(product.getProductCode())
                && (StringUtil.isBlank(x.getSkuCode()) || PromotionConstants.UNLIMITED.equals(x.getSkuCode()) || x.getSkuCode().equals(product.getSkuCode()));
        //3.5 套装商品
        Predicate<ProductSkuDetailDTO> combineSkuPredicate = x -> x.getCombineSkuCode().equals(product.getCombineSkuCode());

		// 3.6 商品标签
		Predicate<TPromoActivityProductVO> tagPredicateAll = x -> isUnlimited(x.getProductTag());// 正反选都适用
        List<String> tags = new ArrayList<>();
        if (StringUtil.isNotEmpty(product.getProductTag())){
            List<String> list = Arrays.asList(product.getProductTag().split(","));
            tags.addAll(list);
        }
        Predicate<TPromoActivityProductVO> tagPredicate = x -> Arrays.asList(x.getProductTag().split(",")).stream().anyMatch(y -> tags.contains(y));// 反选.negate()

        Map<String, ActivityCacheDTO> newCache = new HashMap<>();//符合的活动
        if (MapUtils.isEmpty(caches)) {
            return newCache;
        }

        Iterator<Entry<String, ActivityCacheDTO>> iterator = caches.entrySet().iterator();
        Map<String, List<ProductSkuDetailDTO>> stringListMap = new HashedMap<>();
        if (CollectionUtils.isNotEmpty(productSkuDetailDTOS)) {
            stringListMap = productSkuDetailDTOS.stream().sorted(Comparator.comparing(ProductSkuDetailDTO::getType).reversed()).filter(x -> x.getProductCode().equals(product.getProductCode())).collect(Collectors.groupingBy(ProductSkuDetailDTO::getActivityCode));
        }
        a:
        while (iterator.hasNext()) { // NOSONAR
            Entry<String, ActivityCacheDTO> next = iterator.next();
            ActivityCacheDTO activityCacheDTO = next.getValue();
            String activityCode = activityCacheDTO.getActivityModel().getActivityCode();
            //正反选商品
            Boolean selection = ProductSelectionEnum.SELECT.equalsCode(activityCacheDTO.getActivityModel().getProductSelectionType());
            Predicate<TPromoActivityProductVO> newCgPredicate = selection ? cgPredicateAll.or(cgPredicate) : cgPredicateAll.or(cgPredicate.negate());// 品类
            Predicate<TPromoActivityProductVO> newBdPredicate = selection ? bdPredicateAll.or(bdPredicate) : bdPredicateAll.or(bdPredicate.negate());// 品牌
            Predicate<TPromoActivityProductVO> newAttributePredicate = selection ? attributePredicate : attributePredicate1;//标签属性
            Predicate<ProductSkuDetailDTO> newSkuPredicate = selection ? skuPredicate : skuPredicate.negate();//标签属性
            Predicate<ProductSkuDetailDTO> newcombineSkuPredicate = selection ? combineSkuPredicate : combineSkuPredicate.negate();//标签属性
			Predicate<TPromoActivityProductVO> newTagPredicate = selection ? tagPredicateAll.or(tagPredicate) : tagPredicateAll.or(tagPredicate.negate());// 商品标签

            StringBuilder seqNumBuilder = new StringBuilder();

            List<TPromoActivityProductVO> productScopes = activityCacheDTO.getPromoProducts();
            // key：productCode value:ProductSkuDetailDTO
            List<ProductSkuDetailDTO> productDetails = stringListMap.get(activityCode); // 指定商品的活动的spuCode不能为空或者0000
            //---------------全商品 productType==00
            if (CollectionUtils.isEmpty(productScopes) && MapUtils.isEmpty(activityCacheDTO.getPromoProductDetails())) {
                newCache.put(next.getKey(), setNewCacheDTO(activityCacheDTO, seqNumBuilder, 0));
                continue a;
            }

            List<ProductSkuDetailDTO> promoProductCombines = activityCacheDTO.getPromoProductCombines();

            //-------------指定范围 productType==01
            List<TPromoActivityProductVO> products = activityCacheDTO.getPromoProducts();//这个list理论上只有一个元素
            if (!CollectionUtils.isEmpty(products) && StringUtil.isBlank(product.getCombineSkuCode())) {//如果入参套装编码有值，就不判读商品属性
                for (TPromoActivityProductVO tPromoActivityProductVO : products) {
                    //指定范围的数据对比：品类、品牌and标签属性
                    boolean categoryCodeTest = newCgPredicate.test(tPromoActivityProductVO);
                    boolean brandCodeTest = newBdPredicate.test(tPromoActivityProductVO);
                    boolean attributeTest = newAttributePredicate.test(tPromoActivityProductVO);
                    boolean productTagTest = newTagPredicate.test(tPromoActivityProductVO);
					if (categoryCodeTest && brandCodeTest && attributeTest && productTagTest) {
                        //不是多个商品范围(只要符合一个条件即可满足)
                        if (!TPromoActivityProductVO.moreThanOneSeq(products)) {
                            newCache.put(next.getKey(), setNewCacheDTO(activityCacheDTO, seqNumBuilder, tPromoActivityProductVO.getSeqNum()));
                            continue a;
                        } else {
                            //多范围(此商品池一个范围一条数据,所以不会重复)
                            seqNumBuilder.append(tPromoActivityProductVO.getSeqNum() + ",");
                        }
                    }
                }
            }

            //-------------指定商品spu、sku productType==02
            //包含该spu,说明当前spu下的数据可能符合sku
            if (!MapUtils.isEmpty(activityCacheDTO.getPromoProductDetails())) {
                int selectCount = 0;//反选计数器
                if (!selection && CollectionUtils.isEmpty(productDetails)) {
                    newCache.put(next.getKey(), setNewCacheDTO(activityCacheDTO, seqNumBuilder, 1));
                    continue a;
                }
                if (!CollectionUtils.isEmpty(productDetails)) {
                    for (ProductSkuDetailDTO productDetail : productDetails) {
                        if (selection) {
                            if (newSkuPredicate.test(productDetail)) {
                                if (null != productDetail.getType() && 2 == productDetail.getType()) {
                                    continue a;
                                }
                                // 不是多个商品范围
                                if (!ProductSkuDetailDTO.moreThanOneSeq(productDetails)) {
                                    newCache.put(next.getKey(), setNewCacheDTO(activityCacheDTO, seqNumBuilder, productDetail.getSeqNum()));
                                    continue a;
                                } else {//多个商品池
                                    //防止重复seqNum（该商品项可能符合多个商品池。1,1,2,3这种不允许）
                                    if (!("," + seqNumBuilder.toString()).contains("," + productDetail.getSeqNum() + ",")) {
                                        seqNumBuilder.append(productDetail.getSeqNum() + ",");
                                    }
                                }
                            }
                        } else {
                            if (newSkuPredicate.test(productDetail)) {
                                if (null != productDetail.getType() && 2 == productDetail.getType()) {
                                    continue a;
                                }
                                selectCount++;
                            } else {
                                // 如果包含任意一个商品，反选判断失败，进入下一个活动
                                continue a;
                            }
                        }
                    }
                    if (productDetails.size() == selectCount) {
                        newCache.put(next.getKey(), setNewCacheDTO(activityCacheDTO, seqNumBuilder, 1));
                        continue a;
                    }
                }
            }

            if (!CollectionUtils.isEmpty(promoProductCombines)) {//套装判断
                int selectReverseCount = 0;//反选计数器
                for (ProductSkuDetailDTO combinSkuCode : promoProductCombines) {
                    if (selection) {
                        if (newcombineSkuPredicate.test(combinSkuCode)) {
                            //不是多个商品范围
                            if (!ProductSkuDetailDTO.moreThanOneSeq(promoProductCombines)) {
                                newCache.put(next.getKey(), setNewCacheDTO(activityCacheDTO, seqNumBuilder, combinSkuCode.getSeqNum()));
                                continue a;
                            } else {//多个商品池
                                //防止重复seqNum（该商品项可能符合多个商品池。1,1,2,3这种不允许）
                                if (!("," + seqNumBuilder.toString()).contains("," + combinSkuCode.getSeqNum() + ",")) {
                                    seqNumBuilder.append(combinSkuCode.getSeqNum() + ",");
                                }
                            }
                        }
                    } else {
                        if (newcombineSkuPredicate.test(combinSkuCode)) {
                            selectReverseCount++;
                        } else {
                            // 如果包含任意一个商品，反选判断失败，进入下一个活动
                            continue a;
                        }
                    }
                }
                if (promoProductCombines.size() == selectReverseCount) {
                    //反选没有捆绑等多商品池活动
                    newCache.put(next.getKey(), setNewCacheDTO(activityCacheDTO, seqNumBuilder, 1));
                    continue a;
                }

            }

            //指定多个商品池  productType==03
            if (!StringUtils.isEmpty(seqNumBuilder)) {//不为空说明该活动有池匹配上了
                newCache.put(next.getKey(), setNewCacheDTO(activityCacheDTO, seqNumBuilder, -1));
            }
        }
        return newCache;
    }

    public ActivityCacheDTO setNewCacheDTO(ActivityCacheDTO activityCacheDTO, StringBuilder seqNumBuilder, int seqNum) {

        if (seqNum == -1) {
            activityCacheDTO.setSeqNum(seqNumBuilder.substring(0, seqNumBuilder.lastIndexOf(",")));//去掉最后一个","
        } else {
            seqNumBuilder.append(seqNum);
            activityCacheDTO.setSeqNum(seqNumBuilder.toString());
        }
        ActivityCacheDTO cacheDTO = new ActivityCacheDTO();
        BeanUtils.copyProperties(activityCacheDTO, cacheDTO);
        return cacheDTO;
    }

    public Predicate<TPromoActivityProductVO> attributeNegatePredicate(ProductCodes product) {

        return x -> {
            if (CollectionUtils.isEmpty(product.getAttributes())) {
                return false;
            }
            for (ProductAttribute y : product.getAttributes()) {
				if (!CollectionUtils.isEmpty(x.getAttributes())) {
					for (ProductAttribute xx : x.getAttributes()) {
						if (attributeCheckDetailNegate(y, xx.getAttributeCode(), xx.getAttributeValues())) {
							return false;
						}
					}
				}
				if (!CollectionUtils.isEmpty(x.getSpuAttributes())) {
					for (ProductAttribute xx : x.getSpuAttributes()) {
						if (attributeCheckDetailNegate(y, xx.getAttributeCode(), xx.getAttributeValues())) {
							return false;
						}
					}
				}
            }
            return true;
        };
    }

    private boolean attributeCheckDetailNegate(ProductAttribute y, String attributeCode, String attributeValue) {

        return !PromotionConstants.UNLIMITED.equals(attributeCode) && attributeCheckDetail(y, attributeCode, attributeValue);
    }

    public Predicate<TPromoActivityProductVO> attributePredicate(ProductCodes product) {

        return x -> {
            // 如果没有设置属性要求，则视为匹配
            if (CollectionUtils.isEmpty(x.getAttributes()) && CollectionUtils.isEmpty(x.getSpuAttributes())) {
                return true;
            }
            // 检查SKU属性
            boolean skuFlag = true;
            if (!CollectionUtils.isEmpty(x.getAttributes())) {
                // 如果商品没有SKU属性但规则要求有，则不匹配
                if (CollectionUtils.isEmpty(product.getAttributes())) {
                    return false;
                }

                // 确保所有属性都匹配
                skuFlag = x.getAttributes().stream()
                        .allMatch(xx -> checkAttributeList(product.getAttributes(), xx));
            }
            // 检查SPU属性
            boolean spuFlag = true;
            if (!CollectionUtils.isEmpty(x.getSpuAttributes())) {
                // 如果商品没有SPU属性但规则要求有，则不匹配
                if (CollectionUtils.isEmpty(product.getSpuAttributes())) {
                    return false;
                }

                // 使用allMatch确保所有属性都匹配
                spuFlag = x.getSpuAttributes().stream()
                        .allMatch(xx -> checkAttributeList(product.getSpuAttributes(), xx));
            }
            return skuFlag && spuFlag;
        };
    }

	private boolean checkAttributeList(List<ProductAttribute> attributes, ProductAttribute xx) {
		for (ProductAttribute y : attributes) {
			if (attributeCheckDetail(y, xx.getAttributeCode(), xx.getAttributeValues())) {
				return true;
			}
		}
		return false;
	}


    private boolean attributeCheckDetail(ProductAttribute y, String attributeCode, String attributeValue) {

        return attributeCode.equals(y.getAttributeCode()) && (PromotionConstants.UNLIMITED.equals(attributeValue)
                || ("," + attributeValue + ",").contains("," + y.getAttributeValues() + ","));
    }

    /**
     * 所有属性都不限的检验
     */
    public Predicate<TPromoActivityProductVO> attributeAllPredicate() {

        return x -> {
			if (CollectionUtils.isEmpty(x.getAttributes()) && CollectionUtils.isEmpty(x.getSpuAttributes())) {
                return true;
            }
			if (!CollectionUtils.isEmpty(x.getAttributes())) {
				for (ProductAttribute xx : x.getAttributes()) {
					if (!this.isUnlimited(xx.getAttributeCode())) {
						return false;
					}
				}
			}
			if (!CollectionUtils.isEmpty(x.getSpuAttributes())) {
				for (ProductAttribute xx : x.getSpuAttributes()) {
					if (!this.isUnlimited(xx.getAttributeCode())) {
						return false;
					}
				}
			}
            return true;
        };
    }

    public boolean isUnlimited(String code) {

        return StringUtils.isBlank(code) || PromotionConstants.UNLIMITED.equals(code);
    }

    public Map<String, ActivityCacheDTO> queryActivityByCustomCondition(Map<String, ActivityCacheDTO> activityMap, Map<String, String> customMap) {

        if (MapUtils.isEmpty(activityMap)) {
            return activityMap;
        }
        if (MapUtils.isNotEmpty(customMap)){
            Map<String, ActivityCacheDTO> newCaches = new HashMap<>();
            for (Entry<String, ActivityCacheDTO> e : activityMap.entrySet()) {
                ActivityModel activity = e.getValue().getActivityModel();
                String customCondition = activity.getCustomCondition();
				JSONArray jsonArray = null;
				if (StringUtil.isNotEmpty(customCondition)) {
					jsonArray = JSONArray.parseArray(customCondition);
				}
				if (CollectionUtils.isEmpty(jsonArray)) {
                    continue;
                }
				List<CustomCondition> customConditions = convertCustomCondition(jsonArray);

                Set<Entry<String, String>> entries = customMap.entrySet();

                //记录匹配次数
                checkCustomCondition(customMap, newCaches, e, customConditions, entries);
            }
            return newCaches;
        }else {
            return activityMap;
        }
    }

    public Map<String, ActivityCacheDTO> filterActivityByCustomCondition(String tenantCode, Map<String, ActivityCacheDTO> activityMap, Map<String, String> customMap) {

        if (MapUtils.isEmpty(activityMap)) {
            return activityMap;
        }
        //默认开启 1为开启强制验证
        JsonResult<String> masterResult = masterDataFeignClient.getValueValue(tenantCode, TENANT_CUSTOM_CONDITION);

        if (null != masterResult && StringUtils.isNotBlank(masterResult.getData()) && !TurnOnAndOffEnum.TURN_ON.code().equals(masterResult.getData())) {
            return activityMap;
        }
        Map<String, ActivityCacheDTO> newCaches = new HashMap<>();

        for (Entry<String, ActivityCacheDTO> e : activityMap.entrySet()) {
            ActivityModel activity = e.getValue().getActivityModel();
			JSONArray jsonArray = null;
            String customCondition = activity.getCustomCondition();
			if (StringUtil.isNotEmpty(customCondition)) {
				jsonArray = JSONArray.parseArray(customCondition);
			}
			if (CollectionUtils.isEmpty(jsonArray)) {
                newCaches.put(e.getKey(), e.getValue());
            }
			if (!CollectionUtils.isEmpty(jsonArray) && MapUtils.isNotEmpty(customMap)) {
                //自定义条件必须完全符合
				List<CustomCondition> customConditions = convertCustomCondition(jsonArray);
                Set<Entry<String, String>> entries = customMap.entrySet();
                //记录匹配次数
                checkCustomCondition(customMap, newCaches, e, customConditions, entries);
            }

        }
        return newCaches;
    }

    public void checkCustomCondition(Map<String, String> customMap, Map<String, ActivityCacheDTO> newCaches, Entry<String, ActivityCacheDTO> e, List<CustomCondition> customConditions, Set<Entry<String, String>> entries) {
        int count = 0;
        for (Entry<String, String> entry : entries) {
            String key = entry.getKey();
            String value = entry.getValue();
            try {
                List<String> list = JSONArray.parseArray(value).toJavaList(String.class);
                for (CustomCondition condition : customConditions) {
                    String customKey = condition.getCustomKey();
                    List<String> customValueList = condition.getCustomValueList();
                    boolean anyMatch = customValueList.stream().anyMatch(x -> list.stream().anyMatch(y -> y.equals(x)));
                    if (key.equals(customKey) && anyMatch) {
                        count++;
                        break;
                    }
                }
            }catch (Exception exception){
                log.error("custom condition format error");
                //项目数据
            }

        }

        if (count == customMap.size() && customConditions.size() == customMap.size() ) {
            newCaches.put(e.getKey(), e.getValue());
        }
    }


    //数据解析
	public List<CustomCondition> convertCustomCondition(JSONArray jsonArray) {

        List<CustomCondition> customConditions = new ArrayList<>();

        for (Object o : jsonArray) {
            CustomCondition customCondition = new CustomCondition();

            JSONObject jsonObject = JSONObject.parseObject(o.toString());
            String customKey = jsonObject.getString("customKey");
            String customValue = jsonObject.getString("customValue");

            List<String> customValueList = new ArrayList<>();

            try {
                JSONArray jsonArray1 = JSONArray.parseArray(customValue);
                for (Object o1 : jsonArray1) {
                    customValueList.add(o1.toString());
                }
            }catch (Exception e){
                //解析数组失败，则为单个值
                customValueList.add(customValue);
            }

            customCondition.setCustomKey(customKey);
            customCondition.setCustomValue(customValue);
            customCondition.setCustomValueList(customValueList);
            customConditions.add(customCondition);
        }
        return customConditions;
    }

    public boolean checkProductCondition( List<TPromoActivityProductVO> promoProducts,MallProductConditionParam productCondition){

        if (CollectionUtils.isEmpty(promoProducts)){
            return false;
        }
        TPromoActivityProductVO productVO = promoProducts.get(0);

        //1.属性类型过滤
        String attrTypeParam = productCondition.getAttrType();
        if (StringUtils.isNotBlank(attrTypeParam)) {
            String attrType = productVO.getAttrType();
            if (!attrTypeParam.equals(attrType)) {
               return false;
            }
        }

        //2.分类过滤
        List<String> categoryCodes = productCondition.getCategoryCodes();
        if (CollectionUtils.isNotEmpty(categoryCodes)) {
            String categoryCode = productVO.getCategoryCode();
            return isAddFlag(categoryCodes, categoryCode);
        }

        //3.品牌过滤
        List<String> brandCodes = productCondition.getBrandCodes();
        if (CollectionUtils.isNotEmpty(brandCodes)) {
            String brandCode = productVO.getBrandCode();
            return isAddFlag(brandCodes, brandCode);
        }

        //4.属性code以及属性值判断
        List<ProductAttribute> attributeParamList = productCondition.getAttributes();
        if (CollectionUtils.isNotEmpty(attributeParamList)) {
            List<ProductAttribute> attributes = productVO.getAttributes();
            return checkAttributes(attributes, attributeParamList);
        }

        return true;
    }


    public boolean getProductBlackListBoolean(List<String> productBlackList,String activityCode){
        if (CollectionUtils.isNotEmpty(productBlackList)) {
            List<ProductDetail> productDetails = productDetailService.getProductSpuSkus(activityCode);
            List<String> productCodes = productDetails.stream().filter(x -> x.getType().equals(Integer.valueOf(SeqNumEnum.B.code()))).map(ProductDetail::getProductCode).collect(Collectors.toList());
            for (String productCode : productCodes) {
                if (productBlackList.stream().anyMatch(y -> y.equals(productCode))) {
                    return false;
                }
            }
        }
        return true;
    }

    public Map<String, ActivityCacheDTO> filterMallActivityByProduct(Map<String, ActivityCacheDTO> activityMap, MallProductConditionParam productCondition, String conditionProductType) {
        Map<String, ActivityCacheDTO> newCaches = new HashMap<>();
        for (Entry<String, ActivityCacheDTO> e : activityMap.entrySet()) {
            String key = e.getKey();
            //商品黑名单
            List<String> productBlackList = productCondition.getProductBlackList();
            boolean productBlackListBoolean = getProductBlackListBoolean(productBlackList, key);
            if (!productBlackListBoolean) {
                continue;
            }
            //根据商品条件
            if (ProductTypeEnum.CUSTOM_RANGE.code().equals(conditionProductType)) {
                List<TPromoActivityProductVO> promoProducts = e.getValue().getPromoProducts();
                boolean b = checkProductCondition(promoProducts, productCondition);
                if (!b) {
                    continue;
                }
                newCaches.put(e.getKey(), e.getValue());
            } else {
                String activityCode = e.getKey();
                List<String> productCodeList = productCondition.getProductCode();
                List<String> skuCodeList = productCondition.getSkuCodes();
                //商品或sku
                List<ProductDetail> productDetails = productDetailService.getProductSpuSkus(activityCode);
                boolean b = checkProductOrSku(productCodeList, productDetails, skuCodeList);
                if (b) {
                    newCaches.put(e.getKey(), e.getValue());
                }
            }
        }
        return newCaches;
    }

    public boolean checkProductOrSku(List<String> productCodeList,List<ProductDetail> productDetails,List<String> skuCodeList){
        if (CollectionUtils.isNotEmpty(productCodeList)) {
            List<String> productCodes = productDetails.stream().filter(x -> x.getType() == 1).map(ProductDetail::getProductCode).collect(Collectors.toList());
            return checkProductOrSku1(productCodes, productCodeList);
        }
        if (CollectionUtils.isNotEmpty(skuCodeList)) {
            List<String> skuCodes = productDetails.stream().filter(x -> x.getType() == 1).map(ProductDetail::getSkuCode).collect(Collectors.toList());
            return checkProductOrSku1(skuCodes, skuCodeList);
        }
        return true;
    }

    public boolean checkProductOrSku1(List<String> skuCodes,List<String> skuCodeList){

        if (CollectionUtils.isEmpty(skuCodes)){
            return false;
        }
        for (String productCode : skuCodes) {
            if (skuCodeList.stream().noneMatch(y -> y.equals(productCode))) {
                return false;
            }
        }
        return true;
    }

    public boolean checkAttributes(List<ProductAttribute> attributes, List<ProductAttribute> attributeParamList){

        if (CollectionUtils.isEmpty(attributes)){
         return false;
        }
        result:for (ProductAttribute attribute : attributes) {
            //1.先判断属性code是否存在
            String attributeCode = attribute.getAttributeCode();
            if (attributeParamList.stream().noneMatch(y -> y.getAttributeCode().equals(attributeCode))) {
              return false;
            }
            //2.code存在判断属性值是否有符合的
            for (ProductAttribute productAttribute : attributeParamList) {
                if (attributeCode.equals(productAttribute.getAttributeCode())) {
                    //活动对应的属性
                    String attributeValues1 = attribute.getAttributeValues();
                    List<String> list = getStringSplit(attributeValues1);
                    //入参对应的属性
                    String attributeValues = productAttribute.getAttributeValues();
                    List<String> paramList = getStringSplit(attributeValues);
                    for (String s : list) {
                        if (paramList.stream().noneMatch(y -> y.equals(s))) {
                            return false;
                        }
                    }
                }
            }
        }
        return true;
    }

    public boolean isAddFlag(List<String> brandCodes, String brandCode) {
        if (!PRODUCT_RELATION_ALL.equals(brandCode)) {
            List<String> list = getStringSplit(brandCode);
            for (String s : brandCodes) {
                if (list.stream().noneMatch(y -> y.equals(s))) {
                    return false;
                }
            }
        }
        return true;
    }

    private List<String> getStringSplit(String strSplit) {
        String[] split = strSplit.split(",");
        return Arrays.asList(split);
    }

    public Map<String, ActivityCacheDTO> filterNoProductListPrice(Map<String, ActivityCacheDTO> activityMap, BigDecimal productListPrice) {
        Map<String, ActivityCacheDTO> newCaches = new HashMap<>(activityMap);
        for (Entry<String, ActivityCacheDTO> e : activityMap.entrySet()) {
            ActivityCacheDTO activity = e.getValue();
            ActivityModel activityModel = activity.getActivityModel();
            if (StringUtil.isNotEmpty(activityModel.getPriceCondition())
                    && activityModel.getPriceCondition().equals(PriceConditionEnum.LIST_PRICE.code())
                    && null == productListPrice) {
                newCaches.remove(e.getKey());
            }
        }
        return newCaches;
    }


    /**
     * 检查优先购买权活动
     */
    public Map<String, ActivityCacheDTO> filterNoRightOfFirstRefusal(Map<String, ActivityCacheDTO> activityMap, ProductCodes productCodes, ShoppingCartDTO shoppingCart) {
        Map<String, ActivityCacheDTO> newCaches = new HashMap<>(activityMap);
        for (Entry<String, ActivityCacheDTO> e : activityMap.entrySet()) {
            ActivityCacheDTO activity = e.getValue();
            ActivityModel activityModel = activity.getActivityModel();
            //如果活动是分享助力活动，且是优先购买权类型，则需要校验优先购买权
            if (com.gtech.promotion.code.marketing.ActivityTypeEnum.BOOST_SHARDING.code().equals(activityModel.getActivityType())
            && activity.getBoostSharingModel().getBoostSharingType().equals(BoostSharingTypeEnum.RIGHT_FIRST.code())) {
                WriteOffOfPreEmptiveRightsDto dto = new WriteOffOfPreEmptiveRightsDto();
                dto.setActivityCode(activityModel.getActivityCode());
                dto.setDomainCode(shoppingCart.getDomainCode());
                dto.setMemberCode(shoppingCart.getUserCode());
                dto.setTenantCode(shoppingCart.getTenantCode());
                dto.setRightOfFirstRefusalProductCode(productCodes.getProductCode());
                List<RightOfFirstRefusalEntity> rightOfFirstRefusalEntities = rightOfFirstRefusalService.queryRightOfFirstRefusalByMember(dto);
                Check.check(CollectionUtils.isEmpty(rightOfFirstRefusalEntities), TPromoProductChecker.NO_RIGHT_OF_FIRST_REFUSAL);
            }
        }
        return newCaches;
    }

	public void compensationCouponUseLimit() {
		List<ActivityEntity> activityList = activityService.queryActivityLimitTenant();
		if (CollectionUtils.isEmpty(activityList)) {
			return;
		}
		activityList.stream().collect(Collectors.groupingBy(ActivityEntity::getTenantCode)).forEach((tenantCode, list) -> {
			List<String> activityCodes = list.stream().map(ActivityEntity::getActivityCode).collect(Collectors.toList());
			Map<String, BigDecimal> limitMap = incentiveLimitedService.getLimitedByActivityCodes(tenantCode, activityCodes, "01");
			limitMap.forEach((activityCode, limitValue) -> {
				String key = Constants.APP_KEY + ":INC_LIM01:TCODE=" + tenantCode + ":ACODE=" + activityCode;
				try {
					String value = redisTemplate.opsForValue().get(key);
					if (!StringUtil.isEmpty(value)) {
						int cacheLimit = Integer.parseInt(value);
						int dbCount = couponCodeUserService.queryCouponCountByCodeAndStataus(tenantCode, activityCode,
								Arrays.asList(CouponStatusEnum.USED.code(), CouponStatusEnum.LOCKED.code()));
						int realCount = limitValue.intValue() - dbCount;
						if (cacheLimit != realCount) {
							log.info("compensationCouponUseLimit coupon redis key :{}, cacheLimit:{}, realCount:{}", key, cacheLimit, realCount);
							redisTemplate.opsForValue().set(key, realCount + "");
						}
					}
				} catch (Exception e) {
					log.error("key check error:{}", key);
					log.error(e.getMessage(), e);
				}
			});
		});
	}




}
