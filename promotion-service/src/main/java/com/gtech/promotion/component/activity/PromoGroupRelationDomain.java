package com.gtech.promotion.component.activity;

import com.alibaba.fastjson.JSON;
import com.gtech.commons.redis.GTechRedisTemplate;
import com.gtech.commons.utils.BeanCopyUtils;
import com.gtech.commons.utils.CheckUtils;
import com.gtech.promotion.code.PromotionConstants;
import com.gtech.promotion.code.activity.ActivityTypeEnum;
import com.gtech.promotion.code.activity.GroupRelationEnum;
import com.gtech.promotion.dao.model.activity.ActivityGroupRelationVO;
import com.gtech.promotion.dao.model.activity.GroupRelationVO;
import com.gtech.promotion.dao.model.activity.PromoGroupVO;
import com.gtech.promotion.dto.cache.ActivityCacheDTO;
import com.gtech.promotion.exception.ErrorCodes;
import com.gtech.promotion.service.activity.PromotionGroupRelationService;
import com.gtech.promotion.service.activity.PromotionGroupService;
import com.gtech.promotion.utils.EasyCacheUtil;
import com.gtech.promotion.vo.param.activity.ActivityGroupRelationQueryParam;
import com.gtech.promotion.vo.param.activity.GroupSettingRelationParam;
import com.gtech.promotion.vo.result.activity.ActivityGroupCache;
import com.gtech.promotion.vo.result.activity.ActivityGroupExclusionResult;
import com.gtech.promotion.vo.result.activity.ActivityGroupRelationResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import tk.mybatis.mapper.util.StringUtil;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description :
 * <AUTHOR> gaojie.li
 * @Date : 2023/7/12 10:55
 */
@Component
public class PromoGroupRelationDomain {


    @Autowired
    private PromotionGroupRelationService promotionGroupRelationService;

    @Autowired
    private PromotionGroupService promotionGroupService;

    @Autowired
    private GTechRedisTemplate redisTemplate;

    public static final String GROUP_KEY_PRE = "group:relation:";


    public List<ActivityGroupCache> getTenantGroupCache(String tenantCode) {
        String key = GROUP_KEY_PRE + tenantCode;
        String groupCacheStr;

        groupCacheStr = (String) EasyCacheUtil.get(key);
        if (StringUtil.isNotEmpty(groupCacheStr)) {
            // 取缓存
            return JSON.parseArray(groupCacheStr, ActivityGroupCache.class);
        }


        groupCacheStr = redisTemplate.opsValueGet(PromotionConstants.APP_KEY, key, String.class);
        if (!StringUtil.isEmpty(groupCacheStr)) {
            // 设置缓存（压测）
            EasyCacheUtil.set(key, groupCacheStr, 5L);
            return JSON.parseArray(groupCacheStr, ActivityGroupCache.class);
        }
        return refreshGroupCache(tenantCode);
    }

    public List<ActivityGroupCache> refreshGroupCache(String tenantCode) {
        List<ActivityGroupCache> list = new ArrayList<>();
        ActivityGroupRelationQueryParam param = new ActivityGroupRelationQueryParam();
        param.setTenantCode(tenantCode);
        List<ActivityGroupRelationResult> groupListCache = queryAllActivityGroupRelation(param);
        if (!CollectionUtils.isEmpty(groupListCache)) {
            for (ActivityGroupRelationResult activityGroupRelationResult : groupListCache) {
                ActivityGroupCache activityGroupCache = new ActivityGroupCache();
                activityGroupCache.setGroupCode(activityGroupRelationResult.getGroupCode());
                activityGroupCache.setPriority(activityGroupRelationResult.getPriority());
                List<ActivityGroupExclusionResult> relations = activityGroupRelationResult.getRelations();
                if (!CollectionUtils.isEmpty(relations)){
                    List<String> relationList = relations.stream().map(ActivityGroupExclusionResult::getGroupCode)
                            .collect(Collectors.toList());
                    activityGroupCache.setRelationList(relationList);
                }
                list.add(activityGroupCache);
            }
        }
        redisTemplate.opsValueSet(PromotionConstants.APP_KEY, GROUP_KEY_PRE + tenantCode, JSON.toJSONString(list));
        return list;
    }

    public List<ActivityGroupRelationResult> queryAllActivityGroupRelation(ActivityGroupRelationQueryParam param){

        List<ActivityGroupRelationResult> relationResults = new ArrayList<>();
        List<PromoGroupVO> promoGroupVOS = promotionGroupService.listActivityGroupByTenantCode(param.getTenantCode());
        Collections.reverse(promoGroupVOS);

        //分组编码A
        List<String> collect = promoGroupVOS.stream().map(PromoGroupVO::getGroupCode).collect(Collectors.toList());
        List<ActivityGroupRelationVO> activityGroupRelationVOS = promotionGroupRelationService.queryGroupRelationByGroupCodeA(param.getTenantCode(), collect);

        //分组编码A对应的互斥分组
        Map<String, List<ActivityGroupRelationVO>> collect1 = activityGroupRelationVOS.stream().collect(Collectors.groupingBy(ActivityGroupRelationVO::getGroupCodeA));

        for (PromoGroupVO group : promoGroupVOS){

            ActivityGroupRelationResult activityGroupRelationResult = BeanCopyUtils.jsonCopyBean(group, ActivityGroupRelationResult.class);

            //分组编码A对应的互斥分组
            List<ActivityGroupRelationVO> voList = collect1.get(group.getGroupCode());
            if (!CollectionUtils.isEmpty(voList)){
                List<ActivityGroupExclusionResult> relations = new ArrayList<>();
                for (ActivityGroupRelationVO relationVO : voList) {
                    ActivityGroupExclusionResult groupRelation = new ActivityGroupExclusionResult();
                    groupRelation.setGroupCode(relationVO.getGroupCodeB());
                    relations.add(groupRelation);
                }
                activityGroupRelationResult.setRelations(relations);
            }
            relationResults.add(activityGroupRelationResult);
        }
        return relationResults;
    }

    @Transactional
    public int settingActivityGroupRelation(GroupSettingRelationParam relationParam){


        String tenantCode = relationParam.getTenantCode();
        String domainCode = relationParam.getDomainCode();

        List<GroupRelationVO> relations = BeanCopyUtils.jsonCopyList(relationParam.getRelations(),GroupRelationVO.class);

        List<PromoGroupVO> promoGroupVOS = promotionGroupService.listActivityGroupByTenantCode(tenantCode);

        List<String> groupCodes = new ArrayList<>();
        for (PromoGroupVO group : promoGroupVOS){
            groupCodes.add(String.valueOf(group.getGroupCode()));
        }
        int row = 0;
        for (GroupRelationVO groupRelationVO : relations){

            CheckUtils.isTrue(groupCodes.contains(groupRelationVO.getGroupCodeA()), ErrorCodes.PARAM_EMPTY, groupRelationVO.getGroupCodeA());
            CheckUtils.isTrue(groupCodes.contains(groupRelationVO.getGroupCodeB()), ErrorCodes.PARAM_EMPTY, groupRelationVO.getGroupCodeB());

            if (groupRelationVO.getRelation().equals(Integer.parseInt(GroupRelationEnum.RELATION_2.code()))){

                int i = promotionGroupRelationService.queryGroupRelationByGroupCodeAB(tenantCode, groupRelationVO.getGroupCodeA(), groupRelationVO.getGroupCodeB());
                if (i > 0){
                    continue;
                }
                row +=promotionGroupRelationService.createGroupRelation(tenantCode, domainCode, groupRelationVO);

            } else {
                row += promotionGroupRelationService.deleteGroupRelation(tenantCode, domainCode,groupRelationVO);
            }
        }

        //缓存刷新
        refreshGroupCache(tenantCode);

        return row;

    }




    public Map<String, ActivityCacheDTO> filterActivityByCoupon(Map<String, ActivityCacheDTO> filtedActivityMap, String tenantCode) {

        List<ActivityGroupCache> tenantGroupCache = getTenantGroupCache(tenantCode);

        if (CollectionUtils.isEmpty(tenantGroupCache)){
            return filtedActivityMap;
        }

        Map<String, List<String>> groupRelationList = tenantGroupCache.stream()
                .filter(x -> !CollectionUtils.isEmpty(x.getRelationList()))
                .collect(Collectors.toMap(ActivityGroupCache::getGroupCode,
                        x -> (CollectionUtils.isEmpty(x.getRelationList())) ?
                                new ArrayList<>() : x.getRelationList()));
        List<String> filterActivity = new ArrayList<>();
        //优惠券 组
        Map<String, String> couponMap = filtedActivityMap.values().stream().filter(x -> x.getActivityModel().getActivityType().equals(ActivityTypeEnum.COUPON.code())).collect(Collectors.toMap(x -> x.getActivityModel().getActivityCode(), x->x.getActivityModel().getGroupCode()));

        List<ActivityCacheDTO> activityCacheDTOS = filtedActivityMap.values().stream().filter(x -> x.getActivityModel().getActivityType().equals(ActivityTypeEnum.ACTIVITY.code())).collect(Collectors.toList());

        couponMap.forEach((k,v)->{
            List<String> relationList = groupRelationList.get(v);
            if (CollectionUtils.isEmpty(relationList)){
                return;
            }
            List<String> noActivityList = activityCacheDTOS.stream()
                    .filter(x-> relationList.contains(x.getActivityModel().getGroupCode()))
                    .map(x->x.getActivityModel().getActivityCode())
                    .collect(Collectors.toList());
            filterActivity.addAll(noActivityList);
        });

        //filtedActivityMap去除filterActivity中所有的key
        filterActivity.forEach(filtedActivityMap::remove);

        return filtedActivityMap;
    }




}
