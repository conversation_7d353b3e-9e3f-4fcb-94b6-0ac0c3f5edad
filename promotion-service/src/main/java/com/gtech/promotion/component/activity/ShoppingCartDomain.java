/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.component.activity;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Sets;
import com.gtech.commons.utils.CheckUtils;
import com.gtech.commons.utils.StringUtil;
import com.gtech.promotion.calc.TemplateEnum;
import com.gtech.promotion.callable.ErrorCouponAndReason;
import com.gtech.promotion.checker.coupon.CouponActivityChecker;
import com.gtech.promotion.checker.coupon.CouponErrorChecker;
import com.gtech.promotion.code.PromotionConstants;
import com.gtech.promotion.code.activity.ActivityTypeEnum;
import com.gtech.promotion.code.activity.FuncTypeEnum;
import com.gtech.promotion.code.activity.SettingTypeEnum;
import com.gtech.promotion.component.boostsharing.BoostSharingComponent;
import com.gtech.promotion.component.coupon.CouponCodeUserComponent;
import com.gtech.promotion.dao.model.activity.ActivityModel;
import com.gtech.promotion.dao.model.coupon.PromoPassVO;
import com.gtech.promotion.dao.model.coupon.TPromoCouponInnerCodeVO;
import com.gtech.promotion.dto.cache.ActivityCacheDTO;
import com.gtech.promotion.dto.cache.ProductSkuDetailDTO;
import com.gtech.promotion.dto.in.activity.ShoppingCartActivity;
import com.gtech.promotion.dto.in.activity.ShoppingCartDTO;
import com.gtech.promotion.dto.in.activity.ShoppingCartItem;
import com.gtech.promotion.exception.Check;
import com.gtech.promotion.exception.ErrorCodes;
import com.gtech.promotion.exception.PromotionException;
import com.gtech.promotion.helper.ActivityCodeComparator;
import com.gtech.promotion.helper.ActivityRedisHelpler;
import com.gtech.promotion.helper.TemplateHelper;
import com.gtech.promotion.service.activity.ActivityProductDetailService;
import com.gtech.promotion.service.activity.ActivityService;
import com.gtech.promotion.service.activity.TPromoActivityExpressionService;
import com.gtech.promotion.service.coupon.PromoCouponInnerCodeService;
import com.gtech.promotion.utils.ActivityFilterUtil;
import com.gtech.promotion.utils.EasyCacheUtil;
import com.gtech.promotion.utils.ExpressionHelper;
import com.gtech.promotion.vo.bean.ProductCodes;
import com.gtech.promotion.vo.result.activity.ActivityGroupCache;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.collections4.map.HashedMap;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.Map.Entry;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
public class ShoppingCartDomain {

	public static final int INT = -999;
	@Autowired
	private TPromoActivityExpressionService expressionService;

	@Autowired
	private CouponCodeUserComponent couponCodeUserComponent;

	@Autowired
	private ActivityRedisHelpler redisService;

	@Autowired
	private ActivityCacheDomain activityCacheDomain;

	@Autowired
	private TemplateHelper templateHelper;
	@Autowired
	private ActivityProductDetailService productDetailService;

	@Autowired
	private PromoCouponInnerCodeService couponInnerCodeService;

	@Autowired
	private ActivityService activityService;

	@Autowired
	private PromoGroupRelationDomain promoGroupRelationDomain;


	@Autowired
	private BoostSharingComponent boostSharingComponent;

	@Value("#{'${promotion.group.black.list:}'.split(',')}")
	private List<String> groupBlackList;

	/**
	 * 根据购物车商品查询商品对应所有活动
	 *
	 * @param shoppingCart
	 * @param activityCacheMap
	 * @return
	 */
	public ShoppingCartDTO queryActivityByShoppingCartProduct(ShoppingCartDTO shoppingCart,
															  Map<String, ActivityCacheDTO> activityCacheMap) {

		if (MapUtils.isEmpty(activityCacheMap) || CollectionUtils.isEmpty(shoppingCart.getPromoProducts())) {
			return shoppingCart;
		}
		// 去除不符合渠道和会员的活动
		Map<String, ActivityCacheDTO> filtedActivityMap = ActivityFilterUtil.filterActivityByOrgCodes(activityCacheMap,
				shoppingCart.getOrgCodes());
		filtedActivityMap = ActivityFilterUtil.filterActivityByQualifications(filtedActivityMap,
				shoppingCart.getQualifications());
		filtedActivityMap = ActivityFilterUtil.filterActivityByTime(filtedActivityMap, shoppingCart.getPromotionTime());
		if (MapUtils.isEmpty(filtedActivityMap)) {
			return shoppingCart;
		}
		// 存放活动id=模板编码的map
		Map<String, ActivityCodeComparator> activityCodeComparatorMap = new HashMap<>();
		Map<String, ActivityCacheDTO> usedActivityMap = new HashMap<>();
		Map<Object, Object> sortRules = redisService.getActivitySetting(shoppingCart.getTenantCode(),
				SettingTypeEnum.PRICE_SORT.code());

		// 遍历购物车所有sku，绑定活动
		List<String> productCodes = new ArrayList<>();
		for (ShoppingCartItem shoppingCartItem : shoppingCart.getPromoProducts()) {
			productCodes.add(shoppingCartItem.getProductCode());
		}
		List<ProductSkuDetailDTO> productSkuDetailDTOS = productDetailService
				.queryListByActivityCodesAndProductCodes(filtedActivityMap.keySet(), productCodes);

//		Map<String, List<ProductSkuDetailDTO>> blackProductMap = productSkuDetailDTOS.stream()
//				.filter(p -> 2 == p.getType()).collect(Collectors.groupingBy(ProductSkuDetailDTO::getActivityCode))
		for (ShoppingCartItem shoppingCartItem : shoppingCart.getPromoProducts()) {
			ProductCodes productDTO = new ProductCodes();
			productDTO.setCombineSkuCode(shoppingCartItem.getCombineSkuCode());
			productDTO.setAttributes(shoppingCartItem.getAttributes());
			productDTO.setSpuAttributes(shoppingCartItem.getSpuAttributes());
			productDTO.setCategoryCodes(shoppingCartItem.getCategoryCodes());
			productDTO.setSpuAttributes(shoppingCartItem.getSpuAttributes());
			productDTO.setBrandCode(shoppingCartItem.getBrandCode());
			String productCode = shoppingCartItem.getProductCode();
			String skuCode = shoppingCartItem.getSkuCode();
			productDTO.setProductCode(productCode);
			productDTO.setSkuCode(skuCode);
			// 根据商铺和商品条件过滤活动
			List<String> orgCodes = new ArrayList<>();
			orgCodes.add(shoppingCartItem.getOrgCode());

			Map<String, BigDecimal> activity0411Price = new HashedMap<>();
			// 过滤
			Map<String, ActivityCacheDTO> itemFilteredActivityMap = ActivityFilterUtil
					.filterActivityByOrgCodes(filtedActivityMap, orgCodes);

			// 商品校验
			itemFilteredActivityMap = activityCacheDomain.filterActivityByProduct(itemFilteredActivityMap, productDTO,
					productSkuDetailDTOS);
			TreeMap<ActivityCodeComparator, ShoppingCartActivity> usedActivities = new TreeMap<>(
					Comparator.reverseOrder());
			// 封装活动信息到商品中
			for (Entry<String, ActivityCacheDTO> entry : itemFilteredActivityMap.entrySet()) {
				ActivityCacheDTO activityCacheDTO = entry.getValue();
				ActivityModel activityModel = activityCacheDTO.getActivityModel();
				ActivityCodeComparator comparator = ActivityCodeComparator.builder()
						.templateCode(activityModel.getTemplateCode()).activityCode(activityModel.getActivityCode())
						.activityType(activityModel.getActivityType()).priority(activityModel.getPriority())
						.highPrioriy(shoppingCart.isHighPrioriyActivity(activityModel.getActivityCode())).build();
				activityCodeComparatorMap.put(activityModel.getActivityCode(), comparator);
				deal0411(productCode, skuCode, activity0411Price, activityCacheDTO, activityModel.getActivityCode(), shoppingCartItem);
				String sortRule = (String) sortRules.get(activityCacheDTO.getPromoTemplate().getTagCode());
				usedActivityMap.put(entry.getKey(), entry.getValue());
				usedActivities.put(comparator,
						new ShoppingCartActivity(activityModel, shoppingCart.getUserCode(), null,
								activityCacheDTO.getSeqNum(), sortRule,
								templateHelper.templateCode2TagCode(activityModel.getTemplateCode()), comparator));
			}
			shoppingCartItem.setUsedActivitys(new ArrayList<>(usedActivities.values()));
			shoppingCartItem.setActivity0411Price(activity0411Price);
		}
		activityCacheMap.clear();
		activityCacheMap.putAll(usedActivityMap);
		this.filterABActivity(shoppingCart.getPromoProducts());
		expressionCheck(shoppingCart, activityCodeComparatorMap);
		return shoppingCart;
	}


	/**
	 * 根据购物车查询活动,返回添加活动后的购物车
	 *
	 * @param shoppingCart 购物车
	 * @return 购物车
	 * cacheSwitch 是否开启缓存 true 开启 false 关闭
	 */
	public ShoppingCartDTO queryActivity(ShoppingCartDTO shoppingCart, Map<String, ActivityCacheDTO> activityCacheMap,boolean cacheSwitch) {
		if (MapUtils.isEmpty(activityCacheMap) || CollectionUtils.isEmpty(shoppingCart.getPromoProducts())) {
			Check.check(StringUtil.isNotBlank(shoppingCart.getCouponCodes()), CouponErrorChecker.NO_EFFECTIVE);
			return shoppingCart;
		}

		//优惠码指令
		dealPromoPassword(shoppingCart);

		// 调用券接口获取符合的券活动id集合
		Set<String> matchedActivityCodes = Sets.newHashSet();
		List<String> couponsCodeCanUse = new ArrayList<>();
		List<String> couponsCodeCanUseFilted = new ArrayList<>();

		this.dealCouponCodes(shoppingCart, activityCacheMap, matchedActivityCodes, couponsCodeCanUse,cacheSwitch);

		Iterator<Entry<String, ActivityCacheDTO>> iterator = activityCacheMap.entrySet().iterator();
		while (iterator.hasNext()) {
			Entry<String, ActivityCacheDTO> next = iterator.next();
			ActivityCacheDTO activityCacheDTO = next.getValue();
			// 去除不符合的券活动
			if (activityCacheDTO.getActivityModel().isCouponActivity()
					&& !matchedActivityCodes.contains(activityCacheDTO.getActivityModel().getActivityCode())) {
				iterator.remove();
			}
		}

		// 去除不符合渠道和会员的活动
		Map<String, ActivityCacheDTO> filtedActivityMap = ActivityFilterUtil.filterActivityByOrgCodes(activityCacheMap,
				shoppingCart.getOrgCodes());
		filtedActivityMap = ActivityFilterUtil.filterActivityByQualifications(filtedActivityMap,
				shoppingCart.getQualifications());

		filtedActivityMap = ActivityFilterUtil.filterActivityByTime(filtedActivityMap, shoppingCart.getPromotionTime());

		//去除和优惠券互斥的活动
		//filtedActivityMap = promoGroupRelationDomain.filterActivityByCoupon(filtedActivityMap,shoppingCart.getTenantCode());


		if (MapUtils.isEmpty(filtedActivityMap)) {
			return shoppingCart;
		}
		Map<Object, Object> sortRules = redisService.getActivitySetting(shoppingCart.getTenantCode(),
				SettingTypeEnum.PRICE_SORT.code());
		log.info("sortRules:{}", sortRules);

		// 存放活动id=模板编码的map
		Map<String, ActivityCodeComparator> activityCodeComparatorMap = new HashMap<>();
		Map<String, ActivityCacheDTO> usedActivityMap = new HashMap<>();

		// 遍历购物车所有sku，绑定活动
		List<String> productCodes = new ArrayList<>();
		for (ShoppingCartItem shoppingCartItem : shoppingCart.getPromoProducts()) {
			productCodes.add(shoppingCartItem.getProductCode());
		}

		String productSkuDetailDtosKey = shoppingCart.getTenantCode()+":"+shoppingCart.getUserCode()+":"+"productSkuDetailDTOS";
		List<ProductSkuDetailDTO> productSkuDetailDTOS;
		productSkuDetailDTOS = (List<ProductSkuDetailDTO>) EasyCacheUtil.get(productSkuDetailDtosKey);
		if (CollectionUtils.isEmpty(productSkuDetailDTOS) || !cacheSwitch){
			productSkuDetailDTOS = productDetailService
					.queryListByActivityCodesAndProductCodes(filtedActivityMap.keySet(), productCodes);
		}

		Map<String, List<ProductSkuDetailDTO>> blackProductMap = productSkuDetailDTOS.stream()
				.filter(p -> 2 == p.getType()).collect(Collectors.groupingBy(ProductSkuDetailDTO::getActivityCode));
		for (ShoppingCartItem shoppingCartItem : shoppingCart.getPromoProducts()) {

			ProductCodes productDTO = new ProductCodes();
			productDTO.setCategoryCodes(shoppingCartItem.getCategoryCodes());
			productDTO.setBrandCode(shoppingCartItem.getBrandCode());
			String productCode = shoppingCartItem.getProductCode();
			String skuCode = shoppingCartItem.getSkuCode();
			productDTO.setProductCode(productCode);
			productDTO.setSkuCode(skuCode);
			productDTO.setCombineSkuCode(shoppingCartItem.getCombineSkuCode());
			productDTO.setAttributes(shoppingCartItem.getAttributes());
			productDTO.setSpuAttributes(shoppingCartItem.getSpuAttributes());
			productDTO.setProductTag(shoppingCartItem.getProductTag());

			// 根据商铺和商品条件过滤活动
			List<String> orgCodes = new ArrayList<>();
			orgCodes.add(shoppingCartItem.getOrgCode());
			Map<String, ActivityCacheDTO> itemFilteredActivityMap = ActivityFilterUtil
					.filterActivityByOrgCodes(filtedActivityMap, orgCodes);

			itemFilteredActivityMap = activityCacheDomain.filterBlackProduct(itemFilteredActivityMap, productDTO,
					blackProductMap);

			itemFilteredActivityMap = activityCacheDomain.filterActivityByProduct(itemFilteredActivityMap, productDTO,
					productSkuDetailDTOS);

			itemFilteredActivityMap = activityCacheDomain.filterNoProductListPrice(itemFilteredActivityMap, shoppingCartItem.getProductListPrice());

			List<String> existGroupCodeList = getGroupCodeList(itemFilteredActivityMap.values().stream().collect(Collectors.toList()));
			//优先购买权限制检查

			//修改,只有checkOut的时候主动发起优先购买权检查
			TreeMap<ActivityCodeComparator, ShoppingCartActivity> usedActivities = new TreeMap<>(
					Comparator.reverseOrder());
			Map<String, BigDecimal> activity0411Price = new HashedMap<>();
			// 封装活动信息到商品中
			for (Entry<String, ActivityCacheDTO> entry : itemFilteredActivityMap.entrySet()) {
				ActivityCacheDTO activityCacheDTO = entry.getValue();
				ActivityModel activityModel = activityCacheDTO.getActivityModel();
				List<String> couponCodes = null;
				if (activityCacheDTO.getActivityModel() != null) {
					couponCodes = activityCacheDTO.getActivityModel().getCouponCodes();
					couponsCodeCanUseFilted.addAll(couponCodes);
				}

				setPriorityZero(activityCacheDTO, existGroupCodeList);

				//这里设置优先级排序方式,改变比较器中的排序执行顺序
				String priorityMode = ActivityCodeComparator.GROUP;
				if (groupBlackList.contains(activityCacheDTO.getActivityModel().getTenantCode())){
					priorityMode = ActivityCodeComparator.EXPRESSION;
				}

				Integer groupPriority = getGroupPriority(activityCacheDTO, activityModel);
				ActivityCodeComparator comparator = ActivityCodeComparator.builder()
						.activityCode(activityModel.getActivityCode()).templateCode(activityModel.getTemplateCode())
						.activityType(activityModel.getActivityType()).groupPriority(groupPriority).priority(activityModel.getPriority())
						.highPrioriy(activityModel.getHighPrioriy())
						.priorityMode(priorityMode)
						.build();

				activityCodeComparatorMap.put(activityModel.getActivityCode(), comparator);

				deal0411(productCode, skuCode, activity0411Price, activityCacheDTO, activityModel.getActivityCode(),shoppingCartItem);
				String sortRule = (String) sortRules.get(activityCacheDTO.getPromoTemplate().getTagCode());

				usedActivityMap.put(entry.getKey(), entry.getValue());
				usedActivities.put(comparator,
						new ShoppingCartActivity(activityModel, shoppingCart.getUserCode(), couponCodes,
								activityCacheDTO.getSeqNum(), sortRule,
								templateHelper.templateCode2TagCode(activityModel.getTemplateCode()), comparator));
			}

			shoppingCartItem.setUsedActivitys(new ArrayList<>(usedActivities.values()));
			shoppingCartItem.setActivity0411Price(activity0411Price);
		}

		activityCacheMap.clear();
		activityCacheMap.putAll(usedActivityMap);

		this.filterABActivity(shoppingCart.getPromoProducts());
		expressionAndCheck(shoppingCart, couponsCodeCanUse, couponsCodeCanUseFilted, activityCodeComparatorMap);

		//后置券活动优先级处理,为了保持所有商品命中的券活动优先级保持一致,防止无法识别多个优先级的相同活动为同一个活动
		postProcessingCouponActivityPriority(shoppingCart);


		return shoppingCart;
	}

	private void postProcessingCouponActivityPriority(ShoppingCartDTO shoppingCart) {
		//将券活动的优先级全部取出,重新赋值为优先级最高的

		Map<String, List<ShoppingCartActivity>> groupedActivities = shoppingCart.getPromoProducts().stream()
				.flatMap(promoProduct -> promoProduct.getUsedActivitys().stream())
				.filter(usedActivity -> usedActivity.getActivityType().equals(ActivityTypeEnum.COUPON.code()))
				.collect(Collectors.groupingBy(ShoppingCartActivity::getActivityCode));

		// 针对每个分组找出最高优先级并设置
		groupedActivities.forEach((activityCode, activities) -> {
			int maxPriority = activities.stream()
					.mapToInt(x->x.getComparator().getGroupPriority())
					.min()
					.orElseThrow(() -> new PromotionException("group priority not found"));

			activities.forEach(activity -> activity.getComparator().setGroupPriority(maxPriority));
		});
	}

	private Integer getGroupPriority(ActivityCacheDTO activityCacheDTO, ActivityModel activityModel) {

		String tenantCode = activityCacheDTO.getActivityModel().getTenantCode();
		if (groupBlackList.contains(tenantCode)) {
			return null;
		}
		Integer groupPriority = null;
		List<ActivityGroupCache> groupCacheList = activityCacheDTO.getGroupCacheList();
		if (!CollectionUtils.isEmpty(groupCacheList)) {
			Map<String, ActivityGroupCache> groupCacheMap = groupCacheList.stream()
					.collect(Collectors.toMap(ActivityGroupCache::getGroupCode, Function.identity()));
			ActivityGroupCache activityGroupCache = groupCacheMap.get(activityModel.getGroupCode());
			if (activityGroupCache != null) {
				groupPriority = activityGroupCache.getPriority();
			}
		}
		return groupPriority;
	}




	public void dealPromoPassword(ShoppingCartDTO shoppingCart) {
		//判断是否是优惠码还是优惠码指令
		if (StringUtil.isNotEmpty(shoppingCart.getCouponCodes())){
			String[] split = shoppingCart.getCouponCodes().split(",");
			for (String promoPassword : split) {
				TPromoCouponInnerCodeVO activityCodeByCouponCode = couponInnerCodeService.findActivityCodeByCouponCode(shoppingCart.getTenantCode(), promoPassword);
				if (null == activityCodeByCouponCode){
					checkPromoPassword(shoppingCart, promoPassword);
				}
			}
		}
	}

	private void checkPromoPassword(ShoppingCartDTO shoppingCart, String promoPassword) {

		List<PromoPassVO> promoPassVOS = couponInnerCodeService.findCouponCodeByPassword(shoppingCart.getTenantCode(), promoPassword);
		if (CollectionUtils.isEmpty(promoPassVOS)){
			throw new PromotionException(CouponErrorChecker.NO_EFFECTIVE);
		}else {
			List<String> collect = promoPassVOS.stream().map(PromoPassVO::getActivityCode).collect(Collectors.toList());

			String activityCode = activityService.findActivityNewByActivityCode(shoppingCart.getTenantCode(), collect);

			if (null == activityCode){
				throw new PromotionException(CouponErrorChecker.NO_EFFECTIVE);
			}
			Map<String, String> collect1 = promoPassVOS.stream().collect(Collectors.toMap(PromoPassVO::getActivityCode, PromoPassVO::getCouponCode));

			String couponCode = collect1.get(activityCode);
			//指令优惠码
			String couponCodes = shoppingCart.getCouponCodes();
			String replace = couponCodes.replace(promoPassword, couponCode);
			shoppingCart.setCouponCodes(replace);
		}
	}


	private void filterABActivity(List<ShoppingCartItem> scItemList) {
		Map<String, BigDecimal> priceAMap = new HashedMap<>();
		for (ShoppingCartItem scItem : scItemList) {
			List<ShoppingCartActivity> usedActivitys = scItem.getUsedActivitys();
			if (CollectionUtils.isEmpty(usedActivitys) || !"01".equals(scItem.getSelectionFlag())) {
				continue;
			}

			Iterator<ShoppingCartActivity> iterator = usedActivitys.iterator();
			while (iterator.hasNext()) {

				ShoppingCartActivity scActivity = iterator.next();
				if (!TemplateEnum.code2ABTemplate(scActivity.getTemplateCode())) {
					continue;
				}
				String activityCode = scActivity.getActivityCode();

				boolean delete = true;
				for (ShoppingCartItem shoppingCartItem : scItemList) {
					if (scItem.getSkuCode().equals(shoppingCartItem.getSkuCode())
							|| (StringUtil.isNotBlank(scItem.getCombineSkuCode())
							&& StringUtil.isNotBlank(shoppingCartItem.getCombineSkuCode())
							&& scItem.getCombineSkuCode().equals(shoppingCartItem.getCombineSkuCode()))) {
						continue;
					}
					List<ShoppingCartActivity> usedActivitys1 = shoppingCartItem.getUsedActivitys();
					for (ShoppingCartActivity shoppingCartActivity : usedActivitys1) {
						if (scActivity.getSeqNum().contains("1") && scActivity.getSeqNum().contains("2")) {
							if (shoppingCartActivity.getActivityCode().equals(scActivity.getActivityCode())
									&& (shoppingCartActivity.getSeqNum().equals("1")
									|| shoppingCartActivity.getSeqNum().equals("2"))) {
								delete = false;
							}
						} else if (scActivity.getSeqNum().equals("1") || scActivity.getSeqNum().equals("2")) {
							boolean flage = shoppingCartActivity.getActivityCode().equals(scActivity.getActivityCode())
									&& ((scActivity.getSeqNum().equals("1")
									&& shoppingCartActivity.getSeqNum().contains("2"))
									|| (scActivity.getSeqNum().equals("2")
									&& shoppingCartActivity.getSeqNum().contains("1")));
							if (flage) {
								delete = false;
							}
						}
						if (!delete) {
							break;
						}
					}
				}
				if (delete) {
					iterator.remove();
				} else {
					if ("1".equals(scActivity.getSeqNum()) && (!priceAMap.containsKey(activityCode)
							|| priceAMap.get(activityCode).compareTo(scItem.getProductPrice()) > 0)) {
						priceAMap.put(activityCode, scItem.getProductPrice());
					}
				}
			}
		}

		if (MapUtils.isNotEmpty(priceAMap)) {
			// 如果活动有定义A单价必须大于B， 这里进行处理
			for (ShoppingCartItem shoppingCartItem : scItemList) {
				List<ShoppingCartActivity> usedActivitys = shoppingCartItem.getUsedActivitys();
				if (CollectionUtils.isEmpty(usedActivitys) || !"01".equals(shoppingCartItem.getSelectionFlag())) {
					continue;
				}

				Iterator<ShoppingCartActivity> iterator = usedActivitys.iterator();
				while (iterator.hasNext()) {

					ShoppingCartActivity scActivity = iterator.next();
					if (!TemplateEnum.code2ABTemplate(scActivity.getTemplateCode())) {
						continue;
					}

					if ("01".equals(scActivity.getActivityModel().getProductCondition())
							&& "2".equals(scActivity.getSeqNum()) && shoppingCartItem.getProductPrice()
							.compareTo(priceAMap.get(scActivity.getActivityCode())) >= 0) {
						iterator.remove();
					}
				}
			}
		}
	}

	// 表达式校验和填充
	private void expressionCheck(ShoppingCartDTO shoppingCart,
								 Map<String, ActivityCodeComparator> activityCodeComparatorMap) {

		if (MapUtils.isNotEmpty(activityCodeComparatorMap)) {
			// 表达式
			String expression = expressionService.getActivityExpression(shoppingCart.getTenantCode());
			shoppingCart.setActivityExpr(ExpressionHelper.sortActivity(expression, activityCodeComparatorMap));
			if (!groupBlackList.contains(shoppingCart.getTenantCode())) {
				List<ActivityGroupCache> list = promoGroupRelationDomain.getTenantGroupCache(shoppingCart.getTenantCode());
				shoppingCart.setGroupCacheList(list);
			}
		}
	}

	private void expressionAndCheck(ShoppingCartDTO shoppingCart, List<String> couponsCodeCanUse,
									List<String> couponsCodeCanUseFilted, Map<String, ActivityCodeComparator> activityCodeComparatorMap) {

		if (MapUtils.isNotEmpty(activityCodeComparatorMap)) {

			this.checkCoupons(couponsCodeCanUse, couponsCodeCanUseFilted);

			// 表达式
			String expression = expressionService.getActivityExpression(shoppingCart.getTenantCode());
			shoppingCart.setActivityExpr(ExpressionHelper.sortActivity(expression, activityCodeComparatorMap));

			if (!groupBlackList.contains(shoppingCart.getTenantCode())) {
				List<ActivityGroupCache> list = promoGroupRelationDomain.getTenantGroupCache(shoppingCart.getTenantCode());
				shoppingCart.setGroupCacheList(list);
			}
		} else {

			if (CollectionUtils.isNotEmpty(couponsCodeCanUse)) {
				Check.check(true, CouponActivityChecker.COUPON_NO_PRODUCT, couponsCodeCanUse.get(0));
			}
		}
	}

	/**
	 * 券处理
	 */
	private void dealCouponCodes(ShoppingCartDTO shoppingCart, Map<String, ActivityCacheDTO> activityCacheMap,
								 Set<String> activityCodes, List<String> couponsCodeCanUse, boolean cacheSwitch) {

		if (StringUtil.isBlank(shoppingCart.getCouponCodes())) {
			return;
		}

//		List<String> existGroupCodeList = getGroupCodeList(activityCacheMap.values().stream().collect(Collectors.toList()));

		StringBuilder couponsCodes = new StringBuilder();

		// 判断券是否可用
		for (String couponCode : shoppingCart.getCouponCodes().split(",")) {
			if (StringUtil.isBlank(couponCode)) {
				continue;
			}

			String activityCode = couponCodeUserComponent.isCanUse(shoppingCart.getTenantCode(), couponCode,
					shoppingCart.getUserCode(),activityCacheMap,cacheSwitch);
			if (!"0".equals(activityCode)) {

				activityCodes.add(activityCode);

				ActivityCacheDTO activityCacheDTO = activityCacheMap.get(activityCode);
				CheckUtils.isNotNull(activityCacheDTO, ErrorCodes.VALIDATE_COUPON_ACTIVITY_EXIST);

				if (CollectionUtils.isNotEmpty(activityCacheDTO.getActivityModel().getCouponCodes())
						&& TemplateEnum.code2SelfExclusive(activityCacheDTO.getActivityModel().getTemplateCode())) {

					shoppingCart.addErrorCouponAndReasons(
							new ErrorCouponAndReason(couponCode, ErrorCodes.COUPON_USE_MORE_THAN_ONE_FAIL));
				} else {
					activityCacheDTO.getActivityModel().getCouponCodes().add(couponCode);
//					setPriorityZero(activityCacheDTO,existGroupCodeList);

					// 可使用的券
					couponsCodes.append(couponCode + ",");
					couponsCodeCanUse.add(couponCode);
				}
			}
		}

		// 剔除不可用的券(防止提交订单时锁定不能使用的券)
		shoppingCart.setCouponCodes(couponsCodes.toString());
	}

	private List<String> getGroupCodeList(List<ActivityCacheDTO> activityList) {
		if (CollectionUtils.isEmpty(activityList)) {
			return Collections.emptyList();
		}
		List<String> existGroupCodeList = new ArrayList<>();
		activityList.forEach(vo -> existGroupCodeList.add(vo.getActivityModel().getGroupCode()));
		return existGroupCodeList;

	}

	private void setPriorityZero(ActivityCacheDTO activityCacheDTO,List<String> existGroupCodeList) {
		log.debug("activityCacheDTO:{}", JSON.toJSONString(activityCacheDTO));
		log.debug("existGroupCodeList:{}", JSON.toJSONString(existGroupCodeList));
		if (!activityCacheDTO.getActivityModel().getActivityType().equals(ActivityTypeEnum.COUPON.code())){
			return;
		}
		// 优先使用优惠券
		//当前活动优先级
		List<ActivityGroupCache> groupCacheList = activityCacheDTO.getGroupCacheList();
		if (CollectionUtils.isEmpty(groupCacheList)) {
			return;
		}
		Map<String, Integer> priorityMap = groupCacheList.stream().collect(Collectors.toMap(ActivityGroupCache::getGroupCode, ActivityGroupCache::getPriority));
		log.debug("priorityMap:{}", JSON.toJSONString(priorityMap));
		String groupCode = activityCacheDTO.getActivityModel().getGroupCode();
		groupCacheList.forEach(vo -> {
			//设置为高优先级
			activityCacheDTO.getActivityModel().setHighPrioriy(true);
			// 当前券活动的所属分组与匹配的活动存在互斥则优先使用优惠券，叠加的则不调整优先级
			if (vo.getGroupCode().equals(groupCode) && CollectionUtils.isNotEmpty(vo.getRelationList())) {
				//设置分组优先级,将高优先级的互斥的存入券组优先级
				vo.getRelationList().forEach(code -> {
					if (existGroupCodeList.contains(code)) {
						Integer groupPriority = vo.getPriority();
						Integer comparePriority = priorityMap.get(code);
						if (groupPriority > comparePriority) {
							vo.setPriority(comparePriority);

							log.debug("groupPriority > comparePriority:{}", JSON.toJSONString(comparePriority));
						}
					}
				});

			}
		});
		log.debug("groupCacheList:{}", JSON.toJSONString(groupCacheList));
	}

	/**
	 * 校验券码是否满足购物车商品和时间条件，会员和店铺已经校验过了
	 *
	 * @param couponsCodeCanUse
	 * @param couponsCodeCanUseFilted
	 */
	private void checkCoupons(List<String> couponsCodeCanUse, List<String> couponsCodeCanUseFilted) {
		if (!CollectionUtils.isEmpty(couponsCodeCanUseFilted)) {
			if (couponsCodeCanUseFilted.size() != couponsCodeCanUse.size()) {
				for (String couponCode : couponsCodeCanUse) {
					Check.check(!couponsCodeCanUseFilted.contains(couponCode), CouponActivityChecker.COUPON_NO_PRODUCT,
							couponCode);
				}
			}
		} else {
			if (!CollectionUtils.isEmpty(couponsCodeCanUse)) {
				Check.check(true, CouponActivityChecker.COUPON_NO_PRODUCT, couponsCodeCanUse.get(0));
			}
		}
	}

	/**
	 * 0411每个不同特价活动价格绑定到商品
	 *
	 * @param productCode
	 * @param activity0411Price
	 * @param activityCacheDTO
	 * @param shoppingCartItem
	 */
	private void deal0411(String productCode, String skuCode, Map<String, BigDecimal> activity0411Price,
						  ActivityCacheDTO activityCacheDTO, String activityCode, ShoppingCartItem shoppingCartItem) {
		String code = FuncTypeEnum.IncentiveEnum.EACH_FIXED_MONEY.code();// 0411
		if (code.equals(activityCacheDTO.getPromoTemplate().getTemplateCode().substring(12, 16))) {
			Set<String> activityCodes = new HashSet<>();
			activityCodes.add(activityCacheDTO.getActivityModel().getActivityCode());
			List<ProductSkuDetailDTO> productSkuDetailDTOS = productDetailService
					.queryListByActivityCodesAndProductCode(activityCodes, productCode);
			if (!CollectionUtils.isEmpty(productSkuDetailDTOS)) {
				for (ProductSkuDetailDTO x : productSkuDetailDTOS) {
					if (skuCodeIsequals(skuCode, x)) {
						if (StringUtil.isNotEmpty(x.getPriceType()) && x.getPriceType().equals("2")){
							BigDecimal productPrice = shoppingCartItem.getProductPrice();
							//指定商品金额打折
							activity0411Price.put(activityCode, productPrice.multiply(new BigDecimal(x.getPriceDiscount()).multiply(new BigDecimal("0.1"))));
						}else{
							activity0411Price.put(activityCode, x.getPromoPrice());
						}
						break;
					}
				}
			}
		}
	}

	private boolean skuCodeIsequals(String skuCode, ProductSkuDetailDTO x) {
		return StringUtil.isBlank(x.getSkuCode()) || PromotionConstants.UNLIMITED.equals(x.getSkuCode())
				|| x.getSkuCode().equals(skuCode);
	}


	public List<String> filterCouponDomain(String tenantCode,String couponCodes) {
		//判断是否是优惠码还是优惠码指令
		List<String> coupons = new ArrayList<>();
		if (StringUtil.isNotEmpty(couponCodes)){
			String[] split = couponCodes.split(",");
			for (String promoPassword : split) {
				TPromoCouponInnerCodeVO activityCodeByCouponCode = couponInnerCodeService.findActivityCodeByCouponCode(tenantCode, promoPassword);
				if (null == activityCodeByCouponCode){
					coupons.add(filterCoupon(tenantCode, promoPassword));
				}else {
					coupons.add(activityCodeByCouponCode.getCouponCode());
				}
			}
		}
		return coupons;
	}

	public String filterCoupon(String tenantCode,String promoPassword) {

		List<PromoPassVO> promoPassVOS = couponInnerCodeService.findCouponCodeByPassword(tenantCode, promoPassword);
		if (CollectionUtils.isEmpty(promoPassVOS)) {
			throw new PromotionException(CouponErrorChecker.NO_EFFECTIVE);
		} else {
			List<String> collect = promoPassVOS.stream().map(PromoPassVO::getActivityCode).collect(Collectors.toList());
			String activityCode = activityService.findActivityNewByActivityCode(tenantCode, collect);

			if (null == activityCode) {
				throw new PromotionException(CouponErrorChecker.NO_EFFECTIVE);
			}
			Map<String, String> collect1 = promoPassVOS.stream().collect(Collectors.toMap(PromoPassVO::getActivityCode, PromoPassVO::getCouponCode));

			return collect1.get(activityCode);
		}
	}
}
