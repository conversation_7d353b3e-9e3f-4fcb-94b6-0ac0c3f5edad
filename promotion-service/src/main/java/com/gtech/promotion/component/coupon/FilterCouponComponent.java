/*
  Copyright (c) 2019 GTech All Rights Reserved.

  This software is the confidential and proprietary information of GTech. You shall not disclose such
  Confidential Information and shall use it only in accordance with the terms of the license agreement
  you entered into with GTech.

  GTECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS
  OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A
  PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY
  LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.component.coupon;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.gtech.commons.exception.ErrorCode;
import com.gtech.commons.exception.Exceptions;
import com.gtech.commons.page.PageData;
import com.gtech.commons.utils.BeanCopyUtils;
import com.gtech.commons.utils.ConvertUtils;
import com.gtech.commons.utils.DateUtil;
import com.gtech.commons.utils.StringUtil;
import com.gtech.promotion.RedisClient;
import com.gtech.promotion.calc.CalcExecuter;
import com.gtech.promotion.callable.CouponChooseCallable;
import com.gtech.promotion.callable.CouponFilterCallable;
import com.gtech.promotion.callable.ErrorCouponAndReason;
import com.gtech.promotion.callable.ThreadPoolUtil;
import com.gtech.promotion.checker.SystemChecker;
import com.gtech.promotion.checker.coupon.CouponActivityChecker;
import com.gtech.promotion.checker.coupon.CouponCodeUserChecker;
import com.gtech.promotion.checker.coupon.CouponErrorChecker;
import com.gtech.promotion.code.CodeHelper;
import com.gtech.promotion.code.activity.ActivityTypeEnum;
import com.gtech.promotion.code.activity.FuncTypeEnum;
import com.gtech.promotion.code.activity.FunctionParamTypeEnum;
import com.gtech.promotion.code.activity.OpsTypeEnum;
import com.gtech.promotion.code.coupon.CouponFrozenStatusEnum;
import com.gtech.promotion.code.coupon.CouponStatusEnum;
import com.gtech.promotion.code.coupon.CouponTypeEnum;
import com.gtech.promotion.code.coupon.FaceUnitEnum;
import com.gtech.promotion.component.activity.*;
import com.gtech.promotion.dao.model.activity.*;
import com.gtech.promotion.dao.model.coupon.QueryUserCouponVO;
import com.gtech.promotion.dao.model.coupon.TPromoCouponCodeUserVO;
import com.gtech.promotion.dao.model.coupon.TPromoCouponInnerCodeVO;
import com.gtech.promotion.domain.StoreInfoDomain;
import com.gtech.promotion.domain.coupon.CouponDomain;
import com.gtech.promotion.domain.coupon.CouponReleaseDomain;
import com.gtech.promotion.dto.cache.ActivityCacheDTO;
import com.gtech.promotion.dto.cache.ProductSkuDetailDTO;
import com.gtech.promotion.dto.in.activity.FrozenCouponCodeInDTO;
import com.gtech.promotion.dto.in.activity.ShoppingCartDTO;
import com.gtech.promotion.dto.in.activity.ShoppingCartItem;
import com.gtech.promotion.dto.in.activity.TenantProductDTO;
import com.gtech.promotion.dto.in.coupon.ChooseCouponByCartCouponInfoDTO;
import com.gtech.promotion.dto.in.coupon.ChooseCouponByCartDTO;
import com.gtech.promotion.dto.in.coupon.ConditionAndFace;
import com.gtech.promotion.dto.out.activity.ShoppingCartOutDTO;
import com.gtech.promotion.dto.out.coupon.CouponActivityProductDTO;
import com.gtech.promotion.dto.out.coupon.CouponInfoDTO;
import com.gtech.promotion.dto.out.coupon.CouponReleaseTimeOutDTO;
import com.gtech.promotion.dto.out.coupon.StoreOutDTO;
import com.gtech.promotion.exception.Check;
import com.gtech.promotion.exception.ErrorCodes;
import com.gtech.promotion.exception.PromotionException;
import com.gtech.promotion.page.RequestPage;
import com.gtech.promotion.service.activity.*;
import com.gtech.promotion.service.common.GroovyService;
import com.gtech.promotion.service.coupon.PromoCouponCodeUserService;
import com.gtech.promotion.service.coupon.PromoCouponInnerCodeService;
import com.gtech.promotion.service.coupon.PromoCouponReleaseService;
import com.gtech.promotion.utils.*;
import com.gtech.promotion.vo.bean.ActivityPeriod;
import com.gtech.promotion.vo.bean.ProductCodes;
import com.gtech.promotion.vo.param.activity.CustomCondition;
import com.gtech.promotion.vo.param.activity.UpdateCouponParam;
import com.gtech.promotion.vo.param.activity.UpdateUserCouponParam;
import com.gtech.promotion.vo.param.coupon.QueryCouponActivityListByProductListParam;
import com.gtech.promotion.vo.param.coupon.QueryCouponListByUserParam;
import com.gtech.promotion.vo.result.coupon.QueryCouponActivityListByProductListResult;
import com.gtech.promotion.vo.result.coupon.QueryCouponActivityListByProductResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.Map.Entry;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 *
 */
@Slf4j
@Component
public class FilterCouponComponent {

    @Autowired
    private PromoCouponCodeUserService couponCodeUserService;

    @Autowired
    private PromoCouponInnerCodeService couponInnerCodeService;

    @Autowired
    private ActivityService activityService;

    @Autowired
    private ActivityCacheDomain activityCacheDomain;

    @Autowired
    private ActivityFuncParamService promoActivityFuncParamService;

    @Autowired
    private ActivityFuncRankService promoActivityFuncRankService;

    @Autowired
    private PromoCouponReleaseService promoCouponReleaseService;

    @Autowired
    private TemplateService templateService;

    @Autowired
    private ShoppingCartDomain shoppingCartDomain;

    @Autowired
    private ActivityStoreService tPromoActivityStoreService;
    @Autowired
    private ActivityPeriodService activityPeriodService;

    @Autowired
    private CalcExecuter calcExecuter;

    @Autowired
    private FunctionParamDomain functionParamDomain;

    @Autowired
    private ActivityComponentDomain activityComponentDomain;

    @Autowired
    private CouponActivityComponent couponActivityComponent;

    @Autowired
    private ActivityExpireComponentDomain activityExpireComponentDomain;
    @Autowired
    private ActivityProductDetailService productDetailService;

    @Autowired
    private GroovyService groovyService;

	@Autowired
	private ActivityLanguageService languageService;

    @Autowired
    private RedisClient redisClient;

    private void fillActivityInfo(PageData<CouponDomain> couponDomainPage, List<ActivityModel> activityModelList,
			String language, List<String> activityCodes, List<TPromoActivityStoreVO> storeVOs) {

		String tenantCode = activityModelList.get(0).getTenantCode();
        List<CouponDomain> couponDomainList = couponDomainPage.getList();
        Map<String, ActivityModel> activityModelMap = Maps.uniqueIndex(activityModelList,
                x -> null == x ? null : x.getActivityCode());

		Map<String, ActivityPeriodModel> preiodMap = new HashMap<>();
		List<ActivityPeriodModel> preiodModelList = activityPeriodService.queryPeriodByActivityCodes(tenantCode, activityCodes);
		if (!CollectionUtils.isEmpty(preiodModelList)) {
			preiodMap = preiodModelList.stream().collect(Collectors.toMap(ActivityPeriodModel::getActivityCode, Function.identity(), (k1, k2) -> k1));
		}

		Map<String, ActivityLanguageModel> languageMap = new HashMap<>();
		if (!StringUtil.isEmpty(language)) {
			List<ActivityLanguageModel> languageList = languageService.queryActivityLanguagesByActivityCodes(tenantCode, language, activityCodes);
			languageMap = languageList.stream().collect(Collectors.toMap(ActivityLanguageModel::getActivityCode, Function.identity(), (k1, k2) -> k1));
		}

		List<ActivityFunctionParamRankModel> rankVOs = this.promoActivityFuncRankService.getRankListByActivityCodes(tenantCode,
				activityCodes);

        for (CouponDomain couponDomain : couponDomainList) {
            ActivityModel activityModel = activityModelMap.get(couponDomain.getActivityCode());
            if (null == activityModel) {
                continue;
            }
//            this.activityComponentDomain.loadActivityLanguage(activityModel, language);
			activityModel.setLanguage(languageMap.get(activityModel.getActivityCode()));

            String templateCode = activityModel.getTemplateCode();
            String substring = templateCode.substring(templateCode.length() - 2);
            couponDomain.setRewardType(substring);
            if (FaceUnitEnum.DISCOUNT.code().equals(substring)) {
                BigDecimal faceValue = couponDomain.getFaceValue().multiply(new BigDecimal(10));
                couponDomain.setFaceValue(faceValue);
            }
			String condition = activityModel.getCustomCondition();
			if (!StringUtil.isEmpty(condition)) {
				couponDomain.setCustomConditions(JSONArray.parseArray(condition, CustomCondition.class));
			}
            couponDomain.setActivityLabel(activityModel.getActivityLabel());
            couponDomain.setActivityName(activityModel.getActivityName());
            couponDomain.setActivityCode(activityModel.getActivityCode());
            couponDomain.setActivityStatus(activityModel.getActivityStatus());
            couponDomain.setActivityUrl(activityModel.getActivityUrl());
            couponDomain.setActivityRemark(activityModel.getActivityRemark());
            couponDomain.setActivityDesc(activityModel.getActivityDesc());
			couponDomain.setActivityPeriod(BeanCopyUtils.jsonCopyBean(preiodMap.get(activityModel.getActivityCode()),
                    ActivityPeriod.class));

            if (Constants.DEFAULT_USED_REF_ID.equals(couponDomain.getUsedRefId())) {
                couponDomain.setUsedRefId(null);
            }
            // 店铺出参添加
			List<TPromoActivityStoreVO> childStoreVOs = storeVOs.stream().filter(vo -> vo.getActivityCode().equals(couponDomain.getActivityCode()))
					.collect(Collectors.toList());
            List<StoreOutDTO> stores = new ArrayList<>();
			for (TPromoActivityStoreVO tPromoActivityStoreVO : childStoreVOs) {
                StoreOutDTO store = new StoreOutDTO();
                store.setOrgCode(tPromoActivityStoreVO.getOrgCode());
                store.setStoreName(tPromoActivityStoreVO.getStoreName());
                stores.add(store);
            }
            couponDomain.setStores(BeanCopyUtils.jsonCopyList(stores, StoreInfoDomain.class));
			List<ActivityFunctionParamRankModel> childRankVOs = rankVOs.stream().filter(vo -> vo.getActivityCode().equals(couponDomain.getActivityCode()))
					.collect(Collectors.toList());
			if (!CollectionUtils.isEmpty(childRankVOs)) {
				ActivityFunctionParamRankModel rankVO = childRankVOs.get(0);
                // 填充条件
                FunctionParamModel param = this.promoActivityFuncParamService
                        .getRuleFuncParamListByRankIdAndFunctionCode(rankVO.getId(), templateCode.substring(8, 12));
                couponDomain.setConditionUnit(param.getParamUnit());
                couponDomain.setConditionValue(ConvertUtils.toBigDecimal(param.getParamValue(), null));
            }
        }
    }

    @Transactional(readOnly = true)
    public PageData<CouponDomain> queryUserCouponList(QueryCouponListByUserParam param) {

        RequestPage page = new RequestPage(Math.max(param.getPageNum(), 1), param.getPageSize());
        TPromoCouponCodeUserVO couponCodeUserVO = new TPromoCouponCodeUserVO();
        couponCodeUserVO.setUserCode(param.getUserCode());
        couponCodeUserVO.setTenantCode(param.getTenantCode());
        couponCodeUserVO.setStatus(param.getStatus());
        couponCodeUserVO.setTakeLabel(param.getTakeLabel());

        PageData<CouponDomain> couponDomainPage = null;

        //可查询冻结券
        if (Boolean.TRUE.equals(param.isFlag())){

            QueryUserCouponVO queryUserCouponVO = BeanCopyUtils.jsonCopyBean(param, QueryUserCouponVO.class);
            queryUserCouponVO.setPageNo(page.getPageNo());
            queryUserCouponVO.setPageCount(page.getPageCount());
            queryUserCouponVO.setSort(param.getSortByReceivedTime());
            couponDomainPage = couponCodeUserService.queryUserCouponByUserId(couponCodeUserVO,queryUserCouponVO);

        }else {
            //不包含冻结券
            couponDomainPage = couponCodeUserService.getUserCouponByUserId(couponCodeUserVO, page,
                    param.getOpsTypeList(), param.getBeginTime(), param.getEndTime(), param.getSortByReceivedTime());

        }

        if (null == couponDomainPage) {
            PageData<CouponDomain> objectPageData = new PageData<>();
            objectPageData.setTotal(0L);
            objectPageData.setList(new ArrayList<>());
            return  objectPageData;
        }

        Set<String> activityCodes = new HashSet<>();
        if (null != couponDomainPage.getList()){
            for (CouponDomain e : couponDomainPage.getList()) {
                activityCodes.add(e.getActivityCode());
            }
        }

        if (CollectionUtils.isEmpty(activityCodes)){
            PageData<CouponDomain> objectPageData = new PageData<>();
            objectPageData.setTotal(0L);
            objectPageData.setList(new ArrayList<>());
            return  objectPageData;
        }

        List<ActivityModel> activityModelList = this.activityService.queryActivityByActivityCodes(param.getTenantCode(),
				new ArrayList<>(activityCodes));
        if (CollectionUtils.isEmpty(activityModelList)) {
            couponDomainPage.getList().clear();
            couponDomainPage.setTotal(0L);
            return couponDomainPage;
        }

        // 暂时不去掉，业务存在重复？
        if (this.activityExpireComponentDomain.expireActivity(activityModelList) > 0) {
            couponDomainPage = couponCodeUserService.getUserCouponByUserId(couponCodeUserVO, page,
                    param.getOpsTypeList(), param.getBeginTime(), param.getEndTime(), param.getSortByReceivedTime());
        }

		List<TPromoActivityStoreVO> storeVOs = tPromoActivityStoreService.getStoresByActivityCodes(param.getTenantCode(), new ArrayList<>(activityCodes));

		this.fillActivityInfo(couponDomainPage, activityModelList, param.getLanguage(), new ArrayList<>(activityCodes), storeVOs);

        List<CouponDomain> list = new ArrayList<>();
        for (CouponDomain couponDomain : couponDomainPage.getList()) {
			String activityCode = couponDomain.getActivityCode();
            String orgCode = param.getOrgCode();
            boolean flag = false;
            if (!CollectionUtils.isEmpty(storeVOs) && StringUtil.isNotBlank(orgCode)) {
				boolean anyMatch = storeVOs.stream().anyMatch(x -> x.getOrgCode().equals(orgCode) && x.getActivityCode().equals(activityCode));
                if (anyMatch){
                    flag = true;
                }
            }else {
                flag = true;
            }
            if (flag){
                list.add(couponDomain);
            }
            this.couponActivityComponent.expireCoupon(couponDomain);
        }
        couponDomainPage.setList(list);

        return couponDomainPage;
    }

    @Transactional
    public List<CouponInfoDTO> filterCoupon(ShoppingCartDTO shoppingCart) {

        final List<CouponInfoDTO> emptyResult = Collections.emptyList();
        List<CouponInfoDTO> result = Lists.newArrayList();




        // 1、获取符合商品条件的券活动
        // 2、根据券活动获取用户的券码
        // 3、多线程进行购物车计算，每张券码放进购物车就是一个执行任务
        // 4、根据计算结果，选取所有有效的优惠券
        // 5、获取优惠券的相关信息，并返回
        // 判断 券码是否存在 匿名券和优惠码存在转类型
        List<String> inputCouponCodes = new ArrayList<>();
        List<TPromoCouponInnerCodeVO> couponInnerCodes = new ArrayList<>();
        if (StringUtil.isNotBlank(shoppingCart.getCouponCodes())) {

            //优惠码指令
            shoppingCartDomain.dealPromoPassword(shoppingCart);

            inputCouponCodes = Splitter.on(",").trimResults().splitToList(shoppingCart.getCouponCodes());
            couponInnerCodes = couponInnerCodeService.getCouponInnerCodeByCodes(shoppingCart.getTenantCode(),
                    inputCouponCodes);
            checkCoupon(inputCouponCodes, couponInnerCodes, shoppingCart.getUserCode());
        }

        // 获取缓存的券活动
        Map<String, ActivityCacheDTO> couponActivityMap = this.filterCouponActivity(shoppingCart, couponInnerCodes);
        if (MapUtils.isEmpty(couponActivityMap)) {
            return returnFalseCoupon(emptyResult, result, couponInnerCodes, ErrorCodes.COUPON_ACTIVITY_MATCH_FAILED);
        }

        // 3、根据活动id获取用户拥有的有效券码
        List<TPromoCouponCodeUserVO> couponCodeUserVOs = couponCodeUserService.getMyCodesByActivityCodes(
                shoppingCart.getUserCode(), shoppingCart.getTenantCode(), couponActivityMap.keySet(),
                shoppingCart.getOpsTypeList());
        if (CollectionUtils.isEmpty(couponCodeUserVOs) && StringUtil.isBlank(shoppingCart.getCouponCodes())) {
            return emptyResult;
        }


        String key = shoppingCart.getTenantCode()+":"+shoppingCart.getUserCode()+":"+"couponCodeUserVOs";
        EasyCacheUtil.set(key,couponCodeUserVOs,4);


        // 优惠券时 避免重复
        List<String> couponCodes = new ArrayList<>();
        // 判断 券码是否存在 匿名券和优惠码存在转类型
        getCouponAndCheckCoupon(shoppingCart, inputCouponCodes, couponInnerCodes, couponCodeUserVOs, couponCodes);
        Check.check(CollectionUtils.isEmpty(couponCodeUserVOs) && StringUtil.isNotBlank(shoppingCart.getCouponCodes()),
                CouponErrorChecker.MEMBER_NOT_COUPON);
        if (CollectionUtils.isEmpty(couponCodeUserVOs)) {
            return emptyResult;
        }

        Map<String, TPromoCouponCodeUserVO> couponCodeUserMap = this.couponCodeUserVOList2Map(couponCodeUserVOs);
        shoppingCart.autoHighPrioriyActivities(couponCodeUserMap);
        log.info("autoHighPrioriyActivities:{}", couponCodeUserMap);
        boolean validateInputCoupons = this.validateInputCoupons(shoppingCart, couponCodeUserMap, couponActivityMap);

        List<Future<ShoppingCartDTO>> futures = this.runCouponFilterCallAbles(shoppingCart, couponCodeUserMap,
                couponActivityMap);
        if (CollectionUtils.isEmpty(futures)) {
            return returnFalseCoupon(emptyResult, result, couponInnerCodes, ErrorCodes.COUPON_ACTIVITY_MATCH_FAILED);
        }
        Set<String> selectCouponCodeSet = new HashSet<>();
        Map<String, ErrorCode> errorCouponMap = new HashMap<>();
        for (Future<ShoppingCartDTO> future : futures) {
            checkResult(inputCouponCodes, validateInputCoupons, selectCouponCodeSet, errorCouponMap, future);
        }

        // 4、判断券码的状态和冻结状态
        if (CollectionUtils.isNotEmpty(couponCodeUserVOs)) {
            // 5、根据选定的券码返回叠加券码
            getCouponCodeUsers(result, couponActivityMap, couponCodeUserVOs, selectCouponCodeSet, errorCouponMap,shoppingCart.getTenantCode());
        }

        Comparator<CouponInfoDTO> comparator = Comparator.comparing(CouponInfoDTO::getIsReward)
                .thenComparing(CouponInfoDTO::getValidEnd);
        return result.stream().sorted(comparator.reversed()).collect(Collectors.toList());
    }

    public void getCouponCodeUsers(List<CouponInfoDTO> result, Map<String, ActivityCacheDTO> couponActivityMap,
                                   List<TPromoCouponCodeUserVO> couponCodeUserVOs, Set<String> selectCouponCodeSet,
                                   Map<String, ErrorCode> errorCouponMap,String tenantCode) {

        List<String> activityCodes = couponCodeUserVOs.stream().map(TPromoCouponCodeUserVO::getActivityCode).collect(Collectors.toList());

        //Map<String, ConditionAndFace> couponValueMap = functionParamDomain.findConditionAndFaceByActivityCodes(tenantCode, activityCodes);

        for (TPromoCouponCodeUserVO couponCodeUser : couponCodeUserVOs) {
            if (!couponActivityMap.containsKey(couponCodeUser.getActivityCode())) {

                CouponInfoDTO couponInfoDto = this.setCouponInfoDTO(ErrorCodes.VALIDATE_COUPON_ACTIVITY_EXIST,
                        couponCodeUser);
                if (null != couponInfoDto) {
                    result.add(couponInfoDto);
                }
                continue;
            }

            CouponInfoDTO couponInfoDTO = BeanCopyUtils.jsonCopyBean(couponCodeUser, CouponInfoDTO.class);
            ActivityCacheDTO activityCacheDTO = couponActivityMap.get(couponCodeUser.getActivityCode());
            ActivityModel activityModel = activityCacheDTO.getActivityModel();

			String condition = activityModel.getCustomCondition();
			if (!StringUtil.isEmpty(condition)) {
				couponInfoDTO.setCustomConditions(JSONArray.parseArray(condition, CustomCondition.class));
			}
            couponInfoDTO.setActivityPeriod(
                    BeanCopyUtils.jsonCopyBean(activityCacheDTO.getPeriodModel(), ActivityPeriod.class));
            couponInfoDTO.setCouponStatus(couponCodeUser.getStatus());
            couponInfoDTO.setValidBegin(couponCodeUser.getValidStartTime());
            couponInfoDTO.setValidEnd(couponCodeUser.getValidEndTime());
            couponInfoDTO.setPromoPassword(couponCodeUser.getPromoPassword());
            couponInfoDTO.setActivityCode(activityModel.getActivityCode());
            couponInfoDTO.setActivityName(activityModel.getActivityName());
            couponInfoDTO.setActivityDesc(activityModel.getActivityDesc());
            couponInfoDTO.setActivityShortDesc(activityModel.getActivityRemark());
            couponInfoDTO.setActivityLabel(activityModel.getActivityLabel());
            couponInfoDTO.setUserlimitMax(activityModel.getUserLimitMax());
//            final ConditionAndFace conditionAndFace = functionParamDomain.findConditionAndFaceByActivityCode(activityModel.getActivityCode());
            //ConditionAndFace conditionAndFace = couponValueMap.get(activityModel.getActivityCode());
            Map<String, ConditionAndFace> conditionAndFaceByActivityCodesCache = functionParamDomain.findConditionAndFaceByActivityCodesCache(Lists.newArrayList(activityCacheDTO));
            ConditionAndFace conditionAndFace = conditionAndFaceByActivityCodesCache.get(activityModel.getActivityCode());


            couponInfoDTO.setFaceUnit(conditionAndFace.getFaceUnit());
            couponInfoDTO.setFaceValue(conditionAndFace.getFaceValue());
            couponInfoDTO.setConditionValue(conditionAndFace.getConditionValue());
            couponInfoDTO.setConditionUnit(conditionAndFace.getConditionUnit());

            String templateCode = activityModel.getTemplateCode();
            couponInfoDTO.setRewardType(templateCode.substring(templateCode.length() - 2));
            if (CollectionUtils.isEmpty(selectCouponCodeSet)) {
                couponInfoDTO.setIsReward(false);
            } else {
                couponInfoDTO.setIsReward(selectCouponCodeSet.contains(couponCodeUser.getCouponCode()));
            }

            if (errorCouponMap.containsKey(couponCodeUser.getCouponCode())) {
                couponInfoDTO.setIsReward(false);
                couponInfoDTO.setFailedReason(errorCouponMap.get(couponCodeUser.getCouponCode()));
            }
            if (Boolean.FALSE.equals(couponInfoDTO.getIsReward()) && StringUtil.isBlank(couponInfoDTO.getFalseReason())) {
                couponInfoDTO.setFailedReason(ErrorCodes.COUPON_USE_FAILED);
            }
            result.add(couponInfoDTO);
        }
    }


    public void getCouponAndCheckCoupon(ShoppingCartDTO shoppingCart, List<String> inputCouponCodes,
                                        List<TPromoCouponInnerCodeVO> couponInnerCodes, List<TPromoCouponCodeUserVO> couponCodeUserVOs,
                                        List<String> couponCodes) {
        if (CollectionUtils.isNotEmpty(inputCouponCodes)) {
            if (!CollectionUtils.isEmpty(couponCodeUserVOs)) {
                couponCodeUserVOs.forEach(x -> couponCodes.add(x.getCouponCode()));
            }

            List<String> collect = couponInnerCodes.stream().map(TPromoCouponInnerCodeVO::getReleaseCode).collect(Collectors.toList());

            List<CouponReleaseDomain> couponReleaseDomainList = promoCouponReleaseService.queryReleaseByCondition(shoppingCart.getTenantCode(), null, collect);


            String key = Constants.APP_KEY +":"+ shoppingCart.getTenantCode() +":"+ shoppingCart.getUserCode() +":"+ "couponReleaseDomainList";
            EasyCacheUtil.set(key,couponReleaseDomainList,4);


            Map<String, List<CouponReleaseDomain>> releaseMap = couponReleaseDomainList.stream().collect(Collectors.groupingBy(CouponReleaseDomain::getReleaseCode));

            // 添加匿名券和优惠码
            couponInnerCodes.stream().filter(x -> !couponCodes.contains(x.getCouponCode())).forEach(y -> {

                ActivityModel activityModel = activityComponentDomain.findValidActivity(y.getTenantCode(),
                        y.getActivityCode(), shoppingCart.getLanguage(), new Date());
                if (null != activityModel && !CouponTypeEnum.PROMOTION_COUPON.equalsCode(y.getCouponType())) {

                    TPromoCouponCodeUserVO userVO = BeanCopyUtils.jsonCopyBean(y, TPromoCouponCodeUserVO.class);
                    List<CouponReleaseDomain> releaseDomains = releaseMap.get(y.getReleaseCode());

                    if (CollectionUtils.isNotEmpty(releaseDomains)){
                        userVO.setValidTime(releaseDomains.get(0), activityModel.getActivityEnd());
                    }
//                    CouponReleaseDomain releaseVO = promoCouponReleaseService.findCouponReleaseByReleaseCode(y.getTenantCode(), y.getReleaseCode());
                    // 设置可用时间段
                    couponCodeUserVOs.add(userVO);
                    couponCodes.add(y.getCouponCode());
                }
            });
        }
    }

    public void checkResult(List<String> inputCouponCodes, boolean validateInputCoupons,
                            Set<String> selectCouponCodeSet, Map<String, ErrorCode> errorCouponMap, Future<ShoppingCartDTO> future) {
        try {
            ShoppingCartDTO shoppingCart1 = future.get();
            if (null == shoppingCart1) {
                if(validateInputCoupons) selectCouponCodeSet.addAll(inputCouponCodes);
                return;
            }

            List<String> triedCouponCodes = Collections.emptyList();
            if (StringUtil.isNotBlank(shoppingCart1.getCouponCodes())) {
                triedCouponCodes = Splitter.on(",").splitToList(shoppingCart1.getCouponCodes());
            }

            if (CollectionUtils.isNotEmpty(inputCouponCodes) && validateInputCoupons
                    && !this.validateTriedCoupons(inputCouponCodes, triedCouponCodes)) {
                // input coupon list is not empty and all valid, current check failed ==> so
                // discard this result.
                selectCouponCodeSet.addAll(inputCouponCodes);
                return;
            }

            selectCouponCodeSet.addAll(triedCouponCodes);

            if (CollectionUtils.isNotEmpty(shoppingCart1.getErrorCouponAndReasons())) {
                for (ErrorCouponAndReason e : shoppingCart1.getErrorCouponAndReasons()) {
                    errorCouponMap.put(e.getErrorCoupon(), e.getFailedReason());
                }
            }
        } catch (Exception e) {
            log.error("筛选计算出错:{}", e);// NOSONAR
            log.error("券码筛选计算出错:{}", e.getMessage());// NOSONAR
            throw new PromotionException(SystemChecker.CALC_ERROR);
        }
    }

    public boolean validateInputCoupons(ShoppingCartDTO scParam, Map<String, TPromoCouponCodeUserVO> couponCodeUserMap,
                                        Map<String, ActivityCacheDTO> couponActivityMap) {

        if (StringUtil.isBlank(scParam.getCouponCodes())) {
            return true;
        }

        if (MapUtils.isEmpty(couponCodeUserMap) || MapUtils.isEmpty(couponActivityMap)) {
            return false;
        }

        List<String> inputCouponCodes = Splitter.on(",").trimResults().splitToList(scParam.getCouponCodes());
        CouponFilterCallable couponFilter = new CouponFilterCallable(shoppingCartDomain,
                this.filterActivityCacheMap(couponActivityMap, couponCodeUserMap, scParam.getCouponCodes()), scParam,
                calcExecuter);
        ShoppingCartDTO scResult = couponFilter.filterCouponsByShoppingCart();
        if (null == scResult) {
            throw Exceptions.fail(ErrorCodes.COUPON_USE_FAILED_P, inputCouponCodes);
        }
        if (CollectionUtils.isNotEmpty(scResult.getErrorCouponAndReasons())) {
            throw Exceptions.fail(ErrorCodes.COUPON_USE_FAILED_P,
                    scResult.getErrorCouponAndReasons().get(0).getErrorCoupon());
        }

        return true;
    }

    public boolean validateTriedCoupons(List<String> inputCouponCodes, List<String> triedCouponCodes) {

        if (CollectionUtils.isEmpty(inputCouponCodes)) {
            return true;
        }

        if (CollectionUtils.isEmpty(triedCouponCodes)) {
            return false;
        }

        for (String s : inputCouponCodes) {
            if (!triedCouponCodes.contains(s)) {
                return false;
            }
        }

        return true;
    }

    private Map<String, ActivityCacheDTO> filterCouponActivity(ShoppingCartDTO shoppingCart,
                                                               List<TPromoCouponInnerCodeVO> couponInnerCodeList) {

        // <couponCode, activityCode> map
        Map<String, String> couponCodeMap = Collections.emptyMap();
        if (CollectionUtils.isNotEmpty(couponInnerCodeList)) {
            couponCodeMap = couponInnerCodeList.stream().collect(
                    Collectors.toMap(TPromoCouponInnerCodeVO::getCouponCode, TPromoCouponInnerCodeVO::getActivityCode));
        }

        // Get all coupon activity info
        Map<String, ActivityCacheDTO> couponActivityMap = activityCacheDomain.getActivityCacheMap(
                shoppingCart.getTenantCode(), shoppingCart.getLanguage(), 2, ActivityTypeEnum.COUPON);
        this.validateInputCouponCodes(couponCodeMap, couponActivityMap, ErrorCodes.COUPON_FILTER_EFFECTIVE_FAILED);

        // 1、根据渠道和会员过滤活动
        couponActivityMap = ActivityFilterUtil.filterActivityByOrgCodes(couponActivityMap, shoppingCart.getOrgCodes());
        couponActivityMap = ActivityFilterUtil.filterActivityByQualifications(couponActivityMap,
                shoppingCart.getQualifications());
        this.validateInputCouponCodes(couponCodeMap, couponActivityMap, ErrorCodes.COUPON_FILTER_MEMBER_FAILED);

        // 2、根据商品过滤活动
        couponActivityMap = filterByProducts(couponActivityMap, shoppingCart.getPromoProducts());
        if (MapUtils.isEmpty(couponActivityMap)) {
            return Collections.emptyMap();
        } else {
            Set<String> activityCodes = couponActivityMap.keySet();
            for (TPromoCouponInnerCodeVO vo : couponInnerCodeList) {
                if (!activityCodes.contains(vo.getActivityCode())) {
                    return Collections.emptyMap();
                }
            }
        }

        return couponActivityMap;
    }

    /**
     * @param couponCodeMap     -- <couponCode, activityCode> map
     * @param couponActivityMap -- <activityCode, ActivityCacheDTO> map
     */
    private void validateInputCouponCodes(Map<String, String> couponCodeMap,
                                          Map<String, ActivityCacheDTO> couponActivityMap, ErrorCode errorCode) {

        if (MapUtils.isEmpty(couponCodeMap)) {
            return;
        }

        if (MapUtils.isEmpty(couponActivityMap)) {
            throw Exceptions.fail(errorCode, String.join(",", couponCodeMap.keySet()));
        }
        for (Map.Entry<String, String> entry : couponCodeMap.entrySet()) {
            if (!couponActivityMap.containsKey(entry.getValue())) {
                throw Exceptions.fail(errorCode, entry.getKey());
            }
        }
    }

    private List<CouponInfoDTO> returnFalseCoupon(List<CouponInfoDTO> emptyResult, List<CouponInfoDTO> result,
                                                  List<TPromoCouponInnerCodeVO> couponInnerCodes, ErrorCode reason) {
        if (CollectionUtils.isEmpty(couponInnerCodes)) {
            return emptyResult;
        }
        couponInnerCodes.forEach(
                x -> result.add(setCouponInfoDTO(reason, BeanCopyUtils.jsonCopyBean(x, TPromoCouponCodeUserVO.class))));
        return result;
    }

    private void checkCoupon(List<String> inputCouponCodes, List<TPromoCouponInnerCodeVO> couponInnerCodes,
                             String userCode) {

        Check.check(CollectionUtils.isEmpty(couponInnerCodes) || inputCouponCodes.size() > couponInnerCodes.size(),
                CouponErrorChecker.NO_EFFECTIVE);
        List<String> inputCouponCodesCopy = BeanCopyUtils.jsonCopyList(inputCouponCodes, String.class);

        for (TPromoCouponInnerCodeVO innerCodeVO : couponInnerCodes) {

            String now = DateValidUtil.getNowFormatDate();

            Check.check(CouponFrozenStatusEnum.FROZENED.equalsCode(innerCodeVO.getFrozenStatus()),
                    CouponErrorChecker.FREEZE, CouponStatusEnum.getByCode(innerCodeVO.getStatus()).desc(),
                    innerCodeVO.getCouponCode());

            if (CouponTypeEnum.PROMOTION_COUPON.equalsCode(innerCodeVO.getCouponType())) {

                Check.check(!CouponStatusEnum.GRANTED.equalsCode(innerCodeVO.getStatus()), CouponErrorChecker.ERROR,
                        CouponStatusEnum.getByCode(innerCodeVO.getStatus()).desc(), innerCodeVO.getCouponCode());

                final TPromoCouponCodeUserVO userCouponInfo = couponCodeUserService
                        .getUserCouponInfo(innerCodeVO.getTenantCode(), innerCodeVO.getCouponCode(), userCode);
                Check.check(null == userCouponInfo, CouponErrorChecker.MEMBER_NOT_COUPON);

                if (userCouponInfo.getValidStartTime().compareTo(now) > 0
                        || userCouponInfo.getValidEndTime().compareTo(now) <= 0) {
                    Check.check(true, CouponErrorChecker.VALID_TIME_NO_REACH_COUPON_CODE,
                            userCouponInfo.getCouponCode());
                }

            } else {

                Check.check(!CouponStatusEnum.UN_GRANT.equalsCode(innerCodeVO.getStatus()), CouponErrorChecker.ERROR,
                        CouponStatusEnum.getByCode(innerCodeVO.getStatus()).desc(), innerCodeVO.getCouponCode());

                if (innerCodeVO.getReceiveStartTime().compareTo(now) > 0
                        || innerCodeVO.getReceiveEndTime().compareTo(now) <= 0) {
                    Check.check(true, CouponErrorChecker.RECEIVE_NO_REACH_COUPON_CODE, innerCodeVO.getCouponCode());
                }
            }
            inputCouponCodesCopy.remove(innerCodeVO.getCouponCode());
        }

        Check.check(!inputCouponCodesCopy.isEmpty(), CouponErrorChecker.NO_EFFECTIVE);
    }

    public List<Future<ShoppingCartDTO>> runCouponFilterCallAbles(ShoppingCartDTO shoppingCart,
                                                                  Map<String, TPromoCouponCodeUserVO> couponCodeMap, Map<String, ActivityCacheDTO> activityCacheMap) {

        //script緩存,5分鐘
        String scriptKey = shoppingCart.getTenantCode()+":"+"ANY_SKU_QUANTITY";
        if (null == EasyCacheUtil.get(scriptKey)) {
            ActivityScriptModel activityScriptModel = groovyService.findByCode(shoppingCart.getTenantCode(), "ANY_SKU_QUANTITY");
            EasyCacheUtil.set(scriptKey,activityScriptModel,5*60L);
        }

        //couponCode缓存
        String couponCodeKey = shoppingCart.getTenantCode()+":"+shoppingCart.getUserCode()+":"+"couponCodes";
        if (null == EasyCacheUtil.get(couponCodeKey)) {
            //查询couponCodeMap的所有库存
            List<String> couponCodes = new ArrayList<>(couponCodeMap.keySet());
            List<TPromoCouponInnerCodeVO> couponInnerCodeByCodes = couponInnerCodeService.getCouponInnerCodeByCodes(shoppingCart.getTenantCode(), couponCodes);
            EasyCacheUtil.set(couponCodeKey,couponInnerCodeByCodes,4L);
        }



        //activity Product缓存

        String productSkuDetailDtosKey = shoppingCart.getTenantCode()+":"+shoppingCart.getUserCode()+":"+"productSkuDetailDTOS";
        if (null == EasyCacheUtil.get(productSkuDetailDtosKey)){
            // 遍历购物车所有sku，绑定活动
            List<String> productCodes = new ArrayList<>();
            for (ShoppingCartItem shoppingCartItem : shoppingCart.getPromoProducts()) {
                productCodes.add(shoppingCartItem.getProductCode());
            }
            List<ProductSkuDetailDTO> productSkuDetailDTOS = productDetailService
                    .queryListByActivityCodesAndProductCodes(activityCacheMap.keySet(), productCodes);
            EasyCacheUtil.set(productSkuDetailDtosKey,productSkuDetailDTOS,4L);
        }






        List<CouponFilterCallable> callAbles = new ArrayList<>(couponCodeMap.size());
        for (Entry<String, TPromoCouponCodeUserVO> entry : couponCodeMap.entrySet()) {

            TPromoCouponCodeUserVO tPromoCouponCodeUserVO = entry.getValue();

            ShoppingCartDTO scTemp = BeanCopyUtils.jsonCopyBean(shoppingCart, ShoppingCartDTO.class);
            if (StringUtil.isNotBlank(scTemp.getCouponCodes())) {
                List<String> list = Splitter.on(",").trimResults().splitToList(scTemp.getCouponCodes());
                if (!list.contains(tPromoCouponCodeUserVO.getCouponCode())) {
                    scTemp.setCouponCodes(scTemp.getCouponCodes() + "," + tPromoCouponCodeUserVO.getCouponCode());
                    callAbles.add(new CouponFilterCallable(shoppingCartDomain,
                            this.filterActivityCacheMap(activityCacheMap, couponCodeMap, scTemp.getCouponCodes()),
                            scTemp, calcExecuter));
                }
            } else {
                scTemp.setCouponCodes(tPromoCouponCodeUserVO.getCouponCode());
                CouponFilterCallable couponFilterCallable = new CouponFilterCallable(shoppingCartDomain,
                        this.filterActivityCacheMap(activityCacheMap, couponCodeMap, scTemp.getCouponCodes()), scTemp,
                        calcExecuter);
                callAbles.add(couponFilterCallable);
            }
        }

        if (CollectionUtils.isEmpty(callAbles) && StringUtil.isNotBlank(shoppingCart.getCouponCodes())) {
            ShoppingCartDTO scTemp = BeanCopyUtils.jsonCopyBean(shoppingCart, ShoppingCartDTO.class);
            callAbles.add(new CouponFilterCallable(shoppingCartDomain, activityCacheMap, scTemp, calcExecuter));
        }

        return ThreadPoolUtil.pushTasks(callAbles);
    }

    private Map<String, ActivityCacheDTO> filterActivityCacheMap(Map<String, ActivityCacheDTO> activityCacheMap,
                                                                 Map<String, TPromoCouponCodeUserVO> couponCodeMap, String couponCodes) {

        Map<String, ActivityCacheDTO> filteredMap = new HashMap<>();

        for (String code : Splitter.on(",").trimResults().split(couponCodes)) {

            TPromoCouponCodeUserVO couponCodeUserVo = couponCodeMap.get(code);
            if (null == couponCodeUserVo) {
                continue;
            }

            ActivityCacheDTO activityCacheDTO = activityCacheMap.get(couponCodeUserVo.getActivityCode());
            if (null != activityCacheDTO) {
                filteredMap.put(couponCodeUserVo.getActivityCode(),
                        BeanCopyUtils.jsonCopyBean(activityCacheDTO, ActivityCacheDTO.class));
            }
        }

        return filteredMap;
    }

    private Map<String, TPromoCouponCodeUserVO> couponCodeUserVOList2Map(
            List<TPromoCouponCodeUserVO> couponCodeUserVOs) {

        Map<String, TPromoCouponCodeUserVO> map = new HashMap<>();
        if (CollectionUtils.isEmpty(couponCodeUserVOs)) {
            return map;
        }

        for (TPromoCouponCodeUserVO e : couponCodeUserVOs) {
            map.put(e.getCouponCode(), e);
        }

        return map;
    }

    private CouponInfoDTO setCouponInfoDTO(ErrorCode reason, TPromoCouponCodeUserVO paramVo) {

        ActivityModel activityModel = this.couponActivityComponent.findValidActivity(paramVo.getTenantCode(),
                paramVo.getActivityCode(), null, new Date());
        if (null == activityModel) {
            return null;
        }

        CouponReleaseDomain releaseDomain = promoCouponReleaseService
                .findCouponReleaseByReleaseCode(paramVo.getTenantCode(), paramVo.getReleaseCode());
        // 设置可用时间段
        paramVo.setValidTime(releaseDomain, activityModel.getActivityEnd());
        CouponInfoDTO couponInfoDTO = new CouponInfoDTO();
        BeanUtils.copyProperties(paramVo, couponInfoDTO);
        couponInfoDTO.setCouponStatus(paramVo.getStatus());
        couponInfoDTO.setValidBegin(paramVo.getValidStartTime());
        couponInfoDTO.setValidEnd(paramVo.getValidEndTime());
        couponInfoDTO.setActivityCode(activityModel.getActivityCode());
        couponInfoDTO.setActivityName(activityModel.getActivityName());
        couponInfoDTO.setActivityLabel(activityModel.getActivityLabel());
        couponInfoDTO.setActivityDesc(activityModel.getActivityDesc());
        couponInfoDTO.setActivityShortDesc(activityModel.getActivityRemark());
        couponInfoDTO.setUserlimitMax(activityModel.getUserLimitMax());

        final ConditionAndFace conditionAndFace = functionParamDomain
                .findConditionAndFaceByActivityCode(activityModel.getActivityCode());
        couponInfoDTO.setFaceUnit(conditionAndFace.getFaceUnit());
        couponInfoDTO.setFaceValue(conditionAndFace.getFaceValue());
        couponInfoDTO.setConditionUnit(conditionAndFace.getConditionUnit());
        couponInfoDTO.setConditionValue(conditionAndFace.getConditionValue());

        String templateCode = activityModel.getTemplateCode();
        couponInfoDTO.setRewardType(templateCode.substring(templateCode.length() - 2));
        couponInfoDTO.setIsReward(false);
        couponInfoDTO.setFailedReason(reason);
        return couponInfoDTO;
    }

    /**
     * 根据商品过滤活动
     *
     * @param couponActivityMap 待筛选的活动列表
     * @param promoProducts     商品
     * @return 过滤后的活动
     */
    private Map<String, ActivityCacheDTO> filterByProducts(Map<String, ActivityCacheDTO> couponActivityMap,
                                                           List<ShoppingCartItem> promoProducts) {
        Map<String, ActivityCacheDTO> resultCacheMap = new HashMap<>();
        List<String> productCodes = new ArrayList<>();
        for (ShoppingCartItem shoppingCartItem : promoProducts) {
            productCodes.add(shoppingCartItem.getProductCode());
        }
        List<ProductSkuDetailDTO> productSkuDetailDTOS = productDetailService
                .queryListByActivityCodesAndProductCodes(couponActivityMap.keySet(), productCodes);
        for (ShoppingCartItem shoppingCartItem : promoProducts) {
            ProductCodes productDTO = new ProductCodes();
            productDTO.setCategoryCodes(shoppingCartItem.getCategoryCodes());
            productDTO.setBrandCode(shoppingCartItem.getBrandCode());
            productDTO.setProductCode(shoppingCartItem.getProductCode());
            productDTO.setSkuCode(shoppingCartItem.getSkuCode());
            productDTO.setAttributes(shoppingCartItem.getAttributes());
            productDTO.setCombineSkuCode(shoppingCartItem.getCombineSkuCode());
            productDTO.setSpuAttributes(shoppingCartItem.getSpuAttributes());
            productDTO.setProductTag(shoppingCartItem.getProductTag());
            Map<String, ActivityCacheDTO> productCouponActivityMap = ActivityFilterUtil
                    .filterActivityByActivityType(couponActivityMap, ActivityTypeEnum.COUPON);
            List<String> orgCodes = new ArrayList<>();
            orgCodes.add(shoppingCartItem.getOrgCode());
            productCouponActivityMap = ActivityFilterUtil.filterActivityByOrgCodes(productCouponActivityMap, orgCodes);
            productCouponActivityMap = ActivityFilterUtil.filterActivityByTime(productCouponActivityMap, null);

            productCouponActivityMap = activityCacheDomain.filterActivityByProduct(productCouponActivityMap, productDTO,
                    productSkuDetailDTOS);

            if (MapUtils.isEmpty(productCouponActivityMap)) {
                continue;
            }
            resultCacheMap.putAll(productCouponActivityMap);
        }
        return resultCacheMap;
    }

    @Transactional(readOnly = true)
    public List<CouponActivityProductDTO> searchActivityByProduct(TenantProductDTO tenantProductDTO) {

        // 获取缓存的有效优惠券活动
        Map<String, ActivityCacheDTO> activityMap = activityCacheDomain.getActivityCacheMap(
                tenantProductDTO.getTenantCode(), tenantProductDTO.getLanguage(), 2, ActivityTypeEnum.COUPON);

        List<CouponActivityProductDTO> result = Lists.newArrayList();
        List<ActivityCacheDTO> activityList = Lists.newArrayList();
        ProductCodes product = BeanCopyUtils.jsonCopyBean(tenantProductDTO, ProductCodes.class);

        activityMap = ActivityFilterUtil.filterActivityByActivityType(activityMap, ActivityTypeEnum.COUPON);
        activityMap = ActivityFilterUtil.filterActivityByTime(activityMap, null);

        List<ProductSkuDetailDTO> productSkuDetailDTOS = productDetailService
                .queryListByActivityCodesAndProductCode(activityMap.keySet(), tenantProductDTO.getProductCode());
        activityMap = activityCacheDomain.filterActivityByProduct(activityMap, product, productSkuDetailDTOS);

        // 根据会员过滤活动
        activityMap = ActivityFilterUtil.filterActivityByOrgCodes(activityMap, tenantProductDTO.getOrgCodes());
        activityMap = ActivityFilterUtil.filterActivityByQualifications(activityMap,
                tenantProductDTO.getQualifications());

        // 根据showFlag过滤活动
        Iterator<Map.Entry<String, ActivityCacheDTO>> iterator = activityMap.entrySet().iterator();
        getActivityByShowFlag(activityList, iterator);

        // 获取促销活动券表list
        List<ActivityModel> activityModelList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(activityList)) {
            List<String> activityCodes = Lists.newArrayList();
            getActivityCodesFromCache(activityList, activityModelList, activityCodes);

            if (CollectionUtils.isEmpty(activityModelList)) {
                return Collections.emptyList();
            }

            Map<String, ActivityModel> couponActivityVOMap = activityModelList.stream()
                    .collect(Collectors.toMap(ActivityModel::getActivityCode, a -> a, (k1, k2) -> k1));

            List<CouponReleaseDomain> couponRelease = promoCouponReleaseService
					.queryReleasesByActivityCodes(tenantProductDTO.getTenantCode(), activityCodes, true);
            for (ActivityCacheDTO cacheDTO : activityList) {
                ActivityModel activityModel = cacheDTO.getActivityModel();
                CouponActivityProductDTO activityProductDTO = BeanCopyUtils.jsonCopyBean(activityModel,
                        CouponActivityProductDTO.class);
                activityProductDTO
                        .setActivityPeriod(BeanCopyUtils.jsonCopyBean(cacheDTO.getPeriodModel(), ActivityPeriod.class));
                if (!CollectionUtils.isEmpty(couponRelease)) {
                    getReleaseDomainToInfo(couponRelease, activityModel, activityProductDTO);
                }

                TemplateModel template = this.templateService.getTemplateByCode(activityModel.getTemplateCode());

                activityProductDTO
                        .setUserLimitMax(couponActivityVOMap.get(activityModel.getActivityCode()).getUserLimitMax());
                activityProductDTO
                        .setCouponType(couponActivityVOMap.get(activityModel.getActivityCode()).getCouponType());
                activityProductDTO.setActivitySort(template.getTagCode());
                // 店铺对应的url填充 一个店铺对应一个url
                List<TPromoActivityStoreVO> promoStores = cacheDTO.getPromoChannels();
                List<String> orgCodes = tenantProductDTO.getOrgCodes();
                getActivityUrl(activityProductDTO, promoStores, orgCodes);
                // 模板函数编码的值，0406赠品选择数量限制
                getGiftLimitMax0406(cacheDTO, activityModel, activityProductDTO);

                List<QualificationModel> qualificationModels = cacheDTO.getQualificationModels();
                activityProductDTO.setQualifications(QualificationModel.convert(qualificationModels));
                activityProductDTO.setGiveaways(cacheDTO.getGiveaways());
                activityProductDTO.setRewardType(activityModel.getRewardType());
                activityProductDTO.setPromoScope(
                        TemplateCodeSubstringUtil.subStringTemplateCodeBegin2End4(activityModel.getTemplateCode()));
                getFuncParams(cacheDTO, activityProductDTO);
                result.add(activityProductDTO);
            }
        }
        return result;
    }




    public List<CouponActivityProductDTO> convertCouponActivityProductDTO(List<QueryCouponActivityListByProductListResult> resultList){
        ArrayList<CouponActivityProductDTO> couponActivityProductDTOS = new ArrayList<>();
        //将resultList转换为CouponActivityProductDto类型
        for (QueryCouponActivityListByProductListResult listByProductListResult : resultList) {
            List<QueryCouponActivityListByProductResult> productActivityList = listByProductListResult.getProductActivityList();
            for (QueryCouponActivityListByProductResult productActivity : productActivityList) {
                //spu维度
                CouponActivityProductDTO couponActivityProductDTO = BeanCopyUtils.jsonCopyBean(productActivity,CouponActivityProductDTO.class);
                couponActivityProductDTOS.add(couponActivityProductDTO);
            }
            //sku维度
            List<QueryCouponActivityListByProductListResult.SkuActivity> skuList = listByProductListResult.getSkuList();
            if (CollectionUtils.isEmpty(skuList)){
                continue;
            }
            for (QueryCouponActivityListByProductListResult.SkuActivity skuActivity : skuList) {
                CouponActivityProductDTO couponActivitySkuDTO = BeanCopyUtils.jsonCopyBean(skuActivity,CouponActivityProductDTO.class);
                couponActivityProductDTOS.add(couponActivitySkuDTO);
            }
        }
        return couponActivityProductDTOS;
    }



    public List<QueryCouponActivityListByProductListResult> searchActivityByProductList(QueryCouponActivityListByProductListParam param) {
        log.debug("searchActivityByProductList:Param {}",JSON.toJSONString(param));
        // 获取缓存的有效优惠券活动
        Map<String, ActivityCacheDTO> activityMap = activityCacheDomain.getActivityCacheMap(
                param.getTenantCode(), param.getLanguage(), 2, ActivityTypeEnum.COUPON);
        List<TenantProductDTO> tenantProductDTOs = BeanCopyUtils.jsonCopyList(param.getProductList(), TenantProductDTO.class);

        //条件过滤
        activityMap = ActivityFilterUtil.filterActivityByTime(activityMap, null);
        activityMap = ActivityFilterUtil.filterActivityByQualifications(activityMap,param.getQualifications());

        //SPU 维度查询
        List<ProductSkuDetailDTO> productSkuDetailDTOS = productDetailService
                .queryListByActivityCodesAndProductCodes(activityMap.keySet(), tenantProductDTOs.stream().map(ProductCodes::getProductCode).collect(Collectors.toList()));
        Map<String, List<ProductSkuDetailDTO>> productActivityList = productSkuDetailDTOS.stream().collect(Collectors.groupingBy(ProductSkuDetailDTO::getProductCode));

        Map<String, TemplateModel> templateMap = templateService.queryTemplateAll().stream()
                .collect(Collectors.toMap(TemplateModel::getTemplateCode, x -> x));

        /*List<String> catchActivityCodes = activityMap.values().stream().map(x -> x.getActivityModel().getActivityCode()).collect(Collectors.toList());
        List<CouponReleaseDomain> allCouponRelease = promoCouponReleaseService.queryReleasesByActivityCodes(param.getTenantCode(), catchActivityCodes);
        Map<String, CouponReleaseDomain> couponReleaseDomainMap = allCouponRelease.stream().collect(Collectors.toMap(CouponReleaseDomain::getActivityCode, x -> x));
*/

        //返回值树形结构
        List<QueryCouponActivityListByProductListResult> resultList = new CopyOnWriteArrayList<>();
        List<QueryCouponActivityListByProductListParam.Product> productList = param.getProductList();
        for (QueryCouponActivityListByProductListParam.Product product : productList) {
            QueryCouponActivityListByProductListResult listByProductListResult = new QueryCouponActivityListByProductListResult();
            if (StringUtil.isNotBlank(product.getProductCode())){
                listByProductListResult.setProductCode(product.getProductCode());
            }
            if (StringUtil.isNotBlank(product.getSkuCode())){
                QueryCouponActivityListByProductListResult.SkuActivity skuActivity = new QueryCouponActivityListByProductListResult.SkuActivity();
                skuActivity.setSkuCode(product.getSkuCode());
                listByProductListResult.setSkuList(Lists.newArrayList(skuActivity));
            }

            resultList.add(listByProductListResult);
        }


        HashMap<String, List<QueryCouponActivityListByProductResult>> spuMap = new HashMap<>();
        HashMap<String, List<QueryCouponActivityListByProductResult>> skuMap = new HashMap<>();
        Set<String> allActivityCodes = new HashSet<>(activityMap.keySet());
        Map<String, List<CouponReleaseDomain>> couponReleaseMap = new HashMap<>();
        if (!allActivityCodes.isEmpty()) {
            List<CouponReleaseDomain> allCouponReleases = promoCouponReleaseService.queryReleasesByActivityCodes(param.getTenantCode(), new ArrayList<>(allActivityCodes), true);
            couponReleaseMap = allCouponReleases.stream().collect(Collectors.groupingBy(CouponReleaseDomain::getActivityCode));
        }


        for (TenantProductDTO tenantProductDTO : tenantProductDTOs) {

            Map<String, ActivityCacheDTO> cacheDTOMap = new HashMap<>(activityMap);
            // 获取缓存的有效优惠券活动
            ActivityProductCheckUtil.checkProductCombine(tenantProductDTO);// 商品校验
            if (StringUtil.isNotEmpty(param.getOrgCode())) {
                tenantProductDTO.setOrgCodes(Collections.singletonList(param.getOrgCode()));
            }
            tenantProductDTO.setOrgCodes(CodeHelper.getOrgCodes(tenantProductDTO.getOrgCodes()));

            List<ActivityCacheDTO> activityList = Lists.newArrayList();
            ProductCodes product = BeanCopyUtils.jsonCopyBean(tenantProductDTO, ProductCodes.class);
            cacheDTOMap = activityCacheDomain.filterActivityByProduct(cacheDTOMap, product, productActivityList.getOrDefault(tenantProductDTO.getProductCode(), Lists.newArrayList()));
            // 根据会员过滤活动
            cacheDTOMap = ActivityFilterUtil.filterActivityByOrgCodes(cacheDTOMap, tenantProductDTO.getOrgCodes());

            // 根据showFlag过滤活动
            Iterator<Map.Entry<String, ActivityCacheDTO>> iterator = cacheDTOMap.entrySet().iterator();
            getActivityByShowFlag(activityList, iterator);

            // 获取促销活动券表list
            List<ActivityModel> activityModelList = new ArrayList<>();
            if (!CollectionUtils.isEmpty(activityList)) {
                List<String> activityCodes = Lists.newArrayList();
                getActivityCodesFromCache(activityList, activityModelList, activityCodes);

                if (CollectionUtils.isEmpty(activityModelList)) {
                    return Collections.emptyList();
                }

                Map<String, ActivityModel> couponActivityVOMap = activityModelList.stream()
                        .collect(Collectors.toMap(ActivityModel::getActivityCode, a -> a, (k1, k2) -> k1));
                List<CouponReleaseDomain> couponRelease = Lists.newArrayList();
                //List<CouponReleaseDomain> couponRelease = activityCodes.stream().map(couponReleaseMap::get).flatMap(List::stream).collect(Collectors.toList());
                // 检查activityCodes是否为空，避免NullPointerException
                if (CollectionUtils.isNotEmpty(activityCodes) && null != couponReleaseMap) {
                    couponRelease = activityCodes.stream()
                            .map(couponReleaseMap::get)
                            .filter(CollectionUtils::isNotEmpty) // 过滤掉couponReleaseMap.get()返回null的情况
                            .flatMap(List::stream)
                            .collect(Collectors.toList());
                }

                //List<CouponReleaseDomain> couponRelease = promoCouponReleaseService.queryReleasesByActivityCodes(param.getTenantCode(), activityCodes,true);
                //从couponReleaseDomainMap中批量获取key
                /*List<CouponReleaseDomain> couponReleaseDomainList = Lists.newArrayList();
                for (String activityCode : activityCodes) {
                    couponReleaseDomainList.add(couponReleaseDomainMap.get(activityCode));
                }
                couponReleaseDomainList = activityCodes.stream()
                        .map(activityCode -> couponReleaseDomainMap.computeIfAbsent(activityCode, k -> new CouponReleaseDomain()))
                        .collect(Collectors.toList());*/

                for (ActivityCacheDTO cacheDTO : activityList) {
                    assemblyReturnValue(templateMap, spuMap, skuMap, tenantProductDTO, couponActivityVOMap, couponRelease, cacheDTO);
                }


            }


        }

        resultList.forEach(x -> {
            x.setProductActivityList(BeanCopyUtils.jsonCopyList(spuMap.getOrDefault(x.getProductCode(), new ArrayList<>()), QueryCouponActivityListByProductResult.class));
            if (CollectionUtils.isNotEmpty(x.getSkuList())) {
                x.getSkuList().forEach(y -> y.setSkuActivityList(BeanCopyUtils.jsonCopyList(skuMap.getOrDefault(y.getSkuCode(), new ArrayList<>()), QueryCouponActivityListByProductResult.class)));
            }
        });


        return resultList;
    }

    private void assemblyReturnValue(Map<String, TemplateModel> templateMap, HashMap<String, List<QueryCouponActivityListByProductResult>> spuMap, HashMap<String, List<QueryCouponActivityListByProductResult>> skuMap, TenantProductDTO tenantProductDTO, Map<String, ActivityModel> couponActivityVOMap, List<CouponReleaseDomain> couponRelease, ActivityCacheDTO cacheDTO) {
        ActivityModel activityModel = cacheDTO.getActivityModel();
        CouponActivityProductDTO activityProductDTO = BeanCopyUtils.jsonCopyBean(activityModel,
                CouponActivityProductDTO.class);
        activityProductDTO
                .setActivityPeriod(BeanCopyUtils.jsonCopyBean(cacheDTO.getPeriodModel(), ActivityPeriod.class));
        if (!CollectionUtils.isEmpty(couponRelease)) {
            getReleaseDomainToInfo(couponRelease, activityModel, activityProductDTO);
        }

        TemplateModel template = templateMap.get(activityModel.getTemplateCode());

        activityProductDTO
                .setUserLimitMax(couponActivityVOMap.get(activityModel.getActivityCode()).getUserLimitMax());
        activityProductDTO
                .setCouponType(couponActivityVOMap.get(activityModel.getActivityCode()).getCouponType());
        activityProductDTO.setActivitySort(template.getTagCode());
        // 店铺对应的url填充 一个店铺对应一个url
        List<TPromoActivityStoreVO> promoStores = cacheDTO.getPromoChannels();
        List<String> orgCodes = tenantProductDTO.getOrgCodes();
        getActivityUrl(activityProductDTO, promoStores, orgCodes);
        // 模板函数编码的值，0406赠品选择数量限制
        getGiftLimitMax0406(cacheDTO, activityModel, activityProductDTO);

        List<QualificationModel> qualificationModels = cacheDTO.getQualificationModels();
        activityProductDTO.setQualifications(QualificationModel.convert(qualificationModels));
        activityProductDTO.setGiveaways(cacheDTO.getGiveaways());
        activityProductDTO.setRewardType(activityModel.getRewardType());
        activityProductDTO.setPromoScope(
                TemplateCodeSubstringUtil.subStringTemplateCodeBegin2End4(activityModel.getTemplateCode()));
        getFuncParams(cacheDTO, activityProductDTO);


        //如果为空,就新建,如果不为空,就添加
        if (CollectionUtils.isEmpty(spuMap.get(tenantProductDTO.getProductCode()))) {
            List<CouponActivityProductDTO> couponActivityProductDTOS = Lists.newArrayList();
            couponActivityProductDTOS.add(activityProductDTO);
            spuMap.put(tenantProductDTO.getProductCode(),BeanCopyUtils.jsonCopyList(couponActivityProductDTOS, QueryCouponActivityListByProductResult.class));
        } else {
            spuMap.get(tenantProductDTO.getProductCode()).add(BeanCopyUtils.jsonCopyBean(activityProductDTO, QueryCouponActivityListByProductResult.class));
        }

        //如果为空,就新建,如果不为空,就添加
        if (CollectionUtils.isEmpty(skuMap.get(tenantProductDTO.getSkuCode()))) {
            List<CouponActivityProductDTO> couponActivityProductDTOS = Lists.newArrayList();
            couponActivityProductDTOS.add(activityProductDTO);
            skuMap.put(tenantProductDTO.getSkuCode(),BeanCopyUtils.jsonCopyList(couponActivityProductDTOS, QueryCouponActivityListByProductResult.class));
        } else {
            skuMap.get(tenantProductDTO.getSkuCode()).add(BeanCopyUtils.jsonCopyBean(activityProductDTO, QueryCouponActivityListByProductResult.class));
        }
    }







    public void getFuncParams(ActivityCacheDTO cacheDTO, CouponActivityProductDTO activityProductDTO) {
        for (FunctionParamModel vo : cacheDTO.getPromoFuncParams()) {
            if (FuncTypeEnum.PARAM.equalsCode(vo.getFunctionType())
                    && StringUtil.isBlank(activityProductDTO.getConditionUnit())) {
                // 填充条件
                activityProductDTO.setConditionUnit(vo.getParamUnit());
                activityProductDTO
                        .setConditionValue(vo.getParamValue() != null ? new BigDecimal(vo.getParamValue()) : null);
            }
            if (FuncTypeEnum.IncentiveEnum.REDUCE_AMOUNT.equalsCode(vo.getFunctionCode())
                    && FunctionParamTypeEnum.NUMBER.equalsCode(vo.getParamType())) {
                activityProductDTO.setFaceValue(new BigDecimal(vo.getParamValue()));
                activityProductDTO.setFaceUnit(FaceUnitEnum.MONEY.code());
            } else if (FuncTypeEnum.IncentiveEnum.DISCOUNT.equalsCode(vo.getFunctionCode())
                    && FunctionParamTypeEnum.NUMBER.equalsCode(vo.getParamType())) {
                activityProductDTO.setFaceUnit(FaceUnitEnum.DISCOUNT.code());
                activityProductDTO.setFaceValue(new BigDecimal(vo.getParamValue()).multiply(new BigDecimal(10)));
            }
            if (StringUtil.isNotBlank(activityProductDTO.getFaceUnit())) {
                break;
            }
        }
    }

    public void getGiftLimitMax0406(ActivityCacheDTO cacheDTO, ActivityModel activityModel,
                                    CouponActivityProductDTO activityProductDTO) {
        if (FuncTypeEnum.IncentiveEnum.GIVEAWAY.equalsCode(activityModel.getTemplateCode().substring(12, 16))) {
            List<FunctionParamModel> promoFuncParams = cacheDTO.getPromoFuncParams();
            if (!CollectionUtils.isEmpty(promoFuncParams)) {
                promoFuncParams.forEach(promoFunc -> {// 赠品活动只能有一个层级
                    if (FuncTypeEnum.INCENTIVE.equalsCode(promoFunc.getFunctionType())
                            && FuncTypeEnum.IncentiveEnum.GIVEAWAY.equalsCode(promoFunc.getFunctionCode())) {// 04
                        // 0406
                        String paramValue = promoFunc.getParamValue();
                        activityProductDTO.setGiftLimitMax(paramValue);
                    }
                });
            }
        }
    }

    public void getActivityUrl(CouponActivityProductDTO activityProductDTO, List<TPromoActivityStoreVO> promoStores,
                               List<String> orgCodes) {
        if (CollectionUtils.isNotEmpty(promoStores) && CollectionUtils.isNotEmpty(orgCodes)) {
            for (TPromoActivityStoreVO activityStoreVO : promoStores) {
                if (orgCodes.contains(activityStoreVO.getOrgCode())) {
                    activityProductDTO.setActivityUrl(activityStoreVO.getUrl());
                }
            }
        }
    }

    public void getReleaseDomainToInfo(List<CouponReleaseDomain> couponRelease, ActivityModel activityModel,
                                       CouponActivityProductDTO activityProductDTO) {
        List<CouponReleaseDomain> collect = couponRelease.stream().filter(
                x -> x.getActivityCode().equals(activityModel.getActivityCode()) && x.getReleaseStatus().equals("02"))
                .collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(collect)) {
            List<CouponReleaseTimeOutDTO> couponReleaseTimeOutDTOS = new ArrayList<>();
            for (CouponReleaseDomain releaseVO : collect) {
                CouponReleaseTimeOutDTO time = BeanCopyUtils.jsonCopyBean(releaseVO, CouponReleaseTimeOutDTO.class);
                if (StringUtils.isNotBlank(releaseVO.getValidStartTime())
                        && StringUtils.isNotBlank(releaseVO.getValidEndTime())) {
                    time.setValidEndTime(releaseVO.getValidEndTime());
                    time.setValidStartTime(releaseVO.getValidStartTime());
                } else {
                    time.setValidDays(releaseVO.getValidDays());
                }
                time.setCreateTime(releaseVO.getCreateTime());

                if (null != releaseVO.getInventory() && releaseVO.getInventory().intValue() < 0){
                    releaseVO.setInventory(0);
                }
                time.setInventory(releaseVO.getInventory());
                couponReleaseTimeOutDTOS.add(time);
            }
            activityProductDTO.setCouponValidTime(couponReleaseTimeOutDTOS);
        }
    }

    public void getActivityCodesFromCache(List<ActivityCacheDTO> activityList, List<ActivityModel> activityModelList,
                                          List<String> activityCodes) {
        for (ActivityCacheDTO cacheDTO : activityList) {
            activityCodes.add(cacheDTO.getActivityModel().getActivityCode());
            activityModelList.add(cacheDTO.getActivityModel());
        }
    }

    public void getActivityByShowFlag(List<ActivityCacheDTO> activityList,
                                      Iterator<Entry<String, ActivityCacheDTO>> iterator) {
        while (iterator.hasNext()) {
            ActivityCacheDTO value = iterator.next().getValue();
            if (value.getActivityModel().getShowFlag() == 1) {
                activityList.add(value);
            } else {
                iterator.remove();
            }
        }
    }

    @Transactional
    public void updateCouponCode(UpdateCouponParam param) {

        List<TPromoCouponInnerCodeVO> couponInnerCodeVOS = new ArrayList<>();

        List<UpdateUserCouponParam> couponParams = param.getCouponParams();

        for (UpdateUserCouponParam couponParam : couponParams) {
            TPromoCouponInnerCodeVO vo = BeanCopyUtils.jsonCopyBean(couponParam, TPromoCouponInnerCodeVO.class);
            vo.setTenantCode(param.getTenantCode());
            couponInnerCodeVOS.add(vo);
        }

        couponInnerCodeService.updateBatchInnerCodeByCouponCodes(couponInnerCodeVOS);

        List<TPromoCouponCodeUserVO> userVOList = BeanCopyUtils.jsonCopyList(couponInnerCodeVOS, TPromoCouponCodeUserVO.class);
        couponCodeUserService.updateCouponUserByCode(userVOList);

        Map<String, String> couponMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(userVOList)){
            List<String> collect = userVOList.stream().map(TPromoCouponCodeUserVO::getCouponCode).collect(Collectors.toList());
            List<TPromoCouponCodeUserVO> userVOS = couponCodeUserService.queryUserCouponInfo(param.getTenantCode(), collect);
            Map<String, String> collect1 = userVOS.stream().collect(Collectors.toMap(TPromoCouponCodeUserVO::getCouponCode, TPromoCouponCodeUserVO::getActivityCode));
            couponMap.putAll(collect1);
        }

        for (TPromoCouponCodeUserVO couponCodeUserVO : userVOList) {

            String activityCode = couponMap.get(couponCodeUserVO.getCouponCode());
            String couponKey = Constants.PROMOTION_COUPON_USER + ":TENANTCODE=" + param.getTenantCode()
                    + ":ACTIVITYCODE=" + activityCode + ":USERCODE=" + couponCodeUserVO.getUserCode() + ":COUPONCODE=" + couponCodeUserVO.getCouponCode();
            String validEndTime = couponCodeUserVO.getValidEndTime();
            Date date = com.gtech.commons.utils.DateUtil.parseDate(validEndTime, DateUtil.FORMAT_YYYYMMDDHHMISS_14);
            long expireTime = date.getTime() / 1000;
            long currentTime = System.currentTimeMillis() / 1000;
            if (CouponStatusEnum.GRANTED.code().equals(couponCodeUserVO.getStatus())){
                redisClient.setStringValue(couponKey, JSON.toJSONString(couponCodeUserVO), expireTime - currentTime, TimeUnit.SECONDS);
            }
        }

    }

    @Transactional
    public void frozenCouponCode(FrozenCouponCodeInDTO frozenDTO) {

        //校验是否过期
        int i = couponInnerCodeService.frozenFindInnerCode(frozenDTO.getTenantCode(), frozenDTO.getCouponCode());

        Check.check(i>0, CouponCodeUserChecker.EXPIRE_TIME);

        couponInnerCodeService.frozenInnerCode(frozenDTO.getTenantCode(), frozenDTO.getCouponCode(),
                frozenDTO.getFrozenStatus(), frozenDTO.getLogicDelete());
        couponCodeUserService.frozenCouponCodeUser(frozenDTO.getTenantCode(), frozenDTO.getCouponCode(),
                frozenDTO.getFrozenStatus(), frozenDTO.getLogicDelete());
    }

    public List<ShoppingCartOutDTO> chooseCouponsByCart(ShoppingCartDTO shoppingCart) {
        List<String> opsTypeList = new ArrayList<>();
        opsTypeList.add(OpsTypeEnum.OPS_202.code());
        TPromoCouponCodeUserVO couponCodeUserVO = new TPromoCouponCodeUserVO();
        couponCodeUserVO.setUserCode(shoppingCart.getUserCode());
        couponCodeUserVO.setTenantCode(shoppingCart.getTenantCode());
        couponCodeUserVO.setStatus("1");
        couponCodeUserVO.setTakeLabel(null);

        PageData<CouponDomain> userCouponByUserId = couponCodeUserService.getUserCouponByUserId(couponCodeUserVO, new RequestPage(1, 200),
                opsTypeList, null, null, 0);
        if (userCouponByUserId.getTotal() == 0) {
            return Collections.emptyList();
        }
        Map<String, List<CouponDomain>> collect = userCouponByUserId.getList().stream()
                .filter(couponDomain -> couponDomain.getFrozenStatus().equals(CouponFrozenStatusEnum.UN_FROZEN.code()))
                .collect(Collectors.groupingBy(CouponDomain::getActivityCode));
        // 优惠券过多，请手动指定
        Check.check(collect.size() > 5, CouponActivityChecker.TO_MANY_COUPON);
        List<String> coupons = new ArrayList<>();
        getCouponCodes(collect, coupons);
        Map<String, List<CouponDomain>> couponMap = userCouponByUserId.getList().stream()
                .filter(couponDomain -> couponDomain.getFrozenStatus().equals(CouponFrozenStatusEnum.UN_FROZEN.code()))
                .collect(Collectors.groupingBy(CouponDomain::getCouponCode));

        List<String> allSubCouponList = ListUtils.getAllSubList(coupons);
        List<CouponChooseCallable> callAbles = new ArrayList<>();
        getCallAbles(shoppingCart, allSubCouponList, callAbles);
        List<Future<List<ShoppingCartOutDTO>>> futures = ThreadPoolUtil.pushTasks(callAbles);
        List<ChooseCouponByCartDTO> chooseCouponByCartDTOS = new ArrayList<>();
        for (Future<List<ShoppingCartOutDTO>> future : futures) {
            if (future.isDone()) {
                List<ShoppingCartOutDTO> shoppingCartOutDTOS = null;
                try {
                    shoppingCartOutDTOS = future.get();
                } catch (InterruptedException | ExecutionException e) {
                    log.error("chooseCouponsByCart future.get() error", e);
                    Thread.currentThread().interrupt();
                }
                if (CollectionUtils.isEmpty(shoppingCartOutDTOS)) {
                    continue;
                }
                ChooseCouponByCartDTO chooseCouponByCartDTO = new ChooseCouponByCartDTO();
                BigDecimal discountAmount = BigDecimal.ZERO;
                List<ChooseCouponByCartCouponInfoDTO> couponByCartCouponInfoDTOS = new ArrayList<>();
                discountAmount = getBigDecimal(couponMap, shoppingCartOutDTOS, discountAmount,
                        couponByCartCouponInfoDTOS);
                chooseCouponByCartDTO.setDiscountAmount(discountAmount);
                chooseCouponByCartDTO.setCoupons(couponByCartCouponInfoDTOS);
                chooseCouponByCartDTO.setShoppingCartOutDTOS(shoppingCartOutDTOS);
                chooseCouponByCartDTOS.add(chooseCouponByCartDTO);
            }
        }
        if (CollectionUtils.isEmpty(chooseCouponByCartDTOS)) {
            return Collections.emptyList();
        }
        Collections.sort(chooseCouponByCartDTOS);
        return chooseCouponByCartDTOS.get(0).getShoppingCartOutDTOS();
    }

    public void getCallAbles(ShoppingCartDTO shoppingCart, List<String> allSubCouponList,
                             List<CouponChooseCallable> callAbles) {
        for (String s : allSubCouponList) {
            ShoppingCartDTO shoppingCartTemp = BeanCopyUtils.jsonCopyBean(shoppingCart, ShoppingCartDTO.class);
            shoppingCartTemp.setCouponCodes(s);
            callAbles.add(
                    new CouponChooseCallable(shoppingCartDomain, activityCacheDomain, shoppingCartTemp, calcExecuter));
        }
    }

    public BigDecimal getBigDecimal(Map<String, List<CouponDomain>> couponMap,
                                    List<ShoppingCartOutDTO> shoppingCartOutDTOS, BigDecimal discountAmount,
                                    List<ChooseCouponByCartCouponInfoDTO> couponByCartCouponInfoDTOS) {
        for (ShoppingCartOutDTO shoppingCartOutDTO : shoppingCartOutDTOS) {
            if (shoppingCartOutDTO.isEffectiveFlag()) {
                discountAmount = discountAmount.add(shoppingCartOutDTO.getPromoRewardAmount());
                if (StringUtil.isNotBlank(shoppingCartOutDTO.getCouponCode())) {
                    ChooseCouponByCartCouponInfoDTO e = new ChooseCouponByCartCouponInfoDTO();
                    e.setCouponCode(shoppingCartOutDTO.getCouponCode());
                    e.setValidEndTime(couponMap.get(shoppingCartOutDTO.getCouponCode()).get(0).getValidEndTime());
                    couponByCartCouponInfoDTOS.add(e);
                }
            }
        }
        return discountAmount;
    }

    public void getCouponCodes(Map<String, List<CouponDomain>> collect, List<String> coupons) {
        for (Entry<String, List<CouponDomain>> stringListEntry : collect.entrySet()) {
            List<CouponDomain> value = stringListEntry.getValue();
            value.sort(Comparator.comparing(CouponDomain::getValidEndTime).reversed());
            CouponDomain couponDomain = value.get(0);
            String couponCode = couponDomain.getCouponCode();
            coupons.add(couponCode);
        }
    }

}
