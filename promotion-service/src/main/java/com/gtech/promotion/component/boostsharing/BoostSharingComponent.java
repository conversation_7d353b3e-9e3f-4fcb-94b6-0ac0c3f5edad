package com.gtech.promotion.component.boostsharing;

import cn.hutool.core.map.MapUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.rocketmq.common.message.Message;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.gtech.basic.masterdata.response.JsonResult;
import com.gtech.commons.exception.Exceptions;
import com.gtech.commons.page.PageData;
import com.gtech.commons.redis.GTechCodeGenerator;
import com.gtech.commons.result.PageResult;
import com.gtech.commons.result.Result;
import com.gtech.commons.utils.BeanCopyUtils;
import com.gtech.commons.utils.DateUtil;
import com.gtech.commons.utils.StringUtil;
import com.gtech.member.web.vo.bean.MemberTagBean;
import com.gtech.member.web.vo.param.GetMemberProfileParam;
import com.gtech.member.web.vo.result.GetMemberProfileResult;
import com.gtech.promotion.checker.activity.TPromoProductChecker;
import com.gtech.promotion.checker.coupon.CouponAllocateChecker;
import com.gtech.promotion.checker.marketing.MarketingChecker;
import com.gtech.promotion.code.activity.ActivityStatusEnum;
import com.gtech.promotion.code.coupon.CouponReleaseSourceEnum;
import com.gtech.promotion.code.coupon.ReleaseTimeSameActivityEnum;
import com.gtech.promotion.code.coupon.ReleaseTypeEnum;
import com.gtech.promotion.code.coupon.TakeLabelEnum;
import com.gtech.promotion.code.marketing.*;
import com.gtech.promotion.component.coupon.CouponCodeUserComponent;
import com.gtech.promotion.component.coupon.CouponReleaseComponent;
import com.gtech.promotion.component.flashsale.FlashSaleComponent;
import com.gtech.promotion.component.marketing.MarketingCacheComponent;
import com.gtech.promotion.component.marketing.TicketComponent;
import com.gtech.promotion.dao.entity.marketing.HelpRecordEntity;
import com.gtech.promotion.dao.entity.marketing.RightOfFirstRefusalEntity;
import com.gtech.promotion.dao.entity.marketing.SharingRecordEntity;
import com.gtech.promotion.dao.model.activity.ActivityModel;
import com.gtech.promotion.dao.model.activity.QualificationModel;
import com.gtech.promotion.dao.model.marketing.*;
import com.gtech.promotion.dao.model.marketing.flashsale.CacheFlashSaleModel;
import com.gtech.promotion.dao.model.marketing.flashsale.FlashSaleQualificationModel;
import com.gtech.promotion.dao.model.marketing.flashsale.FlashSaleStoreModel;
import com.gtech.promotion.domain.coupon.CouponReleaseDomain;
import com.gtech.promotion.domain.marketing.TicketReleaseDomain;
import com.gtech.promotion.domain.marketing.TicketSendDomain;
import com.gtech.promotion.dto.boostsharing.ExportBoostSharingDetailDto;
import com.gtech.promotion.dto.boostsharing.ExportBoostSharingTotalDto;
import com.gtech.promotion.dto.boostsharing.FilterNoRightOfFirstRefusalDto;
import com.gtech.promotion.dto.in.activity.ShoppingCartItem;
import com.gtech.promotion.dto.in.coupon.ReleaseCouponInDTO;
import com.gtech.promotion.dto.in.flashsale.*;
import com.gtech.promotion.exception.Check;
import com.gtech.promotion.exception.ErrorCodes;
import com.gtech.promotion.feign.MasterDataFeignClient;
import com.gtech.promotion.feign.MemberFeignClient;
import com.gtech.promotion.helper.RedisLock;
import com.gtech.promotion.mq.MQProducerFactory;
import com.gtech.promotion.page.RequestPage;
import com.gtech.promotion.pojo.MqEnums;
import com.gtech.promotion.service.activity.ActivityService;
import com.gtech.promotion.service.flashsale.FlashSaleQualificationService;
import com.gtech.promotion.service.flashsale.FlashSaleStoreService;
import com.gtech.promotion.service.marketing.*;
import com.gtech.promotion.utils.Constants;
import com.gtech.promotion.utils.MarketingFilterUtil;
import com.gtech.promotion.vo.bean.ProductCodes;
import com.gtech.promotion.vo.bean.Qualification;
import com.gtech.promotion.vo.param.coupon.SendCouponToUserParam;
import com.gtech.promotion.vo.param.marketing.TicketReleaseQueryParam;
import com.gtech.promotion.vo.param.marketing.flashsale.HelpRecordResult;
import com.gtech.promotion.vo.result.boostsharding.ExportBoostSharingDetailResult;
import com.gtech.promotion.vo.result.boostsharding.ExportBoostSharingTotalResult;
import com.gtech.promotion.vo.result.flashsale.BoostSharingRewardsResult;
import com.gtech.promotion.vo.result.flashsale.CreateHelpRecordResult;
import com.gtech.promotion.vo.result.flashsale.QueryRightOfFirstRefusalResult;
import com.gtech.promotion.vo.result.flashsale.QuerySharingInformationResult;
import com.gtech.promotion.vo.result.marketing.TicketReleaseQueryResult;
import com.gtech.promotion.vo.result.marketing.TicketSendResult;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Component
public class BoostSharingComponent {


   @Autowired
   private MarketingService marketingService;

    @Autowired
    private SharingRecordService sharingRecordService;

    @Autowired
    private HelpRecordService helpRecordService;

    @Autowired
    private BoostSharingService boostSharingService;

    @Autowired
    private RightOfFirstRefusalService rightOfFirstRefusalService;

    @Autowired
    private CouponCodeUserComponent couponCodeUserComponent;

    @Autowired
    private CouponReleaseComponent couponReleaseComponent;




    @Autowired
    private GTechCodeGenerator codeGenerator;
    @Autowired
    private TicketComponent ticketComponent;

    @Autowired
    private FlashSaleQualificationService flashSaleQualificationService;

    @Autowired
    private FlashSaleStoreService flashSaleStoreService;

    @Autowired
    private MemberFeignClient memberFeignClient;

    @Autowired
    private MarketingCacheComponent marketingCacheComponent;

    @Autowired
    private FlashSaleComponent flashSaleComponent;

    @Autowired
    private ActivityService activityService;


    @Autowired
    private MasterDataFeignClient masterDataFeignClient;

    @Autowired
    private RedisLock redisLock;

    public static final String BOOST_SHARDING_HELP = "BoostSharingHelp";
    public static final String BOOST_SHARDING_SHARDING = "BoostSharingSharing";
    public static final String RIGHT_OF_FIRST_REFUSAL = "RightOfFirstRefusal";
    public static final String APP_KEY =  Constants.APP_KEY;
    public static final String SHARING =  "Sharing";
    public static final String HELP =  "Help";
    public static final String TEMPLATE_EXPR = "BS[D:yyyyMMddHHmmss][SM:%06d]";


    /**
     * 创建分享统计记录
     */
    public String createBoostSharing(SharingRecordDto dto) {
        //校验活动
        BoostSharingModel boostSharingModel = checkActivityAndSharing(dto.getDomainCode(), dto.getTenantCode(), dto.getOrgCode(), dto.getActivityCode(),dto.getSharingMemberCode(),SHARING);


        SharingRecordModel model = BeanCopyUtils.jsonCopyBean(dto, SharingRecordModel.class);
        String code = codeGenerator.generateCode(dto.getTenantCode(), BOOST_SHARDING_SHARDING, TEMPLATE_EXPR, 1L);
        model.setNumberOfBoostSharing(boostSharingModel.getNumberOfBoostSharing());
        model.setActivityStatus(BoostSharingEnum.PROCESSING.code());
        model.setSharingRecordCode(code);
        sharingRecordService.insert(model);
        return code;

    }

    public BoostSharingModel checkActivityAndSharing(String domainCode,String tenantCode,String orgCode,String activityCode,String memberCode,String type) {
        //活动校验
        MarketingModel byActivityCode = marketingService.findByActivityCode(activityCode);
        Check.check(null == byActivityCode, MarketingChecker.THE_ACTIVITY_INFORMATION_ERROR);
        Check.check(!tenantCode.equals(Objects.requireNonNull(byActivityCode).getTenantCode()), MarketingChecker.THE_ACTIVITY_INFORMATION_ERROR);
        Check.check(!byActivityCode.getDomainCode().equals(domainCode), MarketingChecker.THE_ACTIVITY_INFORMATION_ERROR);
        Check.check(!byActivityCode.getOrgCode().equals(orgCode) && !byActivityCode.getOrgCode().equals("default"), MarketingChecker.THE_ACTIVITY_INFORMATION_ERROR);
        Check.check(DateUtil.parseDate(byActivityCode.getActivityEnd(), DateUtil.FORMAT_YYYYMMDDHHMISS_14).getTime() < System.currentTimeMillis(), CouponAllocateChecker.END_TIME);
        Check.check(DateUtil.parseDate(byActivityCode.getActivityBegin(), DateUtil.FORMAT_YYYYMMDDHHMISS_14).getTime() >= System.currentTimeMillis(), CouponAllocateChecker.START_TIME);



        //活动状态
        Check.check(!byActivityCode.getActivityStatus().equals(ActivityStatusEnum.EFFECTIVE.code()), MarketingChecker.ACTIVITY_STATUS_IS_ABNORMAL);


        GetMemberProfileResult memberInfoByMemberCode = getMemberInfoByMemberCode(domainCode, tenantCode, memberCode);
        Check.check(null == memberInfoByMemberCode, MarketingChecker.TICKET_SEND_NO_MEMBER_FIND);
        if (null != memberInfoByMemberCode && type.equals(SHARING)) {
            List<MemberTagBean> tags = new ArrayList<>();
            if (null != memberInfoByMemberCode.getTags()) {
                tags = memberInfoByMemberCode.getTags();
            }
            Map<String, List<String>> qualification = getQualification(memberInfoByMemberCode,tags , orgCode);
            checkTheConditionsOfPersonnelAndStores(orgCode, activityCode,qualification);
        }


        BoostSharingModel boostSharingModel = boostSharingService.findBoostShardingInfo(domainCode, tenantCode, "", activityCode);
        Check.check(null == boostSharingModel, MarketingChecker.THE_SHARING_RECORD_DOES_NOT_EXIST);
        return boostSharingModel;
    }


    public GetMemberProfileResult getMemberInfoByMemberCode(String domainCode,String tenantCode,String memberCode){
        GetMemberProfileParam param = new GetMemberProfileParam();
        param.setMemberCode(memberCode);
        param.setDomainCode(domainCode);
        param.setTenantCode(tenantCode);
        Result<GetMemberProfileResult> memberProfile =
                memberFeignClient.getMemberProfile(param);
       return memberProfile.getData();
    }




    public Map<String, List<String>> getQualification( GetMemberProfileResult memberInfo,
                                                       List<MemberTagBean> memberTagsDTOList,String orgCode) {
        Map<String, List<String>> qualificationMap = new HashMap<>();
        if (null != memberInfo && StringUtils.isNotBlank(memberInfo.getMemberCode())) {
            qualificationMap.put(Constants.MEMBER_ORG_CODE, Collections.singletonList(orgCode));
            qualificationMap.put(Constants.IS_MEMBER, Collections.singletonList(Constants.STRING_ONE));
        } else {
            qualificationMap.put(Constants.IS_MEMBER, Collections.singletonList(Constants.STRING_ZERO));
        }
        if (!org.springframework.util.CollectionUtils.isEmpty(memberTagsDTOList)) {
            List<String> memberTagCodes = new ArrayList<>();
            memberTagsDTOList.forEach(x -> memberTagCodes.add(x.getTagCode()));
            qualificationMap.put(Constants.MEMBER_TAG_CODE, memberTagCodes);
        }
        return qualificationMap;
    }


    public void checkTheConditionsOfPersonnelAndStores(String orgCode, String activityCode, Map<String, List<String>> qualifications) {
        List<FlashSaleQualificationModel> qualificationModels = flashSaleQualificationService.findListByActivityCode(activityCode);
        List<FlashSaleStoreModel> saleStoreModels = flashSaleStoreService.findListByActivityCode(activityCode);



        Map<String, CacheFlashSaleModel> flashSaleCacheMap = new HashMap<>();
        CacheFlashSaleModel flashSaleModel = new CacheFlashSaleModel();
        flashSaleModel.setQualifications(qualificationModels);
        flashSaleModel.setStores(saleStoreModels);
        flashSaleCacheMap.put(activityCode,flashSaleModel);
        if (CollectionUtils.isNotEmpty(qualificationModels)){
            flashSaleCacheMap =  MarketingFilterUtil.filterActivityByQualifications(flashSaleCacheMap,qualifications);
        }
        if (CollectionUtils.isNotEmpty(saleStoreModels)) {
            flashSaleCacheMap = MarketingFilterUtil.filterActivityByOrgCodes(flashSaleCacheMap, Lists.newArrayList(orgCode));
        }
        Check.check(null == flashSaleCacheMap.get(activityCode) , MarketingChecker.ACTIVITY_CONDITIONS_DO_NOT_MATCH);
    }


    @Transactional
    public CreateHelpRecordResult createHelpRecord(HelpRecordDto dto) {

        //校验活动是否存在
        BoostSharingModel boostSharingModel = checkActivityAndSharing(dto.getDomainCode(), dto.getTenantCode(), dto.getOrgCode(), dto.getActivityCode(),dto.getHelpMemberCode(),HELP);

        Check.check(dto.getSharingMemberCode().equals(dto.getHelpMemberCode()),MarketingChecker.SHARERS_AND_FACILITATORS_CANNOT_BE_THE_SAME);

        SharingRecordModel sharingRecordModel = sharingRecordService.findBysharingRecordCode(dto.getSharingRecordCode(),dto.getSharingMemberCode());
        Check.check(null == sharingRecordModel, MarketingChecker.THE_SHARING_RECORD_DOES_NOT_EXIST);
        //校验是否是相同活动
        assert sharingRecordModel != null;
        Check.check(!dto.getActivityCode().equals(sharingRecordModel.getActivityCode()), MarketingChecker.THE_ACTIVITY_INFORMATION_ERROR);

        //检查该分享助力是否已经晚场
        Check.check(sharingRecordModel.getActivityStatus().equals(BoostSharingEnum.FINISH.code()), MarketingChecker.SHARING_HAS_BEEN_COMPLETED);


        //检查是否已经参加过活动
        List<HelpRecordEntity> helpRecordEntity = helpRecordService.checkMemberHelpRecord(dto.getSharingRecordCode(), dto.getHelpMemberCode());
        if (CollectionUtils.isNotEmpty(helpRecordEntity)) {
            Check.check(true, MarketingChecker.THE_USER_HAS_ALREADY_PARTICIPATED_IN_THIS_ACTIVITY);
        }
        CreateHelpRecordResult result;
        result = insertBoostSharingRecord(dto, boostSharingModel, sharingRecordModel);
        return result;
    }

    @Transactional
    public CreateHelpRecordResult insertBoostSharingRecord(HelpRecordDto dto, BoostSharingModel boostSharingModel, SharingRecordModel sharingRecordModel) {

        BoostSharingEnum activityStatus = BoostSharingEnum.PROCESSING;
        BoostSharingHelpStatusEnum helpStatus = BoostSharingHelpStatusEnum.SUCCESS;
        BoostSharingTypeEnum type = BoostSharingTypeEnum.getEnumByCode(boostSharingModel.getBoostSharingType());
        BoostSharingRewardEnum rewardType = BoostSharingRewardEnum.COUPON_FOR_HELPERS_ONLY;

        long numberOfPeopleHelping = Long.parseLong(sharingRecordModel.getNumberOfPeopleWhoHaveHelped()) + 1;


        //先修改数据库,判断能否修改成功,如果不能修改成功,则直接返回
        int i = modifyTheNumberOfAssists(dto, boostSharingModel, sharingRecordModel, numberOfPeopleHelping);

        if (i < 1){
            helpStatus = BoostSharingHelpStatusEnum.FAILURE;
            return assembleHelpRecordReturnValue(dto,"",activityStatus,helpStatus, new BoostSharingRewardsResult());
        }

        //只有会员第一次助力才会增加助力人数,并且助力人数不能超过活动设置的助力人数
        if (numberOfPeopleHelping == Long.parseLong(sharingRecordModel.getNumberOfBoostSharing())){
            activityStatus = BoostSharingEnum.FINISH;
            sharingRecordModel.setActivityStatus(activityStatus.code());
            rewardType = BoostSharingRewardEnum.defaultReward(type);
        }
        dto.setHelpRecordStatus(helpStatus.code());
        String helpRecordCode = insertHelpRecord(dto);
        updateSharingRecordStatus(sharingRecordModel);
        //发放奖励
        BoostSharingRewardsResult boostSharingRewardsResult = distributeRewards(dto, rewardType, boostSharingModel);
        return assembleHelpRecordReturnValue(dto,helpRecordCode,activityStatus,helpStatus,boostSharingRewardsResult);
    }


    public int modifyTheNumberOfAssists(HelpRecordDto dto, BoostSharingModel boostSharingModel, SharingRecordModel sharingRecordModel, long numberOfPeopleHelping) {
        return increaseTheNumberOfPeopleHelping(dto, boostSharingModel, sharingRecordModel, numberOfPeopleHelping);

    }


    private int increaseTheNumberOfPeopleHelping(HelpRecordDto dto, BoostSharingModel boostSharingModel, SharingRecordModel sharingRecordModel, long numberOfPeopleHelping) {
        SharingRecordModel increaseModel = new SharingRecordModel();
        increaseModel.setSharingRecordCode(sharingRecordModel.getSharingRecordCode());
        increaseModel.setNumberOfPeopleWhoHaveHelped(String.valueOf(numberOfPeopleHelping));
        increaseModel.setUpdateTime(sharingRecordModel.getUpdateTime());

        return sharingRecordService.increaseTheNumberOfPeopleHelping(dto, boostSharingModel, increaseModel);
    }

    public CreateHelpRecordResult assembleHelpRecordReturnValue(HelpRecordDto dto, String helpRecordCode, BoostSharingEnum status, BoostSharingHelpStatusEnum helpStatus, BoostSharingRewardsResult boostSharingRewardsResult) {
        CreateHelpRecordResult result = new CreateHelpRecordResult();
        result.setDomainCode(dto.getDomainCode());
        result.setTenantCode(dto.getTenantCode());
        result.setActivityCode(dto.getActivityCode());
        result.setOrgCode(dto.getOrgCode());
        result.setSharingRecordStatus(status.code());
        result.setHelpRecordStatus(helpStatus.code());
        result.setHelpRecordCode(helpRecordCode);
        result.setSharingMemberCode(dto.getSharingMemberCode());
        result.setHelpMemberCode(dto.getHelpMemberCode());
        result.setSharingRecordCode(dto.getSharingRecordCode());
        result.setBoostSharingRewardsResult(boostSharingRewardsResult);
        return result;
    }


    /**
     * 创建助力记录
     */
    public String insertHelpRecord(HelpRecordDto dto){
        HelpRecordModel helpRecordModel = BeanCopyUtils.jsonCopyBean(dto, HelpRecordModel.class);
        String helpRecordCode = codeGenerator.generateCode(dto.getTenantCode(), BOOST_SHARDING_HELP, TEMPLATE_EXPR, 1L);
        helpRecordModel.setHelpRecordCode(helpRecordCode);
        helpRecordService.insert(helpRecordModel);
        return helpRecordModel.getHelpRecordCode();
    }


    /**
     * 修改分享记录状态
     */
    public void updateSharingRecordStatus(SharingRecordModel model){
        sharingRecordService.updateSharingRecord(model);
    }


    /**
     * 发放奖励
     */
    @Transactional
    public BoostSharingRewardsResult distributeRewards(HelpRecordDto dto, BoostSharingRewardEnum rewardType,BoostSharingModel boostSharingModel){
        if (StringUtil.isNotEmpty(boostSharingModel.getLuckyDrawActivityCode())
        && rewardType.equalsCode(BoostSharingRewardEnum.SHARER_RIGHT_OF_FIRST_REFUSAL.code())){
            rewardType = BoostSharingRewardEnum.SHARER_LUCKY_DRAW_BONUS;
        }
        String helpToGetCouponActivityCode = boostSharingModel.getHelpToGetCouponActivityCode();
        String helpMemberCode = dto.getHelpMemberCode();
        //助力人发券
        if (StringUtil.isNotBlank(helpToGetCouponActivityCode)){
            this.sendCoupon(dto, helpToGetCouponActivityCode, helpMemberCode);
        }
        String sharingMemberCode = dto.getSharingMemberCode();
        BoostSharingRewardsResult boostSharingRewardsResult = new BoostSharingRewardsResult();
        //分享人发放奖励
        switch (rewardType){
            case COUPON_FOR_HELPERS_ONLY:
                break;
            case SHARE_PEOPLE_AND_HELP_PEOPLE_COUPONS:
                //发放分享人优惠券
                String shareToGetCouponActivityCode = boostSharingModel.getShareToGetCouponActivityCode();
                this.sendCoupon(dto, shareToGetCouponActivityCode, sharingMemberCode);
                boostSharingRewardsResult.setRewardType(BoostSharingRewardTypeEnum.COUPON.code());
                boostSharingRewardsResult.setRewardActivityCode(boostSharingModel.getShareToGetCouponActivityCode());
                boostSharingRewardsResult.setRewardActivityName(boostSharingModel.getShareToGetCouponActivityName());

                break;
            case SHARER_RIGHT_OF_FIRST_REFUSAL:
                this.bindTheUserWithTheRightOfFirstRefusal(dto,boostSharingModel.getRightOfFirstRefusalProductCode());
                boostSharingRewardsResult.setRewardType(BoostSharingRewardTypeEnum.RIGHT_OF_FIRST_REFUSAL.code());
                boostSharingRewardsResult.setRewardActivityCode(boostSharingModel.getShareToGetCouponActivityCode());
                boostSharingRewardsResult.setRewardActivityName(boostSharingModel.getShareToGetCouponActivityName());
                break;
            case SHARER_LUCKY_DRAW_BONUS:
                String luckyDrawActivityCode = boostSharingModel.getLuckyDrawActivityCode();
                this.sendTicket(dto, luckyDrawActivityCode, sharingMemberCode);
                boostSharingRewardsResult.setRewardType(BoostSharingRewardTypeEnum.RAFFLE_OPPORTUNITIES.code());
                boostSharingRewardsResult.setRewardActivityCode(boostSharingModel.getLuckyDrawActivityCode());
                boostSharingRewardsResult.setRewardActivityName(boostSharingModel.getLuckyDrawActivityName());
                break;
        }

        return boostSharingRewardsResult;

    }


    /**
     * 投放抽奖机会
     */
    @Transactional
    public List<TicketSendResult> sendTicket(HelpRecordDto dto, String helpToGetCouponActivityCode, String helpMemberCode) {
        //添加批次信息
        TicketReleaseDomain ticketReleaseDomain = new TicketReleaseDomain();
        ticketReleaseDomain.setActivityCode(helpToGetCouponActivityCode);
        ticketReleaseDomain.setDomainCode(dto.getDomainCode());
        ticketReleaseDomain.setTenantCode(dto.getTenantCode());
        ticketReleaseDomain.setOrgCode(dto.getOrgCode());
        //创建人为活动code
        ticketReleaseDomain.setOperateUser(dto.getActivityCode());

        TicketReleaseQueryParam param = new TicketReleaseQueryParam();
        param.setDomainCode(dto.getDomainCode());
        param.setTenantCode(dto.getTenantCode());
        param.setOrgCode(dto.getOrgCode());
        param.setActivityCode(helpToGetCouponActivityCode);
        PageResult<TicketReleaseQueryResult> ticketReleaseQueryResultPageResult = ticketComponent.queryRelease(ticketReleaseDomain, param);
        if (null == ticketReleaseQueryResultPageResult.getData()
                || ticketReleaseQueryResultPageResult.getData().getTotal()<=0){
            ticketReleaseDomain.setQuality(1L);
            ticketComponent.release(ticketReleaseDomain);
        }else {
            PageData<TicketReleaseQueryResult> data = ticketReleaseQueryResultPageResult.getData();
            List<TicketReleaseQueryResult> list = data.getList();
            //list过滤第一条createUser为活动code的数据,如果不存在就创建一条
            TicketReleaseQueryResult ticketReleaseQueryResult = list.stream().filter(x -> x.getCreateUser().equals(dto.getActivityCode())).findFirst().orElseGet(() -> {
                ticketReleaseDomain.setQuality(1L);
                String release = ticketComponent.release(ticketReleaseDomain);
                ticketReleaseDomain.setReleaseCode(release);
                return BeanCopyUtils.jsonCopyBean(ticketReleaseDomain, TicketReleaseQueryResult.class);
            });

            if (null != ticketReleaseQueryResult){
                TicketReleaseDomain releaseSpecifiedQuantityDomain = new TicketReleaseDomain();
                releaseSpecifiedQuantityDomain.setDomainCode(dto.getDomainCode());
                releaseSpecifiedQuantityDomain.setTenantCode(dto.getTenantCode());
                releaseSpecifiedQuantityDomain.setOrgCode(dto.getOrgCode());
                releaseSpecifiedQuantityDomain.setActivityCode(helpToGetCouponActivityCode);
                releaseSpecifiedQuantityDomain.setQuality(1L);
                releaseSpecifiedQuantityDomain.setOperateUser(dto.getActivityCode());
                releaseSpecifiedQuantityDomain.setReleaseCode(ticketReleaseQueryResult.getReleaseCode());
                int i = ticketComponent.releaseSpecifiedQuantity(releaseSpecifiedQuantityDomain);
                Check.check(i<=0, MarketingChecker.THE_ACTIVITY_INFORMATION_ERROR);
            }

        }

        TicketSendDomain ticketSendDomain = new TicketSendDomain();
        ticketSendDomain.setActivityCode(helpToGetCouponActivityCode);
        ticketSendDomain.setMemberCodes(Lists.newArrayList(helpMemberCode));
        ticketSendDomain.setTenantCode(dto.getTenantCode());
        ticketSendDomain.setDomainCode(dto.getDomainCode());
        ticketSendDomain.setOrgCode(dto.getOrgCode());
        ticketSendDomain.setQuality(1);
        ticketSendDomain.setRightOfFirstRefusalSourceCode(dto.getSharingRecordCode());
        return ticketComponent.sendTicket(ticketSendDomain);
    }


    /**
     * 投放券
     */
    @Transactional
    public void sendCoupon(HelpRecordDto dto, String activityCode, String memberCode) {
        //release参数
        ReleaseCouponInDTO releaseCouponInDTO = new ReleaseCouponInDTO();
        releaseCouponInDTO.setReleaseSource(CouponReleaseSourceEnum.SYSTEM_CREATE.code());
        releaseCouponInDTO.setTimeSameActivity("1");
        releaseCouponInDTO.setActivityCode(activityCode);
        releaseCouponInDTO.setTenantCode(dto.getTenantCode());
        releaseCouponInDTO.setReleaseType(ReleaseTypeEnum.IMMEDIATELY.code());
        releaseCouponInDTO.setTimeSameActivity(ReleaseTimeSameActivityEnum.SAME.getCode());
        releaseCouponInDTO.setReceiveTimeSameActivity(ReleaseTimeSameActivityEnum.SAME.getCode());
        Map<String, String> outCouponMap = new HashMap<>();

        //查询参数
        CouponReleaseDomain releaseDomain = new CouponReleaseDomain();
        releaseDomain.setTenantCode(dto.getTenantCode());
        releaseDomain.setActivityCode(activityCode);
        releaseDomain.setReleaseStatus(null);
        RequestPage page = new RequestPage();
        page.setPageCount(100);

        //判断过期或者结束券活动,不发券
        ActivityModel couponActivity = activityService.findActivity(dto.getTenantCode(), activityCode, null);
        if (null == couponActivity || !couponActivity.getActivityStatus().equals(ActivityStatusEnum.EFFECTIVE.code())){
            return;
        }



        PageInfo<CouponReleaseDomain> couponReleaseList = couponReleaseComponent.queryCouponRelease(releaseDomain, page);
        if (CollectionUtils.isEmpty(couponReleaseList.getList())){
            releaseCouponInDTO.setReleaseQuantity(1);
            releaseCouponInDTO.setCreateUser(dto.getActivityCode());
            couponReleaseComponent.releaseCoupon(releaseCouponInDTO, outCouponMap, null);

        }else {
            List<CouponReleaseDomain> couponRelease = couponReleaseList.getList();
            //list过滤第一条createUser为活动code的数据,如果不存在就创建一条
            CouponReleaseDomain couponReleaseDomain = couponRelease.stream().filter(x->StringUtil.isNotBlank(x.getCreateUser())).filter(x -> x.getCreateUser().equals(dto.getActivityCode())).findFirst().orElseGet(() -> {
                releaseCouponInDTO.setReleaseQuantity(1);

                String releaseCouponCode = couponReleaseComponent.releaseCoupon(releaseCouponInDTO, outCouponMap, null);
                releaseDomain.setReleaseCode(releaseCouponCode);
                return BeanCopyUtils.jsonCopyBean(releaseDomain, CouponReleaseDomain.class);
            });

            if (null != couponReleaseDomain){
                CouponReleaseDomain releaseSpecifiedQuantityDomain = new CouponReleaseDomain();
                releaseSpecifiedQuantityDomain.setTenantCode(dto.getTenantCode());
                releaseSpecifiedQuantityDomain.setActivityCode(activityCode);
                releaseSpecifiedQuantityDomain.setReleaseQuantity(1);
                releaseSpecifiedQuantityDomain.setReleaseCode(couponReleaseDomain.getReleaseCode());
                int i = couponReleaseComponent.releaseSpecifiedQuantity(releaseSpecifiedQuantityDomain);
                Check.check(i<=0, MarketingChecker.THE_ACTIVITY_INFORMATION_ERROR);
            }

        }

        SendCouponToUserParam param = new SendCouponToUserParam();
        param.setDomainCode(dto.getDomainCode());
        param.setTenantCode(dto.getTenantCode());
        param.setActivityCode(activityCode);
        param.setUserCode(memberCode);
        param.setTakeLabel(TakeLabelEnum.OTHER.code());
        param.setReceiveCount(1);
        Map<String, List<String>> memberQualification = getMemberQualification(dto.getDomainCode(), dto.getTenantCode(), memberCode, dto.getOrgCode());
        if (MapUtil.isNotEmpty(memberQualification)){
            List<QualificationModel> qualificationModels = QualificationModel.convertToModel(memberQualification);
            param.setQualifications(BeanCopyUtils.jsonCopyList(qualificationModels, Qualification.class));
        }


        JsonResult<String> couponAutoSend = masterDataFeignClient.getValueValue(dto.getTenantCode(), "COUPON_AUTO_SEND");
        if (null == couponAutoSend || !couponAutoSend.getSuccess() || StringUtil.isBlank(couponAutoSend.getData()) || couponAutoSend.getData().equals("0")){

            couponCodeUserComponent.sendCouponToUserByUserCode(param);
        }else {
            SendCouponMessage sendCouponMessage = new SendCouponMessage();
            sendCouponMessage.setActivityCode(activityCode);
            sendCouponMessage.setMemberCode(memberCode);
            sendCouponMessage.setSendCouponToUserParam(param);
            sendCouponMessage(sendCouponMessage);
        }

    }

    public ExportBoostSharingTotalResult exportBoostSharingTotal(ExportBoostSharingTotalDto dto) {

       return boostSharingService.exportBoostSharingTotal(dto);

    }

    public List<ExportBoostSharingDetailResult> exportBoostSharingDetail(ExportBoostSharingDetailDto dto) {
       return boostSharingService.exportBoostSharingDetail(dto);
    }


    @Data
    public static class SendCouponMessage{

        private String activityCode;

        private String memberCode;

        private SendCouponToUserParam sendCouponToUserParam;

    }


    public void sendCouponMessage(SendCouponMessage sendCouponMessage){

        //消息通知失败
        log.info(" Snd coupon start send message.");
        String param = JSON.toJSONString(sendCouponMessage.getSendCouponToUserParam());

        Message message = new Message(MqEnums.MARKETING_SHARING_MQ.getTopicName(),"sendCoupon",sendCouponMessage.getActivityCode(), param.getBytes());

        try {
            MQProducerFactory.getInstance().getProducer(MqEnums.MARKETING_SHARING_MQ.getTopicName()).send(message);
        } catch (Exception e) {
            log.error("Snd coupon send message error");
            log.error(e.getMessage(), e);
        }
        log.info("Snd coupon end send message.");
    }



    public Map<String, List<String>> getMemberQualification(String domainCode,String  tenantCode,String  memberCode,String orgCode){
        GetMemberProfileResult memberInfoByMemberCode = getMemberInfoByMemberCode(domainCode, tenantCode, memberCode);
        if (null != memberInfoByMemberCode) {
            List<MemberTagBean> tags = new ArrayList<>();
            if (null != memberInfoByMemberCode.getTags()) {
                tags = memberInfoByMemberCode.getTags();
            }
            return getQualification(memberInfoByMemberCode,tags , orgCode);
        }
        return new HashMap<>();
    }




    /**
     * 投放优先购买权
     */
    public void bindTheUserWithTheRightOfFirstRefusal(HelpRecordDto dto,String productCode) {
        IssuanceOfPreEmptiveRightsDto issuanceOfPreEmptiveRightsDto = BeanCopyUtils.jsonCopyBean(dto, IssuanceOfPreEmptiveRightsDto.class);
        issuanceOfPreEmptiveRightsDto.setMemberCode(dto.getSharingMemberCode());
        issuanceOfPreEmptiveRightsDto.setRightOfFirstRefusalProductCode(productCode);
        issuanceOfPreEmptiveRights(issuanceOfPreEmptiveRightsDto);
    }


    /**
     * 查询助力记录
     */
    public PageResult<QuerySharingInformationResult> querySharingInformation(QuerySharingInformationDto dto, RequestPage page) {
        SharingRecordModel sharingRecordModel = BeanCopyUtils.jsonCopyBean(dto, SharingRecordModel.class);
        HelpRecordModel helpRecordModel = BeanCopyUtils.jsonCopyBean(dto, HelpRecordModel.class);

        PageHelper.startPage(page.getPageNo(), page.getPageCount());
        List<SharingRecordEntity> sharingRecord = sharingRecordService.querySharingRecord(sharingRecordModel);
        List<String> sharingRecordCodeList = sharingRecord.stream().map(SharingRecordEntity::getSharingRecordCode).collect(Collectors.toList());
        List<HelpRecordEntity> helpRecordList = helpRecordService.queryHelpRecord(helpRecordModel,sharingRecordCodeList);
        return genQuerySharingRecordResult(sharingRecord, helpRecordList);
    }

    public static PageResult<QuerySharingInformationResult> genQuerySharingRecordResult(List<SharingRecordEntity> sharingRecords, List<HelpRecordEntity> helpRecordList) {
        PageInfo<SharingRecordEntity> pageInfo = PageInfo.of(sharingRecords);


        Map<String, List<HelpRecordEntity>> helpRecordListMap = helpRecordList.stream().collect(Collectors.groupingBy(HelpRecordEntity::getSharingRecordCode));
        List<QuerySharingInformationResult> results = new ArrayList<>();
        sharingRecords.forEach(sharingRecord -> {
            QuerySharingInformationResult querySharingInformationResult = new QuerySharingInformationResult();
            querySharingInformationResult.setDomainCode(sharingRecord.getDomainCode());
            querySharingInformationResult.setTenantCode(sharingRecord.getTenantCode());
            querySharingInformationResult.setActivityCode(sharingRecord.getActivityCode());
            querySharingInformationResult.setOrgCode(sharingRecord.getOrgCode());
            querySharingInformationResult.setActivityStatus(sharingRecord.getActivityStatus());
            querySharingInformationResult.setNumberOfPeopleWhoHaveHelped(sharingRecord.getNumberOfPeopleWhoHaveHelped());
            querySharingInformationResult.setNumberOfBoostSharing(sharingRecord.getNumberOfBoostSharing());
            querySharingInformationResult.setHelpRecordResultList(BeanCopyUtils.jsonCopyList(helpRecordListMap.getOrDefault(sharingRecord.getSharingRecordCode(), Lists.newArrayList()), HelpRecordResult.class));
            querySharingInformationResult.setSharingMemberCode(sharingRecord.getSharingMemberCode());
            querySharingInformationResult.setSharingRecordCode(sharingRecord.getSharingRecordCode());
            results.add(querySharingInformationResult);
        });
        return new PageResult<>(results, pageInfo.getTotal());
    }





    public void issuanceOfPreEmptiveRights(IssuanceOfPreEmptiveRightsDto dto) {
        RightOfFirstRefusalModel rightOfFirstRefusalModel = BeanCopyUtils.jsonCopyBean(dto, RightOfFirstRefusalModel.class);

        if (StringUtil.isNotBlank(dto.getSharingRecordCode())){
            String activityCode = sharingRecordService.findBysharingRecordCode(dto.getSharingRecordCode(), dto.getMemberCode()).getActivityCode();
            rightOfFirstRefusalModel.setActivityCode(activityCode);
            dto.setActivityCode(activityCode);
        }
        if (StringUtil.isEmpty(rightOfFirstRefusalModel.getRightOfFirstRefusalCode())){
            String rightOfFirstRefusalCode = codeGenerator.generateCode(dto.getTenantCode(), RIGHT_OF_FIRST_REFUSAL, TEMPLATE_EXPR, 1L);
            rightOfFirstRefusalModel.setRightOfFirstRefusalCode(rightOfFirstRefusalCode);
        }

        if (StringUtil.isBlank(dto.getRightOfFirstRefusalProductCode())){
            BoostSharingModel boostShardingInfo = boostSharingService.findBoostShardingInfo(dto.getDomainCode(), dto.getTenantCode(), "", dto.getActivityCode());
            rightOfFirstRefusalModel.setRightOfFirstRefusalProductCode(boostShardingInfo.getRightOfFirstRefusalProductCode());
        }
        rightOfFirstRefusalModel.setRightOfFirstRefusalStatus(RightOfFirstRefusalStatusEnum.UN_USED.code());
        rightOfFirstRefusalService.insert(rightOfFirstRefusalModel);
    }

    @Transactional
    public void writeOffOfPreEmptiveRights(WriteOffOfPreEmptiveRightsDto dto) {

        RightOfFirstRefusalEntity rightOfFirstRefusalByMember = rightOfFirstRefusalService.findRightOfFirstRefusalByMember(dto);
        if (rightOfFirstRefusalByMember == null) {
            Check.check(true, MarketingChecker.THE_RIGHT_OF_FIRST_REFUSAL_HAS_ALREADY_BEEN_USED);
        }
        int i = rightOfFirstRefusalService.updateStatus(dto);
        Check.check(i!=1, MarketingChecker.FAILED_TO_USE_THE_RIGHT_OF_FIRST_REFUSAL);

    }


    public List<QueryRightOfFirstRefusalResult> queryRightOfFirstRefusal(WriteOffOfPreEmptiveRightsDto dto) {
        List<RightOfFirstRefusalEntity> rightOfFirstRefusalEntities = rightOfFirstRefusalService.queryRightOfFirstRefusalByMember(dto);
        return BeanCopyUtils.jsonCopyList(rightOfFirstRefusalEntities, QueryRightOfFirstRefusalResult.class);
    }

    public List<HelpRecordResult> queryHelpRecord(QuerySharingInformationDto dto) {
        HelpRecordModel helpRecordModel  = BeanCopyUtils.jsonCopyBean(dto, HelpRecordModel.class);
        List<HelpRecordEntity> helpRecordEntities = helpRecordService.queryHelpRecord(helpRecordModel, Lists.newArrayList());
        return BeanCopyUtils.jsonCopyList(helpRecordEntities, HelpRecordResult.class);
    }




    /**
     * 检查优先购买权活动
     */
    public Map<String, CacheFlashSaleModel> filterNoRightOfFirstRefusal(FilterNoRightOfFirstRefusalDto filterNoRightOfFirstRefusalDto) {

        String domainCode = filterNoRightOfFirstRefusalDto.getDomainCode();
        String tenantCode = filterNoRightOfFirstRefusalDto.getTenantCode();
        String language = filterNoRightOfFirstRefusalDto.getLanguage();
        String userCode = filterNoRightOfFirstRefusalDto.getUserCode();
        List<ShoppingCartItem> cartItems = filterNoRightOfFirstRefusalDto.getCartItems();
        boolean writeOff = filterNoRightOfFirstRefusalDto.isWriteOff();
        String orderId;
        if (StringUtil.isNotBlank(filterNoRightOfFirstRefusalDto.getOrderId())){
            orderId = filterNoRightOfFirstRefusalDto.getOrderId();
        } else {
            orderId = "";
        }

        Map<String, CacheFlashSaleModel> marketingCacheMap = marketingCacheComponent.getFlashSaleCacheMap(tenantCode, language, ActivityTypeEnum.BOOST_SHARDING.code());
        //check
        String orgCode = cartItems.stream().findFirst().map(ShoppingCartItem::getOrgCode).orElse("default");
        Map<String, List<String>> qualification = getQualification(getMemberInfoByMemberCode(domainCode, tenantCode, userCode), Lists.newArrayList(),orgCode);

        //过滤活动
        marketingCacheMap = MarketingFilterUtil.filterActivityByQualifications(marketingCacheMap, qualification);
        marketingCacheMap = MarketingFilterUtil.filterActivityByTime(marketingCacheMap, null);
        for (ShoppingCartItem cartItem : cartItems) {
            marketingCacheMap = MarketingFilterUtil.filterActivityByOrgCodes(marketingCacheMap,Lists.newArrayList(cartItem.getOrgCode()));
        }
        //查询所有商品是否存在优先购买活动

        Map<String,List<RightOfFirstRefusalEntity>> rightOfFirstRefusalMap = new HashMap<>();
        Collection<CacheFlashSaleModel> flashSaleModels = marketingCacheMap.values();
        //flashSaleModels根据activityEnd排序
        List<CacheFlashSaleModel> sortedByEnd = flashSaleModels.stream().sorted(Comparator.comparing(CacheFlashSaleModel::getActivityEnd)).collect(Collectors.toList());

        /*List<BoostSharingModel> boostSharingModels = boostSharingService.queryBoostSharingByActivityList(tenantCode,
                marketingCacheMap.values().stream().filter(x -> x.getActivityType().equals(ActivityTypeEnum.BOOST_SHARDING.code())).map(CacheFlashSaleModel::getActivityCode).collect(Collectors.toList()));*/
        List<BoostSharingModel> boostSharingModels = marketingCacheMap.values().stream().filter(x -> x.getActivityType().equals(ActivityTypeEnum.BOOST_SHARDING.code())).map(CacheFlashSaleModel::getBoostSharingModel).collect(Collectors.toList());

        List<CacheFlashSaleModel> spuMarketing  = flashSaleComponent.getBoostSharing(tenantCode, new ArrayList<>(marketingCacheMap.values()), boostSharingModels);
        if (CollectionUtils.isEmpty(spuMarketing)){
            //缓存为空 查库
            boostSharingModels = boostSharingService.queryBoostSharingByActivityList(tenantCode,
                    marketingCacheMap.values().stream().filter(x -> x.getActivityType().equals(ActivityTypeEnum.BOOST_SHARDING.code())).map(CacheFlashSaleModel::getActivityCode).collect(Collectors.toList()));
            spuMarketing  = flashSaleComponent.getBoostSharing(tenantCode, new ArrayList<>(marketingCacheMap.values()), boostSharingModels);
        }


        Map<String, List<ShoppingCartItem>> byProduct = cartItems.stream().collect(Collectors.groupingBy(ProductCodes::getProductCode));
        //cartItems转为map类型,key是productCode,value是quantity
        //Map<String, Integer> productQuantityMap = cartItems.stream().collect(Collectors.toMap(ProductCodes::getProductCode, ShoppingCartItem::getQuantity));

        //cartItems转为map类型,key是productCode,value是quantity,如果重复,数量合并
        Map<String, Integer> productQuantityMap = cartItems.stream().collect(Collectors.toMap(ProductCodes::getProductCode, ShoppingCartItem::getQuantity, Integer::sum));



        //遍历map类型的byProduct
        List<CacheFlashSaleModel> finalSpuMarketing = spuMarketing;
        byProduct.forEach((k, v)->{

            List<RightOfFirstRefusalEntity> rightOfFirstRefusalByProduct = new ArrayList<>();
            for (CacheFlashSaleModel activity : finalSpuMarketing) {
                //如果活动是分享助力活动，且是优先购买权类型，则需要校验优先购买权
                if (com.gtech.promotion.code.marketing.ActivityTypeEnum.BOOST_SHARDING.code().equals(activity.getActivityType())
                        && activity.getBoostSharingModel().getBoostSharingType().equals(BoostSharingTypeEnum.RIGHT_FIRST.code())
                        && k.equals(activity.getBoostSharingModel().getRightOfFirstRefusalProductCode())) {
                    //check 抢购时间
                    BoostSharingModel boostSharingModel = activity.getBoostSharingModel();
                    String startTime = boostSharingModel.getRightOfFirstRefusalStartTime();
                    String endTime = boostSharingModel.getRightOfFirstRefusalEndTime();
                    //如果不在这个时间段,直接跳过
                    if (DateUtil.parseDate(startTime, DateUtil.FORMAT_YYYYMMDDHHMISS_14).getTime() > System.currentTimeMillis()
                            || DateUtil.parseDate(endTime, DateUtil.FORMAT_YYYYMMDDHHMISS_14).getTime() < System.currentTimeMillis()) {
                        continue;
                    }
                    WriteOffOfPreEmptiveRightsDto dto = new WriteOffOfPreEmptiveRightsDto();
                    dto.setActivityCode(activity.getActivityCode());
                    dto.setDomainCode(domainCode);
                    dto.setMemberCode(userCode);
                    dto.setTenantCode(tenantCode);
                    dto.setRightOfFirstRefusalProductCode(k);
                    List<RightOfFirstRefusalEntity> rightOfFirstRefusalEntities = rightOfFirstRefusalService.queryRightOfFirstRefusalByMember(dto);
                    Check.check(CollectionUtils.isEmpty(rightOfFirstRefusalEntities), TPromoProductChecker.NO_RIGHT_OF_FIRST_REFUSAL);
                    Check.check(productQuantityMap.get(k) > rightOfFirstRefusalEntities.size(), TPromoProductChecker.NO_RIGHT_OF_FIRST_REFUSAL);

                    rightOfFirstRefusalByProduct.addAll(rightOfFirstRefusalEntities);
                }
            }
            rightOfFirstRefusalMap.put(k,rightOfFirstRefusalByProduct);
        });


        if (writeOff){
            //核销
            //优先购买权锁定
            rightOfFirstRefusalMap.forEach((productCode,rightOfFirstRefusalEntities) -> {
                List<RightOfFirstRefusalEntity> filterList = rightOfFirstRefusalEntities.stream()
                        .filter(x -> productCode.equals(x.getRightOfFirstRefusalProductCode())).collect(Collectors.toList());
                Integer count = productQuantityMap.get(productCode);
                for (int i = 0; i < count; i++) {
                    int finalI = i;
                    filterList.stream().findFirst().ifPresent(x->{
                        WriteOffOfPreEmptiveRightsDto dto = new WriteOffOfPreEmptiveRightsDto();
                        dto.setDomainCode(domainCode);
                        dto.setTenantCode(tenantCode);
                        dto.setMemberCode(userCode);
                        dto.setOrderId(orderId);
                        dto.setOrderIDIsNull(true);
                        dto.setRightOfFirstRefusalCode(x.getRightOfFirstRefusalCode());
                        dto.setActivityCode(Optional.of(rightOfFirstRefusalEntities.get(finalI).getActivityCode()).orElseThrow(() -> Exceptions.fail(ErrorCodes.ERROR_LOCK_RIGHT_OF_FIRST_REFUSAL)));
                        dto.setOldStatus(RightOfFirstRefusalStatusEnum.UN_USED.code());
                        dto.setNewStatus(RightOfFirstRefusalStatusEnum.LOCK.code());
                        int updateCount = rightOfFirstRefusalService.updateStatus(dto);
                        if (updateCount != 1) {
                            throw Exceptions.fail(ErrorCodes.ERROR_LOCK_RIGHT_OF_FIRST_REFUSAL);
                        }
                    });
                }
            });
        }



        return marketingCacheMap;
    }






}
