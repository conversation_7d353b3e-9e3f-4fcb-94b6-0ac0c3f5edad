/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * <PERSON>ECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.component.activity;

import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import com.gtech.commons.utils.StringUtil;
import com.gtech.promotion.checker.activity.TPromoActivityChecker;
import com.gtech.promotion.exception.Check;
import com.gtech.promotion.helper.ActivityRedisHelpler;
import com.gtech.promotion.service.activity.TemplateService;

/**
 * 模板标签
 */
@Service
public class TemplateDomain {

    @Autowired
    private ActivityRedisHelpler redisService;
    
    @Autowired
    private TemplateService templateService;

    /**
     * 查询所有模板标签编码和模板id
     * 
     * @return Map k:tagCode  v:templateIds
     */
    public Map<String, String> findTemplateTagAll(){

        final Map<String, String> tagMap = templateService.findTemplateTagAll();
        redisService.addTemplateTags(tagMap);

        return tagMap;
    }

    /**
     * 根据 活动标签 从缓存或者库中查询 模板ids
     * 
     * @return  模板ids
     */
    public String findTemplateTagByTagId(String tagCode){

        String templateTag = redisService.getTemplateTag(tagCode);

        if(StringUtil.isBlank(templateTag)){
            final Map<String, String> tagMap = templateService.findTemplateTagAll();
            redisService.addTemplateTags(tagMap);
            Check.check(CollectionUtils.isEmpty(tagMap),TPromoActivityChecker.NOT_ACTIVITY_STATUS_TYPE);
            templateTag = tagMap.get(tagCode);//NOSONAR
        }

        Check.check(StringUtil.isBlank(templateTag), TPromoActivityChecker.NOT_ACTIVITY_STATUS_TYPE);
        return templateTag;
    }


}
