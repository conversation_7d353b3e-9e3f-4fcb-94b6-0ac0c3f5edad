/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * <PERSON>ECH MAKES NO REPRESENTATIONS OR WA<PERSON>RANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.component.config.feign;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import com.gtech.commons.utils.StringUtil;

import feign.RequestInterceptor;
import feign.RequestTemplate;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;

/**
 * FeignRequestInterceptor
 */
@Slf4j
@Component
public class FeignRequestInterceptor implements RequestInterceptor {

    @Setter
    @Value("${titan.gateway.url:}")
    private String gatewayUrl;

    @Override
    public void apply(RequestTemplate requestTemplate) {
		// log.info("gatewayUrl:{}" , gatewayUrl);
		// log.info("调用feign请求:{}" , requestTemplate);
		// log.info("调用feign请求:{}" , requestTemplate.url());
        if (StringUtil.isNotBlank(gatewayUrl) && gatewayUrl.startsWith("http://gateway")) {
            requestTemplate.header("client-id", "vssystem");
            requestTemplate.header("client-secret", "vspass001");
        }
    }
}
