package com.gtech.promotion.component.purchaseconstraint.dto;

import com.gtech.promotion.component.purchaseconstraint.PcRuleComponent;
import com.gtech.promotion.dao.entity.purchaseconstraint.PurchaseConstraintCustomer;
import com.gtech.promotion.dto.in.purchaseconstraint.PcRuleOverlayCheckDTO;
import com.gtech.promotion.vo.bean.PurchaseConstraintRule;
import lombok.Data;

import java.io.Closeable;
import java.io.IOException;
import java.math.BigDecimal;

/**
 * 限购检查结果
 */
@Data
public class PcRuleCheckResult implements Closeable {
    /**
     * 增量值
     */
    private BigDecimal value;
    /**
     * sku编码
     */
    private String skuCode;
    /**
     * SPU编码
     */
    private String productCode;
    /**
     * 限购编码
     */
    private String pcCode;

    private PurchaseConstraintRule pcRule;

    private PcRuleOverlayCheckDTO pcRuleCheckDTO;

    private PurchaseConstraintCustomer customer;

    @Override
    public void close() throws IOException {
        PcRuleComponent.LOCAL_CUSTOMER_MAP.remove();
    }
}
