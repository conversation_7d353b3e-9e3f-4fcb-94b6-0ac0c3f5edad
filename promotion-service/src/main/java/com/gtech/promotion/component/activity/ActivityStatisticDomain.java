/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.component.activity;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.gtech.commons.utils.BeanCopyUtils;
import com.gtech.commons.utils.DateUtil;
import com.gtech.commons.utils.StringUtil;
import com.gtech.promotion.callable.ActivityStatisticCreateCallable;
import com.gtech.promotion.callable.ThreadPoolUtil;
import com.gtech.promotion.checker.activity.ActivityStatisticChecker;
import com.gtech.promotion.code.activity.ActivityTypeEnum;
import com.gtech.promotion.dao.entity.activity.TPromoActivityIncentiveEntity;
import com.gtech.promotion.dao.entity.activity.TPromoActivityStatisticEntity;
import com.gtech.promotion.dao.entity.activity.TPromoOrderEntity;
import com.gtech.promotion.dao.model.activity.ActivityModel;
import com.gtech.promotion.dao.mongo.activity.OrderDetailEntity;
import com.gtech.promotion.dto.in.activity.*;
import com.gtech.promotion.dto.out.activity.*;
import com.gtech.promotion.exception.Check;
import com.gtech.promotion.service.activity.ActivityService;
import com.gtech.promotion.service.activity.TPromoActivityIncentiveService;
import com.gtech.promotion.service.activity.TPromoActivityStatisticService;
import com.gtech.promotion.service.activity.TPromoOrderService;
import com.gtech.promotion.service.coupon.PromoCouponCodeUserService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Component
public class ActivityStatisticDomain {

    @Autowired
    private TPromoOrderService orderService;

    @Autowired
    private TPromoActivityIncentiveService incentiveService;

    @Autowired
    private ActivityService activityService;

    @Autowired
    private MongoTemplate mongoTemplate;

    @Autowired
    private TPromoActivityStatisticService statisticService;

    @Autowired
    private PromoCouponCodeUserService couponCodeUserService;

    private static final String ZERO = "0000";

    /**
     * 新增活动数据统计记录
     */
    public void createActivityStatistic(){

        ActivityStatisticCreateCallable createCallable = new ActivityStatisticCreateCallable(orderService, incentiveService, activityService, mongoTemplate, statisticService, couponCodeUserService);

        ThreadPoolUtil.pushTask(createCallable);
    }

    /**
     * 查询活动统计数据
     */
    public List<ActivityStatisticQueryOutDTO> queryActivityStatistic(QueryActivityStatisticInDTO paramDTO){

        ActivityModel activity = activityService.findActivityByActivityCode(paramDTO.getTenantCode(), paramDTO.getActivityCode());

        Check.check(null == activity, ActivityStatisticChecker.NOT_EXIST_ACTIVITY);

        String noWDateTime = DateUtil.format(new Date(), DateUtil.FORMAT_YYYYMMDD);

        int compare = paramDTO.getStartTime().compareTo(noWDateTime);

        int compareTo = paramDTO.getEndTime().compareTo(noWDateTime);

        List<ActivityStatisticQueryOutDTO> outDTOList = new ArrayList<>();

        List<ActivityStatisticInfoODTO> copyList = new ArrayList<>();

        if (compare < 0){

            List<TPromoActivityStatisticEntity> list = statisticService.queryActivityStatistic(paramDTO);

            if (!CollectionUtils.isEmpty(list)){

                copyList = copyToList(list, ActivityStatisticInfoODTO.class);
            }

        }

        if (compareTo >= 0){

            TPromoActivityStatisticInDTO statisticInDTO = dataStatistic(paramDTO, activity);

            if (null != statisticInDTO){

                ActivityStatisticInfoODTO statisticInfoODTO = new ActivityStatisticInfoODTO();

                BeanUtils.copyProperties(statisticInDTO, statisticInfoODTO);

                copyList.add(statisticInfoODTO);
            }
        }

        TreeMap<String, Object> treeMap = getDatesBetweenTwoDate(DateUtil.parseDate(paramDTO.getStartTime(), DateUtil.FORMAT_YYYYMMDD), DateUtil.parseDate(paramDTO.getEndTime(), DateUtil.FORMAT_YYYYMMDD));

        if (!CollectionUtils.isEmpty(copyList)){

            for (ActivityStatisticInfoODTO statisticInfoODTO : copyList){
                treeMap.put(statisticInfoODTO.getStatisticDate(), statisticInfoODTO);
            }

            Iterator<Map.Entry<String, Object>> iterator = treeMap.entrySet().iterator();

            while (iterator.hasNext()){
                Map.Entry<String, Object> next = iterator.next();
                ActivityStatisticQueryOutDTO outDTO = new ActivityStatisticQueryOutDTO();
                outDTO.setStatisticDate(next.getKey());
                outDTO.setStatisticData(String.valueOf(next.getValue()).equals(ZERO) ? null : (ActivityStatisticInfoODTO) next.getValue());
                outDTOList.add(outDTO);
            }
        }

        log.info("查询活动统计数据记录出参:{}", JSONObject.toJSONString(outDTOList));

        return outDTOList;
    }

    private TPromoActivityStatisticInDTO dataStatistic(QueryActivityStatisticInDTO paramDTO,ActivityModel activity){

        List<TPromoOrderEntity> list = orderService.queryPromoOrderToday(paramDTO);

        if (CollectionUtils.isEmpty(list)){
            return null;
        }

        //所有租铺的活动奖励
        List<TPromoActivityIncentiveEntity> incentiveList = incentiveService.getListByOrderIds(list.stream().map(TPromoOrderEntity::getId).collect(Collectors.toList()));

        //查询此租铺下活动信息
        List<TPromoActivityIncentiveEntity> entities = incentiveList.stream().filter(x -> x.getActivityCode().equals(activity.getActivityCode())).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(entities)){
            return null;
        }

        //优惠金额
        BigDecimal incentiveAmount = new BigDecimal(0);
        //活动参与的人数
        Set<String> set = new HashSet<>();
        //活动参与次数
        List<String> promoIds = new ArrayList<>();

        //活动下商品总价
        BigDecimal productPrice = new BigDecimal(0);

        //订单在至少一个活动下的总减免金额
        BigDecimal reduceAmount = new BigDecimal(0);

        for (TPromoActivityIncentiveEntity incentiveEntity : entities){
            set.add(incentiveEntity.getUserCode());
            promoIds.add(incentiveEntity.getPromoOrderId());
            //该活动下该订单在当前活动下的减免金额   sum:该活动的所有减免金额
            incentiveAmount = incentiveAmount.add(incentiveEntity.getIncentiveAmount());

            List<BigDecimal> collect = incentiveList.stream().filter(x -> x.getPromoOrderId().equals(incentiveEntity.getPromoOrderId()))
                .map(TPromoActivityIncentiveEntity::getIncentiveAmount).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(collect)) {
                for (BigDecimal bigDecimal : collect) {
                    reduceAmount = reduceAmount.add(bigDecimal);
                }
            }

            Query query = new Query();
            Criteria criteria = Criteria.where("promoOrderId").is(incentiveEntity.getPromoOrderId()).and("tenantCode").is(paramDTO.getTenantCode());
            query.addCriteria(criteria);

            List<OrderDetailEntity> entityList = mongoTemplate.find(query, OrderDetailEntity.class);
            for (OrderDetailEntity detailVO : entityList){
                productPrice = productPrice.add(detailVO.getProductAmount());
            }
        }

        //该活动一天总的领取券的数量
        Integer allocateCouponCount = null;

        Integer useCouponCount = null;

        if (activity.getActivityType().equals(ActivityTypeEnum.COUPON.code())){

            allocateCouponCount = couponCodeUserService.getAllocateCouponCountToday111(paramDTO.getTenantCode(), activity.getActivityCode());

            useCouponCount = couponCodeUserService.getUseCouponCountToday111(paramDTO.getTenantCode(), activity.getActivityCode());
        }

        return TPromoActivityStatisticInDTO.builder()
                  .tenantCode(paramDTO.getTenantCode())
                  .activityCode(activity.getActivityCode())
                  .orderCount(promoIds.size())
                  .memberCount(set.size())
                  .reduceAmount(incentiveAmount)
                  .orderAmount(productPrice.subtract(reduceAmount))
                  .statisticDate(DateUtil.format(new Date(), DateUtil.FORMAT_YYYYMMDD))
                  .allocateCouponCount(allocateCouponCount)
                  .useCouponCount(useCouponCount)
                  .build();

    }

    /**
     * 查询活动统计数据总数
     */
    public ActivityStatisticSumQueryOutDTO queryActivityStatisticSum(QueryActivityStatisticSumInDTO paramDTO){

        ActivityStatisticSumQueryOutDTO statisticSum = statisticService.queryActivityStatisticSum(paramDTO);

        QueryActivityStatisticInDTO build = QueryActivityStatisticInDTO.builder()
                        .tenantCode(paramDTO.getTenantCode())
                        .activityCode(paramDTO.getActivityCode())
                        .startTime(DateUtil.format(new Date(), DateUtil.FORMAT_YYYYMMDD))
                        .endTime(DateUtil.format(new Date(), DateUtil.FORMAT_YYYYMMDD)).build();

        ActivityModel activity = activityService.findActivityByActivityCode(paramDTO.getTenantCode(), paramDTO.getActivityCode());

        Check.check(null == activity, ActivityStatisticChecker.NOT_EXIST_ACTIVITY);

        TPromoActivityStatisticInDTO statisticInDTO = dataStatistic(build, activity);

        return getActivityStatisticSumQueryOutDTO(statisticSum, statisticInDTO, activity);
    }

    private ActivityStatisticSumQueryOutDTO getActivityStatisticSumQueryOutDTO(ActivityStatisticSumQueryOutDTO statisticSum, TPromoActivityStatisticInDTO statisticInDTO,
                    ActivityModel activity) {

        List<TPromoActivityIncentiveEntity> list = incentiveService.getListByActivityCode(activity.getActivityCode());
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }

        List<String> collect = list.stream().map(TPromoActivityIncentiveEntity::getUserCode).distinct().collect(Collectors.toList());

        if (null != statisticInDTO) {
            if (null == statisticSum) {
                statisticSum = new ActivityStatisticSumQueryOutDTO();
                statisticSum.setOrderCount(statisticInDTO.getOrderCount());
                statisticSum.setOrderAmount(statisticInDTO.getOrderAmount());
                statisticSum.setReduceAmount(statisticInDTO.getReduceAmount());
            } else {
                statisticSum.setOrderCount(statisticSum.getOrderCount() + statisticInDTO.getOrderCount());
                statisticSum.setOrderAmount(statisticSum.getOrderAmount().add(statisticInDTO.getOrderAmount()));
                statisticSum.setReduceAmount(statisticSum.getReduceAmount().add(statisticInDTO.getReduceAmount()));
                statisticSum.setMemberCount(collect.size());
            }
        }

        return statisticSum;
    }

    /**
     * 根据开始时间和结束时间返回时间段内的时间map
     *
     * @param beginDate
     * @param endDate
     * @return List
     */
    private static TreeMap<String, Object> getDatesBetweenTwoDate(Date beginDate,Date endDate){
        TreeMap<String, Object> treeMap = new TreeMap<>();
        // 把开始时间加入Set
        treeMap.put(DateUtil.format(beginDate, DateUtil.FORMAT_YYYYMMDD), ZERO);
        Calendar cal = Calendar.getInstance();
        // 使用给定的 Date 设置此 Calendar 的时间
        cal.setTime(beginDate);
        while (true){
            // 根据日历的规则，为给定的日历字段添加或减去指定的时间量
            cal.add(Calendar.DAY_OF_MONTH, 1);
            // 测试此日期是否在指定日期之后
            if (endDate.after(cal.getTime())){
                treeMap.put(DateUtil.format(cal.getTime(), DateUtil.FORMAT_YYYYMMDD), ZERO);
            }else{
                break;
            }
        }
        // 把结束时间加入集合
        treeMap.put(DateUtil.format(endDate, DateUtil.FORMAT_YYYYMMDD), ZERO);
        return treeMap;
    }

    private <T> List<T> copyToList(Object source,Class<T> targetClazz){

        return JSON.parseArray(JSON.toJSONString(source), targetClazz);
    }

    /**   
     * 数据汇总分析 
     */
    public ActivityDataAnalyzeOutDto dataOfAnalyze(StartAndEndTimeInDTO startAndEndTime){
        ActivityDataAnalyzeOutDto analyze = new ActivityDataAnalyzeOutDto();
        List<String> list = new ArrayList<>();
        if (StringUtil.isNotBlank(startAndEndTime.getTenantCodes())){
            String[] tenantCodes = startAndEndTime.getTenantCodes().split(",");
            list = Arrays.asList(tenantCodes);
        }
        long accumulativeTotal = activityService.accumulativeTotal(list);
        analyze.setActivityTotal(accumulativeTotal);
        long usedCouponAmount = couponCodeUserService.usedCouponAmount(list);
        analyze.setUsedCouponTotal(usedCouponAmount);
        if (StringUtil.isBlank(startAndEndTime.getStartTime()) && StringUtil.isBlank(startAndEndTime.getEndTime())){
            analyze.setActivityTotalInTime(accumulativeTotal);
            analyze.setUsedCouponTotalInTime(usedCouponAmount);
        }else{
            analyze.setActivityTotalInTime(activityService.accumulativeTotal(startAndEndTime.getStartTime(), startAndEndTime.getEndTime(), list));
            analyze.setUsedCouponTotalInTime(couponCodeUserService.usedCouponAmount(startAndEndTime.getStartTime(), startAndEndTime.getEndTime(), list));
        }
        return analyze;
    }

    /**   
     * 活跃的活动总数包含券活动 
     */
    public Long queryActivityActiveTotal(String tenantCode){

        return activityService.queryEffectActivityByTenantCode(tenantCode);
    }

    /**   
     * 返回促销活动统计数据 
     */
    public ActivityTenantOutDTO queryActivityTenantData(ActivityTenantInDTO tenantInDTO){
        ActivityTenantOutDTO tenantOutDTO = new ActivityTenantOutDTO();
        int receivedTotal = couponCodeUserService.getReceivedCouponAmount(tenantInDTO);
        tenantOutDTO.setReceivedTotal(receivedTotal);
        int usedCouponAmount = couponCodeUserService.getUsedCouponAmount(tenantInDTO);
        tenantOutDTO.setUsedTotal(usedCouponAmount);
        int orderTotal = orderService.getPayOrderAmount(tenantInDTO);
        tenantOutDTO.setOrderTotal(orderTotal);
        double incentiveMoneyTotal = incentiveService.getPayOrderDiscountMoneyTotal(tenantInDTO);
        tenantOutDTO.setIncentiveMoneyTotal(incentiveMoneyTotal);
        return tenantOutDTO;
    }



    public List<OrderActivityOutDTO> queryCouponOrderData(CouponOrderStatisticInDTO statisticInDTO) {

        //查询订单
        List<TPromoOrderEntity> list = orderService.queryPromoOrderByOrderIdList(statisticInDTO);
        if (CollectionUtils.isEmpty(list)){
            return new ArrayList<>();
        }
        //查询每个活动抵扣多少金额
        List<TPromoActivityIncentiveEntity> listByOrderIds = incentiveService.getListByOrderIds(list.stream().map(TPromoOrderEntity::getId).collect(Collectors.toList()));
        Map<String, List<TPromoActivityIncentiveEntity>> byPromOrderId = listByOrderIds.stream().collect(Collectors.groupingBy(TPromoActivityIncentiveEntity::getPromoOrderId));


        List<OrderActivityOutDTO> resultList = new ArrayList<>();

        Query query = new Query();
        Criteria criteria = Criteria.where("promoOrderId").in(new ArrayList<>(byPromOrderId.keySet()));
        query.addCriteria(criteria);
        List<OrderDetailEntity> entityList = mongoTemplate.find(query, OrderDetailEntity.class);
        //根据每个订单的总金额
        Map<String, BigDecimal> orderAmounts = entityList.stream()
                .collect(Collectors.groupingBy(
                        OrderDetailEntity::getPromoOrderId,
                        Collectors.reducing(
                                BigDecimal.ZERO,
                                entity -> (entity != null && entity.getProductAmount() != null) ? entity.getProductAmount() : BigDecimal.ZERO,
                                BigDecimal::add
                        )
                ));

        for (TPromoOrderEntity tPromoOrderEntity : list) {
            OrderActivityOutDTO orderActivityOutDTO = new OrderActivityOutDTO();
            orderActivityOutDTO.setOrderId(tPromoOrderEntity.getOrderId());
            orderActivityOutDTO.setTotalAmount(orderAmounts.get(String.valueOf(tPromoOrderEntity.getId())));
            orderActivityOutDTO.setOrderDetails(BeanCopyUtils.jsonCopyList(byPromOrderId.get(String.valueOf(tPromoOrderEntity.getId())), OrderActivityOutDTO.OrderDetail.class));
            resultList.add(orderActivityOutDTO);
        }


        return resultList;

    }
}
