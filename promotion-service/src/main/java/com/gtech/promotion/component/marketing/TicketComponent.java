package com.gtech.promotion.component.marketing;

import com.gtech.commons.page.PageData;
import com.gtech.commons.page.PageParam;
import com.gtech.commons.redis.GTechCodeGenerator;
import com.gtech.commons.result.PageResult;
import com.gtech.commons.utils.BeanCopyUtils;
import com.gtech.commons.utils.StringUtil;
import com.gtech.promotion.code.marketing.TicketStatusEnum;
import com.gtech.promotion.dao.model.marketing.BaseModel;
import com.gtech.promotion.dao.model.marketing.TicketModel;
import com.gtech.promotion.dao.model.marketing.TicketReleaseModel;
import com.gtech.promotion.domain.marketing.TicketReleaseDomain;
import com.gtech.promotion.domain.marketing.TicketSendDomain;
import com.gtech.promotion.dto.in.marketing.TicketSendOutDto;
import com.gtech.promotion.service.marketing.TicketReleaseService;
import com.gtech.promotion.service.marketing.TicketService;
import com.gtech.promotion.utils.MarketingConstants;
import com.gtech.promotion.vo.result.marketing.TicketReleaseQueryResult;
import com.gtech.promotion.vo.result.marketing.TicketSendResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component
public class TicketComponent {

    @Autowired
    private TicketReleaseService ticketReleaseService;

    @Autowired
    private TicketService ticketService;

    @Autowired
    private GTechCodeGenerator gTechCodeGenerator;

    @Transactional
    public String release(TicketReleaseDomain ticketReleaseDomain) {
        TicketReleaseModel ticketReleaseModel = BeanCopyUtils.jsonCopyBean(ticketReleaseDomain, TicketReleaseModel.class);
        String releaseCode = gTechCodeGenerator.generateCode(MarketingConstants.APP_KEY, "TRL", MarketingConstants.MKT_CODE_TEMPLATE_EXP, 105L);
        ticketReleaseModel.setReleaseCode(releaseCode);
        ticketReleaseModel.setInventory(ticketReleaseDomain.getQuality());
        ticketReleaseModel.setCreateUser(ticketReleaseDomain.getOperateUser());
        ticketReleaseService.insert(ticketReleaseModel);
        return releaseCode;
    }

    //修改发型数量
    @Transactional
    public int releaseSpecifiedQuantity(TicketReleaseDomain ticketReleaseDomain) {
        TicketReleaseModel ticketReleaseModel = BeanCopyUtils.jsonCopyBean(ticketReleaseDomain, TicketReleaseModel.class);
        ticketReleaseModel.setCreateUser(ticketReleaseDomain.getOperateUser());

        return ticketReleaseService.releaseSpecifiedQuantity(ticketReleaseModel);
    }



    public PageResult<TicketReleaseQueryResult> queryRelease(TicketReleaseDomain ticketReleaseDomain, PageParam param) {
        TicketReleaseModel ticketReleaseModel = BeanCopyUtils.jsonCopyBean(ticketReleaseDomain, TicketReleaseModel.class);
        PageData<TicketReleaseModel> pageData = ticketReleaseService.selectPageList(ticketReleaseModel, param);
        return PageResult.ok(BeanCopyUtils.jsonCopyList(pageData.getList(), TicketReleaseQueryResult.class), pageData.getTotal());
    }

    @Transactional
    public List<TicketSendResult> sendTicket(TicketSendDomain ticketSendDomain) {
        List<TicketSendResult> results = new ArrayList<>();
        List<String> memberCodes = ticketSendDomain.getMemberCodes();
        int totalCount = ticketSendDomain.getQuality() * memberCodes.size();

        BaseModel baseModel = BeanCopyUtils.jsonCopyBean(ticketSendDomain, BaseModel.class);

        List<TicketSendOutDto> ticketSendOutDtos = ticketReleaseService.deductInventory(baseModel, totalCount);

        String[] allocates = new String[totalCount];
        getAllocates(ticketSendDomain, memberCodes, allocates);
        int allocatesBegin = 0;
        Map<String, List<String>> tempResult = new HashMap<>();
        for (TicketSendOutDto ticketSendOutDto : ticketSendOutDtos) {
            for (; allocatesBegin < allocates.length; allocatesBegin++) {

                if (ticketSendOutDto.getSendCount() == 0){
                    break;
                }
                String memberCode = allocates[allocatesBegin];
                List<String> ticketCodes;
                ticketCodes = getStrings(tempResult, memberCode);
                String ticketCode = gTechCodeGenerator.generateCode(MarketingConstants.APP_KEY, "TKC", MarketingConstants.MKT_CODE_TEMPLATE_EXP, 105L);
                if (StringUtil.isNotBlank(ticketSendDomain.getRightOfFirstRefusalSourceCode())){
                    ticketCode = ticketSendDomain.getRightOfFirstRefusalSourceCode();
                }
                TicketModel ticketModel = BeanCopyUtils.jsonCopyBean(baseModel, TicketModel.class);
                ticketModel.setReleaseCode(ticketSendOutDto.getReleaseCode());
                ticketModel.setTicketCode(ticketCode);
                ticketModel.setMemberCode(memberCode);
                ticketModel.setStatus(TicketStatusEnum.NO_USE.code());
                if (StringUtil.isNotBlank(ticketSendDomain.getFrozenStatus())){
                    ticketModel.setFrozenStatus(ticketSendDomain.getFrozenStatus());
                }
                ticketService.insert(ticketModel);

                ticketSendOutDto.setSendCount(ticketSendOutDto.getSendCount() - 1);
                ticketCodes.add(ticketCode);
                tempResult.put(memberCode, ticketCodes);
            }
        }
        if (!CollectionUtils.isEmpty(tempResult)){
            for (Map.Entry<String, List<String>> stringListEntry : tempResult.entrySet()) {
                results.add(TicketSendResult.ok(stringListEntry.getKey(), stringListEntry.getValue()));
            }
        }
        return results;
    }

    public List<String> getStrings(Map<String, List<String>> tempResult, String memberCode) {
        List<String> ticketCodes;
        if (tempResult.containsKey(memberCode)){
            ticketCodes = tempResult.get(memberCode);
        }else{
            ticketCodes = new ArrayList<>();
        }
        return ticketCodes;
    }

    public void getAllocates(TicketSendDomain ticketSendDomain, List<String> memberCodes, String[] allocates) {
        for (int i = 0; i < memberCodes.size(); i++) {
            for (int j = ticketSendDomain.getQuality() * i; j < ticketSendDomain.getQuality() * (i + 1); j++) {
                allocates[j] = memberCodes.get(i);
            }
        }
    }
}
