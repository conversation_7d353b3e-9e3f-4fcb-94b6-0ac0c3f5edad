/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.component.activity;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.UUID;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.gtech.commons.exception.Exceptions;
import com.gtech.commons.result.Result;
import com.gtech.commons.utils.BeanCopyUtils;
import com.gtech.commons.utils.StringUtil;
import com.gtech.promotion.checker.activity.TPromoProductChecker;
import com.gtech.promotion.checker.activity.TPromoProductDetailChecker;
import com.gtech.promotion.code.activity.FuncTypeEnum;
import com.gtech.promotion.dao.model.activity.ActivityFunctionParamRankModel;
import com.gtech.promotion.dao.model.activity.ActivityModel;
import com.gtech.promotion.dao.model.activity.ActivityPeriodModel;
import com.gtech.promotion.dao.model.activity.FunctionParamModel;
import com.gtech.promotion.dao.model.activity.GiveawayVO;
import com.gtech.promotion.dao.model.activity.TPromoActivityProductDetailVO;
import com.gtech.promotion.domain.activity.ActivityDomain;
import com.gtech.promotion.dto.out.activity.ActivityProductOutDTO;
import com.gtech.promotion.dto.out.activity.ProductTokenOutDTO;
import com.gtech.promotion.dto.out.product.ProductDetailDTO;
import com.gtech.promotion.dto.out.product.ProductOutDTO;
import com.gtech.promotion.exception.Check;
import com.gtech.promotion.exception.ErrorCodes;
import com.gtech.promotion.helper.TemplateHelper;
import com.gtech.promotion.page.RequestPage;
import com.gtech.promotion.service.activity.ActivityFuncParamService;
import com.gtech.promotion.service.activity.ActivityFuncRankService;
import com.gtech.promotion.service.activity.ActivityPeriodService;
import com.gtech.promotion.service.activity.ActivityProductDetailService;
import com.gtech.promotion.service.activity.GiveawayService;
import com.gtech.promotion.service.mongo.activity.TPromoProductService;
import com.gtech.promotion.utils.Constants;
import com.gtech.promotion.vo.bean.ActivityPeriod;
import com.gtech.promotion.vo.bean.Giveaway;
import com.gtech.promotion.vo.bean.ProductDetail;
import com.gtech.promotion.vo.bean.ProductScope;

import lombok.extern.slf4j.Slf4j;

/**
 * 商品服务
 * 
 */
@Slf4j
@Service
public class ProductDomain {


    @Autowired
    private ActivityProductDetailService productDetailService;

    @Autowired
    private TPromoProductService productService;

    @Autowired
    private ActivityComponentDomain activityDomain;

    @Autowired
    private GiveawayService giveawayService;

    @Autowired
    private ActivityFuncRankService tPromoActivityFuncRankService;

    @Autowired
    private ActivityFuncParamService tPromoActivityFuncParamService;

    @Autowired
    private StringRedisTemplate redisTemplate;

    @Autowired
    private TemplateHelper templateHelper;
    @Autowired
    private ActivityPeriodService activityPeriodService;

    private static final String TENANT_CODE = ":TENANTCODE=";

    private static final String SKU_TOKEN = ":SKUTOKEN=";

    @Transactional
    public Result<Object> createProductSku(String activityCode, String tenantCode, String skuToken) {

        if (StringUtils.isEmpty(skuToken)) {
            return Result.ok();
        }
        //组装key
        StringBuilder key = new StringBuilder();
        key.append(Constants.PROMOTION_ACTIVITY_SKU_CREATE).append(TENANT_CODE).append(tenantCode).append(SKU_TOKEN).append(skuToken);
        //集合
        List<TPromoActivityProductDetailVO> productDetailVOs = new ArrayList<>();
        try {
            //所有的key集合
            Set<String> keys = redisTemplate.<String, String> opsForHash().keys(key.toString());
            if (CollectionUtils.isNotEmpty(keys)) {
                keys.forEach(x -> {
                    String value = redisTemplate.<String, String> opsForHash().get(key.toString(), x);
                    Integer seqNum = Integer.parseInt(x.substring(x.lastIndexOf('=') + 1));
                    //修改或者添加(1:之前是sku,2:之前不是sku)
                    productDetailService.deleteProductSkuBySeqNum(activityCode, seqNum);
                    if (!StringUtils.isEmpty(value)) {
                        List<TPromoActivityProductDetailVO> list = JSON.parseArray(value, TPromoActivityProductDetailVO.class);
                        setAttr(activityCode, tenantCode, seqNum, list);
                        productDetailVOs.addAll(list);
                    }
                });
                //删除多个key
                redisTemplate.delete(keys);
            } else {
                Check.check(true, TPromoProductChecker.NULL_SKU);
            }

        } catch (Exception e) {
            log.error("促销redis服务挂了,商品sku信息缓存无法取出", e);
        }

        //批量插入
        if (CollectionUtils.isNotEmpty(productDetailVOs)) {
            productDetailService.insertProductSku(productDetailVOs);
        }

        return Result.ok();
    }

    private void setAttr(String activityCode, String tenantCode, Integer seqNum, List<TPromoActivityProductDetailVO> list) {

        list.forEach(y -> {
            y.setTenantCode(tenantCode);
            y.setSeqNum(seqNum);
            y.setActivityCode(activityCode);
            if (Objects.isNull(y.getCreateTime())) {
                y.setCreateTime(new Date());
            }
            if (Objects.isNull(y.getUpdateTime())) {
                y.setUpdateTime(new Date());
            }
            if (StringUtil.isBlank(y.getLogicDelete())) {
                y.setLogicDelete("0");
            }
        });
    }

    public ActivityProductOutDTO queryActivityProductByCode(String tenantCode, String language, String activityCode,Integer seqNum,RequestPage page){

        ActivityModel activityModel = this.activityDomain.findValidActivity(tenantCode, activityCode, language, new Date());
        if (activityModel == null) {
            throw Exceptions.fail(ErrorCodes.VALIDATE_ACTIVITY_EXIST, activityCode);
        }

        //商品分类属性品牌信息处理
        List<ProductOutDTO> products = new ArrayList<>();
        //商品池数量
        Integer seqCount = 0;
        List<ProductDetail> queryProductResult = new ArrayList<>();

        // 多个商品范围
        List<ProductScope> productDTO = productService.getProducts(activityCode);
        Set<Integer> set = new HashSet<>();
        for (int i = 0; i < productDTO.size(); i++){
            set.add(productDTO.get(i).getSeqNum());
        }
        seqCount = getSeqCount111(activityCode, set);
        seqNum = productsAdd(seqNum, products, productDTO, set);
        // 商品详情处理
        if (null != page){
            PageHelper.startPage(page.getPageNo(), page.getPageCount());
        }
        PageInfo<ProductDetail> productSkus = productDetailService.queryProductSkusGroup111(activityCode, seqNum);

        List<ProductDetail> list = productSkus.getList();
        List<String> spuCodes = new ArrayList<>();
        List<String> combineSpuCodes = new ArrayList<>();
        combineAndSpuCodeAdd(list, spuCodes);
        List<ProductDetail> queryProductSpusIn = new ArrayList<>();
        List<ProductDetail> queryProductcombinSpusIn = new ArrayList<>();
        queryProductSpusIn = getSpus(seqNum, activityCode, spuCodes, queryProductSpusIn);
        if (!CollectionUtils.isEmpty(combineSpuCodes)){
            queryProductcombinSpusIn = productDetailService.queryProductcombinSpusIn111(activityCode, combineSpuCodes);
        }
        addAll(queryProductResult, queryProductSpusIn, queryProductcombinSpusIn);

        String templateTagCode = templateHelper.templateCode2TagCode(activityModel.getTemplateCode());
        //赠品处理
        List<Giveaway> giveaways = giveawayDeal(tenantCode, activityCode, templateTagCode);
        //赠送数量处理
        String giftLimitMax = giftLimitMaxDeal111(activityCode, giveaways);
        ActivityPeriodModel period = activityPeriodService.findPeriod(tenantCode, activityCode);
        ActivityPeriod activityPeriod = BeanCopyUtils.jsonCopyBean(period, ActivityPeriod.class);
        //组装出参
        return ActivityProductOutDTO.builder().activityBegin(activityModel.getActivityBegin()).activityDesc(activityModel.getActivityDesc()).activityEnd(activityModel.getActivityEnd())
                .activityLabel(activityModel.getActivityLabel()).activityName(activityModel.getActivityName()).activityPeriod(activityPeriod).activityRemark(activityModel.getActivityRemark())
                .activitySort(activityModel.getTagCode()).activityType(activityModel.getActivityType()).giftLimitMax(giftLimitMax).seqCount(seqCount).giveaways(giveaways)
                .spuSkuTotal(productSkus.getTotal()).products(products).productDetils(queryProductResult).ribbonImage(activityModel.getRibbonImage()).ribbonPosition(activityModel.getRibbonPosition())
                .ribbonText(activityModel.getRibbonText()).productSelectionType(activityModel.getProductSelectionType()).coolDown(activityModel.getCoolDown()).opsType(activityModel.getOpsType())
                .warmBegin(activityModel.getWarmBegin()).build();
    }

    private void addAll(List<ProductDetail> queryProductResult,List<ProductDetail> queryProductSpusIn,List<ProductDetail> queryProductcombinSpusIn){
        if (!CollectionUtils.isEmpty(queryProductSpusIn)){
            queryProductResult.addAll(queryProductSpusIn);
        }
        if (!CollectionUtils.isEmpty(queryProductcombinSpusIn)){
            queryProductResult.addAll(queryProductcombinSpusIn);
        }
    }

    private List<ProductDetail> getSpus(Integer seqNum, String activityCode, List<String> spuCodes, List<ProductDetail> queryProductSpusIn) {

        if (!CollectionUtils.isEmpty(spuCodes)) {
            queryProductSpusIn = productDetailService.queryProductSpusIn111(activityCode, spuCodes);
            Iterator<ProductDetail> iterator = queryProductSpusIn.iterator();
            while (iterator.hasNext()) {
                if (!iterator.next().getSeqNum().equals(seqNum)) {//去掉spuCode相同， seqNum不同的skucode
                    iterator.remove();
                }
            }
        }
        return queryProductSpusIn;
    }

    private Integer getSeqCount111(String activityCode, Set<Integer> set){

        List<Integer> seqNums = productDetailService.getSeqNumCount111(activityCode);//商品池数量
        if (CollectionUtils.isNotEmpty(seqNums)){
            set.addAll(seqNums);
        }
        return set.size();
    }

    private void combineAndSpuCodeAdd(List<ProductDetail> list, List<String> spuCodes){
        if (!CollectionUtils.isEmpty(list)){
            for (ProductDetail spuSkuDTO : list){
                if (StringUtil.isNotBlank(spuSkuDTO.getProductCode())){
                    spuCodes.add(spuSkuDTO.getProductCode());
                }
            }
        }
    }

    private Integer productsAdd(Integer seqNum,List<ProductOutDTO> products,List<ProductScope> productDTO,Set<Integer> set){
        if (!CollectionUtils.isEmpty(set)){ // 有商品范围，不是全部商品
            ArrayList<Integer> arrayList = new ArrayList<>(set);
            Collections.sort(arrayList);
            Check.check(seqNum < 1 || arrayList.size() < seqNum, TPromoProductChecker.TOO_BIG_SKUNUM);
            seqNum = arrayList.get(seqNum - 1);
            for (int i = 0; i < productDTO.size(); i++){
                if (productDTO.get(i).getSeqNum().equals(seqNum)){
                    ProductOutDTO convert = BeanCopyUtils.jsonCopyBean(productDTO.get(i), ProductOutDTO.class);
                    products.add(convert);
                }
            }
        }
        return seqNum;
    }

    private List<Giveaway> giveawayDeal(String tenantCode, String activityCode, String templateTagCode){
        List<Giveaway> giveaways = new ArrayList<>();
        if ("06".equals(templateTagCode)){
            List<GiveawayVO> giveawayVOS = giveawayService.getGiftListByActivityCode(tenantCode, activityCode);
            giveawayVOS.forEach(x -> {
                Giveaway giveaway = new Giveaway();
                giveaway.setGiveawayCode(x.getGiveawayCode());
                giveaway.setGiveawayName(x.getGiveawayName());
                giveaway.setGiveawayNum(x.getGiveawayNum());
                giveaways.add(giveaway);
            });
        }
        return giveaways;
    }

    private String giftLimitMaxDeal111(String activityCode, List<Giveaway> giveaways){
        String giftLimitMax = "";
        //模板函数编码的值，0406赠品选择数量限制
        if (!CollectionUtils.isEmpty(giveaways)){
            List<ActivityFunctionParamRankModel> rankList = tPromoActivityFuncRankService.getRankListByActivityCode(activityCode);
            ActivityFunctionParamRankModel rank = rankList.get(0);
            List<FunctionParamModel> promoFuncParams = tPromoActivityFuncParamService.getRuleFuncParamListByRankId(rank.getId());
            for (FunctionParamModel tPromoActivityFuncParamVO : promoFuncParams){
                if (FuncTypeEnum.IncentiveEnum.GIVEAWAY.equalsCode(tPromoActivityFuncParamVO.getFunctionCode())){//0406
                    giftLimitMax = tPromoActivityFuncParamVO.getParamValue();
                }
            }

        }
        return giftLimitMax;
    }

    @Transactional
    public Result<ProductTokenOutDTO> saveProductSkuToRedis(List<TPromoActivityProductDetailVO> productDetailVOs,String tenantCode,String skuToken,Long seqNum,String withPrice){

        StringBuilder key = new StringBuilder();
        key.append(Constants.PROMOTION_ACTIVITY_SKU_CREATE).append(TENANT_CODE).append(tenantCode).append(SKU_TOKEN);
        //token为空
        if (StringUtils.isEmpty(skuToken)){
            skuToken = UUID.randomUUID().toString() + withPrice;
        }
        key.append(skuToken);
        String value = "";
        if (!CollectionUtils.isEmpty(productDetailVOs)){
            value = JSON.toJSONString(productDetailVOs);
        }
        try{
            redisTemplate.opsForHash().put(key.toString(), "SEQNUM=" + seqNum, value);
            redisTemplate.expire(key.toString(), 1800, TimeUnit.SECONDS);
        }catch (Exception e){
            log.error("促销redis服务挂了,商品sku信息缓存未加入", e);
        }

        ProductTokenOutDTO dto = new ProductTokenOutDTO();
        dto.setSkuToken(skuToken);
        return Result.ok(dto);
    }

    public List<ProductDetailDTO> queryProductSkusByseqNum111(String activityCode, String tenantCode, String language, Integer seqNum){

        ActivityModel activity = this.activityDomain.findActivityByActivityCode(tenantCode, language, activityCode);
        Check.check(null == activity, TPromoProductDetailChecker.ERROR_ACTIVITY_TENANT_CODE);
        return BeanCopyUtils.jsonCopyList(productDetailService.queryProductSkusBySeqNum111(activityCode, seqNum), ProductDetailDTO.class);
    }

    /**
     * 获得对应缓存的sku数据条数
     */
    @Transactional
    public Integer getSkuSize(ActivityDomain activityDomain){
        Integer size = 0;
        //有上传文件
        //组装key
        if (StringUtils.isNotEmpty(activityDomain.getSkuToken())){
            StringBuilder key = new StringBuilder();
            key.append(Constants.PROMOTION_ACTIVITY_SKU_CREATE).append(TENANT_CODE).append(activityDomain.getTenantCode()).append(SKU_TOKEN).append(activityDomain.getSkuToken());
            try{
                Set<String> keys = redisTemplate.<String, String> opsForHash().keys(key.toString());
                if (!CollectionUtils.isEmpty(keys)){
                    List<String> list = redisTemplate.<String, String> opsForHash().multiGet(key.toString(), keys);
                    size = list.stream().filter(x -> !StringUtils.isEmpty(x)).collect(Collectors.toList()).size();
                }
            }catch (Exception e){
                log.error("促销redis服务挂了,商品sku信息数量未查出", e);
            }
        }
        return size;
    }

}
