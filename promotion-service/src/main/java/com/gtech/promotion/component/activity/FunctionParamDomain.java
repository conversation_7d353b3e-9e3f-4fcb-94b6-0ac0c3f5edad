/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.component.activity;

import com.gtech.promotion.checker.activity.TPromoActivityChecker;
import com.gtech.promotion.code.activity.FuncTypeEnum;
import com.gtech.promotion.code.activity.FunctionParamTypeEnum;
import com.gtech.promotion.code.coupon.FaceUnitEnum;
import com.gtech.promotion.dao.model.activity.ActivityFunctionParamRankModel;
import com.gtech.promotion.dao.model.activity.FunctionParamModel;
import com.gtech.promotion.dto.cache.ActivityCacheDTO;
import com.gtech.promotion.dto.in.coupon.ConditionAndFace;
import com.gtech.promotion.exception.Check;
import com.gtech.promotion.service.activity.ActivityFuncParamService;
import com.gtech.promotion.service.activity.ActivityFuncRankService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <功能描述>
 */
@Service
public class FunctionParamDomain {

    @Autowired
    private ActivityFuncRankService promoActivityFuncRankService;

    @Autowired
    private ActivityFuncParamService promoActivityFuncParamService;

    /**
     * 根据活动id获取条件值和面值
     */
    public ConditionAndFace findConditionAndFaceByActivityCode(String activityCode) {

        List<ActivityFunctionParamRankModel> rankList = promoActivityFuncRankService.getRankListByActivityCode(activityCode);
        Check.check(CollectionUtils.isEmpty(rankList), TPromoActivityChecker.NOT_NULL);

        ActivityFunctionParamRankModel rankVO = rankList.get(0);

        List<FunctionParamModel> params = promoActivityFuncParamService.getRuleFuncParamListByRankId(rankVO.getId());
        Check.check(CollectionUtils.isEmpty(params), TPromoActivityChecker.NOT_NULL);

        final ConditionAndFace conditionAndFace = new ConditionAndFace();
        params.forEach(x->{
            if (FunctionParamTypeEnum.NUMBER.equalsCode(x.getParamType())){
                if (FuncTypeEnum.PARAM.equalsCode(x.getFunctionType())){

                    conditionAndFace.setConditionUnit(x.getParamUnit());
                    conditionAndFace.setConditionValue(new BigDecimal(x.getParamValue()));

                }else if (FuncTypeEnum.INCENTIVE.equalsCode(x.getFunctionType())){

                    if (FuncTypeEnum.IncentiveEnum.DISCOUNT.equalsCode(x.getFunctionCode())){
                        conditionAndFace.setFaceUnit(FaceUnitEnum.DISCOUNT.code());
                        conditionAndFace.setFaceValue(new BigDecimal(x.getParamValue()).multiply(new BigDecimal(10)));
                    }else{
                        conditionAndFace.setFaceUnit(FaceUnitEnum.MONEY.code());
                        conditionAndFace.setFaceValue(new BigDecimal(x.getParamValue()));
                    }
                }
            }
        });
        return conditionAndFace;
    }

    /**
     * 根据活动id获取条件值和面值
     */
    public Map<String,ConditionAndFace> findConditionAndFaceByActivityCodes(String tenantCode, List<String> activityCodes) {

        List<ActivityFunctionParamRankModel> rankList = promoActivityFuncRankService.getRankListByActivityCodes(tenantCode,activityCodes);
        Check.check(CollectionUtils.isEmpty(rankList), TPromoActivityChecker.NOT_NULL);

        Map<String, List<ActivityFunctionParamRankModel>> collect = rankList.stream().collect(Collectors.groupingBy(ActivityFunctionParamRankModel::getId));

        Iterator<List<ActivityFunctionParamRankModel>> iterator1 = collect.values().iterator();

        Map<String,ConditionAndFace> map = new HashMap<>();

        while (iterator1.hasNext()){
            List<ActivityFunctionParamRankModel> next = iterator1.next();
            ActivityFunctionParamRankModel rankVO = next.get(0);
            List<FunctionParamModel> params = promoActivityFuncParamService.getRuleFuncParamListByRankId(rankVO.getId());
            ConditionAndFace conditionAndFace = setParamMap(params);

            map.put(rankVO.getActivityCode(),conditionAndFace);

        }

        return map;
    }



    public Map<String,ConditionAndFace> findConditionAndFaceByActivityCodesCache(List<ActivityCacheDTO> activityCacheDTO) {
        //PromoFuncRanks 合并到一个list里面
        Map<String,ConditionAndFace> map = new HashMap<>();
        for (ActivityCacheDTO cacheDTO : activityCacheDTO) {
            List<FunctionParamModel> paramList = cacheDTO.getPromoFuncParams();
            ConditionAndFace conditionAndFace = setParamMap(paramList);
            map.put(cacheDTO.getActivityModel().getActivityCode(), conditionAndFace);
        }
    
        return map;
    }

    public ConditionAndFace setParamMap(List<FunctionParamModel> paramList) {
        Check.check(CollectionUtils.isEmpty(paramList), TPromoActivityChecker.NOT_NULL);
        ConditionAndFace conditionAndFace = new ConditionAndFace();
        paramList.forEach(x -> {
            if (FunctionParamTypeEnum.NUMBER.equalsCode(x.getParamType())) {
                if (FuncTypeEnum.PARAM.equalsCode(x.getFunctionType())) {
                    conditionAndFace.setConditionValue(new BigDecimal(x.getParamValue()));
                    conditionAndFace.setConditionUnit(x.getParamUnit());
                } else if (FuncTypeEnum.INCENTIVE.equalsCode(x.getFunctionType())) {
                    if (FuncTypeEnum.IncentiveEnum.DISCOUNT.equalsCode(x.getFunctionCode())) {
                        conditionAndFace.setFaceValue(new BigDecimal(x.getParamValue()).multiply(new BigDecimal(10)));
                        conditionAndFace.setFaceUnit(FaceUnitEnum.DISCOUNT.code());
                    } else {
                        conditionAndFace.setFaceUnit(FaceUnitEnum.MONEY.code());
                        conditionAndFace.setFaceValue(new BigDecimal(x.getParamValue()));
                    }
                }
            }
        });
        return conditionAndFace;
    }


}
