package com.gtech.promotion.component.purchaseconstraint;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.gtech.commons.exception.GTechBaseException;
import com.gtech.commons.result.Result;
import com.gtech.commons.utils.DateUtil;
import com.gtech.commons.utils.StringUtil;
import com.gtech.pim.request.BaseQueryProductRequest;
import com.gtech.pim.request.CatalogAttrRequest;
import com.gtech.pim.request.CatalogQueryProductRequest;
import com.gtech.pim.response.CatalogProductBaseResponse;
import com.gtech.promotion.RedisClient;
import com.gtech.promotion.checker.purchaseconstraint.PurchaseConstraintChecker;
import com.gtech.promotion.checker.purchaseconstraint.PurchaseConstraintRuleChecker;
import com.gtech.promotion.code.PromotionConstants;
import com.gtech.promotion.code.purchaseconstraint.PcRuleCalculateTypeEnum;
import com.gtech.promotion.code.purchaseconstraint.PurchaseConstraintRuleTimeTypeEnum;
import com.gtech.promotion.code.purchaseconstraint.PurchaseConstraintRuleTypeEnum;
import com.gtech.promotion.component.purchaseconstraint.dto.PcRuleCacheValuePair;
import com.gtech.promotion.component.purchaseconstraint.dto.PcRuleCheckResult;
import com.gtech.promotion.dao.entity.purchaseconstraint.PurchaseConstraintCustomer;
import com.gtech.promotion.dao.entity.purchaseconstraint.PurchaseConstraintDetail;
import com.gtech.promotion.dao.model.purchaseconstraint.PcRuleCalculateModel;
import com.gtech.promotion.dao.model.purchaseconstraint.PurchaseConstraintCustomerListMode;
import com.gtech.promotion.dao.model.purchaseconstraint.PurchaseConstraintDetailListModel;
import com.gtech.promotion.dto.cache.PurchaseConstraintCacheDTO;
import com.gtech.promotion.dto.in.purchaseconstraint.PcRuleOverlayCheckDTO;
import com.gtech.promotion.exception.PromotionException;
import com.gtech.promotion.feign.CatalogClientConsumer;
import com.gtech.promotion.feign.OrderClientConsumer;
import com.gtech.promotion.feign.enums.ConditionTypeEnum;
import com.gtech.promotion.feign.request.CustomOrderSkuStatisticsQuery;
import com.gtech.promotion.feign.request.QueryMemberPurchaseRequest;
import com.gtech.promotion.feign.response.CustomOrderSkuStatisticsQueryOut;
import com.gtech.promotion.feign.response.MemberPurchaseStatisticsResp;
import com.gtech.promotion.service.purchaseconstraint.PurchaseConstraintCustomerService;
import com.gtech.promotion.service.purchaseconstraint.PurchaseConstraintDetailService;
import com.gtech.promotion.vo.bean.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.util.DigestUtils;

import java.math.BigDecimal;
import java.text.MessageFormat;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.function.Supplier;
import java.util.stream.Collectors;

import static com.gtech.promotion.checker.purchaseconstraint.PurchaseConstraintRuleChecker.*;
import static com.gtech.promotion.code.activity.ProductSelectionTypeEnum.EXCLUDE;
import static com.gtech.promotion.code.activity.ProductSelectionTypeEnum.INCLUDE;
import static com.gtech.promotion.code.activity.ProductTypeEnum.CUSTOM_PRODUCT;
import static com.gtech.promotion.code.activity.ProductTypeEnum.CUSTOM_RANGE;
import static com.gtech.promotion.code.purchaseconstraint.PcRuleCalculateTypeEnum.INCREMENT;
import static com.gtech.promotion.code.purchaseconstraint.PurchaseConstraintRuleTypeEnum.CUSTOMER_MAX_QTY_PER_PRODUCT;
import static com.gtech.promotion.code.purchaseconstraint.PurchaseConstraintRuleTypeEnum.valueOfCode;
import static com.gtech.promotion.component.activity.ActivityCacheDomain.PRODUCT_RELATION_ALL;

@Slf4j
@Component
public class PcRuleComponent {
    /**
     * 限购缓存增量读写 lock key
     * 租户 userCode
     */
    public static final String PROMOTION_PC_INCREMENT_LOCK_KEY = "PROMOTION:PC:INCREMENT_LOCK:%s:%s";

    public static final ThreadLocal<Map<String, PurchaseConstraintCustomer>> LOCAL_CUSTOMER_MAP = new InheritableThreadLocal<>();
    @Autowired
    private RedisClient redisClient;
    @Autowired
    private PurchaseConstraintComponent pcComponent;
    @Autowired
    private CatalogClientConsumer catalogClientConsumer;
    @Autowired
    private OrderClientConsumer orderClientConsumer;
    @Autowired
    private PurchaseConstraintCustomerService pcCustomerService;
    @Autowired
    private PurchaseConstraintDetailService pcDetailService;
    @Autowired
    private TransactionTemplate transactionTemplate;
    /**
     * 限购缓存key过期时间: 默认5分钟
     */
    @Value("${promotion.pc.rule.redis.expired:5}")
    private Long expired;

    @Value("${pcrule.check.oms:false}")
    private boolean checkOms;

    /**
     * 限购检查,限购增量操作
     *
     * @return 如果存在检查不通过的 才会存在结果; 否则null
     */
    public Result<PcRuleCheckResult> pcRuleIncrement(PcRuleCalculateModel model, Map<String,Map<Integer, String>> ruleTypeMap) {
        log.info("pcRuleIncrement#param: {}", model);
        model.valid();
        // 重复请求校验
        if (!this.checkIdempotent(model)) {
            return null;
        }
        PcRuleCalculateTypeEnum type = PcRuleCalculateTypeEnum.getByCode(model.getType());
        switch (Objects.requireNonNull(type)) {
            case INCREMENT:
                if (Boolean.FALSE.equals(model.getForward())) {
                    this.decrement(model);
                } else {
                    List<Result<PcRuleCheckResult>> checkResults = this.ruleCheck(model,ruleTypeMap);
                    // 没有检查结果; 直接返回
                    if (CollectionUtils.isEmpty(checkResults)) {
                        return null;
                    }
                    // 有异常的检查结果; 直接返回
                    Optional<Result<PcRuleCheckResult>> first = checkResults.stream().filter(f -> !f.isSuccess()).findFirst();
                    if (first.isPresent()) {
                        Result<PcRuleCheckResult> pcRuleCheckResultResult = first.get();
                        log.warn("限购增联操作#限购检查不通过: {}", pcRuleCheckResultResult);
                        return pcRuleCheckResultResult;
                    }

                    // 限购增量加
                    List<PcRuleCheckResult> collect = checkResults.stream().map(Result::getData).collect(Collectors.toList());
                    this.ruleIncrement(model, collect);


                }
                break;
            case DECREMENT:
                this.decrement(model);
                return null;
            case CHECK:
                Optional<Result<PcRuleCheckResult>> first = this.ruleCheck(model,ruleTypeMap).stream().filter(f -> !f.isSuccess()).findFirst();
                if (first.isPresent()) {
                    Result<PcRuleCheckResult> pcRuleCheckResultResult = first.get();
                    log.warn("限购检查#限购检查不通过: {}", pcRuleCheckResultResult);
                    return pcRuleCheckResultResult;
                }

        }
        return null;
    }

    private List<Result<PcRuleCheckResult>> ruleCheck(PcRuleCalculateModel model, Map<String,Map<Integer, String>> ruleTypeMap) {
        // 限购规则过滤
        List<PurchaseConstraintCacheDTO> allPcRuleCacheList = this.filterRules(model);
        // 获取有效的增量商品集合
        List<PcRuleCalculateModel.IncrementProduct> incrementProducts = this.filterIncrementProducts(model, allPcRuleCacheList);
        // 获取sku命中的限购信息集合;
        Map<String, List<PurchaseConstraintCacheDTO>> skuPcMap = this.skuMappingPcMap(incrementProducts, allPcRuleCacheList);
        if (MapUtils.isEmpty(skuPcMap)) {
            return Collections.emptyList();
        }
        // 所有商品增量检查结果
        List<Result<PcRuleCheckResult>> checkResults = new ArrayList<>();

        HashMap<String,BigDecimal> maxAmount = new HashMap<>();
        HashMap<String,BigDecimal> maxQty = new HashMap<>();
        HashMap<String,Map<String,BigDecimal>> maxEachQty = new HashMap<>();


        incrementProductFor:for (PcRuleCalculateModel.IncrementProduct incrementProduct : incrementProducts) {
            List<PurchaseConstraintCacheDTO> pcRuleCacheList = skuPcMap.get(incrementProduct.getSkuCode());
            for (PurchaseConstraintCacheDTO pc : pcRuleCacheList) {
                // 是否有异常的标识
                for (PurchaseConstraintRule pcRule : pc.getPurchaseConstraintRuleList()) {
                    // 执行检查,增量
                    Result<PcRuleCheckResult> resultResult = this.ruleCheck(model, incrementProduct, pc, pcRule,
                            () -> this.getCustomerMap(model, incrementProducts, skuPcMap),ruleTypeMap,maxAmount,maxQty,maxEachQty);
                    checkResults.add(resultResult);
                    if (!resultResult.isSuccess()) {
                        log.error("pcRuleIncrement#限购检查失败: {}", resultResult.getMessage());
                        break incrementProductFor;
                    }
                }

            }
        }
        List<PurchaseConstraintCustomer> customers = new ArrayList<>();
        List<PurchaseConstraintDetail> details = new ArrayList<>();

        List<PcRuleCheckResult> pcRuleCheckResults = checkResults.stream().map(Result::getData)
                .filter(f -> f.getCustomer() != null)
                .collect(Collectors.toList());



        //查询oms订单明细
        pcRuleCheckResultsForeach(pcRuleCheckResults, customers, details);

        //防止冲突
        List<PurchaseConstraintCustomer> customersInserts = new ArrayList<>(customers.stream()
                // 合并,防止累计金额类型重复
                .collect(Collectors.toMap(x -> x.getCustomerCode(), a -> a,
                        (a, b) -> {
                            a.setPurchaseConstraintValueUsed(a.getPurchaseConstraintValueUsed().add(b.getPurchaseConstraintValueUsed()));
                            return a;
                        }))
                .values());

        List<PurchaseConstraintDetail> detailInserts = new ArrayList<>(details.stream()
                // 存在相同限购,不同规则,合并编码
                .collect(Collectors.toMap(x -> x.getOrderId() + x.getSkuCode() + x.getPurchaseConstraintCode(), a -> a,
                        (a, b) -> {
                            a.setCustomerCode(a.getCustomerCode() + "," + b.getCustomerCode());
                            return a;
                        }))
                .values());



        transactionTemplate.execute(transactionStatus -> {
            try {
                pcCustomerService.insert(customersInserts);
                pcDetailService.insert(detailInserts);
            } catch (Exception e) {
                log.error("限购检查插入数据失败: {}", e.getMessage());
                throw new GTechBaseException(e);
            }
            return null;
        });
        return checkResults;
    }

    public void pcRuleCheckResultsForeach(List<PcRuleCheckResult> pcRuleCheckResults, List<PurchaseConstraintCustomer> customers, List<PurchaseConstraintDetail> details) {
        for (PcRuleCheckResult pcRuleCheckResult : pcRuleCheckResults) {
            PurchaseConstraintCustomer customer = pcRuleCheckResult.getCustomer();
            customers.add(customer);

            //如果不检查oms,则不查询oms订单明细
            if (!checkOms || customer.getPurchaseConstraintValueUsed().compareTo(BigDecimal.ZERO) == 0){
                continue;
            }


            PcRuleOverlayCheckDTO pcRuleCheckDTO = pcRuleCheckResult.getPcRuleCheckDTO();
            List<CustomOrderSkuStatisticsQueryOut> list = this.queryCustomOrderSkuStatistics(pcRuleCheckDTO);
            if (CollectionUtils.isEmpty(list)) {
                continue;
            }
            for (CustomOrderSkuStatisticsQueryOut m : list) {
                for (CustomOrderSkuStatisticsQueryOut.OrderSkuStatisticsQueryItem item : m.getStatisticsItems()) {
                    PurchaseConstraintDetail detail = new PurchaseConstraintDetail();
                    detail.setTenantCode(pcRuleCheckDTO.getTenantCode());
                    detail.setCustomerCode(customer.getCustomerCode());
                    detail.setPurchaseConstraintCode(pcRuleCheckDTO.getPc().getPurchaseConstraintCode());
                    detail.setOrderId(m.getOrderCode());
                    detail.setProductCode(item.getProductCode());
                    detail.setSkuCode(item.getSkuCode());
                    detail.setDetailAmount(item.getAmount());
                    detail.setDetailQty(item.getCount());
                    detail.setDetailAmountBack(item.getReturnAmount().add(item.getCancelAmount()));
                    detail.setDetailQtyBack((item.getReturnCount() + item.getCancelCount()));
                    detail.setCreateTime(new Date());
                    detail.setUpdateTime(new Date());
                    details.add(detail);
                }
            }
        }
    }

    /**
     * 批量查询customer集合;
     * 先查询线程对象;
     * 如果不存在再查询数据库;
     *
     * @return k=customerCode; value=customer对象
     */
    public Map<String, PurchaseConstraintCustomer> getCustomerMap(PcRuleCalculateModel model, List<PcRuleCalculateModel.IncrementProduct> incrementProducts, Map<String, List<PurchaseConstraintCacheDTO>> skuPcMap) {

        Map<String, PurchaseConstraintCustomer> customerMap;
        List<String> customerCodes = new ArrayList<>();
        for (PcRuleCalculateModel.IncrementProduct incrementProduct : incrementProducts) {
            List<PurchaseConstraintCacheDTO> pcRuleCacheList = skuPcMap.get(incrementProduct.getSkuCode());
            for (PurchaseConstraintCacheDTO pc : pcRuleCacheList) {
                for (PurchaseConstraintRule pcRule : pc.getPurchaseConstraintRuleList()) {
                    Date statisticsBegin = PcUtil.getStatisticsBegin(pcRule.getPurchaseConstraintRuleTimeType(), pcRule.getPurchaseConstraintRuleTimeValue());
                    Date statisticsEnd = PcUtil.getStatisticsEnd(pcRule.getPurchaseConstraintRuleTimeType(), pcRule.getPurchaseConstraintRuleTimeValue());
                    customerCodes.add(getMd5DigestAsHex(model, incrementProduct, pc, pcRule, statisticsBegin, statisticsEnd));
                }
            }
        }
        PurchaseConstraintCustomerListMode customerListMode = new PurchaseConstraintCustomerListMode();
        customerListMode.setTenantCode(model.getTenantCode());
        customerListMode.setCustomerCodes(customerCodes);
        List<PurchaseConstraintCustomer> customers = pcCustomerService.list(customerListMode);
        customerMap = Optional.ofNullable(customers)
                .filter(CollectionUtils::isNotEmpty)
                .map(mList -> mList.stream().collect(Collectors.toMap(PurchaseConstraintCustomer::getCustomerCode, a -> a)))
                .orElseGet(HashMap::new);
        return customerMap;
    }

    private String getMd5DigestAsHex(PcRuleCalculateModel model,
                                     PcRuleCalculateModel.IncrementProduct incrementProduct,
                                     PurchaseConstraintCacheDTO pc,
                                     PurchaseConstraintRule pcRule,
                                     Date statisticsBegin,
                                     Date statisticsEnd) {
        return DigestUtils.md5DigestAsHex(JSON.toJSONString(PcRuleCacheValuePair.builder()
                        .tenantCode(model.getTenantCode())
                        .pcCode(pc.getPurchaseConstraintCode())
                        .userCode(model.getUserCode())
                        .productCode(incrementProduct.getProductCode())
                        .statisticsBegin(statisticsBegin)
                        .statisticsEnd(statisticsEnd)
                        .build()
                        .getPcRuleKey(pcRule.getPurchaseConstraintRuleType()))
                .getBytes());
    }

    private List<String> queryPimProductCode(String tenantCode, String orgCode, ProductScope productScope) {

        CatalogQueryProductRequest request = new CatalogQueryProductRequest();
        request.setTenantCode(tenantCode);
        request.setOrgCode(orgCode);
        if (StringUtils.isNotBlank(productScope.getBrandCode()) && !PromotionConstants.UNLIMITED.equals(productScope.getBrandCode())) {
            request.setBrandCodeList(Arrays.asList(productScope.getBrandCode().split(",")));
        }
        if (StringUtils.isNotBlank(productScope.getCategoryCode()) && !PromotionConstants.UNLIMITED.equals(productScope.getBrandCode())) {
            request.setCategoryCodeList(Arrays.asList(productScope.getCategoryCode().split(",")));
        }
        List<CatalogAttrRequest> catalogAttrRequests = Optional.ofNullable(productScope.getAttributes())
                .map(x -> x.stream().map(m -> {
                    CatalogAttrRequest catalogAttrRequest = new CatalogAttrRequest();
                    catalogAttrRequest.setAttrCode(m.getAttributeCode());
                    catalogAttrRequest.setAttrType(2);
                    catalogAttrRequest.setAttrValue(m.getAttributeValues().split(","));
                    return catalogAttrRequest;
                }).collect(Collectors.toList()))
                .orElse(new ArrayList<>());
        Optional.ofNullable(productScope.getSpuAttributes())
                .ifPresent(x -> x.forEach(m -> {
                    CatalogAttrRequest catalogAttrRequest = new CatalogAttrRequest();
                    catalogAttrRequest.setAttrCode(m.getAttributeCode());
                    catalogAttrRequest.setAttrType(1);
                    catalogAttrRequest.setAttrValue(m.getAttributeValues().split(","));
                    catalogAttrRequests.add(catalogAttrRequest);
                }));
        if (CollectionUtils.isNotEmpty(catalogAttrRequests)) {
            List<List<CatalogAttrRequest>> attrList = new ArrayList<>();
            attrList.add(catalogAttrRequests);
            request.setAttrList(attrList);
        }
        request.setReturnFields("productCode");
        if (StringUtil.isNotBlank(productScope.getProductTag())) {
            List<List<BaseQueryProductRequest.Condition>> conditionList = new ArrayList<>();
            BaseQueryProductRequest.Condition condition = new BaseQueryProductRequest.Condition();
            condition.setKey("tags.tagCode");
            condition.setType(ConditionTypeEnum.IN.getCode());
            condition.setValues(productScope.getProductTag().split(","));
            conditionList.add(Lists.newArrayList(condition));
            request.setConditionList(conditionList);
        }

        return catalogClientConsumer.searchProductAll(request)
                .stream()
                .map(CatalogProductBaseResponse::getProductCode)
                .collect(Collectors.toList());

    }


    private List<MemberPurchaseStatisticsResp> getMemberPurchaseStatisticsResps(PcRuleCalculateModel.IncrementProduct increment, PcRuleOverlayCheckDTO pcRuleCheckDTO) {
        QueryMemberPurchaseRequest memberPurchaseRequest = new QueryMemberPurchaseRequest();
        memberPurchaseRequest.setTenantCode(pcRuleCheckDTO.getTenantCode());
        memberPurchaseRequest.setMemberCode(pcRuleCheckDTO.getUserCode());
        memberPurchaseRequest.setCreateTimeStart(pcRuleCheckDTO.getStatisticsBegin());
        memberPurchaseRequest.setCreateTimeEnd(pcRuleCheckDTO.getStatisticsEnd());
        memberPurchaseRequest.setProductCodes(Lists.newArrayList(increment.getProductCode()));
        return Optional.ofNullable(orderClientConsumer.queryMemberPurchaseStatistics(memberPurchaseRequest))
                .orElse(new ArrayList<>());
    }


    /**
     * 限购增量操作
     * <p>
     * 1. 插入限购订单明细
     * 2. 增量限购统计
     * 3. 增量Redis缓存
     */
    private void ruleIncrement(PcRuleCalculateModel model, List<PcRuleCheckResult> ruleIncrementResults) {
        String tenantCode = model.getTenantCode();
        //根据sku命中限购分组
        Map<String, List<PcRuleCheckResult>> skuHitPcGroupMap = ruleIncrementResults.stream()
                .collect(Collectors.groupingBy(x -> x.getPcCode() + x.getSkuCode()));

        // 限购明细待持久化集合
        List<PurchaseConstraintDetail> pcDetails = new ArrayList<>();
        // 限购统计待增量集合
        List<PurchaseConstraintCustomer> customers = new ArrayList<>();

        skuHitPcGroupMap.forEach((key, value) -> {
            for (PcRuleCheckResult ruleIncrementResult : value) {
                // 基础数据准备
                BigDecimal incrementValue = ruleIncrementResult.getValue();
                PcRuleOverlayCheckDTO pcRuleCheckDTO = ruleIncrementResult.getPcRuleCheckDTO();
                PurchaseConstraintCacheDTO pc = pcRuleCheckDTO.getPc();
                PurchaseConstraintRule pcRule = ruleIncrementResult.getPcRule();
                PcRuleCalculateModel.IncrementProduct increment = pcRuleCheckDTO.getIncrement();

                // 当前订单的限购详情
                PurchaseConstraintDetail detail = new PurchaseConstraintDetail();
                detail.setCustomerCode(pcRuleCheckDTO.getCacheKeyMd5());
                detail.setTenantCode(tenantCode);
                detail.setOrderId(model.getOrderCode());
                detail.setPurchaseConstraintCode(pc.getPurchaseConstraintCode());
                detail.setProductCode(ruleIncrementResult.getProductCode());
                detail.setSkuCode(ruleIncrementResult.getSkuCode());
                detail.setDetailAmount(increment.getSellAmount());
                detail.setDetailQty(increment.getSkuCount());
                detail.setDetailAmountBack(BigDecimal.ZERO);
                detail.setDetailQtyBack(0);
                detail.setCreateTime(new Date());
                detail.setUpdateTime(new Date());
                pcDetails.add(detail);

                // 历史订单的限购详情
                PurchaseConstraintCustomer customer = new PurchaseConstraintCustomer();
                customer.setTenantCode(tenantCode);
                customer.setCustomerCode(pcRuleCheckDTO.getCacheKeyMd5());
                customer.setPurchaseConstraintCode(pc.getPurchaseConstraintCode());
                customer.setPurchaseConstraintRuleType(pcRule.getPurchaseConstraintRuleType());
                customer.setUserCode(pcRuleCheckDTO.getUserCode());
                customer.setProductCode(ruleIncrementResult.getProductCode());
                customer.setPurchaseConstraintValue(new BigDecimal(pcRule.getPurchaseConstraintValue()));
                customer.setPurchaseConstraintValueUsed(incrementValue);
                customer.setCustomerStartTime(pcRuleCheckDTO.getStatisticsBegin());
                customer.setCustomerEndTime(pcRuleCheckDTO.getStatisticsEnd());
                customer.setUpdateTime(new Date());
                customers.add(customer);
            }
        });

        List<PurchaseConstraintCustomer> customersInserts = new ArrayList<>(customers.stream()
                // 合并,防止累计金额类型重复
                .collect(Collectors.toMap(x -> x.getCustomerCode(), a -> a,
                        (a, b) -> {
                            a.setPurchaseConstraintValueUsed(a.getPurchaseConstraintValueUsed().add(b.getPurchaseConstraintValueUsed()));
                            return a;
                        }))
                .values());

        transactionTemplate.execute(transactionStatus -> {
            customersIncrement(customersInserts, pcDetails);
            return null;
        });
        //清空缓存
        clearPcRuleCache(model);

    }

    public void customersIncrement(List<PurchaseConstraintCustomer> customers, List<PurchaseConstraintDetail> pcDetails) {
        if (pcCustomerService.increment(customers) == customers.size()) {
            List<PurchaseConstraintDetail> detailInserts = new ArrayList<>(pcDetails.stream()
                    .collect(Collectors.toMap(x -> x.getOrderId() + x.getSkuCode() + x.getPurchaseConstraintCode(), a -> a,
                            (a, b) -> {
                                // 存在相同限购,不同规则,合并编码
                                a.setCustomerCode(a.getCustomerCode() + "," + b.getCustomerCode());
                                // 兼容当前订单取最大值
                                a.setDetailAmount(a.getDetailAmount().max(b.getDetailAmount()));
                                a.setDetailQty(Math.max(a.getDetailQty(), b.getDetailQty()));
                                return a;
                            }))
                    .values());
            pcDetailService.insert(detailInserts);
        } else {
            log.error("com/gtech/promotion/component/purchaseconstraint/PcRuleComponent.java:440#限购插入数据失败");
            throw new PromotionException(PurchaseConstraintChecker.INCREMENT_ERROR);
        }
        List<String> cs = customers.stream().map(PurchaseConstraintCustomer::getCustomerCode).collect(Collectors.toList());
        redisClient.delete(cs);
    }

    /**
     * sku映射限购集合,移除未映射的限购商品
     *
     * @return k=skuCode;value=限购集合
     */
    private Map<String, List<PurchaseConstraintCacheDTO>> skuMappingPcMap(List<PcRuleCalculateModel.IncrementProduct> incrementProducts, List<PurchaseConstraintCacheDTO> allPcRuleCacheList) {
        if (CollectionUtils.isEmpty(incrementProducts)) {
            return Collections.emptyMap();
        }

        allPcRuleCacheList = allPcRuleCacheList.stream()
                .sorted((pc1, pc2) -> {
                    if (pc1.getPriority().equals(pc2.getPriority())) {
                        return Long.compare(pc2.getCreateTime().getTime(), pc1.getCreateTime().getTime());
                    }
                    return Integer.compare(pc1.getPriority(), pc2.getPriority());
                })
                .collect(Collectors.toList());

        // sku 命中限购集合
        List<PurchaseConstraintCacheDTO> finalAllPcRuleCacheList = allPcRuleCacheList;
        Function<PcRuleCalculateModel.IncrementProduct, List<PurchaseConstraintCacheDTO>> incrementProductListFunction = x -> Optional
                .ofNullable(pcComponent.filterPurchaseConstraintByProduct(finalAllPcRuleCacheList, getProductCodes(x))).orElseGet(Collections::emptyList);
        // sku映射限购集合
        Map<String, List<PurchaseConstraintCacheDTO>> skuPcMap = incrementProducts.stream().collect(Collectors.toMap(PcRuleCalculateModel.IncrementProduct::getSkuCode, incrementProductListFunction));
        // 移除未命中限购的限购商品
        incrementProducts.removeIf(incrementProduct -> CollectionUtils.isEmpty(skuPcMap.get(incrementProduct.getSkuCode())));

        //再次移出高级价格限购活动
        incrementProducts.forEach(product->{
            List<PurchaseConstraintCacheDTO> purchaseConstraintCacheDTOS = skuPcMap.get(product.getSkuCode());
            if (product.getPriceSetting() == 0){
                purchaseConstraintCacheDTOS.removeIf(purchaseConstraintCacheDTO -> purchaseConstraintCacheDTO.getPriceSetting() == 1);
            }
        });


        return skuPcMap;
    }

    private ProductCodes getProductCodes(PcRuleCalculateModel.IncrementProduct incrementProduct) {
        return ProductCodes.builder()
                .categoryCodes(incrementProduct.getCategoryCodes())
                .brandCode(incrementProduct.getBrandCode())
                .productCode(incrementProduct.getProductCode())
                .skuCode(incrementProduct.getSkuCode())
                .attributes(incrementProduct.getAttributes())
                .productTag(incrementProduct.getProductTag())
                .spuAttributes(incrementProduct.getSpuAttributes())
                .build();
    }

    private List<PcRuleCalculateModel.IncrementProduct> filterIncrementProducts(PcRuleCalculateModel model, List<PurchaseConstraintCacheDTO> allPcRuleCacheList) {
        if (CollectionUtils.isEmpty(allPcRuleCacheList)) {
            return Collections.emptyList();
        }
        Map<String, PurchaseConstraintCacheDTO> pcMap = allPcRuleCacheList.stream()
                .collect(Collectors.toMap(PurchaseConstraintCacheDTO::getPurchaseConstraintCode, a -> a));
        // 过滤增量商品
        List<PcRuleCalculateModel.IncrementProduct> incrementProducts = model.getIncrementProducts();
        // 开启增量加
        if (INCREMENT.equalsCode(model.getType())) {
            // 过滤商品命中的限购
            incrementProducts.removeIf(product -> {
                product.getHitPcCodes().removeIf(pcCode -> pcMap.get(pcCode) == null);
                return product.getHitPcCodes().isEmpty();
            });
            // 移除没有命中限购的商品
            incrementProducts.removeIf(product -> CollectionUtils.isEmpty(product.getHitPcCodes()));
            // 高级价格限购
        }
        return incrementProducts;
    }

    /**
     * 验证高级价格限购
     *
     * @return true: 高级价格限购验证通过; false:不通过
     */
    public boolean checkPriceSetting(PcRuleCalculateModel.IncrementProduct product, Map<String, PurchaseConstraintCacheDTO> pcMap) {
        for (String hitPcCode : product.getHitPcCodes()) {
            PurchaseConstraintCacheDTO purchaseConstraintCacheDTO = pcMap.get(hitPcCode);
            Integer priceSetting = purchaseConstraintCacheDTO.getPriceSetting();
            // 如果限购开启高级价格限购
            if (Objects.equals(priceSetting, 1)) {
                return Objects.equals(product.getPriceSetting(), priceSetting);
            }
        }

        return true;
    }

    private void decrement(PcRuleCalculateModel model) {
        log.info("decrement#param: {}", JSON.toJSONString(model));
        List<PcRuleCalculateModel.IncrementProduct> incrementProducts = model.getIncrementProducts();
        List<String> skuCodes = incrementProducts.stream().map(PcRuleCalculateModel.IncrementProduct::getSkuCode).collect(Collectors.toList());
        Map<String, PcRuleCalculateModel.IncrementProduct> skuMap = incrementProducts.stream().collect(Collectors.toMap(PcRuleCalculateModel.IncrementProduct::getSkuCode, a -> a, (a, b) -> a));
        PurchaseConstraintDetailListModel listModel = new PurchaseConstraintDetailListModel();
        listModel.setTenantCode(model.getTenantCode());
        listModel.setOrderCodes(Lists.newArrayList(model.getOrderCode()));
        listModel.setSkuCodes(skuCodes);
        // 查询限购订单明细
        List<PurchaseConstraintDetail> list = pcDetailService.list(listModel);
        log.info("decrement#orderCode{};#递减列表{}", model.getOrderCode(), JSON.toJSONString(list));
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        pcCustomerService.decrement(model, list, skuMap);
    }


    /**
     * 限购累计规则检查
     * <p>
     * 如果检查失败,redis缓存和保留
     */
    private Result<PcRuleCheckResult> ruleCheck(PcRuleCalculateModel model,
                                                PcRuleCalculateModel.IncrementProduct increment,
                                                PurchaseConstraintCacheDTO pc,
                                                PurchaseConstraintRule pcRule,
                                                Supplier<Map<String, PurchaseConstraintCustomer>> customerMap,
                                                Map<String,Map<Integer, String>> ruleTypeMap,
                                                Map<String , BigDecimal> maxAmount,
                                                Map<String, BigDecimal> maxQty,
                                                HashMap<String, Map<String, BigDecimal>> maxEachQty) {
        Date statisticsBegin = PcUtil.getStatisticsBegin(PurchaseConstraintRuleTimeTypeEnum.getByCode(pcRule.getPurchaseConstraintRuleTimeType()), pcRule.getPurchaseConstraintRuleTimeValue());
        Date statisticsEnd = PcUtil.getStatisticsEnd(PurchaseConstraintRuleTimeTypeEnum.getByCode(pcRule.getPurchaseConstraintRuleTimeType()), pcRule.getPurchaseConstraintRuleTimeValue());
        log.info("ruleIncrement#计算统计时间范围: {} {}", statisticsBegin, statisticsEnd);
        // 构建累计规则检查对象
        PcRuleOverlayCheckDTO pcRuleCheckDTO = getPcRuleOverlayCheckDTO(model, increment, pc, pcRule, statisticsBegin, statisticsEnd);
        // 设置额度
        BigDecimal limit = new BigDecimal(pcRule.getPurchaseConstraintValue());
        // 获取增量值
        BigDecimal incrementValue = this.getIncrementValue(increment, pcRule.getPurchaseConstraintRuleType());
        // 限购检查增量结果基本信息
        PcRuleCheckResult ruleIncrementResult = this.getPcRuleCheckResult(increment, pc, pcRule, pcRuleCheckDTO, incrementValue);
        // 已用额度:Redis
        BigDecimal usedValue = this.getUsedValue(increment, pcRule, customerMap, pcRuleCheckDTO, ruleIncrementResult);
        // 设置额度 - 增量值 - 已用额度 < 0  抛出异常
        //这里添加一个开关priorityPcMap,如果不是优先级最高的限购,则不抛出异常
        if (increment.getHitPcCodes().contains(pc.getPurchaseConstraintCode())) {
            //设置最大金额和最大数量
            incrementValue = getMaxAmountOrMaxQty(pc, pcRule, maxAmount, maxQty,maxEachQty, incrementValue,increment);
            // 限购检查
            Result<PcRuleCheckResult> failed = getPcRuleCheckResultResult(pc, pcRule, limit, incrementValue, usedValue, ruleIncrementResult,ruleTypeMap.get(increment.getSkuCode()));
            if (failed != null) return failed;
        }

        return Result.ok(ruleIncrementResult);
    }

    private static BigDecimal getMaxAmountOrMaxQty(PurchaseConstraintCacheDTO pc, PurchaseConstraintRule pcRule, Map<String, BigDecimal> maxAmount, Map<String, BigDecimal> maxQty, HashMap<String, Map<String, BigDecimal>> maxEachQty, BigDecimal incrementValue, PcRuleCalculateModel.IncrementProduct increment) {
        if (PurchaseConstraintRuleTypeEnum.CUSTOMER_MAX_AMOUNT.getCode().equals(pcRule.getPurchaseConstraintRuleType())){
            //如果存在就累加,不存在就添加
            maxAmount.computeIfAbsent(pc.getPurchaseConstraintCode(), k->new BigDecimal(0));
            BigDecimal finalIncrementValue = incrementValue;
            maxAmount.computeIfPresent(pc.getPurchaseConstraintCode(),(k, v)->v.add(finalIncrementValue));
            incrementValue = maxAmount.get(pc.getPurchaseConstraintCode());
        }else if (PurchaseConstraintRuleTypeEnum.CUSTOMER_MAX_QTY_ALL_PRODUCTS.getCode().equals(pcRule.getPurchaseConstraintRuleType())){
            maxQty.computeIfAbsent(pc.getPurchaseConstraintCode(), k->new BigDecimal(0));
            BigDecimal finalIncrementValue = incrementValue;
            maxQty.computeIfPresent(pc.getPurchaseConstraintCode(),(k, v)->v.add(finalIncrementValue));
            incrementValue = maxQty.get(pc.getPurchaseConstraintCode());
        } else if (PurchaseConstraintRuleTypeEnum.CUSTOMER_MAX_QTY_PER_PRODUCT.getCode().equals(pcRule.getPurchaseConstraintRuleType())) {
            maxEachQty.computeIfAbsent(pc.getPurchaseConstraintCode(), k -> new HashMap<>());
            Map<String, BigDecimal> maxQtyPerProductMap = maxEachQty.get(pc.getPurchaseConstraintCode());
            maxQtyPerProductMap.computeIfAbsent(increment.getProductCode(), k -> new BigDecimal(0));
            BigDecimal finalIncrementValue = incrementValue;
            maxQtyPerProductMap.computeIfPresent(increment.getProductCode(), (k, v) -> v.add(finalIncrementValue));
            incrementValue = maxQtyPerProductMap.get(increment.getProductCode());
        }
        return incrementValue;
    }

    public void clearPcRuleCache(PcRuleCalculateModel model) {

        // 限购规则过滤
        List<PurchaseConstraintCacheDTO> allPcRuleCacheList = this.filterRules(model);
        // 获取有效的增量商品集合
        List<PcRuleCalculateModel.IncrementProduct> incrementProducts = this.filterIncrementProducts(model, allPcRuleCacheList);
        // 获取sku命中的限购信息集合
        Map<String, List<PurchaseConstraintCacheDTO>> skuPcMap = this.skuMappingPcMap(incrementProducts, allPcRuleCacheList);

        for (PcRuleCalculateModel.IncrementProduct incrementProduct : incrementProducts) {
            List<PurchaseConstraintCacheDTO> pcRuleCacheList = skuPcMap.get(incrementProduct.getSkuCode());
            for (PurchaseConstraintCacheDTO pc : pcRuleCacheList) {
                for (PurchaseConstraintRule pcRule : pc.getPurchaseConstraintRuleList()) {
                    Date statisticsBegin = PcUtil.getStatisticsBegin(PurchaseConstraintRuleTimeTypeEnum.getByCode(pcRule.getPurchaseConstraintRuleTimeType()), pcRule.getPurchaseConstraintRuleTimeValue());
                    Date statisticsEnd = PcUtil.getStatisticsEnd(PurchaseConstraintRuleTimeTypeEnum.getByCode(pcRule.getPurchaseConstraintRuleTimeType()), pcRule.getPurchaseConstraintRuleTimeValue());
                    // 构建累计规则检查对象
                    PcRuleOverlayCheckDTO pcRuleCheckDTO = getPcRuleOverlayCheckDTO(model, incrementProduct, pc, pcRule, statisticsBegin, statisticsEnd);
                    log.info("clearPcRuleCache#清除缓存: {}", JSON.toJSONString(pcRuleCheckDTO));
                    redisClient.delete(pcRuleCheckDTO.getCacheKeyMd5());
                }
            }
        }
    }



    public Result<PcRuleCheckResult> getPcRuleCheckResultResult(PurchaseConstraintCacheDTO pc, PurchaseConstraintRule pcRule, BigDecimal limit, BigDecimal incrementValue, BigDecimal usedValue, PcRuleCheckResult ruleIncrementResult, Map<Integer, String> ruleTypeMap) {
        if (limit.subtract(incrementValue).subtract(usedValue).compareTo(BigDecimal.ZERO) < 0) {
            String code = null;
            String message = null;
            switch (valueOfCode(pcRule.getPurchaseConstraintRuleType())) {
                // 会员累计购买单个商品最大数量
                case CUSTOMER_MAX_QTY_PER_PRODUCT:
                    if (null != ruleTypeMap && ( !ruleTypeMap.containsKey(PurchaseConstraintRuleTypeEnum.CUSTOMER_MAX_QTY_PER_PRODUCT.getCode())
                            || !ruleTypeMap.get(PurchaseConstraintRuleTypeEnum.CUSTOMER_MAX_QTY_PER_PRODUCT.getCode()).equals(pc.getPurchaseConstraintCode())
                    )){
                        return null;
                    }
                    code = CUSTOMER_MAX_QTY_PER_PRODUCT_VALID_FAIL.getCode();
                    message = MessageFormat.format(CUSTOMER_MAX_QTY_PER_PRODUCT_VALID_FAIL.getMessage(), pcRule.getPurchaseConstraintValue());
                    break;
                // 会员累计购买所有商品最大数量
                case CUSTOMER_MAX_QTY_ALL_PRODUCTS: {
                    if (null != ruleTypeMap && ( !ruleTypeMap.containsKey(PurchaseConstraintRuleTypeEnum.CUSTOMER_MAX_QTY_ALL_PRODUCTS.getCode())
                            || !ruleTypeMap.get(PurchaseConstraintRuleTypeEnum.CUSTOMER_MAX_QTY_ALL_PRODUCTS.getCode()).equals(pc.getPurchaseConstraintCode())
                    )){
                        return null;
                    }
					code = CUSTOMER_MAX_QTY_ALL_PRODUCTS_VALID_FAIL.getCode()
							+ (pcRule.getPurchaseConstraintRuleTimeType() == null ? "" : pcRule.getPurchaseConstraintRuleTimeType());
					message = MessageFormat.format(PurchaseConstraintRuleChecker.getMessageByCode(code),
                            PurchaseConstraintRuleTimeTypeEnum.getErrorMessageVal(pcRule.getPurchaseConstraintRuleTimeType()),
                            pc.getPurchaseConstraintName());
                    break;
                }
                // 会员累计购买最大金额
                case CUSTOMER_MAX_AMOUNT:
                    if (null != ruleTypeMap && ( !ruleTypeMap.containsKey(PurchaseConstraintRuleTypeEnum.CUSTOMER_MAX_AMOUNT.getCode())
                            || !ruleTypeMap.get(PurchaseConstraintRuleTypeEnum.CUSTOMER_MAX_AMOUNT.getCode()).equals(pc.getPurchaseConstraintCode())
                    )){
                        return null;
                    }
					code = CUSTOMER_MAX_AMOUNT_VALID_FAIL.getCode()
							+ (pcRule.getPurchaseConstraintRuleTimeType() == null ? "" : pcRule.getPurchaseConstraintRuleTimeType());
					message = MessageFormat.format(PurchaseConstraintRuleChecker.getMessageByCode(code),
                            PurchaseConstraintRuleTimeTypeEnum.getErrorMessageVal(pcRule.getPurchaseConstraintRuleTimeType()),
                            pc.getPurchaseConstraintName());
                    break;
                default:
                    // 只处理累计统计, 当前订单统计不在这里处理
                    break;
            }
            Result<PcRuleCheckResult> failed = Result.failed(code, message);
            failed.setData(ruleIncrementResult);
            return failed;
        }
        return null;
    }

    private PcRuleOverlayCheckDTO getPcRuleOverlayCheckDTO(PcRuleCalculateModel model,
                                                           PcRuleCalculateModel.IncrementProduct increment,
                                                           PurchaseConstraintCacheDTO pc,
                                                           PurchaseConstraintRule pcRule,
                                                           Date statisticsBegin,
                                                           Date statisticsEnd) {
        // 组装缓存KEY
        String cacheKey = this.getCacheKey(model, increment, pc, pcRule, statisticsBegin, statisticsEnd);
        // 构建累计规则检查对象
        PcRuleOverlayCheckDTO pcRuleCheckDTO = new PcRuleOverlayCheckDTO();
        pcRuleCheckDTO.setTenantCode(model.getTenantCode());
        pcRuleCheckDTO.setOrgCode(pc.getOrgCode());
        pcRuleCheckDTO.setUserCode(model.getUserCode());
        pcRuleCheckDTO.setStatisticsBegin(statisticsBegin);
        pcRuleCheckDTO.setStatisticsEnd(statisticsEnd);
        pcRuleCheckDTO.setCacheKeyMd5(DigestUtils.md5DigestAsHex(JSON.toJSONString(cacheKey).getBytes()));
        pcRuleCheckDTO.setPc(pc);
        pcRuleCheckDTO.setIncrement(increment);

        if (CUSTOMER_MAX_QTY_PER_PRODUCT.equalsCode(pcRule.getPurchaseConstraintRuleType())) {
            pcRuleCheckDTO.setIncludeProductCodes(Lists.newArrayList(increment.getProductCode()));
        } else {
            // 命中的包含商品
            List<String> includeProductCodes = new ArrayList<>();
            // 命中的排除商品
            List<String> excludeProductCodes = new ArrayList<>();
            // 构建命中的商品codes includeProductCodes|excludeProductCodes
            String productSelectionType = pc.getProductSelectionType();
            // 正选时,排除的商品
            List<String> productDetailBlackCodeList = Optional.ofNullable(pc.getProductDetailBlackList())
                    .map(x -> x.stream().map(ProductDetail::getProductCode).collect(Collectors.toList()))
                    .orElse(new ArrayList<>());

            if (CUSTOM_RANGE.equalsCode(pc.getConditionProductType())) {
                List<String> productCodeListForScope = this.queryPimProductCode(pcRuleCheckDTO.getTenantCode(), pcRuleCheckDTO.getOrgCode(), pc.getProducts()
                        .get(0));
                if (INCLUDE.equalsCode(productSelectionType)) {
                    productCodeListForScope.removeIf(productDetailBlackCodeList::contains);
                    includeProductCodes = productCodeListForScope;
                } else if (EXCLUDE.equalsCode(productSelectionType)) {
                    excludeProductCodes.addAll(productCodeListForScope);
                }
            } else if (CUSTOM_PRODUCT.equalsCode(pc.getConditionProductType())) {
                List<ProductDetail> productDetails = pc.getProductDetails();
                if (INCLUDE.equalsCode(productSelectionType)) {
                    productDetails.stream().map(ProductDetail::getProductCode).forEach(includeProductCodes::add);
                    productDetails.removeIf(x -> productDetailBlackCodeList.contains(x.getProductCode()));
                    Optional.ofNullable(pc.getProductDetailBlackList())
                            .ifPresent(fi -> fi.stream().map(ProductDetail::getProductCode)
                                    .forEach(excludeProductCodes::add));
                } else if (EXCLUDE.equalsCode(productSelectionType)) {
                    productDetails.stream().map(ProductDetail::getProductCode).forEach(excludeProductCodes::add);
                }
            }
            pcRuleCheckDTO.setIncludeProductCodes(includeProductCodes);
            pcRuleCheckDTO.setExcludeProductCodes(excludeProductCodes);
        }
        log.info("ruleIncrement#构建累计规则检查对象: {}", pcRuleCheckDTO);
        return pcRuleCheckDTO;
    }

    private BigDecimal getUsedValue(PcRuleCalculateModel.IncrementProduct increment,
                                    PurchaseConstraintRule pcRule,
                                    Supplier<Map<String, PurchaseConstraintCustomer>> customerMap,
                                    PcRuleOverlayCheckDTO pcRuleCheckDTO,
                                    PcRuleCheckResult ruleIncrementResult) {
        log.info("getUsedValue#pcRuleCheckDTO.cacheKeyMd5: {}", pcRuleCheckDTO.getCacheKeyMd5());
        BigDecimal usedValue = Optional.ofNullable(redisClient.getString(pcRuleCheckDTO.getCacheKeyMd5()))
                // redis 缓存不为空
                .filter(StringUtils::isNotBlank)
                // redis value 转化 BigDecimal
                .map(BigDecimal::new)
                .orElse(null);
        log.info("限购检查缓存值#usedValue: {}", usedValue);
        // 已用额度:DB
        if (usedValue == null || usedValue.compareTo(BigDecimal.ZERO) == 0) {
            usedValue = Optional.ofNullable(customerMap.get().get(pcRuleCheckDTO.getCacheKeyMd5()))
                    .map(PurchaseConstraintCustomer::getPurchaseConstraintValueUsed)
                    .orElse(null);
            log.info("限购检查DB值#usedValue: {}", usedValue);
        }
        // 已用额度:OMS
        if (usedValue == null || usedValue.compareTo(BigDecimal.ZERO) == 0) {
            PurchaseConstraintCustomer customer = Objects.requireNonNull(this.getUsedValueByOms(increment, pcRule, pcRuleCheckDTO));
            ruleIncrementResult.setCustomer(customer);
            usedValue = customer.getPurchaseConstraintValueUsed();
            log.info("限购检查OMS值#usedValue: {}", usedValue);
        }

        //如果oms也没有订单
        if (usedValue == null) {
            usedValue = BigDecimal.ZERO;
            log.info("限购检查空值#usedValue: {}", usedValue);
        }
        log.info("限购检查最终值usedValue: {}", usedValue);
        redisClient.setStringValue(pcRuleCheckDTO.getCacheKeyMd5(), String.valueOf(usedValue), expired, TimeUnit.MINUTES);
        return usedValue;
    }

    private PurchaseConstraintCustomer getUsedValueByOms(PcRuleCalculateModel.IncrementProduct increment,
                                                         PurchaseConstraintRule pcRule,
                                                         PcRuleOverlayCheckDTO pcRuleCheckDTO) {
        PurchaseConstraintCustomer customer = new PurchaseConstraintCustomer();
        customer.setTenantCode(pcRuleCheckDTO.getTenantCode());
        customer.setCustomerCode(pcRuleCheckDTO.getCacheKeyMd5());
        customer.setPurchaseConstraintCode(pcRuleCheckDTO.getPc().getPurchaseConstraintCode());
        customer.setPurchaseConstraintRuleType(pcRule.getPurchaseConstraintRuleType());
        customer.setUserCode(pcRuleCheckDTO.getUserCode());
        customer.setPurchaseConstraintValue(new BigDecimal(pcRule.getPurchaseConstraintValue()));
        customer.setCustomerStartTime(pcRuleCheckDTO.getStatisticsBegin());
        customer.setCustomerEndTime(pcRuleCheckDTO.getStatisticsEnd());
        customer.setCreateTime(new Date());
        customer.setUpdateTime(new Date());
        switch (valueOfCode(pcRule.getPurchaseConstraintRuleType())) {
            // 会员累计购买单个商品最大数量
            case CUSTOMER_MAX_QTY_PER_PRODUCT:
                customer.setProductCode(increment.getProductCode());
                customer.setPurchaseConstraintValueUsed(!checkOms ? BigDecimal.ZERO : this.getMemberPurchaseStatisticsResps(increment, pcRuleCheckDTO)
                        .stream()
                        .findFirst()
                        .map(x -> new BigDecimal(String.valueOf(x.getEfficientQty())))
                        .orElse(BigDecimal.ZERO));
                break;
            // 会员累计购买所有商品最大数量
            case CUSTOMER_MAX_QTY_ALL_PRODUCTS:
                customer.setPurchaseConstraintValueUsed(!checkOms ? BigDecimal.ZERO : this.getMemberPurchaseStatisticsResps(pcRuleCheckDTO)
                        .stream()
                        .map(m -> new BigDecimal(String.valueOf(m.getEfficientQty())))
                        .reduce(BigDecimal.ZERO, BigDecimal::add));
                break;
            // 会员累计购买最大金额
            case CUSTOMER_MAX_AMOUNT:
                customer.setPurchaseConstraintValueUsed(!checkOms ? BigDecimal.ZERO : this.getMemberPurchaseStatisticsResps(pcRuleCheckDTO)
                        .stream()
                        .map(MemberPurchaseStatisticsResp::getEfficientAmount)
                        .reduce(BigDecimal.ZERO, BigDecimal::add));
                break;
            default:
                return null;
        }
        return customer;
    }

    private BigDecimal getIncrementValue(PcRuleCalculateModel.IncrementProduct increment, int pcRuleType) {
        switch (valueOfCode(pcRuleType)) {
            case CUSTOMER_MAX_QTY_PER_PRODUCT:
            case CUSTOMER_MAX_QTY_ALL_PRODUCTS:
                return new BigDecimal(String.valueOf(increment.getSkuCount()));
            case CUSTOMER_MAX_AMOUNT:
                return new BigDecimal(String.valueOf(increment.getSellAmount()));
            default:
                return BigDecimal.ZERO;
        }
    }


    private PcRuleCheckResult getPcRuleCheckResult(PcRuleCalculateModel.IncrementProduct increment, PurchaseConstraintCacheDTO pc, PurchaseConstraintRule pcRule, PcRuleOverlayCheckDTO pcRuleCheckDTO, BigDecimal incrementValue) {
        PcRuleCheckResult ruleIncrementResult = new PcRuleCheckResult();
        ruleIncrementResult.setSkuCode(increment.getSkuCode());
        ruleIncrementResult.setProductCode(increment.getProductCode());
        ruleIncrementResult.setPcCode(pc.getPurchaseConstraintCode());
        ruleIncrementResult.setPcRule(pcRule);
        ruleIncrementResult.setPcRuleCheckDTO(pcRuleCheckDTO);
        ruleIncrementResult.setValue(incrementValue);
        log.info("ruleIncrement#限购检查增量结果基本信息: {}", ruleIncrementResult);
        return ruleIncrementResult;
    }

    private String getCacheKey(PcRuleCalculateModel model, PcRuleCalculateModel.IncrementProduct increment, PurchaseConstraintCacheDTO pc, PurchaseConstraintRule pcRule, Date statisticsBegin, Date statisticsEnd) {
        String pcRuleKey = PcRuleCacheValuePair.builder()
                .tenantCode(model.getTenantCode())
                .pcCode(pc.getPurchaseConstraintCode())
                .userCode(model.getUserCode())
                .productCode(increment.getProductCode())
                .statisticsBegin(statisticsBegin)
                .statisticsEnd(statisticsEnd)
                .build()
                .getPcRuleKey(pcRule.getPurchaseConstraintRuleType());
        log.info("ruleIncrement#组装缓存KEY: {}", pcRuleKey);
        return pcRuleKey;
    }

    /**
     * 1.过滤累计规则;
     * 2.验证订单日期是否当前统计周期范围;
     * 3.（人员、店铺、商品）过滤后的限购集合;
     */
    private List<PurchaseConstraintCacheDTO> filterRules(PcRuleCalculateModel model) {
        List<PurchaseConstraintCacheDTO> pcCacheDTOs = model.getPcCacheDTOs();
        if (CollectionUtils.isEmpty(pcCacheDTOs)) {
            return Collections.emptyList();
        }
        // 过滤规则不为空的限购
        List<PurchaseConstraintCacheDTO> pcs = pcCacheDTOs.stream()
                .filter(f -> CollectionUtils.isNotEmpty(f.getPurchaseConstraintRuleList()))
                .collect(Collectors.toList());
        // 移除（人员、店铺、商品）过滤后的限购集合
        pcs.removeIf(pc -> !pcComponent.checkCommonPurchaseConstraint(pc, model.getQualifications(), Lists.newArrayList(model.getOrgCode()), Boolean.FALSE));
        for (PurchaseConstraintCacheDTO pc : pcs) {
            // 移除非累计规则
            pc.getPurchaseConstraintRuleList().removeIf(pcRule -> PurchaseConstraintRuleTypeEnum.addUpRuleTypeList().stream().noneMatch(f -> f.getCode().equals(pcRule.getPurchaseConstraintRuleType())));
            // 移除订单日期不在统计范围内的
            Optional.ofNullable(model.getOrderDate()).ifPresent(orderDate -> this.ruleRemoveByOrderDate(orderDate, pc.getPurchaseConstraintRuleList()));
        }
        // 移除没有限购规则的限购
        pcs.removeIf(pc -> CollectionUtils.isEmpty(pc.getPurchaseConstraintRuleList()));
        return pcs;

    }

    private void ruleRemoveByOrderDate(Date orderDate, List<PurchaseConstraintRule> pcRules) {
        pcRules.removeIf(pcRule -> {
            Date statisticsBegin = PcUtil.getStatisticsBegin(pcRule.getPurchaseConstraintRuleTimeType(), pcRule.getPurchaseConstraintRuleTimeValue());
            Date statisticsEnd = PcUtil.getStatisticsEnd(pcRule.getPurchaseConstraintRuleTimeType(), pcRule.getPurchaseConstraintRuleTimeValue());
            return (statisticsBegin != null && statisticsBegin.compareTo(orderDate) > 0) || (statisticsEnd != null && statisticsEnd.compareTo(orderDate) < 0);
        });
    }


    private boolean checkIdempotent(PcRuleCalculateModel model) {
        PcRuleCalculateTypeEnum type = PcRuleCalculateTypeEnum.getByCode(model.getType());
        switch (Objects.requireNonNull(type)) {
            case INCREMENT:
            case DECREMENT:
                // 增量是否已经操作过的标记;方式重复增量
                String keyPre = String.format("PROMOTION:PC:" + "INCREMENT_PC_RULE:%s:%s:%s:%s:%s",
                        model.getTenantCode(),
                        model.getUserCode(),
                        model.getOrderCode(),
                        model.getReturnCode(),
                        model.getOrderStatus());
                boolean absent = Boolean.TRUE.equals(redisClient.setIfAbsent(keyPre, keyPre, 5L, TimeUnit.MINUTES));
                if (Boolean.TRUE.equals(model.getForward())) {
                    if (absent) {
                        log.info("限购增量幂等检查#增量,递减流程: 允许访问;");
                    } else {
                        log.warn("限购增量幂等检查#增量,递减流程: 拒绝访问;");
                    }
                    return absent;
                } else {
                    if (absent) {
                        log.warn("限购增量幂等检查#回滚流程: 拒绝访问;");
                    } else {
                        log.info("限购增量幂等检查#回滚流程: 允许访问;");
                    }
                    // 删除幂等的key, 否则事务回滚再次请求 会多次回滚
                    redisClient.delete(keyPre);
                    return !absent;
                }
            case CHECK:
                log.info("限购增量幂等检查#限购检查,不进行幂等检查 useIncrement: false");
                break;
        }
        return true;

    }

    private List<MemberPurchaseStatisticsResp> getMemberPurchaseStatisticsResps(PcRuleOverlayCheckDTO checkDTO) {
        List<ActivityStore> channelStores = checkDTO.getPc().getChannelStores();
        if (CollectionUtils.isEmpty(channelStores)) {
            ActivityStore activityStore = new ActivityStore();
            activityStore.setOrgCode(PRODUCT_RELATION_ALL);
            channelStores = Lists.newArrayList(activityStore);
        }
        List<MemberPurchaseStatisticsResp> statisticsRespList = new ArrayList<>();

        List<String> includeProductCodes = checkDTO.getIncludeProductCodes();
        List<String> excludeProductCodes = checkDTO.getExcludeProductCodes();

        QueryMemberPurchaseRequest memberPurchaseRequest = new QueryMemberPurchaseRequest();
        memberPurchaseRequest.setTenantCode(checkDTO.getTenantCode());
        memberPurchaseRequest.setMemberCode(checkDTO.getUserCode());
        memberPurchaseRequest.setCreateTimeStart(checkDTO.getStatisticsBegin());
        memberPurchaseRequest.setCreateTimeEnd(checkDTO.getStatisticsEnd());
        if (CollectionUtils.isNotEmpty(includeProductCodes)) {
            List<List<String>> partition = Lists.partition(includeProductCodes, 100);
            for (List<String> strings : partition) {
                memberPurchaseRequest.setProductCodes(strings);
                memberPurchaseRequest.setExcludeProductCodes(checkDTO.getExcludeProductCodes());
                for (ActivityStore channelStore : channelStores) {
                    if (!channelStore.getOrgCode().equals(PRODUCT_RELATION_ALL)) {
                        memberPurchaseRequest.setOrgCode(channelStore.getOrgCode());
                    }
                    List<MemberPurchaseStatisticsResp> statisticsList = Optional.ofNullable(orderClientConsumer.queryMemberPurchaseStatistics(memberPurchaseRequest)).orElse(new ArrayList<>());
                    Optional.of(statisticsList)
                            .filter(CollectionUtils::isNotEmpty)
                            .ifPresent(statisticsRespList::addAll);
                }
            }
        } else if (CollectionUtils.isNotEmpty(excludeProductCodes)) {
            List<List<String>> partition = Lists.partition(excludeProductCodes, 100);
            for (List<String> strings : partition) {
                memberPurchaseRequest.setExcludeProductCodes(strings);
                for (ActivityStore channelStore : channelStores) {
                    if (!channelStore.getOrgCode().equals(PRODUCT_RELATION_ALL)) {
                        memberPurchaseRequest.setOrgCode(channelStore.getOrgCode());
                    }
                    List<MemberPurchaseStatisticsResp> statisticsList = Optional.ofNullable(orderClientConsumer.queryMemberPurchaseStatistics(memberPurchaseRequest)).orElse(new ArrayList<>());
                    Optional.of(statisticsList)
                            .filter(CollectionUtils::isNotEmpty)
                            .ifPresent(statisticsRespList::addAll);
                }
            }
            // 移除排除的商品
            statisticsRespList.removeIf(x -> excludeProductCodes.contains(x.getProductCode()));
        } else {
            for (ActivityStore channelStore : channelStores) {
                if (!channelStore.getOrgCode().equals(PRODUCT_RELATION_ALL)) {
                    memberPurchaseRequest.setOrgCode(channelStore.getOrgCode());
                }
                List<MemberPurchaseStatisticsResp> statisticsList = Optional.ofNullable(orderClientConsumer.queryMemberPurchaseStatistics(memberPurchaseRequest)).orElse(new ArrayList<>());
                Optional.of(statisticsList)
                        .filter(CollectionUtils::isNotEmpty)
                        .ifPresent(statisticsRespList::addAll);
            }

        }
        return statisticsRespList;

    }

    private List<CustomOrderSkuStatisticsQueryOut> queryCustomOrderSkuStatistics(PcRuleOverlayCheckDTO checkDTO) {
        List<ActivityStore> channelStores = checkDTO.getPc().getChannelStores();
        if (CollectionUtils.isEmpty(channelStores)) {
            ActivityStore activityStore = new ActivityStore();
            activityStore.setOrgCode(PRODUCT_RELATION_ALL);
            channelStores = Lists.newArrayList(activityStore);
        }
        List<CustomOrderSkuStatisticsQueryOut> statisticsRespList = new ArrayList<>();

        List<String> includeProductCodes = checkDTO.getIncludeProductCodes();
        List<String> excludeProductCodes = checkDTO.getExcludeProductCodes();

        CustomOrderSkuStatisticsQuery memberPurchaseRequest = new CustomOrderSkuStatisticsQuery();
        memberPurchaseRequest.setTenantCode(checkDTO.getTenantCode());
        memberPurchaseRequest.setMemberCode(checkDTO.getUserCode());
        memberPurchaseRequest.setCreateTimeStart(DateUtil.format(checkDTO.getStatisticsBegin(), "yyyy-MM-dd HH:mm:ss"));
        memberPurchaseRequest.setCreateTimeEnd((DateUtil.format(checkDTO.getStatisticsEnd(), "yyyy-MM-dd HH:mm:ss")));
        if (CollectionUtils.isNotEmpty(includeProductCodes)) {
            List<List<String>> partition = Lists.partition(includeProductCodes, 100);
            for (List<String> strings : partition) {
                memberPurchaseRequest.setProductCodes(strings);
                memberPurchaseRequest.setExcludeProductCodes(checkDTO.getExcludeProductCodes());
                for (ActivityStore channelStore : channelStores) {
                    if (!channelStore.getOrgCode().equals(PRODUCT_RELATION_ALL)) {
                        memberPurchaseRequest.setOrgCode(channelStore.getOrgCode());
                    }
                    List<CustomOrderSkuStatisticsQueryOut> statisticsList = Optional.ofNullable(orderClientConsumer.queryCustomOrderSkuStatistics(memberPurchaseRequest)).orElse(new ArrayList<>());
                    Optional.of(statisticsList)
                            .filter(CollectionUtils::isNotEmpty)
                            .ifPresent(statisticsRespList::addAll);
                }
            }
        } else if (CollectionUtils.isNotEmpty(excludeProductCodes)) {
            List<List<String>> partition = Lists.partition(excludeProductCodes, 100);
            for (List<String> strings : partition) {
                memberPurchaseRequest.setExcludeProductCodes(strings);
                for (ActivityStore channelStore : channelStores) {
                    if (!channelStore.getOrgCode().equals(PRODUCT_RELATION_ALL)) {
                        memberPurchaseRequest.setOrgCode(channelStore.getOrgCode());
                    }
                    List<CustomOrderSkuStatisticsQueryOut> statisticsList = Optional.ofNullable(orderClientConsumer.queryCustomOrderSkuStatistics(memberPurchaseRequest)).orElse(new ArrayList<>());
                    Optional.of(statisticsList)
                            .filter(CollectionUtils::isNotEmpty)
                            .ifPresent(statisticsRespList::addAll);
                }
            }
            // 移除排除的商品
            statisticsRespList.removeIf(x -> {
                List<CustomOrderSkuStatisticsQueryOut.OrderSkuStatisticsQueryItem> items = x.getStatisticsItems();
                items.removeIf(xx -> excludeProductCodes.contains(xx.getProductCode()));
                return !Optional.of(items)
                        .filter(CollectionUtils::isNotEmpty).isPresent();
            });
        } else {
            for (ActivityStore channelStore : channelStores) {
                if (!channelStore.getOrgCode().equals(PRODUCT_RELATION_ALL)) {
                    memberPurchaseRequest.setOrgCode(channelStore.getOrgCode());
                }
                List<CustomOrderSkuStatisticsQueryOut> statisticsList = Optional.ofNullable(orderClientConsumer.queryCustomOrderSkuStatistics(memberPurchaseRequest)).orElse(new ArrayList<>());
                Optional.of(statisticsList)
                        .filter(CollectionUtils::isNotEmpty)
                        .ifPresent(statisticsRespList::addAll);
            }

        }
        String[] statuses = {"61", "62"};
        statisticsRespList.removeIf(x -> Arrays.asList(statuses).contains(x.getOrderStatus()));
        return statisticsRespList;

    }

}
