package com.gtech.promotion.component.purchaseconstraint.dto;

import com.gtech.promotion.checker.SystemChecker;
import com.gtech.promotion.code.purchaseconstraint.PurchaseConstraintRuleTypeEnum;
import com.gtech.promotion.exception.PromotionException;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

import static com.gtech.promotion.code.purchaseconstraint.PurchaseConstraintRuleTypeEnum.*;

@Slf4j
@Data
@Builder
public class PcRuleCacheValuePair {
    /**
     * 限购KEY前缀
     */
    public static final String PROMOTION_PC_PRE = "PROMOTION:PC:";

    /**
     * 累计规则的Redis Key
     */
    protected static final Map<Integer, String> KEY_MAP = new HashMap<>();

    static {
        /*
         * 用户每个商品最大购买量.<br/>
         * TENANT_CODE:PC_CODE:{@link PurchaseConstraintRuleTypeEnum}:USER_CODE:PRODUCT_CODE:StartTime:EndTime
         * 示例: "998"
         */
        KEY_MAP.put(CUSTOMER_MAX_QTY_PER_PRODUCT.getCode(), "PROMOTION:PC:%s:%s:%s:%s:%s:%s:%s");
        /*
         * 会员累计购买所有商品最大数量.<br/>
         * TENANT_CODE:PC_CODE:{@link PurchaseConstraintRuleTypeEnum}:USER_CODE:StartTime:EndTime
         * 示例: "998"
         */
        KEY_MAP.put(CUSTOMER_MAX_QTY_ALL_PRODUCTS.getCode(), "PROMOTION:PC:%s:%s:%s:%s:%s:%s");
        /*
         * 会员累计购买最大金额
         * TENANT_CODE:PC_CODE:{@link PurchaseConstraintRuleTypeEnum}:USER_CODE:StartTime:EndTime
         * 示例: "998.89"
         */
        KEY_MAP.put(CUSTOMER_MAX_AMOUNT.getCode(), "PROMOTION:PC:%s:%s:%s:%s:%s:%s");
    }

    /**
     * 租户
     */
    private String tenantCode;
    /**
     * 限购编码
     */
    private String pcCode;
    /**
     * 用户编码
     */
    private String userCode;
    /**
     * 商品编码
     */
    private String productCode;

    private Date statisticsBegin;
    private Date statisticsEnd;

    public String getPcRuleKey(PurchaseConstraintRuleTypeEnum ruleTypeEnum) {
        if (null == ruleTypeEnum) {
            throw new PromotionException(SystemChecker.NULL_VO_OR_ILLEGAL_FORMAT_PARAM);
        }
        if (StringUtils.isAnyBlank(tenantCode, pcCode, userCode)) {
            throw new PromotionException(SystemChecker.NULL_VO_OR_ILLEGAL_FORMAT_PARAM);
        }
        String ruleKey = KEY_MAP.get(ruleTypeEnum.getCode());
        switch (ruleTypeEnum) {
            // 会员累计购买单个商品最大数量
            case CUSTOMER_MAX_QTY_PER_PRODUCT:
                if (StringUtils.isBlank(productCode)) {
                    log.info("getPcRuleKey: productCode 不能为空");
                    throw new PromotionException(SystemChecker.NULL_VO_OR_ILLEGAL_FORMAT_PARAM);
                }
                return String.format(ruleKey, tenantCode, pcCode, ruleTypeEnum.getCode(), userCode, productCode, statisticsBegin, statisticsEnd);
            // 会员累计购买所有商品最大数量
            case CUSTOMER_MAX_QTY_ALL_PRODUCTS:
                //会员累计购买最大金额
            case CUSTOMER_MAX_AMOUNT:

                return String.format(ruleKey, tenantCode, pcCode, ruleTypeEnum.getCode(), userCode, statisticsBegin, statisticsEnd);
            default:
                // 只处理累计统计, 当前订单统计不在这里处理
                return null;
        }
    }

    public String getPcRuleKey(Integer purchaseConstraintRuleType) {
        return getPcRuleKey(PurchaseConstraintRuleTypeEnum.valueOfCode(purchaseConstraintRuleType));
    }
}
