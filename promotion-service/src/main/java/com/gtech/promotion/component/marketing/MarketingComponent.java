package com.gtech.promotion.component.marketing;

import com.alibaba.fastjson.JSONObject;
import com.gtech.basic.idm.web.vo.result.QueryOpUserAccountListResult;
import com.gtech.basic.idm.web.vo.result.QueryUserResult;
import com.gtech.commons.page.PageData;
import com.gtech.commons.redis.GTechCodeGenerator;
import com.gtech.commons.result.PageResult;
import com.gtech.commons.utils.BeanCopyUtils;
import com.gtech.commons.utils.DateUtil;
import com.gtech.commons.utils.StringUtil;
import com.gtech.promotion.checker.activity.TPromoActivityChecker;
import com.gtech.promotion.checker.activity.TPromoIncentiveLimitedChecker;
import com.gtech.promotion.checker.marketing.MarketingChecker;
import com.gtech.promotion.code.activity.ActivityStatusEnum;
import com.gtech.promotion.code.activity.IncentiveLimitedFlagEnum;
import com.gtech.promotion.code.activity.LuckyDrawRuleFlagEnum;
import com.gtech.promotion.code.activity.OperationTypeEnum;
import com.gtech.promotion.code.marketing.*;
import com.gtech.promotion.component.activity.ActivityComponentDomain;
import com.gtech.promotion.component.activity.PromoGroupDomain;
import com.gtech.promotion.component.feign.IdmFeignClientComponent;
import com.gtech.promotion.component.flashsale.DataSyncComponent;
import com.gtech.promotion.dao.model.activity.*;
import com.gtech.promotion.dao.model.marketing.*;
import com.gtech.promotion.dao.model.marketing.flashsale.FlashSaleStoreModel;
import com.gtech.promotion.domain.marketing.LuckyDrawDomain;
import com.gtech.promotion.domain.marketing.MarketingDomain;
import com.gtech.promotion.dto.in.marketing.MarketingQueryInDto;
import com.gtech.promotion.exception.Check;
import com.gtech.promotion.helper.ActivityRedisHelpler;
import com.gtech.promotion.service.activity.*;
import com.gtech.promotion.service.flashsale.FlashSaleStoreService;
import com.gtech.promotion.service.marketing.*;
import com.gtech.promotion.utils.Constants;
import com.gtech.promotion.utils.MarketingConstants;
import com.gtech.promotion.vo.bean.*;
import com.gtech.promotion.vo.bean.marketing.MarketingLanguage;
import com.gtech.promotion.vo.bean.marketing.MarketingPrize;
import com.gtech.promotion.vo.bean.marketing.MarketingPrizeLanguage;
import com.gtech.promotion.vo.result.marketing.LuckyDrawFindResult;
import com.gtech.promotion.vo.result.marketing.MarketingPrizeResult;
import com.gtech.promotion.vo.result.marketing.MarketingQueryResult;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.security.SecureRandom;
import java.util.*;
import java.util.stream.Collectors;

@Component
public class MarketingComponent {

	private Random random = new SecureRandom();

	@Autowired
	private MarketingService marketingService;

	@Autowired
	private MarketingLanguageService languageService;

	@Autowired
	private PrizeService prizeService;

	@Autowired
	private PrizeLanguageService prizeLanguageService;

	@Autowired
	private GTechCodeGenerator gTechCodeGenerator;

	@Autowired
	private MarketingCacheComponent marketingCacheComponent;

	@Autowired
	private ActivityService activityService;

	@Autowired
	private QualificationService qualificationService;

	@Autowired
	private TPromoIncentiveLimitedService incentiveLimitedService;

	@Autowired
	private LuckyDrawRuleService luckyDrawRuleService;

	@Autowired
	private OperationLogService operationLogService;
	@Autowired
	private ActivityPeriodService activityPeriodService;
	@Autowired
	private IdmFeignClientComponent idmFeignClientComponent;
	@Autowired
	private DataSyncComponent dataSyncComponent;

	@Autowired
	private ActivityComponentDomain activityComponentDomain;

	@Autowired
	private FlashSaleStoreService flashSaleStoreService;

	@Autowired
	private PromoGroupDomain promoGroupDomain;


	@Autowired
	private MarketingGroupUserService marketingGroupUserService;

	@Autowired
	private MarketingGroupCodeService marketingGroupCodeService;

	@Autowired
	private MarketingGroupService marketingGroupService;

	@Autowired
	private MarketingGroupComponent marketingGroupComponent;

	@Autowired
	private ActivityRedisHelpler activityRedisHelpler;

	@Transactional
	public String createMarketing(LuckyDrawDomain luckyDrawDomain) {
		MarketingModel marketingModel = BeanCopyUtils.jsonCopyBean(luckyDrawDomain, MarketingModel.class);

		String activityCode = gTechCodeGenerator.generateCode(MarketingConstants.APP_KEY,
				luckyDrawDomain.getActivityType(), MarketingConstants.MKT_CODE_TEMPLATE_EXP, 105L);
		marketingModel.setActivityCode(activityCode);
		marketingModel.setCreateUser(luckyDrawDomain.getOperateUser());
		marketingModel.setActivityStatus(ActivityStatusEnum.PENDING.code());

		String opsType = marketingModel.getOpsType();


		//是否开启分组

		//这里检查是否有分组记录,如果没有分组则新建分组,并且默认与所有促销和营销互斥
		if (StringUtil.isNotBlank(opsType)){
			String groupCode = promoGroupDomain.queryGroupCode(luckyDrawDomain.getDomainCode(), luckyDrawDomain.getTenantCode(), opsType);
			marketingModel.setGroupCode(groupCode);
		}


		/*boolean groupFlag = promoGroupDomain.checkGroupEnabledByTenantCode(luckyDrawDomain.getTenantCode(), opsType);

		if (groupFlag){
			//opsType 作为分组编码 直接关联活动
			marketingModel.setGroupCode(opsType);
		}*/

		int insert = marketingService.insert(marketingModel);

		if (insert == 1) {
			createMarketingPeriod(luckyDrawDomain, activityCode);
			createMarketingLanguage(luckyDrawDomain, activityCode);
			createMarketingPrize(luckyDrawDomain, activityCode);


		}
		// 用户资格
		createActivityQualification(luckyDrawDomain, luckyDrawDomain.getTenantCode(), activityCode);

		// 活动规则
		createLuckyDrawRule(luckyDrawDomain, luckyDrawDomain.getTenantCode(), activityCode);

		// 活动限制条件
		createIncentiveLimit(luckyDrawDomain, luckyDrawDomain.getTenantCode(), activityCode);

		operationLogService.insertLog(
				OperationLogModel.builder().tenantCode(luckyDrawDomain.getTenantCode()).activityCode(activityCode)
						.operationType(OperationTypeEnum.CREATION.code())
						.createLastName(luckyDrawDomain.getOperateLastName())
						.createFirstName(luckyDrawDomain.getOperateFirstName())
						.createUser(luckyDrawDomain.getOperateUser()).build(),
				JSONObject.toJSONString(luckyDrawDomain));
		return activityCode;
	}

	private void createLuckyDrawRule(LuckyDrawDomain luckyDrawDomain, String tenantCode, String activityCode) {
		if (LuckyDrawRuleFlagEnum.YES.equalsCode(luckyDrawDomain.getLuckyDrawRuleFlag())) {
			List<LuckyDrawRule> luckyDrawRules = luckyDrawDomain.getLuckyDrawRules();
			List<LuckyDrawRuleModel> list = BeanCopyUtils.jsonCopyList(luckyDrawRules, LuckyDrawRuleModel.class);
			luckyDrawRuleService.createLuckyDrawRule(activityCode, list, tenantCode);
		}
	}

	private void createIncentiveLimit(LuckyDrawDomain luckyDrawDomain, String tenantCode, String activityCode) {

		if (IncentiveLimitedFlagEnum.YES.equalsCode(luckyDrawDomain.getIncentiveLimitedFlag())) {
			List<IncentiveLimited> limiteds = luckyDrawDomain.getIncentiveLimiteds();
			Check.check(CollectionUtils.isEmpty(limiteds), TPromoIncentiveLimitedChecker.NOT_NULL_LIMITATION_LIST);
			List<TPromoIncentiveLimitedVO> list = BeanCopyUtils.jsonCopyList(limiteds, TPromoIncentiveLimitedVO.class);
			incentiveLimitedService.insertLuckyDrawLimitedList(activityCode, list, tenantCode);
		}
	}

	private void createActivityQualification(LuckyDrawDomain luckyDrawDomain, String tenantCode, String activityCode) {

		List<Qualification> qualifications = luckyDrawDomain.getQualifications();
		// 资格创建
		if (CollectionUtils.isNotEmpty(qualifications)) {
			List<QualificationModel> qualificationModels = new ArrayList<>();
			for (Qualification qualification : qualifications) {
				for (int i = 0; i < qualification.getQualificationValue().size(); i++) {
					String s = qualification.getQualificationValue().get(i);
					QualificationModel qualificationModel = new QualificationModel();
					qualificationModel.setDomainCode(luckyDrawDomain.getDomainCode());
					qualificationModel.setTenantCode(tenantCode);
					qualificationModel.setActivityCode(activityCode);
					qualificationModel.setQualificationCode(qualification.getQualificationCode());
					qualificationModel.setIsExclude(qualification.getIsExclude());
					qualificationModel.setQualificationValue(s);
					if (CollectionUtils.isNotEmpty(qualification.getQualificationValueName())
							&& qualification.getQualificationValueName().size() > i) {
						qualificationModel.setQualificationValueName(qualification.getQualificationValueName().get(i));
					}
					qualificationModels.add(qualificationModel);
				}
			}
			qualificationService.createLuckyDrawQualifications(qualificationModels);
		}
	}

	private void createMarketingPeriod(LuckyDrawDomain luckyDrawDomain, String activityCode) {
		if (null != luckyDrawDomain.getActivityPeriod()) {
			ActivityPeriodModel activityPeriodModel = BeanCopyUtils.jsonCopyBean(luckyDrawDomain.getActivityPeriod(),
					ActivityPeriodModel.class);
			activityPeriodModel.setDomainCode(luckyDrawDomain.getDomainCode());
			activityPeriodModel.setTenantCode(luckyDrawDomain.getTenantCode());
			activityPeriodModel.setActivityCode(activityCode);
			activityPeriodModel.setCreateUser(luckyDrawDomain.getOperateUser());
			activityPeriodService.createPeriod(activityPeriodModel);
		}
	}

	@Transactional
	public int updateMarketing(LuckyDrawDomain luckyDrawDomain) {
		String activityCode = luckyDrawDomain.getActivityCode();
		languageService.deleteByActivityCode(activityCode);
		MarketingModel model = marketingService.findByActivityCode(activityCode);
		List<PrizeModel> prizeModels = prizeService.findListByActivityCode(activityCode);
		boolean notNeedUpdate = ActivityStatusEnum.EFFECTIVE.equalsCode(model.getActivityStatus())
				&& !model.isNeedToDoExpire();
		if (notNeedUpdate) {
			luckyDrawDomain.setActivityStatus(ActivityStatusEnum.EFFECTIVE.code());
			// 如果有一个奖品的配额小于原先的配额则不更新
			Check.check(!validatePrizeQuota(luckyDrawDomain, prizeModels), MarketingChecker.PRIZE_QUOTA);
			luckyDrawDomain.ofInfoNull();
			luckyDrawDomain.setOrgCode(model.getOrgCode());
		} else {
			luckyDrawDomain.setActivityStatus(ActivityStatusEnum.PENDING.code());
		}
		activityPeriodService.deletePeriod(luckyDrawDomain.getTenantCode(), activityCode);
		prizeService.deleteByActivityCode(activityCode);
		prizeLanguageService.deleteByActivityCode(activityCode);
		MarketingModel marketingModel = BeanCopyUtils.jsonCopyBean(luckyDrawDomain, MarketingModel.class);
		marketingModel.setUpdateUser(luckyDrawDomain.getOperateUser());
		int update = marketingService.updateByActivityCode(marketingModel);

		if (update == 1) {
			createMarketingPeriod(luckyDrawDomain, activityCode);
			createMarketingLanguage(luckyDrawDomain, activityCode);
			createMarketingPrize(luckyDrawDomain, activityCode);
		}

		// 用户资格 先删除再创建
		qualificationService.deleteLuckyDrawQualifications(luckyDrawDomain.getTenantCode(), activityCode);
		createActivityQualification(luckyDrawDomain, luckyDrawDomain.getTenantCode(), activityCode);

		// 活动规则 先删除再创建
		luckyDrawRuleService.deleteLuckyDrawRuleByActivityCode(activityCode);
		createLuckyDrawRule(luckyDrawDomain, luckyDrawDomain.getTenantCode(), activityCode);

		// 活动限制条件 先删除再创建
		incentiveLimitedService.deleteLuckyDrawLimitedByActivityCode(activityCode);
		createIncentiveLimit(luckyDrawDomain, luckyDrawDomain.getTenantCode(), activityCode);

		operationLogService.insertLog(
				OperationLogModel.builder().tenantCode(luckyDrawDomain.getTenantCode()).activityCode(activityCode)
						.operationType(OperationTypeEnum.EDITION.code())
						.createLastName(luckyDrawDomain.getOperateLastName())
						.createFirstName(luckyDrawDomain.getOperateFirstName())
						.createUser(luckyDrawDomain.getOperateUser()).build(),
				JSONObject.toJSONString(luckyDrawDomain));
		if (notNeedUpdate) {
			marketingCacheComponent.updateCacheByStatus(model, ActivityStatusEnum.EFFECTIVE.code());
		}
		return update;
	}

	private boolean validatePrizeQuota(LuckyDrawDomain luckyDrawDomain, List<PrizeModel> prizeModels) {
		boolean flag = true;
		if (prizeModels.size() == luckyDrawDomain.getMarketingPrizes().size()) {
			for (PrizeModel prizeModel : prizeModels) {
				flag = getBoolean(luckyDrawDomain, flag, prizeModel);
			}
		}
		return flag;
	}

	private boolean getBoolean(LuckyDrawDomain luckyDrawDomain, boolean flag, PrizeModel prizeModel) {
		for (MarketingPrize marketingPrize : luckyDrawDomain.getMarketingPrizes()) {
			if (prizeModel.getPrizeName().equals(marketingPrize.getPrizeName())) {
				if (marketingPrize.getPrizeQuota() >= prizeModel.getPrizeQuota()) {
					break;
				} else {
					flag = false;
				}
			}
		}
		return flag;
	}

	private void createMarketingLanguage(LuckyDrawDomain luckyDrawDomain, String activityCode) {
		for (MarketingLanguage marketingLanguage : luckyDrawDomain.getMarketingLanguages()) {
			MarketingLanguageModel languageModel = BeanCopyUtils.jsonCopyBean(marketingLanguage,
					MarketingLanguageModel.class);
			languageModel.setDomainCode(luckyDrawDomain.getDomainCode());
			languageModel.setTenantCode(luckyDrawDomain.getTenantCode());
			languageModel.setOrgCode(luckyDrawDomain.getOrgCode());
			languageModel.setActivityCode(activityCode);
			languageService.insert(languageModel);
		}
	}

	private void createMarketingPrize(LuckyDrawDomain luckyDrawDomain, String activityCode) {
		List<Integer> orders = getOrders(luckyDrawDomain.getMarketingPrizes());
		for (MarketingPrize marketingPrize : luckyDrawDomain.getMarketingPrizes()) {
			if (PrizeTypeEnum.COUPON.equalsCode(marketingPrize.getPrizeType())) {
				ActivityModel couponActivity = activityService
						.findActivityByActivityCode(luckyDrawDomain.getTenantCode(), marketingPrize.getPrizeCode());
				Check.check(null == couponActivity, MarketingChecker.PRIZE_ACTIVITY_NOT_EXIST);
				Check.check(!couponActivity.isValid(new Date()), MarketingChecker.PRIZE_ACTIVITY_NOT_EFFECTIVE);
			}
			PrizeModel prizeModel = BeanCopyUtils.jsonCopyBean(marketingPrize, PrizeModel.class);
			prizeModel.setDomainCode(luckyDrawDomain.getDomainCode());
			prizeModel.setTenantCode(luckyDrawDomain.getTenantCode());
			prizeModel.setPrizeInventory(prizeModel.getPrizeQuota());
			prizeModel.setOrgCode(luckyDrawDomain.getOrgCode());
			String prizeNo = gTechCodeGenerator.generateCode(MarketingConstants.APP_KEY, "PNO",
					MarketingConstants.MKT_CODE_TEMPLATE_EXP, 105L);
			prizeModel.setPrizeNo(prizeNo);
			prizeModel.setActivityCode(activityCode);
			if (null == prizeModel.getPrizeOrder()) {
				prizeModel.setPrizeOrder(orders.remove(random.nextInt(orders.size())));
			}
			if (StringUtil.isEmpty(prizeModel.getPrizeCode())) {
				String prizeCode = gTechCodeGenerator.generateCode(MarketingConstants.APP_KEY, "PCD",
						MarketingConstants.MKT_CODE_TEMPLATE_EXP, 105L);
				prizeModel.setPrizeCode(prizeCode);
			}
			prizeService.insert(prizeModel);

			for (MarketingPrizeLanguage marketingPrizeLanguage : marketingPrize.getMarketingPrizeLanguages()) {
				if (StringUtil.isNotBlank(marketingPrizeLanguage.getPrizeName())) {
					PrizeLanguageModel prizeLanguageModel = BeanCopyUtils.jsonCopyBean(marketingPrizeLanguage,
							PrizeLanguageModel.class);
					prizeLanguageModel.setDomainCode(luckyDrawDomain.getDomainCode());
					prizeLanguageModel.setTenantCode(luckyDrawDomain.getTenantCode());
					prizeLanguageModel.setOrgCode(luckyDrawDomain.getOrgCode());
					prizeLanguageModel.setPrizeNo(prizeNo);
					prizeLanguageModel.setActivityCode(activityCode);
					prizeLanguageService.insert(prizeLanguageModel);
				}
			}
		}
	}

	private List<Integer> getOrders(List<MarketingPrize> marketingPrizes) {
		List<Integer> result = new ArrayList<>();
		for (int i = 1; i <= marketingPrizes.size(); i++) {
			result.add(i);
		}
		for (MarketingPrize marketingPrize : marketingPrizes) {
			if (null != marketingPrize.getPrizeOrder()) {
				result.remove(marketingPrize.getPrizeOrder());
			}
		}
		return result;
	}

	@Transactional
	public int updateMarketingStatus(MarketingDomain marketingDomain) {
		updateMarketingStatusCheck(marketingDomain);
		String activityStatus = marketingDomain.getActivityStatus();
		String activityCode = marketingDomain.getActivityCode();
		MarketingModel condition = BeanCopyUtils.jsonCopyBean(marketingDomain, MarketingModel.class);
		condition.setActivityStatus(null);
		MarketingModel result = new MarketingModel();

		result.setActivityStatus(activityStatus);
		result.setActivityCode(activityCode);
		if (ActivityStatusEnum.PENDING.code().equals(activityStatus)) {
			result.setCreateUser(marketingDomain.getOperateUser());
		}
		if (ActivityStatusEnum.IN_AUDIT.code().equals(activityStatus)) {
			result.setAuditUser(marketingDomain.getOperateUser());
		}
		result.setUpdateUser(marketingDomain.getOperateUser());
		int update = marketingService.updateByActivityCode(result);
		MarketingModel byActivityCode = marketingService.findByActivityCode(activityCode);
		marketingCacheComponent.updateCacheByStatus(byActivityCode, activityStatus);

		String operationType;
		String tenantCode = byActivityCode.getTenantCode();
		if (ActivityStatusEnum.EFFECTIVE.code().equals(activityStatus)) {
			operationType = OperationTypeEnum.ACTIVATION.code();
			if (ActivityTypeEnum.FLASH_SALE.equalsCode(byActivityCode.getActivityType())
					&& DateUtil.parseDate(byActivityCode.getActivityEnd(), DateUtil.FORMAT_YYYYMMDDHHMISS_14)
							.getTime() > System.currentTimeMillis()
					&& DateUtil.parseDate(byActivityCode.getActivityBegin(), DateUtil.FORMAT_YYYYMMDDHHMISS_14)
							.getTime() <= System.currentTimeMillis()) {
				dataSyncComponent.sendPriceToPim(tenantCode, activityCode, false);
			}
		} else if (ActivityStatusEnum.IN_AUDIT.code().equals(activityStatus)) {
			operationType = OperationTypeEnum.APPROVAL.code();
			if (ActivityTypeEnum.FLASH_SALE.equalsCode(byActivityCode.getActivityType())) {
				dataSyncComponent.sendPriceToPim(tenantCode, activityCode, true);
			}
		} else {
			operationType = ActivityStatusEnum.END.code().equals(activityStatus) ? OperationTypeEnum.TERMINATION.code()
					: OperationTypeEnum.COMPLETION.code();
			if (ActivityTypeEnum.FLASH_SALE.equalsCode(byActivityCode.getActivityType())) {
				dataSyncComponent.sendPriceToPim(tenantCode, activityCode, true);
			}
		}
		operationLogService.insertLog(
				OperationLogModel.builder().tenantCode(marketingDomain.getTenantCode()).activityCode(activityCode)
						.operationType(operationType).createLastName(marketingDomain.getOperateLastName())
						.createFirstName(marketingDomain.getOperateFirstName())
						.createUser(marketingDomain.getOperateUser()).build(),
				JSONObject.toJSONString(marketingDomain));

		try {
			//拼团业务处理
			if (ActivityStatusEnum.END.code().equals(activityStatus) && ActivityTypeEnum.GROUP.code().equals(byActivityCode.getActivityType())) {

				MarketingGroupMode marketingGroup = marketingGroupService.findMarketingGroupByActivityCode(tenantCode, activityCode);

				//0允许 1不允许（ 0开 1关闭）
				Integer autoGroupFlag = marketingGroup.getAutoGroupFlag();
				List<String> groupStatusList = Arrays.asList(MarketingGroupStatusEnum.GROUP_NO_START.code(), MarketingGroupStatusEnum.GROUP_PROCESSING.code());
				List<MarketingGroupCodeMode> list = marketingGroupCodeService.queryGroupByActivityCode(tenantCode, activityCode,groupStatusList,null);

				for (MarketingGroupCodeMode marketingGroupCodeMode : list) {

					String groupStatus = marketingGroupCodeMode.getGroupStatus();
					String marketingGroupCode = marketingGroupCodeMode.getMarketingGroupCode();
					if (String.valueOf(autoGroupFlag).equals(AutoGroupFlagEnum.NO.code())) {

						if (groupStatus.equals(MarketingGroupStatusEnum.GROUP_NO_START.code()) || (groupStatus.equals(MarketingGroupStatusEnum.GROUP_PROCESSING.code())
								&& marketingGroupCodeMode.getInventory() > 0)) {

							//更新拼团表，用户表状态失败
							marketingGroupUserService.updateGroupStatusByMarketingGroupCode(tenantCode, marketingGroupCode, UserGroupStatusEnum.CANCEL);
							marketingGroupCodeService.updateMarketingCodeGroupStatus(tenantCode, activityCode, marketingGroupCode, MarketingGroupStatusEnum.GROUP_FAIL.code());

							//模糊删除该拼团下所有消息记录 即该团团长消息记录
							String matchKey = activityRedisHelpler.queryFuzzyMatchingMarketingGroupUserKey(tenantCode, activityCode, marketingGroupCode);
							activityRedisHelpler.deleteFuzzyMatch(matchKey);
							//mq消息推送
							marketingGroupComponent.sendMessageToOms(marketingGroupCodeMode, "0");
						}
					}else {

						//活动终止， 允许自动成团，针对进行中的，未开始的不在这触发，在支付时确认是否成团
						if (groupStatus.equals(MarketingGroupStatusEnum.GROUP_PROCESSING.code())){

							//更新拼团表，用户表状态支付变拼团成功
							marketingGroupUserService.updateGroupUserStatus(tenantCode, marketingGroupCode, UserGroupStatusEnum.FINISH.code(),
									UserGroupStatusEnum.PAID.code());
							marketingGroupCodeService.updateMarketingCodeGroupStatus(tenantCode, activityCode, marketingGroupCode, MarketingGroupStatusEnum.GROUP_SUCCESS.code());

							//模糊删除该拼团下所有消息记录 即该团团长消息记录
							String matchKey = activityRedisHelpler.queryFuzzyMatchingMarketingGroupUserKey(tenantCode, activityCode, marketingGroupCode);
							activityRedisHelpler.deleteFuzzyMatch(matchKey);
							//mq消息推送
							marketingGroupComponent.sendMessageToOms(marketingGroupCodeMode, "1");
						}

					}
				}
			}
		}catch (Exception e){
			//拼团业务不影响主流程
		}


		return update;
	}

	public LuckyDrawFindResult findMarketing(MarketingDomain marketingDomain) {
		BaseModel baseModel = BeanCopyUtils.jsonCopyBean(marketingDomain, BaseModel.class);
		MarketingModel marketingModel = marketingService.findByActivityCode(baseModel.getActivityCode());
		LuckyDrawFindResult result = BeanCopyUtils.jsonCopyBean(marketingModel, LuckyDrawFindResult.class);

		List<MarketingLanguageModel> marketingLanguageModels = languageService
				.findListByActivityCode(baseModel.getActivityCode());
		result.setMarketingLanguages(BeanCopyUtils.jsonCopyList(marketingLanguageModels, MarketingLanguage.class));

		ActivityPeriodModel period = activityPeriodService.findPeriod(baseModel.getTenantCode(),
				baseModel.getActivityCode());
		result.setActivityPeriod(BeanCopyUtils.jsonCopyBean(period, ActivityPeriod.class));

		List<PrizeModel> prizeModels = prizeService.findListByActivityCode(baseModel.getActivityCode());
		result.setMarketingPrizes(BeanCopyUtils.jsonCopyList(prizeModels, MarketingPrizeResult.class));

		// 参与资格列表
		List<QualificationModel> qualificationModels = qualificationService
				.queryLuckyDrawQualifications(baseModel.getTenantCode(), baseModel.getActivityCode());
		List<Qualification> qualifications = QualificationModel.convert(qualificationModels);
		result.setQualifications(qualifications);

		// 限制条件
		List<TPromoIncentiveLimitedVO> limitedVOs = incentiveLimitedService
				.getLuckyDrawLimitedListByActivityCode(baseModel.getActivityCode());
		List<IncentiveLimited> incentiveLimiteds = BeanCopyUtils.jsonCopyList(limitedVOs, IncentiveLimited.class);
		result.setIncentiveLimiteds(incentiveLimiteds);

		// 抽奖规则
		List<LuckyDrawRuleModel> luckyDrawRuleModels = luckyDrawRuleService
				.queryLuckyDrawRulesByActivityCode(baseModel.getTenantCode(), baseModel.getActivityCode());
		List<LuckyDrawRule> luckyDrawRules = BeanCopyUtils.jsonCopyList(luckyDrawRuleModels, LuckyDrawRule.class);
		result.setLuckyDrawRules(luckyDrawRules);

		List<PrizeLanguageModel> prizeLanguageModels = prizeLanguageService
				.findListByActivityCode(baseModel.getActivityCode());
		Map<String, List<PrizeLanguageModel>> collect = prizeLanguageModels.stream()
				.collect(Collectors.groupingBy(PrizeLanguageModel::getPrizeNo));

		for (MarketingPrizeResult prize : result.getMarketingPrizes()) {
			String prizeNo = prize.getPrizeNo();
			if (collect.containsKey(prizeNo)) {
				prize.setMarketingPrizeLanguages(
						BeanCopyUtils.jsonCopyList(collect.get(prizeNo), MarketingPrizeLanguage.class));
			}
		}

		return result;
	}

	public PageResult<MarketingQueryResult> queryMarketingList(MarketingQueryInDto inDto) {
		PageData<MarketingModel> marketingModelPageData = marketingService.queryMarketingList(inDto);
		List<QueryOpUserAccountListResult> idmOpUserAccount = idmFeignClientComponent
				.getIdmOpUserAccount(BeanCopyUtils.jsonCopyList(marketingModelPageData.getList(), ActivityModel.class));
		List<QueryUserResult> userAccountList = idmFeignClientComponent.queryIdmUserList(inDto.getDomainCode(),
				inDto.getTenantCode(),
				BeanCopyUtils.jsonCopyList(marketingModelPageData.getList(), ActivityModel.class));
		if (CollectionUtils.isNotEmpty(idmOpUserAccount) || CollectionUtils.isNotEmpty(userAccountList)) {
			String auditConfig = activityComponentDomain.getActivityAuditConfig(inDto.getTenantCode());
			for (MarketingModel model : marketingModelPageData.getList()) {

				String activityCode = model.getActivityCode();
				List<FlashSaleStoreModel> listByActivityCode = flashSaleStoreService.findListByActivityCode(activityCode);
				model.setStores(BeanCopyUtils.jsonCopyList(listByActivityCode, ActivityStore.class));

				if (CollectionUtils.isNotEmpty(idmOpUserAccount)) {
					QueryOpUserAccountListResult queryOpUserAccountListResult = idmOpUserAccount.stream()
							.filter(x -> x.getUserCode().equals(model.getCreateUser())).findFirst()
							.orElse(new QueryOpUserAccountListResult());
					model.setCreateUserFirstName(queryOpUserAccountListResult.getFirstName());
					model.setCreateUserLastName(queryOpUserAccountListResult.getLastName());
				}
				if (StringUtil.isEmpty(model.getCreateUserFirstName()) && StringUtil.isEmpty(model.getCreateUserLastName()) && CollectionUtils.isNotEmpty(userAccountList)) {

					QueryUserResult queryUserResult = userAccountList.stream()
							.filter(x -> x.getUserCode().equals(model.getCreateUser())).findFirst()
							.orElse(new QueryUserResult());
					model.setCreateUserFirstName(queryUserResult.getFirstName());
					model.setCreateUserLastName(queryUserResult.getLastName());

				}
				buildMarketingModelAuditConfig(model, auditConfig);
				setMarketCommitUser(model);
			}
		}
		return new PageResult<>(
				BeanCopyUtils.jsonCopyList(marketingModelPageData.getList(), MarketingQueryResult.class),
				marketingModelPageData.getTotal());
	}

	public void setMarketCommitUser(MarketingModel model) {
		String activityStatus = model.getActivityStatus();
		if (ActivityStatusEnum.CLOSURE.code().equals(activityStatus)
				|| ActivityStatusEnum.EFFECTIVE.code().equals(activityStatus)
				|| ActivityStatusEnum.END.code().equals(activityStatus)) {
			model.setCommitUser(model.getUpdateUser());
		}
	}

	public void buildMarketingModelAuditConfig(MarketingModel model, String auditConfig) {
		model.setNeedAudit(activityComponentDomain.isNeedAudit(auditConfig));
		model.setNeedDifferentOperator(activityComponentDomain.isAuditDifferentOperator(auditConfig));
	}

	@Transactional
	public int extendMarketing(MarketingDomain marketingDomain, String endTime) {
		MarketingModel selectOne = marketingService.findByActivityCode(marketingDomain.getActivityCode());
		Check.check(null == selectOne, MarketingChecker.ACTIVITY_NOT_EXIST);
		Check.check(
				selectOne.isNeedToDoExpire() || selectOne.getActivityStatus().equals(ActivityStatusEnum.END.code())
						|| selectOne.getActivityStatus().equals(ActivityStatusEnum.CLOSURE.code()),
				TPromoActivityChecker.ACTIVITY_END);
		long activityEnd = DateUtil.parseDate(selectOne.getActivityEnd(), DateUtil.FORMAT_YYYYMMDDHHMISS_14).getTime();
		long endTimeLong = DateUtil.parseDate(endTime, DateUtil.FORMAT_YYYYMMDDHHMISS_14).getTime();
		Check.check(activityEnd >= endTimeLong, TPromoActivityChecker.END_TIME_BEFORE_ACTIVITY_END_TIME);

		MarketingModel result = new MarketingModel();
		result.setActivityEnd(endTime);
		result.setActivityCode(marketingDomain.getActivityCode());
		int update = marketingService.updateByActivityCode(result);

		operationLogService.insertLog(
				OperationLogModel.builder().activityCode(marketingDomain.getActivityCode())
						.tenantCode(marketingDomain.getTenantCode()).createUser(marketingDomain.getOperateUser())
						.createLastName(marketingDomain.getOperateLastName())
						.createFirstName(marketingDomain.getOperateFirstName())
						.operationType(OperationTypeEnum.EXTENSION.code()).build(),
				JSONObject.toJSONString(marketingDomain));

		selectOne.setActivityEnd(endTime);
		marketingCacheComponent.updateCacheByStatus(selectOne, ActivityStatusEnum.EFFECTIVE.code());
		return update;
	}

	@Transactional
	public int expireMarketing() {
		List<MarketingModel> marketingModels = marketingService.queryShouldExpireMarketingList();
		if (CollectionUtils.isNotEmpty(marketingModels)) {
			for (MarketingModel marketingModel : marketingModels) {
				dataSyncComponent.sendPriceToPim(marketingModel.getTenantCode(), marketingModel.getActivityCode(),
						true);
			}
		}
		return marketingService.expireMarketing();
	}

	private void updateMarketingStatusCheck(MarketingDomain marketingDomain) {
		String activityStatus = marketingDomain.getActivityStatus();
		MarketingModel model = marketingService.findByActivityCode(marketingDomain.getActivityCode());
		Check.check(null == model, MarketingChecker.ACTIVITY_NOT_EXIST);
		String auditConfig = activityComponentDomain.getActivityAuditConfig(model.getTenantCode());
		if (StringUtil.isEmpty(auditConfig)
				|| Constants.NEED_AUDIT_NO.equals(activityComponentDomain.isNeedAudit(auditConfig))) {
			Check.check(ActivityStatusEnum.IN_AUDIT.equalsCode(activityStatus),
					TPromoActivityChecker.TENANT_NOT_NEED_AUDIT);
		} else {
			String needDifferent = activityComponentDomain.isAuditDifferentOperator(auditConfig);
			if (ActivityStatusEnum.IN_AUDIT.code().equals(activityStatus)
					&& Constants.NEED_DIFFERENT_YES.equals(needDifferent)) {
				Check.check(activityComponentDomain.isContainOperator(model.getCreateUser(), null,
						marketingDomain.getOperateUser()), TPromoActivityChecker.CREATE_AUDIT_NEED_DIFFERENT);
			}
			if (ActivityStatusEnum.EFFECTIVE.code().equals(activityStatus)
					&& Constants.NEED_DIFFERENT_YES.equals(needDifferent)) {
				Check.check(
						activityComponentDomain.isContainOperator(model.getCreateUser(), model.getAuditUser(),
								marketingDomain.getOperateUser()),
						TPromoActivityChecker.CREATE_AUDIT_COMMIT_NEED_DIFFERENT);
			}
		}
	}

}
