/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.component.activity;

import com.gtech.commons.utils.BeanCopyUtils;
import com.gtech.promotion.code.activity.FuncTypeEnum;
import com.gtech.promotion.dao.model.activity.*;
import com.gtech.promotion.dto.cache.ActivityCacheDTO;
import com.gtech.promotion.dto.cache.ProductSkuDetailDTO;
import com.gtech.promotion.dto.in.activity.TenantProductDTO;
import com.gtech.promotion.dto.out.activity.ActivityInfoDTO;
import com.gtech.promotion.utils.ActivityFilterUtil;
import com.gtech.promotion.vo.bean.ActivityPeriod;
import com.gtech.promotion.vo.bean.Giveaway;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 活动查询
 * 
 */
@Service
public class ActivityQueryDomain {

    @Autowired
    private ActivityCacheDomain activityCacheDomain;

    /**
     * 根据商品查询促销列表
     * 
     * @param product 系统商户和商品信息
     */
    public List<ActivityInfoDTO> getActivityByProduct(TenantProductDTO product, Map<String, ActivityCacheDTO> activityCacheMap, List<ProductSkuDetailDTO> productSkuDetailDTOS){

        activityCacheMap = ActivityFilterUtil.filterActivityByOrgCodes(activityCacheMap, product.getOrgCodes());
        activityCacheMap = activityCacheDomain.filterActivityByProduct(activityCacheMap, product, productSkuDetailDTOS);
        //返回对象集合
        List<ActivityInfoDTO> activities = new ArrayList<>();
        //数据处理
        activityCacheMap.forEach((x,y) -> {
            //活动数据
            ActivityInfoDTO activity = getActivityInfoDTO(y);
            //url处理
            urlDeal(product, y, activity);
            //参与资格处理
            qualificationDeal(y, activity);
            //赠品列表集合处理
            giveawaysDeal(y, activity);

            activities.add(activity);
        });

        return activities;
    }

    public List<ActivityInfoDTO> cacheConvertActivity(Map<String, ActivityCacheDTO> activityCacheMap){

        //返回对象集合
        List<ActivityInfoDTO> activities = new ArrayList<>();
        //数据处理
        activityCacheMap.forEach((x,y) -> {
            //活动数据
            ActivityInfoDTO activity = getActivityInfoDTO(y);

            //参与资格处理
            qualificationDeal(y, activity);
            //赠品列表集合处理
            giveawaysDeal(y, activity);

            activities.add(activity);
        });

        return activities;
    }

    public ActivityInfoDTO getActivityInfoDTO(ActivityCacheDTO y) {
        ActivityModel activityVo = y.getActivityModel();
        ActivityInfoDTO activity = BeanCopyUtils.jsonCopyBean(activityVo, ActivityInfoDTO.class);
        activity.setActivityPeriod(BeanCopyUtils.jsonCopyBean(y.getPeriodModel(), ActivityPeriod.class));
        activity.setActivityCode(activityVo.getActivityCode());
        activity.setActivitySort(Integer.valueOf(y.getPromoTemplate().getTagCode()).toString());
        activity.setPromoScope(y.getPromoTemplate().getTemplateCode().substring(2, 4));
        activity.setSeqNum("0".equals(y.getSeqNum()) ? "1" : y.getSeqNum());
        return activity;
    }

    // url处理
    private void urlDeal(TenantProductDTO product, ActivityCacheDTO y, ActivityInfoDTO activity) {

        List<TPromoActivityStoreVO> promoStores = y.getPromoChannels();
        List<String> orgCodes = product.getOrgCodes();
        if (CollectionUtils.isEmpty(promoStores) || CollectionUtils.isEmpty(orgCodes)) {
            return;
        }

        for (TPromoActivityStoreVO activityStoreVO : promoStores) {
            if (orgCodes.contains(activityStoreVO.getOrgCode())) {
                // 只有入参传了对应的店铺编码，才会去把该店铺的url设置到活动上；为空或者匹配不上，都取默认主表的
                activity.setActivityUrl(activityStoreVO.getUrl());
            }
        }
    }

    private void giveawaysDeal(ActivityCacheDTO y, ActivityInfoDTO activity){

        List<Giveaway> giveaways = BeanCopyUtils.jsonCopyList(y.getGiveaways(), Giveaway.class);
        //模板函数编码的值，0406赠品选择数量限制
        TemplateModel promoTemplate = y.getPromoTemplate();
        if (FuncTypeEnum.IncentiveEnum.GIVEAWAY.equalsCode(promoTemplate.getTemplateCode().substring(12, 16))){
            List<FunctionParamModel> promoFuncParams = y.getPromoFuncParams();
            if (!CollectionUtils.isEmpty(promoFuncParams)){
                promoFuncParams.forEach(promoFunc -> {//赠品活动只能有一个层级
                    if (FuncTypeEnum.IncentiveEnum.GIVEAWAY.equalsCode(promoFunc.getFunctionCode())){//0406
                        String paramValue = promoFunc.getParamValue();
                        activity.setGiftLimitMax(paramValue);
                    }
                });
            }
        }

        activity.setGiveaways(giveaways);
    }

    //会员等级处理
    private void qualificationDeal(ActivityCacheDTO y, ActivityInfoDTO activity){
        activity.setQualifications(QualificationModel.convert(y.getQualificationModels()));
    }

}
