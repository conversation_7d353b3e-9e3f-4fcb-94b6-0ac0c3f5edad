package com.gtech.promotion.component.activity;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.gtech.basic.masterdata.response.JsonResult;
import com.gtech.commons.code.LogicDeleteEnum;
import com.gtech.commons.redis.GTechCodeGenerator;
import com.gtech.commons.utils.BeanCopyUtils;
import com.gtech.commons.utils.CheckUtils;
import com.gtech.commons.utils.StringUtil;
import com.gtech.promotion.checker.activity.TPromoActivityChecker;
import com.gtech.promotion.code.activity.GroupTypeEnum;
import com.gtech.promotion.code.activity.PromotionGroupEnum;
import com.gtech.promotion.code.activity.TurnOnAndOffEnum;
import com.gtech.promotion.code.marketing.DeleteEnum;
import com.gtech.promotion.dao.entity.activity.PromoGroupRelationEntity;
import com.gtech.promotion.dao.mapper.activity.PromoGroupRelationMapper;
import com.gtech.promotion.dao.model.activity.*;
import com.gtech.promotion.dao.model.marketing.MarketingModel;
import com.gtech.promotion.dto.cache.ActivityCacheDTO;
import com.gtech.promotion.dto.in.activity.GroupBindingActivityVO;
import com.gtech.promotion.dto.in.activity.GroupPriorityVO;
import com.gtech.promotion.dto.in.activity.GroupQueryListVO;
import com.gtech.promotion.dto.out.activity.GroupActivityVO;
import com.gtech.promotion.exception.Check;
import com.gtech.promotion.exception.ErrorCodes;
import com.gtech.promotion.feign.MasterDataFeignClient;
import com.gtech.promotion.helper.ActivityRedisHelpler;
import com.gtech.promotion.page.RequestPage;
import com.gtech.promotion.service.activity.ActivityService;
import com.gtech.promotion.service.activity.PromotionGroupRelationService;
import com.gtech.promotion.service.activity.PromotionGroupService;
import com.gtech.promotion.service.activity.TPromoActivityExpressionService;
import com.gtech.promotion.service.marketing.MarketingService;
import com.gtech.promotion.vo.param.activity.*;
import com.gtech.promotion.vo.result.activity.ActivityGroupResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description :
 * <AUTHOR> gaojie.li
 * @Date : 2023/6/14 9:53
 */
@Component
@Slf4j
public class PromoGroupDomain {

    public static final String DISABLE_OVERLAY_MUTEX_ARRAY = "Disable_Overlay_Mutex_Array";

    @Autowired
    private PromotionGroupService promotionGroupService;

    @Autowired
    private ActivityService activityService;
    @Autowired
    private MarketingService marketingService;

	@Autowired
	private GTechCodeGenerator codeGenerator;

    @Autowired
    private ActivityRedisHelpler redisService;

    @Autowired
    private ActivityCacheDomain activityCacheDomain;


    @Autowired
    private TPromoActivityExpressionService expressionService;

    @Autowired
    private PromoGroupRelationMapper promoGroupRelationMapper;



	public static final String GROUP_CODE = "groupCode";
    public static final String TENANT_GROUP_RELATION = "TENANT_GROUP_RELATION";
	public static final String TENANT_GROUP_BLACKLIST = "TENANT_GROUP_BLACKLIST";


    @Autowired
    private MasterDataFeignClient masterDataFeignClient;


    @Autowired
    private PromoGroupRelationDomain promoGroupRelationDomain;


    @Autowired
    private PromotionGroupRelationService promotionGroupRelationService;



    /***
     * 是否开启分组
     * @param tenantCode
     * @param opsType
     * @return
     */
    public boolean checkGroupEnabledByTenantCode(String tenantCode,String opsType) {
        //分组是否开启
        JsonResult<String> result = null;
        try {
            result = masterDataFeignClient.getValueValue(tenantCode, TENANT_GROUP_RELATION);
        } catch (Exception e) {
            log.error("group config error in master data.");
        }

        if (null != result && result.getSuccess() &&
                TurnOnAndOffEnum.TURN_ON.code().equals(result.getData())) {
            int count = promotionGroupService.getGroupCountGroupType(tenantCode, opsType);
            //初始化分组
            Check.check(count <= 0, TPromoActivityChecker.INIT_GROUP);
            //opsType 作为分组编码 直接关联活动
            return true;
        }
        return false;
    }

    /**
     * 1.分组叠加互斥黑名单(检查当前租户是否在黑名单中,如果在黑名单中,则使用自动创建分组功能)
     *
     * 2.
     * 2.1初始化活动所属分组编码
     * update promo_activity set group_code = ops_type where tenant_code = '100016';
     * update marketing set group_code = ops_type where tenant_code = '100016';
     *
     * 2.2调用api生成分组(包含优先级) 和 互斥 (已存在的优先级按照默认的顺序排序)
     *
     * 2.3 删除reids：PROMOTION_ACTIVITY_CACHE:TENANTCODE
     *         删除redis: PROMOTION_COUPON_CODE_CACHE:TENANTCODE
     *         删除redis: PROMOTION_ACTIVITY_CACHE_LEVEL_TWO
     *         删除redis：group:relation:
     * @param tenantCode
     * @param opsType
     * @return
     */
    public String queryGroupCode(String domainCode,String tenantCode,String opsType){

        //查询当前是否有对应的组
        String groupCode = StringUtil.EMPTY;

        //如果有对应的组，返回组编码
        PromoGroupVO groupByGroupCode = promotionGroupService.getGroupByGroupCode(tenantCode, opsType);
        if (null != groupByGroupCode){
            return groupByGroupCode.getGroupCode();
        }


        //如果有没有对应的组，初始化分组
        GroupCreateParam createParam = new GroupCreateParam();
        createParam.setTenantCode(tenantCode);
        createParam.setDomainCode(domainCode);
        createParam.setOperatorUser("system");
        PromotionGroupEnum promotionGroupEnum = PromotionGroupEnum.getPromotionGroupEnum(opsType);
        createParam.setGroupName(promotionGroupEnum.getDesc());
        createParam.setExtParam(PromotionGroupEnum.getEnUSNameByCode(opsType));
        PromoGroupVO groupOutVO = promotionGroupService.getLastedGroup(tenantCode);

        setGroupMode(createParam, promotionGroupEnum.getCode(), groupOutVO, GroupTypeEnum.DEFAULT.code());

        //添加分组互斥关系
        List<String> dataDisableList = Lists.newArrayList(
                PromotionGroupEnum.NINE_GRID.getCode(),
                PromotionGroupEnum.TURNTABLE.getCode(),
                PromotionGroupEnum.SMASH_GOLDEN_EGG.getCode(),
                PromotionGroupEnum.MULTI_PERSON_GROUP.getCode(),
                PromotionGroupEnum.SECKILL.getCode(),
                PromotionGroupEnum.PRESALE.getCode(),
                PromotionGroupEnum.BOOST_SHARING.getCode()
        );
        if (!dataDisableList.contains(opsType)) {
            createGroupRelationExclusion(opsType, dataDisableList, tenantCode, domainCode);
        }
        //缓存刷新
        promoGroupRelationDomain.refreshGroupCache(tenantCode);
        return opsType;
    }




    @Transactional
    public void init (String domainCode,String tenantCode){

        //修复旧数据分组编码
        promotionGroupService.refreshActivityGroupData(domainCode,tenantCode);


        //查询所有活动和营销活动,查询所有已使用的ops_type
        List<ActivityModel> activityModels = activityService.queryActivityByTenantCode(tenantCode, null, null,null);
        Map<String, List<ActivityModel>> activityByOpsType = activityModels.stream().collect(Collectors.groupingBy(ActivityModel::getOpsType));
        List<MarketingModel> marketingModels = marketingService.queryMarketingList(tenantCode, null, null);
        Map<String, List<MarketingModel>> marketingByOpsType = marketingModels.stream().collect(Collectors.groupingBy(MarketingModel::getOpsType));

        //如果opsType是1开头,则是活动,如果是2开头,则是券活动自动拼接1_或者2_到templeteCode前面
        Map<String, List<String>> tempaleOpsTypeMap = new HashMap<>();
        Map<String, List<String>> opsTempaleCodeMap = new HashMap<>();
        /*for (Map.Entry<String, List<ActivityModel>> entry : activityByOpsType.entrySet()) {
            if (entry.getKey().startsWith("1")){
                for (ActivityModel activityModel : entry.getValue()) {
                    tempaleOpsTypeMap.put("1_"+activityModel.getTemplateCode(),entry.getKey());
                }
            }else if (entry.getKey().startsWith("2")){
                for (ActivityModel activityModel : entry.getValue()) {
                    tempaleOpsTypeMap.put("2_"+activityModel.getTemplateCode(),entry.getKey());
                }
            }
        }*/
        activityByOpsType.forEach((k,v)->{
            List<String> list = new ArrayList<>();
            for (ActivityModel activityModel : v) {
                list.add(activityModel.getTemplateCode());
            }
            opsTempaleCodeMap.put(k,list);
        });


        for (Map.Entry<String, List<ActivityModel>> entry : activityByOpsType.entrySet()) {
            for (ActivityModel activityModel : entry.getValue()) {
                //如果存在就追加,否则就新增
                if (tempaleOpsTypeMap.containsKey(activityModel.getTemplateCode())){
                    tempaleOpsTypeMap.get(activityModel.getTemplateCode()).add(entry.getKey());
                }else {
                    tempaleOpsTypeMap.put(activityModel.getTemplateCode(), Lists.newArrayList(entry.getKey()));
                }
            }
        }



        List<String> allOpsType = new ArrayList<>(activityByOpsType.keySet());
        allOpsType.addAll(marketingByOpsType.keySet());
        allOpsType = allOpsType.stream().distinct().collect(Collectors.toList());
        String expression = expressionService.getActivityExpression(tenantCode);


        //给所有已有活动初始化分组
        Map<String, Integer> orderedMap = new LinkedHashMap<>();
        List<String> and = Arrays.asList(expression.split("and"));
        //and截取除'1_'/'2_'以外的字符
        and = and.stream().map(s -> s.replace("1_", "").replace("2_", "")).collect(Collectors.toList());


        for (String group : and) {
            orderedMap.put(group, and.indexOf(group)+1);
        }
        List<PromoGroupMode> promoGroupModes = new ArrayList<>();
        for (Map.Entry<String, List<String>> tempale : tempaleOpsTypeMap.entrySet()) {
            for (String value : tempale.getValue()) {

                Map<String, Integer> orderMap = orderedMap.entrySet().stream().filter(entry -> entry.getKey().contains(tempale.getKey())).collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
                if (orderMap.isEmpty()) {
                    continue;
                }
                //获取value值
                Integer order = orderMap.values().stream().findFirst().get();

                //查询orderedMap中key包含opsType的value值
                //Integer collect = orderedMap.entrySet().stream().filter(entry -> entry.getKey().contains(tempale.getKey())).map(Map.Entry::getValue).collect(Collectors.toList()).get(0);
                PromoGroupMode promoGroupMode = new PromoGroupMode();
                promoGroupMode.setGroupCode(value);
                promoGroupMode.setPriority((long) (order));
                promoGroupMode.setTenantCode(tenantCode);
                promoGroupMode.setGroupName(PromotionGroupEnum.getPromotionGroupEnum(value).getDesc());
                promoGroupMode.setDomainCode(domainCode);
                promoGroupMode.setCreateUser("system");
                promoGroupMode.setUpdateUser("system");
                promoGroupMode.setType(GroupTypeEnum.DEFAULT.code());
                promoGroupMode.setLogicDelete(LogicDeleteEnum.NORMAL.code());
                promoGroupMode.setExtParam(PromotionGroupEnum.getEnUSNameByCode(value));
                promoGroupModes.add(promoGroupMode);
            }
        }

        //promoGroupModes 去除groupCode相同的数据
        promoGroupModes = promoGroupModes.stream().distinct().collect(Collectors.toList());

        //promoGroupModes中的priority字段根据顺序排序,再按照顺序重新给priority按照1-9的顺序赋值
        promoGroupModes = promoGroupModes.stream().sorted(Comparator.comparing(PromoGroupMode::getPriority)).collect(Collectors.toList());
        for (int i = 0; i < promoGroupModes.size(); i++) {
            promoGroupModes.get(i).setPriority((long) (i+1));
        }

        //营销分组添加
        List<String> marketingGroup = new ArrayList<>(marketingByOpsType.keySet());
        marketingGroup = marketingGroup.stream().distinct().collect(Collectors.toList());
        for (String groupCode : marketingGroup) {
            PromoGroupMode promoGroupMode = new PromoGroupMode();
            promoGroupMode.setGroupCode(groupCode);
            promoGroupMode.setPriority((long) (promoGroupModes.size()+1));
            promoGroupMode.setTenantCode(tenantCode);
            promoGroupMode.setGroupName(PromotionGroupEnum.getPromotionGroupEnum(groupCode).getDesc());
            promoGroupMode.setDomainCode(domainCode);
            promoGroupMode.setCreateUser("system");
            promoGroupMode.setUpdateUser("system");
            promoGroupMode.setType(GroupTypeEnum.DEFAULT.code());
            promoGroupMode.setLogicDelete(LogicDeleteEnum.NORMAL.code());
            promoGroupMode.setExtParam(PromotionGroupEnum.getEnUSNameByCode(groupCode));
            promoGroupModes.add(promoGroupMode);
        }

        promotionGroupService.insertActivityGroupList(promoGroupModes);

        //String str = "{\"1_0101020103010402\":\"101\",\"1_0102020303020401\":\"103\",\"1_0102020203020413\":\"102\",\"1_0102020203020404\":\"104\",\"1_0101020603020402\":\"105\",\"1_0101020703020406\":\"106\",\"1_0102020203020415\":\"107\",\"2_0104020103010401\":\"201\",\"2_0102020203020401\":\"202\",\"2_0102020203030401\":\"203\",\"2_0101020703020406\":\"204\",\"2_0102020203020405\":\"205\",\"2_0101020603020401\":\"206\"}";



        //过滤map中存在在allOpsType中的数据
        List<String> finalAllOpsType = allOpsType;
        tempaleOpsTypeMap = tempaleOpsTypeMap.entrySet().stream()
                .filter(entry -> entry.getValue().stream().anyMatch(finalAllOpsType::contains))
                .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));

        for (String singleGroup : expression.split("and")) {
            for (String template : tempaleOpsTypeMap.keySet()) {
                if (singleGroup.contains(template)) {
                    for (String code : tempaleOpsTypeMap.keySet()) {
                        if (singleGroup.contains(code) && !template.equals(code)) {
                            for (String value : tempaleOpsTypeMap.get(template)) {
                                for (String value2 : tempaleOpsTypeMap.get(code)) {
                                    PromoGroupRelationEntity entityA = new PromoGroupRelationEntity();
                                    entityA.setGroupCodeA(value);
                                    entityA.setGroupCodeB(value2);
                                    entityA.setTenantCode(tenantCode);
                                    entityA.setDomainCode(domainCode);
                                    if (entityA.getGroupCodeA().equals(entityA.getGroupCodeB())) {
                                        continue;
                                    }
                                    try {
                                        promoGroupRelationMapper.insertSelective(entityA);
                                    } catch (DuplicateKeyException e) {
                                        log.error(e.getMessage());
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
        //TODO 维护所有活动和营销活动的opsType
        String[] all = { "101", "102", "103", "104", "105", "106", "107", "108", "201", "202", "203", "204", "205", "206", "207", "208" };
        String[] ma = { "301", "302", "303", "401", "501", "601", "701" };

        //营销和促销互斥
        for (String a : all) {
            for (String b : ma) {
                GroupRelationVO groupRelationVO = new GroupRelationVO();
                groupRelationVO.setGroupCodeA(a);
                groupRelationVO.setGroupCodeB(b);
                groupRelationVO.setRelation(2);
                try {
                    promotionGroupRelationService.createGroupRelation(tenantCode, domainCode, groupRelationVO);
                } catch (DuplicateKeyException e) {
                    log.error(e.getMessage());
                }
            }
        }
        //营销和营销互斥
        for (int i = 0; i < ma.length; i++) {
            for (int j = 0; j < ma.length; j++) {
                if (i == j) {
                    continue;
                }
                PromoGroupRelationEntity entityA = new PromoGroupRelationEntity();
                entityA.setGroupCodeA(ma[i]);
                entityA.setGroupCodeB(ma[j]);
                entityA.setTenantCode(tenantCode);
                entityA.setDomainCode(domainCode);
                if (entityA.getGroupCodeA().equals(entityA.getGroupCodeB())){
                    continue;
                }
                try {
                    promoGroupRelationMapper.insertSelective(entityA);
                } catch (DuplicateKeyException e) {
                    log.error(e.getMessage());
                }
            }
        }
        //缓存刷新
        promoGroupRelationDomain.refreshGroupCache(tenantCode);

        //删除reids：PROMOTION_ACTIVITY_CACHE:TENANTCODE
        //        删除redis: PROMOTION_COUPON_CODE_CACHE:TENANTCODE
        //        删除redis: PROMOTION_ACTIVITY_CACHE_LEVEL_TWO
        //        删除redis：group:relation:
        //        删除redis：PROMOTION_ACTIVITY_CACHE_LEVEL_TWO
        //        删除redis：MARKETING_ACTIVITY_CACHE:
        redisService.removeActivityCacheLevelTwo(tenantCode);
        redisService.deleteRedisByKey("PROMOTION_ACTIVITY_CACHE:TENANTCODE"+"=" + tenantCode);
        //删除券
        redisService.deleteRedisByKey("PROMOTION_COUPON_CODE_CACHE:TENANTCODE"+"=" + tenantCode);
        //删除营销
        redisService.deleteRedisByKey("MARKETING_ACTIVITY_CACHE_LEVEL_TWO:TENANTCODE"+"=" + tenantCode);
        redisService.deleteRedisByKey("MARKETING_ACTIVITY_CACHE:TENANTCODE"+"=" + tenantCode);



    }







    /**
     * 初始化分组
     * @param groupInitParam
     */
    @Transactional
    public void initGroupDomain(GroupInitParam groupInitParam) {

        //分组是否开启
        JsonResult<String> result = masterDataFeignClient.getValueValue(groupInitParam.getTenantCode(), TENANT_GROUP_RELATION);

        Check.check(!(null != result && result.getSuccess() && TurnOnAndOffEnum.TURN_ON.code().equals(result.getData())), TPromoActivityChecker.TURN_ON_GROUP);

        //促销和营销中默认互斥
        JsonResult<String> disableList = masterDataFeignClient.getValueValue(groupInitParam.getTenantCode(), DISABLE_OVERLAY_MUTEX_ARRAY);

        String dataResult = disableList.getData();

        JSONObject request = new JSONObject();
        request.put("ddCode", TENANT_GROUP_RELATION);
        request.put("tenantCode", groupInitParam.getTenantCode());

        JSONObject jsonObject = masterDataFeignClient.queryDdPages(request);
        JSONObject data = jsonObject.getJSONObject("data");
        JSONArray list = data.getJSONArray("list");
        for (Object object : list) {
            JSONObject jsonObject1 = JSONObject.parseObject(object.toString());

            GroupCreateParam createParam = new GroupCreateParam();
            createParam.setTenantCode(groupInitParam.getTenantCode());
            createParam.setDomainCode(groupInitParam.getDomainCode());
            createParam.setOperatorUser(groupInitParam.getOperatorUser());

            //作为分组编码
            String ddValue = jsonObject1.getString("ddValue");
            //分组名称
            String ddText = jsonObject1.getString("ddText");
            createParam.setGroupName(ddText);
            createParam.setExtParam(PromotionGroupEnum.getEnUSNameByCode(ddValue));
            PromoGroupVO groupOutVO = promotionGroupService.getLastedGroup(groupInitParam.getTenantCode());
            setGroupMode(createParam, ddValue, groupOutVO, GroupTypeEnum.DEFAULT.code());

            //互斥分组
            if (null != dataResult) {
                List<String> dataDisableList = JSONArray.parseArray(dataResult, String.class);
                if (!dataDisableList.contains(ddValue)){
                    createGroupRelationExclusion(ddValue, dataDisableList, groupInitParam.getTenantCode(), groupInitParam.getDomainCode());
                }
            }
        }

        //缓存刷新
        promoGroupRelationDomain.refreshGroupCache(groupInitParam.getTenantCode());
    }


    public int setGroupMode(GroupCreateParam groupParam, String groupCode, PromoGroupVO groupOutVO,String groupType) {

        Long priority = 1L;
        if (null != groupOutVO && groupOutVO.getPriority() != null) {
            priority = groupOutVO.getPriority() + 1;
        }
        PromoGroupMode promoGroupMode = new PromoGroupMode();
        promoGroupMode.setGroupCode(groupCode);
        promoGroupMode.setPriority(priority);
        promoGroupMode.setTenantCode(groupParam.getTenantCode());
        promoGroupMode.setGroupName(groupParam.getGroupName());
        promoGroupMode.setDomainCode(groupParam.getDomainCode());
        promoGroupMode.setCreateUser(groupParam.getOperatorUser());
        promoGroupMode.setUpdateUser(groupParam.getOperatorUser());
        promoGroupMode.setType(groupType);
        if (StringUtil.isNotEmpty(groupParam.getExtParam())){
            promoGroupMode.setExtParam(groupParam.getExtParam());
        }
        return promotionGroupService.insertActivityGroup(promoGroupMode);
    }

    /**
     * 新建分组
     * @param groupParam
     * @return
     */
    @Transactional
    public String createGroupDomain(GroupCreateParam groupParam) {

        PromoGroupVO groupOutVO = promotionGroupService.getLastedGroup(groupParam.getTenantCode());

        String newGroupCode = codeGenerator.generateCode(groupParam.getTenantCode(), GROUP_CODE, "PG[D:yyyyMMddHHmmss][SM:%06d]", 1l);

        int i = setGroupMode(groupParam, newGroupCode, groupOutVO, GroupTypeEnum.CUSTOM.code());


        //促销和营销中默认互斥
        JsonResult<String> disableList = masterDataFeignClient.getValueValue(groupParam.getTenantCode(), DISABLE_OVERLAY_MUTEX_ARRAY);

        String dataResult = disableList.getData();
        //互斥分组
        if (null != dataResult) {
            List<String> dataDisableList = JSONArray.parseArray(dataResult, String.class);
            createGroupRelationExclusion(newGroupCode, dataDisableList, groupParam.getTenantCode(), groupParam.getDomainCode());
        }

        if (i> 0){
            //缓存刷新
            promoGroupRelationDomain.refreshGroupCache(groupParam.getTenantCode());
        }

        return newGroupCode;

    }

    public void createGroupRelationExclusion(String newGroupCode, List<String> dataDisableList, String tenantCode, String domainCode) {
        for (String s : dataDisableList) {
            Check.check(StringUtil.isEmpty(s), TPromoActivityChecker.DISABLE_GROUP);
            GroupRelationVO groupRelationVO = new GroupRelationVO();
            groupRelationVO.setGroupCodeA(newGroupCode);
            groupRelationVO.setGroupCodeB(s);
            groupRelationVO.setRelation(2);
            //捕获重复key异常
            try {
                promotionGroupRelationService.createGroupRelation(tenantCode, domainCode, groupRelationVO);
            } catch (DuplicateKeyException e) {
                log.error("createGroupRelationExclusion error", e);
            }
        }
    }

    /**
     * 删除分组
     * @param groupDeleteParam
     * @return
     */
    @Transactional
    public Integer deleteGroupDomain(GroupDeleteParam groupDeleteParam) {

        List<ActivityModel> activityModels = activityService.queryActivityByGroupCode(groupDeleteParam.getTenantCode(), groupDeleteParam.getGroupCode());

        CheckUtils.isTrue(!CollectionUtils.isNotEmpty(activityModels), ErrorCodes.GROUP_ACTIVITY_EFFECTIVE);

        PromoGroupVO groupVO = promotionGroupService.getGroupByGroupCode(groupDeleteParam.getTenantCode(), groupDeleteParam.getGroupCode());

        CheckUtils.isTrue(null != groupVO, ErrorCodes.PARAM_GROUP_NOT_EXIST);

        CheckUtils.isTrue(!GroupTypeEnum.DEFAULT.code().equals(groupVO.getType()), ErrorCodes.DEFAULT_GROUP);

        int i = promotionGroupService.deleteActivityGroup(groupDeleteParam.getTenantCode(), groupDeleteParam.getGroupCode(), groupDeleteParam.getOperatorUser());

        promotionGroupRelationService.deleteGroupRelationByGroupCode(groupDeleteParam.getTenantCode(), groupDeleteParam.getDomainCode(), groupDeleteParam.getGroupCode());

        if (i > 0){
            //缓存刷新
            promoGroupRelationDomain.refreshGroupCache(groupDeleteParam.getTenantCode());
        }

        return i;

    }

    /**
     * 更新分组
     * @param groupUpdateParam
     * @return
     */
    @Transactional
    public Integer updateGroupDomain(GroupUpdateParam groupUpdateParam) {

        PromoGroupMode promoGroupMode = BeanCopyUtils.jsonCopyBean(groupUpdateParam, PromoGroupMode.class);
        promoGroupMode.setUpdateUser(groupUpdateParam.getOperatorUser());
        return promotionGroupService.updateActivityGroup(promoGroupMode);
    }

    /**
     * 查询分组
     * @param groupQueryParam
     * @return
     */
    public List<ActivityGroupResult> queryActivityGroupListDomain(GroupQueryParam groupQueryParam) {

        if (StringUtil.isEmpty(groupQueryParam.getLogicDelete())){
            groupQueryParam.setLogicDelete(DeleteEnum.NORMAL.code());
        }
        GroupQueryVO groupQueryVO = BeanCopyUtils.jsonCopyBean(groupQueryParam, GroupQueryVO.class);

        List<PromoGroupMode> promoGroupModes = promotionGroupService.queryActivityGroupList(groupQueryVO);

        List<PromoGroupMode> collect = promoGroupModes.stream().sorted(Comparator.comparing(PromoGroupMode::getPriority)).collect(Collectors.toList());

        return BeanCopyUtils.jsonCopyList(collect,ActivityGroupResult.class);

    }

    /**
     * 绑定活动到分组
     * @param bindingActivityParam
     * @return
     */
    public Integer bindingActivityToGroupDomain(GroupBindingActivityParam bindingActivityParam) {

        List<String> activityCodes = bindingActivityParam.getActivityCodes();
        int i = 0;
        for (String activityCode : activityCodes) {
            GroupBindingActivityVO groupBindingActivityVO = new GroupBindingActivityVO();
            groupBindingActivityVO.setTenantCode(bindingActivityParam.getTenantCode());
            groupBindingActivityVO.setActivityCode(activityCode);
            groupBindingActivityVO.setGroupCode(bindingActivityParam.getGroupCode());
            groupBindingActivityVO.setDomainCode(bindingActivityParam.getDomainCode());
            int i1 = activityService.bindingActivityToGroup(groupBindingActivityVO);
            if (i1 >0 ){
                setActivityCache(bindingActivityParam.getTenantCode(),activityCode,bindingActivityParam.getGroupCode());
            }
            i += i1;
        }
        return i;

    }


    public void setActivityCache(String tenantCode,String activityCode,String groupCode){

        ActivityModel activity = activityService.findActivity(tenantCode, activityCode, null);
        ActivityCacheDTO cacheDTO = activityCacheDomain.getActivityCacheDTO(activity);
        cacheDTO.getActivityModel().setGroupCode(groupCode);
        redisService.removeActivityCache(tenantCode, activity.getActivityType(), activityCode,activity.getActivityStatus());
        redisService.setActivityCache(cacheDTO);
        redisService.removeActivityCacheLevelTwo(tenantCode);

    }

    /**
     * 分组活动列表
     * @param param
     * @return
     */
    public PageInfo<GroupActivityVO> queryActivityListUnderGroup(GroupQueryListParam param) {

        GroupQueryListVO groupQueryListVO = BeanCopyUtils.jsonCopyBean(param, GroupQueryListVO.class);

        RequestPage page = new RequestPage(param.getPageNo(), param.getPageCount());
        PageInfo<GroupActivityVO> pageInfo = activityService.queryActivityListUnderGroup(groupQueryListVO, page);
        List<GroupActivityVO> list = pageInfo.getList();

        if (CollectionUtils.isNotEmpty(list)) {
            for (GroupActivityVO groupActivityVO : list) {

                String groupCode = groupActivityVO.getGroupCode();
                groupActivityVO.setActivityOrgCode(groupActivityVO.getOrgCode());

                if (StringUtil.isNotEmpty(groupCode)){
                    PromoGroupVO groupVO = promotionGroupService.getGroupByGroupCode(param.getTenantCode(), groupCode);
                    groupActivityVO.setGroupName(groupVO.getGroupName());
                }
            }
        }

        return pageInfo;
    }

    /**
     * 分组设置优先级
     * @param priorityParam
     * @return
     */
    @Transactional
    public Integer updateGroupPriorityDomain(GroupSettingPriorityParam priorityParam) {


        GroupPriorityVO priorityVO = BeanCopyUtils.jsonCopyBean(priorityParam, GroupPriorityVO.class);

        //参数传入的数据， 越在前优先级越大
        List<GroupPriorityVO.ActivityGroup> groups = priorityVO.getGroups();

        List<PromoGroupVO> promoGroupVOS = promotionGroupService.listActivityGroupByTenantCode(priorityParam.getTenantCode());

        //优先级排序
        List<Long> collect = promoGroupVOS.stream().map(PromoGroupVO::getPriority).collect(Collectors.toList());
        Collections.sort(collect);

        //赋值
        for (int i = 0; i < groups.size(); i++){
            GroupPriorityVO.ActivityGroup activityGroup = groups.get(i);
            activityGroup.setPriority(collect.get(i));
        }
        int row = promotionGroupService.updateActivityGroupPriority(priorityVO);

        if (row > 0){

            //缓存刷新
            promoGroupRelationDomain.refreshGroupCache(priorityParam.getTenantCode());
        }

        return row;
    }
}
