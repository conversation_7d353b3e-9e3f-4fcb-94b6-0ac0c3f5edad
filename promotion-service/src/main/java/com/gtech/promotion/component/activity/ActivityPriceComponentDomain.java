package com.gtech.promotion.component.activity;

import java.math.BigDecimal;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.gtech.basic.masterdata.client.MasterDataClient;
import com.gtech.basic.masterdata.response.JsonResult;
import com.gtech.commons.redis.GTechRedisTemplate;
import com.gtech.promotion.calc.CalcConstants;
import com.gtech.promotion.utils.Constants;

import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
public class ActivityPriceComponentDomain {

    @Autowired
    private MasterDataClient masterDataClient;

    @Autowired
    private GTechRedisTemplate redisTemplate;

    public static final String TENANT_SCALE =  "TENANT_DEFAULT_BIGDECIMAL_SCALE";
	public static final String TENANT_POWER_SCALE = "TENANT_DEFAULT_BIGDECIMAL_POWER_SCALE";

    //获取租户金额保留位数
    public Integer getTenantPrecision(String tenantCode){

        Integer precision = CalcConstants.LAST_PRECISION;

        if(StringUtils.isEmpty(tenantCode)){
            return precision;
        }

        String key = Constants.APP_KEY + ":" + TENANT_SCALE + ":" + tenantCode;
        precision = redisTemplate.opsValueGet(Constants.APP_KEY,key,Integer.class);
        if (null !=  precision){
            //设置小数点
            return precision;
        }
        try {
            JsonResult<String> jsonResult = masterDataClient.getValueValue(tenantCode, TENANT_SCALE);
            if(null != jsonResult && !StringUtils.isEmpty(jsonResult.getData())){
                precision = Integer.valueOf(jsonResult.getData());
            }
        }catch (Exception e){
            log.error("获取数据字典TENANT_DEFAULT_BIGDECIMAL_SCALE失败！");
            log.error(e.getMessage(), e);
        }
        redisTemplate.opsValueSet(Constants.APP_KEY, key,precision, 5*60*1000L);
        return precision;
    }

	// 获取租户金额保留位数
	public BigDecimal getTenantPowerPrecision(String tenantCode) {

		BigDecimal precision = null;

		if (StringUtils.isEmpty(tenantCode)) {
			return precision;
		}

		String key = Constants.APP_KEY + ":" + TENANT_POWER_SCALE + ":" + tenantCode;
		precision = redisTemplate.opsValueGet(Constants.APP_KEY, key, BigDecimal.class);
		if (null != precision) {
			// 设置小数点
			if (precision.compareTo(BigDecimal.ZERO) == 0) {
				return null;
			}
			return precision;
		}
		try {
			JsonResult<String> jsonResult = masterDataClient.getValueValue(tenantCode, TENANT_POWER_SCALE);
			if (null != jsonResult && !StringUtils.isEmpty(jsonResult.getData())) {
				precision = new BigDecimal(jsonResult.getData());
			}
		} catch (Exception e) {
			log.error("获取数据字典TENANT_DEFAULT_BIGDECIMAL_POWER_SCALE失败！");
			log.error(e.getMessage(), e);
		}
		if (precision == null) {
			redisTemplate.opsValueSet(Constants.APP_KEY, key, BigDecimal.ZERO, 5 * 60 * 1000L);
			return null;
		}
		redisTemplate.opsValueSet(Constants.APP_KEY, key, precision, 5 * 60 * 1000L);
		if (precision.compareTo(BigDecimal.ZERO) == 0) {
			return null;
		}
		return precision;
	}

}
