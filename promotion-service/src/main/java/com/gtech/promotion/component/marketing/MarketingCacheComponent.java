package com.gtech.promotion.component.marketing;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.gtech.commons.utils.BeanCopyUtils;
import com.gtech.commons.utils.CryptoUtils;
import com.gtech.commons.utils.DateUtil;
import com.gtech.commons.utils.StringUtil;
import com.gtech.promotion.checker.activity.TPromoActivityChecker;
import com.gtech.promotion.code.activity.ActivityStatusEnum;
import com.gtech.promotion.code.activity.SelectorProductTypeEnum;
import com.gtech.promotion.code.marketing.ActivityTypeEnum;
import com.gtech.promotion.dao.model.activity.ActivityPeriodModel;
import com.gtech.promotion.dao.model.marketing.BaseModel;
import com.gtech.promotion.dao.model.marketing.CacheMarketingModel;
import com.gtech.promotion.dao.model.marketing.MarketingLanguageModel;
import com.gtech.promotion.dao.model.marketing.MarketingModel;
import com.gtech.promotion.dao.model.marketing.flashsale.CacheFlashSaleModel;
import com.gtech.promotion.dao.model.marketing.flashsale.FlashSaleProductModel;
import com.gtech.promotion.dao.model.marketing.flashsale.FlashSaleQualificationModel;
import com.gtech.promotion.dao.model.marketing.flashsale.FlashSaleStoreModel;
import com.gtech.promotion.exception.Check;
import com.gtech.promotion.service.activity.ActivityPeriodService;
import com.gtech.promotion.service.flashsale.FlashSaleProductService;
import com.gtech.promotion.service.flashsale.FlashSaleQualificationService;
import com.gtech.promotion.service.flashsale.FlashSaleStoreService;
import com.gtech.promotion.service.marketing.BoostSharingService;
import com.gtech.promotion.service.marketing.MarketingLanguageService;
import com.gtech.promotion.service.marketing.MarketingService;
import com.gtech.promotion.utils.MarketingConstants;
import com.gtech.promotion.vo.bean.ProductCodes;
import com.gtech.promotion.vo.param.marketing.flashsale.SkuParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.collections4.map.HashedMap;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.stream.Collectors;

@Slf4j
@Component
public class MarketingCacheComponent {

    @Autowired
    private StringRedisTemplate redisTemplate;

    @Autowired
    private MarketingService marketingService;

    @Autowired
    private FlashSaleQualificationService flashSaleQualificationService;
    @Autowired
    private MarketingLanguageService marketingLanguageService;

    @Autowired
    private BoostSharingService boostSharingService;
    @Autowired
    private FlashSaleProductService flashSaleProductService;
    @Autowired
    private FlashSaleStoreService flashSaleStoreService;
    @Autowired
    private ActivityPeriodService activityPeriodService;

    private static final String TENANT_CODE = "TENANT_CODE";
    private static final String ACTIVITY_CODE = "ACTIVITY_CODE";
    private static final String ACTIVITY_TYPE = "ACTIVITY_TYPE";
    private static final String STATUS = "STATUS";
    private static final AtomicBoolean lockOk = new AtomicBoolean();
    private Map<String, String> localCacheMap = new HashMap<>();

    public void updateCacheByStatus(MarketingModel marketingModel, String activityStatus){
        String activityCode = marketingModel.getActivityCode();
        MarketingModel marketingModel1 = marketingService.findByActivityCode(activityCode);
        Check.check(null == marketingModel1, TPromoActivityChecker.NOT_NULL);
        Check.check(ActivityStatusEnum.EFFECTIVE.equalsCode(activityStatus) && DateUtil.parseDate(marketingModel1.getActivityEnd(), DateUtil.FORMAT_YYYYMMDDHHMISS_14).getTime() <= System.currentTimeMillis(), TPromoActivityChecker.END_TIME_NOW);
        if (ActivityStatusEnum.EFFECTIVE.equalsCode(activityStatus)) {
            CacheMarketingModel cacheMarketingModel = BeanCopyUtils.jsonCopyBean(marketingModel1, CacheMarketingModel.class);
            cacheMarketingModel.setLanguages(marketingLanguageService.findListByActivityCode(activityCode));
            setActivityCache(cacheMarketingModel, activityStatus);
        }else if (ActivityStatusEnum.END.equalsCode(activityStatus) || ActivityStatusEnum.CLOSURE.equalsCode(activityStatus)){
            removeActivityCache(marketingModel1);
        }
    }

    public MarketingModel getMarketingCache(String tenantCode, String activityCode){
        String key = createKey(tenantCode, ActivityTypeEnum.LUCKY_DRAW.code(),  activityCode, ActivityStatusEnum.EFFECTIVE.code());
        String s = redisTemplate.opsForValue().get(key);
        if (StringUtil.isNotBlank(s)){
            return JSONObject.parseObject(s, MarketingModel.class);
        }
        return null;
    }

    public Map<String, CacheFlashSaleModel> getFlashSaleCacheMap(String tenantCode, String language,String activityType){
        String levelTwoCache = null;
        String levelTwoKey = MarketingConstants.MARKETING_ACTIVITY_CACHE_LEVEL_TWO + ":" + TENANT_CODE + "="
                + tenantCode + ":" + ACTIVITY_TYPE + "="+activityType;
        // redis缓存
        try {
            // 本地缓存
            levelTwoCache = redisTemplate.opsForValue().get(levelTwoKey);
            if (StringUtil.isNotBlank(levelTwoCache)) {
                String localCacheMapElement = localCacheMap.get(levelTwoKey);
                if (StringUtil.isNotBlank(localCacheMapElement) && levelTwoCache.equals(CryptoUtils.md5Encrypt(localCacheMapElement))) {
                    log.info("flash sale直接走本地缓存，{}，{}", tenantCode, levelTwoCache);
                    Map<String, CacheFlashSaleModel> map = JSON.parseObject(localCacheMapElement, new TypeReference<Map<String, CacheFlashSaleModel>>() {});
                    if (MapUtils.isEmpty(map)){
                        return map;
                    }
                    for(Map.Entry<String, CacheFlashSaleModel> entry : map.entrySet()) {
                        dealLanguage(language, entry.getValue());
                    }
                    return map;
                }
                localCacheMap.remove(levelTwoKey);
            }
            log.info("flash sale本地缓存已过期，{}", tenantCode);
        } catch (Exception e) {
            //
            log.error("flash sale本地缓存出错", e);
        }
        Map<String, CacheFlashSaleModel> map = new HashedMap<>();
        List<MarketingModel> marketingModels = marketingService.queryMarketingList(tenantCode,activityType, ActivityStatusEnum.EFFECTIVE.code());
        if (CollectionUtils.isEmpty(marketingModels)){
            String jsonStrMapValue = JSON.toJSONString(map);
            localCacheMap.put(levelTwoKey, jsonStrMapValue);
            redisTemplate.opsForValue().set(levelTwoKey, CryptoUtils.md5Encrypt(jsonStrMapValue), 5L, TimeUnit.MINUTES);
            return map;
        }
        List<String> keys = new ArrayList<>();
        for (MarketingModel marketingModel : marketingModels) {
            keys.add(createKey(tenantCode, activityType, marketingModel.getActivityCode(), ActivityStatusEnum.EFFECTIVE.code()));
        }
        List<String> values = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(keys)) {
            //获取活动
            values = redisTemplate.opsForValue().multiGet(keys);
        }

        if (getActivityMarketingCache(tenantCode, language, map, values))
            return this.getActivityCacheFromMysql(marketingModels);
        String jsonStrMapValue = JSON.toJSONString(map);
        localCacheMap.put(levelTwoKey, jsonStrMapValue);
        redisTemplate.opsForValue().set(levelTwoKey, CryptoUtils.md5Encrypt(jsonStrMapValue), 5L, TimeUnit.MINUTES);
        return map;
    }



    public void appendMarketingModelToCache(String tenantCode, MarketingModel marketingModel) {
        String cacheKey = createKey(tenantCode, marketingModel.getActivityType(), marketingModel.getActivityCode(), ActivityStatusEnum.EFFECTIVE.code());

        // Update the local cache map
        String levelTwoKey = MarketingConstants.MARKETING_ACTIVITY_CACHE_LEVEL_TWO + ":" + TENANT_CODE + "="
                + tenantCode + ":" + ACTIVITY_TYPE + "=" + marketingModel.getActivityType();
        String localCacheMapElement = localCacheMap.get(levelTwoKey);

        // Ensure the local cache is up-to-date before appending
        if (StringUtil.isNotBlank(localCacheMapElement)) {
            Map<String, CacheMarketingModel> cachedMap = JSON.parseObject(localCacheMapElement, new TypeReference<Map<String, CacheMarketingModel>>() {});
            if (!MapUtils.isEmpty(cachedMap)) {
                cachedMap.put(cacheKey, convertToCacheMarketingModel(marketingModel));
                localCacheMap.put(levelTwoKey, JSON.toJSONString(cachedMap));
            }
        }

        // Update Redis cache
        String cacheValue = JSON.toJSONString(marketingModel);
        redisTemplate.opsForValue().append(cacheKey, cacheValue);

        // Update Redis level two cache key with updated MD5 hash of the local cache map
        String updatedJsonStrMapValue = JSON.toJSONString(localCacheMap.get(levelTwoKey));
        redisTemplate.opsForValue().set(levelTwoKey, CryptoUtils.md5Encrypt(updatedJsonStrMapValue), 5L, TimeUnit.MINUTES);
    }

    private CacheMarketingModel convertToCacheMarketingModel(MarketingModel marketingModel) {
        // Implement conversion logic from MarketingModel to CacheMarketingModel
        return BeanCopyUtils.jsonCopyBean(marketingModel, CacheMarketingModel.class);
    }



    public Map<String, CacheFlashSaleModel> getFlashSaleCacheMapByActivityTypeList(String tenantCode, String language,List<String> activityTypeList){
        Map<String, CacheFlashSaleModel> resultMap = new HashMap<>();
        for (String activityType : activityTypeList) {
            Map<String, CacheFlashSaleModel> map = this.getFlashSaleCacheMap(tenantCode, language, activityType);
            resultMap.putAll(map);
        }
        return resultMap;
    }



    public boolean getActivityMarketingCache(String tenantCode, String language, Map<String, CacheFlashSaleModel> map, List<String> values) {
        if (CollectionUtils.isEmpty(values) || values.stream().anyMatch(StringUtil::isBlank)) {
            if (lockOk.compareAndSet(false, true)){
                try {
                    log.info("redis中没有flash sale，走db，{}", tenantCode);
                    return true;
                } finally {
                    lockOk.set(false);
                }
            }
        }else {
            for (String string : values) {
                CacheFlashSaleModel cacheDTO = JSON.parseObject(string, CacheFlashSaleModel.class);
                if (null != cacheDTO) {
                    dealLanguage(language, cacheDTO);
                    map.put(cacheDTO.getActivityCode(), cacheDTO);
                }
            }
        }
        return false;
    }

    private void dealLanguage(String language, CacheFlashSaleModel cacheDTO) {
        if (StringUtil.isNotBlank(language)){
            List<MarketingLanguageModel> languages = cacheDTO.getLanguages();
            if (CollectionUtils.isNotEmpty(languages)){
                for (MarketingLanguageModel marketingLanguageModel : languages) {
                    if (marketingLanguageModel.getLanguage().equals(language)) {
                        getActivityInfoByMarketingLanguage(cacheDTO, marketingLanguageModel);
                        break;
                    }
                }
            }

        }
    }

    private void getActivityInfoByMarketingLanguage(CacheFlashSaleModel cacheDTO, MarketingLanguageModel marketingLanguageModel) {
        if (StringUtil.isNotBlank(marketingLanguageModel.getActivityName())){
            cacheDTO.setActivityName(marketingLanguageModel.getActivityName());
        }

        if (StringUtil.isNotBlank(marketingLanguageModel.getActivityDesc())){
            cacheDTO.setActivityDesc(marketingLanguageModel.getActivityDesc());
        }

        if (StringUtil.isNotBlank(marketingLanguageModel.getActivityShortDesc())){
            cacheDTO.setActivityShortDesc(marketingLanguageModel.getActivityShortDesc());
        }

        if (StringUtil.isNotBlank(marketingLanguageModel.getActivityLabel())){
            cacheDTO.setActivityLabel(marketingLanguageModel.getActivityLabel());
        }
    }

    private Map<String, CacheFlashSaleModel> getActivityCacheFromMysql(List<MarketingModel> marketingModels) {
        Map<String, CacheFlashSaleModel> map = new HashedMap<>();
        String tenantCode = marketingModels.get(0).getTenantCode();
        List<String> activityCodes = marketingModels.stream().map(MarketingModel::getActivityCode).collect(Collectors.toList());

        //渠道对象列表
        List<FlashSaleStoreModel> storeModels = flashSaleStoreService.getStoresByActivityCodes(tenantCode, activityCodes);
        Map<String, List<FlashSaleStoreModel>> storeMap = storeModels.stream().collect(Collectors.groupingBy(FlashSaleStoreModel::getActivityCode));

        //资格
        List<FlashSaleQualificationModel> qualificationsModels = flashSaleQualificationService.getQualificationsByActivityCodes(tenantCode, activityCodes);
        Map<String, List<FlashSaleQualificationModel>> qualificationMap = qualificationsModels.stream().collect(Collectors.groupingBy(FlashSaleQualificationModel::getActivityCode));

        //多语言列表
        List<MarketingLanguageModel> languageModels = marketingLanguageService.getLanguagesByActivityCodes(tenantCode, activityCodes);
        Map<String, List<MarketingLanguageModel>> languagesMap = languageModels.stream().collect(Collectors.groupingBy(MarketingLanguageModel::getActivityCode));

        //周期
        List<ActivityPeriodModel> periodModels = activityPeriodService.queryPeriodByActivityCodes(tenantCode, activityCodes);
        Map<String, List<ActivityPeriodModel>> periodMap = periodModels.stream().collect(Collectors.groupingBy(ActivityPeriodModel::getActivityCode));

        //分享助力
//        List<BoostSharingModel> boostSharingModels = boostSharingService.queryBoostSharingByActivityList(tenantCode, activityCodes);
//        Map<String, BoostSharingModel> boostSharingMap = boostSharingModels.stream().collect(Collectors.toMap(BoostSharingModel::getActivityCode, Function.identity(), (o, n) -> o));

        for (MarketingModel marketingModel : marketingModels) {
            CacheFlashSaleModel cacheFlashSaleModel = BeanCopyUtils.jsonCopyBean(marketingModel, CacheFlashSaleModel.class);
            String activityCode = marketingModel.getActivityCode();
            cacheFlashSaleModel.setStores(getListOrEmpty(storeMap.get(activityCode)));
            cacheFlashSaleModel.setQualifications(getListOrEmpty(qualificationMap.get(activityCode)));
            cacheFlashSaleModel.setLanguages(getListOrEmpty(languagesMap.get(activityCode)));
            cacheFlashSaleModel.setActivityPeriod(CollectionUtils.isNotEmpty(periodMap.get(activityCode)) ? periodMap.get(activityCode).get(0) : null);
            //cacheFlashSaleModel.setBoostSharingModel(null != boostSharingMap.get(activityCode) ? boostSharingMap.get(activityCode) : null);
            map.put(activityCode, cacheFlashSaleModel);
            setActivityCache(cacheFlashSaleModel, ActivityStatusEnum.EFFECTIVE.code());
        }
        return map;
    }

    private <T> List<T> getListOrEmpty(List<T> list){
        return CollectionUtils.isEmpty(list) ? new ArrayList<>() : list;
    }

    private void removeActivityCache(MarketingModel marketingModel) {
        String key = createKey(marketingModel.getTenantCode(), marketingModel.getActivityType(),  marketingModel.getActivityCode(), ActivityStatusEnum.EFFECTIVE.code());
        String levelTwoKey = MarketingConstants.MARKETING_ACTIVITY_CACHE_LEVEL_TWO + ":" + TENANT_CODE + "="
                + marketingModel.getTenantCode() + ":" + ACTIVITY_TYPE + "="+marketingModel.getActivityType();
        redisTemplate.delete(key);
        redisTemplate.delete(levelTwoKey);
    }

    private void setActivityCache(CacheMarketingModel marketingModel, String targetStatus) {
        String key = createKey(marketingModel.getTenantCode(), marketingModel.getActivityType(), marketingModel.getActivityCode(), targetStatus);
        // 当前时间戳
        long currentTime = System.currentTimeMillis() / 1000;
        //活动结束时间
        long endTime = DateUtil.parseDate(marketingModel.getActivityEnd(), DateUtil.FORMAT_YYYYMMDDHHMISS_14).getTime() / 1000;
        long time = endTime - currentTime;
        if (time < 0){
            time = 1L;
        }
        String value = JSONObject.toJSONString(marketingModel);
        if (ActivityTypeEnum.FLASH_SALE.equalsCode(marketingModel.getActivityType()) ||
                ActivityTypeEnum.PRE_SALE.equalsCode(marketingModel.getActivityType()) ||
                ActivityTypeEnum.GROUP.equalsCode(marketingModel.getActivityType()) ||
                ActivityTypeEnum.BOOST_SHARDING.equalsCode(marketingModel.getActivityType())
        ) {
            CacheFlashSaleModel cacheFlashSaleModel = BeanCopyUtils.jsonCopyBean(marketingModel, CacheFlashSaleModel.class);
            cacheFlashSaleModel.setQualifications(flashSaleQualificationService.findListByActivityCode(marketingModel.getActivityCode()));
            cacheFlashSaleModel.setStores(flashSaleStoreService.findListByActivityCode(marketingModel.getActivityCode()));
            cacheFlashSaleModel.setActivityPeriod(activityPeriodService.findPeriod(marketingModel.getTenantCode(), marketingModel.getActivityCode()));
            if (ActivityTypeEnum.BOOST_SHARDING.equalsCode(marketingModel.getActivityType())) {
                //分享助力
                cacheFlashSaleModel.setBoostSharingModel(boostSharingService.findBoostShardingInfo(marketingModel.getDomainCode(), marketingModel.getTenantCode(), marketingModel.getOrgCode(), marketingModel.getActivityCode()));
            }
            value = JSONObject.toJSONString(cacheFlashSaleModel);
        }
        redisTemplate.opsForValue().set(key, value, time, TimeUnit.SECONDS);
        String key1 = MarketingConstants.MARKETING_ACTIVITY_CACHE_LEVEL_TWO + ":" + TENANT_CODE + "="
                + marketingModel.getTenantCode() + ":" + ACTIVITY_TYPE + "="+marketingModel.getActivityType();
        redisTemplate.delete(key1);
    }

    private String createKey(String tenantCode, String activityType, String activityCode, String targetStatus) {
        return MarketingConstants.MARKETING_ACTIVITY_CACHE + ":" + TENANT_CODE + "="
                    + tenantCode + ":" + ACTIVITY_TYPE + "="
                    + activityType + ":" + ACTIVITY_CODE + "="
                    + activityCode
                    + ":" + STATUS + "=" + targetStatus;
    }

    public void expire(String tenantCode, String activityCode) {
        BaseModel baseModel = new BaseModel();
        baseModel.setTenantCode(tenantCode);
        baseModel.setActivityCode(activityCode);
        marketingService.findByActivityCode(activityCode);
    }

    public Map<String, CacheFlashSaleModel> filterActivityByProduct(Map<String, CacheFlashSaleModel> caches, ProductCodes product, List<FlashSaleProductModel> products, String productFlag) {
        Map<String, CacheFlashSaleModel> newCache = new HashMap<>();//符合的活动
        if (MapUtils.isEmpty(caches) || CollectionUtils.isEmpty(products)) {
            return newCache;
        }

        Map<String, List<FlashSaleProductModel>> stringListMap = products.stream().collect(Collectors.groupingBy(FlashSaleProductModel::getActivityCode));
        Set<String> combineSkuCodeSet = new HashSet<>();
        Set<String> skuCodeSet = new HashSet<>();

        Set<String> combineSpuCodeSet = new HashSet<>();
        Set<String> spuCodeSet = new HashSet<>();

        //TODO 组合商品不支持拼团商品维度
        if(!StringUtils.isEmpty(product.getSkuCode())){
            skuCodeSet.add(product.getCombineSkuCode());
            combineSkuCodeSet.add(product.getSkuCode());
            spuCodeSet.add(product.getProductCode());
        }
        if(!CollectionUtils.isEmpty(product.getSkuList())){
            skuCodeSet = product.getSkuList().stream().map(SkuParam::getSkuCode).collect(Collectors.toSet());
            combineSkuCodeSet = product.getSkuList().stream().map(SkuParam::getCombineSkuCode).collect(Collectors.toSet());
            spuCodeSet = product.getSkuList().stream().map(SkuParam::getProductCode).collect(Collectors.toSet());
        }

        for (Map.Entry<String, CacheFlashSaleModel> next : caches.entrySet()) {
            CacheFlashSaleModel activityCacheDTO = next.getValue();
            List<FlashSaleProductModel> productDetails = stringListMap.get(activityCacheDTO.getActivityCode());
            if (!CollectionUtils.isEmpty(productDetails)) {
                List<FlashSaleProductModel> cacheProducts = new ArrayList<>();
                CacheFlashSaleModel value = setNewCacheDTO(activityCacheDTO);
                for (FlashSaleProductModel productDetail : productDetails) {
                    if (SelectorProductTypeEnum.SELECT_SPU.code().equals(productFlag)){
                        if(spuCodeSet.contains(productDetail.getProductCode()) || combineSkuCodeSet.contains(productDetail.getProductCode())){
                            cacheProducts.add(productDetail);
                        }
                    }else {
                        if(skuCodeSet.contains(productDetail.getSkuCode()) || combineSkuCodeSet.contains(productDetail.getSkuCode())){
                            cacheProducts.add(productDetail);
                        }
                    }
                }
                value.setProducts(cacheProducts);
                newCache.put(next.getKey(), value);
            }
        }
        return newCache;
    }


    private CacheFlashSaleModel setNewCacheDTO(CacheFlashSaleModel activityCacheDTO) {
        CacheFlashSaleModel cacheDTO = new CacheFlashSaleModel();
        BeanUtils.copyProperties(activityCacheDTO, cacheDTO);
        return cacheDTO;
    }


    @PostConstruct
    public void initMarketActivity(){
        log.info("start initMarketActivity!");
        List<MarketingModel> marketingModelList = initMarketData();
        if(CollectionUtils.isEmpty(marketingModelList)){
            return;
        }
        Map<String,List<MarketingModel>> marketMap = marketingModelList.stream().collect(Collectors.groupingBy(MarketingModel::getTenantCode));
        for (Map.Entry<String,List<MarketingModel>> entry : marketMap.entrySet()) {
            String tenantCode = entry.getKey();
            List<MarketingModel> modelList = entry.getValue();
            if(CollectionUtils.isEmpty(modelList)){
                continue;
            }
            List<String> activityList = modelList.stream().map(MarketingModel::getActivityType).distinct().collect(Collectors.toList());
            if(!CollectionUtils.isEmpty(activityList)){
                for(String activityType : activityList){
                    this.getFlashSaleCacheMap(tenantCode,null,activityType);
                }

            }
        }
        log.info("end initMarketActivity!");
    }

    private List<MarketingModel> initMarketData(){
        return marketingService.queryMarketingList(null,null, ActivityStatusEnum.EFFECTIVE.code());
    }
}
