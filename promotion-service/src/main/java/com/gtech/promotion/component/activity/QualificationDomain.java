/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.component.activity;

import com.gtech.commons.utils.ConvertUtils;
import com.gtech.commons.utils.LocalCacheUtil;
import com.gtech.promotion.dao.model.activity.QualificationModel;
import com.gtech.promotion.helper.QualificationFilter;
import com.gtech.promotion.service.activity.QualificationService;
import com.gtech.promotion.vo.bean.Qualification;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 活动会员相关
 */
@Service
public class QualificationDomain {

    @Autowired
    private QualificationService qualificationService;

    private static final Long TIMEOUT = 60 * 1000L;

    /**
     * 校验会员等级
     */
    public boolean checkQualification(String tenantCode, String activityCode, List<Qualification> qualifications) {

        String key = "QualificationDomain#checkQualification" + activityCode + ConvertUtils.toString(qualifications);

        Boolean checkCache = LocalCacheUtil.load(key, Boolean.class);
        if (null != checkCache) {
            return checkCache;
        }

        List<QualificationModel> qualificationModels = qualificationService.queryQualifications(tenantCode, activityCode);

        QualificationFilter filter = new QualificationFilter(qualificationModels);
        boolean filter1 = filter.filter(QualificationModel.convertToModel(qualifications));
        LocalCacheUtil.save(key, filter1, TIMEOUT);
        return filter1;
    }

    public boolean checkQualificationForSendCoupon(String tenantCode, String activityCode, List<Qualification> qualifications) {

        String key = "QualificationDomain#checkQualificationForSendCoupon" + activityCode + ConvertUtils.toString(qualifications);

        Boolean checkCache = LocalCacheUtil.load(key, Boolean.class);
        if (null != checkCache) {
            return checkCache;
        }

        List<QualificationModel> qualificationModels = qualificationService.queryQualificationsForSendCoupon(tenantCode, activityCode);

        QualificationFilter filter = new QualificationFilter(qualificationModels);
        boolean filter1 = filter.filter(QualificationModel.convertToModel(qualifications));
        LocalCacheUtil.save(key, filter1, TIMEOUT);
        return filter1;
    }

}
