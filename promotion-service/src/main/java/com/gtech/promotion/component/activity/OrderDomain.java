/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.component.activity;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.gtech.basic.masterdata.response.JsonResult;
import com.gtech.commons.exception.Exceptions;
import com.gtech.commons.utils.*;
import com.gtech.promotion.calc.CalcConstants;
import com.gtech.promotion.calc.CalcExecuter;
import com.gtech.promotion.calc.TemplateEnum;
import com.gtech.promotion.calc.model.CalcShoppingCart;
import com.gtech.promotion.calc.model.SkuQuantity;
import com.gtech.promotion.checker.activity.TPromoOrderChecker;
import com.gtech.promotion.code.activity.*;
import com.gtech.promotion.code.marketing.RightOfFirstRefusalStatusEnum;
import com.gtech.promotion.component.coupon.CouponInnerCodeComponent;
import com.gtech.promotion.component.coupon.LockCouponComponent;
import com.gtech.promotion.dao.entity.activity.ActivityEntity;
import com.gtech.promotion.dao.entity.activity.TPromoActivityIncentiveEntity;
import com.gtech.promotion.dao.model.activity.ActivityModel;
import com.gtech.promotion.dao.model.activity.TPromoActivityIncentiveVO;
import com.gtech.promotion.dao.model.activity.TPromoIncentiveLimitedVO;
import com.gtech.promotion.dao.model.activity.TPromoOrderVO;
import com.gtech.promotion.dto.cache.ActivityCacheDTO;
import com.gtech.promotion.dto.in.activity.OrderCommitDTO;
import com.gtech.promotion.dto.in.activity.OrderGiveawayDTO;
import com.gtech.promotion.dto.in.activity.ShoppingCartDTO;
import com.gtech.promotion.dto.in.activity.ShoppingCartItem;
import com.gtech.promotion.dto.in.coupon.TCouponLockDTO;
import com.gtech.promotion.dto.in.coupon.TCouponUnLockDTO;
import com.gtech.promotion.dto.in.coupon.TCouponUseInDTO;
import com.gtech.promotion.dto.in.flashsale.WriteOffOfPreEmptiveRightsDto;
import com.gtech.promotion.dto.out.activity.ShoppingCartItemActivityOutDTO;
import com.gtech.promotion.dto.out.activity.ShoppingCartItemOutDTO;
import com.gtech.promotion.dto.out.activity.ShoppingCartOutDTO;
import com.gtech.promotion.exception.Check;
import com.gtech.promotion.exception.ErrorCodes;
import com.gtech.promotion.feign.MasterDataFeignClient;
import com.gtech.promotion.helper.PromotionOrderNoUtil;
import com.gtech.promotion.helper.RedisOpsHelper;
import com.gtech.promotion.helper.RedisOpsHelper.IncentiveLimitedParam;
import com.gtech.promotion.service.activity.ActivityService;
import com.gtech.promotion.service.activity.TPromoActivityIncentiveService;
import com.gtech.promotion.service.activity.TPromoIncentiveLimitedService;
import com.gtech.promotion.service.activity.TPromoOrderService;
import com.gtech.promotion.service.marketing.RightOfFirstRefusalService;
import com.gtech.promotion.service.mongo.activity.TPromoOrderDetailService;
import com.gtech.promotion.vo.bean.Giveaway;
import com.gtech.promotion.vo.mongo.TPromoOrderDetailActivityVO;
import com.gtech.promotion.vo.mongo.TPromoOrderDetailVO;
import com.gtech.promotion.vo.param.activity.ActivityRelationProduct;
import com.gtech.promotion.vo.param.activity.CreateOrderAndCouponParam;
import com.gtech.promotion.vo.param.activity.CreateOrderAndCouponShoppingCartItems;
import com.gtech.promotion.vo.param.activity.LocalAmountObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

import static com.gtech.promotion.checker.activity.TPromoOrderChecker.GIVEAWAY_CHOOSE_OVER_LIMIT;
import static com.gtech.promotion.checker.activity.TPromoOrderChecker.GIVEAWAY_POOL_ONLY_ONE;
import static com.gtech.promotion.code.activity.ActivityExtendParamsEnum.GIVEAWAY_POOL_ENABLE;
import static com.gtech.promotion.code.activity.ActivityExtendParamsEnum.GIVEAWAY_RULES;

/**
 * 促销订单
 */
@Slf4j
@Service
public class OrderDomain {



    @Autowired
    private ShoppingCartDomain shoppingCartDomain;

    @Autowired
    private LockCouponComponent couponDomain;

    @Autowired
    private CouponInnerCodeComponent innerDomain;

    @Autowired
    private TPromoOrderService orderService;

    @Autowired
    private TPromoActivityIncentiveService activityIncentiveService;

    @Autowired
    private TPromoOrderDetailService orderDetailService;

    @Autowired
    private TPromoIncentiveLimitedService limitedService;

    @Autowired
    private RedisOpsHelper incentiveLimitedHelper;

    @Autowired
    private CalcExecuter calcExecuter;

    @Autowired
    private ActivityService activityService;

    @Autowired
    private MasterDataFeignClient masterDataFeignClient;

    @Autowired
    private RightOfFirstRefusalService rightOfFirstRefusalService;




    private Map<String,LocalAmountObject> amountMap = new ConcurrentHashMap<>();

    public List<ShoppingCartOutDTO> checkOrder(OrderCommitDTO orderCommitDTO, Map<String, ActivityCacheDTO> activityCacheMap){

        //校验数据,正确时返回购物车计算结果
        List<ShoppingCartOutDTO> shoppingCartOutDTOs = checkParams(orderCommitDTO, activityCacheMap);

        StringBuilder couponCode = new StringBuilder();



        for (ShoppingCartOutDTO shoppingCartOutDTO : shoppingCartOutDTOs) {
            if (shoppingCartOutDTO.isEffectiveFlag()
                    && ActivityTypeEnum.isCoupon(shoppingCartOutDTO.getActivityType())) {
                couponCode.append(shoppingCartOutDTO.getCouponCode()).append(",");
            }


        }
        orderCommitDTO.setCouponCodes(couponCode.toString());
        //没有活动可参加并且有扣减金额
        Check.check(CollectionUtils.isEmpty(shoppingCartOutDTOs), TPromoOrderChecker.TENANT_CODE_NO_ACTIVITY);
        return shoppingCartOutDTOs;
    }

    /**
     * 提交资源
     * 
     * @return 成功或失败信息
     */
    @Transactional
    public List<ShoppingCartOutDTO> commitOrder(OrderCommitDTO orderCommitDTO, Map<String, ActivityCacheDTO> activityCacheMap, List<ShoppingCartOutDTO> shoppingCartOutDTOs){

        //保存数据总订单数据
        TPromoOrderVO orderVO = new TPromoOrderVO();
        orderVO.setTenantCode(orderCommitDTO.getTenantCode());
        orderVO.setPromoOrderNo(PromotionOrderNoUtil.createCode(orderCommitDTO.getOrderId(), orderCommitDTO.getUserCode(), orderCommitDTO.getTenantCode()));
        orderVO.setPromoOrderSeq(1);
        orderVO.setPromoOrderTime(DateUtil.getCurrentDateAsString(DateUtil.FORMAT_YYYYMMDDHHMISS_14));
        orderVO.setOrderId(orderCommitDTO.getOrderId());
        orderVO.setOrderStatus(OrderStatusEnum.UNPAID.code());
        orderVO.setOrgCode(StoreParamTypeEnum.STORE_ALL_STORE.code());
        orderVO.setUserCode(orderCommitDTO.getUserCode());

        //保存数据,重复时会报错
        String orderId = orderService.insertTPromoOrder(orderVO);
        //校验并锁定资源
        List<IncentiveLimitedParam> limitedKeys = lockSource(shoppingCartOutDTOs, orderCommitDTO, activityCacheMap);

        //保存商品信息
        //传入的商品
        List<ShoppingCartItem> products = orderCommitDTO.getPromoProducts();
        List<TPromoOrderDetailVO> promoOrderDetailVOS = new ArrayList<>(products.size());

        try{
            //记录当前促销关联的订单、会员、奖励信息
            setOrderIncentive(orderCommitDTO, shoppingCartOutDTOs, orderId);

            products.forEach(x -> {
                // mongodb赋值
                TPromoOrderDetailVO promoOrderDetailVO = new TPromoOrderDetailVO();
                promoOrderDetailVO.setAttributes(x.getAttributes());
                promoOrderDetailVO.setTenantCode(orderCommitDTO.getTenantCode());
                promoOrderDetailVO.setBrandCode(x.getBrandCode());
                promoOrderDetailVO.setCategoryCodes(x.getCategoryCodes());
                promoOrderDetailVO.setProductAmount(x.getProductAmount());
                promoOrderDetailVO.setProductPrice(x.getProductPrice());
                promoOrderDetailVO.setPromoOrderId(orderId);
                promoOrderDetailVO.setQuantity(x.getQuantity());
                promoOrderDetailVO.setSkuCode(x.getSkuCode());
                promoOrderDetailVO.setProductCode(x.getProductCode());
                promoOrderDetailVO.setCombineSkuCode(x.getCombineSkuCode());
                List<com.gtech.promotion.vo.mongo.TPromoOrderDetailActivityVO> activities = new ArrayList<>();
                shoppingCartOutDTOs.forEach(m -> {
                    //参与的商品信息
                    List<ShoppingCartItemOutDTO> cartItemOutDTOs = m.getShoppingCartItems();
                    if (!CollectionUtils.isEmpty(cartItemOutDTOs)) {
                        shoppingActivity(x, activities, cartItemOutDTOs);
                    }
                });
                promoOrderDetailVO.setActivities(activities);
                promoOrderDetailVOS.add(promoOrderDetailVO);
            });
            // 最后保存mongo数据，出现异常可以不手工回滚
            orderDetailService.insertOrderDetailList(promoOrderDetailVOS);
        }catch (Exception e){
            log.error("接口提交订单:接口基本完成,插入数据时报错,参数:{}", JSON.toJSONString(orderCommitDTO));
            this.incentiveLimitedHelper.rollBackRedisData(limitedKeys);//数据回滚
            throw e;
        }
        return shoppingCartOutDTOs;
    }

    public void setOrderIncentive(OrderCommitDTO orderCommitDTO, List<ShoppingCartOutDTO> shoppingCartOutDTOs, String orderId) {
        shoppingCartOutDTOs.forEach(x -> {
            TPromoActivityIncentiveVO incentiveVO = new TPromoActivityIncentiveVO();
            incentiveVO.setActivityCode(x.getActivityCode());
            incentiveVO.setPromoOrderId(orderId);
            incentiveVO.setIncentiveType(x.getRewardType());
            incentiveVO.setIncentiveAmount(x.getPromoRewardAmount());
            incentiveVO.setIncentiveTimes(x.getRewardTimes());
            incentiveVO.setUserCode(orderCommitDTO.getUserCode());
            incentiveVO.setTenantCode(orderCommitDTO.getTenantCode());
            incentiveVO.setIncentivePostage(x.getPromoRewardPostage());
            activityIncentiveService.insertActivityIncentive(incentiveVO);
        });
    }

    public void shoppingActivity(ShoppingCartItem x, List<TPromoOrderDetailActivityVO> activities, List<ShoppingCartItemOutDTO> cartItemOutDTOs) {
        //每个商品
        cartItemOutDTOs.forEach(y -> {
            String oCode = x.getSkuCode();
            String sCode = y.getSkuCode();
            if (StringUtil.isBlank(x.getSkuCode())){
                oCode = x.getCombineSkuCode();
            }
            if (StringUtil.isBlank(y.getSkuCode())){
                sCode = y.getCombineSkuCode();
            }
            //单个商品参加的所有活动
            if (oCode.equals(sCode) && !CollectionUtils.isEmpty(y.getShoppingCartItemActivitys())){
                y.getShoppingCartItemActivitys().forEach(z -> setOrderDetailActivityVO(activities, z));
            }

        });
    }

    public void setOrderDetailActivityVO(List<TPromoOrderDetailActivityVO> activities, ShoppingCartItemActivityOutDTO z) {
        //只保存生效的活动
        if (z.isEffectiveFlag()){
            //保存促销订单明细规则
            TPromoOrderDetailActivityVO tPromoOrderDetailActivityVO = new TPromoOrderDetailActivityVO();
            tPromoOrderDetailActivityVO.setActivityCode(z.getActivityCode());
            tPromoOrderDetailActivityVO.setActivityLabel(z.getActivityLabel());
            tPromoOrderDetailActivityVO.setActivityName(z.getActivityName());
            tPromoOrderDetailActivityVO.setActivityType(z.getActivityType());
            tPromoOrderDetailActivityVO.setAmountAfter(z.getAfterAmount());
            tPromoOrderDetailActivityVO.setAmountBefore(z.getBeforeAmount());
            tPromoOrderDetailActivityVO.setCouponCode(z.getCouponCode());
            tPromoOrderDetailActivityVO.setGiveawayLimitMax(z.getGiveawayLimitMax());
            tPromoOrderDetailActivityVO.setGiveaways(z.getGiveaways());
            tPromoOrderDetailActivityVO.setPromoScope(z.getPromoScope());
            activities.add(tPromoOrderDetailActivityVO);
        }
    }

    private List<ShoppingCartOutDTO> checkParams(OrderCommitDTO orderCommitDTO,Map<String, ActivityCacheDTO> activityCacheMap){

        Check.check(StringUtils.isEmpty(orderCommitDTO.getUserCode()), TPromoOrderChecker.NOT_NULL_USER_ID);
        CheckUtils.isNotBlank(orderCommitDTO.getOrderId(), ErrorCodes.PARAM_EMPTY, "orderId");
        Check.check(null == orderCommitDTO.getPromoDeductedAmount(), TPromoOrderChecker.NULL_ORDER_AMOUNT);

        //1.购物车计算---start----
        //转成购物车对象
        ShoppingCartDTO shoppingCart = BeanCopyUtils.jsonCopyBean(orderCommitDTO, ShoppingCartDTO.class);

        ShoppingCartDTO newShoppingCart = shoppingCartDomain.queryActivity(shoppingCart, activityCacheMap,false);

        //购物车商品去过重,将入参对象的商品也去重
        orderCommitDTO.setPromoProducts(shoppingCart.getPromoProducts());

        //计算获得参与的活动
        List<ShoppingCartOutDTO> shoppingCartOutDTOs = calcExecuter.calc(new CalcShoppingCart(newShoppingCart), activityCacheMap);

        if (CollectionUtils.isEmpty(shoppingCartOutDTOs)){
            return new ArrayList<>();
        }

        //去除不满足的活动后的计算数据
        List<ShoppingCartOutDTO> newShoppingCartOutDTOs = new ArrayList<>();

        //map存放参与活动的数据
        Map<String, ShoppingCartOutDTO> activityCodes = new HashMap<>();
        getActivityCodesAndOutDtos(shoppingCartOutDTOs, newShoppingCartOutDTOs, activityCodes);

        //赋值
        shoppingCartOutDTOs = newShoppingCartOutDTOs;
        Check.check(CollectionUtils.isEmpty(shoppingCartOutDTOs), TPromoOrderChecker.TENANT_CODE_NO_ACTIVITY);
        //-----购物车计算 end

        //2.校验金额---start----

        //获得计算减免金额
        BigDecimal promoRewardAmount = BigDecimal.ZERO;
        BigDecimal promoRewardPostage = BigDecimal.ZERO;
        for (ShoppingCartOutDTO shoppingCartOutDTO : shoppingCartOutDTOs){
            promoRewardAmount = shoppingCartOutDTO.getPromoRewardAmount().add(promoRewardAmount);
            promoRewardPostage = shoppingCartOutDTO.getPromoRewardPostage().add(promoRewardPostage);
        }

        String valueCode = "PROMOTION_AMOUNT_ERROR_RANGE";
        String key = orderCommitDTO.getTenantCode() + "-" + valueCode;
        LocalAmountObject local = amountMap.get(key);
        BigDecimal absValue = BigDecimal.ZERO;
        Calendar nowTime = Calendar.getInstance();
        nowTime.add(Calendar.MINUTE, -5);
        long timeInMillis = nowTime.getTimeInMillis();
        if (null != local && local.getLastExpiredTime()>=timeInMillis){
            absValue = new BigDecimal(local.getScopeValue());
        }else {
            absValue = getBigDecimalByMasterData(orderCommitDTO, valueCode, key, absValue);
        }
        //校验减免金额
        log.info("活动重新校验后的减免金额：{}， 接口传入的减免金额：{} ", promoRewardAmount, orderCommitDTO.getPromoDeductedAmount());

//        BigDecimal d1 = promoRewardAmount.setScale(CalcConstants.LAST_PRECISION, CalcConstants.LAST_ROUND)
//        BigDecimal d2 = orderCommitDTO.getPromoDeductedAmount().setScale(CalcConstants.LAST_PRECISION, CalcConstants.LAST_ROUND)
//        BigDecimal d3 = d1.subtract(d2)
//        BigDecimal d4 = d3.abs()
//        int a = d4.compareTo(absValue)
        CheckUtils.isTrue(promoRewardAmount.setScale(CalcConstants.LAST_PRECISION, CalcConstants.LAST_ROUND).subtract(
            orderCommitDTO.getPromoDeductedAmount().setScale(CalcConstants.LAST_PRECISION, CalcConstants.LAST_ROUND)).abs().compareTo(absValue) <= 0,
            ErrorCodes.CREATE_ORDER_DEDUCTED_AMOUNT_ERROR, promoRewardAmount);
        //---------校验金额end

        //校验减免运费
        log.info("活动重新校验后的减免运费金额：{}， 接口传入的减免运费金额：{} ", promoRewardPostage, orderCommitDTO.getPromoRewardPostage());
        CheckUtils.isTrue(promoRewardPostage.setScale(CalcConstants.LAST_PRECISION, CalcConstants.LAST_ROUND).subtract(
                orderCommitDTO.getPromoRewardPostage().setScale(CalcConstants.LAST_PRECISION, CalcConstants.LAST_ROUND)).abs().compareTo(absValue) <= 0,
                ErrorCodes.CREATE_ORDER_DEDUCTED_POSTAGE_ERROR, promoRewardPostage);
        //---------校验运费end


        //3.校验赠品---start----
        if (!CollectionUtils.isEmpty(orderCommitDTO.getPromoGiveaways())){

            for (OrderGiveawayDTO orderGiveawayDTO : orderCommitDTO.getPromoGiveaways()){

                //无赠品继续
                if (CollectionUtils.isEmpty(orderGiveawayDTO.getGiveaways())){
                    continue;
                }

                //先判断是否参与了活动
                Check.check(!activityCodes.containsKey(orderGiveawayDTO.getActivityCode()), TPromoOrderChecker.ERROR_GIVEAWAY_ACTIVITY);

                //n中赠品选择m个
                ShoppingCartOutDTO cart = activityCodes.get(orderGiveawayDTO.getActivityCode());

                Check.check(null == cart, TPromoOrderChecker.ERROR_GIVEAWAY_ACTIVITY);

                //活动是否是赠品活动
                Check.check(!cart.getRewardType().equals(ActivityTagCodeEnum.GIVEAWAY.code()), TPromoOrderChecker.ERROR_GIVEAWAY_ACTIVITY);

                //可以选择的赠品种类m  为0表示全部、
//                int m = Integer.parseInt(cart.getGiveawayLimitMax())

                //实际选择种类
//                int realM = orderGiveawayDTO.getGiveaways().size()

                //选择的种类大于可赠送范围
//                Check.check(m != 0 && realM > m, TPromoOrderChecker.ERROR_GIFT_NUM)

                //赠品是否包含提交订单时所传赠品
                Map<String, Integer> giveawayNumMap = cart.getGiveaways().stream().collect(Collectors.toMap(Giveaway::getGiveawayCode, Giveaway::getGiveawayNum));

                orderGiveawayDTO.getGiveaways().forEach(x -> {
                    //库中不包含时
                    Check.check(!giveawayNumMap.containsKey(x.getGiveawayCode()), TPromoOrderChecker.ERROR_GIVEAWAY_ACTIVITY);
                    Check.check(giveawayNumMap.get(x.getGiveawayCode()) < x.getGiveawayNum(), TPromoOrderChecker.ERROR_GIVEAWAY_NUM_ACTIVITY);
                });
                // 赠品池配置检查
                JSONObject extendParams = Optional.ofNullable(cart.getExtendParams())
                        .orElse(new JSONObject());
                String giveawayPoolEnable = String.valueOf(extendParams.get(GIVEAWAY_POOL_ENABLE.getCode()));
                Map<Integer, List<Giveaway>> giveawayMap = Optional.ofNullable(cart.getGiveaways())
                        .map(m -> m.stream().collect(Collectors.groupingBy(Giveaway::getRankParam)))
                        .orElse(Collections.emptyMap());
                giveawayMap.forEach((rankParam, giveawayList) -> {
                    if (GiveawayPoolEnableEnum.ENABLE.equalsCode(giveawayPoolEnable)) {
                        // 启用赠品池, 赠品只能有一种
                        Check.check(giveawayList.size() > 1, GIVEAWAY_POOL_ONLY_ONE);
                    } else if (GiveawayPoolEnableEnum.DISABLE.equalsCode(giveawayPoolEnable)) {
                        List<ActivityEntity.GiveawayRule> giveawayRules = Optional.ofNullable(extendParams.get(GIVEAWAY_RULES.getCode()))
                                .map(m -> BeanCopyUtils.jsonCopyList(m, ActivityEntity.GiveawayRule.class))
                                .orElse(Lists.newArrayList());
                        giveawayRules.stream()
                                .filter(giveawayRule -> GiveawayMethodEnum.PARTIAL.equalsCode(giveawayRule.getGiveawayMethod()))
                                // 部分送,选择赠品种类不能超出设置上限  (这里修改检查的值从活动计算的赠品数量改为提交订单时传入的赠品数量)
                                .forEach(giveawayRule -> Check.check(orderCommitDTO.getPromoGiveaways()
                                        .size() > giveawayRule.getGiveawayChooseQty(), GIVEAWAY_CHOOSE_OVER_LIMIT));

                    }

                });

            }
        }

        //包邮验证 // NOSONAR
//        if ((!CollectionUtils.isEmpty(shoppingCartOutDTOs)) && orderCommitDTO.getFreePostage().equals(1)){ // NOSONAR
//            Check.check(!shoppingCartOutDTOs.stream().anyMatch(x -> x.getRewardType().equals("05")), TPromoOrderChecker.ERROR_FREE_POSTAGE);// NOSONAR
//        }// NOSONAR

        //校验结束
        return shoppingCartOutDTOs;
    }

    private BigDecimal getBigDecimalByMasterData(OrderCommitDTO orderCommitDTO, String valueCode, String key, BigDecimal absValue) {
        try {
            JsonResult<String> param = masterDataFeignClient.getValueValue(orderCommitDTO.getTenantCode(), valueCode);
            String data = param.getData();
            LocalAmountObject object = new LocalAmountObject();
            if (!StringUtil.isNotEmpty(data)) {
                data = "0";
            }
            object.setScopeValue(data);
            object.setLastExpiredTime(System.currentTimeMillis());
            amountMap.put(key,object);
            absValue= new BigDecimal(data);
        } catch (Exception e){
            log.error("query PROMOTION_AMOUNT_ERROR_RANGE error: ", e);
        }
        return absValue;
    }

    private void getActivityCodesAndOutDtos(List<ShoppingCartOutDTO> shoppingCartOutDTOs, List<ShoppingCartOutDTO> newShoppingCartOutDTOs, Map<String, ShoppingCartOutDTO> activityCodes) {
        shoppingCartOutDTOs.forEach(x -> {
            if (x.isEffectiveFlag()){
                newShoppingCartOutDTOs.add(x);
                activityCodes.put(x.getActivityCode(), x);
            }
        });
    }

    /**
     * 锁定资源
     * 
     * @param shoppingCartOutDTOs 购物车计算出参数据
     * @return  1:成功
     */
    @Transactional
    public List<IncentiveLimitedParam> lockSource(List<ShoppingCartOutDTO> shoppingCartOutDTOs, OrderCommitDTO orderCommitDTO, Map<String, ActivityCacheDTO> activityCacheMap) {

        //所有数量限制的集合key: redis自增的key value: 值(出现异常时用于数据回滚).
        List<IncentiveLimitedParam> ilParamList = new ArrayList<>();
        // 每日订单数的限制，同一个活动在购物车不同商品上出现， 按1次计算
        List<String> dayOrderActivityCodeList = new ArrayList<>();
        List<String> userDayOrderList = new ArrayList<>();
        //锁定资源(校验是不是符合限定的数据)
        shoppingCartOutDTOs.forEach(x -> {

            if (x.isEffectiveFlag()) {

                // 缓存数据
                ActivityCacheDTO cacheDTO = activityCacheMap.get(x.getActivityCode());
            
                // 活动参与次数
                BigDecimal times = ConvertUtils.toBigDecimal(x.getRewardTimes(), new BigDecimal(1));
                // 活动享受优惠金额
                BigDecimal promoDeductedAmount = ConvertUtils.toBigDecimal(x.getPromoRewardAmount(), BigDecimal.ZERO);

                // 获得限制
                if (IncentiveLimitedFlagEnum.YES.code().equals(cacheDTO.getActivityModel().getIncentiveLimitedFlag())) {
                    List<String> skuCodes = new ArrayList<>();
                    List<Integer> skuQuantity = new ArrayList<>();
                    x.getShoppingCartItems().forEach(y -> {
                        skuCodes.add(y.getSkuCode());
                        skuQuantity.add(y.getQuantity());
                    });
                    //活动的次数限制
                    List<TPromoIncentiveLimitedVO> limitedVOs = cacheDTO.getIncentiveLimiteds();
                    for (TPromoIncentiveLimitedVO limitedVO : limitedVOs) {
                        // Redis超时时间
                        long timeout = DateUtil.parseDate(cacheDTO.getActivityModel().getActivityEnd(), DateUtil.FORMAT_YYYYMMDDHHMISS_14).getTime() - System.currentTimeMillis();

                        IncentiveLimitedParam param = IncentiveLimitedParam.builder()
                            .limitedCode(limitedVO.getLimitationCode())
                            .tenantCode(orderCommitDTO.getTenantCode())
                            .activityCode(x.getActivityCode())
                            .userCode(orderCommitDTO.getUserCode())
                            .totalValue(limitedVO.getLimitationValue())
                            .build();

                        setIncentive(times, promoDeductedAmount, param);

                        if (LimitationCodeEnum.USER_TOTAL_COUNT.equalsCode(limitedVO.getLimitationCode())) {

                            if (!this.incentiveLimitedHelper.lockRedisDataSupportSku(ilParamList, param, timeout, skuCodes, skuQuantity)) {
                                throw Exceptions.fail(ErrorCodes.LOCK_PROMOTION_RESOURCES_FAILED_USER_TOTAL_COUNT);
                            }

                        } else if (LimitationCodeEnum.USER_TOTAL_AMOUNT.equalsCode(limitedVO.getLimitationCode())) {

                            if (!this.incentiveLimitedHelper.lockRedisDataSupportSku(ilParamList, param, timeout, skuCodes, skuQuantity)) {
                                throw Exceptions.fail(ErrorCodes.LOCK_PROMOTION_RESOURCES_FAILED_USER_TOTAL_COUNT);
                            }

                        } else if (LimitationCodeEnum.ACTIVITY_TOTAL_COUNT.equalsCode(limitedVO.getLimitationCode())) {

                            if (!this.incentiveLimitedHelper.lockRedisDataSupportSku(ilParamList, param, timeout, skuCodes, skuQuantity)) {
                                throw Exceptions.fail(ErrorCodes.LOCK_PROMOTION_RESOURCES_FAILED_ACTIVITY_TOTAL_COUNT);
                            }

                        } else if (LimitationCodeEnum.ACTIVITY_TOTAL_AMOUNT.equalsCode(limitedVO.getLimitationCode())) {

                            if (!this.incentiveLimitedHelper.lockRedisDataSupportSku(ilParamList, param, timeout, skuCodes, skuQuantity)) {
                                throw Exceptions.fail(ErrorCodes.LOCK_PROMOTION_RESOURCES_FAILED_ACTIVITY_TOTAL_AMOUNT);
                            }

                        } else if (LimitationCodeEnum.USER_SKU_COUNT.equalsCode(limitedVO.getLimitationCode())) {

                            if (!this.incentiveLimitedHelper.lockRedisDataSupportSku(ilParamList, param, timeout, skuCodes, skuQuantity)) {
                                throw Exceptions.fail(ErrorCodes.LOCK_PROMOTION_RESOURCES_FAILED_USER_SKU_TOTAL_TOTAL);
                            }

                        } else if (LimitationCodeEnum.SKU_COUNT.equalsCode(limitedVO.getLimitationCode())) {

                            if (!this.incentiveLimitedHelper.lockRedisDataSupportSku(ilParamList, param, timeout, skuCodes, skuQuantity)) {
                                throw Exceptions.fail(ErrorCodes.LOCK_PROMOTION_RESOURCES_FAILED_SKU_TOTAL_TOTAL);
                            }

                        } else if (LimitationCodeEnum.ACTIVITY_DAY_ORDER_COUNT.equalsCode(limitedVO.getLimitationCode())) {
                            timeout = 24 * 60 * 60 * 1000L;
                            if (!dayOrderActivityCodeList.contains(param.getActivityCode())) {
                                dayOrderActivityCodeList.add(param.getActivityCode());
                                param.setIncentiveValue(BigDecimal.ONE);
                                if (!this.incentiveLimitedHelper.lockRedisDataSupportSku(ilParamList, param, timeout, skuCodes, skuQuantity)) {
                                    throw Exceptions.fail(ErrorCodes.LOCK_PROMOTION_RESOURCES_FAILED_ACTIVITY_TOTAL_AMOUNT);
                                }
                            }

                        } else if (LimitationCodeEnum.USER_DAY_ORDER_COUNT.equalsCode(limitedVO.getLimitationCode())) {
                            timeout = 24 * 60 * 60 * 1000L;
                            if (!userDayOrderList.contains(param.getActivityCode())) {
                                userDayOrderList.add(param.getActivityCode());
                                param.setIncentiveValue(BigDecimal.ONE);
                                if (!this.incentiveLimitedHelper.lockRedisDataSupportSku(ilParamList, param, timeout, skuCodes, skuQuantity)) {
                                    throw Exceptions.fail(ErrorCodes.LOCK_PROMOTION_RESOURCES_FAILED_ACTIVITY_TOTAL_COUNT);
                                }
                            }

                        } else {
                            //不走该逻辑
                        }
                    }
                }



                try {
                    //优先购买权锁定
                    x.getShoppingCartItems().forEach(cardItem -> {

                        List<ShoppingCartItemActivityOutDTO> collect = cardItem.getShoppingCartItemActivitys().stream().filter(activity -> activity.getActivityType().equals(com.gtech.promotion.code.marketing.ActivityTypeEnum.BOOST_SHARDING.code())).collect(Collectors.toList());
                        if (CollectionUtils.isNotEmpty(collect)) {

                            WriteOffOfPreEmptiveRightsDto dto = new WriteOffOfPreEmptiveRightsDto();
                            dto.setDomainCode(orderCommitDTO.getDomainCode());
                            dto.setTenantCode(orderCommitDTO.getTenantCode());
                            dto.setMemberCode(orderCommitDTO.getUserCode());
                            dto.setActivityCode(collect.get(0).getActivityCode());
                            dto.setOldStatus(RightOfFirstRefusalStatusEnum.UN_USED.code());
                            dto.setNewStatus(RightOfFirstRefusalStatusEnum.LOCK.code());
                            int i = rightOfFirstRefusalService.updateStatus(dto);
                            if (i!= 1){
                               throw  Exceptions.fail(ErrorCodes.ERROR_LOCK_RIGHT_OF_FIRST_REFUSAL);
                            }
                        }

                    });
                }catch (Exception e){
                    log.error("接口提交订单:优先购买权锁定出错");
                    this.incentiveLimitedHelper.rollBackRedisData(ilParamList);//数据回滚
                    throw e;
                }




            }
        });

        try { //券码锁定
            if (StringUtils.isNotEmpty(orderCommitDTO.getCouponCodes())) {
                TCouponLockDTO couponLockDTO = new TCouponLockDTO();
                couponLockDTO.setUserCode(orderCommitDTO.getUserCode());
                couponLockDTO.setOrderNo(orderCommitDTO.getOrderId());
                couponLockDTO.setCouponCodes(orderCommitDTO.getCouponCodes());
                couponLockDTO.setTenantCode(orderCommitDTO.getTenantCode());
                couponDomain.lockCoupon(couponLockDTO);
            }
        } catch (Exception e) {
            log.error("接口提交订单:券码锁定出错");
            this.incentiveLimitedHelper.rollBackRedisData(ilParamList);//数据回滚
            throw e;
        }

        return ilParamList;
    }

    public void setIncentive(BigDecimal times, BigDecimal promoDeductedAmount, IncentiveLimitedParam param) {
        if (param.isTimesValue()) {
            param.setIncentiveValue(times);
        } else {
            param.setIncentiveValue(promoDeductedAmount);
        }
    }

    /**   
     * 取消订单 
     * 
     * @return  返回 1 成功，其他失败
     */
    @Transactional
    public Integer cancelOrder(String tenantCode,String orderId){

        TPromoOrderVO order = orderService.queryOrderBySalesOrderNo(tenantCode, orderId);

        WriteOffOfPreEmptiveRightsDto rightBuy = new WriteOffOfPreEmptiveRightsDto();
        rightBuy.setOrderId(orderId);
        rightBuy.setTenantCode(tenantCode);
        rightBuy.setNewStatus(RightOfFirstRefusalStatusEnum.UN_USED.code());
        rightOfFirstRefusalService.updateStatus(rightBuy);

        if (Objects.isNull(order)){
            return 1;
        }
        String id = order.getId();
        int row = updateOrderStatus(id);
        if (row > 0){
            List<TPromoActivityIncentiveEntity> incentiveList = activityIncentiveService.getListByOrderId(tenantCode, id);
            List<TPromoOrderDetailVO> tPromoOrderDetailVOS = orderDetailService.queryOrderDetail(order.getId());

            // 每日订单数的限制，同一个活动在购物车不同商品上出现， 按1次计算
            List<String> dayOrderActivityCodeList = new ArrayList<>();
            for (TPromoActivityIncentiveEntity incentive : incentiveList){
                List<SkuQuantity> skuCodes = getSkuCodesByActivity(tPromoOrderDetailVOS, incentive);
                //资源回滚  奖励次数
                List<TPromoIncentiveLimitedVO> limitedList = limitedService.getLimitedListByActivityCode(incentive.getActivityCode());
                for (TPromoIncentiveLimitedVO limited : limitedList){
                    rollbackIncentiveLimitedTimes(order, dayOrderActivityCodeList, incentive, skuCodes, limited);
                }
            }
            //订单取消 券码资源回滚
            TCouponUnLockDTO unLock = new TCouponUnLockDTO();
            unLock.setOrderNo(order.getOrderId());
            unLock.setTenantCode(order.getTenantCode());
            couponDomain.unLockCoupon(unLock);



        }
        return row;
    }

    /**
     * 退单，券资源释放
     * @param tenantCode
     * @param orderId 销售订单编号
     * @return
     */
    @Transactional
    public Integer salesReturnOrder(String tenantCode,String orderId) {
        TPromoOrderVO order = orderService.queryOrderBySalesOrderNo(tenantCode, orderId);
        if (Objects.isNull(order)) {
            return 1;
        }
        String orderStatus = OrderStatusEnum.SALES_RETURN.code();
        Integer row = orderService.updateOrderLogicDelete(order.getTenantCode(), orderId, orderStatus);
        String id = order.getId();//订单id
        if (row > 0) {
            List<TPromoActivityIncentiveEntity> incentiveList = activityIncentiveService.getListByOrderId(tenantCode, id);
            List<TPromoOrderDetailVO> tPromoOrderDetailVOS = orderDetailService.queryOrderDetail(order.getId());
            // 每日订单数的限制，同一个活动在购物车不同商品上出现， 按1次计算
            List<String> dayOrderActivityCodeList = new ArrayList<>();
			if (!CollectionUtils.isEmpty(incentiveList)) {
				for (TPromoActivityIncentiveEntity incentive : incentiveList) {
					ActivityModel activity = activityService.findActivityByActivityCode(tenantCode, incentive.getActivityCode());
					// 获取券活动对应得sku数量
					List<SkuQuantity> skuCodes = getSkuCodesByCouponActivity(tPromoOrderDetailVOS, incentive);
					// 资源回滚 奖励次数
					List<TPromoIncentiveLimitedVO> limitedList = limitedService.getLimitedListByActivityCode(incentive.getActivityCode());
					for (TPromoIncentiveLimitedVO limited : limitedList) {
						// 只对券活动数据进行回滚操作
						if (ActivityTypeEnum.COUPON.code().equals(activity.getActivityType())) {
							rollbackIncentiveLimitedTimes(order, dayOrderActivityCodeList, incentive, skuCodes, limited);
						}
					}
				}
            }
            //退单 券码资源回滚
            TCouponUnLockDTO unLock = new TCouponUnLockDTO();
            unLock.setOrderNo(order.getOrderId());
            unLock.setTenantCode(order.getTenantCode());
            unLock.setOrderStatus("04");//退单必填
            couponDomain.unLockCoupon(unLock);
        }
            return row;
    }
    //取消订单或退单 回滚奖励次数
    private void rollbackIncentiveLimitedTimes(TPromoOrderVO order, List<String> dayOrderActivityCodeList, TPromoActivityIncentiveEntity incentive, List<SkuQuantity> skuCodes, TPromoIncentiveLimitedVO limited) {
        if (LimitationCodeEnum.isTimes(limited.getLimitationCode())) {
            if (LimitationCodeEnum.ACTIVITY_DAY_ORDER_COUNT.equalsCode(limited.getLimitationCode())) {
                if (dayOrderActivityCodeList.contains(incentive.getActivityCode())) {
                    return;
                } else {
                    dayOrderActivityCodeList.add(incentive.getActivityCode());
                }
            }
            this.incentiveLimitedHelper.rollBackRedisData(limited.getLimitationCode(), order.getTenantCode(), incentive.getActivityCode(),
                    order.getUserCode(), ConvertUtils.toBigDecimal(incentive.getIncentiveTimes(), BigDecimal.ONE), skuCodes);
        } else {
            this.incentiveLimitedHelper.rollBackRedisData(limited.getLimitationCode(), order.getTenantCode(), incentive.getActivityCode(),
                    order.getUserCode(), incentive.getIncentiveAmount(), skuCodes);
        }
    }
    //退单获取券活动相关
    private List<SkuQuantity> getSkuCodesByCouponActivity(List<TPromoOrderDetailVO> tPromoOrderDetailVOS, TPromoActivityIncentiveEntity incentive) {
        List<SkuQuantity> skuCodes = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(tPromoOrderDetailVOS)) {
            for (TPromoOrderDetailVO tPromoOrderDetailVO : tPromoOrderDetailVOS) {
                setSkuQuantity(incentive, skuCodes, tPromoOrderDetailVO);
            }
        }
        return skuCodes;
    }

    private void setSkuQuantity(TPromoActivityIncentiveEntity incentive, List<SkuQuantity> skuCodes, TPromoOrderDetailVO tPromoOrderDetailVO) {
        if (CollectionUtils.isNotEmpty(tPromoOrderDetailVO.getActivities())) {
            for (TPromoOrderDetailActivityVO activity : tPromoOrderDetailVO.getActivities()) {
                if (ActivityTypeEnum.COUPON.code().equals(activity.getActivityType()) && activity.getActivityCode().equals(incentive.getActivityCode())) {
                    SkuQuantity skuQuantity = new SkuQuantity();
                    skuQuantity.setSkuCode(tPromoOrderDetailVO.getSkuCode());
                    skuQuantity.setQuantity(tPromoOrderDetailVO.getQuantity());
                    skuCodes.add(skuQuantity);
                }
            }
        }
    }

    //针对所有活动
    private List<SkuQuantity> getSkuCodesByActivity(List<TPromoOrderDetailVO> tPromoOrderDetailVOS, TPromoActivityIncentiveEntity incentive) {
        List<SkuQuantity> skuCodes = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(tPromoOrderDetailVOS)) {
            for (TPromoOrderDetailVO tPromoOrderDetailVO : tPromoOrderDetailVOS) {
                if (CollectionUtils.isNotEmpty(tPromoOrderDetailVO.getActivities())) {
                    for (TPromoOrderDetailActivityVO activity : tPromoOrderDetailVO.getActivities()) {
                        if (activity.getActivityCode().equals(incentive.getActivityCode())) {
                            SkuQuantity skuQuantity = new SkuQuantity();
                            skuQuantity.setSkuCode(tPromoOrderDetailVO.getSkuCode());
                            skuQuantity.setQuantity(tPromoOrderDetailVO.getQuantity());
                            skuCodes.add(skuQuantity);
                        }
                    }
                }
            }
        }
        return skuCodes;
    }

    /**   
     * 更新订单相关表的操作
     * 
     * @param id 订单id
     */
    @Transactional
    public Integer updateOrderStatus(String id){
        //根据主键 修改订单状态和逻辑删除状态
        TPromoOrderVO updateOrder = new TPromoOrderVO();
        updateOrder.setId(id);
        return orderService.updateOrderStatusById(updateOrder);
    }

    /**   
     * 订单支付
     */
    @Transactional
    public Integer getPayOrderReduceResource(String tenantCode,String orderNo){
        TPromoOrderVO orderVO = orderService.queryOrderBySalesOrderNo(tenantCode, orderNo);

        WriteOffOfPreEmptiveRightsDto rightBuy = new WriteOffOfPreEmptiveRightsDto();
        rightBuy.setOrderId(orderNo);
        rightBuy.setTenantCode(tenantCode);
        rightBuy.setOldStatus(RightOfFirstRefusalStatusEnum.LOCK.code());
        rightBuy.setNewStatus(RightOfFirstRefusalStatusEnum.USED.code());
        rightOfFirstRefusalService.updateStatus(rightBuy);

        if (Objects.isNull(orderVO) || OrderStatusEnum.PAID.code().equals(orderVO.getOrderStatus())){
            return 1;
        }
        Check.check(OrderStatusEnum.CANCELED.equalsCode(orderVO.getOrderStatus()), TPromoOrderChecker.ERROR_CANCEL_ORDER_NO);
        //修改订单状态  改成已支付
        String orderStatus = OrderStatusEnum.PAID.code();
        Integer row = orderService.updateOrderLogicDelete(orderVO.getTenantCode(), orderVO.getOrderId(), orderStatus);
        //券
        TCouponUseInDTO tCouponUseInDTO = new TCouponUseInDTO();
        tCouponUseInDTO.setTenantCode(orderVO.getTenantCode());
        tCouponUseInDTO.setUsedRefId(orderVO.getOrderId());
        innerDomain.useCoupon(tCouponUseInDTO);



        return row;
    }


    /**
     * 赢在一起业务---创单并核销券
     * @return 成功或失败信息
     */
    @Transactional
    public void commitOrderAndVerify(CreateOrderAndCouponParam param) {

        //保存数据总订单数据
        TPromoOrderVO orderVO = new TPromoOrderVO();
        orderVO.setTenantCode(param.getTenantCode());
        orderVO.setPromoOrderSeq(1);
        orderVO.setPromoOrderNo(PromotionOrderNoUtil.createCode(param.getOrderNo(), param.getMemberCode(), param.getTenantCode()));
        orderVO.setPromoOrderTime(DateUtil.getCurrentDateAsString(DateUtil.FORMAT_YYYYMMDDHHMISS_14));
        orderVO.setUserCode(param.getMemberCode());
        orderVO.setOrderId(param.getOrderNo());
        orderVO.setOrderStatus(OrderStatusEnum.UNPAID.code());
        orderVO.setOrgCode(StoreParamTypeEnum.STORE_ALL_STORE.code());

        //保存数据,重复时会报错
        String orderId = orderService.insertTPromoOrder(orderVO);
        //校验并锁定券资源
        TCouponLockDTO couponLockDTO = new TCouponLockDTO();
        couponLockDTO.setUserCode(param.getMemberCode());
        couponLockDTO.setOrderNo(param.getOrderNo());
        couponLockDTO.setCouponCodes(param.getCouponCodes());
        couponLockDTO.setTenantCode(param.getTenantCode());
        couponDomain.lockCoupon(couponLockDTO);

        //保存商品信息
        //活动对应商品信息
        List<ActivityRelationProduct> products = param.getActivityRelationProducts();
        Check.check(CollectionUtils.isEmpty(products),TPromoOrderChecker.TENANT_CODE_NO_ACTIVITY);
        //记录当前促销关联的订单、会员、奖励信息
        products.forEach(x -> {
            TPromoActivityIncentiveVO incentiveVO = new TPromoActivityIncentiveVO();
            incentiveVO.setTenantCode(param.getTenantCode());
            incentiveVO.setActivityCode(x.getActivityCode());
            incentiveVO.setPromoOrderId(orderId);//订单id
            ActivityModel activity = activityService.findEffectiveActivity(param.getTenantCode(), x.getActivityCode());
            Check.check(null == activity, TPromoOrderChecker.TENANT_CODE_NO_ACTIVITY);
            setIncentiveType(incentiveVO, activity);
            incentiveVO.setIncentiveAmount(x.getPromoRewardAmount());
            incentiveVO.setIncentivePostage(x.getPromoRewardPostage());
            incentiveVO.setIncentiveTimes(1);
            incentiveVO.setTenantCode(param.getTenantCode());
            incentiveVO.setUserCode(param.getMemberCode());
            activityIncentiveService.insertActivityIncentive(incentiveVO);
        });

        List<TPromoOrderDetailVO> promoOrderDetailVOS = new ArrayList<>();

        for (ActivityRelationProduct product : products) {
            setOrderDetail(param, orderId, promoOrderDetailVOS, product);
        }
        // 最后保存mongo数据，出现异常可以不手工回滚
        orderDetailService.insertOrderDetailList(promoOrderDetailVOS);
    }

    public void setOrderDetail(CreateOrderAndCouponParam param, String orderId, List<TPromoOrderDetailVO> promoOrderDetailVOS, ActivityRelationProduct product) {
        List<CreateOrderAndCouponShoppingCartItems> shoppingCartItems = product.getShoppingCartItems();
        shoppingCartItems.forEach(x -> {
            // mongodb赋值
            TPromoOrderDetailVO promoOrderDetailVO = new TPromoOrderDetailVO();
            promoOrderDetailVO.setTenantCode(param.getTenantCode());
            promoOrderDetailVO.setPromoOrderId(orderId);
            promoOrderDetailVO.setSkuCode(x.getSkuCode());
            promoOrderDetailVO.setProductCode(x.getProductCode());
            promoOrderDetailVOS.add(promoOrderDetailVO);

        });
    }

    public void setIncentiveType(TPromoActivityIncentiveVO incentiveVO, ActivityModel activity) {
        if (TemplateEnum.T0702.equalsCode(activity.getTemplateCode())) {
            incentiveVO.setIncentiveType("04");
        } else {
            incentiveVO.setIncentiveType(activity.getTemplateCode().substring(14, 16));
        }
    }
}
