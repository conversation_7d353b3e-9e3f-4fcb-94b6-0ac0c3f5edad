package com.gtech.promotion.component.coupon;

import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.gtech.basic.idm.web.vo.result.QueryOpUserAccountListResult;
import com.gtech.basic.idm.web.vo.result.QueryUserResult;
import com.gtech.commons.code.LogicDeleteEnum;
import com.gtech.commons.exception.Exceptions;
import com.gtech.commons.redis.GTechCodeGenerator;
import com.gtech.commons.utils.BeanCopyUtils;
import com.gtech.commons.utils.DateUtil;
import com.gtech.commons.utils.StringUtil;
import com.gtech.promotion.checker.coupon.CouponActivityChecker;
import com.gtech.promotion.code.activity.*;
import com.gtech.promotion.code.coupon.*;
import com.gtech.promotion.component.activity.ActivityCacheDomain;
import com.gtech.promotion.component.activity.ActivityComponentDomain;
import com.gtech.promotion.component.activity.ActivityExpireComponentDomain;
import com.gtech.promotion.component.feign.IdmFeignClientComponent;
import com.gtech.promotion.dao.model.activity.*;
import com.gtech.promotion.dao.model.coupon.CountCouponCodeModel;
import com.gtech.promotion.dao.model.coupon.CouponReleaseModel;
import com.gtech.promotion.dao.model.coupon.TPromoCouponInnerCodeVO;
import com.gtech.promotion.domain.activity.ActivityDomain;
import com.gtech.promotion.domain.coupon.CouponDomain;
import com.gtech.promotion.domain.coupon.CouponReleaseDomain;
import com.gtech.promotion.dto.cache.ActivityCacheDTO;
import com.gtech.promotion.dto.in.coupon.*;
import com.gtech.promotion.dto.out.activity.TPromoActivityOutDTO;
import com.gtech.promotion.dto.out.coupon.*;
import com.gtech.promotion.exception.Check;
import com.gtech.promotion.exception.ErrorCodes;
import com.gtech.promotion.exception.PromotionException;
import com.gtech.promotion.helper.ActivityRedisHelpler;
import com.gtech.promotion.helper.QualificationFilter;
import com.gtech.promotion.service.activity.*;
import com.gtech.promotion.service.coupon.PromoCouponActivityService;
import com.gtech.promotion.service.coupon.PromoCouponCodeUserService;
import com.gtech.promotion.service.coupon.PromoCouponInnerCodeService;
import com.gtech.promotion.service.coupon.PromoCouponReleaseService;
import com.gtech.promotion.service.mongo.activity.TPromoProductService;
import com.gtech.promotion.utils.FaceClassUtil;
import com.gtech.promotion.utils.MarketingConstants;
import com.gtech.promotion.utils.TemplateCodeSubstringUtil;
import com.gtech.promotion.vo.bean.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Component
public class CouponActivityComponent {

    @Autowired
    private ActivityService activityService;

    @Autowired
    private PromoCouponActivityService couponActivityService;

    @Autowired
    private PromoCouponReleaseService couponReleaseService;

    @Autowired
    private PromoCouponInnerCodeService couponInnerCodeService;

    @Autowired
    private PromoCouponCodeUserService couponCodeUserService;

    @Autowired
    private ActivityComponentDomain activityComponentDomain;

    @Autowired
    private ActivityCacheDomain activityCacheDomain;

    @Autowired
    private TPromoProductService tPromoProductService;

    @Autowired
    private TemplateService templateService;

    @Autowired
    private GiveawayService giveawayService;

    @Autowired
    private ActivityFuncParamService promoActivityFuncParamService;

    @Autowired
    private ActivityFuncRankService promoActivityFuncRankService;

    @Autowired
    private ActivityStoreService tPromoActivityStoreService;
    @Autowired
    private ActivityPeriodService activityPeriodService;

    @Autowired
    private ActivityLanguageService languageService;

    @Autowired
    private QualificationService qualificationService;

    @Autowired
    private ActivityRedisHelpler redisService;

    @Autowired
    private ActivityExpireComponentDomain activityExpireComponentDomain;

    @Autowired
    private OperationLogService operationLogService;

    @Autowired
    private IdmFeignClientComponent idmFeignClientComponent;

    @Autowired
    private PromotionGroupService promotionGroupService;

    @Autowired
    private GTechCodeGenerator gTechCodeGenerator;
    public static final String TEMPLATE_EXPR = "[D:yyMMddHHmmss][SM:%03d]";
    /**
     * @return activityCode
     */
    @Transactional
    public String createCouponActivity(CreateCouponActivityDTO createCouponActivityDTO) {

        // 创建活动
        ActivityDomain activityDomain = BeanCopyUtils.jsonCopyBean(createCouponActivityDTO, ActivityDomain.class);
        activityDomain.setActivityType(ActivityTypeEnum.COUPON.code());
        Check.check(!CouponTypeEnum.exist(createCouponActivityDTO.getCouponType()), CouponActivityChecker.ERROR_COUPON_TYPE);
        String activityCode = activityComponentDomain.createPromoActivity(activityDomain);

        operationLogService.insertLog(OperationLogModel.builder()
                .operationType(OperationTypeEnum.CREATION.code())
                .createUser(activityDomain.getCreateUser()).tenantCode(activityDomain.getTenantCode())
                .createLastName(createCouponActivityDTO.getOperateLastName()).createFirstName(createCouponActivityDTO.getOperateFirstName())
                .activityCode(activityCode).build(), JSONObject.toJSONString(createCouponActivityDTO));
        // 获取新创建活动的活动id
        try {
            Check.check(StringUtil.isBlank(activityCode), CouponActivityChecker.ACTIVITY_CREATE_FAILED);
            createCouponActivityDTO.setActivityCode(activityCode);
            ActivityModel couponActivityVO = BeanCopyUtils.jsonCopyBean(createCouponActivityDTO, ActivityModel.class);
            couponActivityVO.setTenantCode(activityDomain.getTenantCode());
            couponActivityVO.setActivityCode(activityCode);

            String id = couponActivityService.createCouponActivity(couponActivityVO);
            Check.check(StringUtil.isBlank(id), CouponActivityChecker.COUPON_ACTIVITY_CREATE_FAILED);
            activityCacheDomain.putCache(activityDomain.getTenantCode(), activityCode);
            // 如果是促销优惠码，则需要生成一条投放数据及券码数据
            if (CouponTemplateEnum.COUPON_CODE.equalsCode(createCouponActivityDTO.getCouponType())) {
                // 检查促销优惠码是否存在
                String promotionCode = createCouponActivityDTO.getPromotionCode();
                Check.check(StringUtil.isEmpty(promotionCode), CouponActivityChecker.NULL_PROMOTION_CODE);
                promotionCode = promotionCode.trim();
                createCouponActivityDTO.setPromotionCode(promotionCode);

                // 保存投放信息
                CouponReleaseModel releaseVo = convertReleaseVO(createCouponActivityDTO);
                releaseVo.setTenantCode(activityDomain.getTenantCode());
                releaseVo.setTimeSameActivity("1");
                releaseVo.setReceiveTimeSameActivity("1");
                // 投放编码
                couponReleaseService.createCouponRelease(releaseVo);
                // 保存券码信息
                List<TPromoCouponInnerCodeVO> innerCodeVOs = new ArrayList<>();
                TPromoCouponInnerCodeVO innerCodeVO = convertCouponVO(createCouponActivityDTO);
                String templateCode = createCouponActivityDTO.getTemplateCode();
                String substring = templateCode.substring(templateCode.length() - 2);
                if (FaceUnitEnum.DISCOUNT.code().equals(substring)) {
                    innerCodeVO.setFaceValue(innerCodeVO.getFaceValue().divide(new BigDecimal(10)));
                }
                innerCodeVO.setReleaseCode(releaseVo.getReleaseCode());
                innerCodeVOs.add(innerCodeVO);
                //券码插入
                insrtCouponCode(innerCodeVOs);
            }

        } catch (Exception e) {
            log.error("创建券活动出异常mongodb回滚", e);
            tPromoProductService.deleteProducts(activityCode);
            throw e;
        }
        return activityCode;
    }

    private void insrtCouponCode(List<TPromoCouponInnerCodeVO> innerCodeVOs) {

        try {
            couponInnerCodeService.insertPromoCouponInnerCode(innerCodeVOs);
        } catch (DuplicateKeyException e1) {
            throw new PromotionException(CouponActivityChecker.PROMOTION_CODE_EXIST);
        }
    }

    /**
     * 查找Effective状态的活动(该活动可能还没有开始，不包含已经结束的活动)
     */
    public ActivityModel findEffectiveActivity(String tenantCode, String activityCode, String language) {

        ActivityModel activityModel = this.activityService.findEffectiveActivity(tenantCode, activityCode);
        if (null == activityModel) {
            return null;
        }
        ActivityModel couponActivityModel = this.couponActivityService.findEffectiveActivity(tenantCode, activityCode);
        if (null == couponActivityModel) {
            return null;
        }
        BeanCopyUtils.copyProps(activityModel, couponActivityModel);
        this.activityExpireComponentDomain.expireActivity(couponActivityModel);

        this.activityComponentDomain.loadActivityLanguage(couponActivityModel, language);

        return couponActivityModel;
    }

    /**
     * 查找有效活动（处于活动有效起止时间内的有效活动）
     */
    public ActivityModel findValidActivity(String tenantCode, String activityCode, String language, Date date) {
        ActivityModel activityModel = this.activityService.findEffectiveActivity(tenantCode, activityCode);
        if (null == activityModel || !activityModel.isValid(date)) {
            return null;
        }

        return findAndExpireActivity(activityModel,language);
    }

    public ActivityModel findSendCouponValidActivity(String tenantCode, String activityCode, String language, Date date){
        ActivityModel activityModel = this.activityService.findEffectiveActivity(tenantCode, activityCode);
        if (null == activityModel) {
            return null;
        }

        if(!ActivityStatusEnum.EFFECTIVE.equalsCode(activityModel.getActivityStatus()) || DateUtil.parseDate(activityModel.getActivityEnd(), DateUtil.FORMAT_YYYYMMDDHHMISS_14).getTime() <= date.getTime()){
            return null;
        }
        return findAndExpireActivity(activityModel,language);
    }

    public ActivityModel findAndExpireActivity(ActivityModel activityModel,String language){
        ActivityModel couponActivityModel = this.couponActivityService.findEffectiveActivity(activityModel.getTenantCode(), activityModel.getActivityCode());
        if (null == couponActivityModel) {
            return null;
        }

        BeanCopyUtils.copyProps(activityModel, couponActivityModel);
        this.activityExpireComponentDomain.expireActivity(couponActivityModel);

        this.activityComponentDomain.loadActivityLanguage(couponActivityModel, language);

        return couponActivityModel;
    }

    /**
     * @return activityCode
     */
    @Transactional
    public String updateCouponActivity(UpdateCouponActivityInDTO updateCouponActivityDTO) {

        // 获取mongodb数据，用于异常回滚
        List<ProductScope> preProducts = tPromoProductService.getProducts(updateCouponActivityDTO.getActivityCode());
        updateCouponActivityDTO.setActivityType(ActivityTypeEnum.COUPON.code());
        Check.check(!CouponTypeEnum.exist(updateCouponActivityDTO.getCouponType()), CouponActivityChecker.ERROR_COUPON_TYPE);
        int i = activityComponentDomain.updatePromoActivity(updateCouponActivityDTO);

        String tenantCode = updateCouponActivityDTO.getTenantCode();
        String activityCode = updateCouponActivityDTO.getActivityCode();
        operationLogService.insertLog(OperationLogModel.builder()
                .operationType(OperationTypeEnum.EDITION.code())
                .createUser(updateCouponActivityDTO.getUpdateUser()).tenantCode(tenantCode)
                .createLastName(updateCouponActivityDTO.getOperateLastName()).createFirstName(updateCouponActivityDTO.getOperateFirstName())
                .activityCode(activityCode).build(), JSONObject.toJSONString(updateCouponActivityDTO));
        try {
            if (i == 1) {
                // 查询券活动信息
                ActivityModel couponActivityModel = couponActivityService.findCouponActivity(tenantCode, activityCode);
                if (null == couponActivityModel) {
                    throw Exceptions.fail(ErrorCodes.VALIDATE_COUPON_ACTIVITY_EXIST);
                }

                // 投放总量修改时, 只能大于等于0
                Check.check(Long.valueOf(updateCouponActivityDTO.getTotalQuantity()) < 0, CouponActivityChecker.TOTAL_QUANTITY_OUT);
                couponActivityModel = BeanCopyUtils.jsonCopyBean(updateCouponActivityDTO, ActivityModel.class);
                couponActivityModel.setLogicDelete(Integer.valueOf(LogicDeleteEnum.NORMAL.code()));
                couponActivityService.updateCouponActivityByActivityCode(couponActivityModel);

                String couponType = updateCouponActivityDTO.getCouponType();
                // 如果是促销优惠码，则需要修改促销优惠码
                if (CouponTemplateEnum.COUPON_CODE.code().equals(couponType)) {
                    //优惠码处理，抽方法降低复杂度
                    updateCouponActivityCouponCode(updateCouponActivityDTO);
                }
                // 不是sku上传的时候
            }
        } catch (Exception e) {
            log.error("更新券活动出异常mongodb回滚", e);
            if (!CollectionUtils.isEmpty(preProducts)) {
                tPromoProductService.insertProducts(preProducts, activityCode, updateCouponActivityDTO.getTenantCode());
            }
            throw e;
        }
        return activityCode;
    }

    private void updateCouponActivityCouponCode(UpdateCouponActivityInDTO couponActivityModel) {

        CouponReleaseDomain releaseDomain = new CouponReleaseDomain();

        releaseDomain.setTenantCode(couponActivityModel.getTenantCode());
        releaseDomain.setActivityCode(couponActivityModel.getActivityCode());

        PageInfo<CouponReleaseDomain> queryReleases = couponReleaseService.queryReleases(releaseDomain, null);
        List<CouponReleaseDomain> releaseVOs = queryReleases.getList();
        if (CollectionUtils.isNotEmpty(releaseVOs)) {
            // 促销优惠码只会存在一条投放记录
            releaseDomain = releaseVOs.get(0);
            releaseDomain.setReceiveStartTime(couponActivityModel.getActivityBegin());
            releaseDomain.setReceiveEndTime(couponActivityModel.getActivityEnd());
            releaseDomain.setValidStartTime(couponActivityModel.getActivityBegin());
            releaseDomain.setValidEndTime(couponActivityModel.getActivityEnd());
            releaseDomain.setReleaseQuantity(couponActivityModel.getTotalQuantity());
            releaseDomain.setTimeSameActivity("1");
            releaseDomain.setReceiveTimeSameActivity("1");
            couponReleaseService.updateCouponReleaseByReleaseCode(releaseDomain);

            List<TPromoCouponInnerCodeVO> innerCodeVOs = couponInnerCodeService.queryInnerCouponByReleaseCode(couponActivityModel.getTenantCode(),
                couponActivityModel.getActivityCode(), releaseDomain.getReleaseCode());
            if (CollectionUtils.isNotEmpty(innerCodeVOs)) {
                innerCodeVOsEmpty(couponActivityModel, innerCodeVOs);
            }
        }
    }

    private void innerCodeVOsEmpty(UpdateCouponActivityInDTO updateCouponActivityDTO, List<TPromoCouponInnerCodeVO> innerCodeVOs) {

        // 促销优惠码只会有一条投放记录
        TPromoCouponInnerCodeVO vo = innerCodeVOs.get(0);
        vo.setReceiveStartTime(updateCouponActivityDTO.getActivityBegin());
        vo.setReceiveEndTime(updateCouponActivityDTO.getActivityEnd());
        vo.setCouponCode(updateCouponActivityDTO.getPromotionCode());
        vo.setPromoPassword(updateCouponActivityDTO.getPromoPassword());
        vo.setFaceValue(null);
        vo.setFaceUnit(null);
        for (FunctionParam paramVO : updateCouponActivityDTO.getFuncParams()) {
            if (FuncTypeEnum.IncentiveEnum.REDUCE_AMOUNT.equalsCode(paramVO.getFunctionCode())) {
                vo.setFaceValue(new BigDecimal(paramVO.getParamValue()));
                vo.setFaceUnit(FaceUnitEnum.MONEY.code());
            }
            if (FuncTypeEnum.IncentiveEnum.DISCOUNT.equalsCode(paramVO.getFunctionCode())) {
                vo.setFaceValue(new BigDecimal(paramVO.getParamValue()).divide(new BigDecimal(10)));
                vo.setFaceUnit(FaceUnitEnum.DISCOUNT.code());
            }
        }
        updateInnerCoude(vo);
    }

    private void updateInnerCoude(TPromoCouponInnerCodeVO vo) {

        try {
            couponInnerCodeService.updateInnerCoudeById(vo);
        } catch (DuplicateKeyException e1) {
            throw new PromotionException(CouponActivityChecker.PROMOTION_CODE_EXIST);
        }
    }

    public CouponActivityOutDTO findCouponActivityOutDetail(String tenantCode, String activityCode, String orgCode) {

        // 查询活动信息
        TPromoActivityOutDTO activityOutDTO = this.activityComponentDomain.findActivityDetail(tenantCode, activityCode, null);
        //校验店铺编码
        checkOrgCode111(orgCode, activityCode);
        Check.check(activityOutDTO == null, CouponActivityChecker.ACTIVITY_NOT_EXIST);

        ActivityModel couponActivityModel = couponActivityService.findCouponActivity(tenantCode, activityCode);
        if (null == couponActivityModel) {
            throw Exceptions.fail(ErrorCodes.VALIDATE_COUPON_ACTIVITY_EXIST);
        }

        CouponActivityOutDTO outDTO = BeanCopyUtils.jsonCopyBean(activityOutDTO, CouponActivityOutDTO.class);
        
        String couponType = couponActivityModel.getCouponType();
        
        List<CouponReleaseModel> couponReleaseModelList = this.couponReleaseService.queryCouponRelease(activityCode);

        outDTO.setReserveInventory(couponActivityModel.getReserveInventory());
        outDTO.setCouponType(couponType);
        outDTO.setReleasedQuantity(CouponReleaseModel.countReleaseQuantity(couponReleaseModelList));
        outDTO.setTotalQuantity(couponActivityModel.getTotalQuantity());

        // 优惠券 领取总量
        if (CouponTypeEnum.PROMOTION_COUPON.equalsCode(couponType)) {

            List<CountCouponCodeModel> cccModels = this.couponCodeUserService.countCouponCode(tenantCode, activityCode, null);

            // 券码已领取的总量
            outDTO.setReceivedQuantity(CountCouponCodeModel.countByStatus(cccModels, CouponStatusEnum.GRANTED));
            // 券码已使用的总量
            outDTO.setUsedTotal(CountCouponCodeModel.countByStatus(cccModels, CouponStatusEnum.USED));

        } else if (CouponTypeEnum.ANONYMITY_COUPON.code().equals(outDTO.getCouponType())) {

            List<CountCouponCodeModel> cccModels = couponInnerCodeService.countCouponCode(tenantCode, activityCode, null);

            // 已领取

            if (CollectionUtils.isEmpty(cccModels)){
                outDTO.setReceivedQuantity(0);
            }else {
                //匿名券数量新的领取逻辑统计
                int total= 0;
                for (CountCouponCodeModel cccModel : cccModels) {
                    int i = couponInnerCodeService.countAnonymityCouponCode(tenantCode, activityCode, cccModel.getReleaseCode());
                    total += i;
                }
                //领取数量以前默认0 现在要按最新匿名券领取规则计算领取数量
                outDTO.setReceivedQuantity(total);
            }
            // 券码已使用的总量
            outDTO.setUsedTotal(CountCouponCodeModel.countByStatus(cccModels, CouponStatusEnum.USED));

        } else {
            // 促销优惠码
            List<TPromoCouponInnerCodeVO> couponInnerCodeVOs = couponInnerCodeService.queryInnerCouponByReleaseCode(tenantCode, activityCode, couponReleaseModelList.get(0).getReleaseCode());
            if (!CollectionUtils.isEmpty(couponInnerCodeVOs)) {
                outDTO.setPromotionCode(couponInnerCodeVOs.get(0).getCouponCode());
                outDTO.setPromoPassword(couponInnerCodeVOs.get(0).getPromoPassword());
            }

            List<CountCouponCodeModel> cccModels = this.couponCodeUserService.countCouponCode(tenantCode, activityCode, null);
            if (null != cccModels) {
                //券码生成的总量
                outDTO.setReceivedQuantity(outDTO.getTotalQuantity());
                // 券码已使用的总量
                outDTO.setUsedTotal(CountCouponCodeModel.countByStatus(cccModels, CouponStatusEnum.USED));
                outDTO.setTotalQuantity(outDTO.getTotalQuantity());
            }
        }

        return outDTO;
    }

    public CouponInfoOutDTO findCouponActivityByActivityCode(CouponInfoInDTO couponInfo) {

        ActivityModel activityModel = this.findValidActivity(couponInfo.getTenantCode(), couponInfo.getActivityCode(), couponInfo.getLanguage(), new Date());
        returnException(couponInfo, activityModel);

        checkStore(couponInfo, activityModel);

        CouponInfoOutDTO info = new CouponInfoOutDTO();
        info.setActivityCode(activityModel.getActivityCode());
        info.setActivityDesc(activityModel.getActivityDesc());
        info.setActivityLabel(activityModel.getActivityLabel());
        info.setActivityName(activityModel.getActivityName());
        info.setCouponType(activityModel.getCouponType());
        info.setWarmBegin(activityModel.getWarmBegin());
        info.setWarmEnd(activityModel.getWarmEnd());
        info.setBackgroundImage(activityModel.getBackgroundImage());
        info.setCoolDown(activityModel.getCoolDown());
        info.setProductSelectionType(activityModel.getProductSelectionType());
        info.setActivityRemark(activityModel.getActivityRemark());
        info.setActivityPeriod(BeanCopyUtils.jsonCopyBean(activityPeriodService.findPeriod(activityModel.getTenantCode(), activityModel.getActivityCode()), ActivityPeriod.class));
        List<Giveaway> giveaways = Lists.newArrayList();
        info.setActivityUrl(activityModel.getActivityUrl());//填充主表
        //有对应的url就填充，没有就填充主表
        String orgCode = couponInfo.getOrgCode();
        List<TPromoActivityStoreVO> storeVOs = tPromoActivityStoreService.getStoresByActivityCode(activityModel.getActivityCode());
        if (!CollectionUtils.isEmpty(storeVOs)) {
            setActivityUrl(info, orgCode, storeVOs);
        }
        //填充奖励类型
        TemplateModel template = templateService.getTemplateByCode(activityModel.getTemplateCode());
        info.setRewardType(TemplateCodeSubstringUtil.getRewardType(template.getTemplateCode()));
        String templateCode = TemplateCodeSubstringUtil.subStringTemplateCodeBegin8End12(template.getTemplateCode());
        setGiveaways(activityModel, info, giveaways);
        info.setGiveaways(giveaways);
        //条件单位、值
        List<FunctionParamModel> paramList = new ArrayList<>();
        List<ActivityFunctionParamRankModel> rankList = promoActivityFuncRankService.getRankListByActivityCode(activityModel.getActivityCode());
        paramList = setRankAndFunctionParam(info, templateCode, paramList, rankList);
        //填充面值
        FaceClass faceClass = FaceClassUtil.faceUnitAndFaceValue(info.getRewardType(), paramList);
        info.setFaceValue(faceClass.getFaceValue());
        info.setFaceUnit(faceClass.getFaceUnit());
        // 参与资格
        List<QualificationModel> models = qualificationService.queryQualifications(activityModel.getTenantCode(), activityModel.getActivityCode());
        info.setQualifications(QualificationModel.convert(models));

        Integer userLimitMax = activityModel.getUserLimitMax();
        info.setUserLimitMax(userLimitMax);
        //领取状态：1：可领取；2：未开始 3：已结束；4：已领完；5：已被限领；6：当前会员不符合要求（会员等级或会员标签）；优先级 （7和2级别相同）7263541
        //2 未开始
        if (ActivityStatusEnum.PENDING.code().equals(activityModel.getActivityStatus()) || ActivityStatusEnum.IN_AUDIT.code().equals(activityModel.getActivityStatus())
                        || ActivityStatusEnum.REJECTED.code().equals(activityModel.getActivityStatus())) {
            info.setReceiveStatus(Integer.parseInt(ReceiveStatusEnum.NOT_START.code()));
            return info;
        }
        //7：活动已关闭
        if (ActivityStatusEnum.END.code().equals(activityModel.getActivityStatus()) || ActivityStatusEnum.CLOSURE.code().equals(activityModel.getActivityStatus())
                        || ActivityStatusEnum.SUSPEND.code().equals(activityModel.getActivityStatus())) {
            info.setReceiveStatus(Integer.parseInt(ReceiveStatusEnum.TERMINATION.code()));
            return info;
        }
        //是否有投放
        List<CouponReleaseModel> releaseModels = couponReleaseService.queryCouponRelease(activityModel.getActivityCode());
        if (CollectionUtils.isEmpty(releaseModels)) {
            info.setReceiveStatus(Integer.parseInt(ReceiveStatusEnum.NOT_START.code()));
            return info;

        } else {

            List<CouponReleaseTimeOutDTO> releaseTimeOutDTOS = new ArrayList<>();
            for (CouponReleaseModel releaseVO : releaseModels) {
                combineCouponTime(releaseTimeOutDTOS, releaseVO);
            }

            info.setCouponValidTime(releaseTimeOutDTOS);
        }
        //开始时间集合
        List<String> sortStart = Lists.newArrayList();
        //大结束时间集合
        List<String> sortEnd = Lists.newArrayList();
        for (CouponReleaseModel tPromoCouponReleaseVO : releaseModels) {
            String status = tPromoCouponReleaseVO.getReleaseStatus();
            if ("04".equals(status)) {
                continue;
            }
            sortStart.add(tPromoCouponReleaseVO.getReceiveStartTime());
            sortEnd.add(tPromoCouponReleaseVO.getReceiveEndTime());
        }
        if (CollectionUtils.isEmpty(sortStart) || CollectionUtils.isEmpty(sortEnd)) {
            info.setReceiveStatus(Integer.parseInt(ReceiveStatusEnum.NOT_START.code()));
            return info;
        }
        Collections.sort(sortStart);
        Collections.sort(sortEnd);
        //最小开始和最大结束时间
        info.setStartReceiveTime(sortStart.get(0));
        info.setEndReceiveTime(sortEnd.get(sortEnd.size() - 1));
        String now = DateUtil.format(new Date(), DateUtil.FORMAT_YYYYMMDDHHMISS_14);
        releaseModels.removeIf(x -> Long.parseLong(x.getReceiveEndTime()) <= Long.parseLong(now));
        if (getReceiveStatus(couponInfo, activityModel, info, userLimitMax, sortStart, sortEnd)) {
            return info;
        }

        return getReceiveStatus1(info, releaseModels, now);
    }

    public void setGiveaways(ActivityModel activityModel, CouponInfoOutDTO info, List<Giveaway> giveaways) {
        if ("06".equals(info.getRewardType())) {
            //赠品信息 填充
            List<GiveawayVO> giveawayVOS = giveawayService.getGiftListByActivityCode(activityModel.getTenantCode(), activityModel.getActivityCode());
            giveawayVOS.forEach(x -> {
                Giveaway dto = new Giveaway();
                dto.setGiveawayCode(x.getGiveawayCode());
                dto.setGiveawayName(x.getGiveawayName());
                dto.setGiveawayNum(x.getGiveawayNum());
                giveaways.add(dto);
            });
        }
    }

    public List<FunctionParamModel> setRankAndFunctionParam(CouponInfoOutDTO info, String templateCode, List<FunctionParamModel> paramList, List<ActivityFunctionParamRankModel> rankList) {
        if (!CollectionUtils.isEmpty(rankList)) {
            ActivityFunctionParamRankModel rankVO = rankList.get(0);
            //函数参数层级
            paramList = promoActivityFuncParamService.getRuleFuncParamListByRankId(rankVO.getId());
            //填充条件
            FunctionParamModel param = promoActivityFuncParamService.getRuleFuncParamListByRankIdAndFunctionCode(rankVO.getId(), templateCode);
            info.setConditionUnit(param.getParamUnit());
            if (StringUtil.isNotEmpty(param.getParamValue())){
                info.setConditionValue(new BigDecimal(param.getParamValue()));
            }
        }
        return paramList;
    }

    public void checkStore(CouponInfoInDTO couponInfo, ActivityModel activityModel) {
        if (!StoreTypeEnum.ALL.code().equals(activityModel.getStoreType())) {
            //校验店铺下是否有该活动
            checkOrgCode111(couponInfo.getOrgCode(), activityModel.getActivityCode());
        }
    }

    public void returnException(CouponInfoInDTO couponInfo, ActivityModel activityModel) {
        if (null == activityModel) {
            throw Exceptions.fail(ErrorCodes.VALIDATE_COUPON_ACTIVITY_EXIST, couponInfo.getActivityCode());
        }
    }

    private CouponInfoOutDTO getReceiveStatus1(CouponInfoOutDTO info, List<CouponReleaseModel> releaseVOs, String now) {

        if (info.getReceiveStatus() == 0) {
            boolean rightTime = true;
            //投放集合
            for (CouponReleaseModel releaseModel : releaseVOs) {
                if (Long.parseLong(releaseModel.getReceiveEndTime()) > Long.parseLong(now)
                                || Long.parseLong(releaseModel.getReceiveStartTime()) < Long.parseLong(now)) {
                    rightTime = false;
                    int count = couponCodeUserService.countByReleaseCode(releaseModel.getTenantCode(), releaseModel.getActivityCode(), releaseModel.getReleaseCode());
                    //查询状态为未被领取且领取时间未结束
                    int effect = releaseModel.getReleaseQuantity();
                    int result = effect - count;
                    if (result > 0) {
                        info.setReceiveStatus(Integer.parseInt(ReceiveStatusEnum.RECEIVE.code()));
                        break;
                    }
                }
            }
            //3：已结束 
            if (rightTime) {
                info.setReceiveStatus(Integer.parseInt(ReceiveStatusEnum.END.code()));
                return info;
            }
            //4：已领完 判断有效库存
            if (info.getReceiveStatus() == 0) {
                info.setReceiveStatus(Integer.parseInt(ReceiveStatusEnum.BROUGHT.code()));
                return info;
            }
        }
        return info;
    }

    private boolean getReceiveStatus(CouponInfoInDTO couponInfoInDTO, ActivityModel activityModel, CouponInfoOutDTO info, Integer userLimitMax, List<String> sortStart, List<String> sortEnd) {

        String now = DateUtil.format(new Date(), DateUtil.FORMAT_YYYYMMDDHHMISS_14);
        if (info.getReceiveStatus() == 0 && Long.parseLong(sortStart.get(0)) > Long.parseLong(now)) {
            //2：未开始  当前时间早于最小领用开始时间
            info.setReceiveStatus(Integer.parseInt(ReceiveStatusEnum.NOT_START.code()));
            return true;
        }
        QualificationFilter filter = new QualificationFilter(QualificationModel.convertToModel(info.getQualifications()));
        if (!filter.filter(QualificationModel.convertToModel(info.getQualifications()))) {
            info.setReceiveStatus(Integer.parseInt(ReceiveStatusEnum.NOT_CONDITION.code()));
            return true;
        }
        if (info.getReceiveStatus() == 0 && Long.parseLong(sortEnd.get(sortEnd.size() - 1)) < Long.parseLong(now)) {
            //3：已结束 当前时间大于最大领用结束时间
            info.setReceiveStatus(Integer.parseInt(ReceiveStatusEnum.END.code()));
            return true;
        }
        if (info.getReceiveStatus() == 0 && StringUtil.isNotBlank(couponInfoInDTO.getUserCode()) && userLimitMax != 0) {
            //5：已被限领
            int count = couponCodeUserService.getUserCouponCountByActivityCode(activityModel.getTenantCode(), activityModel.getActivityCode(), couponInfoInDTO.getUserCode());
            //判断是否限领
            if (count >= userLimitMax) {
                info.setReceiveStatus(Integer.parseInt(ReceiveStatusEnum.LIMIT.code()));
                return true;
            }
        }
        return false;
    }

    private void setActivityUrl(CouponInfoOutDTO info, String orgCode, List<TPromoActivityStoreVO> storeVOs) {

        for (TPromoActivityStoreVO activityStoreVO : storeVOs) {
            if (activityStoreVO.getOrgCode().equals(orgCode)) {
                info.setActivityUrl(activityStoreVO.getUrl());
                break;
            }
        }
    }

    private void combineCouponTime(List<CouponReleaseTimeOutDTO> releaseTimeOutDTOS, CouponReleaseModel releaseVO) {

        if (releaseVO.getReleaseStatus().equals(CouponReleaseEnum.CREATE_COUPON.code())) {
            CouponReleaseTimeOutDTO time = new CouponReleaseTimeOutDTO();
            if (StringUtils.isNotBlank(releaseVO.getValidStartTime()) && StringUtils.isNotBlank(releaseVO.getValidEndTime())) {
                time.setValidStartTime(releaseVO.getValidStartTime());
                time.setValidEndTime(releaseVO.getValidEndTime());
            } else {
                time.setValidDays(releaseVO.getValidDays());
            }
            releaseTimeOutDTOS.add(time);
        }
    }

    public PageInfo<CouponActivityListOutDTO> queryCouponActivityList(CouponActivityListInDTO inDTO) {

        Map<String, String> tagMap = templateService.findTemplateTagAll();
        String templeCodes = templetCodeDeal(inDTO.getTagCode(), tagMap);
        inDTO.setTemplateCodes(templeCodes);

        // 各时间段校验
        PageInfo<CouponActivityListOutDTO> promoCouponsDTOs = couponActivityService.queryCouponActivityList(inDTO);
        if (null == promoCouponsDTOs || CollectionUtils.isEmpty(promoCouponsDTOs.getList())) {
            return promoCouponsDTOs;
        }

        List<CouponActivityListOutDTO> list = promoCouponsDTOs.getList();
        List<ActivityModel> activityModels = BeanCopyUtils.jsonCopyList(list, ActivityModel.class);
        List<QueryOpUserAccountListResult> idmOpUserAccount = idmFeignClientComponent.getIdmOpUserAccount(activityModels);
        List<QueryUserResult> userAccountList = idmFeignClientComponent.queryIdmUserList(inDTO.getDomainCode(),inDTO.getTenantCode(),activityModels);

        for (CouponActivityListOutDTO dto : list) {
            if (CollectionUtils.isNotEmpty(idmOpUserAccount)){
                QueryOpUserAccountListResult queryOpUserAccountListResult = idmOpUserAccount.stream().filter(x -> x.getUserCode().equals(dto.getCreateUser())).findFirst().orElse(new QueryOpUserAccountListResult());
                dto.setCreateUserFirstName(queryOpUserAccountListResult.getFirstName());
                dto.setCreateUserLastName(queryOpUserAccountListResult.getLastName());
            }
            if (CollectionUtils.isNotEmpty(userAccountList)){
            	QueryUserResult queryUserResult = userAccountList.stream().filter(x -> x.getUserCode().equals(dto.getCreateUser())).findFirst().orElse(new QueryUserResult());
            	dto.setCreateUserFirstName(queryUserResult.getFirstName());
            	dto.setCreateUserLastName(queryUserResult.getLastName());
            }
            dto.setLanguage(this.languageService.findActivityLanguage(inDTO.getTenantCode(), dto.getActivityCode(), inDTO.getLanguage()));
            List<TPromoActivityStoreVO> storeVOs = tPromoActivityStoreService.getStoresByActivityCode(dto.getActivityCode());
            List<ActivityStore> stores = new ArrayList<>();
            for (TPromoActivityStoreVO storeVO : storeVOs) {
                ActivityStore store = new ActivityStore();
                store.setChannelCode(storeVO.getChannelCode());
                store.setChannelName(storeVO.getChannelName());
                store.setOrgCode(storeVO.getOrgCode());
                store.setStoreName(storeVO.getStoreName());
                stores.add(store);
            }

            dto.setStores(stores);

            String groupName = null;
            if (StringUtil.isNotEmpty(dto.getGroupCode())){
                PromoGroupVO promoGroupVO = promotionGroupService.getGroupByGroupCode(inDTO.getTenantCode(), dto.getGroupCode());
                if (null != promoGroupVO){
                    groupName = promoGroupVO.getGroupName();
                }
            }
            dto.setGroupName(groupName);
            getReleaseQuantityAndTagCode(tagMap, dto);
        }
        String auditConfig = activityComponentDomain.getActivityAuditConfig(inDTO.getTenantCode());
        List<CouponActivityListOutDTO> couponActivityListOutDTOS = promoCouponsDTOs.getList();

        String needAudit = activityComponentDomain.isNeedAudit(auditConfig);
        String needDifferentOperator = activityComponentDomain.isAuditDifferentOperator(auditConfig);
        couponActivityListOutDTOS.forEach(a-> {
            a.setNeedAudit(needAudit);
            a.setNeedDifferentOperator(needDifferentOperator);
            setCommitUser(a);
        });
        return promoCouponsDTOs;
    }

    public void setCommitUser(CouponActivityListOutDTO promoActivityOutDTO){
        String activityStatus = promoActivityOutDTO.getActivityStatus();
        if(ActivityStatusEnum.CLOSURE.code().equals(activityStatus)
                || ActivityStatusEnum.EFFECTIVE.code().equals(activityStatus)
                || ActivityStatusEnum.END.code().equals(activityStatus)){
            promoActivityOutDTO.setCommitUser(promoActivityOutDTO.getUpdateUser());
        }
    }

    public void getReleaseQuantityAndTagCode(Map<String, String> tagMap, CouponActivityListOutDTO dto) {
        if (CouponTypeEnum.PROMOTION_CODE.code().equals(dto.getCouponType())) {
            dto.setTagCode("-1");
            dto.setReleaseQuantity(dto.getTotalQuantity());
        } else {
            String tagCode = null;
            for (Map.Entry<String, String> stringStringEntry : tagMap.entrySet()) {
                if (stringStringEntry.getValue().contains(dto.getTemplateCode())){
                    tagCode = stringStringEntry.getKey();
                }
            }
            dto.setTagCode(tagCode);
            dto.setReleaseQuantity(String.valueOf(couponReleaseService.queryReleaseCount111(dto.getActivityCode())));
        }
    }

    /**
     * 根据活动分类标签组装模板id
     */
    private String templetCodeDeal(String tagCodes, Map<String, String> tagMap) {

        if (StringUtil.isBlank(tagCodes)) {
            return null;
        }

        List<String> templetCodes = new ArrayList<>();
        for (String tagCode : tagCodes.split(",")) {
            String templateCode = tagMap.get(tagCode);
            if (StringUtils.isNotBlank(templateCode)) {
                templetCodes.add(templateCode);
            }
        }
        return String.join(",", templetCodes);
    }

    /**
     * 手动关闭优惠券活动 修改冻结状态
     */
    @Transactional
    public void closeCoupon(String tenantCode, String activityCode) {

        //投放
        couponReleaseService.stopCouponRelease(activityCode);
        //将券码状态改为冻结状态
        //将券码状态改为冻结状态并逻辑删除
        couponInnerCodeService.logicDelete(tenantCode, activityCode);
        couponCodeUserService.logicDelete(tenantCode, activityCode);
    }

    public List<CouponActivityDTO> queryCouponActivityByTenantCode(String tenantCode, String language, String orgCode) {

        //查询已经生效的券活动 给会员那边使用
        List<ActivityModel> storeList = effectCouponActivityList(tenantCode, language);
        List<ActivityModel> list = screeningOrgCode(storeList, orgCode);
        return BeanCopyUtils.jsonCopyList(list, CouponActivityDTO.class);
    }

    @Transactional(readOnly = true)
    public PageInfo<StoreCouponActivityOutDTO> queryCouponActivityInStore(StoreCouponActivityInDTO store) {

        List<StoreCouponActivityOutDTO> storeLists = new ArrayList<>();
        //查询租户下已经生效的券活动 并过滤店铺编码
        Map<String, ActivityCacheDTO> activityCacheMap = activityCacheDomain.getActivityCacheMap(store.getTenantCode(), store.getLanguage(), 2, ActivityTypeEnum.COUPON);
        long now = Long.parseLong(DateUtil.getCurrentDateAsString(DateUtil.FORMAT_YYYYMMDDHHMISS_14));
        List<ActivityCacheDTO> collect = getActivityCacheDTOS(activityCacheMap, now);

        if (CollectionUtils.isEmpty(collect)) {
            return new PageInfo<>(storeLists);
        }
        List<String> activityCodes = new ArrayList<>();
        collect.stream().forEach(x -> activityCodes.add(x.getActivityModel().getActivityCode()));
		List<CouponReleaseDomain> releaseDomains = couponReleaseService.queryReleasesByActivityCodes(store.getTenantCode(), activityCodes, true);
        //过滤店铺编码
        for (int i = 0; i < collect.size(); i++) {
            ActivityCacheDTO activityCacheDTO = collect.get(i);
            ActivityModel tPromoActivityVO = activityCacheDTO.getActivityModel();
            StoreCouponActivityOutDTO storeDTO = BeanCopyUtils.jsonCopyBean(tPromoActivityVO, StoreCouponActivityOutDTO.class);
            storeDTO.setActivityPeriod(BeanCopyUtils.jsonCopyBean(activityCacheDTO.getPeriodModel(), ActivityPeriod.class));
            //有对应的url就填充，没有就填充主表
            String orgCode = store.getOrgCode();
            List<TPromoActivityStoreVO> storeVOs = tPromoActivityStoreService.getStoresByActivityCode(tPromoActivityVO.getActivityCode());
            if (!CollectionUtils.isEmpty(storeVOs) && StringUtil.isNotBlank(orgCode)) {
                boolean flag = false;
                for (TPromoActivityStoreVO activityStoreVO : storeVOs) {
                    if (orgCode.equals(activityStoreVO.getOrgCode())) {
                        flag = true;
                        storeDTO.setActivityUrl(activityStoreVO.getUrl());
                        break;
                    }
                }
                if (!flag) {
                    continue;
                }
            }
            //填充奖励类型
            TemplateModel template = activityCacheDTO.getPromoTemplate();
            storeDTO.setRewardType(TemplateCodeSubstringUtil.getRewardType(template.getTemplateCode()));
            //条件单位、值
            List<FunctionParamModel> paramList = activityCacheDTO.getPromoFuncParams();
            if (!CollectionUtils.isEmpty(paramList)) {
                getConditionValueByFunctionParam(storeDTO, paramList);
            }
            //填充面值
            FaceClass faceClass = FaceClassUtil.faceUnitAndFaceValue(storeDTO.getRewardType(), paramList);
            storeDTO.setFaceValue(faceClass.getFaceValue());
            storeDTO.setFaceUnit(faceClass.getFaceUnit());
            Integer userLimitMax = activityCacheDTO.getActivityModel().getUserLimitMax();
            storeDTO.setUserLimitMax(userLimitMax);
            //是否有投放
            List<CouponReleaseDomain> releaseDomainSubs = releaseDomains.stream().filter(x -> x.getActivityCode().equals(tPromoActivityVO.getActivityCode())).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(releaseDomainSubs)) {
                storeDTO.setReceiveStatus(Integer.parseInt(ReceiveStatusEnum.NOT_START.code()));
                storeLists.add(storeDTO);
                continue;
            }
            //领取状态：1：可领取；2：未开始 3：已结束；4：已领完；5：已被限领；6：当前会员不符合要求（会员等级或会员标签）；优先级
            //开始时间集合
            List<String> sortStart = Lists.newArrayList();
            //结束时间集合
            List<String> sortEnd = Lists.newArrayList();
            getReceiveTime(releaseDomainSubs, sortStart, sortEnd);
            if (CollectionUtils.isEmpty(sortStart) || CollectionUtils.isEmpty(sortEnd)) {
                storeDTO.setReceiveStatus(Integer.parseInt(ReceiveStatusEnum.NOT_START.code()));
                storeLists.add(storeDTO);
                continue;
            } else {
                Collections.sort(sortStart);
                Collections.sort(sortEnd);
                //最小开始和最大结束时间
                storeDTO.setStartReceiveTime(sortStart.get(0));
                storeDTO.setEndReceiveTime(sortEnd.get(sortEnd.size() - 1));
            }
            //过滤所有的领取未结束的投放
            releaseDomainSubs.removeIf(x -> Long.parseLong(x.getReceiveEndTime()) <= now);

            //2：未开始  当前时间早于最小领用开始时间
            if (storeDTO.getReceiveStatus() == 0 && Long.valueOf(sortStart.get(0)) > now) {
                storeDTO.setReceiveStatus(Integer.parseInt(ReceiveStatusEnum.NOT_START.code()));
                storeLists.add(storeDTO);
                continue;
            }
            //会员信息 校验等级
            QualificationFilter filter = new QualificationFilter(activityCacheDTO.getQualificationModels());
            boolean filter1 = filter.filter(QualificationModel.convertToModel(store.getQualifications()));
            if (!filter1) {
                storeDTO.setReceiveStatus(Integer.parseInt(ReceiveStatusEnum.NOT_CONDITION.code()));
                storeLists.add(storeDTO);
                continue;
            }
            if (storeDTO.getReceiveStatus() == 0 && Long.valueOf(sortEnd.get(sortEnd.size() - 1)) < now) {
                //3：已结束 当前时间大于最大领用结束时间
                storeDTO.setReceiveStatus(Integer.parseInt(ReceiveStatusEnum.END.code()));
                storeLists.add(storeDTO);
                continue;
            }
            if (storeDTO.getReceiveStatus() == 0 && StringUtil.isNotBlank(store.getUserCode()) && null != userLimitMax && userLimitMax != 0) {
                //5：已被限领
                int count = redisService.getCouponUserCount111(store.getTenantCode(), tPromoActivityVO.getActivityCode(), store.getUserCode());
                //判断是否限领
                if (count >= userLimitMax) {
                    //领取状态：1：可领取；2：未开始 3：已结束；4：已领完；5：已被限领；6：当前会员不符合要求（会员等级或会员标签）；优先级  （7和2级别相同）7263541
                    storeDTO.setReceiveStatus(Integer.parseInt(ReceiveStatusEnum.LIMIT.code()));
                    storeLists.add(storeDTO);
                    continue;
                }
            }
            if (storeDTO.getReceiveStatus() == 0) {
                boolean rightTime = true;
                //投放集合
                for (CouponReleaseDomain releaseDomain : releaseDomainSubs) {
                    if (Long.parseLong(releaseDomain.getReceiveEndTime()) > now || Long.parseLong(releaseDomain.getReceiveStartTime()) < now) {
                        rightTime = false;
                        int result = releaseDomain.getInventory();
                        if (result > 0) {
                            storeDTO.setReceiveStatus(Integer.parseInt(ReceiveStatusEnum.RECEIVE.code()));
                            storeLists.add(storeDTO);
                            break;
                        }
                    }
                }
                //3：已结束
                if (rightTime) {
                    storeDTO.setReceiveStatus(Integer.parseInt(ReceiveStatusEnum.END.code()));
                    storeLists.add(storeDTO);
                    continue;
                }
                //4：已领完 判断有效库存
                if (storeDTO.getReceiveStatus() == 0) {
                    storeDTO.setReceiveStatus(Integer.parseInt(ReceiveStatusEnum.BROUGHT.code()));
                    storeLists.add(storeDTO);
                }
            }
        }
        return new PageInfo<>(storeLists);
    }

    @Transactional(readOnly = true)
    public PageInfo<MemberTagCouponActivityOutDTO> queryCouponActivityByMemberTag(MemberTagCouponActivityInDTO memberTagInDTO) {
        String tenantCode = memberTagInDTO.getTenantCode();
        List<MemberTagCouponActivityOutDTO> memberTagList = new ArrayList<>();
        Map<String, ActivityCacheDTO> activityMap = activityCacheDomain.getActivityCacheMap(tenantCode, tenantCode, 2, ActivityTypeEnum.COUPON);
        long now = Long.parseLong(DateUtil.getCurrentDateAsString(DateUtil.FORMAT_YYYYMMDDHHMISS_14));
        List<ActivityCacheDTO> collect = getActivityCacheDTOS(activityMap, now);
        if (CollectionUtils.isEmpty(collect)) {
            return new PageInfo<>(memberTagList);
        }

        List<String> activityCodeList = new ArrayList<>();
        for(ActivityCacheDTO activityCacheDTO : collect){
            ActivityModel activityModel = activityCacheDTO.getActivityModel();
            if(null != activityModel && !StringUtils.isEmpty(activityModel.getActivityCode())){
                activityCodeList.add(activityModel.getActivityCode());
            }
        }

        if(CollectionUtils.isEmpty(activityCodeList)){
            return new PageInfo<>(memberTagList);
        }

        List<QualificationModel> qualificationModelList = qualificationService.queryQualificationsByMemberTags(tenantCode,activityCodeList,memberTagInDTO.getMemberTagList());
        Set<String> memberActivityCodes = qualificationModelList.stream().map(QualificationModel::getActivityCode).collect(Collectors.toSet());
        if(CollectionUtils.isEmpty(memberActivityCodes)){
            return new PageInfo<>(memberTagList);
        }

        for(ActivityCacheDTO activityCacheDTO : collect){
            ActivityModel activityModel = activityCacheDTO.getActivityModel();
            if(memberActivityCodes.contains(activityModel.getActivityCode())){
                MemberTagCouponActivityOutDTO memberTagDTO = BeanCopyUtils.jsonCopyBean(activityModel, MemberTagCouponActivityOutDTO.class);
                memberTagList.add(memberTagDTO);
            }
        }
        return new PageInfo<>(memberTagList);
    }

    public void getReceiveTime(List<CouponReleaseDomain> releaseDomainSubs, List<String> sortStart, List<String> sortEnd) {
        for (CouponReleaseDomain e : releaseDomainSubs) {
            if ("04".equals(e.getReleaseStatus())) {
                continue;
            }
            sortStart.add(e.getReceiveStartTime());
            sortEnd.add(e.getReceiveEndTime());
        }
    }

    public void getConditionValueByFunctionParam(StoreCouponActivityOutDTO storeDTO, List<FunctionParamModel> paramList) {
        //填充条件
        FunctionParamModel param = new FunctionParamModel();
        Optional<FunctionParamModel> paramOptional = paramList.stream().filter(x -> x.getFunctionType().equals(FuncTypeEnum.PARAM.code()))
            .findFirst();
        if (paramOptional.isPresent()) {
            param = paramOptional.get();
        }
        storeDTO.setConditionUnit(param.getParamUnit());
        if (StringUtil.isNotEmpty(param.getParamValue())){
            storeDTO.setConditionValue(new BigDecimal(param.getParamValue()));
        }
    }

    public List<ActivityCacheDTO> getActivityCacheDTOS(Map<String, ActivityCacheDTO> activityCacheMap, long now) {
        return activityCacheMap.values().stream().filter(x -> x.getActivityModel().getCouponType().equals(CouponTypeEnum.PROMOTION_COUPON.code())
                            && now > Long.valueOf(x.getActivityModel().getActivityBegin())
                            && (x.getActivityModel().getShowFlag() == null || 1 == x.getActivityModel().getShowFlag()))
                .collect(Collectors.toList());
    }

    private List<ActivityModel> effectCouponActivityList(String tenantCode, String language) {

        //查询已经生效的券活动
        List<ActivityModel> list = activityService.queryActivityByTenantCode(tenantCode, ActivityTypeEnum.COUPON, null, ActivityStatusEnum.EFFECTIVE);
        if (CollectionUtils.isEmpty(list)) {
            return list;
        }

        Iterator<ActivityModel> amIterator = list.iterator();
        while (amIterator.hasNext()) {
            
            ActivityModel activityModel = amIterator.next();

            ActivityModel conponActivityModel = couponActivityService.findCouponActivity(tenantCode, activityModel.getActivityCode());
            String now = DateUtil.getCurrentDateAsString(DateUtil.FORMAT_YYYYMMDDHHMISS_14);
            if (Long.valueOf(now) < Long.valueOf(activityModel.getActivityBegin()) || !CouponTypeEnum.PROMOTION_COUPON.equalsCode(conponActivityModel.getCouponType())) {
                amIterator.remove();
            }

            activityModel.setLanguage(languageService.findActivityLanguage(tenantCode, activityModel.getActivityCode(), language));
        }

        return list;
    }

    private List<ActivityModel> screeningOrgCode(List<ActivityModel> list, String orgCode) {

        List<ActivityModel> storeList = new ArrayList<>();
        //过滤全部店铺
        if (StringUtil.isNotBlank(orgCode)) {
            if (!StoreTypeEnum.ALL.code().equals(orgCode)) {
                filterOrgCode(list, orgCode, storeList);
            }
        } else {
            storeList.addAll(list);
        }
        return storeList;
    }

    private void filterOrgCode(List<ActivityModel> list, String orgCode, List<ActivityModel> storeList) {

        Iterator<ActivityModel> it = list.iterator();
        while (it.hasNext()) {
            ActivityModel x = it.next();
            //00 不限店铺  01过滤自定义店铺
            if (StoreTypeEnum.CUSTOM.code().equals(x.getStoreType())) {
                List<TPromoActivityStoreVO> storeVOs = tPromoActivityStoreService.getStoresByActivityCode(x.getActivityCode());
                for (TPromoActivityStoreVO storeVO : storeVOs) {
                    if (orgCode.equals(String.valueOf(storeVO.getOrgCode()))) {
                        storeList.add(x);
                    }
                }
            } else {
                storeList.add(x);
            }
        }
    }

    private void checkOrgCode111(String orgCode, String activityCode) {

        if (StringUtil.isNotBlank(orgCode)) {
            Boolean flag = false;
            List<TPromoActivityStoreVO> storeVOs = tPromoActivityStoreService.getStoresByActivityCode(activityCode);
            for (TPromoActivityStoreVO tPromoActivityStoreVO : storeVOs) {
                if (orgCode.equals(String.valueOf(tPromoActivityStoreVO.getOrgCode()))) {
                    flag = true;
                }
            }
            Check.check(!flag, CouponActivityChecker.ACTIVITY_STORE_NOT_EXIST);
        }
    }

    private TPromoCouponInnerCodeVO convertCouponVO(CreateCouponActivityDTO activityDTO) {

        TPromoCouponInnerCodeVO vo = new TPromoCouponInnerCodeVO();
        vo.setTenantCode(activityDTO.getTenantCode());
        vo.setActivityCode(activityDTO.getActivityCode());
        vo.setCouponType(CouponTemplateEnum.COUPON_CODE.code());
        vo.setStatus(CouponStatusEnum.UN_GRANT.code());
        vo.setFrozenStatus(CouponFrozenStatusEnum.UN_FROZEN.code());
        vo.setCouponCode(activityDTO.getPromotionCode());
        vo.setReceiveStartTime(DateUtil.format(new Date(), DateUtil.FORMAT_YYYYMMDDHHMISS_14));
        vo.setReceiveEndTime(activityDTO.getActivityEnd());
        vo.setPromoPassword(activityDTO.getPromoPassword());
        for (FunctionParam paramVO : activityDTO.getFuncParams()) {
            if (FuncTypeEnum.IncentiveEnum.REDUCE_AMOUNT.equalsCode(paramVO.getFunctionCode())) {
                vo.setFaceValue(new BigDecimal(paramVO.getParamValue()));
                vo.setFaceUnit(FaceUnitEnum.MONEY.code());
            }
            if (FuncTypeEnum.IncentiveEnum.DISCOUNT.equalsCode(paramVO.getFunctionCode())) {
                vo.setFaceValue(new BigDecimal(paramVO.getParamValue()));
                vo.setFaceUnit(FaceUnitEnum.DISCOUNT.code());
            }
        }
        return vo;
    }
    private String getCouponReleaseCode() {
        return gTechCodeGenerator.generateCode(MarketingConstants.APP_KEY, "couponReleaseCode", TEMPLATE_EXPR, 1L);
    }
    private CouponReleaseModel convertReleaseVO(CreateCouponActivityDTO activityDTO) {

        CouponReleaseModel promoCouponReleaseVO = new CouponReleaseModel();
        promoCouponReleaseVO.setReleaseCode(getCouponReleaseCode());
        promoCouponReleaseVO.setReleaseStatus(CouponReleaseStatusEnum.CREATE_COUPON.code());
        promoCouponReleaseVO.setReleaseQuantity(activityDTO.getTotalQuantity());
        promoCouponReleaseVO.setReleaseTime(ReleaseTypeEnum.IMMEDIATELY.code());
        promoCouponReleaseVO.setReleaseTime(DateUtil.format(new Date(), DateUtil.FORMAT_YYYYMMDDHHMISS_14));
        promoCouponReleaseVO.setReceiveStartTime(activityDTO.getActivityBegin());
        promoCouponReleaseVO.setReceiveEndTime(activityDTO.getActivityEnd());
        promoCouponReleaseVO.setValidStartTime(activityDTO.getActivityBegin());
        promoCouponReleaseVO.setValidEndTime(activityDTO.getActivityEnd());
        promoCouponReleaseVO.setReleaseType(CouponReleaseTypeEnum.IMMEDIATELY.code());
        promoCouponReleaseVO.setActivityCode(activityDTO.getActivityCode());
        promoCouponReleaseVO.setCouponType(activityDTO.getCouponType());



        return promoCouponReleaseVO;
    }

    /**
     * 促销券过期处理
     */
    @Transactional(propagation=Propagation.REQUIRES_NEW)
    public void expireCoupon(CouponDomain couponDomain) {
        
        if (couponDomain.isNeedToDoExpire()) {
            this.couponInnerCodeService.updateCouponStatus(couponDomain.getTenantCode(), couponDomain.getActivityCode(),
                couponDomain.getCouponCode(), CouponStatusEnum.EXPIRE);
            this.couponCodeUserService.updateCouponStatus(couponDomain.getTenantCode(), couponDomain.getActivityCode(),
                couponDomain.getCouponCode(), CouponStatusEnum.EXPIRE, null);

            couponDomain.setStatus(CouponStatusEnum.EXPIRE.code());
        }
    }

    public List<ExportCouponOutDTO> exportCoupon(ExportCouponInDTO exportCouponInDTO) {
        return couponInnerCodeService.exportCoupon(exportCouponInDTO);
    }

    public List<CouponReleaseModel> queryCouponReleaseList(String activityCode) {
        return couponReleaseService.queryCouponRelease(activityCode);
    }


    public List<CouponReleaseModel> findReleaseByCouponCode(CouponReleaseListInDTO dto) {
        List<TPromoCouponInnerCodeVO> couponList = couponInnerCodeService.getCouponInnerCodeByCodes(dto.getTenantCode(), dto.getCouponCodeList());
        if (CollectionUtils.isEmpty(couponList)) {
            return Lists.newArrayList();
        }
        List<String> couponReleaseCode = couponList.stream().map(TPromoCouponInnerCodeVO::getReleaseCode).collect(Collectors.toList());
        List<CouponReleaseDomain> couponReleaseDomains = couponReleaseService.queryReleaseByCondition(dto.getTenantCode(),null, couponReleaseCode);
        return BeanCopyUtils.jsonCopyList(couponReleaseDomains, CouponReleaseModel.class);
    }



}
