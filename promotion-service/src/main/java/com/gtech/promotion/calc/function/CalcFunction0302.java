/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.calc.function;

import com.gtech.promotion.calc.CalcFunction;
import com.gtech.promotion.calc.CalcResult;
import com.gtech.promotion.calc.model.CalcActivityIncentive;
import com.gtech.promotion.calc.model.CalcShoppingCart;
import com.gtech.promotion.calc.model.CalcShoppingCartItem;
import com.gtech.promotion.code.activity.ActivityTagCodeEnum;
import com.gtech.promotion.code.activity.FuncTypeEnum;
import com.gtech.promotion.code.activity.NeedMoreUnitEnum;
import com.gtech.promotion.exception.ErrorCodes;
import lombok.Getter;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.Map.Entry;

/**
 * 原子函数：条件参数(03) - 数量(02)
 * 
 */
@Component
public class CalcFunction0302 implements CalcFunction{

    @Getter
    private FunctionEnum function = FunctionEnum.F0302;

    static {
        // Registers the current atomic function
        CalcFunctionRegister.register(FunctionEnum.F0302, new CalcFunction0302());
    }

    /**
     * 构造函数私有化，不允许自己生成该对象
     */
    private CalcFunction0302(){
    }

    /*
     * (non-Javadoc)
     * @see com.gtech.promotion.calc.CalcFunction#calc(com.gtech.promotion.calc.model.ICalcPromoObject, com.gtech.promotion.calc.model.CalcActivityIncentive, java.util.Deque, 
     * com.gtech.promotion.calc.function.FunctionParamMap, com.gtech.promotion.calc.CalcFunction)
     */
    @Override
    public CalcResult calc(CalcShoppingCart promoObject, CalcActivityIncentive incentive, Deque<FunctionEnum> funcList, FunctionParamMap paramMap, CalcFunction caller) {

        try{
            Integer qty = promoObject.getOriginalQuantity();
            if (ActivityTagCodeEnum.BUY_A_DISCOUNT_B.equalsCode(promoObject.getCalcActivity().getTagCode())){//买A优惠B
                qty = promoObject.getOriginalQuantity("1");
            }
            String paramValue = paramMap.getFuncParamValue(this.function.code());
            Integer paramQty = Integer.parseInt(paramValue);

            if (!FuncTypeEnum.CONDITION.equals(caller.getFunction().type())){
                return CalcResult.ERROR_DEFAULT;
            }

            switch (caller.getFunction()) {

                case F0202:
                    return handler(incentive, qty, paramQty, 1);
                case F0206:
                    return handlerF01(incentive, promoObject, paramQty, 1, false);
                case F0207:
                    return handlerF01(incentive, promoObject, paramQty, 0, true);
                case F0208:
                case F0203:
                case F0204:
                    return handler(incentive, qty, paramQty, qty / paramQty);

                case F0205:
                    Set<String> seqNums = promoObject.getCalcActivity().getSeqNums();//这里的seqNum会去重
                    if (seqNums.size() == Integer.parseInt(paramValue)){
                        HashMap<String, Integer> hashMap = seqNumCount(promoObject, incentive);
                        if (hashMap.size() < Integer.parseInt(paramValue)){
                            incentive.setFailedReason(ErrorCodes.ACTIVITY_LIMIT_CONDITION_QUANTITY);
                            return CalcResult.ERROR_DEFAULT;
                        }
                        incentive.setIncentiveTimes(incentiveTimeDeal(hashMap));
                        incentive.setPromoQuantity(qty);
                        return CalcResult.SUCCESS_TRUE;//各商品范围不能有交集且不能是全商品
                    }else{
                        incentive.setFailedReason(ErrorCodes.ACTIVITY_LIMIT_CONDITION_QUANTITY);
                        return CalcResult.SUCCESS_FALSE;
                    }

                default:
                    return CalcResult.ERROR_DEFAULT;
            }

        }catch (RuntimeException e){
            return CalcResult.ERROR_DEFAULT;
        }
    }

    private CalcResult handlerF01(CalcActivityIncentive incentive, CalcShoppingCart project, Integer paramQty, int incentiveTimes, boolean sum) {
        boolean flag = false;
        int promoQuantity = 0;
        List<CalcShoppingCartItem> calcShoppingCartItemList = project.getCalcShoppingCartItemList();
        Iterator<CalcShoppingCartItem> iterator = calcShoppingCartItemList.iterator();
        List<CalcShoppingCartItem> temp = new ArrayList<>(calcShoppingCartItemList);
        if (incentive.getTemplateCode().startsWith(FunctionEnum.F0102.code() + FunctionEnum.F0206.code())){
            int i = 0;
            i = getReturnInt(calcShoppingCartItemList, i);
            if (i >= paramQty){
                promoQuantity = i;
                incentiveTimes = 1;
                flag = true;
            }
        }else {
            while (iterator.hasNext()){
                CalcShoppingCartItem next = iterator.next();
                Integer qty = next.getQuantity();
                if (qty.compareTo(paramQty) < 0){
                    iterator.remove();
                }else{
                    flag = true;
                    promoQuantity += 1;
                    if (sum){
                        incentiveTimes += 1;
                    }
                }
            }
        }
        return getCalcResult(incentive, project, paramQty, incentiveTimes, flag, promoQuantity, temp);
    }

    private int getReturnInt(List<CalcShoppingCartItem> calcShoppingCartItemList, int i) {
        for (CalcShoppingCartItem calcShoppingCartItem : calcShoppingCartItemList) {
            i += calcShoppingCartItem.getQuantity();
        }
        return i;
    }

    private CalcResult getCalcResult(CalcActivityIncentive incentive, CalcShoppingCart project, Integer paramQty, int incentiveTimes, boolean flag, int promoQuantity, List<CalcShoppingCartItem> temp) {
        if (flag){
            incentive.setIncentiveTimes(incentiveTimes);
            incentive.setPromoQuantity(promoQuantity);
            return CalcResult.SUCCESS_TRUE;
        }else{
            project.setCalcShoppingCartItemList(temp);
            incentive.setNeedMoreAmount(String.valueOf(paramQty - promoQuantity));
            incentive.setNeedMoreUnit(NeedMoreUnitEnum.PIECE.code());
            incentive.setFailedReason(ErrorCodes.ACTIVITY_LIMIT_CONDITION_QUANTITY);
            return CalcResult.SUCCESS_FALSE;
        }
    }

    private HashMap<String, Integer> seqNumCount(CalcShoppingCart promoObject,CalcActivityIncentive activityIncentive){
        List<CalcShoppingCartItem> cartItemList = promoObject.getCalcShoppingCartItemList();
        HashMap<String, Integer> hashMap = new HashMap<>();//k:seqnum;v:数量
        for (CalcShoppingCartItem calcShoppingCartItem : cartItemList){//将所有seqNum出现次数合并
            Set<String> set = calcShoppingCartItem.getCurrentActivity(activityIncentive.getActivityCode()).getSeqNums();//该商品项属于该活动的商品池序号
            if (!CollectionUtils.isEmpty(set)){
                for (String seqNum : set){//应该只有一个，理论上一个商品应该只属于一个池，不能属于2个池
                    if (hashMap.containsKey(seqNum)){
                        hashMap.put(seqNum, hashMap.get(seqNum) + calcShoppingCartItem.getQuantity());
                    }else{
                        hashMap.put(seqNum, calcShoppingCartItem.getQuantity());
                    }
                }
            }
        }
        return hashMap;
    }

    /**
     * 获取奖励次数
     */
    private Integer incentiveTimeDeal(HashMap<String, Integer> hashMap){
        Integer incentiveTimes = 0;
        for (Entry<String, Integer> entry : hashMap.entrySet()){//取最小的seqnum出现次数  就是奖励次数了
            Integer value = entry.getValue();
            if (incentiveTimes == 0L){
                incentiveTimes = value;
            }
            if (value < incentiveTimes){
                incentiveTimes = value;
            }
        }
        return incentiveTimes;
    }

    private CalcResult handler(CalcActivityIncentive activityIncentive,Integer qty,Integer paramQty,int incentiveTimes){
        if (qty.compareTo(paramQty) < 0){
            activityIncentive.setNeedMoreAmount(String.valueOf(paramQty - qty));
            activityIncentive.setNeedMoreUnit(NeedMoreUnitEnum.PIECE.code());
            activityIncentive.setFailedReason(ErrorCodes.ACTIVITY_LIMIT_CONDITION_QUANTITY);
            return CalcResult.SUCCESS_FALSE;
        }else{
            activityIncentive.setIncentiveTimes(incentiveTimes);
            //参与促销商品数量 满足条件的商品数量 paramQty*(qty / paramQty)
            activityIncentive.setPromoQuantity(qty);
            return CalcResult.SUCCESS_TRUE;
        }
    }
}
