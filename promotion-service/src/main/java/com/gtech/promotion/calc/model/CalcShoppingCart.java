/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.calc.model;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import com.gtech.commons.utils.BeanCopyUtils;
import com.gtech.commons.utils.StringUtil;
import com.gtech.promotion.calc.CalcActivity;
import com.gtech.promotion.calc.CalcConstants;
import com.gtech.promotion.calc.TemplateEnum;
import com.gtech.promotion.code.activity.FuncTypeEnum.ScopeEnum;
import com.gtech.promotion.code.activity.SettingTypeEnum.PriceSortEnum;
import com.gtech.promotion.dto.in.activity.ShoppingCartDTO;
import com.gtech.promotion.dto.in.activity.ShoppingCartItem;
import com.gtech.promotion.vo.bean.ShoppingCartStore;

import lombok.Getter;
import lombok.Setter;

/**
 * 购物车计算对象：属性有“商品列表，商品里面包含其所属活动”、“活动奖励信息列表”
 * 
 */
@Getter
@Setter
public class CalcShoppingCart extends ShoppingCartDTO {

    private static final long serialVersionUID = 2937227447185312658L;

    private transient Map<String, Integer> leftTimes0104Map = new HashMap<>();

    // Current calculating activity
    @Getter
    private transient CalcActivity calcActivity;
    public void setCalcActivity(CalcActivity calcActivity) {
        this.calcActivity = calcActivity;
        if (CollectionUtils.isNotEmpty(this.calcShoppingCartItemList)) {
            for(CalcShoppingCartItem sci : this.calcShoppingCartItemList) {
                sci.setCalcActivity(calcActivity);
            }
        }
    }

    // Shopping cart items list for calculating
    private transient List<CalcShoppingCartItem> calcShoppingCartItemList = null;

    // Promotion incentive list
    @Getter
    private transient List<CalcActivityIncentive> activityIncentiveList = null;
    public void addActivityIncentive(CalcActivityIncentive activityIncentive){

        if (null == this.activityIncentiveList){
            this.activityIncentiveList = new ArrayList<>();
        }
        this.activityIncentiveList.add(activityIncentive);
    }

    public CalcShoppingCart(){
        super();
    }

    public CalcShoppingCart(ShoppingCartDTO source){

        String[] igonre = { "orgCodes", "cartStoreList", "calcShoppingCartItemList", "activityIncentiveList", "leftTimes0104Map"};
        BeanCopyUtils.copyProps(source, this, igonre);

        this.setCartStoreList(BeanCopyUtils.jsonCopyList(source.getCartStoreList(), ShoppingCartStore.class));

        if (CollectionUtils.isNotEmpty(source.getPromoProducts())){
            this.calcShoppingCartItemList = new ArrayList<>();
            for (ShoppingCartItem sci : source.getPromoProducts()){
                this.calcShoppingCartItemList.add(new CalcShoppingCartItem(sci));
            }
        }
    }

    public CalcShoppingCart(CalcShoppingCart source){

        String[] igonres = { "orgCodes", "cartStoreList", "calcShoppingCartItemList", "activityIncentiveList", "leftTimes0104Map"};
        BeanCopyUtils.copyProps(source, this, igonres);

        this.calcShoppingCartItemList = BeanCopyUtils.jsonCopyList(source.getCalcShoppingCartItemList(), CalcShoppingCartItem.class);
    }

    public boolean deductLeftTimes0104(String skuCode,String groupCode) {
        
        this.initLeftTimes0104(skuCode,groupCode);

        Integer leftTimes0104 = this.leftTimes0104Map.get(skuCode);
        if (null == leftTimes0104) {
            return false;
        }

        if (leftTimes0104 <= 0) {
            return false;
        }

        this.leftTimes0104Map.put(skuCode, --leftTimes0104);
        return true;
    }

    private void initLeftTimes0104(String skuCode,String groupCode) {

        if (StringUtil.isNotEmpty(groupCode)){
            if (StringUtils.isBlank(skuCode) || CollectionUtils.isEmpty(this.calcShoppingCartItemList)) {
                return;
            }
        }else {
            if (StringUtils.isBlank(skuCode) || leftTimes0104Map.containsKey(skuCode) || CollectionUtils.isEmpty(this.calcShoppingCartItemList)) {
                return;
            }
        }

        for (CalcShoppingCartItem cscItem : this.calcShoppingCartItemList) {
            if (skuCode.equals(cscItem.getSkuCode())) {
                this.leftTimes0104Map.put(skuCode, cscItem.getQuantity());
            }
        }
    }

    /**
     * Get the original amount of all items
     */
    public BigDecimal getOriginalAmount(){

        if (CollectionUtils.isEmpty(this.getCalcShoppingCartItemList())){
            return BigDecimal.ZERO;
        }

        BigDecimal amount = BigDecimal.ZERO;
        for (CalcShoppingCartItem item : this.getCalcShoppingCartItemList()){
            amount = amount.add(item.getProductAmount());
        }

        return amount;
    }

    /**
     * 获取商品的促销金额
     * 
     * @return 商品的促销金额
     */
    public BigDecimal getPromoAmount(){

        if (CollectionUtils.isEmpty(this.getCalcShoppingCartItemList())){
            return BigDecimal.ZERO;
        }

        BigDecimal amount = BigDecimal.ZERO;
        for (CalcShoppingCartItem item : this.getCalcShoppingCartItemList()){
            amount = amountAdd(amount, item);
        }

        return amount;
    }

    public BigDecimal getPromoAmount(String seqNum){
        if (CollectionUtils.isEmpty(this.getCalcShoppingCartItemList())){
            return BigDecimal.ZERO;
        }
        BigDecimal amount = BigDecimal.ZERO;
        for (CalcShoppingCartItem item : this.getCalcShoppingCartItemList()){
            if (item.seqNumcheck(seqNum)){
                amount = amountAdd(amount, item);
            }
        }
        return amount;
    }

    private BigDecimal amountAdd(BigDecimal amount, CalcShoppingCartItem item){

        if (this.calcActivity != null && this.calcActivity.getPrecison() != null){
            amount = amount.add(item.getPromoAmount().setScale(this.calcActivity.getPrecison(), CalcConstants.DEFAULT_ROUND));
			if (calcActivity.getPowerPrecison() != null) {
				amount = amount.divide(calcActivity.getPowerPrecison()).setScale(0, CalcConstants.DEFAULT_ROUND).multiply(calcActivity.getPowerPrecison());
			}
        }else{
            amount = amount.add(item.getPromoAmount());
        }

        return amount;
    }

    /**
     * 获取商品的原始数量
     * 
     * @return 商品的原始数量=所有sku的件数之和
     */
    public Integer getOriginalQuantity(){
        if (CollectionUtils.isEmpty(this.getCalcShoppingCartItemList())){
            return 0;
        }
        Integer qty = 0;
        for (CalcShoppingCartItem scItem : this.getCalcShoppingCartItemList()){
            qty += scItem.getQuantity();
        }

        return qty;
    }

    public Integer getOriginalQuantity(String seqNum){
        if (CollectionUtils.isEmpty(this.getCalcShoppingCartItemList())){
            return 0;
        }
        Integer qty = 0;
        for (CalcShoppingCartItem item : this.getCalcShoppingCartItemList()){
            if (item.seqNumcheck(seqNum)){
                qty += item.getQuantity();
            }
        }

        return qty;
    }

    /**
     * 增加购物车单品项到活动单品项列表
     *
     * @param calcShoppingCartItem 购物车单品项
     * @param sortRule 排序规则： 1-降序；2升序
     */
    public void addCalcShoppingCartItem(CalcShoppingCartItem calcShoppingCartItem,String sortRule,String seqNums,CalcActivity itemActivity){

        if (null == calcShoppingCartItemList){
            this.calcShoppingCartItemList = new ArrayList<>();
        }
        calcShoppingCartItem.addSeqActivityMap(itemActivity.getActivityCode(), seqNums);
        if (calcShoppingCartItem.getProductPrice().compareTo(BigDecimal.ZERO) == 0) {
            calcShoppingCartItemList.add(0, calcShoppingCartItem);
            return;
        }

        this.calcShoppingCartItemList.add(calcShoppingCartItem);

        if (PriceSortEnum.DESC.equalsCode(sortRule)){
            reversedSort();
        }else if (PriceSortEnum.ASC.equalsCode(sortRule)){
            calcShoppingCartItemList.sort(Comparator.comparing(ShoppingCartItem::getProductPrice));
        }else if (StringUtil.isBlank(sortRule)){
            if (TemplateEnum.T0303.code().equals(itemActivity.getTemplateCode()) 
                            || TemplateEnum.T0802.code().equals(itemActivity.getTemplateCode()) 
                            || TemplateEnum.T0801.code().equals(itemActivity.getTemplateCode())
                            || TemplateEnum.T1203.code().equals(itemActivity.getTemplateCode())
                            || TemplateEnum.T1204.code().equals(itemActivity.getTemplateCode())
            ){//每第、第和买送 的活动 是升序
                calcShoppingCartItemList.sort(Comparator.comparing(ShoppingCartItem::getProductPrice));
            }else{
                reversedSort();
            }
        }

    }

    private void reversedSort() {
        List<CalcShoppingCartItem> collect1 = calcShoppingCartItemList.stream().filter(x -> x.getProductPrice().compareTo(BigDecimal.ZERO) == 0).collect(Collectors.toList());
        List<CalcShoppingCartItem> collect = calcShoppingCartItemList.stream().filter(x -> x.getProductPrice().compareTo(BigDecimal.ZERO) > 0).sorted((x, y) -> y.getProductPrice().compareTo(x.getProductPrice())).collect(Collectors.toList());
        collect1.addAll(collect);
        calcShoppingCartItemList = collect1;
    }

    /**
     * 获取当前的促销类型
     */
    public ScopeEnum getPromoScope(){

        return this.calcActivity.getPromoScope();
    }
}
