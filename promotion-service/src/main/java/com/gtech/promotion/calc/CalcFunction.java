

/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.calc;

import java.util.Deque;
import java.util.Iterator;
import java.util.List;

import org.apache.commons.collections4.CollectionUtils;

import com.gtech.promotion.calc.function.FunctionEnum;
import com.gtech.promotion.calc.function.FunctionParamMap;
import com.gtech.promotion.calc.model.CalcActivityIncentive;
import com.gtech.promotion.calc.model.CalcShoppingCart;
import com.gtech.promotion.calc.model.CalcShoppingCartItem;
import com.gtech.promotion.utils.ExpressionHelper;
import com.gtech.promotion.vo.result.activity.ActivityGroupCache;

/**
 * 促销原子函数计算<BR>
 * 原子函数的实现必须都是无状态的，所有操作只对当前促销对象负责
 * 
 */

public interface CalcFunction{

    /**
     * FunctionEnum
     */
    FunctionEnum getFunction();

    /**
     * Calculate promotion activity with ICalcPromoObject (Shopping cart object or Shopping cart item detail object)
     * 
     * @param calcShoppingCart -- CalcShoppingCart
     * @param incentive -- Promotional campaign effective incentive information.
     * @param funcList -- Current template function list.
     * @param paramMap -- The parameter map for current template function list. 
     * @param caller -- The caller function for current function
     * @return Calculate result.
     */
    CalcResult calc(CalcShoppingCart calcShoppingCart, CalcActivityIncentive incentive, Deque<FunctionEnum> funcList, FunctionParamMap paramMap, CalcFunction caller);

    /**
     * Check whether the current activity can be combined.
     * 
     * @param tryFlag -- If tryFlag is false means only check, not effective.
     */
    default boolean checkActivitySku(CalcActivityIncentive incentive, List<CalcShoppingCartItem> cscItemList, boolean tryFlag) {

        Iterator<CalcShoppingCartItem> scItemIt =  cscItemList.iterator();
        while(scItemIt.hasNext()) {
            CalcShoppingCartItem scItem = scItemIt.next();
            List<ActivityGroupCache> groupCacheList = scItem.getGroupCacheList();
            boolean flag = false;
            if(CollectionUtils.isEmpty(groupCacheList)) {
            	 flag = ExpressionHelper.checkActivitySku(scItem.getActivityExpr(), scItem.getCalcActivity(), scItem.getSkuCode(), tryFlag);
            } else {
				// 分组叠加互斥
            	String groupCode = scItem.getCalcActivity().getActivityModel().getGroupCode();
				String activityCode = scItem.getCalcActivity().getActivityModel().getActivityCode();
				flag = ExpressionHelper.checkActivitySkuWithGroup(scItem.getSkuCode(), groupCode, activityCode, groupCacheList, tryFlag);
            }
            // 活动中去掉此sku
            if (!flag){
                scItem.setCurrentActivityNull(incentive.getActivityCode());//删除该活动下该商品的该活动
                scItemIt.remove();//删除该活动下该商品
                return false;
            }
        }

        return true;
    }
}
