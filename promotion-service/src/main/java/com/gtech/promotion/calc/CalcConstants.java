/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR <PERSON><PERSON><PERSON>NTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.calc;

import java.math.MathContext;
import java.math.RoundingMode;

/**
 * 
 *
 * <AUTHOR>
 * @Date 2020-03-14
 */
public class CalcConstants {

    private CalcConstants() {
        // No codes.
    }

    public static final int DEFAULT_PRECISION = MathContext.DECIMAL64.getPrecision();
    public static final RoundingMode DEFAULT_ROUND = RoundingMode.HALF_UP;
    public static final int LAST_PRECISION = 2;
    public static final RoundingMode LAST_ROUND = RoundingMode.HALF_UP;
    public static final MathContext DEF_MATHCONTEXT = new MathContext(DEFAULT_PRECISION, RoundingMode.HALF_UP);
    public static final RoundingMode ROUND_DOWN = RoundingMode.DOWN;

}
