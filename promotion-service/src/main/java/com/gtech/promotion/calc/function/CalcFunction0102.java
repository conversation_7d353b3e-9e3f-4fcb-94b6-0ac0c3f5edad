/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.calc.function;

import org.springframework.stereotype.Component;

import lombok.Getter;

/**
 * 原子函数：促销范围(01) - 商品范围(02)
 * 
 */
@Component
public class CalcFunction0102 extends CalcFunction01{

    @Getter
    private FunctionEnum function = FunctionEnum.F0102;

    static{
        // Registers the current atomic function
        CalcFunctionRegister.register(FunctionEnum.F0102, new CalcFunction0102());
    }

    /**
     * 构造函数私有化，不允许自己生成该对象
     */
    private CalcFunction0102(){
        // No codes
    }

}
