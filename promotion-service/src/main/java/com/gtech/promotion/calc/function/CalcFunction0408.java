/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.calc.function;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Deque;
import java.util.List;
import java.util.Set;

import org.springframework.stereotype.Component;

import com.gtech.promotion.calc.CalcConstants;
import com.gtech.promotion.calc.CalcFunction;
import com.gtech.promotion.calc.CalcResult;
import com.gtech.promotion.calc.model.CalcActivityIncentive;
import com.gtech.promotion.calc.model.CalcShoppingCart;
import com.gtech.promotion.calc.model.CalcShoppingCartItem;
import com.gtech.promotion.code.activity.SeqNumEnum;
import com.gtech.promotion.exception.ErrorCodes;

import lombok.Getter;

/**
 * 买A送B
 * 原子函数：促销奖励(04) - 满送(08)买送（不同类）
 * 
 */
@Component
public class CalcFunction0408 extends CalcFunction04{

    @Getter
    private FunctionEnum function = FunctionEnum.F0408;

    static {
        // Registers the current atomic function
        CalcFunctionRegister.register(FunctionEnum.F0408, new CalcFunction0408());
    }

    /**
     * 构造函数私有化，不允许自己生成该对象
     */
    private CalcFunction0408(){
        // No codes
    }

    /*
     * (non-Javadoc)
     * @see com.gtech.promotion.calc.CalcFunction#calc(com.gtech.promotion.calc.model.ICalcPromoObject, com.gtech.promotion.calc.model.CalcActivityIncentive, java.util.Deque, 
     * com.gtech.promotion.calc.function.FunctionParamMap, com.gtech.promotion.calc.CalcFunction)
     */
    @Override
    public CalcResult calc(CalcShoppingCart promoObject, CalcActivityIncentive incentive, Deque<FunctionEnum> funcList, FunctionParamMap paramMap, CalcFunction caller) {

        try{
            //长度不符合,即(有A没B or 有B没A) //买A赠B(A和B不相同)
            if (promoObject.getCalcActivity().getSeqNums().size() != 2){
                incentive.setFailedReason(ErrorCodes.ACTIVITY_LIMIT_CONDITION_QUANTITY);
                return CalcResult.ERROR_DEFAULT;
            }
            int precision = promoObject.getCalcActivity().getPrecison();
			BigDecimal powerPrecison = promoObject.getCalcActivity().getPowerPrecison();
            loadCommonValue(promoObject);
            List<CalcShoppingCartItem> itemList = promoObject.getCalcShoppingCartItemList();
            //条件参数:每满数量
            Integer conditionNum = Integer.parseInt(paramMap.getFuncParamValue(FunctionEnum.F0302.code()));
            //奖励参数:奖励数量
            Integer incentiveNum = Integer.parseInt(paramMap.getFuncParamValue(this.function.code()));
            //B池商品集合
            List<CalcShoppingCartItem> itemListB = new ArrayList<>();
            //奖励次数统计
            Integer activityTimes = incentiveTimesCount(incentive, itemList, conditionNum, incentiveNum, itemListB);
            if (activityTimes == -1){
                return CalcResult.ERROR_DEFAULT;
            }
            //总优惠金额
            BigDecimal incentiveAmount = countReduceAmount(itemListB, activityTimes,precision).setScale(precision, CalcConstants.ROUND_DOWN);
			if (powerPrecison != null) {
				incentiveAmount = incentiveAmount.divide(powerPrecison).setScale(0, CalcConstants.ROUND_DOWN).multiply(powerPrecison);
			}
            // 减金额的比率
            BigDecimal scale = getScale(incentiveAmount, promoObject.getPromoAmount());

            if (!this.checkLimitedAndCombine(incentive, promoObject.getCalcShoppingCartItemList(), activityTimes, incentiveAmount)) {
                return CalcResult.SUCCESS_LIMITED;
            }

            //分摊设置商品和商品下活动的金额
			handlerShoppingCartItemList(itemList, incentive, scale, precision, incentiveAmount, powerPrecison);
            return CalcResult.SUCCESS_TRUE;
        }catch (RuntimeException e){
            return CalcResult.ERROR_DEFAULT;
        }
    }

    /**
     * 奖励次数统计
     * 
     * @return Integer -1:未达到条件次数; >0:B池参与活动的数量
     */
    private Integer incentiveTimesCount(CalcActivityIncentive incentive, List<CalcShoppingCartItem> itemList, Integer conditionNum, Integer incentiveNum,
                    List<CalcShoppingCartItem> itemListB) {

        Integer quantityA = 0;//A池商品总数量
        Integer quantityB = 0;//B池商品总数量
        for (CalcShoppingCartItem calcShoppingCartItem : itemList) {
            Set<String> seqNums = calcShoppingCartItem.getCurrentActivity(incentive.getActivityCode()).getSeqNums();
            for (String seqNum : seqNums) {
                if (SeqNumEnum.A.equalsCode(seqNum)) {
                    quantityA += calcShoppingCartItem.getQuantity();
                } else if (SeqNumEnum.B.equalsCode(seqNum)) {
                    itemListB.add(calcShoppingCartItem);
                    quantityB += calcShoppingCartItem.getQuantity();
                }
            }
        }
        //由A计算奖励次数
        Integer timesA = quantityA / conditionNum;
        //由B计算奖励次数
        Integer timesB = (quantityB % incentiveNum) == 0 ? quantityB / incentiveNum : (quantityB / incentiveNum) + 1;
        //如果A参与0次则失败
        if (timesA == 0) {
            incentive.setFailedReason(ErrorCodes.ACTIVITY_LIMIT_CONDITION_QUANTITY);
            return -1;
        }
        //实际可参与次数,取小的
        Integer incentiveTimes = timesA > timesB ? timesB : timesA;
        incentive.setIncentiveTimes(incentiveTimes);
        //B中参与活动的商品的数量
        return (incentiveTimes * incentiveNum - quantityB > 0 ? quantityB : incentiveTimes * incentiveNum);
    }

}
