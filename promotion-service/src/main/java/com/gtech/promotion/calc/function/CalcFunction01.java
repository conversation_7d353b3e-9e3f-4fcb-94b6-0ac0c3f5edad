
/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.calc.function;

import com.gtech.commons.utils.ConvertUtils;
import com.gtech.promotion.calc.CalcActivity;
import com.gtech.promotion.calc.CalcFunction;
import com.gtech.promotion.calc.CalcResult;
import com.gtech.promotion.calc.model.CalcActivityIncentive;
import com.gtech.promotion.calc.model.CalcShoppingCart;
import com.gtech.promotion.calc.model.CalcShoppingCartItem;
import com.gtech.promotion.code.activity.LimitationCodeEnum;
import com.gtech.promotion.helper.ActivityIncentiveLimited;
import com.gtech.promotion.helper.RedisOpsHelper;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.Deque;
import java.util.Iterator;
import java.util.List;

/**
 * 原子函数：促销范围(01)
 * 
 */
public abstract class CalcFunction01 implements CalcFunction {

    /*
     * (non-Javadoc)
     * @see com.gtech.promotion.calc.CalcFunction#calc(com.gtech.promotion.calc.model.ICalcPromoObject, com.gtech.promotion.calc.model.CalcActivityIncentive, java.util.Deque, 
     * com.gtech.promotion.calc.function.FunctionParamMap, com.gtech.promotion.calc.CalcFunction)
     */
    @Override
    public CalcResult calc(CalcShoppingCart promoObject, CalcActivityIncentive incentive, Deque<FunctionEnum> funcList, FunctionParamMap paramMap, CalcFunction caller) {
        
        if (promoObject == null || CollectionUtils.isEmpty(promoObject.getCalcShoppingCartItemList())) {

            return CalcResult.ERROR_DEFAULT;
        }

        // 循环判断当前sku是否可以参加当前活动id（互斥叠加）
        List<CalcShoppingCartItem> cscItemList = promoObject.getCalcShoppingCartItemList();

        if (!this.checkActivitySku(incentive, cscItemList, true)) {
            return CalcResult.SUCCESS_FALSE;
        }

        this.checkLimit(incentive.getCalcActivity(), LimitationCodeEnum.USER_SKU_COUNT, cscItemList);
        this.checkLimit(incentive.getCalcActivity(), LimitationCodeEnum.SKU_COUNT, cscItemList);

        if (CollectionUtils.isEmpty(cscItemList)){
            return CalcResult.SUCCESS_FALSE;
        }

        return CalcResult.SUCCESS_TRUE;
    }

    private void checkLimit(CalcActivity calcActivity, LimitationCodeEnum limitedCode, List<CalcShoppingCartItem> cscItemList) {

        RedisOpsHelper redisOpsHelper = calcActivity.getRedisOpsHelper();
        ActivityIncentiveLimited incentiveLimited = calcActivity.getIncentiveLimited();
        if (!incentiveLimited.haveLimited(limitedCode)) {
            return;
        }
        BigDecimal remainingValue;
        Iterator<CalcShoppingCartItem> iterator = cscItemList.iterator();
        while (iterator.hasNext()){
            CalcShoppingCartItem shoppingCartItem = iterator.next();
            remainingValue = redisOpsHelper.getLimitedValue(limitedCode, calcActivity.getTenantCode(), calcActivity.getActivityCode(), calcActivity.getMemberCode(), shoppingCartItem.getSkuCode());
            if (null == remainingValue) {
                remainingValue = incentiveLimited.getLimitedValue(limitedCode);
            }
            if (remainingValue.compareTo(ConvertUtils.toBigDecimal(shoppingCartItem.getQuantity())) < 0) {
                shoppingCartItem.setCurrentActivityNull(calcActivity.getActivityCode());
                shoppingCartItem.setActivityExpr(shoppingCartItem.getActivityExpr().replace(calcActivity.getActivityCode(), "0"));
                iterator.remove();
            }
        }
    }

}
