/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.calc.function;

import java.util.Deque;

import org.springframework.stereotype.Component;

import com.gtech.promotion.calc.CalcFunction;
import com.gtech.promotion.calc.CalcResult;
import com.gtech.promotion.calc.model.CalcActivityIncentive;
import com.gtech.promotion.calc.model.CalcShoppingCart;
import com.gtech.promotion.code.activity.FuncTypeEnum;

import lombok.Getter;

/**
 * 原子函数：条件参数(03) - 无(01)
 * 
 */
@Component
public class CalcFunction0301 implements CalcFunction {

    @Getter
    private FunctionEnum function = FunctionEnum.F0301;

    static {
        // Registers the current atomic function
        CalcFunctionRegister.register(FunctionEnum.F0301, new CalcFunction0301());
    }

    /**
     * 构造函数私有化，不允许自己生成该对象
     */
    private CalcFunction0301() {
        // No codes
    }

    /*
     * (non-Javadoc)
     * @see com.gtech.promotion.calc.CalcFunction#calc(com.gtech.promotion.calc.model.ICalcPromoObject, com.gtech.promotion.calc.model.CalcActivityIncentive, java.util.Deque, 
     * com.gtech.promotion.calc.function.FunctionParamMap, com.gtech.promotion.calc.CalcFunction)
     */
    @Override
    public CalcResult calc(CalcShoppingCart promoObject, CalcActivityIncentive incentive, Deque<FunctionEnum> funcList, FunctionParamMap paramMap, CalcFunction caller) {

        try {
            if (!FuncTypeEnum.CONDITION.equals(caller.getFunction().type())) {
                return CalcResult.ERROR_DEFAULT;
            }

            if (FunctionEnum.F0201.equals(caller.getFunction())) {
                //目前无条件只有单品  活动奖励次数只能是1
                incentive.setIncentiveTimes(1);
                return CalcResult.SUCCESS_TRUE;
            }

            return CalcResult.ERROR_DEFAULT;

        } catch (Exception e) {
            return CalcResult.ERROR_DEFAULT;
        }
    }

}
