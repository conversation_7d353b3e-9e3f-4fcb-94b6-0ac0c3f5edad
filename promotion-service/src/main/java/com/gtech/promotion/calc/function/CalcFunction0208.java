/**
 * Copyright (c) 2025 GTech All Rights Reserved.
 */
package com.gtech.promotion.calc.function;

import java.math.BigDecimal;
import java.util.Deque;

import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSON;
import com.gtech.commons.utils.ConvertUtils;
import com.gtech.promotion.calc.CalcFunction;
import com.gtech.promotion.calc.CalcResult;
import com.gtech.promotion.calc.model.CalcActivityIncentive;
import com.gtech.promotion.calc.model.CalcShoppingCart;

import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

/**
 * 原子函数：促销条件(02) - 商品范围可重复打折扣(08)
 * 
 * 可重复打折扣条件函数，支持设置购买件数和赠送件数，可重复应用
 */
@Component
@Slf4j
public class CalcFunction0208 extends CalcFunction02 {

    @Getter
    private FunctionEnum function = FunctionEnum.F0208;

    static {
        // 注册当前原子函数
        CalcFunctionRegister.register(FunctionEnum.F0208, new CalcFunction0208());
    }

    /**
     * 构造函数私有化，不允许自己生成该对象
     */
    private CalcFunction0208() {
        // No codes
    }


}