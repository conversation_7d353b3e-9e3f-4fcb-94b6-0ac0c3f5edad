/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.calc;

import com.alibaba.fastjson.JSON;
import com.gtech.commons.exception.Exceptions;
import com.gtech.commons.utils.BeanCopyUtils;
import com.gtech.promotion.calc.function.CalcFunctionRegister;
import com.gtech.promotion.calc.function.FunctionEnum;
import com.gtech.promotion.calc.function.FunctionParamMap;
import com.gtech.promotion.calc.model.*;
import com.gtech.promotion.code.activity.ItemScopeTypeEnum;
import com.gtech.promotion.dao.model.activity.ActivityFunctionParamRankModel;
import com.gtech.promotion.dao.model.activity.FunctionParamModel;
import com.gtech.promotion.dao.model.activity.TemplateFunctionModel;
import com.gtech.promotion.dao.model.activity.TemplateModel;
import com.gtech.promotion.dto.cache.ActivityCacheDTO;
import com.gtech.promotion.exception.ErrorCodes;
import com.gtech.promotion.utils.ExpressionHelper;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;

import java.util.*;
import java.util.Map.Entry;


/**
 * 促销模板维度计算
 *
 * <AUTHOR>
 * @Date 2019-11-18
 */
@Getter
@Setter
@Slf4j
public class CalcTemplate {

    // 模板函数列表
    private LinkedList<FunctionEnum> functionList;

    // 促销活动模板函数层级列表()
    private List<CalcActivityFuncRank> calcFuncRankList;

    // 当前模板的对应的促销活动
    private CalcActivity calcActivity;

    public CalcTemplate() {

    }

    public CalcTemplate(CalcTemplate source) {

        BeanCopyUtils.copyProps(source, this);

        this.calcActivity = new CalcActivity(source.calcActivity);
    }

    /**
     * 根据活动ID和模板ID，加载相信的促销活动模板信息以及相关的活动参数信息
     * 
     * @param activityId 活动ID
     * @param templateId 模板ID
     */
    public void loadTemplate(ActivityCacheDTO ruleCacheDTO, CalcActivity calcActivity){

        this.calcActivity = calcActivity;

        // 获取当前活动模板基本信息
        TemplateModel templateModel = ruleCacheDTO.getPromoTemplate();
        if (null == templateModel){
            throw Exceptions.fail(ErrorCodes.CALC_TEMPLATE_NOT_FOUND, JSON.toJSONString(calcActivity));
        }

        // 获取当前促销模板函数的基本信息
        List<TemplateFunctionModel> tempFuncList = ruleCacheDTO.getPromoTemplateFunctions();
        if (CollectionUtils.isEmpty(tempFuncList)){
            throw Exceptions.fail(ErrorCodes.CALC_TEMPLATE_FUNCTION_NOT_FOUND, JSON.toJSONString(calcActivity));
        }

        // 模板函数信息
        this.functionList = new LinkedList<>();
        for (TemplateFunctionModel templateFunction : tempFuncList){

            // 添加到链表末尾
            this.functionList.addLast(FunctionEnum.code2Enum(templateFunction.getFunctionCode()));
        }

        // 加载当前模板的活动参数信息
        this.loadActivityFunctionParams(ruleCacheDTO);
    }

    public CalcResult calc(CalcShoppingCart calcSCart){
        log.info("into CalcTemplate calc");
        CalcResult calcResult = CalcResult.ERROR_DEFAULT;

        if (null == calcSCart || CollectionUtils.isEmpty(this.calcFuncRankList) || CollectionUtils.isEmpty(this.functionList)){
            return calcResult;
        }

        if (ItemScopeTypeEnum.BY_SPU.equalsCode(this.calcActivity.getItemScopeType())) {
            log.info("into spu scope!");
            Map<String, List<CalcShoppingCartItem>> scItemsMaps = scItemsMapsBySpu(calcSCart.getCalcShoppingCartItemList());
            for(Entry<String, List<CalcShoppingCartItem>> entry : scItemsMaps.entrySet()) {
                CalcShoppingCart csc = new CalcShoppingCart(calcSCart);
                csc.setCalcShoppingCartItemList(entry.getValue());
                
                CalcTemplate calcTemplate = new CalcTemplate(this);
                if (CalcResult.SUCCESS_TRUE.equals(calcTemplate.calcByScope(csc))){
                    this.calcActivity.setGiveawayLimitMax(calcTemplate.getCalcActivity().getGiveawayLimitMax());
                    this.calcActivity.addRewards(calcTemplate.getCalcActivity());
                    calcResult = CalcResult.SUCCESS_TRUE;
                } else {
                    this.calcActivity.addRewards(calcTemplate.getCalcActivity());
                }
            }

            return calcResult;
        } else {
            return this.calcByScope(calcSCart);
        }
    }

    private CalcResult calcByScope(CalcShoppingCart calcShoppingCart){
        log.info("into calcByScope.");
        // 获取最高函数层级对象
        CalcActivityFuncRank funcRank = this.getMaxActivityFuncRank(Integer.MAX_VALUE);
        while (funcRank != null){

            CalcResult calcResult = this.calc(calcShoppingCart, funcRank.getFuncRankId(), new FunctionParamMap(funcRank.getFuncParamList()));
            if (CalcResult.SUCCESS_TRUE.equals(calcResult)){
                return CalcResult.SUCCESS_TRUE;
            }

            // 获取最高函数层级对象
            funcRank = this.getMaxActivityFuncRank(funcRank.getRankParam());
        }

        return CalcResult.ERROR_DEFAULT;
    }

    /**
     * 根据促销模板计算
     * 
     * @param calcShoppingCart 促销对象
     * @param funcRankId 生效的模板函数层级ID
     * @param paramMap 促销参数
     */
    private CalcResult calc(CalcShoppingCart calcShoppingCart, String funcRankId, FunctionParamMap paramMap){

        CalcResult calcResult = CalcResult.ERROR_DEFAULT;

        LinkedList<FunctionEnum> funcListTemp = new LinkedList<>(this.functionList);
        CalcActivityIncentive activityIncentive = new CalcActivityIncentive(calcActivity, calcActivity.getMemberCode(), funcRankId);

        // 开始计算
        while (CollectionUtils.isNotEmpty(funcListTemp)){

            //原子函数注册管理器
            CalcFunction funcCalc = CalcFunctionRegister.getInstance(funcListTemp.removeFirst());
            switch (funcCalc.getFunction().type()) {

                case SCOPE:
                    //进入原子计算函数 010x
                    calcResult = funcCalc.calc(calcShoppingCart, activityIncentive, funcListTemp, paramMap, null);
                    if (!CalcResult.SUCCESS_TRUE.equals(calcResult)){
                        // 计算失败，直接返回
                        return CalcResult.SUCCESS_FALSE;
                    }
                    break;

                case CONDITION:
                    //进入原子计算函数 020x -> 030x
                    calcResult = funcCalc.calc(calcShoppingCart, activityIncentive, funcListTemp, paramMap, null);
                    calcActivity.setBeforeAmount(calcShoppingCart.getPromoAmount());
                    calcActivity.setAfterAmount(calcShoppingCart.getPromoAmount());
                    if (!CalcResult.SUCCESS_TRUE.equals(calcResult)){
                        calcActivity.setNeedMoreAmount(activityIncentive.getNeedMoreAmount());
                        calcActivity.setNeedMoreUnit(activityIncentive.getNeedMoreUnit());
                        calcActivity.setFailedReason(activityIncentive.getFailedReason());
                        // 计算失败，直接返回
                        return CalcResult.SUCCESS_FALSE;
                    }
                    break;

                case INCENTIVE:
                    //进入原子计算函数 040x
                    calcResult = funcCalc.calc(calcShoppingCart, activityIncentive, funcListTemp, paramMap, null);
                    if (!CalcResult.SUCCESS_TRUE.equals(calcResult)){
                        // 当前sku取消参加当前活动的计算（可叠加但是03条件不满足的）活动和商品对应关系保留，因为如果这个活动是最后一个，出参要显示，如果这个活动后面还有低优先级的活动满足再删除
                        cancelExpressSku(calcShoppingCart);
                        calcActivity.setFailedReason(activityIncentive.getFailedReason());
                        // 计算失败，直接返回
                        return CalcResult.SUCCESS_FALSE;
                    }
                    calcActivity.setRewardTimes(activityIncentive.getIncentiveTimes());
                    break;

                default:
                    return CalcResult.ERROR_DEFAULT;

            }
        }

        // 当前模板的促销活动计算OK，计入有效促销奖励
        this.calcActivity.setEffectiveFlag(true);
        calcShoppingCart.addActivityIncentive(activityIncentive);
        this.calcActivity.setGiveaways(calcShoppingCart.getCalcActivity().getGiveaways());
        this.calcActivity.setConditionUnit(activityIncentive.getConditionUnit());
        this.calcActivity.setConditionValue(activityIncentive.getConditionValue());
        this.calcActivity.setGiveawayLimitMax(activityIncentive.getGiftLimitMax());
        this.calcActivity.setAfterAmount(calcShoppingCart.getPromoAmount());
        return calcResult;
    }

    private Map<String, List<CalcShoppingCartItem>> scItemsMapsBySpu(List<CalcShoppingCartItem> cscItems) {

        Map<String, List<CalcShoppingCartItem>> scItemsMaps = new HashMap<>();
        for(CalcShoppingCartItem scItem : cscItems) {
            List<CalcShoppingCartItem> cscItemsSub = scItemsMaps.get(scItem.getProductCode());
            if (null == cscItemsSub) {
                cscItemsSub = new ArrayList<>();
                scItemsMaps.put(scItem.getProductCode(), cscItemsSub);
            }
            cscItemsSub.add(scItem);
        }

        return scItemsMaps;
    }

    /**
     * 加载当前模板的活动参数信息
     */
    private void loadActivityFunctionParams(ActivityCacheDTO ruleCacheDTO){

        // 活动模板函数层级参数信息
        List<ActivityFunctionParamRankModel> funcRankVoList = ruleCacheDTO.getPromoFuncRanks();
        if (CollectionUtils.isEmpty(funcRankVoList)){
            return;
        }

        this.calcFuncRankList = new ArrayList<>();

        for (ActivityFunctionParamRankModel funcRankVo : funcRankVoList){
            CalcActivityFuncRank calcActivityFuncRank = new CalcActivityFuncRank();

            calcActivityFuncRank.setFuncRankId(funcRankVo.getId());
            calcActivityFuncRank.setRankParam(funcRankVo.getRankParam());

            List<FunctionParamModel> activityFuncParamVoList = ruleCacheDTO.getPromoFuncParams();
            if (!CollectionUtils.isEmpty(activityFuncParamVoList)){
                List<CalcActivityFuncParam> funcParamList = new ArrayList<>();

                for (FunctionParamModel activityFuncParamVo : activityFuncParamVoList){
                    if (funcRankVo.getId().equals(activityFuncParamVo.getRankId())){

                        CalcActivityFuncParam calcActivityFuncParam = new CalcActivityFuncParam();

                        calcActivityFuncParam.setFuncCode(activityFuncParamVo.getFunctionCode());
                        calcActivityFuncParam.setFuncType(activityFuncParamVo.getFunctionType());
                        calcActivityFuncParam.setParamType(activityFuncParamVo.getParamType());
                        calcActivityFuncParam.setParamValue(activityFuncParamVo.getParamValue());
                        calcActivityFuncParam.setParamUnit(activityFuncParamVo.getParamUnit());

                        funcParamList.add(calcActivityFuncParam);
                    }
                }

                calcActivityFuncRank.setFuncParamList(funcParamList);
            }

            this.calcFuncRankList.add(calcActivityFuncRank);
        }
    }

    private void cancelExpressSku(CalcShoppingCart calePromoObject){
        List<CalcShoppingCartItem> calcShoppingCartItemList1 = calePromoObject.getCalcShoppingCartItemList();
        for (CalcShoppingCartItem item : calcShoppingCartItemList1){
            ExpressionHelper.cancelActivitySku(item.getSkuCode());
        }
    }

    /**
     * <方法描述> 获取最高函数层级对象
     * 
     * @param rankParam
     * @return
     */
    protected CalcActivityFuncRank getMaxActivityFuncRank(int rankParam){

        CalcActivityFuncRank funcRankResult = null;

        for (CalcActivityFuncRank funcRank : this.calcFuncRankList){
            if (funcRank.getRankParam() >= rankParam){
                continue;
            }

            if (null == funcRankResult || funcRank.getRankParam() > funcRankResult.getRankParam()){//相等的时候返回null
                funcRankResult = funcRank;
            }
        }

        return funcRankResult;
    }

}
