/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * <PERSON>ECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.calc.function;

import java.util.Deque;
import java.util.List;

import org.springframework.util.CollectionUtils;

import com.gtech.promotion.calc.CalcFunction;
import com.gtech.promotion.calc.CalcResult;
import com.gtech.promotion.calc.model.CalcActivityIncentive;
import com.gtech.promotion.calc.model.CalcShoppingCartItem;
import com.gtech.promotion.calc.model.CalcShoppingCart;
import com.gtech.promotion.code.activity.FuncTypeEnum;
import com.gtech.promotion.utils.ExpressionHelper;

/**
 * 原子函数：促销条件(02)
 * 
 */
public abstract class CalcFunction02 implements CalcFunction{

    /*
     * (non-Javadoc)
     * @see com.gtech.promotion.calc.CalcFunction#calc(com.gtech.promotion.calc.model.ICalcPromoObject, com.gtech.promotion.calc.model.CalcActivityIncentive, java.util.Deque, 
     * com.gtech.promotion.calc.function.FunctionParamMap, com.gtech.promotion.calc.CalcFunction)
     */
    @Override
    public CalcResult calc(CalcShoppingCart promoObject, CalcActivityIncentive incentive, Deque<FunctionEnum> funcList, FunctionParamMap paramMap, CalcFunction caller) {

        CalcResult calcResult = CalcResult.ERROR_DEFAULT;

        if (CollectionUtils.isEmpty(funcList)){
            // 当前原子函数必须拥有一个参数函数，且符合Param03Enum
            return calcResult;
        }

        // 进入下一步计算（当前函数处理完成后，必须有后续函数处理，且符合Param03Enum）
        CalcFunction funcCalc = CalcFunctionRegister.getInstance(funcList.removeFirst());
        if (FuncTypeEnum.PARAM.equals(funcCalc.getFunction().type())) {
            //进入原子计算函数 030x
            calcResult = funcCalc.calc(promoObject, incentive, funcList, paramMap, this);
            if (!CalcResult.SUCCESS_TRUE.equals(calcResult)){
                // 当前sku取消参加当前活动的计算（不满足活动条件）
                List<CalcShoppingCartItem> calcShoppingCartItemList = promoObject.getCalcShoppingCartItemList();
                for (CalcShoppingCartItem item : calcShoppingCartItemList){
                    ExpressionHelper.cancelActivitySku(item.getSkuCode());
                }
            }
        }

        return calcResult;
    }

}
