/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.calc.function;

import java.math.BigDecimal;
import java.util.Deque;
import java.util.List;

import org.springframework.stereotype.Component;

import com.gtech.promotion.calc.CalcConstants;
import com.gtech.promotion.calc.CalcFunction;
import com.gtech.promotion.calc.CalcResult;
import com.gtech.promotion.calc.model.CalcActivityIncentive;
import com.gtech.promotion.calc.model.CalcShoppingCart;
import com.gtech.promotion.calc.model.CalcShoppingCartItem;
import com.gtech.promotion.exception.ErrorCodes;

import lombok.Getter;

/**
 * 买A送A
 * 原子函数：促销奖励(04) - 满送(07)买送（同类）
 * 
 */
@Component
public class CalcFunction0407 extends CalcFunction04{

    @Getter
    private FunctionEnum function = FunctionEnum.F0407;

    static {
        // Registers the current atomic function
        CalcFunctionRegister.register(FunctionEnum.F0407, new CalcFunction0407());
    }

    /**
     * 构造函数私有化，不允许自己生成该对象
     */
    private CalcFunction0407(){
        // No codes
    }

    /*
     * (non-Javadoc)
     * @see com.gtech.promotion.calc.CalcFunction#calc(com.gtech.promotion.calc.model.ICalcPromoObject, com.gtech.promotion.calc.model.CalcActivityIncentive, java.util.Deque, 
     * com.gtech.promotion.calc.function.FunctionParamMap, com.gtech.promotion.calc.CalcFunction)
     */
    @Override
    public CalcResult calc(CalcShoppingCart promoObject, CalcActivityIncentive incentive, Deque<FunctionEnum> funcList, FunctionParamMap paramMap, CalcFunction caller) {
        try{
            //长度不符合,即(多于一个,活动不是商品的范围池不是一个)//买A赠A
            if (promoObject.getCalcActivity().getSeqNums().size() != 1){
                incentive.setFailedReason(ErrorCodes.ACTIVITY_PARAM_ERROR);
                return CalcResult.ERROR_DEFAULT;
            }
            int precision = promoObject.getCalcActivity().getPrecison();
			BigDecimal powerPrecison = promoObject.getCalcActivity().getPowerPrecison();
            loadCommonValue(promoObject);
            List<CalcShoppingCartItem> itemList = promoObject.getCalcShoppingCartItemList();
            //条件参数:每满数量
            Integer conditionNum = Integer.parseInt(paramMap.getFuncParamValue(FunctionEnum.F0302.code()));
            //奖励参数:奖励数量
            Integer incentiveNum = Integer.parseInt(paramMap.getFuncParamValue(this.function.code()));
            //商品总数量
            Integer quantity = itemQuantity(itemList);
            //商品的数量小于或者等于条件的时候,直接false
            if (quantity <= conditionNum){
                incentive.setFailedReason(ErrorCodes.ACTIVITY_LIMIT_CONDITION_QUANTITY);
                return CalcResult.ERROR_DEFAULT;
            }
            //奖励次数和参与优惠商品统计
            Integer activityProductsTimes = countTimes(incentive, conditionNum, incentiveNum, quantity);
            //总优惠金额
            BigDecimal incentiveAmount = countReduceAmount(itemList, activityProductsTimes,precision).setScale(precision, CalcConstants.DEFAULT_ROUND);
			if (powerPrecison != null) {
				incentiveAmount = incentiveAmount.divide(powerPrecison).setScale(0, CalcConstants.ROUND_DOWN).multiply(powerPrecison);
			}
            //分摊设置商品和商品下活动金额
            BigDecimal scale = getScale(incentiveAmount, promoObject.getPromoAmount());

            if (!this.checkLimitedAndCombine(incentive, promoObject.getCalcShoppingCartItemList(), activityProductsTimes, incentiveAmount)) {
                return CalcResult.SUCCESS_LIMITED;
            }

			handlerShoppingCartItemList(promoObject.getCalcShoppingCartItemList(), incentive, scale, precision, incentiveAmount, powerPrecison);
            return CalcResult.SUCCESS_TRUE;

        }catch (RuntimeException e){
            return CalcResult.ERROR_DEFAULT;
        }
    }

    /**
     * 所有商品总数
     * 
     * @param itemList
     */
    private Integer itemQuantity(List<CalcShoppingCartItem> itemList){
        Integer quantity = 0;
        for (CalcShoppingCartItem calcShoppingCartItem : itemList){
            quantity += calcShoppingCartItem.getQuantity();
        }
        return quantity;
    }

    /**
     * 奖励次数和参与优惠商品统计
     */
    private Integer countTimes(CalcActivityIncentive incentive, Integer conditionNum, Integer incentiveNum, Integer quantity) {

        Integer incentiveTimes = 0;//奖励次数
        Integer activityProductsTimes = 0;//可参与的奖励商品
        if (quantity % (conditionNum + incentiveNum) > conditionNum) {
            incentiveTimes = quantity / (conditionNum + incentiveNum) + 1;
            activityProductsTimes = quantity - incentiveTimes * conditionNum;
        } else {
            incentiveTimes = quantity / (conditionNum + incentiveNum);
            activityProductsTimes = incentiveNum * incentiveTimes;
        }
        incentive.setIncentiveTimes(incentiveTimes);
        //设置奖励为买A送A
        return activityProductsTimes;
    }

}
