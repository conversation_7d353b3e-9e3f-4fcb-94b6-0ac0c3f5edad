/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.calc.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.gtech.commons.exception.ErrorCode;
import com.gtech.commons.utils.BeanCopyUtils;
import com.gtech.commons.utils.EnumUtil;
import com.gtech.promotion.calc.CalcActivity;
import com.gtech.promotion.code.activity.FuncTypeEnum;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.math.BigDecimal;

/**
 * 促销活动生效奖励信息
 *
 */
@Getter
@Setter
@ToString
public class CalcActivityIncentive {

    private CalcActivity calcActivity;
    @JSONField(serialize = false)
    public String getTemplateCode() {
        return this.calcActivity.getTemplateCode();
    }

    // Member user code
    private String memberCode;

    // 生效的模板函数层级ID
    private String funcRankId;

    // 赠品赠送最大限制数量
    private String giftLimitMax;

    // 条件值单位: 01-金额  02-数量
    private String conditionUnit;

    // 条件值
    private BigDecimal conditionValue;

    // Activity use failed reason
    private ErrorCode failedReason;

    // 还差多少满足活动
    private String needMoreAmount;

    // 数值单位
    private String needMoreUnit;

    // 活动生效奖励次数
    private Integer incentiveTimes;

    // 参与促销的商品数量
    private Integer promoQuantity;
    
    public CalcActivityIncentive(CalcActivityIncentive source) {
        
        BeanCopyUtils.copyProps(source, this);
        this.calcActivity = new CalcActivity(source.getCalcActivity());
    }

    /**
     * 构造函数
     */
    public CalcActivityIncentive(CalcActivity calcActivity, String memberCode, String funcRankId) {
        this.calcActivity = calcActivity;
        this.memberCode = memberCode;
        this.funcRankId = funcRankId;
    }

    /**
     * Get the promotion IncentiveEnum
     */
    public FuncTypeEnum.IncentiveEnum getPromoScope(){

        return EnumUtil.code2Enum(FuncTypeEnum.IncentiveEnum.class, this.getTemplateCode().substring(12,16));
    }

    public String getActivityCode() {

        return this.calcActivity.getActivityCode();
    }
}
