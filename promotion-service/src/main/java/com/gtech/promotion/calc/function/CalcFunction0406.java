/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.calc.function;

import com.alibaba.fastjson.JSON;
import com.gtech.promotion.calc.CalcActivity;
import com.gtech.promotion.calc.CalcFunction;
import com.gtech.promotion.calc.CalcResult;
import com.gtech.promotion.calc.CalcTemplate;
import com.gtech.promotion.calc.model.CalcActivityFuncRank;
import com.gtech.promotion.calc.model.CalcActivityIncentive;
import com.gtech.promotion.calc.model.CalcShoppingCart;
import com.gtech.promotion.calc.model.CalcShoppingCartItem;
import com.gtech.promotion.code.activity.NeedMoreUnitEnum;
import com.gtech.promotion.vo.bean.Giveaway;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.Deque;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 原子函数：促销奖励(04) - 送赠品(06)
 * 
 */
@Slf4j
@Component
public class CalcFunction0406 extends CalcFunction04 {

    @Getter
    private FunctionEnum function = FunctionEnum.F0406;

    static {
        // Registers the current atomic function
        CalcFunctionRegister.register(FunctionEnum.F0406, new CalcFunction0406());
    }

    /**
     * 构造函数私有化，不允许自己生成该对象
     */
    private CalcFunction0406() {
        // No codes
    }

    /*
     * (non-Javadoc)
     * @see com.gtech.promotion.calc.CalcFunction#calc(com.gtech.promotion.calc.model.ICalcPromoObject, com.gtech.promotion.calc.model.CalcActivityIncentive, java.util.Deque, 
     * com.gtech.promotion.calc.function.FunctionParamMap, com.gtech.promotion.calc.CalcFunction)
     */
    @Override
    public CalcResult calc(CalcShoppingCart promoObject, CalcActivityIncentive incentive, Deque<FunctionEnum> funcList, FunctionParamMap paramMap, CalcFunction caller) {
        log.info("into 0406 calc!promoObject:{}", JSON.toJSONString(promoObject));
        try {
            if (!this.checkLimitedAndCombine(incentive, promoObject.getCalcShoppingCartItemList(), 1, BigDecimal.ZERO)) {
                return CalcResult.SUCCESS_LIMITED;
            }

            setCondition(incentive, paramMap);
            String paramValue = paramMap.getFuncParamValue(this.function.code());
            int incentiveTimes = incentive.getIncentiveTimes().intValue();
            incentive.setGiftLimitMax(paramValue);
            log.info("incentiveTimes:{},calcShoppingCartItemList size:{}",incentiveTimes,promoObject.getCalcShoppingCartItemList().size());
            for (int i = 0; i < promoObject.getCalcShoppingCartItemList().size(); i++) {
                CalcShoppingCartItem calcShoppingCartItem = promoObject.getCalcShoppingCartItemList().get(i);
                CalcActivity calcActivity = calcShoppingCartItem.getCurrentActivity(incentive.getActivityCode());
                log.info("into getGiveawayByRank.");
                getGiveawayByRank(calcActivity,incentive.getFuncRankId());
                List<Giveaway> giveaways = calcActivity.getGiveaways();
                for (Giveaway giveaway : giveaways) {//重置所有赠品数量=原赠品数量*奖励次数
                    giveaway.setGiveawayNum(giveaway.getGiveawayNum() * incentiveTimes);
                }
                calcActivity.setGiveawayLimitMax(String.valueOf(giveaways.size()));
                calcActivity.setGiveaways(giveaways);
                calcActivity.setBeforeAmount(calcShoppingCartItem.getPromoAmount());
                calcActivity.setAfterAmount(calcShoppingCartItem.getPromoAmount());
                calcActivity.setEffectiveFlag(true);
                promoObject.getCalcActivity().setGiveaways(giveaways);
                promoObject.getCalcActivity().setGiveawayLimitMax(String.valueOf(giveaways.size()));
            }
            return CalcResult.SUCCESS_TRUE;

        } catch (Exception e) {
            return CalcResult.ERROR_DEFAULT;
        }
    }

    public void getGiveawayByRank(CalcActivity calcActivity,String rankId){
        List<Giveaway> giveaways = calcActivity.getGiveaways();
        CalcTemplate calcTemplate  = calcActivity.getCalcTemplate();
        if(null == calcTemplate){
            return;
        }

        List<CalcActivityFuncRank> calcFuncRankList = calcTemplate.getCalcFuncRankList();
        if(CollectionUtils.isEmpty(calcFuncRankList)){
            return;
        }

        List<CalcActivityFuncRank> currentFuncRankList = calcFuncRankList.stream().filter(p-> p.getFuncRankId().equals(rankId)).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(currentFuncRankList)){
            return;
        }

        CalcActivityFuncRank calcActivityFuncRank = currentFuncRankList.get(0);
        calcActivity.setGiveaways(giveaways.stream().filter(p-> p.getRankParam().equals(calcActivityFuncRank.getRankParam())).collect(Collectors.toList()));
    }



    //送赠品要在券之后再判断是否满足活动条件，先把赠品的活动条件值返回到出参给使用者判断，临时上hotfix
    private void setCondition(CalcActivityIncentive incentive, FunctionParamMap paramMap) {

        if (paramMap.getFuncParam(FunctionEnum.F0302.code()) != null) {

            incentive.setConditionUnit(NeedMoreUnitEnum.PIECE.code());
            incentive.setConditionValue(new BigDecimal(paramMap.getFuncParamValue(FunctionEnum.F0302.code())));

        } else if (paramMap.getFuncParam(FunctionEnum.F0303.code()) != null) {

            incentive.setConditionUnit(NeedMoreUnitEnum.YUAN.code());
            incentive.setConditionValue(new BigDecimal(paramMap.getFuncParamValue(FunctionEnum.F0303.code())));
        }
    }

}
