/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WA<PERSON>RANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.calc.function;

import com.gtech.promotion.code.activity.FuncTypeEnum;

/**
 * FunctionEnum
 *
 * <AUTHOR>
 * @Date 2020-03-23
 */
public enum FunctionEnum{

    F0101("0101", FuncTypeEnum.SCOPE, "单品"),
    F0102("0102", FuncTypeEnum.SCOPE, "商品范围"),
    F0103("0103", FuncTypeEnum.SCOPE, "订单"),
    F0104("0104", FuncTypeEnum.SCOPE, "单件"),

    F0201("0201", FuncTypeEnum.CONDITION, "无"),
    F0202("0202", FuncTypeEnum.CONDITION, "满"),
    F0203("0203", FuncTypeEnum.CONDITION, "每满"),
    F0204("0204", FuncTypeEnum.CONDITION, "每第"),
    F0205("0205", FuncTypeEnum.CONDITION, "捆绑"),
    F0206("0206", FuncTypeEnum.CONDITION, "单品第"),
    F0207("0207", FuncTypeEnum.CONDITION, "单品满"),
    F0208("0208", FuncTypeEnum.CONDITION, "单品每第"),

    F0301("0301", FuncTypeEnum.PARAM, "None"),
    F0302("0302", FuncTypeEnum.PARAM, "Quantity"),
    F0303("0303", FuncTypeEnum.PARAM, "Amount"),

    F0401("0401", FuncTypeEnum.INCENTIVE, "减金额"),
    F0402("0402", FuncTypeEnum.INCENTIVE, "打折扣"),
    F0403("0403", FuncTypeEnum.INCENTIVE, "单件固定金额"),
    F0404("0404", FuncTypeEnum.INCENTIVE, "组合固定金额"),
    F0405("0405", FuncTypeEnum.INCENTIVE, "Postage percent off"),
    F0406("0406", FuncTypeEnum.INCENTIVE, "Send gift"),
    F0407("0407", FuncTypeEnum.INCENTIVE, "Send same product"),
    F0408("0408", FuncTypeEnum.INCENTIVE, "Send other product"),
    F0409("0409", FuncTypeEnum.INCENTIVE, "Send coupon"),
    F0411("0411", FuncTypeEnum.INCENTIVE, "每件不同特价"),
    F0412("0412", FuncTypeEnum.INCENTIVE, "买A范围商品B范围商品减金额"),
    F0413("0413", FuncTypeEnum.INCENTIVE, "买A范围商品B范围商品打折扣"),
    F0414("0414", FuncTypeEnum.INCENTIVE, "每件减固定金额"),
    F0415("0415", FuncTypeEnum.INCENTIVE, "Postage discount"),
    F0416("0416", FuncTypeEnum.INCENTIVE, "免费"),
    ;

    FunctionEnum(String code, FuncTypeEnum type, String desc){
        this.code = code;
        this.type = type;
        this.desc = desc;
    }

    /**
     * Code==>Type
     */
    public static FunctionEnum code2Enum(String code){
        FunctionEnum[] values = FunctionEnum.values();
        for (FunctionEnum type : values){
            if (type.code().equals(code)){
                return type;
            }
        }
        return null;
    }

    /**
     * Code==>Type
     */
    public static FuncTypeEnum code2Type(String code){
        FunctionEnum[] values = FunctionEnum.values();
        for (FunctionEnum type : values){
            if (type.code().equals(code)){
                return type.type();
            }
        }
        return null;
    }

    /**
     * Code==>Description
     */
    public static String code2Desc(String code){
        FunctionEnum[] values = FunctionEnum.values();
        for (FunctionEnum type : values){
            if (type.code().equals(code)){
                return type.desc;
            }
        }
        return null;
    }

    public static boolean canRank(String code){
        return F0202.equalsCode(code) || F0206.equalsCode(code) || F0208.equalsCode(code);
    }

    private String code;
    private FuncTypeEnum type;
    private String desc;

    public String code() {
        return code;
    }
    public FuncTypeEnum type() {
        return type;
    }
    public String desc() {
        return desc;
    }
    public boolean isScopeFunction() {

        return FuncTypeEnum.SCOPE.equals(type);
    }
    public boolean isConditionFunction() {

        return FuncTypeEnum.CONDITION.equals(type);
    }
    public boolean isParamFunction() {

        return FuncTypeEnum.PARAM.equals(type);
    }
    public boolean isIncentiveFunction() {

        return FuncTypeEnum.INCENTIVE.equals(type);
    }
    public boolean equalsCode(String code) {

        return this.code().equals(code);
    }
}
