/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.calc.function;

import com.google.common.collect.Maps;
import com.gtech.commons.exception.Exceptions;
import com.gtech.promotion.calc.CalcFunction;
import com.gtech.promotion.exception.ErrorCodes;

import java.util.Map;

/**
 * 原子函数注册管理器
 * 
 */
public abstract class CalcFunctionRegister {

    private static final Map<String, CalcFunction> funcMap = Maps.newHashMap();
    
    private CalcFunctionRegister() {
        // No codes.
    }

    /**
     * Register a new CalcFunction instance.
     */
    public static void register(FunctionEnum function, CalcFunction functionInstance) {

        if (funcMap.containsKey(function.code())) {
            throw Exceptions.fail(ErrorCodes.CALC_FUNCTION_DUPLICATED, function.code());
        }

        funcMap.put(function.code(), functionInstance);
    }

    public static void clear() {
        funcMap.clear();
    }

    /**
     * Get CalcFunction instance by FunctionEnum.
     */
    public static CalcFunction getInstance(FunctionEnum function) {

        if (null == function) {
            return null;
        }

        return funcMap.get(function.code());
    }

}
