/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.calc;

import com.gtech.commons.utils.EnumUtil;
import com.gtech.promotion.code.activity.FuncTypeEnum;

/**
 * <功能描述>
 */
public enum TemplateEnum {

    T0101("01", "0101020103010401", "单品无条件减金额", true, false),
    T0102("01", "0101020103010402", "单品无条件打折扣", true, false),
    T0103("01", "0101020103010403", "单品无条件设为每件特价", true, false),
    T0104("01", "0101020103010411", "单品无条件设为每件不同特价", true, false),

    T0201("02", "0102020203020401", "商品范围满数量减金额", true, false),
    T0202("02", "0102020203030401", "商品范围满金额减金额", true, false),
    T0203("02", "0102020303020401", "商品范围每满数量减金额", true, false),
    T0204("02", "0102020303030401", "商品范围每满金额减金额", true, false),
    T0205("02", "0103020203020401", "订单满数量减金额", true, false),
    T0206("02", "0103020203030401", "订单满金额减金额", true, false),
    T0207("02", "0103020303020401", "订单每满数量减金额", true, false),
    T0208("02", "0103020303030401", "订单每满金额减金额", true, false),
    T0209("02", "0102020203030414", "商品范围满金额每件减金额", true, false),
    T0210("02", "0105020203020401", "商品范围内同SPU满数量减金额", true, false),
    T0211("02", "0105020203030401", "商品范围内同SPU满金额减金额", true, false),
    T0301("02", "0102020203020402", "商品范围满数量打折扣", true, false),
    T0302("02", "0102020203030402", "商品范围满金额打折扣", true, false),
    T0303("02", "0102020403020402", "商品范围每第数量打折扣", true, false),
    T0304("02", "0103020203020402", "订单满数量打折扣", true, false),
    T0305("02", "0103020203030402", "订单满金额打折扣", true, false),
    T0306("02", "0102020403020401", "商品范围每第数量减金额", true, false),

    T0401("04", "0102020203020404", "商品范围满数量设为总计特价", true, false),
    T0402("04", "0102020303020404", "商品范围每满数量设为总计特价", true, false),

    T0501("05", "0103020203020405", "订单满数量邮费打折", true, false),
    T0502("05", "0103020203030405", "订单满金额邮费打折", true, false),
    T0503("05", "0102020203020405", "商品范围满数量邮费打折", true, false),
    T0504("05", "0102020203030405", "商品范围满金额邮费打折", true, false),
    T0505("05", "0102020203020415", "商品范围满数量邮费减金额", true, false),
    T0506("05", "0102020203030415", "商品范围满金额邮费减金额", true, false),

    T0601("06", "0103020203020406", "订单满数量送赠品", true, false),
    T0602("06", "0103020203030406", "订单满金额送赠品", true, false),
    T0603("06", "0102020203020406", "商品范围满数量送赠品", true, false),
    T0604("06", "0102020203030406", "商品范围满金额送赠品", true, false),
    T0605("06", "0102020303020406", "商品范围每满数量送赠品", true, false),
    T0606("06", "0102020303030406", "商品范围每满金额送赠品", true, false),
    T0607("06", "0101020703020406", "单品满数量送赠品", true, false),
    T0608("06", "0101020703030406", "单品满金额送赠品", true, false),

    T0701("07", "0102020503020404", "多组商品范围各选1件设为总计特价", true, false),
    T0702("07", "0102020503020401", "多组商品范围各选1件每次减金额", true, false),

    T0801("08", "0102020303020407", "商品范围每满数量送同类商品", true, false),
    T0802("08", "0102020303020408", "商品范围每满数量送不同类商品", true, false),

    T0901("09", "0102020203020409", "商品范围满数量送优惠券", true, false),
    T0902("09", "0102020203030409", "商品范围满金额送优惠券", true, false),


    T1001("10", "0102020203020412", "商品范围A满足数量，商品范围B减金额", true, true),
    T1002("10", "0102020203030412", "商品范围A满足金额，商品范围B减金额", true, true),
    T1003("10", "0102020203020413", "商品范围A满足数量，商品范围B打折扣", true, true),
    T1004("10", "0102020203030413", "商品范围A满足金额，商品范围B打折扣", true, true),

    T1101("11", "0104020103010401", "单件商品用券减金额", false, false),
    T1102("11", "0104020103010402", "单件商品用券打折扣", false, false),

    T1201("12", "0101020603020402", "单品第数量打折扣", true, false),
    T1202("12", "0101020603020401", "单品第数量减金额", true, false),
    T1203("12", "0102020603020402", "商品范围第数量打折扣", true, false),
    T1204("12", "0102020603020401", "商品范围第数量减金额", true, false),
    T1205("12", "0101020403020402", "单品每第数量打折扣", true, false),
    T1206("12", "0101020403020401", "单品每第数量减金额", true, false),
    T1207("12", "0102020803020416", "商品范围单品每数量免费", true, false),
    T1208("12", "0102020603020416", "商品范围第数量免费", true, false),
    T1209("12", "0101020803020416", "单品第数量免费", true, false),
    T1210("12", "0101020603020416", "单品每第数量免费", true, false),
    ;

    TemplateEnum(String tagCode, String code, String name, boolean selfExclusive, boolean abTemplate){
        this.tagCode = tagCode;
        this.code = code;
        this.name = name;
        this.selfExclusive = selfExclusive;
        this.abTemplate = abTemplate;
    }

    /**
     * Code==>selfExclusive
     */
    public static boolean code2SelfExclusive(String code){
        TemplateEnum[] values = TemplateEnum.values();
        for (TemplateEnum type : values){
            if (type.code().equals(code)){
                return type.selfExclusive();
            }
        }
        return false;
    }

    /**
     * Code==>moreThanOneProduct
     */
    public static boolean code2ABTemplate(String code){
        TemplateEnum[] values = TemplateEnum.values();
        for (TemplateEnum type : values){
            if (type.code().equals(code)){
                return type.abTemplate();
            }
        }
        return false;
    }

    /**
     * Code==>TagCode
     */
    public static String code2TagCode(String code){
        TemplateEnum[] values = TemplateEnum.values();
        for (TemplateEnum type : values){
            if (type.code().equals(code)){
                return type.tagCode();
            }
        }
        return "";
    }

    /**
     * Code==>TagCode
     */
    public static FuncTypeEnum.ScopeEnum code2Scope(String code){
        TemplateEnum[] values = TemplateEnum.values();
        for (TemplateEnum type : values){
            if (type.code().equals(code)){
                return type.scope();
            }
        }
        return null;
    }


    private String name;
    private String tagCode;
    private String code;
    private boolean selfExclusive;
    private boolean abTemplate;

    public String getName() {
        return name;
    }
    public String code() {
        return code;
    }
    public String tagCode() {
        return tagCode;
    }
    public boolean selfExclusive() {
        return selfExclusive;
    }
    public boolean abTemplate() {
        return abTemplate;
    }

    /**
     * Get the promotion scope
     */
    public FuncTypeEnum.ScopeEnum scope(){

        return EnumUtil.code2Enum(FuncTypeEnum.ScopeEnum.class, code.substring(0,4));
    }

    public boolean equalsCode(String code) {

        return this.code().equals(code);
    }
}
