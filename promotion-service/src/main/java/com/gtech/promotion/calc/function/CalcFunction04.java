/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.calc.function;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;

import com.gtech.commons.exception.ErrorCode;
import com.gtech.commons.utils.ConvertUtils;
import com.gtech.commons.utils.StringUtil;
import com.gtech.promotion.calc.CalcActivity;
import com.gtech.promotion.calc.CalcConstants;
import com.gtech.promotion.calc.CalcFunction;
import com.gtech.promotion.calc.model.CalcActivityFuncParam;
import com.gtech.promotion.calc.model.CalcActivityFuncRank;
import com.gtech.promotion.calc.model.CalcActivityIncentive;
import com.gtech.promotion.calc.model.CalcShoppingCart;
import com.gtech.promotion.calc.model.CalcShoppingCartItem;
import com.gtech.promotion.code.activity.LimitationCodeEnum;
import com.gtech.promotion.code.activity.SeqNumEnum;
import com.gtech.promotion.exception.ErrorCodes;
import com.gtech.promotion.helper.ActivityIncentiveLimited;
import com.gtech.promotion.helper.RedisOpsHelper;
import com.gtech.promotion.utils.TemplateCodeSubstringUtil;

/**
 * 原子函数：促销条件(04)
 * 
 */
public abstract class CalcFunction04 implements CalcFunction{

    // 活动每单限制最大金额
    protected BigDecimal maxAmountLimit;

    /**
     * 加载公共属性：最大限制金额和金额精确度（元角分）
     * 
     * @param promoObject
     */
    public void loadCommonValue(CalcShoppingCart promoObject){

        maxAmountLimit = BigDecimal.valueOf(Long.MAX_VALUE);
        ActivityIncentiveLimited incentiveLimited = promoObject.getCalcActivity().getIncentiveLimited();
        if (null != incentiveLimited && incentiveLimited.haveLimited(LimitationCodeEnum.ORDER_AMOUNT)) {
            maxAmountLimit = incentiveLimited.getLimitedValue(LimitationCodeEnum.ORDER_AMOUNT);
        }
    }

    public void setCalcActivityValue(CalcShoppingCart promoObject, CalcActivityIncentive incentive, BigDecimal postage) {
        int precision = promoObject.getCalcActivity().getPrecison();
		BigDecimal promoRewardPostage = postage.subtract(promoObject.getPostage()).setScale(precision, CalcConstants.ROUND_DOWN);
		if (promoObject.getCalcActivity().getPowerPrecison() != null) {
			promoRewardPostage = promoRewardPostage.divide(promoObject.getCalcActivity().getPowerPrecison()).setScale(0, CalcConstants.ROUND_DOWN)
					.multiply(promoObject.getCalcActivity().getPowerPrecison());
		}
		incentive.getCalcActivity().setPromoRewardPostage(promoRewardPostage);
        incentive.getCalcActivity().setEffectiveFlag(true);
        for (CalcShoppingCartItem scItem : promoObject.getCalcShoppingCartItemList()) {
            String activityCode = incentive.getActivityCode();
            CalcActivity calcActivity = scItem.getCurrentActivity(activityCode);
            if (null != calcActivity) {
                calcActivity.setEffectiveFlag(true);
            }
        }
    }

    /**
     * 获取优惠扣减的比例
     * 
     * @param reduceAmount 优惠总金额（需要扣减掉的总价格）
     * @param promoAmount 当前商品集合的总价
     * @return 扣减比例
     */
    public BigDecimal getScale(BigDecimal reduceAmount, BigDecimal promoAmount) {

        BigDecimal scale = BigDecimal.ZERO;
        if (promoAmount.compareTo(BigDecimal.ZERO) != 0) {//如果减成了0,0做为除数会报错
            scale = reduceAmount.divide(promoAmount, CalcConstants.DEFAULT_PRECISION, CalcConstants.ROUND_DOWN);
        }
        return scale;
    }

    /**
     * 统计总优惠金额
     * 
     * @param itemListB
     * @param activityTimes
     */
    public BigDecimal countReduceAmount(List<CalcShoppingCartItem> itemListB,Integer activityTimes,int precision){
        BigDecimal reduceAmount = BigDecimal.ZERO;
        int num = 0;
        //计算优惠总金额
        for (int i = 0; i < itemListB.size(); i++){
            if (num < activityTimes){
                CalcShoppingCartItem cartItem = itemListB.get(i);
                Integer itemQuantity = cartItem.getQuantity();
                BigDecimal promoAmount = cartItem.getPromoAmount().setScale(precision, CalcConstants.ROUND_DOWN);
                reduceAmount = promoAmount.divide(new BigDecimal(cartItem.getQuantity()), precision, CalcConstants.ROUND_DOWN)
                                .multiply(new BigDecimal(num + itemQuantity > activityTimes ? activityTimes - num : itemQuantity)).add(reduceAmount);
                num += itemQuantity;
            }else{
                break;
            }
        }
        return reduceAmount;
    }

    /**
     * 减金额分摊
     */
	public void handlerShoppingCartItemList(List<CalcShoppingCartItem> cList, CalcActivityIncentive incentive, BigDecimal scale, int precision,
			BigDecimal totalPromoAmount, BigDecimal powerPrecison) {
		cList.sort((a, b) -> a.getPromoAmount().setScale(precision, CalcConstants.ROUND_DOWN)
				.subtract(b.getPromoAmount().setScale(precision, CalcConstants.ROUND_DOWN)).intValue());
        BigDecimal totalReduceAmount = BigDecimal.ZERO;
        Iterator iterator = cList.iterator();

        List<CalcShoppingCartItem> integerItemList = new ArrayList<>();
        //排除促销价为整数
        while (iterator.hasNext()) {
            CalcShoppingCartItem scItem = (CalcShoppingCartItem)iterator.next();
            BigDecimal beforeAmount = scItem.getPromoAmount();
            BigDecimal reduceAmount = beforeAmount.multiply(scale);
            if(Boolean.TRUE.equals(isIntegerNumber(reduceAmount))){
                BigDecimal afterAmount = beforeAmount.subtract(reduceAmount).max(BigDecimal.ZERO);
                scItem.setPromoAmount(afterAmount);
                scItem.setPromoQuantity(scItem.getQuantity());
                scItem.setCalculateResult(incentive.getActivityCode(), beforeAmount, afterAmount);
                totalReduceAmount = totalReduceAmount.add(reduceAmount);
                iterator.remove();
                integerItemList.add(scItem);
            }
        }

        if(CollectionUtils.isNotEmpty(cList)) {
			BigDecimal leaveReduceAmount = BigDecimal.ZERO;
			Map<String, BigDecimal> skuMap = new HashMap<>();
            for (int i = 0; i < cList.size(); i++) {
                CalcShoppingCartItem scItem = cList.get(i);
				String skuCode = scItem.getSkuCode();
                // 该sku总价，上个活动计算后价格，如果没有上个活动就是原单价*数量
                BigDecimal beforeAmount = scItem.getPromoAmount();
//                BigDecimal afterAmount = BigDecimal.ZERO
                BigDecimal reduceAmount = BigDecimal.ZERO;
				reduceAmount = reduceAmount.add(beforeAmount.multiply(scale).setScale(precision, CalcConstants.ROUND_DOWN));
				if (powerPrecison != null) {
					reduceAmount = reduceAmount.divide(powerPrecison).setScale(0, CalcConstants.ROUND_DOWN).multiply(powerPrecison);
				}
				// 优惠后该sku总价，参加下个活动时总价
				totalReduceAmount = totalReduceAmount.add(reduceAmount);
				skuMap.put(skuCode, reduceAmount);

            }
			// 计算后多余优惠均摊
			leaveReduceAmount = totalPromoAmount.subtract(totalReduceAmount);
			// 价格最小单位
			BigDecimal unit = BigDecimal.ONE.divide(BigDecimal.valueOf(Math.pow(10, precision)));
			// 价格最小单位数量
    		int leaveResult = Integer.parseInt(leaveReduceAmount.multiply(BigDecimal.valueOf(Math.pow(10, precision))).setScale(0).toString());
			if (powerPrecison != null) {
				unit = powerPrecison;
				leaveResult = Integer.parseInt(leaveReduceAmount.divide(powerPrecison).setScale(0).toString());
			}
    		for (int i = 0; i < cList.size(); i++) {
                CalcShoppingCartItem scItem = cList.get(i);
				BigDecimal beforeAmount = scItem.getPromoAmount();
				String skuCode = scItem.getSkuCode();
				BigDecimal reduceAmount = skuMap.get(skuCode);
				if (leaveResult > i) {
					reduceAmount = reduceAmount.add(unit);
				}
				BigDecimal afterAmount = beforeAmount.subtract(reduceAmount).max(BigDecimal.ZERO);
				scItem.setPromoAmount(afterAmount);
				scItem.setPromoQuantity(scItem.getQuantity());
				scItem.setCalculateResult(incentive.getActivityCode(), beforeAmount, afterAmount);
            }
        }

        cList.addAll(integerItemList);
    }

    public Boolean isIntegerNumber(BigDecimal number){
            //整数
            return new BigDecimal(number.intValue()).compareTo(number)==0;
    }

    /**
     * 是否是B池商品
     */
    public boolean isSeqB(String activityCode, CalcShoppingCartItem cartItem){

        return MapUtils.isNotEmpty(cartItem.getSeqActivityMap()) 
            && StringUtil.isNotBlank(cartItem.getSeqActivityMap().get(activityCode)) 
            && cartItem.getSeqActivityMap().get(activityCode).indexOf(SeqNumEnum.B.code()) >= 0;
    }

    /**
     * 商品池B的商品数量
     */
    public int countSeqBNum(List<CalcShoppingCartItem> promoObjects, String activityCode) {

        int seqB = 0;
        Iterator<CalcShoppingCartItem> iterator = promoObjects.iterator();
        while (iterator.hasNext()) {
            CalcShoppingCartItem cartItem = iterator.next();
            if (isSeqB(activityCode, cartItem)) {
                seqB++;
            }
        }
        return seqB;
    }

    /**
     * 0412/0413商品池B价格分摊
     * 
     * @param cList
     * @param incentive
     * @param reduceAmount
     * @param scale
     * @param seqB
     */
	public void handlerShoppingCartItemList(List<CalcShoppingCartItem> cList, CalcActivityIncentive incentive, BigDecimal reduceAmount, BigDecimal scale,
			int seqB, int precision, BigDecimal powerPrecison) {

        int j = 0;//记录B池商品计算次数
        //计数：如果舍去的数值累计到了1元，就要分摊到当前商品上
        BigDecimal subtractAmount = BigDecimal.ZERO;

        for (int i = 0; i < cList.size(); i++){

            CalcShoppingCartItem scItem = cList.get(i);
            String activityCode = incentive.getActivityCode();

            if (this.isSeqB(activityCode, scItem)){
                j++;
                //该sku总价，上个活动计算后价格，如果没有上个活动就是原单价*数量
                BigDecimal beforeAmount = scItem.getPromoAmount().setScale(precision, CalcConstants.ROUND_DOWN);
				if (powerPrecison != null) {
					beforeAmount = beforeAmount.divide(powerPrecison).setScale(0, CalcConstants.ROUND_DOWN).multiply(powerPrecison);
				}
                //该sku优惠金额
                BigDecimal realReduce = beforeAmount.multiply(scale).setScale(precision, CalcConstants.ROUND_DOWN);
				if (powerPrecison != null) {
					realReduce = realReduce.divide(powerPrecison).setScale(0, CalcConstants.ROUND_DOWN).multiply(powerPrecison);
				}
                //累计因为保留小数偏差的金额
                subtractAmount = subtractAmount.add(realReduce);
                //如果舍去的数值累计到了1元，就要分摊到当前商品上
                if (subtractAmount.compareTo(BigDecimal.ONE) >= 0){
                    realReduce = realReduce.add(BigDecimal.ONE);
                    subtractAmount = subtractAmount.subtract(BigDecimal.ONE);
                }
                //尾款在最后一项商品
                if (j == seqB){
                    realReduce = reduceAmount;
                }else{
                    reduceAmount = reduceAmount.subtract(realReduce);
                }
                //优惠后该sku总价，参加下个活动时总价
                BigDecimal afterAmount = beforeAmount.subtract(realReduce).max(BigDecimal.ZERO);
                scItem.setPromoAmount(afterAmount);
                scItem.setCalculateResult(incentive.getActivityCode(), beforeAmount, afterAmount);
            }else{
                scItem.getCurrentActivity(incentive.getActivityCode()).setEffectiveFlag(true);
                scItem.setCalculateResult(incentive.getActivityCode(), scItem.getPromoAmount(), scItem.getPromoAmount());
            }
        }
    }

    /**
     * Check the current activity's incentive limited and combine relations.
     */
    protected boolean checkLimitedAndCombine(CalcActivityIncentive calcActivityIncentive, List<CalcShoppingCartItem> cscItemList, int incentiveTimes, BigDecimal incentiveValue) {
        
        return this.checkLimit(calcActivityIncentive, incentiveTimes, incentiveValue)
            && this.checkActivitySku(calcActivityIncentive, cscItemList, false);
    }

    /**
     * 校验活动限制
     */
    protected boolean checkLimit(CalcActivityIncentive calcActivityIncentive, int incentiveTimes, BigDecimal incentiveValue) {

        CalcActivity calcActivity = calcActivityIncentive.getCalcActivity();
        ActivityIncentiveLimited incentiveLimited = calcActivity.getIncentiveLimited();
        if (null == incentiveLimited) {
            return true;
        }

        if (incentiveValue == null) {
            incentiveValue = BigDecimal.ZERO;
        }

        return this.checkLimit(calcActivity, LimitationCodeEnum.ACTIVITY_TOTAL_COUNT, BigDecimal.valueOf(incentiveTimes), ErrorCodes.ACTIVITY_LIMIT_ACTIVITY_TOTAL_COUNT)
            && this.checkLimit(calcActivity, LimitationCodeEnum.USER_TOTAL_COUNT, BigDecimal.valueOf(incentiveTimes), ErrorCodes.ACTIVITY_LIMIT_USER_TOTAL_COUNT)

            && this.checkLimit(calcActivity, LimitationCodeEnum.ACTIVITY_TOTAL_AMOUNT, incentiveValue, ErrorCodes.ACTIVITY_LIMIT_ACTIVITY_TOTAL_AMOUNT)
            && this.checkLimit(calcActivity, LimitationCodeEnum.USER_TOTAL_AMOUNT, incentiveValue, ErrorCodes.ACTIVITY_LIMIT_USER_TOTAL_AMOUNT)

            && this.checkLimit(calcActivity, LimitationCodeEnum.ACTIVITY_DAY_ORDER_COUNT, BigDecimal.ONE, ErrorCodes.ACTIVITY_LIMIT_CONDITION_DAY_ORDER)
            && this.checkLimit(calcActivity, LimitationCodeEnum.USER_DAY_ORDER_COUNT, BigDecimal.ONE, ErrorCodes.ACTIVITY_LIMIT_CONDITION_DAY_ORDER);
    }

    private boolean checkLimit(CalcActivity calcActivity, LimitationCodeEnum limitedCode, BigDecimal incentiveValue, ErrorCode failedReason) {

        RedisOpsHelper redisOpsHelper = calcActivity.getRedisOpsHelper();
        ActivityIncentiveLimited incentiveLimited = calcActivity.getIncentiveLimited();

        if (!incentiveLimited.haveLimited(limitedCode)) {
            return true;
        }
        BigDecimal remainingValue;
        if (LimitationCodeEnum.SKU_COUNT.equals(limitedCode) || LimitationCodeEnum.USER_SKU_COUNT.equals(limitedCode)) {
            for (CalcShoppingCartItem shoppingCartItem : calcActivity.getCalcShoppingCart().getCalcShoppingCartItemList()) {
                remainingValue = redisOpsHelper.getLimitedValue(limitedCode, calcActivity.getTenantCode(), calcActivity.getActivityCode(), calcActivity.getMemberCode(), shoppingCartItem.getSkuCode());
                if (setValue(calcActivity, limitedCode, incentiveValue, failedReason, incentiveLimited, remainingValue))
                    return false;
            }
        }else {
            remainingValue = redisOpsHelper.getLimitedValue(limitedCode, calcActivity.getTenantCode(), calcActivity.getActivityCode(), calcActivity.getMemberCode(), "");
            if (setValue(calcActivity, limitedCode, incentiveValue, failedReason, incentiveLimited, remainingValue))
                return false;
        }

        return true;
    }

    private boolean setValue(CalcActivity calcActivity, LimitationCodeEnum limitedCode, BigDecimal incentiveValue, ErrorCode failedReason, ActivityIncentiveLimited incentiveLimited, BigDecimal remainingValue) {
        if (null == remainingValue) {
            remainingValue = incentiveLimited.getLimitedValue(limitedCode);
        }
        if (remainingValue.compareTo(incentiveValue) < 0) {
            calcActivity.setFailedReason(failedReason);
            return true;
        }
        return false;
    }

    protected CalcShoppingCartItem nextItem(List<CalcShoppingCartItem> scItemList, Map<String, CalcShoppingCartItem> usedItemMap) {
        
        if (CollectionUtils.isEmpty(scItemList)) {
            return null;
        }

        for(CalcShoppingCartItem scItem : scItemList) {

            if (usedItemMap.containsKey(scItem.keyString())) {
                // 当前项已经参与过活动
                continue;
            }

            return scItem;
        }

        return null;
    }

    protected void dealNoPromoItems(CalcShoppingCart promoObject, Map<String, CalcShoppingCartItem> usedItemMap) {

        for(CalcShoppingCartItem scItem = this.nextItem(promoObject.getCalcShoppingCartItemList(), usedItemMap); scItem != null;
                        scItem = this.nextItem(promoObject.getCalcShoppingCartItemList(), usedItemMap)) {
            usedItemMap.put(scItem.keyString(), scItem);
        }
    }

	protected void dealMaxOrderAmount(BigDecimal totalIncentiveValue, CalcShoppingCart promoObject, CalcActivityIncentive incentive, BigDecimal promoAmount) {

		int precision = promoObject.getCalcActivity().getPrecison();
		BigDecimal powerPrecison = promoObject.getCalcActivity().getPowerPrecison();
        String substring = TemplateCodeSubstringUtil.subStringTemplateCodeBegin4End8(incentive.getTemplateCode());
        if (totalIncentiveValue.compareTo(maxAmountLimit) > 0){
            BigDecimal temp = maxAmountLimit;
            totalIncentiveValue = maxAmountLimit;
            int size = promoObject.getCalcShoppingCartItemList().size();
            for (int i = 0; i < size; i++) {
                CalcShoppingCartItem scItem = promoObject.getCalcShoppingCartItemList().get(i);
                CalcActivity calcActivity = scItem.getCurrentActivity(incentive.getActivityCode());
                BigDecimal beforeAmount = calcActivity.getBeforeAmount().setScale(precision, CalcConstants.ROUND_DOWN);
                if (powerPrecison != null) {
                	beforeAmount = beforeAmount.divide(powerPrecison).setScale(0, CalcConstants.ROUND_DOWN).multiply(powerPrecison);
    			}
                //单品0401 该sku总优惠金额 = （活动减的金额*商品数量）保留小数
                BigDecimal reduceAmount = totalIncentiveValue.multiply(beforeAmount.divide(promoAmount, CalcConstants.DEF_MATHCONTEXT)).setScale(precision, CalcConstants.ROUND_DOWN);
                if (FunctionEnum.F0206.equalsCode(substring)) {//0206第
                    reduceAmount = totalIncentiveValue.divide(ConvertUtils.toBigDecimal(size), CalcConstants.DEF_MATHCONTEXT).setScale(precision, CalcConstants.ROUND_DOWN);
                }

                if (powerPrecison != null) {
					reduceAmount = reduceAmount.divide(powerPrecison).setScale(0, CalcConstants.ROUND_DOWN).multiply(powerPrecison);
                }
                if (i == size - 1){
                    reduceAmount = temp;
                }else{
                    temp = temp.subtract(reduceAmount);
                }
                // 新的商品金额 = 当前的商品金额 - 当前的扣减金额
                BigDecimal afterAmount = beforeAmount.subtract(reduceAmount).max(BigDecimal.ZERO);

                // 保存新的促销金额
                scItem.setPromoAmount(afterAmount);
                calcActivity.setBeforeAmount(beforeAmount);
                calcActivity.setAfterAmount(afterAmount);
                calcActivity.setEffectiveFlag(true);

                scItem.setPromoQuantity(scItem.getQuantity());
                scItem.setCalculateResult(incentive.getActivityCode(), beforeAmount, afterAmount);
            }
        }
    }

    protected BigDecimal postageCommon(CalcShoppingCart promoObject, CalcActivityIncentive incentive, FunctionParamMap paramMap, String functionCode) {
        BigDecimal promoAmount = promoObject.getPromoAmount();
        incentive.getCalcActivity().setBeforeAmount(promoAmount);
        incentive.getCalcActivity().setAfterAmount(promoAmount);

        //04促销奖励参数的数值
        String paramValue = paramMap.getFuncParamValue(functionCode);
        return ConvertUtils.toBigDecimal(paramValue).setScale(2, CalcConstants.ROUND_DOWN);
    }

    protected Integer getFuncParamValue(CalcActivityIncentive incentive,int rankIndex,int funcParamIndex){
        List<CalcActivityFuncRank> calcFuncRankList = incentive.getCalcActivity().getCalcTemplate().getCalcFuncRankList();
        if(!CollectionUtils.isEmpty(calcFuncRankList)) {
            CalcActivityFuncRank calcActivityFuncRank = calcFuncRankList.get(rankIndex);
            List<CalcActivityFuncParam> funcParamList = calcActivityFuncRank.getFuncParamList();
            String paramValue = funcParamList.get(funcParamIndex).getParamValue();
            if(!StringUtils.isEmpty(paramValue)){
                return Integer.valueOf(paramValue);
            }
        }
        return null;
    }
}
