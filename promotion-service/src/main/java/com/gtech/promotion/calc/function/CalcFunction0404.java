/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.calc.function;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Deque;
import java.util.List;
import java.util.Set;

import org.springframework.stereotype.Component;

import com.gtech.promotion.calc.CalcActivity;
import com.gtech.promotion.calc.CalcConstants;
import com.gtech.promotion.calc.CalcFunction;
import com.gtech.promotion.calc.CalcResult;
import com.gtech.promotion.calc.model.CalcActivityIncentive;
import com.gtech.promotion.calc.model.CalcShoppingCart;
import com.gtech.promotion.calc.model.CalcShoppingCartItem;

import lombok.Getter;

/**
 * 原子函数：促销奖励(04) - 组合固定金额(04)
 * 
 */
@Component
public class CalcFunction0404 extends CalcFunction04{

    @Getter
    private FunctionEnum function = FunctionEnum.F0404;

    static {
        // Registers the current atomic function
        CalcFunctionRegister.register(FunctionEnum.F0404, new CalcFunction0404());
    }

    /**
     * 构造函数私有化，不允许自己生成该对象
     */
    private CalcFunction0404(){
        // No codes
    }

    /*
     * (non-Javadoc)
     * @see com.gtech.promotion.calc.CalcFunction#calc(com.gtech.promotion.calc.model.ICalcPromoObject, com.gtech.promotion.calc.model.CalcActivityIncentive, java.util.Deque, 
     * com.gtech.promotion.calc.function.FunctionParamMap, com.gtech.promotion.calc.CalcFunction)
     */
    @Override
    public CalcResult calc(CalcShoppingCart promoObject, CalcActivityIncentive incentive, Deque<FunctionEnum> funcList, FunctionParamMap paramMap, CalcFunction caller) {

        try{
            if (!this.checkActivitySku(incentive, promoObject.getCalcShoppingCartItemList(), false)) {
                return CalcResult.SUCCESS_LIMITED;
            }

            this.loadCommonValue(promoObject);
            int precision = promoObject.getCalcActivity().getPrecison();
			BigDecimal powerPrecison = promoObject.getCalcActivity().getPowerPrecison();

            String substring = incentive.getTemplateCode().substring(4, 8);

            //奖励次数
            Integer incentiveTimes = incentive.getIncentiveTimes();
            //奖励参数值，设为固定金额0404
            BigDecimal afuncParamValue = new BigDecimal(paramMap.getFuncParamValue(this.function.code()));
            //总固定金额 = 奖励次数 * 奖励数值
            BigDecimal promoIncentiveAmount = afuncParamValue.multiply(BigDecimal.valueOf(incentiveTimes)).setScale(precision, CalcConstants.ROUND_DOWN);
			if (powerPrecison != null) {
				promoIncentiveAmount = promoIncentiveAmount.divide(powerPrecison).setScale(0, CalcConstants.ROUND_DOWN).multiply(powerPrecison);
			}
            //条件参数值，（每满or满）数量 或者 捆绑活动的商品池数 0404目前只有0302
            String conditinParamValue = paramMap.getFuncParamValue(FunctionEnum.F0302.code());
            //按照角标顺序选出maxCount件商品计算其总价=activityAmount（参与促销商品的总价）。（那么优惠的总金额 promoRewardAmount = activityAmount - 总固定金额 ）
            BigDecimal activityAmount = BigDecimal.ZERO;//参与捆绑活动的商品总价
            if (FunctionEnum.F0205.equalsCode(substring)){
                //捆绑活动
                activityAmount = activityAmountDeal(promoObject, incentive, incentiveTimes, activityAmount).setScale(precision, CalcConstants.ROUND_DOWN);
            }else{
                //满或每满的固定金额活动
                activityAmount = activityAmountDeal(promoObject, incentiveTimes, conditinParamValue, activityAmount).setScale(precision, CalcConstants.ROUND_DOWN);
            }
			if (powerPrecison != null) {
				activityAmount = activityAmount.divide(powerPrecison).setScale(0, CalcConstants.ROUND_DOWN).multiply(powerPrecison);
			}
            // 促销优惠的总金额（活动减免金额）
            BigDecimal reduceAmount = activityAmount.subtract(promoIncentiveAmount);
            if(reduceAmount.compareTo(BigDecimal.ZERO)<0) {
            	return CalcResult.ERROR_DEFAULT;
            }
            // 减金额的比率
            BigDecimal scale = getScale(reduceAmount, promoObject.getPromoAmount());

			handlerProduct(promoObject, incentive, reduceAmount, scale);
            return CalcResult.SUCCESS_TRUE;

        }catch (RuntimeException e){
            return CalcResult.ERROR_DEFAULT;
        }
    }

	private BigDecimal handlerProduct(CalcShoppingCart promoObject, CalcActivityIncentive incentive, BigDecimal reduceAmount, BigDecimal scale) {

		int precision = promoObject.getCalcActivity().getPrecison();
		BigDecimal powerPrecison = promoObject.getCalcActivity().getPowerPrecison();

        //计数：如果舍去的数值累计到了1元，就要分摊到当前商品上
        BigDecimal subtractAmount = BigDecimal.ZERO;
        List<CalcShoppingCartItem> cartItemList = promoObject.getCalcShoppingCartItemList();
        for (int i = 0; i < cartItemList.size(); i++) {
            CalcShoppingCartItem scItem = cartItemList.get(i);
            if (scItem.isExistActivity(incentive.getActivityCode())) {
                CalcActivity calcActivity = scItem.getCurrentActivity(incentive.getActivityCode());
                //该单品总价：  数量*单价
                BigDecimal beforAmount = scItem.getPromoAmount().setScale(precision, CalcConstants.ROUND_DOWN);
				if (powerPrecison != null) {
					beforAmount = beforAmount.divide(powerPrecison).setScale(0, CalcConstants.ROUND_DOWN).multiply(powerPrecison);
				}
                // 当前商品项的扣减金额
                BigDecimal reduceItemAmount = beforAmount.multiply(scale).setScale(precision, RoundingMode.HALF_UP);
				if (powerPrecison != null) {
					reduceItemAmount = reduceItemAmount.divide(powerPrecison).setScale(0, RoundingMode.HALF_UP).multiply(powerPrecison);
				}
                BigDecimal realReduce = beforAmount.multiply(scale).setScale(precision, CalcConstants.ROUND_DOWN);
				if (powerPrecison != null) {
					realReduce = realReduce.divide(powerPrecison).setScale(0, CalcConstants.ROUND_DOWN).multiply(powerPrecison);
				}
                //累计因为保留小数偏差的金额
                subtractAmount = reduceItemAmount.subtract(realReduce).add(subtractAmount);
                //如果舍去的数值累计到了1元，就要分摊到当前商品上
                if (subtractAmount.compareTo(BigDecimal.ONE) >= 0) {
                    realReduce = realReduce.add(BigDecimal.ONE);
                    subtractAmount = subtractAmount.subtract(BigDecimal.ONE);
                }
                if (i == cartItemList.size() - 1) {
                    realReduce = reduceAmount;//尾款：最后一项商品分摊的优惠金额就是剩下的
                } else {
                    reduceAmount = reduceAmount.subtract(realReduce);//记录：扣减每个商品项分摊的金额
                }
                //促销后该单品总价
                BigDecimal afterAmount = beforAmount.subtract(realReduce);
                if (afterAmount.compareTo(BigDecimal.ZERO) < 0){
                    afterAmount = BigDecimal.ZERO;
                }
                scItem.setPromoAmount(afterAmount);//该商品项促销后总价  如果有后续活动也会操作该sku的促销总金额
                calcActivity.setBeforeAmount(beforAmount);//参加活动计算前的商品项总价  该sku只会参加该活动一次
                calcActivity.setAfterAmount(afterAmount);//参加活动计算后的商品项总价    该sku只会参加该活动一次
                calcActivity.setEffectiveFlag(true);
            }
        }

        return reduceAmount;
    }

    /**
     * 参与活动的商品总价
     * 
     * @param promoObject
     * @param incentiveTimes
     * @param conditinParamValue
     * @param activityAmount
     */
    private BigDecimal activityAmountDeal(CalcShoppingCart promoObject,Integer incentiveTimes,String conditinParamValue,BigDecimal activityAmount){
        //可参与活动的总奖励数量
        BigDecimal maxCount = new BigDecimal(conditinParamValue).multiply(BigDecimal.valueOf(incentiveTimes));
        List<CalcShoppingCartItem> itemList = promoObject.getCalcShoppingCartItemList();
        //计数器
        Long count = 0L;
        for (CalcShoppingCartItem cartItem : itemList){
            //每种商品的件数
            Integer quantity = cartItem.getQuantity();
            //计算总奖励数量maxCount件商品总价
            for (int i = 0; i < quantity; i++){
                count++;
                if (count > maxCount.longValueExact()){
                    break;
                }
                activityAmount = activityAmount.add(cartItem.getPromoAmount().divide(new BigDecimal(cartItem.getQuantity()), 2, RoundingMode.HALF_UP));
            }
            if (count > maxCount.longValueExact()){
                break;
            }
        }
        return activityAmount;
    }

    /**
     * 参与活动的商品总价
     * 
     * @param promoObject
     * @param activityIncentive
     * @param incentiveTimes
     * @param activityAmount
     */
    private BigDecimal activityAmountDeal(CalcShoppingCart promoObject,CalcActivityIncentive activityIncentive,Integer incentiveTimes,BigDecimal activityAmount){
        Set<String> seqNumSet = promoObject.getCalcActivity().getSeqNums();//当前活动的所有商品池seqNum[1,2,3]
        ArrayList<Object> arrayList = new ArrayList<>();
        addTimes(incentiveTimes, seqNumSet, arrayList);
        for (CalcShoppingCartItem cartItem : promoObject.getCalcShoppingCartItemList()){
            String seqNum = cartItem.getSeqActivityMap().get(activityIncentive.getActivityCode());//seqNum不能为null
            if (arrayList.contains(seqNum)){
                Integer skuIncentiveTimes = Collections.frequency(arrayList, seqNum);//该sku可参与次数
                BigDecimal afterPrice = cartItem.getPromoAmount().divide(new BigDecimal(cartItem.getQuantity()), 2, RoundingMode.HALF_UP);//商品原单价或者上一个活动计算后单价
                if (skuIncentiveTimes.compareTo(cartItem.getQuantity()) <= 0){//该sku可参与次数小于等于该sku数量时，取skuIncentiveTimes
                    activityAmount = afterPrice.multiply(new BigDecimal(skuIncentiveTimes)).add(activityAmount);
                    for (int i = 0; i < incentiveTimes; i++){
                        arrayList.remove(seqNum);
                    }
                }else{//该sku可参与次数大于等于该sku数量时，取该sku数量
                    activityAmount = afterPrice.multiply(new BigDecimal(cartItem.getQuantity())).add(activityAmount);
                    for (int i = 0; i < cartItem.getQuantity(); i++){
                        arrayList.remove(seqNum);
                    }
                }
            }
        }
        return activityAmount;
    }

    private void addTimes(Integer incentiveTimes,Set<String> seqNumSet,ArrayList<Object> arrayList){
        for (int i = 0; i < incentiveTimes; i++){
            arrayList.addAll(seqNumSet);//例：触发俩次为1,1,2,2,3,3
        }
    }

}
