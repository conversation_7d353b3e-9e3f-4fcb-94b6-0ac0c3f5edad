package com.gtech.promotion.calc.function;

import java.math.BigDecimal;
import java.util.Deque;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSON;
import com.gtech.commons.utils.ConvertUtils;
import com.gtech.promotion.calc.CalcConstants;
import com.gtech.promotion.calc.CalcFunction;
import com.gtech.promotion.calc.CalcResult;
import com.gtech.promotion.calc.model.CalcActivityIncentive;
import com.gtech.promotion.calc.model.CalcShoppingCart;
import com.gtech.promotion.calc.model.CalcShoppingCartItem;
import com.gtech.promotion.utils.TemplateCodeSubstringUtil;

import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

/**
 * 原子函数：促销奖励(04) - 减金额(01)
 * 
 */
@Component
@Slf4j
public class CalcFunction0401 extends CalcFunction04{

    @Getter
    private FunctionEnum function = FunctionEnum.F0401;

    static {
        // Registers the current atomic function
        CalcFunctionRegister.register(FunctionEnum.F0401, new CalcFunction0401());
    }

    /**
     * 构造函数私有化，不允许自己生成该对象
     */
    private CalcFunction0401(){
        // No codes
    }


    /*
     * (non-Javadoc)
     * @see com.gtech.promotion.calc.CalcFunction#calc(com.gtech.promotion.calc.model.ICalcPromoObject, com.gtech.promotion.calc.model.CalcActivityIncentive, java.util.Deque, 
     * com.gtech.promotion.calc.function.FunctionParamMap, com.gtech.promotion.calc.CalcFunction)
     */
    @Override
    public CalcResult calc(CalcShoppingCart promoObject, CalcActivityIncentive incentive, Deque<FunctionEnum> funcList, FunctionParamMap paramMap, CalcFunction caller) {
        try{
            int precision = promoObject.getCalcActivity().getPrecison();
			BigDecimal powerPrecison = promoObject.getCalcActivity().getPowerPrecison();
            //04促销奖励参数的数值  减paramAmount元
            String paramValue = paramMap.getFuncParamValue(this.function.code());
            loadCommonValue(promoObject);
            //奖励金额
            BigDecimal incentiveValue = ConvertUtils.toBigDecimal(paramValue).setScale(precision, CalcConstants.ROUND_DOWN);
			if (powerPrecison != null) {
				incentiveValue = incentiveValue.divide(powerPrecison).setScale(0, CalcConstants.ROUND_DOWN).multiply(powerPrecison);
			}

            // 订单中参与当前活动的商品的参与计算的总价格
            BigDecimal productAmount = promoObject.getPromoAmount();
            switch (promoObject.getPromoScope()) {
                case ITEM:
					return handler0101(promoObject, incentive, incentiveValue);

                case PRODUCT:
                case ORDER:
					return handler0401Product(promoObject, incentive, incentiveValue, productAmount);
                    
                case SINGLE:
					return handler0104(promoObject, incentive, incentiveValue);

                default:
                    return CalcResult.ERROR_DEFAULT;
            }
        }catch (Exception e){
            return CalcResult.ERROR_DEFAULT;
        }
    }

	private CalcResult handler0101(CalcShoppingCart promoObject, CalcActivityIncentive incentive, BigDecimal incentiveValue) {

		int precision = promoObject.getCalcActivity().getPrecison();
		BigDecimal powerPrecison = promoObject.getCalcActivity().getPowerPrecison();
        log.info("promoObject:{},incentive:{},incentiveValue:{}", JSON.toJSONString(promoObject),JSON.toJSONString(incentive),incentiveValue);
        if (returnCalcResult(promoObject, incentive, incentiveValue)) return CalcResult.SUCCESS_LIMITED;

        BigDecimal promoAmountOrigin = promoObject.getPromoAmount();
        // 活动奖励金额
        List<CalcShoppingCartItem> scItems = promoObject.getCalcShoppingCartItemList();
        if (CollectionUtils.isEmpty(scItems)) {
            return CalcResult.ERROR_DEFAULT;
        }



        BigDecimal totalQuantity = BigDecimal.ZERO;
        for (int i = 0; i < scItems.size(); i++) {
            CalcShoppingCartItem scItem = scItems.get(i);
            BigDecimal beforeAmount = scItem.getPromoAmount().setScale(precision, CalcConstants.ROUND_DOWN);
			if (powerPrecison != null) {
				beforeAmount = beforeAmount.divide(powerPrecison).setScale(0, CalcConstants.ROUND_DOWN).multiply(powerPrecison);
			}
            //单品0401 该sku总优惠金额 = （活动减的金额*商品数量）保留小数
            BigDecimal reduceAmount = incentiveValue.multiply(ConvertUtils.toBigDecimal(scItem.getQuantity())).min(maxAmountLimit).min(beforeAmount).setScale(precision, CalcConstants.ROUND_DOWN);
			if (powerPrecison != null) {
				reduceAmount = reduceAmount.divide(powerPrecison).setScale(0, CalcConstants.ROUND_DOWN).multiply(powerPrecison);
			}
            String substring = TemplateCodeSubstringUtil.subStringTemplateCodeBegin4End8(incentive.getTemplateCode());
            if (FunctionEnum.F0206.equalsCode(substring)) {//0206第
                reduceAmount = incentiveValue.min(maxAmountLimit);
                reduceAmount = reduceAmount.min(scItem.getPromoAmount().divide(ConvertUtils.toBigDecimal(scItem.getQuantity()),precision,CalcConstants.ROUND_DOWN)).setScale(precision, CalcConstants.ROUND_DOWN);
                totalQuantity = totalQuantity.add(BigDecimal.ONE);
            }else if (FunctionEnum.F0204.equalsCode(substring)) {//0204 每第
                Integer value = getFuncParamValue(incentive,0,2);
                if(null != value) {
                    int reduceCount = scItem.getQuantity() / value;
                    BigDecimal amount = incentiveValue;
                    if (amount.compareTo(scItem.getProductAmount()) > -1) {
                        amount = scItem.getProductAmount();
                    }
                    reduceAmount = amount.multiply(ConvertUtils.toBigDecimal(reduceCount)).setScale(precision, CalcConstants.ROUND_DOWN);
                    totalQuantity = totalQuantity.add(new BigDecimal(reduceCount));
                }
            }else {
                totalQuantity = totalQuantity.add(ConvertUtils.toBigDecimal(scItem.getQuantity()));
            }

			if (powerPrecison != null) {
				reduceAmount = reduceAmount.divide(powerPrecison).setScale(0, CalcConstants.ROUND_DOWN).multiply(powerPrecison);
			}

            // 新的商品金额 = 当前的商品金额 - 当前的扣减金额
            BigDecimal afterAmount = beforeAmount.subtract(reduceAmount).max(BigDecimal.ZERO);
            scItem.setPromoAmount(afterAmount);
            scItem.setPromoQuantity(scItem.getQuantity());
            scItem.setCalculateResult(incentive.getActivityCode(), beforeAmount, afterAmount);
        }

        BigDecimal totalIncentiveValue = incentiveValue.multiply(totalQuantity);
		dealMaxOrderAmount(totalIncentiveValue, promoObject, incentive, promoAmountOrigin);

        return CalcResult.SUCCESS_TRUE;
    }

	private CalcResult handler0104(CalcShoppingCart promoObject, CalcActivityIncentive incentive, BigDecimal incentiveValue) {

		int precision = promoObject.getCalcActivity().getPrecison();
		BigDecimal powerPrecison = promoObject.getCalcActivity().getPowerPrecison();
		// 活动奖励金额
        List<CalcShoppingCartItem> scItems = promoObject.getCalcShoppingCartItemList();
        scItems.stream().forEach(x->{
            System.err.println(x.keyString());
        });

        if (CollectionUtils.isEmpty(scItems)) {
            return CalcResult.ERROR_DEFAULT;
        }
        // Current promotion total incentive amount
        BigDecimal totalIncentiveAmount = BigDecimal.ZERO;

        Map<String, CalcShoppingCartItem> usedItemMap = new HashMap<>();
        // 活动奖励金额
        Integer leftTimes = incentive.getIncentiveTimes();
        String groupCode = incentive.getCalcActivity().getActivityModel().getGroupCode();

        while(leftTimes > 0) {

            CalcShoppingCartItem calcShoppingCartItem = this.nextItem(promoObject.getCalcShoppingCartItemList(), usedItemMap);
            if (null == calcShoppingCartItem) {
                break;
            }
            Integer times = leftTimes;
            if (leftTimes > calcShoppingCartItem.getQuantity()) {
                times = calcShoppingCartItem.getQuantity();
                leftTimes -= times;
            } else {
                leftTimes = 0;
            }
            for(int i = 0; i < times; i++) {
                if (!promoObject.deductLeftTimes0104(calcShoppingCartItem.getSkuCode(),groupCode)) {
                    leftTimes += (times-i);
                    times = i;
                    break;
                }
            }

            BigDecimal beforeAmount = calcShoppingCartItem.getPromoAmount().setScale(precision, CalcConstants.ROUND_DOWN);
			if (powerPrecison != null) {
				beforeAmount = beforeAmount.divide(powerPrecison).setScale(0, CalcConstants.ROUND_DOWN).multiply(powerPrecison);
			}
            BigDecimal itemIncentiveAmount = incentiveValue.min(maxAmountLimit).multiply(new BigDecimal(times)).min(beforeAmount).setScale(precision, CalcConstants.ROUND_DOWN);
			if (powerPrecison != null) {
				itemIncentiveAmount = itemIncentiveAmount.divide(powerPrecison).setScale(0, CalcConstants.ROUND_DOWN).multiply(powerPrecison);
			}

            BigDecimal afterAmount = beforeAmount.subtract(itemIncentiveAmount).max(BigDecimal.ZERO);
            itemIncentiveAmount = beforeAmount.subtract(afterAmount);
            totalIncentiveAmount = totalIncentiveAmount.add(itemIncentiveAmount);
            if (returnCalcResult(promoObject, incentive, totalIncentiveAmount)) return CalcResult.SUCCESS_LIMITED;
            // Save promotion result to scItem
            setSctemPromoQuantity(calcShoppingCartItem, times);
            calcShoppingCartItem.setPromoAmount(afterAmount);
            calcShoppingCartItem.setCalculateResult(incentive.getActivityCode(), beforeAmount, afterAmount);
            usedItemMap.put(calcShoppingCartItem.keyString(), calcShoppingCartItem);
        }

        this.dealNoPromoItems(promoObject, usedItemMap);
        setIncentiveTimes(incentive, leftTimes);

        return CalcResult.SUCCESS_TRUE;
    }

    private void setIncentiveTimes(CalcActivityIncentive incentive, Integer leftTimes) {
        if (leftTimes > 0) {
            incentive.setIncentiveTimes(incentive.getIncentiveTimes() - leftTimes);
        }
    }

    private void setSctemPromoQuantity(CalcShoppingCartItem scItem, Integer times) {
        if (scItem.getPromoQuantity() < times) {
            scItem.setPromoQuantity(times);
        }
    }

    private boolean returnCalcResult(CalcShoppingCart promoObject, CalcActivityIncentive incentive, BigDecimal totalIncentiveAmount) {
        return !this.checkLimitedAndCombine(incentive, promoObject.getCalcShoppingCartItemList(), 1, totalIncentiveAmount);
    }

	private CalcResult handler0401Product(CalcShoppingCart promoObject, CalcActivityIncentive incentive, BigDecimal incentiveValue, BigDecimal productAmount) {

        if (returnCalcResult(promoObject, incentive, incentiveValue)) return CalcResult.SUCCESS_LIMITED;
        int precision = promoObject.getCalcActivity().getPrecison();
		BigDecimal powerPrecison = promoObject.getCalcActivity().getPowerPrecison();
        // 活动生效奖励次数
        BigDecimal incentiveTimes = BigDecimal.valueOf(incentive.getIncentiveTimes());
        // 范围0401 活动总优惠金额 = （活动减的金额*奖励触发次数）保留小数
        BigDecimal reduce = incentiveValue.multiply(incentiveTimes).min(maxAmountLimit).min(promoObject.getPromoAmount()).setScale(precision, CalcConstants.ROUND_DOWN);
		if (powerPrecison != null) {
			reduce = reduce.divide(powerPrecison).setScale(0, CalcConstants.ROUND_DOWN).multiply(powerPrecison);
		}

        // 减金额的比率
        BigDecimal scale = getScale(reduce, productAmount);

		handlerShoppingCartItemList(promoObject.getCalcShoppingCartItemList(), incentive, scale, precision, reduce, powerPrecison);

        return CalcResult.SUCCESS_TRUE;
    }

}
