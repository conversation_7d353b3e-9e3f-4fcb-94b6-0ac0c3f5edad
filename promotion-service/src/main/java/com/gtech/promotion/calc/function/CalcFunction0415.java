/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.calc.function;

import com.gtech.promotion.calc.CalcConstants;
import com.gtech.promotion.calc.CalcFunction;
import com.gtech.promotion.calc.CalcResult;
import com.gtech.promotion.calc.model.CalcActivityIncentive;
import com.gtech.promotion.calc.model.CalcShoppingCart;
import com.gtech.promotion.code.activity.LimitationCodeEnum;
import com.gtech.promotion.helper.ActivityIncentiveLimited;
import lombok.Getter;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.Deque;

/**
 * 原子函数：促销奖励(04) - 邮费减金额(15)
 * 
 */
@Component
public class CalcFunction0415 extends CalcFunction04 {

    @Getter
    private FunctionEnum function = FunctionEnum.F0415;

    static {
        // Registers the current atomic function
        CalcFunctionRegister.register(FunctionEnum.F0415, new CalcFunction0415());
    }

    /**
     * 构造函数私有化，不允许自己生成该对象
     */
    private CalcFunction0415() {
        // No codes
    }

    /*
     * (non-Javadoc)
     * @see com.gtech.promotion.calc.CalcFunction#calc(com.gtech.promotion.calc.model.ICalcPromoObject, com.gtech.promotion.calc.model.CalcActivityIncentive, java.util.Deque, 
     * com.gtech.promotion.calc.function.FunctionParamMap, com.gtech.promotion.calc.CalcFunction)
     */
    @Override
    public CalcResult calc(CalcShoppingCart promoObject, CalcActivityIncentive incentive, Deque<FunctionEnum> funcList, FunctionParamMap paramMap, CalcFunction caller) {
        try {
            this.loadCommonValue(promoObject);
            int precision = promoObject.getCalcActivity().getPrecison();
            BigDecimal powerPrecison = promoObject.getCalcActivity().getPowerPrecison();
            if (!this.checkLimitedAndCombine(incentive, promoObject.getCalcShoppingCartItemList(), 1, BigDecimal.ZERO)) {
                return CalcResult.SUCCESS_LIMITED;
            }

            // 活动限制最大优惠金额
            BigDecimal maxMoneyLimit = maxAmountLimit.setScale(precision, CalcConstants.ROUND_DOWN);
            if (powerPrecison != null) {
                maxMoneyLimit = maxMoneyLimit.divide(powerPrecison).setScale(0, CalcConstants.ROUND_DOWN).multiply(powerPrecison);
            }

            BigDecimal incentiveValue = postageCommon(promoObject, incentive, paramMap, this.function.code());

            //该商品折扣 大于限制金额，则取限制金额
            if (incentiveValue.compareTo(maxMoneyLimit) > 0) {
                incentiveValue = maxMoneyLimit;
            }

            BigDecimal postage = promoObject.getPostage();
            BigDecimal subtract = postage.subtract(incentiveValue);
            promoObject.setPostage(subtract.max(BigDecimal.ZERO));
            setCalcActivityValue(promoObject, incentive, postage);
            return CalcResult.SUCCESS_TRUE;
        } catch (Exception e) {
            return CalcResult.ERROR_DEFAULT;
        }
    }

}
