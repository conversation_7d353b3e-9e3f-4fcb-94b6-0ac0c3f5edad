/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.calc.function;

import java.util.Deque;

import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import com.gtech.promotion.calc.CalcActivity;
import com.gtech.promotion.calc.CalcFunction;
import com.gtech.promotion.calc.CalcResult;
import com.gtech.promotion.calc.model.CalcActivityIncentive;
import com.gtech.promotion.calc.model.CalcShoppingCart;

import lombok.Getter;

/**
 * 原子函数：促销范围(01) - 单件(04)
 * 
 */
@Component
public class CalcFunction0104 extends CalcFunction01 {

    @Getter
    private FunctionEnum function = FunctionEnum.F0104;

    static{
        // Registers the current atomic function
        CalcFunctionRegister.register(FunctionEnum.F0104, new CalcFunction0104());
    }

    /**
     * 构造函数私有化，不允许自己生成该对象
     */
    private CalcFunction0104(){
        // No codes
    }

    /*
     * (non-Javadoc)
     * @see com.gtech.promotion.calc.CalcFunction#calc(com.gtech.promotion.calc.model.ICalcPromoObject, com.gtech.promotion.calc.model.CalcActivityIncentive, java.util.Deque, 
     * com.gtech.promotion.calc.function.FunctionParamMap, com.gtech.promotion.calc.CalcFunction)
     */
    @Override
    public CalcResult calc(CalcShoppingCart promoObject, CalcActivityIncentive incentive, Deque<FunctionEnum> funcList, FunctionParamMap paramMap, CalcFunction caller) {

        if (promoObject == null || CollectionUtils.isEmpty(promoObject.getCalcShoppingCartItemList())) {
            return CalcResult.ERROR_DEFAULT;
        }

        if (!this.checkActivitySku(incentive, promoObject.getCalcShoppingCartItemList(), true)) {
            return CalcResult.SUCCESS_FALSE;
        }

        // 原子函数注册管理器
        CalcFunction funcCalc = CalcFunctionRegister.getInstance(funcList.removeFirst());
        CalcActivity calcActivity = promoObject.getCalcActivity();
        switch (funcCalc.getFunction().type()) {

            case CONDITION:

                calcActivity.setBeforeAmount(promoObject.getPromoAmount());
                //进入原子计算函数 020x -> 030x
                CalcResult calcResult = funcCalc.calc(promoObject, incentive, funcList, paramMap, this);
                if (!CalcResult.SUCCESS_TRUE.equals(calcResult)){
                    calcActivity.setNeedMoreAmount(incentive.getNeedMoreAmount());
                    calcActivity.setNeedMoreUnit(incentive.getNeedMoreUnit());
                    calcActivity.setFailedReason(incentive.getFailedReason());
                    // 计算失败，直接返回
                    return calcResult;
                }
                incentive.setIncentiveTimes(this.calcIncentiveTimes(calcActivity));
                return CalcResult.SUCCESS_TRUE;

            case SCOPE:
            case PARAM:
            case INCENTIVE:
            default:
                return CalcResult.ERROR_DEFAULT;

        }
    }

    private int calcIncentiveTimes(CalcActivity calcActivity) {
        
        if (null == calcActivity || null == calcActivity.getCartActivity() || CollectionUtils.isEmpty(calcActivity.getCartActivity().getCouponCodes())) {
            return 0;
        }

        return calcActivity.getCartActivity().getCouponCodes().size();
    }
}
