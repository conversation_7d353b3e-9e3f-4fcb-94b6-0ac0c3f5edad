/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.calc.model;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;

import com.alibaba.fastjson.annotation.JSONField;
import com.gtech.commons.utils.BeanCopyUtils;
import com.gtech.commons.utils.ConvertUtils;
import com.gtech.commons.utils.StringUtil;
import com.gtech.promotion.calc.CalcActivity;
import com.gtech.promotion.code.activity.FlagTypeEnum;
import com.gtech.promotion.dto.in.activity.ShoppingCartActivity;
import com.gtech.promotion.dto.in.activity.ShoppingCartItem;
import com.gtech.promotion.vo.result.activity.ActivityGroupCache;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 购物车明细项（单品0101）
 *
 */
@Getter
@Setter
@ToString(callSuper = true)
public class CalcShoppingCartItem extends ShoppingCartItem implements Serializable {

    // serialVersionUID
    private static final long serialVersionUID = 1534473317118339574L;

    // Quantity of promotions participated in
    @Getter
    private int promoQuantity = 0;
    public void setPromoQuantity(int promoQuantity) {
        this.promoQuantity = promoQuantity;
    }

    // Promotion finally amount. (Initialize value from productAmount of shopping cart product item)
    @Getter
    private BigDecimal promoAmount;
    public void setPromoAmount(BigDecimal promoAmount) {
        this.promoAmount = promoAmount;
    }

    // 当前购物车参与的所有促销活动的优先级互斥叠加表达式
    private String activityExpr;

	private List<ActivityGroupCache> groupCacheList;

    // 当前计算活动
    private transient CalcActivity calcActivity;
    public void setCalcActivity(CalcActivity calcActivity) {
        this.calcActivity = calcActivity;
    }
    public CalcActivity getCalcActivity() {
        return this.calcActivity;
    }

    // Activity list of associated
    @Getter
    private transient List<CalcActivity> calcActivityList = new ArrayList<>();
    public final void addCalcActivityList(CalcActivity calcActivity){

        this.calcActivityList.add(calcActivity);
    }

    // 促销奖励信息
    @Getter
    private transient List<CalcActivityIncentive> activityIncentiveList = new ArrayList<>();
    public void addActivityIncentive(CalcActivityIncentive activityIncentive){

        this.activityIncentiveList.add(activityIncentive);
    }

    @JSONField(serialize = false)
    public String keyString() {

        return ConvertUtils.toString(this.getProductCode(), "") + ConvertUtils.toString(this.getSkuCode(), "") + ConvertUtils.toString(this.getQuantity(), "0");
    }

    /**
     * 记录商品属于捆绑活动的商品池（为了区分不同件数的商品属于相同的池，根据奖励次数和件数共同计算是否参与活动时使用-0404）
     * seqActivityMap:k：activityid；v：seqnum
     */
    @Getter
    private Map<String, String> seqActivityMap;
    public void addSeqActivityMap(String activityCode, String seqNum) {

        if (MapUtils.isEmpty(this.seqActivityMap)) {
            this.seqActivityMap = new HashMap<>();
            this.seqActivityMap.put(activityCode, seqNum);
        } else {
            this.seqActivityMap.put(activityCode, seqNum);
        }
    }
    
    public CalcShoppingCartItem() { 
        // No codes.
    }

    public CalcShoppingCartItem(ShoppingCartItem shoppingCartItem){

        super();

        String[] igonre = { "promoAmount", "calcActivityList" };
        BeanCopyUtils.copyProps(shoppingCartItem, this, igonre);
        this.promoAmount = shoppingCartItem.getProductAmount();
    }

    /**
     * 判断是否参与此活动
     */
    public boolean isExistActivity(String activityCode){
        if (CollectionUtils.isNotEmpty(this.getUsedActivitys()) && (StringUtil.isBlank(this.getSelectionFlag()) || FlagTypeEnum.YES.equalsCode(this.getSelectionFlag()))){
            for (ShoppingCartActivity shoppingCartActivity : this.getUsedActivitys()){
                if (shoppingCartActivity.getActivityCode().equals(activityCode)){
                    return true;
                }
            }
        }
        return false;
    }

    public CalcActivity getCurrentActivity(String activityCode) {

        for (int i = 0; i < calcActivityList.size(); i++) {
            CalcActivity ca = calcActivityList.get(i);
            if (ca.getActivityCode().equals(activityCode)) {
                return ca;
            }
        }

        return null;
    }

    public void setCalculateResult(String activityCode, BigDecimal beforeAmount, BigDecimal afterAmount) {

        CalcActivity ca = this.getCurrentActivity(activityCode);
        if (ca != null) {
            ca.setCalculateResult(beforeAmount, afterAmount);
        }
    }

    /**
     * 将该商品下的当前活动置为null
     */
    public void setCurrentActivityNull(String activityCode) {

        ArrayList<CalcActivity> list = new ArrayList<>();
        for (int i = 0; i < calcActivityList.size(); i++) {
            CalcActivity ca = calcActivityList.get(i);
            if (!ca.getActivityCode().equals(activityCode)) {
                list.add(ca);
            }
        }

        this.calcActivityList = list;
    }

    public String getCombineOrSkuCode(){
        return StringUtil.isBlank(getCombineSkuCode()) ? getSkuCode() : getCombineSkuCode();
    }

    /**
     * SeqActivityMap不为空，活动商品绑定关系不为空，且改商品属于该活动的该seqNum时返回true
     * 
     * @return true:该商品符合，计入 false：不计入
     */
    public boolean seqNumcheck(String seqNum){

        return MapUtils.isNotEmpty(this.getSeqActivityMap()) 
                        && StringUtil.isNotBlank(this.getSeqActivityMap().get(calcActivity.getActivityCode()))
                        && this.getSeqActivityMap().get(calcActivity.getActivityCode()).indexOf(seqNum) >= 0;
    }

}
