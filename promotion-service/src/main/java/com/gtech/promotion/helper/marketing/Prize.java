package com.gtech.promotion.helper.marketing;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class Prize {

    // 奖品编号
    private String prizeNo;
    // 奖品库存（剩余中奖次数）
    private Integer prizeInventory;
    // 概率
    private BigDecimal prizeProbability;

    private String prizeType;

    private boolean noInventory;
}
