package com.gtech.promotion.helper.marketing;

import java.util.List;

public abstract class AbstractDraw {

    private static AbstractDraw abstractDraw = null;
    public static final Prize EMPTY_PRIZE = Prize.builder().noInventory(true).build();

    public static AbstractDraw getDraw(){
        if (null == abstractDraw){
            abstractDraw = new NumberSectionDraw();
        }
        return abstractDraw;
    }

    public boolean haveInventory(List<Prize> prizes){
        long total = 0L;
        for (Prize prize : prizes) {
            total += prize.getPrizeInventory();
        }
        return total > 0;
    }

    public abstract Prize lottery(List<Prize> prizes);

}
