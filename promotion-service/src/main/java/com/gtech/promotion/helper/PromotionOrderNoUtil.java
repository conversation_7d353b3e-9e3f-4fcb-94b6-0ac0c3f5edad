/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.helper;
import com.gtech.promotion.utils.NumberUtil;

/**
 * 促销订单编号工具类
 */
public class PromotionOrderNoUtil {
	private PromotionOrderNoUtil() {
		throw new IllegalStateException("Utility class");
	}
    /**
     * 生成促销订单编号
     * 规则:7位类订单号 + 4位用户编码 + 3位（tenantCode）+ 3位随机数 + 1位校验码 = 18位
     * @param orderId 订单号
     * @param userCode 用户编码
     * @param tenantCode 系统商户编码
     * @return 活动编码
     */
    public static String createCode(String orderId, String userCode , String tenantCode){
        String order = createLengthStr(orderId, 7);
        String tenant = String.format("%03d",Long.parseLong(tenantCode) % 1000);
        String user = "";
        try {
            Integer.parseInt(userCode);
            user = createLengthStr(userCode, 4);
        }catch (Exception e){
            user = createLengthStr("", 4);
        }
        String code = order + user + tenant + NumberUtil.getRandomNumbers(3);
        return code + CodeHelper.getCheckSum(code);
    }

    /**
     * 取字符串后几位，不够随机补充
     * @param str
     * @param length
     * @return
     */
    private static String createLengthStr(String str, int length){
        int len = str.length();
        if (len >= length){
            return str.substring(len - length);
        }else{
            String randomNumbers = NumberUtil.getRandomNumbers(length - len);
            return str + randomNumbers;
        }
    }

}
