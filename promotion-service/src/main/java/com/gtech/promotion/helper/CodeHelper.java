/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR <PERSON><PERSON>RANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.helper;

import org.apache.commons.lang3.StringUtils;

import com.gtech.promotion.checker.SystemChecker;
import com.gtech.promotion.exception.PromotionException;

/**
 * 
 *
 * <AUTHOR>
 * @Date 2020-02-23
 */
public class CodeHelper {
    
    private CodeHelper() {
        // No codes.
    }

    private static final long STARTTIME = 1483200000000000L;//2017.1.1 0:0:0:000:000

    // 系数因子20位
    private static int[] factors = new int[]{
            13,4,19,7,5,5,7,2,4,2,8,4,11,4,7,4,2,14,17,23,
            13,4,19,7,5,5,7,2,4,2,8,4,11,4,7,4,2,14,17,23
    };

    /**
     * 获取校验位
     * 
     * @param string 待获取校验位的字符串
     * @return 校验位
     */
    static String getCheckSum(String string) {

        if (string.length() > factors.length) {
            throw new PromotionException(SystemChecker.TOLONG_STRING.getCode(), "字符串长度不能超过" + factors.length);
        }

        int sum = 0;
        for (int i = 0; i < string.length(); i++) {
            int t = Integer.parseInt(String.valueOf(string.charAt(i)));
            sum += t * factors[i];
        }

        return String.valueOf(sum % 9);
    }

    /**
     * 生成活动编码
     * 规则:1位类型（1：活动 2：券）+ 2位模板标签id + 13位毫秒数（当前距离2017/1/1 0:0:0的毫秒数） + 3位随机数 + 1位校验码 = 20位
     * 
     * @param templateId 模板id
     * @param type 活动类型 1：活动 2：券
     * @return 活动编码
     */
    public static String newActivityCode(String templateTagCode, String type) {

        templateTagCode = String.format("%02d", (Integer.parseInt(templateTagCode) % 100));
        long current = currentTimeMicro() - STARTTIME;
        String ms = String.format("%016d", current);

        StringBuilder sb = new StringBuilder();
        sb.append(type).append(templateTagCode).append(ms);
        String code = getCheckSum(sb.toString());
        return sb.append(code).toString();
    }

    private static long currentTimeMicro() {

        return (System.currentTimeMillis() * 1000) + (System.nanoTime() % 1000000 / 1000);
    }

    public static int compareActivityCode(String code1, String code2) {

        if (StringUtils.isBlank(code1) || code1.length() < 3) {
            return -1;
        }
        if (StringUtils.isBlank(code2) || code2.length() < 3) {
            return 1;
        }

        return code1.substring(3).compareTo(code2.substring(3));
    }

    /**
     * 验证字符串最后一位校验位是否合法
     * 
     * @param str 活动编码
     * @return true：合法
     */
    public static boolean validateCode(String str){

        if (str.length() > factors.length + 1) {
            return false;
        }

        String substring = String.valueOf(str.charAt(str.length() - 1));
        int sum = 0;
        for (int i = 0; i < str.length() - 1; i++) {
            int t = Integer.parseInt(String.valueOf(str.charAt(i)));
            sum += t * factors[i];
        }

        return substring.equals(String.valueOf(sum % 9));
    }

}
