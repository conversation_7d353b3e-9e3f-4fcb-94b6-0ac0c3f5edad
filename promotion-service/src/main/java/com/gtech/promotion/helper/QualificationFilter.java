package com.gtech.promotion.helper;

import com.gtech.promotion.code.activity.IsExcludeEnum;
import com.gtech.promotion.dao.model.activity.QualificationModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class QualificationFilter {

    public static final String MEMBER_TAG_CODE = "memberTagCode";
    // 活动定义的参与资格
    private List<QualificationModel> qualificationModels;

    public boolean filter(List<QualificationModel> list){

        if (CollectionUtils.isEmpty(qualificationModels)){
            return true;
        }

        String isExclude = qualificationModels.get(0).getIsExclude();

        if (null != isExclude && IsExcludeEnum.EXCLUDE.code().equals(isExclude)){

            boolean flag = true;
            for (QualificationModel qualificationModel : list) {
                String qualificationCode = qualificationModel.getQualificationCode();
                if (qualificationCode.equals(MEMBER_TAG_CODE)){
                    flag = false;
                    break;
                }
            }
            if (flag){
                QualificationModel qualificationModel = new QualificationModel();
                qualificationModel.setQualificationCode(MEMBER_TAG_CODE);
                qualificationModel.setQualificationValue(MEMBER_TAG_CODE);
                list.add(qualificationModel);
            }
        }

        Map<String, List<QualificationModel>> collect =
                qualificationModels.stream().collect(Collectors.groupingBy(QualificationModel::getQualificationCode));

        setDefaultMember(collect, list);

        if (CollectionUtils.isEmpty(list)){

            if (null != isExclude && IsExcludeEnum.EXCLUDE.code().equals(isExclude)){
                return true;
            }
            return false;
        }
        Map<String, List<QualificationModel>> paramMap =
                list.stream().collect(Collectors.groupingBy(QualificationModel::getQualificationCode));

        for (Map.Entry<String, List<QualificationModel>> entry : collect.entrySet()) {
            boolean flag = false;
            for (QualificationModel qualificationModel : list) {

                if (qualificationModel.getQualificationCode().equals(entry.getKey())){
                    List<QualificationModel> value = entry.getValue();
                    if (!CollectionUtils.isEmpty(value)){
                        if (IsExcludeEnum.EXCLUDE.code().equals(value.get(0).getIsExclude())){
                            //入参
                            List<QualificationModel> models = paramMap.get(MEMBER_TAG_CODE);
                            for (QualificationModel model : models) {
                                flag = entry.getValue().stream().noneMatch(x -> x.getQualificationValue().equals(model.getQualificationValue()));
                                if (!flag){
                                    return false;
                                }
                            }
                        } else {
                            boolean anyMatch = entry.getValue().stream().anyMatch(x -> x.getQualificationValue().equals(qualificationModel.getQualificationValue()));
                            if (anyMatch){
                                flag = true;
                                break;
                            }
                        }
                    }
                }
            }
            if (!flag){
                return false;
            }
        }
        return true;
    }

    private void setDefaultMember(Map<String, List<QualificationModel>> collect, List<QualificationModel> list) {
        String isMember = "isMember";
        if (!collect.containsKey(isMember)) {
            return;
        }
        if (CollectionUtils.isEmpty(list) || list.stream().noneMatch(x->x.getQualificationCode().equals(isMember))){
            QualificationModel qualificationModel = new QualificationModel();
            qualificationModel.setQualificationCode(isMember);
            qualificationModel.setQualificationValue("1");
            list.add(qualificationModel);
        }
    }
}
