package com.gtech.promotion.helper;

import java.util.Collections;
import java.util.UUID;
import java.util.concurrent.TimeUnit;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.script.DefaultRedisScript;
import org.springframework.data.redis.core.script.RedisScript;
import org.springframework.scripting.support.StaticScriptSource;
import org.springframework.stereotype.Component;

import com.gtech.commons.utils.StringUtil;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component
public class RedisLock {

	@Autowired
	private StringRedisTemplate redisTemplate;

	private static final DefaultRedisScript<Object> UNLOCK_SCRIPT;

	static {

		String unlock = "if redis.call('get', KEYS[1]) == ARGV[1] then " + "return redis.call('del', KEYS[1]) "
				+ "else " + "return 0 " + "end";
		// 加载释放锁的脚本
		UNLOCK_SCRIPT = new DefaultRedisScript<>();
		UNLOCK_SCRIPT.setScriptSource(new StaticScriptSource(unlock));
		UNLOCK_SCRIPT.setResultType(Object.class);
	}

	public RedisLock(StringRedisTemplate redisTemplate) {
		this.redisTemplate = redisTemplate;
	}

	/**
	 * 获取锁
	 *
	 * @param lockName    锁名称
	 * @param releaseTime 超时时间(单位:毫秒)
	 * @return key 解锁标识
	 */
	public String tryLock(String lockName, Long releaseTime) {
		// 存入的线程信息的前缀，防止与其它JVM中线程信息冲突
		String key = UUID.randomUUID().toString();
		Boolean flag = redisTemplate.opsForValue().setIfAbsent(lockName, key, releaseTime, TimeUnit.MILLISECONDS);
		if (null != flag && flag) {
			return key;
		} else {
			return null;
		}
	}

	/**
	 * 获取锁,获取失败重试
	 *
	 * @param lockName    锁名称
	 * @param releaseTime 超时时间(单位:毫秒)
	 * @param retryCount  重试次数
	 * @return key 解锁标识
	 */
	public String tryLockAndRetry(String lockName, Long releaseTime, int retryCount) {
		String key = tryLock(lockName, releaseTime);
		if (StringUtil.isBlank(key)) {
			while (retryCount > 0) {
				try {
					retryCount--;
					Thread.sleep(500);
				} catch (InterruptedException e) {
					log.warn("Interrupted!", e);
					Thread.currentThread().interrupt();
				}
				key = tryLock(lockName, releaseTime);
				if (StringUtil.isNotBlank(key)) {
					break;
				}
			}
		}
		return key;
	}

	/**
	 * 释放锁
	 *
	 * @param lockName 锁名称
	 * @param key      解锁标识
	 */
	public void unlock(String lockName, String key) {
//        // 执行脚本
//        redisTemplate.execute(
//                UNLOCK_SCRIPT,
//                Collections.singletonList(lockName),
//                key, null)
		// Redis 执行 Lua 脚本抛出 StatusOutput does not support set(long) 异常
		redisTemplate.execute(RedisScript.of(
				"if redis.call('get', KEYS[1]) == ARGV[1] then return redis.call('del', KEYS[1]) else return 0 end",
				Long.class), Collections.singletonList(lockName), key, null);

	}

}
