/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * <PERSON>ECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.helper;

import com.gtech.commons.utils.ConvertUtils;
import com.gtech.commons.utils.StringUtil;
import com.gtech.promotion.code.PromotionConstants;
import com.gtech.promotion.code.activity.ActivityTypeEnum;
import lombok.*;
import org.apache.commons.lang3.StringUtils;

/**
 * ActivityCodeComparator
 *
 * <AUTHOR>
 * @Date 2020-03-30
 */
@Getter
@Setter
@ToString
@EqualsAndHashCode
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ActivityCodeComparator implements Comparable<ActivityCodeComparator> {

    // Activity code.
    private String activityCode;

    // Activity extend code.
    private String activityExtCode;

    // Activity type.
    private String activityType;
    public boolean isCouponActivity() {

        return ActivityTypeEnum.COUPON.equalsCode(activityType);
    }

    // Template code.
    private String templateCode;
    public String expressionTemplateCode() {
        
        return this.isCouponActivity() ? "2_" + templateCode : "1_" + templateCode;
    }

	// Group priority
	private Integer groupPriority;
	public int getGroupPriority() {

		return ConvertUtils.toInteger(this.groupPriority, PromotionConstants.DEF_PRIORITY);
	}

    // Activity priority.
    private Integer priority;
    public int getPriority() {

        return ConvertUtils.toInteger(this.priority, PromotionConstants.DEF_PRIORITY);
    }

    // High priority activity. (Selected in shopping cart.)
    private Boolean highPrioriy;
    public boolean isHighPriority() {

        return Boolean.TRUE.equals(this.highPrioriy);
    }

    private String activityExpr;
    //表达式 0-或者 分组-1
    private String priorityMode;

    //常量
    public static final String EXPRESSION = "0";
    public static final String GROUP = "1";


    /**
     * this.[priority] > o.[priority] ==> 1
     * this.[priority] = o.[priority] ==> 0
     * this.[priority] < o.[priority] ==> -1
     */
    @Override
    public int compareTo(ActivityCodeComparator o) {
        if (getResultByActivityCode(o)) return 0; // Ignore
		if (this.getGroupPriority() != o.getGroupPriority()) {
			return this.getGroupPriority() > o.getGroupPriority() ? -1 : 1;
		}
        //分组优先级相同，再比较高优先级 非分组的租户防止空指针修改
        if (null != this.priorityMode && this.priorityMode.equals(GROUP)){
            if (this.isHighPriority() && !o.isHighPriority()) {
                return 1;
            }
            if (!this.isHighPriority() && o.isHighPriority()) {
                return -1;
            }
        }

		if (this.getPriority() != o.getPriority()) {
			return this.getPriority() > o.getPriority() ? -1 : 1;
		}

        Integer x = getIntegerByActivityExtCode(o);
        if (x != null) return x;

        if (this.isHighPriority() && !o.isHighPriority()) {
            return 1;
        }
        if (!this.isHighPriority() && o.isHighPriority()) {
            return -1;
        }

		if (StringUtils.isNotBlank(this.activityExpr) && this.groupPriority == null) {
            return this.activityExpr.indexOf(activityCode) < this.activityExpr.indexOf(o.activityCode) ? 1 : -1;
        }

		if (this.getPriority() == o.getPriority() || this.getGroupPriority() == o.getGroupPriority()) {
            return CodeHelper.compareActivityCode(this.activityCode, o.getActivityCode());
        }

        return this.getPriority() > o.getPriority() ? -1 : 1;
    }

	public static void main(String[] args) {
		System.out.println("20301063240766271323".substring(3));
	}

    public Integer getIntegerByActivityExtCode(ActivityCodeComparator o) {
        if (this.activityCode.equals(o.activityCode)) {
            if (getReturnBoolean(o)) return activityExtCode.compareTo(o.getActivityExtCode());
            return 0;
        }
        return null;
    }

    public boolean getReturnBoolean(ActivityCodeComparator o) {
        return StringUtil.isNotBlank(activityExtCode) && StringUtil.isNotBlank(o.getActivityExtCode());
    }

    public boolean getResultByActivityCode(ActivityCodeComparator o) {
        return null == o || StringUtil.isBlank(activityCode) || StringUtil.isBlank(o.getActivityCode());
    }

}
