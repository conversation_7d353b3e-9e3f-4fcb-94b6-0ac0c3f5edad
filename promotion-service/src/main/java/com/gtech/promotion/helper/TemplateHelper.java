/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.helper;

import com.alibaba.fastjson.TypeReference;
import com.gtech.commons.utils.CheckUtils;
import com.gtech.commons.utils.LocalCacheUtil;
import com.gtech.commons.utils.StringUtil;
import com.gtech.promotion.calc.function.FunctionEnum;
import com.gtech.promotion.code.activity.FunctionParamUnitEnum;
import com.gtech.promotion.dao.model.activity.TemplateModel;
import com.gtech.promotion.exception.ErrorCodes;
import com.gtech.promotion.service.activity.TemplateService;
import com.gtech.promotion.utils.NumberUtil;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 * @Date 2020-03-11
 */
@Component
public class TemplateHelper {

    private static final String PARAM_VALUE = "paramValue";
    private static final String PARAM_UNIT = "paramUnit";
    private static final Long TEMPLATE_MODEL_TIMEOUT = 600L;
    private static final String TEMPLATE_MODEL_MAP = "templateModelMap";

    @Autowired
    private TemplateService templateService;

    /**
     * Get all template model map. 
     * @return Map<templateCode, TemplateModel>
     */
    public Map<String, TemplateModel> getTemplateModelMap() {
        
        Map<String, TemplateModel> modelMap = LocalCacheUtil.load(TEMPLATE_MODEL_MAP, new TypeReference<Map<String, TemplateModel>>(){});
        if (MapUtils.isNotEmpty(modelMap)) {
            return modelMap;
        }

        List<TemplateModel> modelList = templateService.queryTemplateAll();
        if (CollectionUtils.isEmpty(modelList)) {
            return Collections.emptyMap();
        }

        modelMap = new HashMap<>();
        for(TemplateModel m : modelList) {
            modelMap.put(m.getTemplateCode(), m);
        }

        LocalCacheUtil.save(TEMPLATE_MODEL_MAP, modelMap, TEMPLATE_MODEL_TIMEOUT);
        return modelMap;
    }

    /**
     * templateCode ==> tagCode
     */
    public String templateCode2TagCode(String templateCode) {
        
        Map<String, TemplateModel> modelMap = this.getTemplateModelMap();
        if (MapUtils.isEmpty(modelMap)) {
            return null;
        }

        TemplateModel model = modelMap.get(templateCode);
        if (null == model) {
            return null;
        }

        return model.getTagCode();
    }

    /**
     * @return paramValue (Maybe rectified.)
     */
    public static void validateFunctionParam(String functionCode, String paramUnit, String paramValue) {

        FunctionEnum function = FunctionEnum.code2Enum(functionCode);
        if (null != function) {
            switch (function) {

                case F0303: // 条件参数 - 金额
                case F0401: // 促销奖励 - 减金额
                case F0403: // 促销奖励 - 单件固定金额
                case F0404: // 促销奖励 - 组合固定金额
                case F0412: // 促销奖励 - 买A范围商品B范围商品减金额
                case F0414: // 促销奖励 - 每件减固定金额
                case F0415: // 促销奖励 - 邮费减金额

                    CheckUtils.isTrue(NumberUtil.checkMoney(paramValue, null), ErrorCodes.PARAM_ERROR, PARAM_VALUE);
                    CheckUtils.isTrue(FunctionParamUnitEnum.AMOUNT.equalsCode(paramUnit), ErrorCodes.PARAM_ERROR, PARAM_UNIT);
                    return;

                case F0402: // 促销奖励 - 打折扣
                case F0413: // 促销奖励 - 买A范围商品B范围商品打折扣
                case F0405: // 促销奖励 - 邮费打折

                    CheckUtils.isTrue(NumberUtil.checkDiscount(paramValue), ErrorCodes.PARAM_ERROR, PARAM_VALUE);
                    CheckUtils.isTrue(FunctionParamUnitEnum.DISCOUNT.equalsCode(paramUnit), ErrorCodes.PARAM_ERROR, PARAM_UNIT);
                    return;

                case F0302: // 条件参数 - 数量
                case F0406: // 促销奖励 - 送赠品
                case F0407: // 促销奖励 - 满送
                case F0408: // 促销奖励 - 满送

                    CheckUtils.isTrue(NumberUtil.isPositiveLongValue(paramValue), ErrorCodes.PARAM_ERROR, PARAM_VALUE);
                    CheckUtils.isTrue(FunctionParamUnitEnum.COUNT.equalsCode(paramUnit), ErrorCodes.PARAM_ERROR, PARAM_UNIT);
                    return;

                case F0301: // 条件参数 - 无

                    CheckUtils.isTrue(StringUtil.isBlank(paramValue), ErrorCodes.PARAM_ERROR, PARAM_VALUE);
                    CheckUtils.isTrue(FunctionParamUnitEnum.NULL.equalsCode(paramUnit), ErrorCodes.PARAM_ERROR, PARAM_UNIT);
                    return;

                default:
                    break;
            }
        }
    }
}
