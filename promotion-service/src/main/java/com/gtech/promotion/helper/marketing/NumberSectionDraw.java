package com.gtech.promotion.helper.marketing;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ThreadLocalRandom;

public class NumberSectionDraw extends AbstractDraw {

    @Override
    public Prize lottery(List<Prize> prizes) {
        if (!haveInventory(prizes)){
            return EMPTY_PRIZE;
        }
        List<Integer> section = new ArrayList<>();
        section.add(1);
        int sectionTemp = 1;
        for (int i = 0; i < prizes.size(); i++) {
            Prize prize = prizes.get(i);
            BigDecimal multiply = prize.getPrizeProbability().multiply(new BigDecimal("100"));
            sectionTemp += multiply.intValue();
            section.add(sectionTemp);
        }

        int e = 10001;
        if (!section.get(section.size() - 1).equals(e)){
            section.add(e);
            prizes.add(null);
        }

        ThreadLocalRandom random = ThreadLocalRandom.current();
        int randomInt = random.nextInt(1, e);

        //判断取到的随机数在哪个奖品的概率区间中
        for (int i = 0; i < section.size() - 1; i++) {
            if(randomInt >= section.get(i)
                    && randomInt < section.get(i + 1)){
                return prizes.get(i);
            }
        }
        return null;
    }
}
