/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 * <p>
 * This software is the confidential and proprietary information of GTech. You shall not disclose such
 * Confidential Information and shall use it only in accordance with the terms of the license agreement
 * you entered into with GTech.
 * <p>
 * <PERSON><PERSON><PERSON> MAKES NO REPRESENTATIONS OR WAR<PERSON>NTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.helper;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.gtech.commons.utils.DateUtil;
import com.gtech.commons.utils.StringUtil;
import com.gtech.promotion.RedisClient;
import com.gtech.promotion.domain.coupon.CouponReleaseDomain;
import com.gtech.promotion.dto.cache.ActivityCacheDTO;
import com.gtech.promotion.utils.Constants;
import com.gtech.promotion.utils.MarketingConstants;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.ZSetOperations;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.TimeUnit;

/**
 * <功能描述>
 *
 */
@Service
public class ActivityRedisHelpler {

    @Autowired
    private RedisClient redisClient;

    private static final String TENANT_CODE = "TENANTCODE";

    private static final String ACTIVITY_CODE = "ACTIVITYCODE";

    private static final String MARKETING_GROUP_CODE = "MARKETINGGROUPCODE";

    private static final String RELEASE_CODE = "RELEASECODE";

    private static final String RELEASE_TIME = "RELEASETIME";

    private static final String MAX = "MAX";

    private static final String START = "START";

    private static final String END = "END";

    private static final String USER_CODE = "USERCODE";

    private static final String ACTIVITY_TYPE = "ACTIVITYTYPE";

    private static final String STATUS = "STATUS";


    //删除缓存
    public void deleteRedisByKey(String key){
        redisClient.delete(key);

    }


    //模糊匹配删除缓存
    public void deleteFuzzyMatch(String pattern){

        redisClient.deleteFuzzyMatch(pattern);

    }

    //模糊匹配该拼团
    public String  queryFuzzyMatchingMarketingGroupUserKey(String tenantCode, String activityCode,String marketingGroupCode) {

        return MarketingConstants.MARKETING_GROUP_USER_CACHE + ":" + TENANT_CODE + "=" + tenantCode
                + ":" + ACTIVITY_CODE + "=" + activityCode
                + ":" + MARKETING_GROUP_CODE + "=" + marketingGroupCode;
    }


    //获取拼团key
    public String  getMarketingGroupUserKey(String tenantCode, String activityCode,String marketingGroupCode) {

        return MarketingConstants.MARKETING_GROUP_USER_CACHE + ":" + TENANT_CODE + "=" + tenantCode
                + ":" + ACTIVITY_CODE + "=" + activityCode
                + ":" + MARKETING_GROUP_CODE + "=" + marketingGroupCode;
    }

    public void  setMarketingGroupCode(String tenantCode, String activityCode,String marketingGroupCode,  String effectiveTime,String content){

        String marketingGroupUserKey = getMarketingGroupUserKey(tenantCode, activityCode, marketingGroupCode);

        // 当前时间戳
        long currentTime = System.currentTimeMillis() / 1000;
        //有效时间
        long endTime = DateUtil.parseDate(effectiveTime, DateUtil.FORMAT_YYYYMMDDHHMISS_14).getTime() / 1000;

        //剩余有效时间，自动过期时间
        long time = endTime - currentTime;

        redisClient.setStringValue(marketingGroupUserKey, JSON.toJSONString(content), time, TimeUnit.SECONDS);
    }


    public void addCouponDelayRelease(String releaseCode, String tenantCode, String activityCode, String releaseTime, Integer releaseQuantity, String content) {

        String keyTime = getDelayReleaseKey(releaseCode, tenantCode, activityCode, releaseTime, releaseQuantity);
        long endTime = DateUtil.parseDate(releaseTime, DateUtil.FORMAT_YYYYMMDDHHMISS_14).getTime();
        redisClient.setStringValue(keyTime, content, endTime - System.currentTimeMillis(), TimeUnit.MILLISECONDS);
    }

    public void deleteCouponDelayRelease(String releaseCode, String tenantCode, String activityCode, String releaseTime, Integer releaseQuantity) {

        String keyTime = getDelayReleaseKey(releaseCode, tenantCode, activityCode, releaseTime, releaseQuantity);
        redisClient.delete(keyTime);
    }

    /**
     * 预约投放的key
     */
    private String getDelayReleaseKey(String releaseCode, String tenantCode, String activityCode, String releaseTime, Integer releaseQuantity) {

        return Constants.PROMOTION_COUPON_RELEASE_TIME + ":" + TENANT_CODE + "=" + tenantCode + ":" + ACTIVITY_CODE + "=" + activityCode + ":" + RELEASE_CODE + "=" + releaseCode + ":"
                + RELEASE_TIME + "=" + releaseTime + ":" + MAX + "=" + releaseQuantity;
    }

    public String getDelayReleaseKeyValue(String releaseCode, String tenantCode, String activityCode, String releaseTime, Integer releaseQuantity) {

        return redisClient.getString(getDelayReleaseKey(releaseCode, tenantCode, activityCode, releaseTime, releaseQuantity));
    }

    public void addCoupon(Set<ZSetOperations.TypedTuple<String>> cacheCet, String releaseCode, String tenantCode, String activityCode, String receiveStartTime, String receiveEndTime,
                          Integer releaseQuantity) {

        String key = Constants.PROMOTION_COUPON_CODE_CACHE + ":" + TENANT_CODE + "=" + tenantCode + ":" + ACTIVITY_CODE + "=" + activityCode + ":" + RELEASE_CODE + "=" + releaseCode
                + ":" + START + "=" + receiveStartTime + ":" + END + "=" + receiveEndTime + ":"
                + MAX + "=" + releaseQuantity;
        redisClient.addZSet(key, cacheCet);
        long endTime = DateUtil.parseDate(receiveEndTime, DateUtil.FORMAT_YYYYMMDDHHMISS_14).getTime();
        redisClient.expire(key, endTime - System.currentTimeMillis(), TimeUnit.MILLISECONDS);
    }

    public Set<String> getAllReleaseKesByActivityCode(String tenantCode, String activityCode) {

        return redisClient.scan(Constants.PROMOTION_COUPON_CODE_CACHE + ":" + TENANT_CODE + "=" + tenantCode + ":" + ACTIVITY_CODE + "=" + activityCode + ":" + RELEASE_CODE + "=*");
    }

    public String getExpression(String tenantCode) {

        return redisClient.getString(Constants.PROMOTION_ACTIVITY_EXPRESSION.concat(":").concat(tenantCode));
    }

    public void setExpression(String tenantCode, String expression) {

        String key = Constants.PROMOTION_ACTIVITY_EXPRESSION.concat(":" + tenantCode);
        redisClient.setStringValue(key, expression);
    }

    public Map<Object, Object> getActivitySetting(String tenantCode, String settingType) {

        Map<Object, Object> hashMap = redisClient.getHashMap(Constants.PROMOTION_ACTIVITY_SETTING.concat(":").concat(tenantCode).concat(":").concat(settingType));
        if (CollectionUtils.isEmpty(hashMap)) {
            hashMap = new HashMap<>();
        }
        return hashMap;
    }

    public void setActivitySetting(String tenantCode, Integer settingType, String settingCode, String settingValue) {

        String key = Constants.PROMOTION_ACTIVITY_SETTING.concat(":").concat(tenantCode).concat(":").concat(settingType.toString());
        redisClient.putHash(key, settingCode, settingValue);
    }

    public void setActivityCacheLevelTwo(String tenantCode, String hashKey, String value) {

        String key = Constants.PROMOTION_ACTIVITY_CACHE_LEVEL_TWO + ":" + TENANT_CODE + "=" + tenantCode;
        redisClient.putHash(key, hashKey, value);
        redisClient.expire(key, 5L, TimeUnit.MINUTES);
    }

    public String getActivityCacheLevelTwo(String tenantCode, String hashKey) {

        Object value = redisClient.getHashValue(Constants.PROMOTION_ACTIVITY_CACHE_LEVEL_TWO + ":" + TENANT_CODE + "=" + tenantCode, hashKey);
        if (value != null) {
            return value.toString();
        }
        return null;
    }

    public void removeActivityCacheLevelTwo(String tenantCode) {

        redisClient.delete(Constants.PROMOTION_ACTIVITY_CACHE_LEVEL_TWO + ":" + TENANT_CODE + "=" + tenantCode);
    }

    public void removeActivityCache(String tenantCode, String activityType, String activityCode, String activityStatus) {

        String key = Constants.PROMOTION_ACTIVITY_CACHE + ":" + TENANT_CODE + "=" + tenantCode + ":" + ACTIVITY_TYPE + "=" + activityType + ":" + ACTIVITY_CODE + "=" + activityCode
                + ":" + STATUS + "=" + activityStatus;
        redisClient.delete(key);

    }

    public void setActivityCache(ActivityCacheDTO activityCache) {

        // 当前时间戳
        long currentTime = System.currentTimeMillis() / 1000;
        //活动结束时间
        long endTime = DateUtil.parseDate(activityCache.getActivityModel().getActivityEnd(), DateUtil.FORMAT_YYYYMMDDHHMISS_14).getTime() / 1000;
        long time = endTime - currentTime;
        String tenantCode = activityCache.getActivityModel().getTenantCode();
        String activityType = activityCache.getActivityModel().getActivityType();
        String activityStatus = activityCache.getActivityModel().getActivityStatus();
        String activityCode = activityCache.getActivityModel().getActivityCode();
        String key = Constants.PROMOTION_ACTIVITY_CACHE + ":" + TENANT_CODE + "=" + tenantCode + ":" + ACTIVITY_TYPE + "=" + activityType + ":" + ACTIVITY_CODE + "=" + activityCode
                + ":" + STATUS + "=" + activityStatus;
        redisClient.setStringValue(key, JSON.toJSONString(activityCache), time, TimeUnit.SECONDS);
    }

    public String getActivityCache111(String tenantCode, String activityType, String activityCode, String activityStatus) {

        String key = Constants.PROMOTION_ACTIVITY_CACHE
                + ":" + TENANT_CODE + "=" + tenantCode
                + ":" + ACTIVITY_TYPE + "=" + activityType
                + ":" + ACTIVITY_CODE + "=" + activityCode
                + ":" + STATUS + "=" + activityStatus;

        return redisClient.getString(key);
    }

    public String getReleaseCoupon(CouponReleaseDomain releaseDomain) {

        String key = Constants.PROMOTION_COUPON_CODE_CACHE
                + ":" + TENANT_CODE + "=" + releaseDomain.getTenantCode()
                + ":" + ACTIVITY_CODE + "=" + releaseDomain.getActivityCode()
                + ":" + RELEASE_CODE + "=" + releaseDomain.getReleaseCode()
                + ":" + START + "=" + releaseDomain.getReceiveStartTime()
                + ":" + END + "=" + releaseDomain.getReceiveEndTime()
                + ":" + MAX + "=" + releaseDomain.getReleaseQuantity();

        return redisClient.getFirstStringValueFormZSet(key);
    }

    public int getCouponUserCount111(String tenantCode, String activityCode, String userCode) {

        return redisClient.getInt(getCouponUserCountKey111(tenantCode, activityCode, userCode));
    }

    public void addCouponUserCount111(String tenantCode, String activityCode, String userCode, Long expireTimeMS) {

        String limitKey = getCouponUserCountKey111(tenantCode, activityCode, userCode);
        if (expireTimeMS == null) {
            redisClient.increment(limitKey, 1);
        } else {
            redisClient.setStringValue(limitKey, "1", expireTimeMS, TimeUnit.MILLISECONDS);
        }
    }

    /**
     * 用户领券成功后记录活动领取次数的key
     */
    private String getCouponUserCountKey111(String tenantCode, String activityCode, String userCode) {

        return Constants.PROMOTION_COUPON_LIMIT_USER + ":" + TENANT_CODE + "=" + tenantCode + ":" + ACTIVITY_CODE + "=" + activityCode + ":" + USER_CODE + "=" + userCode;
    }

    public int getCouponReleaseLimit(String tenantCode, String activityCode, String releaseCode) {

        return redisClient.getInt(getCouponReleaseLimitKey(tenantCode, activityCode, releaseCode));
    }

    public void addCouponReleaseLimit(String tenantCode, String activityCode, String releaseCode, Long expireTimeMS) {

        String limitKey = getCouponReleaseLimitKey(tenantCode, activityCode, releaseCode);
        if (expireTimeMS == null) {
            redisClient.increment(limitKey, 1);
        } else {
            redisClient.setStringValue(limitKey, "1", expireTimeMS, TimeUnit.MILLISECONDS);
        }
    }

    public String getCouponReleaseLimitKey(String tenantCode, String activityCode, String releaseCode) {

        return Constants.PROMOTION_COUPON_LIMIT_RELEASE + ":" + TENANT_CODE + "=" + tenantCode + ":" + ACTIVITY_CODE + "=" + activityCode + ":" + RELEASE_CODE + "=" + releaseCode;
    }

    public String getCouponReleaseLimitKeyValue(String tenantCode, String activityCode, String releaseCode) {

        return redisClient.getString(getCouponReleaseLimitKey(tenantCode, activityCode, releaseCode));
    }

    // 解锁用户使用券：当用户处于券使用状态，订单结束前不可以使用新的券
    public void unlockCouponUser(String tenantCode, String activityCode, String userCode) {

        String key = Constants.PROMOTION_COUPON_LIMIT_USER + ":" + TENANT_CODE + "=" + tenantCode + ":" + ACTIVITY_CODE + "=" + activityCode + ":USERCODE=" + userCode;
        //查询现有缓存资源值 
        String limitValueStr = redisClient.getString(key);
        if (StringUtil.isNotBlank(limitValueStr)) {
            //资源回滚  奖励次数
            redisClient.increment(key, -1);
        }
    }

    public void unlockCouponReleaseLimit(String tenantCode, String activityCode, String releaseCode) {

        String key = getCouponReleaseLimitKey(tenantCode, activityCode, releaseCode);
        String limitValueStr = redisClient.getString(key);
        if (StringUtil.isNotBlank(limitValueStr)) {
            //资源回滚  奖励次数
            redisClient.increment(key, -1);
        }
    }

    public long getDelayReleaseMakeKeyValue(String releaseCode, String tenantCode, String activityCode, String receiveStartTime, String receiveEndTime, Integer releaseQuantity) {

        return redisClient.getZSetSize(getDelayReleaseMakeKey(releaseCode, tenantCode, activityCode, receiveStartTime, receiveEndTime, releaseQuantity));
    }

    private String getDelayReleaseMakeKey(String releaseCode, String tenantCode, String activityCode, String receiveStartTime, String receiveEndTime, Integer releaseQuantity) {

        return Constants.PROMOTION_COUPON_CODE_CACHE + ":" + TENANT_CODE + "=" + tenantCode + ":" + ACTIVITY_CODE + "=" + activityCode + ":" + RELEASE_CODE + "=" + releaseCode + ":"
                + START + "=" + receiveStartTime + ":" + END + "=" + receiveEndTime + ":" + MAX + "=" + releaseQuantity;
    }

    public void getCouponUserCountKeyValue111(String tenantCode, String activityCode, String userCode, Integer count) {

        String limitKey = getCouponUserCountKey111(tenantCode, activityCode, userCode);
        String value = redisClient.getString(limitKey);
        if (count > 0 && StringUtil.isBlank(value)) {
            redisClient.setStringValue(limitKey, count + "");
        }
    }

    public void setCouponReleaseLimitKeyValue(String releaseLimitKey, String value) {

        redisClient.setStringValue(releaseLimitKey, value);
    }

    public void addTemplateTags(Map<String, String> templateTagMap) {

        redisClient.putAllHash(Constants.PROMOTION_TEMPLATE_TAG, templateTagMap);
    }

    public String getTemplateTag(String tagCode) {

        final Object hashValue = redisClient.getHashValue(Constants.PROMOTION_TEMPLATE_TAG, tagCode);
        if (hashValue != null) {
            return hashValue.toString();
        }
        return null;
    }

    public void setRedisProductActivity(String tenantCode, String productCode, String orgCode, String value, Long validEndTime) {

        redisClient.setStringValue(Constants.PROMOTION_SPU_ACTIVITY_CACHE + ":" + tenantCode + ":" + productCode + ":" + orgCode, value, validEndTime, TimeUnit.SECONDS);
    }

    public String getRedisProductActivity(String tenantCode, String productCode, String orgCode) {

        String redisKey = Constants.PROMOTION_SPU_ACTIVITY_CACHE + ":" + tenantCode + ":" + productCode + ":" + orgCode;
        return redisClient.getString(redisKey);
    }

    public Set<String> getAll(String keyPattern) {

        return redisClient.scan(keyPattern);
    }

    public void setRedisGroupRelation(String tenantCode, Map<String, List<String>> relationMap) {

        redisClient.setStringValue(Constants.PROMOTION_GROUP_RELATION_KEY + ":" + tenantCode, JSONObject.toJSONString(relationMap));

    }

    public String getRedisGroupRelation(String tenantCode) {

        return redisClient.getString(Constants.PROMOTION_GROUP_RELATION_KEY + ":" + tenantCode);

    }


}
