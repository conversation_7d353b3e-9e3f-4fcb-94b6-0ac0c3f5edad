package com.gtech.promotion.helper;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.gtech.promotion.dto.cache.BaseCacheDTO;
import com.gtech.promotion.dto.cache.CustomConditionAware;
import com.gtech.promotion.feign.MasterDataFeignClient;
import com.gtech.promotion.vo.param.activity.CustomCondition;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;

@Slf4j
@Component
public class CustomerConditionHelper {

    public static final String TENANT_CUSTOM_CONDITION = "TENANT_CUSTOM_CONDITION";
    public static final String CONDITION = "Condition";
    public static final String RULE = "Rule";
    @Autowired
    private MasterDataFeignClient masterDataFeignClient;






    /*void filterCacheByCustomConditionByPc(){
        Map<String, ActivityCacheDTO> activityMap = new HashMap<>();
        Map<String, PurchaseConstraintCacheDTO> pcMap = new HashMap<>();
        Map<String, ActivityCacheDTO> activityCacheDTOMap = filterCacheByCustomCondition("tenantCode", activityMap, new HashMap<>());
        Map<String, PurchaseConstraintCacheDTO> tenantCode = filterCacheByCustomCondition("tenantCode", pcMap, new HashMap<>());
    }*/


    /**
     * 根据自定义条件过滤缓存
     * 需要注意,入参的类型必须是BaseCacheDTO的子类,并且实现了CustomConditionAware接口
     * @param tenantCode
     * @param cacheMap
     * @param customMap
     * @param <T>
     * @return
     */

    public <T extends BaseCacheDTO & CustomConditionAware> Map<String, T> filterCacheByCustomCondition(String tenantCode, Map<String, T> cacheMap, Map<String, String> customMap,String type) {
        if (MapUtils.isEmpty(cacheMap)) {
            return cacheMap;
        }
        // Default: 1 for enabling mandatory validation
        //恒定开启,需要开关自行校验
        /*JsonResult<String> masterResult = masterDataFeignClient.getValueValue(tenantCode, TENANT_CUSTOM_CONDITION);

        if (null != masterResult && StringUtils.isNotBlank(masterResult.getData()) && !TurnOnAndOffEnum.TURN_ON.code().equals(masterResult.getData())) {
            return cacheMap;
        }*/
        Map<String, T> newCaches = new HashMap<>();

        for (Map.Entry<String, T> e : cacheMap.entrySet()) {
            T value = e.getValue();
            JSONArray jsonArray = null;
            String customCondition = "";
            if (type.equals(CONDITION)){
                customCondition= value.getCustomCondition();
            }else if(type.equals(RULE)){
                customCondition= value.getCustomRule();
            }

            if (StringUtils.isNotEmpty(customCondition)) {
                jsonArray = JSONArray.parseArray(customCondition);
            }
            if (CollectionUtils.isEmpty(jsonArray)) {
                newCaches.put(e.getKey(), value);
            }
            if (!CollectionUtils.isEmpty(jsonArray) && MapUtils.isNotEmpty(customMap)) {
                // Custom conditions must be fully met
                List<CustomCondition> customConditions = convertCustomCondition(jsonArray);
                Set<Map.Entry<String, String>> entries = customMap.entrySet();
                // Record matching counts
                checkCustomConditionByDto(customMap, newCaches, e, customConditions, entries);
            }
        }
        return newCaches;
    }

    public <T extends BaseCacheDTO> void checkCustomConditionByDto(Map<String, String> customMap, Map<String, T> newCaches, Map.Entry<String, T> e, List<CustomCondition> customConditions, Set<Map.Entry<String, String>> entries) {
        int count = 0;
        for (Map.Entry<String, String> entry : entries) {
            String key = entry.getKey();
            String value = entry.getValue();
            try {
                List<String> list = JSONArray.parseArray(value).toJavaList(String.class);
                for (CustomCondition condition : customConditions) {
                    String customKey = condition.getCustomKey();
                    List<String> customValueList = condition.getCustomValueList();
                    boolean anyMatch = customValueList.stream().anyMatch(x -> list.stream().anyMatch(y -> y.equals(x)));
                    if (key.equals(customKey) && anyMatch) {
                        count++;
                        break;
                    }
                }
            } catch (Exception exception) {
                log.error("custom condition format error");
                // Project data
            }
        }

        if (count == customMap.size() && customConditions.size() == customMap.size()) {
            newCaches.put(e.getKey(), e.getValue());
        }
    }



    //数据解析
    public List<CustomCondition> convertCustomCondition(JSONArray jsonArray) {

        List<CustomCondition> customConditions = new ArrayList<>();

        for (Object o : jsonArray) {
            CustomCondition customCondition = new CustomCondition();

            JSONObject jsonObject = JSON.parseObject(o.toString());
            String customKey = jsonObject.getString("customKey");
            String customValue = jsonObject.getString("customValue");

            List<String> customValueList = new ArrayList<>();

            try {
                JSONArray jsonArray1 = JSON.parseArray(customValue);
                for (Object o1 : jsonArray1) {
                    customValueList.add(o1.toString());
                }
            }catch (Exception e){
                //解析数组失败，则为单个值
                customValueList.add(customValue);
            }

            customCondition.setCustomKey(customKey);
            customCondition.setCustomValue(customValue);
            customCondition.setCustomValueList(customValueList);
            customConditions.add(customCondition);
        }
        return customConditions;
    }



}
