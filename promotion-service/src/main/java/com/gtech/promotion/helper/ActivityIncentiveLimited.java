/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.helper;

import java.io.Serializable;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.HashMap;
import java.util.Map;

import com.gtech.promotion.code.activity.LimitationCodeEnum;

/**
 * 
 *
 * <AUTHOR>
 * @Date 2020-02-13
 */
public class ActivityIncentiveLimited implements Serializable {

    private static final long serialVersionUID = -7999914241001945505L;

    private Map<String, BigDecimal> limitedMap = new HashMap<>();

    public void setLimited(String limitedCode, BigDecimal limitedValue) {

        this.limitedMap.put(limitedCode, limitedValue.setScale(0, RoundingMode.HALF_UP));
    }

    public boolean haveLimited(LimitationCodeEnum limitedCode) {

        return this.limitedMap.containsKey(limitedCode.code());
    }

    public BigDecimal getLimitedValue(LimitationCodeEnum limitedCode) {

        return this.limitedMap.get(limitedCode.code());
    }

}
