/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.helper;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import com.gtech.commons.exception.Exceptions;
import com.gtech.commons.utils.StringUtil;
import com.gtech.promotion.calc.function.FunctionEnum;
import com.gtech.promotion.dao.model.activity.TemplateFunctionModel;
import com.gtech.promotion.exception.ErrorCodes;

/**
 * 
 *
 * <AUTHOR>
 * @Date 2020-05-18
 */
public class FunctionHelper {
    
    private FunctionHelper() {
        // No codes
    }

    public static void validateFunctionCode(String templateCode, String functionCode) {

        if (StringUtil.isBlank(templateCode) || templateCode.length() != 16) {
            throw Exceptions.fail(ErrorCodes.PARAM_ERROR_TEMPLATE_CODE, templateCode);
        }

        if (!Arrays.asList(templateCode.substring(0, 4), templateCode.substring(4, 8), templateCode.substring(8, 12), templateCode.substring(12, 16)).contains(functionCode)) {
            throw Exceptions.fail(ErrorCodes.PARAM_ERROR_FUNCTION_CODE, functionCode, templateCode);
        }
    }

    /**
     * Query template function list by templateCode
     */
    public static List<TemplateFunctionModel> queryTemplateFunctionList(String templateCode) {

        if (StringUtil.isBlank(templateCode) || templateCode.length() != 16) {
            return Collections.emptyList();
        }

        return Arrays.asList(
            buildTemplateFunctionModel(templateCode.substring(0, 4)),
            buildTemplateFunctionModel(templateCode.substring(4, 8)),
            buildTemplateFunctionModel(templateCode.substring(8, 12)),
            buildTemplateFunctionModel(templateCode.substring(12, 16)));
    }

    private static TemplateFunctionModel buildTemplateFunctionModel(String functionCode) {

        FunctionEnum functionEnum = FunctionEnum.code2Enum(functionCode);
        if (null == functionEnum) {
            return null;
        }

        return TemplateFunctionModel.builder()
            .functionCode(functionEnum.code())
            .functionType(functionEnum.type().code())
            .functionDesc(functionEnum.desc())
            .functionName(functionEnum.name()).build();
    }

    /**
     * Retrieve template function list by functionCode
     */
    public static TemplateFunctionModel getTemplateFunctionByCode(String functionCode) {

        return buildTemplateFunctionModel(functionCode);
    }

}
