<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.gtech.promotion</groupId>
        <artifactId>promotion-pom</artifactId>
        <version>${promotion.version}</version>
    </parent>

    <artifactId>promotion-service</artifactId>

    <dependencies>
        <dependency>
            <groupId>com.gtech.ecomm</groupId>
            <artifactId>order-feign</artifactId>
            <version>1.0.3-RELEASE</version>
        </dependency>
        <dependency>
            <groupId>io.sentry</groupId>
            <artifactId>sentry-spring-boot-starter</artifactId>
            <version>6.7.0</version>
        </dependency>
        <dependency>
            <groupId>com.gtech.commons</groupId>
            <artifactId>gtech-commons</artifactId>
        </dependency>
        <dependency>
            <groupId>com.gtech.promotion</groupId>
            <artifactId>promotion-common</artifactId>
        </dependency>
        <dependency>
            <groupId>com.gtech.basic</groupId>
            <artifactId>idm-common</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.gtech.commons</groupId>
                    <artifactId>gtech-commons-basic</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.commons</groupId>
                    <artifactId>commons-lang3</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.commons</groupId>
                    <artifactId>commons-collections4</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.alibaba</groupId>
                    <artifactId>fastjson</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.projectlombok</groupId>
                    <artifactId>lombok</artifactId>
                </exclusion>
            </exclusions>
        </dependency>


        <dependency>
            <groupId>com.gtech.member</groupId>
            <artifactId>member-common</artifactId>
        </dependency>
        <dependency>
            <groupId>com.gtech.member</groupId>
            <artifactId>member-client</artifactId>
        </dependency>

        <dependency>
            <groupId>org.codehaus.groovy</groupId>
            <artifactId>groovy-all</artifactId>
            <type>pom</type>
            <exclusions>
                <exclusion>
                    <groupId>org.codehaus.groovy</groupId>
                    <artifactId>groovy-testng</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <!-- mybatis 相关 -->
        <dependency>
            <groupId>org.mybatis.spring.boot</groupId>
            <artifactId>mybatis-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>tk.mybatis</groupId>
            <artifactId>mapper-spring-boot-starter</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>mybatis</artifactId>
                    <groupId>org.mybatis</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>mybatis-spring</artifactId>
                    <groupId>org.mybatis</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.github.pagehelper</groupId>
            <artifactId>pagehelper-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-mongodb</artifactId>
        </dependency>
        <!-- mybatis 相关 -->

        <!-- mysql -->
        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
        </dependency>
        <!-- mysql -->

        <!-- druid连接池 -->
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>druid</artifactId>
        </dependency>
        <!-- druid连接池 -->

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-aop</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.shardingsphere</groupId>
            <artifactId>sharding-jdbc-spring-boot-starter</artifactId>
        </dependency>

        <dependency>
            <groupId>com.gtech.basic</groupId>
            <artifactId>filecloud-api-feign</artifactId>
        </dependency>

        <dependency>
            <groupId>com.gtech</groupId>
            <artifactId>pim-client</artifactId>
        </dependency>

        <dependency>
            <groupId>com.alibaba.rocketmq</groupId>
            <artifactId>rocketmq-client</artifactId>
        </dependency>
        <!-- https://mvnrepository.com/artifact/org.apache.poi/poi-ooxml -->
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi-ooxml</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-openfeign</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-netflix-eureka-client</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>commons-collections</groupId>
                    <artifactId>commons-collections</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.gtech.basic</groupId>
            <artifactId>masterdata-client</artifactId>
        </dependency>
    </dependencies>


</project>
