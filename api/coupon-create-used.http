#### 券活动创建
POST {{ url }}/promotion/coupon/createCouponActivity
Content-Type: application/json
{
    "activityType":"02",
    "couponType": "01",
    "totalQuantity": 0,
    "domainCode": "DC0001",
    "tenantCode": "100001",
    "activityBegin": "20230707000000",
    "activityEnd": "20230710235959",
    "sponsors": "Gulf",
    "promotionCategory": "",
    "promotionCategoryName": "",
    "activityName": "赠品2222",
    "activityUrl": "",
    "backgroundImage": "",
    "activityLanguages": [
        {
            "activityName": "赠品111",
            "activityLabel": "",
            "activityDesc": "",
            "language": "zh-CN"
        },
        {
            "activityName": "赠品111",
            "activityLabel": "",
            "activityDesc": "",
            "language": "en-US"
        },
        {
            "activityName": "赠品111",
            "activityLabel": "",
            "activityDesc": "",
            "language": "id-ID"
        }
    ],
    "storeType": "00",
    "qualifications": [],
    "productSelectionType": "01",
    "conditionProductType": "02",
    "conditionProductDetails": [
        {
            "skuCode": "SK23070400009045",
            "skuName": "s09091688464844",
            "productCode": "SP230704134530",
            "spuName": "p09091688464844",
            "orgCode": "default",
            "orgName": "Default",
            "skuUrl": ""
        }
    ],
    "customConditions": [],
    "templateCode": "0102020203020406",
    "funcParams": [
        {
            "functionType": "01",
            "functionCode": "0102",
            "paramType": "01",
            "paramValue": "",
            "paramUnit": "",
            "rankParam": 1
        },
        {
            "functionType": "02",
            "functionCode": "0202",
            "paramType": "01",
            "paramValue": "",
            "paramUnit": "",
            "rankParam": 1
        },
        {
            "functionType": "03",
            "functionCode": "0302",
            "paramType": "02",
            "paramValue": 1,
            "paramUnit": "02",
            "rankParam": 1
        },
        {
            "functionType": "04",
            "functionCode": "0406",
            "paramType": "02",
            "paramValue": 1,
            "paramUnit": "02",
            "rankParam": 1
        }
    ],
    "giveaways": [
        {
            "giveawayCode": "SK23070400009046",
            "giveawayName": "skuNam11688464867",
            "giveawayNum": "1",
            "giveawayType": 1,
            "opsType": "",
            "rankParam": 1
        }
    ],
    "itemScopeType": "1",
    "incentiveLimitedFlag": "00",
    "orgCode": "default",
    "opsType": "106",
    "ribbonImage": "",
    "ribbonPosition": "",
    "ribbonText": "",
    "conditionProductScopes": [],
    "incentiveProductScopes": []
}

#####状态变更
POST {{ url }}/promotion/activity/status/update
Content-Type: application/json
{
    "tenantCode": "100001",
    "domainCode": "DC0001",
    "activityCode": "20602054989087554814",
    "activityStatus":"04",
    "orgCode":"default"
}

####投放
POST {{ url }}/promotion/coupon/createCouponRelease
Content-Type: application/json

{    "tenantCode": "100001",
    "domainCode": "DC0001",
            "activityCode": "20602054989087554814",
            "releaseType": "01",
            "timeSameActivity": "0",
            "receiveStart": "20230707000000",
            "receiveEnd": "20230710235959",
            "receiveTimeSameActivity": "0",
            "validStart": "20230707000000",
            "validEnd": "20230710235959",
            "releaseQuantity": 10,
            "couponCodePrefix": ""
}

#### 领取
POST {{ url }}/promotion/coupon/sendCoupon
Content-Type: application/json

{
  "domainCode": "DC0001",
  "tenantCode": "100001",
  "activityCode": "20602054989087554814",
  "takeLabel": "02",
  "receiveCount":"1",
  "operatorCode":"11222",
  "releaseCode": "1688699577619",
  "userCodes":"10230703002980"

}

#####购物车

POST {{ url }}/promotion/activity/calcShoppingCart
Content-Type: application/json

{
  "channelCode": "100001",
  "domainCode": "DC0001",
  "memberCode": "10230703002980",
  "tenantCode": "100001",
  "couponCodes":"400479119000060902",
  "cartStoreList": [
    {
      "cartItemList": [
        {
          "productCode": "SP230704134530",
          "skuCode": "SK23070400009045",
          "productPrice": "500",
          "quantity": "01",
          "selectionFlag": "01"
        }
      ]
    }
  ]
}



#### 创单
POST {{ url }}/promotion/order/createOrder
Content-Type: application/json

{
  "cartStoreList": [
    {
      "cartItemList": [
        {
          "productCode": "SP230704134530",
          "skuCode": "SK23070400009045",
          "productPrice": "500",
          "quantity": "01",
          "selectionFlag": "01"
        }
      ]
    }
  ],
  "couponCodes": "400479119000060902",
  "domainCode": "DC0001",
  "freePostage": 0,
  "language": "id-ID",
  "memberCode": "10230703002980",
  "orderNo": "10230703002980",
  "postage": 0.0,
  "promoDeductedAmount": 0,
  "promoRewardPostage": 0.0,
  "tenantCode": "100001",
  "promoGiveaways": [
    {
      "activityCode": "20602054989087554814",
      "giveaways": [
        {
          "giveawayCode": "SK23070400009046",
          "giveawayName": "skuNam11688464867",
          "giveawayNum": 1,
          "giveawayType": 1,
          "opsType": "",
          "rankParam": 1
        }
      ]
    }
  ]
}
