### 创建秒杀活动
POST {{ url }}/promotion/marketing/flashSale/create
Content-Type: application/json

{
  "domainCode":"1",
  "tenantCode": "2",
  "orgCode": "1",
  "activityType": "04",
  "opsType":"401",
  "activityName": "test1111",
  "activityBegin":"20201029121212",
  "activityEnd":"20211007121212",
  "sponsors":"tttt",
  "operateUser":"1212",
  "backgroundImage": "",
  "ribbonImage":"",
  "ribbonPosition":"",
  "ribbonText":"",
  "marketingLanguages":[
    {
      "activityName": "test11",
      "activityLabel": "tttttlll",
      "language":"zh_CN"
    },
    {
      "activityName": "test1232",
      "activityLabel": "tttttll2323232l",
      "language":"en_US"
    }
  ],
  "products":[
    {
      "skuCode":"1313123123",
      "skuName":"skueName",
      "skuQuota":20,
      "maxPerUser":2,
      "listPrice": 100,
      "salePrice": 80,
      "flashPrice": 50
    },
    {
      "skuCode":"131312312311222",
      "skuName":"skueName1",
      "skuQuota":20,
      "maxPerUser":3,
      "listPrice": 1100,
      "salePrice": 180,
      "flashPrice": 150
    },
    {
      "skuCode":"13131231231345",
      "skuName":"skueName2",
      "skuQuota":20,
      "maxPerUser":1,
      "listPrice": 2100,
      "salePrice": 280,
      "flashPrice": 250
    }
  ],
  "flashSaleStores":[{
    "orgCode":"1212",
    "storeName":"dsfsdfsfs"
  }],
  "flashSaleQualifications":[{
    "qualificationCode":"isMember",
    "qualificationValue": ["1"]
  }]
}

### 更新秒杀活动
POST {{ url }}/promotion/marketing/flashSale/update
Content-Type: application/json

{
  "activityCode": "1020101400022461",
  "domainCode":"1",
  "tenantCode": "1",
  "orgCode": "1",
  "activityType": "04",
  "opsType":"401",
  "activityName": "test1111",
  "activityBegin":"20201029121212",
  "activityEnd":"20210907121212",
  "sponsors":"tttt",
  "operateUser":"1212",
  "ribbonImage":"",
  "ribbonPosition":"",
  "ribbonText":"",
  "marketingLanguages":[
    {
      "activityName": "test11",
      "activityLabel": "tttttlll",
      "language":"zh_CN"
    },
    {
      "activityName": "test1232",
      "activityLabel": "tttttll2323232l",
      "language":"en_US"
    }
  ],
  "products":[
    {
      "skuCode":"1313123123",
      "skuName":"skueName",
      "skuQuota":20,
      "maxPerUser":2,
      "listPrice": 100,
      "salePrice": 80,
      "flashPrice": 50
    },
    {
      "skuCode":"131312312311222",
      "skuName":"skueName1",
      "skuQuota":20,
      "maxPerUser":3,
      "listPrice": 1100,
      "salePrice": 180,
      "flashPrice": 150
    },
    {
      "skuCode":"13131231231345",
      "skuName":"skueName2",
      "skuQuota":20,
      "maxPerUser":1,
      "listPrice": 2100,
      "salePrice": 280,
      "flashPrice": 250
    }
  ],
  "flashSaleStores":[{
    "orgCode":"1212",
    "storeName":"dsfsdfsfs"
  }],
  "flashSaleQualifications":[{
    "qualificationCode":"isMember",
    "qualificationValue": ["1"]
  }]
}

### 更新秒杀活动状态
POST {{ url }}/promotion/marketing/activity/status/update
Content-Type: application/json

{
  "activityCode": "1020101400022461",
  "domainCode":"1",
  "tenantCode": "1",
  "orgCode": "1",
  "activityStatus": "04",
  "operateUser": "1212"
}
### 提交秒杀订单
POST {{ url }}/promotion/marketing/flashSale/createOrder
Content-Type: application/json

{
  "activityCode": "1020101400022461",
  "cartStoreList": [
    {
      "cartItemList": [
        {
          "productAmount": 13320,
          "productCode": "SP200917001117",
          "productPrice": 666,
          "quantity": 1,
          "selectionFlag": "01",
          "skuCode": "1313123123"
        },
        {
          "productAmount": 888,
          "productCode": "SP200917001118",
          "productPrice": 888,
          "quantity": 1,
          "selectionFlag": "01",
          "skuCode": "131312312311222"
        }
      ],
      "orgCode": "1212"
    }
  ],
  "qualifications": {
    "isMember": ["1"]
  },
  "promoDeductedAmount": 1354,
  "orderNo": "123",
  "domainCode": "1",
  "memberCode": "10200703091979",
  "tenantCode": "1"
}

### 取消秒杀订单
POST {{ url }}/promotion/marketing/flashSale/cancelOrder
Content-Type: application/json

{
  "domainCode":"1",
  "tenantCode":"1",
  "orderNo":"123"
}