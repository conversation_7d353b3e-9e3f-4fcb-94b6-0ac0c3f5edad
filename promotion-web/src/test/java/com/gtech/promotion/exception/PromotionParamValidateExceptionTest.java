package com.gtech.promotion.exception;

import com.gtech.commons.exception.ErrorCode;
import org.junit.Test;

public class PromotionParamValidateExceptionTest {
    
    @Test
    public void test(){
        PromotionParamValidateException exception = new PromotionParamValidateException();
        PromotionParamValidateException exception1 = new PromotionParamValidateException(new Exception());
        PromotionParamValidateException exception2 = new PromotionParamValidateException("error",new Exception(),"123");
        PromotionParamValidateException exception3 = new PromotionParamValidateException("error","error",new Exception(),"123");
        PromotionParamValidateException exception4 = new PromotionParamValidateException(ErrorCode.DEF_ERROR_CODE,new Exception(),"123");
        PromotionParamValidateException exception6 = new PromotionParamValidateException("error","123",new Exception());
    }
}
