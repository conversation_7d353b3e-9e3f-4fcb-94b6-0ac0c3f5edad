package com.gtech.promotion.exception;

import com.gtech.commons.exception.ErrorCode;
import org.junit.Test;

public class PromotionCalcExceptionTest {

    @Test
    public void test(){
        PromotionCalcException exception = new PromotionCalcException();
        PromotionCalcException exception1 = new PromotionCalcException(new Exception());
        PromotionCalcException exception2 = new PromotionCalcException("error",new Exception(),"123");
        PromotionCalcException exception3 = new PromotionCalcException("error","123",new Exception());
        PromotionCalcException exception4 = new PromotionCalcException("error","error",new Exception(),"123");
        PromotionCalcException exception5 = new PromotionCalcException(ErrorCode.DEF_ERROR_CODE,new Exception(),"123");
        PromotionCalcException exception6 = new PromotionCalcException("error","123",new Exception());
    }
}
