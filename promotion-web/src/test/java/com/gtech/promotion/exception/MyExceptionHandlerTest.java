/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.exception;

import javax.servlet.http.HttpServletRequest;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.springframework.beans.TypeMismatchException;
import org.springframework.core.MethodParameter;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.mock.web.MockHttpServletRequest;
import org.springframework.validation.BeanPropertyBindingResult;
import org.springframework.validation.BindException;
import org.springframework.validation.BindingResult;
import org.springframework.web.HttpRequestMethodNotSupportedException;

import com.gtech.commons.result.Result;
import com.gtech.promotion.checker.SystemChecker;
import com.gtech.promotion.exceptionhandler.MyExceptionHandler;
import org.springframework.web.bind.MethodArgumentNotValidException;

import java.lang.reflect.Method;

public class MyExceptionHandlerTest {

    private MyExceptionHandler handler;
    private HttpServletRequest request;
    private HttpServletRequest requestv130;
    private String exceptionStr = "this is a error string for test";

    @Before
    public void before(){
        handler = new MyExceptionHandler();
        request = new MockHttpServletRequest();
        requestv130 = new MockHttpServletRequest();
        ((MockHttpServletRequest) request).setRequestURI("/v1/junit/test");
        ((MockHttpServletRequest) request).setQueryString("queryString");
        ((MockHttpServletRequest) requestv130).setRequestURI("/order/update");
        ((MockHttpServletRequest) requestv130).setQueryString("v130QueryString");
    }

    @Test
    public void testProcessNapoleanException(){
        Result<Object> result = handler.processNapoleanException(request, new PromotionException(SystemChecker.NULL_VO));

        Assert.assertFalse(result.isSuccess());
        Assert.assertEquals(SystemChecker.NULL_VO.getCode(), result.getCode());
    }

    @Test
    public void testProcessHttpMessageNotReadableException(){
        Result<Object> result = handler.processHttpMessageNotReadableException(request, new HttpMessageNotReadableException(exceptionStr));

        Assert.assertFalse(result.isSuccess());
        Assert.assertEquals(SystemChecker.NULL_VO_OR_ILLEGAL_FORMAT_PARAM.getCode(), result.getCode());
        try {
            handler.processHttpMessageNotReadableException(request, new HttpMessageNotReadableException("field"));
        }catch (Exception e){

        }

    }

    @Test
    public void testProcessNullPointerException(){
        Result<Object> result = handler.processNullPointerException(request, new NullPointerException(exceptionStr));

        Assert.assertFalse(result.isSuccess());
        Assert.assertEquals(SystemChecker.NULL_POINTER.getCode(), result.getCode());
    }

    @Test
    public void testProcessMethodNotSupportedException(){
        Result<Object> result = handler.processMethodNotSupportedException(request, new HttpRequestMethodNotSupportedException(exceptionStr));

        Assert.assertFalse(result.isSuccess());
        Assert.assertEquals(SystemChecker.HTTP_METHOD_ILLEGAL.getCode(), result.getCode());
    }

    @Test
    public void testProcessDataIntegrityViolationExceptionException_1(){
        Result<Object> result = handler.processDataIntegrityViolationExceptionException(request, new DataIntegrityViolationException("Data too long for column"));

        Assert.assertFalse(result.isSuccess());
        Assert.assertEquals(SystemChecker.DATA_TOOLONG.getCode(), result.getCode());
    }
    @Test
    public void testProcessDataIntegrityViolationExceptionException_2(){
        Result<Object> result = handler.processDataIntegrityViolationExceptionException(request, new DataIntegrityViolationException("Duplicate entry"));

        Assert.assertFalse(result.isSuccess());
        Assert.assertEquals(SystemChecker.DUPLICATE_KEY.getCode(), result.getCode());
    }

    @Test
    public void testProcessDataIntegrityViolationExceptionException_3(){
        Result<Object> result = handler.processDataIntegrityViolationExceptionException(request, new DataIntegrityViolationException("' cannot be null"));

        Assert.assertFalse(result.isSuccess());
        Assert.assertEquals(SystemChecker.NOT_NULL_PROMO.getCode(), result.getCode());
    }

    @Test
    public void testProcessDataIntegrityViolationExceptionException_4(){
        Result<Object> result = handler.processDataIntegrityViolationExceptionException(request, new DataIntegrityViolationException(exceptionStr));

        Assert.assertFalse(result.isSuccess());
        Assert.assertEquals(SystemChecker.DATA_INTEGRITY_VIOLATION.getCode(), result.getCode());
    }

    @Test
    public void testProcessTypeMismatchException(){
        Result<Object> result = handler.processTypeMismatchException(request, new TypeMismatchException(new Object(), String.class));

        Assert.assertFalse(result.isSuccess());
        Assert.assertEquals(SystemChecker.ILLEGAL_FORMAT_PARAM.getCode(), result.getCode());
    }

    @Test
    public void testProcessBindException(){
        Result<Object> result = handler.processBindException(request, new BindException(new Object(), exceptionStr));

        Assert.assertFalse(result.isSuccess());
        Assert.assertEquals(SystemChecker.ILLEGAL_FORMAT_PARAM.getCode(), result.getCode());
    }

    @Test
    public void testProcessIllegalArgumentException(){
        Result<Object> result = handler.processIllegalArgumentException(request, new IllegalArgumentException(exceptionStr));

        Assert.assertFalse(result.isSuccess());
        Assert.assertEquals(SystemChecker.ILLEGAL_FORMAT_PARAM.getCode(), result.getCode());
    }

    @Test
    public void testProcessOtherException(){
        Result<Object> result = handler.processOtherException(request, new RuntimeException(exceptionStr));

        Assert.assertFalse(result.isSuccess());
        Assert.assertEquals(SystemChecker.UNKNOWN_ERROR.getCode(), result.getCode());
    }

    @Test
    public void testArgumentNotValidHandler(){
        try {
            Method method = MyExceptionHandler.class.getMethod("processOtherException");
            MethodParameter parameter = new MethodParameter(method,1);
            BindingResult bindingResult = new BeanPropertyBindingResult(new MyExceptionHandler(),"MyExceptionHandler");
            MethodArgumentNotValidException ex = new MethodArgumentNotValidException(parameter,bindingResult);
            handler.argumentNotValidHandler(ex);
        }catch (Exception e){
            System.out.println(e);
        }
    }
}
