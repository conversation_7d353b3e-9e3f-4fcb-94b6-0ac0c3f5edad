package com.gtech.promotion.exception;
import com.gtech.commons.exception.ErrorCode;
import org.junit.Test;

public class PromotionCouponExceptionTest {

    @Test
    public void test(){
        PromotionCouponException exception = new PromotionCouponException();
        PromotionCouponException exception1 = new PromotionCouponException(new Exception());
        PromotionCouponException exception2 = new PromotionCouponException("error",new Exception(),"123");
        PromotionCouponException exception3 = new PromotionCouponException("error","error",new Exception(),"123");
        PromotionCouponException exception4 = new PromotionCouponException(ErrorCode.DEF_ERROR_CODE,new Exception(),"123");
        PromotionCouponException exception6 = new PromotionCouponException("error","123",new Exception());
    }
}
