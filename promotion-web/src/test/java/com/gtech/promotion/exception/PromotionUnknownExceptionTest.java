package com.gtech.promotion.exception;

import com.gtech.commons.exception.ErrorCode;
import org.junit.Test;

public class PromotionUnknownExceptionTest {
    
    @Test
    public void test(){
        PromotionUnknownException exception = new PromotionUnknownException();
        PromotionUnknownException exception1 = new PromotionUnknownException(new Exception());
        PromotionUnknownException exception2 = new PromotionUnknownException("error",new Exception(),"123");
        PromotionUnknownException exception3 = new PromotionUnknownException("error","error",new Exception(),"123");
        PromotionUnknownException exception4 = new PromotionUnknownException(ErrorCode.DEF_ERROR_CODE,new Exception(),"123");
        PromotionUnknownException exception6 = new PromotionUnknownException("error","123",new Exception());
    }
}
