package com.gtech.promotion.exception;

import com.gtech.promotion.checker.Checker;
import com.gtech.promotion.checker.SystemChecker;
import org.junit.Assert;
import org.junit.Test;

/**
 * <AUTHOR>
 * @since 2021/8/16 13:59
 */
public class PromotionUnsupportedExceptionTest {

    private final PromotionUnsupportedException exception = new PromotionUnsupportedException("123");

    @Test
    public void getCode(){
        PromotionUnsupportedException exception1 = new PromotionUnsupportedException(SystemChecker.CAN_NOT_INSTANCE_ERROR);
        String code = exception.getCode();
        Assert.assertNull(code);
    }
}
