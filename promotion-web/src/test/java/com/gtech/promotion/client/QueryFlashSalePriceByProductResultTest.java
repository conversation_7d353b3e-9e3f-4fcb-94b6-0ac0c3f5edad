package com.gtech.promotion.client;

import com.gtech.promotion.vo.result.flashsale.QueryFlashSalePriceByProductResult;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;

import java.math.BigDecimal;

@RunWith(MockitoJUnitRunner.class)
public class QueryFlashSalePriceByProductResultTest {

    @Test
    public void createQueryFlashSalePriceByProductResult(){
        QueryFlashSalePriceByProductResult t = new QueryFlashSalePriceByProductResult();
        t.setPrice(new BigDecimal("12"));
        t.setProductCode("test");
    }
}
