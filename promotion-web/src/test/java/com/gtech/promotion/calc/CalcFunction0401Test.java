package com.gtech.promotion.calc;

import com.gtech.promotion.calc.function.CalcFunction0401;
import com.gtech.promotion.calc.model.CalcActivityIncentive;
import com.gtech.promotion.calc.model.CalcShoppingCart;
import com.gtech.promotion.calc.model.CalcShoppingCartItem;
import com.gtech.promotion.helper.RedisOpsHelper;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.junit.MockitoJUnitRunner;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

@RunWith(MockitoJUnitRunner.class)
public class CalcFunction0401Test {
    @InjectMocks
    CalcFunction0401 calcFunction0401;

    @Test
    public void calc(){
//        List<CalcShoppingCartItem> calcShoppingCartItemList = new ArrayList<>();
//        CalcShoppingCartItem shoppingCartItem = new CalcShoppingCartItem();
//        shoppingCartItem.setPromoAmount(new BigDecimal(918));
//        calcShoppingCartItemList.add(shoppingCartItem);
//        CalcShoppingCart promoObject = new CalcShoppingCart();
//        promoObject.setCalcShoppingCartItemList(calcShoppingCartItemList);
//
//        RedisOpsHelper redisOpsHelper = new RedisOpsHelper();
//        CalcActivity calcActivity = new CalcActivity(redisOpsHelper,"test");
//        CalcTemplate calcTemplate = new CalcTemplate();
//        calcActivity.setCalcTemplate();
//        CalcActivityIncentive incentive = new CalcActivityIncentive(calcActivity,"test","361178");
//
//        calcFunction0401.handler0101T(promoObject,incentive,null);
    }
}
