package com.gtech.promotion.calc;

import com.gtech.promotion.calc.function.CalcFunction0402;
import com.gtech.promotion.calc.model.CalcShoppingCartItem;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.junit.MockitoJUnitRunner;

import java.math.BigDecimal;

@RunWith(MockitoJUnitRunner.class)
public class CalcFunction0402Test {
    @InjectMocks
    CalcFunction0402 calcFunction0402;
    @Test
    public void get0206PromAmount(){
        CalcShoppingCartItem scItem = new CalcShoppingCartItem();
        scItem.setQuantity(1);
        scItem.setProductPrice(new BigDecimal("100"));
        calcFunction0402.get0206PromAmount(new BigDecimal(10),BigDecimal.ZERO,scItem,2);

        scItem.setQuantity(2);
        calcFunction0402.get0206PromAmount(new BigDecimal(10),BigDecimal.ZERO,scItem,2);

        scItem.setQuantity(1);
        calcFunction0402.get0206PromAmount(new BigDecimal(10),new BigDecimal("0.9"),scItem,2);

        scItem.setQuantity(2);
        calcFunction0402.get0206PromAmount(new BigDecimal(10),new BigDecimal("0.9"),scItem,2);

    }
}
