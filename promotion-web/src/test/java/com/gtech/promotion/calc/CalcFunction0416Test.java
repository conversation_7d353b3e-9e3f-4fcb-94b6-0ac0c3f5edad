package com.gtech.promotion.calc;

import com.gtech.promotion.calc.function.CalcFunction0416;
import com.gtech.promotion.calc.model.CalcShoppingCartItem;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.junit.MockitoJUnitRunner;

import java.math.BigDecimal;

@RunWith(MockitoJUnitRunner.class)
public class CalcFunction0416Test {
    @InjectMocks
    CalcFunction0416 calcFunction0416;
    
    @Test
    public void get0206PromAmount(){
        CalcShoppingCartItem scItem = new CalcShoppingCartItem();
        scItem.setQuantity(1);
        scItem.setProductPrice(new BigDecimal("100"));
        calcFunction0416.get0206PromAmount(new BigDecimal(10), BigDecimal.ZERO, scItem, 2);

        scItem.setQuantity(2);
        calcFunction0416.get0206PromAmount(new BigDecimal(10), BigDecimal.ZERO, scItem, 2);

        scItem.setQuantity(1);
        calcFunction0416.get0206PromAmount(new BigDecimal(10), new BigDecimal("0.9"), scItem, 2);

        scItem.setQuantity(2);
        calcFunction0416.get0206PromAmount(new BigDecimal(10), new BigDecimal("0.9"), scItem, 2);
    }
    
    @Test
    public void sumIncentiveAmount(){

    }
}