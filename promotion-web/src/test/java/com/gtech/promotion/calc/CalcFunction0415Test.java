package com.gtech.promotion.calc;

import com.gtech.promotion.calc.function.CalcFunction0415;
import com.gtech.promotion.calc.function.FunctionEnum;
import com.gtech.promotion.calc.function.FunctionParamMap;
import com.gtech.promotion.calc.model.CalcActivityFuncParam;
import com.gtech.promotion.calc.model.CalcActivityIncentive;
import com.gtech.promotion.calc.model.CalcShoppingCart;
import com.gtech.promotion.component.activity.ActivityPriceComponentDomain;
import com.gtech.promotion.helper.RedisOpsHelper;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.math.BigDecimal;
import java.util.ArrayDeque;
import java.util.ArrayList;
import java.util.Deque;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class CalcFunction0415Test {

    @InjectMocks
    private CalcFunction0415 calcFunction0415;

    @Mock
    private RedisOpsHelper redisOpsHelper;

    @Mock
    private ActivityPriceComponentDomain activityPriceComponentDomain;

    @Test
    public void calc_exception(){
        CalcShoppingCart calcShoppingCart = new CalcShoppingCart();
        CalcActivityIncentive calcActivityIncentive = new CalcActivityIncentive(new CalcActivity(redisOpsHelper, ""), "", "");
        Deque<FunctionEnum> functionEnums = new ArrayDeque<>();
        FunctionParamMap functionParamMap = new FunctionParamMap(new ArrayList<>());
        CalcFunction calcFunction = calcFunction0415;
//        when(activityPriceComponentDomain.getTenantPrecision(any())).thenReturn(2);
        CalcResult calc = calcFunction0415.calc(calcShoppingCart, calcActivityIncentive, functionEnums, functionParamMap, calcFunction);
        Assert.assertEquals(CalcResult.ERROR_DEFAULT.getResultCode(), calc.getResultCode());
    }

    @Test
    public void calc(){
        CalcShoppingCart calcShoppingCart = new CalcShoppingCart();
        calcShoppingCart.setPostage(BigDecimal.ZERO);
        calcShoppingCart.setCalcShoppingCartItemList(new ArrayList<>());
        CalcActivityIncentive calcActivityIncentive = new CalcActivityIncentive(new CalcActivity(redisOpsHelper, ""), "", "");
        Deque<FunctionEnum> functionEnums = new ArrayDeque<>();
        ArrayList<CalcActivityFuncParam> funcParamList = new ArrayList<>();
        CalcActivityFuncParam e = new CalcActivityFuncParam();
        e.setFuncCode(calcFunction0415.getFunction().code());
        e.setParamValue("1");
        funcParamList.add(e);
        FunctionParamMap functionParamMap = new FunctionParamMap(funcParamList);
        CalcFunction calcFunction = calcFunction0415;
//        when(activityPriceComponentDomain.getTenantPrecision(any())).thenReturn(2);
        CalcResult calc = calcFunction0415.calc(calcShoppingCart, calcActivityIncentive, functionEnums, functionParamMap, calcFunction);
    }
}
