package com.gtech.promotion.calc;

import com.gtech.promotion.calc.function.CalcFunction0414;
import com.gtech.promotion.calc.function.FunctionEnum;
import com.gtech.promotion.calc.function.FunctionParamMap;
import com.gtech.promotion.calc.model.CalcActivityFuncParam;
import com.gtech.promotion.calc.model.CalcActivityIncentive;
import com.gtech.promotion.calc.model.CalcShoppingCart;
import com.gtech.promotion.calc.model.CalcShoppingCartItem;
import com.gtech.promotion.component.activity.ActivityPriceComponentDomain;
import com.gtech.promotion.helper.RedisOpsHelper;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.math.BigDecimal;
import java.util.ArrayDeque;
import java.util.ArrayList;
import java.util.Deque;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class CalcFunction0414Test {

    @InjectMocks
    private CalcFunction0414 calcFunction0414;

    @Mock
    private RedisOpsHelper redisOpsHelper;

    @Mock
    private ActivityPriceComponentDomain activityPriceComponentDomain;


    @Test
    public void calc_exception(){
        CalcShoppingCart calcShoppingCart = new CalcShoppingCart();
        CalcActivityIncentive calcActivityIncentive = new CalcActivityIncentive(new CalcActivity(redisOpsHelper, ""), "", "");
        Deque<FunctionEnum> functionEnums = new ArrayDeque<>();
        FunctionParamMap functionParamMap = new FunctionParamMap(new ArrayList<>());
        CalcFunction calcFunction = calcFunction0414;
//        when(activityPriceComponentDomain.getTenantPrecision(any())).thenReturn(2);
        CalcResult calc = calcFunction0414.calc(calcShoppingCart, calcActivityIncentive, functionEnums, functionParamMap, calcFunction);
        Assert.assertEquals(CalcResult.ERROR_DEFAULT.getResultCode(), calc.getResultCode());
    }
    @Test
    public void calc(){
        CalcShoppingCart calcShoppingCart = new CalcShoppingCart();
        calcShoppingCart.setPostage(BigDecimal.ZERO);
        ArrayList<CalcShoppingCartItem> calcShoppingCartItemList = new ArrayList<>();
        CalcShoppingCartItem e1 = new CalcShoppingCartItem();
        e1.setPromoAmount(BigDecimal.ONE);
        e1.setQuantity(1);
        e1.setActivityExpr("");
        calcShoppingCartItemList.add(e1);
        calcShoppingCart.setCalcShoppingCartItemList(calcShoppingCartItemList);
        CalcActivity calcActivity = new CalcActivity(redisOpsHelper, "");
        calcActivity.setIncentiveLimited(null);
        calcShoppingCart.setCalcActivity(calcActivity);
        CalcActivityIncentive calcActivityIncentive = new CalcActivityIncentive(calcActivity, "", "");
        Deque<FunctionEnum> functionEnums = new ArrayDeque<>();
        ArrayList<CalcActivityFuncParam> funcParamList = new ArrayList<>();
        CalcActivityFuncParam e = new CalcActivityFuncParam();
        e.setFuncCode(calcFunction0414.getFunction().code());
        e.setParamValue("1");
        funcParamList.add(e);
        FunctionParamMap functionParamMap = new FunctionParamMap(funcParamList);
        CalcFunction calcFunction = calcFunction0414;
//        when(activityPriceComponentDomain.getTenantPrecision(any())).thenReturn(2);
        CalcResult calc = calcFunction0414.calc(calcShoppingCart, calcActivityIncentive, functionEnums, functionParamMap, calcFunction);
//        Assert.assertEquals(CalcResult.SUCCESS_TRUE.getResultCode(), calc.getResultCode());
    }
}
