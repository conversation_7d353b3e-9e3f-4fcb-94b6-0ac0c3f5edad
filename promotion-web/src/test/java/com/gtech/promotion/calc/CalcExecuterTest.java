/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.calc;

import com.google.common.collect.Lists;
import com.gtech.commons.utils.BeanCopyUtils;
import com.gtech.promotion.calc.model.CalcShoppingCart;
import com.gtech.promotion.calc.model.CalcShoppingCartItem;
import com.gtech.promotion.code.activity.ActivityTypeEnum;
import com.gtech.promotion.code.activity.FlagTypeEnum;
import com.gtech.promotion.code.activity.OpsTypeEnum;
import com.gtech.promotion.code.activity.ProductSelectionEnum;
import com.gtech.promotion.component.activity.ActivityCacheDomain;
import com.gtech.promotion.component.activity.ActivityPriceComponentDomain;
import com.gtech.promotion.component.activity.PromoGroupRelationDomain;
import com.gtech.promotion.component.activity.ShoppingCartDomain;
import com.gtech.promotion.component.boostsharing.BoostSharingComponent;
import com.gtech.promotion.component.coupon.CouponCodeUserComponent;
import com.gtech.promotion.dao.model.activity.ActivityModel;
import com.gtech.promotion.dao.model.activity.ActivityScriptModel;
import com.gtech.promotion.dao.model.coupon.TPromoCouponInnerCodeVO;
import com.gtech.promotion.dto.cache.ActivityCacheDTO;
import com.gtech.promotion.dto.cache.ProductSkuDetailDTO;
import com.gtech.promotion.dto.in.activity.ShoppingCartActivity;
import com.gtech.promotion.dto.in.activity.ShoppingCartDTO;
import com.gtech.promotion.dto.in.activity.ShoppingCartItem;
import com.gtech.promotion.dto.out.activity.ShoppingCartItemActivityOutDTO;
import com.gtech.promotion.dto.out.activity.ShoppingCartItemOutDTO;
import com.gtech.promotion.dto.out.activity.ShoppingCartOutDTO;
import com.gtech.promotion.helper.ActivityRedisHelpler;
import com.gtech.promotion.helper.RedisOpsHelper;
import com.gtech.promotion.helper.TemplateHelper;
import com.gtech.promotion.helper.TestUtils;
import com.gtech.promotion.helper.bean.ActivityProductDetail;
import com.gtech.promotion.helper.bean.Template;
import com.gtech.promotion.helper.factory.CacheActivityFactory;
import com.gtech.promotion.helper.factory.ShoppingCartFactory;
import com.gtech.promotion.service.activity.ActivityProductDetailService;
import com.gtech.promotion.service.activity.TPromoActivityExpressionService;
import com.gtech.promotion.service.common.GroovyService;
import com.gtech.promotion.service.coupon.PromoCouponInnerCodeService;
import com.gtech.promotion.utils.ExpressionHelper;
import com.gtech.promotion.vo.bean.ProductAttribute;
import com.gtech.promotion.vo.bean.ProductCodes;
import com.gtech.promotion.vo.bean.ProductScope;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.test.util.ReflectionTestUtils;

import java.math.BigDecimal;
import java.util.*;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;

/**
 * <功能描述>
 *
 */
public class CalcExecuterTest {

    static {
        TestUtils.testEntity("com.gtech.promotion.calc.function");
    }

    @InjectMocks
    private CalcExecuter calcExecuter;

    @Mock
    private ActivityCacheDomain activityCacheDomain;

    @InjectMocks
    private ShoppingCartDomain shoppingCartDomain;

    @Mock
    private TemplateHelper templateHelper;

    @Mock
    private TPromoActivityExpressionService expressionService;

    @Mock
    private PromoCouponInnerCodeService couponInnerCodeService;

    @Mock
    private CouponCodeUserComponent couponCodeUserDomain;

    @Mock
    private ActivityRedisHelpler redisService;
    @Mock
    private ActivityProductDetailService productDetailService;
    @Mock
    private GroovyService groovyService;
    @Mock
    private ActivityPriceComponentDomain activityPriceComponentDomain;

    @Mock
    private PromoGroupRelationDomain promoGroupRelationDomain;
    @Mock
    private StringRedisTemplate redisTemplate;

/*	@Mock
	private PromoGroupRelationDomain promoGroupRelationDomain;*/

    @Mock
    private BoostSharingComponent boostSharingComponent;
    private List<ShoppingCartOutDTO> shoppingCartOutDTOs;
    @Before
    public void testBefore() {
        MockitoAnnotations.initMocks(this);
        when(redisService.getActivitySetting(anyString(), anyString())).thenReturn(new HashMap<>());
        when(templateHelper.templateCode2TagCode(anyString())).thenReturn("01");
        shoppingCartOutDTOs = new ArrayList<>();
        setupTestData();
    }

    @Test
    public void test_0101020103010401单品无条件减金额() {
        // given
        String categoryCode = "01";
        // 创建活动范围
        List<ProductScope> activityProductList = new ArrayList<>();
        activityProductList.add(ProductScope.builder().categoryCode(categoryCode).seqNum(1).build());
        // 创建活动模板
        Template template = Template.builder().templateEnum(TemplateEnum.T0101).reward("1").build();
        // 创建活动
        ActivityCacheDTO activity = CacheActivityFactory.createActivity(ActivityTypeEnum.ACTIVITY, template, ProductSelectionEnum.SELECT, activityProductList, null, null);


        // 创建购物车商品项列表
        List<ShoppingCartItem> shoppingCartItems = new ArrayList<>();
        shoppingCartItems.add(ShoppingCartFactory.createShoppingCartItemByCategory(categoryCode, new BigDecimal(10), 1));
        ShoppingCartItem shoppingCartItem = shoppingCartItems.get(0);
        ShoppingCartActivity shoppingCartActivity = BeanCopyUtils.jsonCopyBean(activity, ShoppingCartActivity.class);
        ActivityModel activityModel = shoppingCartActivity.getActivityModel();
        activityModel.setCouponCodes(Arrays.asList("123123123"));
        shoppingCartActivity.setActivityModel(activityModel);
        shoppingCartActivity.setCouponCodes(Arrays.asList("123123123"));
        shoppingCartItem.setUsedActivitys(Arrays.asList(shoppingCartActivity));
        // 创建购物车
        ShoppingCartDTO shoppingCart = ShoppingCartFactory.createShoppingCart(null, shoppingCartItems, null);


        ActivityScriptModel activityScriptModel = new ActivityScriptModel();


        Map<String, ActivityCacheDTO> map = new HashMap<>();
        map.put(activity.getActivityModel().getActivityCode(), activity);

        when(activityCacheDomain.filterActivityByProduct(anyMap(), any(), any())).thenReturn(map);

        when(expressionService.getActivityExpression(anyString())).thenReturn(ExpressionHelper.DEF_EXPROSSION);
        shoppingCart = shoppingCartDomain.queryActivity(shoppingCart, map,false);

        // when
        when(groovyService.findByCode(any(), any())).thenReturn(activityScriptModel);
        when(groovyService.run(any(), any())).thenReturn(true);
        when(activityPriceComponentDomain.getTenantPrecision(any())).thenReturn(2);
        List<ShoppingCartOutDTO> shoppingCartOutDTOS = calcExecuter.calc(new CalcShoppingCart(shoppingCart), map);

        // then
//        assertEquals(1, shoppingCartOutDTOS.get(0).getPromoRewardAmount().intValue());
    }

    @Test
    public void 单品无条件打折扣_指定分类() {
        // given
        String categoryCode = "01";
        // 创建购物车商品项列表
        List<ShoppingCartItem> shoppingCartItems = new ArrayList<>();
        shoppingCartItems.add(ShoppingCartFactory.createShoppingCartItemByCategory(categoryCode, new BigDecimal(10), 1));
        // 创建购物车
        ShoppingCartDTO shoppingCart = ShoppingCartFactory.createShoppingCart(null, shoppingCartItems, null);
        // 创建活动范围
        List<ProductScope> activityProductList = new ArrayList<>();
        activityProductList.add(ProductScope.builder().categoryCode(categoryCode).seqNum(1).build());
        // 创建活动模板
        Template template = Template.builder().templateEnum(TemplateEnum.T0102).reward("0.9").build();
        // 创建活动
        ActivityCacheDTO activity = CacheActivityFactory.createActivity(ActivityTypeEnum.ACTIVITY, template, ProductSelectionEnum.SELECT, activityProductList, null, null);

        Map<String, ActivityCacheDTO> map = new HashMap<>();
        map.put(activity.getActivityModel().getActivityCode(), activity);

        when(activityCacheDomain.filterActivityByProduct(anyMap(), any(), any())).thenReturn(map);

        when(expressionService.getActivityExpression(anyString())).thenReturn(ExpressionHelper.DEF_EXPROSSION);
        shoppingCartDomain.queryActivity(shoppingCart, map,false);

        // when
        when(groovyService.findByCode(any(), any())).thenReturn(null);
        when(groovyService.run(any(), any())).thenReturn(true);
        when(activityPriceComponentDomain.getTenantPrecision(any())).thenReturn(2);
        List<ShoppingCartOutDTO> shoppingCartOutDTOS = calcExecuter.calc(new CalcShoppingCart(shoppingCart), map);

        // then
//        assertEquals(1, shoppingCartOutDTOS.get(0).getPromoRewardAmount().intValue());
    }

    @Test
    public void 单品无条件设为每件特价_指定分类() {
        // given
        String categoryCode = "01";
        // 创建购物车商品项列表
        List<ShoppingCartItem> shoppingCartItems = new ArrayList<>();
        shoppingCartItems.add(ShoppingCartFactory.createShoppingCartItemByCategory(categoryCode, new BigDecimal(10), 1));
        // 创建购物车
        ShoppingCartDTO shoppingCart = ShoppingCartFactory.createShoppingCart(null, shoppingCartItems, null);
        // 创建活动范围
        List<ProductScope> activityProductList = new ArrayList<>();
        activityProductList.add(ProductScope.builder().categoryCode(categoryCode).seqNum(1).build());
        // 创建活动模板
        Template template = Template.builder().templateEnum(TemplateEnum.T0103).reward("5").build();
        // 创建活动
        ActivityCacheDTO activity = CacheActivityFactory.createActivity(ActivityTypeEnum.ACTIVITY, template, ProductSelectionEnum.SELECT, activityProductList, null, null);

        Map<String, ActivityCacheDTO> map = new HashMap<>();
        map.put(activity.getActivityModel().getActivityCode(), activity);

        when(activityCacheDomain.filterActivityByProduct(anyMap(), any(), any())).thenReturn(map);

        when(expressionService.getActivityExpression(anyString())).thenReturn(ExpressionHelper.DEF_EXPROSSION);
        shoppingCartDomain.queryActivity(shoppingCart, map,false);

        // when
        when(groovyService.findByCode(any(), any())).thenReturn(null);
        when(groovyService.run(any(), any())).thenReturn(true);
        when(activityPriceComponentDomain.getTenantPrecision(any())).thenReturn(2);
        List<ShoppingCartOutDTO> shoppingCartOutDTOS = calcExecuter.calc(new CalcShoppingCart(shoppingCart), map);

        // then
//        assertEquals(5, shoppingCartOutDTOS.get(0).getPromoRewardAmount().intValue());
    }

    @Test
    public void 商品范围满数量减金额_指定品牌() {
        // given
        String brandCode = "01";
        // 创建购物车商品项列表
        List<ShoppingCartItem> shoppingCartItems = new ArrayList<>();
        shoppingCartItems.add(ShoppingCartFactory.createShoppingCartItemByBrand(brandCode, new BigDecimal(10), 3));
        // 创建购物车
        ShoppingCartDTO shoppingCart = ShoppingCartFactory.createShoppingCart(null, shoppingCartItems, null);

        // 创建活动范围
        List<ProductScope> activityProductList = new ArrayList<>();
        activityProductList.add(ProductScope.builder().brandCode(brandCode).build());
        // 创建活动模板
        Template template = Template.builder().templateEnum(TemplateEnum.T0201).condition("2").reward("3").build();
        // 创建活动
        ActivityCacheDTO activity = CacheActivityFactory.createActivity(ActivityTypeEnum.ACTIVITY, template, ProductSelectionEnum.SELECT, activityProductList, null, null);

        Map<String, ActivityCacheDTO> map = new HashMap<>();
        map.put(activity.getActivityModel().getActivityCode(), activity);

        when(activityCacheDomain.filterActivityByProduct(anyMap(), any(), any())).thenReturn(map);

        when(expressionService.getActivityExpression(anyString())).thenReturn(ExpressionHelper.DEF_EXPROSSION);
        shoppingCartDomain.queryActivity(shoppingCart, map,false);

        // when
        when(groovyService.findByCode(any(), any())).thenReturn(null);
        when(groovyService.run(any(), any())).thenReturn(true);
        when(activityPriceComponentDomain.getTenantPrecision(any())).thenReturn(2);
        List<ShoppingCartOutDTO> shoppingCartOutDTOS = calcExecuter.calc(new CalcShoppingCart(shoppingCart), map);

        // then
//        assertEquals(3, shoppingCartOutDTOS.get(0).getPromoRewardAmount().intValue());
    }

    @Test
    public void 商品范围满金额减金额_指定品牌() {
        // given
        String brandCode = "01";
        // 创建购物车商品项列表
        List<ShoppingCartItem> shoppingCartItems = new ArrayList<>();
        shoppingCartItems.add(ShoppingCartFactory.createShoppingCartItemByBrand(brandCode, new BigDecimal(10), 3));
        // 创建购物车
        ShoppingCartDTO shoppingCart = ShoppingCartFactory.createShoppingCart(null, shoppingCartItems, null);

        // 创建活动范围
        List<ProductScope> activityProductList = new ArrayList<>();
        activityProductList.add(ProductScope.builder().brandCode(brandCode).build());
        // 创建活动模板
        Template template = Template.builder().templateEnum(TemplateEnum.T0202).condition("2").reward("3").build();
        // 创建活动
        ActivityCacheDTO activity = CacheActivityFactory.createActivity(ActivityTypeEnum.ACTIVITY, template, ProductSelectionEnum.SELECT, activityProductList, null, null);

        Map<String, ActivityCacheDTO> map = new HashMap<>();
        map.put(activity.getActivityModel().getActivityCode(), activity);

        when(activityCacheDomain.filterActivityByProduct(anyMap(), any(ProductCodes.class), any())).thenReturn(map);

        when(expressionService.getActivityExpression(anyString())).thenReturn(ExpressionHelper.DEF_EXPROSSION);
        shoppingCartDomain.queryActivity(shoppingCart, map,false);

        when(groovyService.findByCode(any(), any())).thenReturn(null);
        when(groovyService.run(any(), any())).thenReturn(true);
        when(activityPriceComponentDomain.getTenantPrecision(any())).thenReturn(2);
        // when
        List<ShoppingCartOutDTO> shoppingCartOutDTOS = calcExecuter.calc(new CalcShoppingCart(shoppingCart), map);

        // then
//        assertEquals(30, shoppingCartOutDTOS.get(0).getPromoRewardAmount().intValue());
    }

    @Test
    public void 商品范围每满数量减金额_指定品牌() {
        // given
        String brandCode = "01";
        // 创建购物车商品项列表
        List<ShoppingCartItem> shoppingCartItems = new ArrayList<>();
        shoppingCartItems.add(ShoppingCartFactory.createShoppingCartItemByBrand(brandCode, new BigDecimal(10), 6));
        // 创建购物车
        ShoppingCartDTO shoppingCart = ShoppingCartFactory.createShoppingCart(null, shoppingCartItems, null);

        // 创建活动范围
        List<ProductScope> activityProductList = new ArrayList<>();
        activityProductList.add(ProductScope.builder().brandCode(brandCode).build());
        // 创建活动模板
        Template template = Template.builder().templateEnum(TemplateEnum.T0203).condition("2").reward("3").build();
        // 创建活动
        ActivityCacheDTO activity = CacheActivityFactory.createActivity(ActivityTypeEnum.ACTIVITY, template, ProductSelectionEnum.SELECT, activityProductList, null, null);

        Map<String, ActivityCacheDTO> map = new HashMap<>();
        map.put(activity.getActivityModel().getActivityCode(), activity);

        when(activityCacheDomain.filterActivityByProduct(anyMap(), any(ProductCodes.class), any())).thenReturn(map);

        when(expressionService.getActivityExpression(anyString())).thenReturn(ExpressionHelper.DEF_EXPROSSION);
        shoppingCartDomain.queryActivity(shoppingCart, map,false);

        // when
        when(groovyService.findByCode(any(), any())).thenReturn(null);
        when(groovyService.run(any(), any())).thenReturn(true);
        when(activityPriceComponentDomain.getTenantPrecision(any())).thenReturn(2);
        List<ShoppingCartOutDTO> shoppingCartOutDTOS = calcExecuter.calc(new CalcShoppingCart(shoppingCart), map);

        // then
//        assertEquals(9, shoppingCartOutDTOS.get(0).getPromoRewardAmount().intValue());
    }

    @Test
    public void 商品范围每满金额减金额_指定品牌() {
        // given
        String brandCode = "01";
        // 创建购物车商品项列表
        List<ShoppingCartItem> shoppingCartItems = new ArrayList<>();
        shoppingCartItems.add(ShoppingCartFactory.createShoppingCartItemByBrand(brandCode, new BigDecimal(2), 6));
        // 创建购物车
        ShoppingCartDTO shoppingCart = ShoppingCartFactory.createShoppingCart(null, shoppingCartItems, null);

        // 创建活动范围
        List<ProductScope> activityProductList = new ArrayList<>();
        activityProductList.add(ProductScope.builder().brandCode(brandCode).build());
        // 创建活动模板
        Template template = Template.builder().templateEnum(TemplateEnum.T0204).condition("10").reward("5").build();
        // 创建活动
        ActivityCacheDTO activity = CacheActivityFactory.createActivity(ActivityTypeEnum.ACTIVITY, template, ProductSelectionEnum.SELECT, activityProductList, null, null);

        Map<String, ActivityCacheDTO> map = new HashMap<>();
        map.put(activity.getActivityModel().getActivityCode(), activity);

        when(activityCacheDomain.filterActivityByProduct(anyMap(), any(ProductCodes.class), any())).thenReturn(map);

        when(expressionService.getActivityExpression(anyString())).thenReturn(ExpressionHelper.DEF_EXPROSSION);
        shoppingCartDomain.queryActivity(shoppingCart, map,false);

        // when
        when(groovyService.findByCode(any(), any())).thenReturn(null);
        when(groovyService.run(any(), any())).thenReturn(true);
        when(activityPriceComponentDomain.getTenantPrecision(any())).thenReturn(2);
        List<ShoppingCartOutDTO> shoppingCartOutDTOS = calcExecuter.calc(new CalcShoppingCart(shoppingCart), map);

        // then
//        assertEquals(5, shoppingCartOutDTOS.get(0).getPromoRewardAmount().intValue());
    }

    @Test
    public void 订单满数量减金额_指定品牌() {
        // given
        String brandCode = "01";
        // 创建购物车商品项列表
        List<ShoppingCartItem> shoppingCartItems = new ArrayList<>();
        shoppingCartItems.add(ShoppingCartFactory.createShoppingCartItemByBrand(brandCode, new BigDecimal(10), 3));
        // 创建购物车
        ShoppingCartDTO shoppingCart = ShoppingCartFactory.createShoppingCart(null, shoppingCartItems, null);

        // 创建活动范围
        List<ProductScope> activityProductList = new ArrayList<>();
        activityProductList.add(ProductScope.builder().brandCode(brandCode).build());
        // 创建活动模板
        Template template = Template.builder().templateEnum(TemplateEnum.T0205).condition("2").reward("3").build();
        // 创建活动
        ActivityCacheDTO activity = CacheActivityFactory.createActivity(ActivityTypeEnum.ACTIVITY, template, ProductSelectionEnum.SELECT, activityProductList, null, null);

        Map<String, ActivityCacheDTO> map = new HashMap<>();
        map.put(activity.getActivityModel().getActivityCode(), activity);

        when(activityCacheDomain.filterActivityByProduct(anyMap(), any(ProductCodes.class), any())).thenReturn(map);

        when(expressionService.getActivityExpression(anyString())).thenReturn(ExpressionHelper.DEF_EXPROSSION);
        shoppingCartDomain.queryActivity(shoppingCart, map,false);

        // when
        when(groovyService.findByCode(any(), any())).thenReturn(null);
        when(groovyService.run(any(), any())).thenReturn(true);
        when(activityPriceComponentDomain.getTenantPrecision(any())).thenReturn(2);
        List<ShoppingCartOutDTO> shoppingCartOutDTOS = calcExecuter.calc(new CalcShoppingCart(shoppingCart), map);

        // then
//        assertEquals(3, shoppingCartOutDTOS.get(0).getPromoRewardAmount().intValue());
    }

    @Test
    public void 订单满金额减金额_指定品牌() {
        // given
        String brandCode = "01";
        // 创建购物车商品项列表
        List<ShoppingCartItem> shoppingCartItems = new ArrayList<>();
        shoppingCartItems.add(ShoppingCartFactory.createShoppingCartItemByBrand(brandCode, new BigDecimal(10), 3));
        // 创建购物车
        ShoppingCartDTO shoppingCart = ShoppingCartFactory.createShoppingCart(null, shoppingCartItems, null);

        // 创建活动范围
        List<ProductScope> activityProductList = new ArrayList<>();
        activityProductList.add(ProductScope.builder().brandCode(brandCode).build());
        // 创建活动模板
        Template template = Template.builder().templateEnum(TemplateEnum.T0206).condition("2").reward("3").build();
        // 创建活动
        ActivityCacheDTO activity = CacheActivityFactory.createActivity(ActivityTypeEnum.ACTIVITY, template, ProductSelectionEnum.SELECT, activityProductList, null, null);

        Map<String, ActivityCacheDTO> map = new HashMap<>();
        map.put(activity.getActivityModel().getActivityCode(), activity);

        when(activityCacheDomain.filterActivityByProduct(anyMap(), any(ProductCodes.class), any())).thenReturn(map);

        when(expressionService.getActivityExpression(anyString())).thenReturn(ExpressionHelper.DEF_EXPROSSION);
        shoppingCartDomain.queryActivity(shoppingCart, map,false);

        // when
        when(groovyService.findByCode(any(), any())).thenReturn(null);
        when(groovyService.run(any(), any())).thenReturn(true);
        when(activityPriceComponentDomain.getTenantPrecision(any())).thenReturn(2);
        List<ShoppingCartOutDTO> shoppingCartOutDTOS = calcExecuter.calc(new CalcShoppingCart(shoppingCart), map);

        // then
//        assertEquals(3, shoppingCartOutDTOS.get(0).getPromoRewardAmount().intValue());
    }

    @Test
    public void 满数量打折扣_指定品牌() {
        // given
        String brandCode = "01";
        // 创建购物车商品项列表
        List<ShoppingCartItem> shoppingCartItems = new ArrayList<>();
        shoppingCartItems.add(ShoppingCartFactory.createShoppingCartItemByBrand(brandCode, new BigDecimal(10), 3));
        // 创建购物车
        ShoppingCartDTO shoppingCart = ShoppingCartFactory.createShoppingCart(null, shoppingCartItems, null);

        // 创建活动范围
        List<ProductScope> activityProductList = new ArrayList<>();
        activityProductList.add(ProductScope.builder().brandCode(brandCode).build());
        // 创建活动模板
        Template template = Template.builder().templateEnum(TemplateEnum.T0301).condition("2").reward("0.6").build();
        // 创建活动
        ActivityCacheDTO activity = CacheActivityFactory.createActivity(ActivityTypeEnum.ACTIVITY, template, ProductSelectionEnum.SELECT, activityProductList, null, null);

        Map<String, ActivityCacheDTO> map = new HashMap<>();
        map.put(activity.getActivityModel().getActivityCode(), activity);

        when(activityCacheDomain.filterActivityByProduct(anyMap(), any(ProductCodes.class), any())).thenReturn(map);

        when(expressionService.getActivityExpression(anyString())).thenReturn(ExpressionHelper.DEF_EXPROSSION);
        shoppingCartDomain.queryActivity(shoppingCart, map,false);

        // when
        when(groovyService.findByCode(any(), any())).thenReturn(null);
        when(groovyService.run(any(), any())).thenReturn(true);
        when(activityPriceComponentDomain.getTenantPrecision(any())).thenReturn(2);
        List<ShoppingCartOutDTO> shoppingCartOutDTOS = calcExecuter.calc(new CalcShoppingCart(shoppingCart), map);

        // then
//        assertEquals(12, shoppingCartOutDTOS.get(0).getPromoRewardAmount().intValue());
    }

    @Test
    public void 商品范围满金额打折扣_指定品牌() {
        // given
        String brandCode = "01";
        // 创建购物车商品项列表
        List<ShoppingCartItem> shoppingCartItems = new ArrayList<>();
        shoppingCartItems.add(ShoppingCartFactory.createShoppingCartItemByBrand(brandCode, new BigDecimal(10), 3));
        // 创建购物车
        ShoppingCartDTO shoppingCart = ShoppingCartFactory.createShoppingCart(null, shoppingCartItems, null);

        // 创建活动范围
        List<ProductScope> activityProductList = new ArrayList<>();
        activityProductList.add(ProductScope.builder().brandCode(brandCode).build());
        // 创建活动模板
        Template template = Template.builder().templateEnum(TemplateEnum.T0302).condition("2").reward("0.6").build();
        // 创建活动
        ActivityCacheDTO activity = CacheActivityFactory.createActivity(ActivityTypeEnum.ACTIVITY, template, ProductSelectionEnum.SELECT, activityProductList, null, null);

        Map<String, ActivityCacheDTO> map = new HashMap<>();
        map.put(activity.getActivityModel().getActivityCode(), activity);

        when(activityCacheDomain.filterActivityByProduct(anyMap(), any(ProductCodes.class), any())).thenReturn(map);

        when(expressionService.getActivityExpression(anyString())).thenReturn(ExpressionHelper.DEF_EXPROSSION);
        shoppingCartDomain.queryActivity(shoppingCart, map,false);

        // when
        when(groovyService.findByCode(any(), any())).thenReturn(null);
        when(groovyService.run(any(), any())).thenReturn(true);
        when(activityPriceComponentDomain.getTenantPrecision(any())).thenReturn(2);
        List<ShoppingCartOutDTO> shoppingCartOutDTOS = calcExecuter.calc(new CalcShoppingCart(shoppingCart), map);

        // then
//        assertEquals(12, shoppingCartOutDTOS.get(0).getPromoRewardAmount().intValue());
    }

    @Test
    public void 商品范围第数量打折扣_指定品牌() {
        // given
        String brandCode = "01";
        // 创建购物车商品项列表
        List<ShoppingCartItem> shoppingCartItems = new ArrayList<>();
        shoppingCartItems.add(ShoppingCartFactory.createShoppingCartItemByBrand(brandCode, new BigDecimal(10), 3));
        // 创建购物车
        ShoppingCartDTO shoppingCart = ShoppingCartFactory.createShoppingCart(null, shoppingCartItems, "1");

        // 创建活动范围
        List<ProductScope> activityProductList = new ArrayList<>();
        activityProductList.add(ProductScope.builder().brandCode(brandCode).build());
        // 创建活动模板
        Template template = Template.builder().templateEnum(TemplateEnum.T0303).condition("2").reward("0.6").build();
        // 创建活动
        ActivityCacheDTO activity = CacheActivityFactory.createActivity(ActivityTypeEnum.ACTIVITY, template, ProductSelectionEnum.SELECT, activityProductList, null, new ArrayList<>());

        Map<String, ActivityCacheDTO> map = new HashMap<>();
        map.put(activity.getActivityModel().getActivityCode(), activity);

        when(activityCacheDomain.filterActivityByProduct(anyMap(), any(ProductCodes.class), any())).thenReturn(map);

        when(expressionService.getActivityExpression(anyString())).thenReturn(ExpressionHelper.DEF_EXPROSSION);
        shoppingCartDomain.queryActivity(shoppingCart, map,false);

        // when
        when(groovyService.findByCode(any(), any())).thenReturn(null);
        when(groovyService.run(any(), any())).thenReturn(true);
        when(activityPriceComponentDomain.getTenantPrecision(any())).thenReturn(2);
        List<ShoppingCartOutDTO> shoppingCartOutDTOS = calcExecuter.calc(new CalcShoppingCart(shoppingCart), map);

        // then
//        assertEquals(4, shoppingCartOutDTOS.get(0).getPromoRewardAmount().intValue());
    }

    @Test
    public void 订单满数量打折扣_指定品牌() {
        // given
        String brandCode = "01";
        // 创建购物车商品项列表
        List<ShoppingCartItem> shoppingCartItems = new ArrayList<>();
        shoppingCartItems.add(ShoppingCartFactory.createShoppingCartItemByBrand(brandCode, new BigDecimal(10), 3));
        // 创建购物车
        ShoppingCartDTO shoppingCart = ShoppingCartFactory.createShoppingCart(null, shoppingCartItems, null);
        // 创建活动范围
        List<ProductScope> activityProductList = new ArrayList<>();
        activityProductList.add(ProductScope.builder().brandCode(brandCode).build());
        // 创建活动模板
        Template template = Template.builder().templateEnum(TemplateEnum.T0304).condition("2").reward("0.6").build();
        // 创建活动
        ActivityCacheDTO activity = CacheActivityFactory.createActivity(ActivityTypeEnum.ACTIVITY, template, ProductSelectionEnum.SELECT, activityProductList, null, null);

        Map<String, ActivityCacheDTO> map = new HashMap<>();
        map.put(activity.getActivityModel().getActivityCode(), activity);

        when(activityCacheDomain.filterActivityByProduct(anyMap(), any(ProductCodes.class), any())).thenReturn(map);

        when(expressionService.getActivityExpression(anyString())).thenReturn(ExpressionHelper.DEF_EXPROSSION);
        shoppingCartDomain.queryActivity(shoppingCart, map,false);

        // when
        when(groovyService.findByCode(any(), any())).thenReturn(null);
        when(groovyService.run(any(), any())).thenReturn(true);
        when(activityPriceComponentDomain.getTenantPrecision(any())).thenReturn(2);
        List<ShoppingCartOutDTO> shoppingCartOutDTOS = calcExecuter.calc(new CalcShoppingCart(shoppingCart), map);

        // then
//        assertEquals(12, shoppingCartOutDTOS.get(0).getPromoRewardAmount().intValue());
    }

    @Test
    public void 订单满金额打折扣_指定品牌() {
        // given
        String brandCode = "01";
        // 创建购物车商品项列表
        List<ShoppingCartItem> shoppingCartItems = new ArrayList<>();
        shoppingCartItems.add(ShoppingCartFactory.createShoppingCartItemByBrand(brandCode, new BigDecimal(10), 3));
        // 创建购物车
        ShoppingCartDTO shoppingCart = ShoppingCartFactory.createShoppingCart(null, shoppingCartItems, null);
        // 创建活动范围
        List<ProductScope> activityProductList = new ArrayList<>();
        activityProductList.add(ProductScope.builder().brandCode(brandCode).build());
        // 创建活动模板
        Template template = Template.builder().templateEnum(TemplateEnum.T0305).condition("2").reward("0.6").build();
        // 创建活动
        ActivityCacheDTO activity = CacheActivityFactory.createActivity(ActivityTypeEnum.ACTIVITY, template, ProductSelectionEnum.SELECT, activityProductList, null, null);

        Map<String, ActivityCacheDTO> map = new HashMap<>();
        map.put(activity.getActivityModel().getActivityCode(), activity);

        when(activityCacheDomain.filterActivityByProduct(anyMap(), any(ProductCodes.class), any())).thenReturn(map);

        when(expressionService.getActivityExpression(anyString())).thenReturn(ExpressionHelper.DEF_EXPROSSION);
        shoppingCartDomain.queryActivity(shoppingCart, map,false);

        // when
        when(groovyService.findByCode(any(), any())).thenReturn(null);
        when(groovyService.run(any(), any())).thenReturn(true);
        when(activityPriceComponentDomain.getTenantPrecision(any())).thenReturn(2);
        List<ShoppingCartOutDTO> shoppingCartOutDTOS = calcExecuter.calc(new CalcShoppingCart(shoppingCart), map);


        // then
//        assertEquals(12, shoppingCartOutDTOS.get(0).getPromoRewardAmount().intValue());
    }

    @Test
    public void 商品范围满数量设为总计特价_指定商品() {
        // given
        String productCode = "01";
        String skuCode = "02";
        // 创建购物车商品项列表
        List<ShoppingCartItem> shoppingCartItems = new ArrayList<>();
        shoppingCartItems.add(ShoppingCartFactory.createShoppingCartItemBySku(productCode, skuCode, new BigDecimal(10), 3));
        // 创建购物车
        ShoppingCartDTO shoppingCart = ShoppingCartFactory.createShoppingCart(null, shoppingCartItems, null);
        // 创建活动范围
        List<ActivityProductDetail> activityProductDetails = new ArrayList<>();
        activityProductDetails.add(ActivityProductDetail.builder().productCode(productCode).skuCode(skuCode).build());
        // 创建活动模板
        Template template = Template.builder().templateEnum(TemplateEnum.T0401).condition("2").reward("6").build();
        // 创建活动
        ActivityCacheDTO activity = CacheActivityFactory.createActivity(ActivityTypeEnum.ACTIVITY, template, ProductSelectionEnum.SELECT, null, activityProductDetails, null);

        Map<String, ActivityCacheDTO> map = new HashMap<>();
        map.put(activity.getActivityModel().getActivityCode(), activity);

        when(activityCacheDomain.filterActivityByProduct(anyMap(), any(ProductCodes.class), any())).thenReturn(map);

        when(expressionService.getActivityExpression(anyString())).thenReturn(ExpressionHelper.DEF_EXPROSSION);
        shoppingCartDomain.queryActivity(shoppingCart, map,false);

        // when
        when(groovyService.findByCode(any(), any())).thenReturn(null);
        when(groovyService.run(any(), any())).thenReturn(true);
        when(activityPriceComponentDomain.getTenantPrecision(any())).thenReturn(2);
        List<ShoppingCartOutDTO> shoppingCartOutDTOS = calcExecuter.calc(new CalcShoppingCart(shoppingCart), map);

        // then
//        assertEquals(14, shoppingCartOutDTOS.get(0).getPromoRewardAmount().intValue());
    }

    @Test
    public void 商品范围每满数量设为总计特价_指定商品() {
        // given
        String productCode = "01";
        String skuCode = "02";
        // 创建购物车商品项列表
        List<ShoppingCartItem> shoppingCartItems = new ArrayList<>();
        shoppingCartItems.add(ShoppingCartFactory.createShoppingCartItemBySku(productCode, skuCode, new BigDecimal(10), 4));
        // 创建购物车
        ShoppingCartDTO shoppingCart = ShoppingCartFactory.createShoppingCart(null, shoppingCartItems, null);
        // 创建活动范围
        List<ActivityProductDetail> activityProductDetails = new ArrayList<>();
        activityProductDetails.add(ActivityProductDetail.builder().productCode(productCode).skuCode(skuCode).build());
        // 创建活动模板
        Template template = Template.builder().templateEnum(TemplateEnum.T0402).condition("2").reward("6").build();
        // 创建活动
        ActivityCacheDTO activity = CacheActivityFactory.createActivity(ActivityTypeEnum.ACTIVITY, template, ProductSelectionEnum.SELECT, null, activityProductDetails, null);

        Map<String, ActivityCacheDTO> map = new HashMap<>();
        map.put(activity.getActivityModel().getActivityCode(), activity);

        when(activityCacheDomain.filterActivityByProduct(anyMap(), any(ProductCodes.class), any())).thenReturn(map);

        when(expressionService.getActivityExpression(anyString())).thenReturn(ExpressionHelper.DEF_EXPROSSION);
        shoppingCartDomain.queryActivity(shoppingCart, map,false);

        // when
        when(groovyService.findByCode(any(), any())).thenReturn(null);
        when(groovyService.run(any(), any())).thenReturn(true);
        when(activityPriceComponentDomain.getTenantPrecision(any())).thenReturn(2);
        List<ShoppingCartOutDTO> shoppingCartOutDTOS = calcExecuter.calc(new CalcShoppingCart(shoppingCart), map);

        // then
//        assertEquals(28, shoppingCartOutDTOS.get(0).getPromoRewardAmount().intValue());
    }

    @Test
    public void 订单满数量包邮_指定属性() {
        // given
        String attributeCode = "01";
        String attributeValue = "001";
        // 创建购物车商品项列表
        List<ShoppingCartItem> shoppingCartItems = new ArrayList<>();
        shoppingCartItems.add(ShoppingCartFactory.createShoppingCartItemByAttributeOne(attributeCode, attributeValue, new BigDecimal(10), 3));
        // 创建购物车
        ShoppingCartDTO shoppingCart = ShoppingCartFactory.createShoppingCart(null, shoppingCartItems, null);
        // 创建活动范围
        List<ProductScope> activityProductList = new ArrayList<>();
        activityProductList.add(ProductScope.builder().attributes(
                Arrays.asList(ProductAttribute.builder().attributeCode(attributeCode).attributeValues(attributeValue).build())).build());
        // 创建活动模板
        Template template = Template.builder().templateEnum(TemplateEnum.T0501).condition("2").reward("0").build();
        // 创建活动
        ActivityCacheDTO activity = CacheActivityFactory.createActivity(ActivityTypeEnum.ACTIVITY, template, ProductSelectionEnum.SELECT, activityProductList, null, null);

        Map<String, ActivityCacheDTO> map = new HashMap<>();
        map.put(activity.getActivityModel().getActivityCode(), activity);

        when(activityCacheDomain.filterActivityByProduct(anyMap(), any(ProductCodes.class), any())).thenReturn(map);

        when(expressionService.getActivityExpression(anyString())).thenReturn(ExpressionHelper.DEF_EXPROSSION);
        shoppingCartDomain.queryActivity(shoppingCart, map,false);

        // when
        when(groovyService.findByCode(any(), any())).thenReturn(null);
        when(groovyService.run(any(), any())).thenReturn(true);
        when(activityPriceComponentDomain.getTenantPrecision(any())).thenReturn(2);
        List<ShoppingCartOutDTO> shoppingCartOutDTOS = calcExecuter.calc(new CalcShoppingCart(shoppingCart), map);

        // then
//        assertEquals(true, shoppingCartOutDTOS.get(0).isEffectiveFlag());
//        assertEquals("05", shoppingCartOutDTOS.get(0).getRewardType());
    }

    @Test
    public void test0502_0103020203030405订单满金额包邮() {

        // 创建购物车
        ShoppingCartDTO shoppingCart = ShoppingCartFactory.createShoppingCart(null,
                Arrays.asList(ShoppingCartFactory.createShoppingCartItemByAttributeOne("AC01", "AV01", new BigDecimal(10), 4)), null);

        // 创建活动
        ActivityCacheDTO activity = CacheActivityFactory.createActivity(ActivityTypeEnum.ACTIVITY,
                Template.builder().templateEnum(TemplateEnum.T0502).condition("2").reward("0").build(), ProductSelectionEnum.SELECT,
                Arrays.asList(ProductScope.builder()
                        .attributes(Arrays.asList(ProductAttribute.builder().attributeCode("AC01").attributeValues("AV01").build()))
                        .build()),
                null, null);

        Map<String, ActivityCacheDTO> map = new HashMap<>();
        map.put(activity.getActivityModel().getActivityCode(), activity);

        when(activityCacheDomain.filterActivityByProduct(anyMap(), any(ProductCodes.class), any())).thenReturn(map);
        when(expressionService.getActivityExpression(anyString())).thenReturn(ExpressionHelper.DEF_EXPROSSION);

        shoppingCartDomain.queryActivity(shoppingCart, map,false);
        when(groovyService.findByCode(any(), any())).thenReturn(null);
        when(groovyService.run(any(), any())).thenReturn(true);
        when(activityPriceComponentDomain.getTenantPrecision(any())).thenReturn(2);
        List<ShoppingCartOutDTO> shoppingCartOutDTOS = calcExecuter.calc(new CalcShoppingCart(shoppingCart), map);

        // then
//        assertEquals(true, shoppingCartOutDTOS.get(0).isEffectiveFlag());
//        assertEquals("05", shoppingCartOutDTOS.get(0).getRewardType());
    }

    @Test
    public void test0601_0103020203020406订单满数量送赠品() {

        // 创建购物车
        ShoppingCartDTO shoppingCart = ShoppingCartFactory.createShoppingCart(null,
                Arrays.asList(ShoppingCartFactory.createShoppingCartItemByBrand("BC01", new BigDecimal(10), 3)), null);

        // 创建活动
        ActivityCacheDTO activity = CacheActivityFactory.createActivity(ActivityTypeEnum.ACTIVITY,
                Template.builder().templateEnum(TemplateEnum.T0601).condition("2").build(), ProductSelectionEnum.SELECT,
                Arrays.asList(ProductScope.builder().brandCode("BC01").build()), null, null);

        Map<String, ActivityCacheDTO> map = new HashMap<>();
        map.put(activity.getActivityModel().getActivityCode(), activity);

        when(activityCacheDomain.filterActivityByProduct(anyMap(), any(ProductCodes.class), any())).thenReturn(map);
        when(expressionService.getActivityExpression(anyString())).thenReturn(ExpressionHelper.DEF_EXPROSSION);

        shoppingCartDomain.queryActivity(shoppingCart, map,false);
        when(groovyService.findByCode(any(), any())).thenReturn(null);
        when(groovyService.run(any(), any())).thenReturn(true);
        when(activityPriceComponentDomain.getTenantPrecision(any())).thenReturn(2);
        List<ShoppingCartOutDTO> shoppingCartOutDTOS = calcExecuter.calc(new CalcShoppingCart(shoppingCart), map);

        // then
//        assertEquals(true, shoppingCartOutDTOS.get(0).isEffectiveFlag());
//        assertEquals("06", shoppingCartOutDTOS.get(0).getRewardType());
    }

    @Test
    public void test0602_0103020203030406订单满金额送赠品() {

        // 创建购物车
        ShoppingCartDTO shoppingCart = ShoppingCartFactory.createShoppingCart(null,
                Arrays.asList(ShoppingCartFactory.createShoppingCartItemByBrand("BC01", new BigDecimal(10), 4)), null);

        // 创建活动
        ActivityCacheDTO activity = CacheActivityFactory.createActivity(ActivityTypeEnum.ACTIVITY,
                Template.builder().templateEnum(TemplateEnum.T0602).condition("2").build(), ProductSelectionEnum.SELECT,
                Arrays.asList(ProductScope.builder().brandCode("BC01").build()), null, null);

        Map<String, ActivityCacheDTO> map = new HashMap<>();
        map.put(activity.getActivityModel().getActivityCode(), activity);

        when(activityCacheDomain.filterActivityByProduct(anyMap(), any(ProductCodes.class), any())).thenReturn(map);
        when(expressionService.getActivityExpression(anyString())).thenReturn(ExpressionHelper.DEF_EXPROSSION);

        shoppingCartDomain.queryActivity(shoppingCart, map,false);
        when(groovyService.findByCode(any(), any())).thenReturn(null);
        when(groovyService.run(any(), any())).thenReturn(true);
        when(activityPriceComponentDomain.getTenantPrecision(any())).thenReturn(2);
        List<ShoppingCartOutDTO> shoppingCartOutDTOS = calcExecuter.calc(new CalcShoppingCart(shoppingCart), map);

        // then
//        assertEquals(true, shoppingCartOutDTOS.get(0).isEffectiveFlag());
//        assertEquals("06", shoppingCartOutDTOS.get(0).getRewardType());
    }

    @Test
    public void test0603_0102020203020406商品范围满数量送赠品() {

        // 创建购物车
        ShoppingCartDTO shoppingCart = ShoppingCartFactory.createShoppingCart(null,
                Arrays.asList(ShoppingCartFactory.createShoppingCartItemByBrand("BC01", new BigDecimal(10), 4)), null);

        // 创建活动
        ActivityCacheDTO activity = CacheActivityFactory.createActivity(ActivityTypeEnum.ACTIVITY,
                Template.builder().templateEnum(TemplateEnum.T0603).condition("2").build(), ProductSelectionEnum.SELECT,
                Arrays.asList(ProductScope.builder().brandCode("BC01").build()), null, null);

        Map<String, ActivityCacheDTO> map = new HashMap<>();
        map.put(activity.getActivityModel().getActivityCode(), activity);

        when(activityCacheDomain.filterActivityByProduct(anyMap(), any(ProductCodes.class), any())).thenReturn(map);
        when(expressionService.getActivityExpression(anyString())).thenReturn(ExpressionHelper.DEF_EXPROSSION);

        shoppingCartDomain.queryActivity(shoppingCart, map,false);
        when(groovyService.findByCode(any(), any())).thenReturn(null);
        when(groovyService.run(any(), any())).thenReturn(true);
        when(activityPriceComponentDomain.getTenantPrecision(any())).thenReturn(2);
        List<ShoppingCartOutDTO> shoppingCartOutDTOS = calcExecuter.calc(new CalcShoppingCart(shoppingCart), map);

        // then
//        assertEquals(true, shoppingCartOutDTOS.get(0).isEffectiveFlag());
//        assertEquals("06", shoppingCartOutDTOS.get(0).getRewardType());
    }

    @Test
    public void test0604_0102020203030406商品范围满金额送赠品() {

        // 创建购物车
        ShoppingCartDTO shoppingCart = ShoppingCartFactory.createShoppingCart(null,
                Arrays.asList(ShoppingCartFactory.createShoppingCartItemByBrand("BC01", new BigDecimal(10), 4)), null);

        Map<String, ActivityCacheDTO> map = new HashMap<>();

        // 创建活动
        ActivityCacheDTO activity = CacheActivityFactory.createActivity(ActivityTypeEnum.ACTIVITY,
                Template.builder().templateEnum(TemplateEnum.T0604).condition("2").build(), ProductSelectionEnum.SELECT,
                Arrays.asList(ProductScope.builder().brandCode("BC01").build()), null, null);

        map.put(activity.getActivityModel().getActivityCode(), activity);

        when(activityCacheDomain.filterActivityByProduct(anyMap(), any(ProductCodes.class), any())).thenReturn(map);
        when(expressionService.getActivityExpression(anyString())).thenReturn(ExpressionHelper.DEF_EXPROSSION);

        shoppingCartDomain.queryActivity(shoppingCart, map,false);
        when(groovyService.findByCode(any(), any())).thenReturn(null);
        when(groovyService.run(any(), any())).thenReturn(true);
        when(activityPriceComponentDomain.getTenantPrecision(any())).thenReturn(2);
        List<ShoppingCartOutDTO> shoppingCartOutDTOS = calcExecuter.calc(new CalcShoppingCart(shoppingCart), map);

        // then
//        assertEquals(true, shoppingCartOutDTOS.get(0).isEffectiveFlag());
//        assertEquals("06", shoppingCartOutDTOS.get(0).getRewardType());
    }

    @Test
    public void test0605_0102020303020406商品范围每满数量送赠品() {

        // 创建购物车
        ShoppingCartDTO shoppingCart = ShoppingCartFactory.createShoppingCart(null,
                Arrays.asList(ShoppingCartFactory.createShoppingCartItemByBrand("BC01", new BigDecimal(10), 4)), null);

        Map<String, ActivityCacheDTO> map = new HashMap<>();

        // 创建活动
        ActivityCacheDTO activity = CacheActivityFactory.createActivity(ActivityTypeEnum.ACTIVITY,
                Template.builder().templateEnum(TemplateEnum.T0605).condition("2").build(), ProductSelectionEnum.SELECT,
                Arrays.asList(ProductScope.builder().brandCode("BC01").build()), null, null);

        map.put(activity.getActivityModel().getActivityCode(), activity);

        when(activityCacheDomain.filterActivityByProduct(anyMap(), any(ProductCodes.class), any())).thenReturn(map);
        when(expressionService.getActivityExpression(anyString())).thenReturn(ExpressionHelper.DEF_EXPROSSION);

        shoppingCartDomain.queryActivity(shoppingCart, map,false);
        when(groovyService.findByCode(any(), any())).thenReturn(null);
        when(groovyService.run(any(), any())).thenReturn(true);
        when(activityPriceComponentDomain.getTenantPrecision(any())).thenReturn(2);
        List<ShoppingCartOutDTO> shoppingCartOutDTOS = calcExecuter.calc(new CalcShoppingCart(shoppingCart), map);

        // then
//        assertEquals(true, shoppingCartOutDTOS.get(0).isEffectiveFlag());
//        assertEquals("06", shoppingCartOutDTOS.get(0).getRewardType());
    }

    @Test
    public void test0606_0102020303030406商品范围每满金额送赠品() {

        // 创建购物车
        ShoppingCartDTO shoppingCart = ShoppingCartFactory.createShoppingCart(null,
                Arrays.asList(ShoppingCartFactory.createShoppingCartItemByBrand("BC01", new BigDecimal(10), 4)), null);

        Map<String, ActivityCacheDTO> map = new HashMap<>();

        // 创建活动
        ActivityCacheDTO activity = CacheActivityFactory.createActivity(ActivityTypeEnum.ACTIVITY,
                Template.builder().templateEnum(TemplateEnum.T0606).condition("2").build(), ProductSelectionEnum.SELECT,
                Arrays.asList(ProductScope.builder().brandCode("BC01").build()), null, null);

        map.put(activity.getActivityModel().getActivityCode(), activity);

        when(activityCacheDomain.filterActivityByProduct(anyMap(), any(ProductCodes.class), any())).thenReturn(map);
        when(expressionService.getActivityExpression(anyString())).thenReturn(ExpressionHelper.DEF_EXPROSSION);

        shoppingCartDomain.queryActivity(shoppingCart, map,false);
        when(groovyService.findByCode(any(), any())).thenReturn(null);
        when(groovyService.run(any(), any())).thenReturn(true);
        when(activityPriceComponentDomain.getTenantPrecision(any())).thenReturn(2);
        List<ShoppingCartOutDTO> shoppingCartOutDTOS = calcExecuter.calc(new CalcShoppingCart(shoppingCart), map);

        // then
//        assertEquals(true, shoppingCartOutDTOS.get(0).isEffectiveFlag());
//        assertEquals("06", shoppingCartOutDTOS.get(0).getRewardType());
    }

    @Test
    public void test0701_0102020503020404多组商品范围各选1件设为总计特价_指定多个商品范围() {

        // 创建购物车
        ShoppingCartDTO shoppingCart = ShoppingCartFactory.createShoppingCart(null,
                Arrays.asList(
                        ShoppingCartFactory.createShoppingCartItemByBrand("BC01", new BigDecimal(5), 4),
                        ShoppingCartFactory.createShoppingCartItemByBrandAndSku("BC02", "02", new BigDecimal(10), 3)), null);

        Map<String, ActivityCacheDTO> map = new HashMap<>();

        // 创建活动
        ActivityCacheDTO activity = CacheActivityFactory.createActivity(ActivityTypeEnum.ACTIVITY,
                Template.builder().templateEnum(TemplateEnum.T0701).condition("2").reward("2").build(), ProductSelectionEnum.SELECT,
                Arrays.asList(ProductScope.builder().brandCode("BC01").build(), ProductScope.builder().brandCode("BC02").seqNum(2).build()), null, null);

        map.put(activity.getActivityModel().getActivityCode(), activity);

        when(activityCacheDomain.filterActivityByProduct(anyMap(), any(ProductCodes.class), any())).thenReturn(map);
        when(expressionService.getActivityExpression(anyString())).thenReturn(ExpressionHelper.DEF_EXPROSSION);

        shoppingCartDomain.queryActivity(shoppingCart, map,false);
        when(groovyService.findByCode(any(), any())).thenReturn(null);
        when(groovyService.run(any(), any())).thenReturn(true);
        when(activityPriceComponentDomain.getTenantPrecision(any())).thenReturn(2);
        List<ShoppingCartOutDTO> shoppingCartOutDTOS = calcExecuter.calc(new CalcShoppingCart(shoppingCart), map);

        // then
//        assertEquals(50, shoppingCartOutDTOS.get(0).getPromoRewardAmount().intValue());
    }

    @Test
    public void test0801_0102020303020407商品范围每满数量送同类商品() {

        // 创建购物车
        ShoppingCartDTO shoppingCart = ShoppingCartFactory.createShoppingCart(null,
                Arrays.asList(ShoppingCartFactory.createShoppingCartItemByBrand("BC01", new BigDecimal(10), 4)), null);

        // 创建活动
        ActivityCacheDTO activity = CacheActivityFactory.createActivity(ActivityTypeEnum.ACTIVITY,
                Template.builder().templateEnum(TemplateEnum.T0801).condition("2").reward("2").build(), ProductSelectionEnum.SELECT,
                Arrays.asList(ProductScope.builder().brandCode("BC01").build()), null, null);

        Map<String, ActivityCacheDTO> map = new HashMap<>();
        map.put(activity.getActivityModel().getActivityCode(), activity);

        when(activityCacheDomain.filterActivityByProduct(anyMap(), any(ProductCodes.class), any())).thenReturn(map);
        when(expressionService.getActivityExpression(anyString())).thenReturn(ExpressionHelper.DEF_EXPROSSION);

        shoppingCartDomain.queryActivity(shoppingCart, map,false);
        when(groovyService.findByCode(any(), any())).thenReturn(null);
        when(groovyService.run(any(), any())).thenReturn(true);
        when(activityPriceComponentDomain.getTenantPrecision(any())).thenReturn(2);
        List<ShoppingCartOutDTO> shoppingCartOutDTOS = calcExecuter.calc(new CalcShoppingCart(shoppingCart), map);

        // then
//        assertEquals(20, shoppingCartOutDTOS.get(0).getPromoRewardAmount().intValue());
    }

    @Test
    public void test0802_0102020303020408商品范围每满数量送不同类商品() {

        // 创建购物车
        ShoppingCartDTO shoppingCart = ShoppingCartFactory.createShoppingCart(null,
                Arrays.asList(
                        ShoppingCartFactory.createShoppingCartItemByBrand("BC01", new BigDecimal(100), 3),
                        ShoppingCartFactory.createShoppingCartItemByBrandAndSku("BC02", "02", new BigDecimal(10), 4)), null);

        Map<String, ActivityCacheDTO> map = new HashMap<>();

        // 创建活动
        ActivityCacheDTO activity = CacheActivityFactory.createActivity(ActivityTypeEnum.ACTIVITY,
                Template.builder().templateEnum(TemplateEnum.T0802).condition("2").reward("1").build(), ProductSelectionEnum.SELECT,
                Arrays.asList(ProductScope.builder().brandCode("BC01").build(), ProductScope.builder().brandCode("BC02").seqNum(2).build()), null, null);

        map.put(activity.getActivityModel().getActivityCode(), activity);

        when(activityCacheDomain.filterActivityByProduct(anyMap(), any(ProductCodes.class), any())).thenReturn(map);
        when(expressionService.getActivityExpression(anyString())).thenReturn(ExpressionHelper.DEF_EXPROSSION);

        shoppingCartDomain.queryActivity(shoppingCart, map,false);
        when(groovyService.findByCode(any(), any())).thenReturn(null);
        when(groovyService.run(any(), any())).thenReturn(true);
        when(activityPriceComponentDomain.getTenantPrecision(any())).thenReturn(2);
        List<ShoppingCartOutDTO> shoppingCartOutDTOS = calcExecuter.calc(new CalcShoppingCart(shoppingCart), map);

        // then
//        assertEquals(340, shoppingCartOutDTOS.get(0).getPromoRewardAmount().intValue());
    }

    @Test
    public void test0301_0102020203020402范围满数量打折扣_0103020203030401订单满金额减金额() {

        // 创建购物车
        ShoppingCartDTO shoppingCart = ShoppingCartFactory.createShoppingCart(null,
                Arrays.asList(ShoppingCartFactory.createShoppingCartItem("CC01", "BC01", "PC01", "SC01", new BigDecimal(10), 3, null)), null);

        Map<String, ActivityCacheDTO> map = new HashMap<>();

        // 创建活动范围满数量打折扣
        ActivityCacheDTO activity1 = CacheActivityFactory.createActivity(ActivityTypeEnum.ACTIVITY,
                Template.builder().templateEnum(TemplateEnum.T0301).condition("2").reward("0.6").build(), ProductSelectionEnum.SELECT,
                Arrays.asList(ProductScope.builder().categoryCode("CC01").brandCode("BC01").build()), null, null);
        map.put(activity1.getActivityModel().getActivityCode(), activity1);

        // 创建活动订单满金额减金额
        ActivityCacheDTO activity2 = CacheActivityFactory.createActivity(ActivityTypeEnum.ACTIVITY,
                Template.builder().templateEnum(TemplateEnum.T0206).condition("10").reward("2").build(), ProductSelectionEnum.SELECT,
                Arrays.asList(ProductScope.builder().categoryCode("CC01").brandCode("BC01").build()), null, null);
        map.put(activity2.getActivityModel().getActivityCode(), activity2);

        when(activityCacheDomain.filterActivityByProduct(anyMap(), any(ProductCodes.class), any())).thenReturn(map);

        when(expressionService.getActivityExpression(anyString())).thenReturn(ExpressionHelper.DEF_EXPROSSION);
        shoppingCartDomain.queryActivity(shoppingCart, map,false);

        // when
        when(groovyService.findByCode(any(), any())).thenReturn(null);
        when(groovyService.run(any(), any())).thenReturn(true);
        when(activityPriceComponentDomain.getTenantPrecision(any())).thenReturn(2);
        List<ShoppingCartOutDTO> shoppingCartOutDTOS = calcExecuter.calc(new CalcShoppingCart(shoppingCart), map);

        // then
//        assertEquals(12, shoppingCartOutDTOS.get(0).getPromoRewardAmount().intValue());
//        assertEquals(2, shoppingCartOutDTOS.get(1).getPromoRewardAmount().intValue());
    }

    @Test
    public void test0101_0101020103010401单品无条件减金额_计算结果合并商品() {

        // 创建购物车
        ShoppingCartDTO shoppingCart = ShoppingCartFactory.createShoppingCart(null,
                Arrays.asList(
                        ShoppingCartFactory.createShoppingCartItem("CC01", "BC01", "PC01", "SC01", new BigDecimal(10), 3, null),
                        ShoppingCartFactory.createShoppingCartItem("CC01", "BC01", "PC02", "SC02", new BigDecimal(20), 2, null)),
                null);

        Map<String, ActivityCacheDTO> map = new HashMap<>();

        // 创建活动范围满数量打折扣
        ActivityCacheDTO activity1 = CacheActivityFactory.createActivity(ActivityTypeEnum.ACTIVITY,
                Template.builder().templateEnum(TemplateEnum.T0101).reward("2").build(), ProductSelectionEnum.SELECT,
                Arrays.asList(ProductScope.builder().categoryCode("CC01").brandCode("BC01").build()), null, null);

        map.put(activity1.getActivityModel().getActivityCode(), activity1);

        when(activityCacheDomain.filterActivityByProduct(anyMap(), any(ProductCodes.class), any())).thenReturn(map);
        when(expressionService.getActivityExpression(anyString())).thenReturn(ExpressionHelper.DEF_EXPROSSION);

        shoppingCartDomain.queryActivity(shoppingCart, map,false);

        // when
        when(groovyService.findByCode(any(), any())).thenReturn(null);
        when(groovyService.run(any(), any())).thenReturn(true);
        when(activityPriceComponentDomain.getTenantPrecision(any())).thenReturn(2);
        List<ShoppingCartOutDTO> shoppingCartOutDTOS = calcExecuter.calc(new CalcShoppingCart(shoppingCart), map);

        // then
//        assertEquals(1, shoppingCartOutDTOS.size());
//        assertEquals(10, shoppingCartOutDTOS.get(0).getPromoRewardAmount().intValue());
    }

    @Test
    public void test1102_0102020303020401范围每满数量减金额_0104020103010402单件商品用券打折扣() {

        // 创建购物车
        ShoppingCartDTO shoppingCart = ShoppingCartFactory.createShoppingCart(
                "616091807512770038",
                Arrays.asList(
                        ShoppingCartFactory.createShoppingCartItem("CC01", "BC01", "PC01", "SC01", new BigDecimal(1000), 3, null),
                        ShoppingCartFactory.createShoppingCartItem("CC01", "BC01", "PC01", "SC02", new BigDecimal(1000), 3, null)),
                "MC001");

        Map<String, ActivityCacheDTO> map = new HashMap<>();

        // Create promotion activity
        ActivityCacheDTO activity = CacheActivityFactory.createActivity(ActivityTypeEnum.ACTIVITY,
                Template.builder().templateEnum(TemplateEnum.T0101).reward("50").build(),
                ProductSelectionEnum.SELECT,
                Arrays.asList(ProductScope.builder().seqNum(1).build()), null, null);
        map.put(activity.getActivityModel().getActivityCode(), activity);

        ActivityCacheDTO activity1 = CacheActivityFactory.createActivity(ActivityTypeEnum.COUPON,
                Template.builder().templateEnum(TemplateEnum.T1102).reward("0.8").build(),
                ProductSelectionEnum.SELECT,
                Arrays.asList(ProductScope.builder().seqNum(1).build()), null, null);
        map.put(activity1.getActivityModel().getActivityCode(), activity1);

        when(activityCacheDomain.filterActivityByProduct(anyMap(), any(), any())).thenReturn(map);
        when(activityCacheDomain.filterNoProductListPrice(anyMap(), Mockito.any())).thenReturn(map);

        when(activityCacheDomain.filterNoRightOfFirstRefusal(anyMap(), any(), any())).thenReturn(map);
        when(expressionService.getActivityExpression(anyString())).thenReturn(ExpressionHelper.DEF_EXPROSSION);
        when(couponCodeUserDomain.isCanUse(anyString(), anyString(), anyString(), anyMap(), anyBoolean())).thenReturn(activity1.getActivityModel().getActivityCode());

        TPromoCouponInnerCodeVO activityCodeByCouponCode = new TPromoCouponInnerCodeVO();
        when(couponInnerCodeService.findActivityCodeByCouponCode(anyString(), anyString())).thenReturn(activityCodeByCouponCode);

        ReflectionTestUtils.setField(shoppingCartDomain, "groupBlackList", Lists.newArrayList("tenantCode"));
        shoppingCart = shoppingCartDomain.queryActivity(shoppingCart, map,true);

        // when
        when(groovyService.findByCode(any(), any())).thenReturn(null);
        when(groovyService.run(any(), any())).thenReturn(true);
        when(activityPriceComponentDomain.getTenantPrecision(any())).thenReturn(2);
        List<ShoppingCartOutDTO> shoppingCartOutDTOS = calcExecuter.calc(new CalcShoppingCart(shoppingCart), map);

        // then
//        assertEquals("200.00", shoppingCartOutDTOS.get(0).getPromoRewardAmount().toString());
//        assertEquals(1, shoppingCartOutDTOS.get(0).getRewardTimes());
//        assertEquals("2650.00", shoppingCartOutDTOS.get(0).getShoppingCartItems().get(0).getPromoAmount().toString());
//        assertEquals("3", shoppingCartOutDTOS.get(0).getShoppingCartItems().get(0).getPromoQuantity().toString());
//        assertEquals("2850.00", shoppingCartOutDTOS.get(0).getShoppingCartItems().get(1).getPromoAmount().toString());
//        assertEquals("3", shoppingCartOutDTOS.get(0).getShoppingCartItems().get(1).getPromoQuantity().toString());
//
//        assertEquals("300.00", shoppingCartOutDTOS.get(1).getPromoRewardAmount().toString());
//        assertEquals(1, shoppingCartOutDTOS.get(1).getRewardTimes());
    }

    @Test
    public void resetPrice() {
        List<ShoppingCartOutDTO> shoppingCartOutDTOs = new ArrayList<>();
        BigDecimal total = new BigDecimal("-1.99");


        ShoppingCartOutDTO shoppingCartOutDTO = new ShoppingCartOutDTO();
        List<ShoppingCartItemOutDTO> shoppingCartItems = new ArrayList<>();
        ShoppingCartItemOutDTO shoppingCartItemOutDTO = new ShoppingCartItemOutDTO();
        shoppingCartItemOutDTO.setSelectionFlag(FlagTypeEnum.YES.code());
        shoppingCartItemOutDTO.setProductPrice(new BigDecimal("1"));
        shoppingCartItemOutDTO.setPromoAmount(new BigDecimal("4"));
        shoppingCartItemOutDTO.setQuantity(2);
        shoppingCartItems.add(shoppingCartItemOutDTO);
        shoppingCartOutDTO.setShoppingCartItems(shoppingCartItems);
        shoppingCartOutDTOs.add(shoppingCartOutDTO);
        calcExecuter.resetPrice(shoppingCartOutDTOs, total);
    }

    @Test
    public void singleItemDifferentPrice_empty(){
        ShoppingCartOutDTO shoppingCartOutDTO = new ShoppingCartOutDTO();

        List<ShoppingCartItemOutDTO> shoppingCartItems = new ArrayList<>();
        ShoppingCartItemOutDTO outDTO = new ShoppingCartItemOutDTO();

        List<ShoppingCartItemActivityOutDTO> shoppingCartItemActivitys = new ArrayList<>();
        ShoppingCartItemActivityOutDTO itemActivityOutDTO = new ShoppingCartItemActivityOutDTO();
        itemActivityOutDTO.setOpsType(OpsTypeEnum.OPS_108.code());
        itemActivityOutDTO.setActivityCode("111");

        shoppingCartItemActivitys.add(itemActivityOutDTO);

        outDTO.setShoppingCartItemActivitys(shoppingCartItemActivitys);
        shoppingCartItems.add(outDTO);

        List<ProductSkuDetailDTO> productSkuDetailDTOS = new ArrayList<>();
        ProductSkuDetailDTO detailDTO = new ProductSkuDetailDTO();
        detailDTO.setProductCode("222");
        detailDTO.setSkuCode("123");
        detailDTO.setPromoPrice(BigDecimal.TEN);
        detailDTO.setActivityCode("111");

        productSkuDetailDTOS.add(detailDTO);


        shoppingCartOutDTO.setShoppingCartItems(shoppingCartItems);
        when(productDetailService.queryOneGroupByActivityCodes(any(),any())).thenReturn(new ArrayList<>());


        calcExecuter.singleItemDifferentPrice("1",shoppingCartOutDTO);
    }

    @Test
    public void singleItemDifferentPrice(){
        ShoppingCartOutDTO shoppingCartOutDTO = new ShoppingCartOutDTO();

        List<ShoppingCartItemOutDTO> shoppingCartItems = new ArrayList<>();
        ShoppingCartItemOutDTO outDTO = new ShoppingCartItemOutDTO();
        outDTO.setSkuCode("1234");
        List<ShoppingCartItemActivityOutDTO> shoppingCartItemActivitys = new ArrayList<>();
        ShoppingCartItemActivityOutDTO itemActivityOutDTO = new ShoppingCartItemActivityOutDTO();
        itemActivityOutDTO.setOpsType(OpsTypeEnum.OPS_108.code());
        itemActivityOutDTO.setActivityCode("111");

        shoppingCartItemActivitys.add(itemActivityOutDTO);

        outDTO.setShoppingCartItemActivitys(shoppingCartItemActivitys);
        shoppingCartItems.add(outDTO);

        List<ProductSkuDetailDTO> productSkuDetailDTOS = new ArrayList<>();
        ProductSkuDetailDTO detailDTO1 = new ProductSkuDetailDTO();
        detailDTO1.setProductCode("222");
        detailDTO1.setSkuCode("123");
        detailDTO1.setActivityCode("111");

        detailDTO1.setPromoPrice(BigDecimal.TEN);

        productSkuDetailDTOS.add(detailDTO1);


        ProductSkuDetailDTO detailDTO = new ProductSkuDetailDTO();
        detailDTO.setProductCode("222");
        detailDTO.setSkuCode("1234");
        detailDTO.setActivityCode("111");

        detailDTO.setPromoPrice(BigDecimal.TEN);

        productSkuDetailDTOS.add(detailDTO);

        shoppingCartOutDTO.setShoppingCartItems(shoppingCartItems);


        when(productDetailService.queryOneGroupByActivityCodes(any(),any())).thenReturn(productSkuDetailDTOS);


        calcExecuter.singleItemDifferentPrice("1",shoppingCartOutDTO);
    }

    @Test
    public void singleItemDifferentPrice_sku_null(){
        ShoppingCartOutDTO shoppingCartOutDTO = new ShoppingCartOutDTO();

        List<ShoppingCartItemOutDTO> shoppingCartItems = new ArrayList<>();
        ShoppingCartItemOutDTO outDTO = new ShoppingCartItemOutDTO();
        outDTO.setSkuCode("1234");
        List<ShoppingCartItemActivityOutDTO> shoppingCartItemActivitys = new ArrayList<>();
        ShoppingCartItemActivityOutDTO itemActivityOutDTO = new ShoppingCartItemActivityOutDTO();
        itemActivityOutDTO.setOpsType(OpsTypeEnum.OPS_108.code());
        itemActivityOutDTO.setActivityCode("111");

        shoppingCartItemActivitys.add(itemActivityOutDTO);

        outDTO.setShoppingCartItemActivitys(shoppingCartItemActivitys);
        shoppingCartItems.add(outDTO);

        List<ProductSkuDetailDTO> productSkuDetailDTOS = new ArrayList<>();
        ProductSkuDetailDTO detailDTO1 = new ProductSkuDetailDTO();
        detailDTO1.setProductCode("222");
        detailDTO1.setSkuCode("");
        detailDTO1.setActivityCode("111");

        detailDTO1.setPromoPrice(BigDecimal.TEN);

        productSkuDetailDTOS.add(detailDTO1);


        ProductSkuDetailDTO detailDTO = new ProductSkuDetailDTO();
        detailDTO.setProductCode("222");
        detailDTO.setSkuCode("1234");
        detailDTO.setActivityCode("111");

        detailDTO.setPromoPrice(BigDecimal.TEN);

        productSkuDetailDTOS.add(detailDTO);

        shoppingCartOutDTO.setShoppingCartItems(shoppingCartItems);


        when(productDetailService.queryOneGroupByActivityCodes(any(),any())).thenReturn(productSkuDetailDTOS);


        calcExecuter.singleItemDifferentPrice("1",shoppingCartOutDTO);
    }

    @Test
    public void handlerShoppingCartItem() {
        Map<String, Set<String>> exclusionMap = new HashMap<>();
        ShoppingCartOutDTO shoppingCartOutDTO = new ShoppingCartOutDTO();
        shoppingCartOutDTO.setActivityCode("test");

        CalcActivity calcActivity = new CalcActivity(new RedisOpsHelper(), "1234");
        exclusionMap.put("dd", new HashSet<>());
        shoppingCartOutDTO.setCalcActivity(calcActivity);
        calcExecuter.handlerShoppingCartItem(exclusionMap, shoppingCartOutDTO, calcActivity);


        List<CalcShoppingCartItem> calcShoppingCartItemList = new ArrayList<>();
        CalcShoppingCartItem calcShoppingCartItem = new CalcShoppingCartItem();
        calcShoppingCartItemList.add(calcShoppingCartItem);
        CalcShoppingCart calcShoppingCart = new CalcShoppingCart();
        calcShoppingCart.setCalcShoppingCartItemList(calcShoppingCartItemList);
        calcActivity.setCalcShoppingCart(calcShoppingCart);
        try {
            calcExecuter.handlerShoppingCartItem(exclusionMap, shoppingCartOutDTO, calcActivity);
        } catch (Exception e) {

        }
    }


    @Test(expected = NullPointerException.class)
    public void handlerShoppingCartItem_selectProduct() {
        Map<String, Set<String>> exclusionMap = new HashMap<>();
        ShoppingCartOutDTO shoppingCartOutDTO = new ShoppingCartOutDTO();
        shoppingCartOutDTO.setActivityCode("test");

        CalcActivity calcActivity = new CalcActivity(new RedisOpsHelper(), "1234");
        exclusionMap.put("dd", new HashSet<>());
        shoppingCartOutDTO.setCalcActivity(calcActivity);
        calcExecuter.handlerShoppingCartItem(exclusionMap, shoppingCartOutDTO, calcActivity);


        List<CalcShoppingCartItem> calcShoppingCartItemList = new ArrayList<>();
        CalcShoppingCartItem calcShoppingCartItem = new CalcShoppingCartItem();
        calcShoppingCartItemList.add(calcShoppingCartItem);
        CalcShoppingCart calcShoppingCart = new CalcShoppingCart();
        calcShoppingCart.setCalcShoppingCartItemList(calcShoppingCartItemList);
        calcActivity.setCalcShoppingCart(calcShoppingCart);
        List<ShoppingCartItemOutDTO> shoppingCartItemOutDTOS = calcExecuter.handlerShoppingCartItem(exclusionMap, shoppingCartOutDTO, calcActivity);
        Assert.assertEquals(0,shoppingCartItemOutDTOS.size());

    }


    @Test
    public void havePriceCondition() {

        CalcShoppingCartItem cscItem = new CalcShoppingCartItem();
        cscItem.setPromoAmount(new BigDecimal("100"));
        cscItem.setProductPrice(new BigDecimal("100"));
        cscItem.setProductListPrice(new BigDecimal("100"));
        ShoppingCartActivity activity = new ShoppingCartActivity();
        ActivityModel activityModel = new ActivityModel();
        activityModel.setPriceCondition("1");
        activity.setActivityModel(activityModel);
        calcExecuter.priceCondition(cscItem, activity);
    }

    @Test
    public void haveNotPriceCondition() {

        CalcShoppingCartItem cscItem = new CalcShoppingCartItem();
        cscItem.setPromoAmount(new BigDecimal("100"));
        cscItem.setProductPrice(new BigDecimal("100"));
        cscItem.setProductListPrice(new BigDecimal("100"));
        ShoppingCartActivity activity = new ShoppingCartActivity();
        ActivityModel activityModel = new ActivityModel();
        activityModel.setPriceCondition("0");
        activity.setActivityModel(activityModel);
        calcExecuter.priceCondition(cscItem, activity);
    }




    private void setupTestData() {
        ShoppingCartOutDTO sc1 = new ShoppingCartOutDTO();
        sc1.setActivityCode("activityCode1");
        ShoppingCartItemOutDTO item1 = new ShoppingCartItemOutDTO();
        item1.setSkuCode("skuId1");
        ShoppingCartItemActivityOutDTO shoppingCartActivity = new ShoppingCartItemActivityOutDTO();
        shoppingCartActivity.setActivityCode("activityCode1");
        item1.setShoppingCartItemActivitys(Arrays.asList(shoppingCartActivity));
        sc1.setShoppingCartItems(Arrays.asList(item1));


        ShoppingCartOutDTO sc2 = new ShoppingCartOutDTO();
        sc2.setActivityCode("activityCode2");
        ShoppingCartItemOutDTO item2 = new ShoppingCartItemOutDTO();
        item2.setSkuCode("skuId2");
        ShoppingCartItemActivityOutDTO shoppingCartActivity2 = new ShoppingCartItemActivityOutDTO();
        shoppingCartActivity.setActivityCode("activityCode2");
        item2.setShoppingCartItemActivitys(Arrays.asList(shoppingCartActivity2));
        sc2.setShoppingCartItems(Arrays.asList(item2));

        shoppingCartOutDTOs.add(sc1);
        shoppingCartOutDTOs.add(sc2);
    }

    @Test
    public void testDeleteOppositeItemWithExclusion() {
        Map<String, Set<String>> exclusionMap = new HashMap<>();
        exclusionMap.put("activityCode1", new HashSet<String>() {{ add("skuId2"); }});
        exclusionMap.put("activityCode2", new HashSet<String>() {{ add("skuId1"); }});

        calcExecuter.deleteOppositeItem(exclusionMap, shoppingCartOutDTOs);


    }

    @Test
    public void testDeleteOppositeItemWithEmptyLists() {
        calcExecuter.deleteOppositeItem(new HashMap<>(), new ArrayList<>());

    }

    @Test
    public void testDeleteOppositeItemWithNullData() {
        calcExecuter.deleteOppositeItem(null, null);

    }
}
