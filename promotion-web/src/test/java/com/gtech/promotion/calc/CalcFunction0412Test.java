package com.gtech.promotion.calc;

import com.gtech.promotion.calc.function.CalcFunction0412;
import com.gtech.promotion.calc.function.FunctionEnum;
import com.gtech.promotion.calc.function.FunctionParamMap;
import com.gtech.promotion.calc.model.CalcActivityFuncParam;
import com.gtech.promotion.calc.model.CalcActivityIncentive;
import com.gtech.promotion.calc.model.CalcShoppingCart;
import com.gtech.promotion.code.activity.SeqNumEnum;
import com.gtech.promotion.component.activity.ActivityPriceComponentDomain;
import com.gtech.promotion.helper.RedisOpsHelper;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.math.BigDecimal;
import java.util.*;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class CalcFunction0412Test {

    @InjectMocks
    private CalcFunction0412 calcFunction0412;

    @Mock
    private RedisOpsHelper redisOpsHelper;

    @Mock
    private ActivityPriceComponentDomain activityPriceComponentDomain;
    @Test
    public void calc_exception(){
        CalcShoppingCart calcShoppingCart = new CalcShoppingCart();
        CalcActivityIncentive calcActivityIncentive = new CalcActivityIncentive(new CalcActivity(redisOpsHelper, ""), "", "");
        Deque<FunctionEnum> functionEnums = new ArrayDeque<>();
        FunctionParamMap functionParamMap = new FunctionParamMap(new ArrayList<>());
        CalcFunction calcFunction = calcFunction0412;
//        when(activityPriceComponentDomain.getTenantPrecision(any())).thenReturn(2);
        CalcResult calc = calcFunction0412.calc(calcShoppingCart, calcActivityIncentive, functionEnums, functionParamMap, calcFunction);
        Assert.assertEquals(CalcResult.ERROR_DEFAULT.getResultCode(), calc.getResultCode());
    }
    @Test
    public void calc_no_b(){
        CalcShoppingCart calcShoppingCart = new CalcShoppingCart();
        CalcActivity calcActivity = new CalcActivity(redisOpsHelper, "");
        calcActivity.setSeqNums(new HashSet<>());
        calcShoppingCart.setCalcActivity(calcActivity);
        CalcActivityIncentive calcActivityIncentive = new CalcActivityIncentive(calcActivity, "", "");
        Deque<FunctionEnum> functionEnums = new ArrayDeque<>();
        FunctionParamMap functionParamMap = new FunctionParamMap(new ArrayList<>());
        CalcFunction calcFunction = calcFunction0412;
//        when(activityPriceComponentDomain.getTenantPrecision(any())).thenReturn(2);
        CalcResult calc = calcFunction0412.calc(calcShoppingCart, calcActivityIncentive, functionEnums, functionParamMap, calcFunction);
        Assert.assertEquals(CalcResult.ERROR_DEFAULT.getResultCode(), calc.getResultCode());
    }

    @Test
    public void calc(){
        CalcShoppingCart calcShoppingCart = new CalcShoppingCart();
        calcShoppingCart.setPostage(BigDecimal.ZERO);
        calcShoppingCart.setCalcShoppingCartItemList(new ArrayList<>());
        CalcActivity calcActivity = new CalcActivity(redisOpsHelper, "");
        Set<String> strings = new HashSet<>();
        strings.add(SeqNumEnum.A.code());
        strings.add(SeqNumEnum.B.code());
        calcActivity.setSeqNums(strings);
        calcShoppingCart.setCalcActivity(calcActivity);

        CalcActivityIncentive calcActivityIncentive = new CalcActivityIncentive(calcActivity, "", "");
        calcActivityIncentive.setIncentiveTimes(1);
        Deque<FunctionEnum> functionEnums = new ArrayDeque<>();
        ArrayList<CalcActivityFuncParam> funcParamList = new ArrayList<>();
        CalcActivityFuncParam e = new CalcActivityFuncParam();
        e.setFuncCode(calcFunction0412.getFunction().code());
        e.setParamValue("1");
        funcParamList.add(e);
        FunctionParamMap functionParamMap = new FunctionParamMap(funcParamList);
        CalcFunction calcFunction = calcFunction0412;
//        when(activityPriceComponentDomain.getTenantPrecision(any())).thenReturn(2);
        CalcResult calc = calcFunction0412.calc(calcShoppingCart, calcActivityIncentive, functionEnums, functionParamMap, calcFunction);
//        Assert.assertEquals(CalcResult.SUCCESS_TRUE.getResultCode(), calc.getResultCode());
    }
}
