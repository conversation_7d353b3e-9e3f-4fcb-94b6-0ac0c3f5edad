package com.gtech.promotion.callable;

import com.gtech.promotion.calc.CalcExecuter;
import com.gtech.promotion.component.activity.ShoppingCartDomain;
import com.gtech.promotion.dto.cache.ActivityCacheDTO;
import com.gtech.promotion.dto.in.activity.ShoppingCartDTO;
import com.gtech.promotion.dto.out.activity.ShoppingCartOutDTO;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyBoolean;

@RunWith(MockitoJUnitRunner.class)
public class CouponFilterCallableTest {
    @Mock
    private ShoppingCartDomain shoppingCartDomain;
    @Mock
    private CalcExecuter calcExecuter;

    @Test
    public void filterCouponsByShoppingCartTest(){
        Map<String, ActivityCacheDTO> activityCacheMap = new HashMap<>();
        ShoppingCartDTO shoppingCart = new ShoppingCartDTO();
        CouponFilterCallable couponFilterCallable = new CouponFilterCallable(shoppingCartDomain,activityCacheMap,shoppingCart,calcExecuter);
        try {
            couponFilterCallable.call();
        }catch (Exception e){

        }

        ShoppingCartDTO shoppingCartDTO = new ShoppingCartDTO();
        shoppingCartDTO.setCouponCodes("123,222");
        Mockito.when(shoppingCartDomain.queryActivity(any(), any(),anyBoolean())).thenReturn(shoppingCartDTO);
        List<ShoppingCartOutDTO> shoppingCartOutDTOS = new ArrayList<>();
        ShoppingCartOutDTO shoppingCartOutDTO = new ShoppingCartOutDTO();
        shoppingCartOutDTO.setActivityCode("test");
        shoppingCartOutDTO.setActivityLabel("test");
        shoppingCartOutDTO.setActivityName("test");
        shoppingCartOutDTO.setCouponCode("123");
        shoppingCartOutDTOS.add(shoppingCartOutDTO);
        Mockito.when(calcExecuter.calc(any(), any())).thenReturn(shoppingCartOutDTOS);

        couponFilterCallable.filterCouponsByShoppingCart();

    }
}
