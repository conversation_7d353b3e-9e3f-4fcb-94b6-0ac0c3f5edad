package com.gtech.promotion.controller;

import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.gtech.commons.page.PageData;
import com.gtech.commons.result.PageResult;
import com.gtech.commons.result.Result;
import com.gtech.promotion.component.activity.ActivityComponentDomain;
import com.gtech.promotion.component.coupon.CouponActivityComponent;
import com.gtech.promotion.component.coupon.CouponInnerCodeComponent;
import com.gtech.promotion.component.coupon.FilterCouponComponent;
import com.gtech.promotion.component.feign.BasicClientDomain;
import com.gtech.promotion.controller.coupon.CouponInnerCodeController;
import com.gtech.promotion.dao.model.coupon.TPromoCouponCodeUserVO;
import com.gtech.promotion.dao.model.coupon.TPromoCouponInnerCodeVO;
import com.gtech.promotion.dto.in.activity.CouponDataInDTO;
import com.gtech.promotion.dto.in.activity.CouponQuantityDTO;
import com.gtech.promotion.dto.in.activity.FrozenCouponCodeInDTO;
import com.gtech.promotion.dto.in.activity.ManagementDataInDTO;
import com.gtech.promotion.dto.in.coupon.TCouponListQueryDTO;
import com.gtech.promotion.dto.out.coupon.CouponActivityListOutDTO;
import com.gtech.promotion.dto.out.coupon.CouponInfoQueryOutDTO;
import com.gtech.promotion.dto.out.coupon.CouponReleaseOutDto;
import com.gtech.promotion.dto.out.coupon.ManagementDataOutDTO;
import com.gtech.promotion.exception.PromotionException;
import com.gtech.promotion.service.coupon.PromoCouponInnerCodeService;
import com.gtech.promotion.service.coupon.PromoCouponReleaseService;
import com.gtech.promotion.utils.DateUtil;
import com.gtech.promotion.vo.param.activity.GetCouponActivityParam;
import com.gtech.promotion.vo.param.activity.GetCouponReleaseParam;
import com.gtech.promotion.vo.param.activity.UpdateCouponParam;
import com.gtech.promotion.vo.param.coupon.ExportCouponRelationParam;
import com.gtech.promotion.vo.result.coupon.ExportCouponRelationResult;
import org.apache.commons.lang.time.DateUtils;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.mock.web.MockHttpServletRequest;
import org.springframework.mock.web.MockHttpServletResponse;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.*;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class CouponInnerCodeControllerTest {

    @InjectMocks
    private CouponInnerCodeController couponInnerCodeController;
    @Mock
    private CouponInnerCodeComponent couponInnerCodeDomain;
    @Mock
    private FilterCouponComponent filterCouponDomain;
    @Mock
    private BasicClientDomain basicClientDomain;

    @Mock
    private CouponActivityComponent couponActivityDomain;

    @Mock
    private PromoCouponInnerCodeService promoCouponInnerCodeService;

    @Mock
    private PromoCouponReleaseService promoCouponReleaseService;


    @Mock
    private ActivityComponentDomain activityComponentDomain;

    private static final String COUPON_STATUS = "couponStatus";

    private static final String COUPON_FROZEN_STATUS = "couponFrozenStatus";

    private static final String COUPON_TYPE = "couponType";

    private static final String COUPON_TAKE_LABEL = "couponTakeLabel";

    @Test
    public void queryCouponListInfo() {
        TCouponListQueryDTO param = new TCouponListQueryDTO();
        param.setActivityCode("666888");
        param.setTenantCode("1");
        param.setPageNo(1);
        param.setPageCount(10);
        PageInfo<CouponInfoQueryOutDTO> pageList = new PageInfo<>();
        when(couponInnerCodeDomain.queryCouponListInfo(any(), any())).thenReturn(pageList);
        PageResult<CouponInfoQueryOutDTO> resultPageResult = couponInnerCodeController.queryCouponListInfo(param);
        Assert.assertNotNull(resultPageResult);
    }

    @Test(expected = PromotionException.class)
    public void queryCouponListInfo_null() {
        PageResult<CouponInfoQueryOutDTO> resultPageResult = couponInnerCodeController.queryCouponListInfo(null);
        Assert.assertNotNull(resultPageResult);
    }

    @Test
    public void exportManagementCouponCodeData() throws IOException {
        String startTime = "20210425000000";
        Date date = DateUtils.addDays(new Date(), 1);
        String dateString = DateUtil.getDateString(date, DateUtil.DATETIMESTOREFORMAT);
        ManagementDataInDTO param = new ManagementDataInDTO();
        param.setReceiveStartTime(startTime);
        param.setReceiveEndTime(dateString);
        param.setReleaseEndTime(dateString);
        param.setReleaseStartTime(startTime);
        param.setUsedStartTime(startTime);
        param.setUsedEndTime(dateString);
        param.setTenantCode("11");
        HttpServletResponse response = new MockHttpServletResponse();
        HttpServletRequest request = new MockHttpServletRequest();
        Map<String, String> hashMap = new HashMap<>();
        hashMap.put(COUPON_STATUS, "01");
        hashMap.put(COUPON_FROZEN_STATUS, "01");
        hashMap.put(COUPON_TYPE, "01");
        hashMap.put(COUPON_TAKE_LABEL, "01");
        when(basicClientDomain.getLanguages(any())).thenReturn(hashMap);
        couponInnerCodeController.exportManagementCouponCodeData(param, response, request);

    }

    @Test(expected = PromotionException.class)
    public void exportManagementCouponCodeData_1() {
        String startTime = "20210425000000";
        Date date = DateUtils.addDays(new Date(), 1);
        String dateString = DateUtil.getDateString(date, DateUtil.DATETIMESTOREFORMAT);
        ManagementDataInDTO param = new ManagementDataInDTO();
        param.setReceiveStartTime(startTime);
        param.setReceiveEndTime(dateString);
        param.setReleaseEndTime(dateString);
        param.setReleaseStartTime(startTime);
        param.setUsedStartTime(startTime);
        param.setUsedEndTime(dateString);
        param.setTenantCode("11");
        HttpServletResponse response = new MockHttpServletResponse();
        HttpServletRequest request = new MockHttpServletRequest();
        Map<String, String> hashMap = new HashMap<>();
        hashMap.put(COUPON_STATUS, "01");
        hashMap.put(COUPON_FROZEN_STATUS, "01");
        hashMap.put(COUPON_TYPE, "01");
        hashMap.put(COUPON_TAKE_LABEL, "01");
        when(basicClientDomain.getLanguages(any())).thenReturn(hashMap);
        try {
            Mockito.doThrow(new PromotionException("TEST")).when(couponInnerCodeDomain).exportManagementCodeInfos(Mockito.any(), Mockito.any(), Mockito.anyMap());
        } catch (IOException e) {
        }

        couponInnerCodeController.exportManagementCouponCodeData(param, response, request);

    }

    @Test
    public void exportManagementCouponCodeDataException() {
        String startTime = "20210425000000";
        Date date = DateUtils.addDays(new Date(), 1);
        String dateString = DateUtil.getDateString(date, DateUtil.DATETIMESTOREFORMAT);
        ManagementDataInDTO param = new ManagementDataInDTO();
        param.setReceiveStartTime(startTime);
        param.setReceiveEndTime(dateString);
        param.setReleaseEndTime(dateString);
        param.setReleaseStartTime(startTime);
        param.setUsedStartTime(startTime);
        param.setUsedEndTime(dateString);
        param.setTenantCode("11");
        HttpServletRequest request = new MockHttpServletRequest();
        HttpServletResponse response = new MockHttpServletResponse();
        couponInnerCodeController.exportManagementCouponCodeData(param, response, request);

    }

    @Test
    public void managementData() {
        String startTime = "20210425000000";
        Date date = DateUtils.addDays(new Date(), 1);
        String dateString = DateUtil.getDateString(date, DateUtil.DATETIMESTOREFORMAT);
        ManagementDataInDTO param = new ManagementDataInDTO();
        param.setReceiveStartTime(startTime);
        param.setReceiveEndTime(dateString);
        param.setReleaseEndTime(dateString);
        param.setReleaseStartTime(startTime);
        param.setUsedStartTime(startTime);
        param.setUsedEndTime(dateString);
        param.setTenantCode("11");
        param.setStatus("01");
        param.setCouponType("01");
        PageData<ManagementDataOutDTO> pageData = new PageData<>();
        List<ManagementDataOutDTO> list = new ArrayList<>();
        ManagementDataOutDTO dataOutDTO = new ManagementDataOutDTO();
        list.add(dataOutDTO);
        pageData.setList(list);
        pageData.setTotal(1L);
        when(couponInnerCodeDomain.getPageInfo(any())).thenReturn(pageData);
        PageResult<ManagementDataOutDTO> result = couponInnerCodeController.managementData(param);
        Assert.assertNotNull(result);
    }

    @Test
    public void frozenCouponCode() {
        FrozenCouponCodeInDTO param = new FrozenCouponCodeInDTO();
        param.setCouponCode("11");
        param.setFrozenStatus(2);
        param.setTenantCode("1");
        param.setLogicDelete(0);
        doNothing().when(filterCouponDomain).frozenCouponCode(any());
        Result<Object> result = couponInnerCodeController.frozenCouponCode(param);
        Assert.assertNotNull(result);
    }

    @Test(expected = Exception.class)
    public void frozenCouponCode_Exception() {
        FrozenCouponCodeInDTO param = null;
        Result<Object> result = couponInnerCodeController.frozenCouponCode(param);
        Assert.assertNotNull(result);
    }

    @Test(expected = Exception.class)
    public void managementPromoCouponCodeData() {
        CouponDataInDTO param = new CouponDataInDTO();
        String startTime = "20210425000000";
        Date date = DateUtils.addDays(new Date(), 1);
        String dateString = DateUtil.getDateString(date, DateUtil.DATETIMESTOREFORMAT);
        param.setReceiveStartTime(startTime);
        param.setReceiveEndTime(dateString);
        param.setReleaseEndTime(dateString);
        param.setReleaseStartTime(startTime);
        param.setUsedStartTime(startTime);
        param.setUsedEndTime(dateString);
        param.setTenantCode("11");
        param.setIsUseStatus("2");
        PageResult<ManagementDataOutDTO> result = couponInnerCodeController.managementPromoCouponCodeData(param);
        Assert.assertNotNull(result);
    }


    @Test(expected = Exception.class)
    public void managementData_exception_1() {
        String startTime = "20210425000000";
        ManagementDataInDTO param = new ManagementDataInDTO();
        param.setTenantCode("11");
        param.setReceiveStartTime(startTime);
        PageResult<ManagementDataOutDTO> result = couponInnerCodeController.managementData(param);
        Assert.assertNotNull(result);
    }

    @Test(expected = Exception.class)
    public void managementData_exception_2() {
        Date date = DateUtils.addDays(new Date(), 1);
        String dateString = DateUtil.getDateString(date, DateUtil.DATETIMESTOREFORMAT);
        ManagementDataInDTO param = new ManagementDataInDTO();
        param.setTenantCode("11");
        param.setReleaseEndTime(dateString);
        PageResult<ManagementDataOutDTO> result = couponInnerCodeController.managementData(param);
        Assert.assertNotNull(result);
    }

    @Test(expected = Exception.class)
    public void managementData_exception_3() {
        Date date = DateUtils.addDays(new Date(), 1);
        String dateString = DateUtil.getDateString(date, DateUtil.DATETIMESTOREFORMAT);
        ManagementDataInDTO param = new ManagementDataInDTO();
        param.setTenantCode("11");
        param.setUsedEndTime(dateString);
        PageResult<ManagementDataOutDTO> result = couponInnerCodeController.managementData(param);
        Assert.assertNotNull(result);
    }


    @Test(expected = PromotionException.class)
    public void test_null() {
        //given
        couponInnerCodeController.exportFile(null, null, null);
    }

    @Test
    public void findActivityByCouponCode() {
        GetCouponActivityParam param = new GetCouponActivityParam();
        param.setTenantCode("11");
        param.setDomainCode("11");
        param.setCouponCode("1");
        PageInfo<CouponActivityListOutDTO> pageInfo = new PageInfo<>();
        List<CouponActivityListOutDTO> activityListOutDTOS = new ArrayList<>();
        pageInfo.setList(activityListOutDTOS);

        List<TPromoCouponInnerCodeVO> vos = new ArrayList<>();
        TPromoCouponInnerCodeVO vo = new TPromoCouponInnerCodeVO();
        vo.setActivityCode("111");
        vo.setCouponCode("1");
        vo.setReleaseCode("1");
        vos.add(vo);

        when(couponInnerCodeDomain.findActivityCodeByCouponCode(Mockito.anyString(), Mockito.anyString())).thenReturn(vos);
        when(couponActivityDomain.queryCouponActivityList(any())).thenReturn(pageInfo);
        PageResult<CouponActivityListOutDTO> result = couponInnerCodeController.findActivityByCouponCode(param);
        Assert.assertEquals(0, result.getData().getList().size());
    }


    @Test
    public void findActivityByCouponCode_user() {
        GetCouponActivityParam param = new GetCouponActivityParam();
        param.setTenantCode("11");
        param.setDomainCode("11");
        param.setCouponCode("1");
        PageInfo<CouponActivityListOutDTO> pageInfo = new PageInfo<>();
        List<CouponActivityListOutDTO> activityListOutDTOS = new ArrayList<>();

        CouponActivityListOutDTO dto = new CouponActivityListOutDTO();
        dto.setCouponCode("111");
        activityListOutDTOS.add(dto);
        pageInfo.setList(activityListOutDTOS);

        List<TPromoCouponInnerCodeVO> vos = new ArrayList<>();
        TPromoCouponInnerCodeVO vo = new TPromoCouponInnerCodeVO();
        vo.setCouponCode("111");
        vo.setActivityCode("111");
        vo.setReleaseCode("1");
        vos.add(vo);

        List<TPromoCouponCodeUserVO> userVOS = new ArrayList<>();
        TPromoCouponCodeUserVO userVO = new TPromoCouponCodeUserVO();
        userVO.setCouponCode("111");
        userVOS.add(userVO);

        when(couponInnerCodeDomain.findActivityCodeByCouponCode(Mockito.anyString(), Mockito.anyString())).thenReturn(vos);

        when(couponInnerCodeDomain.queryActivityByCouponCodes(Mockito.anyString(), Mockito.anyList())).thenReturn(userVOS);

        when(couponActivityDomain.queryCouponActivityList(any())).thenReturn(pageInfo);
        PageResult<CouponActivityListOutDTO> result = couponInnerCodeController.findActivityByCouponCode(param);
        Assert.assertEquals(1, result.getData().getList().size());
    }

    @Test
    public void findReleaseByCouponCode() {
        GetCouponReleaseParam param = new GetCouponReleaseParam();
        param.setTenantCode("11");
        param.setDomainCode("11");
        param.setCouponCodeList(Lists.newArrayList("1"));
        Result<List<CouponReleaseOutDto>> result = couponInnerCodeController.findReleaseByCouponCode(param);
        Assert.assertTrue(result.isSuccess());
    }


    @Test
    public void updateCouponByCode() {
        UpdateCouponParam param = new UpdateCouponParam();
        param.setTenantCode("11");
        param.setDomainCode("11");
        Result<String> stringResult = couponInnerCodeController.updateCouponByCode(param);
        Assert.assertTrue(stringResult.isSuccess());
    }


    @Test
    public void exportCouponRelationInfo(){

        ExportCouponRelationParam param = new ExportCouponRelationParam();
        param.setTenantCode("11");
        param.setDomainCode("11");
        param.setActivityCode("11");
        param.setReleaseCode("!1");
        param.setMaxCouponCode("!11");
        Result<List<ExportCouponRelationResult>> listResult = couponInnerCodeController.exportCouponRelationInfo(param);

        Assert.assertEquals(0,listResult.getData().size());
    }

    @Test
    public void findActivityByCouponCode_null() {
        GetCouponActivityParam param = new GetCouponActivityParam();
        param.setTenantCode("11");
        param.setDomainCode("11");
        param.setCouponCode("1");
        when(couponInnerCodeDomain.findActivityCodeByCouponCode(Mockito.anyString(), Mockito.anyString())).thenReturn(null);
        PageResult<CouponActivityListOutDTO> result = couponInnerCodeController.findActivityByCouponCode(param);
        Assert.assertEquals(0, result.getData().getList().size());
    }


    @Test
    public void getCouponQuantity() {
        CouponQuantityDTO couponQuantityDTO = new CouponQuantityDTO();
        try {
            couponInnerCodeController.getCouponQuantity(couponQuantityDTO);
        } catch (Exception e) {

        }
        couponQuantityDTO.setTenantCode("100001");
        try {
            couponInnerCodeController.getCouponQuantity(couponQuantityDTO);
        } catch (Exception e) {

        }
        couponQuantityDTO.setActivityCode("test");
        try {
            couponInnerCodeController.getCouponQuantity(couponQuantityDTO);
        } catch (Exception e) {

        }
        couponQuantityDTO.setReleaseCode("test");
        try {
            couponInnerCodeController.getCouponQuantity(couponQuantityDTO);
        } catch (Exception e) {

        }
        couponQuantityDTO.setStatus("02");
        try {
            couponInnerCodeController.getCouponQuantity(couponQuantityDTO);
        } catch (Exception e) {

        }
        when(promoCouponInnerCodeService.getCouponQuantity(any())).thenReturn(1);
        couponQuantityDTO.setFrozenStatus("02");
        try {
            couponInnerCodeController.getCouponQuantity(couponQuantityDTO);
        } catch (Exception e) {

        }
    }
}
