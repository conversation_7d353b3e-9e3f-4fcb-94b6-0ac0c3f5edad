package com.gtech.promotion.controller.purchaseconstraint;

import com.google.common.collect.Lists;
import com.gtech.commons.result.Result;
import com.gtech.promotion.component.purchaseconstraint.PurchaseConstraintComponent;
import com.gtech.promotion.vo.bean.ProductAttribute;
import com.gtech.promotion.vo.param.purchaseconstraint.CalAvailableQtyByProductListParam;
import com.gtech.promotion.vo.param.purchaseconstraint.CalAvailableQtyParam;
import com.gtech.promotion.vo.param.purchaseconstraint.CalAvailableQtyProductParam;
import com.gtech.promotion.vo.param.purchaseconstraint.CheckPurchaseConstraintParam;
import com.gtech.promotion.vo.result.purchaseconstraint.CalAvailableQtyByProductListResult;
import com.gtech.promotion.vo.result.purchaseconstraint.CalAvailableQtyResult;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RunWith(MockitoJUnitRunner.class)
public class MallPurchaseConstraintControllerTest {

    @InjectMocks
    private MallPurchaseConstraintController mallPurchaseConstraintController;

    @Mock
    private PurchaseConstraintComponent pcComponent;
    @Mock
    private PurchaseConstraintComponent purchaseConstraintComponent;

    @Test
    public void checkPurchaseConstraint(){
        CheckPurchaseConstraintParam param = CheckPurchaseConstraintParam.builder()
                                                .channelCode("11")
                                                .domainCode("1111")
                                                .memberCode("111")
                                                .orgCode("111")
                                                .tenantCode("112")
                                                .touristFlag(0)
                                                .qualifications(new HashMap<>())
                                                .checkPurchaseConstraintProducts(new ArrayList<>())
                                                .build();

        Result result = mallPurchaseConstraintController.checkPurchaseConstraint(param);
        Assert.assertTrue(result.isSuccess());
    }

    @Test
    public void calAvailableQty(){
        CalAvailableQtyParam param = new CalAvailableQtyParam();
        param.setDomainCode("1111");
        param.setTenantCode("22222");
        param.setMemberCode("11111");
        param.setOrgCode("1111");

        CalAvailableQtyProductParam calAvailableQtyProduct = new CalAvailableQtyProductParam();
        calAvailableQtyProduct.setProductCode("1234");
        calAvailableQtyProduct.setSkuCode("4567");
        param.setCalAvailableQtyProduct(calAvailableQtyProduct);

        Result<List<CalAvailableQtyResult>> result = mallPurchaseConstraintController.calAvailableQty(param);
        Assert.assertTrue(result.isSuccess());

        Map<String, List<ProductAttribute>> skuAttributeMap = new HashMap<>();
        skuAttributeMap.put(calAvailableQtyProduct.getSkuCode(), new ArrayList<>());
        param.setSkuAttributeMap(skuAttributeMap);
        result = mallPurchaseConstraintController.calAvailableQty(param);
        Assert.assertTrue(result.isSuccess());
    }



    @Test
    public void calAvailableQtyByProductList(){
        CalAvailableQtyByProductListParam param = new CalAvailableQtyByProductListParam();
        param.setDomainCode("1111");
        param.setTenantCode("22222");
        param.setMemberCode("11111");
        param.setOrgCode("1111");

        CalAvailableQtyProductParam calAvailableQtyProduct = new CalAvailableQtyProductParam();
        calAvailableQtyProduct.setProductCode("1234");
        calAvailableQtyProduct.setSkuCode("4567");
        param.setCalAvailableQtyProductList(Lists.newArrayList(calAvailableQtyProduct));
        List<CalAvailableQtyResult> calAvailableQtyResultList = new ArrayList<>();
        CalAvailableQtyResult calAvailableQtyResult = new CalAvailableQtyResult();
        calAvailableQtyResult.setSkuCode("4567");

        calAvailableQtyResultList.add(calAvailableQtyResult);
        Mockito.when(pcComponent.calAvailableQty(Mockito.any())).thenReturn(calAvailableQtyResultList);

        Result<List<CalAvailableQtyByProductListResult>> result = mallPurchaseConstraintController.calAvailableQtyByProductList(param);


    }

}
