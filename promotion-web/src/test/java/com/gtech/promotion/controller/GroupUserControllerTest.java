package com.gtech.promotion.controller;

import com.github.pagehelper.PageInfo;
import com.gtech.commons.result.PageResult;
import com.gtech.commons.result.Result;
import com.gtech.promotion.component.marketing.MarketingGroupComponent;
import com.gtech.promotion.controller.flashsale.GroupUserController;
import com.gtech.promotion.vo.param.marketing.MarketingGroupUserListParam;
import com.gtech.promotion.vo.param.marketing.MarketingGroupUserParam;
import com.gtech.promotion.vo.param.marketing.flashsale.FindGroupUserParam;
import com.gtech.promotion.vo.result.flashsale.MarketingGroupUserListResult;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.List;

import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class GroupUserControllerTest {

    @InjectMocks
    private GroupUserController groupUserController;



    @Mock
    private MarketingGroupComponent marketingGroupComponent;

    @Test
    public void queryGroupUserList(){

        MarketingGroupUserParam param = new MarketingGroupUserParam();
        param.setDomainCode("!");
        param.setTenantCode("!");
        param.setActivityCode("!");

        List<String> groupStatus = new ArrayList<>();
        groupStatus.add("01");
        List<MarketingGroupUserListResult> results = new ArrayList<>();
        MarketingGroupUserListResult result = new MarketingGroupUserListResult();
        results.add(result);
        when(marketingGroupComponent.queryGroupUserList(Mockito.any())).thenReturn(results);

        Result<List<MarketingGroupUserListResult>> listResult = groupUserController.queryGroupUserList(param);
        Assert.assertEquals(1, listResult.getData().size());

    }


    @Test
    public void findUserGroupByMarketingGroupAndUserCode(){

        FindGroupUserParam param = new FindGroupUserParam();
        param.setDomainCode("!");
        param.setTenantCode("!");
        param.setUserCode("!");
        param.setMarketingGroupCode("1");
        MarketingGroupUserListResult result = new MarketingGroupUserListResult();

        when(marketingGroupComponent.findUserGroupByMarketingGroupAndUserCode(Mockito.any())).thenReturn(result);

        Result<MarketingGroupUserListResult> result1 = groupUserController.findUserGroupByMarketingGroupAndUserCode(param);
        Assert.assertNotNull( result1);

    }


    @Test
    public void queryGroupUserList1(){

        MarketingGroupUserParam param = new MarketingGroupUserParam();
        param.setDomainCode("!");
        param.setTenantCode("!");
        param.setActivityCode("!");
        List<String> groupStatus = new ArrayList<>();
        groupStatus.add("04");
        List<MarketingGroupUserListResult> results = new ArrayList<>();
        MarketingGroupUserListResult result = new MarketingGroupUserListResult();
        results.add(result);
        Result<List<MarketingGroupUserListResult>> listResult = groupUserController.queryGroupUserList(param);
        Assert.assertEquals(0, listResult.getData().size());

    }

    @Test
    public void queryAllGroupUserList(){

        MarketingGroupUserListParam param = new MarketingGroupUserListParam();
        param.setDomainCode("!");
        param.setTenantCode("!");
        param.setActivityCode("!");

        List<String> groupStatus = new ArrayList<>();
        groupStatus.add("04");

        PageInfo<MarketingGroupUserListResult> pageInfo = new PageInfo<>();

        List<MarketingGroupUserListResult> results = new ArrayList<>();
        MarketingGroupUserListResult result = new MarketingGroupUserListResult();
        results.add(result);

        pageInfo.setList(results);
        when(marketingGroupComponent.queryAllGroupUserList(Mockito.any())).thenReturn(pageInfo);

        PageResult<MarketingGroupUserListResult> list = groupUserController.queryAllGroupUserList(param);
        Assert.assertEquals(1, list.getData().getList().size());

    }

}
