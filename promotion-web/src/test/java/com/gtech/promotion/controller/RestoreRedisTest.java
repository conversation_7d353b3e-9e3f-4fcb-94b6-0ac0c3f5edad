/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * <PERSON>ECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.controller;

import com.google.common.collect.Lists;
import com.gtech.promotion.BaseTest;
import com.gtech.promotion.dto.in.activity.ActivityParamDTO;
import com.gtech.promotion.dto.in.activity.ActivityParamPageDTO;
import com.gtech.promotion.dto.in.activity.TenantCodeInDTO;
import com.gtech.promotion.dto.in.activity.TenantProductDTO;
import com.gtech.promotion.dto.in.coupon.CouponInfoInDTO;
import com.gtech.promotion.dto.in.coupon.RedisInnerCodeRestore;
import com.gtech.promotion.dto.in.coupon.TCouponListQueryDTO;
import com.gtech.promotion.dto.in.coupon.TCouponQueryOrApplyDTO;
import com.gtech.promotion.vo.bean.ProductAttribute;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;

/**
 */
public class RestoreRedisTest extends BaseTest{

    //查询已经生效的优惠券活动列表
    public TenantCodeInDTO tenantCodeInDTO(){
        TenantCodeInDTO tenantCodeInDTO = new TenantCodeInDTO();
        tenantCodeInDTO.setTenantCode("1234567890");
        return tenantCodeInDTO;
    }

    //查询优惠券活动详情
    public ActivityParamDTO activityParamDTO(){
        ActivityParamDTO activityParamDTO = new ActivityParamDTO();
        activityParamDTO.setTenantCode("1234567890");
        return activityParamDTO;
    }

    //查询券活动详情-对外
    public CouponInfoInDTO couponInfoInDTO(){
        CouponInfoInDTO couponInfoInDTO = new CouponInfoInDTO();
        couponInfoInDTO.setTenantCode("1234567890");
        couponInfoInDTO.setQualifications(new ArrayList<>());
        couponInfoInDTO.setUserCode("");
        return couponInfoInDTO;
    }

    //根据商品查询优惠券活动列表-对外
    public TenantProductDTO searchCouponByProduct(){
        TenantProductDTO productDTO = new TenantProductDTO();
        productDTO.setTenantCode("1234567890");
        productDTO.setCategoryCodes(Arrays.asList("18041800015418"));
        productDTO.setSkuCode("SKUA001");
        productDTO.setBrandCode("1234");
        productDTO.setProductCode("66666666");
        productDTO.setQualifications(new HashMap<>());
        List<ProductAttribute> attributes = Lists.newArrayList();
        ProductAttribute attributelDTO = new ProductAttribute();
        attributelDTO.setAttributeCode("0000");
        attributelDTO.setAttributeValues("0000");
        productDTO.setAttributes(attributes);
        attributes.add(attributelDTO);
        return productDTO;
    }

    //查询投所有放券记录
    public ActivityParamPageDTO release(){
        ActivityParamPageDTO dto = new ActivityParamPageDTO();
        dto.setTenantCode("1234567890");
        return dto;
    }

    //查询券码列表
    public TCouponListQueryDTO queryCouponListInfo(){
        TCouponListQueryDTO dto = new TCouponListQueryDTO();
        dto.setTenantCode("1234567890");
        dto.setCouponStatus("");
        dto.setCreateTimeStart("");
        dto.setCreateTimeEnd("");
        dto.setPageNo(1);
        dto.setPageCount(10);
        return dto;
    }

    //查询会员某一张优惠券券码信息-对外
    public TCouponQueryOrApplyDTO couponInfoDTO(){
        TCouponQueryOrApplyDTO couponInfoDTO = new TCouponQueryOrApplyDTO();
        couponInfoDTO.setTenantCode("1234567890");
        couponInfoDTO.setUserCode("123");
        return couponInfoDTO;
    }

    public RedisInnerCodeRestore restoreRedis(){
        RedisInnerCodeRestore restore = new RedisInnerCodeRestore();
        restore.setTenantCode("1234567890");
        return restore;
    }

}
