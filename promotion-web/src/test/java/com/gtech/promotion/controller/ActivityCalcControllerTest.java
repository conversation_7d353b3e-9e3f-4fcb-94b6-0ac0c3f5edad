package com.gtech.promotion.controller;

import com.alibaba.fastjson.JSON;
import com.gtech.commons.utils.BeanCopyUtils;
import com.gtech.promotion.PromotionWebApplication;
import com.gtech.promotion.dto.in.activity.UpdateActivityStatusInDTO;
import com.gtech.promotion.helper.ActivityHelper;
import com.gtech.promotion.helper.BaseControllerTests;
import com.gtech.promotion.vo.bean.ProductAttribute;
import com.gtech.promotion.vo.bean.ShoppingCartItem;
import com.gtech.promotion.vo.bean.ShoppingCartStore;
import com.gtech.promotion.vo.param.activity.CalcShoppingCartParam;
import com.gtech.promotion.vo.param.activity.CreatePromoActivityParam;
import com.gtech.promotion.vo.param.activity.UpdatePromoActivityParam;
import com.gtech.promotion.vo.result.activity.CalcShoppingCartResult;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.junit.Assert;
import org.junit.FixMethodOrder;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.junit.runners.MethodSorters;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = {PromotionWebApplication.class})
@FixMethodOrder(MethodSorters.NAME_ASCENDING)
@ActiveProfiles("dev")
@Transactional
@AutoConfigureMockMvc
public class ActivityCalcControllerTest extends BaseControllerTests {


    public static final String orgCode01 = "orgCode";

    @Test
    public void test_0101_0201_0301_0401_单品无条件减金额() {
        super.setup();

        List<ProductAttribute> attrList = Arrays.asList(
                ProductAttribute.builder().attributeCode("AC001").attributeValues("AV001").build());

        UpdatePromoActivityParam reduceActivity = this.createAcivity(ActivityHelper.buildActivity0102_0202_0302_0401_商品范围满数量减金额());
        this.updateActivityStatus(reduceActivity, "04");

        List<ShoppingCartItem> shoppingCartItems = Arrays.asList(
                ShoppingCartItem.builder()
                        .categoryCodes(Arrays.asList("22"))
                        .brandCode("221")
                        .productCode("PC001")
                        .skuCode("SC001")
                        .attributes(attrList)
                        .quantity(1)
                        .productPrice(new BigDecimal(100))
                        .selectionFlag("01")
                        .build(),
                ShoppingCartItem.builder()
                        .categoryCodes(Arrays.asList("22"))
                        .brandCode("221")
                        .productCode("PC002")
                        .skuCode("SC002")
                        .quantity(1)
                        .productPrice(new BigDecimal(200))
                        .selectionFlag("01")
                        .build());

        CalcShoppingCartParam orgCode1 = buildCalcShoppingCartParam(buildCartStore(orgCode01, "orgCode1", shoppingCartItems));
        this.calcShoppingCart(orgCode1, null);



    }

    @Test
    public void test_0102_0202_0302_0402_商品范围满数量打折扣() {
        super.setup();

        List<ProductAttribute> attrList = Arrays.asList(
                ProductAttribute.builder().attributeCode("AC001").attributeValues("AV001").build());

        UpdatePromoActivityParam reduceActivity = this.createAcivity(ActivityHelper.buildActivity0102_0202_0302_0402_商品范围满数量打折扣());
        this.updateActivityStatus(reduceActivity, "04");

        List<ShoppingCartItem> shoppingCartItems = Arrays.asList(
                ShoppingCartItem.builder()
                        .categoryCodes(Arrays.asList("22"))
                        .brandCode("221")
                        .productCode("PC001")
                        .skuCode("SC001")
                        .attributes(attrList)
                        .quantity(1)
                        .productPrice(new BigDecimal(100))
                        .selectionFlag("01")
                        .build(),
                ShoppingCartItem.builder()
                        .categoryCodes(Arrays.asList("22"))
                        .brandCode("221")
                        .productCode("PC002")
                        .skuCode("SC002")
                        .quantity(1)
                        .productPrice(new BigDecimal(200))
                        .selectionFlag("01")
                        .build());

        CalcShoppingCartParam calcParam = buildCalcShoppingCartParam(buildCartStore(orgCode01, "orgCode1", shoppingCartItems));
        this.calcShoppingCart(calcParam, null);
    }


    @Test
    public void test_0102_0202_0302_0404_商品范围满数量设为总计特价() {
        super.setup();

        List<ProductAttribute> attrList = Arrays.asList(ProductAttribute.builder().attributeCode("12").attributeValues("122").build());

        UpdatePromoActivityParam fixActivity = this.createAcivity(ActivityHelper.buildActivity0102_0202_0302_0404_商品范围满数量设为总计特价());
        this.updateActivityStatus(fixActivity, "04");

        List<ShoppingCartItem> shoppingCartItems = Arrays.asList(
                ShoppingCartItem.builder()
                        .categoryCodes(Arrays.asList("22"))
                        .brandCode("221")
                        .productCode("PC001")
                        .skuCode("SC001")
                        .attributes(attrList)
                        .quantity(2)
                        .productPrice(new BigDecimal(100))
                        .selectionFlag("01")
                        .build(),
                ShoppingCartItem.builder()
                        .categoryCodes(Arrays.asList("22"))
                        .brandCode("221")
                        .productCode("PC002")
                        .skuCode("SC002")
                        .quantity(1)
                        .productPrice(new BigDecimal(200))
                        .selectionFlag("01")
                        .build());

        CalcShoppingCartParam calcParam = buildCalcShoppingCartParam(buildCartStore(orgCode01, "orgCode1", shoppingCartItems));
        this.calcShoppingCart(calcParam, null);
    }

    @Test
    public void test_0103_0202_0302_0405_订单满数量包邮() {
        super.setup();

        List<ProductAttribute> attrList = Arrays.asList(ProductAttribute.builder().attributeCode("12").attributeValues("122").build());

        UpdatePromoActivityParam freeMailActivity = this.createAcivity(ActivityHelper.buildActivity0103_0202_0302_0405_订单满数量包邮());
        this.updateActivityStatus(freeMailActivity, "04");


        List<ShoppingCartItem> shoppingCartItems = Arrays.asList(
                ShoppingCartItem.builder()
                        .categoryCodes(Arrays.asList("22"))
                        .brandCode("221")
                        .productCode("PC001")
                        .skuCode("SC001")
                        .attributes(attrList)
                        .quantity(2)
                        .productPrice(new BigDecimal(100))
                        .selectionFlag("01")
                        .build(),
                ShoppingCartItem.builder()
                        .categoryCodes(Arrays.asList("4546>65465>546545"))
                        .brandCode("221")
                        .productCode("PC002")
                        .skuCode("SC002")
                        .quantity(1)
                        .productPrice(new BigDecimal(200))
                        .selectionFlag("01")
                        .build());

        CalcShoppingCartParam calcParam = buildCalcShoppingCartParam(buildCartStore(orgCode01, "orgCode1", shoppingCartItems));
        this.calcShoppingCart(calcParam, null);
    }



    @Test
    public void test_0102_0203_0302_0407_商品范围每满数量送同类商品() {
        super.setup();

        List<ProductAttribute> attrList = Arrays.asList(ProductAttribute.builder().attributeCode("121").attributeValues("1221").build());

        UpdatePromoActivityParam freeMailActivity = this.createAcivity(ActivityHelper.buildActivity0102_0203_0302_0407_商品范围每满数量送同类商品());
        this.updateActivityStatus(freeMailActivity, "04");


        List<ShoppingCartItem> shoppingCartItems = Arrays.asList(
                ShoppingCartItem.builder()
                        .categoryCodes(Arrays.asList("22"))
                        .brandCode("221")
                        .productCode("PC001")
                        .skuCode("SC001")
                        .attributes(attrList)
                        .quantity(2)
                        .productPrice(new BigDecimal(100))
                        .selectionFlag("01")
                        .build(),
                ShoppingCartItem.builder()
                        .categoryCodes(Arrays.asList("4546>65465>546545"))
                        .brandCode("221")
                        .productCode("PC002")
                        .skuCode("SC002")
                        .quantity(1)
                        .productPrice(new BigDecimal(200))
                        .selectionFlag("01")
                        .build());

        CalcShoppingCartParam calcParam = buildCalcShoppingCartParam(buildCartStore(orgCode01, "orgCode1", shoppingCartItems));
        this.calcShoppingCart(calcParam, null);
    }


    @Test
    public void test_0102_0208_0302_0416_商品范围每数量免费() {
        super.setup();

        List<ProductAttribute> attrList = Arrays.asList(ProductAttribute.builder().attributeCode("121").attributeValues("1221").build());

        UpdatePromoActivityParam freeMailActivity = this.createAcivity(ActivityHelper.buildActivity0102_0208_0302_0416_商品范围每数量免费());
        this.updateActivityStatus(freeMailActivity, "04");


        List<ShoppingCartItem> shoppingCartItems = Arrays.asList(
                ShoppingCartItem.builder()
                        .categoryCodes(Arrays.asList("22"))
                        .brandCode("221")
                        .productCode("PC001")
                        .skuCode("SC001")
                        .attributes(attrList)
                        .quantity(2)
                        .productPrice(new BigDecimal(100))
                        .selectionFlag("01")
                        .build(),
                ShoppingCartItem.builder()
                        .categoryCodes(Arrays.asList("4546>65465>546545"))
                        .brandCode("221")
                        .productCode("PC002")
                        .skuCode("SC002")
                        .quantity(1)
                        .productPrice(new BigDecimal(200))
                        .selectionFlag("01")
                        .build());

        CalcShoppingCartParam calcParam = buildCalcShoppingCartParam(buildCartStore(orgCode01, "orgCode1", shoppingCartItems));
        this.calcShoppingCart(calcParam, null);
    }

    @Test
    public void test_0101_0208_0302_0416_单品每数量免费() {
        super.setup();

        List<ProductAttribute> attrList = Arrays.asList(ProductAttribute.builder().attributeCode("121").attributeValues("1221").build());

        UpdatePromoActivityParam freeMailActivity = this.createAcivity(ActivityHelper.buildActivity0101_0208_0302_0416_单品每数量免费());
        this.updateActivityStatus(freeMailActivity, "04");


        List<ShoppingCartItem> shoppingCartItems = Arrays.asList(
                ShoppingCartItem.builder()
                        .categoryCodes(Arrays.asList("22"))
                        .brandCode("221")
                        .productCode("PC001")
                        .skuCode("SC001")
                        .attributes(attrList)
                        .quantity(2)
                        .productPrice(new BigDecimal(100))
                        .selectionFlag("01")
                        .build(),
                ShoppingCartItem.builder()
                        .categoryCodes(Arrays.asList("4546>65465>546545"))
                        .brandCode("221")
                        .productCode("PC002")
                        .skuCode("SC002")
                        .quantity(1)
                        .productPrice(new BigDecimal(200))
                        .selectionFlag("01")
                        .build());

        CalcShoppingCartParam calcParam = buildCalcShoppingCartParam(buildCartStore(orgCode01, "orgCode1", shoppingCartItems));
        this.calcShoppingCart(calcParam, null);
    }





    @Test
    public void test_0102_0206_0302_0416_商品范围每数量免费() {
        super.setup();

        List<ProductAttribute> attrList = Arrays.asList(ProductAttribute.builder().attributeCode("121").attributeValues("1221").build());

        UpdatePromoActivityParam freeMailActivity = this.createAcivity(ActivityHelper.buildActivity0102_0206_0302_0416_商品范围每数量免费());
        this.updateActivityStatus(freeMailActivity, "04");


        List<ShoppingCartItem> shoppingCartItems = Arrays.asList(
                ShoppingCartItem.builder()
                        .categoryCodes(Arrays.asList("22"))
                        .brandCode("221")
                        .productCode("PC001")
                        .skuCode("SC001")
                        .attributes(attrList)
                        .quantity(2)
                        .productPrice(new BigDecimal(100))
                        .selectionFlag("01")
                        .build(),
                ShoppingCartItem.builder()
                        .categoryCodes(Arrays.asList("4546>65465>546545"))
                        .brandCode("221")
                        .productCode("PC002")
                        .skuCode("SC002")
                        .quantity(1)
                        .productPrice(new BigDecimal(200))
                        .selectionFlag("01")
                        .build());

        CalcShoppingCartParam calcParam = buildCalcShoppingCartParam(buildCartStore(orgCode01, "orgCode1", shoppingCartItems));
        this.calcShoppingCart(calcParam, null);
    }

    @Test
    public void test_0101_0206_0302_0416_单品每数量免费() {
        super.setup();

        List<ProductAttribute> attrList = Arrays.asList(ProductAttribute.builder().attributeCode("121").attributeValues("1221").build());

        UpdatePromoActivityParam freeMailActivity = this.createAcivity(ActivityHelper.buildActivity0101_0206_0302_0416_单品每数量免费());
        this.updateActivityStatus(freeMailActivity, "04");


        List<ShoppingCartItem> shoppingCartItems = Arrays.asList(
                ShoppingCartItem.builder()
                        .categoryCodes(Arrays.asList("22"))
                        .brandCode("221")
                        .productCode("PC001")
                        .skuCode("SC001")
                        .attributes(attrList)
                        .quantity(2)
                        .productPrice(new BigDecimal(100))
                        .selectionFlag("01")
                        .build(),
                ShoppingCartItem.builder()
                        .categoryCodes(Arrays.asList("4546>65465>546545"))
                        .brandCode("221")
                        .productCode("PC002")
                        .skuCode("SC002")
                        .quantity(1)
                        .productPrice(new BigDecimal(200))
                        .selectionFlag("01")
                        .build());

        CalcShoppingCartParam calcParam = buildCalcShoppingCartParam(buildCartStore(orgCode01, "orgCode1", shoppingCartItems));
        this.calcShoppingCart(calcParam, null);
    }

    private List<CalcShoppingCartResult> calcShoppingCart(CalcShoppingCartParam cscParam, String errorCode) {

        TestResult testResult = super.mockMvcPost("/activity/calcShoppingCart", cscParam, errorCode);
        if (StringUtils.isNotBlank(errorCode)) {
            //Assert.assertTrue(errorCode.equals(testResult.getCode()));
            return new ArrayList<>();
        }
        Assert.assertTrue(testResult.isSuccess() && StringUtils.isNotBlank(testResult.getData()));
        List<CalcShoppingCartResult> cscResultList = JSON.parseArray(testResult.getData(), CalcShoppingCartResult.class);

        BigDecimal bigDecimal = BigDecimal.ZERO;
        if (CollectionUtils.isNotEmpty(cscResultList)) {
            for (CalcShoppingCartResult shoppingCartOutDTO : cscResultList) {
                if (shoppingCartOutDTO.getPromoRewardAmount() != null) {
                    BigDecimal promoRewardAmount = shoppingCartOutDTO.getPromoRewardAmount();
                    bigDecimal = bigDecimal.add(promoRewardAmount);
                }
            }
        }

        return cscResultList;
    }
    private UpdatePromoActivityParam createAcivity(CreatePromoActivityParam createPromoActivity) {

        UpdatePromoActivityParam updatePromoActivity = BeanCopyUtils.jsonCopyBean(createPromoActivity, UpdatePromoActivityParam.class);

        // 创建促销活动
        TestResult testResult = super.mockMvcPost("/activity/create", createPromoActivity);
        updatePromoActivity.setActivityCode(testResult.getData());
        return updatePromoActivity;
    }
    private List<ShoppingCartStore> buildCartStore(String orgCode, String orgName, List<ShoppingCartItem> shoppingCartItems) {
        List<ShoppingCartStore> cartStoreList = Arrays.asList(
                ShoppingCartStore.builder()
                        .orgCode(orgCode01).storeName("SName001")
                        .cartItemList(shoppingCartItems)
                        .build());
        return cartStoreList;
    }


    private CalcShoppingCartParam buildCalcShoppingCartParam(List<ShoppingCartStore> cartStoreList) {

        List<ProductAttribute> attrList = Arrays.asList(
                ProductAttribute.builder().attributeCode("AC001").attributeValues("AV001").build());


        // 购物车查询和计算
        CalcShoppingCartParam calcShoppingCartParam = new CalcShoppingCartParam();
        calcShoppingCartParam.setDomainCode(ActivityHelper.domainCode);
        calcShoppingCartParam.setTenantCode(ActivityHelper.tenantCode);
        calcShoppingCartParam.setMemberCode("memberCode");
        calcShoppingCartParam.setQualifications(new HashMap<>());
        calcShoppingCartParam.setCartStoreList(cartStoreList);

        return calcShoppingCartParam;
    }


    private void updateActivityStatus(UpdatePromoActivityParam param, String status) {

        this.updateActivityStatus(param.getTenantCode(), param.getActivityCode(), status);
    }
    private void updateActivityStatus(String tenantCode, String activityCode, String status) {

        UpdateActivityStatusInDTO param = UpdateActivityStatusInDTO.builder()
                .tenantCode(tenantCode)
                .activityCode(activityCode)
                .activityStatus(status).build();

        super.mockMvcPost("/activity/status/update", param);
    }

}
