/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * <PERSON>ECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.controller;

import com.alibaba.fastjson.JSON;
import com.gtech.commons.utils.BeanCopyUtils;
import com.gtech.commons.utils.DateUtil;
import com.gtech.promotion.code.activity.ActivityTypeEnum;
import com.gtech.promotion.dto.in.activity.UpdateActivityStatusInDTO;
import com.gtech.promotion.helper.ActivityHelper;
import com.gtech.promotion.helper.BaseControllerTests;
import com.gtech.promotion.vo.bean.ProductAttribute;
import com.gtech.promotion.vo.bean.ShoppingCartItem;
import com.gtech.promotion.vo.bean.ShoppingCartStore;
import com.gtech.promotion.vo.param.activity.*;
import com.gtech.promotion.vo.param.coupon.CreateCouponActivityParam;
import com.gtech.promotion.vo.param.coupon.UpdateCouponActivityParam;
import com.gtech.promotion.vo.result.activity.CalcShoppingCartResult;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.junit.Assert;

import java.math.BigDecimal;
import java.util.*;

/**
 * <功能描述>
 */
public class OrderTests extends BaseControllerTests {

    public static final String orgCode01 = "OC0001";
    public static String memberCode = "1";

    // 创建促销活动
    protected UpdatePromoActivityParam createAcivity(CreatePromoActivityParam createPromoActivity) {

        UpdatePromoActivityParam updatePromoActivity = BeanCopyUtils.jsonCopyBean(createPromoActivity, UpdatePromoActivityParam.class);

        // 创建促销活动
        TestResult testResult = super.mockMvcPost("/activity/create", createPromoActivity);
        updatePromoActivity.setActivityCode(testResult.getData());
        return updatePromoActivity;
    }

    protected UpdateCouponActivityParam createCouponActivity(CreatePromoActivityParam createPromoActivity) {

        CreateCouponActivityParam createCouponActivity = BeanCopyUtils.jsonCopyBean(createPromoActivity, CreateCouponActivityParam.class);

        createCouponActivity.setCouponType("01");
        createCouponActivity.setTotalQuantity("10000");
        createCouponActivity.setActivityType(ActivityTypeEnum.COUPON.code());

        createCouponActivity.setActivityBegin(DateUtil.format(this.addDate(-40), DateUtil.FORMAT_YYYYMMDDHHMISS_14));
        createCouponActivity.setActivityEnd(DateUtil.format(this.addDate(200), DateUtil.FORMAT_YYYYMMDDHHMISS_14));

        return this.createCouponActivity(createCouponActivity);
    }

    protected UpdateCouponActivityParam createCouponActivity(CreateCouponActivityParam createCouponActivity) {

        UpdateCouponActivityParam updateCouponActivity = BeanCopyUtils.jsonCopyBean(createCouponActivity, UpdateCouponActivityParam.class);

        // 创建促销活动
        TestResult testResult = super.mockMvcPost("/coupon/createCouponActivity", updateCouponActivity);
        updateCouponActivity.setActivityCode(testResult.getData());

        this.updateActivityStatus(updateCouponActivity.getTenantCode(), updateCouponActivity.getActivityCode(), "04");

        return updateCouponActivity;
    }

    protected Date addDate(int amount) {

        Calendar dateTime = Calendar.getInstance();
        dateTime.add(Calendar.SECOND, amount*100);
        return dateTime.getTime();
    }

    protected void updateActivityStatus(String tenantCode, String activityCode, String status) {

        UpdateActivityStatusInDTO param = UpdateActivityStatusInDTO.builder()
            .tenantCode(tenantCode)
            .activityCode(activityCode)
            .activityStatus(status).build();

        super.mockMvcPost("/activity/status/update", param);
    }

    // 创建促销订单
    protected TestResult createOrder(CalcShoppingCartParam cscParam, String orderNo, BigDecimal incentiveAmount, List<CreateOrderParam.PromoGiveaway> orderGifts, String errorCode) {

        CreateOrderParam coParam = new CreateOrderParam();

        coParam.setDomainCode(ActivityHelper.domainCode);
        coParam.setTenantCode(ActivityHelper.tenantCode);
        coParam.setMemberCode(cscParam.getMemberCode());
        coParam.setFreePostage(0);
        coParam.setOrderNo(orderNo);
        coParam.setCouponCodes(cscParam.getCouponCodes());
        coParam.setCartStoreList(cscParam.getCartStoreList());
        coParam.setPromoDeductedAmount(incentiveAmount);
        coParam.setPromoGiveaways(orderGifts);

        // 提交订单
        return super.mockMvcPost("/order/createOrder", coParam, errorCode);
    }

    // 取消订单
    protected void cancelOrder(String orderNo) {

        CancelOrderParam cancelOrderParam = new CancelOrderParam();
        cancelOrderParam.setDomainCode(ActivityHelper.domainCode);
        cancelOrderParam.setTenantCode(ActivityHelper.tenantCode);
        cancelOrderParam.setOrderNo(orderNo);
        super.mockMvcPost("/order/cancelOrder", cancelOrderParam);
    }

    // 确认订单
    protected void confirmOrder(String orderNo) {

        ConfirmOrderParam confirmOrderParam = new ConfirmOrderParam();
        confirmOrderParam.setDomainCode(ActivityHelper.domainCode);
        confirmOrderParam.setTenantCode(ActivityHelper.tenantCode);
        confirmOrderParam.setOrderNo(orderNo);
        super.mockMvcPost("/order/confirmOrder", confirmOrderParam);
    }

    protected CalcShoppingCartParam buildCalcShoppingCartParam(int qty1, int qty2) {

        List<ProductAttribute> attrList = Arrays.asList(
            ProductAttribute.builder().attributeCode("AC001").attributeValues("AV001").build());

        List<ShoppingCartStore> cartStoreList = Arrays.asList(
            ShoppingCartStore.builder()
                .orgCode(orgCode01).storeName("SName001")
                .cartItemList(Arrays.asList(
                    ShoppingCartItem.builder()
                        .categoryCodes(Arrays.asList("CC001"))
                        .brandCode("BC001")
                        .productCode("PC001")
                        .skuCode("SC001")
                        .attributes(attrList)
                        .quantity(qty1)
                        .productPrice(new BigDecimal(100))
                        .selectionFlag("01")
                        .build(),
                    ShoppingCartItem.builder()
                        .categoryCodes(Arrays.asList("22"))
                        .brandCode("BC002")
                        .productCode("PC002")
                        .skuCode("SC002")
                        .quantity(qty2)
                        .productPrice(new BigDecimal(200))
                        .selectionFlag("01")
                        .build()))
                .build());

        // 购物车查询和计算
        CalcShoppingCartParam calcShoppingCartParam = new CalcShoppingCartParam();
        calcShoppingCartParam.setDomainCode(ActivityHelper.domainCode);
        calcShoppingCartParam.setTenantCode(ActivityHelper.tenantCode);
        calcShoppingCartParam.setMemberCode(memberCode);
        calcShoppingCartParam.setQualifications(new HashMap<>());
        calcShoppingCartParam.setCartStoreList(cartStoreList);

        return calcShoppingCartParam;
    }

    protected List<CalcShoppingCartResult> calcShoppingCart(CalcShoppingCartParam cscParam, String errorCode) {

        TestResult testResult = super.mockMvcPost("/activity/calcShoppingCart", cscParam, errorCode);
        if (StringUtils.isNotBlank(errorCode)) {
            //Assert.assertTrue(errorCode.equals(testResult.getCode()));
            return new ArrayList<>();
        }
        Assert.assertTrue(testResult.isSuccess() && StringUtils.isNotBlank(testResult.getData()));
        List<CalcShoppingCartResult> cscResultList = JSON.parseArray(testResult.getData(), CalcShoppingCartResult.class);

        BigDecimal bigDecimal = BigDecimal.ZERO;
        if (CollectionUtils.isNotEmpty(cscResultList)) {
            for (CalcShoppingCartResult shoppingCartOutDTO : cscResultList) {
                if (shoppingCartOutDTO.getPromoRewardAmount() != null) {
                    BigDecimal promoRewardAmount = shoppingCartOutDTO.getPromoRewardAmount();
                    bigDecimal = bigDecimal.add(promoRewardAmount);
                }
            }
        }

        return cscResultList;
    }

    protected BigDecimal sumPromoDeductedAmount(List<CalcShoppingCartResult> cscResultList) {

        BigDecimal bigDecimal = BigDecimal.ZERO;
        if (CollectionUtils.isNotEmpty(cscResultList)) {
            for (CalcShoppingCartResult shoppingCartOutDTO : cscResultList) {
                if (shoppingCartOutDTO.getPromoRewardAmount() != null) {
                    BigDecimal promoRewardAmount = shoppingCartOutDTO.getPromoRewardAmount();
                    bigDecimal = bigDecimal.add(promoRewardAmount);
                }
            }
        }

        return bigDecimal;
    }
}
