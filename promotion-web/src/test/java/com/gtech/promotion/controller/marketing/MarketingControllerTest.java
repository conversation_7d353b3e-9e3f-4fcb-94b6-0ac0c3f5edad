package com.gtech.promotion.controller.marketing;

import com.gtech.commons.result.PageResult;
import com.gtech.commons.result.Result;
import com.gtech.commons.utils.DateUtil;
import com.gtech.promotion.code.activity.ActivityStatusEnum;
import com.gtech.promotion.code.marketing.ActivityOpsTypeEnum;
import com.gtech.promotion.code.marketing.ActivityTypeEnum;
import com.gtech.promotion.code.marketing.PrizeTypeEnum;
import com.gtech.promotion.component.marketing.MarketingComponent;
import com.gtech.promotion.vo.bean.marketing.MarketingLanguage;
import com.gtech.promotion.vo.bean.marketing.MarketingPrize;
import com.gtech.promotion.vo.bean.marketing.MarketingPrizeLanguage;
import com.gtech.promotion.vo.param.marketing.*;
import com.gtech.promotion.vo.result.marketing.LuckyDrawFindResult;
import com.gtech.promotion.vo.result.marketing.MarketingQueryResult;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

@RunWith(MockitoJUnitRunner.class)
public class MarketingControllerTest {

    @InjectMocks
    private MarketingController marketingController;
    @InjectMocks
    private LuckyDrawController luckyDrawController;

    @Mock
    private MarketingComponent marketingComponent;

    @Test
    public void create_奖品券(){
        LuckyDrawCreateParam param = createParam(PrizeTypeEnum.COUPON);
        param.setIncentiveLimitedFlag("01");
        param.setLuckyDrawRuleFlag("01");
        Mockito.when(marketingComponent.createMarketing(Mockito.any())).thenReturn("1");
        Result<String> serializableResult = luckyDrawController.create(param);
        Assert.assertEquals("1", serializableResult.getData());
    }


    private LuckyDrawUpdateParam createUpdateParam(PrizeTypeEnum prizeTypeEnum) {
        LuckyDrawUpdateParam param = new LuckyDrawUpdateParam();
        param.setActivityCode("1");
        param.setOperateUser("1");
        param.setDomainCode("1");
        param.setTenantCode("1");
        param.setOrgCode("1");
        param.setActivityName("1");
        param.setActivityType(ActivityTypeEnum.LUCKY_DRAW.code());
        param.setOpsType(ActivityOpsTypeEnum.DA_ZHUAN_PAN.code());
        param.setSponsors("1");
        param.setActivityBegin("20200202121212");
        param.setActivityEnd(DateUtil.format(DateUtil.addDay(1), DateUtil.FORMAT_YYYYMMDDHHMISS_14));
        List<MarketingLanguage> marketingLanguages = new ArrayList<>();
        MarketingLanguage language = new MarketingLanguage();
        language.setActivityLabel("1");
        language.setActivityName("1");
        language.setLanguage("zh-CN");
        marketingLanguages.add(language);
        param.setMarketingLanguages(marketingLanguages);
        List<MarketingPrize> marketingPrizes = new ArrayList<>();
        MarketingPrize prize = new MarketingPrize();
        prize.setPrizeCode("1");
        prize.setPrizeName("1");
        prize.setPrizeType(prizeTypeEnum.code());
        prize.setPrizeNum(1);
        prize.setPrizeOrder(1);
        prize.setPrizeQuota(1);
        prize.setPrizeProbability(new BigDecimal("0.1"));
        List<MarketingPrizeLanguage> marketingPrizeLanguages = new ArrayList<>();
        MarketingPrizeLanguage prizeLanguage = new MarketingPrizeLanguage();
        prizeLanguage.setLanguage("zh-CN");
        prizeLanguage.setPrizeName("11");
        marketingPrizeLanguages.add(prizeLanguage);
        prize.setMarketingPrizeLanguages(marketingPrizeLanguages);
        marketingPrizes.add(prize);
        param.setMarketingPrizes(marketingPrizes);
        return param;
    }

    private LuckyDrawCreateParam createParam(PrizeTypeEnum prizeTypeEnum) {
        LuckyDrawCreateParam param = new LuckyDrawCreateParam();
        param.setDomainCode("1");
        param.setOperateUser("1");
        param.setTenantCode("1");
        param.setOrgCode("1");
        param.setActivityName("1");
        param.setActivityType(ActivityTypeEnum.LUCKY_DRAW.code());
        param.setOpsType(ActivityOpsTypeEnum.DA_ZHUAN_PAN.code());
        param.setSponsors("1");
        param.setActivityBegin("20200202121212");
        param.setActivityEnd(DateUtil.format(DateUtil.addDay(1), DateUtil.FORMAT_YYYYMMDDHHMISS_14));
        List<MarketingLanguage> marketingLanguages = new ArrayList<>();
        MarketingLanguage language = new MarketingLanguage();
        language.setActivityLabel("1");
        language.setActivityName("1");
        language.setLanguage("zh-CN");
        marketingLanguages.add(language);
        param.setMarketingLanguages(marketingLanguages);
        List<MarketingPrize> marketingPrizes = new ArrayList<>();
        MarketingPrize prize = new MarketingPrize();
        prize.setPrizeCode("1");
        prize.setPrizeName("1");
        prize.setPrizeType(prizeTypeEnum.code());
        prize.setPrizeNum(1);
        prize.setPrizeOrder(1);
        prize.setPrizeQuota(1);
        prize.setPrizeProbability(new BigDecimal("0.1"));
        List<MarketingPrizeLanguage> marketingPrizeLanguages = new ArrayList<>();
        MarketingPrizeLanguage prizeLanguage = new MarketingPrizeLanguage();
        prizeLanguage.setLanguage("zh-CN");
        prizeLanguage.setPrizeName("11");
        marketingPrizeLanguages.add(prizeLanguage);
        prize.setMarketingPrizeLanguages(marketingPrizeLanguages);
        marketingPrizes.add(prize);
        param.setMarketingPrizes(marketingPrizes);
        return param;
    }

    @Test
    public void create_没有奖品(){
        LuckyDrawCreateParam param = createParam(PrizeTypeEnum.DEFAULT);
        param.setIncentiveLimitedFlag("01");
        param.setLuckyDrawRuleFlag("01");
        Mockito.when(marketingComponent.createMarketing(Mockito.any())).thenReturn("1");
        Result<String> serializableResult = luckyDrawController.create(param);
        Assert.assertEquals("1", serializableResult.getData());
    }

    @Test
    public void update(){
        LuckyDrawUpdateParam param = createUpdateParam(PrizeTypeEnum.DEFAULT);
        param.setIncentiveLimitedFlag("01");
        param.setLuckyDrawRuleFlag("01");
        Mockito.when(marketingComponent.updateMarketing(Mockito.any())).thenReturn(1);
        Result<Serializable> serializableResult = luckyDrawController.update(param);
        Assert.assertTrue(serializableResult.isSuccess());
    }

    @Test
    public void updateStatus(){
        MarketingUpdateStatusParam param = new MarketingUpdateStatusParam();
        param.setDomainCode("1");
        param.setTenantCode("1");
        param.setOrgCode("1");
        param.setActivityCode("1");
        param.setOperateUser("1");
        param.setActivityStatus(ActivityStatusEnum.EFFECTIVE.code());
        Mockito.when(marketingComponent.updateMarketingStatus(Mockito.any())).thenReturn(1);
        Result<Serializable> serializableResult = marketingController.updateStatus(param);
        Assert.assertTrue(serializableResult.isSuccess());
    }

    @Test
    public void extendMarketing(){
        MarketingExtendParam param = new MarketingExtendParam();
        param.setDomainCode("1");
        param.setTenantCode("1");
        param.setOrgCode("1");
        param.setActivityCode("1");
        param.setOperateUser("1");
        param.setEndTime("20201212121212");
        Mockito.when(marketingComponent.extendMarketing(Mockito.any(), Mockito.any())).thenReturn(1);
        Result<Serializable> serializableResult = marketingController.extendMarketing(param);
        Assert.assertTrue(serializableResult.isSuccess());
    }

    @Test
    public void find(){
        MarketingFindParam param = new MarketingFindParam();
        param.setDomainCode("1");
        param.setTenantCode("1");
        param.setOrgCode("1");
        param.setActivityCode("1");
        LuckyDrawFindResult marketingFindResult = new LuckyDrawFindResult();
        marketingFindResult.setActivityCode("1");
        Mockito.when(marketingComponent.findMarketing(Mockito.any())).thenReturn(marketingFindResult);
        Result<LuckyDrawFindResult> marketingFindResultResult = luckyDrawController.find(param);
        Assert.assertEquals(marketingFindResult.getActivityCode(), marketingFindResultResult.getData().getActivityCode());
    }

    @Test
    public void query(){
        MarketingQueryParam param = new MarketingQueryParam();
        param.setDomainCode("1");
        param.setTenantCode("1");
        param.setOrgCode("1");

        PageResult<MarketingQueryResult> resultPageResult = new PageResult<>();
        Mockito.when(marketingComponent.queryMarketingList(Mockito.any())).thenReturn(resultPageResult);
        PageResult<MarketingQueryResult> query = marketingController.query(param);
        Assert.assertEquals(resultPageResult.getData().getTotal(), query.getData().getTotal());
    }

    @Test
    public void expireMarketing(){
        Mockito.when(marketingComponent.expireMarketing()).thenReturn(1);
        Result<Serializable> serializableResult = marketingController.expireMarketing();
        Assert.assertTrue(serializableResult.isSuccess());
    }
}
