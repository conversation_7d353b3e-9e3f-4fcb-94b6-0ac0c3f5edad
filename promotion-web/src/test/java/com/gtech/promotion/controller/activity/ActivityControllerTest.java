package com.gtech.promotion.controller.activity;

import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.gtech.basic.masterdata.response.JsonResult;
import com.gtech.commons.result.PageResult;
import com.gtech.commons.result.Result;
import com.gtech.promotion.code.activity.ProductTypeEnum;
import com.gtech.promotion.component.activity.ActivityComponentDomain;
import com.gtech.promotion.dto.in.activity.TPromoActivityListMallInDTO;
import com.gtech.promotion.dto.in.activity.TenantCodeActivityIdDTO;
import com.gtech.promotion.dto.out.activity.TPromoActivityListMallOutDTO;
import com.gtech.promotion.exception.PromotionParamValidateException;
import com.gtech.promotion.feign.MasterDataFeignClient;
import com.gtech.promotion.vo.bean.ActivityLanguage;
import com.gtech.promotion.vo.bean.ProductDetail;
import com.gtech.promotion.vo.bean.ProductScope;
import com.gtech.promotion.vo.param.UpdateWinTogetherStatus;
import com.gtech.promotion.vo.param.activity.*;
import com.gtech.promotion.vo.result.activity.QueryListResult;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

@RunWith(MockitoJUnitRunner.class)
public class ActivityControllerTest {

    @InjectMocks
    private ActivityController activityController;

    @Mock
    private ActivityComponentDomain activityComponentDomain;

    @Mock
    private MasterDataFeignClient masterDataFeignClient;

    @Test
    public void queryList() {
        QueryListParam param = new QueryListParam();
        param.setTenantCode("1");
        param.setDomainCode("1");
        Mockito.when(activityComponentDomain.queryList(Mockito.any())).thenReturn(new PageInfo<>());
        PageResult<QueryListResult> stringResult = activityController.queryList(param);
        Assert.assertEquals(0, stringResult.getData().getTotal().intValue());
    }

    @Test
    public void testActivityExtend() {
        ActivityExtendParam param = new ActivityExtendParam("1", "1", "20200101010101", "1", "12", "323");
        Mockito.when(activityComponentDomain.activityExtend(Mockito.any())).thenReturn(1);
        Result<String> stringResult = activityController.activityExtend(param);
        Assert.assertEquals("Update succeeded.", stringResult.getData());
    }

    @Test
    public void testupdateActivityProductDetailBlackList() {
        List<ProductDetail> productDetailBlackList = new ArrayList<>();
        UpdateActivityProductDetailBlackListParam blackListDTO = new UpdateActivityProductDetailBlackListParam();
        blackListDTO.setProductDetailBlackList(productDetailBlackList);
        blackListDTO.setTenantCode("1");
        Mockito.when(activityComponentDomain.updateActivityProductDetailBlackList(Mockito.any(), Mockito.any())).thenReturn(1);
        Result<Serializable> result = activityController.updateActivityProductDetailBlackList(blackListDTO);
        Assert.assertTrue(result.isSuccess());
    }

    @Test
    public void testGetQueryAllActivityMall() {
        TPromoActivityListMallInDTO param = new TPromoActivityListMallInDTO();
        param.setOrgCode("1");
        param.setTenantCode("1");

        List<TPromoActivityListMallOutDTO> listMallOutDTOS = new ArrayList<>();

        Mockito.when(activityComponentDomain.queryMallAllActivity(Mockito.any())).thenReturn(listMallOutDTOS);
        Result<List<TPromoActivityListMallOutDTO>> queryAllActivityMall = activityController.getQueryAllActivityMall(param);
        Assert.assertEquals(queryAllActivityMall.getData().size(), listMallOutDTOS.size());
    }

    @Test
    public void testDeleteActivity() {
        TenantCodeActivityIdDTO param = new TenantCodeActivityIdDTO("1", "1");
        Mockito.doNothing().when(activityComponentDomain).deleteActivityCorrelation(Mockito.any(), Mockito.any());
        Result<Object> result = activityController.deleteActivity(param);
        Assert.assertTrue(result.isSuccess());
    }

    @Test
    public void updateActivityProduct() {
        UpdateActivityProductParam param = new UpdateActivityProductParam();
        param.setDomainCode("1");
        param.setTenantCode("1");
        param.setActivityCode("1");
        param.setTemplateCode("0101020103010401");
        param.setConditionProductType(ProductTypeEnum.CUSTOM_RANGE.code());
        ArrayList<ProductScope> products = new ArrayList<>();
        products.add(ProductScope.builder().categoryCode("1").build());
        param.setProducts(products);
        Mockito.when(activityComponentDomain.updateActivityProduct(Mockito.any())).thenReturn(1);
        Result<Object> result = activityController.updateActivityProduct(param);
        Assert.assertTrue(result.isSuccess());
    }

    @Test
    public void expireActivity() {
        Mockito.when(activityComponentDomain.expireActivity()).thenReturn(1);
        Result<Serializable> serializableResult = activityController.expireActivity();
        Assert.assertTrue(serializableResult.isSuccess());
    }

    @Test
    public void existPromotionCategory() {
        QueryPromotionCategoryParam param = new QueryPromotionCategoryParam();
        param.setPromotionCategory("1");
        param.setTenantCode("1");
        Mockito.when(activityComponentDomain.existPromotionCategory(Mockito.any())).thenReturn(1);
        Result<Integer> serializableResult = activityController.existPromotionCategory(param);
        Assert.assertEquals(1, serializableResult.getData().intValue());
    }

    @Test
    public void updatePromotionCategory() {
        QueryPromotionCategoryParam param = new QueryPromotionCategoryParam();
        param.setPromotionCategory("1");
        param.setTenantCode("1");
        Mockito.when(activityComponentDomain.updatePromotionCategoryNull(Mockito.any())).thenReturn(1);
        Result<Integer> serializableResult = activityController.updatePromotionCategory(param);
        Assert.assertEquals(1, serializableResult.getData().intValue());
    }

    @Test
    public void checkCustomCondition_empty() {

        List<CustomCondition> customConditions = new ArrayList<>();
        CustomCondition customCondition = new CustomCondition();

        customCondition.setCustomValue("1");
        customCondition.setCustomValueList(new ArrayList<>());
        customCondition.setCustomKey("1");

        JsonResult<String> jsonResult = new JsonResult<>();
        jsonResult.setData("1");
        jsonResult.setSuccess(true);

        activityController.checkCustomCondition(customConditions, "1");

    }

    @Test
    public void checkCustomCondition_not_empty_1() {

        List<CustomCondition> customConditions = new ArrayList<>();
        CustomCondition customCondition = new CustomCondition();

        customCondition.setCustomValue("1");
        customCondition.setCustomValueList(new ArrayList<>());
        customCondition.setCustomKey("1");
        customConditions.add(customCondition);

        JsonResult<String> jsonResult = new JsonResult<>();
        jsonResult.setData("1");
        jsonResult.setSuccess(true);

        Mockito.when(masterDataFeignClient.getValueValue(Mockito.anyString(), Mockito.anyString())).thenReturn(jsonResult);
        activityController.checkCustomCondition(customConditions, "1");

    }

    @Test(expected = PromotionParamValidateException.class)
    public void checkCustomCondition_not_empty_2() {

        List<CustomCondition> customConditions = new ArrayList<>();

        CustomCondition customCondition1 = new CustomCondition();

        customCondition1.setCustomValue("1");
        customCondition1.setCustomValueList(new ArrayList<>());
        customCondition1.setCustomKey("1");
        customConditions.add(customCondition1);

        CustomCondition customCondition = new CustomCondition();

        customCondition.setCustomValue("1");
        customCondition.setCustomValueList(new ArrayList<>());
        customCondition.setCustomKey("1");
        customConditions.add(customCondition);

        JsonResult<String> jsonResult = new JsonResult<>();
        jsonResult.setData("1");
        jsonResult.setSuccess(true);

        Mockito.when(masterDataFeignClient.getValueValue(Mockito.anyString(), Mockito.anyString())).thenReturn(jsonResult);
        activityController.checkCustomCondition(customConditions, "1");

    }


    @Test
    public void updateBatchWinTogetherActivity() {
        WinTogetherDifferentParam winTogetherDifferentParam = new WinTogetherDifferentParam();
        winTogetherDifferentParam.setActivityCode("100000");
        winTogetherDifferentParam.setTemplateCode("100000100000100000100000100000100000");
        winTogetherDifferentParam.setConditionProductType(ProductTypeEnum.CUSTOM_RANGE.code());
        ActivityLanguage activityLanguage = new ActivityLanguage();
        activityLanguage.setLanguage("zh-cn");

        WinTogetherParam param = new WinTogetherParam();
        param.setDomainCode("100000");
        param.setTenantCode("100000");
        param.setActivityParams(Lists.newArrayList(winTogetherDifferentParam));
        param.setActivityType("100000");
        param.setActivityLanguages(Lists.newArrayList(activityLanguage));
        param.setActivityBegin("20230101000000");
        param.setActivityEnd("20440101000000");
        Result result = activityController.updateBatchWinTogetherActivity(param);
        Assert.assertTrue(result.isSuccess());
    }

    @Test
    public void updateWinTogetherActivityStatus() {
        UpdateWinTogetherStatus param = new UpdateWinTogetherStatus();
        param.setDomainCode("100000");
        param.setTenantCode("100000");
        param.setActivityStatus("100000");
        param.setOperateUser("100000");
        param.setActivityCodes(Lists.newArrayList("100000"));
        Result result = activityController.updateWinTogetherActivityStatus(param);
        Assert.assertTrue(result.isSuccess());
    }

    @Test
    public void updateExternalActivityId() {
        UpdateExternalActivityInParam param = new UpdateExternalActivityInParam();
        param.setDomainCode("100000");
        param.setTenantCode("100000");
        param.setOperateUser("100000");
        param.setActivityCode("1");
        param.setTenantCode("1");
        param.setExternalActivityId("!1");
        Result result = activityController.updateExternalActivityId(param);
        Assert.assertTrue(result.isSuccess());
    }

}