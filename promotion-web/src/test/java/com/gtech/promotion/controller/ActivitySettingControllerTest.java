package com.gtech.promotion.controller;

import com.gtech.commons.result.Result;
import com.gtech.promotion.code.activity.SettingTypeEnum;
import com.gtech.promotion.controller.activity.ActivitySettingController;
import com.gtech.promotion.dao.entity.activity.TPromoActivityExpressionEntity;
import com.gtech.promotion.dto.in.activity.TpromoActivityExpressionDTO;
import com.gtech.promotion.dto.in.activity.TpromoActivitySettingDTO;
import com.gtech.promotion.service.activity.TPromoActivityExpressionService;
import com.gtech.promotion.service.activity.TPromoActivitySettingService;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class ActivitySettingControllerTest {

    @InjectMocks
    private ActivitySettingController activitySettingController;

    @Mock
    private TPromoActivityExpressionService expressionService;
    @Mock
    private TPromoActivitySettingService settingService;


    @Test
    public void activityExpression_0(){
        TpromoActivityExpressionDTO param = new TpromoActivityExpressionDTO();
        param.setTenantCode("0");
        param.setExpression("0");
        Mockito.doNothing().when(expressionService).addAllActivityExpression();
        Result<Object> stringResult = activitySettingController.activityExpression(param);
        Assert.assertEquals(null, stringResult.getData());
    }
    @Test
    public void activityExpression_1(){
        TpromoActivityExpressionDTO param = new TpromoActivityExpressionDTO();
        param.setTenantCode("1");
        param.setExpression("0");
        Mockito.when(expressionService.selectExpression(Mockito.any())).thenReturn(null);
        Result<Object> stringResult = activitySettingController.activityExpression(param);
        Assert.assertEquals(null, stringResult.getData());
    }
    @Test
    public void activityExpression_2(){
        TPromoActivityExpressionEntity entity = new TPromoActivityExpressionEntity();
        TpromoActivityExpressionDTO param = new TpromoActivityExpressionDTO();
        param.setTenantCode("1");
        param.setExpression("0");
        Mockito.when(expressionService.selectExpression(Mockito.any())).thenReturn(entity);
        Result<Object> stringResult = activitySettingController.activityExpression(param);
        Assert.assertEquals(null, stringResult.getData());
    }
    @Test
    public void activitySetting_0(){
        TpromoActivitySettingDTO param = new TpromoActivitySettingDTO();
        param.setTenantCode("1");
        param.setSettingType(0);
        Mockito.doNothing().when(settingService).addAllActivitySetting();
        Result<Object> stringResult = activitySettingController.activitySetting(param);
        Assert.assertEquals(null, stringResult.getData());
    }
    @Test
    public void activitySetting_1(){
        TpromoActivitySettingDTO param = new TpromoActivitySettingDTO();
        param.setTenantCode("1");
        param.setSettingType(1);
        param.setSettingCode("1");
        param.setSettingValue("1");
        Mockito.when(settingService.selectSetting(Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(null);
        Mockito.doNothing().when(settingService).addActivitySetting(Mockito.any(),Mockito.anyInt(),Mockito.any(),Mockito.any());
        Result<Object> stringResult = activitySettingController.activitySetting(param);
        Assert.assertEquals(null, stringResult.getData());
    }

    @Test
    public void activitySetting(){
        TpromoActivitySettingDTO setting = new TpromoActivitySettingDTO();
        setting.setTenantCode("100001");
//        setting.setSettingType(Integer.valueOf(SettingTypeEnum.PRICE_SORT.code()));
        try {
            activitySettingController.activitySetting(setting);
        }catch (Exception e){

        }
        setting.setSettingCode("test");
        setting.setSettingValue("test");
        setting.setSettingType(2);
        try{
        activitySettingController.activitySetting(setting);
        }catch (Exception e){

        }

        setting.setSettingType(Integer.valueOf(SettingTypeEnum.PRICE_SORT.code()));
    }

}
