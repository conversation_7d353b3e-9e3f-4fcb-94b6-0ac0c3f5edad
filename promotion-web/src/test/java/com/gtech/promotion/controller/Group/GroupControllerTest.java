package com.gtech.promotion.controller.Group;

import com.github.pagehelper.PageInfo;
import com.gtech.commons.result.PageResult;
import com.gtech.commons.result.Result;
import com.gtech.promotion.component.activity.PromoGroupDomain;
import com.gtech.promotion.controller.activity.GroupController;
import com.gtech.promotion.dto.out.activity.GroupActivityVO;
import com.gtech.promotion.vo.param.activity.*;
import com.gtech.promotion.vo.result.activity.ActivityGroupResult;
import com.gtech.promotion.vo.result.activity.QueryActivityGroupListResult;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.List;

/**
 * @Description :
 * <AUTHOR> gaojie.li
 * @Date : 2023/6/16 10:01
 */
@RunWith(MockitoJUnitRunner.class)
public class GroupControllerTest {

    @InjectMocks
    private GroupController groupController;

    @Mock
    private PromoGroupDomain promoGroupDomain;

    @Test
    public void initGroup(){

        GroupInitParam param = new GroupInitParam();
        param.setTenantCode("1");
        param.setDomainCode("1");

        param.setOperatorUser("1");
        Mockito.doNothing().when(promoGroupDomain).initGroupDomain(Mockito.any());
        Result<String> group = groupController.initGroup(param);
        Assert.assertEquals(null, group.getData());
    }

    @Test
    public void createGroup(){

        GroupCreateParam param = new GroupCreateParam();
        param.setTenantCode("1");
        param.setGroupName("1");
        param.setDomainCode("1");

        param.setOperatorUser("1");
        Mockito.when(promoGroupDomain.createGroupDomain(Mockito.any())).thenReturn("1");
        Result<String> group = groupController.createGroup(param);
        Assert.assertEquals("1", group.getData());
    }

    @Test
    public void updateGroup(){

        GroupUpdateParam param = new GroupUpdateParam();
        param.setTenantCode("1");
        param.setGroupName("1");
        param.setDomainCode("1");
        param.setGroupCode("1");
        param.setOperatorUser("1");
        Mockito.when(promoGroupDomain.updateGroupDomain(Mockito.any())).thenReturn(1);
        Result<Integer> group = groupController.updateGroup(param);
        Assert.assertEquals(1, group.getData().intValue());
    }

    @Test
    public void deleteGroup(){

        GroupDeleteParam param = new GroupDeleteParam();
        param.setTenantCode("1");
        param.setGroupCode("1");
        param.setOperatorUser("1");
        param.setDomainCode("1");

        Mockito.when(promoGroupDomain.deleteGroupDomain(Mockito.any())).thenReturn(1);
        Result<Integer> group = groupController.deleteGroup(param);
        Assert.assertEquals(1, group.getData().intValue());
    }

    @Test
    public void queryActivityGroupList(){

        GroupQueryParam param = new GroupQueryParam();
        param.setTenantCode("1");
        param.setGroupCode("1");
        param.setLogicDelete("0");
        param.setDomainCode("1");

        param.setGroupName("!");
        List<ActivityGroupResult>  results = new ArrayList<>();
        ActivityGroupResult activityGroupResult = new ActivityGroupResult();
        activityGroupResult.setGroupName("1");
        activityGroupResult.setGroupCode("1");
        activityGroupResult.setPriority(1);
        activityGroupResult.setTenantCode("1");
        activityGroupResult.setLogicDelete("0");
        activityGroupResult.setType("01");
        results.add(activityGroupResult);
        Mockito.when(promoGroupDomain.queryActivityGroupListDomain(Mockito.any())).thenReturn(results);
        Result<List<ActivityGroupResult>> group = groupController.queryActivityGroupList(param);
        Assert.assertEquals(1, group.getData().size());
    }

    @Test
    public void bindingActivityToGroup(){

        GroupBindingActivityParam param = new GroupBindingActivityParam();
        param.setTenantCode("1");
        param.setGroupCode("1");
        List<String> list = new ArrayList<>();
        list.add("1");
        param.setActivityCodes(list);
        param.setDomainCode("1");

        List<ActivityGroupResult>  results = new ArrayList<>();
        ActivityGroupResult activityGroupResult = new ActivityGroupResult();
        activityGroupResult.setGroupName("1");
        activityGroupResult.setGroupCode("1");
        activityGroupResult.setPriority(1);
        activityGroupResult.setTenantCode("1");
        activityGroupResult.setLogicDelete("0");
        activityGroupResult.setType("01");
        results.add(activityGroupResult);

        Mockito.when(promoGroupDomain.bindingActivityToGroupDomain(Mockito.any())).thenReturn(1);
        Result<Integer> integerResult = groupController.bindingActivityToGroup(param);
        Assert.assertEquals(1, integerResult.getData().intValue());
    }


    @Test
    public void queryActivityListUnderGroup(){

        GroupQueryListParam param = new GroupQueryListParam();
        param.setTenantCode("1");
        param.setGroupCode("1");
        param.setDomainCode("1");

        List<GroupActivityVO>  results = new ArrayList<>();
        GroupActivityVO activityGroupResult = new GroupActivityVO();
        activityGroupResult.setGroupName("1");
        activityGroupResult.setGroupCode("1");
        activityGroupResult.setTenantCode("1");
        results.add(activityGroupResult);

        PageInfo<GroupActivityVO> pageInfo = new PageInfo<>();

        pageInfo.setTotal(1L);
        pageInfo.setList(results);

        Mockito.when(promoGroupDomain.queryActivityListUnderGroup(Mockito.any())).thenReturn(pageInfo);

        PageResult<QueryActivityGroupListResult> resultPageResult = groupController.queryActivityListUnderGroup(param);

        Assert.assertEquals(1, resultPageResult.getData().getList().size());
    }

    @Test
    public void updateGroupPriority(){

        GroupSettingPriorityParam param = new GroupSettingPriorityParam();
        param.setDomainCode("!");
        param.setTenantCode("1");

        List<ActivityGroupParam> groups = new ArrayList<>();
        ActivityGroupParam activityGroupParam = new ActivityGroupParam();
        activityGroupParam.setGroupCode("!");

        groups.add(activityGroupParam);

        param.setGroups(groups);
        Result<Integer> integerResult = groupController.updateGroupPriority(param);
        Assert.assertEquals(0, integerResult.getData().intValue());

    }

}
