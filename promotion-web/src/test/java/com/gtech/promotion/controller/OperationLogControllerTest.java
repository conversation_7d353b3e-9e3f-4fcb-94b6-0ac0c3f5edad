package com.gtech.promotion.controller;

import com.gtech.commons.result.Result;
import com.gtech.promotion.controller.activity.OperationLogController;
import com.gtech.promotion.service.activity.OperationLogService;
import com.gtech.promotion.vo.param.activity.QueryOperationLogsByActivityCodeParam;
import com.gtech.promotion.vo.result.activity.QueryOperationLogsByActivityCodeResult;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.List;

@RunWith(MockitoJUnitRunner.class)
public class OperationLogControllerTest {

    @InjectMocks
    private OperationLogController operationLogController;

    @Mock
    private OperationLogService operationLogService;

    @Test
    public void queryOperationLogsByActivityCode(){
        QueryOperationLogsByActivityCodeParam param = new QueryOperationLogsByActivityCodeParam("1", "1");

        Mockito.when(operationLogService.queryOperationLogsByActivityCode(Mockito.any())).thenReturn(new ArrayList<>());
        Result<List<QueryOperationLogsByActivityCodeResult>> listResult = operationLogController.queryOperationLogsByActivityCode(param);
        Assert.assertEquals(0, listResult.getData().size());
    }
}
