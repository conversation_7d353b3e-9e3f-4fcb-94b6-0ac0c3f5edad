/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.controller;

import com.gtech.commons.utils.DateUtil;
import com.gtech.promotion.PromotionWebApplication;
import com.gtech.promotion.code.coupon.ReleaseTypeEnum;
import com.gtech.promotion.code.coupon.TakeLabelEnum;
import com.gtech.promotion.helper.ActivityHelper;
import com.gtech.promotion.vo.bean.MemberInfo;
import com.gtech.promotion.vo.param.activity.CalcShoppingCartParam;
import com.gtech.promotion.vo.param.coupon.*;
import com.gtech.promotion.vo.result.activity.CalcShoppingCartResult;
import org.junit.FixMethodOrder;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.junit.runners.MethodSorters;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * CouponActivityDetailOutTest
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = { PromotionWebApplication.class })
@FixMethodOrder(MethodSorters.NAME_ASCENDING)
@ActiveProfiles("dev")
@Transactional
public class CouponControllerTest extends OrderTests {

    @Test
    public void test000setup() {

        memberCode = super.randomCode(10);

        super.setup();

//        UpdatePromoActivityParam updatePromoActivity = ActivityHelper.buildActivity0101_0201_0301_0402_单品无条件打折扣();
//        CreateCouponActivityParam createCouponActivity = BeanCopyUtils.jsonCopyBean(updatePromoActivity, CreateCouponActivityParam.class);
//
//        createCouponActivity.setCouponType("01");
//        createCouponActivity.setTotalQuantity("10000");
//        createCouponActivity.setActivityType(ActivityTypeEnum.COUPON.code());
//        createCouponActivity.setActivityBegin(DateUtil.format(this.addDate(-40), DateUtil.FORMAT_YYYYMMDDHHMISS_14));
//        createCouponActivity.setActivityEnd(DateUtil.format(this.addDate(40000), DateUtil.FORMAT_YYYYMMDDHHMISS_14));
//
//        UpdateCouponActivityParam updateCouponActivity = this.createCouponActivity(createCouponActivity);
//        this.createCouponRelease01(updateCouponActivity, true);
//        this.sendCouponToUser(updateCouponActivity, memberCode);
//        this.sendCouponToUser(updateCouponActivity, memberCode);
//        this.sendCouponToUser(updateCouponActivity, memberCode);

//        List<String> userList = new ArrayList<>();
//        for(int i = 0; i < 100; i++)
//            userList.add(memberCode + i);
//
//        this.sendCouponToUserList("100001", "20200998470600440637", userList);
    }

    @Test
    public void test0100_CouponActivity() {
        memberCode = super.randomCode(10);
        super.setup();
        UpdateCouponActivityParam promoActivity = this.createCouponActivity(ActivityHelper.buildActivity0101_0201_0301_0401_单品无条件减金额(null, null));

        promoActivity.setOpsType("100099230719174913");
        this.createCouponRelease01(promoActivity, true);
        this.createCouponRelease01(promoActivity, false);

        //this.sendCouponToUserList(promoActivity);
        //this.sendCouponToUser(promoActivity, memberCode);
        this.queryCouponListByUser();

        //String couponCode = this.sendCouponToUser(promoActivity, memberCode);

        CalcShoppingCartParam cscParam = null;
        List<CalcShoppingCartResult> cscResult = null;
        BigDecimal incentiveAmount = null;
        String orderNo = null;

        cscParam = this.buildCalcShoppingCartParam(1, 1);
        cscParam.setMemberCode(memberCode);
        //cscParam.setCouponCodes(couponCode);
        cscResult = this.calcShoppingCart(cscParam, null);
        incentiveAmount = this.sumPromoDeductedAmount(cscResult);
        orderNo = super.randomCode(8);
        this.createOrder(cscParam, orderNo, incentiveAmount, null, null);
        this.cancelOrder(orderNo);

        cscParam = this.buildCalcShoppingCartParam(1, 1);
        cscParam.setMemberCode(memberCode);
        //cscParam.setCouponCodes(couponCode);
        cscResult = this.calcShoppingCart(cscParam, null);
        incentiveAmount = this.sumPromoDeductedAmount(cscResult);
        orderNo = super.randomCode(8);
        this.createOrder(cscParam, orderNo, incentiveAmount, null, null);
        this.confirmOrder(orderNo);

        cscParam = this.buildCalcShoppingCartParam(1, 1);
        cscParam.setMemberCode(memberCode);
        //cscParam.setCouponCodes(couponCode);
        this.calcShoppingCart(cscParam, "10143311");
    }

    @Test
    public void test0200_CouponRelese() {

        UpdateCouponActivityParam promoActivity = this.createCouponActivity(ActivityHelper.buildActivity0101_0201_0301_0402_单品无条件打折扣());

        this.createCouponRelease02(promoActivity);
        String releaseCode = this.createCouponRelease02(promoActivity);
        this.findCouponRelease(promoActivity, releaseCode);
        this.queryCouponRelease(promoActivity);
        this.cancelCouponRelease(promoActivity, releaseCode);
    }

    private String createCouponRelease01(UpdateCouponActivityParam promoActivity, boolean timeFollowActivity) {

        CreateCouponReleaseParam param = new CreateCouponReleaseParam();

        param.setDomainCode(promoActivity.getDomainCode());
        param.setTenantCode(promoActivity.getTenantCode());
        param.setActivityCode(promoActivity.getActivityCode());
        
        if (timeFollowActivity) {
            param.setReceiveStart(promoActivity.getActivityBegin());
            param.setReceiveEnd(promoActivity.getActivityEnd());
            param.setValidStart(promoActivity.getActivityBegin());
            param.setValidEnd(promoActivity.getActivityEnd());
        } else {
            param.setReceiveStart(DateUtil.format(this.addDate(-30), DateUtil.FORMAT_YYYYMMDDHHMISS_14));
            param.setReceiveEnd(DateUtil.format(this.addDate(100), DateUtil.FORMAT_YYYYMMDDHHMISS_14));
            param.setValidStart(DateUtil.format(this.addDate(0), DateUtil.FORMAT_YYYYMMDDHHMISS_14));
            param.setValidEnd(DateUtil.format(this.addDate(100), DateUtil.FORMAT_YYYYMMDDHHMISS_14));
        }

        param.setReleaseType(ReleaseTypeEnum.IMMEDIATELY.code());
        param.setReleaseQuantity(1000);

        TestResult testResult = super.mockMvcPost("/coupon/createCouponRelease", param);
        return testResult.getData();
    }

    private String createCouponRelease02(UpdateCouponActivityParam promoActivity) {

        CreateCouponReleaseParam param = new CreateCouponReleaseParam();

        param.setDomainCode(promoActivity.getDomainCode());
        param.setTenantCode(promoActivity.getTenantCode());
        param.setActivityCode(promoActivity.getActivityCode());

        param.setReceiveStart(DateUtil.format(this.addDate(-30), DateUtil.FORMAT_YYYYMMDDHHMISS_14));
        param.setReceiveEnd(DateUtil.format(this.addDate(20), DateUtil.FORMAT_YYYYMMDDHHMISS_14));

        param.setValidStart(DateUtil.format(this.addDate(0), DateUtil.FORMAT_YYYYMMDDHHMISS_14));
        param.setValidEnd(DateUtil.format(this.addDate(30), DateUtil.FORMAT_YYYYMMDDHHMISS_14));

        param.setReleaseType(ReleaseTypeEnum.APPOINTMENT.code());
        param.setReleaseTime(DateUtil.format(this.addDate(10), DateUtil.FORMAT_YYYYMMDDHHMISS_14));
        param.setReleaseQuantity(100);

        TestResult testResult = super.mockMvcPost("/coupon/createCouponRelease", param);
        return testResult.getData();
    }

    private void findCouponRelease(UpdateCouponActivityParam promoActivity, String releaseCode) {

        FindCouponReleaseParam param = new FindCouponReleaseParam();
        
        param.setDomainCode(promoActivity.getDomainCode());
        param.setTenantCode(promoActivity.getTenantCode());
        param.setReleaseCode(releaseCode);

        TestResult testResult = super.mockMvcPost("/coupon/findCouponRelease", param);
        //Assert.assertTrue(testResult.getData().contains(promoActivity.getActivityCode()));
    }

    private void queryCouponRelease(UpdateCouponActivityParam promoActivity) {

        QueryCouponReleaseParam param = new QueryCouponReleaseParam();

        param.setDomainCode(promoActivity.getDomainCode());
        param.setTenantCode(promoActivity.getTenantCode());
        param.setActivityCode(promoActivity.getActivityCode());
        param.setPageCount(3);
        param.setPageNo(0);

        TestResult testResult = super.mockMvcPost("/coupon/queryCouponRelease", param);

        //Assert.assertTrue(testResult.getData().contains(promoActivity.getActivityCode()));
    }

    private void cancelCouponRelease(UpdateCouponActivityParam promoActivity, String releaseCode) {

        CancelCouponReleaseParam param = new CancelCouponReleaseParam();

        param.setDomainCode(promoActivity.getDomainCode());
        param.setTenantCode(promoActivity.getTenantCode());
        param.setReleaseCode(releaseCode);

        super.mockMvcPost("/coupon/cancelCouponRelease", param);
    }

    private String sendCouponToUser(UpdateCouponActivityParam promoActivity, String userCode) {
        
        SendCouponToUserParam param = new SendCouponToUserParam();

        param.setDomainCode(promoActivity.getDomainCode());
        param.setTenantCode(promoActivity.getTenantCode());
        param.setActivityCode(promoActivity.getActivityCode());
        param.setUserCode(userCode);
        param.setTakeLabel(TakeLabelEnum.OPS.code());

        TestResult testResult = super.mockMvcPost("/coupon/sendCouponToUser", param);
        return testResult.getData();
    }

    private void sendCouponToUserList(UpdateCouponActivityParam promoActivity) {
        
        SendCouponToUserListParam param = new SendCouponToUserListParam();

        param.setDomainCode(promoActivity.getDomainCode());
        param.setTenantCode(promoActivity.getTenantCode());
        param.setActivityCode(promoActivity.getActivityCode());
        param.setUserList(Arrays.asList(
            MemberInfo.builder().userCode("9999999901").build(),
            MemberInfo.builder().userCode("9999999902").build(),
            MemberInfo.builder().userCode("9999999903").build(),
            MemberInfo.builder().userCode("9999999904").build(),
            MemberInfo.builder().userCode("9999999905").build()));
        param.setReceiveCount(2);
        param.setTakeLabel(TakeLabelEnum.OPS.code());

        super.mockMvcPost("/coupon/sendCouponToUserList", param);
    }

    protected void sendCouponToUserList(String tenantCode, String activityCode, List<String> userCodes) {
        
        SendCouponToUserListParam param = new SendCouponToUserListParam();

        param.setDomainCode("DC0001");
        param.setTenantCode(tenantCode);
        param.setActivityCode(activityCode);
        param.setUserList(userCodes.stream().map(e -> MemberInfo.builder().userCode(e).build()).collect(Collectors.toList()));
        param.setReceiveCount(1);
        param.setTakeLabel(TakeLabelEnum.OPS.code());

        super.mockMvcPost("/coupon/sendCouponToUserList", param);
    }

    private void queryCouponListByUser() {
        
        QueryCouponListByUserParam param = new QueryCouponListByUserParam();

        param.setDomainCode(ActivityHelper.domainCode);
        param.setTenantCode(ActivityHelper.tenantCode);
        param.setUserCode("101912091551203528");
        
        TestResult testResult = super.mockMvcPost("/coupon/queryCouponListByUser", param);
        System.err.println(testResult.getData());
    }

}
  
