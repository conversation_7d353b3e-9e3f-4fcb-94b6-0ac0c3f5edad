package com.gtech.promotion.controller.flashsale;

import com.gtech.commons.result.Result;
import com.gtech.promotion.component.flashsale.DataSyncComponent;
import com.gtech.promotion.component.flashsale.FlashSaleComponent;
import com.gtech.promotion.controller.flashsale.FlashSaleController;
import com.gtech.promotion.exception.PromotionException;
import com.gtech.promotion.exception.PromotionParamValidateException;
import com.gtech.promotion.vo.bean.IncentiveLimited;
import com.gtech.promotion.vo.bean.marketing.MarketingGroup;
import com.gtech.promotion.vo.bean.marketing.MarketingLanguage;
import com.gtech.promotion.vo.bean.marketing.flashsale.FlashSaleProduct;
import com.gtech.promotion.vo.param.marketing.flashsale.FlashSaleCreateParam;
import com.gtech.promotion.vo.param.marketing.flashsale.FlashSaleFindParam;
import com.gtech.promotion.vo.param.marketing.flashsale.FlashSaleUpdateParam;
import com.gtech.promotion.vo.param.marketing.flashsale.QueryFlashSalePriceByProductSyncParam;
import com.gtech.promotion.vo.result.flashsale.FlashSaleFindResult;
import com.gtech.promotion.vo.result.flashsale.QueryFlashSalePriceByProductResult;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

@RunWith(MockitoJUnitRunner.class)
public class FlashSaleControllerTest {

    @InjectMocks
    private FlashSaleController flashSaleController;

    @Mock
    private FlashSaleComponent flashSaleComponent;
    @Mock
    private DataSyncComponent dataSyncComponent;



    @Test
    public void createFlashSale1() {
        FlashSaleCreateParam param = new FlashSaleCreateParam();
        param.setDomainCode("1");
        param.setTenantCode("1");
        param.setOrgCode("1");
        param.setActivityType("06");
        param.setOpsType("301");
        param.setActivityName("1");
        param.setActivityBegin("20210816171302");
        param.setActivityEnd("21990816171302");
        param.setSponsors("1");
        param.setOperateUser("1");
        param.setImportNo("1");
        List<MarketingLanguage> marketingLanguages = new ArrayList<>();
        MarketingLanguage marketingLanguage = new MarketingLanguage();
        marketingLanguage.setLanguage("zh-CN");
        marketingLanguages.add(marketingLanguage);
        param.setMarketingLanguages(marketingLanguages);
        List<FlashSaleProduct> products = new ArrayList<>();
        FlashSaleProduct product = new FlashSaleProduct();
        product.setFlashPrice(BigDecimal.TEN);
        product.setListPrice(BigDecimal.TEN);
        product.setSalePrice(BigDecimal.TEN);
        product.setMaxPerUser(1);
        product.setSkuCode("1");
        product.setSkuName("1");
        product.setSkuQuota(1);
        products.add(product);
        param.setProducts(products);

        List<IncentiveLimited> limitedList = new ArrayList<>();

        IncentiveLimited limited = new IncentiveLimited();
        limited.setLimitationValue(new BigDecimal(1));
        limited.setLimitationCode("01");
        limitedList.add(limited);
        param.setIncentiveLimitedFlag("01");
        param.setIncentiveLimiteds(limitedList);

        Result<String> result = flashSaleController.createFlashSale(param);
        Assert.assertEquals(null, result.getData());
    }



    @Test(expected = PromotionException.class)
    public void createFlashSale11() {
        FlashSaleCreateParam param = new FlashSaleCreateParam();
        param.setDomainCode("1");
        param.setTenantCode("1");
        param.setOrgCode("1");
        param.setActivityType("06");
        param.setOpsType("301");
        param.setActivityName("1");
        param.setActivityBegin("20210816171302");
        param.setActivityEnd("21990816171302");
        param.setSponsors("1");
        param.setOperateUser("1");
        param.setImportNo("1");
        List<MarketingLanguage> marketingLanguages = new ArrayList<>();
        MarketingLanguage marketingLanguage = new MarketingLanguage();
        marketingLanguage.setLanguage("zh-CN");
        marketingLanguages.add(marketingLanguage);
        param.setMarketingLanguages(marketingLanguages);
        List<FlashSaleProduct> products = new ArrayList<>();
        FlashSaleProduct product = new FlashSaleProduct();
        product.setFlashPrice(BigDecimal.TEN);
        product.setGroupLeaderPrice(BigDecimal.TEN);
        product.setListPrice(BigDecimal.ONE);
        product.setSalePrice(BigDecimal.TEN);
        product.setMaxPerUser(1);
        product.setSkuCode("1");
        product.setSkuName("1");
        product.setSkuQuota(1);
        products.add(product);
        param.setProducts(products);

        param.setIncentiveLimitedFlag("01");

//        param.setIncentiveLimitedList();

        Result<String> result = flashSaleController.createFlashSale(param);
        Assert.assertEquals(null, result.getData());
    }


    @Test
    public void createFlashSale() {
        FlashSaleCreateParam param = new FlashSaleCreateParam();
        param.setDomainCode("1");
        param.setTenantCode("1");
        param.setOrgCode("1");
        param.setActivityType("06");
        param.setOpsType("301");
        param.setActivityName("1");
        param.setActivityBegin("20210816171302");
        param.setActivityEnd("21990816171302");
        param.setSponsors("1");
        param.setOperateUser("1");
        param.setImportNo("1");
        List<MarketingLanguage> marketingLanguages = new ArrayList<>();
        MarketingLanguage marketingLanguage = new MarketingLanguage();
        marketingLanguage.setLanguage("zh-CN");
        marketingLanguages.add(marketingLanguage);
        param.setMarketingLanguages(marketingLanguages);
        List<FlashSaleProduct> products = new ArrayList<>();
        FlashSaleProduct product = new FlashSaleProduct();
        product.setFlashPrice(BigDecimal.TEN);
        product.setListPrice(BigDecimal.TEN);
        product.setSalePrice(BigDecimal.TEN);
        product.setMaxPerUser(1);
        product.setSkuCode("1");
        product.setSkuName("1");
        product.setSkuQuota(1);
        products.add(product);
        param.setProducts(products);

        Result<String> result = flashSaleController.createFlashSale(param);
        Assert.assertEquals(null, result.getData());
    }

    @Test(expected = PromotionParamValidateException.class)
    public void createFlashSale_group3() {
        FlashSaleCreateParam param = new FlashSaleCreateParam();
        param.setDomainCode("1");
        param.setTenantCode("1");
        param.setOrgCode("1");
        param.setActivityType("06");
        param.setOpsType("601");
        param.setActivityName("1");
        param.setActivityBegin("20210816171302");
        param.setActivityEnd("21990816171302");
        param.setSponsors("1");
        param.setOperateUser("1");
        param.setImportNo("1");
        List<MarketingLanguage> marketingLanguages = new ArrayList<>();
        MarketingLanguage marketingLanguage = new MarketingLanguage();
        marketingLanguage.setLanguage("zh-CN");
        marketingLanguages.add(marketingLanguage);
        param.setMarketingLanguages(marketingLanguages);

        MarketingGroup marketingGroup = new MarketingGroup();
        marketingGroup.setGroupSize(1);
        param.setMarketingGroup(marketingGroup);
        Result<String> result = flashSaleController.createFlashSale(param);
        Assert.assertEquals(null, result.getData());
    }

    @Test(expected = PromotionParamValidateException.class)
    public void createFlashSale_group4() {
        FlashSaleCreateParam param = new FlashSaleCreateParam();
        param.setDomainCode("1");
        param.setTenantCode("1");
        param.setOrgCode("1");
        param.setActivityType("06");
        param.setOpsType("601");
        param.setActivityName("1");
        param.setActivityBegin("20210816171302");
        param.setActivityEnd("21990816171302");
        param.setSponsors("1");
        param.setOperateUser("1");
        param.setImportNo("1");
        List<MarketingLanguage> marketingLanguages = new ArrayList<>();
        MarketingLanguage marketingLanguage = new MarketingLanguage();
        marketingLanguage.setLanguage("zh-CN");
        marketingLanguages.add(marketingLanguage);
        param.setMarketingLanguages(marketingLanguages);

        MarketingGroup marketingGroup = new MarketingGroup();

        param.setMarketingGroup(marketingGroup);
        Result<String> result = flashSaleController.createFlashSale(param);
        Assert.assertEquals(null, result.getData());
    }

    @Test(expected = PromotionParamValidateException.class)
    public void createFlashSale_group5() {
        FlashSaleCreateParam param = new FlashSaleCreateParam();
        param.setDomainCode("1");
        param.setTenantCode("1");
        param.setOrgCode("1");
        param.setActivityType("06");
        param.setOpsType("601");
        param.setActivityName("1");
        param.setActivityBegin("20210816171302");
        param.setActivityEnd("21990816171302");
        param.setSponsors("1");
        param.setOperateUser("1");
        param.setImportNo("1");
        List<MarketingLanguage> marketingLanguages = new ArrayList<>();
        MarketingLanguage marketingLanguage = new MarketingLanguage();
        marketingLanguage.setLanguage("zh-CN");
        marketingLanguages.add(marketingLanguage);
        param.setMarketingLanguages(marketingLanguages);

        MarketingGroup marketingGroup = new MarketingGroup();

        param.setMarketingGroup(marketingGroup);
        Result<String> result = flashSaleController.createFlashSale(param);
        Assert.assertEquals(null, result.getData());
    }

    @Test(expected = PromotionParamValidateException.class)
    public void createFlashSale_group1() {
        FlashSaleCreateParam param = new FlashSaleCreateParam();
        param.setDomainCode("1");
        param.setTenantCode("1");
        param.setOrgCode("1");
        param.setActivityType("06");
        param.setOpsType("601");
        param.setActivityName("1");
        param.setActivityBegin("20210816171302");
        param.setActivityEnd("21990816171302");
        param.setSponsors("1");
        param.setOperateUser("1");
        param.setImportNo("1");
        List<MarketingLanguage> marketingLanguages = new ArrayList<>();
        MarketingLanguage marketingLanguage = new MarketingLanguage();
        marketingLanguage.setLanguage("zh-CN");
        marketingLanguages.add(marketingLanguage);
        param.setMarketingLanguages(marketingLanguages);

        MarketingGroup marketingGroup = new MarketingGroup();
        marketingGroup.setEffectiveHour(2);
        marketingGroup.setGroupSize(1);
        param.setMarketingGroup(marketingGroup);
        Result<String> result = flashSaleController.createFlashSale(param);
        Assert.assertEquals(null, result.getData());
    }

    @Test(expected = PromotionParamValidateException.class)
    public void createFlashSale_group2() {
        FlashSaleCreateParam param = new FlashSaleCreateParam();
        param.setDomainCode("1");
        param.setTenantCode("1");
        param.setOrgCode("1");
        param.setActivityType("06");
        param.setOpsType("601");
        param.setActivityName("1");
        param.setActivityBegin("20210816171302");
        param.setActivityEnd("21990816171302");
        param.setSponsors("1");
        param.setOperateUser("1");
        param.setImportNo("1");
        List<MarketingLanguage> marketingLanguages = new ArrayList<>();
        MarketingLanguage marketingLanguage = new MarketingLanguage();
        marketingLanguage.setLanguage("zh-CN");
        marketingLanguages.add(marketingLanguage);
        param.setMarketingLanguages(marketingLanguages);

        MarketingGroup marketingGroup = new MarketingGroup();
        marketingGroup.setEffectiveHour(2);
        marketingGroup.setGroupSize(1);
        param.setMarketingGroup(marketingGroup);
        Result<String> result = flashSaleController.createFlashSale(param);
        Assert.assertEquals(null, result.getData());
    }

    @Test
    public void createFlashSale_group() {
        FlashSaleCreateParam param = new FlashSaleCreateParam();
        param.setDomainCode("1");
        param.setTenantCode("1");
        param.setOrgCode("1");
        param.setActivityType("03");
        param.setOpsType("301");
        param.setActivityName("1");
        param.setActivityBegin("20210816171302");
        param.setActivityEnd("21990816171302");
        param.setSponsors("1");
        param.setOperateUser("1");
        param.setImportNo("1");
        List<MarketingLanguage> marketingLanguages = new ArrayList<>();
        MarketingLanguage marketingLanguage = new MarketingLanguage();
        marketingLanguage.setLanguage("zh-CN");
        marketingLanguages.add(marketingLanguage);
        param.setMarketingLanguages(marketingLanguages);

        List<FlashSaleProduct> products = new ArrayList<>();
        FlashSaleProduct product = new FlashSaleProduct();
        product.setSalePrice(new BigDecimal(1));
        product.setListPrice(new BigDecimal(1));
        product.setSalePrice(new BigDecimal(1));
        product.setMaxPerUser(1);
        product.setFlashPrice(new BigDecimal(1));
        product.setSkuQuota(1);
        product.setSkuCode("1");
        product.setSkuName("1");
        products.add(product);
        MarketingGroup marketingGroup = new MarketingGroup();
        marketingGroup.setEffectiveHour(1);
        marketingGroup.setGroupSize(2);
        param.setMarketingGroup(marketingGroup);
        param.setProducts(products);
        Result<String> result = flashSaleController.createFlashSale(param);
        Assert.assertEquals(null, result.getData());
    }

    @Test
    public void createFlashSale_group6() {
        FlashSaleCreateParam param = new FlashSaleCreateParam();
        param.setDomainCode("1");
        param.setTenantCode("1");
        param.setOrgCode("1");
        param.setActivityType("03");
        param.setOpsType("301");
        param.setActivityName("1");
        param.setActivityBegin("20210816171302");
        param.setActivityEnd("21990816171302");
        param.setSponsors("1");
        param.setOperateUser("1");
        param.setImportNo("");
        List<MarketingLanguage> marketingLanguages = new ArrayList<>();
        MarketingLanguage marketingLanguage = new MarketingLanguage();
        marketingLanguage.setLanguage("zh-CN");
        marketingLanguages.add(marketingLanguage);
        param.setMarketingLanguages(marketingLanguages);

        FlashSaleProduct flashSaleProduct = new FlashSaleProduct();
        flashSaleProduct.setFlashPrice(new BigDecimal(1));
        flashSaleProduct.setSalePrice(new BigDecimal(1));
        flashSaleProduct.setListPrice(new BigDecimal(1));
        flashSaleProduct.setSkuQuota(1);
        flashSaleProduct.setSkuName("1");
        flashSaleProduct.setMaxPerUser(1);
        flashSaleProduct.setGroupLeaderPrice(new BigDecimal(1));

        MarketingGroup marketingGroup = new MarketingGroup();
        marketingGroup.setEffectiveHour(1);
        marketingGroup.setGroupSize(2);
        marketingGroup.setShowLeaderPrice(1);
        param.setMarketingGroup(marketingGroup);
        ArrayList<FlashSaleProduct> objects = new ArrayList<>();
        objects.add(flashSaleProduct);
        param.setProducts(objects);

        param.setShippingTime("1");

        Result<String> result = flashSaleController.createFlashSale(param);
        Assert.assertEquals(null, result.getData());
    }
    @Test
    public void createFlashSale_group8() {
        FlashSaleCreateParam param = new FlashSaleCreateParam();
        param.setDomainCode("1");
        param.setTenantCode("1");
        param.setOrgCode("1");
        param.setActivityType("03");
        param.setOpsType("301");
        param.setActivityName("1");
        param.setActivityBegin("20210816171302");
        param.setActivityEnd("21990816171302");
        param.setSponsors("1");
        param.setOperateUser("1");
        param.setImportNo("");
        List<MarketingLanguage> marketingLanguages = new ArrayList<>();
        MarketingLanguage marketingLanguage = new MarketingLanguage();
        marketingLanguage.setLanguage("zh-CN");
        marketingLanguages.add(marketingLanguage);
        param.setMarketingLanguages(marketingLanguages);

        FlashSaleProduct flashSaleProduct = new FlashSaleProduct();
        flashSaleProduct.setFlashPrice(new BigDecimal(1));
        flashSaleProduct.setSalePrice(new BigDecimal(1));
        flashSaleProduct.setListPrice(new BigDecimal(1));
        flashSaleProduct.setSkuQuota(1);
        flashSaleProduct.setSkuName("1");
        flashSaleProduct.setMaxPerUser(1);

        MarketingGroup marketingGroup = new MarketingGroup();
        marketingGroup.setEffectiveHour(1);
        marketingGroup.setGroupSize(2);
        marketingGroup.setShowLeaderPrice(0);
        param.setMarketingGroup(marketingGroup);
        ArrayList<FlashSaleProduct> objects = new ArrayList<>();
        objects.add(flashSaleProduct);
        param.setProducts(objects);

        param.setShippingTime("1");

        Result<String> result = flashSaleController.createFlashSale(param);
        Assert.assertEquals(null, result.getData());
    }

    @Test
    public void updateFlashSale() {
        FlashSaleUpdateParam param = new FlashSaleUpdateParam();
        param.setDomainCode("1");
        param.setTenantCode("1");
        param.setOrgCode("1");
        param.setActivityType("03");
        param.setOpsType("301");
        param.setActivityName("1");
        param.setActivityBegin("20210816171302");
        param.setActivityEnd("21990816171302");
        param.setSponsors("1");
        param.setOperateUser("1");
        param.setActivityCode("1");
        param.setImportNo("1");
        List<MarketingLanguage> marketingLanguages = new ArrayList<>();
        MarketingLanguage marketingLanguage = new MarketingLanguage();
        marketingLanguage.setLanguage("zh-CN");
        marketingLanguages.add(marketingLanguage);
        param.setMarketingLanguages(marketingLanguages);

        List<FlashSaleProduct> products = new ArrayList<>();
        FlashSaleProduct product = new FlashSaleProduct();
        product.setFlashPrice(BigDecimal.TEN);
        product.setListPrice(BigDecimal.TEN);
        product.setSalePrice(BigDecimal.TEN);
        product.setMaxPerUser(1);
        product.setSkuCode("1");
        product.setSkuName("1");
        product.setSkuQuota(1);
        products.add(product);
        param.setProducts(products);

        Mockito.when(flashSaleComponent.updateFlashSale(Mockito.any())).thenReturn(1);
        Result<Serializable> result = flashSaleController.updateFlashSale(param);
        Assert.assertEquals(null, result.getData());
    }


    @Test
    public void updateFlashSale1() {
        FlashSaleUpdateParam param = new FlashSaleUpdateParam();
        param.setDomainCode("1");
        param.setTenantCode("1");
        param.setOrgCode("1");
        param.setActivityType("03");
        param.setOpsType("301");
        param.setActivityName("1");
        param.setActivityBegin("20210816171302");
        param.setActivityEnd("21990816171302");
        param.setSponsors("1");
        param.setOperateUser("1");
        param.setActivityCode("1");
        param.setImportNo("");
        List<MarketingLanguage> marketingLanguages = new ArrayList<>();
        MarketingLanguage marketingLanguage = new MarketingLanguage();
        marketingLanguage.setLanguage("zh-CN");
        marketingLanguages.add(marketingLanguage);
        param.setMarketingLanguages(marketingLanguages);

        List<FlashSaleProduct> products = new ArrayList<>();
        FlashSaleProduct product = new FlashSaleProduct();
        product.setFlashPrice(BigDecimal.TEN);
        product.setListPrice(BigDecimal.TEN);
        product.setSalePrice(BigDecimal.TEN);
        product.setMaxPerUser(1);
        product.setMaxPerUserFlag(1);
        product.setSkuCode("1");
        product.setSkuName("1");
        product.setSkuQuota(1);
        product.setLimitFlag(1);
        products.add(product);
        param.setProducts(products);

        Mockito.when(flashSaleComponent.updateFlashSale(Mockito.any())).thenReturn(1);
        Result<Serializable> result = flashSaleController.updateFlashSale(param);
        Assert.assertEquals(null, result.getData());
    }

    @Test
    public void updateFlashSale2() {
        FlashSaleUpdateParam param = new FlashSaleUpdateParam();
        param.setDomainCode("1");
        param.setTenantCode("1");
        param.setOrgCode("1");
        param.setActivityType("03");
        param.setOpsType("301");
        param.setActivityName("1");
        param.setActivityBegin("20210816171302");
        param.setActivityEnd("21990816171302");
        param.setSponsors("1");
        param.setOperateUser("1");
        param.setActivityCode("1");
        param.setImportNo("");
        List<MarketingLanguage> marketingLanguages = new ArrayList<>();
        MarketingLanguage marketingLanguage = new MarketingLanguage();
        marketingLanguage.setLanguage("zh-CN");
        marketingLanguages.add(marketingLanguage);
        param.setMarketingLanguages(marketingLanguages);

        List<FlashSaleProduct> products = new ArrayList<>();
        FlashSaleProduct product = new FlashSaleProduct();
        product.setFlashPrice(BigDecimal.TEN);
        product.setListPrice(BigDecimal.TEN);
        product.setSalePrice(BigDecimal.TEN);
        product.setMaxPerUser(1);
        product.setMaxPerUserFlag(0);
        product.setSkuCode("1");
        product.setSkuName("1");
        product.setSkuQuota(1);
        product.setLimitFlag(0);
        products.add(product);
        param.setProducts(products);

        Mockito.when(flashSaleComponent.updateFlashSale(Mockito.any())).thenReturn(1);
        Result<Serializable> result = flashSaleController.updateFlashSale(param);
        Assert.assertEquals(null, result.getData());
    }

    @Test
    public void findFlashSale() {
        FlashSaleFindParam param = new FlashSaleFindParam();
        param.setDomainCode("1");
        param.setTenantCode("1");
        param.setOrgCode("1");
        param.setActivityCode("1");
        Mockito.when(flashSaleComponent.findFlashSale(Mockito.any())).thenReturn(null);
        Result<FlashSaleFindResult> result = flashSaleController.findFlashSale(param);
        Assert.assertEquals(null, result.getData());
    }

    @Test
    public void queryFlashSalePriceByProductSync() {
        QueryFlashSalePriceByProductSyncParam param = new QueryFlashSalePriceByProductSyncParam();
        param.setTenantCode("1");
        List<QueryFlashSalePriceByProductSyncParam.Product> products = new ArrayList<>();
        QueryFlashSalePriceByProductSyncParam.Product product = new QueryFlashSalePriceByProductSyncParam.Product();
        product.setProductCode("1");
        product.setSkuCode("1");
        products.add(product);
        param.setProducts(products);
        Mockito.when(dataSyncComponent.queryFlashSalePriceByProduct(Mockito.any())).thenReturn(new ArrayList<>());
        Result<List<QueryFlashSalePriceByProductResult>> result = flashSaleController.queryFlashSalePriceByProductSync(param);
        Assert.assertEquals(0, result.getData().size());
    }

    @Test
    public void syncPriceTimer() {
        Mockito.doNothing().when(dataSyncComponent).syncPriceTimer();
        Result<Serializable> result = flashSaleController.syncPriceTimer();
        Assert.assertEquals(null, result.getData());
    }

}
