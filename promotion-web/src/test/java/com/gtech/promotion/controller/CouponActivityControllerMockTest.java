package com.gtech.promotion.controller;

import com.github.pagehelper.PageInfo;
import com.gtech.basic.masterdata.response.JsonResult;
import com.gtech.commons.result.PageResult;
import com.gtech.commons.result.Result;
import com.gtech.commons.utils.BeanCopyUtils;
import com.gtech.promotion.code.activity.ActivityStatusEnum;
import com.gtech.promotion.code.activity.ActivityTypeEnum;
import com.gtech.promotion.code.coupon.CouponStatusEnum;
import com.gtech.promotion.code.coupon.CouponTypeEnum;
import com.gtech.promotion.component.coupon.CouponActivityComponent;
import com.gtech.promotion.controller.coupon.CouponActivityController;
import com.gtech.promotion.dao.model.activity.ActivityLanguageModel;
import com.gtech.promotion.dao.model.activity.ActivityModel;
import com.gtech.promotion.dao.model.coupon.CouponReleaseModel;
import com.gtech.promotion.dao.model.coupon.TPromoCouponInnerCodeVO;
import com.gtech.promotion.dto.in.activity.TenantCodeInDTO;
import com.gtech.promotion.dto.in.coupon.CouponActivityInDTO;
import com.gtech.promotion.dto.in.coupon.UpdateCouponActivityInDTO;
import com.gtech.promotion.dto.out.coupon.*;
import com.gtech.promotion.exception.PromotionParamValidateException;
import com.gtech.promotion.feign.MasterDataFeignClient;
import com.gtech.promotion.service.activity.ActivityLanguageService;
import com.gtech.promotion.service.activity.ActivityService;
import com.gtech.promotion.service.coupon.PromoCouponCodeUserService;
import com.gtech.promotion.service.coupon.PromoCouponInnerCodeService;
import com.gtech.promotion.utils.DateUtil;
import com.gtech.promotion.vo.bean.ActivityLanguage;
import com.gtech.promotion.vo.param.activity.CustomCondition;
import com.gtech.promotion.vo.param.activity.FindActivityParam;
import com.gtech.promotion.vo.param.coupon.ExportCouponParam;
import com.gtech.promotion.vo.param.coupon.QueryCouponActivityListByMemberTagParam;
import com.gtech.promotion.vo.param.coupon.QueryCouponActivityListParam;
import com.gtech.promotion.vo.param.coupon.UpdateCouponActivityParam;
import com.gtech.promotion.vo.result.coupon.ExportCouponResult;
import com.gtech.promotion.vo.result.coupon.QueryCouponActivityListResult;
import org.apache.commons.lang.time.DateUtils;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

@RunWith(MockitoJUnitRunner.class)
public class CouponActivityControllerMockTest {

    @InjectMocks
    private CouponActivityController couponActivityController;
    @Mock
    private CouponActivityComponent couponActivityDomain;
    @Mock
    private PromoCouponInnerCodeService promoCouponInnerCodeService;
    @Mock
    private ActivityService activityService;
    @Mock
    private ActivityLanguageService activityLanguageService;
    @Mock
    private PromoCouponCodeUserService couponCodeUserService;


    @Mock
    private MasterDataFeignClient masterDataFeignClient;

    @Test
    public void queryExportCoupon(){
        ExportCouponParam exportCouponParam = new ExportCouponParam();
        exportCouponParam.setDomainCode("1");
        exportCouponParam.setTenantCode("1");
        exportCouponParam.setActivityCode("1");
        List<CouponReleaseModel> list = new ArrayList<>();
        CouponReleaseModel e = new CouponReleaseModel();
        e.setCouponType(CouponTypeEnum.ANONYMITY_COUPON.code());
        e.setReleaseCode("1");
        list.add(e);
        List<ExportCouponOutDTO> list1 = new ArrayList<>();
        ExportCouponOutDTO exportCouponOutDTO = new ExportCouponOutDTO();
        exportCouponOutDTO.setStatus(CouponStatusEnum.EXPIRE.code());
        exportCouponOutDTO.setReleaseCode("1");
        list1.add(exportCouponOutDTO);
        Mockito.when(couponActivityDomain.queryCouponReleaseList(Mockito.any())).thenReturn(list);
        Mockito.when(couponActivityDomain.exportCoupon(Mockito.any())).thenReturn(list1);
        Result<List<ExportCouponResult>> listResult = couponActivityController.queryExportCoupon(exportCouponParam);
        Assert.assertEquals(1, listResult.getData().size());
        e.setCouponType(CouponTypeEnum.PROMOTION_CODE.code());
        try {
            couponActivityController.queryExportCoupon(exportCouponParam);
        }catch (Exception e1){

        }
    }

    @Test
    public void queryExportCoupon_111(){
        ExportCouponParam exportCouponParam = new ExportCouponParam();
        exportCouponParam.setDomainCode("1");
        exportCouponParam.setTenantCode("1");
        exportCouponParam.setActivityCode("1");
        List<CouponReleaseModel> list = new ArrayList<>();

        CouponReleaseModel e3 = new CouponReleaseModel();
        e3.setCouponType(CouponTypeEnum.ANONYMITY_COUPON.code());
        e3.setCouponType("02");
        e3.setReleaseCode("3");
        list.add(e3);

        CouponReleaseModel e1 = new CouponReleaseModel();
        e1.setCouponType(CouponTypeEnum.ANONYMITY_COUPON.code());
        e1.setValidDays(1);
        e1.setCouponType("02");
        e1.setReleaseCode("2");
        list.add(e1);
        CouponReleaseModel e = new CouponReleaseModel();
        e.setCouponType(CouponTypeEnum.ANONYMITY_COUPON.code());
        e.setReleaseCode("1");
        list.add(e);

        List<ExportCouponOutDTO> list1 = new ArrayList<>();

        ExportCouponOutDTO exportCouponOutDTO2 = new ExportCouponOutDTO();
        exportCouponOutDTO2.setValidStartTime("111111111");
        exportCouponOutDTO2.setStatus(CouponStatusEnum.UN_GRANT.code());
        exportCouponOutDTO2.setReleaseCode("3");
        list1.add(exportCouponOutDTO2);

        ExportCouponOutDTO exportCouponOutDTO1 = new ExportCouponOutDTO();
        exportCouponOutDTO1.setValidStartTime("111111111");
        exportCouponOutDTO1.setStatus(CouponStatusEnum.UN_GRANT.code());
        exportCouponOutDTO1.setReleaseCode("2");
        list1.add(exportCouponOutDTO1);

        ExportCouponOutDTO exportCouponOutDTO = new ExportCouponOutDTO();
        exportCouponOutDTO.setStatus(CouponStatusEnum.EXPIRE.code());
        exportCouponOutDTO.setReleaseCode("1");
        list1.add(exportCouponOutDTO);
        Mockito.when(couponActivityDomain.queryCouponReleaseList(Mockito.any())).thenReturn(list);
        Mockito.when(couponActivityDomain.exportCoupon(Mockito.any())).thenReturn(list1);
        Result<List<ExportCouponResult>> listResult = couponActivityController.queryExportCoupon(exportCouponParam);
        Assert.assertEquals(2, listResult.getData().size());
    }

    @Test
    public void queryExportCoupon_not_validTime(){

        Calendar instance = Calendar.getInstance();
        instance.add(Calendar.DATE, 1);
        Date endDate = instance.getTime();
        String endTime = com.gtech.commons.utils.DateUtil.format(endDate, com.gtech.commons.utils.DateUtil.FORMAT_YYYYMMDDHHMISS_14);

        ExportCouponParam exportCouponParam = new ExportCouponParam();
        exportCouponParam.setDomainCode("1");
        exportCouponParam.setTenantCode("1");
        exportCouponParam.setActivityCode("1");
        List<CouponReleaseModel> list = new ArrayList<>();
        CouponReleaseModel e = new CouponReleaseModel();
        e.setCouponType(CouponTypeEnum.ANONYMITY_COUPON.code());
        e.setReleaseCode("1");
        e.setValidDays(1);
        e.setValidEndTime(endTime);
        e.setValidStartTime(endTime);
        e.setReceiveEndTime(endTime);
        list.add(e);
        List<ExportCouponOutDTO> list1 = new ArrayList<>();
        ExportCouponOutDTO exportCouponOutDTO = new ExportCouponOutDTO();
        exportCouponOutDTO.setStatus(CouponStatusEnum.EXPIRE.code());
        exportCouponOutDTO.setReleaseCode("1");
        list1.add(exportCouponOutDTO);
        Mockito.when(couponActivityDomain.queryCouponReleaseList(Mockito.any())).thenReturn(list);
        Mockito.when(couponActivityDomain.exportCoupon(Mockito.any())).thenReturn(list1);
        Result<List<ExportCouponResult>> listResult = couponActivityController.queryExportCoupon(exportCouponParam);
        Assert.assertEquals(0, listResult.getData().size());
        e.setCouponType(CouponTypeEnum.PROMOTION_CODE.code());
        try {
            couponActivityController.queryExportCoupon(exportCouponParam);
        }catch (Exception e1){

        }
    }

    @Test
    public void queryExportCoupon_validTime(){

        Calendar instance = Calendar.getInstance();
        instance.add(Calendar.DATE, 2);
        Date endDate = instance.getTime();
        String endTime = com.gtech.commons.utils.DateUtil.format(endDate, com.gtech.commons.utils.DateUtil.FORMAT_YYYYMMDDHHMISS_14);

        ExportCouponParam exportCouponParam = new ExportCouponParam();
        exportCouponParam.setDomainCode("1");
        exportCouponParam.setTenantCode("1");
        exportCouponParam.setActivityCode("1");
        List<CouponReleaseModel> list = new ArrayList<>();
        CouponReleaseModel e = new CouponReleaseModel();
        e.setCouponType(CouponTypeEnum.ANONYMITY_COUPON.code());
        e.setReleaseCode("1");
        e.setValidDays(1);
        e.setReceiveEndTime(endTime);
        list.add(e);
        List<ExportCouponOutDTO> list1 = new ArrayList<>();
        ExportCouponOutDTO exportCouponOutDTO = new ExportCouponOutDTO();
        exportCouponOutDTO.setStatus(CouponStatusEnum.EXPIRE.code());
        exportCouponOutDTO.setReleaseCode("1");
        list1.add(exportCouponOutDTO);
        Mockito.when(couponActivityDomain.queryCouponReleaseList(Mockito.any())).thenReturn(list);
        Mockito.when(couponActivityDomain.exportCoupon(Mockito.any())).thenReturn(list1);
        Result<List<ExportCouponResult>> listResult = couponActivityController.queryExportCoupon(exportCouponParam);
        Assert.assertEquals(0, listResult.getData().size());
        e.setCouponType(CouponTypeEnum.PROMOTION_CODE.code());
        try {
            couponActivityController.queryExportCoupon(exportCouponParam);
        }catch (Exception e1){

        }
    }

    @Test
    public void queryExportCoupon_validTime_receiveEndTime(){

        Calendar instance = Calendar.getInstance();
        instance.add(Calendar.DATE, 2);
        Date endDate = instance.getTime();
        String endTime = com.gtech.commons.utils.DateUtil.format(endDate, com.gtech.commons.utils.DateUtil.FORMAT_YYYYMMDDHHMISS_14);

        ExportCouponParam exportCouponParam = new ExportCouponParam();
        exportCouponParam.setDomainCode("1");
        exportCouponParam.setTenantCode("1");
        exportCouponParam.setActivityCode("1");
        List<CouponReleaseModel> list = new ArrayList<>();
        CouponReleaseModel e = new CouponReleaseModel();
        e.setCouponType(CouponTypeEnum.ANONYMITY_COUPON.code());
        e.setReleaseCode("1");
        e.setValidDays(3);
        e.setReceiveEndTime(endTime);
        list.add(e);
        List<ExportCouponOutDTO> list1 = new ArrayList<>();
        ExportCouponOutDTO exportCouponOutDTO = new ExportCouponOutDTO();
        exportCouponOutDTO.setStatus(CouponStatusEnum.EXPIRE.code());
        exportCouponOutDTO.setReleaseCode("1");
        list1.add(exportCouponOutDTO);
        Mockito.when(couponActivityDomain.queryCouponReleaseList(Mockito.any())).thenReturn(list);
        Mockito.when(couponActivityDomain.exportCoupon(Mockito.any())).thenReturn(list1);
        Result<List<ExportCouponResult>> listResult = couponActivityController.queryExportCoupon(exportCouponParam);
        Assert.assertEquals(0, listResult.getData().size());
        e.setCouponType(CouponTypeEnum.PROMOTION_CODE.code());
        try {
            couponActivityController.queryExportCoupon(exportCouponParam);
        }catch (Exception e1){

        }
    }

    @Test
    public void queryExportCoupon_validTime_receiveEndTime_1(){

        Calendar instance = Calendar.getInstance();
        instance.add(Calendar.DATE, 2);
        Date endDate = instance.getTime();
        String endTime = com.gtech.commons.utils.DateUtil.format(endDate, com.gtech.commons.utils.DateUtil.FORMAT_YYYYMMDDHHMISS_14);

        ExportCouponParam exportCouponParam = new ExportCouponParam();
        exportCouponParam.setDomainCode("1");
        exportCouponParam.setTenantCode("1");
        exportCouponParam.setActivityCode("1");
        List<CouponReleaseModel> list = new ArrayList<>();
        CouponReleaseModel e = new CouponReleaseModel();
        e.setCouponType(CouponTypeEnum.ANONYMITY_COUPON.code());
        e.setReleaseCode("1");
        e.setValidDays(3);
        e.setReceiveEndTime("20221114140102");
        list.add(e);
        List<ExportCouponOutDTO> list1 = new ArrayList<>();
        ExportCouponOutDTO exportCouponOutDTO = new ExportCouponOutDTO();
        exportCouponOutDTO.setStatus(CouponStatusEnum.EXPIRE.code());
        exportCouponOutDTO.setReleaseCode("1");
        list1.add(exportCouponOutDTO);
        Mockito.when(couponActivityDomain.queryCouponReleaseList(Mockito.any())).thenReturn(list);
        Mockito.when(couponActivityDomain.exportCoupon(Mockito.any())).thenReturn(list1);

        Result<List<ExportCouponResult>> listResult = couponActivityController.queryExportCoupon(exportCouponParam);
        Assert.assertEquals(0, listResult.getData().size());
        e.setCouponType(CouponTypeEnum.PROMOTION_CODE.code());
        try {
            couponActivityController.queryExportCoupon(exportCouponParam);
        }catch (Exception e1){

        }
    }


    @Test
    public void updateCouponActivity(){

        UpdateCouponActivityParam updateCouponActivityParam = new UpdateCouponActivityParam();
        updateCouponActivityParam.setActivityCode("666888");
        updateCouponActivityParam.setCouponType(CouponTypeEnum.PROMOTION_COUPON.code());
        updateCouponActivityParam.setTotalQuantity("1");
        updateCouponActivityParam.setDomainCode("1");
        updateCouponActivityParam.setTemplateCode("0101020203030404");
        updateCouponActivityParam.setTenantCode("1");
        updateCouponActivityParam.setConditionProductType("01");
        Date date = DateUtils.addDays(new Date(), 1);
        String dateString = DateUtil.getDateString(date, DateUtil.DATETIMESTOREFORMAT);
        updateCouponActivityParam.setActivityBegin("20210425000000");
        updateCouponActivityParam.setActivityEnd(dateString);
        ActivityLanguage activityLanguage = new ActivityLanguage();
        activityLanguage.setLanguage("zh");
        List<ActivityLanguage> activityLanguages = new ArrayList<>();
        activityLanguages.add(activityLanguage);
        updateCouponActivityParam.setActivityLanguages(activityLanguages);
        updateCouponActivityParam.setActivityType(ActivityTypeEnum.COUPON.code());
        UpdateCouponActivityInDTO updateDTO = BeanCopyUtils.jsonCopyBean(updateCouponActivityParam, UpdateCouponActivityInDTO.class);
        updateDTO.setUpdateUser(updateCouponActivityParam.getOperateUser());
        Mockito.when(couponActivityDomain.updateCouponActivity(Mockito.any())).thenReturn("1");
        Result<String> stringResult = couponActivityController.updateCouponActivity(updateCouponActivityParam);
        Assert.assertEquals("1", stringResult.getData());
    }

    @Test
    public void queryCouponActivityList(){

        QueryCouponActivityListParam param = new QueryCouponActivityListParam();
        param.setActivityCode("666888");
        param.setCouponType(CouponTypeEnum.PROMOTION_COUPON.code());
        param.setDomainCode("1");
        param.setTenantCode("1");
        Date date = DateUtils.addDays(new Date(), 1);
        String dateString = DateUtil.getDateString(date, DateUtil.DATETIMESTOREFORMAT);
        param.setActivityBeginTo(dateString);
        param.setActivityEndFrom("20210425000000");
        param.setActivityEndTo(dateString);
        param.setActivityEndFrom("20210425000000");
        PageInfo<CouponActivityListOutDTO> couponActivityList = new PageInfo<>();
        Mockito.when(couponActivityDomain.queryCouponActivityList(Mockito.any())).thenReturn(couponActivityList);

        PageResult<QueryCouponActivityListResult> resultPageResult = couponActivityController.queryCouponActivityList(param);
        Assert.assertNotNull(resultPageResult);
    }

    @Test
    public void queryCouponActivityListByMemberTag(){

        QueryCouponActivityListByMemberTagParam param = new QueryCouponActivityListByMemberTagParam();
        param.setActivityCode("666888");
        param.setCouponType(CouponTypeEnum.PROMOTION_COUPON.code());
        param.setDomainCode("1");
        param.setTenantCode("1");
        Date date = DateUtils.addDays(new Date(), 1);
        String dateString = DateUtil.getDateString(date, DateUtil.DATETIMESTOREFORMAT);
        param.setActivityBeginTo(dateString);
        param.setActivityEndFrom("20210425000000");
        param.setActivityEndTo(dateString);
        param.setActivityEndFrom("20210425000000");
        List<String> memberTagList = new ArrayList<>();
        memberTagList.add("1");
        param.setMemberTagList(memberTagList);
        PageInfo<CouponActivityListOutDTO> couponActivityList = new PageInfo<>();
        Mockito.when(couponActivityDomain.queryCouponActivityList(Mockito.any())).thenReturn(couponActivityList);

        PageResult<QueryCouponActivityListResult> resultPageResult = couponActivityController.queryCouponActivityListByQualification(param);
    }

    @Test
    public void findCouponActivity(){

        FindActivityParam param = new FindActivityParam();
        param.setActivityCode("666888");
        param.setDomainCode("1");
        param.setTenantCode("1");
        CouponActivityOutDTO dto = new CouponActivityOutDTO();
        dto.setActivityCode("666888");
        Mockito.when(couponActivityDomain.findCouponActivityOutDetail(Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(dto);
        Result<CouponActivityOutDTO> couponActivity = couponActivityController.findCouponActivity(param);
        Assert.assertEquals("666888",couponActivity.getData().getActivityCode());
    }

    @Test
    public void queryCouponActivity(){
        CouponActivityInDTO param = new CouponActivityInDTO();
        param.setPromotionCode("11");
        param.setLanguage("zh");
        param.setTenantCode("1");

        ActivityModel activityVO = new ActivityModel();
        activityVO.setActivityStatus(ActivityStatusEnum.END.code());
        ActivityLanguageModel activityLanguage = new ActivityLanguageModel();
        TPromoCouponInnerCodeVO vo = new TPromoCouponInnerCodeVO();
        Mockito.when(promoCouponInnerCodeService.findCouponByCouponCode(Mockito.any(),Mockito.any())).thenReturn(vo);
        Mockito.when(activityService.findActivity(Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(activityVO);
        Mockito.when(activityLanguageService.findActivityLanguage(Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(activityLanguage);
        Result<CouponActivityCodeOutDTO> result = couponActivityController.queryCouponActivity(param);
        Assert.assertNotNull(result.getData());

        try {
            Mockito.when(promoCouponInnerCodeService.findCouponByCouponCode(Mockito.any(),Mockito.any())).thenReturn(null);
            couponActivityController.queryCouponActivity(param);
        }catch (Exception e){

        }


    }

    @Test
    public void updateCouponCode(){
        CouponActivityInDTO param = new CouponActivityInDTO();
        param.setTenantCode("11");
        param.setPromotionCode("11");
        param.setLanguage("zh");
        Mockito.when(promoCouponInnerCodeService.updateInnerCodeByCode(Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(1);
        Mockito.when(couponCodeUserService.updatePromotionCode(Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(1);
        Result<String> stringResult = couponActivityController.updateCouponCode(param);
        Assert.assertEquals("更新成功",stringResult.getData());

        try {
            couponActivityController.updateCouponCode(null);
        }catch (Exception e){

        }
    }

    @Test
    public void queryCouponActivityByTenantCode(){
        TenantCodeInDTO param = new TenantCodeInDTO();
        param.setChannelCode("11");
        param.setLanguage("zh");
        param.setOrgCode("org");
        param.setTenantCode("11");
        List<CouponActivityDTO> list = new ArrayList<>();
        Mockito.when(couponActivityDomain.queryCouponActivityByTenantCode(Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(list);
        Result<List<CouponActivityDTO>> result = couponActivityController.queryCouponActivityByTenantCode(param);
        Assert.assertNotNull(result);
    }

    @Test
    public void checkCustomCondition_empty(){

        List<CustomCondition> customConditions = new ArrayList<>();
        CustomCondition customCondition = new CustomCondition();

        customCondition.setCustomValue("1");
        customCondition.setCustomValueList(new ArrayList<>());
        customCondition.setCustomKey("1");

        JsonResult<String> jsonResult = new JsonResult<>();
        jsonResult.setData("1");
        jsonResult.setSuccess(true);
        couponActivityController.checkCustomCondition(customConditions,"1");

    }

    @Test
    public void checkCustomCondition_not_empty_1(){

        List<CustomCondition> customConditions = new ArrayList<>();
        CustomCondition customCondition = new CustomCondition();

        customCondition.setCustomValue("1");
        customCondition.setCustomValueList(new ArrayList<>());
        customCondition.setCustomKey("1");
        customConditions.add(customCondition);

        JsonResult<String>  jsonResult = new JsonResult<>();
        jsonResult.setData("1");
        jsonResult.setSuccess(true);

        Mockito.when(masterDataFeignClient.getValueValue(Mockito.anyString(),Mockito.anyString())).thenReturn(jsonResult);
        couponActivityController.checkCustomCondition(customConditions,"1");

    }

    @Test(expected = PromotionParamValidateException.class)
    public void checkCustomCondition_not_empty_2(){

        List<CustomCondition> customConditions = new ArrayList<>();

        CustomCondition customCondition1 = new CustomCondition();

        customCondition1.setCustomValue("1");
        customCondition1.setCustomValueList(new ArrayList<>());
        customCondition1.setCustomKey("1");
        customConditions.add(customCondition1);

        CustomCondition customCondition = new CustomCondition();

        customCondition.setCustomValue("1");
        customCondition.setCustomValueList(new ArrayList<>());
        customCondition.setCustomKey("1");
        customConditions.add(customCondition);

        JsonResult<String>  jsonResult = new JsonResult<>();
        jsonResult.setData("1");
        jsonResult.setSuccess(true);

        Mockito.when(masterDataFeignClient.getValueValue(Mockito.anyString(),Mockito.anyString())).thenReturn(jsonResult);
        couponActivityController.checkCustomCondition(customConditions,"1");

    }
}
