package com.gtech.promotion.controller.point2;

import com.gtech.commons.page.PageData;
import com.gtech.commons.result.PageResult;
import com.gtech.commons.result.Result;
import com.gtech.promotion.component.coupon.RegularDeductionOfExpiredPointsComponent;
import com.gtech.promotion.controller.point.PointTransactionController;
import com.gtech.promotion.service.point.PointTransactionService;
import com.gtech.promotion.vo.param.point.query.GetPointTransactionParam;
import com.gtech.promotion.vo.param.point.query.QueryPointTransactionParam;
import com.gtech.promotion.vo.result.point.PointTransactionResult;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2021-09-08 14:40
 */
@RunWith(MockitoJUnitRunner.class)
public class PointTransactionControllerMockTest {
    @InjectMocks
    private PointTransactionController pointTransactionController;
    @Mock
    private PointTransactionService pointTransactionService;
    @Mock
    private RegularDeductionOfExpiredPointsComponent regularDeductionOfExpiredPointsComponent;

    @Test
    public void queryPointTransactionList(){
        QueryPointTransactionParam param = new QueryPointTransactionParam();
        param.setDomainCode("1");
        param.setTenantCode("1");
        Mockito.when(pointTransactionService.queryPointTransactionPage(Mockito.any())).thenReturn(new PageData<>());
        PageResult<PointTransactionResult> result = pointTransactionController.queryPointTransactionList(param);
        Assert.assertTrue(result.isSuccess());
    }

    @Test
    public void getPointTransaction(){
        GetPointTransactionParam param = new GetPointTransactionParam();
        param.setDomainCode("1");
        param.setTenantCode("1");
        param.setTransactionSn("1");
        Mockito.when(pointTransactionService.getPointTransaction(Mockito.any())).thenReturn(new PointTransactionResult());
        Result<PointTransactionResult> pointCampaign = pointTransactionController.getPointTransaction(param);
        Assert.assertTrue(pointCampaign.isSuccess());
    }

    @Test
    public void regularDeductionOfExpiredPoints(){
        Mockito.doNothing().when(regularDeductionOfExpiredPointsComponent).regularDeductionOfExpiredPoints();
        Result<Serializable> serializableResult = pointTransactionController.regularDeductionOfExpiredPoints();
        Assert.assertTrue(serializableResult.isSuccess());
    }
}
