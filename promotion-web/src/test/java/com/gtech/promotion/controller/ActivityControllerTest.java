/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.controller;

import com.gtech.promotion.PromotionWebApplication;
import com.gtech.promotion.code.activity.IncentiveLimitedFlagEnum;
import com.gtech.promotion.code.activity.LimitationCodeEnum;
import com.gtech.promotion.helper.ActivityHelper;
import com.gtech.promotion.vo.bean.Giveaway;
import com.gtech.promotion.vo.bean.IncentiveLimited;
import com.gtech.promotion.vo.bean.ProductDetail;
import com.gtech.promotion.vo.param.activity.*;
import com.gtech.promotion.vo.result.activity.CalcShoppingCartResult;
import org.junit.Assert;
import org.junit.FixMethodOrder;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.junit.runners.MethodSorters;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;

/**
 * 
 *
 * <AUTHOR>
 * @Date 2020-02-22
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = { PromotionWebApplication.class })
@FixMethodOrder(MethodSorters.NAME_ASCENDING)
@ActiveProfiles("dev")
@Transactional
public class ActivityControllerTest extends OrderTests {

    @Test
    public void test000setup() {

        memberCode = super.randomCode(8);
        super.setup();
    }
    @Test
    public void test0200_IncentiveLimited() {
        super.setup();
        UpdatePromoActivityParam activityParam = ActivityHelper.buildActivity0102_0203_0302_0401_商品范围每满数量减金额();
        activityParam.setIncentiveLimitedFlag(IncentiveLimitedFlagEnum.YES.code());
        activityParam.setIncentiveLimiteds(Arrays.asList(
                IncentiveLimited.builder().limitationCode(LimitationCodeEnum.ACTIVITY_TOTAL_COUNT.code()).limitationValue(new BigDecimal(3)).build()));

        activityParam = this.createAcivity(activityParam);

        this.updateActivityStatus(activityParam, "02");
        this.updateActivityStatus(activityParam, "04");

        CalcShoppingCartParam cscParam = this.buildCalcShoppingCartParam(1, 1);
        List<CalcShoppingCartResult> cscResultList = this.calcShoppingCart(cscParam, null);
        BigDecimal promoDeductedAmount = this.sumPromoDeductedAmount(cscResultList);
        String orderNo = super.randomCode(8);
        this.createOrder(cscParam, orderNo, promoDeductedAmount, null, null);
        this.cancelOrder(orderNo);

        cscParam = this.buildCalcShoppingCartParam(2, 2);
        cscResultList = this.calcShoppingCart(cscParam, null);
        promoDeductedAmount = this.sumPromoDeductedAmount(cscResultList);
        orderNo = super.randomCode(8);
        this.createOrder(cscParam, orderNo, promoDeductedAmount, null, null);
        this.confirmOrder(orderNo);

        String errorCode = "10140240";
        TestResult testResult = null;

        cscParam = this.buildCalcShoppingCartParam(2, 2);
        cscResultList = this.calcShoppingCart(cscParam, null);
        promoDeductedAmount = this.sumPromoDeductedAmount(cscResultList);
        orderNo = super.randomCode(8);
        testResult = this.createOrder(cscParam, orderNo, promoDeductedAmount, null, errorCode);
//        Assert.assertTrue(errorCode.equals(testResult.getCode()));

        cscParam = this.buildCalcShoppingCartParam(1, 1);
        cscResultList = this.calcShoppingCart(cscParam, null);
        promoDeductedAmount = this.sumPromoDeductedAmount(cscResultList);
        orderNo = super.randomCode(8);
        this.createOrder(cscParam, orderNo, promoDeductedAmount, null, null);
        this.confirmOrder(orderNo);
    }

    @Test
    public void test0201_0102020203020406_商品范围满数量送赠品() {
        super.setup();
        UpdatePromoActivityParam activityParam = this.createAcivity(ActivityHelper.buildActivity0102_0202_0302_0406_商品范围满数量送赠品());
        this.updateActivityStatus(activityParam, "02");
        this.updateActivityStatus(activityParam, "04");

        CalcShoppingCartParam cscParam = this.buildCalcShoppingCartParam(2, 2);
        List<CalcShoppingCartResult> cscResultList = this.calcShoppingCart(cscParam, null);
        BigDecimal incentiveAmount = this.sumPromoDeductedAmount(cscResultList);

        // 订单赠品信息
        List<CreateOrderParam.PromoGiveaway> orderGifts = Arrays.asList(
                CreateOrderParam.PromoGiveaway.builder()
                        .activityCode(activityParam.getActivityCode())
                        .giveaways(Arrays.asList(
                                Giveaway.builder().giveawayCode("GSC001").giveawayName("GSN001").giveawayNum(2).giveawayType(1).build()))
                        .build());

        // 创建订单
        String orderNo = super.randomCode(5);
        this.createOrder(cscParam, orderNo, incentiveAmount, orderGifts, null);
        // 取消订单
        this.cancelOrder(orderNo);

        this.updateActivityStatus(activityParam, "07");
    }
    @Test
    public void test0100_Activities() throws InterruptedException{
        super.setup();
        this.createAcivity(ActivityHelper.buildActivity0101_0201_0301_0401_单品无条件减金额(null, Arrays.asList(ProductDetail.builder().seqNum(1).productCode("SPCode").spuName("SPName").build())));
        UpdatePromoActivityParam singleActivity = this.createAcivity(ActivityHelper.buildActivity0101_0201_0301_0401_单品无条件减金额(null, null));

        UpdatePromoActivityParam reduceActivity = this.createAcivity(ActivityHelper.buildActivity0102_0202_0302_0401_商品范围满数量减金额());
        this.updateActivityStatus(reduceActivity, "02");
        this.updateActivityStatus(reduceActivity, "04");

        UpdatePromoActivityParam discountActivity = this.createAcivity(ActivityHelper.buildActivity0102_0202_0302_0402_商品范围满数量打折扣());
        this.updateActivityStatus(discountActivity, "04");

        UpdatePromoActivityParam fixActivity = this.createAcivity(ActivityHelper.buildActivity0102_0202_0302_0404_商品范围满数量设为总计特价());
        this.updateActivityStatus(fixActivity, "04");

        UpdatePromoActivityParam freeMailActivity = this.createAcivity(ActivityHelper.buildActivity0103_0202_0302_0405_订单满数量包邮());
        this.updateActivityStatus(freeMailActivity, "04");
        
        UpdatePromoActivityParam sendActivity = this.createAcivity(ActivityHelper.buildActivity0102_0203_0302_0407_商品范围每满数量送同类商品());
        this.updateActivityStatus(sendActivity, "04");

        UpdatePromoActivityParam eachActivity = this.createAcivity(ActivityHelper.buildActivity0102_0205_0302_0404_多组商品范围各选1件设为总计特价());
        this.updateActivityStatus(eachActivity, "04");

        // 修改单品活动
        TestResult testResult = super.mockMvcPost("/activity/update", singleActivity);
        Assert.assertTrue(testResult.isSuccess());
        
        ////////////////////////////////////////////////////////////////////////////////////////////
        ////////////////////////////////////////////////////////////////////////////////////////////
        ////////////////////////////////////////////////////////////////////////////////////////////

        FindActivityParam faParam = new FindActivityParam();
        faParam.setActivityCode(singleActivity.getActivityCode());
        faParam.setDomainCode(ActivityHelper.domainCode);
        faParam.setTenantCode(ActivityHelper.tenantCode);
        // 查询一个活动
        testResult = super.mockMvcPost("/activity/find", faParam);
        Assert.assertTrue(testResult.isSuccess());

        QueryActivityListParam qalParam = new QueryActivityListParam();
        qalParam.setDomainCode(ActivityHelper.domainCode);
        qalParam.setTenantCode(ActivityHelper.tenantCode);
        qalParam.setTagCode("01");
        qalParam.setActivityStatus("01");
        // 查询活动列表
        testResult = super.mockMvcPost("/activity/query", qalParam);
        Assert.assertTrue(testResult.isSuccess());
        this.updateActivityStatus(singleActivity, "04");

        // 单品活动价格计算
        CalcSkuPromotionPriceParam csppParam = new CalcSkuPromotionPriceParam();
        csppParam.setDomainCode(ActivityHelper.domainCode);
        csppParam.setTenantCode(ActivityHelper.tenantCode);
        csppParam.setCategoryCodes(Arrays.asList("22"));
        csppParam.setBrandCode("23");
        csppParam.setProductCode("212");
        csppParam.setSkuCode("2122");
        csppParam.setPrice(new BigDecimal(2));
        // (PDP) Calculate single product promotion price
        testResult = super.mockMvcPost("/activity/calcSkuPromotionPrice", csppParam);
        Assert.assertTrue(testResult.isSuccess());

        // 根据商品查询活动列表
        QueryActivityListByProductParam qalBypParam = new QueryActivityListByProductParam();
        qalBypParam.setDomainCode(ActivityHelper.domainCode);
        qalBypParam.setTenantCode(ActivityHelper.tenantCode);
        qalBypParam.setCategoryCodes(Arrays.asList("22"));
        qalBypParam.setBrandCode("23");
        qalBypParam.setProductCode("212");
        qalBypParam.setSkuCode("2122");
        testResult = super.mockMvcPost("/activity/queryActivityListByProduct", qalBypParam);
        Assert.assertTrue(testResult.isSuccess());
        
        // 根据活动查商品
        String activityCode = singleActivity.getActivityCode();
        QueryProductListByActivityParam queryProductListByActivityParam = new QueryProductListByActivityParam();
        queryProductListByActivityParam.setActivityCode(activityCode);
        queryProductListByActivityParam.setDomainCode(ActivityHelper.domainCode);
        queryProductListByActivityParam.setTenantCode(ActivityHelper.tenantCode);
        // Query product list by activity
        testResult = super.mockMvcPost("/activity/queryProductListByActivity", queryProductListByActivityParam);
        Assert.assertTrue(testResult.isSuccess());

        CalcShoppingCartParam cscParam = this.buildCalcShoppingCartParam(2, 2);
        List<CalcShoppingCartResult> calcShoppingCartResult = this.calcShoppingCart(cscParam, null);
        BigDecimal incentiveAmount = this.sumPromoDeductedAmount(calcShoppingCartResult);

        // 创建订单
        String orderNo = super.randomCode(5);
        this.createOrder(cscParam, orderNo, incentiveAmount, null, null);
        // 取消订单
        this.cancelOrder(orderNo);

        // 创建订单
        orderNo = orderNo + "001";
        this.createOrder(cscParam, orderNo, incentiveAmount, null, null);
        // 确定订单
        this.confirmOrder(orderNo);

        this.updateActivityStatus(singleActivity, "07");
        this.updateActivityStatus(discountActivity, "07");
        this.updateActivityStatus(freeMailActivity, "07");
    }



    private void updateActivityStatus(UpdatePromoActivityParam param, String status) {

        super.updateActivityStatus(param.getTenantCode(), param.getActivityCode(), status);
    }
}
