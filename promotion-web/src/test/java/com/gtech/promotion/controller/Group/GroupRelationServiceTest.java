package com.gtech.promotion.controller.Group;

import com.gtech.promotion.dao.entity.activity.PromoGroupRelationEntity;
import com.gtech.promotion.dao.mapper.activity.PromoGroupRelationMapper;
import com.gtech.promotion.dao.model.activity.ActivityGroupRelationVO;
import com.gtech.promotion.dao.model.activity.GroupRelationVO;
import com.gtech.promotion.dao.model.activity.QueryGroupRelationVO;
import com.gtech.promotion.service.activity.PromotionGroupService;
import com.gtech.promotion.service.impl.activity.PromotionGroupRelationServiceImpl;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import tk.mybatis.mapper.mapperhelper.EntityHelper;
import tk.mybatis.mapper.mapperhelper.MapperHelper;

import java.util.ArrayList;
import java.util.List;

/**
 * @Description :
 * <AUTHOR> gaojie.li
 * @Date : 2023/6/16 14:08
 */
@RunWith(MockitoJUnitRunner.class)

public class GroupRelationServiceTest {

    @InjectMocks
    private PromotionGroupRelationServiceImpl  promotionGroupRelationService;

    @Mock
    private PromoGroupRelationMapper promoGroupRelationMapper;

    @Mock
    private PromotionGroupService promotionGroupService;


    @Before
    public void before() {
        EntityHelper.initEntityNameMap(PromoGroupRelationEntity.class, new MapperHelper().getConfig());
    }


    @Test
    public void queryListGroupRelationByGroupCodeA(){

        List<PromoGroupRelationEntity> promoGroupEntities = new ArrayList<>();

        PromoGroupRelationEntity entity = new PromoGroupRelationEntity();

        entity.setGroupCodeA("1");
        entity.setGroupCodeB("!");
        entity.setTenantCode("1");
        entity.setLogicDelete(0);
        promoGroupEntities.add(entity);

        Mockito.when(promoGroupRelationMapper.selectByCondition(Mockito.any())).thenReturn(promoGroupEntities);

        QueryGroupRelationVO relationVO = new QueryGroupRelationVO();
        relationVO.setGroupCodeA("1");
        relationVO.setTenantCode("1");

        List<ActivityGroupRelationVO> activityGroupRelationVOS = promotionGroupRelationService.queryListGroupRelationByGroupCodeA(relationVO);

        Assert.assertEquals(1, activityGroupRelationVOS.size());
    }


    @Test
    public void createGroupRelation(){

        GroupRelationVO relationVO = new GroupRelationVO();

        Mockito.when(promoGroupRelationMapper.insertSelective(Mockito.any())).thenReturn(1);

        int groupRelation = promotionGroupRelationService.createGroupRelation("1", "1",relationVO);

        Assert.assertEquals(1, groupRelation);

    }

    @Test
    public void deleteGroupRelation(){

        GroupRelationVO relationVO = new GroupRelationVO();

        Mockito.when(promoGroupRelationMapper.delete(Mockito.any())).thenReturn(1);

        int groupRelation = promotionGroupRelationService.deleteGroupRelation("1", "1",relationVO);

        Assert.assertEquals(1, groupRelation);

    }


    @Test
    public void queryGroupRelationByGroupCodeA(){
        List<ActivityGroupRelationVO> voList = promotionGroupRelationService.queryGroupRelationByGroupCodeA("1", new ArrayList<>());
        Assert.assertEquals(0, voList.size());
    }

    @Test
    public void queryGroupRelationByGroupCodeA11(){
        List<String> list = new ArrayList<>();
        list.add("!");
        List<ActivityGroupRelationVO> voList = promotionGroupRelationService.queryGroupRelationByGroupCodeA("1", list);
        Assert.assertEquals(0, voList.size());
    }


    @Test
    public void deleteGroupRelationByGroupCode(){


        Mockito.when(promoGroupRelationMapper.delete(Mockito.any())).thenReturn(1);

        int i = promotionGroupRelationService.deleteGroupRelationByGroupCode("1", "1", "!");
        Assert.assertEquals(1, i);
    }

    @Test
    public void deleteGroupRelationByTenantCode(){


        Mockito.when(promoGroupRelationMapper.deleteByCondition(Mockito.any())).thenReturn(1);

        int i = promotionGroupRelationService.deleteGroupRelationByTenantCode("1", "1");
        Assert.assertEquals(1, i);
    }

}
