package com.gtech.promotion.controller.flashsale;

import com.google.common.collect.Lists;
import com.gtech.commons.redis.GTechCodeGenerator;
import com.gtech.commons.result.PageResult;
import com.gtech.commons.result.Result;
import com.gtech.promotion.RedisClient;
import com.gtech.promotion.code.marketing.ActivityTypeEnum;
import com.gtech.promotion.component.boostsharing.BoostSharingComponent;
import com.gtech.promotion.component.flashsale.FlashSaleComponent;
import com.gtech.promotion.component.marketing.MarketingCacheComponent;
import com.gtech.promotion.dao.entity.marketing.MarketingGroupEntity;
import com.gtech.promotion.dao.model.marketing.flashsale.CacheFlashSaleModel;
import com.gtech.promotion.dao.model.marketing.flashsale.FlashSaleProductModel;
import com.gtech.promotion.dto.in.flashsale.SharingRecordDto;
import com.gtech.promotion.dto.out.marketing.FlashSaleActivityInfoDTO;
import com.gtech.promotion.dto.out.marketing.FlashSaleActivityPriceDTO;
import com.gtech.promotion.dto.out.marketing.FlashSaleSkuActivityPriceDTO;
import com.gtech.promotion.exception.PromotionException;
import com.gtech.promotion.helper.ActivityRedisHelpler;
import com.gtech.promotion.service.flashsale.FlashSaleOrderService;
import com.gtech.promotion.service.flashsale.FlashSaleProductService;
import com.gtech.promotion.service.marketing.MarketingGroupCodeService;
import com.gtech.promotion.service.marketing.MarketingGroupService;
import com.gtech.promotion.service.marketing.MarketingGroupUserService;
import com.gtech.promotion.vo.bean.ShoppingCartItem;
import com.gtech.promotion.vo.bean.ShoppingCartStore;
import com.gtech.promotion.vo.param.activity.*;
import com.gtech.promotion.vo.param.marketing.*;
import com.gtech.promotion.vo.param.marketing.boostsharding.ExportBoostSharingDetailParam;
import com.gtech.promotion.vo.param.marketing.boostsharding.ExportBoostSharingTotalParam;
import com.gtech.promotion.vo.param.marketing.flashsale.*;
import com.gtech.promotion.vo.result.flashpresale.ExportFlashPreSaleParam;
import com.gtech.promotion.vo.result.flashpresale.ExportGroupParam;
import com.gtech.promotion.vo.result.flashsale.*;
import org.apache.commons.collections4.map.HashedMap;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.dao.DuplicateKeyException;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@RunWith(MockitoJUnitRunner.class)
public class MallFlashSaleControllerTest {

    @InjectMocks
    private MallFlashSaleController mallFlashSaleController;

    @Mock
    private FlashSaleComponent flashSaleComponent;

    @Mock
    private MarketingCacheComponent marketingCacheComponent;

    @Mock
    private FlashSaleProductService flashSaleProductService;

    @Mock
    private MarketingGroupService marketingGroupService;

    @Mock
    private GTechCodeGenerator codeGenerator;

    @Mock
    private FlashSaleOrderService flashSaleOrderService;

    @Mock
    private MarketingGroupUserService marketingGroupUserService;

    @Mock
    private RedisClient redisClient;

    @Mock
    private BoostSharingComponent boostSharingComponent;

    @Mock
    private ActivityRedisHelpler activityRedisHelpler;

    @Mock
    private MarketingGroupCodeService marketingGroupCodeService;

    @Test
    public void calSkuFlashSalePrice_null(){
        FlashSalePdpPriceParam param = new FlashSalePdpPriceParam();
        param.setDomainCode("1");
        param.setTenantCode("1");
        param.setSkuCode("1");
        param.setActivityCode("1");

        Map<String, CacheFlashSaleModel> flashSaleCacheMap = new HashedMap<>();
        flashSaleCacheMap.put("1", new CacheFlashSaleModel());
//        Mockito.when(marketingCacheComponent.getFlashSaleCacheMap(Mockito.anyString(), Mockito.anyString())).thenReturn(flashSaleCacheMap);
        Mockito.when(marketingCacheComponent.filterActivityByProduct(Mockito.any(), Mockito.any(), Mockito.any(),Mockito.any())).thenReturn(flashSaleCacheMap);
        Mockito.when(flashSaleComponent.activitySkuPrice(Mockito.any(), Mockito.any())).thenReturn(null);
        Result<CalcFlashSaleSkuPromotionPriceResult> calcFlashSaleSkuPromotionPriceResultResult = mallFlashSaleController.calSkuFlashSalePrice(param);
        Assert.assertTrue(calcFlashSaleSkuPromotionPriceResultResult.isSuccess());
        Assert.assertNull(calcFlashSaleSkuPromotionPriceResultResult.getData());
    }

    @Test
    public void calSkuFlashSalePrice_empty(){
        FlashSalePdpPriceParam param = new FlashSalePdpPriceParam();
        param.setDomainCode("1");
        param.setTenantCode("1");
        param.setSkuCode("1");
        param.setActivityCode("1");
        FlashSaleSkuActivityPriceDTO skuActivityPriceDTO = new FlashSaleSkuActivityPriceDTO();

        Map<String, CacheFlashSaleModel> flashSaleCacheMap = new HashedMap<>();
        flashSaleCacheMap.put("1", new CacheFlashSaleModel());
//        Mockito.when(marketingCacheComponent.getFlashSaleCacheMap(Mockito.anyString(), Mockito.anyString())).thenReturn(flashSaleCacheMap);
        Mockito.when(marketingCacheComponent.filterActivityByProduct(Mockito.any(), Mockito.any(), Mockito.any(),Mockito.any() )).thenReturn(flashSaleCacheMap);
        Mockito.when(flashSaleComponent.activitySkuPrice(Mockito.any(), Mockito.any())).thenReturn(skuActivityPriceDTO);
        Result<CalcFlashSaleSkuPromotionPriceResult> calcFlashSaleSkuPromotionPriceResultResult = mallFlashSaleController.calSkuFlashSalePrice(param);
        Assert.assertTrue(calcFlashSaleSkuPromotionPriceResultResult.isSuccess());
        Assert.assertNull(calcFlashSaleSkuPromotionPriceResultResult.getData());
    }

    @Test
    public void calSkuFlashSalePrice(){
        FlashSalePdpPriceParam param = new FlashSalePdpPriceParam();
        param.setDomainCode("1");
        param.setTenantCode("1");
        param.setSkuCode("1");
        param.setActivityCode("1");
        FlashSaleSkuActivityPriceDTO skuActivityPriceDTO = new FlashSaleSkuActivityPriceDTO();
        List<FlashSaleActivityPriceDTO> activity = new ArrayList<>();
        FlashSaleActivityPriceDTO dto = new FlashSaleActivityPriceDTO();
        dto.setActivityCode("1");
        activity.add(dto);
        skuActivityPriceDTO.setActivity(activity);

        Map<String, CacheFlashSaleModel> flashSaleCacheMap = new HashedMap<>();
        flashSaleCacheMap.put("1", new CacheFlashSaleModel());
//        Mockito.when(marketingCacheComponent.getFlashSaleCacheMap(Mockito.anyString(), Mockito.anyString())).thenReturn(flashSaleCacheMap);
        Mockito.when(marketingCacheComponent.filterActivityByProduct(Mockito.any(), Mockito.any(), Mockito.any(),Mockito.any() )).thenReturn(flashSaleCacheMap);
        Mockito.when(flashSaleComponent.activitySkuPrice(Mockito.any(), Mockito.any())).thenReturn(skuActivityPriceDTO);
        Result<CalcFlashSaleSkuPromotionPriceResult> calcFlashSaleSkuPromotionPriceResultResult = mallFlashSaleController.calSkuFlashSalePrice(param);
        Assert.assertTrue(calcFlashSaleSkuPromotionPriceResultResult.isSuccess());
        Assert.assertEquals("1", calcFlashSaleSkuPromotionPriceResultResult.getData().getActivityCode());
    }



    @Test
    public void calSkuListFlashSalePrice_empty(){
        FlashSaleNewPdpPriceParam param = new FlashSaleNewPdpPriceParam();
        List<SkuParam> skuList = new ArrayList<>();
        SkuParam skuParam = new SkuParam();
        skuParam.setSkuCode("test");
        skuList.add(skuParam);
        param.setDomainCode("1");
        param.setTenantCode("1");
        param.setSkuList(skuList);
        param.setActivityCode("1");

        List<FlashSaleSkuActivityPriceDTO> activityPriceDTOList = new ArrayList<>();
        FlashSaleSkuActivityPriceDTO skuActivityPriceDTO = new FlashSaleSkuActivityPriceDTO();

        activityPriceDTOList.add(skuActivityPriceDTO);
        Map<String, CacheFlashSaleModel> flashSaleCacheMap = new HashedMap<>();
        flashSaleCacheMap.put("1", new CacheFlashSaleModel());
//        Mockito.when(marketingCacheComponent.getFlashSaleCacheMap(Mockito.anyString(), Mockito.anyString())).thenReturn(flashSaleCacheMap);
        Mockito.when(marketingCacheComponent.filterActivityByProduct(Mockito.any(), Mockito.any(), Mockito.any(),Mockito.any() )).thenReturn(flashSaleCacheMap);
        Mockito.when(flashSaleComponent.activitySkuPriceList(Mockito.any(), Mockito.any())).thenReturn(activityPriceDTOList);
        Result<List<CalcFlashSaleSkuPromotionPriceResult>> calcFlashSaleSkuPromotionPriceResultResult = mallFlashSaleController.calSkuListFlashSalePrice(param);
        Assert.assertTrue(calcFlashSaleSkuPromotionPriceResultResult.isSuccess());

        List<FlashSaleActivityPriceDTO> activityList = new ArrayList<>();
        FlashSaleActivityPriceDTO flashSaleActivityPriceDTO = new FlashSaleActivityPriceDTO();
        flashSaleActivityPriceDTO.setMaxPerUser(1);
        activityList.add(flashSaleActivityPriceDTO);
        skuActivityPriceDTO.setActivity(activityList);
        Mockito.when(flashSaleComponent.activitySkuPriceList(Mockito.any(), Mockito.any())).thenReturn(activityPriceDTOList);
        Result<List<CalcFlashSaleSkuPromotionPriceResult>> calcFlashSaleSkuPromotionPriceResultResult1 = mallFlashSaleController.calSkuListFlashSalePrice(param);
        Assert.assertTrue(calcFlashSaleSkuPromotionPriceResultResult1.isSuccess());

    }


    @Test
    public void calSkuListFlashSalePrice(){
        FlashSaleNewPdpPriceParam param = new FlashSaleNewPdpPriceParam();
        List<SkuParam> skuList = new ArrayList<>();
        SkuParam skuParam = new SkuParam();
        skuParam.setSkuCode("test");
        skuList.add(skuParam);
        param.setDomainCode("1");
        param.setTenantCode("1");
        param.setSkuList(skuList);
        param.setActivityCode("1");
        FlashSaleSkuActivityPriceDTO skuActivityPriceDTO = new FlashSaleSkuActivityPriceDTO();

        Map<String, CacheFlashSaleModel> flashSaleCacheMap = new HashedMap<>();
        flashSaleCacheMap.put("1", new CacheFlashSaleModel());
//        Mockito.when(marketingCacheComponent.getFlashSaleCacheMap(Mockito.anyString(), Mockito.anyString())).thenReturn(flashSaleCacheMap);
        Mockito.when(marketingCacheComponent.filterActivityByProduct(Mockito.any(), Mockito.any(), Mockito.any(),Mockito.any() )).thenReturn(flashSaleCacheMap);
//        Mockito.when(flashSaleComponent.activitySkuPrice(Mockito.any(), Mockito.any())).thenReturn(skuActivityPriceDTO);
        Result<List<CalcFlashSaleSkuPromotionPriceResult>> calcFlashSaleSkuPromotionPriceResultResult = mallFlashSaleController.calSkuListFlashSalePrice(param);
        Assert.assertTrue(calcFlashSaleSkuPromotionPriceResultResult.isSuccess());
        Assert.assertNull(calcFlashSaleSkuPromotionPriceResultResult.getData());
    }

    @Test
    public void queryActivityListByProduct(){
        QueryActivityListByProductParam param = new QueryActivityListByProductParam();
        param.setDomainCode("1");
        param.setTenantCode("1");
        param.setSkuCode("1");
        List<FlashSaleActivityInfoDTO> activity = new ArrayList<>();
        FlashSaleActivityInfoDTO dto = new FlashSaleActivityInfoDTO();
        dto.setActivityCode("1");
        activity.add(dto);

        Map<String, CacheFlashSaleModel> flashSaleCacheMap = new HashedMap<>();
//        Mockito.when(marketingCacheComponent.getFlashSaleCacheMap(Mockito.anyString(), Mockito.anyString())).thenReturn(flashSaleCacheMap);
        Mockito.when(marketingCacheComponent.filterActivityByProduct(Mockito.any(), Mockito.any(), Mockito.any(),Mockito.any() )).thenReturn(flashSaleCacheMap);
        Mockito.when(flashSaleComponent.getActivitiesByProduct(Mockito.any(), Mockito.any())).thenReturn(activity);
        Result<List<QueryFlashSaleListByProductResult>> listResult = mallFlashSaleController.queryActivityListByProduct(param);
        Assert.assertTrue(listResult.isSuccess());
    }

    @Test
    public void queryActivityListByProduct_有org(){
        QueryActivityListByProductParam param = new QueryActivityListByProductParam();
        param.setDomainCode("1");
        param.setTenantCode("1");
        param.setSkuCode("1");
        param.setOrgCodes("1,2");
        List<FlashSaleActivityInfoDTO> activity = new ArrayList<>();
        FlashSaleActivityInfoDTO dto = new FlashSaleActivityInfoDTO();
        dto.setActivityCode("1");
        activity.add(dto);

        Map<String, CacheFlashSaleModel> flashSaleCacheMap = new HashedMap<>();
//        Mockito.when(marketingCacheComponent.getFlashSaleCacheMap(Mockito.anyString(), Mockito.anyString())).thenReturn(flashSaleCacheMap);
        Mockito.when(marketingCacheComponent.filterActivityByProduct(Mockito.any(), Mockito.any(), Mockito.any(),Mockito.any() )).thenReturn(flashSaleCacheMap);
        Mockito.when(flashSaleComponent.getActivitiesByProduct(Mockito.any(), Mockito.any())).thenReturn(activity);
        Result<List<QueryFlashSaleListByProductResult>> listResult = mallFlashSaleController.queryActivityListByProduct(param);
        Assert.assertTrue(listResult.isSuccess());
    }

    @Test
    public void queryActivityList(){
        FlashSaleQueryListParam param = new FlashSaleQueryListParam();
        param.setDomainCode("1");
        param.setTenantCode("1");
        PageResult<FlashSaleQueryListResult> pageResult = new PageResult<>();

        Mockito.when(flashSaleComponent.queryActivityList(Mockito.any())).thenReturn(pageResult);
        PageResult<FlashSaleQueryListResult> flashSaleQueryListResultPageResult = mallFlashSaleController.queryFlashSaleActivityList(param);
        Assert.assertTrue(flashSaleQueryListResultPageResult.isSuccess());
        Assert.assertEquals(0, flashSaleQueryListResultPageResult.getData().getList().size());
    }

    @Test
    public void queryProductListByActivity(){
        FlashSaleQueryProductListParam param = new FlashSaleQueryProductListParam();
        param.setDomainCode("1");
        param.setTenantCode("1");
        param.setActivityCode("1");

        Mockito.when(flashSaleComponent.queryProductsByActivityCode(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(null);
        Result<FlashSaleQueryProductListResult> flashSaleQueryProductListResultResult = mallFlashSaleController.queryProductListByActivity(param);
        Assert.assertTrue(flashSaleQueryProductListResultResult.isSuccess());
        Assert.assertNull(flashSaleQueryProductListResultResult.getData());
    }

    @Test
    public void calcFlashSaleOrderPrice_empty(){
        FlashSaleOrderCalaParam param = new FlashSaleOrderCalaParam();
        param.setDomainCode("1");
        param.setTenantCode("1");
        param.setMemberCode("1");
        param.setActivityCode("1");
        List<ShoppingCartStore> cartStoreList = new ArrayList<>();
        ShoppingCartStore shoppingCartStore = new ShoppingCartStore();
        List<ShoppingCartItem> cartItemList = new ArrayList<>();
        ShoppingCartItem item = new ShoppingCartItem();
        item.setSkuCode("1");
        item.setProductCode("1");
        item.setQuantity(1);
        item.setProductPrice(new BigDecimal("123"));
        item.setSelectionFlag("01");
        cartItemList.add(item);
        shoppingCartStore.setCartItemList(cartItemList);
        cartStoreList.add(shoppingCartStore);
        param.setCartStoreList(cartStoreList);
        List<FlashSaleActivityInfoDTO> activity = new ArrayList<>();
        FlashSaleActivityInfoDTO dto = new FlashSaleActivityInfoDTO();
        dto.setActivityCode("1");
        activity.add(dto);
        Map<String, CacheFlashSaleModel> flashSaleCacheMap = new HashedMap<>();
        CacheFlashSaleModel value = new CacheFlashSaleModel();
        value.setProducts(new ArrayList<>());
        flashSaleCacheMap.put("1", value);
        FlashSaleSkuActivityPriceDTO skuActivityPriceDTO = new FlashSaleSkuActivityPriceDTO();

//        Mockito.when(marketingCacheComponent.filterActivityByProduct(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(flashSaleCacheMap);
//        Mockito.when(flashSaleComponent.getActivitiesByProduct(Mockito.any())).thenReturn(activity);
//        Mockito.when(flashSaleComponent.activitySkuPrice(Mockito.any(), Mockito.any())).thenReturn(skuActivityPriceDTO);
        Map<String, CacheFlashSaleModel> activityCacheMap = new HashedMap<>();
        CacheFlashSaleModel cacheFlashSaleModel = new CacheFlashSaleModel();
        cacheFlashSaleModel.setActivityCode("test");
        cacheFlashSaleModel.setActivityBegin("20211028");
        cacheFlashSaleModel.setActivityEnd("20211029");
        activityCacheMap.put("test", cacheFlashSaleModel);
//        Mockito.when(marketingCacheComponent.getFlashSaleCacheMap(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(activityCacheMap);

        FlashSaleProductModel flashSaleProductModel = new FlashSaleProductModel();
        flashSaleProductModel.setMaxPerUser(12);
        flashSaleProductModel.setSkuInventory(34);
//        Mockito.when(flashSaleProductService.findByActivityAndSku(Mockito.any(), Mockito.any())).thenReturn(flashSaleProductModel);
        Result<List<FlashSaleOrderCalaResult>> listResult = mallFlashSaleController.calcFlashSaleOrderPrice(param);
        Assert.assertTrue(listResult.isSuccess());
        Assert.assertEquals(0, listResult.getData().size());
    }





    @Test
    public void createOrder_扣减为0(){
        FlashSaleCreateOrderParam param = new FlashSaleCreateOrderParam();
        param.setOrderNo("1");
        param.setPromoDeductedAmount(BigDecimal.ZERO);
        param.setDomainCode("1");
        param.setTenantCode("1");
        param.setMemberCode("1");
        param.setActivityCode("1");
        List<ShoppingCartStore> cartStoreList = new ArrayList<>();
        ShoppingCartStore shoppingCartStore = new ShoppingCartStore();
        List<ShoppingCartItem> cartItemList = new ArrayList<>();
        ShoppingCartItem item = new ShoppingCartItem();
        item.setSkuCode("1");
        item.setProductCode("1");
        item.setQuantity(1);
        item.setProductPrice(new BigDecimal("123"));
        item.setSelectionFlag("02");
        cartItemList.add(item);
        shoppingCartStore.setCartItemList(cartItemList);
        cartStoreList.add(shoppingCartStore);
        param.setCartStoreList(cartStoreList);
        List<FlashSaleActivityInfoDTO> activity = new ArrayList<>();
        FlashSaleActivityInfoDTO dto = new FlashSaleActivityInfoDTO();
        dto.setActivityCode("1");
        activity.add(dto);

        Map<String, CacheFlashSaleModel> flashSaleCacheMap = new HashedMap<>();
        CacheFlashSaleModel flashSaleModel = new CacheFlashSaleModel();
        List<FlashSaleProductModel> productModels = new ArrayList<>();
        FlashSaleProductModel productModel = new FlashSaleProductModel();
        productModel.setSkuCode("1");
        productModel.setFlashPrice(new BigDecimal("12"));
        productModels.add(productModel);
        flashSaleModel.setProducts(productModels);
        flashSaleCacheMap.put("1", flashSaleModel);

        FlashSaleSkuActivityPriceDTO skuActivityPriceDTO = new FlashSaleSkuActivityPriceDTO();
        List<FlashSaleActivityPriceDTO> activityPriceDTOS = new ArrayList<>();
        FlashSaleActivityPriceDTO priceDTO = new FlashSaleActivityPriceDTO();
        priceDTO.setActivityCode("1");
        activityPriceDTOS.add(priceDTO);
        skuActivityPriceDTO.setActivity(activityPriceDTOS);
        //Mockito.when(marketingCacheComponent.filterActivityByProduct(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(flashSaleCacheMap);
//        Mockito.when(flashSaleComponent.checkQuota(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(true);
        Map<String, CacheFlashSaleModel> activityCacheMap = new HashedMap<>();
        CacheFlashSaleModel cacheFlashSaleModel = new CacheFlashSaleModel();
        cacheFlashSaleModel.setActivityCode("test");
        cacheFlashSaleModel.setActivityBegin("20211028");
        cacheFlashSaleModel.setActivityEnd("20211029");
        activityCacheMap.put("test", cacheFlashSaleModel);
//        Mockito.when(marketingCacheComponent.getFlashSaleCacheMap(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(activityCacheMap);
        FlashSaleProductModel flashSaleProductModel = new FlashSaleProductModel();
        flashSaleProductModel.setMaxPerUser(12);
        flashSaleProductModel.setSkuInventory(34);
//        Mockito.when(flashSaleProductService.findByActivityAndSku(Mockito.any(), Mockito.any())).thenReturn(flashSaleProductModel);
        Result<String> order = mallFlashSaleController.createOrder(param);
        Assert.assertTrue(order.isSuccess());
        Assert.assertNull(order.getData());
    }

    @Test
    public void getMarketingGroupCode(){

        Result<String> marketingGroupCode = mallFlashSaleController.getMarketingGroupCode("1");

        Assert.assertTrue(marketingGroupCode.isSuccess());
    }

    @Test
    public void createOrder(){
        FlashSaleCreateOrderParam param = new FlashSaleCreateOrderParam();
        param.setOrderNo("1");
        param.setPromoDeductedAmount(new BigDecimal("1"));
        param.setDomainCode("1");
        param.setTenantCode("1");
        param.setMemberCode("1");
        param.setActivityCode("1");
        List<ShoppingCartStore> cartStoreList = new ArrayList<>();
        ShoppingCartStore shoppingCartStore = new ShoppingCartStore();
        List<ShoppingCartItem> cartItemList = new ArrayList<>();
        ShoppingCartItem item = new ShoppingCartItem();
        item.setSkuCode("1");
        item.setProductCode("1");
        item.setQuantity(1);
        item.setProductPrice(new BigDecimal("123"));
        item.setSelectionFlag("01");
        cartItemList.add(item);
        shoppingCartStore.setCartItemList(cartItemList);
        cartStoreList.add(shoppingCartStore);
        param.setCartStoreList(cartStoreList);
        List<FlashSaleActivityInfoDTO> activity = new ArrayList<>();
        FlashSaleActivityInfoDTO dto = new FlashSaleActivityInfoDTO();
        dto.setActivityCode("1");
        activity.add(dto);

        Map<String, CacheFlashSaleModel> flashSaleCacheMap = new HashedMap<>();
        CacheFlashSaleModel flashSaleModel = new CacheFlashSaleModel();
        List<FlashSaleProductModel> productModels = new ArrayList<>();
        FlashSaleProductModel productModel = new FlashSaleProductModel();
        productModel.setSkuCode("1");
        productModel.setFlashPrice(new BigDecimal("12"));
        productModels.add(productModel);
        flashSaleModel.setProducts(productModels);
        flashSaleCacheMap.put("1", flashSaleModel);

        FlashSaleSkuActivityPriceDTO skuActivityPriceDTO = new FlashSaleSkuActivityPriceDTO();
        List<FlashSaleActivityPriceDTO> activityPriceDTOS = new ArrayList<>();
        FlashSaleActivityPriceDTO priceDTO = new FlashSaleActivityPriceDTO();
        priceDTO.setActivityCode("1");
        activityPriceDTOS.add(priceDTO);
        skuActivityPriceDTO.setActivity(activityPriceDTOS);

//        Mockito.when(marketingCacheComponent.filterActivityByProduct(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(flashSaleCacheMap);
//        Mockito.when(flashSaleComponent.getActivitiesByProduct(Mockito.any())).thenReturn(activity);
//        Mockito.when(flashSaleComponent.activitySkuPrice(Mockito.any(), Mockito.any())).thenReturn(skuActivityPriceDTO);
//        Mockito.when(flashSaleComponent.checkQuota(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(true);
        Mockito.doNothing().when(flashSaleComponent).commitOrder(Mockito.any(), Mockito.any());
        Map<String, CacheFlashSaleModel> activityCacheMap = new HashedMap<>();
        CacheFlashSaleModel cacheFlashSaleModel = new CacheFlashSaleModel();
        cacheFlashSaleModel.setActivityCode("test");
        cacheFlashSaleModel.setActivityBegin("20211028");
        cacheFlashSaleModel.setActivityEnd("20211029");
        activityCacheMap.put("test", cacheFlashSaleModel);
//        Mockito.when(marketingCacheComponent.getFlashSaleCacheMap(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(activityCacheMap);

        FlashSaleProductModel flashSaleProductModel = new FlashSaleProductModel();
        flashSaleProductModel.setMaxPerUser(12);
        flashSaleProductModel.setSkuInventory(34);
//        Mockito.when(flashSaleProductService.findByActivityAndSku(Mockito.any(), Mockito.any())).thenReturn(flashSaleProductModel);
        Result<String> order = mallFlashSaleController.createOrder(param);
        Assert.assertTrue(order.isSuccess());
        Assert.assertNull(order.getData());
    }

    @Test
    public void cancelOrder(){
        CancelOrderParam param = new CancelOrderParam();
        param.setDomainCode("1");
        param.setTenantCode("1");
        param.setOrderNo("1");
        Mockito.when(flashSaleComponent.cancelOrder(Mockito.any(), Mockito.any())).thenReturn(1);
        Result<Object> objectResult = mallFlashSaleController.cancelOrder(param);
        Assert.assertTrue(objectResult.isSuccess());
        Assert.assertNull(objectResult.getData());
    }

    @Test
    public void confirmOrder(){
        ConfirmOrderParam param = new ConfirmOrderParam();
        param.setDomainCode("1");
        param.setTenantCode("1");
        param.setOrderNo("1");

        Mockito.when(flashSaleComponent.confirmOrder(Mockito.any(), Mockito.any())).thenReturn(1);
        Result<Object> objectResult = mallFlashSaleController.confirmOrder(param);
        Assert.assertTrue(objectResult.isSuccess());
        Assert.assertNull(objectResult.getData());
    }



    @Test
    public void queryActivityListByProductList(){
        QueryActivityListByProductListParam param = new QueryActivityListByProductListParam();
        param.setDomainCode("!");
        param.setTenantCode("!");
        ArrayList<QueryActivityListByProductListParam.Product> products = new ArrayList<>();
        QueryActivityListByProductListParam.Product product = new QueryActivityListByProductListParam.Product();
        product.setSkuCode("!");
        product.setProductCode("!");
        products.add(product);
        QueryActivityListByProductListParam.Product product1 = new QueryActivityListByProductListParam.Product();
        product1.setSkuCode("!");
        product1.setProductCode("!");
        product1.setOrgCodes("!");
        products.add(product1);
        param.setProducts(products);
        Map<String, CacheFlashSaleModel> activityCacheMap = new HashedMap<>();

        Mockito.when(marketingCacheComponent.getFlashSaleCacheMap(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(activityCacheMap);
        Mockito.when(marketingCacheComponent.filterActivityByProduct(Mockito.any(), Mockito.any(), Mockito.any(),Mockito.any() )).thenReturn(activityCacheMap);
        Mockito.when(flashSaleComponent.getActivitiesByProduct(Mockito.any(), Mockito.any())).thenReturn(new ArrayList<>());
        List<MarketingGroupEntity> marketingGroupEntities = new ArrayList<>();
        MarketingGroupEntity marketingGroupEntity = new MarketingGroupEntity();
        marketingGroupEntity.setActivityCode("test");
        marketingGroupEntities.add(marketingGroupEntity);
        Mockito.when(marketingGroupService.selectMarketingGroupList(Mockito.any(), Mockito.any())).thenReturn(marketingGroupEntities);

        Result<List<QueryFlashSaleListByProductListResult>> listResult = mallFlashSaleController.queryActivityListByProductList(param);
        Assert.assertEquals(products.size(), listResult.getData().size());
    }


    @Test
    public void createOrder_exception(){
        FlashSaleCreateOrderParam param = new FlashSaleCreateOrderParam();
        param.setOrderNo("1");
        param.setPromoDeductedAmount(new BigDecimal("1"));
        param.setDomainCode("1");
        param.setTenantCode("1");
        param.setMemberCode("1");
        param.setActivityCode("1");
        List<ShoppingCartStore> cartStoreList = new ArrayList<>();
        ShoppingCartStore shoppingCartStore = new ShoppingCartStore();
        List<ShoppingCartItem> cartItemList = new ArrayList<>();
        ShoppingCartItem item = new ShoppingCartItem();
        item.setSkuCode("1");
        item.setProductCode("1");
        item.setQuantity(1);
        item.setProductPrice(new BigDecimal("123"));
        item.setSelectionFlag("01");
        cartItemList.add(item);
        shoppingCartStore.setCartItemList(cartItemList);
        cartStoreList.add(shoppingCartStore);
        param.setCartStoreList(cartStoreList);
        List<FlashSaleActivityInfoDTO> activity = new ArrayList<>();
        FlashSaleActivityInfoDTO dto = new FlashSaleActivityInfoDTO();
        dto.setActivityCode("1");
        activity.add(dto);
        Map<String, CacheFlashSaleModel> flashSaleCacheMap = new HashedMap<>();
        CacheFlashSaleModel flashSaleModel = new CacheFlashSaleModel();
        List<FlashSaleProductModel> productModels = new ArrayList<>();
        FlashSaleProductModel productModel = new FlashSaleProductModel();
        productModel.setSkuCode("1");
        productModel.setFlashPrice(new BigDecimal("12"));
        productModels.add(productModel);
        flashSaleModel.setProducts(productModels);
        flashSaleCacheMap.put("1", flashSaleModel);
        FlashSaleSkuActivityPriceDTO skuActivityPriceDTO = new FlashSaleSkuActivityPriceDTO();
        List<FlashSaleActivityPriceDTO> activityPriceDTOS = new ArrayList<>();
        FlashSaleActivityPriceDTO priceDTO = new FlashSaleActivityPriceDTO();
        priceDTO.setActivityCode("1");
        activityPriceDTOS.add(priceDTO);
        skuActivityPriceDTO.setActivity(activityPriceDTOS);
        List<FlashSaleOrderCalaResult> list = new ArrayList<>();
        Mockito.when(flashSaleComponent.handlerShoppingCart(Mockito.any())).thenReturn(list);
        Result<String> order = mallFlashSaleController.createOrder(param);
        Assert.assertTrue(order.isSuccess());
        Assert.assertNull(order.getData());
    }



    @Test
    public void createOrder_DuplicateKeyException(){

        FlashSaleCreateOrderParam param = new FlashSaleCreateOrderParam();
        param.setOrderNo("1");
        param.setPromoDeductedAmount(new BigDecimal("1"));
        param.setDomainCode("1");
        param.setTenantCode("1");
        param.setMemberCode("1");
        param.setActivityCode("1");
        List<ShoppingCartStore> cartStoreList = new ArrayList<>();
        ShoppingCartStore shoppingCartStore = new ShoppingCartStore();
        List<ShoppingCartItem> cartItemList = new ArrayList<>();
        ShoppingCartItem item = new ShoppingCartItem();
        item.setSkuCode("1");
        item.setProductCode("1");
        item.setQuantity(1);
        item.setProductPrice(new BigDecimal("123"));
        item.setSelectionFlag("01");
        cartItemList.add(item);
        shoppingCartStore.setCartItemList(cartItemList);
        cartStoreList.add(shoppingCartStore);
        param.setCartStoreList(cartStoreList);
        List<FlashSaleActivityInfoDTO> activity = new ArrayList<>();
        FlashSaleActivityInfoDTO dto = new FlashSaleActivityInfoDTO();
        dto.setActivityCode("1");
        activity.add(dto);
        Map<String, CacheFlashSaleModel> flashSaleCacheMap = new HashedMap<>();
        CacheFlashSaleModel flashSaleModel = new CacheFlashSaleModel();
        List<FlashSaleProductModel> productModels = new ArrayList<>();
        FlashSaleProductModel productModel = new FlashSaleProductModel();
        productModel.setSkuCode("1");
        productModel.setFlashPrice(new BigDecimal("12"));
        productModels.add(productModel);
        flashSaleModel.setProducts(productModels);
        flashSaleCacheMap.put("1", flashSaleModel);
        FlashSaleSkuActivityPriceDTO skuActivityPriceDTO = new FlashSaleSkuActivityPriceDTO();
        List<FlashSaleActivityPriceDTO> activityPriceDTOS = new ArrayList<>();
        FlashSaleActivityPriceDTO priceDTO = new FlashSaleActivityPriceDTO();
        priceDTO.setActivityCode("1");
        activityPriceDTOS.add(priceDTO);
        skuActivityPriceDTO.setActivity(activityPriceDTOS);
        List<FlashSaleOrderCalaResult> list = new ArrayList<>();
        Mockito.when(flashSaleComponent.handlerShoppingCart(Mockito.any())).thenReturn(list);

        Mockito.doThrow(new DuplicateKeyException("1")).when(flashSaleComponent).commitOrder(Mockito.any(),Mockito.any());

        Result<String> order = mallFlashSaleController.createOrder(param);
        Assert.assertTrue(order.isSuccess());
        Assert.assertNull(order.getData());
    }


    @Test(expected = NullPointerException.class)
    public void createOrder_Exception(){

        FlashSaleCreateOrderParam param = new FlashSaleCreateOrderParam();
        param.setOrderNo("1");
        param.setPromoDeductedAmount(new BigDecimal("1"));
        param.setDomainCode("1");
        param.setTenantCode("1");
        param.setMemberCode("1");
        param.setActivityCode("1");
        List<ShoppingCartStore> cartStoreList = new ArrayList<>();
        ShoppingCartStore shoppingCartStore = new ShoppingCartStore();
        List<ShoppingCartItem> cartItemList = new ArrayList<>();
        ShoppingCartItem item = new ShoppingCartItem();
        item.setSkuCode("1");
        item.setProductCode("1");
        item.setQuantity(1);
        item.setProductPrice(new BigDecimal("123"));
        item.setSelectionFlag("01");
        cartItemList.add(item);
        shoppingCartStore.setCartItemList(cartItemList);
        cartStoreList.add(shoppingCartStore);
        param.setCartStoreList(cartStoreList);
        List<FlashSaleActivityInfoDTO> activity = new ArrayList<>();
        FlashSaleActivityInfoDTO dto = new FlashSaleActivityInfoDTO();
        dto.setActivityCode("1");
        activity.add(dto);
        Map<String, CacheFlashSaleModel> flashSaleCacheMap = new HashedMap<>();
        CacheFlashSaleModel flashSaleModel = new CacheFlashSaleModel();
        List<FlashSaleProductModel> productModels = new ArrayList<>();
        FlashSaleProductModel productModel = new FlashSaleProductModel();
        productModel.setSkuCode("1");
        productModel.setFlashPrice(new BigDecimal("12"));
        productModels.add(productModel);
        flashSaleModel.setProducts(productModels);
        flashSaleCacheMap.put("1", flashSaleModel);
        FlashSaleSkuActivityPriceDTO skuActivityPriceDTO = new FlashSaleSkuActivityPriceDTO();
        List<FlashSaleActivityPriceDTO> activityPriceDTOS = new ArrayList<>();
        FlashSaleActivityPriceDTO priceDTO = new FlashSaleActivityPriceDTO();
        priceDTO.setActivityCode("1");
        activityPriceDTOS.add(priceDTO);
        skuActivityPriceDTO.setActivity(activityPriceDTOS);
        List<FlashSaleOrderCalaResult> list = new ArrayList<>();
        Mockito.when(flashSaleComponent.handlerShoppingCart(Mockito.any())).thenReturn(list);

        Mockito.doThrow(new NullPointerException("1")).when(flashSaleComponent).commitOrder(Mockito.any(),Mockito.any());

        mallFlashSaleController.createOrder(param);

    }

    @Test(expected = PromotionException.class)
    public void createOrder_DuplicateKeyException1(){

        FlashSaleCreateOrderParam param = new FlashSaleCreateOrderParam();
        param.setOrderNo("1");
        param.setPromoDeductedAmount(new BigDecimal("1"));
        param.setDomainCode("1");
        param.setTenantCode("1");
        param.setMemberCode("1");
        param.setActivityCode("1");
        param.setMarketingGroupCode("11");
        param.setActivityType(ActivityTypeEnum.GROUP.code());
        List<ShoppingCartStore> cartStoreList = new ArrayList<>();
        ShoppingCartStore shoppingCartStore = new ShoppingCartStore();
        List<ShoppingCartItem> cartItemList = new ArrayList<>();
        ShoppingCartItem item = new ShoppingCartItem();
        item.setSkuCode("1");
        item.setProductCode("1");
        item.setQuantity(1);
        item.setProductPrice(new BigDecimal("123"));
        item.setSelectionFlag("01");
        cartItemList.add(item);
        shoppingCartStore.setCartItemList(cartItemList);
        cartStoreList.add(shoppingCartStore);
        param.setCartStoreList(cartStoreList);
        List<FlashSaleActivityInfoDTO> activity = new ArrayList<>();
        FlashSaleActivityInfoDTO dto = new FlashSaleActivityInfoDTO();
        dto.setActivityCode("1");
        activity.add(dto);
        Map<String, CacheFlashSaleModel> flashSaleCacheMap = new HashedMap<>();
        CacheFlashSaleModel flashSaleModel = new CacheFlashSaleModel();
        List<FlashSaleProductModel> productModels = new ArrayList<>();
        FlashSaleProductModel productModel = new FlashSaleProductModel();
        productModel.setSkuCode("1");
        productModel.setFlashPrice(new BigDecimal("12"));
        productModels.add(productModel);
        flashSaleModel.setProducts(productModels);
        flashSaleCacheMap.put("1", flashSaleModel);
        FlashSaleSkuActivityPriceDTO skuActivityPriceDTO = new FlashSaleSkuActivityPriceDTO();
        List<FlashSaleActivityPriceDTO> activityPriceDTOS = new ArrayList<>();
        FlashSaleActivityPriceDTO priceDTO = new FlashSaleActivityPriceDTO();
        priceDTO.setActivityCode("1");
        activityPriceDTOS.add(priceDTO);
        skuActivityPriceDTO.setActivity(activityPriceDTOS);
        List<FlashSaleOrderCalaResult> list = new ArrayList<>();
        Mockito.when(flashSaleComponent.handlerShoppingCart(Mockito.any())).thenReturn(list);

        Mockito.doThrow(new DuplicateKeyException("1")).when(flashSaleComponent).commitOrder(Mockito.any(),Mockito.any());
        Result<String> order = mallFlashSaleController.createOrder(param);
        Assert.assertTrue(order.isSuccess());
        Assert.assertNull(order.getData());
    }


    @Test
    public void testCreateSharingRecord() {
        CreateSharingRecordParam param = new CreateSharingRecordParam();
        param.setDomainCode("1");
        param.setTenantCode("1");
        param.setOrgCode("1");
        param.setActivityCode("1");
        param.setSharingMemberCode("1");
        SharingRecordDto sharingRecordDto = new SharingRecordDto();
        Mockito.when(boostSharingComponent.createBoostSharing(Mockito.any())).thenReturn("sharingRecordCode");

        Result<String> result = mallFlashSaleController.createSharingRecord(param);

    }


    @Test
    public void testCreateHelpRecord() throws Exception {
        CreateHelpRecordParam param = new CreateHelpRecordParam();
        param.setDomainCode("1");
        param.setTenantCode("1");
        param.setOrgCode("1");
        param.setActivityCode("1");
        param.setSharingMemberCode("1");
        param.setSharingRecordCode("1");
        param.setHelpMemberCode("1");
        mallFlashSaleController.createHelpRecord(param);
    }

    @Test
    public void testQuerySharingInformation(){
        QuerySharingInformationParam param = new QuerySharingInformationParam();
        param.setDomainCode("1");
        param.setTenantCode("1");
        param.setOrgCode("1");
        param.setActivityCode("1");
        param.setSharingMemberCode("1");
        mallFlashSaleController.querySharingInformation(param);
    }

    @Test
    public void testIssuanceOfPreEmptiveRights(){
        IssuanceOfPreEmptiveRightsParam param = new IssuanceOfPreEmptiveRightsParam();
        param.setDomainCode("1");
        param.setTenantCode("1");
        param.setOrgCode("1");
        param.setActivityCode("1");
        param.setMemberCode("1");
        param.setRightOfFirstRefusalCode("1");
        mallFlashSaleController.issuanceOfPreEmptiveRights(param);
    }
    @Test
    public void testWriteOffOfPreEmptiveRights(){
        WriteOffOfPreEmptiveRightsParam param = new WriteOffOfPreEmptiveRightsParam();
        param.setDomainCode("1");
        param.setTenantCode("1");
        param.setOrgCode("1");
        param.setActivityCode("1");
        param.setMemberCode("1");
        param.setRightOfFirstRefusalCode("1");
        mallFlashSaleController.writeOffOfPreEmptiveRights(param);
    }

    @Test
    public void testQueryRightOfFirstRefusal(){
        QueryRightOfFirstRefusalParam param = new QueryRightOfFirstRefusalParam();
        param.setDomainCode("1");
        param.setTenantCode("1");
        param.setOrgCode("1");
        param.setActivityCode("1");
        param.setMemberCode("1");
        param.setRightOfFirstRefusalCode("1");
        mallFlashSaleController.queryRightOfFirstRefusal(param);
    }

    @Test
    public void exportFlashOrPreSaleDetail(){

        ExportFlashPreSaleParam param = new ExportFlashPreSaleParam();
        param.setDomainCode("1");
        param.setTenantCode("1");
        param.setActivityCode("1");
        mallFlashSaleController.exportFlashOrPreSaleDetail(param);
    }

    @Test
    public void exportFlashOrPreSaleProductDetail(){

        ExportFlashPreSaleParam param = new ExportFlashPreSaleParam();
        param.setDomainCode("1");
        param.setTenantCode("1");
        param.setActivityCode("1");
        mallFlashSaleController.exportFlashOrPreSaleProductDetail(param);
    }
    @Test
    public void exportGroupData(){

        ExportGroupParam param = new ExportGroupParam();
        param.setDomainCode("1");
        param.setTenantCode("1");
        param.setActivityCode("1");
        mallFlashSaleController.exportGroupData(param);
    }

    @Test
    public void queryHelpRecord(){

        QueryAssistanceInformationParam param = new
                QueryAssistanceInformationParam();
        param.setDomainCode("1");
        param.setTenantCode("1");
        param.setOrgCode("1");
        param.setActivityCode("1");
        param.setMemberCode("1");
        mallFlashSaleController.queryHelpRecord(param);

    }

    @Test
    public void filterNoRightOfFirstRefusal(){

        FilterNoRightOfFirstRefusalParam param = new
                FilterNoRightOfFirstRefusalParam();
        param.setDomainCode("1");
        param.setTenantCode("1");
        param.setUserCode("1");
        param.setCartItems(Lists.newArrayList(new FilterNoRightOfFirstRefusalParam.CartItem()));
        mallFlashSaleController.filterNoRightOfFirstRefusal(param);

    }

    @Test
    public void exportBoostSharingTotal(){

        ExportBoostSharingTotalParam param = new
                ExportBoostSharingTotalParam();
        param.setDomainCode("1");
        param.setTenantCode("1");
        param.setActivityCode("1");
        mallFlashSaleController.exportBoostSharingTotal(param);

    }


    @Test
    public void exportBoostSharingDetail(){

        ExportBoostSharingDetailParam param = new
                ExportBoostSharingDetailParam();
        param.setDomainCode("1");
        param.setTenantCode("1");
        param.setActivityCode("1");
        mallFlashSaleController.exportBoostSharingDetail(param);

    }

}

