package com.gtech.promotion.controller.point2;

import com.gtech.commons.result.PageResult;
import com.gtech.commons.result.Result;
import com.gtech.promotion.controller.point.PointCampaignController;
import com.gtech.promotion.service.point.PointCampaignService;
import com.gtech.promotion.vo.param.point.PointCampaignParam;
import com.gtech.promotion.vo.param.point.query.PointCampaignUniqueParam;
import com.gtech.promotion.vo.param.point.query.QueryPointCampaignParam;
import com.gtech.promotion.vo.result.point.PointCampaignResult;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021-09-08 14:29
 */
@RunWith(MockitoJUnitRunner.class)
public class PointCampaignControllerMockTest {
    @InjectMocks
    private PointCampaignController pointCampaignController;
    @Mock
    private PointCampaignService pointCampaignService;

    @Test
    public void queryPointCampaignList(){
        QueryPointCampaignParam pointAccountQueryVo = new QueryPointCampaignParam();
        Mockito.when(pointCampaignService.queryPointCampaignPage(Mockito.any())).thenReturn(new PageResult<>());
        PageResult<PointCampaignResult> result = pointCampaignController.queryPointCampaignList(pointAccountQueryVo);
        Assert.assertTrue(result.isSuccess());
    }

    @Test
    public void getPointCampaign(){
        PointCampaignUniqueParam pointAccountQueryVo = new PointCampaignUniqueParam();
        Mockito.when(pointCampaignService.getPointCampaign(Mockito.any())).thenReturn(new PointCampaignResult());
        Result<PointCampaignResult> pointCampaign = pointCampaignController.getPointCampaign(pointAccountQueryVo);
        Assert.assertTrue(pointCampaign.isSuccess());
    }

    @Test
    public void updatePointCampaignStatus(){
        PointCampaignUniqueParam.PointCampaignStatusUniqueVo pointAccountQueryVo = new PointCampaignUniqueParam.PointCampaignStatusUniqueVo();
        Mockito.doNothing().when(pointCampaignService).updatePointCampaignStatus(Mockito.any());
        Result<Void> voidResult = pointCampaignController.updatePointCampaignStatus(pointAccountQueryVo);
        Assert.assertTrue(voidResult.isSuccess());
    }

    @Test
    public void createPointCampaign(){
        PointCampaignParam pointAccountQueryVo = new PointCampaignParam();
        Mockito.when(pointCampaignService.savePointCampaign(Mockito.any())).thenReturn("1");
        Result<Map<String, String>> result = pointCampaignController.createPointCampaign(pointAccountQueryVo);
        Assert.assertTrue(result.isSuccess());
    }

    @Test
    public void editPointCampaign(){
        PointCampaignParam pointAccountQueryVo = new PointCampaignParam();
        Mockito.doNothing().when(pointCampaignService).updatePointCampaign(Mockito.any());
        Result<Void> voidResult = pointCampaignController.editPointCampaign(pointAccountQueryVo);
        Assert.assertTrue(voidResult.isSuccess());
    }
}
