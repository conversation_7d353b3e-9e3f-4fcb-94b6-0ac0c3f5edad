package com.gtech.promotion.controller.marketing;

import com.gtech.commons.page.PageData;
import com.gtech.commons.result.PageResult;
import com.gtech.commons.result.Result;
import com.gtech.promotion.component.marketing.LuckyDrawComponent;
import com.gtech.promotion.vo.param.marketing.*;
import com.gtech.promotion.vo.result.coupon.ActivityParticipateInResult;
import com.gtech.promotion.vo.result.marketing.*;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

@RunWith(MockitoJUnitRunner.class)
public class LuckyDrawControllerTest {

    @InjectMocks
    private MallLuckyDrawController mallLuckyDrawController;

    @Mock
    private LuckyDrawComponent luckyDrawComponent;

    @Test
    public void test_getLuckyDrawDetail(){
        LuckyDrawDetailParam param = new LuckyDrawDetailParam();
        param.setDomainCode("1");
        param.setTenantCode("1");
        param.setOrgCode("1");
        param.setActivityCode("1");
        param.setLanguage("1");

        LuckyDrawDetailResult result = new LuckyDrawDetailResult();
        result.setActivityName("123");
        Mockito.when(luckyDrawComponent.getLuckyDrawDetail(Mockito.any())).thenReturn(result);
        Result<LuckyDrawDetailResult> luckyDrawDetail = mallLuckyDrawController.getLuckyDrawDetail(param);
        Assert.assertEquals(result.getActivityName(), luckyDrawDetail.getData().getActivityName());
    }

    @Test
    public void test_draw(){
        LuckyDrawParam param = new LuckyDrawParam();
        param.setDomainCode("1");
        param.setTenantCode("1");
        param.setOrgCode("1");
        param.setActivityCode("1");
        param.setQuality(1);
        param.setMemberCode("1");
        param.setLanguage("1");

        LuckyDrawResult luckyDrawResult = new LuckyDrawResult();
        luckyDrawResult.setPrizes(new ArrayList<>());
        Mockito.when(luckyDrawComponent.getLuckyDrawResult(Mockito.any())).thenReturn(luckyDrawResult);
        Result<LuckyDrawResult> draw = mallLuckyDrawController.draw(param);
        Assert.assertEquals(luckyDrawResult.getPrizes().size(), draw.getData().getPrizes().size());
    }

    @Test
    public void test_queryChanceList(){
        LuckyDrawMemberChanceParam param = new LuckyDrawMemberChanceParam();
        param.setDomainCode("1");
        param.setTenantCode("1");
        param.setOrgCode("1");
        param.setMemberCode("1");
        param.setLanguage("1");

        PageData<LuckyDrawMemberChanceResult> pageResult = new PageData<>();
        Mockito.when(luckyDrawComponent.queryChanceList(Mockito.any())).thenReturn(pageResult);
        PageResult<LuckyDrawMemberChanceResult> pageResult1 = mallLuckyDrawController.queryChanceList(param);
        Assert.assertEquals(pageResult.getTotal(), pageResult1.getData().getTotal());
    }

    @Test
    public void test_queryLuckyRecordList(){
        LuckyDrawMemberRecordParam param = new LuckyDrawMemberRecordParam();
        param.setDomainCode("1");
        param.setTenantCode("1");
        param.setOrgCode("1");
        param.setMemberCode("1");
        param.setActivityCode("1");
        param.setLanguage("1");

        PageData<LuckyDrawMemberRecordResult> pageResult = new PageData<>();
        Mockito.when(luckyDrawComponent.queryLuckyRecordList(Mockito.any())).thenReturn(pageResult);
        PageResult<LuckyDrawMemberRecordResult> pageResult1 = mallLuckyDrawController.queryLuckyRecordList(param);
        Assert.assertEquals(pageResult.getTotal(), pageResult1.getData().getTotal());
    }

    @Test
    public void sendTicketByProduct(){
        JudgeQualificationParam param = new JudgeQualificationParam();
        param.setDomainCode("1");
        param.setTenantCode("1");
        param.setOrgCode("1");
        param.setMemberCode("1");
        param.setQuantity(new BigDecimal(1));
        param.setAmount(new BigDecimal(1));
        List<String> productCodes = new ArrayList<>();
        productCodes.add("1");
        param.setProductCodes(productCodes);
        Mockito.when(luckyDrawComponent.judgeQualification(Mockito.any())).thenReturn(new ArrayList<>());

        Result<List<ActivityParticipateInResult>> pageResult1 = mallLuckyDrawController.sendTicketByProduct(param);
        Assert.assertEquals(0,pageResult1.getData().size());
    }

    @Test
    public void eligibleActivities(){
        EligibleActivitiesParam param = new EligibleActivitiesParam();
        param.setDomainCode("1");
        param.setTenantCode("1");
        param.setOrgCode("1");
        param.setMemberCode("1");
        param.setQuantity(new BigDecimal(1));
        param.setAmount(new BigDecimal(1));
        List<String> productCodes = new ArrayList<>();
        productCodes.add("1");
        param.setProductCodes(productCodes);
        Mockito.when(luckyDrawComponent.eligibleActivities(Mockito.any())).thenReturn(new ArrayList<>());
        Result<List<ActivityParticipateInResult>> pageResult1 = mallLuckyDrawController.eligibleActivities(param);
        Assert.assertEquals(0,pageResult1.getData().size());
    }

    @Test
    public void queryMemberLuckyDrawFrequency(){
        QueryMemberLuckyDrawFrequencyParam param = new QueryMemberLuckyDrawFrequencyParam();
        param.setDomainCode("1");
        param.setTenantCode("1");
        param.setOrgCode("1");
        param.setMemberCode("1");
        param.setActivityCode("1s");
        Mockito.when(luckyDrawComponent.queryMemberLuckyDrawFrequency(Mockito.any())).thenReturn(null);
        Result<QueryMemberLuckyDrawFrequencyResult> pageResult1 = mallLuckyDrawController.queryMemberLuckyDrawFrequency(param);
        Assert.assertEquals(null,pageResult1.getData());
    }

    @Test
    public void updateFrozenStatus(){
        UpdateFrozenStatusParam param = new UpdateFrozenStatusParam();
        param.setDomainCode("1");
        param.setTenantCode("1");
        param.setOrgCode("1");
        param.setMemberCode("1");
        param.setActivityCode("1s");
        param.setQuality(1);
        param.setFrozenStatus("01");
        Mockito.doNothing().when(luckyDrawComponent).updateFrozenStatus(Mockito.any());
        Result<QueryMemberLuckyDrawFrequencyResult> pageResult1 = mallLuckyDrawController.updateFrozenStatus(param);
        Assert.assertEquals(null,pageResult1.getData());
    }
}
