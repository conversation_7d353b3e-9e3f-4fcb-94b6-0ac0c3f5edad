package com.gtech.promotion.controller;

import com.gtech.commons.result.Result;
import com.gtech.promotion.component.activity.ActivityStatisticDomain;
import com.gtech.promotion.controller.activity.ActivityStatisticController;
import com.gtech.promotion.dto.in.activity.ActivityActiveInDTO;
import com.gtech.promotion.dto.in.activity.ActivityTenantInDTO;
import com.gtech.promotion.dto.in.activity.QueryActivityStatisticInDTO;
import com.gtech.promotion.dto.in.activity.QueryActivityStatisticSumInDTO;
import com.gtech.promotion.dto.out.activity.ActivityStatisticQueryOutDTO;
import com.gtech.promotion.dto.out.activity.ActivityStatisticSumQueryOutDTO;
import com.gtech.promotion.dto.out.activity.ActivityTenantOutDTO;
import com.gtech.promotion.dto.out.activity.ActivityTotalOutDto;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.List;

@RunWith(MockitoJUnitRunner.class)
public class ActivityStatisticControllerTest {

    @InjectMocks
    private ActivityStatisticController activityStatisticController;

    @Mock
    private ActivityStatisticDomain statisticDomain;

    @Test
    public void createActivityStatistic() {
        Mockito.doNothing().when(statisticDomain).createActivityStatistic();
        Result<String> stringResult = activityStatisticController.createActivityStatistic();
        Assert.assertEquals("调用成功", stringResult.getData());
    }

    @Test
    public void queryActivityStatistic() {
        QueryActivityStatisticInDTO dto = new QueryActivityStatisticInDTO();
        dto.setTenantCode("1");
        dto.setActivityCode("1");
        dto.setEndTime("20210805");
        dto.setStartTime("20210805");
        Mockito.when(statisticDomain.queryActivityStatistic(Mockito.any())).thenReturn(new ArrayList<>());
        Result<List<ActivityStatisticQueryOutDTO>> stringResult = activityStatisticController.queryActivityStatistic(dto);
        Assert.assertEquals(0, stringResult.getData().size());
    }

    @Test
    public void queryActivityStatisticSum() {
        QueryActivityStatisticSumInDTO dto = new QueryActivityStatisticSumInDTO();
        dto.setTenantCode("1");
        dto.setActivityCode("1");
        Mockito.when(statisticDomain.queryActivityStatisticSum(Mockito.any())).thenReturn(null);
        Result<ActivityStatisticSumQueryOutDTO> stringResult = activityStatisticController.queryActivityStatisticSum(dto);
        Assert.assertEquals(null, stringResult.getData());
    }

    @Test
    public void queryActivityActiveTotal() {
        ActivityActiveInDTO dto = new ActivityActiveInDTO();
        dto.setTenantCode("1");
        Mockito.when(statisticDomain.queryActivityActiveTotal(Mockito.any())).thenReturn(null);
        Result<ActivityTotalOutDto> stringResult = activityStatisticController.queryActivityActiveTotal(dto);
        Assert.assertEquals(null, stringResult.getData().getActivityTotal());
    }

    @Test
    public void queryActivityTenantData() {
        ActivityTenantInDTO dto = new ActivityTenantInDTO();
        dto.setTenantCode("1");
        dto.setStartTime("20190305000000");
        dto.setEndTime("20200305000000");
        Mockito.when(statisticDomain.queryActivityTenantData(Mockito.any())).thenReturn(null);
        Result<ActivityTenantOutDTO> stringResult = activityStatisticController.queryActivityTenantData(dto);
        Assert.assertEquals(null, stringResult.getData());
    }

    @Test
    public void checkStartAndEndTimeTest(){
        ActivityTenantInDTO tenantDTO = new ActivityTenantInDTO();
        tenantDTO.setTenantCode("1");
//        tenantDTO.setStartTime("20190305000000");
        tenantDTO.setEndTime("20200305000000");
//        Mockito.when(statisticDomain.queryActivityTenantData(Mockito.any())).thenReturn(null);
        try {
            activityStatisticController.queryActivityTenantData(tenantDTO);
        }catch (Exception e){

        }

        tenantDTO.setStartTime("20190305000000");
        tenantDTO.setEndTime(null);
        try {
            activityStatisticController.queryActivityTenantData(tenantDTO);
        }catch (Exception e){

        }

        tenantDTO.setEndTime("TEST");
        tenantDTO.setEndTime("TEST");
        try {
            activityStatisticController.queryActivityTenantData(tenantDTO);
        }catch (Exception e){

        }
        tenantDTO.setStartTime("20190305000000");
        try {
            activityStatisticController.queryActivityTenantData(tenantDTO);
        }catch (Exception e){

        }
        tenantDTO.setEndTime("20180305000000");
        try {
            activityStatisticController.queryActivityTenantData(tenantDTO);
        }catch (Exception e){

        }
    }
}
