package com.gtech.promotion.controller.marketing;

import com.gtech.commons.result.PageResult;
import com.gtech.commons.result.Result;
import com.gtech.member.web.vo.result.QueryMemberCodesResult;
import com.gtech.member.web.vo.result.TagMemberListResult;
import com.gtech.promotion.component.marketing.TicketComponent;
import com.gtech.promotion.exception.PromotionException;
import com.gtech.promotion.feign.MemberFeignClient;
import com.gtech.promotion.vo.param.marketing.TicketReleaseParam;
import com.gtech.promotion.vo.param.marketing.TicketReleaseQueryParam;
import com.gtech.promotion.vo.param.marketing.TicketSendParam;
import com.gtech.promotion.vo.result.marketing.TicketReleaseQueryResult;
import com.gtech.promotion.vo.result.marketing.TicketSendResult;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.List;

@RunWith(MockitoJUnitRunner.class)
public class TicketControllerTest {

    @InjectMocks
    private TicketController ticketController;

    @Mock
    private TicketComponent ticketComponent;

    @Mock
    private MemberFeignClient memberFeignClient;

    @Test
    public void test_release(){
        TicketReleaseParam param = new TicketReleaseParam();
        param.setDomainCode("1");
        param.setTenantCode("1");
        param.setOrgCode("1");
        param.setActivityCode("1");
        param.setOperateUser("1");
        param.setQuality(1L);

        Mockito.when(ticketComponent.release(Mockito.any())).thenReturn("123");
        Result<String> release = ticketController.release(param);
        Assert.assertEquals("123", release.getData());
    }

    @Test
    public void test_queryRelease(){
        TicketReleaseQueryParam param = new TicketReleaseQueryParam();
        param.setDomainCode("1");
        param.setTenantCode("1");
        param.setOrgCode("1");
        param.setActivityCode("1");

        PageResult<TicketReleaseQueryResult> pageResult = new PageResult<>();
        Mockito.when(ticketComponent.queryRelease(Mockito.any(), Mockito.any())).thenReturn(pageResult);
        PageResult<TicketReleaseQueryResult> pageResult1 = ticketController.queryRelease(param);
        Assert.assertEquals(pageResult.getData().getTotal(), pageResult1.getData().getTotal());
    }

    @Test
    public void test_sendTicket(){
        TicketSendParam param = new TicketSendParam();
        param.setDomainCode("1");
        param.setTenantCode("1");
        param.setOrgCode("1");
        param.setActivityCode("1");
        param.setQuality(1);
        List<String> list = new ArrayList<>();
        list.add("12313");
        param.setMemberCodes(list);

        List<TicketSendResult> sendResults = new ArrayList<>();
        Mockito.when(ticketComponent.sendTicket(Mockito.any())).thenReturn(sendResults);
        Result<Integer> listResult = ticketController.sendTicket(param);
        Assert.assertEquals(sendResults.size(), (long)listResult.getData());
    }

    @Test(expected = PromotionException.class)
    public void test_sendTicket_1(){
        TicketSendParam param = new TicketSendParam();
        param.setDomainCode("1");
        param.setTenantCode("1");
        param.setOrgCode("1");
        param.setActivityCode("1");
        param.setQuality(1);
        List<String> list = new ArrayList<>();
        list.add("12313");
        param.setMemberMobiles(list);

        PageResult<QueryMemberCodesResult> resultPageResult = new PageResult<>();
        Mockito.when(memberFeignClient.queryMemberCodesByMobiles(Mockito.any())).thenReturn(resultPageResult);
        ticketController.sendTicket(param);
    }

    @Test
    public void test_sendTicket_2(){
        TicketSendParam param = new TicketSendParam();
        param.setDomainCode("1");
        param.setTenantCode("1");
        param.setOrgCode("1");
        param.setActivityCode("1");
        param.setQuality(1);
        List<String> list = new ArrayList<>();
        list.add("12313");
        param.setMemberMobiles(list);

        List<QueryMemberCodesResult> list1 = new ArrayList<>();
        QueryMemberCodesResult result = new QueryMemberCodesResult();
        result.setMemberCode("11");
        list1.add(result);
        PageResult<QueryMemberCodesResult> resultPageResult = new PageResult<>(list1, 1L);
        Mockito.when(memberFeignClient.queryMemberCodesByMobiles(Mockito.any())).thenReturn(resultPageResult);
        List<TicketSendResult> sendResults = new ArrayList<>();
        Mockito.when(ticketComponent.sendTicket(Mockito.any())).thenReturn(sendResults);
        Result<Integer> integerResult = ticketController.sendTicket(param);
        Assert.assertEquals(sendResults.size(), (long)integerResult.getData());
    }

    @Test(expected = PromotionException.class)
    public void test_sendTicket_3(){
        TicketSendParam param = new TicketSendParam();
        param.setDomainCode("1");
        param.setTenantCode("1");
        param.setOrgCode("1");
        param.setActivityCode("1");
        param.setQuality(1);
        List<String> list = new ArrayList<>();
        list.add("12313");
        param.setMemberTagCodes(list);

        PageResult<TagMemberListResult> resultPageResult = new PageResult<>();
        Mockito.when(memberFeignClient.queryMemberCodesByTagCodes(Mockito.any())).thenReturn(resultPageResult);
        ticketController.sendTicket(param);
    }

    @Test
    public void test_sendTicket_4(){
        TicketSendParam param = new TicketSendParam();
        param.setDomainCode("1");
        param.setTenantCode("1");
        param.setOrgCode("1");
        param.setActivityCode("1");
        param.setQuality(1);
        List<String> list = new ArrayList<>();
        list.add("12313");
        param.setMemberTagCodes(list);

        List<TagMemberListResult> list1 = new ArrayList<>();
        TagMemberListResult result = new TagMemberListResult();
        result.setMemberCode("11");
        list1.add(result);
        PageResult<TagMemberListResult> resultPageResult = new PageResult<>(list1, 1L);
        Mockito.when(memberFeignClient.queryMemberCodesByTagCodes(Mockito.any())).thenReturn(resultPageResult);
        List<TicketSendResult> sendResults = new ArrayList<>();
        Mockito.when(ticketComponent.sendTicket(Mockito.any())).thenReturn(sendResults);
        Result<Integer> listResult = ticketController.sendTicket(param);
        Assert.assertEquals(sendResults.size(), (long)listResult.getData());
    }
}
