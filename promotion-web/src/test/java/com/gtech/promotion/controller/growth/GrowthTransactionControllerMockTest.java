package com.gtech.promotion.controller.growth;

import com.gtech.commons.page.PageData;
import com.gtech.commons.result.PageResult;
import com.gtech.commons.result.Result;
import com.gtech.promotion.service.growth.GrowthTransactionService;
import com.gtech.promotion.vo.param.growth.query.GetGrowthTransactionParam;
import com.gtech.promotion.vo.param.growth.query.QueryGrowthTransactionParam;
import com.gtech.promotion.vo.result.growth.GrowthTransactionResult;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2021/3/30 14:13
 */
@RunWith(MockitoJUnitRunner.class)
public class GrowthTransactionControllerMockTest {

    @InjectMocks
    private GrowthTransactionController growthTransactionController;

    @Mock
    private GrowthTransactionService growthTransactionService;

    @Test
    public void queryGrowthTransactionList(){
        QueryGrowthTransactionParam param = new QueryGrowthTransactionParam();
        param.setDomainCode("1");
        param.setTenantCode("1");
        PageResult<GrowthTransactionResult> resultPageResult = new PageResult<>();
        PageData<GrowthTransactionResult> data = new PageData<>();
        List<GrowthTransactionResult> list = new ArrayList<>();
        GrowthTransactionResult result = new GrowthTransactionResult();
        result.setCreateTime(new Date());
        result.setUpdateTime(new Date());
        list.add(result);
        data.setList(list);
        resultPageResult.setData(data);

        Mockito.when(growthTransactionService.queryGrowthTransactionPage(Mockito.any())).thenReturn(resultPageResult);
        PageResult<GrowthTransactionResult> pageResult = growthTransactionController.queryGrowthTransactionList(param);
        Assert.assertTrue(pageResult.isSuccess());
    }

    @Test
    public void getGrowthTransaction(){
        GetGrowthTransactionParam param = new GetGrowthTransactionParam();
        param.setDomainCode("1");
        param.setTenantCode("1");
        param.setTransactionSn("1");
        GrowthTransactionResult result = new GrowthTransactionResult();
        result.setCreateTime(new Date());
        result.setUpdateTime(new Date());

        Mockito.when(growthTransactionService.getGrowthTransaction(Mockito.any())).thenReturn(result);
        Result<GrowthTransactionResult> resultResult = growthTransactionController.getGrowthTransaction(param);
        Assert.assertTrue(resultResult.isSuccess());
    }
}
