package com.gtech.promotion.controller;

import com.gtech.promotion.controller.activity.OrderController;
import com.gtech.promotion.dao.model.activity.ActivityFunctionParamRankModel;
import com.gtech.promotion.dao.model.activity.FunctionParamModel;
import com.gtech.promotion.dao.model.activity.QualificationModel;
import com.gtech.promotion.dao.model.activity.TPromoIncentiveLimitedVO;
import com.gtech.promotion.dto.cache.ActivityCacheDTO;
import com.gtech.promotion.vo.bean.Giveaway;
import com.gtech.promotion.vo.bean.ShoppingCartItem;
import com.gtech.promotion.vo.bean.ShoppingCartStore;
import com.gtech.promotion.vo.param.activity.CancelOrderParam;
import com.gtech.promotion.vo.param.activity.ConfirmOrderParam;
import com.gtech.promotion.vo.param.activity.CreateOrderParam;
import com.gtech.promotion.vo.param.activity.SalesReturnOrderParam;
import org.apache.commons.collections4.map.HashedMap;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.junit.MockitoJUnitRunner;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;


@RunWith(MockitoJUnitRunner.class)
public class OrderControllerExceptionTest {

    @InjectMocks
    private OrderController orderController;

    @Test(expected = NullPointerException.class)
    public void createOrder(){
        CreateOrderParam param = new CreateOrderParam();
        param.setOrderNo("1");
        param.setPromoDeductedAmount(new BigDecimal(1));
        param.setFreePostage(1);
        param.setTenantCode("1");
        param.setDomainCode("1");
        param.setMemberCode("1");
        List<ShoppingCartStore> cartStoreList = new ArrayList<>();
        ShoppingCartStore store = new ShoppingCartStore();
        List<ShoppingCartItem> cartItemList = new ArrayList<>();
        ShoppingCartItem shoppingCartItem = new ShoppingCartItem();
        shoppingCartItem.setSkuCode("1");
        shoppingCartItem.setSelectionFlag("1");
        shoppingCartItem.setProductPrice(new BigDecimal(1));
        shoppingCartItem.setQuantity(1);
        cartItemList.add(shoppingCartItem);
        store.setCartItemList(cartItemList);
        cartStoreList.add(store);
        param.setCartStoreList(cartStoreList);
        List<CreateOrderParam.PromoGiveaway> promoGiveaways = new ArrayList<>();
        CreateOrderParam.PromoGiveaway promoGiveaway = new CreateOrderParam.PromoGiveaway();
        promoGiveaway.setActivityCode("1");
        List<Giveaway> giveaways = new ArrayList<>();
        promoGiveaway.setGiveaways(giveaways);
        promoGiveaways.add(promoGiveaway);
        Map<String, ActivityCacheDTO> activityCacheMap = new HashedMap<>();
        ActivityCacheDTO activityCacheDTO = new ActivityCacheDTO();
        List<ActivityFunctionParamRankModel> rankModelList = new ArrayList<>();
        ActivityFunctionParamRankModel model = new ActivityFunctionParamRankModel();
        model.setId("1");
        rankModelList.add(model);
        activityCacheDTO.setPromoFuncRanks(rankModelList);
        List<FunctionParamModel> paramModels = new ArrayList<>();
        FunctionParamModel model1 = new FunctionParamModel();
        model1.setRankId("1");
        paramModels.add(model1);
        activityCacheDTO.setPromoFuncParams(paramModels);
        List<TPromoIncentiveLimitedVO> list = new ArrayList<>();
        activityCacheDTO.setIncentiveLimiteds(list);
        List<QualificationModel> qualificationModels = new ArrayList<>();
        activityCacheDTO.setQualificationModels(qualificationModels);
        activityCacheDTO.setGiveaways(giveaways);
        activityCacheMap.put("1",activityCacheDTO);
        orderController.createOrder(param);
    }
    @Test(expected = Exception.class)
    public void cancelOrder(){
        CancelOrderParam param = new CancelOrderParam();
        param.setDomainCode("1");
        param.setTenantCode("1");
        param.setOrderNo("1");
        orderController.cancelOrder(param);
    }

    @Test(expected = Exception.class)
    public void confirmOrder(){
        ConfirmOrderParam param = new ConfirmOrderParam();
        param.setDomainCode("1");
        param.setTenantCode("1");
        param.setOrderNo("1");
        orderController.confirmOrder(param);
    }

    @Test(expected = Exception.class)
    public void salesReturnOrder(){
        SalesReturnOrderParam param = new SalesReturnOrderParam();
        param.setDomainCode("1");
        param.setTenantCode("1");
        param.setOrderNo("1");
        orderController.salesReturnOrder(param);
    }

}
