package com.gtech.promotion.controller;

import com.github.pagehelper.PageInfo;
import com.gtech.commons.exception.ErrorCode;
import com.gtech.commons.page.PageData;
import com.gtech.commons.result.PageResult;
import com.gtech.commons.result.Result;
import com.gtech.promotion.component.activity.QualificationDomain;
import com.gtech.promotion.component.coupon.*;
import com.gtech.promotion.controller.coupon.MallCouponController;
import com.gtech.promotion.dao.entity.coupon.PromoCouponSendDetailEntity;
import com.gtech.promotion.dao.entity.coupon.PromoCouponSendLogEntity;
import com.gtech.promotion.dao.model.activity.ActivityModel;
import com.gtech.promotion.dao.model.activity.PromoCouponSendDetailModel;
import com.gtech.promotion.dao.model.coupon.CouponSendLogVO;
import com.gtech.promotion.domain.coupon.CouponDomain;
import com.gtech.promotion.domain.coupon.CouponReleaseDomain;
import com.gtech.promotion.dto.out.activity.ShoppingCartOutDTO;
import com.gtech.promotion.dto.out.coupon.CouponInfoOutDTO;
import com.gtech.promotion.exception.ErrorCodes;
import com.gtech.promotion.exception.PromotionCouponException;
import com.gtech.promotion.service.coupon.CouponSendDetailService;
import com.gtech.promotion.service.coupon.CouponSendLogService;
import com.gtech.promotion.service.coupon.PromoCouponReleaseService;
import com.gtech.promotion.vo.bean.MemberInfo;
import com.gtech.promotion.vo.bean.ShoppingCartItem;
import com.gtech.promotion.vo.bean.ShoppingCartStore;
import com.gtech.promotion.vo.param.coupon.*;
import com.gtech.promotion.vo.result.activity.CalcShoppingCartResult;
import com.gtech.promotion.vo.result.coupon.*;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RunWith(MockitoJUnitRunner.class)
public class MallCouponControllerTest {

    @InjectMocks
    private MallCouponController mallCouponController;

    @Mock
    private CouponReserveComponent couponReserveComponent;

    @Mock
    private FilterCouponComponent filterCouponDomain;

    @Mock
    private CouponInnerCodeComponent couponInnerCodeDomain;

    @Mock
    private CouponActivityComponent couponActivityDomain;

    @Mock
    private CouponCodeUserComponent couponUserCodeDomain;

    @Mock
    private PromoCouponReleaseService couponReleaseService;

    @Mock
    private ReceiveCouponBatchComponent receiveCouponBatchDomain;

    @Mock
    private QualificationDomain qualificationDomain;
    @Mock
    private CouponSendLogService couponSendLogService;
    @Mock
    private CouponSendDetailService couponSendDetailService;

    @Mock
    private CouponCodeUserComponent couponCodeUserComponent;

    @Test
    public void sendAnonymousCoupon(){
        List<SendAnonymousCouponResult> results = new ArrayList<>();
        SendAnonymousCoupon param = new SendAnonymousCoupon();
        param.setDomainCode("1");
        param.setTenantCode("1");
        param.setActivityCode("1");
        param.setReleaseCode("1");
        param.setSendTotal(1);
        Mockito.when(receiveCouponBatchDomain.sendAnonymousCoupon(Mockito.any())).thenReturn(results);
        Result<List<SendAnonymousCouponResult>> listResult = mallCouponController.sendAnonymousCoupon(param);
        Assert.assertEquals(0,listResult.getData().size());
    }


    @Test
    public void queryUsedCouponList(){
        QueryCouponUsedListParam param = new QueryCouponUsedListParam();
        param.setDomainCode("1");
        param.setTenantCode("1");
        Mockito.when(couponUserCodeDomain.queryUsedCouponList(Mockito.any())).thenReturn(new PageData<>());
        PageResult<QueryCouponUsedListResult> resultPageResult = mallCouponController.queryUsedCouponList(param);
        Assert.assertNotNull(resultPageResult.getData());
    }
    @Test
    public void querySendCouponLogList(){
    	QueryCouponSendLogParam param = new QueryCouponSendLogParam();
    	param.setTenantCode("1");
    	param.setActivityCode("1");
    	List<PromoCouponSendLogEntity> couponSendLogEntityList =new ArrayList<>();
    	PromoCouponSendLogEntity promoCouponSendLogEntity=new PromoCouponSendLogEntity();
    	promoCouponSendLogEntity.setActivityCode("1");
    	promoCouponSendLogEntity.setSendBatchNo("1");
    	couponSendLogEntityList.add(promoCouponSendLogEntity);
    	 PageInfo<PromoCouponSendLogEntity> couponSendLogEntityPageInfo = new PageInfo<>(couponSendLogEntityList);
    	 List<PromoCouponSendDetailEntity> sendDetailEntityList  =new ArrayList<>();
    	 PromoCouponSendDetailEntity promoCouponSendDetailEntity=new PromoCouponSendDetailEntity();
    	 promoCouponSendDetailEntity.setBatchNo("1");   
    	 sendDetailEntityList.add(promoCouponSendDetailEntity);
    	 Mockito.when(couponSendLogService.queryCouponSendLogList(Mockito.any())).thenReturn(couponSendLogEntityPageInfo);
    	 Mockito.when(couponSendDetailService.queryCouponSendDetailList(Mockito.any())).thenReturn(sendDetailEntityList);
    	PageResult<CouponSendLogVO> resultPageResult = mallCouponController.querySendCouponLogList(param);
    	Assert.assertNotNull(resultPageResult.getData());
    }

    @Test
    public void chooseCouponsByCart_empty() {
        FilterCouponsByCartParam param = new FilterCouponsByCartParam();
        param.setDomainCode("1");
        param.setTenantCode("1");
        param.setMemberCode("1");
        List<ShoppingCartStore> cartStoreList = new ArrayList<>();
        ShoppingCartStore store = new ShoppingCartStore();
        List<ShoppingCartItem> cartItemList = new ArrayList<>();
        ShoppingCartItem cartItem = new ShoppingCartItem();
        cartItem.setProductPrice(new BigDecimal("10"));
        cartItem.setQuantity(10);
        cartItem.setSelectionFlag("00");
        cartItem.setSkuCode("1");
        cartItemList.add(cartItem);
        store.setCartItemList(cartItemList);
        cartStoreList.add(store);
        param.setCartStoreList(cartStoreList);
        Mockito.when(filterCouponDomain.chooseCouponsByCart(Mockito.any())).thenReturn(new ArrayList<>());
        Result<List<CalcShoppingCartResult>> listResult = mallCouponController.chooseCouponsByCart(param);
        Assert.assertTrue(listResult.isSuccess());
    }

    @Test
    public void chooseCouponsByCart_not_empty() {
        FilterCouponsByCartParam param = new FilterCouponsByCartParam();
        param.setDomainCode("1");
        param.setTenantCode("1");
        param.setMemberCode("1");
        List<ShoppingCartStore> cartStoreList = new ArrayList<>();
        ShoppingCartStore store = new ShoppingCartStore();
        List<ShoppingCartItem> cartItemList = new ArrayList<>();
        ShoppingCartItem cartItem = new ShoppingCartItem();
        cartItem.setProductPrice(new BigDecimal("10"));
        cartItem.setQuantity(10);
        cartItem.setSelectionFlag("00");
        cartItem.setSkuCode("1");
        cartItemList.add(cartItem);
        store.setCartItemList(cartItemList);
        cartStoreList.add(store);
        param.setCartStoreList(cartStoreList);
        List<ShoppingCartOutDTO> shoppingCartOutDTOS = new ArrayList<>();
        ShoppingCartOutDTO dto = new ShoppingCartOutDTO();
        ErrorCode errorCode = ErrorCodes.ERROR_UNKNOWN;
        dto.setFailedReason(errorCode);
        shoppingCartOutDTOS.add(dto);
        Mockito.when(filterCouponDomain.chooseCouponsByCart(Mockito.any())).thenReturn(shoppingCartOutDTOS);
        Result<List<CalcShoppingCartResult>> listResult = mallCouponController.chooseCouponsByCart(param);
        Assert.assertTrue(listResult.isSuccess());
    }
    @Test
    public void filterCouponsByCart() {
        FilterCouponsByCartParam param = new FilterCouponsByCartParam();
        param.setDomainCode("1");
        param.setTenantCode("1");
        param.setMemberCode("1");
        List<ShoppingCartStore> cartStoreList = new ArrayList<>();
        ShoppingCartStore store = new ShoppingCartStore();
        List<ShoppingCartItem> cartItemList = new ArrayList<>();
        ShoppingCartItem cartItem = new ShoppingCartItem();
        cartItem.setProductPrice(new BigDecimal("10"));
        cartItem.setQuantity(10);
        cartItem.setSelectionFlag("00");
        cartItem.setSkuCode("1");
        cartItemList.add(cartItem);
        store.setCartItemList(cartItemList);
        cartStoreList.add(store);
        param.setCartStoreList(cartStoreList);

        Mockito.when(filterCouponDomain.filterCoupon(Mockito.any())).thenReturn(new ArrayList<>());
        Result<List<FilterCouponsByCartResult>> listResult = mallCouponController.filterCouponsByCart(param);
        Assert.assertTrue(listResult.isSuccess());
    }

    @Test
    public void queryCouponActivityListByProduct() {
        QueryCouponActivityListByProductParam param = new QueryCouponActivityListByProductParam();
        param.setDomainCode("1");
        param.setTenantCode("1");
//        Mockito.when(filterCouponDomain.searchActivityByProduct(Mockito.any())).thenReturn(new ArrayList<>());
        Result<List<QueryCouponActivityListByProductResult>> listResult = mallCouponController.queryCouponActivityListByProduct(param);
        Assert.assertTrue(listResult.isSuccess());
    }


    @Test
    public void queryCouponActivityListByProduct_orgCode() {
        QueryCouponActivityListByProductParam param = new QueryCouponActivityListByProductParam();
        param.setDomainCode("1");
        param.setTenantCode("1");
        param.setOrgCode("11");
        //Mockito.when(filterCouponDomain.searchActivityByProduct(Mockito.any())).thenReturn(new ArrayList<>());
        Result<List<QueryCouponActivityListByProductResult>> listResult = mallCouponController.queryCouponActivityListByProduct(param);
        Assert.assertTrue(listResult.isSuccess());
    }

    @Test
    public void queryCouponActivityListByProductList() {
        QueryCouponActivityListByProductListParam param = new QueryCouponActivityListByProductListParam();
        param.setDomainCode("1");
        param.setTenantCode("1");
        ArrayList<QueryCouponActivityListByProductListParam.Product> products = new ArrayList<>();
        QueryCouponActivityListByProductListParam.Product product = new QueryCouponActivityListByProductListParam.Product();
        product.setProductCode("1");
        product.setSkuCode("1");

        products.add(product);
        param.setProductList(products);
        mallCouponController.queryCouponActivityListByProductList(param);

    }

    @Test(expected = PromotionCouponException.class)
    public void checkUserLimit() {
        List<PromoCouponSendDetailModel> couponSendDetailModelList  = new ArrayList<>();
        PromoCouponSendDetailModel promoCouponSendDetailModel = new PromoCouponSendDetailModel();
        promoCouponSendDetailModel.setUserCode("1");
        promoCouponSendDetailModel.setFailReason("1");
        promoCouponSendDetailModel.setActivityCode("1");
        promoCouponSendDetailModel.setTenantCode("1");
        promoCouponSendDetailModel.setBatchNo("1");
        couponSendDetailModelList.add(promoCouponSendDetailModel);
        mallCouponController.checkUserLimit(couponSendDetailModelList);

    }

    @Test
    public void queryCouponActivityListByStore() {
        QueryCouponActivityListByStoreParam param = new QueryCouponActivityListByStoreParam();
        param.setDomainCode("1");
        param.setTenantCode("1");

        Mockito.when(couponActivityDomain.queryCouponActivityInStore(Mockito.any())).thenReturn(new PageInfo<>());
        PageResult<QueryCouponActivityListByStoreResult> result = mallCouponController.queryCouponActivityListByStore(param);
        Assert.assertTrue(result.isSuccess());
    }

//    @Test
//    public void queryCouponActivityListByMemberTag(){
//        QueryCouponActivityListByMemberTagParam param = new QueryCouponActivityListByMemberTagParam();
//        try {
//            mallCouponController.queryCouponActivityListByMemberTag(param);
//        }catch (Exception e){}
//        param.setTenantCode("100001");
//        try {
//            mallCouponController.queryCouponActivityListByMemberTag(param);
//        }catch (Exception e){}
//        List<String> memberTagList = new ArrayList<>();
//        memberTagList.add("member1");
//        try {
//            mallCouponController.queryCouponActivityListByMemberTag(param);
//        }catch (Exception e){}
//    }

    @Test
    public void queryCouponListByUser() {
        QueryCouponListByUserParam param = new QueryCouponListByUserParam();
        param.setDomainCode("1");
        param.setTenantCode("1");
        param.setUserCode("1");

        Mockito.when(filterCouponDomain.queryUserCouponList(Mockito.any())).thenReturn(new PageData<>());
        PageResult<QueryCouponListByUserResult> result = mallCouponController.queryCouponListByUser(param);
        Assert.assertTrue(result.isSuccess());
    }

    @Test
    public void findCouponDetail() {
        FindCouponDetailParam param = new FindCouponDetailParam();
        param.setDomainCode("1");
        param.setTenantCode("1");
        param.setActivityCode("1");

        Mockito.when(couponActivityDomain.findCouponActivityByActivityCode(Mockito.any())).thenReturn(new CouponInfoOutDTO());
        Result<FindCouponDetailResult> couponDetail = mallCouponController.findCouponDetail(param);
        Assert.assertTrue(couponDetail.isSuccess());
    }

    @Test
    public void findUserCoupon() {
        FindUserCouponParam param = new FindUserCouponParam();
        param.setDomainCode("1");
        param.setTenantCode("1");
        param.setUserCode("1");
        param.setCouponCode("1");

        Mockito.when(couponInnerCodeDomain.getCouponInfo(Mockito.any())).thenReturn(new CouponDomain());
        Result<FindUserCouponResult> userCoupon = mallCouponController.findUserCoupon(param);
        Assert.assertTrue(userCoupon.isSuccess());
    }

    @Test
    public void verifyCouponCode() {
        VerifyCouponCodeParam param = new VerifyCouponCodeParam();
        param.setDomainCode("1");
        param.setTenantCode("1");
        param.setUserCode("1");
        param.setCouponCodes("1");
        param.setUsedRefId("1");

        Mockito.when(couponUserCodeDomain.queryCheckAndUseCouponCode(Mockito.any())).thenReturn(1);
        Result<Integer> result = mallCouponController.verifyCouponCode(param);
        Assert.assertTrue(result.isSuccess());
    }

    @Test
    public void sendCouponToUser() {
        SendCouponToUserParam param = new SendCouponToUserParam();
        param.setDomainCode("1");
        param.setTenantCode("1");
        param.setUserCode("1");
        param.setActivityCode("1");
        param.setTakeLabel("01");
        param.setFrozenStatus(1);
        param.setReceiveCount(1);
        ActivityModel activityModel = new ActivityModel();
        activityModel.setCouponType("01");
        List<CouponReleaseDomain> couponRelease = new ArrayList<>();
        CouponReleaseDomain domain = new CouponReleaseDomain();
        domain.setActivityCode("1");
        couponRelease.add(domain);
        Map<String, String> userCouponMap = new HashMap<>();
        userCouponMap.put("1", "1");
//        Mockito.when(couponActivityDomain.findSendCouponValidActivity(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(activityModel);
//        Mockito.when(qualificationDomain.checkQualificationForSendCoupon(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(true);
//        Mockito.when(couponReleaseService.queryCanReceiveReleases(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(couponRelease);
//        Mockito.when(couponInnerCodeDomain.allocateCoupon(Mockito.anyList(), Mockito.anyInt(), Mockito.anyString(), Mockito.any(), Mockito.anyList(),
  //              Mockito.anyInt())).thenReturn(userCouponMap);
        Result<String> stringResult = mallCouponController.sendCouponToUser(param);
        Assert.assertTrue(stringResult.isSuccess());
    }



    @Test
    public void sendCouponToUser_empty_1() {
        SendCouponToUserParam param = new SendCouponToUserParam();
        param.setDomainCode("1");
        param.setTenantCode("1");
        param.setUserCode("1");
        param.setActivityCode("1");
        param.setTakeLabel("01");
        param.setFrozenStatus(1);
        param.setReceiveCount(1);
        ActivityModel activityModel = new ActivityModel();
        activityModel.setCouponType("01");
        List<CouponReleaseDomain> couponRelease = new ArrayList<>();
        CouponReleaseDomain domain = new CouponReleaseDomain();
        domain.setActivityCode("1");
        couponRelease.add(domain);
        //Map<String, String> userCouponMap = new HashMap<>();
        //userCouponMap.put("1","1");

//        Mockito.when(couponActivityDomain.findValidActivity(Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(activityModel);
//        Mockito.when(qualificationDomain.checkQualificationForSendCoupon(Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(true);
//        Mockito.when(couponReleaseService.queryCanReceiveReleases(Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(couponRelease);
        Result<String> stringResult = mallCouponController.sendCouponToUser(param);
        Assert.assertTrue(stringResult.isSuccess());
    }
    @Test
    public void sendCouponToUser_empty() {
        SendCouponToUserParam param = new SendCouponToUserParam();
        param.setDomainCode("1");
        param.setTenantCode("1");
        param.setUserCode("1");
        param.setActivityCode("1");
        param.setTakeLabel("01");
        param.setFrozenStatus(1);
        param.setReceiveCount(1);
        ActivityModel activityModel = new ActivityModel();
        activityModel.setCouponType("01");
//        Mockito.when(couponActivityDomain.findValidActivity(Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(activityModel);
//        Mockito.when(qualificationDomain.checkQualificationForSendCoupon(Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(true);
//        Mockito.when(couponReleaseService.queryCanReceiveReleases(Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(new ArrayList<>());
        Result<String> stringResult = mallCouponController.sendCouponToUser(param);
        Assert.assertTrue(stringResult.isSuccess());
    }
    @Test
    public void sendCouponToUserList() {
        SendCouponToUserListParam param = new SendCouponToUserListParam();
        param.setDomainCode("1");
        param.setTenantCode("1");
        param.setActivityCode("1");
        param.setTakeLabel("01");
        param.setReceiveCount(1);
        List<MemberInfo> userList = new ArrayList<>();
        MemberInfo memberInfo = new MemberInfo();
        memberInfo.setUserCode("1");
        userList.add(memberInfo);
        param.setUserList(userList);
        try {
            Result<List<SendCouponToUserListResult>> listResult = mallCouponController.sendCouponToUserList(param);
        }catch (Exception e){

        }
//        Assert.assertTrue(listResult.isSuccess());
    }


    @Test
    public void sendCoupon() {
    	SendCouponParam  param = new SendCouponParam ();
    	param.setDomainCode("1");
    	param.setTenantCode("1");
    	param.setActivityCode("1");
    	param.setTakeLabel("01");
    	param.setReceiveCount(1);
    	List<MemberInfo> userList = new ArrayList<>();
    	MemberInfo memberInfo = new MemberInfo();
    	memberInfo.setUserCode("1");
    	userList.add(memberInfo);
    	param.setUserList(userList);
        Result<List<SendCouponToUserListResult>> listResult1 = mallCouponController.sendCoupon(param);

        Assert.assertTrue(listResult1.isSuccess());
    }

    @Test
    public void reserveCouponQuota() {
        ReserveCouponQuotaParam param = new ReserveCouponQuotaParam();
        param.setTenantCode("1");
        param.setDomainCode("1");
        param.setOrderNo("1");
        Mockito.doNothing().when(couponReserveComponent).reserveCouponQuota(Mockito.any());
        Result<Object> objectResult = mallCouponController.reserveCouponQuota(param);
        Assert.assertTrue(objectResult.isSuccess());
    }

    @Test
    public void returnCouponQuota() {
        ReserveCouponQuotaParam param = new ReserveCouponQuotaParam();
        param.setTenantCode("1");
        param.setDomainCode("1");
        param.setOrderNo("1");
        Mockito.doNothing().when(couponReserveComponent).returnCouponQuota(Mockito.any());
        Result<Object> objectResult = mallCouponController.returnCouponQuota(param);
        Assert.assertTrue(objectResult.isSuccess());
    }

    @Test
    public void releaseCouponQuota() {
        ReserveCouponQuotaParam param = new ReserveCouponQuotaParam();
        param.setTenantCode("1");
        param.setDomainCode("1");
        param.setOrderNo("1");
        Mockito.doNothing().when(couponReserveComponent).releaseCouponQuota(Mockito.any());
        Result<Object> objectResult = mallCouponController.releaseCouponQuota(param);
        Assert.assertTrue(objectResult.isSuccess());
    }
}