package com.gtech.promotion.helper;


import com.gtech.basic.masterdata.response.JsonResult;
import com.gtech.promotion.dto.cache.PurchaseConstraintCacheDTO;
import com.gtech.promotion.feign.MasterDataFeignClient;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.HashMap;
import java.util.Map;

@RunWith(MockitoJUnitRunner.class)
public class CustomerConditionHelperTest {

    @InjectMocks
    private CustomerConditionHelper customerConditionHelper;

    @Mock
    private MasterDataFeignClient masterDataFeignClient;

    @Test
    public void test_filterCacheByCustomConditionNotEmpty() {
        // Create a mock of MasterDataFeignClient
        MasterDataFeignClient masterDataFeignClientMock = Mockito.mock(MasterDataFeignClient.class);




        // Create a sample cache map
        Map<String, PurchaseConstraintCacheDTO> cacheMap = new HashMap<>();
        PurchaseConstraintCacheDTO cacheDTO = new PurchaseConstraintCacheDTO();
        cacheDTO.setCustomCondition("[{\"customKey\":\"key1\",\"customValue\":\"value1\"}]");
        cacheMap.put("cache1", cacheDTO);

        // Create a sample custom map
        Map<String, String> customMap = new HashMap<>();
        customMap.put("key1", "[\"value1\"]");

        // Create a sample master result
        JsonResult<String> masterResult = new JsonResult<>();
        masterResult.setData("1");

        // Mock the getValueValue method of MasterDataFeignClient to return the sample master result
        //Mockito.when(masterDataFeignClientMock.getValueValue(Mockito.anyString(), Mockito.anyString())).thenReturn(masterResult);

        // Invoke the filterCacheByCustomCondition method
        Map<String, PurchaseConstraintCacheDTO> filteredCacheMap = customerConditionHelper.filterCacheByCustomCondition("tenantCode", cacheMap, customMap,"Condition");

        // Assert that the filtered cache map contains the expected cacheDTO
        Assert.assertTrue(filteredCacheMap.containsKey("cache1"));
        Assert.assertEquals(cacheDTO, filteredCacheMap.get("cache1"));


        Map<String, String> customMap2 = new HashMap<>();
        customMap2.put("key2", "[\"value1\"]");
        // Invoke the filterCacheByCustomCondition method
        Map<String, PurchaseConstraintCacheDTO> filteredCacheMap2 = customerConditionHelper.filterCacheByCustomCondition("tenantCode", cacheMap, customMap2,"Condition");

        // Assert that the filtered cache map contains the expected cacheDTO
        Assert.assertFalse(filteredCacheMap2.containsKey("cache1"));
        Assert.assertNotEquals(cacheDTO, filteredCacheMap2.get("cache1"));

    }

}
