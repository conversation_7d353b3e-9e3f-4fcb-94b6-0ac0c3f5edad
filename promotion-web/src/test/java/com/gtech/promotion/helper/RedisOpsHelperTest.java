package com.gtech.promotion.helper;

import com.google.common.collect.Lists;
import com.gtech.commons.redis.GTechRedisTemplate;
import com.gtech.promotion.calc.model.SkuQuantity;
import com.gtech.promotion.code.activity.LimitationCodeEnum;
import com.gtech.promotion.dao.model.activity.TPromoIncentiveLimitedVO;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.data.redis.core.HashOperations;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.ValueOperations;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @since 2021/8/17 16:43
 */
@RunWith(MockitoJUnitRunner.class)
public class RedisOpsHelperTest {
    @InjectMocks
    private RedisOpsHelper redisOpsHelper;
    @Mock
    private GTechRedisTemplate redisTemplate;
    @Mock
    private StringRedisTemplate stringRedisTemplate;
    @Mock
    private ValueOperations<String, String> stringStringValueOperations;
    @Mock
    private HashOperations<String, Object, Object> stringObjectObjectHashOperations;

    @Test
    public void buildIncentiveKey() {
        String s = redisOpsHelper.buildIncentiveKey("11", "1", "1", "1", "1");
        Assert.assertNotNull(s);
        String s1 = redisOpsHelper.buildIncentiveKey("12", "1", "1", "1", "1");
        String s2 = redisOpsHelper.buildIncentiveKey("13", "1", "1", "1", "1");
        String s3 = redisOpsHelper.buildIncentiveKey("14", "1", "1", "1", "1");
        String s4 = redisOpsHelper.buildIncentiveKey("15", "1", "1", "1", "1");
        String s5 = redisOpsHelper.buildIncentiveKey("16", "1", "1", "1", "1");
        String s6 = redisOpsHelper.buildIncentiveKey("17", "1", "1", "1", "1");
        String s7 = redisOpsHelper.buildIncentiveKey("01", "1", "1", "1", "1");
        String s8 = redisOpsHelper.buildIncentiveKey("02", "1", "1", "1", "1");
        String s9 = redisOpsHelper.buildIncentiveKey("03", "1", "1", "1", "1");
        String s10 = redisOpsHelper.buildIncentiveKey("04", "1", "1", "1", "1");
        String s11 = redisOpsHelper.buildIncentiveKey("09", "1", "1", "1", "1");
        String s12 = redisOpsHelper.buildIncentiveKey("31", "1", "1", "1", "1");
        String s13 = redisOpsHelper.buildIncentiveKey("33", "1", "1", "1", "1");
    }

    @Test
    public void buildIncentiveKey1() {
        String s13 = redisOpsHelper.buildIncentiveKey(LimitationCodeEnum.ACTIVITY_DAY_AMOUNT, "1", "1", "1", "1");
        Assert.assertNotNull(s13);
    }

    @Test
    public void getLimitedValue() {
        Mockito.when(redisTemplate.opsHashHasKey(Mockito.any(), Mockito.anyString(), Mockito.any())).thenReturn(false);
        BigDecimal s13 = redisOpsHelper.getLimitedValue("11", "1", "1", "1", "1");
        Assert.assertNull(s13);
    }

    @Test
    public void getLimitedValue1() {
        Mockito.when(redisTemplate.opsHashHasKey(Mockito.any(), Mockito.anyString(), Mockito.any())).thenReturn(true);
        Mockito.when(redisTemplate.opsHashIncrement(Mockito.any(), Mockito.anyString(), Mockito.any(), Mockito.anyLong())).thenReturn(null);
        BigDecimal s13 = redisOpsHelper.getLimitedValue("11", "1", "1", "1", "1");
        Assert.assertNull(s13);
    }

    @Test
    public void getLimitedValue2() {
        Mockito.when(redisTemplate.opsHashHasKey(Mockito.any(), Mockito.anyString(), Mockito.any())).thenReturn(true);
        Mockito.when(redisTemplate.opsHashIncrement(Mockito.any(), Mockito.anyString(), Mockito.any(), Mockito.anyLong())).thenReturn(1L);
        BigDecimal s13 = redisOpsHelper.getLimitedValue("11", "1", "1", "1", "1");
        Assert.assertNotNull(s13);
    }

    @Test
    public void getLimitedValue3() {
        Mockito.when(redisTemplate.hasKey(Mockito.any(), Mockito.anyString())).thenReturn(false);
        BigDecimal s13 = redisOpsHelper.getLimitedValue("21", "1", "1", "1", "1");
        Assert.assertNull(s13);
    }

    @Test
    public void getLimitedValue4() {
        Mockito.when(redisTemplate.hasKey(Mockito.any(), Mockito.anyString())).thenReturn(true);
        Mockito.when(redisTemplate.opsValueIncrement(Mockito.any(), Mockito.anyString(), Mockito.anyLong())).thenReturn(null);
        BigDecimal s13 = redisOpsHelper.getLimitedValue("21", "1", "1", "1", "1");
        Assert.assertNull(s13);
    }

    @Test
    public void getLimitedValue5() {
        Mockito.when(redisTemplate.hasKey(Mockito.any(), Mockito.anyString())).thenReturn(true);
        Mockito.when(redisTemplate.opsValueIncrement(Mockito.any(), Mockito.anyString(), Mockito.anyLong())).thenReturn(1L);
        BigDecimal s13 = redisOpsHelper.getLimitedValue("21", "1", "1", "1", "1");
        Assert.assertNotNull(s13);
    }

    @Test
    public void extendKey() {
        TPromoIncentiveLimitedVO tPromoIncentiveLimitedVO = new TPromoIncentiveLimitedVO();
        tPromoIncentiveLimitedVO.setLimitationCode("09");
        boolean b = redisOpsHelper.extendKey(tPromoIncentiveLimitedVO, 1);
        Assert.assertTrue(b);
        tPromoIncentiveLimitedVO.setLimitationCode("11");
        Mockito.when(redisTemplate.expire(Mockito.any(), Mockito.anyString(), Mockito.anyLong())).thenReturn(true);
        boolean b1 = redisOpsHelper.extendKey(tPromoIncentiveLimitedVO, 1);
        Assert.assertTrue(b1);
    }

    @Test
    public void NewRedisHelper() {
        BigDecimal a = new BigDecimal("12");
        RedisOpsHelper.IncentiveLimitedParam IncentiveLimitedParam = new RedisOpsHelper.IncentiveLimitedParam("11", "test", "test", "test", "test", a, a);
    }

    @Test
    public void lockRedisDataSupportSku() {


        RedisOpsHelper.IncentiveLimitedParam incentiveLimitedParam = new RedisOpsHelper.IncentiveLimitedParam();
        incentiveLimitedParam.setTotalValue(new BigDecimal("1"));
        incentiveLimitedParam.setIncentiveValue(new BigDecimal("1"));

        RedisOpsHelper.IncentiveLimitedParam param = new RedisOpsHelper.IncentiveLimitedParam();
        param.setTotalValue(new BigDecimal("1"));
        param.setIncentiveValue(new BigDecimal("1"));

        Mockito.when(stringRedisTemplate.opsForValue()).thenReturn(stringStringValueOperations);
        Mockito.when(stringStringValueOperations.get(Mockito.any())).thenReturn("0");
        // 默认 stringStringValueOperations=0
        redisOpsHelper.lockRedisDataSupportSku(Lists.newArrayList(incentiveLimitedParam), param, 1L, Lists.newArrayList("skuCode:001"), Lists.newArrayList(1));
        // 默认 stringStringValueOperations=1
        Mockito.when(stringStringValueOperations.get(Mockito.any())).thenReturn("1");
        redisOpsHelper.lockRedisDataSupportSku(Lists.newArrayList(incentiveLimitedParam), param, 1L, Lists.newArrayList("skuCode:001"), Lists.newArrayList(1));
        // 每人限制总次数 stringObjectObjectHashOperations = 0
        Mockito.when(stringRedisTemplate.opsForHash()).thenReturn(stringObjectObjectHashOperations);
        Mockito.when(stringObjectObjectHashOperations.get(Mockito.any(), Mockito.any())).thenReturn("0");
        param.setLimitedCode(LimitationCodeEnum.USER_TOTAL_COUNT.code());
        redisOpsHelper.lockRedisDataSupportSku(Lists.newArrayList(incentiveLimitedParam), param, 1L, Lists.newArrayList("skuCode:001"), Lists.newArrayList(1));

        // 每人限制总次数 stringObjectObjectHashOperations = 1
        Mockito.when(stringObjectObjectHashOperations.get(Mockito.any(), Mockito.any())).thenReturn("1");
        redisOpsHelper.lockRedisDataSupportSku(Lists.newArrayList(incentiveLimitedParam), param, 1L, Lists.newArrayList("skuCode:001"), Lists.newArrayList(1));

        // 活动限制每人单SKU次数

//        param.setLimitedCode(LimitationCodeEnum.USER_SKU_COUNT.code());
//        redisOpsHelper.lockRedisDataSupportSku(Lists.newArrayList(incentiveLimitedParam), param, 1L, Lists.newArrayList("skuCode:001"), Lists.newArrayList(1));
    }

    @Test
    public void lockRedisData() {
    }

    @Test
    public void redisInitData() {
    }

    @Test
    public void setRedisOpsValue() {
    }

    @Test
    public void getRedisTemplateOpsHash() {
    }

    @Test
    public void lockRedisDataSpu() {
        RedisOpsHelper.IncentiveLimitedParam incentiveLimitedParam = new RedisOpsHelper.IncentiveLimitedParam();
        incentiveLimitedParam.setTotalValue(new BigDecimal("1"));
        incentiveLimitedParam.setIncentiveValue(new BigDecimal("1"));

        RedisOpsHelper.IncentiveLimitedParam param = new RedisOpsHelper.IncentiveLimitedParam();
        param.setTotalValue(new BigDecimal("1"));
        param.setIncentiveValue(new BigDecimal("1"));
        // 默认
        redisOpsHelper.lockRedisDataSpu(Lists.newArrayList(incentiveLimitedParam), param, 1, "skuCode:001");
        //  活动限制每人单SKU次数
        param.setLimitedCode(LimitationCodeEnum.USER_SKU_COUNT.code());
        Mockito.when(stringRedisTemplate.opsForHash()).thenReturn(stringObjectObjectHashOperations);
        // stringObjectObjectHashOperations = null
        Mockito.when(stringObjectObjectHashOperations.get(Mockito.any(), Mockito.any())).thenReturn(null);
        redisOpsHelper.lockRedisDataSpu(Lists.newArrayList(incentiveLimitedParam), param, 1, "skuCode:001");
        // stringObjectObjectHashOperations = 0
        Mockito.when(stringObjectObjectHashOperations.get(Mockito.any(), Mockito.any())).thenReturn("0");
        redisOpsHelper.lockRedisDataSpu(Lists.newArrayList(incentiveLimitedParam), param, 1, "skuCode:001");
        // redisTemplate = -1
        Mockito.when(redisTemplate.opsHashIncrement(Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyLong())).thenReturn(-1L);
        redisOpsHelper.lockRedisDataSpu(Lists.newArrayList(incentiveLimitedParam), param, 1, "skuCode:001");
    }

    @Test
    public void setRedisTemplateInfo() {
        RedisOpsHelper.IncentiveLimitedParam incentiveLimitedParam = new RedisOpsHelper.IncentiveLimitedParam();
        incentiveLimitedParam.setTotalValue(new BigDecimal("1"));
        incentiveLimitedParam.setIncentiveValue(new BigDecimal("1"));
        // opsHashIncrement -1
        Mockito.when(redisTemplate.opsHashIncrement(Mockito.any(), Mockito.anyString(), Mockito.any(), Mockito.anyLong())).thenReturn(-1L);
        redisOpsHelper.setRedisTemplateInfo(Lists.newArrayList(incentiveLimitedParam), 1, "x", "", 1, 1);
        // opsHashIncrement 1
        Mockito.when(redisTemplate.opsHashIncrement(Mockito.any(), Mockito.anyString(), Mockito.any(), Mockito.anyLong())).thenReturn(1L);
        redisOpsHelper.setRedisTemplateInfo(Lists.newArrayList(incentiveLimitedParam), 1, "x", "", 1, 1);
    }

    @Test
    public void rollBackRedisData() {
        RedisOpsHelper.IncentiveLimitedParam incentiveLimitedParam = new RedisOpsHelper.IncentiveLimitedParam();
        incentiveLimitedParam.setTotalValue(new BigDecimal("1"));
        incentiveLimitedParam.setIncentiveValue(new BigDecimal("1"));
        // 默认
        redisOpsHelper.rollBackRedisData(Lists.newArrayList(incentiveLimitedParam));
        for (LimitationCodeEnum value : LimitationCodeEnum.values()) {
            incentiveLimitedParam.setLimitedCode(value.code());
            redisOpsHelper.rollBackRedisData(Lists.newArrayList(incentiveLimitedParam));
        }

    }

    @Test
    public void testRollBackRedisData() {
        RedisOpsHelper.IncentiveLimitedParam incentiveLimitedParam = new RedisOpsHelper.IncentiveLimitedParam();
        incentiveLimitedParam.setTotalValue(new BigDecimal("1"));
        incentiveLimitedParam.setIncentiveValue(new BigDecimal("1"));
        SkuQuantity skuQuantity = new SkuQuantity();
        skuQuantity.setSkuCode("skuCode:001");
        skuQuantity.setQuantity(1);
        for (LimitationCodeEnum value : LimitationCodeEnum.values()) {
            redisOpsHelper.rollBackRedisData(value.code(),
                    "tenantCodeCode:001",
                    "activityCode:001",
                    "userCode:001",
                    new BigDecimal("1"),
                    Lists.newArrayList(skuQuantity));
        }

    }

    @Test
    public void rollBackRedisDataOpsHash() {
    }
}
