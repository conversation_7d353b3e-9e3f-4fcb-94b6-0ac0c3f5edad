package com.gtech.promotion.helper;

import com.gtech.promotion.dao.model.activity.QualificationModel;
import org.junit.Assert;
import org.junit.Test;

import java.util.ArrayList;
import java.util.List;

public class QualificationFilterTest {

    @Test
    public void filter1(){
        List<QualificationModel> models = new ArrayList<>();
        QualificationFilter qualificationFilter = new QualificationFilter();
        Assert.assertTrue(qualificationFilter.filter(models));
    }

    @Test
    public void filter2(){
        List<QualificationModel> conditionModels = new ArrayList<>();
        QualificationModel e = new QualificationModel();
        e.setQualificationCode("1");
        conditionModels.add(e);
        List<QualificationModel> models = new ArrayList<>();
        QualificationFilter qualificationFilter = new QualificationFilter(conditionModels);
        Assert.assertFalse(qualificationFilter.filter(models));
    }

    @Test
    public void filter3(){
        List<QualificationModel> conditionModels = new ArrayList<>();
        QualificationModel e = new QualificationModel();
        e.setQualificationValue("1");
        e.setQualificationCode("1");
        conditionModels.add(e);
        List<QualificationModel> models = new ArrayList<>();
        QualificationModel e1 = new QualificationModel();
        e1.setQualificationValue("2");
        e1.setQualificationCode("1");
        models.add(e1);
        QualificationFilter qualificationFilter = new QualificationFilter(conditionModels);
        Assert.assertFalse(qualificationFilter.filter(models));
    }

    @Test
    public void filter4(){
        List<QualificationModel> conditionModels = new ArrayList<>();
        QualificationModel e = new QualificationModel();
        e.setQualificationValue("1");
        e.setQualificationCode("1");
        conditionModels.add(e);
        List<QualificationModel> models = new ArrayList<>();
        models.add(e);
        QualificationFilter qualificationFilter = new QualificationFilter(conditionModels);
        Assert.assertTrue(qualificationFilter.filter(models));
    }

    @Test
    public void filter5(){
        List<QualificationModel> conditionModels = new ArrayList<>();
        QualificationModel e = new QualificationModel();
        e.setQualificationValue("1");
        e.setQualificationCode("isMember");
        conditionModels.add(e);
        List<QualificationModel> models = new ArrayList<>();
        models.add(e);
        QualificationFilter qualificationFilter = new QualificationFilter(conditionModels);
        try {
            qualificationFilter.filter(null);
        }catch (Exception e1){

        }

    }


    @Test
    public void filter6(){
        List<QualificationModel> conditionModels = new ArrayList<>();
        QualificationModel e = new QualificationModel();
        e.setQualificationValue("1");
        e.setQualificationCode("111");
        e.setIsExclude("02");
        conditionModels.add(e);
        List<QualificationModel> models = new ArrayList<>();
        QualificationModel e1 = new QualificationModel();
        e1.setQualificationValue("2");
        e1.setQualificationCode("111");
        models.add(e1);
        QualificationFilter qualificationFilter = new QualificationFilter(conditionModels);
        Assert.assertTrue(qualificationFilter.filter(models));
    }

    @Test
    public void filter7(){
        List<QualificationModel> conditionModels = new ArrayList<>();
        QualificationModel e = new QualificationModel();
        e.setQualificationValue("1");
        e.setQualificationCode("111");
        e.setIsExclude("01");
        conditionModels.add(e);
        List<QualificationModel> models = new ArrayList<>();
        QualificationModel e1 = new QualificationModel();
        e1.setQualificationValue("2");
        e1.setQualificationCode("111");
        models.add(e1);
        QualificationFilter qualificationFilter = new QualificationFilter(conditionModels);
        Assert.assertFalse(qualificationFilter.filter(models));
    }

    @Test
    public void filter8(){
        List<QualificationModel> conditionModels = new ArrayList<>();
        QualificationModel e = new QualificationModel();
        e.setQualificationValue("2");
        e.setQualificationCode("111");
        e.setIsExclude("01");
        conditionModels.add(e);
        List<QualificationModel> models = new ArrayList<>();
        QualificationModel e1 = new QualificationModel();
        e1.setQualificationValue("2");
        e1.setQualificationCode("111");
        models.add(e1);
        QualificationFilter qualificationFilter = new QualificationFilter(conditionModels);
        Assert.assertTrue(qualificationFilter.filter(models));
    }

    @Test
    public void filter9(){
        List<QualificationModel> conditionModels = new ArrayList<>();
        QualificationModel e = new QualificationModel();
        e.setQualificationValue("2");
        e.setQualificationCode("111");
        e.setIsExclude("02");
        conditionModels.add(e);
        List<QualificationModel> models = new ArrayList<>();
        QualificationModel e1 = new QualificationModel();
        e1.setQualificationValue("2");
        e1.setQualificationCode("111");
        QualificationFilter qualificationFilter = new QualificationFilter(conditionModels);
        Assert.assertFalse(qualificationFilter.filter(models));
    }

    @Test
    public void filter10(){
        List<QualificationModel> conditionModels = new ArrayList<>();
        QualificationModel e = new QualificationModel();
        e.setQualificationValue("2");
        e.setQualificationCode("111");
        e.setIsExclude("01");
        conditionModels.add(e);
        List<QualificationModel> models = new ArrayList<>();
        QualificationModel e1 = new QualificationModel();
        e1.setQualificationValue("2");
        e1.setQualificationCode("111");
        QualificationFilter qualificationFilter = new QualificationFilter(conditionModels);
        Assert.assertFalse(qualificationFilter.filter(models));
    }

    @Test
    public void filter11(){
        List<QualificationModel> conditionModels = new ArrayList<>();
        QualificationModel e = new QualificationModel();
        e.setQualificationValue("2");
        e.setQualificationCode("111");
        e.setIsExclude("02");
        conditionModels.add(e);
        List<QualificationModel> models = new ArrayList<>();
        QualificationModel e1 = new QualificationModel();
        e1.setQualificationValue("2");
        e1.setQualificationCode("111");
        QualificationFilter qualificationFilter = new QualificationFilter(conditionModels);
        Assert.assertFalse(qualificationFilter.filter(models));
    }

    @Test
    public void filter12(){
        List<QualificationModel> conditionModels = new ArrayList<>();
        QualificationModel e = new QualificationModel();
        e.setQualificationValue("2");
        e.setQualificationCode("111");
        e.setIsExclude("02");
        conditionModels.add(e);
        List<QualificationModel> models = new ArrayList<>();
        QualificationModel e1 = new QualificationModel();
        e1.setQualificationValue("memberTagCode");
        e1.setQualificationCode("memberTagCode");
        models.add(e1);
        QualificationFilter qualificationFilter = new QualificationFilter(conditionModels);
        Assert.assertFalse(qualificationFilter.filter(models));
    }
}
