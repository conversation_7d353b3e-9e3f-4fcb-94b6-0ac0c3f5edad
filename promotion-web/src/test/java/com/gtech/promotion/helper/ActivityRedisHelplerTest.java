package com.gtech.promotion.helper;

import com.gtech.promotion.RedisClient;
import com.gtech.promotion.domain.coupon.CouponReleaseDomain;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.HashMap;
import java.util.HashSet;
import java.util.Set;

/**
 * <AUTHOR>
 * @since 2021/8/17 15:08
 */
@RunWith(MockitoJUnitRunner.class)
public class ActivityRedisHelplerTest {


    @InjectMocks
    private ActivityRedisHelpler redisHelpler;
    @Mock
    private RedisClient redisClient;


    @Test
    public void deleteFuzzyMatch() {
        Mockito.doNothing().when(redisClient).deleteFuzzyMatch(Mockito.any());
        redisHelpler.deleteFuzzyMatch("!1");
    }

    @Test
    public void queryFuzzyMatchingMarketingGroupUserKey(){
        String s = redisHelpler.queryFuzzyMatchingMarketingGroupUserKey("!1", "1", "1");
        Assert.assertNotNull(s);

    }

    @Test
    public void getMarketingGroupUserKey(){
        String s = redisHelpler.getMarketingGroupUserKey("!1",  "1","1");
        Assert.assertNotNull(s);

    }

    @Test
    public void setMarketingGroupCode(){

        redisHelpler.setMarketingGroupCode("!1", "1","1","20230906162258","1");

    }


    @Test
    public void deleteCouponDelayRelease() {
        Mockito.doNothing().when(redisClient).delete(Mockito.anyString());
        redisHelpler.deleteCouponDelayRelease("1", "1", "1", "1", 1);
    }

    @Test
    public void getDelayReleaseKeyValue() {
        Mockito.when(redisClient.getString(Mockito.any())).thenReturn("1");
        String value = redisHelpler.getDelayReleaseKeyValue("1", "1", "1", "1", 1);
        Assert.assertEquals("1",value);
    }

    @Test
    public void getAllReleaseKesByActivityCode(){
        Mockito.when(redisClient.scan(Mockito.any())).thenReturn(new HashSet<>());
        Set<String> stringSet = redisHelpler.getAllReleaseKesByActivityCode("1", "1");
        Assert.assertNotNull(stringSet);
    }

    @Test
    public void setActivitySetting(){
        Mockito.doNothing().when(redisClient).putHash(Mockito.any(),Mockito.any(),Mockito.any());
        redisHelpler.setActivitySetting("1",1,"1","1");
    }

    @Test
    public void getActivityCache111(){
        Mockito.when(redisClient.getString(Mockito.any())).thenReturn("1");
        String activityCache111 = redisHelpler.getActivityCache111("1", "1", "1", "1");
        Assert.assertEquals("1",activityCache111);
    }

    @Test
    public void getReleaseCoupon(){
        CouponReleaseDomain releaseDomain = new CouponReleaseDomain();
        releaseDomain.setTenantCode("");
        releaseDomain.setActivityCode("");
        releaseDomain.setCouponType("");
        releaseDomain.setReleaseCode("");
        releaseDomain.setReleaseStatus("");
        releaseDomain.setReleaseQuantity(0);
        releaseDomain.setInventory(0);
        releaseDomain.setReceivedQuantity(0);
        releaseDomain.setUsedTotal(0);
        releaseDomain.setLocked(0);
        releaseDomain.setReleaseSource("");
        releaseDomain.setReceiveStartTime("");
        releaseDomain.setReceiveEndTime("");
        releaseDomain.setValidDays(0);
        releaseDomain.setValidStartTime("");
        releaseDomain.setValidEndTime("");
        releaseDomain.setReleaseTime("");
        releaseDomain.setReleaseType("");
        releaseDomain.setCouponCodePrefix("");
        releaseDomain.setTimeSameActivity("");

        Mockito.when(redisClient.getFirstStringValueFormZSet(Mockito.any())).thenReturn("1");
        String activityCache111 = redisHelpler.getReleaseCoupon(releaseDomain);
        Assert.assertEquals("1",activityCache111);
    }

    @Test
    public void getCouponUserCount111(){
        Mockito.when(redisClient.getInt(Mockito.anyString())).thenReturn(1);
        int activityCache111 = redisHelpler.getCouponUserCount111("1","1","1");
        Assert.assertEquals(1,activityCache111);
    }

    @Test
    public void addCouponUserCount111(){
        Mockito.doNothing().when(redisClient).setStringValue(Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any());
        redisHelpler.addCouponUserCount111("1","1","1",1L);
    }

    @Test
    public void addCouponUserCount111_Null(){
        Mockito.when(redisClient.increment(Mockito.anyString(),Mockito.anyLong())).thenReturn(1L);
        redisHelpler.addCouponUserCount111("1","1","1",null);
    }

    @Test
    public void getCouponReleaseLimit(){
        Mockito.when(redisClient.getInt(Mockito.any())).thenReturn(1);
        int activityCache111 = redisHelpler.getCouponReleaseLimit("1","1","1");
        Assert.assertEquals(1,activityCache111);
    }

    @Test
    public void addCouponReleaseLimit(){
        Mockito.doNothing().when(redisClient).setStringValue(Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any());
        redisHelpler.addCouponUserCount111("1","1","1",1L);
    }

    @Test
    public void addCouponReleaseLimit_Null(){
        Mockito.when(redisClient.increment(Mockito.anyString(),Mockito.anyLong())).thenReturn(1L);
        redisHelpler.addCouponUserCount111("1","1","1",null);
    }

    @Test
    public void getCouponReleaseLimitKeyValue(){
        Mockito.when(redisClient.getString(Mockito.any())).thenReturn("1");
        String activityCache111 = redisHelpler.getCouponReleaseLimitKeyValue("1", "1", "1");
        Assert.assertEquals("1",activityCache111);
    }

    @Test
    public void unlockCouponUser(){
        Mockito.when(redisClient.getString(Mockito.any())).thenReturn("1");
        Mockito.when(redisClient.increment(Mockito.anyString(),Mockito.anyLong())).thenReturn(1L);
        redisHelpler.unlockCouponUser("1", "1", "1");
    }

    @Test
    public void unlockCouponReleaseLimit(){
        Mockito.when(redisClient.getString(Mockito.any())).thenReturn("1");
        Mockito.when(redisClient.increment(Mockito.anyString(),Mockito.anyLong())).thenReturn(1L);
        redisHelpler.unlockCouponReleaseLimit("1", "1", "1");
    }

    @Test
    public void getDelayReleaseMakeKeyValue(){
        Mockito.when(redisClient.getZSetSize(Mockito.any())).thenReturn(1L);
        long value = redisHelpler.getDelayReleaseMakeKeyValue("1", "1", "1", "1", "1", 1);
        Assert.assertEquals(1,value);
    }

    @Test
    public void getCouponUserCountKeyValue111(){
        Mockito.when(redisClient.getString(Mockito.any())).thenReturn(null);
        Mockito.doNothing().when(redisClient).setStringValue(Mockito.any(),Mockito.any());
        redisHelpler.getCouponUserCountKeyValue111("1", "1", "1",1);
    }

    @Test
    public void setCouponReleaseLimitKeyValue(){
        Mockito.doNothing().when(redisClient).setStringValue(Mockito.any(),Mockito.any());
        redisHelpler.setCouponReleaseLimitKeyValue("1", "1");
    }

    @Test
    public void addTemplateTags(){
        Mockito.doNothing().when(redisClient).putAllHash(Mockito.any(),Mockito.anyMap());
        redisHelpler.addTemplateTags(new HashMap<>());
    }

    @Test
    public void getTemplateTag(){
        Mockito.when(redisClient.getHashValue(Mockito.any(),Mockito.any())).thenReturn("1");
        String templateTag = redisHelpler.getTemplateTag("1");
        Assert.assertNotNull(templateTag);
    }

    @Test
    public void getTemplateTag1(){
        Mockito.when(redisClient.getHashValue(Mockito.any(),Mockito.any())).thenReturn(null);
        String templateTag = redisHelpler.getTemplateTag("1");
        Assert.assertNull(templateTag);
    }

    @Test
    public void setRedisProductActivity(){
        redisHelpler.setRedisProductActivity("1", "1","1","1",1L);
    }

    @Test
    public void getRedisProductActivity(){
        Mockito.when(redisClient.getString(Mockito.any())).thenReturn("1");
        String redisProductActivity = redisHelpler.getRedisProductActivity("1", "1", "1");
        Assert.assertNotNull(redisProductActivity);
    }

    @Test
    public void getAll(){
        Mockito.when(redisClient.scan(Mockito.any())).thenReturn(new HashSet<>());
        Set<String> stringSet = redisHelpler.getAll("1");
        Assert.assertNotNull(stringSet);
    }
    @Test
    public void addCouponDelayReleaseTest() {
        redisHelpler.addCouponDelayRelease(null, null, null, "20220101000000", null, null);
    }
    @Test
    public void addCouponTest() {
        redisHelpler.addCoupon(null, null,null, null, null, "20220101000000", null);
    }
    @Test
    public void setExpressionTest() {
        redisHelpler.setExpression(null, null);
    }
    @Test
    public void addCouponReleaseLimitTest() {
        redisHelpler.addCouponReleaseLimit(null, null, null, null);
    }
    @Test
    public void setRedisGroupRelationTest() {
        redisHelpler.setRedisGroupRelation(null, null);
    }
    @Test
    public void getRedisGroupRelationTest() {
        redisHelpler.getRedisGroupRelation(null);
    }
}
