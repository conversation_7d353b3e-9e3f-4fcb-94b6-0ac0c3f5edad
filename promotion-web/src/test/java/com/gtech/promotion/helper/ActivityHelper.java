/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.helper;

import com.gtech.commons.utils.DateUtil;
import com.gtech.promotion.code.activity.*;
import com.gtech.promotion.vo.bean.*;
import com.gtech.promotion.vo.param.activity.UpdatePromoActivityParam;
import org.apache.commons.collections4.CollectionUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

/**
 * 
 *
 * <AUTHOR>
 * @Date 2020-02-04
 */
public class ActivityHelper {

    public static final String domainCode = "DC0001";
    public static final String juintCode = "**********";
    public static final String tenantCode = juintCode;
//    public static final String tenantCode = "100001";

    public static final List<ActivityStore> storeList = Arrays.asList(
        ActivityStore.builder().channelCode(juintCode + "CH001").channelName("CHName01").orgCode(juintCode + "OR001").storeName("ORName01").build(),
        ActivityStore.builder().channelCode(juintCode + "CH002").channelName("CHName02").orgCode(juintCode + "OR002").storeName("ORName02").build(),
        ActivityStore.builder().channelCode(juintCode + "CH003").channelName("CHName03").orgCode(juintCode + "OR003").storeName("ORName03").build());
    public static final String channelCode = storeList.get(0).getChannelCode();
    public static final String orgCode = storeList.get(0).getOrgCode();

    public static final List<Qualification> qualifications = Arrays.asList(
            Qualification.builder().qualificationValue(new ArrayList<>(Collections.singleton(juintCode + "ML001"))).qualificationCode(QualificationCodeEnum.MEMBER_TAG.code()).build(),
            Qualification.builder().qualificationValue(new ArrayList<>(Collections.singleton(juintCode + "ML002"))).qualificationCode(QualificationCodeEnum.MEMBER_TAG.code()).build());

    public static FunctionParam buildFunctionParam(String functionCode, String paramValue, int rankParam) {

        String paramType = FunctionParamTypeEnum.NULL.code();
        String paramUnit = FunctionParamUnitEnum.NULL.code();
        if ("0303".equals(functionCode) || "0401".equals(functionCode) || "0403".equals(functionCode) || "0404".equals(functionCode)) {
            paramType = FunctionParamTypeEnum.NUMBER.code(); // "02"
            paramUnit = FunctionParamUnitEnum.AMOUNT.code(); // "01"
        } else if ("0402".equals(functionCode)) {
            paramType = FunctionParamTypeEnum.NUMBER.code(); // "02"
            paramUnit = FunctionParamUnitEnum.DISCOUNT.code(); // "03"
        } else if ("0302".equals(functionCode) || "0406".equals(functionCode) || "0407".equals(functionCode) || "0408".equals(functionCode)) {
            paramType = FunctionParamTypeEnum.NUMBER.code(); // "02"
            paramUnit = FunctionParamUnitEnum.COUNT.code(); // "02"
        } else {
            //默认值
        }

        return FunctionParam.builder()
            .functionType(functionCode.substring(0, 2))
            .functionCode(functionCode)
            .paramType(paramType)
            .paramValue(paramValue)
            .paramUnit(paramUnit)
            .rankParam(rankParam).build();
    }

    /**
     * 多语言信息创建
     *
     * @param num -- (1, zh-CN)(2, zh-CN/en-US)(3, zh-CN/en-US/id-ID)
     */
    public static List<ActivityLanguage> buildActivityLanguages(int num, String templateCode){

        List<ActivityLanguage> list = new ArrayList<ActivityLanguage>();

        if (num > 0) {
            list.add(ActivityLanguage.builder()
                .language("zh-CN")
                .activityDesc("CN-Desc")
                .activityRemark("CN-Remark")
                .activityLabel("CN-Label")
                .activityName("CN-" + templateCode).build());
        }
        if (num > 1) {
            list.add(ActivityLanguage.builder()
                .language("en-US")
                .activityDesc("US-Desc")
                .activityRemark("US-Remark")
                .activityLabel("US-Label")
                .activityName("US-" + templateCode).build());
        }
        if (num > 2) {
            list.add(ActivityLanguage.builder()
                .language("id-ID")
                .activityDesc("ID-Desc")
                .activityRemark("ID-Remark")
                .activityLabel("ID-Label")
                .activityName("ID-" + templateCode).build());
        }

       return list;
    }

    /**
     * 设定：激励额度限制
     */
    public static void buildIncentiveLimited(UpdatePromoActivityParam promoActivity, LimitationCodeEnum limit) {

        switch (limit) {
            case ACTIVITY_TOTAL_COUNT:
                promoActivity.setIncentiveLimitedFlag(IncentiveLimitedFlagEnum.YES.code());
                promoActivity.setIncentiveLimiteds(Arrays.asList(
                    IncentiveLimited.builder().limitationCode(limit.code()).limitationValue(new BigDecimal(2)).build()));
                break;
            case USER_TOTAL_COUNT:
                promoActivity.setIncentiveLimitedFlag(IncentiveLimitedFlagEnum.YES.code());
                promoActivity.setIncentiveLimiteds(Arrays.asList(
                    IncentiveLimited.builder().limitationCode(limit.code()).limitationValue(new BigDecimal(2)).build()));
                break;
            case ACTIVITY_TOTAL_AMOUNT:
                promoActivity.setIncentiveLimitedFlag(IncentiveLimitedFlagEnum.YES.code());
                promoActivity.setIncentiveLimiteds(Arrays.asList(
                    IncentiveLimited.builder().limitationCode(limit.code()).limitationValue(new BigDecimal(1000)).build()));
                break;
            default:
                promoActivity.setIncentiveLimitedFlag(IncentiveLimitedFlagEnum.NO.code());
                promoActivity.setIncentiveLimiteds(null);
                break;
        }
    }

    /**
     * 设定：渠道门店限制
     */
    public static void buildChannelStoreLimited(UpdatePromoActivityParam promoActivity, StoreParamTypeEnum store) {

        switch (store) {
            case STORE_CUSTOM:
                promoActivity.setStoreType(store.code());
                promoActivity.setChannelStores(storeList);
                break;
            default:
                promoActivity.setStoreType(StoreParamTypeEnum.STORE_ALL.code());
                promoActivity.setChannelStores(null);
                break;
        }
    }

    /**
     * 设定：会员限制
     */
    public static void buildMemberLimited(UpdatePromoActivityParam promoActivity) {

        promoActivity.setQualifications(qualifications);;
    }

    public static UpdatePromoActivityParam buildActivity0101_0201_0301_0401_单品无条件减金额(List<ProductScope> productScopes, List<ProductDetail> productDetails) {

        List<FunctionParam> funcParams = Arrays.asList(
            ActivityHelper.buildFunctionParam("0101", "", 1), ActivityHelper.buildFunctionParam("0201", "", 1),
            ActivityHelper.buildFunctionParam("0301", "", 1), ActivityHelper.buildFunctionParam("0401", "1", 1));

        UpdatePromoActivityParam promoActivity = new UpdatePromoActivityParam();
        promoActivity.setIncentiveLimitedFlag(IncentiveLimitedFlagEnum.NO.code());
        if (CollectionUtils.isNotEmpty(productScopes)) {
            promoActivity.setProducts(productScopes);
            promoActivity.setConditionProductType(ProductTypeEnum.CUSTOM_RANGE.code());
        } else if (CollectionUtils.isNotEmpty(productDetails)) {
            promoActivity.setProductDetails(productDetails);
            promoActivity.setProductDetailBlackList(productDetails);
            promoActivity.setConditionProductType(ProductTypeEnum.CUSTOM_PRODUCT.code());
        } else {
            promoActivity.setConditionProductType(ProductTypeEnum.ALL.code());
        }
        promoActivity.setSkuToken("");
        promoActivity.setTemplateCode("0101020103010401");
        promoActivity.setFuncParams(funcParams);
        promoActivity.setOpsType("666");
        return buildAcivity(promoActivity);
    }

    public static UpdatePromoActivityParam buildActivity0103_0203_0302_0401_订单每满数量减金额() {

        List<FunctionParam> funcParams = Arrays.asList(
            ActivityHelper.buildFunctionParam("0103", "", 1), ActivityHelper.buildFunctionParam("0203", "", 1),
            ActivityHelper.buildFunctionParam("0302", "2", 1), ActivityHelper.buildFunctionParam("0401", "100", 1));

        UpdatePromoActivityParam promoActivity = new UpdatePromoActivityParam();
        promoActivity.setIncentiveLimitedFlag(IncentiveLimitedFlagEnum.NO.code());
        promoActivity.setConditionProductType("00");
        promoActivity.setSkuToken("");
        promoActivity.setTemplateCode("0103020303020401");
        promoActivity.setFuncParams(funcParams);

        return buildAcivity(promoActivity);
    }

    public static UpdatePromoActivityParam buildActivity0101_0201_0301_0402_单品无条件打折扣() {

        List<FunctionParam> funcParams = Arrays.asList(
            ActivityHelper.buildFunctionParam("0101", "", 1), ActivityHelper.buildFunctionParam("0201", "", 1),
            ActivityHelper.buildFunctionParam("0301", "", 1), ActivityHelper.buildFunctionParam("0402", "0.1", 1));

        UpdatePromoActivityParam promoActivity = new UpdatePromoActivityParam();
        promoActivity.setIncentiveLimitedFlag(IncentiveLimitedFlagEnum.NO.code());
        promoActivity.setConditionProductType("00");
        promoActivity.setSkuToken("");
        promoActivity.setTemplateCode("0101020103010402");
        promoActivity.setFuncParams(funcParams);

        return buildAcivity(promoActivity);
    }

    public static UpdatePromoActivityParam buildActivity0101_0201_0301_0403_单品无条件设为每件特价() {

        List<FunctionParam> funcParams = Arrays.asList(
            ActivityHelper.buildFunctionParam("0101", "", 1), ActivityHelper.buildFunctionParam("0201", "", 1),
            ActivityHelper.buildFunctionParam("0301", "", 1), ActivityHelper.buildFunctionParam("0403", "1", 1));

        UpdatePromoActivityParam promoActivity = new UpdatePromoActivityParam();
        promoActivity.setIncentiveLimitedFlag(IncentiveLimitedFlagEnum.NO.code());
        promoActivity.setConditionProductType("00");
        promoActivity.setSkuToken("");
        promoActivity.setTemplateCode("0101020103010403");
        promoActivity.setFuncParams(funcParams);

        return buildAcivity(promoActivity);
    }

    public static UpdatePromoActivityParam buildActivity0102_0202_0302_0401_商品范围满数量减金额() {

        List<FunctionParam> funcParams = Arrays.asList(
            ActivityHelper.buildFunctionParam("0102", "", 1), ActivityHelper.buildFunctionParam("0202", "", 1),
            ActivityHelper.buildFunctionParam("0302", "2", 1), ActivityHelper.buildFunctionParam("0401", "2", 1));

        List<ProductScope> products = new ArrayList<>();
        ProductScope product = new ProductScope();
        product.setCategoryCode("22");
        product.setBrandCode("221");
        product.setSeqNum(1);
        products.add(product);

        UpdatePromoActivityParam promoActivity = new UpdatePromoActivityParam();
        promoActivity.setIncentiveLimitedFlag(IncentiveLimitedFlagEnum.NO.code());
        promoActivity.setConditionProductType("01");
        promoActivity.setSkuToken("");
        promoActivity.setTemplateCode("0102020203020401");
        promoActivity.setFuncParams(funcParams);
        promoActivity.setProducts(products);

        return buildAcivity(promoActivity);
    }

    public static UpdatePromoActivityParam buildActivity0102_0202_0302_0402_商品范围满数量打折扣() {

        List<FunctionParam> funcParams = Arrays.asList(
            ActivityHelper.buildFunctionParam("0102", "", 1), ActivityHelper.buildFunctionParam("0202", "", 1),
            ActivityHelper.buildFunctionParam("0302", "2", 1), ActivityHelper.buildFunctionParam("0402", "0.3", 1));

        List<IncentiveLimited> limits = new ArrayList<>();
        IncentiveLimited limit = new IncentiveLimited();
        limit.setLimitationCode("01");
        limit.setLimitationValue(new BigDecimal(20));
        limits.add(limit);
        UpdatePromoActivityParam promoActivity = new UpdatePromoActivityParam();
        promoActivity.setIncentiveLimitedFlag("01");
        promoActivity.setConditionProductType("00");
        promoActivity.setSkuToken("");

        promoActivity.setTemplateCode("0102020203020402");
        promoActivity.setFuncParams(funcParams);
        promoActivity.setIncentiveLimiteds(limits);

        return buildAcivity(promoActivity);
    }

    public static UpdatePromoActivityParam buildActivity0102_0203_0302_0401_商品范围每满数量减金额() {

        List<FunctionParam> funcParams = Arrays.asList(
            ActivityHelper.buildFunctionParam("0102", "", 1), ActivityHelper.buildFunctionParam("0203", "", 1),
            ActivityHelper.buildFunctionParam("0302", "2", 1), ActivityHelper.buildFunctionParam("0401", "100", 1));

        List<IncentiveLimited> limits = new ArrayList<>();
        IncentiveLimited limit = new IncentiveLimited();
        limit.setLimitationCode("01");
        limit.setLimitationValue(new BigDecimal(20));
        limits.add(limit);
        UpdatePromoActivityParam promoActivity = new UpdatePromoActivityParam();
        promoActivity.setIncentiveLimitedFlag("01");
        promoActivity.setConditionProductType("00");
        promoActivity.setSkuToken("");

        promoActivity.setTemplateCode("0102020303020401");
        promoActivity.setFuncParams(funcParams);
        promoActivity.setIncentiveLimiteds(limits);

        return buildAcivity(promoActivity);
    }

    public static UpdatePromoActivityParam buildActivity0102_0202_0302_0406_商品范围满数量送赠品()
    {
        List<FunctionParam> funcParams = Arrays.asList(
            ActivityHelper.buildFunctionParam("0102", "", 1), ActivityHelper.buildFunctionParam("0202", "", 1),
            ActivityHelper.buildFunctionParam("0302", "2", 1), ActivityHelper.buildFunctionParam("0406", "1", 1));

        List<Giveaway> gifts = new ArrayList<>();
        Giveaway activityGift = new Giveaway();
        activityGift.setGiveawayCode("GSC001");
        activityGift.setGiveawayName("GSN001");
        activityGift.setGiveawayType(1);
        activityGift.setGiveawayNum(2);
        gifts.add(activityGift);
        List<ProductScope> products = new ArrayList<>();

        UpdatePromoActivityParam promoActivity = new UpdatePromoActivityParam();
        promoActivity.setIncentiveLimitedFlag(IncentiveLimitedFlagEnum.NO.code());
        promoActivity.setConditionProductType("00");
        promoActivity.setSkuToken("");
        promoActivity.setTemplateCode("0102020203020406");
        promoActivity.setFuncParams(funcParams);
        promoActivity.setGiveaways(gifts);
        promoActivity.setProducts(products);
        promoActivity.setActivityCode("0102020203020406");

        return buildAcivity(promoActivity);
    }

    public static UpdatePromoActivityParam buildActivity0103_0202_0302_0405_订单满数量包邮() {
        List<FunctionParam> funcParams = Arrays.asList(
            ActivityHelper.buildFunctionParam("0103", "", 1), ActivityHelper.buildFunctionParam("0202", "", 1),
            ActivityHelper.buildFunctionParam("0302", "2", 1), ActivityHelper.buildFunctionParam("0405", "", 1));

        List<ProductScope> products = new ArrayList<>();
        ProductScope product = new ProductScope();
        product.setCategoryCode("4546>65465>546545");
        product.setCategoryName("GXG");
        products.add(product);

        UpdatePromoActivityParam promoActivity = new UpdatePromoActivityParam();
        promoActivity.setIncentiveLimitedFlag(IncentiveLimitedFlagEnum.NO.code());
        promoActivity.setConditionProductType("00");
        promoActivity.setSkuToken("");
        promoActivity.setTemplateCode("0103020203020405");
        promoActivity.setFuncParams(funcParams);
        promoActivity.setProducts(products);

        return buildAcivity(promoActivity);
    }

    public static UpdatePromoActivityParam buildActivity0102_0202_0302_0404_商品范围满数量设为总计特价() {

        List<FunctionParam> funcParams = Arrays.asList(
            ActivityHelper.buildFunctionParam("0102", "", 1), ActivityHelper.buildFunctionParam("0202", "", 1),
            ActivityHelper.buildFunctionParam("0302", "2", 1), ActivityHelper.buildFunctionParam("0404", "5", 1));

        List<ProductScope> products = new ArrayList<>();
        ProductScope product = new ProductScope();
        product.setAttributes(Arrays.asList(ProductAttribute.builder().attributeCode("12").attributeValues("122").build()));
        products.add(product);

        UpdatePromoActivityParam promoActivity = new UpdatePromoActivityParam();
        promoActivity.setIncentiveLimitedFlag(IncentiveLimitedFlagEnum.NO.code());
        promoActivity.setConditionProductType("00");
        promoActivity.setSkuToken("");
        promoActivity.setTemplateCode("0102020203020404");
        promoActivity.setFuncParams(funcParams);
        promoActivity.setProducts(products);

        return buildAcivity(promoActivity);
    }

    public static UpdatePromoActivityParam buildActivity0102_0203_0302_0407_商品范围每满数量送同类商品() {

        List<FunctionParam> funcParams = Arrays.asList(
            ActivityHelper.buildFunctionParam("0102", "", 1), ActivityHelper.buildFunctionParam("0203", "", 1),
            ActivityHelper.buildFunctionParam("0302", "2", 1), ActivityHelper.buildFunctionParam("0407", "6", 1));

        List<ProductScope> products = new ArrayList<>();
        ProductScope product = new ProductScope();
        product.setAttributes(Arrays.asList(ProductAttribute.builder().attributeCode("121").attributeValues("1221").build()));
        product.setSeqNum(1);
        products.add(product);

        UpdatePromoActivityParam promoActivity = new UpdatePromoActivityParam();

        promoActivity.setIncentiveLimitedFlag(IncentiveLimitedFlagEnum.NO.code());
        promoActivity.setConditionProductType("01");
        promoActivity.setSkuToken("");
        promoActivity.setTemplateCode("0102020303020407");
        promoActivity.setFuncParams(funcParams);
        promoActivity.setProducts(products);

        return buildAcivity(promoActivity);
    }


    public static UpdatePromoActivityParam buildActivity0102_0208_0302_0416_商品范围每数量免费() {

        List<FunctionParam> funcParams = Arrays.asList(
                ActivityHelper.buildFunctionParam("0102", "", 1), ActivityHelper.buildFunctionParam("0208", "", 1),
                ActivityHelper.buildFunctionParam("0302", "2", 1), ActivityHelper.buildFunctionParam("0416", "1", 1));

        List<ProductScope> products = new ArrayList<>();
        ProductScope product = new ProductScope();
        product.setAttributes(Arrays.asList(ProductAttribute.builder().attributeCode("121").attributeValues("1221").build()));
        product.setSeqNum(1);
        products.add(product);

        UpdatePromoActivityParam promoActivity = new UpdatePromoActivityParam();

        promoActivity.setIncentiveLimitedFlag(IncentiveLimitedFlagEnum.NO.code());
        promoActivity.setConditionProductType("01");
        promoActivity.setSkuToken("");
        promoActivity.setTemplateCode("0102020803020416");
        promoActivity.setFuncParams(funcParams);
        promoActivity.setProducts(products);

        return buildAcivity(promoActivity);
    }


    public static UpdatePromoActivityParam buildActivity0101_0208_0302_0416_单品每数量免费() {

        List<FunctionParam> funcParams = Arrays.asList(
                ActivityHelper.buildFunctionParam("0101", "", 1), ActivityHelper.buildFunctionParam("0208", "", 1),
                ActivityHelper.buildFunctionParam("0302", "2", 1), ActivityHelper.buildFunctionParam("0416", "1", 1));

        List<ProductScope> products = new ArrayList<>();
        ProductScope product = new ProductScope();
        product.setAttributes(Arrays.asList(ProductAttribute.builder().attributeCode("121").attributeValues("1221").build()));
        product.setSeqNum(1);
        products.add(product);

        UpdatePromoActivityParam promoActivity = new UpdatePromoActivityParam();

        promoActivity.setIncentiveLimitedFlag(IncentiveLimitedFlagEnum.NO.code());
        promoActivity.setConditionProductType("01");
        promoActivity.setSkuToken("");
        promoActivity.setTemplateCode("0101020803020416");
        promoActivity.setFuncParams(funcParams);
        promoActivity.setProducts(products);

        return buildAcivity(promoActivity);
    }


    public static UpdatePromoActivityParam buildActivity0101_0206_0302_0416_单品每数量免费() {

        List<FunctionParam> funcParams = Arrays.asList(
                ActivityHelper.buildFunctionParam("0102", "", 1), ActivityHelper.buildFunctionParam("0208", "", 1),
                ActivityHelper.buildFunctionParam("0302", "2", 1), ActivityHelper.buildFunctionParam("0416", "1", 1));

        List<ProductScope> products = new ArrayList<>();
        ProductScope product = new ProductScope();
        product.setAttributes(Arrays.asList(ProductAttribute.builder().attributeCode("121").attributeValues("1221").build()));
        product.setSeqNum(1);
        products.add(product);

        UpdatePromoActivityParam promoActivity = new UpdatePromoActivityParam();

        promoActivity.setIncentiveLimitedFlag(IncentiveLimitedFlagEnum.NO.code());
        promoActivity.setConditionProductType("01");
        promoActivity.setSkuToken("");
        promoActivity.setTemplateCode("0102020603020416");
        promoActivity.setFuncParams(funcParams);
        promoActivity.setProducts(products);

        return buildAcivity(promoActivity);
    }


    public static UpdatePromoActivityParam buildActivity0102_0206_0302_0416_商品范围每数量免费() {

        List<FunctionParam> funcParams = Arrays.asList(
                ActivityHelper.buildFunctionParam("0102", "", 1), ActivityHelper.buildFunctionParam("0206", "", 1),
                ActivityHelper.buildFunctionParam("0302", "2", 1), ActivityHelper.buildFunctionParam("0416", "1", 1));

        List<ProductScope> products = new ArrayList<>();
        ProductScope product = new ProductScope();
        product.setAttributes(Arrays.asList(ProductAttribute.builder().attributeCode("121").attributeValues("1221").build()));
        product.setSeqNum(1);
        products.add(product);

        UpdatePromoActivityParam promoActivity = new UpdatePromoActivityParam();

        promoActivity.setIncentiveLimitedFlag(IncentiveLimitedFlagEnum.NO.code());
        promoActivity.setConditionProductType("01");
        promoActivity.setSkuToken("");
        promoActivity.setTemplateCode("0102020603020416");
        promoActivity.setFuncParams(funcParams);
        promoActivity.setProducts(products);

        return buildAcivity(promoActivity);
    }

    public static UpdatePromoActivityParam buildActivity0102_0205_0302_0404_多组商品范围各选1件设为总计特价() {

        List<FunctionParam> funcParams = Arrays.asList(
            ActivityHelper.buildFunctionParam("0102", "", 1), ActivityHelper.buildFunctionParam("0205", "", 1),
            ActivityHelper.buildFunctionParam("0302", "2", 1), ActivityHelper.buildFunctionParam("0404", "7", 1));

        List<ProductScope> products = new ArrayList<>();
        ProductScope product = new ProductScope();
        product.setAttributes(Arrays.asList(ProductAttribute.builder().attributeCode("12").attributeValues("122").build()));
        product.setSeqNum(1);
        ProductScope product2 = new ProductScope();
        product2.setAttributes(Arrays.asList(ProductAttribute.builder().attributeCode("12212123").attributeValues("sdfds12df").build()));
        product2.setSeqNum(2);
        products.add(product);
        products.add(product2);

        UpdatePromoActivityParam promoActivity = new UpdatePromoActivityParam();
        promoActivity.setIncentiveLimitedFlag(IncentiveLimitedFlagEnum.NO.code());
        promoActivity.setConditionProductType("03");
        promoActivity.setSkuToken("");
        promoActivity.setTemplateCode("0102020503020404");
        promoActivity.setFuncParams(funcParams);
        promoActivity.setProducts(products);

        return buildAcivity(promoActivity);
    }

    // 创建促销活动
    private static UpdatePromoActivityParam buildAcivity(UpdatePromoActivityParam promoActivity) {

        String beginTime = DateUtil.format(DateUtil.addDay(-1), DateUtil.FORMAT_YYYYMMDDHHMISS_14);
        String endTime = DateUtil.format(DateUtil.addDay(1), DateUtil.FORMAT_YYYYMMDDHHMISS_14);

        promoActivity.setDomainCode(domainCode);
        promoActivity.setTenantCode(tenantCode);
        promoActivity.setActivityType("01");
        promoActivity.setActivityName("unit test");
        promoActivity.setActivityLanguages(buildActivityLanguages(1, promoActivity.getTemplateCode()));
        promoActivity.setActivityBegin(beginTime);
        promoActivity.setActivityEnd(endTime);
        promoActivity.setOpsType("666");
        return promoActivity;
    }

}
