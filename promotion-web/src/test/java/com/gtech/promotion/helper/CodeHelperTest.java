package com.gtech.promotion.helper;

import org.junit.Test;

public class CodeHelperTest {
    private String a = "fsd;fjaskfljkadlfjaklfjsdklafjldajfklsdjfkla;fldjfljdflsjflsdjakfjalsdjfkljasdl";

    @Test
    public void getCheckSumTest(){

        try {
            CodeHelper.getCheckSum(a);
        }catch (Exception e){

        }
    }

    @Test
    public void compareActivityCodeTest(){

        CodeHelper.compareActivityCode(null, null);
        CodeHelper.compareActivityCode("1234",null);
    }

    @Test
    public void validateCode(){
        CodeHelper.validateCode(a);
        String code ="12345";
        CodeHelper.validateCode(code);
    }
}
