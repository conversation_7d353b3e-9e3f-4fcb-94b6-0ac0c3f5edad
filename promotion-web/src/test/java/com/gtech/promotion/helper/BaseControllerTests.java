/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.helper;

import com.alibaba.fastjson.JSON;
import com.gtech.commons.exception.GTechBaseException;
import com.gtech.commons.page.PageData;
import com.gtech.commons.utils.StringUtil;
import lombok.Data;
import org.junit.Assert;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.mock.web.MockHttpSession;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.result.MockMvcResultHandlers;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.web.context.WebApplicationContext;
import org.springframework.web.cors.CorsConfiguration;
import org.springframework.web.cors.UrlBasedCorsConfigurationSource;

import java.io.Serializable;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.UUID;

/**
 * 
 *
 * <AUTHOR>
 * @Date 2019-09-28
 */
public class BaseControllerTests {

    // MockMvc对象
    protected static MockMvc mockMvc;

    protected static MockHttpSession session;

    @Autowired
    protected WebApplicationContext wac;

    @Data
    public static class TestResult {

        private String code;
        private String message;
        private String data;
        private boolean success;
    }

    @Data
    public static class TestPageData {

        private Long total;
        private String list;
    }

    /**
     * 解析ResponseContent数据为TestResult
     * 
     * @param responseContent ResponseContent数据
     * @param excludeErrorCodes 排除检查的错误代码
     * <AUTHOR>
     */
    protected TestResult parseResult(String responseContent, String...excludeErrorCodes) {

        TestResult resultVO = JSON.parseObject(responseContent, TestResult.class);
        if (null != excludeErrorCodes && Arrays.asList(excludeErrorCodes).contains(resultVO.getCode())) {
            return resultVO;
        }
//        Assert.assertTrue(resultVO.isSuccess());
        return resultVO;
    }

    /**
     * 解析ResponseContent数据为TestResult
     * 
     * @param testResult ResponseContent数据
     * @param targetClazz 排除检查的错误代码
     * <AUTHOR>
     */
    protected <T extends Serializable> PageData<T> parsePageData(TestResult testResult, Class<T> targetClazz) {

        PageData<T> pageData = new PageData<>();

        TestPageData testPageData = JSON.parseObject(testResult.getData(), TestPageData.class);
        if (testPageData != null) {

            pageData.setTotal(testPageData.getTotal());
            
            if (StringUtil.isNotBlank(testPageData.getList())) {

                List<T> list = JSON.parseArray(testPageData.getList(), targetClazz);

                pageData.setList(list);
            }
        }

        return pageData;
    }

    protected TestResult mockMvcPost(String url, Object contentObject, String...excludeErrorCodes) {

        try {
            String resultString = this.mockMvcPostWithoutParese(url, contentObject);

            return parseResult(resultString, excludeErrorCodes);
        } catch (GTechBaseException e) {
            System.err.println(e.getClass() + "{[" + e.getCode() + "]" + e.getMessage() + "}"); // NOSONAR
        } catch (Exception e) {
            e.printStackTrace(); // NOSONAR
            Assert.assertTrue(false);
        }

        return null;
    }

    protected String mockMvcPostWithoutParese(String url, Object contentObject) {

        try {
            String jsonContent = (null == contentObject ? "{}" : JSON.toJSONString(contentObject));
            
            MvcResult result = mockMvc.perform(MockMvcRequestBuilders
                .post(url).contentType(MediaType.APPLICATION_JSON)
                .header(HttpHeaders.ORIGIN, getOrigin())
                .content(jsonContent)).andDo(MockMvcResultHandlers.print()).andReturn();

            String resultString = result.getResponse().getContentAsString();
            System.err.println(resultString); // NOSONAR

            return resultString;
        } catch (GTechBaseException e) {
            System.err.println(e.getClass() + "{[" + e.getCode() + "]" + e.getMessage() + "}"); // NOSONAR
        } catch (Exception e) {
            e.printStackTrace(); // NOSONAR
            Assert.assertTrue(false);
        }

        return null;
    }

    protected TestResult mockMvcGet(String url, String...excludeErrorCodes) {

        try {
            String resultString = this.mockMvcGetWithoutParese(url);

            return parseResult(resultString, excludeErrorCodes);
        } catch (GTechBaseException e) {
            System.err.println(e.getClass() + "{[" + e.getCode() + "]" + e.getMessage() + "}"); // NOSONAR
        } catch (Exception e) {
            e.printStackTrace(); // NOSONAR
            Assert.assertTrue(false);
        }

        return null;
    }

    protected String mockMvcGetWithoutParese(String url) {

        try {
            MvcResult result = mockMvc.perform(MockMvcRequestBuilders
                .get(url).contentType(MediaType.APPLICATION_JSON)
                .header(HttpHeaders.ORIGIN, this.getOrigin())).andReturn();

            String resultString = result.getResponse().getContentAsString();
            System.err.println(resultString); // NOSONAR
            
            return resultString;
        } catch (GTechBaseException e) {
            System.err.println(e.getClass() + "{[" + e.getCode() + "]" + e.getMessage() + "}"); // NOSONAR
        } catch (Exception e) {
            e.printStackTrace(); // NOSONAR
            Assert.assertTrue(false);
        }

        return null;
    }

    public void setup() {

        UrlBasedCorsConfigurationSource source = new UrlBasedCorsConfigurationSource();
        CorsConfiguration config = new CorsConfiguration();
        config.setAllowCredentials(true);
        source.registerCorsConfiguration("/**", config); // CORS 配置对所有接口都有效

//        CasabaCorsFilter corsFiler = new CasabaCorsFilter(source, unexTenantService, requestHeadConfiguration,null);

//        mockMvc = MockMvcBuilders.webAppContextSetup(wac).addFilters(corsFiler).build(); //初始化MockMvc对象 // NOSONAR
        mockMvc = MockMvcBuilders.webAppContextSetup(wac).build(); //初始化MockMvc对象 // NOSONAR
        session = new MockHttpSession(null, UUID.randomUUID().toString().replaceAll("-", "").toLowerCase()); // NOSONAR
    }

    public void standaloneSetup() {

        UrlBasedCorsConfigurationSource source = new UrlBasedCorsConfigurationSource();
        CorsConfiguration config = new CorsConfiguration();
        config.setAllowCredentials(true);
        source.registerCorsConfiguration("/**", config); // CORS 配置对所有接口都有效

//        CasabaCorsFilter corsFiler = new CasabaCorsFilter(source, unexTenantService, requestHeadConfiguration,null);

//        mockMvc = MockMvcBuilders.webAppContextSetup(wac).addFilters(corsFiler).build(); //初始化MockMvc对象 // NOSONAR
        mockMvc = MockMvcBuilders.standaloneSetup(wac).build(); //初始化MockMvc对象 // NOSONAR
        session = new MockHttpSession(null, UUID.randomUUID().toString().replaceAll("-", "").toLowerCase()); // NOSONAR
    }

    protected MockHttpSession getHttpSession() {

        return session;
    }

    protected String getOrigin() {

        return "http://titan-dev.gtech.asia";
    }

    /**
     * Generate a random code.
     */
    protected String randomCode(int length) {

        String randomCode = String.valueOf(new Date().getTime());
        while (randomCode.length() < length) {
            randomCode += String.valueOf(new Date().getTime());
        }

        if (randomCode.length() > length) {
            randomCode = randomCode.substring(randomCode.length() - length, randomCode.length());
        }

        return randomCode;
    }

    protected void sleep(long time) {

        try {
            Thread.sleep(time);// NOSONAR
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
    }
}
