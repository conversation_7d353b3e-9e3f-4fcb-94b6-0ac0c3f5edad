/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.helper.factory;

import com.gtech.promotion.dao.model.activity.ActivityModel;
import com.gtech.promotion.dto.in.activity.ShoppingCartActivity;
import com.gtech.promotion.dto.in.activity.ShoppingCartDTO;
import com.gtech.promotion.dto.in.activity.ShoppingCartItem;
import com.gtech.promotion.utils.DateValidUtil;
import com.gtech.promotion.vo.bean.ProductAttribute;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;

/**
 * <功能描述>
 *
 */
public class ShoppingCartFactory{

    private static final String USELESS = "字符串填充";

    /**
     * 创建购物车
     * 
     * @param couponCodes 券码
     * @param shoppingCartItems 购物车商品集
     * @param memberCode 会员等级和标签编码
     * @return 购物车对象
     */
    public static ShoppingCartDTO createShoppingCart(String couponCodes, List<ShoppingCartItem> shoppingCartItems, String memberCode){
        ShoppingCartDTO shoppingCart = new ShoppingCartDTO();
        shoppingCart.setQualifications(new HashMap<>());
        shoppingCart.setPromoProducts(shoppingCartItems);
        shoppingCart.setTenantCode("8888");
        shoppingCart.setCouponCodes(couponCodes);
        shoppingCart.setUserCode("8888");
        shoppingCart.setPostage(new BigDecimal("50"));
        shoppingCart.setPromotionTime(DateValidUtil.getNowFormatDate());
        shoppingCart.setActivityExpr("123123");
        return shoppingCart;
    }

    /**
     * 根据分类编码创建购物车的商品项
     * 
     * @param categoryCode
     *            分类编码
     * @param productPrice
     *            单价
     * @param quantity
     *            数量
     * @return 购物车商品项
     */
    public static ShoppingCartItem createShoppingCartItemByCategory(String categoryCode,BigDecimal productPrice,int quantity){
        return createShoppingCartItem(categoryCode, USELESS, USELESS, USELESS, productPrice, quantity, null);
    }

    /**
     * 根据品牌编码创建购物车商品项
     * 
     * @param brandCode
     *            品牌编码
     * @param productPrice
     *            单价
     * @param quantity
     *            数量
     * @return 购物车商品项
     */
    public static ShoppingCartItem createShoppingCartItemByBrand(String brandCode,BigDecimal productPrice,int quantity){
        return createShoppingCartItem(USELESS, brandCode, USELESS, USELESS, productPrice, quantity, null);
    }

    /**
     * 根据品牌编码创建购物车商品项
     * 
     * @param brandCode
     *            品牌编码
     * @param productPrice
     *            单价
     * @param quantity
     *            数量
     * @return 购物车商品项
     */
    public static ShoppingCartItem createShoppingCartItemByBrandAndSku(String brandCode,String skuCode,BigDecimal productPrice,int quantity){
        return createShoppingCartItem(USELESS, brandCode, USELESS, skuCode, productPrice, quantity, null);
    }

    /**
     * 根据spu和sku创建购物车的商品项
     * 
     * @param productCode -- Product code.
     * @param skuCode sku编码
     * @param productPrice
     *            单价
     * @param quantity
     *            数量
     * @return 购物车商品项
     */
    public static ShoppingCartItem createShoppingCartItemBySku(String productCode,String skuCode,BigDecimal productPrice,int quantity){
        return createShoppingCartItem(USELESS, USELESS, productCode, skuCode, productPrice, quantity, null);
    }

    /**
     * 根据属性集创建购物车的商品项
     * 
     * @param attributes
     *            属性集
     * @param productPrice
     *            单价
     * @param quantity
     *            数量
     * @return 购物车商品项
     */
    public static ShoppingCartItem createShoppingCartItemByAttribute(List<ProductAttribute> attributes,BigDecimal productPrice,int quantity){
        return createShoppingCartItem(USELESS, USELESS, USELESS, USELESS, productPrice, quantity, attributes);
    }

    /**
     * 根据属性集创建购物车的商品项
     * 
     * @param attributeCode
     *            属性集
     * @param productPrice
     *            单价
     * @param quantity
     *            数量
     * @return 购物车商品项
     */
    public static ShoppingCartItem createShoppingCartItemByAttributeOne(String attributeCode,String attributeValue,BigDecimal productPrice,int quantity){
        List<ProductAttribute> attributes = new ArrayList<ProductAttribute>();
        ProductAttribute attributelDTO = new ProductAttribute();
        attributelDTO.setAttributeCode(attributeCode);
        attributelDTO.setAttributeValues(attributeValue);
        attributes.add(attributelDTO);
        return createShoppingCartItem(USELESS, USELESS, USELESS, USELESS, productPrice, quantity, attributes);
    }

    /**
     * 创建购物车的商品项
     * 
     * @param categoryCode 分类编码
     * @param brandCode 品牌编码
     * @param productCode -- Product code.
     * @param skuCode sku编码
     * @param productPrice 单价
     * @param quantity 数量
     * @param attributes 属性集
     * @return 购物车商品项
     */
    public static ShoppingCartItem createShoppingCartItem(String categoryCode,String brandCode,String productCode,String skuCode,BigDecimal productPrice,int quantity,
                    List<ProductAttribute> attributes){
        ShoppingCartItem shoppingCartItem = new ShoppingCartItem();
        shoppingCartItem.setSelectionFlag("01");
        shoppingCartItem.setCategoryCodes(Arrays.asList(categoryCode));
        shoppingCartItem.setBrandCode(brandCode);
        shoppingCartItem.setProductCode(productCode);
        shoppingCartItem.setSkuCode(skuCode);
        shoppingCartItem.setQuantity(quantity);
        shoppingCartItem.setAttributes(attributes);
        shoppingCartItem.setProductPrice(productPrice);
        shoppingCartItem.setProductAmount(productPrice.multiply(new BigDecimal(quantity)));
        return shoppingCartItem;
    }
}
