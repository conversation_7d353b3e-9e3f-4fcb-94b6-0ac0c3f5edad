package com.gtech.promotion.component;

import com.gtech.basic.masterdata.client.MasterDataClient;
import com.gtech.basic.masterdata.response.JsonResult;
import com.gtech.commons.redis.GTechRedisTemplate;
import com.gtech.promotion.component.activity.ActivityPriceComponentDomain;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class ActivityPriceComponentDomainTest {

    @InjectMocks
    private ActivityPriceComponentDomain activityPriceComponent;

    @Mock
    private MasterDataClient masterDataClient;

    @Mock
    private GTechRedisTemplate redisTemplate;



    @Test
    public void getTenantPrecision(){

        Mockito.when(redisTemplate.opsValueGet(any(),any(),any())).thenReturn(null);

        activityPriceComponent.getTenantPrecision(null);

        JsonResult<String> jsonResult = new JsonResult<>();
        when(masterDataClient.getValueValue(any(),any())).thenReturn(null);
        activityPriceComponent.getTenantPrecision("100001");

        jsonResult.setData("1");
        when(masterDataClient.getValueValue(any(),any())).thenReturn(jsonResult);
        activityPriceComponent.getTenantPrecision("100001");

        when(masterDataClient.getValueValue(any(),any())).thenThrow(new RuntimeException());
        activityPriceComponent.getTenantPrecision("100001");
    }
}
