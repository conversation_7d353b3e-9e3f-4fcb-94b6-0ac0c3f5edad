package com.gtech.promotion.component.feign;

import com.gtech.basic.masterdata.response.JsonResult;
import com.gtech.promotion.feign.MasterDataFeignClient;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class MasterDataFeignClientComponentTest {

    @InjectMocks
    private MasterDataFeignClientComponent masterDataFeignClientComponent;

    @Mock
    private MasterDataFeignClient masterDataFeignClient;

    @Test
    public void masterDataValue(){
        String tenantCode = "100000";
        String masterDataCode = "test";
        String data = "testVal";
        JsonResult<String> jsonResult = JsonResult.success(data);
        Mockito.when(masterDataFeignClient.getValueValue(Mockito.any(), Mockito.any())).thenReturn(jsonResult);
        String value = masterDataFeignClientComponent.masterDataValue(tenantCode, masterDataCode);
        Assert.assertEquals(data, value);


        Mockito.when(masterDataFeignClient.getValueValue(Mockito.any(), Mockito.any())).thenThrow(new RuntimeException());
        value = masterDataFeignClientComponent.masterDataValue(tenantCode, masterDataCode);
        Assert.assertNull(value);

    }
}
