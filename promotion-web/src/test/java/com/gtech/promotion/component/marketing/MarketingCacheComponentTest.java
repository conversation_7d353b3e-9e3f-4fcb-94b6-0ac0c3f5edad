package com.gtech.promotion.component.marketing;

import com.alibaba.fastjson.JSONObject;
import com.gtech.commons.utils.DateUtil;
import com.gtech.promotion.code.activity.ActivityStatusEnum;
import com.gtech.promotion.dao.model.marketing.MarketingLanguageModel;
import com.gtech.promotion.dao.model.marketing.MarketingModel;
import com.gtech.promotion.dao.model.marketing.flashsale.CacheFlashSaleModel;
import com.gtech.promotion.dao.model.marketing.flashsale.FlashSaleProductModel;
import com.gtech.promotion.exception.PromotionException;
import com.gtech.promotion.service.activity.ActivityPeriodService;
import com.gtech.promotion.service.flashsale.FlashSaleQualificationService;
import com.gtech.promotion.service.flashsale.FlashSaleStoreService;
import com.gtech.promotion.service.marketing.impl.MarketingLanguageServiceImpl;
import com.gtech.promotion.service.marketing.impl.MarketingServiceImpl;
import com.gtech.promotion.vo.bean.ProductCodes;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import java.util.*;

import static org.mockito.ArgumentMatchers.any;


@RunWith(MockitoJUnitRunner.class)
public class MarketingCacheComponentTest {

    @InjectMocks
    private MarketingCacheComponent marketingCacheComponent;
    @Mock
    private MarketingServiceImpl marketingService;
    @Mock
    private MarketingLanguageServiceImpl marketingLanguageService;
    @Mock
    private StringRedisTemplate redisTemplate;
    @Mock
    private FlashSaleQualificationService flashSaleQualificationService;
    @Mock
    private FlashSaleStoreService flashSaleStoreService;
    @Mock
    private ActivityPeriodService activityPeriodService;


    @Test
    public void updateCacheByStatus(){
        MarketingModel marketingModel = new MarketingModel();
        MarketingModel marketingModel1 = new MarketingModel();
        marketingModel1.setActivityEnd(DateUtil.format(DateUtil.addDay(1), DateUtil.FORMAT_YYYYMMDDHHMISS_14));
        Mockito.when(marketingService.findByActivityCode(any())).thenReturn(marketingModel1);
        Mockito.when(redisTemplate.delete(Mockito.anyString())).thenReturn(true);
        marketingCacheComponent.updateCacheByStatus(marketingModel, ActivityStatusEnum.END.code());
    }

    @Test
    public void updateCacheByStatus1(){
        MarketingModel marketingModel = new MarketingModel();
        MarketingModel marketingModel1 = new MarketingModel();
        ValueOperations<String, String> valueOperations = Mockito.mock(ValueOperations.class);
        marketingModel1.setActivityEnd(DateUtil.format(DateUtil.addDay(1), DateUtil.FORMAT_YYYYMMDDHHMISS_14));
        Mockito.when(marketingService.findByActivityCode(any())).thenReturn(marketingModel1);
        Mockito.when(marketingLanguageService.findListByActivityCode(any())).thenReturn(null);
        Mockito.when(redisTemplate.opsForValue()).thenReturn(valueOperations);
        Mockito.doNothing().when(valueOperations).set(Mockito.anyString(), Mockito.anyString(), Mockito.anyLong(), any());
        marketingCacheComponent.updateCacheByStatus(marketingModel, ActivityStatusEnum.EFFECTIVE.code());
    }

    @Test(expected = Exception.class)
    public void updateCacheByStatus2(){
        MarketingModel marketingModel = new MarketingModel();
        MarketingModel marketingModel1 = new MarketingModel();
        ValueOperations<String, String> valueOperations = Mockito.mock(ValueOperations.class);
        marketingModel1.setActivityEnd(DateUtil.format(DateUtil.addDay(1), DateUtil.FORMAT_YYYYMMDDHHMISS_14));
        Mockito.when(marketingService.findByActivityCode(any())).thenReturn(null);
        marketingCacheComponent.updateCacheByStatus(marketingModel, ActivityStatusEnum.EFFECTIVE.code());
    }

    @Test(expected = PromotionException.class)
    public void updateCacheByStatus3(){
        MarketingModel marketingModel = new MarketingModel();
        MarketingModel marketingModel1 = new MarketingModel();
        marketingModel1.setActivityEnd(DateUtil.format(new Date(),DateUtil.FORMAT_YYYYMMDDHHMISS_14));
        ValueOperations<String, String> valueOperations = Mockito.mock(ValueOperations.class);
        Mockito.when(marketingService.findByActivityCode(any())).thenReturn(marketingModel1);
        marketingCacheComponent.updateCacheByStatus(marketingModel, ActivityStatusEnum.EFFECTIVE.code());
    }

    @Test
    public void updateCacheByStatus4(){
        MarketingModel marketingModel = new MarketingModel();
        MarketingModel marketingModel1 = new MarketingModel();
        marketingModel1.setActivityType("04");
        ValueOperations<String, String> valueOperations = Mockito.mock(ValueOperations.class);
        marketingModel1.setActivityEnd(DateUtil.format(new Date(System.currentTimeMillis()+1000), DateUtil.FORMAT_YYYYMMDDHHMISS_14));
        Mockito.when(marketingService.findByActivityCode(any())).thenReturn(marketingModel1);
        Mockito.when(marketingLanguageService.findListByActivityCode(any())).thenReturn(null);
        Mockito.when(flashSaleQualificationService.findListByActivityCode(any())).thenReturn(new ArrayList<>());
        Mockito.when(flashSaleStoreService.findListByActivityCode(any())).thenReturn(new ArrayList<>());
        Mockito.when(activityPeriodService.findPeriod(any(), any())).thenReturn(null);
        Mockito.when(redisTemplate.opsForValue()).thenReturn(valueOperations);
        Mockito.doNothing().when(valueOperations).set(Mockito.anyString(), Mockito.anyString(), Mockito.anyLong(), any());
        marketingCacheComponent.updateCacheByStatus(marketingModel, ActivityStatusEnum.EFFECTIVE.code());
    }

    @Test
    public void updateCacheByStatus5(){
        MarketingModel marketingModel = new MarketingModel();
        MarketingModel marketingModel1 = new MarketingModel();
        marketingModel1.setActivityType("06");
        ValueOperations<String, String> valueOperations = Mockito.mock(ValueOperations.class);
        marketingModel1.setActivityEnd(DateUtil.format(new Date(System.currentTimeMillis()+1000), DateUtil.FORMAT_YYYYMMDDHHMISS_14));
        Mockito.when(marketingService.findByActivityCode(any())).thenReturn(marketingModel1);
        Mockito.when(redisTemplate.opsForValue()).thenReturn(valueOperations);
        Mockito.doNothing().when(valueOperations).set(Mockito.anyString(), Mockito.anyString(), Mockito.anyLong(), any());
        marketingCacheComponent.updateCacheByStatus(marketingModel, ActivityStatusEnum.EFFECTIVE.code());
    }

    @Test
    public void getMarketingCache(){
        MarketingModel model = new MarketingModel();
        ValueOperations<String, String> valueOperations = Mockito.mock(ValueOperations.class);
        Mockito.when(redisTemplate.opsForValue()).thenReturn(valueOperations);
        Mockito.when(valueOperations.get(any())).thenReturn(JSONObject.toJSONString(model));
        marketingCacheComponent.getMarketingCache("1","1");
    }

    @Test
    public void getMarketingCache1(){
        MarketingModel model = new MarketingModel();
        ValueOperations<String, String> valueOperations = Mockito.mock(ValueOperations.class);
        Mockito.when(redisTemplate.opsForValue()).thenReturn(valueOperations);
        Mockito.when(valueOperations.get(any())).thenReturn("");
        marketingCacheComponent.getMarketingCache("1","1");
    }

    @Test
    public void getFlashSaleCacheMap(){
        ValueOperations<String, String> valueOperations = Mockito.mock(ValueOperations.class);
        Mockito.when(redisTemplate.opsForValue()).thenReturn(valueOperations);
        Mockito.when(valueOperations.get(any())).thenReturn("");
        marketingCacheComponent.getFlashSaleCacheMap("1","1","04");
    }

    @Test
    public void getFlashSaleCacheMap1(){
        List<MarketingModel> marketingModels = new ArrayList<>();
        MarketingModel model = new MarketingModel();
        model.setActivityEnd(DateUtil.format(DateUtil.addDay(1),DateUtil.FORMAT_YYYYMMDDHHMISS_14));
        marketingModels.add(model);
        ValueOperations<String, String> valueOperations = Mockito.mock(ValueOperations.class);
        Mockito.when(redisTemplate.opsForValue()).thenReturn(valueOperations);
        Mockito.when(valueOperations.get(any())).thenReturn("1");
        Mockito.when(marketingService.queryMarketingList(Mockito.anyString(),Mockito.anyString(),Mockito.anyString())).thenReturn(marketingModels);
        marketingCacheComponent.getFlashSaleCacheMap("1","1","04");
    }

    @Test
    public void getFlashSaleCacheMap2(){
        List<MarketingModel> marketingModels = new ArrayList<>();
        MarketingModel model = new MarketingModel();
        model.setActivityEnd(DateUtil.format(DateUtil.addDay(1),DateUtil.FORMAT_YYYYMMDDHHMISS_14));
        marketingModels.add(model);
        CacheFlashSaleModel saleModel = new CacheFlashSaleModel();
        List<MarketingLanguageModel> languages = new ArrayList<>();
        MarketingLanguageModel languageModel = new MarketingLanguageModel();
        languageModel.setLanguage("1");
        languages.add(languageModel);
        saleModel.setLanguages(languages);
        List<String> values = new ArrayList<>();
        values.add(JSONObject.toJSONString(saleModel));
        ValueOperations<String, String> valueOperations = Mockito.mock(ValueOperations.class);
        Mockito.when(redisTemplate.opsForValue()).thenReturn(valueOperations);
        Mockito.when(valueOperations.get(any())).thenReturn("1");
        Mockito.when(valueOperations.multiGet(any())).thenReturn(values);
        Mockito.when(marketingService.queryMarketingList(Mockito.anyString(),Mockito.anyString(),Mockito.anyString())).thenReturn(marketingModels);
        marketingCacheComponent.getFlashSaleCacheMap("1","1","04");
    }

    @Test
    public void getFlashSaleCacheMap3(){
        List<MarketingModel> marketingModels = new ArrayList<>();
        MarketingModel model = new MarketingModel();
        model.setActivityEnd(DateUtil.format(DateUtil.addDay(1),DateUtil.FORMAT_YYYYMMDDHHMISS_14));
        marketingModels.add(model);
        CacheFlashSaleModel saleModel = new CacheFlashSaleModel();
        List<MarketingLanguageModel> languages = new ArrayList<>();
        MarketingLanguageModel languageModel = new MarketingLanguageModel();
        languageModel.setLanguage("1");
        languageModel.setActivityName("1");
        languageModel.setActivityDesc("1");
        languageModel.setActivityShortDesc("1");
        languageModel.setActivityLabel("1");
        languages.add(languageModel);
        saleModel.setLanguages(languages);
        List<String> values = new ArrayList<>();
        values.add(JSONObject.toJSONString(saleModel));
        ValueOperations<String, String> valueOperations = Mockito.mock(ValueOperations.class);
        Mockito.when(redisTemplate.opsForValue()).thenReturn(valueOperations);
        Mockito.when(valueOperations.get(any())).thenReturn("1");
        Mockito.when(valueOperations.multiGet(any())).thenReturn(values);
        Mockito.when(marketingService.queryMarketingList(Mockito.anyString(),Mockito.anyString(),Mockito.anyString())).thenReturn(marketingModels);
        marketingCacheComponent.getFlashSaleCacheMap("1","1","04");
    }

    @Test
    public void getFlashSaleCacheMap4(){
        List<MarketingModel> marketingModels = new ArrayList<>();
        MarketingModel model = new MarketingModel();
        model.setActivityEnd(DateUtil.format(DateUtil.addDay(1),DateUtil.FORMAT_YYYYMMDDHHMISS_14));
        marketingModels.add(model);
        CacheFlashSaleModel saleModel = new CacheFlashSaleModel();
        List<MarketingLanguageModel> languages = new ArrayList<>();
        MarketingLanguageModel languageModel = new MarketingLanguageModel();
        languageModel.setLanguage("2");
        languageModel.setActivityName("1");
        languageModel.setActivityDesc("1");
        languageModel.setActivityShortDesc("1");
        languageModel.setActivityLabel("1");
        languages.add(languageModel);
        saleModel.setLanguages(languages);
        List<String> values = new ArrayList<>();
        values.add(JSONObject.toJSONString(saleModel));
        ValueOperations<String, String> valueOperations = Mockito.mock(ValueOperations.class);
        Mockito.when(redisTemplate.opsForValue()).thenReturn(valueOperations);
        Mockito.when(valueOperations.get(any())).thenReturn("2d234fbee034d07f0d7cc0c30d4bd339");
        Mockito.when(valueOperations.multiGet(any())).thenReturn(values);
        Mockito.when(marketingService.queryMarketingList(Mockito.anyString(),Mockito.anyString(),Mockito.anyString())).thenReturn(marketingModels);
        marketingCacheComponent.getFlashSaleCacheMap("1","1","04");
        marketingCacheComponent.getFlashSaleCacheMap("1","1","04");
    }

    @Test
    public void getFlashSaleCacheMap5(){
        List<MarketingModel> marketingModels = new ArrayList<>();
        MarketingModel model = new MarketingModel();
        model.setActivityEnd("20210917161622");
        marketingModels.add(model);
        ValueOperations<String, String> valueOperations = Mockito.mock(ValueOperations.class);
        Mockito.when(redisTemplate.opsForValue()).thenReturn(valueOperations);
        Mockito.when(valueOperations.get(any())).thenReturn("1");
        Mockito.when(marketingService.queryMarketingList(Mockito.anyString(),Mockito.anyString(),Mockito.anyString())).thenReturn(marketingModels);
        marketingCacheComponent.getFlashSaleCacheMap("1","1","04");
    }

    @Test
    public void getFlashSaleCacheMap6(){
        List<MarketingModel> marketingModels = new ArrayList<>();
        MarketingModel model = new MarketingModel();
        model.setActivityEnd(DateUtil.format(DateUtil.addDay(1),DateUtil.FORMAT_YYYYMMDDHHMISS_14));
        marketingModels.add(model);
        CacheFlashSaleModel saleModel = new CacheFlashSaleModel();
        List<MarketingLanguageModel> languages = new ArrayList<>();
        MarketingLanguageModel languageModel = new MarketingLanguageModel();
        languageModel.setLanguage("2");
        languageModel.setActivityName("1");
        languageModel.setActivityDesc("1");
        languageModel.setActivityShortDesc("1");
        languageModel.setActivityLabel("1");
        languages.add(languageModel);
        saleModel.setLanguages(languages);
        List<String> values = new ArrayList<>();
        values.add(JSONObject.toJSONString(saleModel));
        ValueOperations<String, String> valueOperations = Mockito.mock(ValueOperations.class);
        Mockito.when(redisTemplate.opsForValue()).thenReturn(valueOperations);
        Mockito.when(valueOperations.get(any())).thenThrow(NullPointerException.class);
        Mockito.when(valueOperations.multiGet(any())).thenReturn(values);
        Mockito.when(marketingService.queryMarketingList(Mockito.anyString(),Mockito.anyString(),Mockito.anyString())).thenReturn(marketingModels);
        marketingCacheComponent.getFlashSaleCacheMap("1","1","04");
        marketingCacheComponent.getFlashSaleCacheMap("1","1","04");
    }

    @Test
    public void expire(){
        marketingCacheComponent.expire("1","1");
    }

    @Test
    public void filterActivityByProduct(){
        Map<String, CacheFlashSaleModel> caches = new HashMap<>();
        List<FlashSaleProductModel> products = new ArrayList<>();
        ProductCodes product = new ProductCodes();
        product.setSkuCode("1");
        product.setCombineSkuCode("1");
        marketingCacheComponent.filterActivityByProduct(caches,product,products,"01" );
    }

    @Test
    public void filterActivityByProduct1(){
        Map<String, CacheFlashSaleModel> caches = new HashMap<>();
        CacheFlashSaleModel saleModel = new CacheFlashSaleModel();
        saleModel.setActivityCode("1");
        caches.put("1",saleModel);
        List<FlashSaleProductModel> products = new ArrayList<>();
        FlashSaleProductModel model = new FlashSaleProductModel();
        model.setActivityCode("1");
        model.setSkuCode("1");
        products.add(model);
        ProductCodes product = new ProductCodes();
        product.setSkuCode("1");
        product.setCombineSkuCode("1");
        marketingCacheComponent.filterActivityByProduct(caches,product,products, "01");
    }

    @Test
    public void filterActivityByProduct2(){
        Map<String, CacheFlashSaleModel> caches = new HashMap<>();
        CacheFlashSaleModel saleModel = new CacheFlashSaleModel();
        saleModel.setActivityCode("1");
        caches.put("1",saleModel);
        List<FlashSaleProductModel> products = new ArrayList<>();
        FlashSaleProductModel model = new FlashSaleProductModel();
        model.setActivityCode("1");
        model.setSkuCode("2");
        products.add(model);
        ProductCodes product = new ProductCodes();
        product.setSkuCode("1");
        product.setCombineSkuCode("1");
        marketingCacheComponent.filterActivityByProduct(caches,product,products, "01");
    }

    @Test
    public void initMarketActivityTest(){
        Mockito.when(marketingService.queryMarketingList(any(),any(),any())).thenReturn(null);
        marketingCacheComponent.initMarketActivity();

        List<MarketingModel> marketingModelList = new ArrayList<>();
        MarketingModel marketingModel1 = new MarketingModel();
        marketingModel1.setTenantCode("100001");
        marketingModel1.setActivityEnd("20221102123212");
        MarketingModel marketingModel2 = new MarketingModel();
        marketingModel2.setTenantCode("100001");
        marketingModel2.setActivityEnd("20221102123212");
        MarketingModel marketingModel3 = new MarketingModel();
        marketingModel3.setTenantCode("100002");
        marketingModel3.setActivityEnd("20221102123212");
        MarketingModel marketingModel4 = new MarketingModel();
        marketingModel4.setTenantCode("100002");
        marketingModel4.setActivityEnd("20221102123212");
        marketingModelList.add(marketingModel1);
        marketingModelList.add(marketingModel2);
        marketingModelList.add(marketingModel3);
        marketingModelList.add(marketingModel4);
        Mockito.when(marketingService.queryMarketingList(any(),any(),any())).thenReturn(marketingModelList);
//        Mockito.when(redisTemplate.opsForValue().get(any())).thenReturn("test");
//        Mockito.doNothing().when(redisTemplate.opsForValue()).set(any(),any(),any(),any());
        ValueOperations<String, String> valueOperations = Mockito.mock(ValueOperations.class);
        Mockito.when(redisTemplate.opsForValue()).thenReturn(valueOperations);
        marketingCacheComponent.initMarketActivity();
    }

}
