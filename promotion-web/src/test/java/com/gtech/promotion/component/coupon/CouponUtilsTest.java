package com.gtech.promotion.component.coupon;

import com.gtech.promotion.helper.CouponUtils;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.List;

/**
 * @Description :
 * <AUTHOR> gaojie.li
 * @Date : 2023/11/14 15:32
 */
@RunWith(MockitoJUnitRunner.class)
public class CouponUtilsTest {



    @Test
    public void generateRandomCouponCode() {
        List<String> arrayList = new ArrayList<>();

        for (int i = 0; i < 100; i++) {
            String s = CouponUtils.generateRandomCouponCode("", 18);
            arrayList.add(s);
        }
        Assert.assertEquals(100, arrayList.size());
    }

    @Test
    public void createCoupons() {
        List<String> arrayList = new ArrayList<>();

        for (int i = 0; i < 100; i++) {
            String s = CouponUtils.createCoupons("200005", "123123123", "", 18);
            arrayList.add(s);
        }
        Assert.assertEquals(100, arrayList.size());
    }
}


