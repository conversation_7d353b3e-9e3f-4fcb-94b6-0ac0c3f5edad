package com.gtech.promotion.component.marketing;

import com.gtech.basic.idm.web.vo.result.QueryUserResult;
import com.gtech.commons.exception.GTechBaseException;
import com.gtech.commons.page.PageData;
import com.gtech.commons.result.Result;
import com.gtech.commons.utils.DateUtil;
import com.gtech.member.web.vo.param.QueryMemberParam;
import com.gtech.member.web.vo.result.QueryMemberListByConditionResult;
import com.gtech.promotion.code.activity.ActivityStatusEnum;
import com.gtech.promotion.code.activity.LimitationCodeEnum;
import com.gtech.promotion.code.marketing.PrizeTypeEnum;
import com.gtech.promotion.code.marketing.TicketStatusEnum;
import com.gtech.promotion.dao.model.activity.ActivityPeriodModel;
import com.gtech.promotion.dao.model.activity.LuckyDrawRuleModel;
import com.gtech.promotion.dao.model.activity.QualificationModel;
import com.gtech.promotion.dao.model.activity.TPromoIncentiveLimitedVO;
import com.gtech.promotion.dao.model.marketing.*;
import com.gtech.promotion.domain.marketing.*;
import com.gtech.promotion.dto.in.marketing.ExportLuckyDrawDto;
import com.gtech.promotion.exception.PromotionException;
import com.gtech.promotion.feign.IdmFeignClient;
import com.gtech.promotion.feign.MemberFeignClient;
import com.gtech.promotion.helper.RedisOpsHelper;
import com.gtech.promotion.service.activity.ActivityPeriodService;
import com.gtech.promotion.service.activity.LuckyDrawRuleService;
import com.gtech.promotion.service.activity.QualificationService;
import com.gtech.promotion.service.activity.TPromoIncentiveLimitedService;
import com.gtech.promotion.service.marketing.*;
import com.gtech.promotion.vo.result.coupon.ActivityParticipateInResult;
import com.gtech.promotion.vo.result.marketing.*;
import org.junit.Assert;
import org.junit.Test;
import org.junit.jupiter.api.Assertions;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@RunWith(MockitoJUnitRunner.class)
public class LuckyDrawComponentTest {

    @InjectMocks
    private LuckyDrawComponent luckyDrawComponent;

    @Mock
    private MarketingService marketingService;

    @Mock
    private MarketingLanguageService languageService;

    @Mock
    private PrizeService prizeService;

    @Mock
    private PrizeLanguageService prizeLanguageService;

    @Mock
    private TicketReleaseService ticketReleaseService;
    @Mock
    private TPromoIncentiveLimitedService incentiveLimitedService;

    @Mock
    private TicketService ticketService;
    @Mock
    private MemberFeignClient memberFeignClient;
    @Mock
    private IdmFeignClient idmFeignClient;

    @Mock
    private PrizeRecordService recordService;
    @Mock
    private ActivityPeriodService activityPeriodService;
    @Mock
    private LuckyDrawRuleService luckyDrawRuleService;
    @Mock
    private QualificationService qualificationService;

    @Mock
    private RedisOpsHelper incentiveLimitedHelper;

    @Mock
    private TicketComponent ticketComponent;

    @Test(expected = PromotionException.class)
    public void getLuckyDrawDetail0(){
        LuckyDrawDetailDomain luckyDrawDetailDomain = new LuckyDrawDetailDomain();
        Mockito.when(marketingService.findByActivityCode(Mockito.any())).thenReturn(null);
        luckyDrawComponent.getLuckyDrawDetail(luckyDrawDetailDomain);
    }

    @Test
    public void getLuckyDrawDetail_1(){
        LuckyDrawDetailDomain luckyDrawDetailDomain = new LuckyDrawDetailDomain();
        MarketingModel marketingModel = new MarketingModel();
        marketingModel.setActivityName("1");
        luckyDrawDetailDomain.setLanguage("1");
        List<PrizeModel> prizeModels = new ArrayList<>();
        PrizeModel prizeModel = new PrizeModel();
        prizeModel.setPrizeNo("1");
        prizeModels.add(prizeModel);
        List<TPromoIncentiveLimitedVO> limitedVOS = new ArrayList<>();
        TPromoIncentiveLimitedVO limitedVO = new TPromoIncentiveLimitedVO();
        limitedVOS.add(limitedVO);
        List<PrizeLanguageModel> prizeLanguageModels = new ArrayList<>();
        PrizeLanguageModel model = new PrizeLanguageModel();
        model.setPrizeNo("1");
        model.setLanguage("1");
        model.setPrizeName("1");
        prizeLanguageModels.add(model);
        ActivityPeriodModel periodModel = new ActivityPeriodModel();
        Mockito.when(marketingService.findByActivityCode(Mockito.any())).thenReturn(marketingModel);
        Mockito.when(languageService.findByLanguage(Mockito.any(), Mockito.any())).thenReturn(null);
        Mockito.when(incentiveLimitedService.getLuckyDrawLimitedListByActivityCode(Mockito.any())).thenReturn(limitedVOS);
        Mockito.when(activityPeriodService.findPeriod(Mockito.any(),Mockito.any())).thenReturn(periodModel);
        Mockito.when(prizeService.findListByActivityCode(Mockito.any())).thenReturn(prizeModels);
        Mockito.when(prizeLanguageService.findListByActivityCode(Mockito.any())).thenReturn(prizeLanguageModels);
        LuckyDrawDetailResult luckyDrawDetail = luckyDrawComponent.getLuckyDrawDetail(luckyDrawDetailDomain);
        Assert.assertEquals(marketingModel.getActivityName(), luckyDrawDetail.getActivityName());
    }


    @Test
    public void getLuckyDrawDetail() {
        LuckyDrawDetailDomain luckyDrawDetailDomain = new LuckyDrawDetailDomain();
        MarketingModel marketingModel = new MarketingModel();
        marketingModel.setActivityName("1");
        List<PrizeModel> prizeModels = new ArrayList<>();
        prizeModels.add(new PrizeModel());
        Mockito.when(marketingService.findByActivityCode(Mockito.any())).thenReturn(marketingModel);
        Mockito.when(languageService.findByLanguage(Mockito.any(), Mockito.any())).thenReturn(null);
        LuckyDrawDetailResult luckyDrawDetail = luckyDrawComponent.getLuckyDrawDetail(luckyDrawDetailDomain);
        Assert.assertEquals(marketingModel.getActivityName(), luckyDrawDetail.getActivityName());
    }
    @Test
    public void getLuckyDrawDetail1(){
        LuckyDrawDetailDomain luckyDrawDetailDomain = new LuckyDrawDetailDomain();
        luckyDrawDetailDomain.setLanguage("1");
        MarketingModel marketingModel = new MarketingModel();
        marketingModel.setActivityName("1");
        List<PrizeModel> prizeModels = new ArrayList<>();
        PrizeModel e = new PrizeModel();
        e.setPrizeNo("1");
        prizeModels.add(e);
        List<PrizeLanguageModel> prizeLanguageModels = new ArrayList<>();
        PrizeLanguageModel prizeLanguageModel = new PrizeLanguageModel();
        prizeLanguageModel.setPrizeNo("1");
        prizeLanguageModel.setLanguage("1");
        prizeLanguageModels.add(prizeLanguageModel);
        MarketingLanguageModel value = new MarketingLanguageModel();
        value.setActivityName("2");
        Mockito.when(marketingService.findByActivityCode(Mockito.any())).thenReturn(marketingModel);
        Mockito.when(languageService.findByLanguage(Mockito.any(), Mockito.any())).thenReturn(value);
        LuckyDrawDetailResult luckyDrawDetail = luckyDrawComponent.getLuckyDrawDetail(luckyDrawDetailDomain);
        Assert.assertEquals(value.getActivityName(), luckyDrawDetail.getActivityName());
    }

    @Test
    public void getLuckyDrawDetail2(){
        LuckyDrawDetailDomain luckyDrawDetailDomain = new LuckyDrawDetailDomain();
        luckyDrawDetailDomain.setLanguage("1");
        MarketingModel marketingModel = new MarketingModel();
        marketingModel.setActivityName("1");
        List<PrizeModel> prizeModels = new ArrayList<>();
        PrizeModel e = new PrizeModel();
        e.setPrizeNo("1");
        prizeModels.add(e);
        List<PrizeLanguageModel> prizeLanguageModels = new ArrayList<>();
        PrizeLanguageModel prizeLanguageModel = new PrizeLanguageModel();
        prizeLanguageModel.setPrizeNo("2");
        prizeLanguageModel.setLanguage("1");
        prizeLanguageModels.add(prizeLanguageModel);
        MarketingLanguageModel value = new MarketingLanguageModel();
        value.setActivityName("2");
        Mockito.when(marketingService.findByActivityCode(Mockito.any())).thenReturn(marketingModel);
        Mockito.when(languageService.findByLanguage(Mockito.any(), Mockito.any())).thenReturn(value);
        LuckyDrawDetailResult luckyDrawDetail = luckyDrawComponent.getLuckyDrawDetail(luckyDrawDetailDomain);
        Assert.assertEquals(value.getActivityName(), luckyDrawDetail.getActivityName());
    }


    @Test(expected = PromotionException.class)
    public void getLuckyDrawResult(){
        LuckyDrawDoDomain luckyDrawDoDomain = new LuckyDrawDoDomain();
        Mockito.when(marketingService.findByActivityCode(Mockito.any())).thenReturn(null);
        luckyDrawComponent.getLuckyDrawResult(luckyDrawDoDomain);
    }

    @Test(expected = PromotionException.class)
    public void getLuckyDrawResult0(){
        LuckyDrawDoDomain luckyDrawDoDomain = new LuckyDrawDoDomain();
        MarketingModel marketingModel = new MarketingModel();
        marketingModel.setActivityStatus(ActivityStatusEnum.CLOSURE.code());
        marketingModel.setActivityBegin(DateUtil.getCurrentDateAsString(DateUtil.FORMAT_YYYYMMDDHHMISS_14));
        marketingModel.setActivityEnd(DateUtil.getCurrentDateAsString(DateUtil.FORMAT_YYYYMMDDHHMISS_14));
        Mockito.when(marketingService.findByActivityCode(Mockito.any())).thenReturn(marketingModel);
        luckyDrawComponent.getLuckyDrawResult(luckyDrawDoDomain);
    }

    @Test(expected = Exception.class)
    public void getLuckyDrawResult1(){
        LuckyDrawDoDomain luckyDrawDoDomain = new LuckyDrawDoDomain();
        MarketingModel marketingModel = new MarketingModel();
        marketingModel.setActivityStatus(ActivityStatusEnum.EFFECTIVE.code());
        Mockito.when(marketingService.findByActivityCode(Mockito.any())).thenReturn(marketingModel);
        luckyDrawComponent.getLuckyDrawResult(luckyDrawDoDomain);
    }

    @Test(expected = Exception.class)
    public void getLuckyDrawResult2(){
        LuckyDrawDoDomain luckyDrawDoDomain = new LuckyDrawDoDomain();
        luckyDrawDoDomain.setQuality(2);
        MarketingModel marketingModel = new MarketingModel();
        marketingModel.setActivityStatus(ActivityStatusEnum.EFFECTIVE.code());
        List<TicketModel> ticketModels = new ArrayList<>();
        ticketModels.add(new TicketModel());
        Mockito.when(marketingService.findByActivityCode(Mockito.any())).thenReturn(marketingModel);
        luckyDrawComponent.getLuckyDrawResult(luckyDrawDoDomain);
    }

    @Test(expected = Exception.class)
    public void getLuckyDrawResult3(){
        LuckyDrawDoDomain luckyDrawDoDomain = new LuckyDrawDoDomain();
        luckyDrawDoDomain.setQuality(1);
        MarketingModel marketingModel = new MarketingModel();
        marketingModel.setActivityStatus(ActivityStatusEnum.EFFECTIVE.code());
        marketingModel.setActivityEnd(DateUtil.format(new Date(), DateUtil.FORMAT_YYYYMMDDHHMISS_14));
        List<TicketModel> ticketModels = new ArrayList<>();
        ticketModels.add(new TicketModel());

        List<PrizeModel> prizeModels = new ArrayList<>();
        PrizeModel prizeModel = new PrizeModel();
        prizeModel.setPrizeInventory(1);
        prizeModel.setPrizeProbability(BigDecimal.ZERO);
        prizeModels.add(prizeModel);

        List<TPromoIncentiveLimitedVO> luckyDrawLimitedListByActivityCode = new ArrayList<>();
        TPromoIncentiveLimitedVO tPromoIncentiveLimitedVO = new TPromoIncentiveLimitedVO();
        tPromoIncentiveLimitedVO.setActivityCode("1");
        luckyDrawLimitedListByActivityCode.add(tPromoIncentiveLimitedVO);

        Mockito.when(marketingService.findByActivityCode(Mockito.any())).thenReturn(marketingModel);
        LuckyDrawResult luckyDrawResult = luckyDrawComponent.getLuckyDrawResult(luckyDrawDoDomain);
        Assert.assertEquals(1, luckyDrawResult.getPrizes().size());
        Assert.assertEquals(LuckyDrawResult.NO_LUCKY, luckyDrawResult.getPrizes().get(0).getResultCode());
    }

    @Test(expected = Exception.class)
    public void getLuckyDrawResult4(){
        LuckyDrawDoDomain luckyDrawDoDomain = new LuckyDrawDoDomain();
        luckyDrawDoDomain.setQuality(1);
        MarketingModel marketingModel = new MarketingModel();
        marketingModel.setActivityStatus(ActivityStatusEnum.EFFECTIVE.code());
        List<TicketModel> ticketModels = new ArrayList<>();
        ticketModels.add(new TicketModel());

        List<PrizeModel> prizeModels = new ArrayList<>();
        PrizeModel prizeModel = new PrizeModel();
        prizeModel.setPrizeNo("1");
        prizeModel.setPrizeInventory(1);
        prizeModel.setPrizeProbability(new BigDecimal("100"));
        prizeModel.setPrizeType(PrizeTypeEnum.DEFAULT.code());
        prizeModels.add(prizeModel);
        Mockito.when(marketingService.findByActivityCode(Mockito.any())).thenReturn(marketingModel);
        LuckyDrawResult luckyDrawResult = luckyDrawComponent.getLuckyDrawResult(luckyDrawDoDomain);
        Assert.assertEquals(1, luckyDrawResult.getPrizes().size());
        Assert.assertEquals(LuckyDrawResult.NO_LUCKY, luckyDrawResult.getPrizes().get(0).getResultCode());
    }

    @Test(expected = Exception.class)
    public void getLuckyDrawResult5(){
        LuckyDrawDoDomain luckyDrawDoDomain = new LuckyDrawDoDomain();
        luckyDrawDoDomain.setQuality(1);
        MarketingModel marketingModel = new MarketingModel();
        marketingModel.setActivityStatus(ActivityStatusEnum.EFFECTIVE.code());
        List<TicketModel> ticketModels = new ArrayList<>();
        ticketModels.add(new TicketModel());

        List<PrizeModel> prizeModels = new ArrayList<>();
        PrizeModel prizeModel = new PrizeModel();
        prizeModel.setPrizeNo("1");
        prizeModel.setPrizeInventory(0);
        prizeModel.setPrizeProbability(new BigDecimal("100"));
        prizeModel.setPrizeType(PrizeTypeEnum.COUPON.code());
        prizeModels.add(prizeModel);
        Mockito.when(marketingService.findByActivityCode(Mockito.any())).thenReturn(marketingModel);
        LuckyDrawResult luckyDrawResult = luckyDrawComponent.getLuckyDrawResult(luckyDrawDoDomain);
        Assert.assertEquals(1, luckyDrawResult.getPrizes().size());
        Assert.assertEquals(LuckyDrawResult.NO_INVENTORY, luckyDrawResult.getPrizes().get(0).getResultCode());
    }

    @Test(expected = Exception.class)
    public void getLuckyDrawResult6(){
        LuckyDrawDoDomain luckyDrawDoDomain = new LuckyDrawDoDomain();
        luckyDrawDoDomain.setQuality(1);
        MarketingModel marketingModel = new MarketingModel();
        marketingModel.setActivityStatus(ActivityStatusEnum.EFFECTIVE.code());
        List<TicketModel> ticketModels = new ArrayList<>();
        ticketModels.add(new TicketModel());

        List<PrizeModel> prizeModels = new ArrayList<>();
        PrizeModel prizeModel = new PrizeModel();
        prizeModel.setPrizeNo("1");
        prizeModel.setPrizeInventory(1);
        prizeModel.setPrizeProbability(new BigDecimal("100"));
        prizeModel.setPrizeType(PrizeTypeEnum.COUPON.code());
        prizeModels.add(prizeModel);
        Mockito.when(marketingService.findByActivityCode(Mockito.any())).thenReturn(marketingModel);
        LuckyDrawResult luckyDrawResult = luckyDrawComponent.getLuckyDrawResult(luckyDrawDoDomain);
        Assert.assertEquals(1, luckyDrawResult.getPrizes().size());
        Assert.assertEquals(LuckyDrawResult.NO_INVENTORY, luckyDrawResult.getPrizes().get(0).getResultCode());
    }

    @Test(expected = Exception.class)
    public void getLuckyDrawResult7(){
        LuckyDrawDoDomain luckyDrawDoDomain = new LuckyDrawDoDomain();
        luckyDrawDoDomain.setQuality(1);
        MarketingModel marketingModel = new MarketingModel();
        marketingModel.setActivityStatus(ActivityStatusEnum.EFFECTIVE.code());
        List<TicketModel> ticketModels = new ArrayList<>();
        ticketModels.add(new TicketModel());
        ticketModels.add(new TicketModel());
        List<PrizeModel> prizeModels = new ArrayList<>();
        PrizeModel prizeModel = new PrizeModel();
        prizeModel.setPrizeNo("1");
        prizeModel.setPrizeInventory(100);
        prizeModel.setPrizeProbability(new BigDecimal("100"));
        prizeModel.setPrizeType(PrizeTypeEnum.COUPON.code());
        prizeModels.add(prizeModel);
        Mockito.when(marketingService.findByActivityCode(Mockito.any())).thenReturn(marketingModel);
        LuckyDrawResult luckyDrawResult = luckyDrawComponent.getLuckyDrawResult(luckyDrawDoDomain);
        Assert.assertEquals(1, luckyDrawResult.getPrizes().size());
        Assert.assertEquals(LuckyDrawResult.LUCKY, luckyDrawResult.getPrizes().get(0).getResultCode());
    }


    @Test
    public void getLuckyDrawResult8(){
        LuckyDrawDoDomain luckyDrawDoDomain = new LuckyDrawDoDomain();
        luckyDrawDoDomain.setQuality(1);
        MarketingModel marketingModel = new MarketingModel();
        marketingModel.setActivityStatus(ActivityStatusEnum.EFFECTIVE.code());
        marketingModel.setActivityBegin(DateUtil.getCurrentDateAsString(DateUtil.FORMAT_YYYYMMDDHHMISS_14));
        marketingModel.setActivityEnd(DateUtil.format(DateUtil.addDay(1),DateUtil.FORMAT_YYYYMMDDHHMISS_14));
        List<TicketModel> ticketModels = new ArrayList<>();
        ticketModels.add(new TicketModel());
        ticketModels.add(new TicketModel());
        List<PrizeModel> prizeModels = new ArrayList<>();
        PrizeModel prizeModel = new PrizeModel();
        prizeModel.setPrizeNo("1");
        prizeModel.setPrizeInventory(100);
        prizeModel.setPrizeProbability(new BigDecimal("100"));
        prizeModel.setPrizeType(PrizeTypeEnum.COUPON.code());
        prizeModels.add(prizeModel);
        Mockito.when(marketingService.findByActivityCode(Mockito.any())).thenReturn(marketingModel);
        Mockito.when(ticketService.selectListByMemberCode(Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(ticketModels);
        LuckyDrawResult luckyDrawResult = luckyDrawComponent.getLuckyDrawResult(luckyDrawDoDomain);
        Assert.assertEquals(1, luckyDrawResult.getPrizes().size());
        Assert.assertEquals(LuckyDrawResult.NO_INVENTORY, luckyDrawResult.getPrizes().get(0).getResultCode());
    }

    @Test
    public void getLuckyDrawResult9(){
        LuckyDrawDoDomain luckyDrawDoDomain = new LuckyDrawDoDomain();
        luckyDrawDoDomain.setQuality(1);
        MarketingModel marketingModel = new MarketingModel();
        marketingModel.setActivityStatus(ActivityStatusEnum.EFFECTIVE.code());
        marketingModel.setActivityBegin(DateUtil.getCurrentDateAsString(DateUtil.FORMAT_YYYYMMDDHHMISS_14));
        marketingModel.setActivityEnd(DateUtil.format(DateUtil.addDay(1),DateUtil.FORMAT_YYYYMMDDHHMISS_14));
        List<TicketModel> ticketModels = new ArrayList<>();
        ticketModels.add(new TicketModel());
        ticketModels.add(new TicketModel());
        List<PrizeModel> prizeModels = new ArrayList<>();
        PrizeModel prizeModel = new PrizeModel();
        prizeModel.setPrizeNo("1");

        prizeModel.setPrizeInventory(100);
        prizeModel.setPrizeProbability(new BigDecimal("100"));
        prizeModel.setPrizeType(PrizeTypeEnum.DEFAULT.code());
        prizeModels.add(prizeModel);
        Mockito.when(marketingService.findByActivityCode(Mockito.any())).thenReturn(marketingModel);
        Mockito.when(ticketService.selectListByMemberCode(Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(ticketModels);
        Mockito.when(prizeService.findListByActivityCode(Mockito.any())).thenReturn(prizeModels);
        LuckyDrawResult luckyDrawResult = luckyDrawComponent.getLuckyDrawResult(luckyDrawDoDomain);
        Assert.assertEquals(1, luckyDrawResult.getPrizes().size());
        Assert.assertEquals(LuckyDrawResult.NO_LUCKY, luckyDrawResult.getPrizes().get(0).getResultCode());
    }

    @Test
    public void getLuckyDrawResult10(){
        LuckyDrawDoDomain luckyDrawDoDomain = new LuckyDrawDoDomain();
        luckyDrawDoDomain.setQuality(1);
        MarketingModel marketingModel = new MarketingModel();
        marketingModel.setActivityStatus(ActivityStatusEnum.EFFECTIVE.code());
        marketingModel.setActivityBegin(DateUtil.getCurrentDateAsString(DateUtil.FORMAT_YYYYMMDDHHMISS_14));
        marketingModel.setActivityEnd(DateUtil.format(DateUtil.addDay(1),DateUtil.FORMAT_YYYYMMDDHHMISS_14));
        List<TicketModel> ticketModels = new ArrayList<>();
        ticketModels.add(new TicketModel());
        ticketModels.add(new TicketModel());
        List<PrizeModel> prizeModels = new ArrayList<>();
        PrizeModel prizeModel = new PrizeModel();
        prizeModel.setPrizeNo("1");

        prizeModel.setPrizeInventory(100);
        prizeModel.setPrizeProbability(new BigDecimal("100"));
        prizeModel.setPrizeType(PrizeTypeEnum.COUPON.code());
        prizeModels.add(prizeModel);
        Mockito.when(marketingService.findByActivityCode(Mockito.any())).thenReturn(marketingModel);
        Mockito.when(ticketService.selectListByMemberCode(Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(ticketModels);
        Mockito.when(prizeService.findListByActivityCode(Mockito.any())).thenReturn(prizeModels);
        LuckyDrawResult luckyDrawResult = luckyDrawComponent.getLuckyDrawResult(luckyDrawDoDomain);
        Assert.assertEquals(1, luckyDrawResult.getPrizes().size());
        Assert.assertEquals(LuckyDrawResult.NO_INVENTORY, luckyDrawResult.getPrizes().get(0).getResultCode());
    }
    @Test
    public void getLuckyDrawResult11(){
        LuckyDrawDoDomain luckyDrawDoDomain = new LuckyDrawDoDomain();
        luckyDrawDoDomain.setQuality(1);
        MarketingModel marketingModel = new MarketingModel();
        marketingModel.setActivityStatus(ActivityStatusEnum.EFFECTIVE.code());
        marketingModel.setActivityBegin(DateUtil.getCurrentDateAsString(DateUtil.FORMAT_YYYYMMDDHHMISS_14));
        marketingModel.setActivityEnd(DateUtil.format(DateUtil.addDay(1),DateUtil.FORMAT_YYYYMMDDHHMISS_14));
        List<TicketModel> ticketModels = new ArrayList<>();
        ticketModels.add(new TicketModel());
        ticketModels.add(new TicketModel());
        List<PrizeModel> prizeModels = new ArrayList<>();
        PrizeModel prizeModel = new PrizeModel();
        prizeModel.setPrizeNo("1");

        prizeModel.setPrizeInventory(100);
        prizeModel.setPrizeProbability(new BigDecimal("100"));
        prizeModel.setPrizeType(PrizeTypeEnum.COUPON.code());
        prizeModels.add(prizeModel);
        Mockito.when(marketingService.findByActivityCode(Mockito.any())).thenReturn(marketingModel);
        Mockito.when(ticketService.selectListByMemberCode(Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(ticketModels);
        Mockito.when(prizeService.findListByActivityCode(Mockito.any())).thenReturn(prizeModels);

        Mockito.when(prizeService.deductInventory(Mockito.any())).thenReturn(1);

        LuckyDrawResult luckyDrawResult = luckyDrawComponent.getLuckyDrawResult(luckyDrawDoDomain);
        Assert.assertEquals(1, luckyDrawResult.getPrizes().size());
        Assert.assertEquals(LuckyDrawResult.LUCKY, luckyDrawResult.getPrizes().get(0).getResultCode());
    }

    @Test(expected = GTechBaseException.class)
    public void getLuckyDrawResult12(){
        LuckyDrawDoDomain luckyDrawDoDomain = new LuckyDrawDoDomain();
        luckyDrawDoDomain.setQuality(1);
        MarketingModel marketingModel = new MarketingModel();
        marketingModel.setActivityStatus(ActivityStatusEnum.EFFECTIVE.code());
        marketingModel.setActivityEnd("20250917183333");
        marketingModel.setActivityBegin("20210917183333");
        List<TicketModel> ticketModels = new ArrayList<>();
        ticketModels.add(new TicketModel());
        ticketModels.add(new TicketModel());
        List<PrizeModel> prizeModels = new ArrayList<>();
        PrizeModel prizeModel = new PrizeModel();
        prizeModel.setPrizeNo("1");
        prizeModel.setPrizeInventory(100);
        prizeModel.setPrizeProbability(new BigDecimal("100"));
        prizeModel.setPrizeType(PrizeTypeEnum.COUPON.code());
        prizeModels.add(prizeModel);
        List<TPromoIncentiveLimitedVO> limitedVOS = new ArrayList<>();
        TPromoIncentiveLimitedVO tPromoIncentiveLimitedVO = new TPromoIncentiveLimitedVO();
        tPromoIncentiveLimitedVO.setLimitationCode(LimitationCodeEnum.USER_TOTAL_COUNT.code());
        limitedVOS.add(tPromoIncentiveLimitedVO);
        Mockito.when(marketingService.findByActivityCode(Mockito.any())).thenReturn(marketingModel);
        Mockito.when(ticketService.selectListByMemberCode(Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(ticketModels);
        Mockito.when(incentiveLimitedService.getLuckyDrawLimitedListByActivityCode(Mockito.any())).thenReturn(limitedVOS);

        LuckyDrawResult luckyDrawResult = luckyDrawComponent.getLuckyDrawResult(luckyDrawDoDomain);
        Assert.assertEquals(1, luckyDrawResult.getPrizes().size());
        Assert.assertEquals(LuckyDrawResult.LUCKY, luckyDrawResult.getPrizes().get(0).getResultCode());
    }

    @Test
    public void getLuckyDrawResult13(){
        LuckyDrawDoDomain luckyDrawDoDomain = new LuckyDrawDoDomain();
        luckyDrawDoDomain.setQuality(1);
        MarketingModel marketingModel = new MarketingModel();
        marketingModel.setActivityStatus(ActivityStatusEnum.EFFECTIVE.code());
        marketingModel.setActivityEnd("20250917183333");
        marketingModel.setActivityBegin("20210917183333");
        List<TicketModel> ticketModels = new ArrayList<>();
        ticketModels.add(new TicketModel());
        ticketModels.add(new TicketModel());
        List<PrizeModel> prizeModels = new ArrayList<>();
        PrizeModel prizeModel = new PrizeModel();
        prizeModel.setPrizeNo("1");
        prizeModel.setPrizeInventory(100);
        prizeModel.setPrizeProbability(new BigDecimal("100"));
        prizeModel.setPrizeType(PrizeTypeEnum.COUPON.code());
        prizeModels.add(prizeModel);
        List<TPromoIncentiveLimitedVO> limitedVOS = new ArrayList<>();
        limitedVOS.add(new TPromoIncentiveLimitedVO());
        Mockito.when(marketingService.findByActivityCode(Mockito.any())).thenReturn(marketingModel);
        Mockito.when(ticketService.selectListByMemberCode(Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(ticketModels);
        Mockito.when(incentiveLimitedService.getLuckyDrawLimitedListByActivityCode(Mockito.any())).thenReturn(limitedVOS);
        Mockito.when(incentiveLimitedHelper.lockRedisData(Mockito.anyList(), Mockito.any(), Mockito.anyLong(), Mockito.any())).thenReturn(true);
        Mockito.when(prizeService.findListByActivityCode(Mockito.any())).thenReturn(prizeModels);
        Mockito.when(prizeService.deductInventory(Mockito.any())).thenReturn(1);

        LuckyDrawResult luckyDrawResult = luckyDrawComponent.getLuckyDrawResult(luckyDrawDoDomain);
        Assert.assertEquals(1, luckyDrawResult.getPrizes().size());
        Assert.assertEquals(LuckyDrawResult.LUCKY, luckyDrawResult.getPrizes().get(0).getResultCode());
    }

    @Test
    public void queryChanceList(){
        LuckyDrawMemberChanceDomain domain = new LuckyDrawMemberChanceDomain();
        PageData<LuckyDrawMemberChanceResult> pageData = new PageData<>();
        Mockito.when(ticketService.queryChanceList(Mockito.any())).thenReturn(pageData);
        PageData<LuckyDrawMemberChanceResult> pageData1 = luckyDrawComponent.queryChanceList(domain);
        Assert.assertEquals(pageData.getTotal(), pageData1.getTotal());
    }

    @Test
    public void queryLuckyRecordList(){
        LuckyDrawMemberRecordDomain domain = new LuckyDrawMemberRecordDomain();
        PageData<TicketModel> pageData = new PageData<>();
        Mockito.when(ticketService.queryListByStatus(Mockito.any(), Mockito.any())).thenReturn(pageData);
        PageData<LuckyDrawMemberRecordResult> recordResultPageData = luckyDrawComponent.queryLuckyRecordList(domain);
        Assert.assertEquals(pageData.getTotal(), recordResultPageData.getTotal());
    }

    @Test
    public void queryLuckyRecordList1(){
        LuckyDrawMemberRecordDomain domain = new LuckyDrawMemberRecordDomain();
        List<TicketModel> list = new ArrayList<>();
        TicketModel e = new TicketModel();
        e.setStatus(TicketStatusEnum.USED.code());
        list.add(e);
        TicketModel e1 = new TicketModel();
        e1.setStatus(TicketStatusEnum.LUCKY.code());
        list.add(e1);
        PageData<TicketModel> pageData = new PageData<>(list, 1L);

        List<PrizeModel> prizeModels = new ArrayList<>();
        PrizeModel prizeModel = new PrizeModel();
        prizeModel.setPrizeNo("1");
        prizeModels.add(prizeModel);
        List<PrizeLanguageModel> prizeLanguageModels = new ArrayList<>();
        PrizeLanguageModel prizeLanguageModel = new PrizeLanguageModel();
        prizeLanguageModel.setPrizeNo("2");
        prizeLanguageModel.setLanguage("2");
        prizeLanguageModels.add(prizeLanguageModel);

        List<PrizeRecordModel> prizeRecordModels = new ArrayList<>();

        Mockito.when(ticketService.queryListByStatus(Mockito.any(), Mockito.any())).thenReturn(pageData);
        Mockito.when(prizeService.findListByActivityCode(Mockito.any())).thenReturn(prizeModels);
        Mockito.when(prizeLanguageService.findListByActivityCode(Mockito.any())).thenReturn(prizeLanguageModels);
        Mockito.when(recordService.queryLuckyRecordList(Mockito.any())).thenReturn(prizeRecordModels);
        PageData<LuckyDrawMemberRecordResult> recordResultPageData = luckyDrawComponent.queryLuckyRecordList(domain);
        Assert.assertEquals(pageData.getTotal(), recordResultPageData.getTotal());
    }

    @Test
    public void queryLuckyRecordList2(){
        LuckyDrawMemberRecordDomain domain = new LuckyDrawMemberRecordDomain();
        domain.setLanguage("2");
        List<TicketModel> list = new ArrayList<>();
        TicketModel e = new TicketModel();
        e.setStatus(TicketStatusEnum.USED.code());
        e.setTicketCode("4");
        list.add(e);
        TicketModel e1 = new TicketModel();
        e1.setStatus(TicketStatusEnum.LUCKY.code());
        e1.setTicketCode("3");
        list.add(e1);
        PageData<TicketModel> pageData = new PageData<>(list, 2L);

        List<PrizeModel> prizeModels = new ArrayList<>();
        PrizeModel prizeModel = new PrizeModel();
        prizeModel.setPrizeNo("2");
        prizeModels.add(prizeModel);
        List<PrizeLanguageModel> prizeLanguageModels = new ArrayList<>();
        PrizeLanguageModel prizeLanguageModel = new PrizeLanguageModel();
        prizeLanguageModel.setPrizeNo("2");
        prizeLanguageModel.setLanguage("2");
        prizeLanguageModels.add(prizeLanguageModel);

        PrizeLanguageModel prizeLanguageModel1 = new PrizeLanguageModel();
        prizeLanguageModel1.setPrizeNo("2");
        prizeLanguageModel1.setLanguage("3");
        prizeLanguageModels.add(prizeLanguageModel1);

        List<PrizeRecordModel> prizeRecordModels = new ArrayList<>();
        PrizeRecordModel prizeRecordModel = new PrizeRecordModel();
        prizeRecordModel.setTicketCode("3");
        prizeRecordModel.setPrizeNo("2");
        prizeRecordModels.add(prizeRecordModel);

        Mockito.when(ticketService.queryListByStatus(Mockito.any(), Mockito.any())).thenReturn(pageData);
        Mockito.when(prizeService.findListByActivityCode(Mockito.any())).thenReturn(prizeModels);
        Mockito.when(prizeLanguageService.findListByActivityCode(Mockito.any())).thenReturn(prizeLanguageModels);
        Mockito.when(recordService.queryLuckyRecordList(Mockito.any())).thenReturn(prizeRecordModels);
        PageData<LuckyDrawMemberRecordResult> recordResultPageData = luckyDrawComponent.queryLuckyRecordList(domain);
        Assert.assertEquals(pageData.getTotal(), recordResultPageData.getTotal());
    }

    @Test
    public void judgeQualification_empty(){
        JudgeQualificationDomain domain = new JudgeQualificationDomain();
        Mockito.when(luckyDrawRuleService.queryLuckyDrawRulesByProductCode(Mockito.any(), Mockito.any())).thenReturn(new ArrayList<>());
        List<ActivityParticipateInResult> activityParticipateInResults = luckyDrawComponent.judgeQualification(domain);
        Assert.assertEquals(0, activityParticipateInResults.size());
    }

    @Test
    public void judgeQualification_empty2(){
        JudgeQualificationDomain domain = new JudgeQualificationDomain();

        List<LuckyDrawRuleModel> luckyDrawRuleModels = new ArrayList<>();
        LuckyDrawRuleModel luckyDrawRuleModel = new LuckyDrawRuleModel();
        luckyDrawRuleModel.setActivityCode("1");
        luckyDrawRuleModels.add(luckyDrawRuleModel);
        List<MarketingModel> marketingModels = new ArrayList<>();
        Mockito.when(luckyDrawRuleService.queryLuckyDrawRulesByProductCode(Mockito.any(), Mockito.any())).thenReturn(luckyDrawRuleModels);
        Mockito.when(marketingService.getMarketingByActivityCode(Mockito.any(), Mockito.anyList(),Mockito.any())).thenReturn(marketingModels);
        List<ActivityParticipateInResult> activityParticipateInResults = luckyDrawComponent.judgeQualification(domain);
        Assert.assertEquals(0, activityParticipateInResults.size());
    }


    @Test
    public void judgeQualification(){
        JudgeQualificationDomain domain = new JudgeQualificationDomain();
        domain.setFrozenStatus("01");
        domain.setAmount(new BigDecimal(1));
        List<LuckyDrawRuleModel> luckyDrawRuleModels = new ArrayList<>();
        LuckyDrawRuleModel luckyDrawRuleModel = new LuckyDrawRuleModel();
        luckyDrawRuleModel.setActivityCode("1");
        luckyDrawRuleModel.setEarnTicket(1);
        luckyDrawRuleModel.setMaxGivingPerUser(1);
        luckyDrawRuleModel.setBuyingQty(new BigDecimal(1));
        luckyDrawRuleModel.setBuyingQty(new BigDecimal(1));
        luckyDrawRuleModels.add(luckyDrawRuleModel);
        List<MarketingModel> marketingModels = new ArrayList<>();
        MarketingModel marketingModel = new MarketingModel();
        marketingModel.setActivityCode("1");
        marketingModel.setActivityEnd("21000823152502");
        marketingModels.add(marketingModel);
        List<TicketSendResult> ticketSendResults = new ArrayList<>();
        TicketSendResult ticketSendResult = new TicketSendResult();
        List<String> ticketCodes = new ArrayList<>();
        ticketCodes.add("1");
        ticketSendResult.setTicketCodes(ticketCodes);
        ticketSendResults.add(ticketSendResult);
        List<QualificationModel> qualificationModels = new ArrayList<>();
        Mockito.when(luckyDrawRuleService.queryLuckyDrawRulesByProductCode(Mockito.any(), Mockito.any())).thenReturn(luckyDrawRuleModels);
        Mockito.when(marketingService.getMarketingByActivityCode(Mockito.any(), Mockito.anyList(),Mockito.any())).thenReturn(marketingModels);
        Mockito.when(qualificationService.queryLuckyDrawQualificationsByActivityCodes(Mockito.any(),Mockito.any())).thenReturn(qualificationModels);
        Mockito.when(incentiveLimitedHelper.lockRedisDataSpu(Mockito.anyList(),Mockito.any(),Mockito.anyLong(),Mockito.any())).thenReturn(1L);
        Mockito.when(ticketComponent.sendTicket(Mockito.any())).thenReturn(ticketSendResults);
        List<ActivityParticipateInResult> activityParticipateInResults = luckyDrawComponent.judgeQualification(domain);
        Assert.assertEquals(1, activityParticipateInResults.size());
    }

    @Test
    public void judgeQualification_exception(){
        JudgeQualificationDomain domain = new JudgeQualificationDomain();
        domain.setFrozenStatus("01");
        domain.setAmount(new BigDecimal(1));
        List<LuckyDrawRuleModel> luckyDrawRuleModels = new ArrayList<>();
        LuckyDrawRuleModel luckyDrawRuleModel = new LuckyDrawRuleModel();
        luckyDrawRuleModel.setActivityCode("1");
        luckyDrawRuleModel.setEarnTicket(1);
        luckyDrawRuleModel.setMaxGivingPerUser(1);
        luckyDrawRuleModel.setBuyingQty(new BigDecimal(1));
        luckyDrawRuleModel.setBuyingQty(new BigDecimal(1));
        luckyDrawRuleModels.add(luckyDrawRuleModel);
        List<MarketingModel> marketingModels = new ArrayList<>();
        MarketingModel marketingModel = new MarketingModel();
        marketingModel.setActivityCode("1");
        marketingModel.setActivityEnd("21000823152502");
        marketingModels.add(marketingModel);
        List<TicketSendResult> ticketSendResults = new ArrayList<>();
        TicketSendResult ticketSendResult = new TicketSendResult();
        ticketSendResults.add(ticketSendResult);
        List<QualificationModel> qualificationModels = new ArrayList<>();
        Mockito.when(luckyDrawRuleService.queryLuckyDrawRulesByProductCode(Mockito.any(), Mockito.any())).thenReturn(luckyDrawRuleModels);
        Mockito.when(marketingService.getMarketingByActivityCode(Mockito.any(), Mockito.anyList(),Mockito.any())).thenReturn(marketingModels);
        Mockito.when(qualificationService.queryLuckyDrawQualificationsByActivityCodes(Mockito.any(),Mockito.any())).thenReturn(qualificationModels);
        Mockito.when(incentiveLimitedHelper.lockRedisDataSpu(Mockito.anyList(),Mockito.any(),Mockito.anyLong(),Mockito.any())).thenReturn(1L);
        Mockito.when(ticketComponent.sendTicket(Mockito.any())).thenReturn(ticketSendResults);
        Mockito.doNothing().when(incentiveLimitedHelper).rollBackRedisData(Mockito.anyList());
        List<ActivityParticipateInResult> activityParticipateInResults = luckyDrawComponent.judgeQualification(domain);
        Assert.assertEquals(1, activityParticipateInResults.size());
    }

    @Test
    public void eligibleActivities(){
        JudgeQualificationDomain domain = new JudgeQualificationDomain();
        luckyDrawComponent.eligibleActivities(domain);
    }

    @Test
    public void eligibleActivities1(){
        JudgeQualificationDomain domain = new JudgeQualificationDomain();
        List<LuckyDrawRuleModel> luckyDrawRuleModels = new ArrayList<>();
        LuckyDrawRuleModel model = new LuckyDrawRuleModel();
        model.setActivityCode("1");
        luckyDrawRuleModels.add(model);
        Mockito.when(luckyDrawRuleService.queryLuckyDrawRulesByProductCode(Mockito.any(),Mockito.any())).thenReturn(luckyDrawRuleModels);
        luckyDrawComponent.eligibleActivities(domain);
    }

    @Test
    public void eligibleActivities2(){
        JudgeQualificationDomain domain = new JudgeQualificationDomain();
        domain.setAmount(BigDecimal.valueOf(2));
        List<LuckyDrawRuleModel> luckyDrawRuleModels = new ArrayList<>();
        LuckyDrawRuleModel model = new LuckyDrawRuleModel();
        model.setActivityCode("1");
        model.setBuyingQty(BigDecimal.valueOf(2));
        model.setMaxGivingPerUser(1);
        model.setEarnTicket(1);
        luckyDrawRuleModels.add(model);
        List<MarketingModel> marketing = new ArrayList<>();
        MarketingModel marketingModel = new MarketingModel();
        marketingModel.setActivityCode("1");
        marketing.add(marketingModel);
        Mockito.when(luckyDrawRuleService.queryLuckyDrawRulesByProductCode(Mockito.any(),Mockito.any())).thenReturn(luckyDrawRuleModels);
        Mockito.when(marketingService.getMarketingByActivityCode(Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(marketing);
        luckyDrawComponent.eligibleActivities(domain);
    }

    @Test
    public void eligibleActivities3(){
        JudgeQualificationDomain domain = new JudgeQualificationDomain();
        domain.setAmount(BigDecimal.valueOf(2));
        List<LuckyDrawRuleModel> luckyDrawRuleModels = new ArrayList<>();
        LuckyDrawRuleModel model = new LuckyDrawRuleModel();
        model.setActivityCode("1");
        model.setBuyingQty(BigDecimal.valueOf(3));
        model.setMaxGivingPerUser(1);
        model.setEarnTicket(1);
        luckyDrawRuleModels.add(model);
        List<MarketingModel> marketing = new ArrayList<>();
        MarketingModel marketingModel = new MarketingModel();
        marketingModel.setActivityCode("1");
        marketing.add(marketingModel);
        Mockito.when(luckyDrawRuleService.queryLuckyDrawRulesByProductCode(Mockito.any(),Mockito.any())).thenReturn(luckyDrawRuleModels);
        Mockito.when(marketingService.getMarketingByActivityCode(Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(marketing);
        luckyDrawComponent.eligibleActivities(domain);
    }

    @Test
    public void queryMemberLuckyDrawFrequency(){
        QueryMemberLuckyDrawFrequencyDomain domain = new QueryMemberLuckyDrawFrequencyDomain();
        luckyDrawComponent.queryMemberLuckyDrawFrequency(domain);
    }

    @Test
    public void queryMemberLuckyDrawFrequency1(){
        QueryMemberLuckyDrawFrequencyDomain domain = new QueryMemberLuckyDrawFrequencyDomain();
        List<TPromoIncentiveLimitedVO> luckyDrawLimitedList = new ArrayList<>();
        TPromoIncentiveLimitedVO vo = new TPromoIncentiveLimitedVO();
        vo.setLimitationCode("13");
        luckyDrawLimitedList.add(vo);
        Mockito.when(incentiveLimitedService.getLuckyDrawLimitedListByActivityCode(Mockito.any())).thenReturn(luckyDrawLimitedList);
        luckyDrawComponent.queryMemberLuckyDrawFrequency(domain);
    }

    @Test
    public void queryMemberLuckyDrawFrequency2(){
        QueryMemberLuckyDrawFrequencyDomain domain = new QueryMemberLuckyDrawFrequencyDomain();
        List<TPromoIncentiveLimitedVO> luckyDrawLimitedList = new ArrayList<>();
        TPromoIncentiveLimitedVO vo = new TPromoIncentiveLimitedVO();
        vo.setLimitationCode("16");
        luckyDrawLimitedList.add(vo);
        Mockito.when(incentiveLimitedService.getLuckyDrawLimitedListByActivityCode(Mockito.any())).thenReturn(luckyDrawLimitedList);
        luckyDrawComponent.queryMemberLuckyDrawFrequency(domain);
    }

    @Test
    public void queryMemberLuckyDrawFrequency3(){
        QueryMemberLuckyDrawFrequencyDomain domain = new QueryMemberLuckyDrawFrequencyDomain();
        List<TPromoIncentiveLimitedVO> luckyDrawLimitedList = new ArrayList<>();
        TPromoIncentiveLimitedVO vo = new TPromoIncentiveLimitedVO();
        vo.setLimitationCode("17");
        luckyDrawLimitedList.add(vo);
        Mockito.when(incentiveLimitedService.getLuckyDrawLimitedListByActivityCode(Mockito.any())).thenReturn(luckyDrawLimitedList);
        luckyDrawComponent.queryMemberLuckyDrawFrequency(domain);
    }

    @Test
    public void queryMemberLuckyDrawFrequency4(){
        QueryMemberLuckyDrawFrequencyDomain domain = new QueryMemberLuckyDrawFrequencyDomain();
        List<TPromoIncentiveLimitedVO> luckyDrawLimitedList = new ArrayList<>();
        TPromoIncentiveLimitedVO vo = new TPromoIncentiveLimitedVO();
        vo.setLimitationCode("11");
        luckyDrawLimitedList.add(vo);
        Mockito.when(incentiveLimitedService.getLuckyDrawLimitedListByActivityCode(Mockito.any())).thenReturn(luckyDrawLimitedList);
        luckyDrawComponent.queryMemberLuckyDrawFrequency(domain);
    }

    @Test
    public void queryMemberLuckyDrawFrequency5(){
        QueryMemberLuckyDrawFrequencyDomain domain = new QueryMemberLuckyDrawFrequencyDomain();
        List<TPromoIncentiveLimitedVO> luckyDrawLimitedList = new ArrayList<>();
        TPromoIncentiveLimitedVO vo = new TPromoIncentiveLimitedVO();
        vo.setLimitationCode("01");
        vo.setLimitationValue(BigDecimal.valueOf(10));
        luckyDrawLimitedList.add(vo);
        Mockito.when(incentiveLimitedService.getLuckyDrawLimitedListByActivityCode(Mockito.any())).thenReturn(luckyDrawLimitedList);
        luckyDrawComponent.queryMemberLuckyDrawFrequency(domain);
    }

    @Test
    public void updateFrozenStatus(){
        luckyDrawComponent.updateFrozenStatus(new LuckyDrawMemberChanceDomain());
    }


    @Test
    public void testExportLuckyDraw() {
        ExportLuckyDrawDto dto = new ExportLuckyDrawDto();
        dto.setPageNum(1);
        dto.setPageSize(10);
        dto.setTenantCode("tenantCode");
        dto.setDomainCode("domainCode");
        dto.setLanguage("language");

        QueryRecordByTicketsModel model = new QueryRecordByTicketsModel();


        List<PrizeRecordModel> prizeRecordModels = new ArrayList<>();
        PrizeRecordModel prizeRecordModel = new PrizeRecordModel();
        prizeRecordModel.setMemberCode("memberCode");
        prizeRecordModel.setPrizeNo("prizeNo");
        prizeRecordModels.add(prizeRecordModel);

        TicketModel ticketModel = new TicketModel();
        ticketModel.setMemberCode("memberCode");
        ticketModel.setStatus("status");
        ticketModel.setUseTime("20231226173026");

        PageData<TicketModel> ticketModelPageData = new PageData<>();
        ticketModelPageData.setList(Collections.singletonList(ticketModel));

        QueryMemberParam queryMemberParam = new QueryMemberParam();
        queryMemberParam.setTenantCode(dto.getTenantCode());
        queryMemberParam.setMemberCodes(prizeRecordModels.stream().map(PrizeRecordModel::getMemberCode).collect(Collectors.toList()));
        queryMemberParam.setDomainCode(dto.getDomainCode());

        Result<List<QueryMemberListByConditionResult>> listResult = new Result<>();
        listResult.setCode("0");
        List<QueryMemberListByConditionResult> memberListByConditionResults = new ArrayList<>();
        QueryMemberListByConditionResult memberListByConditionResult = new QueryMemberListByConditionResult();
        memberListByConditionResult.setMemberCode("memberCode");
        memberListByConditionResult.setFirstName("firstName");
        memberListByConditionResult.setLastName("lastName");
        memberListByConditionResult.setMobile("mobile");
        memberListByConditionResults.add(memberListByConditionResult);
        listResult.setData(memberListByConditionResults);

        Result<List<QueryUserResult>> result = new Result<>();

        List<QueryUserResult> resultList = new ArrayList<>();

        QueryUserResult userResult = new QueryUserResult();

        resultList.add(userResult);

        result.setData(resultList);

        Mockito.when(idmFeignClient.queryUserList(Mockito.any())).thenReturn(result);
        Mockito.when(recordService.queryLuckyRecordList(Mockito.any())).thenReturn(prizeRecordModels);
        Mockito.when(ticketService.queryListByStatus(Mockito.any(), Mockito.any())).thenReturn(ticketModelPageData);
        Mockito.when(memberFeignClient.queryMemberListByMemberCode(Mockito.any())).thenReturn(listResult);

        List<ExportLuckyDrawResult> results = luckyDrawComponent.exportLuckyDraw(dto);

        Assertions.assertEquals(1, results.size());

    }



    @Test
    public void test_exportLuckyDrawTotal_validDto() {
        // Arrange
        ExportLuckyDrawDto exportLuckyDrawDto = new ExportLuckyDrawDto();
        exportLuckyDrawDto.setActivityCode("ACTIVITY_CODE");
        exportLuckyDrawDto.setLanguage("en_US");

        PrizeModel prizeModel = new PrizeModel();
        prizeModel.setPrizeType(PrizeTypeEnum.PRODUCT.code());
        prizeModel.setPrizeName("Product Prize");
        prizeModel.setPrizeCode("PRIZE_CODE");
        prizeModel.setPrizeNum(10);
        prizeModel.setPrizeInventory(5);
        prizeModel.setPrizeQuota(5);

        List<PrizeModel> prizeModels = Collections.singletonList(prizeModel);

        Mockito.when(prizeService.findListByActivityCode(Mockito.anyString())).thenReturn(prizeModels);


        // Act
        List<ExportLuckyDrawResultTotal> result = luckyDrawComponent.exportLuckyDrawTotal(exportLuckyDrawDto);

    }



}
