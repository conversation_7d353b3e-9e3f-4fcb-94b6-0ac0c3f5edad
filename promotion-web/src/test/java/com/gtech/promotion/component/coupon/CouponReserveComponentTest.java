package com.gtech.promotion.component.coupon;

import com.gtech.commons.exception.GTechBaseException;
import com.gtech.promotion.code.activity.ActivityTagCodeEnum;
import com.gtech.promotion.code.activity.CouponReverseStatusEnum;
import com.gtech.promotion.code.activity.GiveawayTypeEnum;
import com.gtech.promotion.dao.entity.activity.TPromoActivityIncentiveEntity;
import com.gtech.promotion.dao.model.activity.ActivityModel;
import com.gtech.promotion.dao.model.activity.GiveawayVO;
import com.gtech.promotion.dao.model.activity.TPromoOrderVO;
import com.gtech.promotion.domain.coupon.CouponReleaseDomain;
import com.gtech.promotion.dto.in.coupon.ReserveCouponDto;
import com.gtech.promotion.dto.in.coupon.ReserveCouponQuotaDto;
import com.gtech.promotion.exception.PromotionException;
import com.gtech.promotion.service.impl.activity.GiveawayServiceImpl;
import com.gtech.promotion.service.impl.activity.TPromoActivityIncentiveServiceImpl;
import com.gtech.promotion.service.impl.activity.TPromoOrderServiceImpl;
import com.gtech.promotion.service.impl.coupon.CouponReserveServiceImpl;
import com.gtech.promotion.service.impl.coupon.PromoCouponActivityServiceImpl;
import com.gtech.promotion.service.impl.coupon.PromoCouponReleaseServiceImpl;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.List;

@RunWith(MockitoJUnitRunner.class)
public class CouponReserveComponentTest{

    @InjectMocks
    private CouponReserveComponent couponReserveComponent;

    @Mock
    private TPromoOrderServiceImpl orderService;

    @Mock
    private TPromoActivityIncentiveServiceImpl incentiveService;

    @Mock
    private GiveawayServiceImpl giveawayService;

    @Mock
    private PromoCouponReleaseServiceImpl couponReleaseService;

    @Mock
    private PromoCouponActivityServiceImpl couponActivityService;

    @Mock
    private CouponReserveServiceImpl couponReserveService;

    @Mock
    private CouponInnerCodeComponent couponInnerCodeDomain;

    @Mock
    private CouponActivityComponent couponActivityDomain;

    @Test(expected = PromotionException.class)
    public void testReserveCouponQuota() {
        ReserveCouponQuotaDto reserveCouponQuotaDto = new ReserveCouponQuotaDto();
        Mockito.when(orderService.queryOrderBySalesOrderNo(Mockito.any(), Mockito.any())).thenReturn(null);
        couponReserveComponent.reserveCouponQuota(reserveCouponQuotaDto);
    }

    @Test
    public void testReserveCouponQuota1() {
        ReserveCouponQuotaDto reserveCouponQuotaDto = new ReserveCouponQuotaDto();
        TPromoOrderVO tPromoOrderVO = new TPromoOrderVO();
        List<TPromoActivityIncentiveEntity> listByOrderId = new ArrayList<>();
        TPromoActivityIncentiveEntity entity = new TPromoActivityIncentiveEntity();
        entity.setIncentiveType("0");
        listByOrderId.add(entity);
        Mockito.when(orderService.queryOrderBySalesOrderNo(Mockito.any(), Mockito.any())).thenReturn(tPromoOrderVO);
        Mockito.when(incentiveService.getListByOrderId(Mockito.any(), Mockito.any())).thenReturn(listByOrderId);
        couponReserveComponent.reserveCouponQuota(reserveCouponQuotaDto);
    }

    @Test
    public void testReserveCouponQuota2() {
        ReserveCouponQuotaDto reserveCouponQuotaDto = new ReserveCouponQuotaDto();
        TPromoOrderVO tPromoOrderVO = new TPromoOrderVO();
        List<TPromoActivityIncentiveEntity> listByOrderId = new ArrayList<>();
        TPromoActivityIncentiveEntity entity = new TPromoActivityIncentiveEntity();
        entity.setIncentiveType(ActivityTagCodeEnum.GIVEAWAY.code());
        listByOrderId.add(entity);
        List<GiveawayVO> giveawayVOS = new ArrayList<>();
        GiveawayVO giveawayVO = new GiveawayVO();
        giveawayVO.setGiveawayType(Integer.parseInt(GiveawayTypeEnum.PRODUCT.code()));
        giveawayVOS.add(giveawayVO);
        Mockito.when(orderService.queryOrderBySalesOrderNo(Mockito.any(), Mockito.any())).thenReturn(tPromoOrderVO);
        Mockito.when(incentiveService.getListByOrderId(Mockito.any(), Mockito.any())).thenReturn(listByOrderId);
        Mockito.when(giveawayService.getGiftListByActivityCode(Mockito.any(), Mockito.any())).thenReturn(giveawayVOS);
        couponReserveComponent.reserveCouponQuota(reserveCouponQuotaDto);
    }

    @Test
    public void testReserveCouponQuota3() {
        ReserveCouponQuotaDto reserveCouponQuotaDto = new ReserveCouponQuotaDto();
        TPromoOrderVO tPromoOrderVO = new TPromoOrderVO();
        List<TPromoActivityIncentiveEntity> listByOrderId = new ArrayList<>();
        TPromoActivityIncentiveEntity entity = new TPromoActivityIncentiveEntity();
        entity.setIncentiveType(ActivityTagCodeEnum.GIVEAWAY.code());
        entity.setIncentiveTimes(1);
        listByOrderId.add(entity);
        List<GiveawayVO> giveawayVOS = new ArrayList<>();
        GiveawayVO giveawayVO = new GiveawayVO();
        giveawayVO.setGiveawayType(Integer.parseInt(GiveawayTypeEnum.COUPON.code()));
        giveawayVO.setGiveawayNum(0);
        giveawayVOS.add(giveawayVO);
        Mockito.when(orderService.queryOrderBySalesOrderNo(Mockito.any(), Mockito.any())).thenReturn(tPromoOrderVO);
        Mockito.when(incentiveService.getListByOrderId(Mockito.any(), Mockito.any())).thenReturn(listByOrderId);
        Mockito.when(giveawayService.getGiftListByActivityCode(Mockito.any(), Mockito.any())).thenReturn(giveawayVOS);
        couponReserveComponent.reserveCouponQuota(reserveCouponQuotaDto);
    }

    @Test(expected = GTechBaseException.class)
    public void testReserveCouponQuota4() {
        ReserveCouponQuotaDto reserveCouponQuotaDto = new ReserveCouponQuotaDto();
        TPromoOrderVO tPromoOrderVO = new TPromoOrderVO();
        List<TPromoActivityIncentiveEntity> listByOrderId = new ArrayList<>();
        TPromoActivityIncentiveEntity entity = new TPromoActivityIncentiveEntity();
        entity.setIncentiveType(ActivityTagCodeEnum.GIVEAWAY.code());
        entity.setIncentiveTimes(1);
        listByOrderId.add(entity);
        List<GiveawayVO> giveawayVOS = new ArrayList<>();
        GiveawayVO giveawayVO = new GiveawayVO();
        giveawayVO.setGiveawayType(Integer.parseInt(GiveawayTypeEnum.COUPON.code()));
        giveawayVO.setGiveawayNum(1);
        giveawayVOS.add(giveawayVO);
        ActivityModel activityModel = new ActivityModel();
        activityModel.setReserveInventory(1);
        List<CouponReleaseDomain> releaseDomains = new ArrayList<>();
        CouponReleaseDomain couponReleaseDomain = new CouponReleaseDomain();
        couponReleaseDomain.setInventory(0);
        releaseDomains.add(couponReleaseDomain);

        Mockito.when(orderService.queryOrderBySalesOrderNo(Mockito.any(), Mockito.any())).thenReturn(tPromoOrderVO);
        Mockito.when(incentiveService.getListByOrderId(Mockito.any(), Mockito.any())).thenReturn(listByOrderId);
        Mockito.when(giveawayService.getGiftListByActivityCode(Mockito.any(), Mockito.any())).thenReturn(giveawayVOS);
        Mockito.when(couponActivityService.findCouponActivity(Mockito.any(), Mockito.any())).thenReturn(activityModel);
        Mockito.when(couponReleaseService.queryCanReceiveReleases(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(releaseDomains);
        couponReserveComponent.reserveCouponQuota(reserveCouponQuotaDto);
    }

    @Test
    public void testReserveCouponQuota5() {
        ReserveCouponQuotaDto reserveCouponQuotaDto = new ReserveCouponQuotaDto();
        TPromoOrderVO tPromoOrderVO = new TPromoOrderVO();
        List<TPromoActivityIncentiveEntity> listByOrderId = new ArrayList<>();
        TPromoActivityIncentiveEntity entity = new TPromoActivityIncentiveEntity();
        entity.setIncentiveType(ActivityTagCodeEnum.GIVEAWAY.code());
        entity.setIncentiveTimes(1);
        listByOrderId.add(entity);
        List<GiveawayVO> giveawayVOS = new ArrayList<>();
        GiveawayVO giveawayVO = new GiveawayVO();
        giveawayVO.setGiveawayType(Integer.parseInt(GiveawayTypeEnum.COUPON.code()));
        giveawayVO.setGiveawayNum(1);
        giveawayVOS.add(giveawayVO);
        ActivityModel activityModel = new ActivityModel();
        activityModel.setReserveInventory(1);
        List<CouponReleaseDomain> releaseDomains = new ArrayList<>();
        CouponReleaseDomain couponReleaseDomain = new CouponReleaseDomain();
        couponReleaseDomain.setInventory(100);
        releaseDomains.add(couponReleaseDomain);

        Mockito.when(orderService.queryOrderBySalesOrderNo(Mockito.any(), Mockito.any())).thenReturn(tPromoOrderVO);
        Mockito.when(incentiveService.getListByOrderId(Mockito.any(), Mockito.any())).thenReturn(listByOrderId);
        Mockito.when(giveawayService.getGiftListByActivityCode(Mockito.any(), Mockito.any())).thenReturn(giveawayVOS);
        Mockito.when(couponActivityService.findCouponActivity(Mockito.any(), Mockito.any())).thenReturn(activityModel);
        Mockito.when(couponReleaseService.queryCanReceiveReleases(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(releaseDomains);
        Mockito.when(couponReserveService.addReserve(Mockito.any())).thenReturn(1);
        Mockito.when(couponActivityService.reserveCouponQuota(Mockito.any(), Mockito.any(), Mockito.anyInt())).thenReturn(1);
        couponReserveComponent.reserveCouponQuota(reserveCouponQuotaDto);
    }

    @Test
    public void testReturnCouponQuota() {
        ReserveCouponQuotaDto reserveCouponQuotaDto = new ReserveCouponQuotaDto();
        Mockito.when(couponReserveService.getReserve(Mockito.any())).thenReturn(null);
        couponReserveComponent.returnCouponQuota(reserveCouponQuotaDto);
    }

    @Test
    public void testReturnCouponQuota1() {
        ReserveCouponQuotaDto reserveCouponQuotaDto = new ReserveCouponQuotaDto();
        ReserveCouponDto reserve = new ReserveCouponDto();
        reserve.setStatus(Integer.valueOf(CouponReverseStatusEnum.RETURN.code()));
        Mockito.when(couponReserveService.getReserve(Mockito.any())).thenReturn(reserve);
        couponReserveComponent.returnCouponQuota(reserveCouponQuotaDto);
    }

    @Test
    public void testReturnCouponQuota2() {
        ReserveCouponQuotaDto reserveCouponQuotaDto = new ReserveCouponQuotaDto();
        ReserveCouponDto reserve = new ReserveCouponDto();
        reserve.setQuota(1);
        reserve.setStatus(Integer.valueOf(CouponReverseStatusEnum.REVERSE.code()));
        Mockito.when(couponReserveService.getReserve(Mockito.any())).thenReturn(reserve);
        Mockito.when(couponReserveService.updateReserve((Mockito.any()))).thenReturn(1);
        Mockito.when(couponActivityService.returnCouponQuota(Mockito.any(), Mockito.any(), Mockito.anyInt())).thenReturn(1);
        couponReserveComponent.returnCouponQuota(reserveCouponQuotaDto);
    }

    @Test
    public void testReleaseCouponQuota() {
        ReserveCouponQuotaDto reserveCouponQuotaDto = new ReserveCouponQuotaDto();
        Mockito.when(couponReserveService.getReserve(Mockito.any())).thenReturn(null);
        couponReserveComponent.releaseCouponQuota(reserveCouponQuotaDto);
    }

    @Test
    public void testReleaseCouponQuota1() {
        ReserveCouponQuotaDto reserveCouponQuotaDto = new ReserveCouponQuotaDto();
        ReserveCouponDto reserve = new ReserveCouponDto();
        reserve.setStatus(Integer.valueOf(CouponReverseStatusEnum.RETURN.code()));
        Mockito.when(couponReserveService.getReserve(Mockito.any())).thenReturn(reserve);
        couponReserveComponent.releaseCouponQuota(reserveCouponQuotaDto);
    }

    @Test(expected = GTechBaseException.class)
    public void testReleaseCouponQuota2() {
        ReserveCouponQuotaDto reserveCouponQuotaDto = new ReserveCouponQuotaDto();
        ReserveCouponDto reserve = new ReserveCouponDto();
        reserve.setQuota(1);
        reserve.setStatus(Integer.valueOf(CouponReverseStatusEnum.REVERSE.code()));
        Mockito.when(couponReserveService.getReserve(Mockito.any())).thenReturn(reserve);
        Mockito.when(couponActivityService.returnCouponQuota(Mockito.any(), Mockito.any(), Mockito.anyInt())).thenReturn(1);
        Mockito.when(couponReserveService.updateReserve(Mockito.any())).thenReturn(1);
        Mockito.when(couponActivityDomain.findValidActivity(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(null);
        couponReserveComponent.releaseCouponQuota(reserveCouponQuotaDto);
    }

    @Test(expected = GTechBaseException.class)
    public void testReleaseCouponQuota3() {
        ReserveCouponQuotaDto reserveCouponQuotaDto = new ReserveCouponQuotaDto();
        ReserveCouponDto reserve = new ReserveCouponDto();
        reserve.setQuota(1);
        reserve.setStatus(Integer.valueOf(CouponReverseStatusEnum.REVERSE.code()));
        ActivityModel activityModel = new ActivityModel();

        Mockito.when(couponReserveService.getReserve(Mockito.any())).thenReturn(reserve);
        Mockito.when(couponActivityService.returnCouponQuota(Mockito.any(), Mockito.any(), Mockito.anyInt())).thenReturn(1);
        Mockito.when(couponReserveService.updateReserve(Mockito.any())).thenReturn(1);
        Mockito.when(couponActivityDomain.findValidActivity(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(activityModel);
        Mockito.when(couponReleaseService.queryCanReceiveReleases(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(null);
        couponReserveComponent.releaseCouponQuota(reserveCouponQuotaDto);
    }

    @Test
    public void testReleaseCouponQuota4() {
        ReserveCouponQuotaDto reserveCouponQuotaDto = new ReserveCouponQuotaDto();
        ReserveCouponDto reserve = new ReserveCouponDto();
        reserve.setQuota(1);
        reserve.setStatus(Integer.valueOf(CouponReverseStatusEnum.REVERSE.code()));
        ActivityModel activityModel = new ActivityModel();
        List<CouponReleaseDomain> couponRelease = new ArrayList<>();
        CouponReleaseDomain releaseDomain = new CouponReleaseDomain();
        couponRelease.add(releaseDomain);

        Mockito.when(couponReserveService.getReserve(Mockito.any())).thenReturn(reserve);
        Mockito.when(couponActivityService.returnCouponQuota(Mockito.any(), Mockito.any(), Mockito.anyInt())).thenReturn(1);
        Mockito.when(couponReserveService.updateReserve(Mockito.any())).thenReturn(1);
        Mockito.when(couponActivityDomain.findValidActivity(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(activityModel);
        Mockito.when(couponReleaseService.queryCanReceiveReleases(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(couponRelease);
        Mockito.when(couponInnerCodeDomain.allocateCoupon(Mockito.any(), Mockito.anyInt(), Mockito.any(), Mockito.any(), Mockito.any(),Mockito.anyInt(), Mockito.any())).thenReturn(null);
        couponReserveComponent.releaseCouponQuota(reserveCouponQuotaDto);
    }
}