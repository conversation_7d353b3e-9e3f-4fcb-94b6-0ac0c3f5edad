package com.gtech.promotion.component.coupon;

import com.google.common.collect.Lists;
import com.gtech.promotion.dao.model.activity.ActivityLanguageModel;
import com.gtech.promotion.dao.model.activity.ActivityModel;
import com.gtech.promotion.dao.model.coupon.TPromoCouponCodeUserVO;
import com.gtech.promotion.dao.model.coupon.TPromoCouponInnerCodeVO;
import com.gtech.promotion.domain.coupon.CouponReleaseDomain;
import com.gtech.promotion.dto.in.coupon.TCouponLockDTO;
import com.gtech.promotion.dto.in.coupon.TCouponUnLockDTO;
import com.gtech.promotion.helper.ActivityRedisHelpler;
import com.gtech.promotion.service.activity.ActivityService;
import com.gtech.promotion.service.coupon.PromoCouponActivityService;
import com.gtech.promotion.service.coupon.PromoCouponCodeUserService;
import com.gtech.promotion.service.coupon.PromoCouponInnerCodeService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021/9/18 10:27
 */
@RunWith(MockitoJUnitRunner.class)
public class LockCouponComponentTest {

    @InjectMocks
    private LockCouponComponent lockCouponComponent;

    @Mock
    private ActivityService activityService;

    @Mock
    private PromoCouponInnerCodeService couponInnerCodeService;

    @Mock
    private CouponInnerCodeComponent couponInnerCodeDomain;

    @Mock
    private PromoCouponCodeUserService couponCodeUserService;

    @Mock
    private PromoCouponActivityService promoCouponActivityService;

    @Mock
    private ActivityRedisHelpler redisService;

    @Test
    public void lockCoupon_02(){

        TCouponLockDTO couponLockDTO = new TCouponLockDTO();
        couponLockDTO.setUserCode("");
        couponLockDTO.setTenantCode("");
        couponLockDTO.setOrderNo("");
        couponLockDTO.setCouponCodes("123");
        List<TPromoCouponInnerCodeVO> innerCodes = new ArrayList<>();

        TPromoCouponInnerCodeVO innerCodeVO = new TPromoCouponInnerCodeVO();
        innerCodeVO.setId("");
        innerCodeVO.setCreateUser("");
        innerCodeVO.setTenantCode("");
        innerCodeVO.setActivityCode("");
        innerCodeVO.setReleaseCode("");
        innerCodeVO.setCouponCode("");
        innerCodeVO.setCouponType("02");
        innerCodeVO.setStatus("01");
        innerCodeVO.setFrozenStatus("01");
        innerCodeVO.setFaceValue(new BigDecimal("0"));
        innerCodeVO.setFaceUnit("");
        innerCodeVO.setTakeLabel("");
        innerCodeVO.setReceiveStartTime("");
        innerCodeVO.setReceiveEndTime("");
        innerCodeVO.setCreateTime(new Date());
        innerCodeVO.setLogicDelete("");

        innerCodes.add(innerCodeVO);

        Mockito.when(couponInnerCodeService.getCouponInnerCodeByCodes(Mockito.any(),Mockito.any())).thenReturn(innerCodes);
        lockCouponComponent.lockCoupon(couponLockDTO);



    }



    @Test
    public void lockCoupon_03(){

        TCouponLockDTO couponLockDTO = new TCouponLockDTO();
        couponLockDTO.setUserCode("");
        couponLockDTO.setTenantCode("");
        couponLockDTO.setOrderNo("");
        couponLockDTO.setCouponCodes("123");
        List<TPromoCouponInnerCodeVO> innerCodes = new ArrayList<>();

        TPromoCouponInnerCodeVO innerCodeVO = new TPromoCouponInnerCodeVO();
        innerCodeVO.setId("");
        innerCodeVO.setCreateUser("");
        innerCodeVO.setTenantCode("");
        innerCodeVO.setActivityCode("");
        innerCodeVO.setReleaseCode("");
        innerCodeVO.setCouponCode("");
        innerCodeVO.setCouponType("03");
        innerCodeVO.setStatus("01");
        innerCodeVO.setFrozenStatus("01");
        innerCodeVO.setFaceValue(new BigDecimal("0"));
        innerCodeVO.setFaceUnit("");
        innerCodeVO.setTakeLabel("");
        innerCodeVO.setReceiveStartTime("");
        innerCodeVO.setReceiveEndTime("");
        innerCodeVO.setCreateTime(new Date());
        innerCodeVO.setLogicDelete("");

        innerCodes.add(innerCodeVO);

        Mockito.when(couponInnerCodeService.getCouponInnerCodeByCodes(Mockito.any(),Mockito.any())).thenReturn(innerCodes);
        lockCouponComponent.lockCoupon(couponLockDTO);



    }


    @Test
    public void lockCoupon_01(){

        TCouponLockDTO couponLockDTO = new TCouponLockDTO();
        couponLockDTO.setUserCode("");
        couponLockDTO.setTenantCode("");
        couponLockDTO.setOrderNo("");
        couponLockDTO.setCouponCodes("123");
        List<TPromoCouponInnerCodeVO> innerCodes = new ArrayList<>();

        TPromoCouponInnerCodeVO innerCodeVO = new TPromoCouponInnerCodeVO();
        innerCodeVO.setId("");
        innerCodeVO.setCreateUser("");
        innerCodeVO.setTenantCode("");
        innerCodeVO.setActivityCode("");
        innerCodeVO.setReleaseCode("");
        innerCodeVO.setCouponCode("");
        innerCodeVO.setCouponType("01");
        innerCodeVO.setStatus("02");
        innerCodeVO.setFrozenStatus("01");
        innerCodeVO.setFaceValue(new BigDecimal("0"));
        innerCodeVO.setFaceUnit("");
        innerCodeVO.setTakeLabel("");
        innerCodeVO.setReceiveStartTime("");
        innerCodeVO.setReceiveEndTime("");
        innerCodeVO.setCreateTime(new Date());
        innerCodeVO.setLogicDelete("");

        innerCodes.add(innerCodeVO);

        Mockito.when(couponInnerCodeService.getCouponInnerCodeByCodes(Mockito.any(),Mockito.any())).thenReturn(innerCodes);
        lockCouponComponent.lockCoupon(couponLockDTO);

    }



    @Test
    public void unLockCoupon_list_null(){

        TCouponUnLockDTO couponUnLockDTO = new TCouponUnLockDTO();

        lockCouponComponent.unLockCoupon(couponUnLockDTO);


    }


    @Test
    public void unLockCoupon(){

        TCouponUnLockDTO couponUnLockDTO = new TCouponUnLockDTO();

        List<TPromoCouponCodeUserVO> list = new ArrayList<>();

        TPromoCouponCodeUserVO userVO = new TPromoCouponCodeUserVO();
        userVO.setValidTime(new CouponReleaseDomain(),"");
        userVO.setId("");
        userVO.setCreateUser("");
        userVO.setLogicDelete("");
        userVO.setTenantCode("");
        userVO.setActivityCode("");
        userVO.setReleaseCode("");
        userVO.setCouponType("");
        userVO.setOpsType("");
        userVO.setStatus("");
        userVO.setFrozenStatus("");
        userVO.setCouponCode("");
        userVO.setUserCode("");
        userVO.setFaceValue(new BigDecimal("0"));
        userVO.setFaceUnit("");
        userVO.setTakeLabel("");
        userVO.setValidStartTime("");
        userVO.setValidEndTime("20300000000000");
        userVO.setReceivedTime("");
        userVO.setUsedRefId("");
        userVO.setUsedTime("");
        userVO.setCreateTime(new Date());

        list.add(userVO);

        ActivityModel activityModel = new ActivityModel();
        activityModel.setCouponInfo(new ActivityModel());
        activityModel.setLanguage(new ActivityLanguageModel());
        activityModel.setItemScopeType(0);
        activityModel.setId(0L);
        activityModel.setDomainCode("");
        activityModel.setTenantCode("");
        activityModel.setActivityCode("");
        activityModel.setCustomCondition("");
        activityModel.setActivityType("");
        activityModel.setActivityLabel("");
        activityModel.setActivityName("");
        activityModel.setActivityDesc("");
        activityModel.setShowFlag(0);
        activityModel.setActivityRemark("");
        activityModel.setActivityExpr("");
        activityModel.setPriority(0);
        activityModel.setTemplateCode("");
        activityModel.setIncentiveLimitedFlag("");
        activityModel.setWarmBegin("");
        activityModel.setWarmEnd("");
        activityModel.setActivityBegin("");
        activityModel.setActivityEnd("");
        activityModel.setPeriodType("");
        activityModel.setProductSelectionType("");
        activityModel.setStoreType("");
        activityModel.setActivityUrl("");
        activityModel.setActivityStatus("");
        activityModel.setSponsors("");
        activityModel.setOpsType("");
        activityModel.setExclusionKey("");
        activityModel.setCreateUser("");
        activityModel.setCreateTime(new Date());
        activityModel.setUpdateUser("");
        activityModel.setTotalQuantity(0);
        activityModel.setUserLimitMax(0);
        activityModel.setCouponType("");
        activityModel.setCouponCode("");
        activityModel.setReserveInventory(0);
        activityModel.setCoolDown("");
        activityModel.setBackgroundImage("");
        activityModel.setProductCondition("");
        activityModel.setRibbonImage("");
        activityModel.setRibbonPosition("");
        activityModel.setRibbonText("");
        activityModel.setCouponCodes(Lists.newArrayList());
        List<TPromoCouponInnerCodeVO> list1 = new ArrayList<>();

        TPromoCouponInnerCodeVO innerCodeVO = new TPromoCouponInnerCodeVO();
        innerCodeVO.setId("");
        innerCodeVO.setCreateUser("");
        innerCodeVO.setTenantCode("");
        innerCodeVO.setActivityCode("");
        innerCodeVO.setReleaseCode("");
        innerCodeVO.setCouponCode("");
        innerCodeVO.setCouponType("");
        innerCodeVO.setStatus("");
        innerCodeVO.setFrozenStatus("");
        innerCodeVO.setFaceValue(new BigDecimal("0"));
        innerCodeVO.setFaceUnit("");
        innerCodeVO.setTakeLabel("");
        innerCodeVO.setReceiveStartTime("");
        innerCodeVO.setReceiveEndTime("");
        innerCodeVO.setCreateTime(new Date());
        innerCodeVO.setLogicDelete("");

        list1.add(innerCodeVO);

        Mockito.when(promoCouponActivityService.findCouponActivity(Mockito.any(),Mockito.any())).thenReturn(activityModel);
        Mockito.when(couponCodeUserService.getCodeUserByUsedRefId(Mockito.any(),Mockito.any())).thenReturn(list);
        Mockito.when(couponInnerCodeService.getCouponInnerCodeByCodes(Mockito.any(),Mockito.any())).thenReturn(list1);
        lockCouponComponent.unLockCoupon(couponUnLockDTO);


    }



    @Test
    public void unLockCoupon_grantedCodes_null(){

        TCouponUnLockDTO couponUnLockDTO = new TCouponUnLockDTO();

        List<TPromoCouponCodeUserVO> list = new ArrayList<>();

        TPromoCouponCodeUserVO userVO = new TPromoCouponCodeUserVO();
        userVO.setValidTime(new CouponReleaseDomain(),"");
        userVO.setId("");
        userVO.setCreateUser("");
        userVO.setLogicDelete("");
        userVO.setTenantCode("");
        userVO.setActivityCode("");
        userVO.setReleaseCode("");
        userVO.setCouponType("");
        userVO.setOpsType("");
        userVO.setStatus("");
        userVO.setFrozenStatus("");
        userVO.setCouponCode("");
        userVO.setUserCode("");
        userVO.setFaceValue(new BigDecimal("0"));
        userVO.setFaceUnit("");
        userVO.setTakeLabel("");
        userVO.setValidStartTime("");
        userVO.setValidEndTime("20000000000000");
        userVO.setReceivedTime("");
        userVO.setUsedRefId("");
        userVO.setUsedTime("");
        userVO.setCreateTime(new Date());

        list.add(userVO);

        ActivityModel activityModel = new ActivityModel();
        activityModel.setCouponInfo(new ActivityModel());
        activityModel.setLanguage(new ActivityLanguageModel());
        activityModel.setItemScopeType(0);
        activityModel.setId(0L);
        activityModel.setDomainCode("");
        activityModel.setTenantCode("");
        activityModel.setActivityCode("");
        activityModel.setCustomCondition("");
        activityModel.setActivityType("");
        activityModel.setActivityLabel("");
        activityModel.setActivityName("");
        activityModel.setActivityDesc("");
        activityModel.setShowFlag(0);
        activityModel.setActivityRemark("");
        activityModel.setActivityExpr("");
        activityModel.setPriority(0);
        activityModel.setTemplateCode("");
        activityModel.setIncentiveLimitedFlag("");
        activityModel.setWarmBegin("");
        activityModel.setWarmEnd("");
        activityModel.setActivityBegin("");
        activityModel.setActivityEnd("");
        activityModel.setPeriodType("");
        activityModel.setProductSelectionType("");
        activityModel.setStoreType("");
        activityModel.setActivityUrl("");
        activityModel.setActivityStatus("");
        activityModel.setSponsors("");
        activityModel.setOpsType("");
        activityModel.setExclusionKey("");
        activityModel.setCreateUser("");
        activityModel.setCreateTime(new Date());
        activityModel.setUpdateUser("");
        activityModel.setTotalQuantity(0);
        activityModel.setUserLimitMax(0);
        activityModel.setCouponType("");
        activityModel.setCouponCode("");
        activityModel.setReserveInventory(0);
        activityModel.setCoolDown("");
        activityModel.setBackgroundImage("");
        activityModel.setProductCondition("");
        activityModel.setRibbonImage("");
        activityModel.setRibbonPosition("");
        activityModel.setRibbonText("");
        activityModel.setCouponCodes(Lists.newArrayList());
        List<TPromoCouponInnerCodeVO> list1 = new ArrayList<>();

        TPromoCouponInnerCodeVO innerCodeVO = new TPromoCouponInnerCodeVO();
        innerCodeVO.setId("");
        innerCodeVO.setCreateUser("");
        innerCodeVO.setTenantCode("");
        innerCodeVO.setActivityCode("");
        innerCodeVO.setReleaseCode("");
        innerCodeVO.setCouponCode("");
        innerCodeVO.setCouponType("");
        innerCodeVO.setStatus("");
        innerCodeVO.setFrozenStatus("");
        innerCodeVO.setFaceValue(new BigDecimal("0"));
        innerCodeVO.setFaceUnit("");
        innerCodeVO.setTakeLabel("");
        innerCodeVO.setReceiveStartTime("");
        innerCodeVO.setReceiveEndTime("");
        innerCodeVO.setCreateTime(new Date());
        innerCodeVO.setLogicDelete("");

        list1.add(innerCodeVO);

        Mockito.when(promoCouponActivityService.findCouponActivity(Mockito.any(),Mockito.any())).thenReturn(activityModel);
        Mockito.when(couponCodeUserService.getCodeUserByUsedRefId(Mockito.any(),Mockito.any())).thenReturn(list);
        lockCouponComponent.unLockCoupon(couponUnLockDTO);


    }


    @Test
    public void updateCouponUnGrant_grantedCodes_contains_true(){

        List<String> grantedCodes = new ArrayList<>();
        grantedCodes.add("123");
        String tenantCode = "";
        TPromoCouponInnerCodeVO vo = new TPromoCouponInnerCodeVO();
        vo.setId("");
        vo.setCreateUser("");
        vo.setTenantCode("");
        vo.setActivityCode("");
        vo.setReleaseCode("");
        vo.setCouponCode("123");
        vo.setCouponType("02");
        vo.setStatus("");
        vo.setFrozenStatus("");
        vo.setFaceValue(new BigDecimal("0"));
        vo.setFaceUnit("");
        vo.setTakeLabel("");
        vo.setReceiveStartTime("");
        vo.setReceiveEndTime("");
        vo.setCreateTime(new Date());
        vo.setLogicDelete("");


        lockCouponComponent.updateCouponUnGrant(grantedCodes,tenantCode,vo);

    }

    @Test
    public void updateCouponUnGrant_grantedCodes_contains(){

        List<String> grantedCodes = new ArrayList<>();
        grantedCodes.add("156");
        String tenantCode = "";
        TPromoCouponInnerCodeVO vo = new TPromoCouponInnerCodeVO();
        vo.setId("");
        vo.setCreateUser("");
        vo.setTenantCode("");
        vo.setActivityCode("");
        vo.setReleaseCode("");
        vo.setCouponCode("123");
        vo.setCouponType("02");
        vo.setStatus("");
        vo.setFrozenStatus("");
        vo.setFaceValue(new BigDecimal("0"));
        vo.setFaceUnit("");
        vo.setTakeLabel("");
        vo.setReceiveStartTime("");
        vo.setReceiveEndTime("");
        vo.setCreateTime(new Date());
        vo.setLogicDelete("");


        lockCouponComponent.updateCouponUnGrant(grantedCodes,tenantCode,vo);

    }



    @Test
    public void updateCouponUnGrant_type_01(){

        List<String> grantedCodes = new ArrayList<>();
        grantedCodes.add("123");
        String tenantCode = "";
        TPromoCouponInnerCodeVO vo = new TPromoCouponInnerCodeVO();
        vo.setId("");
        vo.setCreateUser("");
        vo.setTenantCode("");
        vo.setActivityCode("");
        vo.setReleaseCode("");
        vo.setCouponCode("123");
        vo.setCouponType("01");
        vo.setStatus("");
        vo.setFrozenStatus("");
        vo.setFaceValue(new BigDecimal("0"));
        vo.setFaceUnit("");
        vo.setTakeLabel("");
        vo.setReceiveStartTime("");
        vo.setReceiveEndTime("");
        vo.setCreateTime(new Date());
        vo.setLogicDelete("");


        lockCouponComponent.updateCouponUnGrant(grantedCodes,tenantCode,vo);

    }


    @Test
    public void unLockResourceByUserVO(){

        TPromoCouponCodeUserVO couponCodeUserVO = new TPromoCouponCodeUserVO();
        couponCodeUserVO.setValidTime(new CouponReleaseDomain(),"");
        couponCodeUserVO.setId("");
        couponCodeUserVO.setCreateUser("");
        couponCodeUserVO.setLogicDelete("");
        couponCodeUserVO.setTenantCode("");
        couponCodeUserVO.setActivityCode("");
        couponCodeUserVO.setReleaseCode("");
        couponCodeUserVO.setCouponType("01");
        couponCodeUserVO.setOpsType("");
        couponCodeUserVO.setStatus("");
        couponCodeUserVO.setFrozenStatus("");
        couponCodeUserVO.setCouponCode("");
        couponCodeUserVO.setUserCode("");
        couponCodeUserVO.setFaceValue(new BigDecimal("0"));
        couponCodeUserVO.setFaceUnit("");
        couponCodeUserVO.setTakeLabel("");
        couponCodeUserVO.setValidStartTime("");
        couponCodeUserVO.setValidEndTime("");
        couponCodeUserVO.setReceivedTime("");
        couponCodeUserVO.setUsedRefId("");
        couponCodeUserVO.setUsedTime("");
        couponCodeUserVO.setCreateTime(new Date());


        TCouponUnLockDTO couponUnLockDTO = new TCouponUnLockDTO();

        List<TPromoCouponCodeUserVO> list = new ArrayList<>();
        list.add(couponCodeUserVO);
        List<String> expireCodes = new ArrayList<>();
        List<String> grantedCodes = new ArrayList<>();
        String tenantCode = "";
        ActivityModel activityModel = new ActivityModel();
        activityModel.setCouponInfo(new ActivityModel());
        activityModel.setLanguage(new ActivityLanguageModel());
        activityModel.setItemScopeType(0);
        activityModel.setId(0L);
        activityModel.setDomainCode("");
        activityModel.setTenantCode("");
        activityModel.setActivityCode("");
        activityModel.setCustomCondition("");
        activityModel.setActivityType("");
        activityModel.setActivityLabel("");
        activityModel.setActivityName("");
        activityModel.setActivityDesc("");
        activityModel.setShowFlag(0);
        activityModel.setActivityRemark("");
        activityModel.setActivityExpr("");
        activityModel.setPriority(0);
        activityModel.setTemplateCode("");
        activityModel.setIncentiveLimitedFlag("");
        activityModel.setWarmBegin("");
        activityModel.setWarmEnd("");
        activityModel.setActivityBegin("");
        activityModel.setActivityEnd("");
        activityModel.setPeriodType("");
        activityModel.setProductSelectionType("");
        activityModel.setStoreType("");
        activityModel.setActivityUrl("");
        activityModel.setActivityStatus("");
        activityModel.setSponsors("");
        activityModel.setOpsType("");
        activityModel.setExclusionKey("");
        activityModel.setCreateUser("");
        activityModel.setCreateTime(new Date());
        activityModel.setUpdateUser("");
        activityModel.setTotalQuantity(0);
        activityModel.setUserLimitMax(0);
        activityModel.setCouponType("");
        activityModel.setCouponCode("");
        activityModel.setReserveInventory(0);
        activityModel.setCoolDown("");
        activityModel.setBackgroundImage("");
        activityModel.setProductCondition("");
        activityModel.setRibbonImage("");
        activityModel.setRibbonPosition("");
        activityModel.setRibbonText("");
        activityModel.setCouponCodes(Lists.newArrayList());

        lockCouponComponent.unLockResourceByUserVO(couponUnLockDTO,list,expireCodes,grantedCodes,tenantCode);

    }


    @Test
    public void redisUnlockUserAndQuantity(){

        TPromoCouponCodeUserVO vo = new TPromoCouponCodeUserVO();
        vo.setValidTime(new CouponReleaseDomain(),"");
        vo.setId("");
        vo.setCreateUser("");
        vo.setLogicDelete("");
        vo.setTenantCode("");
        vo.setActivityCode("");
        vo.setReleaseCode("");
        vo.setCouponType("03");
        vo.setOpsType("");
        vo.setStatus("");
        vo.setFrozenStatus("");
        vo.setCouponCode("");
        vo.setUserCode("");
        vo.setFaceValue(new BigDecimal("0"));
        vo.setFaceUnit("");
        vo.setTakeLabel("");
        vo.setValidStartTime("");
        vo.setValidEndTime("");
        vo.setReceivedTime("");
        vo.setUsedRefId("");
        vo.setUsedTime("");
        vo.setCreateTime(new Date());
        ActivityModel activityModel = new ActivityModel();
        activityModel.setCouponInfo(new ActivityModel());
        activityModel.setLanguage(new ActivityLanguageModel());
        activityModel.setItemScopeType(0);
        activityModel.setId(0L);
        activityModel.setDomainCode("");
        activityModel.setTenantCode("");
        activityModel.setActivityCode("");
        activityModel.setCustomCondition("");
        activityModel.setActivityType("");
        activityModel.setActivityLabel("");
        activityModel.setActivityName("");
        activityModel.setActivityDesc("");
        activityModel.setShowFlag(0);
        activityModel.setActivityRemark("");
        activityModel.setActivityExpr("");
        activityModel.setPriority(0);
        activityModel.setTemplateCode("");
        activityModel.setIncentiveLimitedFlag("");
        activityModel.setWarmBegin("");
        activityModel.setWarmEnd("");
        activityModel.setActivityBegin("");
        activityModel.setActivityEnd("");
        activityModel.setPeriodType("");
        activityModel.setProductSelectionType("");
        activityModel.setStoreType("");
        activityModel.setActivityUrl("");
        activityModel.setActivityStatus("");
        activityModel.setSponsors("");
        activityModel.setOpsType("");
        activityModel.setExclusionKey("");
        activityModel.setCreateUser("");
        activityModel.setCreateTime(new Date());
        activityModel.setUpdateUser("");
        activityModel.setTotalQuantity(0);
        activityModel.setUserLimitMax(1);
        activityModel.setCouponType("");
        activityModel.setCouponCode("");
        activityModel.setReserveInventory(0);
        activityModel.setCoolDown("");
        activityModel.setBackgroundImage("");
        activityModel.setProductCondition("");
        activityModel.setRibbonImage("");
        activityModel.setRibbonPosition("");
        activityModel.setRibbonText("");
        activityModel.setCouponCodes(Lists.newArrayList());

        Mockito.when(promoCouponActivityService.findCouponActivity(Mockito.any(),Mockito.any())).thenReturn(activityModel);
        lockCouponComponent.redisUnlockUserAndQuantity(vo);

    }


    @Test
    public void getExpireCodes(){

        TCouponUnLockDTO couponUnLockDTO = new TCouponUnLockDTO();
        couponUnLockDTO.setOrderStatus("01");
        List<String> expireCodes = new ArrayList<>();
        TPromoCouponCodeUserVO vo = new TPromoCouponCodeUserVO();
        ActivityModel activityModel = new ActivityModel();
        activityModel.setCouponInfo(new ActivityModel());
        activityModel.setLanguage(new ActivityLanguageModel());
        activityModel.setItemScopeType(0);
        activityModel.setId(0L);
        activityModel.setDomainCode("");
        activityModel.setTenantCode("");
        activityModel.setActivityCode("");
        activityModel.setCustomCondition("");
        activityModel.setActivityType("");
        activityModel.setActivityLabel("");
        activityModel.setActivityName("");
        activityModel.setActivityDesc("");
        activityModel.setShowFlag(0);
        activityModel.setActivityRemark("");
        activityModel.setActivityExpr("");
        activityModel.setPriority(0);
        activityModel.setTemplateCode("");
        activityModel.setIncentiveLimitedFlag("");
        activityModel.setWarmBegin("");
        activityModel.setWarmEnd("");
        activityModel.setActivityBegin("");
        activityModel.setActivityEnd("20000000000000");
        activityModel.setPeriodType("");
        activityModel.setProductSelectionType("");
        activityModel.setStoreType("");
        activityModel.setActivityUrl("");
        activityModel.setActivityStatus("");
        activityModel.setSponsors("");
        activityModel.setOpsType("");
        activityModel.setExclusionKey("");
        activityModel.setCreateUser("");
        activityModel.setCreateTime(new Date());
        activityModel.setUpdateUser("");
        activityModel.setTotalQuantity(0);
        activityModel.setUserLimitMax(1);
        activityModel.setCouponType("");
        activityModel.setCouponCode("");
        activityModel.setReserveInventory(0);
        activityModel.setCoolDown("");
        activityModel.setBackgroundImage("");
        activityModel.setProductCondition("");
        activityModel.setRibbonImage("");
        activityModel.setRibbonPosition("");
        activityModel.setRibbonText("");
        activityModel.setCouponCodes(Lists.newArrayList());

        Mockito.when(activityService.findActivityByActivityCode(Mockito.any(),Mockito.any())).thenReturn(activityModel);
        lockCouponComponent.getExpireCodes(couponUnLockDTO,expireCodes,vo);


    }

}
