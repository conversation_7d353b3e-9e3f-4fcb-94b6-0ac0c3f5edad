package com.gtech.promotion.component;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.gtech.basic.masterdata.response.JsonResult;
import com.gtech.commons.redis.GTechCodeGenerator;
import com.gtech.promotion.component.activity.ActivityCacheDomain;
import com.gtech.promotion.component.activity.PromoGroupDomain;
import com.gtech.promotion.component.activity.PromoGroupRelationDomain;
import com.gtech.promotion.dao.mapper.activity.PromoGroupRelationMapper;
import com.gtech.promotion.dao.model.activity.ActivityModel;
import com.gtech.promotion.dao.model.activity.PromoGroupMode;
import com.gtech.promotion.dao.model.activity.PromoGroupVO;
import com.gtech.promotion.dao.model.marketing.MarketingModel;
import com.gtech.promotion.dto.cache.ActivityCacheDTO;
import com.gtech.promotion.dto.out.activity.GroupActivityVO;
import com.gtech.promotion.exception.PromotionException;
import com.gtech.promotion.exception.PromotionParamValidateException;
import com.gtech.promotion.feign.MasterDataFeignClient;
import com.gtech.promotion.helper.ActivityRedisHelpler;
import com.gtech.promotion.service.activity.ActivityService;
import com.gtech.promotion.service.activity.PromotionGroupRelationService;
import com.gtech.promotion.service.activity.PromotionGroupService;
import com.gtech.promotion.service.activity.TPromoActivityExpressionService;
import com.gtech.promotion.service.marketing.MarketingService;
import com.gtech.promotion.utils.ExpressionHelper;
import com.gtech.promotion.vo.param.activity.*;
import com.gtech.promotion.vo.result.activity.ActivityGroupResult;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.exceptions.base.MockitoException;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * @Description :
 * <AUTHOR> gaojie.li
 * @Date : 2023/6/16 10:46
 */
@RunWith(MockitoJUnitRunner.class)

public class PromoGroupDomainTest {

    @InjectMocks
    private PromoGroupDomain promoGroupDomain;

    @Mock
    private PromotionGroupService promotionGroupService;

    @Mock
    private ActivityService activityService;
    @Mock
    private MarketingService marketingService;

    @Mock
    private PromoGroupRelationDomain promoGroupRelationDomain;


    @Mock
    private GTechCodeGenerator codeGenerator;

    @Mock
    private MasterDataFeignClient masterDataFeignClient;

    @Mock
    private ActivityRedisHelpler redisService;

    @Mock
    private ActivityCacheDomain activityCacheDomain;

    @Mock
    private PromotionGroupRelationService promotionGroupRelationService;

    @Mock
    private TPromoActivityExpressionService expressionService;

    @Mock
    private PromoGroupRelationMapper promoGroupRelationMapper;

    private static final String TENANT_CODE = "tenant123";
    private static final String DOMAIN_CODE = "domain123";
    private static final String OPS_TYPE = "101";
    private static final String GROUP_CODE = "GROUP_CODE_1";
    @Test
    public void checkGroupEnabledByTenantCode1(){

        JsonResult<String> result = new JsonResult<>();

        result.setData("1");
        result.setSuccess(true);

        Mockito.when(masterDataFeignClient.getValueValue(Mockito.any(),Mockito.any())).thenThrow(new MockitoException("1"));

        boolean b = promoGroupDomain.checkGroupEnabledByTenantCode("1", "1");
        Assert.assertFalse(false);

    }

    @Test
    public void checkGroupEnabledByTenantCode(){

        JsonResult<String> result = new JsonResult<>();

        result.setData("1");
        result.setSuccess(true);

        Mockito.when(masterDataFeignClient.getValueValue(Mockito.anyString(),Mockito.anyString())).thenReturn(result);

        PromoGroupVO groupOutVO = new PromoGroupVO();
        groupOutVO.setPriority(1L);
        groupOutVO.setGroupCode("1");
        Mockito.when(promotionGroupService.getGroupCountGroupType(Mockito.anyString(),Mockito.anyString())).thenReturn(1);

        boolean b = promoGroupDomain.checkGroupEnabledByTenantCode("1", "1");
        Assert.assertTrue(b);


    }

    @Test
    public void checkGroupEnabledByTenantCode_false(){

        JsonResult<String> result = new JsonResult<>();

        result.setData("0");
        result.setSuccess(false);

        Mockito.when(masterDataFeignClient.getValueValue(Mockito.anyString(),Mockito.anyString())).thenReturn(null);


        boolean b = promoGroupDomain.checkGroupEnabledByTenantCode("1", "1");
        Assert.assertFalse(b);
    }

    @Test
    public void checkGroupEnabledByTenantCode_0(){

        JsonResult<String> result = new JsonResult<>();

        result.setData("0");
        result.setSuccess(true);

        Mockito.when(masterDataFeignClient.getValueValue(Mockito.anyString(),Mockito.anyString())).thenReturn(null);


        boolean b = promoGroupDomain.checkGroupEnabledByTenantCode("1", "1");
        Assert.assertFalse(b);
    }

    @Test
    public void checkGroupEnabledByTenantCode_null(){

        JsonResult<String> result = new JsonResult<>();

        result.setData("1");
        result.setSuccess(true);

        Mockito.when(masterDataFeignClient.getValueValue(Mockito.anyString(),Mockito.anyString())).thenReturn(null);


        boolean b = promoGroupDomain.checkGroupEnabledByTenantCode("1", "1");
        Assert.assertFalse(b);
    }

    @Test(expected = PromotionException.class)
    public void checkGroupEnabledByTenantCode_exception(){

        JsonResult<String> result = new JsonResult<>();

        result.setData("1");
        result.setSuccess(true);

        Mockito.when(masterDataFeignClient.getValueValue(Mockito.anyString(),Mockito.anyString())).thenReturn(result);

        PromoGroupVO groupOutVO = new PromoGroupVO();
        groupOutVO.setPriority(1L);
        groupOutVO.setGroupCode("1");
        Mockito.when(promotionGroupService.getGroupCountGroupType(Mockito.anyString(),Mockito.anyString())).thenReturn(0);

        promoGroupDomain.checkGroupEnabledByTenantCode("1","1");

    }


    @Test(expected = PromotionException.class)
    public void initGroupDomain1(){

        GroupInitParam param = new GroupInitParam();
        param.setTenantCode("1");
        param.setOperatorUser("1");

        promoGroupDomain.initGroupDomain(param);
    }


    @Test
    public void testInit() {
        // Arrange
        String domainCode = "testDomain";
        String tenantCode = "testTenant";

        String str = "{\"1_0101020103010402\":\"101\",\"1_0102020303020401\":\"103\",\"1_0102020203020413\":\"102\",\"1_0102020203020404\":\"104\",\"1_0101020603020402\":\"105\",\"1_0101020703020406\":\"106\",\"1_0102020203020415\":\"107\",\"2_0104020103010401\":\"201\",\"2_0102020203020401\":\"202\",\"2_0102020203030401\":\"203\",\"2_0101020703020406\":\"204\",\"2_0102020203020405\":\"205\",\"2_0101020603020401\":\"206\"}";
        Map<String, String> map = JSON.parseObject(str, Map.class);
        // 创建模拟数据
        ActivityModel activityModel1 = new ActivityModel();
        activityModel1.setOpsType("101");
        activityModel1.setTemplateCode("0101020103010402");
        ActivityModel activityModel2 = new ActivityModel();
        activityModel2.setOpsType("201");
        activityModel2.setTemplateCode("0104020103010401");
        ActivityModel activityModel3 = new ActivityModel();
        activityModel3.setOpsType("201");
        activityModel3.setTemplateCode("0104020103010402");

        MarketingModel marketingModel1 = new MarketingModel();
        marketingModel1.setOpsType("103");
        MarketingModel marketingModel2 = new MarketingModel();
        marketingModel2.setOpsType("104");

        List<ActivityModel> activities = Arrays.asList(activityModel1, activityModel2,activityModel3);
        List<MarketingModel> marketings = Arrays.asList(marketingModel1, marketingModel2);

        // 配置模拟行为
//        Mockito.when(activityService.queryActivityByTenantCode(tenantCode, null, null,
//                ActivityStatusEnum.EFFECTIVE, ActivityStatusEnum.PENDING, ActivityStatusEnum.IN_AUDIT, ActivityStatusEnum.SUSPEND))
//                .thenReturn(activities);

//        Mockito.when(marketingService.queryMarketingList(tenantCode, null, ActivityStatusEnum.EFFECTIVE.code()))
//                .thenReturn(marketings);


        Mockito.when(expressionService.getActivityExpression(tenantCode)).thenReturn(ExpressionHelper.DEF_EXPROSSION);

        // Act
        promoGroupDomain.init(domainCode, tenantCode);


    }


    @Test
    public void initGroupDomain(){

        GroupInitParam param = new GroupInitParam();
        param.setTenantCode("1");
        param.setOperatorUser("1");

        JSONObject jsonObject = new JSONObject();

        JSONObject data = new JSONObject();
        JSONArray jsonArray = new JSONArray();

        JSONObject object = new JSONObject();
        object.put("ddValue","11");
        object.put("ddText","11");

        jsonArray.add(object);
        data.put("total",1);

        data.put("list",jsonArray);
        jsonObject.put("data",data);

        JsonResult<String> result = new JsonResult<>();
        result.setSuccess(true);
        result.setData("1");


        JsonResult<String> result2 = new JsonResult<>();
        result2.setSuccess(true);

        result2.setData("[\"301\",\"302\",\"303\",\"401\",\"501\",\"601\",\"701\"]");

        Mockito.when(masterDataFeignClient.getValueValue(Mockito.any(),Mockito.any())).thenReturn(result).thenReturn(result2);

        Mockito.when(masterDataFeignClient.queryDdPages(Mockito.any())).thenReturn(jsonObject);


        PromoGroupVO groupOutVO = new PromoGroupVO();
        groupOutVO.setPriority(1L);
        groupOutVO.setGroupCode("1");
        Mockito.when(promotionGroupService.getLastedGroup(Mockito.any())).thenReturn(groupOutVO);
        promoGroupDomain.initGroupDomain(param);
    }

    @Test
    public void createGroupDomain(){

        GroupCreateParam param = new GroupCreateParam();
        param.setTenantCode("1");
        param.setGroupName("1");
        param.setOperatorUser("1");

        PromoGroupVO groupOutVO = new PromoGroupVO();
        groupOutVO.setPriority(1L);
		groupOutVO.setGroupCode("1");
        Mockito.when(promotionGroupService.getLastedGroup(Mockito.any())).thenReturn(groupOutVO);

        Mockito.when(promotionGroupService.insertActivityGroup(Mockito.any())).thenReturn(1);
        JsonResult<String> result2 = new JsonResult<>();
        result2.setSuccess(true);

        result2.setData("[\"301\",\"302\",\"303\",\"401\",\"501\",\"601\",\"701\"]");
        Mockito.when(masterDataFeignClient.getValueValue(Mockito.any(),Mockito.any())).thenReturn(result2);

        String groupDomain = promoGroupDomain.createGroupDomain(param);
        Assert.assertEquals(null,groupDomain);
    }


    @Test
    public void testQueryGroupCode_WithExistingGroup() {
        //ReflectionTestUtils.setField(promoGroupDomain, "groupBlackList", Lists.newArrayList());
        // Arrange
        PromoGroupVO existingGroup = new PromoGroupVO();
        existingGroup.setGroupCode(GROUP_CODE);
        Mockito.when(promotionGroupService.getGroupByGroupCode(TENANT_CODE, OPS_TYPE)).thenReturn(existingGroup);

        // Act
        String result = promoGroupDomain.queryGroupCode(DOMAIN_CODE, TENANT_CODE, OPS_TYPE);


    }

    @Test
    public void testQueryGroupCode_WithNoExistingGroup() {
        //ReflectionTestUtils.setField(promoGroupDomain, "groupBlackList", Lists.newArrayList());
        // Arrange
        Mockito.when(promotionGroupService.getGroupByGroupCode(TENANT_CODE, OPS_TYPE)).thenReturn(null);

        // Act
        String result = promoGroupDomain.queryGroupCode(DOMAIN_CODE, TENANT_CODE, OPS_TYPE);

    }



    @Test
    public void createGroupDomain_null(){

        GroupCreateParam param = new GroupCreateParam();
        param.setTenantCode("1");
        param.setGroupName("1");
        param.setOperatorUser("1");

        PromoGroupVO groupOutVO = new PromoGroupVO();
        groupOutVO.setPriority(1L);
		groupOutVO.setGroupCode("1");
        Mockito.when(promotionGroupService.getLastedGroup(Mockito.any())).thenReturn(null);

        Mockito.when(promotionGroupService.insertActivityGroup(Mockito.any())).thenReturn(1);

        JsonResult<String> result2 = new JsonResult<>();
        result2.setSuccess(true);

        result2.setData("[\"301\",\"302\",\"303\",\"401\",\"501\",\"601\",\"701\"]");
        Mockito.when(masterDataFeignClient.getValueValue(Mockito.any(),Mockito.any())).thenReturn(result2);


        String groupDomain = promoGroupDomain.createGroupDomain(param);
        Assert.assertEquals(null,groupDomain);
    }



    @Test
    public void createGroupDomain_null_disable(){

        GroupCreateParam param = new GroupCreateParam();
        param.setTenantCode("1");
        param.setGroupName("1");
        param.setOperatorUser("1");

        PromoGroupVO groupOutVO = new PromoGroupVO();
        groupOutVO.setPriority(1L);
        groupOutVO.setGroupCode("1");
        Mockito.when(promotionGroupService.getLastedGroup(Mockito.any())).thenReturn(null);

        Mockito.when(promotionGroupService.insertActivityGroup(Mockito.any())).thenReturn(1);

        JsonResult<String> result2 = new JsonResult<>();
        result2.setSuccess(true);

        result2.setData(null);
        Mockito.when(masterDataFeignClient.getValueValue(Mockito.any(),Mockito.any())).thenReturn(result2);


        String groupDomain = promoGroupDomain.createGroupDomain(param);
        Assert.assertEquals(null,groupDomain);
    }

    @Test
    public void updateGroupDomain(){

        GroupUpdateParam param = new GroupUpdateParam();
        param.setTenantCode("1");
        param.setGroupName("1");
        param.setOperatorUser("1");

        param.setGroupCode("1");
        PromoGroupVO groupOutVO = new PromoGroupVO();
        groupOutVO.setPriority(1L);
		groupOutVO.setGroupCode("1");

        Mockito.when(promotionGroupService.updateActivityGroup(Mockito.any())).thenReturn(1);

        Integer groupDomain = promoGroupDomain.updateGroupDomain(param);

        Assert.assertEquals(1,groupDomain.intValue());
    }

    @Test(expected = PromotionParamValidateException.class)
    public void deleteGroupDomain(){

        GroupDeleteParam param = new GroupDeleteParam();
        param.setTenantCode("1");
        param.setGroupCode("1");
        param.setOperatorUser("1");
        Integer group = promoGroupDomain.deleteGroupDomain(param);
        Assert.assertEquals(1, group.intValue());
    }

    @Test(expected = PromotionParamValidateException.class)
    public void deleteGroupDomain1(){

        GroupDeleteParam param = new GroupDeleteParam();
        param.setTenantCode("1");
        param.setGroupCode("1");
        param.setOperatorUser("1");
        PromoGroupVO groupVO = new PromoGroupVO();
        groupVO.setType("01");
        Mockito.when(promotionGroupService.getGroupByGroupCode(Mockito.anyString(),Mockito.anyString())).thenReturn(groupVO);
        Integer group = promoGroupDomain.deleteGroupDomain(param);
        Assert.assertEquals(1, group.intValue());
    }

    @Test
    public void deleteGroupDomain2(){

        GroupDeleteParam param = new GroupDeleteParam();
        param.setTenantCode("1");
        param.setGroupCode("1");
        param.setOperatorUser("1");
        PromoGroupVO groupVO = new PromoGroupVO();
        groupVO.setType("02");
        Mockito.when(promotionGroupService.getGroupByGroupCode(Mockito.anyString(),Mockito.anyString())).thenReturn(groupVO);
        Mockito.when(promotionGroupService.deleteActivityGroup(Mockito.anyString(),Mockito.anyString(),Mockito.anyString())).thenReturn(1);
        Integer group = promoGroupDomain.deleteGroupDomain(param);
        Assert.assertEquals(1, group.intValue());
    }

    @Test
    public void deleteGroup1(){

        GroupDeleteParam param = new GroupDeleteParam();
        param.setTenantCode("1");
        param.setGroupCode("1");
        param.setOperatorUser("1");
        Mockito.when(activityService.queryActivityByGroupCode(Mockito.anyString(),Mockito.anyString())).thenReturn(new ArrayList<>());
        PromoGroupVO groupVO = new PromoGroupVO();
        groupVO.setType("02");
        Mockito.when(promotionGroupService.getGroupByGroupCode(Mockito.anyString(),Mockito.anyString())).thenReturn(groupVO);

        Integer group = promoGroupDomain.deleteGroupDomain(param);
        Assert.assertEquals(0, group.intValue());
    }

    @Test(expected = PromotionParamValidateException.class)
    public void deleteGroup2(){

        GroupDeleteParam param = new GroupDeleteParam();
        param.setTenantCode("1");
        param.setGroupCode("1");
        param.setOperatorUser("1");

        List<ActivityModel> activityModels = new ArrayList<>();
        ActivityModel activityModel = new ActivityModel();
        activityModels.add(activityModel);
        Mockito.when(activityService.queryActivityByGroupCode(Mockito.anyString(),Mockito.anyString())).thenReturn(activityModels);
        promoGroupDomain.deleteGroupDomain(param);

    }

    @Test
    public void queryActivityGroupList(){

        GroupQueryParam param = new GroupQueryParam();
        param.setTenantCode("1");
        param.setGroupCode("1");
        param.setLogicDelete("0");
        param.setGroupName("!");
        List<PromoGroupMode> results = new ArrayList<>();
        PromoGroupMode activityGroupResult = new PromoGroupMode();
        activityGroupResult.setGroupName("1");
        activityGroupResult.setGroupCode("1");
        activityGroupResult.setPriority(1L);
        activityGroupResult.setTenantCode("1");
        activityGroupResult.setLogicDelete("0");
        activityGroupResult.setType("01");
        results.add(activityGroupResult);

        Mockito.when(promotionGroupService.queryActivityGroupList(Mockito.any())).thenReturn(results);

        List<ActivityGroupResult> group = promoGroupDomain.queryActivityGroupListDomain(param);
        Assert.assertEquals(1, group.size());
    }

    @Test
    public void groupBindingActivityParam(){

        GroupBindingActivityParam param = new GroupBindingActivityParam();
        param.setTenantCode("!");
        List<String> list = new ArrayList<>();
        list.add("1");
        param.setActivityCodes(list);
        param.setDomainCode("!");
        param.setGroupCode("1");
        Mockito.when(activityService.bindingActivityToGroup(Mockito.any())).thenReturn(1);

        ActivityModel activity = new ActivityModel();
        activity.setGroupCode("!");
        Mockito.when(activityService.findActivity(Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(activity);

        ActivityCacheDTO cacheDTO = new ActivityCacheDTO();
        cacheDTO.setActivityModel(activity);

        Mockito.when(activityCacheDomain.getActivityCacheDTO(Mockito.any())).thenReturn(cacheDTO);

        Integer integer = promoGroupDomain.bindingActivityToGroupDomain(param);
        Assert.assertEquals(1,integer.intValue());

    }

    @Test
    public void queryActivityListUnderGroup(){

        GroupQueryListParam param = new GroupQueryListParam();
        param.setTenantCode("!");
        param.setDomainCode("!");
        param.setGroupCode("1");

        List<GroupActivityVO>  results = new ArrayList<>();
        GroupActivityVO activityGroupResult = new GroupActivityVO();
        activityGroupResult.setGroupName("1");
        activityGroupResult.setGroupCode("1");
        activityGroupResult.setTenantCode("1");
        results.add(activityGroupResult);

        PageInfo<GroupActivityVO> pageInfo = new PageInfo<>();

        pageInfo.setTotal(1L);
        pageInfo.setList(results);

        Mockito.when(activityService.queryActivityListUnderGroup(Mockito.any(),Mockito.any())).thenReturn(pageInfo);

        PromoGroupVO groupVO = new PromoGroupVO();
        groupVO.setGroupName("1");
        Mockito.when(promotionGroupService.getGroupByGroupCode(Mockito.anyString(),Mockito.anyString())).thenReturn(groupVO);

        PageInfo<GroupActivityVO> GroupQueryListParam = promoGroupDomain.queryActivityListUnderGroup(param);
        Assert.assertEquals(1,GroupQueryListParam.getTotal());

    }


    @Test
    public void queryActivityListUnderGroup1(){

        GroupQueryListParam param = new GroupQueryListParam();
        param.setTenantCode("!");
        param.setDomainCode("!");
        param.setGroupCode("1");

        List<GroupActivityVO>  results = new ArrayList<>();
        GroupActivityVO activityGroupResult = new GroupActivityVO();
        activityGroupResult.setGroupName("1");
        activityGroupResult.setGroupCode("1");
        activityGroupResult.setTenantCode("1");
        results.add(activityGroupResult);

        PageInfo<GroupActivityVO> pageInfo = new PageInfo<>();

        pageInfo.setTotal(1L);
        pageInfo.setList(new ArrayList<>());

        Mockito.when(activityService.queryActivityListUnderGroup(Mockito.any(),Mockito.any())).thenReturn(pageInfo);

        PageInfo<GroupActivityVO> GroupQueryListParam = promoGroupDomain.queryActivityListUnderGroup(param);
        Assert.assertEquals(1,GroupQueryListParam.getTotal());

    }

    @Test
    public void queryActivityListUnderGroup_null(){

        GroupQueryListParam param = new GroupQueryListParam();
        param.setTenantCode("!");
        param.setDomainCode("!");
        param.setGroupCode("1");

        List<GroupActivityVO>  results = new ArrayList<>();
        GroupActivityVO activityGroupResult = new GroupActivityVO();
        activityGroupResult.setGroupName("1");
        activityGroupResult.setGroupCode("");
        activityGroupResult.setTenantCode("1");
        results.add(activityGroupResult);

        PageInfo<GroupActivityVO> pageInfo = new PageInfo<>();

        pageInfo.setTotal(1L);
        pageInfo.setList(results);

        Mockito.when(activityService.queryActivityListUnderGroup(Mockito.any(),Mockito.any())).thenReturn(pageInfo);

        PageInfo<GroupActivityVO> GroupQueryListParam = promoGroupDomain.queryActivityListUnderGroup(param);
        Assert.assertEquals(1,GroupQueryListParam.getTotal());

    }

    @Test
    public void  updateGroupPriority(){

        GroupSettingPriorityParam param = new GroupSettingPriorityParam();
        param.setDomainCode("!");
        param.setTenantCode("1");

        List<ActivityGroupParam> groups = new ArrayList<>();
        ActivityGroupParam activityGroupParam = new ActivityGroupParam();
        activityGroupParam.setGroupCode("!");

        groups.add(activityGroupParam);
        param.setGroups(groups);

        List<PromoGroupVO> promoGroupVOS = new ArrayList<>();

        PromoGroupVO promoGroupVO = new PromoGroupVO();
        promoGroupVO.setGroupCode("!");
        promoGroupVO.setPriority(1L);
        promoGroupVOS.add(promoGroupVO);

        Mockito.when(promotionGroupService.listActivityGroupByTenantCode(Mockito.any())).thenReturn(promoGroupVOS);


        Integer integer = promoGroupDomain.updateGroupPriorityDomain(param);

        Assert.assertEquals(0,integer.intValue());

    }


    @Test
    public void test_refreshes_activity_group_data() {

        // Set up mock behavior
        Mockito.when(activityService.queryActivityByTenantCode(Mockito.anyString(), Mockito.isNull(), Mockito.isNull(), Mockito.isNull())).thenReturn(new ArrayList<>());
        Mockito.when(marketingService.queryMarketingList(Mockito.anyString(), Mockito.isNull(), Mockito.isNull())).thenReturn(new ArrayList<>());
        Mockito.when(expressionService.getActivityExpression(Mockito.anyString())).thenReturn(ExpressionHelper.DEF_EXPROSSION);

        // Call the method under test
        promoGroupDomain.init("domainCode", "tenantCode");

        // Verify the expected behavior
        Mockito.verify(promotionGroupService).refreshActivityGroupData("domainCode", "tenantCode");
    }
    @Test
    public void test_groups_activities_and_marketing_by_opsType() {


        // Set up mock behavior
        List<ActivityModel> activityModels = new ArrayList<>();
        ActivityModel activityModel = new ActivityModel();
        activityModel.setOpsType("101");
        activityModel.setTemplateCode("0101020103010401");
        activityModel.setGroupCode("101");
        activityModels.add(activityModel);

        ActivityModel activityModel2 = new ActivityModel();
        activityModel2.setOpsType("102");
        activityModel2.setTemplateCode("0101020103010401");
        activityModel2.setGroupCode("102");
        activityModels.add(activityModel2);

        ActivityModel activityModel3 = new ActivityModel();
        activityModel3.setOpsType("201");
        activityModel3.setTemplateCode("0101020103010401");
        activityModel3.setGroupCode("201");
        activityModels.add(activityModel3);

        Mockito.when(activityService.queryActivityByTenantCode(Mockito.anyString(), Mockito.isNull(), Mockito.isNull(), Mockito.isNull())).thenReturn(activityModels);

        List<MarketingModel> marketingModels = new ArrayList<>();
        MarketingModel marketingModel = new MarketingModel();
        marketingModel.setOpsType("401");
        marketingModel.setGroupCode("401");
        marketingModels.add(marketingModel);
        MarketingModel marketingModel2 = new MarketingModel();
        marketingModel2.setOpsType("301");
        marketingModel2.setGroupCode("301");
        marketingModels.add(marketingModel2);
        Mockito.when(marketingService.queryMarketingList(Mockito.anyString(), Mockito.isNull(), Mockito.isNull())).thenReturn(marketingModels);
        Mockito.when(expressionService.getActivityExpression(Mockito.anyString())).thenReturn(ExpressionHelper.DEF_EXPROSSION);

        // Call the method under test
        promoGroupDomain.init("domainCode", "tenantCode");

        // Verify the expected behavior
        Mockito.verify(promotionGroupService).insertActivityGroupList(Mockito.anyList());
    }

}
