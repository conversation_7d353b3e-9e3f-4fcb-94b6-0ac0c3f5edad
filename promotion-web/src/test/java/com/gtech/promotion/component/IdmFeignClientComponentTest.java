package com.gtech.promotion.component;

import com.gtech.basic.idm.web.vo.result.QueryOpUserAccountListResult;
import com.gtech.basic.idm.web.vo.result.QueryUserResult;
import com.gtech.commons.result.Result;
import com.gtech.promotion.component.feign.IdmFeignClientComponent;
import com.gtech.promotion.dao.model.activity.ActivityModel;
import com.gtech.promotion.feign.IdmFeignClient;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.List;

@RunWith(MockitoJUnitRunner.class)
public class IdmFeignClientComponentTest {

    @InjectMocks
    private IdmFeignClientComponent idmFeignClientComponent;

    @Mock
    private IdmFeignClient idmFeignClient;

    @Test
    public void getIdmOpUserAccount(){
        List<ActivityModel> list = new ArrayList<>();
        ActivityModel activityModel = new ActivityModel();
        activityModel.setCreateUser("1");
        list.add(activityModel);
        list.add(activityModel);
        Result<List<QueryOpUserAccountListResult>> result = new Result<>();
        Mockito.when(idmFeignClient.queryOpUserAccountByCodes(Mockito.any())).thenReturn(result);
        List<QueryOpUserAccountListResult> idmOpUserAccount = idmFeignClientComponent.getIdmOpUserAccount(list);
        Assert.assertNull(idmOpUserAccount);
    }

    @Test
    public void queryIdmUserList_empty(){
        List<ActivityModel> list = new ArrayList<>();
        List<QueryUserResult> queryUserResults = idmFeignClientComponent.queryIdmUserList("1", "1", list);
        Assert.assertEquals(0,queryUserResults.size());

    }

    @Test
    public void queryIdmUserList() {
        List<ActivityModel> list = new ArrayList<>();

        ActivityModel activityModel = new ActivityModel();
        activityModel.setCreateUser("1");
        list.add(activityModel);
        list.add(activityModel);

        Result<List<QueryUserResult>> result = new Result<>();

        List<QueryUserResult> resultList = new ArrayList<>();

        QueryUserResult userResult = new QueryUserResult();

        resultList.add(userResult);

        result.setData(resultList);

        Mockito.when(idmFeignClient.queryUserList(Mockito.any())).thenReturn(result);

        List<QueryUserResult> resultList1 = idmFeignClientComponent.queryIdmUserList("1", "1", list);

        Assert.assertEquals(1, resultList1.size());

    }
}
