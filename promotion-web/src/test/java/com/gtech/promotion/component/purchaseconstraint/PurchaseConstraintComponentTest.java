package com.gtech.promotion.component.purchaseconstraint;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.gtech.basic.idm.web.vo.result.QueryOpUserAccountListResult;
import com.gtech.basic.idm.web.vo.result.QueryUserResult;
import com.gtech.basic.masterdata.client.MasterDataClient;
import com.gtech.basic.masterdata.response.JsonResult;
import com.gtech.commons.redis.GTechCodeGenerator;
import com.gtech.commons.result.PageResult;
import com.gtech.commons.result.Result;
import com.gtech.commons.utils.BeanCopyUtils;
import com.gtech.promotion.RedisClient;
import com.gtech.promotion.code.activity.FirstRefusalEnum;
import com.gtech.promotion.code.activity.ProductSelectionEnum;
import com.gtech.promotion.code.activity.ProductSelectionTypeEnum;
import com.gtech.promotion.code.activity.StoreParamTypeEnum;
import com.gtech.promotion.code.purchaseconstraint.PurchaseConstraintRuleTimeTypeEnum;
import com.gtech.promotion.code.purchaseconstraint.PurchaseConstraintRuleTypeEnum;
import com.gtech.promotion.code.purchaseconstraint.PurchaseConstraintStatusEnum;
import com.gtech.promotion.component.activity.ActivityCacheDomain;
import com.gtech.promotion.component.activity.ActivityComponentDomain;
import com.gtech.promotion.component.activity.ProductDomain;
import com.gtech.promotion.component.feign.IdmFeignClientComponent;
import com.gtech.promotion.component.feign.MasterDataFeignClientComponent;
import com.gtech.promotion.component.purchaseconstraint.dto.PcRuleCheckResult;
import com.gtech.promotion.dao.entity.purchaseconstraint.PurchaseConstraintEntity;
import com.gtech.promotion.dao.model.activity.TPromoActivityStoreVO;
import com.gtech.promotion.dao.model.purchaseconstraint.PurchaseConstraintModel;
import com.gtech.promotion.domain.purchaseconstraint.PurchaseConstraintDomain;
import com.gtech.promotion.dto.cache.PurchaseConstraintCacheDTO;
import com.gtech.promotion.dto.in.purchaseconstraint.*;
import com.gtech.promotion.dto.out.product.ProductDetailInDTO;
import com.gtech.promotion.dto.out.purchaseconstraint.FindPurchaseConstraintOutDto;
import com.gtech.promotion.feign.CatalogClientConsumer;
import com.gtech.promotion.feign.MasterDataFeignClient;
import com.gtech.promotion.feign.OrderFeignClient;
import com.gtech.promotion.feign.response.MemberPurchaseStatisticsResp;
import com.gtech.promotion.helper.CustomerConditionHelper;
import com.gtech.promotion.helper.RedisLock;
import com.gtech.promotion.service.activity.*;
import com.gtech.promotion.service.mongo.activity.TPromoProductService;
import com.gtech.promotion.service.purchaseconstraint.PurchaseConstraintRuleService;
import com.gtech.promotion.service.purchaseconstraint.PurchaseConstraintService;
import com.gtech.promotion.utils.Constants;
import com.gtech.promotion.utils.DateUtil;
import com.gtech.promotion.utils.RedissonLockUtil;
import com.gtech.promotion.vo.bean.*;
import com.gtech.promotion.vo.param.activity.CustomCondition;
import com.gtech.promotion.vo.param.purchaseconstraint.query.QueryPurchaseConstraintListParam;
import com.gtech.promotion.vo.result.purchaseconstraint.CalAvailableQtyResult;
import com.gtech.promotion.vo.result.purchaseconstraint.CheckPurchaseConstraintResult;
import com.gtech.promotion.vo.result.purchaseconstraint.QueryPurchaseConstraintListResult;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.*;
import org.mockito.junit.MockitoJUnitRunner;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;


@Slf4j
@RunWith(MockitoJUnitRunner.class)
public class PurchaseConstraintComponentTest {
    @InjectMocks
    private PurchaseConstraintComponent purchaseConstraintComponent;
    @Mock
    private RedisClient redisClient;
    @Mock
    private PurchaseConstraintService purchaseConstraintService;

    @Mock
    private OperationLogService operationLogService;

    @Mock
    private QualificationService qualificationService;

    @Mock
    private TPromoProductService productService;

    @Mock
    private ActivityProductDetailService activityProductDetailService;

    @Mock
    private ActivityPeriodService activityPeriodService;

    @Mock
    private PurchaseConstraintRuleService purchaseConstraintRuleService;

    @Mock
    private ActivityStoreService activityStoreService;


    @Mock
    private ActivityComponentDomain activityComponentDomain;
    @Mock
    private ProductDomain productDomain;


    @Mock
    private MasterDataClient masterDataClient;

    @Mock
    private GTechCodeGenerator codeGenerator;

    @Mock
    private IdmFeignClientComponent idmFeignClientComponent;

    @Mock
    private CatalogClientConsumer catalogClientConsumer;
    @Mock
    private PurchaseConstraintCacheComponent purchaseConstraintCacheComponent;
    @Mock
    private ActivityCacheDomain activityCacheDomain;
    @Mock
    private OrderFeignClient orderFeignClient;

    @Mock
    private MasterDataFeignClientComponent masterDataFeignClientComponent;
    @Mock
    private RedissonLockUtil redissonLockUtil;
    @Captor
    private ArgumentCaptor<Runnable> runnableArgumentCaptor;
    @Mock
    private RedisLock redisLock;
    @Mock
    private RedissonClient redissonClient;
    @Mock
    private RLock rLock;
    @Mock
    private PcRuleComponent pcRuleComponent;
    @Mock
    private MasterDataFeignClient masterDataFeignClient;
    @Mock
    private CustomerConditionHelper customerConditionHelper;

    @Test
    public void queryPurchaseConstraintList() {
        QueryPurchaseConstraintListParam param = new QueryPurchaseConstraintListParam();
        List<PurchaseConstraintEntity> purchaseConstraintEntityList = new ArrayList<>();
        PurchaseConstraintEntity purchaseConstraintEntity = new PurchaseConstraintEntity();
        purchaseConstraintEntity.setCreateUser("123");
        purchaseConstraintEntityList.add(purchaseConstraintEntity);
        Mockito.when(purchaseConstraintService.queryList(Mockito.any())).thenReturn(new PageInfo<>(purchaseConstraintEntityList));

        QueryOpUserAccountListResult opUserAccountListResult = new QueryOpUserAccountListResult();
        opUserAccountListResult.setUserCode("123");
        opUserAccountListResult.setFirstName("zhang");
        opUserAccountListResult.setLastName("san");
        List<QueryOpUserAccountListResult> opUserAccountListResults = new ArrayList<>();
        opUserAccountListResults.add(opUserAccountListResult);
        Mockito.when(idmFeignClientComponent.queryOpUserAccountList(Mockito.any())).thenReturn(opUserAccountListResults);
        PageResult<QueryPurchaseConstraintListResult> purchaseConstraintListResultPageResult =
                purchaseConstraintComponent.queryPurchaseConstraintList(param);
        Assert.assertTrue(purchaseConstraintListResultPageResult.isSuccess());


        List<QueryUserResult> orgUserList = new ArrayList<>();
        QueryUserResult userResult = new QueryUserResult();
        userResult.setUserCode("123");
        userResult.setFirstName("zhang");
        userResult.setLastName("san");
        orgUserList.add(userResult);
        Mockito.when(idmFeignClientComponent.queryOpUserAccountList(Mockito.any())).thenReturn(new ArrayList<>());
        Mockito.when(idmFeignClientComponent
                .queryUserResults(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(orgUserList);
        purchaseConstraintListResultPageResult =
                purchaseConstraintComponent.queryPurchaseConstraintList(param);
        Assert.assertTrue(purchaseConstraintListResultPageResult.isSuccess());
    }

    @Test
    public void updatePurchaseConstraintStatus() {
        UpdatePurchaseConstraintStatusInDTO updatePurchaseConstraintStatusInDTO = new UpdatePurchaseConstraintStatusInDTO();
        updatePurchaseConstraintStatusInDTO.setTenantCode("100000");
        updatePurchaseConstraintStatusInDTO.setPurchaseConstraintCode("pcc0001");
        updatePurchaseConstraintStatusInDTO.setOrgCode("111");
        updatePurchaseConstraintStatusInDTO.setPurchaseConstraintStatus(PurchaseConstraintStatusEnum.IN_AUDIT.getCode());

        // 处理发起审核，但是租户配置不需要审核
        Date purchaseConstraintEndTime = DateUtil.addMinutes(new Date(), 1000);
        PurchaseConstraintModel purchaseConstraintModel = new PurchaseConstraintModel();
        purchaseConstraintModel.setPurchaseConstraintEndTime(purchaseConstraintEndTime);
        Mockito.when(purchaseConstraintService.getPurchaseConstraint(Mockito.any(), Mockito.any())).thenReturn(purchaseConstraintModel);
        String auditConfig = "";
        Mockito.when(activityComponentDomain.getActivityAuditConfig(Mockito.any())).thenReturn(auditConfig);
        Mockito.when(activityComponentDomain.isNeedAudit(Mockito.any())).thenReturn(Constants.NEED_AUDIT_NO);
        boolean success = true;
        try {
            purchaseConstraintComponent.updatePurchaseConstraintStatus(updatePurchaseConstraintStatusInDTO);
        } catch (Exception e) {
            success = false;
        }
        Assert.assertFalse(success);

        // 正常审核请求
        success = true;
        auditConfig = "0,0";
        Mockito.when(activityComponentDomain.getActivityAuditConfig(Mockito.any())).thenReturn(auditConfig);
        Mockito.when(activityComponentDomain.isNeedAudit(Mockito.any())).thenReturn(Constants.NEED_AUDIT_YES);
        Mockito.when(activityComponentDomain.isAuditDifferentOperator(auditConfig)).thenReturn(Constants.NEED_DIFFERENT_YES);
        Mockito.when(activityComponentDomain.isContainOperator(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(Boolean.FALSE);
        try {
            purchaseConstraintComponent.updatePurchaseConstraintStatus(updatePurchaseConstraintStatusInDTO);
        } catch (Exception e) {
            success = false;
        }
        Assert.assertTrue(success);

        // 正常生效请求
        updatePurchaseConstraintStatusInDTO.setPurchaseConstraintStatus(PurchaseConstraintStatusEnum.EFFECTIVE.getCode());
        success = true;
        try {
            purchaseConstraintComponent.updatePurchaseConstraintStatus(updatePurchaseConstraintStatusInDTO);
        } catch (Exception e) {
            success = false;
        }
        Assert.assertTrue(success);
    }

    @Test
    public void createPurchaseConstraint() {
        PurchaseConstraintDomain purchaseConstraintDomain = new PurchaseConstraintDomain();
        purchaseConstraintDomain.setTenantCode("1111");
        purchaseConstraintDomain.setDomainCode("11111");
        purchaseConstraintDomain.setFirstRefusal(0);
        purchaseConstraintDomain.setPurchaseConstraintName("test");
        purchaseConstraintDomain.setOrgCode("test");
        purchaseConstraintDomain.setPeriodType("01");
        ActivityPeriod activityPeriod = new ActivityPeriod();
        purchaseConstraintDomain.setActivityPeriod(activityPeriod);

        // 选择店铺
        purchaseConstraintDomain.setStoreType(StoreParamTypeEnum.STORE_CUSTOM.code());
        List<ActivityStore> channelStores = new ArrayList<>();
        ActivityStore activityStore = new ActivityStore();
        activityStore.setChannelCode("111");
        activityStore.setChannelName("111");
        activityStore.setOrgCode("111");
        activityStore.setStoreName("111");
        activityStore.setUrl("111");
        channelStores.add(activityStore);
        purchaseConstraintDomain.setChannelStores(channelStores);

        // 选择人员
        List<Qualification> qualifications = new ArrayList<>();
        Qualification qualification = new Qualification();
        List<String> qualificationValue = new ArrayList<>();
        qualificationValue.add("aaa");
        qualification.setQualificationValue(qualificationValue);
        List<String> qualificationName = new ArrayList<>();
        qualificationName.add("bb");
        qualification.setQualificationValueName(qualificationName);
        qualifications.add(qualification);
        purchaseConstraintDomain.setQualifications(qualifications);

        // product scope
        List<ProductScope> productScopes = new ArrayList<>();
        ProductScope productScope = new ProductScope();
        productScopes.add(productScope);
        purchaseConstraintDomain.setProducts(productScopes);


        // product detail
        List<ProductDetailInDTO> productDetailInDTOS = new ArrayList<>();
        ProductDetailInDTO productDetailInDTO = new ProductDetailInDTO();
        productDetailInDTOS.add(productDetailInDTO);
        purchaseConstraintDomain.setProductDetails(productDetailInDTOS);

        // product detail black list
        purchaseConstraintDomain.setProductDetailBlackList(productDetailInDTOS);

        // purchase Constraint Rules
        List<PurchaseConstraintRule> purchaseConstraintRules = new ArrayList<>();
        purchaseConstraintRules.add(new PurchaseConstraintRule());
        purchaseConstraintDomain.setPurchaseConstraintRules(purchaseConstraintRules);

        // 生成code
        String purchaseConstraintCode = "code1234";
        Mockito.when(codeGenerator.generateCode(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(purchaseConstraintCode);
        String result = purchaseConstraintComponent.createPurchaseConstraint(purchaseConstraintDomain);

        Assert.assertEquals(purchaseConstraintCode, result);
    }

    @Test
    public void findPurchaseConstraint() {
        FindPurchaseConstraintInDto findPurchaseConstraintInDto = new FindPurchaseConstraintInDto();
        findPurchaseConstraintInDto.setTenantCode("1111");
        findPurchaseConstraintInDto.setPurchaseConstraintCode("2222");
        findPurchaseConstraintInDto.setOrgCode("333");

        PurchaseConstraintModel purchaseConstraintModel = new PurchaseConstraintModel();
        purchaseConstraintModel.setTenantCode(findPurchaseConstraintInDto.getTenantCode());
        purchaseConstraintModel.setPurchaseConstraintCode(findPurchaseConstraintInDto.getPurchaseConstraintCode());
        purchaseConstraintModel.setStoreType(StoreParamTypeEnum.STORE_CUSTOM.code());

        Mockito.when(purchaseConstraintService.getPurchaseConstraint(Mockito.any(), Mockito.any())).thenReturn(purchaseConstraintModel);

        Mockito.when(activityComponentDomain.matchStore(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(Boolean.TRUE);

        List<TPromoActivityStoreVO> activityStoreList = new ArrayList<>();
        Mockito.when(activityStoreService.getStoresByActivityCode(Mockito.any())).thenReturn(activityStoreList);

        Mockito.when(qualificationService.queryQualifications(Mockito.any(), Mockito.any())).thenReturn(new ArrayList<>());


        FindPurchaseConstraintOutDto findPurchaseConstraintOutDto = purchaseConstraintComponent.findPurchaseConstraint(findPurchaseConstraintInDto);

        Assert.assertEquals(findPurchaseConstraintInDto.getTenantCode(), findPurchaseConstraintOutDto.getTenantCode());
    }

    @Test
    public void updatePurchaseConstraint() {
        UpdatePurchaseConstraintInDto updatePurchaseConstraintInDto = new UpdatePurchaseConstraintInDto();
        updatePurchaseConstraintInDto.setTenantCode("1111");
        updatePurchaseConstraintInDto.setDomainCode("11111");
        updatePurchaseConstraintInDto.setFirstRefusal(0);
        updatePurchaseConstraintInDto.setPeriodType("00");
        updatePurchaseConstraintInDto.setPurchaseConstraintName("test");
        updatePurchaseConstraintInDto.setOrgCode("test");
        updatePurchaseConstraintInDto.setPurchaseConstraintCode("testCode");
        List<ProductDetailInDTO> productDetails = new ArrayList<>();
        productDetails.add(new ProductDetailInDTO());
        updatePurchaseConstraintInDto.setProductDetails(productDetails);
        updatePurchaseConstraintInDto.setProductDetailBlackList(productDetails);
        updatePurchaseConstraintInDto.setProducts(Arrays.asList(new ProductScope()));
        updatePurchaseConstraintInDto.setSkuToken("123");

        PurchaseConstraintModel purchaseConstraintModel = new PurchaseConstraintModel();
        Mockito.when(purchaseConstraintService.getPurchaseConstraint(Mockito.any(), Mockito.any())).thenReturn(purchaseConstraintModel);

        Mockito.when(purchaseConstraintService.updatePurchaseConstraintById(Mockito.any())).thenReturn(1);


        purchaseConstraintComponent.updatePurchaseConstraint(updatePurchaseConstraintInDto);
        Assert.assertTrue(true);
    }

    @Test
    public void updatePurchaseConstraintPriority() {
        UpdatePurchaseConstraintPriorityInDto updatePurchaseConstraintInDto = new UpdatePurchaseConstraintPriorityInDto();
        updatePurchaseConstraintInDto.setTenantCode("111");
        List<PurchaseConstraintPriority> purchaseConstraintPriorities = new ArrayList<>();
        PurchaseConstraintPriority purchaseConstraintPriority = new PurchaseConstraintPriority();
        purchaseConstraintPriority.setPurchaseConstraintCode("1111");
        purchaseConstraintPriority.setPurchaseConstraintPriority(1);
        purchaseConstraintPriorities.add(purchaseConstraintPriority);
        updatePurchaseConstraintInDto.setPurchaseConstraintPriorityList(purchaseConstraintPriorities);

        Mockito.when(purchaseConstraintService.updatePurchaseConstraintPriority
                (Mockito.any(), Mockito.any())).thenReturn(purchaseConstraintPriorities.size());

        int result = purchaseConstraintComponent.updatePurchaseConstraintPriority(updatePurchaseConstraintInDto);

        Assert.assertEquals(purchaseConstraintPriorities.size(), result);
    }

    @Test
    public void expirePurchaseConstraint() {
        String tenantCode = "11";
        String purchaseConstraintCode = "22";

        purchaseConstraintComponent.expirePurchaseConstraint(tenantCode, purchaseConstraintCode);
        Assert.assertTrue(true);
    }

    @Test
    public void isEffectivePurchase() {
        Boolean result = purchaseConstraintComponent.isEffectivePurchase(null, null);
        Assert.assertFalse(result);

        PurchaseConstraintModel purchaseConstraintModel = new PurchaseConstraintModel();
        purchaseConstraintModel.setTenantCode("100000");
        purchaseConstraintModel.setPurchaseConstraintCode("1234");
        Date date = new Date();
        result = purchaseConstraintComponent.isEffectivePurchase(purchaseConstraintModel, date);
        Assert.assertFalse(result);

        purchaseConstraintModel.setPurchaseConstraintStatus(PurchaseConstraintStatusEnum.EFFECTIVE.getCode());
        purchaseConstraintModel.setPurchaseConstraintStartTime(DateUtil.addMinutes(date, -100));
        purchaseConstraintModel.setPurchaseConstraintEndTime(DateUtil.addMinutes(date, -50));

        result = purchaseConstraintComponent.isEffectivePurchase(purchaseConstraintModel, date);
        Assert.assertFalse(result);
    }

    @SneakyThrows
    @Test
    public void checkPurchaseConstraint() {
        String tenantCode = "tenantCode:001";
        Mockito.when(redissonClient.getLock(Mockito.any())).thenReturn(rLock);
        Mockito.doReturn(true).when(rLock).tryLock(5000L, 20000L, TimeUnit.MILLISECONDS);
        Mockito.when(activityCacheDomain.attributeAllPredicate()).thenReturn(tPromoActivityProductVO -> false);
        Mockito.when(activityCacheDomain.attributePredicate(Mockito.any())).thenReturn(tPromoActivityProductVO -> true);
        Mockito.when(activityCacheDomain.attributeNegatePredicate(Mockito.any())).thenReturn(tPromoActivityProductVO -> true);
        // 会员标签资格
        Qualification qualification = new Qualification();
        qualification.setQualificationCode(Constants.MEMBER_TAG_CODE);
        qualification.setQualificationValueName(Lists.newArrayList("tagName:001", "tagName:002"));
        qualification.setQualificationValue(Lists.newArrayList("tagCode:001", "tagCode:002"));
        List<Qualification> qualifications = Lists.newArrayList(qualification);
        // mock 限购
        List<PurchaseConstraintCacheDTO> purchaseConstraintCacheDtos = this.getPurchaseConstraintCacheDtos(tenantCode, qualifications);

        List<String> productCodes = Lists.newArrayList("productCode:001", "productCode:002");
        List<String> skuCodes = Lists.newArrayList("skuCode:001", "skuCode:002");
        List<CheckPurchaseConstraintProductDto> products = productCodes.stream()
                .flatMap(productCode -> skuCodes.stream().map(skuCode -> {
                    CheckPurchaseConstraintProductDto checkPurchaseConstraintProductDto = new CheckPurchaseConstraintProductDto();
                    checkPurchaseConstraintProductDto.setQuantity(99);
                    checkPurchaseConstraintProductDto.setPromotionAmount(new BigDecimal("99"));
                    checkPurchaseConstraintProductDto.setProductCode(productCode);
                    checkPurchaseConstraintProductDto.setSkuCode(productCode + skuCode);
                    checkPurchaseConstraintProductDto.setProductTag("001");
                    checkPurchaseConstraintProductDto.setSelected(1);
                    checkPurchaseConstraintProductDto.setPriceSetting(0);
                    checkPurchaseConstraintProductDto.setCategoryCodes(Lists.newArrayList("001"));
                    return checkPurchaseConstraintProductDto;
                })).collect(Collectors.toList());


        CheckPurchaseConstraintInDto purchaseConstraintInDto = new CheckPurchaseConstraintInDto();
        purchaseConstraintInDto.setUseIncrement(true);
        purchaseConstraintInDto.setTenantCode(tenantCode);
        purchaseConstraintInDto.setCheckPurchaseConstraintProducts(products);
        purchaseConstraintInDto.setQualifications(qualifications.stream().collect(Collectors.toMap(Qualification::getQualificationCode, Qualification::getQualificationValue)));

        // 未命中限购
        CheckPurchaseConstraintResult notHitPc = purchaseConstraintComponent.checkPurchaseConstraint(purchaseConstraintInDto);
        log.info("checkPurchaseConstraint: 未命中限购:{}", notHitPc);
        Assert.assertTrue(notHitPc.getCanBuy());
        // 清空本地缓存, 否则不会加载mock数据
        PurchaseConstraintComponent.LOCAL_CATCH.invalidateAll();
        Mockito.when(masterDataFeignClientComponent.masterDataValue(Mockito.any(), Mockito.any())).thenReturn("400");

        // 命中默认限购|可购买
        CheckPurchaseConstraintResult hitDefaultPc = purchaseConstraintComponent.checkPurchaseConstraint(purchaseConstraintInDto);
        log.info("checkPurchaseConstraint: 命中默认限购:{}", hitDefaultPc);
        // 命中限购|可购买
        for (PurchaseConstraintCacheDTO purchaseConstraintCacheDto : purchaseConstraintCacheDtos) {
            Mockito.when(purchaseConstraintCacheComponent.queryValidPcFromCache(Mockito.any())).thenReturn(Lists.newArrayList(purchaseConstraintCacheDto));
            CheckPurchaseConstraintResult hitPc = purchaseConstraintComponent.checkPurchaseConstraint(purchaseConstraintInDto);
            log.info("checkPurchaseConstraint: 命中限购:{}", hitPc);
            Assert.assertTrue(hitPc.getCanBuy());
        }
        // 开启优先购买|校验不通过
        purchaseConstraintCacheDtos.stream()
                .filter(purchaseConstraintCacheDto -> purchaseConstraintCacheDto.getFirstRefusal().equals(FirstRefusalEnum.YES.getCode()) && ProductSelectionEnum.SELECT.equalsCode(purchaseConstraintCacheDto.getProductSelectionType()))
                .forEach(purchaseConstraintCacheDto -> {
                    CheckPurchaseConstraintInDto hitFirstRefusalInput = BeanCopyUtils.jsonCopyBean(purchaseConstraintInDto, CheckPurchaseConstraintInDto.class);
                    hitFirstRefusalInput.setQualifications(null);
                    purchaseConstraintCacheDto.setFirstRefusal(FirstRefusalEnum.YES.getCode());
                    Mockito.when(purchaseConstraintCacheComponent.queryValidPcFromCache(Mockito.any())).thenReturn(Lists.newArrayList(purchaseConstraintCacheDto));

                    Map<String, PurchaseConstraintCacheDTO> pcMap = new HashMap<>();
                    pcMap.put(purchaseConstraintCacheDto.getPurchaseConstraintCode(), purchaseConstraintCacheDto);
                    Mockito.when(customerConditionHelper.filterCacheByCustomCondition(Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any())).thenAnswer(invocation -> {
                        // 这里强转类型，由于Java泛型的擦除，我们只能在运行时确保传入的参数类型正确
                        @SuppressWarnings("unchecked")
                        Map<String, PurchaseConstraintCacheDTO> cacheMap = (Map<String, PurchaseConstraintCacheDTO>) invocation.getArgument(1);
                        return pcMap; // 返回预设的map
                    });
                    CheckPurchaseConstraintResult hitFirstRefusal = purchaseConstraintComponent.checkPurchaseConstraint(hitFirstRefusalInput);
                    log.info("checkPurchaseConstraint: 命中限购,开启优先:{}", hitFirstRefusal);
                    Assert.assertFalse(hitFirstRefusal.getCanBuy());
                });

        // 开启自定义规则|校验不通过
        purchaseConstraintCacheDtos.stream()
                .filter(purchaseConstraintCacheDto -> purchaseConstraintCacheDto.getFirstRefusal().equals(FirstRefusalEnum.NO.getCode()) && ProductSelectionEnum.SELECT.equalsCode(purchaseConstraintCacheDto.getProductSelectionType()))
                .forEach(purchaseConstraintCacheDto -> {
                    CheckPurchaseConstraintInDto hitFirstRefusalInput = BeanCopyUtils.jsonCopyBean(purchaseConstraintInDto, CheckPurchaseConstraintInDto.class);
                    HashMap<String, String> customMap = new HashMap<>();
                    customMap.put("customKey", "customValue");
                    hitFirstRefusalInput.setCustomMap(customMap);
                    purchaseConstraintCacheDto.setCustomRule("[{\"customKey\": \"customKey\", \"customValue\": \"[\"customValue\"]\", \"customValueList\": [\"customValue\"]}]");
                    Mockito.when(purchaseConstraintCacheComponent.queryValidPcFromCache(Mockito.any())).thenReturn(Lists.newArrayList(purchaseConstraintCacheDto));
                    Map<String, PurchaseConstraintCacheDTO> pcMap = new HashMap<>();
                    pcMap.put(purchaseConstraintCacheDto.getPurchaseConstraintCode(), purchaseConstraintCacheDto);
                    Mockito.when(customerConditionHelper.filterCacheByCustomCondition(Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any())).thenAnswer(invocation -> {
                        // 这里强转类型，由于Java泛型的擦除，我们只能在运行时确保传入的参数类型正确
                        @SuppressWarnings("unchecked")
                        Map<String, PurchaseConstraintCacheDTO> cacheMap = (Map<String, PurchaseConstraintCacheDTO>) invocation.getArgument(1);
                        return pcMap; // 返回预设的map
                    });

                    Mockito.when(customerConditionHelper.filterCacheByCustomCondition(Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any())).thenAnswer(invocation -> {
                        // 这里强转类型，由于Java泛型的擦除，我们只能在运行时确保传入的参数类型正确
                        @SuppressWarnings("unchecked")
                        Map<String, PurchaseConstraintCacheDTO> cacheMap = (Map<String, PurchaseConstraintCacheDTO>) invocation.getArgument(1);
                        return new HashMap<>(); // 返回预设的map
                    });
                    CheckPurchaseConstraintResult hitFirstRefusal = purchaseConstraintComponent.checkPurchaseConstraint(hitFirstRefusalInput);
                    log.info("checkPurchaseConstraint: 命中限购,开启自定义规则:{}", hitFirstRefusal);
                    Assert.assertFalse(hitFirstRefusal.getCanBuy());
                });


        products = productCodes.stream()
                .flatMap(productCode -> skuCodes.stream().map(skuCode -> {
                    CheckPurchaseConstraintProductDto checkPurchaseConstraintProductDto = new CheckPurchaseConstraintProductDto();
                    checkPurchaseConstraintProductDto.setQuantity(999);
                    checkPurchaseConstraintProductDto.setPromotionAmount(new BigDecimal("999"));
                    checkPurchaseConstraintProductDto.setProductCode(productCode);
                    checkPurchaseConstraintProductDto.setSkuCode(productCode + skuCode);
                    checkPurchaseConstraintProductDto.setProductTag("001");
                    checkPurchaseConstraintProductDto.setPriceSetting(0);
                    checkPurchaseConstraintProductDto.setCategoryCodes(Lists.newArrayList("001"));
                    return checkPurchaseConstraintProductDto;
                })).collect(Collectors.toList());
        purchaseConstraintInDto.setCheckPurchaseConstraintProducts(products);
        // 命中限购|触发限购
        for (PurchaseConstraintCacheDTO purchaseConstraintCacheDto : purchaseConstraintCacheDtos) {
            Mockito.when(purchaseConstraintCacheComponent.queryValidPcFromCache(Mockito.any())).thenReturn(Lists.newArrayList(purchaseConstraintCacheDto));
            CheckPurchaseConstraintResult triggerPc = purchaseConstraintComponent.checkPurchaseConstraint(purchaseConstraintInDto);
            log.info("checkPurchaseConstraint: 触发限购:{}", triggerPc);
        }
        // 命中限购|不通过
        for (PurchaseConstraintCacheDTO purchaseConstraintCacheDto : purchaseConstraintCacheDtos) {
            List<PurchaseConstraintRule> purchaseConstraintRuleList = purchaseConstraintCacheDto.getPurchaseConstraintRuleList();
            for (PurchaseConstraintRule purchaseConstraintRule : purchaseConstraintRuleList) {
                purchaseConstraintRule.setPurchaseConstraintValue("1");
            }

            Mockito.when(purchaseConstraintCacheComponent.queryValidPcFromCache(Mockito.any())).thenReturn(Lists.newArrayList(purchaseConstraintCacheDto));


            List<CheckPurchaseConstraintProductDto> products2 = productCodes.stream()
                    .flatMap(productCode -> skuCodes.stream().map(skuCode -> {
                        CheckPurchaseConstraintProductDto checkPurchaseConstraintProductDto = new CheckPurchaseConstraintProductDto();
                        checkPurchaseConstraintProductDto.setQuantity(999);
                        checkPurchaseConstraintProductDto.setPromotionAmount(new BigDecimal("999"));
                        checkPurchaseConstraintProductDto.setProductCode(productCode);
                        checkPurchaseConstraintProductDto.setSkuCode(productCode + skuCode);
                        checkPurchaseConstraintProductDto.setProductTag("001");
                        checkPurchaseConstraintProductDto.setSelected(1);
                        checkPurchaseConstraintProductDto.setPriceSetting(0);
                        checkPurchaseConstraintProductDto.setCategoryCodes(Lists.newArrayList("001"));
                        return checkPurchaseConstraintProductDto;
                    })).collect(Collectors.toList());


            CheckPurchaseConstraintInDto purchaseConstraintInDto2 = new CheckPurchaseConstraintInDto();
            purchaseConstraintInDto2.setUseIncrement(true);
            purchaseConstraintInDto2.setTenantCode(tenantCode);
            purchaseConstraintInDto2.setCheckPurchaseConstraintProducts(products2);
            purchaseConstraintInDto2.setQualifications(qualifications.stream().collect(Collectors.toMap(Qualification::getQualificationCode, Qualification::getQualificationValue)));


            CheckPurchaseConstraintResult triggerPc = purchaseConstraintComponent.checkPurchaseConstraint(purchaseConstraintInDto2);
            log.info("checkPurchaseConstraint: 触发限购:{}", triggerPc);
        }


    }

    @SneakyThrows
    @Test
    public void checkOrgCodes() {
        Assert.assertTrue(purchaseConstraintComponent.checkOrgCodes(new PurchaseConstraintCacheDTO(), new ArrayList<>()));

        String orgCode = "testOrg";
        List<ActivityStore> channelStores = new ArrayList<>();
        ActivityStore activityStore = new ActivityStore();
        activityStore.setOrgCode(orgCode);
        channelStores.add(activityStore);
        PurchaseConstraintCacheDTO purchaseConstraintCacheDTO = new PurchaseConstraintCacheDTO();
        purchaseConstraintCacheDTO.setChannelStores(channelStores);
        Assert.assertTrue(purchaseConstraintComponent.checkOrgCodes(purchaseConstraintCacheDTO, Arrays.asList(orgCode)));
        Assert.assertFalse(purchaseConstraintComponent.checkOrgCodes(purchaseConstraintCacheDTO, Arrays.asList("11")));
    }

    @Test
    public void calAvailableQty() {
        CalAvailableQtyInDto calAvailableQtyInDto = new CalAvailableQtyInDto();
        calAvailableQtyInDto.setDomainCode("1111");
        calAvailableQtyInDto.setTenantCode("22222");
        calAvailableQtyInDto.setMemberCode("11111");
        calAvailableQtyInDto.setOrgCode("1111");


        CalAvailableQtyProductInDto calAvailableQtyProduct = new CalAvailableQtyProductInDto();
        String productCode = "1234";
        calAvailableQtyProduct.setProductCode(productCode);
        String skuCode = "4567";
        calAvailableQtyProduct.setSkuCode(skuCode);
        calAvailableQtyInDto.setCalAvailableQtyProduct(calAvailableQtyProduct);

        // 不命中限购
        Mockito
                .when(purchaseConstraintCacheComponent.queryValidPcFromCache(Mockito.any()))
                .thenReturn(new ArrayList<>());

        List<CalAvailableQtyResult> calAvailableQtyResults = purchaseConstraintComponent.calAvailableQty(calAvailableQtyInDto);
        Assert.assertTrue(CalAvailableQtyResult.UN_LIMIT.equals(calAvailableQtyResults.get(0).getAvailableQty()));


        List<PurchaseConstraintCacheDTO> purchaseConstraintCacheDTOList = new ArrayList<>();
        PurchaseConstraintCacheDTO purchaseConstraintCacheDTO = new PurchaseConstraintCacheDTO();
        purchaseConstraintCacheDTO.setPurchaseConstraintCode("pcCode:001");
        purchaseConstraintCacheDTO.setProductSelectionType(ProductSelectionEnum.SELECT.code());
        List<ProductDetail> productDetails = new ArrayList<>();
        ProductDetail productDetail = new ProductDetail();
        productDetail.setProductCode(productCode);
        productDetail.setSkuCode(skuCode);
        productDetail.setSeqNum(1);
        productDetails.add(productDetail);
        purchaseConstraintCacheDTO.setProductDetails(productDetails);

        purchaseConstraintCacheDTOList.add(purchaseConstraintCacheDTO);
        Mockito
                .when(purchaseConstraintCacheComponent.queryValidPcFromCache(Mockito.any()))
                .thenReturn(purchaseConstraintCacheDTOList);
        Mockito.when(activityCacheDomain.attributeAllPredicate()).thenReturn(tPromoActivityProductVO -> true);
        Mockito.when(activityCacheDomain.attributePredicate(Mockito.any())).thenReturn(tPromoActivityProductVO -> true);
        Mockito.when(activityCacheDomain.attributeNegatePredicate(Mockito.any())).thenReturn(tPromoActivityProductVO -> true);
        calAvailableQtyResults = purchaseConstraintComponent.calAvailableQty(calAvailableQtyInDto);
        Assert.assertEquals(CalAvailableQtyResult.UN_LIMIT, calAvailableQtyResults.get(0).getAvailableQty());


        List<PurchaseConstraintRule> purchaseConstraintRuleList = new ArrayList<>();
        PurchaseConstraintRule purchaseConstraintRule1 = new PurchaseConstraintRule();
        purchaseConstraintRule1.setPurchaseConstraintRuleType(PurchaseConstraintRuleTypeEnum.ORDER_MAX_QTY_PER_SKU.getCode());
        purchaseConstraintRule1.setPurchaseConstraintValue("3");
        purchaseConstraintRuleList.add(purchaseConstraintRule1);

        PurchaseConstraintRule purchaseConstraintRule2 = new PurchaseConstraintRule();
        purchaseConstraintRule2.setPurchaseConstraintRuleType(PurchaseConstraintRuleTypeEnum.ORDER_MAX_QUANTITY.getCode());
        purchaseConstraintRule2.setPurchaseConstraintValue("2");
        purchaseConstraintRuleList.add(purchaseConstraintRule2);

        PurchaseConstraintRule purchaseConstraintRule3 = new PurchaseConstraintRule();
        purchaseConstraintRule3.setPurchaseConstraintRuleType(PurchaseConstraintRuleTypeEnum.CUSTOMER_MAX_QTY_PER_PRODUCT.getCode());
        purchaseConstraintRule3.setPurchaseConstraintRuleTimeType(PurchaseConstraintRuleTimeTypeEnum.WEEKLY.getCode());
        purchaseConstraintRule3.setPurchaseConstraintRuleTimeValue("1");
        purchaseConstraintRule3.setPurchaseConstraintValue("1");
        purchaseConstraintRuleList.add(purchaseConstraintRule3);

        PurchaseConstraintRule purchaseConstraintRule4 = new PurchaseConstraintRule();
        purchaseConstraintRule4.setPurchaseConstraintRuleType(PurchaseConstraintRuleTypeEnum.CUSTOMER_MAX_QTY_ALL_PRODUCTS.getCode());
        purchaseConstraintRule4.setPurchaseConstraintValue("0");
        purchaseConstraintRuleList.add(purchaseConstraintRule4);

        purchaseConstraintCacheDTO.setPurchaseConstraintRuleList(purchaseConstraintRuleList);

        List<MemberPurchaseStatisticsResp> memberPurchaseStatisticsResps = new ArrayList<>();
        MemberPurchaseStatisticsResp memberPurchaseStatisticsResp = new MemberPurchaseStatisticsResp();
        memberPurchaseStatisticsResp.setProductCode(productCode);
        memberPurchaseStatisticsResp.setBuyQty(0L);
        memberPurchaseStatisticsResp.setCancelQty(0L);
        memberPurchaseStatisticsResp.setReturnedQty(0L);
        memberPurchaseStatisticsResps.add(memberPurchaseStatisticsResp);
        JsonResult<List<MemberPurchaseStatisticsResp>> omsResult = JsonResult.success(memberPurchaseStatisticsResps);
//        Mockito.when(orderFeignClient.queryMemberPurchaseStatistics(Mockito.any())).thenReturn(omsResult);


        calAvailableQtyResults = purchaseConstraintComponent.calAvailableQty(calAvailableQtyInDto);
        Assert.assertEquals(new Integer(0), calAvailableQtyResults.get(0).getAvailableQty());

        omsResult = JsonResult.success();
//        Mockito.when(orderFeignClient.queryMemberPurchaseStatistics(Mockito.any())).thenReturn(omsResult);
        calAvailableQtyResults = purchaseConstraintComponent.calAvailableQty(calAvailableQtyInDto);
        Assert.assertEquals(new Integer(0), calAvailableQtyResults.get(0).getAvailableQty());


        List<Qualification> qualifications = new ArrayList<>();
        Qualification qualification = new Qualification();
        qualification.setQualificationCode("tagCode");
        qualification.setQualificationValue(Arrays.asList("customer"));
        qualifications.add(qualification);
        purchaseConstraintCacheDTO.setFirstRefusal(FirstRefusalEnum.YES.getCode());
        purchaseConstraintCacheDTO.setQualifications(qualifications);

        calAvailableQtyResults = purchaseConstraintComponent.calAvailableQty(calAvailableQtyInDto);
        Assert.assertEquals(CalAvailableQtyResult.NO_QTY, calAvailableQtyResults.get(0).getAvailableQty());

        purchaseConstraintCacheDTO.setQualifications(new ArrayList<>());
        purchaseConstraintCacheDTO.setFirstRefusal(FirstRefusalEnum.NO.getCode());
        purchaseConstraintCacheDTO.setProductDetailBlackList(productDetails);
        calAvailableQtyResults = purchaseConstraintComponent.calAvailableQty(calAvailableQtyInDto);
        Assert.assertEquals(CalAvailableQtyResult.UN_LIMIT, calAvailableQtyResults.get(0).getAvailableQty());
    }

    private PurchaseConstraintCacheDTO getPurchaseConstraintCacheDTO(String tenantCode,
                                                                     ProductSelectionTypeEnum selectValue,
                                                                     FirstRefusalEnum firstRefusalValue,
                                                                     PurchaseConstraintRuleTypeEnum ruleTypeValue,
                                                                     PurchaseConstraintRuleTimeTypeEnum timeTypeValue,
                                                                     PurchaseConstraintRule purchaseConstraintRule,
                                                                     List<Qualification> qualifications) {

        String pcCode = String.format("pcCode:selectType%s:firstRefusal%s:ruleType%s", selectValue.code(), firstRefusalValue.getCode(), ruleTypeValue.getCode());
        if (timeTypeValue != null) {
            purchaseConstraintRule.setPurchaseConstraintRuleTimeType(timeTypeValue.getCode());
            pcCode = String.format("pcCode:selectType%s:firstRefusal%s:ruleType%s:timeType%s", selectValue.code(), firstRefusalValue.getCode(), ruleTypeValue.getCode(), timeTypeValue.getCode());
        }
        log.info("\n限购编码:{};\n选择类型{};优先购买{};规则类型{};规则额度{};",
                pcCode,
                selectValue.desc(), firstRefusalValue.getDesc(), ruleTypeValue.getDesc(), ruleTypeValue.getDesc());
        ProductAttribute spuAttribute = new ProductAttribute();
        spuAttribute.setAttributeValues("001");

        ProductAttribute attribute = new ProductAttribute();
        attribute.setAttributeValues("002");

        ProductScope productScope = new ProductScope();
        productScope.setCategoryCode("001");
        productScope.setProductTag("001");
        productScope.setAttributes(Lists.newArrayList(attribute));
        productScope.setSpuAttributes(Lists.newArrayList(spuAttribute));

        ActivityPeriod activityPeriod = new ActivityPeriod();
        activityPeriod.setBeginPeriod("0 0 0 ? * * ");
        activityPeriod.setEndPeriod("59 59 23 ? * * ");


        PurchaseConstraintCacheDTO purchaseConstraintCacheDTO = new PurchaseConstraintCacheDTO();
        purchaseConstraintCacheDTO.setProducts(Lists.newArrayList(productScope));
        purchaseConstraintCacheDTO.setFirstRefusal(firstRefusalValue.getCode());
        purchaseConstraintCacheDTO.setPurchaseConstraintRuleList(Lists.newArrayList(purchaseConstraintRule));
        purchaseConstraintCacheDTO.setActivityPeriod(activityPeriod);
        purchaseConstraintCacheDTO.setProductSelectionType(selectValue.code());
        purchaseConstraintCacheDTO.setPurchaseConstraintCode(pcCode);
        purchaseConstraintCacheDTO.setPriority(1);
        purchaseConstraintCacheDTO.setPriceSetting(0);
        purchaseConstraintCacheDTO.setCreateTime(new Date());
        purchaseConstraintCacheDTO.setQualifications(qualifications);

        purchaseConstraintCacheDTO.setTenantCode(tenantCode);

        return purchaseConstraintCacheDTO;
    }

    private List<PurchaseConstraintCacheDTO> getPurchaseConstraintCacheDtos(String tenantCode, List<Qualification> qualifications) {
        List<PurchaseConstraintCacheDTO> list = new ArrayList<>();
        for (ProductSelectionTypeEnum selectValue : ProductSelectionTypeEnum.values()) {
            for (FirstRefusalEnum firstRefusalValue : FirstRefusalEnum.values()) {
                for (PurchaseConstraintRuleTypeEnum ruleTypeValue : PurchaseConstraintRuleTypeEnum.values()) {
                    String ruleValue = "400";
                    PurchaseConstraintRule purchaseConstraintRule = new PurchaseConstraintRule();
                    purchaseConstraintRule.setPurchaseConstraintRuleType(ruleTypeValue.getCode());
                    purchaseConstraintRule.setPurchaseConstraintValue(ruleValue);
                    switch (ruleTypeValue) {

                        case ORDER_MAX_QTY_PER_SKU:
                        case ORDER_MAX_QUANTITY:
                        case ORDER_MAX_AMOUNT_PER_SKU:
                        case ORDER_MAX_AMOUNT:
                            for (PurchaseConstraintRuleTimeTypeEnum timeTypeValue : PurchaseConstraintRuleTimeTypeEnum.values()) {
                                list.add(getPurchaseConstraintCacheDTO(tenantCode, selectValue, firstRefusalValue, ruleTypeValue, timeTypeValue, purchaseConstraintRule, qualifications));
                                switch (timeTypeValue) {
                                    case YEARLY:
                                        purchaseConstraintRule.setPurchaseConstraintRuleTimeValue("0801");
                                        break;
                                    case MONTHLY:
                                        purchaseConstraintRule.setPurchaseConstraintRuleTimeValue("08");
                                        break;
                                    case WEEKLY:
                                        purchaseConstraintRule.setPurchaseConstraintRuleTimeValue("1");
                                        break;
                                    default:
                                }
                            }
                            break;


                        case CUSTOMER_MAX_QTY_PER_PRODUCT:
                        case CUSTOMER_MAX_QTY_ALL_PRODUCTS:
                        case CUSTOMER_MAX_AMOUNT:
                            for (PurchaseConstraintRuleTimeTypeEnum timeTypeValue : PurchaseConstraintRuleTimeTypeEnum.values()) {
                                list.add(getPurchaseConstraintCacheDTO(tenantCode, selectValue, firstRefusalValue, ruleTypeValue, timeTypeValue, purchaseConstraintRule, qualifications));
                                switch (timeTypeValue) {
                                    case YEARLY:
                                        purchaseConstraintRule.setPurchaseConstraintRuleTimeValue("0801");
                                        break;
                                    case MONTHLY:
                                        purchaseConstraintRule.setPurchaseConstraintRuleTimeValue("08");
                                        break;
                                    case WEEKLY:
                                        purchaseConstraintRule.setPurchaseConstraintRuleTimeValue("1");
                                        break;
                                    default:
                                }
                            }
                            break;
                        default:
                            list.add(getPurchaseConstraintCacheDTO(tenantCode, selectValue, firstRefusalValue, ruleTypeValue, null, purchaseConstraintRule, qualifications));
                    }

                }

            }

        }

        log.info("getPurchaseConstraintCacheDtos: {}", JSON.toJSONString(list));
        return list;
    }


    @Test
    public void testCalAvailableQty() {
        // Arrange
        CalAvailableQtyInDto calAvailableQtyInDto = new CalAvailableQtyInDto();
        HashMap<String, List<ProductAttribute>> objectObjectHashMap = new HashMap<>();

        ProductAttribute productAttribute = new ProductAttribute();
        productAttribute.setAttributeCode("001");

        objectObjectHashMap.put("001", new ArrayList<>());

        calAvailableQtyInDto.setSkuAttributeMap(objectObjectHashMap);
        calAvailableQtyInDto.setIgnoreProductScopeFilter(false);
        CalAvailableQtyProductInDto calAvailableQtyProductInDto = new CalAvailableQtyProductInDto();
        calAvailableQtyProductInDto.setSkuCode("skuCode");
        calAvailableQtyProductInDto.setBrandCode("brandCode");
        calAvailableQtyProductInDto.setProductCode("productCode");
        calAvailableQtyInDto.setIgnoreProductScopeFilter(true);
        calAvailableQtyInDto.setCalAvailableQtyProduct(calAvailableQtyProductInDto);
        List<PurchaseConstraintCacheDTO> purchaseConstraintCacheDTOList = new ArrayList<>();
        Mockito.when(purchaseConstraintCacheComponent.queryValidPcFromCache(Mockito.any())).thenReturn(purchaseConstraintCacheDTOList);
        Mockito
                .when(purchaseConstraintCacheComponent.queryValidPcFromCache(Mockito.any()))
                .thenReturn(purchaseConstraintCacheDTOList);
        Mockito.when(activityCacheDomain.attributeAllPredicate()).thenReturn(tPromoActivityProductVO -> true);
        Mockito.when(activityCacheDomain.attributePredicate(Mockito.any())).thenReturn(tPromoActivityProductVO -> true);
        Mockito.when(activityCacheDomain.attributeNegatePredicate(Mockito.any())).thenReturn(tPromoActivityProductVO -> true);
        // Act
        List<CalAvailableQtyResult> resultList = purchaseConstraintComponent.calAvailableQty(calAvailableQtyInDto);


    }

    @Test
    public void testCheckRollupRules() {
        // Create test data
        CheckPurchaseConstraintInDto pcInDto = new CheckPurchaseConstraintInDto();
        CheckPurchaseConstraintProductDto productDto = new CheckPurchaseConstraintProductDto();
        productDto.setProductCode("productCode");
        productDto.setSkuCode("skuCode");
        productDto.setQuantity(1);
        productDto.setPromotionAmount(new BigDecimal("1"));
        productDto.setProductTag("productTag");
        productDto.setCategoryCodes(new ArrayList<>());
        productDto.setSelected(1);
        pcInDto.setCheckPurchaseConstraintProducts(Lists.newArrayList(productDto));

        Map<String, List<PurchaseConstraintCacheDTO>> skuPcListMap = new HashMap<>();
        Map<String, CheckPurchaseConstraintProductDto> skuProductMap = new HashMap<>();
        List<PurchaseConstraintCacheDTO> pcCacheDTOList = new ArrayList<>();
        Map<String, List<PurchaseConstraintCacheDTO>> skuHitPcMap = new HashMap<>();
        skuHitPcMap.put("skuCode", new ArrayList<>());
        Map<String,Map<Integer, String>> ruleTypeMap = new HashMap<>();

        // Add test data to maps

        // Create expected result
        CheckPurchaseConstraintResult expectedResult = new CheckPurchaseConstraintResult();

        // Mock pcRuleIncrement result
        PcRuleCheckResult pcRuleIncrementResult = new PcRuleCheckResult();
        pcRuleIncrementResult.setPcCode("expectedPcCode");
        PcRuleCheckResult mockPcRuleIncrementResult = Mockito.mock(PcRuleCheckResult.class);
        Result<PcRuleCheckResult> mockResult = Result.ok(mockPcRuleIncrementResult);
        Mockito.when(pcRuleComponent.pcRuleIncrement(Mockito.any(), Mockito.any())).thenReturn(mockResult);

        CheckPurchaseConstraintResult result = purchaseConstraintComponent.checkRollupRules(pcInDto, skuPcListMap, skuProductMap, pcCacheDTOList, skuHitPcMap, ruleTypeMap);


    }


    @Test
    public void testFilterPurchaseConstraintCacheByCustomCondition_shouldReturnEmptyList_whenActivityListIsEmpty() {
        // Arrange
        String tenantCode = "tenantCode";
        List<PurchaseConstraintCacheDTO> activityList = new ArrayList<>();
        Map<String, String> customMap = new HashMap<>();

        // Act
        List<PurchaseConstraintCacheDTO> result = purchaseConstraintComponent.filterPurchaseConstraintCacheByCustomCondition(tenantCode, activityList, customMap);

        // Assert
        Assert.assertEquals(0, result.size());
    }

    @Test
    public void testFilterPurchaseConstraintCacheByCustomCondition_shouldReturnSameActivityList_whenMasterResultDataIsNull() {
        // Arrange
        String tenantCode = "tenantCode";
        List<PurchaseConstraintCacheDTO> activityList = new ArrayList<>();
        Map<String, String> customMap = new HashMap<>();

        //Mockito.when(masterDataFeignClient.getValueValue(Mockito.any(), Mockito.any())).thenReturn(null);

        // Act
        List<PurchaseConstraintCacheDTO> result = purchaseConstraintComponent.filterPurchaseConstraintCacheByCustomCondition(tenantCode, activityList, customMap);

        // Assert
        Assert.assertEquals(0, result.size());
    }

    @Test
    public void testFilterPurchaseConstraintCacheByCustomCondition_shouldReturnSameActivityList_whenMasterResultDataIsNotTurnOn() {
        // Arrange
        String tenantCode = "tenantCode";
        List<PurchaseConstraintCacheDTO> activityList = new ArrayList<>();
        PurchaseConstraintCacheDTO dto = new PurchaseConstraintCacheDTO();
        dto.setCustomCondition("[{\"key\":\"value\"}]");
        activityList.add(dto);


        Map<String, String> customMap = new HashMap<>();
        customMap.put("key", "value");
        JsonResult<String> objectJsonResult = new JsonResult<>();
        objectJsonResult.setSuccess(true);
        objectJsonResult.setData("1");



        //Mockito.when(masterDataFeignClient.getValueValue(Mockito.any(), Mockito.any())).thenReturn(objectJsonResult);

        // Act
        List<PurchaseConstraintCacheDTO> result = purchaseConstraintComponent.filterPurchaseConstraintCacheByCustomCondition(tenantCode, activityList, customMap);

        // Assert
        Assert.assertEquals(0, result.size());
    }

    @Test
    public void testFilterPurchaseConstraintCacheByCustomCondition_shouldReturnSameActivityList_whenCustomConditionsIsEmpty() {
        // Arrange
        String tenantCode = "tenantCode";
        List<PurchaseConstraintCacheDTO> activityList = new ArrayList<>();
        Map<String, String> customMap = new HashMap<>();

        PurchaseConstraintCacheDTO activity = new PurchaseConstraintCacheDTO();
        activity.setCustomCondition("");

        activityList.add(activity);

        JsonResult<String> objectJsonResult = new JsonResult<>();
        objectJsonResult.setSuccess(true);
        objectJsonResult.setData("1");
//        Mockito.when(masterDataFeignClient.getValueValue(Mockito.any(), Mockito.any())).thenReturn(objectJsonResult);

        // Act
        List<PurchaseConstraintCacheDTO> result = purchaseConstraintComponent.filterPurchaseConstraintCacheByCustomCondition(tenantCode, activityList, customMap);

    }

    @Test
    public void testFilterPurchaseConstraintCacheByCustomCondition_shouldReturnFilteredActivityList_whenCustomConditionsIsNotEmptyAndCustomMapIsEmpty() {
        // Arrange
        String tenantCode = "tenantCode";
        List<PurchaseConstraintCacheDTO> activityList = new ArrayList<>();
        Map<String, String> customMap = new HashMap<>();

        PurchaseConstraintCacheDTO activity = new PurchaseConstraintCacheDTO();
        activity.setCustomCondition("[{\"key\":\"value\"}]");

        activityList.add(activity);
        JsonResult<String> objectJsonResult = new JsonResult<>();
        objectJsonResult.setSuccess(true);
        objectJsonResult.setData("1");
//        Mockito.when(masterDataFeignClient.getValueValue(Mockito.any(), Mockito.any())).thenReturn(objectJsonResult);

        // Act
        List<PurchaseConstraintCacheDTO> result = purchaseConstraintComponent.filterPurchaseConstraintCacheByCustomCondition(tenantCode, activityList, customMap);

        // Assert
        Assert.assertEquals(0, result.size());
    }

    @Test
    public void testFilterPurchaseConstraintCacheByCustomCondition_shouldReturnFilteredActivityList_whenCustomConditionsIsNotEmptyAndCustomMapIsNotEmpty() {
        // Arrange
        String tenantCode = "tenantCode";
        List<PurchaseConstraintCacheDTO> activityList = new ArrayList<>();
        Map<String, String> customMap = new HashMap<>();

        PurchaseConstraintCacheDTO activity = new PurchaseConstraintCacheDTO();
        activity.setCustomCondition("[{\"key\":\"value\"}]");

        activityList.add(activity);

        JsonResult<String> objectJsonResult = new JsonResult<>();
        objectJsonResult.setSuccess(true);
        objectJsonResult.setData("1");
//        Mockito.when(masterDataFeignClient.getValueValue(Mockito.any(), Mockito.any())).thenReturn(objectJsonResult);

        CustomCondition customCondition = new CustomCondition();
        customCondition.setCustomKey("key");
        customCondition.setCustomValue("value");
        //Mockito.when(purchaseConstraintComponent.convertCustomCondition(JSON.parseArray("[{\"key\":\"value\"}]"))).thenReturn(Lists.newArrayList(customCondition));

        // Act
        List<PurchaseConstraintCacheDTO> result = purchaseConstraintComponent.filterPurchaseConstraintCacheByCustomCondition(tenantCode, activityList, customMap);

        // Assert
        Assert.assertEquals(0, result.size());
    }


}
