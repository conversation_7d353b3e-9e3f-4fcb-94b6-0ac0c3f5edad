package com.gtech.promotion.component.marketing;

import com.gtech.commons.page.PageData;
import com.gtech.commons.redis.GTechCodeGenerator;
import com.gtech.commons.result.PageResult;
import com.gtech.promotion.dao.model.marketing.BaseModel;
import com.gtech.promotion.dao.model.marketing.TicketReleaseModel;
import com.gtech.promotion.domain.marketing.TicketReleaseDomain;
import com.gtech.promotion.domain.marketing.TicketSendDomain;
import com.gtech.promotion.dto.in.marketing.TicketSendOutDto;
import com.gtech.promotion.service.marketing.impl.TicketReleaseServiceImpl;
import com.gtech.promotion.service.marketing.impl.TicketServiceImpl;
import com.gtech.promotion.vo.result.marketing.TicketReleaseQueryResult;
import com.gtech.promotion.vo.result.marketing.TicketSendResult;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.List;

@RunWith(MockitoJUnitRunner.class)
public class TicketComponentTest {

    @InjectMocks
    private TicketComponent ticketComponent;

    @Mock
    private TicketReleaseServiceImpl ticketReleaseService;

    @Mock
    private TicketServiceImpl ticketService;

    @Mock
    private GTechCodeGenerator gTechCodeGenerator;

    @Test
    public void release(){
        TicketReleaseDomain ticketReleaseDomain = new TicketReleaseDomain();
        String releaseCode = "1";
        Mockito.when(gTechCodeGenerator.generateCode(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(releaseCode);
        Mockito.when(ticketReleaseService.insert(Mockito.any())).thenReturn(1);
        String release = ticketComponent.release(ticketReleaseDomain);
        Assert.assertEquals(releaseCode, release);
    }

    @Test
    public void queryRelease(){
        TicketReleaseDomain ticketReleaseDomain = new TicketReleaseDomain();
        PageData<TicketReleaseModel> pageData = new PageData<>();
        Mockito.when(ticketReleaseService.selectPageList(Mockito.any(), Mockito.any())).thenReturn(pageData);
        PageResult<TicketReleaseQueryResult> pageResult = ticketComponent.queryRelease(ticketReleaseDomain, null);
        Assert.assertEquals(pageData.getTotal(), pageResult.getData().getTotal());
    }

    @Test
    public void sendTicket(){
        TicketSendDomain ticketSendDomain = new TicketSendDomain();
        List<String> memberCodes = new ArrayList<>();
        memberCodes.add("1");
        ticketSendDomain.setMemberCodes(memberCodes);
        ticketSendDomain.setQuality(2);
        List<TicketSendOutDto> ticketSendOutDtos = new ArrayList<>();
        Mockito.when(ticketReleaseService.deductInventory(Mockito.any(), Mockito.eq(memberCodes.size() * ticketSendDomain.getQuality()))).thenReturn(ticketSendOutDtos);
        List<TicketSendResult> sendResults = ticketComponent.sendTicket(ticketSendDomain);
        Assert.assertEquals(ticketSendOutDtos.size(), sendResults.size());
    }

    @Test
    public void sendTicket1(){
        TicketSendDomain ticketSendDomain = new TicketSendDomain();
        List<String> memberCodes = new ArrayList<>();
        memberCodes.add("1");
        ticketSendDomain.setMemberCodes(memberCodes);
        ticketSendDomain.setQuality(2);
        List<TicketSendOutDto> outDtos = new ArrayList<>();
        TicketSendOutDto ticketSendOutDto = new TicketSendOutDto();
        ticketSendOutDto.setReleaseCode("1");
        ticketSendOutDto.setSendCount(1);
        outDtos.add(ticketSendOutDto);
        TicketSendOutDto ticketSendOutDto1 = new TicketSendOutDto();
        ticketSendOutDto1.setReleaseCode("2");
        ticketSendOutDto1.setSendCount(1);
        outDtos.add(ticketSendOutDto1);
        Mockito.when(ticketReleaseService.deductInventory(Mockito.any(BaseModel.class), Mockito.eq(memberCodes.size() * ticketSendDomain.getQuality())))
                .thenReturn(outDtos);
        Mockito.when(gTechCodeGenerator.generateCode(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any())).thenReturn("1");
        Mockito.when(ticketService.insert(Mockito.any())).thenReturn(1);
        List<TicketSendResult> sendResults = ticketComponent.sendTicket(ticketSendDomain);
        Assert.assertEquals(1, sendResults.size());
    }
}
