package com.gtech.promotion.component;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.gtech.promotion.component.config.feign.BasicClient;
import com.gtech.promotion.component.impl.feign.BasicClientDomainImpl;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Map;

@RunWith(MockitoJUnitRunner.class)
public class BasicClientDomainImplTest {

    @InjectMocks
    private BasicClientDomainImpl basicClientDomain;

    @Mock
    private BasicClient basicClient;

    @Test
    public void getLanguages_null(){
        JSONObject result = new JSONObject();
        Mockito.when(basicClient.getLanguages(Mockito.any(), Mockito.anyMap())).thenReturn(result);
        Map<String, String> languages = basicClientDomain.getLanguages("");
        Assert.assertEquals(0, languages.size());
    }

    @Test
    public void getLanguages_empty(){
        JSONObject result = new JSONObject();
        result.put("data", new JSONArray());
        Mockito.when(basicClient.getLanguages(Mockito.any(), Mockito.anyMap())).thenReturn(result);
        Map<String, String> languages = basicClientDomain.getLanguages("");
        Assert.assertEquals(0, languages.size());
    }

    @Test
    public void getLanguages(){
        JSONObject result = new JSONObject();
        JSONArray value = new JSONArray();
        JSONObject o1 = new JSONObject();
        o1.put("text", "");
        JSONObject o2 = new JSONObject();
        o2.put("text", "text");
        JSONObject o3 = new JSONObject();
        o3.put("text", "text");
        o3.put("value", "value");
        value.add(o1);
        value.add(o2);
        value.add(o3);
        result.put("data", value);
        Mockito.when(basicClient.getLanguages(Mockito.any(), Mockito.anyMap())).thenReturn(result);
        Map<String, String> languages = basicClientDomain.getLanguages("");
        Assert.assertEquals(1, languages.size());
    }
}
