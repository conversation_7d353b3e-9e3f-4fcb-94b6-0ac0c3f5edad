package com.gtech.promotion.component.activity;

import com.gtech.promotion.code.activity.FuncTypeEnum;
import com.gtech.promotion.code.activity.FunctionParamTypeEnum;
import com.gtech.promotion.dao.model.activity.ActivityFunctionParamRankModel;
import com.gtech.promotion.dao.model.activity.ActivityModel;
import com.gtech.promotion.dao.model.activity.FunctionParamModel;
import com.gtech.promotion.dto.cache.ActivityCacheDTO;
import com.gtech.promotion.dto.in.coupon.ConditionAndFace;
import com.gtech.promotion.exception.PromotionException;
import com.gtech.promotion.service.activity.ActivityFuncParamService;
import com.gtech.promotion.service.activity.ActivityFuncRankService;
import org.assertj.core.util.Lists;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import static org.mockito.Mockito.when;

/**
 * @Description :
 * <AUTHOR> gaojie.li
 * @Date : 2023/8/19 18:41
 */
@RunWith(MockitoJUnitRunner.class)
public class FunctionParamDomainTest {

    @InjectMocks
    private FunctionParamDomain functionParamDomain;

    @Mock
    private ActivityFuncRankService promoActivityFuncRankService;

    @Mock
    private ActivityFuncParamService promoActivityFuncParamService;

    @Test(expected = PromotionException.class)
    public void findConditionAndFaceByActivityCodes(){

        List<String> list = new ArrayList<>();
        list.add("1");

        Map<String, ConditionAndFace> conditionAndFaceByActivityCodes = functionParamDomain.findConditionAndFaceByActivityCodes("1", list);


        Assert.assertEquals(0,conditionAndFaceByActivityCodes.keySet().size());
    }

    @Test(expected = PromotionException.class)
    public void findConditionAndFaceByActivityCodes1(){

        List<String> list = new ArrayList<>();
        list.add("1");

        List<ActivityFunctionParamRankModel> rankList = new ArrayList<>();
        ActivityFunctionParamRankModel model1 = new ActivityFunctionParamRankModel();
        model1.setId("2");
        rankList.add(model1);

        ActivityFunctionParamRankModel model = new ActivityFunctionParamRankModel();
        model.setId("1");
        rankList.add(model);

        List<FunctionParamModel> params = new ArrayList<>();

        when(promoActivityFuncRankService.getRankListByActivityCodes(Mockito.any(),Mockito.any())).thenReturn(rankList);

        when(promoActivityFuncParamService.getRuleFuncParamListByRankId(Mockito.any())).thenReturn(params);

        Map<String, ConditionAndFace> conditionAndFaceByActivityCodes = functionParamDomain.findConditionAndFaceByActivityCodes("1", list);
        Assert.assertEquals(0,conditionAndFaceByActivityCodes.keySet().size());


    }

    @Test
    public void findConditionAndFaceByActivityCodes2(){

        List<String> list = new ArrayList<>();
        list.add("1");

        List<ActivityFunctionParamRankModel> rankList = new ArrayList<>();
        ActivityFunctionParamRankModel model1 = new ActivityFunctionParamRankModel();
        model1.setId("2");
        rankList.add(model1);

        ActivityFunctionParamRankModel model = new ActivityFunctionParamRankModel();
        model.setId("1");
        rankList.add(model);

        List<FunctionParamModel> params = new ArrayList<>();

        FunctionParamModel paramModel1 = new FunctionParamModel();
        paramModel1.setRankId("1");
        paramModel1.setParamValue("11");
        paramModel1.setParamUnit("01");
        params.add(paramModel1);


        FunctionParamModel paramModel = new FunctionParamModel();
        paramModel.setRankId("1");
        paramModel.setParamValue("11");
        paramModel.setParamUnit("01");
        paramModel.setFunctionType("03");
        paramModel.setParamType("02");
        params.add(paramModel);

        when(promoActivityFuncRankService.getRankListByActivityCodes(Mockito.any(),Mockito.any())).thenReturn(rankList);

        when(promoActivityFuncParamService.getRuleFuncParamListByRankId(Mockito.any())).thenReturn(params);

        Map<String, ConditionAndFace> conditionAndFaceByActivityCodes = functionParamDomain.findConditionAndFaceByActivityCodes("1", list);
        Assert.assertEquals(1,conditionAndFaceByActivityCodes.keySet().size());


    }

    @Test
    public void findConditionAndFaceByActivityCodes3(){

        List<String> list = new ArrayList<>();
        list.add("1");

        List<ActivityFunctionParamRankModel> rankList = new ArrayList<>();
        ActivityFunctionParamRankModel model1 = new ActivityFunctionParamRankModel();
        model1.setId("2");
        rankList.add(model1);

        ActivityFunctionParamRankModel model = new ActivityFunctionParamRankModel();
        model.setId("1");
        rankList.add(model);

        List<FunctionParamModel> params = new ArrayList<>();

        FunctionParamModel paramModel1 = new FunctionParamModel();
        paramModel1.setRankId("1");
        paramModel1.setParamValue("11");
        paramModel1.setParamUnit("01");
        params.add(paramModel1);


        FunctionParamModel paramModel = new FunctionParamModel();
        paramModel.setRankId("1");
        paramModel.setParamValue("11");
        paramModel.setParamUnit("01");
        paramModel.setFunctionType("04");
        paramModel.setParamType("02");
        paramModel.setFunctionCode("0402");
        params.add(paramModel);

        when(promoActivityFuncRankService.getRankListByActivityCodes(Mockito.any(),Mockito.any())).thenReturn(rankList);

        when(promoActivityFuncParamService.getRuleFuncParamListByRankId(Mockito.any())).thenReturn(params);

        Map<String, ConditionAndFace> conditionAndFaceByActivityCodes = functionParamDomain.findConditionAndFaceByActivityCodes("1", list);
        Assert.assertEquals(1,conditionAndFaceByActivityCodes.keySet().size());

    }

    @Test
    public void findConditionAndFaceByActivityCodes4(){

        List<String> list = new ArrayList<>();
        list.add("1");

        List<ActivityFunctionParamRankModel> rankList = new ArrayList<>();
        ActivityFunctionParamRankModel model1 = new ActivityFunctionParamRankModel();
        model1.setId("2");
        rankList.add(model1);

        ActivityFunctionParamRankModel model = new ActivityFunctionParamRankModel();
        model.setId("1");
        rankList.add(model);

        List<FunctionParamModel> params = new ArrayList<>();

        FunctionParamModel paramModel1 = new FunctionParamModel();
        paramModel1.setRankId("1");
        paramModel1.setParamValue("11");
        paramModel1.setParamUnit("01");
        params.add(paramModel1);


        FunctionParamModel paramModel = new FunctionParamModel();
        paramModel.setRankId("1");
        paramModel.setParamValue("11");
        paramModel.setParamUnit("01");
        paramModel.setFunctionType("04");
        paramModel.setParamType("02");
        paramModel.setFunctionCode("0401");
        params.add(paramModel);

        when(promoActivityFuncRankService.getRankListByActivityCodes(Mockito.any(),Mockito.any())).thenReturn(rankList);

        when(promoActivityFuncParamService.getRuleFuncParamListByRankId(Mockito.any())).thenReturn(params);

        Map<String, ConditionAndFace> conditionAndFaceByActivityCodes = functionParamDomain.findConditionAndFaceByActivityCodes("1", list);
        Assert.assertEquals(1,conditionAndFaceByActivityCodes.keySet().size());

    }



    @Test
    public void testFindConditionAndFaceByActivityCodesCache() {
        // Arrange
        ActivityCacheDTO activityCacheDTO1 = Mockito.mock(ActivityCacheDTO.class);
        ActivityCacheDTO activityCacheDTO2 = Mockito.mock(ActivityCacheDTO.class);
        ActivityModel activityModel1 = Mockito.mock(ActivityModel.class);
        ActivityModel activityModel2 = Mockito.mock(ActivityModel.class);

        when(activityCacheDTO1.getActivityModel()).thenReturn(activityModel1);
        when(activityCacheDTO2.getActivityModel()).thenReturn(activityModel2);
        when(activityModel1.getActivityCode()).thenReturn("ACTIVITY_1");
        when(activityModel2.getActivityCode()).thenReturn("ACTIVITY_2");

        List<FunctionParamModel> paramList1 = new ArrayList<>();
        FunctionParamModel paramModel1 = new FunctionParamModel();
        paramModel1.setParamType(FunctionParamTypeEnum.NUMBER.code());
        paramModel1.setFunctionType(FuncTypeEnum.PARAM.code());
        paramModel1.setParamValue("100");
        paramList1.add(paramModel1);

        List<FunctionParamModel> paramList2 = new ArrayList<>();
        FunctionParamModel paramModel2 = new FunctionParamModel();
        paramModel2.setParamType(FunctionParamTypeEnum.NUMBER.code());
        paramModel2.setFunctionType(FuncTypeEnum.INCENTIVE.code());
        paramModel2.setFunctionCode(FuncTypeEnum.IncentiveEnum.DISCOUNT.code());
        paramModel2.setParamValue("10");
        paramList2.add(paramModel2);

        when(activityCacheDTO1.getPromoFuncParams()).thenReturn(paramList1);
        when(activityCacheDTO2.getPromoFuncParams()).thenReturn(paramList2);

        functionParamDomain.findConditionAndFaceByActivityCodesCache(Lists.list(activityCacheDTO1, activityCacheDTO2));


    }


}
