package com.gtech.promotion.component.coupon;

import com.gtech.promotion.component.activity.ActivityComponentDomain;
import com.gtech.promotion.dto.in.activity.UpdateExternalActivityInDTO;
import com.gtech.promotion.service.activity.ActivityService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.Silent.class)
public class ActivityComponentDomainTest {

    @InjectMocks
    private ActivityComponentDomain activityComponentDomain;

    @Mock
    private ActivityService activityService;


    @Test
    public void updateExternalActivityId() {

        UpdateExternalActivityInDTO param = new UpdateExternalActivityInDTO();
        param.setDomainCode("100000");
        param.setTenantCode("100000");
        param.setOperateUser("100000");
        param.setActivityCode("1");
        param.setTenantCode("1");
        param.setExternalActivityId("!1");

        activityComponentDomain.updateExternalActivityId(param);

    }




}
