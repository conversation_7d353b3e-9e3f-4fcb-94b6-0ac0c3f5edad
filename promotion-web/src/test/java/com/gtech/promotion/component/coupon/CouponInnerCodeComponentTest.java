package com.gtech.promotion.component.coupon;

import com.github.pagehelper.PageInfo;
import com.gtech.basic.idm.web.vo.result.QueryUserResult;
import com.gtech.commons.exception.GTechBaseException;
import com.gtech.commons.page.PageData;
import com.gtech.commons.result.Result;
import com.gtech.promotion.code.coupon.CouponRuleTypeEnum;
import com.gtech.promotion.code.coupon.CouponTypeEnum;
import com.gtech.promotion.code.coupon.FaceUnitEnum;
import com.gtech.promotion.component.activity.ActivityComponentDomain;
import com.gtech.promotion.component.activity.ActivityStoreDomain;
import com.gtech.promotion.component.activity.FunctionParamDomain;
import com.gtech.promotion.dao.model.activity.ActivityFunctionParamRankModel;
import com.gtech.promotion.dao.model.activity.ActivityModel;
import com.gtech.promotion.dao.model.activity.FunctionParamModel;
import com.gtech.promotion.dao.model.activity.TemplateModel;
import com.gtech.promotion.dao.model.coupon.CouponInnerRelationVO;
import com.gtech.promotion.dao.model.coupon.CouponReleaseModel;
import com.gtech.promotion.dao.model.coupon.TPromoCouponCodeUserVO;
import com.gtech.promotion.dao.model.coupon.TPromoCouponInnerCodeVO;
import com.gtech.promotion.domain.coupon.CouponDomain;
import com.gtech.promotion.domain.coupon.CouponReleaseDomain;
import com.gtech.promotion.domain.coupon.CouponReleaseInventoryDomain;
import com.gtech.promotion.dto.in.activity.ManagementDataInDTO;
import com.gtech.promotion.dto.in.coupon.*;
import com.gtech.promotion.exception.PromotionException;
import com.gtech.promotion.helper.ActivityRedisHelpler;
import com.gtech.promotion.helper.RedisLock;
import com.gtech.promotion.page.RequestPage;
import com.gtech.promotion.service.activity.*;
import com.gtech.promotion.service.coupon.PromoCouponActivityService;
import com.gtech.promotion.service.coupon.PromoCouponCodeUserService;
import com.gtech.promotion.service.coupon.PromoCouponReleaseService;
import com.gtech.promotion.service.impl.coupon.PromoCouponCodeUserServiceImpl;
import com.gtech.promotion.service.impl.coupon.PromoCouponInnerCodeServiceImpl;
import com.gtech.promotion.vo.result.coupon.ExportCouponRelationResult;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

@RunWith(MockitoJUnitRunner.class)
public class CouponInnerCodeComponentTest {

    @InjectMocks
    private CouponInnerCodeComponent couponInnerCodeComponent;

    @Mock
    private PromoCouponInnerCodeServiceImpl promoCouponInnerCodeService;
    @Mock
    private PromoCouponCodeUserServiceImpl promoCouponCodeUserService;
    @Mock
    private PromoCouponCodeUserService promoCouponCodeUserServiceS;
    @Mock
    private CouponActivityComponent couponActivityDomain;
    @Mock
    private ActivityComponentDomain activityDomain;
    @Mock
    private FunctionParamDomain functionParamDomain;
    @Mock
    private ActivityStoreDomain activityStoreDomain;
    @Mock
    private TemplateService templateService;
    @Mock
    private ActivityFuncRankService promoActivityFuncRankService;
    @Mock
    private ActivityFuncParamService promoActivityFuncParamService;
    @Mock
    private ActivityPeriodService activityPeriodService;
    @Mock
    private RedisLock redisLock;
    @Mock
    private PromoCouponReleaseService promoCouponReleaseService;
    @Mock
    private ActivityRedisHelpler redisService;
    @Mock
    private PromoCouponActivityService promoCouponActivityService;
    @Mock
    private ActivityService activityService;
    @Mock
    private CouponInnerCodeComponent couponInnerCodeDomain;

    @Mock
    private CouponCodeUserComponent couponCodeUserComponent;


    @Test(expected = GTechBaseException.class)
    public void getCouponInfo(){
        TCouponQueryOrApplyDTO paramDto = new TCouponQueryOrApplyDTO();
        paramDto.setTenantCode("1");
        paramDto.setCouponCode("1");
        paramDto.setUserCode("1");
        paramDto.setLanguage("1");
        Mockito.when(promoCouponInnerCodeService.findCouponByCouponCode(Mockito.anyString(), Mockito.anyString())).thenReturn(null);
        CouponDomain couponInfo = couponInnerCodeComponent.getCouponInfo(paramDto);
    }

    @Test(expected = GTechBaseException.class)
    public void getCouponInfo_1(){
        TCouponQueryOrApplyDTO paramDto = new TCouponQueryOrApplyDTO();
        paramDto.setTenantCode("1");
        paramDto.setCouponCode("1");
        paramDto.setUserCode("1");
        paramDto.setLanguage("1");
        TPromoCouponInnerCodeVO couponInnerCode = new TPromoCouponInnerCodeVO();
        couponInnerCode.setTenantCode("1");
        couponInnerCode.setActivityCode("1");
        Mockito.when(promoCouponInnerCodeService.findCouponByCouponCode(Mockito.anyString(), Mockito.anyString())).thenReturn(couponInnerCode);
        Mockito.when(promoCouponCodeUserService.getUserCouponInfo(Mockito.anyString(), Mockito.anyString(), Mockito.anyString())).thenReturn(null);
        CouponDomain couponInfo = couponInnerCodeComponent.getCouponInfo(paramDto);
    }

    @Test(expected = GTechBaseException.class)
    public void getCouponInfo_2(){
        TCouponQueryOrApplyDTO paramDto = new TCouponQueryOrApplyDTO();
        paramDto.setTenantCode("1");
        paramDto.setCouponCode("1");
        paramDto.setUserCode("1");
        paramDto.setLanguage("1");
        TPromoCouponInnerCodeVO couponInnerCode = new TPromoCouponInnerCodeVO();
        couponInnerCode.setTenantCode("1");
        couponInnerCode.setActivityCode("1");
        TPromoCouponCodeUserVO couponCodeUser = new TPromoCouponCodeUserVO();
        Mockito.when(promoCouponInnerCodeService.findCouponByCouponCode(Mockito.anyString(), Mockito.anyString())).thenReturn(couponInnerCode);
        Mockito.when(promoCouponCodeUserService.getUserCouponInfo(Mockito.anyString(), Mockito.anyString(), Mockito.anyString())).thenReturn(couponCodeUser);
        Mockito.when(couponActivityDomain.findValidActivity(Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.any())).thenReturn(null);
        CouponDomain couponInfo = couponInnerCodeComponent.getCouponInfo(paramDto);
    }

    @Test(expected = GTechBaseException.class)
    public void getCouponInfo_3(){
        TCouponQueryOrApplyDTO paramDto = new TCouponQueryOrApplyDTO();
        paramDto.setTenantCode("1");
        paramDto.setCouponCode("1");
        paramDto.setUserCode("1");
        paramDto.setLanguage("1");
        TPromoCouponInnerCodeVO couponInnerCode = new TPromoCouponInnerCodeVO();
        couponInnerCode.setTenantCode("1");
        couponInnerCode.setActivityCode("1");
        TPromoCouponCodeUserVO couponCodeUser = new TPromoCouponCodeUserVO();
        ActivityModel couponActivityModel = new ActivityModel();

        Mockito.when(promoCouponInnerCodeService.findCouponByCouponCode(Mockito.anyString(), Mockito.anyString())).thenReturn(couponInnerCode);
        Mockito.when(promoCouponCodeUserService.getUserCouponInfo(Mockito.anyString(), Mockito.anyString(), Mockito.anyString())).thenReturn(couponCodeUser);
        Mockito.when(couponActivityDomain.findValidActivity(Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.any())).thenReturn(couponActivityModel);
        Mockito.when(activityDomain.isEffectiveActivity(Mockito.any(), Mockito.any())).thenReturn(false);
        CouponDomain couponInfo = couponInnerCodeComponent.getCouponInfo(paramDto);
    }

    @Test(expected = GTechBaseException.class)
    public void getCouponInfo_4_优惠码(){
        TCouponQueryOrApplyDTO paramDto = new TCouponQueryOrApplyDTO();
        paramDto.setTenantCode("1");
        paramDto.setCouponCode("1");
        paramDto.setUserCode("1");
        paramDto.setLanguage("1");
        TPromoCouponInnerCodeVO couponInnerCode = new TPromoCouponInnerCodeVO();
        couponInnerCode.setTenantCode("1");
        couponInnerCode.setActivityCode("1");
        TPromoCouponCodeUserVO couponCodeUser = new TPromoCouponCodeUserVO();
        ActivityModel couponActivityModel = new ActivityModel();
        couponActivityModel.setCouponType(CouponTypeEnum.PROMOTION_CODE.code());
        Mockito.when(promoCouponInnerCodeService.findCouponByCouponCode(Mockito.anyString(), Mockito.anyString())).thenReturn(couponInnerCode);
        Mockito.when(promoCouponCodeUserService.getUserCouponInfo(Mockito.anyString(), Mockito.anyString(), Mockito.anyString())).thenReturn(couponCodeUser);
        Mockito.when(couponActivityDomain.findValidActivity(Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.any())).thenReturn(couponActivityModel);
        Mockito.when(activityDomain.isEffectiveActivity(Mockito.any(), Mockito.any())).thenReturn(true);
        CouponDomain couponInfo = couponInnerCodeComponent.getCouponInfo(paramDto);
    }

    @Test(expected = PromotionException.class)
    public void getCouponInfo_5_store校验失败(){
        TCouponQueryOrApplyDTO paramDto = new TCouponQueryOrApplyDTO();
        paramDto.setTenantCode("1");
        paramDto.setCouponCode("1");
        paramDto.setUserCode("1");
        paramDto.setLanguage("1");
        TPromoCouponInnerCodeVO couponInnerCode = new TPromoCouponInnerCodeVO();
        couponInnerCode.setTenantCode("1");
        couponInnerCode.setActivityCode("1");
        TPromoCouponCodeUserVO couponCodeUser = new TPromoCouponCodeUserVO();
        couponCodeUser.setFaceUnit(FaceUnitEnum.DISCOUNT.code());
        ActivityModel couponActivityModel = new ActivityModel();
        couponActivityModel.setCouponType(CouponTypeEnum.PROMOTION_COUPON.code());
        ConditionAndFace findConditionAndFace = new ConditionAndFace();

        Mockito.when(promoCouponInnerCodeService.findCouponByCouponCode(Mockito.anyString(), Mockito.anyString())).thenReturn(couponInnerCode);
        Mockito.when(promoCouponCodeUserService.getUserCouponInfo(Mockito.anyString(), Mockito.anyString(), Mockito.anyString())).thenReturn(couponCodeUser);
        Mockito.when(couponActivityDomain.findValidActivity(Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.any())).thenReturn(couponActivityModel);
        Mockito.when(activityPeriodService.findPeriod(Mockito.any(), Mockito.any())).thenReturn(null);
        Mockito.when(activityDomain.isEffectiveActivity(Mockito.any(), Mockito.any())).thenReturn(true);
        Mockito.when(functionParamDomain.findConditionAndFaceByActivityCode(Mockito.any())).thenReturn(findConditionAndFace);
        Mockito.when(activityStoreDomain.checkStore111(Mockito.any(), Mockito.any())).thenReturn(false);
        CouponDomain couponInfo = couponInnerCodeComponent.getCouponInfo(paramDto);
    }

    @Test
    public void getCouponInfo_6_no_rank(){
        TCouponQueryOrApplyDTO paramDto = new TCouponQueryOrApplyDTO();
        paramDto.setTenantCode("1");
        paramDto.setCouponCode("1");
        paramDto.setUserCode("1");
        paramDto.setLanguage("1");
        TPromoCouponInnerCodeVO couponInnerCode = new TPromoCouponInnerCodeVO();
        couponInnerCode.setTenantCode("1");
        couponInnerCode.setActivityCode("1");
        TPromoCouponCodeUserVO couponCodeUser = new TPromoCouponCodeUserVO();
        couponCodeUser.setFaceUnit(FaceUnitEnum.DISCOUNT.code());
        ActivityModel couponActivityModel = new ActivityModel();
        couponActivityModel.setCouponType(CouponTypeEnum.PROMOTION_COUPON.code());
        ConditionAndFace findConditionAndFace = new ConditionAndFace();
        TemplateModel template = new TemplateModel();
        template.setTemplateCode("0101020103010401");

        Mockito.when(promoCouponInnerCodeService.findCouponByCouponCode(Mockito.anyString(), Mockito.anyString())).thenReturn(couponInnerCode);
        Mockito.when(promoCouponCodeUserService.getUserCouponInfo(Mockito.anyString(), Mockito.anyString(), Mockito.anyString())).thenReturn(couponCodeUser);
        Mockito.when(couponActivityDomain.findValidActivity(Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.any())).thenReturn(couponActivityModel);
        Mockito.when(activityDomain.isEffectiveActivity(Mockito.any(), Mockito.any())).thenReturn(true);
        Mockito.when(activityPeriodService.findPeriod(Mockito.any(), Mockito.any())).thenReturn(null);
        Mockito.when(functionParamDomain.findConditionAndFaceByActivityCode(Mockito.any())).thenReturn(findConditionAndFace);
        Mockito.when(activityStoreDomain.checkStore111(Mockito.any(), Mockito.any())).thenReturn(true);
        Mockito.when(templateService.getTemplateByCode(Mockito.any())).thenReturn(template);
        Mockito.when(promoActivityFuncRankService.getRankListByActivityCode(Mockito.any())).thenReturn(null);
        CouponDomain couponInfo = couponInnerCodeComponent.getCouponInfo(paramDto);
        Assert.assertEquals("01", couponInfo.getRewardType());
    }

    @Test
    public void getCouponInfo_7(){
        TCouponQueryOrApplyDTO paramDto = new TCouponQueryOrApplyDTO();
        paramDto.setTenantCode("1");
        paramDto.setCouponCode("1");
        paramDto.setUserCode("1");
        paramDto.setLanguage("1");
        TPromoCouponInnerCodeVO couponInnerCode = new TPromoCouponInnerCodeVO();
        couponInnerCode.setTenantCode("1");
        couponInnerCode.setActivityCode("1");
        TPromoCouponCodeUserVO couponCodeUser = new TPromoCouponCodeUserVO();
        couponCodeUser.setFaceUnit(FaceUnitEnum.DISCOUNT.code());
        ActivityModel couponActivityModel = new ActivityModel();
        couponActivityModel.setCouponType(CouponTypeEnum.PROMOTION_COUPON.code());
        ConditionAndFace findConditionAndFace = new ConditionAndFace();
        TemplateModel template = new TemplateModel();
        template.setTemplateCode("0101020103010401");
        List<ActivityFunctionParamRankModel> rankList = new ArrayList<>();
        ActivityFunctionParamRankModel rankModel = new ActivityFunctionParamRankModel();
        rankList.add(rankModel);
        FunctionParamModel param = new FunctionParamModel();
        param.setParamValue("1");

        Mockito.when(promoCouponInnerCodeService.findCouponByCouponCode(Mockito.anyString(), Mockito.anyString())).thenReturn(couponInnerCode);
        Mockito.when(promoCouponCodeUserService.getUserCouponInfo(Mockito.anyString(), Mockito.anyString(), Mockito.anyString())).thenReturn(couponCodeUser);
        Mockito.when(couponActivityDomain.findValidActivity(Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.any())).thenReturn(couponActivityModel);
        Mockito.when(activityDomain.isEffectiveActivity(Mockito.any(), Mockito.any())).thenReturn(true);
        Mockito.when(functionParamDomain.findConditionAndFaceByActivityCode(Mockito.any())).thenReturn(findConditionAndFace);
        Mockito.when(activityStoreDomain.checkStore111(Mockito.any(), Mockito.any())).thenReturn(true);
        Mockito.when(templateService.getTemplateByCode(Mockito.any())).thenReturn(template);
        Mockito.when(activityPeriodService.findPeriod(Mockito.any(), Mockito.any())).thenReturn(null);
        Mockito.when(promoActivityFuncRankService.getRankListByActivityCode(Mockito.any())).thenReturn(rankList);
        Mockito.when(promoActivityFuncRankService.getRankListByActivityCode(Mockito.any())).thenReturn(rankList);
        Mockito.when(promoActivityFuncParamService.getRuleFuncParamListByRankIdAndFunctionCode(Mockito.any(), Mockito.any())).thenReturn(param);
        CouponDomain couponInfo = couponInnerCodeComponent.getCouponInfo(paramDto);
        Assert.assertEquals("01", couponInfo.getRewardType());
    }



    @Test
    public void getCouponInfo_8_couponType2(){
        TCouponQueryOrApplyDTO paramDto = new TCouponQueryOrApplyDTO();
        paramDto.setTenantCode("1");
        paramDto.setCouponCode("1");
        paramDto.setUserCode("1");
        paramDto.setLanguage("1");
        TPromoCouponInnerCodeVO couponInnerCode = new TPromoCouponInnerCodeVO();
        couponInnerCode.setTenantCode("1");
        couponInnerCode.setActivityCode("1");
        couponInnerCode.setCouponType("02");
        TPromoCouponCodeUserVO couponCodeUser = new TPromoCouponCodeUserVO();
        couponCodeUser.setFaceUnit(FaceUnitEnum.DISCOUNT.code());
        ActivityModel couponActivityModel = new ActivityModel();
        couponActivityModel.setCouponType(CouponTypeEnum.PROMOTION_COUPON.code());
        ConditionAndFace findConditionAndFace = new ConditionAndFace();
        TemplateModel template = new TemplateModel();
        template.setTemplateCode("0101020103010401");
        List<ActivityFunctionParamRankModel> rankList = new ArrayList<>();
        ActivityFunctionParamRankModel rankModel = new ActivityFunctionParamRankModel();
        rankList.add(rankModel);
        FunctionParamModel param = new FunctionParamModel();
        param.setParamValue("1");

        Mockito.when(promoCouponInnerCodeService.findCouponByCouponCode(Mockito.anyString(), Mockito.anyString())).thenReturn(couponInnerCode);
//        Mockito.when(promoCouponCodeUserService.getUserCouponInfo(Mockito.anyString(), Mockito.anyString(), Mockito.anyString())).thenReturn(couponCodeUser);
        Mockito.when(couponActivityDomain.findValidActivity(Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.any())).thenReturn(couponActivityModel);
        Mockito.when(activityDomain.isEffectiveActivity(Mockito.any(), Mockito.any())).thenReturn(true);
//        Mockito.when(functionParamDomain.findConditionAndFaceByActivityCode(Mockito.any())).thenReturn(findConditionAndFace);
        Mockito.when(activityStoreDomain.checkStore111(Mockito.any(), Mockito.any())).thenReturn(true);
        Mockito.when(templateService.getTemplateByCode(Mockito.any())).thenReturn(template);
        Mockito.when(activityPeriodService.findPeriod(Mockito.any(), Mockito.any())).thenReturn(null);
        Mockito.when(promoActivityFuncRankService.getRankListByActivityCode(Mockito.any())).thenReturn(rankList);
        Mockito.when(promoActivityFuncRankService.getRankListByActivityCode(Mockito.any())).thenReturn(rankList);
        Mockito.when(promoActivityFuncParamService.getRuleFuncParamListByRankIdAndFunctionCode(Mockito.any(), Mockito.any())).thenReturn(param);
        CouponDomain couponInfo = couponInnerCodeComponent.getCouponInfo(paramDto);
        Assert.assertEquals("01", couponInfo.getRewardType());
    }

    @Test
    public void allocateCoupon_receiveCount0(){
        ArrayList<String> userCodes = new ArrayList<>();
        userCodes.add("123");

        ActivityModel activityModel = new ActivityModel();
        activityModel.setReserveInventory(1);
        activityModel.setTemplateCode("0202");
        activityModel.setActivityEnd("202209041111");
        activityModel.setTenantCode("123");
        activityModel.setUserLimitMax(5);


        CouponReleaseDomain couponReleaseDomain1 = new CouponReleaseDomain();
        couponReleaseDomain1.setTenantCode("123");
        couponReleaseDomain1.setActivityCode("123");
        couponReleaseDomain1.setCouponType("123");
        couponReleaseDomain1.setReleaseCode("123");
        couponReleaseDomain1.setReleaseStatus("123");
        couponReleaseDomain1.setReleaseQuantity(0);
        couponReleaseDomain1.setInventory(2);
        couponReleaseDomain1.setReceivedQuantity(0);
        couponReleaseDomain1.setUsedTotal(0);
        couponReleaseDomain1.setLocked(0);
        couponReleaseDomain1.setReleaseSource("213");
        couponReleaseDomain1.setReceiveStartTime("123");
        couponReleaseDomain1.setReceiveEndTime("123");
        couponReleaseDomain1.setValidDays(1);
        couponReleaseDomain1.setValidStartTime("123");
        couponReleaseDomain1.setValidEndTime("123");
        couponReleaseDomain1.setReleaseTime("123");
        couponReleaseDomain1.setReleaseType("123");
        couponReleaseDomain1.setCouponCodePrefix("123");
        couponReleaseDomain1.setTimeSameActivity("123");
        couponReleaseDomain1.setCouponRuleType(CouponRuleTypeEnum.DIGIT.code());

        CouponReleaseDomain couponReleaseDomain2 = new CouponReleaseDomain();
        couponReleaseDomain2.setActivityCode("123");
        couponReleaseDomain2.setCouponType("123");
        couponReleaseDomain2.setReleaseCode("123");
        couponReleaseDomain2.setReleaseStatus("123");
        couponReleaseDomain2.setReleaseQuantity(0);
        couponReleaseDomain2.setTenantCode("123");
        couponReleaseDomain2.setInventory(2);
        couponReleaseDomain2.setReceivedQuantity(0);
        couponReleaseDomain2.setUsedTotal(0);
        couponReleaseDomain2.setLocked(0);
        couponReleaseDomain2.setReleaseSource("213");
        couponReleaseDomain2.setReceiveStartTime("123");
        couponReleaseDomain2.setReceiveEndTime("123");
        couponReleaseDomain2.setValidDays(1);
        couponReleaseDomain2.setValidStartTime("123");
        couponReleaseDomain2.setValidEndTime("123");
        couponReleaseDomain2.setReleaseTime("123");
        couponReleaseDomain2.setReleaseType("123");
        couponReleaseDomain2.setCouponCodePrefix("123");
        couponReleaseDomain2.setTimeSameActivity("123");
        couponReleaseDomain2.setCouponRuleType(CouponRuleTypeEnum.DIGIT_LETTER.code());


        CouponReleaseDomain couponReleaseDomain = new CouponReleaseDomain();
        couponReleaseDomain.setTenantCode("123");
        couponReleaseDomain.setActivityCode("123");
        couponReleaseDomain.setCouponType("123");
        couponReleaseDomain.setReleaseCode("123");
        couponReleaseDomain.setReleaseStatus("123");
        couponReleaseDomain.setReleaseQuantity(0);
        couponReleaseDomain.setInventory(2);
        couponReleaseDomain.setReceivedQuantity(0);
        couponReleaseDomain.setUsedTotal(0);
        couponReleaseDomain.setLocked(0);
        couponReleaseDomain.setReleaseSource("213");
        couponReleaseDomain.setReceiveStartTime("123");
        couponReleaseDomain.setReceiveEndTime("123");
        couponReleaseDomain.setValidDays(1);
        couponReleaseDomain.setValidStartTime("123");
        couponReleaseDomain.setValidEndTime("123");
        couponReleaseDomain.setReleaseTime("123");
        couponReleaseDomain.setReleaseType("123");
        couponReleaseDomain.setCouponCodePrefix("123");
        couponReleaseDomain.setTimeSameActivity("123");
        couponReleaseDomain.setCouponRuleType(null);

        ArrayList<CouponReleaseDomain> relaseDomain = new ArrayList<>();
        relaseDomain.add(couponReleaseDomain);
        relaseDomain.add(couponReleaseDomain1);
        relaseDomain.add(couponReleaseDomain2);

        List<CouponReleaseInventoryDomain> releaseInventoryDomains = new ArrayList<>();

        CouponReleaseInventoryDomain couponReleaseInventoryDomain2 = new CouponReleaseInventoryDomain();
        couponReleaseInventoryDomain2.setReleaseSource("01");
        couponReleaseInventoryDomain2.setSendInventory(2);
        couponReleaseInventoryDomain2.setInventory(1);
        couponReleaseInventoryDomain2.setTenantCode("123123");
        couponReleaseInventoryDomain2.setReleaseCode("123");
        couponReleaseInventoryDomain2.setValidDays(1);
        couponReleaseInventoryDomain2.setCouponCodePrefix("1122");
        couponReleaseInventoryDomain2.setCouponCodeLength(6);

        couponReleaseInventoryDomain2.setCouponRuleType(CouponRuleTypeEnum.DIGIT.code());

        releaseInventoryDomains.add(couponReleaseInventoryDomain2);

        CouponReleaseInventoryDomain couponReleaseInventoryDomain1 = new CouponReleaseInventoryDomain();
        couponReleaseInventoryDomain1.setReleaseSource("01");
        couponReleaseInventoryDomain1.setSendInventory(2);
        couponReleaseInventoryDomain1.setInventory(1);
        couponReleaseInventoryDomain1.setTenantCode("123123");
        couponReleaseInventoryDomain1.setReleaseCode("123");
        couponReleaseInventoryDomain1.setValidDays(1);
        couponReleaseInventoryDomain1.setCouponCodePrefix("1122");
        couponReleaseInventoryDomain1.setCouponCodeLength(18);
        couponReleaseInventoryDomain1.setCouponRuleType(CouponRuleTypeEnum.DIGIT_LETTER.code());

        releaseInventoryDomains.add(couponReleaseInventoryDomain1);

        CouponReleaseInventoryDomain couponReleaseInventoryDomain = new CouponReleaseInventoryDomain();
        couponReleaseInventoryDomain.setReleaseSource("01");
        couponReleaseInventoryDomain.setSendInventory(2);
        couponReleaseInventoryDomain.setInventory(1);
        couponReleaseInventoryDomain.setTenantCode("123123");
        couponReleaseInventoryDomain.setReleaseCode("123");
        couponReleaseInventoryDomain.setValidDays(1);
        couponReleaseInventoryDomain.setCouponCodePrefix("1122");
        couponReleaseInventoryDomain.setCouponRuleType(null);
        couponReleaseInventoryDomain.setCouponCodeLength(10);

        releaseInventoryDomains.add(couponReleaseInventoryDomain);

        ConditionAndFace conditionAndFace = new ConditionAndFace();
        conditionAndFace.setConditionValue(new BigDecimal("2"));
        conditionAndFace.setConditionUnit("2");
        conditionAndFace.setFaceValue(new BigDecimal("2"));
        conditionAndFace.setFaceUnit("2");


        Mockito.when(redisLock.tryLockAndRetry(Mockito.anyString(),Mockito.any(),Mockito.anyInt())).thenReturn("key");
        Mockito.when(promoCouponReleaseService.queryCanReceiveReleases(Mockito.any(), Mockito.any(),Mockito.any())).thenReturn(relaseDomain);
        Mockito.when(promoCouponReleaseService.deductInventory(Mockito.any(), Mockito.any(),Mockito.any(),Mockito.anyInt())).thenReturn(releaseInventoryDomains);
        Mockito.when(functionParamDomain.findConditionAndFaceByActivityCode(Mockito.any())).thenReturn(conditionAndFace);
//        Mockito.when(redisService.getCouponUserCount111(Mockito.anyString(),Mockito.any(),Mockito.any())).thenReturn(1);
        couponInnerCodeComponent.allocateCoupon(userCodes,0,"takeLabel",activityModel,relaseDomain,1,null);
    }


    @Test
    public void findActivityCodeByCouponCode(){

        List<TPromoCouponInnerCodeVO> activityCodeByCouponCode = couponInnerCodeComponent.findActivityCodeByCouponCode("1", "1,2");
        Assert.assertEquals(0,activityCodeByCouponCode.size());
    }


    @Test
    public void queryActivityByCouponCodes(){

        List<TPromoCouponCodeUserVO> activityCodeByCouponCode = couponInnerCodeComponent.queryActivityByCouponCodes("1", Arrays.asList("1"));
        Assert.assertEquals(0,activityCodeByCouponCode.size());
    }

    @Test
    public void queryCouponListInfo_NO_03(){
        TCouponListQueryDTO queryDTO = new TCouponListQueryDTO();
        queryDTO.setTenantCode("111");
        queryDTO.setLanguage("");
        queryDTO.setActivityCode("12");
        queryDTO.setCreateTimeStart("20210428142101");
        queryDTO.setCreateTimeEnd("22220402010101");
        queryDTO.setCouponStatus("");
        queryDTO.setCouponCode("11");
        queryDTO.setReleaseCode("11");
        queryDTO.setPageNo(0);
        queryDTO.setPageCount(0);
        queryDTO.setOrgCode("");
        queryDTO.setChannelCode("");


        RequestPage page = new RequestPage();

        TPromoCouponCodeUserVO tPromoCouponCodeUserVO = new TPromoCouponCodeUserVO();
        tPromoCouponCodeUserVO.setTenantCode("123");
        tPromoCouponCodeUserVO.setActivityCode("123");

        ActivityModel activityModel = new ActivityModel();
        activityModel.setActivityCode("123");
        activityModel.setTenantCode("123");
        activityModel.setCouponType("02");

        TPromoCouponInnerCodeVO tPromoCouponInnerCodeVO = new TPromoCouponInnerCodeVO();
        tPromoCouponInnerCodeVO.setId("1");
        tPromoCouponInnerCodeVO.setCreateUser("1");
        tPromoCouponInnerCodeVO.setTenantCode("1");
        tPromoCouponInnerCodeVO.setActivityCode("1");
        tPromoCouponInnerCodeVO.setReleaseCode("1");
        tPromoCouponInnerCodeVO.setCouponCode("1");
        tPromoCouponInnerCodeVO.setCouponType("1");
        tPromoCouponInnerCodeVO.setStatus("1");
        tPromoCouponInnerCodeVO.setFrozenStatus("1");
        tPromoCouponInnerCodeVO.setFaceValue(new BigDecimal("0"));
        tPromoCouponInnerCodeVO.setFaceUnit("1");
        tPromoCouponInnerCodeVO.setTakeLabel("1");
        tPromoCouponInnerCodeVO.setReceiveStartTime("20210428142101");
        tPromoCouponInnerCodeVO.setReceiveEndTime("20210428142101");
        tPromoCouponInnerCodeVO.setCreateTime(new Date());
        tPromoCouponInnerCodeVO.setLogicDelete("1");


        ArrayList<TPromoCouponInnerCodeVO> tPromoCouponInnerCodeVOS = new ArrayList<>();
        tPromoCouponInnerCodeVOS.add(tPromoCouponInnerCodeVO);

        PageInfo<TPromoCouponInnerCodeVO> couponList =new PageInfo<>();
        couponList.setList(tPromoCouponInnerCodeVOS);

        TPromoCouponCodeUserVO couponInfo =new TPromoCouponCodeUserVO();
        couponInfo.setValidTime(new CouponReleaseDomain(),"");



        Mockito.when(promoCouponActivityService.findCouponActivity(Mockito.any(),Mockito.any())).thenReturn(activityModel);
        Mockito.when(activityStoreDomain.checkStore111(Mockito.any(),Mockito.any())).thenReturn(true);
        Mockito.when(promoCouponInnerCodeService.selectCouponCode(Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(couponList);
        Mockito.when(promoCouponCodeUserService.getUserCouponInfo(Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(couponInfo);

        couponInnerCodeComponent.queryCouponListInfo(queryDTO,page);
    }

    @Test
    public void queryCouponListInfo_03(){
        TCouponListQueryDTO queryDTO = new TCouponListQueryDTO();
        queryDTO.setTenantCode("111");
        queryDTO.setLanguage("");
        queryDTO.setActivityCode("12");
        queryDTO.setCreateTimeStart("20210428142101");
        queryDTO.setCreateTimeEnd("22220402010101");
        queryDTO.setCouponStatus("");
        queryDTO.setCouponCode("11");
        queryDTO.setReleaseCode("11");
        queryDTO.setPageNo(0);
        queryDTO.setPageCount(0);
        queryDTO.setOrgCode("");
        queryDTO.setChannelCode("");


        RequestPage page = new RequestPage();

        TPromoCouponCodeUserVO tPromoCouponCodeUserVO = new TPromoCouponCodeUserVO();
        tPromoCouponCodeUserVO.setTenantCode("123");
        tPromoCouponCodeUserVO.setActivityCode("123");

        ActivityModel activityModel = new ActivityModel();
        activityModel.setActivityCode("123");
        activityModel.setTenantCode("123");
        activityModel.setCouponType("03");

        TPromoCouponInnerCodeVO tPromoCouponInnerCodeVO = new TPromoCouponInnerCodeVO();
        tPromoCouponInnerCodeVO.setId("1");
        tPromoCouponInnerCodeVO.setCreateUser("1");
        tPromoCouponInnerCodeVO.setTenantCode("1");
        tPromoCouponInnerCodeVO.setActivityCode("1");
        tPromoCouponInnerCodeVO.setReleaseCode("1");
        tPromoCouponInnerCodeVO.setCouponCode("1");
        tPromoCouponInnerCodeVO.setCouponType("1");
        tPromoCouponInnerCodeVO.setStatus("1");
        tPromoCouponInnerCodeVO.setFrozenStatus("1");
        tPromoCouponInnerCodeVO.setFaceValue(new BigDecimal("0"));
        tPromoCouponInnerCodeVO.setFaceUnit("1");
        tPromoCouponInnerCodeVO.setTakeLabel("1");
        tPromoCouponInnerCodeVO.setReceiveStartTime("20210428142101");
        tPromoCouponInnerCodeVO.setReceiveEndTime("20210428142101");
        tPromoCouponInnerCodeVO.setCreateTime(new Date());
        tPromoCouponInnerCodeVO.setLogicDelete("1");


        ArrayList<TPromoCouponInnerCodeVO> tPromoCouponInnerCodeVOS = new ArrayList<>();
        tPromoCouponInnerCodeVOS.add(tPromoCouponInnerCodeVO);

        PageInfo<TPromoCouponInnerCodeVO> couponList =new PageInfo<>();
        couponList.setList(tPromoCouponInnerCodeVOS);

        TPromoCouponCodeUserVO couponInfo =new TPromoCouponCodeUserVO();
        couponInfo.setValidTime(new CouponReleaseDomain(),"");


        ArrayList<TPromoCouponCodeUserVO> tPromoCouponCodeUserVOS = new ArrayList<>();
        tPromoCouponCodeUserVOS.add(tPromoCouponCodeUserVO);

        PageInfo<TPromoCouponCodeUserVO> pageInfoCode = new PageInfo<>();
        pageInfoCode.setList(tPromoCouponCodeUserVOS);

        Mockito.when(promoCouponActivityService.findCouponActivity(Mockito.any(),Mockito.any())).thenReturn(activityModel);
        Mockito.when(activityStoreDomain.checkStore111(Mockito.any(),Mockito.any())).thenReturn(true);
        Mockito.when(promoCouponInnerCodeService.selectCouponCode(Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(couponList);
//        Mockito.when(promoCouponCodeUserService.getUserCouponInfo(Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(couponInfo);
        Mockito.when(promoCouponCodeUserService.getUserCouponCode(Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(pageInfoCode);

        couponInnerCodeComponent.queryCouponListInfo(queryDTO,page);
    }



    @Test
    public void useCoupon(){
        TCouponUseInDTO tCouponUseInDTO = new TCouponUseInDTO();

        TPromoCouponCodeUserVO tPromoCouponCodeUserVO = new TPromoCouponCodeUserVO();
        tPromoCouponCodeUserVO.setCouponType("01");

        List<TPromoCouponCodeUserVO> codeUser = new ArrayList<>();
        codeUser.add(tPromoCouponCodeUserVO);

        Mockito.when(promoCouponCodeUserService.getCodeUserByUsedRefId(Mockito.any(),Mockito.any())).thenReturn(codeUser);


        couponInnerCodeComponent.useCoupon(tCouponUseInDTO);

    }


    @Test
    public void receiveCouponCodeByOrder(){

        TPromoCouponInnerCodeVO innerCodeVO = new TPromoCouponInnerCodeVO();
        innerCodeVO.setCouponCode("1");
        innerCodeVO.setCouponType("02");
        innerCodeVO.setFrozenStatus("01");
        innerCodeVO.setTenantCode("123");
        innerCodeVO.setActivityCode("123");
        innerCodeVO.setReceiveEndTime("02230102151411");


        ActivityModel couponActivity = new ActivityModel();
        couponActivity.setCouponCode("123");
        couponActivity.setTotalQuantity(2);
        couponActivity.setActivityEnd("02230102151411");
        couponActivity.setUserLimitMax(20);

        CouponReleaseDomain release = new CouponReleaseDomain();

        release.setReceiveEndTime("30210428152501");
        release.setValidDays(2);


        Mockito.when(couponActivityDomain.findValidActivity(Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(couponActivity);
        Mockito.when(promoCouponReleaseService.findCouponReleaseByReleaseCode(Mockito.any(),Mockito.any())).thenReturn(release);
        Mockito.when(redisService.getCouponUserCount111(Mockito.anyString(),Mockito.any(),Mockito.any())).thenReturn(1);
        couponInnerCodeComponent.receiveCouponCodeByOrder(innerCodeVO,"orderId","takeLabel","userCode");

    }


    @Test
    public void receiveCouponCodeByOrder_03(){

        TPromoCouponInnerCodeVO innerCodeVO = new TPromoCouponInnerCodeVO();
        innerCodeVO.setCouponCode("1");
        innerCodeVO.setCouponType("03");
        innerCodeVO.setFrozenStatus("01");
        innerCodeVO.setTenantCode("123");
        innerCodeVO.setActivityCode("123");
        innerCodeVO.setReceiveEndTime("02230102151411");


        ActivityModel couponActivity = new ActivityModel();
        couponActivity.setCouponCode("123");
        couponActivity.setTotalQuantity(2);
        couponActivity.setActivityEnd("02230102151411");
        couponActivity.setUserLimitMax(20);

        CouponReleaseDomain release = new CouponReleaseDomain();

        release.setReceiveEndTime("30210428152501");
        release.setValidDays(2);


        Mockito.when(couponActivityDomain.findValidActivity(Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(couponActivity);
        Mockito.when(promoCouponReleaseService.findCouponReleaseByReleaseCode(Mockito.any(),Mockito.any())).thenReturn(release);
        Mockito.when(redisService.getCouponUserCount111(Mockito.anyString(),Mockito.any(),Mockito.any())).thenReturn(1);
        couponInnerCodeComponent.receiveCouponCodeByOrder(innerCodeVO,"orderId","takeLabel","userCode");

    }


    @Test
    public void queryManagementUserCouponCodeData(){

        ManagementDataInDTO dataInDTO = new ManagementDataInDTO();

        ActivityModel activityModel = new ActivityModel();
        activityModel.setCouponCode("12312");


        TPromoCouponCodeUserVO tPromoCouponCodeUserVO = new TPromoCouponCodeUserVO();
        tPromoCouponCodeUserVO.setValidTime(new CouponReleaseDomain(),"");
        tPromoCouponCodeUserVO.setTenantCode("123213");
        tPromoCouponCodeUserVO.setActivityCode("123");

        ArrayList<TPromoCouponCodeUserVO> tPromoCouponCodeUserVOS = new ArrayList<>();
        tPromoCouponCodeUserVOS.add(tPromoCouponCodeUserVO);
        PageData<TPromoCouponCodeUserVO> pageInfo = new PageData<>();
        pageInfo.setList(tPromoCouponCodeUserVOS);
        pageInfo.setTotal(20L);

        CouponReleaseDomain couponReleaseDomain = new CouponReleaseDomain();

        Mockito.when(promoCouponCodeUserService.queryManagementUserData(Mockito.any())).thenReturn(pageInfo);
        Mockito.when(activityService.findActivityByActivityCode(Mockito.any(),Mockito.any())).thenReturn(activityModel);
        Mockito.when(promoCouponReleaseService.findCouponReleaseByReleaseCode(Mockito.any(),Mockito.any())).thenReturn(couponReleaseDomain);

        couponInnerCodeComponent.queryManagementUserCouponCodeData(dataInDTO,activityModel);


    }



    @Test
    public void getPageInfo(){
//        ActivityModel tPromoActivityVO = new ActivityModel();

//        Mockito.when(activityService.findActivity(Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(tPromoActivityVO);

        ManagementDataInDTO dataInDTO = new ManagementDataInDTO();
        dataInDTO.setCouponType("01");
        dataInDTO.setStatus("01");
        dataInDTO.setUserCode("123");
        dataInDTO.setReleaseStartTime("123");
//        Mockito.when(couponInnerCodeDomain.queryManagementUserCouponCodeData(Mockito.any(),Mockito.any())).thenReturn(new PageData<>());

        couponInnerCodeComponent.getPageInfo(dataInDTO);

    }

    @Test
    public void getPageInfo_(){
//        ActivityModel tPromoActivityVO = new ActivityModel();

//        Mockito.when(activityService.findActivity(Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(tPromoActivityVO);

        ManagementDataInDTO dataInDTO = new ManagementDataInDTO();
        dataInDTO.setCouponType("01");
        dataInDTO.setStatus("01");
        dataInDTO.setUserCode("123");
        dataInDTO.setReleaseStartTime("123");

        List<CouponReleaseModel> releaseVOs = new ArrayList<>();
        CouponReleaseModel couponReleaseModel = new CouponReleaseModel();
        couponReleaseModel.setActivityCode("123");
        couponReleaseModel.setReleaseCode("123");
        releaseVOs.add(couponReleaseModel);

        Mockito.when(couponInnerCodeDomain.queryManagementUserCouponCodeData(Mockito.any(),Mockito.any())).thenReturn(new PageData<>());
        Mockito.when(promoCouponReleaseService.queryCouponReleaseActivityCode(Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(releaseVOs);

        couponInnerCodeComponent.getPageInfo(dataInDTO);

    }

    @Test
    public void queryManagementInnerCouponCodeData(){

        ManagementDataInDTO dataInDTO = new ManagementDataInDTO();
        ActivityModel tPromoActivityVO = new ActivityModel();


        TPromoCouponInnerCodeVO tPromoCouponInnerCodeVO = new TPromoCouponInnerCodeVO();
        tPromoCouponInnerCodeVO.setTenantCode("123");
        tPromoCouponInnerCodeVO.setActivityCode("123");
        tPromoCouponInnerCodeVO.setStatus("1");
        tPromoCouponInnerCodeVO.setCouponCode("1");
        tPromoCouponInnerCodeVO.setCouponType("1");
        tPromoCouponInnerCodeVO.setFrozenStatus("1");
        tPromoCouponInnerCodeVO.setReleaseCode("1");
        ArrayList<TPromoCouponInnerCodeVO> tPromoCouponInnerCodeVOS = new ArrayList<>();
        tPromoCouponInnerCodeVOS.add(tPromoCouponInnerCodeVO);
        PageData<TPromoCouponInnerCodeVO> pageInfo = new PageData<>();
        pageInfo.setList(tPromoCouponInnerCodeVOS);
        pageInfo.setTotal(12L);

        TPromoCouponCodeUserVO userVO = new TPromoCouponCodeUserVO();
        userVO.setCouponCode("123");

        CouponReleaseDomain releaseVO = new CouponReleaseDomain();

        Mockito.when(promoCouponInnerCodeService.queryManagementData(Mockito.any())).thenReturn(pageInfo);
        Mockito.when(promoCouponCodeUserService.getUserCouponCode(Mockito.any(),Mockito.any())).thenReturn(userVO);
        Mockito.when(promoCouponReleaseService.findCouponReleaseByReleaseCode(Mockito.any(),Mockito.any())).thenReturn(releaseVO);

        couponInnerCodeComponent.queryManagementInnerCouponCodeData(dataInDTO,tPromoActivityVO);
    }

    @Test
    public void bindingUserAndCoupon(){

        BindingCouponInDTO.UserAndCoupon userAndCoupon = new BindingCouponInDTO.UserAndCoupon();
        userAndCoupon.setCouponCode("112");
        userAndCoupon.setUserCode("12312");

        List<BindingCouponInDTO.UserAndCoupon> userAndCoupons = new ArrayList<>();
        userAndCoupons.add(userAndCoupon);

        BindingCouponInDTO bindingCouponInDTO = new BindingCouponInDTO();
        bindingCouponInDTO.setValidStartTime("20");
        bindingCouponInDTO.setValidEndTime("10");

        bindingCouponInDTO.setUserCoupons(userAndCoupons);

        ActivityModel activityVO = new ActivityModel();
        activityVO.setCouponCode("1");
        activityVO.setActivityCode("12");
        activityVO.setActivityType("123");
        activityVO.setActivityBegin("10");
        activityVO.setActivityEnd("20");
        activityVO.setTemplateCode("0202");
        CouponReleaseModel couponReleaseModel = new CouponReleaseModel();
        couponReleaseModel.setReleaseCode("123");
        List<CouponReleaseModel> releaseVOs = new ArrayList<>();
        releaseVOs.add(couponReleaseModel);

        ConditionAndFace conditionAndFace = new ConditionAndFace();
        conditionAndFace.setConditionUnit("123");
        conditionAndFace.setConditionValue(new BigDecimal("123"));
        conditionAndFace.setFaceValue(new BigDecimal("123"));

        Mockito.when(activityService.findActivityByActivityCode(Mockito.any(),Mockito.any())).thenReturn(activityVO);
        Mockito.when(promoCouponReleaseService.queryCouponRelease(Mockito.any())).thenReturn(releaseVOs);
        Mockito.when(functionParamDomain.findConditionAndFaceByActivityCode(Mockito.any())).thenReturn(conditionAndFace);
        couponInnerCodeComponent.bindingUserAndCoupon(bindingCouponInDTO);

    }

    @Test
    public void getLimitUserKey(){

        BindingCouponInDTO.UserAndCoupon userAndCoupon = new BindingCouponInDTO.UserAndCoupon();
        userAndCoupon.setCouponCode("112");
        userAndCoupon.setUserCode("12312");

        List<BindingCouponInDTO.UserAndCoupon> userAndCoupons = new ArrayList<>();
        userAndCoupons.add(userAndCoupon);

        BindingCouponInDTO bindingCouponInDTO = new BindingCouponInDTO();
        bindingCouponInDTO.setValidStartTime("20");
        bindingCouponInDTO.setValidEndTime("10");

        bindingCouponInDTO.setUserCoupons(userAndCoupons);
        ActivityModel activityVO = new ActivityModel();
        activityVO.setCouponCode("1");
        activityVO.setActivityCode("12");

        ActivityModel couponActivity = new ActivityModel();

        couponActivity.setUserLimitMax(2);

        bindingCouponInDTO.setUserCoupons(new ArrayList<>());

        couponInnerCodeComponent.getLimitUserKey(bindingCouponInDTO,activityVO,couponActivity);

    }

    @Test
    public void exportCouponRelationInfo() {

        ExportCouponRelationDto dto = new ExportCouponRelationDto();

        List<CouponInnerRelationVO> couponInnerRelationVOS = new ArrayList<>();
        CouponInnerRelationVO relationVO = new CouponInnerRelationVO();
        relationVO.setReleaseCode("1234");
        relationVO.setCouponCode("1234");
        relationVO.setStatus("01");
        couponInnerRelationVOS.add(relationVO);

        Mockito.when(promoCouponInnerCodeService.exportCouponRelationInfo(Mockito.any())).thenReturn(couponInnerRelationVOS);

        List<CouponReleaseDomain> releaseDomains = new ArrayList<>();
        CouponReleaseDomain domain1 = new CouponReleaseDomain();
        domain1.setValidDays(null);
        domain1.setReleaseCode("1234");
        releaseDomains.add(domain1);

        CouponReleaseDomain domain = new CouponReleaseDomain();
        domain.setValidDays(1);
        domain.setReleaseCode("1234");
        releaseDomains.add(domain);

        Mockito.when(promoCouponReleaseService.queryReleaseByCondition(Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(releaseDomains);

        List<TPromoCouponCodeUserVO> userVOS = new ArrayList<>();
        TPromoCouponCodeUserVO userVO1 = new TPromoCouponCodeUserVO();

        userVO1.setUserCode("111");
        userVO1.setCouponCode("1234");

        userVO1.setUsedRefId("134134");
        userVOS.add(userVO1);

        TPromoCouponCodeUserVO userVO = new TPromoCouponCodeUserVO();

        userVO.setUserCode("111");
        userVO.setCouponCode("12341");

        userVO.setUsedRefId("default");
        userVOS.add(userVO);

        Mockito.when(promoCouponCodeUserService.queryUserCouponInfo(Mockito.any(),Mockito.any())).thenReturn(userVOS);


        Result<List<QueryUserResult>> queryUserResult = new Result<>();
        List<QueryUserResult> queryUserResultList = new ArrayList<>();
        QueryUserResult userResult = new QueryUserResult();
        userResult.setUserCode("111");
        queryUserResultList.add(userResult);

        queryUserResult.setData(queryUserResultList);
        queryUserResult.setCode("0");
        queryUserResult.isSuccess();
        Mockito.when(couponCodeUserComponent.queryUserList(Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(queryUserResult);

        List<ExportCouponRelationResult> results = couponInnerCodeComponent.exportCouponRelationInfo(dto);
        Assert.assertEquals(1,results.size());

    }

    @Test
    public void exportCouponRelationInfo_user_empty() {

        ExportCouponRelationDto dto = new ExportCouponRelationDto();

        List<CouponInnerRelationVO> couponInnerRelationVOS = new ArrayList<>();
        CouponInnerRelationVO relationVO1 = new CouponInnerRelationVO();
        relationVO1.setReleaseCode("12345");
        relationVO1.setCouponCode("12345");
        relationVO1.setValidStartTime("!1");
        relationVO1.setStatus("01");
        couponInnerRelationVOS.add(relationVO1);

        CouponInnerRelationVO relationVO = new CouponInnerRelationVO();
        relationVO.setReleaseCode("1234");
        relationVO.setCouponCode("1234");
        relationVO.setStatus("01");

        couponInnerRelationVOS.add(relationVO);

        Mockito.when(promoCouponInnerCodeService.exportCouponRelationInfo(Mockito.any())).thenReturn(couponInnerRelationVOS);

        List<CouponReleaseDomain> releaseDomains = new ArrayList<>();
        CouponReleaseDomain domain1 = new CouponReleaseDomain();
        domain1.setValidDays(null);
        domain1.setReleaseCode("12345");
        releaseDomains.add(domain1);

        CouponReleaseDomain domain = new CouponReleaseDomain();
        domain.setValidDays(1);
        domain.setReleaseCode("1234");
        releaseDomains.add(domain);

        Mockito.when(promoCouponReleaseService.queryReleaseByCondition(Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(releaseDomains);

        List<TPromoCouponCodeUserVO> userVOS = new ArrayList<>();

        Mockito.when(promoCouponCodeUserService.queryUserCouponInfo(Mockito.any(),Mockito.any())).thenReturn(userVOS);

        List<ExportCouponRelationResult> results = couponInnerCodeComponent.exportCouponRelationInfo(dto);
        Assert.assertEquals(2,results.size());

    }

}
