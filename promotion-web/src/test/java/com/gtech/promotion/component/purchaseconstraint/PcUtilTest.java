package com.gtech.promotion.component.purchaseconstraint;

import cn.hutool.core.date.DateUtil;
import com.gtech.promotion.code.purchaseconstraint.PurchaseConstraintRuleTimeTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;

import java.util.Date;

@Slf4j
public class PcUtilTest {

    @Test
    public void getStatisticsEnd() {
        for (PurchaseConstraintRuleTimeTypeEnum value : PurchaseConstraintRuleTimeTypeEnum.values()) {
            Date statisticsBegin = null;
            Date statisticsEnd = null;
            switch (value) {
                case YEARLY:
                    statisticsBegin = PcUtil.getStatisticsBegin(value, "0701");
                    statisticsEnd = PcUtil.getStatisticsEnd(value, "0701");
                    break;
                case MONTHLY:
                    statisticsBegin = PcUtil.getStatisticsBegin(value, "08");
                    statisticsEnd = PcUtil.getStatisticsEnd(value, "08");
                    break;
                case WEEKLY:
                    statisticsBegin = PcUtil.getStatisticsBegin(value, "6");
                    statisticsEnd = PcUtil.getStatisticsEnd(value, "6");
                    break;
            }
            log.info("{}statisticsBegin:{}", value.getDesc(),DateUtil.format(statisticsBegin,"yyyy-MM-dd HH:mm:ss"));
            log.info("{}statisticsEnd:{}",value.getDesc(), DateUtil.format(statisticsEnd,"yyyy-MM-dd HH:mm:ss"));
        }

    }
}