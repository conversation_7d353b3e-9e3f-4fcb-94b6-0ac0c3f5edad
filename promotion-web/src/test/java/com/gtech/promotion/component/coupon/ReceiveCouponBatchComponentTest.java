package com.gtech.promotion.component.coupon;

import com.gtech.basic.idm.web.vo.result.QueryAccountListResult;
import com.gtech.commons.redis.GTechRedisTemplate;
import com.gtech.commons.utils.DateUtil;
import com.gtech.member.client.MemberClient;
import com.gtech.member.request.QueryMemberByCouponSendParam;
import com.gtech.member.response.MemberInfo;
import com.gtech.member.response.QueryMemberByCouponSendResult;
import com.gtech.member.response.Result;
import com.gtech.promotion.code.coupon.CouponTypeEnum;
import com.gtech.promotion.component.activity.QualificationDomain;
import com.gtech.promotion.dao.mapper.coupon.PromoCouponSendLogMapper;
import com.gtech.promotion.dao.model.activity.ActivityModel;
import com.gtech.promotion.dao.model.activity.PromoCouponSendDetailModel;
import com.gtech.promotion.dao.model.activity.PromoCouponSendLogModel;
import com.gtech.promotion.dao.model.coupon.CouponReleaseModel;
import com.gtech.promotion.dao.model.coupon.ReleaseCouponVO;
import com.gtech.promotion.domain.coupon.CouponReleaseDomain;
import com.gtech.promotion.domain.coupon.CouponReleaseInventoryDomain;
import com.gtech.promotion.dto.in.coupon.ReceiveCouponBatchDTO;
import com.gtech.promotion.dto.in.coupon.SendAnonymousCouponBatchDTO;
import com.gtech.promotion.exception.PromotionCouponException;
import com.gtech.promotion.exception.PromotionException;
import com.gtech.promotion.feign.IdmFeignClient;
import com.gtech.promotion.helper.RedisLock;
import com.gtech.promotion.service.coupon.CouponSendLogService;
import com.gtech.promotion.service.coupon.PromoCouponInnerCodeService;
import com.gtech.promotion.service.coupon.PromoCouponReleaseService;
import com.gtech.promotion.vo.result.coupon.SendAnonymousCouponResult;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.data.redis.core.ZSetOperations;

import java.util.*;

import static org.mockito.Mockito.doNothing;

@RunWith(MockitoJUnitRunner.Silent.class)
public class ReceiveCouponBatchComponentTest {

    @InjectMocks
    private ReceiveCouponBatchComponent receiveCouponBatchComponent;
    @Mock
    private PromoCouponInnerCodeService promoCouponInnerCodeService;
    @Mock
    private PromoCouponReleaseService promoCouponReleaseService;

    @Mock
    private CouponActivityComponent couponActivityDomain;

    @Mock
    private CouponInnerCodeComponent couponInnerCodeDomain;
    @Mock
    private QualificationDomain qualificationDomain;
    @Mock
    private GTechRedisTemplate redisTemplate;
    @Mock
    private CouponSendLogService couponSendLogService;
    @Mock
    private PromoCouponSendLogMapper promoCouponSendLogMapper;
    @Mock
    private ZSetOperations<String,String> zSetOperations;

    private static String APP_KEY = "PROMOTION-";
    @Mock
    private MemberClient memberClient;
    @Mock
    private IdmFeignClient idmFeignClient;

    @Mock
    private RedisLock redisLock;


    @Test(expected = PromotionException.class)
    public void sendAnonymousCoupon_release_null_1(){

        SendAnonymousCouponBatchDTO param = new SendAnonymousCouponBatchDTO();
        param.setDomainCode("1");
        param.setTenantCode("1");
        param.setActivityCode("1");
        param.setSendTotal(1);
        Mockito.when(promoCouponReleaseService.findCanReceiveRelease(Mockito.anyString(), Mockito.anyString())).thenReturn(null);
        receiveCouponBatchComponent.sendAnonymousCoupon(param);
    }

    @Test(expected = PromotionException.class)
    public void sendAnonymousCoupon_release_null(){

        SendAnonymousCouponBatchDTO param = new SendAnonymousCouponBatchDTO();
        param.setDomainCode("1");
        param.setTenantCode("1");
        param.setActivityCode("1");
        param.setReleaseCode("1");
        param.setSendTotal(1);
        Mockito.when(promoCouponReleaseService.findCanReceiveRelease(Mockito.anyString(), Mockito.anyString())).thenReturn(null);
        receiveCouponBatchComponent.sendAnonymousCoupon(param);
    }

    @Test(expected = PromotionException.class)
    public void sendAnonymousCouponActivity_null(){

        SendAnonymousCouponBatchDTO param = new SendAnonymousCouponBatchDTO();
        param.setDomainCode("1");
        param.setTenantCode("1");
        param.setActivityCode("1");
        param.setReleaseCode("1");
        param.setSendTotal(1);
        CouponReleaseModel release = new CouponReleaseModel();
        Mockito.when(promoCouponReleaseService.findCanReceiveRelease(Mockito.anyString(), Mockito.anyString())).thenReturn(release);
        Mockito.when(couponActivityDomain.findSendCouponValidActivity(Mockito.any(), Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(null);
        receiveCouponBatchComponent.sendAnonymousCoupon(param);
    }

    @Test(expected = PromotionException.class)
    public void sendAnonymousCouponActivity_coupon_Type_01(){
        SendAnonymousCouponBatchDTO param = new SendAnonymousCouponBatchDTO();
        param.setDomainCode("1");
        param.setTenantCode("1");
        param.setActivityCode("1");
        param.setReleaseCode("1");
        param.setSendTotal(1);
        CouponReleaseModel release = new CouponReleaseModel();
        Mockito.when(promoCouponReleaseService.findCanReceiveRelease(Mockito.anyString(), Mockito.anyString())).thenReturn(release);
        ActivityModel activityModel = new ActivityModel();
        activityModel.setCouponType("01");
        Mockito.when(couponActivityDomain.findSendCouponValidActivity(Mockito.any(), Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(activityModel);
        receiveCouponBatchComponent.sendAnonymousCoupon(param);
    }


    @Test(expected = PromotionException.class)
    public void sendAnonymousNoCoupon(){

        SendAnonymousCouponBatchDTO param = new SendAnonymousCouponBatchDTO();
        param.setDomainCode("1");
        param.setTenantCode("1");
        param.setActivityCode("1");
        param.setReleaseCode("1");
        param.setSendTotal(1);
        CouponReleaseModel release = new CouponReleaseModel();
        Mockito.when(promoCouponReleaseService.findCanReceiveRelease(Mockito.anyString(), Mockito.anyString())).thenReturn(release);
        ActivityModel activityModel = new ActivityModel();
        activityModel.setCouponType("02");
        Mockito.when(couponActivityDomain.findSendCouponValidActivity(Mockito.any(), Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(activityModel);
        List<ReleaseCouponVO> releaseCouponVOS = new ArrayList<>();
        Mockito.when(promoCouponReleaseService.queryReleaseCouponRecord(Mockito.anyString(), Mockito.anyString(),Mockito.anyString(),Mockito.any(),Mockito.any())).thenReturn(releaseCouponVOS);
        receiveCouponBatchComponent.sendAnonymousCoupon(param);
    }

    @Test(expected = PromotionException.class)
    public void sendAnonymousNoCoupon_total(){

        SendAnonymousCouponBatchDTO param = new SendAnonymousCouponBatchDTO();
        param.setDomainCode("1");
        param.setTenantCode("1");
        param.setActivityCode("1");
        param.setReleaseCode("1");
        param.setSendTotal(10);
        CouponReleaseModel release = new CouponReleaseModel();
        Mockito.when(promoCouponReleaseService.findCanReceiveRelease(Mockito.anyString(), Mockito.anyString())).thenReturn(release);
        ActivityModel activityModel = new ActivityModel();
        activityModel.setCouponType("02");
        Mockito.when(couponActivityDomain.findSendCouponValidActivity(Mockito.any(), Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(activityModel);

        List<ReleaseCouponVO> releaseCouponVOS = new ArrayList<>();
        ReleaseCouponVO vo = new ReleaseCouponVO();
        releaseCouponVOS.add(vo);
        Mockito.when(promoCouponReleaseService.queryReleaseCouponRecord(Mockito.anyString(), Mockito.anyString(),Mockito.anyString(),Mockito.any(),Mockito.any())).thenReturn(releaseCouponVOS);
        receiveCouponBatchComponent.sendAnonymousCoupon(param);
    }

    @Test
    public void sendAnonymousCoupon(){

        SendAnonymousCouponBatchDTO param = new SendAnonymousCouponBatchDTO();
        param.setDomainCode("1");
        param.setTenantCode("1");
        param.setActivityCode("1");
        param.setReleaseCode("1");
        param.setSendTotal(1);
        CouponReleaseModel release = new CouponReleaseModel();
        Mockito.when(promoCouponReleaseService.findCanReceiveRelease(Mockito.anyString(), Mockito.anyString())).thenReturn(release);
        ActivityModel activityModel = new ActivityModel();
        activityModel.setCouponType("02");
        activityModel.setReserveInventory(1);
        Calendar instance = Calendar.getInstance();
        instance.add(Calendar.DATE,2);
        Date startDate = instance.getTime();
        String startTime = DateUtil.format(startDate, DateUtil.FORMAT_YYYYMMDDHHMISS_14);
        activityModel.setActivityEnd(startTime);
        Mockito.when(couponActivityDomain.findSendCouponValidActivity(Mockito.any(), Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(activityModel);
        List<ReleaseCouponVO> releaseCouponVOS = new ArrayList<>();
        ReleaseCouponVO vo1 = new ReleaseCouponVO();
        releaseCouponVOS.add(vo1);
        ReleaseCouponVO vo = new ReleaseCouponVO();
        vo.setValidDays(1);
        vo.setReceiveEndTime(startTime);
        releaseCouponVOS.add(vo);
        Mockito.when(promoCouponReleaseService.queryReleaseCouponRecord(Mockito.anyString(), Mockito.anyString(),Mockito.anyString(),Mockito.any(),Mockito.any())).thenReturn(releaseCouponVOS);
        List<CouponReleaseDomain> releaseDomains = new ArrayList<>();
        CouponReleaseDomain domain = new CouponReleaseDomain();
        domain.setTenantCode("1");
        domain.setInventory(100);
        domain.setActivityCode("1");
        releaseDomains.add(domain);
        Mockito.when(promoCouponReleaseService.queryReleaseByCondition(Mockito.any(), Mockito.any(),Mockito.anyList())).thenReturn(releaseDomains);
        List<CouponReleaseInventoryDomain> domainList = new ArrayList<>();
        CouponReleaseInventoryDomain inventoryDomain = new CouponReleaseInventoryDomain();
        domainList.add(inventoryDomain);
        Mockito.when(promoCouponReleaseService.deductInventory(Mockito.any(), Mockito.any(),Mockito.anyList(),Mockito.anyInt())).thenReturn(domainList);
        doNothing().when(promoCouponInnerCodeService).updateBatchInnerCodeValidTime(Mockito.any());
        List<SendAnonymousCouponResult> results = receiveCouponBatchComponent.sendAnonymousCoupon(param);
        Assert.assertNotNull(results);
    }

    @Test(expected = PromotionCouponException.class)
    public void checkAnonymousInventory(){
        List<CouponReleaseDomain> releaseDomains = new ArrayList<>();

        receiveCouponBatchComponent.checkAnonymousInventory(releaseDomains,1,1);

    }

    @Test
    public void checkAnonymousInventory1(){
        List<CouponReleaseDomain> releaseDomains = new ArrayList<>();
        CouponReleaseDomain domain = new CouponReleaseDomain();
        domain.setInventory(10);
        releaseDomains.add(domain);
        receiveCouponBatchComponent.checkAnonymousInventory(releaseDomains,1,1);
    }

    @Test(expected = PromotionCouponException.class)
    public void checkAnonymousInventory2(){
        List<CouponReleaseDomain> releaseDomains = new ArrayList<>();
        CouponReleaseDomain domain = new CouponReleaseDomain();
        domain.setInventory(0);
        releaseDomains.add(domain);
        receiveCouponBatchComponent.checkAnonymousInventory(releaseDomains,1,1);
    }

    @Test(expected = PromotionCouponException.class)
    public void checkAnonymousInventory5(){
        List<CouponReleaseDomain> releaseDomains = new ArrayList<>();
        CouponReleaseDomain domain = new CouponReleaseDomain();
        domain.setInventory(-1);
        releaseDomains.add(domain);
        receiveCouponBatchComponent.checkAnonymousInventory(releaseDomains,1,1);
    }

    @Test(expected = PromotionCouponException.class)
    public void handleAnonymousInventory(){
        List<CouponReleaseDomain> releaseDomains = new ArrayList<>();
        CouponReleaseDomain domain = new CouponReleaseDomain();
        domain.setInventory(10);
        releaseDomains.add(domain);
        receiveCouponBatchComponent.handleAnonymousInventory(releaseDomains,1,1);
    }

    @Test
    public void handleAnonymousInventory11(){
        List<CouponReleaseDomain> releaseDomains = new ArrayList<>();
        CouponReleaseDomain domain = new CouponReleaseDomain();
        domain.setInventory(10);
        releaseDomains.add(domain);
        List<CouponReleaseInventoryDomain> domainList = new ArrayList<>();
        CouponReleaseInventoryDomain inventoryDomain = new CouponReleaseInventoryDomain();
        domainList.add(inventoryDomain);
        Mockito.when(promoCouponReleaseService.deductInventory(Mockito.any(), Mockito.any(),Mockito.anyList(),Mockito.anyInt())).thenReturn(domainList);
        receiveCouponBatchComponent.handleAnonymousInventory(releaseDomains,1,1);
    }

    @Test
    public void handleAnonymousInventory222(){
        List<CouponReleaseDomain> releaseDomains = new ArrayList<>();

        CouponReleaseDomain domain1 = new CouponReleaseDomain();
        domain1.setInventory(10);
        releaseDomains.add(domain1);

        CouponReleaseDomain domain = new CouponReleaseDomain();
        domain.setInventory(10);
        releaseDomains.add(domain);

        Mockito.when(redisLock.tryLockAndRetry(Mockito.any(), Mockito.any(),Mockito.anyInt())).thenReturn("domainList");

        List<CouponReleaseInventoryDomain> domainList = new ArrayList<>();
        CouponReleaseInventoryDomain inventoryDomain2 = new CouponReleaseInventoryDomain();
        domainList.add(inventoryDomain2);
        CouponReleaseInventoryDomain inventoryDomain = new CouponReleaseInventoryDomain();
        domainList.add(inventoryDomain);
        Mockito.when(promoCouponReleaseService.deductInventory(Mockito.any(), Mockito.any(),Mockito.anyList(),Mockito.anyInt())).thenReturn(domainList);
        doNothing().when(redisLock).unlock(Mockito.any(), Mockito.any());
        receiveCouponBatchComponent.handleAnonymousInventory(releaseDomains,1,1);
    }

    @Test(expected = PromotionException.class)
    public void handleAnonymousInventory22233(){
        List<CouponReleaseDomain> releaseDomains = new ArrayList<>();

        CouponReleaseDomain domain1 = new CouponReleaseDomain();
        domain1.setInventory(10);
        releaseDomains.add(domain1);

        CouponReleaseDomain domain = new CouponReleaseDomain();
        domain.setInventory(10);
        releaseDomains.add(domain);

        Mockito.when(redisLock.tryLockAndRetry(Mockito.any(), Mockito.any(),Mockito.anyInt())).thenReturn("");

        List<CouponReleaseInventoryDomain> domainList = new ArrayList<>();
        CouponReleaseInventoryDomain inventoryDomain2 = new CouponReleaseInventoryDomain();
        domainList.add(inventoryDomain2);
        CouponReleaseInventoryDomain inventoryDomain = new CouponReleaseInventoryDomain();
        domainList.add(inventoryDomain);
        Mockito.when(promoCouponReleaseService.deductInventory(Mockito.any(), Mockito.any(),Mockito.anyList(),Mockito.anyInt())).thenReturn(domainList);
        receiveCouponBatchComponent.handleAnonymousInventory(releaseDomains,1,1);
    }
    @Test
    public void sendAnonymousCoupon_time(){

        SendAnonymousCouponBatchDTO param = new SendAnonymousCouponBatchDTO();
        param.setDomainCode("1");
        param.setTenantCode("1");
        param.setActivityCode("1");
        param.setReleaseCode("1");
        param.setSendTotal(1);
        CouponReleaseModel release = new CouponReleaseModel();
        Mockito.when(promoCouponReleaseService.findCanReceiveRelease(Mockito.anyString(), Mockito.anyString())).thenReturn(release);
        ActivityModel activityModel = new ActivityModel();
        activityModel.setCouponType("02");

        activityModel.setActivityEnd("20221110110456");
        activityModel.setReserveInventory(1);
        Mockito.when(couponActivityDomain.findSendCouponValidActivity(Mockito.any(), Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(activityModel);
        List<ReleaseCouponVO> releaseCouponVOS = new ArrayList<>();
        ReleaseCouponVO vo1 = new ReleaseCouponVO();
        vo1.setValidDays(1);
        vo1.setReceiveEndTime("20221114134526");

        releaseCouponVOS.add(vo1);
        ReleaseCouponVO vo = new ReleaseCouponVO();
        vo.setValidDays(1);
        vo.setReceiveEndTime("20221114134526");
        releaseCouponVOS.add(vo);
        Mockito.when(promoCouponReleaseService.queryReleaseCouponRecord(Mockito.anyString(), Mockito.anyString(),Mockito.anyString(),Mockito.any(),Mockito.any())).thenReturn(releaseCouponVOS);

        List<CouponReleaseDomain> releaseDomains = new ArrayList<>();
        CouponReleaseDomain domain = new CouponReleaseDomain();
        domain.setTenantCode("1");
        domain.setInventory(100);
        domain.setActivityCode("1");
        releaseDomains.add(domain);
        Mockito.when(promoCouponReleaseService.queryReleaseByCondition(Mockito.any(), Mockito.any(),Mockito.anyList())).thenReturn(releaseDomains);
        List<CouponReleaseInventoryDomain> domainList = new ArrayList<>();
        CouponReleaseInventoryDomain inventoryDomain = new CouponReleaseInventoryDomain();
        domainList.add(inventoryDomain);
        Mockito.when(promoCouponReleaseService.deductInventory(Mockito.any(), Mockito.any(),Mockito.anyList(),Mockito.anyInt())).thenReturn(domainList);

        doNothing().when(promoCouponInnerCodeService).updateBatchInnerCodeValidTime(Mockito.any());
        List<SendAnonymousCouponResult> results = receiveCouponBatchComponent.sendAnonymousCoupon(param);
        Assert.assertNotNull(results);
    }

    @Test
    public void receiveCouponBatch_null_activity(){
        ReceiveCouponBatchDTO receiveCouponBatchDTO = new ReceiveCouponBatchDTO();
        try {
            receiveCouponBatchComponent.receiveCouponBatch(receiveCouponBatchDTO);
        }catch (Exception e){

        }
    }

    @Test
    public void receiveCouponBatch_not_PROMOTION_COUPON(){
        try {
            ReceiveCouponBatchDTO receiveCouponBatchDTO = new ReceiveCouponBatchDTO();
            ActivityModel activityModel = new ActivityModel();
            activityModel.setCouponType(CouponTypeEnum.ANONYMITY_COUPON.code());
            Mockito.when(couponActivityDomain.findValidActivity(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(activityModel);
            receiveCouponBatchComponent.receiveCouponBatch(receiveCouponBatchDTO);
        }catch (Exception e){

        }
    }

    @Test
    public void receiveCouponBatch_limit_max_lt(){
        ReceiveCouponBatchDTO receiveCouponBatchDTO = new ReceiveCouponBatchDTO();
        receiveCouponBatchDTO.setReceiveCount(2);
        ActivityModel activityModel = new ActivityModel();
        activityModel.setCouponType(CouponTypeEnum.PROMOTION_COUPON.code());
        activityModel.setUserLimitMax(1);
        Mockito.when(couponActivityDomain.findValidActivity(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(activityModel);
        Mockito.when(couponSendLogService.createCouponSendLog(Mockito.any())).thenReturn(1);
        Mockito.when(promoCouponSendLogMapper.insertSelective(Mockito.any())).thenReturn(1);
        try {
            receiveCouponBatchComponent.receiveCouponBatch(receiveCouponBatchDTO);
        }catch (Exception e){

        }
    }

    @Test
    public void receiveCouponBatch_null_release(){
        ReceiveCouponBatchDTO receiveCouponBatchDTO = new ReceiveCouponBatchDTO();
        receiveCouponBatchDTO.setReceiveCount(1);
        ActivityModel activityModel = new ActivityModel();
        activityModel.setCouponType(CouponTypeEnum.PROMOTION_COUPON.code());
        activityModel.setUserLimitMax(2);
        Mockito.when(couponActivityDomain.findValidActivity(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(activityModel);
        Mockito.when(promoCouponReleaseService.queryCanReceiveReleases(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(new ArrayList<>());
        try {
            receiveCouponBatchComponent.receiveCouponBatch(receiveCouponBatchDTO);
        }catch (Exception e){

        }
    }

    @Test(expected = PromotionCouponException.class)
    public void receiveCouponBatch_null_release3(){
        ReceiveCouponBatchDTO receiveCouponBatchDTO = new ReceiveCouponBatchDTO();
        receiveCouponBatchDTO.setReceiveCount(1);
        ActivityModel activityModel = new ActivityModel();
        activityModel.setCouponType(CouponTypeEnum.PROMOTION_COUPON.code());
        activityModel.setUserLimitMax(0);
        Mockito.when(couponActivityDomain.findValidActivity(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(activityModel);
        Mockito.when(promoCouponReleaseService.queryCanReceiveReleases(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(new ArrayList<>());

        receiveCouponBatchComponent.receiveCouponBatch(receiveCouponBatchDTO);


    }

    @Test(expected = PromotionCouponException.class)
    public void receiveCouponBatch_null_release2(){
        ReceiveCouponBatchDTO receiveCouponBatchDTO = new ReceiveCouponBatchDTO();
        receiveCouponBatchDTO.setReceiveCount(1);
        ActivityModel activityModel = new ActivityModel();
        activityModel.setCouponType(CouponTypeEnum.PROMOTION_COUPON.code());

        Mockito.when(couponActivityDomain.findValidActivity(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(activityModel);
        Mockito.when(promoCouponReleaseService.queryCanReceiveReleases(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(new ArrayList<>());

        receiveCouponBatchComponent.receiveCouponBatch(receiveCouponBatchDTO);

    }

    @Test(expected = PromotionCouponException.class)
    public void receiveCouponBatch_null_release4(){
        ReceiveCouponBatchDTO receiveCouponBatchDTO = new ReceiveCouponBatchDTO();
        receiveCouponBatchDTO.setReceiveCount(1);
        ActivityModel activityModel = new ActivityModel();
        activityModel.setUserLimitMax(1);

        activityModel.setCouponType(CouponTypeEnum.PROMOTION_COUPON.code());

        Mockito.when(couponActivityDomain.findValidActivity(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(activityModel);
        Mockito.when(promoCouponReleaseService.queryCanReceiveReleases(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(new ArrayList<>());

        receiveCouponBatchComponent.receiveCouponBatch(receiveCouponBatchDTO);

    }


    @Test
    public void receiveCouponBatch_qualification_true(){
        ReceiveCouponBatchDTO receiveCouponBatchDTO = new ReceiveCouponBatchDTO();
        receiveCouponBatchDTO.setUserList(Collections.singletonList(new ReceiveCouponBatchDTO.UserAndLevel()));
        ActivityModel activityModel = new ActivityModel();
        activityModel.setCouponType(CouponTypeEnum.PROMOTION_COUPON.code());
        activityModel.setUserLimitMax(0);
        ArrayList<CouponReleaseDomain> releaseDomains = new ArrayList<>();
        releaseDomains.add(new CouponReleaseDomain());
        Map<String, String> userCodesMap = new HashMap<>();
        userCodesMap.put("1", "1");
        Mockito.when(couponActivityDomain.findValidActivity(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(activityModel);
        Mockito.when(promoCouponReleaseService.queryCanReceiveReleases(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(releaseDomains);
        Mockito.when(qualificationDomain.checkQualificationForSendCoupon(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(true);
        Mockito.when(couponInnerCodeDomain.allocateCoupon(Mockito.any(), Mockito.anyInt(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(userCodesMap);
        receiveCouponBatchComponent.receiveCouponBatch(receiveCouponBatchDTO);
//        Assert.assertEquals(1, receiveCouponBatchOutDTOS.size());
    }
    @Test
    public void receiveCouponBatch_qualification_false(){
        ReceiveCouponBatchDTO receiveCouponBatchDTO = new ReceiveCouponBatchDTO();
        receiveCouponBatchDTO.setUserList(Collections.singletonList(new ReceiveCouponBatchDTO.UserAndLevel()));
        ActivityModel activityModel = new ActivityModel();
        activityModel.setCouponType(CouponTypeEnum.PROMOTION_COUPON.code());
        activityModel.setUserLimitMax(0);
        ArrayList<CouponReleaseDomain> releaseDomains = new ArrayList<>();
        releaseDomains.add(new CouponReleaseDomain());
        Map<String, String> userCodesMap = new HashMap<>();
        Mockito.when(couponActivityDomain.findValidActivity(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(activityModel);
        Mockito.when(promoCouponReleaseService.queryCanReceiveReleases(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(releaseDomains);
        Mockito.when(qualificationDomain.checkQualificationForSendCoupon(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(false);
        Mockito.when(couponInnerCodeDomain.allocateCoupon(Mockito.any(), Mockito.anyInt(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(userCodesMap);
        Mockito.when(redisTemplate.hasKey(Mockito.any(),Mockito.anyString())).thenReturn(false);
        doNothing().when(redisTemplate).opsValueSet(Mockito.any(),Mockito.any(),Mockito.any());
        try {
            receiveCouponBatchComponent.receiveCouponBatch(receiveCouponBatchDTO);
        }catch (Exception e){

        }
//        Assert.assertEquals(1, receiveCouponBatchOutDTOS.size());
    }


    @Test
    public void checkRedisKeyExist(){
        Mockito.when(redisTemplate.hasKey(Mockito.any(),Mockito.anyString())).thenReturn(false);
        receiveCouponBatchComponent.checkRedisKeyExist("test");
        Mockito.when(redisTemplate.hasKey(Mockito.any(),Mockito.anyString())).thenReturn(true);
        receiveCouponBatchComponent.checkRedisKeyExist("test");
    }

    @Test
    public void queryMemberAndSendCoupon1(){
        ReceiveCouponBatchDTO paramDto = new ReceiveCouponBatchDTO();
        paramDto.setIsAllUser("0");
        ActivityModel activityModel = new ActivityModel();

        List<CouponReleaseDomain> couponRelease = new ArrayList<>();
        List<String> userCodeList = new ArrayList<>();
        userCodeList.add("1");
        QueryMemberByCouponSendResult queryMemberByCouponSendResult = new QueryMemberByCouponSendResult();
        Result<QueryMemberByCouponSendResult> resultResult = new Result<>(queryMemberByCouponSendResult);
        Mockito.when(memberClient.queryMemberByCouponSend(Mockito.any())).thenReturn(resultResult);
        PromoCouponSendLogModel promoCouponSendLogModel = new PromoCouponSendLogModel();
        receiveCouponBatchComponent.queryMemberAndSendCoupon(paramDto,activityModel,couponRelease,userCodeList,promoCouponSendLogModel);

    }


    @Test
    public void queryMemberAndSendCoupon_exception(){
        ReceiveCouponBatchDTO paramDto = new ReceiveCouponBatchDTO();
        paramDto.setIsAllUser("0");
        ActivityModel activityModel = new ActivityModel();

        List<CouponReleaseDomain> couponRelease = new ArrayList<>();
        List<String> userCodeList = new ArrayList<>();
        userCodeList.add("1");
        QueryMemberByCouponSendResult queryMemberByCouponSendResult = new QueryMemberByCouponSendResult();
        Result<QueryMemberByCouponSendResult> resultResult = new Result<>(queryMemberByCouponSendResult);
        Mockito.when(memberClient.queryMemberByCouponSend(Mockito.any())).thenReturn(resultResult);
        Mockito.when(couponInnerCodeDomain.allocateCoupon(Mockito.anyList(),Mockito.anyInt(),Mockito.anyString()
                ,Mockito.any(),Mockito.anyList(),Mockito.anyInt(), Mockito.any())).thenReturn(new HashMap<>());
        PromoCouponSendLogModel promoCouponSendLogModel = new PromoCouponSendLogModel();
        receiveCouponBatchComponent.queryMemberAndSendCoupon(paramDto,activityModel,couponRelease,userCodeList,promoCouponSendLogModel);

    }

    @Test
    public void queryMemberAndSendCoupon(){
        ReceiveCouponBatchDTO paramDto = new ReceiveCouponBatchDTO();
        paramDto.setIsAllUser("1");
        ActivityModel activityModel = new ActivityModel();

        List<CouponReleaseDomain> couponRelease = new ArrayList<>();

        List<String> userCodeList = new ArrayList<>();

        QueryMemberByCouponSendResult queryMemberByCouponSendResult = new QueryMemberByCouponSendResult();
        Result<QueryMemberByCouponSendResult> resultResult = new Result<>(queryMemberByCouponSendResult);
        Mockito.when(memberClient.queryMemberByCouponSend(Mockito.any())).thenReturn(resultResult);

        PromoCouponSendLogModel promoCouponSendLogModel = new PromoCouponSendLogModel();
        receiveCouponBatchComponent.queryMemberAndSendCoupon(paramDto,activityModel,couponRelease,userCodeList,promoCouponSendLogModel);

        List<MemberInfo> memberInfoList = new ArrayList<>();
        MemberInfo memberInfo = new MemberInfo();
        memberInfo.setMemberCode("test1");
        memberInfoList.add(memberInfo);
        queryMemberByCouponSendResult.setMemberCodeList(memberInfoList);
        resultResult.setData(queryMemberByCouponSendResult);

    }

    @Test
    public void queryMemberAndSendCoupon_exception1(){
        ReceiveCouponBatchDTO paramDto = new ReceiveCouponBatchDTO();
        paramDto.setIsAllUser("0");
        ActivityModel activityModel = new ActivityModel();

        List<CouponReleaseDomain> couponRelease = new ArrayList<>();
        List<String> userCodeList = new ArrayList<>();
        userCodeList.add("1");
        QueryMemberByCouponSendResult queryMemberByCouponSendResult = new QueryMemberByCouponSendResult();
        Result<QueryMemberByCouponSendResult> resultResult = new Result<>(queryMemberByCouponSendResult);
        Mockito.when(memberClient.queryMemberByCouponSend(Mockito.any())).thenReturn(resultResult);
        Mockito.doThrow(new ArrayIndexOutOfBoundsException("1")).when(couponInnerCodeDomain).allocateCoupon(Mockito.anyList(),Mockito.anyInt(),Mockito.anyString()
                ,Mockito.any(),Mockito.anyList(),Mockito.anyInt(), Mockito.any());
        PromoCouponSendLogModel promoCouponSendLogModel = new PromoCouponSendLogModel();
        receiveCouponBatchComponent.queryMemberAndSendCoupon(paramDto,activityModel,couponRelease,userCodeList,promoCouponSendLogModel);

    }

    @Test
    public void queryMemberCount(){
        ReceiveCouponBatchDTO paramDto = new ReceiveCouponBatchDTO();
        paramDto.setIsAllUser("1");
        PromoCouponSendLogModel promoCouponSendLogModel = new PromoCouponSendLogModel();
        List<String> canSendUserList = new ArrayList<>();
        QueryMemberByCouponSendResult queryMemberByCouponSendResult = new QueryMemberByCouponSendResult();
        Result<QueryMemberByCouponSendResult> resultResult = new Result<>(queryMemberByCouponSendResult);
        List<MemberInfo> memberInfoList = new ArrayList<>();
        MemberInfo memberInfo = new MemberInfo();
        memberInfo.setMemberCode("test1");
        memberInfoList.add(memberInfo);
        queryMemberByCouponSendResult.setMemberCodeList(memberInfoList);
        queryMemberByCouponSendResult.setMemberCount(10);
        resultResult.setData(queryMemberByCouponSendResult);
        Mockito.when(memberClient.queryMemberByCouponSend(Mockito.any())).thenReturn(resultResult);
        receiveCouponBatchComponent.queryMemberCount(paramDto,promoCouponSendLogModel,canSendUserList);

        paramDto.setIsAllUser("0");
        paramDto.setUserAccounts("a1,a2");

        com.gtech.commons.result.Result<List<QueryAccountListResult>> accountResult = new com.gtech.commons.result.Result<>();
        Mockito.when(idmFeignClient.queryUserCodeByAccountList(Mockito.any())).thenReturn(accountResult);
        receiveCouponBatchComponent.queryMemberCount(paramDto,promoCouponSendLogModel,canSendUserList);

        List<QueryAccountListResult> accountListResultList = new ArrayList<>();
        QueryAccountListResult accountListResult = new QueryAccountListResult();
        accountListResult.setAccount("a1");
        accountListResult.setUserCode("u1");
        accountListResultList.add(accountListResult);
        accountResult.setData(accountListResultList);
        receiveCouponBatchComponent.queryMemberCount(paramDto,promoCouponSendLogModel,canSendUserList);
    }

    @Test
    public void queryMemberCount1(){
        ReceiveCouponBatchDTO paramDto = new ReceiveCouponBatchDTO();
        paramDto.setIsAllUser("0");
        PromoCouponSendLogModel promoCouponSendLogModel = new PromoCouponSendLogModel();
        List<String> canSendUserList = new ArrayList<>();
        QueryMemberByCouponSendResult queryMemberByCouponSendResult = new QueryMemberByCouponSendResult();
        Result<QueryMemberByCouponSendResult> resultResult1 = new Result<>(queryMemberByCouponSendResult);
        List<MemberInfo> memberInfoList = new ArrayList<>();
        MemberInfo memberInfo = new MemberInfo();
        memberInfo.setMemberCode("test1");
        memberInfoList.add(memberInfo);
        queryMemberByCouponSendResult.setMemberCodeList(memberInfoList);
        queryMemberByCouponSendResult.setMemberCount(0);
        resultResult1.setData(queryMemberByCouponSendResult);

        paramDto.setIsAllUser("0");
        paramDto.setUserTags("1,a2");

        com.gtech.commons.result.Result<List<QueryAccountListResult>> accountResult = new com.gtech.commons.result.Result<>();
        Mockito.when(idmFeignClient.queryUserCodeByAccountList(Mockito.any())).thenReturn(accountResult);

        Mockito.when(memberClient.queryMemberByCouponSend(Mockito.any())).thenReturn(resultResult1);

        receiveCouponBatchComponent.queryMemberCount(paramDto,promoCouponSendLogModel,canSendUserList);

        List<QueryAccountListResult> accountListResultList = new ArrayList<>();
        QueryAccountListResult accountListResult = new QueryAccountListResult();
        accountListResult.setAccount("a1");
        accountListResult.setUserCode("u1");
        accountListResultList.add(accountListResult);
        accountResult.setData(accountListResultList);
        receiveCouponBatchComponent.queryMemberCount(paramDto,promoCouponSendLogModel,canSendUserList);
    }


    @Test
    public void queryMemberTotalCount(){
        QueryMemberByCouponSendParam queryMemberByCouponSend = new QueryMemberByCouponSendParam();
        Result<QueryMemberByCouponSendResult> resultResult = new Result<>();
        QueryMemberByCouponSendResult memberByCouponSendResult = new QueryMemberByCouponSendResult();
        memberByCouponSendResult.setMemberCount(0);
        resultResult.setData(memberByCouponSendResult);
        List<MemberInfo> memberInfoList = new ArrayList<>();
        MemberInfo memberInfo = new MemberInfo();
        memberInfo.setMemberCode("test1");
        memberInfoList.add(memberInfo);
        Mockito.when(memberClient.queryMemberByCouponSend(Mockito.any())).thenReturn(resultResult);
        receiveCouponBatchComponent.queryMemberCount(queryMemberByCouponSend);
    }

    @Test
    public void queryMemberByCountOrMobile(){
        QueryMemberByCouponSendParam queryMemberByCouponSend = new QueryMemberByCouponSendParam();
        List<MemberInfo> memberInfoList = new ArrayList<>();

        Result<QueryMemberByCouponSendResult> resultResult = new Result<>();
        QueryMemberByCouponSendResult memberByCouponSendResult = new QueryMemberByCouponSendResult();
        memberByCouponSendResult.setMemberCount(5);
        List<MemberInfo> memberCodeList = new ArrayList<>();
        MemberInfo memberInfo =  new MemberInfo();
        memberCodeList.add(memberInfo);
        memberByCouponSendResult.setMemberCodeList(memberCodeList);
        resultResult.setData(memberByCouponSendResult);

        Result<QueryMemberByCouponSendResult> resultResult1 = new Result<>();
        QueryMemberByCouponSendResult memberByCouponSendResult1 = new QueryMemberByCouponSendResult();
        memberByCouponSendResult1.setMemberCount(0);
        resultResult1.setData(memberByCouponSendResult1);
        Mockito.when(memberClient.queryMemberByCouponSend(Mockito.any())).thenReturn(resultResult).thenReturn(resultResult1);
        receiveCouponBatchComponent.queryMemberByCountOrMobile(queryMemberByCouponSend,memberInfoList);
    }

    @Test
    public void queryMemberByCouponSend(){
        List<String> mobileList = new ArrayList<>();
        mobileList.add("123123");
        List<String> userCodeList = new ArrayList<>();
        userCodeList.add("u1");
        QueryMemberByCouponSendResult queryMemberByCouponSendResult = new QueryMemberByCouponSendResult();
        Result<QueryMemberByCouponSendResult> resultResult = new Result<>(queryMemberByCouponSendResult);
        List<MemberInfo> memberInfoList = new ArrayList<>();
        MemberInfo memberInfo = new MemberInfo();
        memberInfo.setMemberCode("test1");
        memberInfoList.add(memberInfo);
        queryMemberByCouponSendResult.setMemberCodeList(memberInfoList);
        queryMemberByCouponSendResult.setMemberCount(10);
        resultResult.setData(queryMemberByCouponSendResult);
        Mockito.when(memberClient.queryMemberByCouponSend(Mockito.any())).thenReturn(resultResult);
        receiveCouponBatchComponent.queryMemberByCouponSend("DC001","100001",mobileList,userCodeList,10,10,"dd");
    }

    @Test
    public void convertPromoCouponSendLogModel(){
        ReceiveCouponBatchDTO paramDto = new ReceiveCouponBatchDTO();
        receiveCouponBatchComponent.convertPromoCouponSendLogModel(paramDto);
    }

    @Test
    public void buildPromoCouponSendDetailModel(){
        PromoCouponSendLogModel promoCouponSendLogModel = new PromoCouponSendLogModel();
        receiveCouponBatchComponent.buildPromoCouponSendDetailModel(promoCouponSendLogModel);
    }

    @Test
    public void filterUserMaxLimit(){
        ActivityModel activityModel = new ActivityModel();
        List<String> userCodeList = new ArrayList<>();
        List<PromoCouponSendDetailModel> couponSendDetailModelList = new ArrayList<>();
        PromoCouponSendLogModel promoCouponSendLogModel = new PromoCouponSendLogModel();
        String currentDay = DateUtil.format(new Date(),DateUtil.FORMAT_YYYYMMDD);

        receiveCouponBatchComponent.filterUserMaxLimit(activityModel,userCodeList,couponSendDetailModelList,1,promoCouponSendLogModel,currentDay);

        userCodeList.add("test");
        Mockito.when(redisTemplate.hasKey(Mockito.any(),Mockito.anyString())).thenReturn(false).thenReturn(true);
        doNothing().when(redisTemplate).opsValueSet(Mockito.any(),Mockito.any(),Mockito.any());
        Mockito.when(redisTemplate.opsValueGet(Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(0);
        activityModel.setUserLimitMax(100);
        receiveCouponBatchComponent.filterUserMaxLimit(activityModel,userCodeList,couponSendDetailModelList,1,promoCouponSendLogModel,currentDay);

        Mockito.when(redisTemplate.opsValueGet(Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(2);
        userCodeList.add("test1");
        receiveCouponBatchComponent.filterUserMaxLimit(activityModel,userCodeList,couponSendDetailModelList,1,promoCouponSendLogModel,currentDay);

        Mockito.when(redisTemplate.opsValueGet(Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(2).thenReturn(0);
        userCodeList.add("test1");
        receiveCouponBatchComponent.filterUserMaxLimit(activityModel,userCodeList,couponSendDetailModelList,1,promoCouponSendLogModel,currentDay);

    }


    @Test
    public void filterUserMaxLimit1(){
        ActivityModel activityModel = new ActivityModel();
        activityModel.setUserLimitMaxDay(1);
        List<String> userCodeList = new ArrayList<>();
        List<PromoCouponSendDetailModel> couponSendDetailModelList = new ArrayList<>();
        PromoCouponSendLogModel promoCouponSendLogModel = new PromoCouponSendLogModel();
        String currentDay = DateUtil.format(new Date(),DateUtil.FORMAT_YYYYMMDD);

        receiveCouponBatchComponent.filterUserMaxLimit(activityModel,userCodeList,couponSendDetailModelList,1,promoCouponSendLogModel,currentDay);

        userCodeList.add("test");
        Mockito.when(redisTemplate.hasKey(Mockito.any(),Mockito.anyString())).thenReturn(false).thenReturn(true);
        doNothing().when(redisTemplate).opsValueSet(Mockito.any(),Mockito.any(),Mockito.any());
        Mockito.when(redisTemplate.opsValueGet(Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(0);
        activityModel.setUserLimitMax(100);
        receiveCouponBatchComponent.filterUserMaxLimit(activityModel,userCodeList,couponSendDetailModelList,1,promoCouponSendLogModel,currentDay);

        Mockito.when(redisTemplate.opsValueGet(Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(2);
        userCodeList.add("test1");
        receiveCouponBatchComponent.filterUserMaxLimit(activityModel,userCodeList,couponSendDetailModelList,1,promoCouponSendLogModel,currentDay);

        Mockito.when(redisTemplate.opsValueGet(Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(2).thenReturn(0);
        userCodeList.add("test1");
        receiveCouponBatchComponent.filterUserMaxLimit(activityModel,userCodeList,couponSendDetailModelList,1,promoCouponSendLogModel,currentDay);

    }


    @Test
    public void returnUserMaxLimit(){
        ActivityModel activityModel = new ActivityModel();
        String currentDay = DateUtil.format(new Date(),DateUtil.FORMAT_YYYYMMDD);
        Mockito.when(redisTemplate.opsValueGet(Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(2).thenReturn(0);
        doNothing().when(redisTemplate).opsValueSet(Mockito.any(),Mockito.any(),Mockito.any());
        receiveCouponBatchComponent.returnUserMaxLimit(activityModel,"testUserCode",1,currentDay);
    }


    @Test
    public void receiveCouponBatchAsync_error(){
        ReceiveCouponBatchDTO receiveCouponBatchDTO = new ReceiveCouponBatchDTO();
        receiveCouponBatchDTO.setTenantCode("1");
        receiveCouponBatchDTO.setActivityCode("1");
        receiveCouponBatchDTO.setReleaseCode("1");
        receiveCouponBatchDTO.setErrorMessage("11");
        Mockito.when(couponSendLogService.createCouponSendLog(Mockito.any())).thenReturn(1);
        receiveCouponBatchComponent.receiveCouponBatchAsync(receiveCouponBatchDTO);
    }

    @Test
    public void receiveCouponBatchAsync_redis_key_is_null(){
        ReceiveCouponBatchDTO receiveCouponBatchDTO = new ReceiveCouponBatchDTO();

        Mockito.when(redisTemplate.hasKey(Mockito.anyString(),Mockito.anyString())).thenReturn(true);
        Mockito.when(couponSendLogService.createCouponSendLog(Mockito.any())).thenReturn(1);
        receiveCouponBatchComponent.receiveCouponBatchAsync(receiveCouponBatchDTO);
    }

    @Test
    public void receiveCouponBatchAsync_1(){
        ReceiveCouponBatchDTO receiveCouponBatchDTO = new ReceiveCouponBatchDTO();
        receiveCouponBatchDTO.setTenantCode("1");
        receiveCouponBatchDTO.setActivityCode("1");
        receiveCouponBatchDTO.setReleaseCode("1");
        receiveCouponBatchComponent.receiveCouponBatchAsync(receiveCouponBatchDTO);
    }

    @Test
    public void receiveCouponBatchAsync_activity_null(){
        ReceiveCouponBatchDTO receiveCouponBatchDTO = new ReceiveCouponBatchDTO();
        receiveCouponBatchDTO.setTenantCode("1");
        receiveCouponBatchDTO.setActivityCode("1");
        receiveCouponBatchDTO.setReleaseCode("1");
        Mockito.doNothing().when(couponSendLogService).updateCouponSendLog(Mockito.any());
        receiveCouponBatchComponent.receiveCouponBatchAsync(receiveCouponBatchDTO);
    }

    @Test
    public void receiveCouponBatchAsync_activity_not_null(){
        ReceiveCouponBatchDTO receiveCouponBatchDTO = new ReceiveCouponBatchDTO();
        receiveCouponBatchDTO.setTenantCode("1");
        receiveCouponBatchDTO.setActivityCode("1");
        receiveCouponBatchDTO.setReleaseCode("1");
        Mockito.when(couponSendLogService.createCouponSendLog(Mockito.any())).thenReturn(1);

        ActivityModel activityModel = new ActivityModel();
        activityModel.setCouponType("01");
        Mockito.when(couponActivityDomain.findSendCouponValidActivity(Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(activityModel);

        receiveCouponBatchComponent.receiveCouponBatchAsync(receiveCouponBatchDTO);
    }

    @Test
    public void receiveCouponBatchAsync_activity_not_null_couponType(){
        ReceiveCouponBatchDTO receiveCouponBatchDTO = new ReceiveCouponBatchDTO();
        receiveCouponBatchDTO.setTenantCode("1");
        receiveCouponBatchDTO.setActivityCode("1");
        receiveCouponBatchDTO.setReleaseCode("1");

        ActivityModel activityModel = new ActivityModel();
        activityModel.setCouponType("02");
        Mockito.when(couponActivityDomain.findSendCouponValidActivity(Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(activityModel);

        Mockito.doNothing().when(couponSendLogService).updateCouponSendLog(Mockito.any());

        receiveCouponBatchComponent.receiveCouponBatchAsync(receiveCouponBatchDTO);
    }


    @Test
    public void receiveCouponBatchAsync_activity_not_null_received(){
        ReceiveCouponBatchDTO receiveCouponBatchDTO = new ReceiveCouponBatchDTO();
        receiveCouponBatchDTO.setTenantCode("1");
        receiveCouponBatchDTO.setActivityCode("1");
        receiveCouponBatchDTO.setReleaseCode("1");
        receiveCouponBatchDTO.setReceiveCount(2);

        ActivityModel activityModel = new ActivityModel();
        activityModel.setCouponType("01");
        activityModel.setUserLimitMax(1);
        Mockito.when(couponActivityDomain.findSendCouponValidActivity(Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(activityModel);

        Mockito.doNothing().when(couponSendLogService).updateCouponSendLog(Mockito.any());

        receiveCouponBatchComponent.receiveCouponBatchAsync(receiveCouponBatchDTO);
    }

    @Test
    public void receiveCouponBatchAsync_activity_not_null_received_day(){
        ReceiveCouponBatchDTO receiveCouponBatchDTO = new ReceiveCouponBatchDTO();
        receiveCouponBatchDTO.setTenantCode("1");
        receiveCouponBatchDTO.setActivityCode("1");
        receiveCouponBatchDTO.setReleaseCode("1");
        receiveCouponBatchDTO.setReceiveCount(2);

        ActivityModel activityModel = new ActivityModel();
        activityModel.setCouponType("01");
        activityModel.setUserLimitMax(3);
        activityModel.setUserLimitMaxDay(1);
        Mockito.when(couponActivityDomain.findSendCouponValidActivity(Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(activityModel);

        Mockito.doNothing().when(couponSendLogService).updateCouponSendLog(Mockito.any());

        receiveCouponBatchComponent.receiveCouponBatchAsync(receiveCouponBatchDTO);
    }


    @Test
    public void receiveCouponBatchAsync_activity_not_null_release_empty(){
        ReceiveCouponBatchDTO receiveCouponBatchDTO = new ReceiveCouponBatchDTO();
        receiveCouponBatchDTO.setTenantCode("1");
        receiveCouponBatchDTO.setActivityCode("1");
        receiveCouponBatchDTO.setReleaseCode("1");
        receiveCouponBatchDTO.setReceiveCount(2);

        ActivityModel activityModel = new ActivityModel();
        activityModel.setCouponType("01");
        activityModel.setUserLimitMax(3);
        activityModel.setUserLimitMaxDay(3);
        Mockito.when(couponActivityDomain.findSendCouponValidActivity(Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(activityModel);

        List<CouponReleaseDomain> couponRelease = new ArrayList<>();
        Mockito.when(promoCouponReleaseService.queryCanReceiveReleases(Mockito.anyString(),Mockito.anyString(),Mockito.anyString())).thenReturn(couponRelease);
        Mockito.doNothing().when(couponSendLogService).updateCouponSendLog(Mockito.any());

        receiveCouponBatchComponent.receiveCouponBatchAsync(receiveCouponBatchDTO);
    }

    @Test
    public void receiveCouponBatchAsync_activity_not_null_release_not_empty(){
        ReceiveCouponBatchDTO receiveCouponBatchDTO = new ReceiveCouponBatchDTO();
        receiveCouponBatchDTO.setTenantCode("1");
        receiveCouponBatchDTO.setActivityCode("1");
        receiveCouponBatchDTO.setReleaseCode("1");
        receiveCouponBatchDTO.setReceiveCount(2);

        ActivityModel activityModel = new ActivityModel();
        activityModel.setCouponType("01");
        activityModel.setUserLimitMax(3);
        activityModel.setUserLimitMaxDay(3);
        Mockito.when(couponActivityDomain.findSendCouponValidActivity(Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(activityModel);

        List<CouponReleaseDomain> couponRelease = new ArrayList<>();
        CouponReleaseDomain releaseDomain = new CouponReleaseDomain();
        releaseDomain.setInventory(1);
        couponRelease.add(releaseDomain);

        Mockito.when(promoCouponReleaseService.queryCanReceiveReleases(Mockito.anyString(),Mockito.anyString(),Mockito.anyString())).thenReturn(couponRelease);
        Mockito.doNothing().when(couponSendLogService).updateCouponSendLog(Mockito.any());

        receiveCouponBatchComponent.receiveCouponBatchAsync(receiveCouponBatchDTO);
    }

    @Test
    public void receiveCouponBatchAsync_activity_not_null_release_not_empty_isAllUser(){
        ReceiveCouponBatchDTO receiveCouponBatchDTO = new ReceiveCouponBatchDTO();
        receiveCouponBatchDTO.setTenantCode("1");
        receiveCouponBatchDTO.setActivityCode("1");
        receiveCouponBatchDTO.setReleaseCode("1");
        receiveCouponBatchDTO.setReceiveCount(2);
        receiveCouponBatchDTO.setIsAllUser("1");
        ActivityModel activityModel = new ActivityModel();
        activityModel.setCouponType("01");
        activityModel.setUserLimitMax(3);
        activityModel.setUserLimitMaxDay(3);
        Mockito.when(couponActivityDomain.findSendCouponValidActivity(Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(activityModel);

        List<CouponReleaseDomain> couponRelease = new ArrayList<>();
        CouponReleaseDomain releaseDomain = new CouponReleaseDomain();
        releaseDomain.setInventory(2);
        couponRelease.add(releaseDomain);

        Mockito.when(promoCouponReleaseService.queryCanReceiveReleases(Mockito.anyString(),Mockito.anyString(),Mockito.anyString())).thenReturn(couponRelease);
        Mockito.doNothing().when(couponSendLogService).updateCouponSendLog(Mockito.any());

        com.gtech.member.response.Result<QueryMemberByCouponSendResult> resultResult = new Result<>();
        QueryMemberByCouponSendResult result = new QueryMemberByCouponSendResult();
        result.setMemberCount(1);

        resultResult.setCode("0");
        resultResult.setData(result);
        Mockito.when(memberClient.queryMemberByCouponSend(Mockito.any())).thenReturn(resultResult);

        receiveCouponBatchComponent.receiveCouponBatchAsync(receiveCouponBatchDTO);
    }

    @Test
    public void receiveCouponBatchAsync_activity_not_isAllUser(){
        ReceiveCouponBatchDTO receiveCouponBatchDTO = new ReceiveCouponBatchDTO();
        receiveCouponBatchDTO.setTenantCode("1");
        receiveCouponBatchDTO.setActivityCode("1");
        receiveCouponBatchDTO.setReleaseCode("1");
        receiveCouponBatchDTO.setReceiveCount(2);
        receiveCouponBatchDTO.setIsAllUser("1");
        ActivityModel activityModel = new ActivityModel();
        activityModel.setCouponType("01");
        activityModel.setUserLimitMax(3);
        activityModel.setUserLimitMaxDay(3);
        Mockito.when(couponActivityDomain.findSendCouponValidActivity(Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(activityModel);

        List<CouponReleaseDomain> couponRelease = new ArrayList<>();
        CouponReleaseDomain releaseDomain = new CouponReleaseDomain();
        releaseDomain.setInventory(2);
        couponRelease.add(releaseDomain);

        Mockito.when(promoCouponReleaseService.queryCanReceiveReleases(Mockito.anyString(),Mockito.anyString(),Mockito.anyString())).thenReturn(couponRelease);

        com.gtech.member.response.Result<QueryMemberByCouponSendResult> resultResult = new Result<>();
        QueryMemberByCouponSendResult result = new QueryMemberByCouponSendResult();
        result.setMemberCount(0);

        resultResult.setCode("0");
        resultResult.setData(result);
        Mockito.when(memberClient.queryMemberByCouponSend(Mockito.any())).thenReturn(resultResult);

        Mockito.doNothing().when(couponSendLogService).updateCouponSendLog(Mockito.any());

        receiveCouponBatchComponent.receiveCouponBatchAsync(receiveCouponBatchDTO);
    }

    @Test
    public void receiveCouponBatchAsync_activity_not_isAllUser_0(){
        ReceiveCouponBatchDTO receiveCouponBatchDTO = new ReceiveCouponBatchDTO();
        receiveCouponBatchDTO.setTenantCode("1");
        receiveCouponBatchDTO.setActivityCode("1");
        receiveCouponBatchDTO.setReleaseCode("1");
        receiveCouponBatchDTO.setReceiveCount(2);
        receiveCouponBatchDTO.setIsAllUser("0");
        ActivityModel activityModel = new ActivityModel();
        activityModel.setCouponType("01");
        activityModel.setUserLimitMax(3);
        activityModel.setUserLimitMaxDay(3);
        Mockito.when(couponActivityDomain.findSendCouponValidActivity(Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(activityModel);

        List<CouponReleaseDomain> couponRelease = new ArrayList<>();
        CouponReleaseDomain releaseDomain = new CouponReleaseDomain();
        releaseDomain.setInventory(2);
        couponRelease.add(releaseDomain);

        Mockito.when(promoCouponReleaseService.queryCanReceiveReleases(Mockito.anyString(),Mockito.anyString(),Mockito.anyString())).thenReturn(couponRelease);

        com.gtech.member.response.Result<QueryMemberByCouponSendResult> resultResult = new Result<>();
        QueryMemberByCouponSendResult result = new QueryMemberByCouponSendResult();
        result.setMemberCount(0);

        resultResult.setCode("0");
        resultResult.setData(result);
        Mockito.when(memberClient.queryMemberByCouponSend(Mockito.any())).thenReturn(resultResult);

        Mockito.doNothing().when(couponSendLogService).updateCouponSendLog(Mockito.any());

        receiveCouponBatchComponent.receiveCouponBatchAsync(receiveCouponBatchDTO);
    }

    @Test
    public void receiveCouponBatchAsync_activity_user_tags_not_isAllUser_0(){
        ReceiveCouponBatchDTO receiveCouponBatchDTO = new ReceiveCouponBatchDTO();
        receiveCouponBatchDTO.setTenantCode("1");
        receiveCouponBatchDTO.setActivityCode("1");
        receiveCouponBatchDTO.setReleaseCode("1");
        receiveCouponBatchDTO.setUserTags("1");
        receiveCouponBatchDTO.setReceiveCount(2);
        receiveCouponBatchDTO.setIsAllUser("0");
        ActivityModel activityModel = new ActivityModel();
        activityModel.setCouponType("01");
        activityModel.setUserLimitMax(3);
        activityModel.setUserLimitMaxDay(3);
        Mockito.when(couponActivityDomain.findSendCouponValidActivity(Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(activityModel);

        List<CouponReleaseDomain> couponRelease = new ArrayList<>();
        CouponReleaseDomain releaseDomain = new CouponReleaseDomain();
        releaseDomain.setInventory(2);
        couponRelease.add(releaseDomain);

        Mockito.when(promoCouponReleaseService.queryCanReceiveReleases(Mockito.anyString(),Mockito.anyString(),Mockito.anyString())).thenReturn(couponRelease);

        com.gtech.member.response.Result<QueryMemberByCouponSendResult> resultResult = new Result<>();
        QueryMemberByCouponSendResult result = new QueryMemberByCouponSendResult();
        result.setMemberCount(0);

        resultResult.setCode("0");
        resultResult.setData(result);
        Mockito.when(memberClient.queryMemberByCouponSend(Mockito.any())).thenReturn(resultResult);

        Mockito.doNothing().when(couponSendLogService).updateCouponSendLog(Mockito.any());

        receiveCouponBatchComponent.receiveCouponBatchAsync(receiveCouponBatchDTO);
    }

    @Test
    public void receiveCouponBatchAsync_activity_isAllUser_0(){
        ReceiveCouponBatchDTO receiveCouponBatchDTO = new ReceiveCouponBatchDTO();
        receiveCouponBatchDTO.setTenantCode("1");
        receiveCouponBatchDTO.setActivityCode("1");
        receiveCouponBatchDTO.setReleaseCode("1");
        receiveCouponBatchDTO.setReceiveCount(0);
        receiveCouponBatchDTO.setIsAllUser("1");
        ActivityModel activityModel = new ActivityModel();
        activityModel.setCouponType("01");
        activityModel.setUserLimitMax(3);
        activityModel.setUserLimitMaxDay(3);
        Mockito.when(couponActivityDomain.findSendCouponValidActivity(Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(activityModel);

        List<CouponReleaseDomain> couponRelease = new ArrayList<>();
        CouponReleaseDomain releaseDomain = new CouponReleaseDomain();
        releaseDomain.setInventory(0);
        couponRelease.add(releaseDomain);

        Mockito.when(promoCouponReleaseService.queryCanReceiveReleases(Mockito.anyString(),Mockito.anyString(),Mockito.anyString())).thenReturn(couponRelease);

        com.gtech.member.response.Result<QueryMemberByCouponSendResult> resultResult = new Result<>();
        QueryMemberByCouponSendResult result = new QueryMemberByCouponSendResult();
        result.setMemberCount(2);

        resultResult.setCode("0");
        resultResult.setData(result);
        Mockito.when(memberClient.queryMemberByCouponSend(Mockito.any())).thenReturn(resultResult);

        Mockito.doNothing().when(couponSendLogService).updateCouponSendLog(Mockito.any());

        receiveCouponBatchComponent.receiveCouponBatchAsync(receiveCouponBatchDTO);
    }

    @Test
    public void receiveCouponBatchAsync_activity_isAllUser_3(){
        ReceiveCouponBatchDTO receiveCouponBatchDTO = new ReceiveCouponBatchDTO();
        receiveCouponBatchDTO.setTenantCode("1");
        receiveCouponBatchDTO.setActivityCode("1");
        receiveCouponBatchDTO.setReleaseCode("1");
        receiveCouponBatchDTO.setReceiveCount(0);
        receiveCouponBatchDTO.setIsAllUser("1");
        ActivityModel activityModel = new ActivityModel();
        activityModel.setCouponType("01");
        activityModel.setUserLimitMax(3);
        activityModel.setUserLimitMaxDay(3);
        Mockito.when(couponActivityDomain.findSendCouponValidActivity(Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(activityModel);

        List<CouponReleaseDomain> couponRelease = new ArrayList<>();
        CouponReleaseDomain releaseDomain = new CouponReleaseDomain();
        releaseDomain.setInventory(3);
        couponRelease.add(releaseDomain);

        Mockito.when(promoCouponReleaseService.queryCanReceiveReleases(Mockito.anyString(),Mockito.anyString(),Mockito.anyString())).thenReturn(couponRelease);

        com.gtech.member.response.Result<QueryMemberByCouponSendResult> resultResult = new Result<>();
        QueryMemberByCouponSendResult result = new QueryMemberByCouponSendResult();
        result.setMemberCount(2);

        resultResult.setCode("0");
        resultResult.setData(result);
        Mockito.when(memberClient.queryMemberByCouponSend(Mockito.any())).thenReturn(resultResult);

        Mockito.doNothing().when(couponSendLogService).updateCouponSendLog(Mockito.any());

        receiveCouponBatchComponent.receiveCouponBatchAsync(receiveCouponBatchDTO);
    }


    @Test
    public void receiveCouponBatchAsync_activity_member(){
        ReceiveCouponBatchDTO receiveCouponBatchDTO = new ReceiveCouponBatchDTO();
        receiveCouponBatchDTO.setTenantCode("1");
        receiveCouponBatchDTO.setActivityCode("1");
        receiveCouponBatchDTO.setReleaseCode("1");
        receiveCouponBatchDTO.setReceiveCount(2);
        receiveCouponBatchDTO.setIsAllUser("1");
        ActivityModel activityModel = new ActivityModel();
        activityModel.setCouponType("01");
        activityModel.setUserLimitMax(3);
        activityModel.setUserLimitMaxDay(3);
        Mockito.when(couponActivityDomain.findSendCouponValidActivity(Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(activityModel);

        List<CouponReleaseDomain> couponRelease = new ArrayList<>();
        CouponReleaseDomain releaseDomain = new CouponReleaseDomain();
        releaseDomain.setInventory(2);
        couponRelease.add(releaseDomain);

        Mockito.when(promoCouponReleaseService.queryCanReceiveReleases(Mockito.anyString(),Mockito.anyString(),Mockito.anyString())).thenReturn(couponRelease);
        Mockito.doNothing().when(couponSendLogService).updateCouponSendLog(Mockito.any());

        com.gtech.member.response.Result<QueryMemberByCouponSendResult> resultResult = new Result<>();
        QueryMemberByCouponSendResult result = new QueryMemberByCouponSendResult();
        result.setMemberCount(1);

        resultResult.setCode("0");
        resultResult.setData(result);

        receiveCouponBatchComponent.receiveCouponBatchAsync(receiveCouponBatchDTO);
    }


    @Test(expected = NullPointerException.class)
    public void queryMemberAndSendCoupon3(){
        ReceiveCouponBatchDTO paramDto = new ReceiveCouponBatchDTO();
        paramDto.setIsAllUser("0");
        ActivityModel activityModel = new ActivityModel();

        List<CouponReleaseDomain> couponRelease = new ArrayList<>();
        List<String> userCodeList = new ArrayList<>();
        userCodeList.add("1");
        receiveCouponBatchComponent.queryMemberAndSendCoupon(paramDto,activityModel,couponRelease,userCodeList,null);

    }

    @Test
    public void queryMemberAndSendCoupon2(){
        ReceiveCouponBatchDTO paramDto = new ReceiveCouponBatchDTO();
        paramDto.setIsAllUser("0");

        List<CouponReleaseDomain> couponRelease = new ArrayList<>();
        List<String> userCodeList = new ArrayList<>();
        userCodeList.add("1");

        PromoCouponSendLogModel promoCouponSendLogModel = new PromoCouponSendLogModel();

        receiveCouponBatchComponent.queryMemberAndSendCoupon(paramDto,null,couponRelease,userCodeList,promoCouponSendLogModel);

    }

    @Test
    public void queryMemberAndSendCoupon4(){
        ReceiveCouponBatchDTO paramDto = new ReceiveCouponBatchDTO();
        paramDto.setIsAllUser("0");

        List<CouponReleaseDomain> couponRelease = new ArrayList<>();
        List<String> userCodeList = new ArrayList<>();

        PromoCouponSendLogModel promoCouponSendLogModel = new PromoCouponSendLogModel();

        receiveCouponBatchComponent.queryMemberAndSendCoupon(paramDto,null,couponRelease,userCodeList,promoCouponSendLogModel);

    }

    @Test
    public void queryMemberCount112(){
        ReceiveCouponBatchDTO paramDto = new ReceiveCouponBatchDTO();
        paramDto.setIsAllUser("1");
        PromoCouponSendLogModel promoCouponSendLogModel = new PromoCouponSendLogModel();
        List<String> canSendUserList = new ArrayList<>();
        QueryMemberByCouponSendResult queryMemberByCouponSendResult = new QueryMemberByCouponSendResult();
        Result<QueryMemberByCouponSendResult> resultResult = new Result<>(queryMemberByCouponSendResult);
        List<MemberInfo> memberInfoList = new ArrayList<>();
        MemberInfo memberInfo = new MemberInfo();
        memberInfo.setMemberCode("test1");
        memberInfoList.add(memberInfo);
        queryMemberByCouponSendResult.setMemberCodeList(memberInfoList);
        queryMemberByCouponSendResult.setMemberCount(10);
        resultResult.setData(queryMemberByCouponSendResult);

        paramDto.setIsAllUser("0");

        paramDto.setUserMobiles("1,2");

        com.gtech.commons.result.Result<List<QueryAccountListResult>> accountResult = new com.gtech.commons.result.Result<>();
        Mockito.when(idmFeignClient.queryUserCodeByAccountList(Mockito.any())).thenReturn(accountResult);
        receiveCouponBatchComponent.queryMemberCount(paramDto,promoCouponSendLogModel,canSendUserList);

    }

    @Test
    public void queryMemberCount111(){
        ReceiveCouponBatchDTO paramDto = new ReceiveCouponBatchDTO();
        paramDto.setIsAllUser("1");
        PromoCouponSendLogModel promoCouponSendLogModel = new PromoCouponSendLogModel();
        List<String> canSendUserList = new ArrayList<>();
        QueryMemberByCouponSendResult queryMemberByCouponSendResult = new QueryMemberByCouponSendResult();
        Result<QueryMemberByCouponSendResult> resultResult = new Result<>(queryMemberByCouponSendResult);
        List<MemberInfo> memberInfoList = new ArrayList<>();
        MemberInfo memberInfo = new MemberInfo();
        memberInfo.setMemberCode("test1");
        memberInfoList.add(memberInfo);
        queryMemberByCouponSendResult.setMemberCodeList(memberInfoList);
        queryMemberByCouponSendResult.setMemberCount(10);
        resultResult.setData(queryMemberByCouponSendResult);
        paramDto.setIsAllUser("0");
        paramDto.setUserMobiles("1,2");
        paramDto.setUserCodes("1,2");
        com.gtech.commons.result.Result<List<QueryAccountListResult>> accountResult = new com.gtech.commons.result.Result<>();
        Mockito.when(idmFeignClient.queryUserCodeByAccountList(Mockito.any())).thenReturn(accountResult);

        com.gtech.member.response.Result<QueryMemberByCouponSendResult> memberByCouponSendResultResult = new Result<>();
        QueryMemberByCouponSendResult result = new QueryMemberByCouponSendResult();
        result.setMemberCount(0);
        memberByCouponSendResultResult.setData(result);
        Mockito.when(memberClient.queryMemberByCouponSend(Mockito.any())).thenReturn(memberByCouponSendResultResult);
        List<QueryAccountListResult> accountListResultList = new ArrayList<>();
        QueryAccountListResult accountListResult = new QueryAccountListResult();
        accountListResult.setAccount("a1");
        accountListResult.setUserCode("u1");
        accountListResultList.add(accountListResult);
        accountResult.setData(accountListResultList);
        int i = receiveCouponBatchComponent.queryMemberCount(paramDto, promoCouponSendLogModel, canSendUserList);
        Assert.assertEquals(2,i);
    }

    @Test
    public void queryMemberCount1123(){
        ReceiveCouponBatchDTO paramDto = new ReceiveCouponBatchDTO();
        paramDto.setIsAllUser("1");
        PromoCouponSendLogModel promoCouponSendLogModel = new PromoCouponSendLogModel();
        List<String> canSendUserList = new ArrayList<>();
        QueryMemberByCouponSendResult queryMemberByCouponSendResult = new QueryMemberByCouponSendResult();
        Result<QueryMemberByCouponSendResult> resultResult = new Result<>(queryMemberByCouponSendResult);
        List<MemberInfo> memberInfoList = new ArrayList<>();
        MemberInfo memberInfo = new MemberInfo();
        memberInfo.setMemberCode("test1");
        memberInfoList.add(memberInfo);
        queryMemberByCouponSendResult.setMemberCodeList(memberInfoList);
        queryMemberByCouponSendResult.setMemberCount(10);
        resultResult.setData(queryMemberByCouponSendResult);
        paramDto.setIsAllUser("0");
        paramDto.setUserMobiles("1,2");
        paramDto.setUserCodes("1,2");
        com.gtech.commons.result.Result<List<QueryAccountListResult>> accountResult = new com.gtech.commons.result.Result<>();
        Mockito.when(idmFeignClient.queryUserCodeByAccountList(Mockito.any())).thenReturn(accountResult);

        com.gtech.member.response.Result<QueryMemberByCouponSendResult> memberByCouponSendResultResult = new Result<>();
        QueryMemberByCouponSendResult result = new QueryMemberByCouponSendResult();
        result.setMemberCount(2);
        List<MemberInfo> memberCodeList = new ArrayList<>();
        result.setMemberCodeList(memberCodeList);
        memberByCouponSendResultResult.setData(result);
        Mockito.when(memberClient.queryMemberByCouponSend(Mockito.any())).thenReturn(memberByCouponSendResultResult);
        List<QueryAccountListResult> accountListResultList = new ArrayList<>();
        QueryAccountListResult accountListResult = new QueryAccountListResult();
        accountListResult.setAccount("a1");
        accountListResult.setUserCode("u1");
        accountListResultList.add(accountListResult);
        accountResult.setData(accountListResultList);
        int i = receiveCouponBatchComponent.queryMemberCount(paramDto, promoCouponSendLogModel, canSendUserList);
        Assert.assertEquals(2,i);
    }



    @Test
    public void queryMemberCount1124(){
        ReceiveCouponBatchDTO paramDto = new ReceiveCouponBatchDTO();
        paramDto.setIsAllUser("1");
        PromoCouponSendLogModel promoCouponSendLogModel = new PromoCouponSendLogModel();
        List<String> canSendUserList = new ArrayList<>();
        QueryMemberByCouponSendResult queryMemberByCouponSendResult = new QueryMemberByCouponSendResult();
        Result<QueryMemberByCouponSendResult> resultResult = new Result<>(queryMemberByCouponSendResult);
        List<MemberInfo> memberInfoList = new ArrayList<>();
        MemberInfo memberInfo = new MemberInfo();
        memberInfo.setMemberCode("test1");
        memberInfoList.add(memberInfo);
        queryMemberByCouponSendResult.setMemberCodeList(memberInfoList);
        queryMemberByCouponSendResult.setMemberCount(10);
        resultResult.setData(queryMemberByCouponSendResult);
        paramDto.setIsAllUser("0");
        paramDto.setUserMobiles("1,2");
        paramDto.setUserCodes("1,2");
        com.gtech.commons.result.Result<List<QueryAccountListResult>> accountResult = new com.gtech.commons.result.Result<>();
        Mockito.when(idmFeignClient.queryUserCodeByAccountList(Mockito.any())).thenReturn(accountResult);

        com.gtech.member.response.Result<QueryMemberByCouponSendResult> memberByCouponSendResultResult = new Result<>();
        QueryMemberByCouponSendResult result = new QueryMemberByCouponSendResult();
        result.setMemberCount(2);
        List<MemberInfo> memberCodeList = new ArrayList<>();
        MemberInfo memberInfo1 = new MemberInfo();
        memberCodeList.add(memberInfo1);
        result.setMemberCodeList(memberCodeList);
        memberByCouponSendResultResult.setData(result);
        Mockito.when(memberClient.queryMemberByCouponSend(Mockito.any())).thenReturn(memberByCouponSendResultResult);
        List<QueryAccountListResult> accountListResultList = new ArrayList<>();
        QueryAccountListResult accountListResult = new QueryAccountListResult();
        accountListResult.setAccount("a1");
        accountListResult.setUserCode("u1");
        accountListResultList.add(accountListResult);
        accountResult.setData(accountListResultList);
        result.setMemberCount(0);
        Mockito.when(memberClient.queryMemberByCouponSend(Mockito.any())).thenReturn(memberByCouponSendResultResult);

        int i = receiveCouponBatchComponent.queryMemberCount(paramDto, promoCouponSendLogModel, canSendUserList);
        Assert.assertEquals(2,i);
    }

    @Test
    public void returnUserMaxLimit11(){

        String currentDay = DateUtil.format(new Date(),DateUtil.FORMAT_YYYYMMDD);
        Mockito.when(redisTemplate.opsValueGet(Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(2).thenReturn(1);
        doNothing().when(redisTemplate).opsValueSet(Mockito.any(),Mockito.any(),Mockito.any());
        receiveCouponBatchComponent.returnUserMaxLimit(null,"testUserCode",1,currentDay);
    }
    @Test
    public void queryMemberAndSendCoupon22(){

        ReceiveCouponBatchDTO paramDto = new ReceiveCouponBatchDTO();
        paramDto.setIsAllUser("0");
        ActivityModel activityModel = new ActivityModel();
        activityModel.setUserLimitMax(1);
        List<CouponReleaseDomain> couponRelease = new ArrayList<>();
        List<String> userCodeList = new ArrayList<>();
        userCodeList.add("1");
        QueryMemberByCouponSendResult queryMemberByCouponSendResult = new QueryMemberByCouponSendResult();
        Result<QueryMemberByCouponSendResult> resultResult = new Result<>(queryMemberByCouponSendResult);
        Mockito.when(memberClient.queryMemberByCouponSend(Mockito.any())).thenReturn(resultResult);
        PromoCouponSendLogModel promoCouponSendLogModel = new PromoCouponSendLogModel();
        receiveCouponBatchComponent.queryMemberAndSendCoupon(paramDto,activityModel,couponRelease,userCodeList,promoCouponSendLogModel);

    }


    @Test
    public void receiveCouponBatchAsync_activity_not_null_release_not_empty_isAllUser1(){
        ReceiveCouponBatchDTO receiveCouponBatchDTO = new ReceiveCouponBatchDTO();
        receiveCouponBatchDTO.setTenantCode("1");
        receiveCouponBatchDTO.setActivityCode("1");
        receiveCouponBatchDTO.setReleaseCode("1");
        receiveCouponBatchDTO.setReceiveCount(2);
        receiveCouponBatchDTO.setIsAllUser("1");
        ActivityModel activityModel = new ActivityModel();
        activityModel.setCouponType("01");
        activityModel.setUserLimitMax(3);
        activityModel.setUserLimitMaxDay(3);
        Mockito.when(couponActivityDomain.findSendCouponValidActivity(Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(activityModel);

        List<CouponReleaseDomain> couponRelease = new ArrayList<>();
        CouponReleaseDomain releaseDomain = new CouponReleaseDomain();
        releaseDomain.setInventory(2);
        couponRelease.add(releaseDomain);

        Mockito.when(promoCouponReleaseService.queryCanReceiveReleases(Mockito.anyString(),Mockito.anyString(),Mockito.anyString())).thenReturn(couponRelease);
        Mockito.doNothing().when(couponSendLogService).updateCouponSendLog(Mockito.any());

        com.gtech.member.response.Result<QueryMemberByCouponSendResult> resultResult = new Result<>();
        QueryMemberByCouponSendResult result = new QueryMemberByCouponSendResult();
        result.setMemberCount(1);

        resultResult.setCode("0");
        resultResult.setData(result);
        Mockito.when(memberClient.queryMemberByCouponSend(Mockito.any())).thenReturn(resultResult);

        receiveCouponBatchComponent.receiveCouponBatchAsync(receiveCouponBatchDTO);
    }

}
