package com.gtech.promotion.component.coupon;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.gtech.commons.exception.ErrorCode;
import com.gtech.commons.page.PageData;
import com.gtech.commons.utils.BeanCopyUtils;
import com.gtech.promotion.RedisClient;
import com.gtech.promotion.callable.CouponChooseCallable;
import com.gtech.promotion.callable.ThreadPoolUtil;
import com.gtech.promotion.code.activity.ActivityStatusEnum;
import com.gtech.promotion.code.activity.ActivityTypeEnum;
import com.gtech.promotion.code.coupon.CouponFrozenStatusEnum;
import com.gtech.promotion.code.coupon.CouponReleaseStatusEnum;
import com.gtech.promotion.code.coupon.CouponStatusEnum;
import com.gtech.promotion.code.coupon.CouponTypeEnum;
import com.gtech.promotion.component.activity.*;
import com.gtech.promotion.dao.model.activity.*;
import com.gtech.promotion.dao.model.coupon.TPromoCouponCodeUserVO;
import com.gtech.promotion.dao.model.coupon.TPromoCouponInnerCodeVO;
import com.gtech.promotion.domain.coupon.CouponDomain;
import com.gtech.promotion.domain.coupon.CouponReleaseDomain;
import com.gtech.promotion.dto.cache.ActivityCacheDTO;
import com.gtech.promotion.dto.cache.ProductSkuDetailDTO;
import com.gtech.promotion.dto.in.activity.FrozenCouponCodeInDTO;
import com.gtech.promotion.dto.in.activity.ShoppingCartDTO;
import com.gtech.promotion.dto.in.activity.ShoppingCartItem;
import com.gtech.promotion.dto.in.activity.TenantProductDTO;
import com.gtech.promotion.dto.in.coupon.ChooseCouponByCartCouponInfoDTO;
import com.gtech.promotion.dto.in.coupon.ConditionAndFace;
import com.gtech.promotion.dto.out.activity.ShoppingCartOutDTO;
import com.gtech.promotion.dto.out.coupon.CouponActivityProductDTO;
import com.gtech.promotion.dto.out.coupon.CouponInfoDTO;
import com.gtech.promotion.exception.PromotionCouponException;
import com.gtech.promotion.exception.PromotionException;
import com.gtech.promotion.service.activity.*;
import com.gtech.promotion.service.common.GroovyService;
import com.gtech.promotion.service.coupon.PromoCouponCodeUserService;
import com.gtech.promotion.service.coupon.PromoCouponInnerCodeService;
import com.gtech.promotion.service.coupon.PromoCouponReleaseService;
import com.gtech.promotion.utils.Constants;
import com.gtech.promotion.utils.DateUtil;
import com.gtech.promotion.vo.bean.ActivityPeriod;
import com.gtech.promotion.vo.bean.ShoppingCartStore;
import com.gtech.promotion.vo.param.activity.UpdateCouponParam;
import com.gtech.promotion.vo.param.activity.UpdateUserCouponParam;
import com.gtech.promotion.vo.param.coupon.QueryCouponActivityListByProductListParam;
import com.gtech.promotion.vo.param.coupon.QueryCouponListByUserParam;
import com.gtech.promotion.vo.result.coupon.QueryCouponActivityListByProductListResult;
import com.gtech.promotion.vo.result.coupon.QueryCouponActivityListByProductResult;
import org.apache.commons.lang.time.DateUtils;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.*;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class FilterCouponComponentTest {

    @InjectMocks
    private FilterCouponComponent filterCouponComponent;
    @Mock
    private PromoCouponCodeUserService couponCodeUserService;
    @Mock
    private ShoppingCartDomain shoppingCartDomain;
    @Mock
    private ActivityService activityService;
    @Mock
    private ActivityComponentDomain activityComponentDomain;
    @Mock
    private CouponActivityComponent couponActivityComponent;
    @Mock
    private ActivityExpireComponentDomain activityExpireComponentDomain;
    @Mock
    private ActivityPeriodService activityPeriodService;
    @Mock
    private ActivityFuncParamService promoActivityFuncParamService;
    @Mock
    private ActivityStoreService tPromoActivityStoreService;
    @Mock
    private ActivityFuncRankService promoActivityFuncRankService;
    @Mock
    private ActivityProductDetailService productDetailService;
    @Mock
    private ActivityCacheDomain activityCacheDomain;
    @Mock
    private PromoCouponReleaseService promoCouponReleaseService;
    @Mock
    private TemplateService templateService;
    @Mock
    private PromoCouponInnerCodeService couponInnerCodeService;

    @Mock
    private FunctionParamDomain functionParamDomain;
    @Mock
    private ExecutorService executorService;

    @Mock
    private RedisClient redisClient;

    @Mock
    private GroovyService groovyService;



    private  final String ACTIVITY_CODE = "1";
    private  final String TEMPLATE_CODE="0104020103010402";

    /**
     * 用户对应券为null
     */
    @Test
    public void queryUserCouponListUserIsNull(){
        //given
        //入参
        QueryCouponListByUserParam param = new QueryCouponListByUserParam();
        param.setPageNum(1);
        param.setPageSize(1);
        param.setUserCode("1");
        param.setDomainCode("1");
        param.setTenantCode("1");
        when(couponCodeUserService.getUserCouponByUserId(Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(null);
        PageData<CouponDomain> couponDomainPageData = filterCouponComponent.queryUserCouponList(param);
        Assert.assertEquals(0,couponDomainPageData.getList().size());
    }

//用户对应券不为null
    /**
     * 活动为null
     */
    @Test
    public void queryUserCouponListActivityIsNull(){
        //given
        //入参
        QueryCouponListByUserParam param = new QueryCouponListByUserParam();
        param.setPageNum(1);
        param.setPageSize(1);
        param.setUserCode("1");
        param.setDomainCode("1");
        param.setTenantCode("1");
        //查询用户券返回结果
        List<CouponDomain> list = Lists.newArrayList();
        CouponDomain couponDomain = new CouponDomain();
        couponDomain.setActivityCode(ACTIVITY_CODE);
        list.add(couponDomain);
        PageData<CouponDomain> couponDomainPage = new PageData<>();
        couponDomainPage.setList(list);
        when(couponCodeUserService.getUserCouponByUserId(Mockito.any(),Mockito.any(), Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(couponDomainPage);
        when(activityService.queryActivityByActivityCodes(anyString(),any())).thenReturn(null);
        PageData<CouponDomain> pageData = filterCouponComponent.queryUserCouponList(param);
        Assert.assertNotNull(pageData);
    }
    /**
     * 活动不为null
     */
    @Test
    public void queryUserCouponListActivityNotNull(){
        //given
        //入参
        QueryCouponListByUserParam param = new QueryCouponListByUserParam();
        param.setPageNum(1);
        param.setPageSize(1);
        param.setUserCode("1");
        param.setDomainCode("1");
        param.setTenantCode("1");
        param.setUserCode("1");
        param.setTenantCode("2");
        param.setStatus("01");
        param.setTakeLabel("ss");
        param.setBeginTime("121");
        param.setEndTime("33");
        List<String> stringList = new ArrayList<>();
        stringList.add("1");
        param.setOpsTypeList(stringList);

        //查询用户券返回结果
        List<CouponDomain> list = Lists.newArrayList();
        CouponDomain couponDomain = new CouponDomain();
        couponDomain.setActivityCode(ACTIVITY_CODE);
        couponDomain.setFaceValue(new BigDecimal(0.1));
        couponDomain.setUsedRefId(Constants.DEFAULT_USED_REF_ID);
        list.add(couponDomain);
        PageData<CouponDomain> pageData = new PageData<>();
        pageData.setTotal(1L);
        pageData.setList(list);
        //活动返回结果
        List<ActivityModel> activityModelList = Lists.newArrayList();
        ActivityModel activityModel = new ActivityModel();
        activityModel.setActivityCode(ACTIVITY_CODE);
        activityModel.setTemplateCode(TEMPLATE_CODE);
        activityModel.setActivityLabel("01");
        activityModel.setActivityName("活动名称");
        activityModel.setActivityStatus(ActivityStatusEnum.EFFECTIVE.code());
        activityModel.setActivityUrl("");
        activityModel.setActivityRemark("备注信息");
        activityModel.setActivityDesc("描述信息");
        activityModelList.add(activityModel);
        //活动周期对象
        ActivityPeriodModel period = new ActivityPeriodModel();
        period.setActivityCode(ACTIVITY_CODE);
        //店铺出参添加
        List<TPromoActivityStoreVO> storeVOs = Lists.newArrayList();
        TPromoActivityStoreVO storeVO = new TPromoActivityStoreVO();
        storeVO.setOrgCode("1");
        storeVO.setStoreName("店铺1");
        storeVOs.add(storeVO);
        //函数
        List<ActivityFunctionParamRankModel> rankVOs = Lists.newArrayList();
        ActivityFunctionParamRankModel rankModel = new ActivityFunctionParamRankModel();
        rankModel.setId("1");
        rankVOs.add(rankModel);
        FunctionParamModel functionParamModel = new FunctionParamModel();
        when(couponCodeUserService.getUserCouponByUserId(Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(pageData);
        when(activityService.queryActivityByActivityCodes(Mockito.any(),Mockito.any())).thenReturn(activityModelList);
//        doNothing().when(activityComponentDomain).loadActivityLanguage(Mockito.any(),Mockito.any());
        doNothing().when(couponActivityComponent).expireCoupon(Mockito.any());
        when(activityExpireComponentDomain.expireActivity(activityModelList)).thenReturn(0);
//        when(activityPeriodService.findPeriod(Mockito.any(),Mockito.any())).thenReturn(period);
//        when(tPromoActivityStoreService.getStoresByActivityCode(Mockito.any())).thenReturn(storeVOs);
//        when(promoActivityFuncRankService.getRankListByActivityCode(Mockito.any())).thenReturn(rankVOs);
//        when(promoActivityFuncParamService.getRuleFuncParamListByRankIdAndFunctionCode(Mockito.any(),Mockito.any())).thenReturn(functionParamModel);
        PageData<CouponDomain> responsePageData = filterCouponComponent.queryUserCouponList(param);
        Assert.assertNotNull(responsePageData);
    }
    @Test
    public void testConvertCouponActivityProductDTO() {
        // Arrange
        List<QueryCouponActivityListByProductListResult> resultList = new ArrayList<>();

        // Mock QueryCouponActivityListByProductListResult object
        QueryCouponActivityListByProductListResult productListResult = Mockito.mock(QueryCouponActivityListByProductListResult.class);
        Mockito.when(productListResult.getProductActivityList()).thenReturn(new ArrayList<>());

        // Mock QueryCouponActivityListByProductResult object
        QueryCouponActivityListByProductResult productResult = Mockito.mock(QueryCouponActivityListByProductResult.class);
        // Suppose you have some properties to return when accessed
        // Mockito.when(productResult.getProperty()).thenReturn(value);

        // Add mock product result to the mock product list result
        resultList.add(productListResult);

        // Act
        List<CouponActivityProductDTO> couponActivityProductDTOS = filterCouponComponent.convertCouponActivityProductDTO(resultList);



        // You can add more detailed assertions to validate the conversion results
    }

    @Test
    public void queryUserCouponListActivity_1(){
        //given
        //入参
        QueryCouponListByUserParam param = new QueryCouponListByUserParam();
        param.setPageNum(1);
        param.setPageSize(1);
        param.setUserCode("1");
        param.setDomainCode("1");
        param.setTenantCode("1");
        param.setUserCode("1");
        param.setTenantCode("2");
        param.setStatus("01");
        param.setTakeLabel("ss");
        param.setBeginTime("121");
        param.setEndTime("33");
        param.setFlag(true);
        List<String> stringList = new ArrayList<>();
        stringList.add("1");
        param.setOpsTypeList(stringList);

        //查询用户券返回结果
        List<CouponDomain> list = Lists.newArrayList();
        CouponDomain couponDomain = new CouponDomain();
        couponDomain.setActivityCode(ACTIVITY_CODE);
        couponDomain.setFaceValue(new BigDecimal(0.1));
        couponDomain.setUsedRefId(Constants.DEFAULT_USED_REF_ID);
        list.add(couponDomain);
        PageData<CouponDomain> pageData = new PageData<>();
        pageData.setTotal(1L);
        pageData.setList(list);
        //活动返回结果
        List<ActivityModel> activityModelList = Lists.newArrayList();
        ActivityModel activityModel = new ActivityModel();
        activityModel.setActivityCode(ACTIVITY_CODE);
        activityModel.setTemplateCode(TEMPLATE_CODE);
        activityModel.setActivityLabel("01");
        activityModel.setActivityName("活动名称");
        activityModel.setActivityStatus(ActivityStatusEnum.EFFECTIVE.code());
        activityModel.setActivityUrl("");
        activityModel.setActivityRemark("备注信息");
        activityModel.setActivityDesc("描述信息");
        activityModelList.add(activityModel);
        //活动周期对象
        ActivityPeriodModel period = new ActivityPeriodModel();
        period.setActivityCode(ACTIVITY_CODE);
        //店铺出参添加
        List<TPromoActivityStoreVO> storeVOs = Lists.newArrayList();
        TPromoActivityStoreVO storeVO = new TPromoActivityStoreVO();
        storeVO.setOrgCode("1");
        storeVO.setStoreName("店铺1");
        storeVOs.add(storeVO);
        //函数
        List<ActivityFunctionParamRankModel> rankVOs = Lists.newArrayList();
        ActivityFunctionParamRankModel rankModel = new ActivityFunctionParamRankModel();
        rankModel.setId("1");
        rankVOs.add(rankModel);
        FunctionParamModel functionParamModel = new FunctionParamModel();
        when(couponCodeUserService.queryUserCouponByUserId(Mockito.any(),Mockito.any())).thenReturn(pageData);
        when(activityService.queryActivityByActivityCodes(Mockito.any(),Mockito.any())).thenReturn(activityModelList);
//        doNothing().when(activityComponentDomain).loadActivityLanguage(Mockito.any(),Mockito.any());
        doNothing().when(couponActivityComponent).expireCoupon(Mockito.any());
        when(activityExpireComponentDomain.expireActivity(activityModelList)).thenReturn(0);
//        when(activityPeriodService.findPeriod(Mockito.any(),Mockito.any())).thenReturn(period);
//        when(tPromoActivityStoreService.getStoresByActivityCode(Mockito.any())).thenReturn(storeVOs);
//        when(promoActivityFuncRankService.getRankListByActivityCode(Mockito.any())).thenReturn(rankVOs);
//        when(promoActivityFuncParamService.getRuleFuncParamListByRankIdAndFunctionCode(Mockito.any(),Mockito.any())).thenReturn(functionParamModel);
        PageData<CouponDomain> responsePageData = filterCouponComponent.queryUserCouponList(param);
        Assert.assertNotNull(responsePageData);
    }


    @Test
    public void queryUserCouponListActivity_2(){
        //given
        //入参
        QueryCouponListByUserParam param = new QueryCouponListByUserParam();
        param.setPageNum(1);
        param.setPageSize(1);
        param.setUserCode("1");
        param.setDomainCode("1");
        param.setTenantCode("1");
        param.setUserCode("1");
        param.setTenantCode("2");
        param.setStatus("01");
        param.setTakeLabel("ss");
        param.setBeginTime("121");
        param.setEndTime("33");
        param.setOrgCode("111");
        param.setFlag(true);
        List<String> stringList = new ArrayList<>();
        stringList.add("1");
        param.setOpsTypeList(stringList);

        //查询用户券返回结果
        List<CouponDomain> list = Lists.newArrayList();
        CouponDomain couponDomain = new CouponDomain();
        couponDomain.setActivityCode(ACTIVITY_CODE);
        couponDomain.setFaceValue(new BigDecimal(0.1));
        couponDomain.setUsedRefId(Constants.DEFAULT_USED_REF_ID);
        list.add(couponDomain);
        PageData<CouponDomain> pageData = new PageData<>();
        pageData.setTotal(1L);
        pageData.setList(list);
        //活动返回结果
        List<ActivityModel> activityModelList = Lists.newArrayList();
        ActivityModel activityModel = new ActivityModel();
        activityModel.setActivityCode(ACTIVITY_CODE);
        activityModel.setTemplateCode(TEMPLATE_CODE);
        activityModel.setActivityLabel("01");
        activityModel.setActivityName("活动名称");
        activityModel.setActivityStatus(ActivityStatusEnum.EFFECTIVE.code());
        activityModel.setActivityUrl("");
        activityModel.setActivityRemark("备注信息");
        activityModel.setActivityDesc("描述信息");
        activityModelList.add(activityModel);
        //活动周期对象
        ActivityPeriodModel period = new ActivityPeriodModel();
        period.setActivityCode(ACTIVITY_CODE);
        //店铺出参添加
        List<TPromoActivityStoreVO> storeVOs = Lists.newArrayList();
        TPromoActivityStoreVO storeVO = new TPromoActivityStoreVO();
        storeVO.setOrgCode("111");
        storeVO.setStoreName("店铺1");
        storeVOs.add(storeVO);
        //函数
        List<ActivityFunctionParamRankModel> rankVOs = Lists.newArrayList();
        ActivityFunctionParamRankModel rankModel = new ActivityFunctionParamRankModel();
        rankModel.setId("1");
        rankVOs.add(rankModel);
        FunctionParamModel functionParamModel = new FunctionParamModel();
        when(couponCodeUserService.queryUserCouponByUserId(Mockito.any(),Mockito.any())).thenReturn(pageData);
        when(activityService.queryActivityByActivityCodes(Mockito.any(),Mockito.any())).thenReturn(activityModelList);
//        doNothing().when(activityComponentDomain).loadActivityLanguage(Mockito.any(),Mockito.any());
        doNothing().when(couponActivityComponent).expireCoupon(Mockito.any());
        when(activityExpireComponentDomain.expireActivity(activityModelList)).thenReturn(0);
//        when(activityPeriodService.findPeriod(Mockito.any(),Mockito.any())).thenReturn(period);
//        when(tPromoActivityStoreService.getStoresByActivityCode(Mockito.any())).thenReturn(storeVOs);
//        when(promoActivityFuncRankService.getRankListByActivityCode(Mockito.any())).thenReturn(rankVOs);
//        when(promoActivityFuncParamService.getRuleFuncParamListByRankIdAndFunctionCode(Mockito.any(),Mockito.any())).thenReturn(functionParamModel);
        PageData<CouponDomain> responsePageData = filterCouponComponent.queryUserCouponList(param);
        Assert.assertNotNull(responsePageData);
    }


    @Test
    public void queryUserCouponListActivity_3(){
        //given
        //入参
        QueryCouponListByUserParam param = new QueryCouponListByUserParam();
        param.setPageNum(1);
        param.setPageSize(1);
        param.setUserCode("1");
        param.setDomainCode("1");
        param.setTenantCode("1");
        param.setUserCode("1");
        param.setTenantCode("2");
        param.setStatus("01");
        param.setTakeLabel("ss");
        param.setBeginTime("121");
        param.setEndTime("33");
        param.setOrgCode("1111");
        param.setFlag(true);
        List<String> stringList = new ArrayList<>();
        stringList.add("1");
        param.setOpsTypeList(stringList);

        //查询用户券返回结果
        List<CouponDomain> list = Lists.newArrayList();
        CouponDomain couponDomain = new CouponDomain();
        couponDomain.setActivityCode(ACTIVITY_CODE);
        couponDomain.setFaceValue(new BigDecimal(0.1));
        couponDomain.setUsedRefId(Constants.DEFAULT_USED_REF_ID);
        list.add(couponDomain);
        PageData<CouponDomain> pageData = new PageData<>();
        pageData.setTotal(1L);
        pageData.setList(list);
        //活动返回结果
        List<ActivityModel> activityModelList = Lists.newArrayList();
        ActivityModel activityModel = new ActivityModel();
        activityModel.setActivityCode(ACTIVITY_CODE);
        activityModel.setTemplateCode(TEMPLATE_CODE);
        activityModel.setActivityLabel("01");
        activityModel.setActivityName("活动名称");
        activityModel.setActivityStatus(ActivityStatusEnum.EFFECTIVE.code());
        activityModel.setActivityUrl("");
        activityModel.setActivityRemark("备注信息");
        activityModel.setActivityDesc("描述信息");
        activityModelList.add(activityModel);
        //活动周期对象
        ActivityPeriodModel period = new ActivityPeriodModel();
        period.setActivityCode(ACTIVITY_CODE);
        //函数
        List<ActivityFunctionParamRankModel> rankVOs = Lists.newArrayList();
        ActivityFunctionParamRankModel rankModel = new ActivityFunctionParamRankModel();
        rankModel.setId("1");
        rankVOs.add(rankModel);
        FunctionParamModel functionParamModel = new FunctionParamModel();
        when(couponCodeUserService.queryUserCouponByUserId(Mockito.any(),Mockito.any())).thenReturn(pageData);
        when(activityService.queryActivityByActivityCodes(Mockito.any(),Mockito.any())).thenReturn(activityModelList);
//        doNothing().when(activityComponentDomain).loadActivityLanguage(Mockito.any(),Mockito.any());
        doNothing().when(couponActivityComponent).expireCoupon(Mockito.any());
        when(activityExpireComponentDomain.expireActivity(activityModelList)).thenReturn(0);
//        when(activityPeriodService.findPeriod(Mockito.any(),Mockito.any())).thenReturn(period);
//        when(tPromoActivityStoreService.getStoresByActivityCode(Mockito.any())).thenReturn(new ArrayList<>());
//        when(promoActivityFuncRankService.getRankListByActivityCode(Mockito.any())).thenReturn(rankVOs);
//        when(promoActivityFuncParamService.getRuleFuncParamListByRankIdAndFunctionCode(Mockito.any(),Mockito.any())).thenReturn(functionParamModel);
        PageData<CouponDomain> responsePageData = filterCouponComponent.queryUserCouponList(param);
        Assert.assertNotNull(responsePageData);
    }


    /**
     * 根据商品查询促销列表(入参)
     */
    @Test
    public void searchActivityByProduct(){

        //given
        TenantProductDTO tenantProductDTO = new TenantProductDTO();
        tenantProductDTO.setChannelCode("123");
        tenantProductDTO.setLanguage("zh");
        tenantProductDTO.setTenantCode("12312");
        tenantProductDTO.setBrandCode("0000");
        tenantProductDTO.setSkuCode("sku");
        tenantProductDTO.setProductCode("product");
        List<String> org = new ArrayList<>();
        org.add("org");
        org.add("");
        tenantProductDTO.setOrgCodes(org);
        // 获取缓存的有效优惠券活动
        ActivityModel activityModel = new ActivityModel();
        activityModel.setActivityType(ActivityTypeEnum.COUPON.code());
        activityModel.setActivityCode(ACTIVITY_CODE);
        activityModel.setActivityBegin("20210425000000");
        activityModel.setShowFlag(1);
        Date date = DateUtils.addDays(new Date(), 1);
        String dateString = DateUtil.getDateString(date, DateUtil.DATETIMESTOREFORMAT);
        activityModel.setActivityEnd(dateString);
        activityModel.setTemplateCode("0104020103010406");

        Map<String, ActivityCacheDTO> activityMap = new HashMap<>();
        ActivityCacheDTO activityCacheDTO = new ActivityCacheDTO();
        activityCacheDTO.setActivityModel(activityModel);
        //店铺
        List<TPromoActivityStoreVO> promoStores = Lists.newArrayList();
        TPromoActivityStoreVO storeVO = new TPromoActivityStoreVO();
        storeVO.setOrgCode("org");
        storeVO.setUrl("http://gtech.com.cn");
        promoStores.add(storeVO);
        activityCacheDTO.setPromoChannels(promoStores);

//        //资格
//        List<QualificationModel> qualificationModels = Lists.newArrayList();
//        QualificationModel qualificationModel = new QualificationModel();
//        qualificationModels.add(qualificationModel);
//        activityCacheDTO.setQualificationModels(qualificationModels);

        //函数
        List<FunctionParamModel> promoFuncParams =Lists.newArrayList();
        FunctionParamModel functionParamModel = new FunctionParamModel();
        functionParamModel.setFunctionCode("0406");
        functionParamModel.setFunctionType("04");
        functionParamModel.setParamValue("1");
        promoFuncParams.add(functionParamModel);
        activityCacheDTO.setPromoFuncParams(promoFuncParams);
        activityMap.put(ACTIVITY_CODE,activityCacheDTO);
        //商品sku
        List<ProductSkuDetailDTO> productSkuDetailDTOS = Lists.newArrayList();
        ProductSkuDetailDTO productSkuDetailDTO = new ProductSkuDetailDTO();
        productSkuDetailDTOS.add(productSkuDetailDTO);
        //活动周期对象
        ActivityPeriodModel period = new ActivityPeriodModel();
        period.setActivityCode(ACTIVITY_CODE);
        period.setIntervalWeek(1);
        period.setBeginPeriod("* * 8 * * ? *");
        period.setEndPeriod("* * 20 * * ? *");
        //券投放
        List<CouponReleaseDomain> couponReleaseDomainList = Lists.newArrayList();
        CouponReleaseDomain couponReleaseDomain = new CouponReleaseDomain();
        couponReleaseDomain.setActivityCode(ACTIVITY_CODE);
        couponReleaseDomain.setReleaseStatus(CouponReleaseStatusEnum.CREATE_COUPON.code());
        couponReleaseDomain.setValidStartTime("20210425000000");
        couponReleaseDomain.setValidEndTime(dateString);
        couponReleaseDomainList.add(couponReleaseDomain);
        //模板
        TemplateModel templateModel = new TemplateModel();
        templateModel.setTagCode("org");
        when(activityCacheDomain.getActivityCacheMap(Mockito.any(),Mockito.any(),Mockito.anyInt(),Mockito.any())).thenReturn(activityMap);
        when(productDetailService.queryListByActivityCodesAndProductCode(Mockito.any(),Mockito.any())).thenReturn(productSkuDetailDTOS);
        when(activityCacheDomain.filterActivityByProduct(Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(activityMap);
		when(promoCouponReleaseService.queryReleasesByActivityCodes(Mockito.any(), Mockito.any(), Mockito.anyBoolean())).thenReturn(couponReleaseDomainList);
        when(templateService.getTemplateByCode(Mockito.any())).thenReturn(templateModel);
        List<CouponActivityProductDTO> couponActivityProductDTOS = filterCouponComponent.searchActivityByProduct(tenantProductDTO);
        Assert.assertNotNull(couponActivityProductDTOS);

    }

    //无缓存的券活动情况
    @Test
    public void  filterCoupon_NoCouponActivity() {
        Map<String, ActivityCacheDTO> activityMap = new HashMap<>();
        ActivityCacheDTO activityCacheDTO = new ActivityCacheDTO();
        ActivityModel activityModel = new ActivityModel();
        activityModel.setActivityCode(ACTIVITY_CODE);
        activityModel.setTemplateCode(TEMPLATE_CODE);
        activityModel.setActivityLabel("01");
        activityModel.setActivityName("活动名称");
        activityModel.setActivityStatus(ActivityStatusEnum.EFFECTIVE.code());
        activityModel.setActivityUrl("");
        activityModel.setActivityRemark("备注信息");
        activityModel.setActivityDesc("描述信息");
        activityCacheDTO.setActivityModel(activityModel);
        //店铺
        List<TPromoActivityStoreVO> promoStores = Lists.newArrayList();
        TPromoActivityStoreVO storeVO = new TPromoActivityStoreVO();
        storeVO.setOrgCode("org");
        storeVO.setUrl("http://gtech.com.cn");
        promoStores.add(storeVO);
        activityCacheDTO.setPromoChannels(promoStores);
        //given
        ShoppingCartDTO shoppingCart = new ShoppingCartDTO();
        shoppingCart.setCouponCodes("123456");
        List<ShoppingCartStore> stores = Lists.newArrayList();
        ShoppingCartStore shoppingCartStore = new ShoppingCartStore();
        shoppingCartStore.setOrgCode("org");
        stores.add(shoppingCartStore);
        shoppingCart.setCartStoreList(stores);
        //券码
        List<TPromoCouponInnerCodeVO> couponInnerCodes = new ArrayList<>();
        TPromoCouponInnerCodeVO tPromoCouponInnerCodeVO = new TPromoCouponInnerCodeVO();
        tPromoCouponInnerCodeVO.setCouponCode("123456");
        tPromoCouponInnerCodeVO.setFrozenStatus(CouponFrozenStatusEnum.UN_FROZEN.code());
        tPromoCouponInnerCodeVO.setStatus(CouponStatusEnum.GRANTED.code());
        tPromoCouponInnerCodeVO.setCouponType(CouponTypeEnum.PROMOTION_COUPON.code());
        tPromoCouponInnerCodeVO.setActivityCode(ACTIVITY_CODE);
        couponInnerCodes.add(tPromoCouponInnerCodeVO);
        //用户券
        TPromoCouponCodeUserVO userVO = new TPromoCouponCodeUserVO();
        Date date = DateUtils.addDays(new Date(), 1);
        String dateString = DateUtil.getDateString(date, DateUtil.DATETIMESTOREFORMAT);
        userVO.setValidStartTime("20210425000000");
        userVO.setValidEndTime(dateString);
        //商品明细
        List<ShoppingCartItem> shoppingCartItems = Lists.newArrayList();
        ShoppingCartItem shoppingCartItem = new ShoppingCartItem();
        shoppingCartItem.setProductCode("productCode");
        shoppingCartItems.add(shoppingCartItem);
        shoppingCart.setPromoProducts(shoppingCartItems);
        //缓存
        Map<String, ActivityCacheDTO> productCouponActivityMap = new HashMap<>();
        activityMap.put(ACTIVITY_CODE,activityCacheDTO);

        when(couponInnerCodeService.getCouponInnerCodeByCodes(Mockito.any(), Mockito.any())).thenReturn(couponInnerCodes);
        when(couponCodeUserService.getUserCouponInfo(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(userVO);
        when(activityCacheDomain.getActivityCacheMap(Mockito.any(), Mockito.any(), Mockito.anyInt(), Mockito.any())).thenReturn(activityMap);
        List<ProductSkuDetailDTO> productSkuDetailDTOS = Lists.newArrayList();
        when(productDetailService.queryListByActivityCodesAndProductCodes(Mockito.any(), Mockito.any())).thenReturn(productSkuDetailDTOS);
        when(activityCacheDomain.filterActivityByProduct(Mockito.any(), Mockito.any(),Mockito.any())).thenReturn(productCouponActivityMap);
        List<CouponInfoDTO> couponInfoDTOS = filterCouponComponent.filterCoupon(shoppingCart);
        Assert.assertNotNull(couponInfoDTOS);

    }

    //有缓存的券活动情况
    @Test(expected = PromotionCouponException.class)
    public void  filterCoupon_HaveCouponActivity() {
        //given
        Map<String, ActivityCacheDTO> activityMap = new HashMap<>();
        ActivityCacheDTO activityCacheDTO = new ActivityCacheDTO();
        ActivityModel activityModel = new ActivityModel();
        activityModel.setActivityCode(ACTIVITY_CODE);
        activityModel.setTemplateCode(TEMPLATE_CODE);
        activityModel.setActivityLabel("01");
        activityModel.setActivityName("活动名称");
        activityModel.setActivityStatus(ActivityStatusEnum.EFFECTIVE.code());
        activityModel.setActivityUrl("http://gtech.com.cn");
        activityModel.setActivityRemark("备注信息");
        activityModel.setActivityDesc("描述信息");
        activityCacheDTO.setActivityModel(activityModel);
        activityMap.put("1",activityCacheDTO);
        //店铺
        List<TPromoActivityStoreVO> promoStores = Lists.newArrayList();
        TPromoActivityStoreVO storeVO = new TPromoActivityStoreVO();
        storeVO.setOrgCode("org");
        storeVO.setUrl("http://gtech.com.cn");
        promoStores.add(storeVO);
        activityCacheDTO.setPromoChannels(promoStores);
        //购物车
        ShoppingCartDTO shoppingCart = new ShoppingCartDTO();
        shoppingCart.setCouponCodes("123456");
        List<ShoppingCartStore> stores = Lists.newArrayList();
        ShoppingCartStore shoppingCartStore = new ShoppingCartStore();
        shoppingCartStore.setOrgCode("org");
        stores.add(shoppingCartStore);
        shoppingCart.setCartStoreList(stores);
        //券码
        List<TPromoCouponInnerCodeVO> couponInnerCodes = new ArrayList<>();
        TPromoCouponInnerCodeVO vo = new TPromoCouponInnerCodeVO();
        TPromoCouponInnerCodeVO tPromoCouponInnerCodeVO = new TPromoCouponInnerCodeVO();
        tPromoCouponInnerCodeVO.setCouponCode("123456");
        tPromoCouponInnerCodeVO.setFrozenStatus(CouponFrozenStatusEnum.UN_FROZEN.code());
        tPromoCouponInnerCodeVO.setStatus(CouponStatusEnum.GRANTED.code());
        tPromoCouponInnerCodeVO.setCouponType(CouponTypeEnum.PROMOTION_COUPON.code());
        tPromoCouponInnerCodeVO.setActivityCode(ACTIVITY_CODE);
        couponInnerCodes.add(tPromoCouponInnerCodeVO);
        //用户券
        TPromoCouponCodeUserVO userVO = new TPromoCouponCodeUserVO();
        Date date = DateUtils.addDays(new Date(), 1);
        String dateString = DateUtil.getDateString(date, DateUtil.DATETIMESTOREFORMAT);
        userVO.setValidStartTime("20210425000000");
        userVO.setValidEndTime(dateString);

        //商品明细
        List<ShoppingCartItem> shoppingCartItems = Lists.newArrayList();
        ShoppingCartItem shoppingCartItem = new ShoppingCartItem();
        shoppingCartItem.setProductCode("productCode");
        shoppingCartItem.setProductPrice(new BigDecimal(1));
        shoppingCartItem.setQuantity(1);
        shoppingCartItem.setProductAmount(new BigDecimal(1));
        shoppingCartItems.add(shoppingCartItem);
        shoppingCart.setPromoProducts(shoppingCartItems);
        //缓存
        Map<String, ActivityCacheDTO> productCouponActivityMap = new HashMap<>();
        productCouponActivityMap.put(ACTIVITY_CODE,activityCacheDTO);

        when(couponInnerCodeService.getCouponInnerCodeByCodes(Mockito.any(), Mockito.any())).thenReturn(couponInnerCodes);
        when(couponCodeUserService.getUserCouponInfo(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(userVO);
        when(activityCacheDomain.getActivityCacheMap(Mockito.any(), Mockito.any(), Mockito.anyInt(), Mockito.any())).thenReturn(activityMap);
        List<ProductSkuDetailDTO> productSkuDetailDTOS = Lists.newArrayList();
        ProductSkuDetailDTO productSkuDetailDTO = new ProductSkuDetailDTO();
        productSkuDetailDTO.setProductCode("productCode");
        productSkuDetailDTOS.add(productSkuDetailDTO);
        Mockito.when(productDetailService.queryListByActivityCodesAndProductCodes(Mockito.any(), Mockito.any())).thenReturn(productSkuDetailDTOS);
        Mockito.when(activityCacheDomain.filterActivityByProduct(Mockito.any(), Mockito.any(),Mockito.any())).thenReturn(productCouponActivityMap);
        List<TPromoCouponCodeUserVO> couponCodeUserVOs = Lists.newArrayList();
        couponCodeUserVOs.add(userVO);
        Mockito.when(couponCodeUserService.getMyCodesByActivityCodes(Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(couponCodeUserVOs);
        Mockito.when(shoppingCartDomain.queryActivity(Mockito.any(),Mockito.any(),Mockito.anyBoolean())).thenReturn(shoppingCart);

        List<ShoppingCartOutDTO> scOutList = Lists.newArrayList();
        ShoppingCartOutDTO shoppingCartOutDTO = new ShoppingCartOutDTO();
        BeanCopyUtils.copyProps(shoppingCart,shoppingCartOutDTO);
        shoppingCartOutDTO.setCouponCode("123456");
        shoppingCartOutDTO.setRewardTimes(1);
        shoppingCartOutDTO.setEffectiveFlag(true);
        shoppingCartOutDTO.setActivityType(ActivityTypeEnum.COUPON.code());
        scOutList.add(shoppingCartOutDTO);
//        when(calcExecuter.calc(Mockito.any(),Mockito.any())).thenReturn(scOutList);
//        when(executorService.invokeAll(Mockito.any(), Mockito.any(),Mockito.any())).thenReturn(null);
//        executorService.invokeAll(collection, 3000L, TimeUnit.MILLISECONDS);
        List<CouponInfoDTO> couponInfoDTOS = filterCouponComponent.filterCoupon(shoppingCart);
        Assert.assertNotNull(couponInfoDTOS);
    }



    /**
     * 根据商品查询促销列表(入参)
     */
    @Test
    public void searchActivityByProductList(){

        //given
        TenantProductDTO tenantProductDTO = new TenantProductDTO();
        tenantProductDTO.setChannelCode("123");
        tenantProductDTO.setLanguage("zh");
        tenantProductDTO.setTenantCode("12312");
        tenantProductDTO.setBrandCode("0000");
        tenantProductDTO.setSkuCode("sku");
        tenantProductDTO.setProductCode("product");
        List<String> org = new ArrayList<>();
        org.add("org");
        org.add("");
        tenantProductDTO.setOrgCodes(org);
        // 获取缓存的有效优惠券活动
        ActivityModel activityModel = new ActivityModel();
        activityModel.setActivityType(ActivityTypeEnum.COUPON.code());
        activityModel.setActivityCode(ACTIVITY_CODE);
        activityModel.setActivityBegin("20210425000000");
        activityModel.setShowFlag(1);
        Date date = DateUtils.addDays(new Date(), 1);
        String dateString = DateUtil.getDateString(date, DateUtil.DATETIMESTOREFORMAT);
        activityModel.setActivityEnd(dateString);
        activityModel.setTemplateCode("0104020103010406");

        Map<String, ActivityCacheDTO> activityMap = new HashMap<>();
        ActivityCacheDTO activityCacheDTO = new ActivityCacheDTO();
        activityCacheDTO.setActivityModel(activityModel);
        //店铺
        List<TPromoActivityStoreVO> promoStores = Lists.newArrayList();
        TPromoActivityStoreVO storeVO = new TPromoActivityStoreVO();
        storeVO.setOrgCode("org");
        storeVO.setUrl("http://gtech.com.cn");
        promoStores.add(storeVO);
        activityCacheDTO.setPromoChannels(promoStores);

//        //资格
//        List<QualificationModel> qualificationModels = Lists.newArrayList();
//        QualificationModel qualificationModel = new QualificationModel();
//        qualificationModels.add(qualificationModel);
//        activityCacheDTO.setQualificationModels(qualificationModels);

        //函数
        List<FunctionParamModel> promoFuncParams =Lists.newArrayList();
        FunctionParamModel functionParamModel = new FunctionParamModel();
        functionParamModel.setFunctionCode("0406");
        functionParamModel.setFunctionType("04");
        functionParamModel.setParamValue("1");
        promoFuncParams.add(functionParamModel);
        activityCacheDTO.setPromoFuncParams(promoFuncParams);
        //模板
        TemplateModel templateModel = new TemplateModel();
        templateModel.setTemplateCode("0104020103010406");
        templateModel.setTagCode("org");
        activityCacheDTO.setPromoTemplate(templateModel);
        activityMap.put(ACTIVITY_CODE,activityCacheDTO);
        //商品sku
        List<ProductSkuDetailDTO> productSkuDetailDTOS = Lists.newArrayList();
        ProductSkuDetailDTO productSkuDetailDTO = new ProductSkuDetailDTO();
        productSkuDetailDTOS.add(productSkuDetailDTO);
        //活动周期对象
        ActivityPeriodModel period = new ActivityPeriodModel();
        period.setActivityCode(ACTIVITY_CODE);
        period.setIntervalWeek(1);
        period.setBeginPeriod("* * 8 * * ? *");
        period.setEndPeriod("* * 20 * * ? *");
        //券投放
        List<CouponReleaseDomain> couponReleaseDomainList = Lists.newArrayList();
        CouponReleaseDomain couponReleaseDomain = new CouponReleaseDomain();
        couponReleaseDomain.setActivityCode(ACTIVITY_CODE);
        couponReleaseDomain.setReleaseStatus(CouponReleaseStatusEnum.CREATE_COUPON.code());
        couponReleaseDomain.setValidStartTime("20210425000000");
        couponReleaseDomain.setValidEndTime(dateString);
        couponReleaseDomainList.add(couponReleaseDomain);

        when(activityCacheDomain.getActivityCacheMap(Mockito.any(),Mockito.any(),Mockito.anyInt(),Mockito.any())).thenReturn(activityMap);
        when(activityCacheDomain.filterActivityByProduct(Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(activityMap);
		when(promoCouponReleaseService.queryReleasesByActivityCodes(Mockito.any(), Mockito.any(), Mockito.anyBoolean())).thenReturn(couponReleaseDomainList);
        when(templateService.queryTemplateAll()).thenReturn(Collections.singletonList(templateModel));

        QueryCouponActivityListByProductListParam param = new QueryCouponActivityListByProductListParam();

        List<QueryCouponActivityListByProductListParam.Product> productList = new ArrayList<>();

        QueryCouponActivityListByProductListParam.Product product = new QueryCouponActivityListByProductListParam.Product();
        product.setProductCode("1");
        product.setSkuCode("1");
        productList.add(product);
        param.setProductList(productList);
        List<QueryCouponActivityListByProductListResult> queryCouponActivityListByProductListResults = filterCouponComponent.searchActivityByProductList(param);


        Assert.assertNotNull(queryCouponActivityListByProductListResults);

    }


    /**
     * 根据商品查询促销列表(入参)
     */
    @Test
    public void searchActivityByProductList_orgCode(){

        //given
        TenantProductDTO tenantProductDTO = new TenantProductDTO();
        tenantProductDTO.setChannelCode("123");
        tenantProductDTO.setLanguage("zh");
        tenantProductDTO.setTenantCode("12312");
        tenantProductDTO.setBrandCode("0000");
        tenantProductDTO.setSkuCode("sku");
        tenantProductDTO.setProductCode("product");
        List<String> org = new ArrayList<>();
        org.add("org");
        org.add("");
        tenantProductDTO.setOrgCodes(org);
        // 获取缓存的有效优惠券活动
        ActivityModel activityModel = new ActivityModel();
        activityModel.setActivityType(ActivityTypeEnum.COUPON.code());
        activityModel.setActivityCode(ACTIVITY_CODE);
        activityModel.setActivityBegin("20210425000000");
        activityModel.setShowFlag(1);
        Date date = DateUtils.addDays(new Date(), 1);
        String dateString = DateUtil.getDateString(date, DateUtil.DATETIMESTOREFORMAT);
        activityModel.setActivityEnd(dateString);
        activityModel.setTemplateCode("0104020103010406");

        Map<String, ActivityCacheDTO> activityMap = new HashMap<>();
        ActivityCacheDTO activityCacheDTO = new ActivityCacheDTO();
        activityCacheDTO.setActivityModel(activityModel);
        //店铺
        List<TPromoActivityStoreVO> promoStores = Lists.newArrayList();
        TPromoActivityStoreVO storeVO = new TPromoActivityStoreVO();
        storeVO.setOrgCode("org");
        storeVO.setUrl("http://gtech.com.cn");
        promoStores.add(storeVO);
        activityCacheDTO.setPromoChannels(promoStores);

        //函数
        List<FunctionParamModel> promoFuncParams =Lists.newArrayList();
        FunctionParamModel functionParamModel = new FunctionParamModel();
        functionParamModel.setFunctionCode("0406");
        functionParamModel.setFunctionType("04");
        functionParamModel.setParamValue("1");
        promoFuncParams.add(functionParamModel);
        activityCacheDTO.setPromoFuncParams(promoFuncParams);
        activityMap.put(ACTIVITY_CODE,activityCacheDTO);
        //商品sku
        List<ProductSkuDetailDTO> productSkuDetailDTOS = Lists.newArrayList();
        ProductSkuDetailDTO productSkuDetailDTO = new ProductSkuDetailDTO();
        productSkuDetailDTOS.add(productSkuDetailDTO);
        //活动周期对象
        ActivityPeriodModel period = new ActivityPeriodModel();
        period.setActivityCode(ACTIVITY_CODE);
        period.setIntervalWeek(1);
        period.setBeginPeriod("* * 8 * * ? *");
        period.setEndPeriod("* * 20 * * ? *");
        //券投放
        List<CouponReleaseDomain> couponReleaseDomainList = Lists.newArrayList();
        CouponReleaseDomain couponReleaseDomain = new CouponReleaseDomain();
        couponReleaseDomain.setActivityCode(ACTIVITY_CODE);
        couponReleaseDomain.setReleaseStatus(CouponReleaseStatusEnum.CREATE_COUPON.code());
        couponReleaseDomain.setValidStartTime("20210425000000");
        couponReleaseDomain.setValidEndTime(dateString);
        couponReleaseDomainList.add(couponReleaseDomain);
        //模板
        TemplateModel templateModel = new TemplateModel();
        templateModel.setTagCode("org");
        when(activityCacheDomain.getActivityCacheMap(Mockito.any(),Mockito.any(),Mockito.anyInt(),Mockito.any())).thenReturn(activityMap);
        when(activityCacheDomain.filterActivityByProduct(Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(activityMap);
//        when(templateService.getTemplateByCode(Mockito.any())).thenReturn(templateModel);
        when(promoCouponReleaseService.queryReleasesByActivityCodes(Mockito.any(), Mockito.any(), Mockito.anyBoolean())).thenReturn(couponReleaseDomainList);

        QueryCouponActivityListByProductListParam param = new QueryCouponActivityListByProductListParam();
        param.setOrgCode("orgCode");
        List<QueryCouponActivityListByProductListParam.Product> productList = new ArrayList<>();

        QueryCouponActivityListByProductListParam.Product product = new QueryCouponActivityListByProductListParam.Product();
        product.setProductCode("1");
        product.setSkuCode("1");
        productList.add(product);
        param.setProductList(productList);
        List<QueryCouponActivityListByProductListResult> queryCouponActivityListByProductListResults = filterCouponComponent.searchActivityByProductList(param);


        Assert.assertNotNull(queryCouponActivityListByProductListResults);

    }

    @Test
    public void getCouponCodeUsers(){
        CouponInfoDTO couponInfoDTO = new CouponInfoDTO();
        couponInfoDTO.setActivityId("");
        couponInfoDTO.setCouponCode("");
        couponInfoDTO.setCouponType("");
        couponInfoDTO.setCouponStatus("");
        couponInfoDTO.setValidBegin("");
        couponInfoDTO.setValidEnd("");
        couponInfoDTO.setIsReward(false);
        couponInfoDTO.setActivityCode("123");
        couponInfoDTO.setActivityName("");
        couponInfoDTO.setActivityLabel("");
        couponInfoDTO.setActivityDesc("");
        couponInfoDTO.setActivityShortDesc("");
        couponInfoDTO.setUserlimitMax(0);
        couponInfoDTO.setFaceValue(new BigDecimal("0"));
        couponInfoDTO.setFaceUnit("");
        couponInfoDTO.setConditionUnit("");
        couponInfoDTO.setConditionValue(new BigDecimal("0"));
        couponInfoDTO.setRewardType("");
        couponInfoDTO.setActivityPeriod(new ActivityPeriod());


        List<CouponInfoDTO> result = new ArrayList<>();
        result.add(couponInfoDTO);

        ActivityCacheDTO activityCacheDTO = new ActivityCacheDTO();
        activityCacheDTO.setVersion("");
        activityCacheDTO.setSeqNum("");
        activityCacheDTO.setActivityModel(new ActivityModel());
        activityCacheDTO.setPromoTemplate(new TemplateModel());
        activityCacheDTO.setPeriodModel(new ActivityPeriodModel());
        activityCacheDTO.setQualificationModels(Lists.newArrayList());
        activityCacheDTO.setPromoFuncRanks(Lists.newArrayList());
        activityCacheDTO.setPromoFuncParams(Lists.newArrayList());
        activityCacheDTO.setPromoTemplateFunctions(Lists.newArrayList());
        activityCacheDTO.setPromoChannels(Lists.newArrayList());
        activityCacheDTO.setGiveaways(Lists.newArrayList());
        activityCacheDTO.setPromoProducts(Lists.newArrayList());
        activityCacheDTO.setPromoProductDetails(Maps.newHashMap());
        activityCacheDTO.setPromoProductCombines(Lists.newArrayList());
        activityCacheDTO.setIncentiveLimiteds(Lists.newArrayList());
        activityCacheDTO.setLanguageMap(Maps.newHashMap());



        Map<String, ActivityCacheDTO> couponActivityMap = new HashMap<>();
        couponActivityMap.put("1234",activityCacheDTO);

        TPromoCouponCodeUserVO userVO = new TPromoCouponCodeUserVO();
        userVO.setValidTime(new CouponReleaseDomain(),"");
        userVO.setId("");
        userVO.setCreateUser("");
        userVO.setLogicDelete("");
        userVO.setTenantCode("");
        userVO.setActivityCode("123");
        userVO.setReleaseCode("");
        userVO.setCouponType("");
        userVO.setOpsType("");
        userVO.setStatus("");
        userVO.setFrozenStatus("");
        userVO.setCouponCode("");
        userVO.setUserCode("");
        userVO.setFaceValue(new BigDecimal("0"));
        userVO.setFaceUnit("");
        userVO.setTakeLabel("");
        userVO.setValidStartTime("");
        userVO.setValidEndTime("");
        userVO.setReceivedTime("");
        userVO.setUsedRefId("");
        userVO.setUsedTime("");
        userVO.setCreateTime(new Date());

        List<TPromoCouponCodeUserVO> couponCodeUserVOs = new ArrayList<>();
        couponCodeUserVOs.add(userVO);

        Set<String> selectCouponCodeSet = new HashSet<>();
        Map<String, ErrorCode > errorCouponMap = new HashMap<>();
        ActivityModel activityModel = new ActivityModel();
        activityModel.setCouponInfo(new ActivityModel());
        activityModel.setLanguage(new ActivityLanguageModel());
        activityModel.setItemScopeType(0);
        activityModel.setId(0L);
        activityModel.setDomainCode("");
        activityModel.setTenantCode("");
        activityModel.setActivityCode("");
        activityModel.setCustomCondition("");
        activityModel.setActivityType("");
        activityModel.setActivityLabel("");
        activityModel.setActivityName("");
        activityModel.setActivityDesc("");
        activityModel.setShowFlag(0);
        activityModel.setActivityRemark("");
        activityModel.setActivityExpr("");
        activityModel.setPriority(0);
        activityModel.setTemplateCode("");
        activityModel.setIncentiveLimitedFlag("");
        activityModel.setWarmBegin("");
        activityModel.setWarmEnd("");
        activityModel.setActivityBegin("");
        activityModel.setActivityEnd("20300101010101");
        activityModel.setPeriodType("");
        activityModel.setProductSelectionType("");
        activityModel.setStoreType("");
        activityModel.setActivityUrl("");
        activityModel.setActivityStatus("");
        activityModel.setSponsors("");
        activityModel.setOpsType("");
        activityModel.setExclusionKey("");
        activityModel.setCreateUser("");
        activityModel.setCreateTime(new Date());
        activityModel.setUpdateUser("");
        activityModel.setTotalQuantity(0);
        activityModel.setUserLimitMax(0);
        activityModel.setCouponType("");
        activityModel.setCouponCode("");
        activityModel.setReserveInventory(0);
        activityModel.setCoolDown("1");
        activityModel.setBackgroundImage("1");
        activityModel.setProductCondition("1");
        activityModel.setRibbonImage("1");
        activityModel.setRibbonPosition("1");
        activityModel.setRibbonText("1");
        activityModel.setCouponCodes(Lists.newArrayList());
        activityModel.setTemplateCode("0101010101010101");
        CouponReleaseDomain releaseDomain = new CouponReleaseDomain();
        releaseDomain.setTenantCode("11");
        releaseDomain.setActivityCode("1");
        releaseDomain.setCouponType("1");
        releaseDomain.setReleaseCode("1");
        releaseDomain.setReleaseStatus("1");
        releaseDomain.setReleaseQuantity(0);
        releaseDomain.setInventory(0);
        releaseDomain.setReceivedQuantity(0);
        releaseDomain.setUsedTotal(0);
        releaseDomain.setLocked(0);
        releaseDomain.setReleaseSource("1");
        releaseDomain.setReceiveStartTime("1");
        releaseDomain.setReceiveEndTime("20300101010101");
        releaseDomain.setValidDays(0);
        releaseDomain.setValidStartTime("1");
        releaseDomain.setValidEndTime("1");
        releaseDomain.setReleaseTime("1");
        releaseDomain.setReleaseType("1");
        releaseDomain.setCouponCodePrefix("1");
        releaseDomain.setTimeSameActivity("1");
        ConditionAndFace conditionAndFace = new ConditionAndFace();
        conditionAndFace.setConditionValue(new BigDecimal("0"));
        conditionAndFace.setConditionUnit("1");
        conditionAndFace.setFaceValue(new BigDecimal("0"));
        conditionAndFace.setFaceUnit("1");

        Mockito.when(couponActivityComponent.findValidActivity(Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(activityModel);
        Mockito.when(promoCouponReleaseService.findCouponReleaseByReleaseCode(Mockito.any(),Mockito.any())).thenReturn(releaseDomain);
        Mockito.when(functionParamDomain.findConditionAndFaceByActivityCode(Mockito.any())).thenReturn(conditionAndFace);
        filterCouponComponent.getCouponCodeUsers(result,couponActivityMap,couponCodeUserVOs,selectCouponCodeSet,errorCouponMap,releaseDomain.getTenantCode());


    }

    @Test
    public void getCouponCodeUsers1(){
        CouponInfoDTO couponInfoDTO = new CouponInfoDTO();
        couponInfoDTO.setActivityId("");
        couponInfoDTO.setCouponCode("");
        couponInfoDTO.setCouponType("");
        couponInfoDTO.setCouponStatus("");
        couponInfoDTO.setValidBegin("");
        couponInfoDTO.setValidEnd("");
        couponInfoDTO.setIsReward(false);
        couponInfoDTO.setActivityCode("123");
        couponInfoDTO.setActivityName("");
        couponInfoDTO.setActivityLabel("");
        couponInfoDTO.setActivityDesc("");
        couponInfoDTO.setActivityShortDesc("");
        couponInfoDTO.setUserlimitMax(0);
        couponInfoDTO.setFaceValue(new BigDecimal("0"));
        couponInfoDTO.setFaceUnit("");
        couponInfoDTO.setConditionUnit("");
        couponInfoDTO.setConditionValue(new BigDecimal("0"));
        couponInfoDTO.setRewardType("");
        couponInfoDTO.setActivityPeriod(new ActivityPeriod());


        List<CouponInfoDTO> result = new ArrayList<>();
        result.add(couponInfoDTO);

        ActivityCacheDTO activityCacheDTO = new ActivityCacheDTO();
        activityCacheDTO.setVersion("");
        activityCacheDTO.setSeqNum("");
        ActivityModel activityModel1 = new ActivityModel();
        activityModel1.setActivityCode("123");
        activityModel1.setTemplateCode("1111111111111111111111");
        activityCacheDTO.setActivityModel(activityModel1);
        activityCacheDTO.setPromoTemplate(new TemplateModel());
        activityCacheDTO.setPeriodModel(new ActivityPeriodModel());
        activityCacheDTO.setQualificationModels(Lists.newArrayList());
        activityCacheDTO.setPromoFuncRanks(Lists.newArrayList());
        activityCacheDTO.setPromoFuncParams(Lists.newArrayList());
        activityCacheDTO.setPromoTemplateFunctions(Lists.newArrayList());
        activityCacheDTO.setPromoChannels(Lists.newArrayList());
        activityCacheDTO.setGiveaways(Lists.newArrayList());
        activityCacheDTO.setPromoProducts(Lists.newArrayList());
        activityCacheDTO.setPromoProductDetails(Maps.newHashMap());
        activityCacheDTO.setPromoProductCombines(Lists.newArrayList());
        activityCacheDTO.setIncentiveLimiteds(Lists.newArrayList());
        activityCacheDTO.setLanguageMap(Maps.newHashMap());



        Map<String, ActivityCacheDTO> couponActivityMap = new HashMap<>();
        couponActivityMap.put("123",activityCacheDTO);

        TPromoCouponCodeUserVO userVO = new TPromoCouponCodeUserVO();
        userVO.setValidTime(new CouponReleaseDomain(),"");
        userVO.setId("");
        userVO.setCreateUser("");
        userVO.setLogicDelete("");
        userVO.setTenantCode("");
        userVO.setActivityCode("123");
        userVO.setReleaseCode("");
        userVO.setCouponType("");
        userVO.setOpsType("");
        userVO.setStatus("");
        userVO.setFrozenStatus("");
        userVO.setCouponCode("");
        userVO.setUserCode("");
        userVO.setFaceValue(new BigDecimal("0"));
        userVO.setFaceUnit("");
        userVO.setTakeLabel("");
        userVO.setValidStartTime("");
        userVO.setValidEndTime("");
        userVO.setReceivedTime("");
        userVO.setUsedRefId("");
        userVO.setUsedTime("");
        userVO.setCreateTime(new Date());

        List<TPromoCouponCodeUserVO> couponCodeUserVOs = new ArrayList<>();
        couponCodeUserVOs.add(userVO);

        Set<String> selectCouponCodeSet = new HashSet<>();
        Map<String, ErrorCode > errorCouponMap = new HashMap<>();
        ActivityModel activityModel = new ActivityModel();
        activityModel.setCouponInfo(new ActivityModel());
        activityModel.setLanguage(new ActivityLanguageModel());
        activityModel.setItemScopeType(0);
        activityModel.setId(0L);
        activityModel.setDomainCode("");
        activityModel.setTenantCode("");
        activityModel.setActivityCode("123");
        activityModel.setCustomCondition("");
        activityModel.setActivityType("");
        activityModel.setActivityLabel("");
        activityModel.setActivityName("");
        activityModel.setActivityDesc("");
        activityModel.setShowFlag(0);
        activityModel.setActivityRemark("");
        activityModel.setActivityExpr("");
        activityModel.setPriority(0);
        activityModel.setTemplateCode("");
        activityModel.setIncentiveLimitedFlag("");
        activityModel.setWarmBegin("");
        activityModel.setWarmEnd("");
        activityModel.setActivityBegin("");
        activityModel.setActivityEnd("20300101010101");
        activityModel.setPeriodType("");
        activityModel.setProductSelectionType("");
        activityModel.setStoreType("");
        activityModel.setActivityUrl("");
        activityModel.setActivityStatus("");
        activityModel.setSponsors("");
        activityModel.setOpsType("");
        activityModel.setExclusionKey("");
        activityModel.setCreateUser("");
        activityModel.setCreateTime(new Date());
        activityModel.setUpdateUser("");
        activityModel.setTotalQuantity(0);
        activityModel.setUserLimitMax(0);
        activityModel.setCouponType("");
        activityModel.setCouponCode("");
        activityModel.setReserveInventory(0);
        activityModel.setCoolDown("1");
        activityModel.setBackgroundImage("1");
        activityModel.setProductCondition("1");
        activityModel.setRibbonImage("1");
        activityModel.setRibbonPosition("1");
        activityModel.setRibbonText("1");
        activityModel.setCouponCodes(Lists.newArrayList());
        activityModel.setTemplateCode("0101010101010213101");
        CouponReleaseDomain releaseDomain = new CouponReleaseDomain();
        releaseDomain.setTenantCode("11");
        releaseDomain.setActivityCode("123");
        releaseDomain.setCouponType("1");
        releaseDomain.setReleaseCode("1");
        releaseDomain.setReleaseStatus("1");
        releaseDomain.setReleaseQuantity(0);
        releaseDomain.setInventory(0);
        releaseDomain.setReceivedQuantity(0);
        releaseDomain.setUsedTotal(0);
        releaseDomain.setLocked(0);
        releaseDomain.setReleaseSource("1");
        releaseDomain.setReceiveStartTime("1");
        releaseDomain.setReceiveEndTime("20300101010101");
        releaseDomain.setValidDays(0);
        releaseDomain.setValidStartTime("1");
        releaseDomain.setValidEndTime("1");
        releaseDomain.setReleaseTime("1");
        releaseDomain.setReleaseType("1");
        releaseDomain.setCouponCodePrefix("1");
        releaseDomain.setTimeSameActivity("1");
        ConditionAndFace conditionAndFace = new ConditionAndFace();
        conditionAndFace.setConditionValue(new BigDecimal("0"));
        conditionAndFace.setConditionUnit("1");
        conditionAndFace.setFaceValue(new BigDecimal("0"));
        conditionAndFace.setFaceUnit("1");

//        Mockito.when(couponActivityComponent.findValidActivity(Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(activityModel);
//        Mockito.when(promoCouponReleaseService.findCouponReleaseByReleaseCode(Mockito.any(),Mockito.any())).thenReturn(releaseDomain);

        Map<String,ConditionAndFace> map = new HashMap<>();
        map.put("123",conditionAndFace);

        Mockito.when(functionParamDomain.findConditionAndFaceByActivityCodesCache(Mockito.any())).thenReturn(map);
        filterCouponComponent.getCouponCodeUsers(result,couponActivityMap,couponCodeUserVOs,selectCouponCodeSet,errorCouponMap,releaseDomain.getTenantCode());

    }


    @Test
    public void getCouponCodeUsers_couponInfoDto_null(){
        CouponInfoDTO couponInfoDTO = new CouponInfoDTO();
        couponInfoDTO.setActivityId("");
        couponInfoDTO.setCouponCode("");
        couponInfoDTO.setCouponType("");
        couponInfoDTO.setCouponStatus("");
        couponInfoDTO.setValidBegin("");
        couponInfoDTO.setValidEnd("");
        couponInfoDTO.setIsReward(false);
        couponInfoDTO.setActivityCode("1");
        couponInfoDTO.setActivityName("");
        couponInfoDTO.setActivityLabel("");
        couponInfoDTO.setActivityDesc("");
        couponInfoDTO.setActivityShortDesc("");
        couponInfoDTO.setUserlimitMax(0);
        couponInfoDTO.setFaceValue(new BigDecimal("0"));
        couponInfoDTO.setFaceUnit("");
        couponInfoDTO.setConditionUnit("");
        couponInfoDTO.setConditionValue(new BigDecimal("0"));
        couponInfoDTO.setRewardType("");
        couponInfoDTO.setActivityPeriod(new ActivityPeriod());


        List<CouponInfoDTO> result = new ArrayList<>();
        result.add(couponInfoDTO);

        ActivityCacheDTO activityCacheDTO = new ActivityCacheDTO();
        activityCacheDTO.setVersion("");
        activityCacheDTO.setSeqNum("");
        ActivityModel activityModel1 = new ActivityModel();
        activityModel1.setTemplateCode("111111111111");
        activityCacheDTO.setActivityModel(activityModel1);
        activityCacheDTO.setPromoTemplate(new TemplateModel());
        activityCacheDTO.setPeriodModel(new ActivityPeriodModel());
        activityCacheDTO.setQualificationModels(Lists.newArrayList());
        activityCacheDTO.setPromoFuncRanks(Lists.newArrayList());
        activityCacheDTO.setPromoFuncParams(Lists.newArrayList());
        activityCacheDTO.setPromoTemplateFunctions(Lists.newArrayList());
        activityCacheDTO.setPromoChannels(Lists.newArrayList());
        activityCacheDTO.setGiveaways(Lists.newArrayList());
        activityCacheDTO.setPromoProducts(Lists.newArrayList());
        activityCacheDTO.setPromoProductDetails(Maps.newHashMap());
        activityCacheDTO.setPromoProductCombines(Lists.newArrayList());
        activityCacheDTO.setIncentiveLimiteds(Lists.newArrayList());
        activityCacheDTO.setLanguageMap(Maps.newHashMap());



        Map<String, ActivityCacheDTO> couponActivityMap = new HashMap<>();
        couponActivityMap.put("123",activityCacheDTO);

        TPromoCouponCodeUserVO userVO = new TPromoCouponCodeUserVO();
        userVO.setValidTime(new CouponReleaseDomain(),"");
        userVO.setId("");
        userVO.setCreateUser("");
        userVO.setLogicDelete("");
        userVO.setTenantCode("");
        userVO.setActivityCode("1");
        userVO.setReleaseCode("");
        userVO.setCouponType("");
        userVO.setOpsType("");
        userVO.setStatus("");
        userVO.setFrozenStatus("");
        userVO.setCouponCode("");
        userVO.setUserCode("");
        userVO.setFaceValue(new BigDecimal("0"));
        userVO.setFaceUnit("");
        userVO.setTakeLabel("");
        userVO.setValidStartTime("");
        userVO.setValidEndTime("");
        userVO.setReceivedTime("");
        userVO.setUsedRefId("");
        userVO.setUsedTime("");
        userVO.setCreateTime(new Date());

        List<TPromoCouponCodeUserVO> couponCodeUserVOs = new ArrayList<>();
        couponCodeUserVOs.add(userVO);

        Set<String> selectCouponCodeSet = new HashSet<>();
        Map<String, ErrorCode > errorCouponMap = new HashMap<>();
        ActivityModel activityModel = new ActivityModel();
        activityModel.setCouponInfo(new ActivityModel());
        activityModel.setLanguage(new ActivityLanguageModel());
        activityModel.setItemScopeType(0);
        activityModel.setId(0L);
        activityModel.setDomainCode("");
        activityModel.setTenantCode("");
        activityModel.setActivityCode("1");
        activityModel.setCustomCondition("");
        activityModel.setActivityType("");
        activityModel.setActivityLabel("");
        activityModel.setActivityName("");
        activityModel.setActivityDesc("");
        activityModel.setShowFlag(0);
        activityModel.setActivityRemark("");
        activityModel.setActivityExpr("");
        activityModel.setPriority(0);
        activityModel.setTemplateCode("");
        activityModel.setIncentiveLimitedFlag("");
        activityModel.setWarmBegin("");
        activityModel.setWarmEnd("");
        activityModel.setActivityBegin("");
        activityModel.setActivityEnd("20300101010101");
        activityModel.setPeriodType("");
        activityModel.setProductSelectionType("");
        activityModel.setStoreType("");
        activityModel.setActivityUrl("");
        activityModel.setActivityStatus("");
        activityModel.setSponsors("");
        activityModel.setOpsType("");
        activityModel.setExclusionKey("");
        activityModel.setCreateUser("");
        activityModel.setCreateTime(new Date());
        activityModel.setUpdateUser("");
        activityModel.setTotalQuantity(0);
        activityModel.setUserLimitMax(0);
        activityModel.setCouponType("");
        activityModel.setCouponCode("");
        activityModel.setReserveInventory(0);
        activityModel.setCoolDown("1");
        activityModel.setBackgroundImage("1");
        activityModel.setProductCondition("1");
        activityModel.setRibbonImage("1");
        activityModel.setRibbonPosition("1");
        activityModel.setRibbonText("1");
        activityModel.setCouponCodes(Lists.newArrayList());
        activityModel.setTemplateCode("0101010101010101");

        activityCacheDTO.setActivityModel(activityModel);
        CouponReleaseDomain releaseDomain = new CouponReleaseDomain();
        releaseDomain.setTenantCode("11");
        releaseDomain.setActivityCode("1");
        releaseDomain.setCouponType("1");
        releaseDomain.setReleaseCode("1");
        releaseDomain.setReleaseStatus("1");
        releaseDomain.setReleaseQuantity(0);
        releaseDomain.setInventory(0);
        releaseDomain.setReceivedQuantity(0);
        releaseDomain.setUsedTotal(0);
        releaseDomain.setLocked(0);
        releaseDomain.setReleaseSource("1");
        releaseDomain.setReceiveStartTime("1");
        releaseDomain.setReceiveEndTime("20300101010101");
        releaseDomain.setValidDays(0);
        releaseDomain.setValidStartTime("1");
        releaseDomain.setValidEndTime("1");
        releaseDomain.setReleaseTime("1");
        releaseDomain.setReleaseType("1");
        releaseDomain.setCouponCodePrefix("1");
        releaseDomain.setTimeSameActivity("1");
        ConditionAndFace conditionAndFace = new ConditionAndFace();
        conditionAndFace.setConditionValue(new BigDecimal("0"));
        conditionAndFace.setConditionUnit("1");
        conditionAndFace.setFaceValue(new BigDecimal("0"));
        conditionAndFace.setFaceUnit("1");

        Map<String,ConditionAndFace> map = new HashMap<>();
        map.put("1",conditionAndFace);

//        Mockito.when(functionParamDomain.findConditionAndFaceByActivityCodes(Mockito.any(),Mockito.anyList())).thenReturn(map);
        filterCouponComponent.getCouponCodeUsers(result,couponActivityMap,couponCodeUserVOs,selectCouponCodeSet,errorCouponMap,releaseDomain.getTenantCode());


    }



    @Test
    public void getFuncParams_03_null(){

        ActivityCacheDTO cacheDTO = new ActivityCacheDTO();
        cacheDTO.setVersion("");
        cacheDTO.setSeqNum("");
        cacheDTO.setActivityModel(new ActivityModel());
        cacheDTO.setPromoTemplate(new TemplateModel());
        cacheDTO.setPeriodModel(new ActivityPeriodModel());
        cacheDTO.setQualificationModels(Lists.newArrayList());
        cacheDTO.setPromoFuncRanks(Lists.newArrayList());

        FunctionParamModel functionParamModel = new FunctionParamModel();
        functionParamModel.setId("");
        functionParamModel.setTenantCode("");
        functionParamModel.setRankId("");
        functionParamModel.setFunctionType("03");
        functionParamModel.setFunctionCode("");
        functionParamModel.setFunctionName("");
        functionParamModel.setParamType("");
        functionParamModel.setParamValue("1");
        functionParamModel.setParamUnit("1");
        functionParamModel.setRankParam(0);


        List<FunctionParamModel> promoFuncParams = new ArrayList<>();
        promoFuncParams.add(functionParamModel);


        cacheDTO.setPromoFuncParams(promoFuncParams);
        cacheDTO.setPromoTemplateFunctions(Lists.newArrayList());
        cacheDTO.setPromoChannels(Lists.newArrayList());
        cacheDTO.setGiveaways(Lists.newArrayList());
        cacheDTO.setPromoProducts(Lists.newArrayList());
        cacheDTO.setPromoProductDetails(Maps.newHashMap());
        cacheDTO.setPromoProductCombines(Lists.newArrayList());
        cacheDTO.setIncentiveLimiteds(Lists.newArrayList());
        cacheDTO.setLanguageMap(Maps.newHashMap());



        CouponActivityProductDTO activityProductDTO = new CouponActivityProductDTO();
        activityProductDTO.setFaceValue(new BigDecimal("0"));
        activityProductDTO.setFaceUnit("");
        activityProductDTO.setGiftLimitMax("");
        activityProductDTO.setCouponType("");
        activityProductDTO.setUserLimitMax(0);
        activityProductDTO.setRewardType("");
        activityProductDTO.setConditionUnit("");
        activityProductDTO.setConditionValue(new BigDecimal("0"));
        activityProductDTO.setCouponValidTime(Lists.newArrayList());
        activityProductDTO.setId("");
        activityProductDTO.setActivityType("");
        activityProductDTO.setActivityCode("");
        activityProductDTO.setActivityName("");
        activityProductDTO.setActivityDesc("");
        activityProductDTO.setActivityRemark("");
        activityProductDTO.setActivityLabel("");
        activityProductDTO.setActivitySort("");
        activityProductDTO.setActivityBegin("");
        activityProductDTO.setActivityEnd("");
        activityProductDTO.setWarmBegin("");
        activityProductDTO.setCoolDown("");
        activityProductDTO.setOpsType("");
        activityProductDTO.setQualifications(Lists.newArrayList());
        activityProductDTO.setPromoScope("");
        activityProductDTO.setWarmEnd("");
        activityProductDTO.setActivityUrl("");
        activityProductDTO.setProductSelectionType("");
        activityProductDTO.setGiftLimitMax("");
        activityProductDTO.setGiveaways(Lists.newArrayList());
        activityProductDTO.setSeqNum("");
        activityProductDTO.setBackgroundImage("");
        activityProductDTO.setRibbonImage("");
        activityProductDTO.setRibbonPosition("");
        activityProductDTO.setRibbonText("");
        activityProductDTO.setActivityPeriod(new ActivityPeriod());


        filterCouponComponent.getFuncParams(cacheDTO,activityProductDTO);


    }

    @Test
    public void getFuncParams_02_0401(){

        ActivityCacheDTO cacheDTO = new ActivityCacheDTO();
        cacheDTO.setVersion("");
        cacheDTO.setSeqNum("");
        cacheDTO.setActivityModel(new ActivityModel());
        cacheDTO.setPromoTemplate(new TemplateModel());
        cacheDTO.setPeriodModel(new ActivityPeriodModel());
        cacheDTO.setQualificationModels(Lists.newArrayList());
        cacheDTO.setPromoFuncRanks(Lists.newArrayList());

        FunctionParamModel functionParamModel = new FunctionParamModel();
        functionParamModel.setId("");
        functionParamModel.setTenantCode("");
        functionParamModel.setRankId("");
        functionParamModel.setFunctionType("02");
        functionParamModel.setFunctionCode("0401");
        functionParamModel.setFunctionName("");
        functionParamModel.setParamType("02");
        functionParamModel.setParamValue("1");
        functionParamModel.setParamUnit("1");
        functionParamModel.setRankParam(0);


        List<FunctionParamModel> promoFuncParams = new ArrayList<>();
        promoFuncParams.add(functionParamModel);


        cacheDTO.setPromoFuncParams(promoFuncParams);
        cacheDTO.setPromoTemplateFunctions(Lists.newArrayList());
        cacheDTO.setPromoChannels(Lists.newArrayList());
        cacheDTO.setGiveaways(Lists.newArrayList());
        cacheDTO.setPromoProducts(Lists.newArrayList());
        cacheDTO.setPromoProductDetails(Maps.newHashMap());
        cacheDTO.setPromoProductCombines(Lists.newArrayList());
        cacheDTO.setIncentiveLimiteds(Lists.newArrayList());
        cacheDTO.setLanguageMap(Maps.newHashMap());



        CouponActivityProductDTO activityProductDTO = new CouponActivityProductDTO();
        activityProductDTO.setFaceValue(new BigDecimal("0"));
        activityProductDTO.setFaceUnit("");
        activityProductDTO.setGiftLimitMax("");
        activityProductDTO.setCouponType("");
        activityProductDTO.setUserLimitMax(0);
        activityProductDTO.setRewardType("");
        activityProductDTO.setConditionUnit("");
        activityProductDTO.setConditionValue(new BigDecimal("0"));
        activityProductDTO.setCouponValidTime(Lists.newArrayList());
        activityProductDTO.setId("");
        activityProductDTO.setActivityType("");
        activityProductDTO.setActivityCode("");
        activityProductDTO.setActivityName("");
        activityProductDTO.setActivityDesc("");
        activityProductDTO.setActivityRemark("");
        activityProductDTO.setActivityLabel("");
        activityProductDTO.setActivitySort("");
        activityProductDTO.setActivityBegin("");
        activityProductDTO.setActivityEnd("");
        activityProductDTO.setWarmBegin("");
        activityProductDTO.setCoolDown("");
        activityProductDTO.setOpsType("");
        activityProductDTO.setQualifications(Lists.newArrayList());
        activityProductDTO.setPromoScope("");
        activityProductDTO.setWarmEnd("");
        activityProductDTO.setActivityUrl("");
        activityProductDTO.setProductSelectionType("");
        activityProductDTO.setGiftLimitMax("");
        activityProductDTO.setGiveaways(Lists.newArrayList());
        activityProductDTO.setSeqNum("");
        activityProductDTO.setBackgroundImage("");
        activityProductDTO.setRibbonImage("");
        activityProductDTO.setRibbonPosition("");
        activityProductDTO.setRibbonText("");
        activityProductDTO.setActivityPeriod(new ActivityPeriod());


        filterCouponComponent.getFuncParams(cacheDTO,activityProductDTO);


    }

    @Test
    public void getFuncParams_02_0402(){

        ActivityCacheDTO cacheDTO = new ActivityCacheDTO();
        cacheDTO.setVersion("");
        cacheDTO.setSeqNum("");
        cacheDTO.setActivityModel(new ActivityModel());
        cacheDTO.setPromoTemplate(new TemplateModel());
        cacheDTO.setPeriodModel(new ActivityPeriodModel());
        cacheDTO.setQualificationModels(Lists.newArrayList());
        cacheDTO.setPromoFuncRanks(Lists.newArrayList());

        FunctionParamModel functionParamModel = new FunctionParamModel();
        functionParamModel.setId("");
        functionParamModel.setTenantCode("");
        functionParamModel.setRankId("");
        functionParamModel.setFunctionType("02");
        functionParamModel.setFunctionCode("0402");
        functionParamModel.setFunctionName("");
        functionParamModel.setParamType("02");
        functionParamModel.setParamValue("1");
        functionParamModel.setParamUnit("1");
        functionParamModel.setRankParam(0);


        List<FunctionParamModel> promoFuncParams = new ArrayList<>();
        promoFuncParams.add(functionParamModel);


        cacheDTO.setPromoFuncParams(promoFuncParams);
        cacheDTO.setPromoTemplateFunctions(Lists.newArrayList());
        cacheDTO.setPromoChannels(Lists.newArrayList());
        cacheDTO.setGiveaways(Lists.newArrayList());
        cacheDTO.setPromoProducts(Lists.newArrayList());
        cacheDTO.setPromoProductDetails(Maps.newHashMap());
        cacheDTO.setPromoProductCombines(Lists.newArrayList());
        cacheDTO.setIncentiveLimiteds(Lists.newArrayList());
        cacheDTO.setLanguageMap(Maps.newHashMap());



        CouponActivityProductDTO activityProductDTO = new CouponActivityProductDTO();
        activityProductDTO.setFaceValue(new BigDecimal("0"));
        activityProductDTO.setFaceUnit("");
        activityProductDTO.setGiftLimitMax("");
        activityProductDTO.setCouponType("");
        activityProductDTO.setUserLimitMax(0);
        activityProductDTO.setRewardType("");
        activityProductDTO.setConditionUnit("");
        activityProductDTO.setConditionValue(new BigDecimal("0"));
        activityProductDTO.setCouponValidTime(Lists.newArrayList());
        activityProductDTO.setId("");
        activityProductDTO.setActivityType("");
        activityProductDTO.setActivityCode("");
        activityProductDTO.setActivityName("");
        activityProductDTO.setActivityDesc("");
        activityProductDTO.setActivityRemark("");
        activityProductDTO.setActivityLabel("");
        activityProductDTO.setActivitySort("");
        activityProductDTO.setActivityBegin("");
        activityProductDTO.setActivityEnd("");
        activityProductDTO.setWarmBegin("");
        activityProductDTO.setCoolDown("");
        activityProductDTO.setOpsType("");
        activityProductDTO.setQualifications(Lists.newArrayList());
        activityProductDTO.setPromoScope("");
        activityProductDTO.setWarmEnd("");
        activityProductDTO.setActivityUrl("");
        activityProductDTO.setProductSelectionType("");
        activityProductDTO.setGiftLimitMax("");
        activityProductDTO.setGiveaways(Lists.newArrayList());
        activityProductDTO.setSeqNum("");
        activityProductDTO.setBackgroundImage("");
        activityProductDTO.setRibbonImage("");
        activityProductDTO.setRibbonPosition("");
        activityProductDTO.setRibbonText("");
        activityProductDTO.setActivityPeriod(new ActivityPeriod());


        filterCouponComponent.getFuncParams(cacheDTO,activityProductDTO);


    }


    @Test
    public void frozenCouponCode(){
        FrozenCouponCodeInDTO frozenDTO = new FrozenCouponCodeInDTO();
        filterCouponComponent.frozenCouponCode(frozenDTO);

    }

    @Test(expected = PromotionException.class)
    public void frozenCouponCode_1(){

        FrozenCouponCodeInDTO frozenDTO = new FrozenCouponCodeInDTO();

        //校验是否过期
        Mockito.when(couponInnerCodeService.frozenFindInnerCode(Mockito.any(),Mockito.any())).thenReturn(1);


        filterCouponComponent.frozenCouponCode(frozenDTO);

    }

    @Test
    public void chooseCouponsByCart() throws InterruptedException {
        ThreadPoolUtil.setExecutorService(new ThreadPoolExecutor(10,10,3000L
                ,TimeUnit.MILLISECONDS,new SynchronousQueue<>(), new ThreadPoolExecutor.CallerRunsPolicy()));

        ShoppingCartDTO shoppingCart = new ShoppingCartDTO();
        shoppingCart.setCartStoreList(Lists.newArrayList());
        shoppingCart.setDomainCode("1");
        shoppingCart.setTenantCode("1");
        shoppingCart.setLanguage("1");
        shoppingCart.setOpsTypeList(Lists.newArrayList());
        shoppingCart.setChannelCode(0);
        shoppingCart.setUserCode("1");
        shoppingCart.setPostage(new BigDecimal("0"));
        shoppingCart.setQualifications(Maps.newHashMap());
        shoppingCart.setCouponCodes("1");
        shoppingCart.setPromotionTime("1");
        shoppingCart.setPromoProducts(Lists.newArrayList());
        shoppingCart.setErrorCouponAndReasons(Lists.newArrayList());
        shoppingCart.setActivityExpr("1");
        shoppingCart.setHighPrioriyActivities(Sets.newHashSet());
        PageData<CouponDomain> pageData = new PageData<>();
        pageData.setTotal(2L);
        CouponDomain couponDomain = new CouponDomain();
        couponDomain.setTenantCode("");
        couponDomain.setActivityCode("");
        couponDomain.setActivityName("");
        couponDomain.setActivityLabel("");
        couponDomain.setActivityDesc("");
        couponDomain.setActivityRemark("");
        couponDomain.setActivityStatus("");
        couponDomain.setCouponCode("");
        couponDomain.setCouponType("");
        couponDomain.setOpsType("");
        couponDomain.setFaceValue(new BigDecimal("0"));
        couponDomain.setFaceUnit("");
        couponDomain.setConditionValue(new BigDecimal("0"));
        couponDomain.setConditionUnit("");
        couponDomain.setActivityUrl("");
        couponDomain.setStatus("");
        couponDomain.setFrozenStatus("");
        couponDomain.setReceivedTime("");
        couponDomain.setUsedTime("");
        couponDomain.setValidStartTime("");
        couponDomain.setValidEndTime("");
        couponDomain.setTakeLabel("");
        couponDomain.setUsedRefId("");
        couponDomain.setRewardType("");
        couponDomain.setStores(Lists.newArrayList());
        couponDomain.setActivityPeriod(new ActivityPeriod());
        couponDomain.setFrozenStatus("01");

        ArrayList<CouponDomain> couponDomains = new ArrayList<>();
        couponDomains.add(couponDomain);
        pageData.setList(couponDomains);
        List<Future<List<ShoppingCartOutDTO>>> futures = new ArrayList<>();
        Mockito.when(couponCodeUserService.getUserCouponByUserId(Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(pageData);
        List<CouponChooseCallable> callAbles = new ArrayList<>();
        filterCouponComponent.chooseCouponsByCart(shoppingCart);

    }


    @Test
    public void getBigDecimal(){
        Map<String, List<CouponDomain>> couponMap = new HashMap<>();
        ShoppingCartOutDTO shoppingCartOutDTO = new ShoppingCartOutDTO();
        shoppingCartOutDTO.setLastScale();
        shoppingCartOutDTO.setActivityCode("");
        shoppingCartOutDTO.setActivityName("");
        shoppingCartOutDTO.setCouponCode("");
        shoppingCartOutDTO.setPromoScope("");
        shoppingCartOutDTO.setActivityLabel("");
        shoppingCartOutDTO.setActivityType("");
        shoppingCartOutDTO.setActivityUrl("");
        shoppingCartOutDTO.setProductSelectionType("");
        shoppingCartOutDTO.setGiveawayLimitMax("");
        shoppingCartOutDTO.setEffectiveFlag(false);
        shoppingCartOutDTO.setNeedMoreAmount("");
        shoppingCartOutDTO.setNeedMoreUnit("");
        shoppingCartOutDTO.setPromoRewardAmount(new BigDecimal("0"));
        shoppingCartOutDTO.setPromoRewardPostage(new BigDecimal("0"));
        shoppingCartOutDTO.setRewardType("");
        shoppingCartOutDTO.setUserLimitation("");
        shoppingCartOutDTO.setGiveaways(Lists.newArrayList());
        shoppingCartOutDTO.setShoppingCartItems(Lists.newArrayList());
        shoppingCartOutDTO.setTemplateCode("");
        shoppingCartOutDTO.setConditionUnit("");
        shoppingCartOutDTO.setConditionValue(new BigDecimal("0"));
        shoppingCartOutDTO.setBeforeAmount(new BigDecimal("0"));
        shoppingCartOutDTO.setAfterAmount(new BigDecimal("0"));
        shoppingCartOutDTO.setSponsors("");
        shoppingCartOutDTO.setActivityPeriod(new ActivityPeriod());
        shoppingCartOutDTO.setRibbonImage("");
        shoppingCartOutDTO.setRibbonPosition("");
        shoppingCartOutDTO.setRibbonText("");
        shoppingCartOutDTO.setRewardTimes(0);
        shoppingCartOutDTO.setEffectiveFlag(true);
        shoppingCartOutDTO.setCouponCode("123");
        List<CouponDomain> couponDomains = new ArrayList<>();

        CouponDomain couponDomain = new CouponDomain();
        couponDomain.setTenantCode("");
        couponDomain.setActivityCode("");
        couponDomain.setActivityName("");
        couponDomain.setActivityLabel("");
        couponDomain.setActivityDesc("");
        couponDomain.setActivityRemark("");
        couponDomain.setActivityStatus("");
        couponDomain.setCouponCode("");
        couponDomain.setCouponType("");
        couponDomain.setOpsType("");
        couponDomain.setFaceValue(new BigDecimal("0"));
        couponDomain.setFaceUnit("");
        couponDomain.setConditionValue(new BigDecimal("0"));
        couponDomain.setConditionUnit("");
        couponDomain.setActivityUrl("");
        couponDomain.setStatus("");
        couponDomain.setFrozenStatus("");
        couponDomain.setReceivedTime("");
        couponDomain.setUsedTime("");
        couponDomain.setValidStartTime("");
        couponDomain.setValidEndTime("");
        couponDomain.setTakeLabel("");
        couponDomain.setUsedRefId("");
        couponDomain.setRewardType("");
        couponDomain.setStores(Lists.newArrayList());
        couponDomain.setActivityPeriod(new ActivityPeriod());
        couponDomains.add(couponDomain);

        couponMap.put("123",couponDomains);
        List<ShoppingCartOutDTO> shoppingCartOutDTOS = new ArrayList<>();
        shoppingCartOutDTOS.add(shoppingCartOutDTO);
        BigDecimal discountAmount = new BigDecimal("1");
        List< ChooseCouponByCartCouponInfoDTO > couponByCartCouponInfoDTOS = new ArrayList<>();


        filterCouponComponent.getBigDecimal(couponMap,shoppingCartOutDTOS,discountAmount,couponByCartCouponInfoDTOS);

    }

    /**
     * 活动不为null
     */
    @Test
    public void queryUserCouponListActivity(){
        //given
        //入参
        QueryCouponListByUserParam param = new QueryCouponListByUserParam();
        param.setPageNum(1);
        param.setPageSize(1);
        param.setUserCode("1");
        param.setDomainCode("1");
        param.setTenantCode("1");
        param.setUserCode("1");
        param.setTenantCode("2");
        param.setStatus("01");
        param.setTakeLabel("ss");
        param.setBeginTime("121");
        param.setEndTime("33");
        List<String> stringList = new ArrayList<>();
        stringList.add("1");
        param.setOpsTypeList(stringList);

        //查询用户券返回结果
        List<CouponDomain> list = Lists.newArrayList();
        CouponDomain couponDomain = new CouponDomain();
        couponDomain.setActivityCode(ACTIVITY_CODE);
        couponDomain.setFaceValue(new BigDecimal(0.1));
        couponDomain.setUsedRefId(Constants.DEFAULT_USED_REF_ID);
        list.add(couponDomain);
        PageData<CouponDomain> pageData = new PageData<>();
        pageData.setTotal(1L);
        pageData.setList(list);
        //活动返回结果
        List<ActivityModel> activityModelList = Lists.newArrayList();
        ActivityModel activityModel = new ActivityModel();
        activityModel.setActivityCode(ACTIVITY_CODE);
        activityModel.setTemplateCode(TEMPLATE_CODE);
        activityModel.setActivityLabel("01");
        activityModel.setActivityName("活动名称");
        activityModel.setActivityStatus(ActivityStatusEnum.EFFECTIVE.code());
        activityModel.setActivityUrl("");
        activityModel.setActivityRemark("备注信息");
        activityModel.setActivityDesc("描述信息");
        activityModelList.add(activityModel);
        //活动周期对象
        ActivityPeriodModel period = new ActivityPeriodModel();
        period.setActivityCode(ACTIVITY_CODE);
        //店铺出参添加
        List<TPromoActivityStoreVO> storeVOs = Lists.newArrayList();
        TPromoActivityStoreVO storeVO = new TPromoActivityStoreVO();
        storeVO.setOrgCode("1");
        storeVO.setStoreName("店铺1");
        storeVOs.add(storeVO);
        //函数
        List<ActivityFunctionParamRankModel> rankVOs = Lists.newArrayList();
        ActivityFunctionParamRankModel rankModel = new ActivityFunctionParamRankModel();
        rankModel.setId("1");
        rankVOs.add(rankModel);
        FunctionParamModel functionParamModel = new FunctionParamModel();
        when(couponCodeUserService.getUserCouponByUserId(Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(pageData);
        when(activityService.queryActivityByActivityCodes(Mockito.any(),Mockito.any())).thenReturn(activityModelList);
//        doNothing().when(activityComponentDomain).loadActivityLanguage(Mockito.any(),Mockito.any());
        doNothing().when(couponActivityComponent).expireCoupon(Mockito.any());
        when(activityExpireComponentDomain.expireActivity(activityModelList)).thenReturn(1);
//        when(activityPeriodService.findPeriod(Mockito.any(),Mockito.any())).thenReturn(period);
//        when(tPromoActivityStoreService.getStoresByActivityCode(Mockito.any())).thenReturn(storeVOs);
//        when(promoActivityFuncRankService.getRankListByActivityCode(Mockito.any())).thenReturn(rankVOs);
//        when(promoActivityFuncParamService.getRuleFuncParamListByRankIdAndFunctionCode(Mockito.any(),Mockito.any())).thenReturn(functionParamModel);
        PageData<CouponDomain> responsePageData = filterCouponComponent.queryUserCouponList(param);
        Assert.assertNotNull(responsePageData);
    }

    @Test
    public void queryUserCouponListActivity11(){
        //given
        //入参
        QueryCouponListByUserParam param = new QueryCouponListByUserParam();
        param.setPageNum(1);
        param.setPageSize(1);
        param.setUserCode("1");
        param.setDomainCode("1");
        param.setTenantCode("1");
        param.setUserCode("1");
        param.setTenantCode("2");
        param.setStatus("01");
        param.setTakeLabel("ss");
        param.setBeginTime("121");
        param.setEndTime("33");
        param.setFlag(true);
        List<String> stringList = new ArrayList<>();
        stringList.add("1");
        param.setOpsTypeList(stringList);
        //查询用户券返回结果
        List<CouponDomain> list = Lists.newArrayList();
        CouponDomain couponDomain = new CouponDomain();
        couponDomain.setActivityCode(ACTIVITY_CODE);
        couponDomain.setFaceValue(new BigDecimal(0.1));
        couponDomain.setUsedRefId(Constants.DEFAULT_USED_REF_ID);
        list.add(couponDomain);
        PageData<CouponDomain> pageData = new PageData<>();
        pageData.setTotal(1L);

        pageData.setList(new ArrayList<>());
        //活动返回结果
        ActivityModel activityModel = new ActivityModel();
        activityModel.setActivityCode(ACTIVITY_CODE);
        activityModel.setTemplateCode(TEMPLATE_CODE);
        activityModel.setActivityLabel("01");
        activityModel.setActivityName("活动名称");
        activityModel.setActivityStatus(ActivityStatusEnum.EFFECTIVE.code());
        activityModel.setActivityUrl("");
        activityModel.setActivityRemark("备注信息");
        activityModel.setActivityDesc("描述信息");
        //活动周期对象
        ActivityPeriodModel period = new ActivityPeriodModel();
        period.setActivityCode(ACTIVITY_CODE);
        //店铺出参添加
        List<TPromoActivityStoreVO> storeVOs = Lists.newArrayList();
        TPromoActivityStoreVO storeVO = new TPromoActivityStoreVO();
        storeVO.setOrgCode("1");
        storeVO.setStoreName("店铺1");
        storeVOs.add(storeVO);
        //函数
        List<ActivityFunctionParamRankModel> rankVOs = Lists.newArrayList();
        ActivityFunctionParamRankModel rankModel = new ActivityFunctionParamRankModel();
        rankModel.setId("1");
        rankVOs.add(rankModel);
        when(couponCodeUserService.queryUserCouponByUserId(Mockito.any(),Mockito.any())).thenReturn(pageData);
        PageData<CouponDomain> responsePageData = filterCouponComponent.queryUserCouponList(param);
        Assert.assertEquals(0,responsePageData.getList().size());
    }

    @Test
    public void queryUserCouponListActivity112(){
        //given
        //入参
        QueryCouponListByUserParam param = new QueryCouponListByUserParam();
        param.setPageNum(1);
        param.setPageSize(1);
        param.setUserCode("1");
        param.setDomainCode("1");
        param.setTenantCode("1");
        param.setUserCode("1");
        param.setTenantCode("2");
        param.setStatus("01");
        param.setTakeLabel("ss");
        param.setBeginTime("121");
        param.setEndTime("33");
        param.setFlag(true);
        List<String> stringList = new ArrayList<>();
        stringList.add("1");
        param.setOpsTypeList(stringList);
        //查询用户券返回结果
        List<CouponDomain> list = Lists.newArrayList();
        CouponDomain couponDomain = new CouponDomain();
        couponDomain.setActivityCode(ACTIVITY_CODE);
        couponDomain.setFaceValue(new BigDecimal(0.1));
        couponDomain.setUsedRefId(Constants.DEFAULT_USED_REF_ID);
        //活动返回结果
        ActivityModel activityModel = new ActivityModel();
        activityModel.setActivityCode(ACTIVITY_CODE);
        activityModel.setTemplateCode(TEMPLATE_CODE);
        activityModel.setActivityLabel("01");
        activityModel.setActivityName("活动名称");
        activityModel.setActivityStatus(ActivityStatusEnum.EFFECTIVE.code());
        activityModel.setActivityUrl("");
        activityModel.setActivityRemark("备注信息");
        activityModel.setActivityDesc("描述信息");
        //活动周期对象
        ActivityPeriodModel period = new ActivityPeriodModel();
        period.setActivityCode(ACTIVITY_CODE);
        //店铺出参添加
        List<TPromoActivityStoreVO> storeVOs = Lists.newArrayList();
        TPromoActivityStoreVO storeVO = new TPromoActivityStoreVO();
        storeVO.setOrgCode("1");
        storeVO.setStoreName("店铺1");
        storeVOs.add(storeVO);
        //函数
        List<ActivityFunctionParamRankModel> rankVOs = Lists.newArrayList();
        ActivityFunctionParamRankModel rankModel = new ActivityFunctionParamRankModel();
        rankModel.setId("1");
        rankVOs.add(rankModel);
        PageData<CouponDomain> responsePageData = filterCouponComponent.queryUserCouponList(param);
        Assert.assertNotNull(responsePageData);
    }

    //无缓存的券活动情况
    @Test
    public void  filterCoupon_NoCouponCodes() {
        Map<String, ActivityCacheDTO> activityMap = new HashMap<>();
        ActivityCacheDTO activityCacheDTO = new ActivityCacheDTO();
        ActivityModel activityModel = new ActivityModel();
        activityModel.setActivityCode(ACTIVITY_CODE);
        activityModel.setTemplateCode(TEMPLATE_CODE);
        activityModel.setActivityLabel("01");
        activityModel.setActivityName("活动名称");
        activityModel.setActivityStatus(ActivityStatusEnum.EFFECTIVE.code());
        activityModel.setActivityUrl("");
        activityModel.setActivityRemark("备注信息");
        activityModel.setActivityDesc("描述信息");
        activityCacheDTO.setActivityModel(activityModel);
        //店铺
        List<TPromoActivityStoreVO> promoStores = Lists.newArrayList();
        TPromoActivityStoreVO storeVO = new TPromoActivityStoreVO();
        storeVO.setOrgCode("org");
        storeVO.setUrl("http://gtech.com.cn");
        promoStores.add(storeVO);
        activityCacheDTO.setPromoChannels(promoStores);
        //given
        ShoppingCartDTO shoppingCart = new ShoppingCartDTO();
        shoppingCart.setCouponCodes("123456");
        List<ShoppingCartStore> stores = Lists.newArrayList();
        ShoppingCartStore shoppingCartStore = new ShoppingCartStore();
        shoppingCartStore.setOrgCode("org");
        stores.add(shoppingCartStore);
        shoppingCart.setCartStoreList(stores);
        //券码
        List<TPromoCouponInnerCodeVO> couponInnerCodes = new ArrayList<>();
        TPromoCouponInnerCodeVO tPromoCouponInnerCodeVO = new TPromoCouponInnerCodeVO();
        tPromoCouponInnerCodeVO.setCouponCode("123456");
        tPromoCouponInnerCodeVO.setFrozenStatus(CouponFrozenStatusEnum.UN_FROZEN.code());
        tPromoCouponInnerCodeVO.setStatus(CouponStatusEnum.GRANTED.code());
        tPromoCouponInnerCodeVO.setCouponType(CouponTypeEnum.PROMOTION_COUPON.code());
        tPromoCouponInnerCodeVO.setActivityCode(ACTIVITY_CODE);
        couponInnerCodes.add(tPromoCouponInnerCodeVO);
        //用户券
        TPromoCouponCodeUserVO userVO = new TPromoCouponCodeUserVO();
        Date date = DateUtils.addDays(new Date(), 1);
        String dateString = DateUtil.getDateString(date, DateUtil.DATETIMESTOREFORMAT);
        userVO.setValidStartTime("20210425000000");
        userVO.setValidEndTime(dateString);
        //商品明细
        List<ShoppingCartItem> shoppingCartItems = Lists.newArrayList();
        ShoppingCartItem shoppingCartItem = new ShoppingCartItem();
        shoppingCartItem.setProductCode("productCode");
        shoppingCartItems.add(shoppingCartItem);
        shoppingCart.setPromoProducts(shoppingCartItems);
        //缓存
        Map<String, ActivityCacheDTO> productCouponActivityMap = new HashMap<>();
        activityMap.put(ACTIVITY_CODE,activityCacheDTO);

        when(couponInnerCodeService.getCouponInnerCodeByCodes(Mockito.any(), Mockito.any())).thenReturn(couponInnerCodes);
        when(couponCodeUserService.getUserCouponInfo(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(userVO);
        when(activityCacheDomain.getActivityCacheMap(Mockito.any(), Mockito.any(), Mockito.anyInt(), Mockito.any())).thenReturn(activityMap);
        List<ProductSkuDetailDTO> productSkuDetailDTOS = Lists.newArrayList();
        when(productDetailService.queryListByActivityCodesAndProductCodes(Mockito.any(), Mockito.any())).thenReturn(productSkuDetailDTOS);
        when(activityCacheDomain.filterActivityByProduct(Mockito.any(), Mockito.any(),Mockito.any())).thenReturn(productCouponActivityMap);
        List<CouponInfoDTO> couponInfoDTOS = filterCouponComponent.filterCoupon(shoppingCart);
        Assert.assertNotNull(couponInfoDTOS);

    }


    @Test
    public void getCouponAndCheckCoupon(){
        ShoppingCartDTO shoppingCart = new ShoppingCartDTO();
        List<String> inputCouponCodes = new ArrayList<>();
        List<TPromoCouponInnerCodeVO> couponInnerCodes = new ArrayList<>();
        List<TPromoCouponCodeUserVO> couponCodeUserVOs = new ArrayList<>();
        List<String> couponCodes = new ArrayList<>();
        filterCouponComponent.getCouponAndCheckCoupon(shoppingCart,inputCouponCodes,couponInnerCodes,couponCodeUserVOs,couponCodes);

        inputCouponCodes.add("c1");
        filterCouponComponent.getCouponAndCheckCoupon(shoppingCart,inputCouponCodes,couponInnerCodes,couponCodeUserVOs,couponCodes);
        TPromoCouponCodeUserVO couponCodeUserVO = new TPromoCouponCodeUserVO();
        couponCodeUserVO.setCouponCode("c1");
        couponCodeUserVO.setReleaseCode("1111");
        couponCodeUserVOs.add(couponCodeUserVO);
        filterCouponComponent.getCouponAndCheckCoupon(shoppingCart,inputCouponCodes,couponInnerCodes,couponCodeUserVOs,couponCodes);
        TPromoCouponInnerCodeVO couponCodeUserVOInner = new TPromoCouponInnerCodeVO();
        couponCodeUserVO.setCouponCode("c2");
        couponCodeUserVO.setReleaseCode("1111");
        couponInnerCodes.add(couponCodeUserVOInner);

        ActivityModel activityModel = new ActivityModel();
        when(activityComponentDomain.findValidActivity(any(),any(),any(),any())).thenReturn(activityModel);
        CouponReleaseDomain couponReleaseDomain= new CouponReleaseDomain();
        couponReleaseDomain.setReleaseCode("1111");
//        when(promoCouponReleaseService.findCouponReleaseByReleaseCode(any(),any())).thenReturn(couponReleaseDomain);
        List<CouponReleaseDomain> couponReleaseDomainList = new ArrayList<>();
        couponReleaseDomainList.add(couponReleaseDomain);
//        when(promoCouponReleaseService.queryReleaseByCondition(Mockito.anyString(),isNull(),Mockito.anyList())).thenReturn(couponReleaseDomainList);

        filterCouponComponent.getCouponAndCheckCoupon(shoppingCart,inputCouponCodes,couponInnerCodes,couponCodeUserVOs,couponCodes);


    }

    @Test
    public void getCouponAndCheckCoupon1(){
        ShoppingCartDTO shoppingCart = new ShoppingCartDTO();
        List<String> inputCouponCodes = new ArrayList<>();
        List<TPromoCouponInnerCodeVO> couponInnerCodes = new ArrayList<>();
        List<TPromoCouponCodeUserVO> couponCodeUserVOs = new ArrayList<>();
        List<String> couponCodes = new ArrayList<>();
        inputCouponCodes.add("c1");
        TPromoCouponCodeUserVO couponCodeUserVO = new TPromoCouponCodeUserVO();
        couponCodeUserVO.setCouponCode("c1");
        couponCodeUserVO.setReleaseCode("1111");
        couponCodeUserVOs.add(couponCodeUserVO);
        TPromoCouponInnerCodeVO couponCodeUserVOInner = new TPromoCouponInnerCodeVO();
        couponCodeUserVO.setCouponCode("c2");
        couponCodeUserVO.setReleaseCode("1111");
        couponInnerCodes.add(couponCodeUserVOInner);

        ActivityModel activityModel = new ActivityModel();
        activityModel.setActivityCode("1111");
        when(activityComponentDomain.findValidActivity(any(),any(),any(),any())).thenReturn(activityModel);
        CouponReleaseDomain couponReleaseDomain= new CouponReleaseDomain();
        couponReleaseDomain.setReleaseCode("1111");
        List<CouponReleaseDomain> couponReleaseDomainList = new ArrayList<>();
        couponReleaseDomainList.add(couponReleaseDomain);
//        when(promoCouponReleaseService.queryReleaseByCondition(Mockito.anyString(),isNull(),Mockito.anyList())).thenReturn(couponReleaseDomainList);

        filterCouponComponent.getCouponAndCheckCoupon(shoppingCart,inputCouponCodes,couponInnerCodes,couponCodeUserVOs,couponCodes);

    }

    @Test
    public void checkResult(){
        List<String> inputCouponCodes = new ArrayList<>();
        boolean validateInputCoupons = true;
        Set<String> selectCouponCodeSet = new HashSet<>();
        Map<String, ErrorCode> errorCouponMap = new HashMap<>();
        Future<ShoppingCartDTO> future = new Future<ShoppingCartDTO>() {
            @Override
            public boolean cancel(boolean mayInterruptIfRunning) {
                return false;
            }

            @Override
            public boolean isCancelled() {
                return false;
            }

            @Override
            public boolean isDone() {
                return false;
            }

            @Override
            public ShoppingCartDTO get() throws InterruptedException, ExecutionException {
                return null;
            }

            @Override
            public ShoppingCartDTO get(long timeout, TimeUnit unit) throws InterruptedException, ExecutionException, TimeoutException {
                return null;
            }
        };
        filterCouponComponent.checkResult(inputCouponCodes,validateInputCoupons,selectCouponCodeSet,errorCouponMap,future);

        Future<ShoppingCartDTO> future1 = new Future<ShoppingCartDTO>() {
            @Override
            public boolean cancel(boolean mayInterruptIfRunning) {
                return false;
            }

            @Override
            public boolean isCancelled() {
                return false;
            }

            @Override
            public boolean isDone() {
                return false;
            }

            @Override
            public ShoppingCartDTO get() throws InterruptedException, ExecutionException {
                ShoppingCartDTO shoppingCartDTO = new ShoppingCartDTO();
                return shoppingCartDTO;
            }

            @Override
            public ShoppingCartDTO get(long timeout, TimeUnit unit) throws InterruptedException, ExecutionException, TimeoutException {
                return null;
            }
        };

        filterCouponComponent.checkResult(inputCouponCodes,validateInputCoupons,selectCouponCodeSet,errorCouponMap,future1);
    }

    @Test
    public void validateInputCoupons(){
        ShoppingCartDTO scParam = new ShoppingCartDTO();
        Map<String, TPromoCouponCodeUserVO> couponCodeUserMap = new HashMap<>();
        Map<String, ActivityCacheDTO> couponActivityMap = new HashMap<>();
        filterCouponComponent.validateInputCoupons(scParam,couponCodeUserMap,couponActivityMap);

        scParam.setCouponCodes("te1");
        filterCouponComponent.validateInputCoupons(scParam,couponCodeUserMap,couponActivityMap);
        ActivityCacheDTO activityCacheDTO = new ActivityCacheDTO();
        couponActivityMap.put("t1",activityCacheDTO);
        TPromoCouponCodeUserVO promoCouponCodeUserVO = new TPromoCouponCodeUserVO();
        couponCodeUserMap.put("t1",promoCouponCodeUserVO);
        try {
            filterCouponComponent.validateInputCoupons(scParam, couponCodeUserMap, couponActivityMap);
        }catch (Exception e){

        }
    }

    @Test
    public void validateTriedCoupons(){
        List<String> inputCouponCodes = new ArrayList<>();
        List<String> triedCouponCodes = new ArrayList<>();
        filterCouponComponent.validateTriedCoupons(inputCouponCodes,triedCouponCodes);
        inputCouponCodes.add("t1");
        filterCouponComponent.validateTriedCoupons(inputCouponCodes,triedCouponCodes);
        triedCouponCodes.add("b1");
        filterCouponComponent.validateTriedCoupons(inputCouponCodes,triedCouponCodes);
    }

    @Test
    public void runCouponFilterCallAbles(){
        ShoppingCartDTO shoppingCart = new ShoppingCartDTO();
        Map<String, TPromoCouponCodeUserVO> couponCodeMap = new HashMap<>();

        TPromoCouponCodeUserVO tPromoCouponCodeUserVO = new TPromoCouponCodeUserVO();
        tPromoCouponCodeUserVO.setCouponCode("te1");
        couponCodeMap.put("cpuonCode",tPromoCouponCodeUserVO);
        Map<String, ActivityCacheDTO> activityCacheMap = new HashMap<>();
        ActivityCacheDTO activityCacheDTO = new ActivityCacheDTO();
        activityCacheMap.put("t1",activityCacheDTO);
        ActivityScriptModel activityScriptModel = new ActivityScriptModel();
        Mockito.when(groovyService.findByCode(Mockito.any(),Mockito.any())).thenReturn(activityScriptModel);
        shoppingCart.setPromoProducts(Lists.newArrayList(new ShoppingCartItem()));
        shoppingCart.setCouponCodes("te1");
        try {
            shoppingCart.setCouponCodes("te1");
            filterCouponComponent.runCouponFilterCallAbles(shoppingCart, couponCodeMap, activityCacheMap);
        }catch (Exception e){

        }
    }


    @Test
    public void updateCouponCode() {
        UpdateCouponParam updateCouponParam = new UpdateCouponParam();


        List<UpdateUserCouponParam> couponParams =new ArrayList<>();

        UpdateUserCouponParam param1 = new UpdateUserCouponParam();

        param1.setCouponCode("!");
        param1.setStatus("02");
        String dateStr1 = DateUtil.getDateStr(DateUtil.DATETIMESTOREFORMAT);
        param1.setValidEndTime(dateStr1);
        param1.setValidStartTime(dateStr1);
        couponParams.add(param1);

        UpdateUserCouponParam param = new UpdateUserCouponParam();

        param.setCouponCode("!");
        param.setStatus("01");
        String dateStr = DateUtil.getDateStr(DateUtil.DATETIMESTOREFORMAT);
        param.setValidEndTime(dateStr);
        param.setValidStartTime(dateStr);
        couponParams.add(param);

        updateCouponParam.setCouponParams(couponParams);

        filterCouponComponent.updateCouponCode(updateCouponParam);

    }

    @Test
    public void updateCouponCode_empty() {
        UpdateCouponParam updateCouponParam = new UpdateCouponParam();


        List<UpdateUserCouponParam> couponParams =new ArrayList<>();

        UpdateUserCouponParam param1 = new UpdateUserCouponParam();

        param1.setCouponCode("!");
        param1.setStatus("02");
        String dateStr1 = DateUtil.getDateStr(DateUtil.DATETIMESTOREFORMAT);
        param1.setValidEndTime(dateStr1);
        param1.setValidStartTime(dateStr1);
        UpdateUserCouponParam param = new UpdateUserCouponParam();
        param.setCouponCode("!");
        param.setStatus("01");
        String dateStr = DateUtil.getDateStr(DateUtil.DATETIMESTOREFORMAT);
        param.setValidEndTime(dateStr);
        param.setValidStartTime(dateStr);

        updateCouponParam.setCouponParams(couponParams);

        filterCouponComponent.updateCouponCode(updateCouponParam);

    }
}


