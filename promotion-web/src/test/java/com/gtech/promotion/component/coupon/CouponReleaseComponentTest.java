package com.gtech.promotion.component.coupon;

import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.gtech.commons.exception.GTechBaseException;
import com.gtech.commons.redis.GTechCodeGenerator;
import com.gtech.commons.utils.DateUtil;
import com.gtech.promotion.RedisClient;
import com.gtech.promotion.code.coupon.CouponFrozenStatusEnum;
import com.gtech.promotion.code.coupon.CouponRuleTypeEnum;
import com.gtech.promotion.code.coupon.CouponStatusEnum;
import com.gtech.promotion.dao.model.activity.ActivityLanguageModel;
import com.gtech.promotion.dao.model.activity.ActivityModel;
import com.gtech.promotion.dao.model.coupon.CountCouponCodeModel;
import com.gtech.promotion.dao.model.coupon.CouponReleaseModel;
import com.gtech.promotion.dao.model.coupon.TPromoCouponCodeUserVO;
import com.gtech.promotion.domain.coupon.CouponReleaseDomain;
import com.gtech.promotion.dto.in.coupon.ReleaseCouponInDTO;
import com.gtech.promotion.exception.PromotionException;
import com.gtech.promotion.helper.ActivityRedisHelpler;
import com.gtech.promotion.page.RequestPage;
import com.gtech.promotion.service.activity.ActivityFuncParamService;
import com.gtech.promotion.service.activity.ActivityFuncRankService;
import com.gtech.promotion.service.coupon.PromoCouponCodeUserService;
import com.gtech.promotion.service.coupon.PromoCouponInnerCodeService;
import com.gtech.promotion.service.coupon.PromoCouponReleaseService;
import com.gtech.promotion.utils.Constants;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;

import java.math.BigDecimal;
import java.util.*;

/**
 * <AUTHOR>
 * @Date 2021/9/22 10:19
 */
@RunWith(MockitoJUnitRunner.class)
public class CouponReleaseComponentTest {

    @InjectMocks
    private CouponReleaseComponent couponReleaseComponent;

    @Mock
    private PromoCouponReleaseService couponReleaseService;

    @Mock
    private PromoCouponCodeUserService couponCodeUserService;

    @Mock
    private PromoCouponInnerCodeService couponInnerCodeService;

    @Mock
    private ActivityFuncParamService activityFuncParamService;

    @Mock
    private ActivityFuncRankService activityFuncRankService;

    @Mock
    private CouponActivityComponent couponActivityComponent;

    @Mock
    private ActivityRedisHelpler redisService;


    @Mock
    private CouponInnerCodeComponent couponInnerCodeComponent;

    @Mock
    private RedisClient redisClient;

    @Mock
    private GTechCodeGenerator gTechCodeGenerator;



    @Test
    public void createUaReleaseAndSendCoupon(){


        ReleaseCouponInDTO param = new ReleaseCouponInDTO();

        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.DATE,1);
        String format = DateUtil.format(calendar, DateUtil.FORMAT_YYYYMMDDHHMISS_14);


        param.setActivityCode("1");
        param.setTenantCode("1");
        param.setReleaseType("01");
        param.setReceiveStart("20200831180145");
        param.setReceiveEnd(format);
        param.setReleaseQuantity(10);
        param.setValidStart("20200831180145");
        param.setValidEnd(format);
        List<String> couponCodes = new ArrayList<>();
        couponCodes.add("1");
        couponCodes.add(null);
        couponCodes.add(null);
        param.setCouponCodes(couponCodes);
        param.setTakeLabel("02");
        param.setUserCode("11");

        Map<String, String> outCouponMap = new HashMap<>();

        outCouponMap.put("11","11");
        outCouponMap.put("112","112");
        outCouponMap.put("113","113");




        long now = Long.parseLong(DateUtil.format(DateUtil.now(), DateUtil.FORMAT_YYYYMMDDHHMISS_14));
        // 添加领券记录
        TPromoCouponCodeUserVO couponCodeUserVO = new TPromoCouponCodeUserVO();
        couponCodeUserVO.setReceivedTime(String.valueOf(now));
        couponCodeUserVO.setTenantCode("11");
        couponCodeUserVO.setActivityCode("11");
        couponCodeUserVO.setCouponType("01");
        couponCodeUserVO.setFaceValue(new BigDecimal(11));
        couponCodeUserVO.setFaceUnit("01");


        couponCodeUserVO.setFrozenStatus(CouponFrozenStatusEnum.UN_FROZEN.code());
        couponCodeUserVO.setReleaseCode("11");
        couponCodeUserVO.setUsedRefId(Constants.DEFAULT_USED_REF_ID);
        couponCodeUserVO.setStatus(CouponStatusEnum.GRANTED.code());


        couponCodeUserVO.setValidStartTime("20200831180145");
        couponCodeUserVO.setValidEndTime(format);

        ReleaseCouponInDTO dto = new ReleaseCouponInDTO();
        dto.setTimeSameActivity("1");
        dto.setReleaseType("01");
        dto.setValidStart("20200831180145");

        dto.setReleaseQuantity(1001);
        dto.setReceiveStart("20200831180145");
        dto.setReceiveEnd(format);

        ActivityModel activityModel = new ActivityModel();
        activityModel.setActivityEnd(format);
        activityModel.setActivityBegin("20200831180145");
        activityModel.setActivityStatus("04");
        activityModel.setCouponType("02");
        activityModel.setTotalQuantity(1000000000);
        activityModel.setOpsType("201");
        // populate activityModel with necessary fields, set activity end time before release time

        ArrayList<CouponReleaseDomain> couponReleaseDomains = new ArrayList<>();
        CouponReleaseDomain couponReleaseDomain = new CouponReleaseDomain();
        couponReleaseDomain.setReleaseQuantity(1000);
        couponReleaseDomain.setInventory(1000);
        couponReleaseDomain.setTenantCode("123");
        couponReleaseDomain.setActivityCode("activityCode");
        couponReleaseDomain.setReleaseCode("123124124");
        couponReleaseDomains.add(couponReleaseDomain);

        Mockito.when(couponReleaseService.queryCanReceiveReleasesByTime(Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(couponReleaseDomains);




        Mockito.when(couponActivityComponent.findEffectiveActivity(Mockito.any(), Mockito.any(), Mockito.any()))
                .thenReturn(activityModel);

        outCouponMap.put("123","123");
        activityModel.setTotalQuantity(2000);
        CouponReleaseModel promoCouponReleaseVO = new CouponReleaseModel();
        promoCouponReleaseVO.setTenantCode("1");
        promoCouponReleaseVO.setActivityCode("1");
        promoCouponReleaseVO.setCouponType("1");
        promoCouponReleaseVO.setReleaseCode("1");
        promoCouponReleaseVO.setReleaseStatus("1");
        promoCouponReleaseVO.setReleaseQuantity(0);
        promoCouponReleaseVO.setInventory(0);
        promoCouponReleaseVO.setReleaseSource("1");
        promoCouponReleaseVO.setReleaseType("1");
        promoCouponReleaseVO.setReleaseTime("1");
        promoCouponReleaseVO.setReceiveStartTime("20200831180145");

        promoCouponReleaseVO.setReceiveEndTime(format);
        promoCouponReleaseVO.setValidStartTime("1");
        promoCouponReleaseVO.setValidEndTime(format);
        promoCouponReleaseVO.setValidDays(0);
        promoCouponReleaseVO.setCouponCodePrefix("1");
        promoCouponReleaseVO.setTimeSameActivity("1");

        promoCouponReleaseVO.setReleaseType("01");
        dto.setValidEnd(format);
        param.setValidEnd(format);
        param.setValidStart("20200831180145");


//        Mockito.when(couponActivityComponent.findSendCouponValidActivity(Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(activityModel);

        Mockito.when(couponInnerCodeComponent.builderCouponToUser(Mockito.any(),Mockito.any())).thenReturn(couponCodeUserVO);

        Mockito.when(couponCodeUserService.insert(Mockito.anyList())).thenReturn(1);

		Mockito.doNothing().when(couponInnerCodeService).updateBatchInnerCodeByCouponCodes(Mockito.any());

        String uaReleaseAndSendCoupon = couponReleaseComponent.createUaReleaseAndSendCoupon(param, outCouponMap);
        Assert.assertNotNull(uaReleaseAndSendCoupon);
    }



    @Test
    public  void  findCouponReleaseByReleaseCode(){
        CouponReleaseDomain releaseDomain = new CouponReleaseDomain();
        releaseDomain.setTenantCode("");
        releaseDomain.setActivityCode("");
        releaseDomain.setCouponType("02");
        releaseDomain.setReleaseCode("");
        releaseDomain.setReleaseStatus("");
        releaseDomain.setReleaseQuantity(0);
        releaseDomain.setInventory(0);
        releaseDomain.setReceivedQuantity(0);
        releaseDomain.setUsedTotal(0);
        releaseDomain.setLocked(0);
        releaseDomain.setReleaseSource("");
        releaseDomain.setReceiveStartTime("");
        releaseDomain.setReceiveEndTime("");
        releaseDomain.setValidDays(0);
        releaseDomain.setValidStartTime("");
        releaseDomain.setValidEndTime("");
        releaseDomain.setReleaseTime("");
        releaseDomain.setReleaseType("");
        releaseDomain.setCouponCodePrefix("");
        releaseDomain.setTimeSameActivity("");
        CountCouponCodeModel m = new CountCouponCodeModel();
        m.setActivityCode("");
        m.setReleaseCode("");
        m.setCouponStatus("");
        m.setCouponCount(0);

        releaseDomain.setCouponRuleType("");
        releaseDomain.setCouponType("01");

        List<CountCouponCodeModel> cccModels = new ArrayList<>();
        cccModels.add(m);
//        Mockito.when(couponInnerCodeService.countCouponCode(Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(cccModels);

        Mockito.when(couponReleaseService.findCouponReleaseByReleaseCode(Mockito.any(),Mockito.any())).thenReturn(releaseDomain);
        couponReleaseComponent.findCouponReleaseByReleaseCode("123123123","");

    }

    @Test
    public  void  findCouponReleaseByReleaseCode_promotion_Code(){
        CouponReleaseDomain releaseDomain = new CouponReleaseDomain();
        releaseDomain.setTenantCode("");
        releaseDomain.setActivityCode("");
        releaseDomain.setCouponType("02");
        releaseDomain.setReleaseCode("");
        releaseDomain.setReleaseStatus("");
        releaseDomain.setReleaseQuantity(0);
        releaseDomain.setInventory(0);
        releaseDomain.setReceivedQuantity(0);
        releaseDomain.setUsedTotal(0);
        releaseDomain.setLocked(0);
        releaseDomain.setReleaseSource("");
        releaseDomain.setReceiveStartTime("");
        releaseDomain.setReceiveEndTime("");
        releaseDomain.setValidDays(0);
        releaseDomain.setValidStartTime("");
        releaseDomain.setValidEndTime("");
        releaseDomain.setReleaseTime("");
        releaseDomain.setReleaseType("");
        releaseDomain.setCouponCodePrefix("");
        releaseDomain.setTimeSameActivity("");
        CountCouponCodeModel m = new CountCouponCodeModel();
        m.setActivityCode("");
        m.setReleaseCode("");
        m.setCouponStatus("");
        m.setCouponCount(0);

        releaseDomain.setCouponRuleType("");
        releaseDomain.setCouponType("03");

        List<CountCouponCodeModel> cccModels = new ArrayList<>();
        cccModels.add(m);
//        Mockito.when(couponInnerCodeService.countCouponCode(Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(cccModels);

        Mockito.when(couponReleaseService.findCouponReleaseByReleaseCode(Mockito.any(),Mockito.any())).thenReturn(releaseDomain);
        couponReleaseComponent.findCouponReleaseByReleaseCode("123123123","");

    }

    @Test
    public  void  findCouponReleaseByReleaseCode_01(){
        CouponReleaseDomain releaseDomain = new CouponReleaseDomain();
        releaseDomain.setTenantCode("");
        releaseDomain.setActivityCode("");
        releaseDomain.setCouponType("02");
        releaseDomain.setReleaseCode("");
        releaseDomain.setReleaseStatus("");
        releaseDomain.setReleaseQuantity(0);
        releaseDomain.setInventory(0);
        releaseDomain.setReceivedQuantity(0);
        releaseDomain.setUsedTotal(0);
        releaseDomain.setLocked(0);
        releaseDomain.setReleaseSource("");
        releaseDomain.setReceiveStartTime("");
        releaseDomain.setReceiveEndTime("");
        releaseDomain.setValidDays(0);
        releaseDomain.setValidStartTime("");
        releaseDomain.setValidEndTime("");
        releaseDomain.setReleaseTime("");
        releaseDomain.setReleaseType("");
        releaseDomain.setCouponCodePrefix("");
        releaseDomain.setTimeSameActivity("");
        CountCouponCodeModel m = new CountCouponCodeModel();
        m.setActivityCode("");
        m.setReleaseCode("");
        m.setCouponStatus("");
        m.setCouponCount(0);

        releaseDomain.setCouponRuleType("01");
        releaseDomain.setCouponType("01");

        List<CountCouponCodeModel> cccModels = new ArrayList<>();
        cccModels.add(m);
//        Mockito.when(couponInnerCodeService.countCouponCode(Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(cccModels);

        Mockito.when(couponReleaseService.findCouponReleaseByReleaseCode(Mockito.any(),Mockito.any())).thenReturn(releaseDomain);
        couponReleaseComponent.findCouponReleaseByReleaseCode("123123123","");

    }


    @Test
    public void queryCouponRelease() {
        CouponReleaseDomain domain = new CouponReleaseDomain();
        // set any necessary fields on domain
        RequestPage page = new RequestPage();
        // set any necessary fields on page
        PageInfo<CouponReleaseDomain> pageInfo = new PageInfo<>();
        pageInfo.setList(Arrays.asList(domain));
        Mockito.when(couponReleaseService.queryReleases(domain, page)).thenReturn(pageInfo);
        PageInfo<CouponReleaseDomain> result = couponReleaseComponent.queryCouponRelease(domain, page);
    }


    @Test
    public void releaseCoupon_ActivityModelIsNull() {
        ReleaseCouponInDTO dto = new ReleaseCouponInDTO();
        // populate dto with necessary fields

        Mockito.when(couponActivityComponent.findEffectiveActivity(Mockito.any(), Mockito.any(), Mockito.any()))
                .thenReturn(null);

        // expect some exception here
        Assert.assertThrows(GTechBaseException.class, () -> couponReleaseComponent.releaseCoupon(dto, new HashMap<>(),null));
    }

    @Test
    public void buildReceiveEndTimeByDays(){



        couponReleaseComponent.buildReceiveEndTimeByDays("21230719182424",1);

    }

    @Test
    public void releaseCoupon_() {
        ReleaseCouponInDTO dto = new ReleaseCouponInDTO();
        dto.setTimeSameActivity("1");
        dto.setReleaseType("01");
        dto.setValidStart("20210922");
        dto.setValidEnd("20210924");
        dto.setReleaseQuantity(1001);
        dto.setReceiveStart("20210921");
        dto.setReceiveEnd("21230719182424");
        Map<String, String> outCouponMap = new HashMap<>();
        ActivityModel activityModel = new ActivityModel();
        activityModel.setActivityEnd("21500000000000");
        activityModel.setActivityBegin("20400000000000");
        activityModel.setActivityStatus("04");
        activityModel.setCouponType("02");
        activityModel.setTotalQuantity(1000000000);
        // populate activityModel with necessary fields, set activity end time before release time



        Mockito.when(couponActivityComponent.findEffectiveActivity(Mockito.any(), Mockito.any(), Mockito.any()))
                .thenReturn(activityModel);
        ReleaseCouponInDTO checkReleaseTimeDto = new ReleaseCouponInDTO();

        outCouponMap.put("123","123");
        activityModel.setTotalQuantity(2000);
        Long releaseTotal = 10L;
        CouponReleaseModel promoCouponReleaseVO = new CouponReleaseModel();
        promoCouponReleaseVO.setTenantCode("1");
        promoCouponReleaseVO.setActivityCode("1");
        promoCouponReleaseVO.setCouponType("1");
        promoCouponReleaseVO.setReleaseCode("1");
        promoCouponReleaseVO.setReleaseStatus("1");
        promoCouponReleaseVO.setReleaseQuantity(0);
        promoCouponReleaseVO.setInventory(0);
        promoCouponReleaseVO.setReleaseSource("1");
        promoCouponReleaseVO.setReleaseType("1");
        promoCouponReleaseVO.setReleaseTime("1");
        promoCouponReleaseVO.setReceiveStartTime("1");
        promoCouponReleaseVO.setReceiveEndTime("1");
        promoCouponReleaseVO.setValidStartTime("1");
        promoCouponReleaseVO.setValidEndTime("1");
        promoCouponReleaseVO.setValidDays(0);
        promoCouponReleaseVO.setCouponCodePrefix("1");
        promoCouponReleaseVO.setTimeSameActivity("1");

        promoCouponReleaseVO.setReleaseType("01");
        CouponReleaseDomain releaseDomain = new CouponReleaseDomain();
        Mockito.when(gTechCodeGenerator.generateCode(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any())).thenReturn("1");
        couponReleaseComponent.releaseCoupon(dto, outCouponMap,releaseDomain);
    }


    @Test
    public void countCouponRelease2(){
        CouponReleaseDomain releaseDomain = new CouponReleaseDomain();
        releaseDomain.setTenantCode("");
        releaseDomain.setActivityCode("");
        releaseDomain.setCouponType("02");
        releaseDomain.setReleaseCode("");
        releaseDomain.setReleaseStatus("");
        releaseDomain.setReleaseQuantity(0);
        releaseDomain.setInventory(0);
        releaseDomain.setReceivedQuantity(0);
        releaseDomain.setUsedTotal(0);
        releaseDomain.setLocked(0);
        releaseDomain.setReleaseSource("");
        releaseDomain.setReceiveStartTime("");
        releaseDomain.setReceiveEndTime("");
        releaseDomain.setValidDays(0);
        releaseDomain.setValidStartTime("");
        releaseDomain.setValidEndTime("");
        releaseDomain.setReleaseTime("");
        releaseDomain.setReleaseType("");
        releaseDomain.setCouponCodePrefix("");
        releaseDomain.setTimeSameActivity("");
        releaseDomain.setCouponRuleType("01");
        releaseDomain.setCouponType("03");
        CountCouponCodeModel m = new CountCouponCodeModel();
        m.setActivityCode("");
        m.setReleaseCode("");
        m.setCouponStatus("");
        m.setCouponCount(0);
        List<CountCouponCodeModel> cccModels = new ArrayList<>();
        cccModels.add(m);
//        Mockito.when(couponInnerCodeService.countCouponCode(Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(cccModels);
        couponReleaseComponent.countCouponRelease(releaseDomain);
        releaseDomain.setCouponType("03");
        releaseDomain.setCouponRuleType("");
        couponReleaseComponent.countCouponRelease(releaseDomain);
        releaseDomain.setCouponType("01");
        releaseDomain.setCouponRuleType("02");
        couponReleaseComponent.countCouponRelease(releaseDomain);

    }

    @Test
    public void countCouponRelease1(){
        CouponReleaseDomain releaseDomain = new CouponReleaseDomain();
        releaseDomain.setTenantCode("");
        releaseDomain.setActivityCode("");
        releaseDomain.setCouponType("02");
        releaseDomain.setReleaseCode("");
        releaseDomain.setReleaseStatus("");
        releaseDomain.setReleaseQuantity(0);
        releaseDomain.setInventory(0);
        releaseDomain.setReceivedQuantity(0);
        releaseDomain.setUsedTotal(0);
        releaseDomain.setLocked(0);
        releaseDomain.setReleaseSource("");
        releaseDomain.setReceiveStartTime("");
        releaseDomain.setReceiveEndTime("");
        releaseDomain.setValidDays(0);
        releaseDomain.setValidStartTime("");
        releaseDomain.setValidEndTime("");
        releaseDomain.setReleaseTime("");
        releaseDomain.setReleaseType("");
        releaseDomain.setCouponCodePrefix("");
        releaseDomain.setTimeSameActivity("");
        releaseDomain.setCouponRuleType("");
        releaseDomain.setCouponType("03");
        CountCouponCodeModel m = new CountCouponCodeModel();
        m.setActivityCode("");
        m.setReleaseCode("");
        m.setCouponStatus("");
        m.setCouponCount(0);
        List<CountCouponCodeModel> cccModels = new ArrayList<>();
        cccModels.add(m);
//        Mockito.when(couponInnerCodeService.countCouponCode(Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(cccModels);
        couponReleaseComponent.countCouponRelease(releaseDomain);
        releaseDomain.setCouponType("03");
        couponReleaseComponent.countCouponRelease(releaseDomain);
        releaseDomain.setCouponType("01");
        couponReleaseComponent.countCouponRelease(releaseDomain);

    }

    @Test
    public void countCouponRelease(){
        CouponReleaseDomain releaseDomain = new CouponReleaseDomain();
        releaseDomain.setTenantCode("");
        releaseDomain.setActivityCode("");
        releaseDomain.setCouponType("02");
        releaseDomain.setReleaseCode("");
        releaseDomain.setReleaseStatus("");
        releaseDomain.setReleaseQuantity(0);
        releaseDomain.setInventory(0);
        releaseDomain.setReceivedQuantity(0);
        releaseDomain.setUsedTotal(0);
        releaseDomain.setLocked(0);
        releaseDomain.setReleaseSource("");
        releaseDomain.setReceiveStartTime("");
        releaseDomain.setReceiveEndTime("");
        releaseDomain.setValidDays(0);
        releaseDomain.setValidStartTime("");
        releaseDomain.setValidEndTime("");
        releaseDomain.setReleaseTime("");
        releaseDomain.setReleaseType("");
        releaseDomain.setCouponCodePrefix("");
        releaseDomain.setTimeSameActivity("");
        releaseDomain.setCouponRuleType("");
        releaseDomain.setCouponType("01");
        CountCouponCodeModel m = new CountCouponCodeModel();
        m.setActivityCode("");
        m.setReleaseCode("");
        m.setCouponStatus("");
        m.setCouponCount(0);

        List<CountCouponCodeModel> cccModels = new ArrayList<>();
        cccModels.add(m);
//        Mockito.when(couponInnerCodeService.countCouponCode(Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(cccModels);
        couponReleaseComponent.countCouponRelease(releaseDomain);
        releaseDomain.setCouponType("03");
        couponReleaseComponent.countCouponRelease(releaseDomain);
        releaseDomain.setCouponType("01");
        couponReleaseComponent.countCouponRelease(releaseDomain);

    }

    
    @Test
    public void releaseCoupon(){

        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.DATE,1);
        String format = DateUtil.format(calendar, DateUtil.FORMAT_YYYYMMDDHHMISS_14);
        ReflectionTestUtils.setField(couponReleaseComponent, "MAX_RETRY_TIMES", 1);
        ReleaseCouponInDTO dto = new ReleaseCouponInDTO();
        dto.setTimeSameActivity("1");
        dto.setReleaseType("01");
        dto.setValidStart("20200831180145");
        dto.setValidEnd(format);
        dto.setReleaseQuantity(1001);
        dto.setReceiveStart("20200831180145");
        dto.setReceiveEnd(format);
        Map<String, String> outCouponMap = new HashMap<>();
        ActivityModel activityModel = new ActivityModel();
        activityModel.setActivityEnd(format);
        activityModel.setActivityBegin("20200831180145");
        activityModel.setActivityStatus("04");
        activityModel.setCouponType("02");
        activityModel.setTotalQuantity(1000000000);
        activityModel.setTenantCode("11111111");
        Mockito.when(couponActivityComponent.findEffectiveActivity(Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(activityModel);
//        Mockito.when(couponReleaseService.queryReleaseCount111(Mockito.any())).thenReturn(11111111l);

        CouponReleaseDomain releaseDomain = new CouponReleaseDomain();
        releaseDomain.setTenantCode("111111");
        releaseDomain.setReleaseCode("1122222");
        Mockito.when(gTechCodeGenerator.generateCode(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any())).thenReturn("1");
        couponReleaseComponent.releaseCoupon(dto, outCouponMap,releaseDomain);
    }

    @Test
    public void generateCouponCode(){

        Map<String, String> outCouponMap = new HashMap<>();
        outCouponMap.put("123","123");
        ActivityModel activityModel = new ActivityModel();
        activityModel.setTotalQuantity(2000);
        Long releaseTotal = 10L;
        CouponReleaseModel promoCouponReleaseVO = new CouponReleaseModel();
        promoCouponReleaseVO.setTenantCode("1");
        promoCouponReleaseVO.setActivityCode("1");
        promoCouponReleaseVO.setCouponType("1");
        promoCouponReleaseVO.setReleaseCode("1");
        promoCouponReleaseVO.setReleaseStatus("1");
        promoCouponReleaseVO.setReleaseQuantity(0);
        promoCouponReleaseVO.setInventory(0);
        promoCouponReleaseVO.setReleaseSource("1");
        promoCouponReleaseVO.setReleaseType("1");
        promoCouponReleaseVO.setReleaseTime("1");
        promoCouponReleaseVO.setReceiveStartTime("1");
        promoCouponReleaseVO.setReceiveEndTime("1");
        promoCouponReleaseVO.setValidStartTime("1");
        promoCouponReleaseVO.setValidEndTime("1");
        promoCouponReleaseVO.setValidDays(0);
        promoCouponReleaseVO.setCouponCodePrefix("1");
        promoCouponReleaseVO.setTimeSameActivity("1");

        promoCouponReleaseVO.setReleaseType("01");

        couponReleaseComponent.generateCouponCode(outCouponMap,activityModel,releaseTotal,promoCouponReleaseVO);

    }

    @Test
    public void checkReleaseTime(){

        ReleaseCouponInDTO dto = new ReleaseCouponInDTO();
        dto.setReleaseType("02");
        dto.setReleaseTime("20000000000000");
        dto.setReceiveEnd("28880000000000");
        ActivityModel activityModel = new ActivityModel();
        activityModel.setActivityBegin("19990000000000");
        activityModel.setCouponType("01");
        couponReleaseComponent.checkReleaseTime(dto,activityModel);
    }


    @Test
    public void cancelReleaseCoupon111(){
        CouponReleaseDomain releaseDomain = new CouponReleaseDomain();
        releaseDomain.setTenantCode("");
        releaseDomain.setActivityCode("");
        releaseDomain.setCouponType("");
        releaseDomain.setReleaseCode("");
        releaseDomain.setReleaseStatus("01");
        releaseDomain.setReleaseQuantity(0);
        releaseDomain.setInventory(0);
        releaseDomain.setReceivedQuantity(0);
        releaseDomain.setUsedTotal(0);
        releaseDomain.setLocked(0);
        releaseDomain.setReleaseSource("");
        releaseDomain.setReceiveStartTime("");
        releaseDomain.setReceiveEndTime("");
        releaseDomain.setValidDays(0);
        releaseDomain.setValidStartTime("");
        releaseDomain.setValidEndTime("");
        releaseDomain.setReleaseTime("");
        releaseDomain.setReleaseType("02");
        releaseDomain.setCouponCodePrefix("");
        releaseDomain.setTimeSameActivity("");


        Mockito.when(couponReleaseService.findCouponReleaseByReleaseCode(Mockito.any(),Mockito.any())).thenReturn(releaseDomain);

        couponReleaseComponent.cancelReleaseCoupon111("123","123");

    }


    @Test
    public void couponReleaseTimer(){
        CouponReleaseModel model = new CouponReleaseModel();
        model.setTenantCode("1");
        model.setActivityCode("1");
        model.setCouponType("1");
        model.setReleaseCode("1");
        model.setReleaseStatus("1");
        model.setReleaseQuantity(0);
        model.setInventory(0);
        model.setReleaseSource("1");
        model.setReleaseType("1");
        model.setReleaseTime("1");
        model.setReceiveStartTime("1");
        model.setReceiveEndTime("1");
        model.setValidStartTime("1");
        model.setValidEndTime("1");
        model.setValidDays(0);
        model.setCouponCodePrefix("1");
        model.setTimeSameActivity("1");


        model.setReleaseQuantity(2000);

        List<CouponReleaseModel> releaseVOs = new ArrayList<>();
        releaseVOs.add(model);


        ActivityModel activityModel = new ActivityModel();
        activityModel.setCouponInfo(new ActivityModel());
        activityModel.setLanguage(new ActivityLanguageModel());
        activityModel.setItemScopeType(0);
        activityModel.setId(0L);
        activityModel.setDomainCode("1");
        activityModel.setTenantCode("1");
        activityModel.setActivityCode("1");
        activityModel.setCustomCondition("1");
        activityModel.setActivityType("1");
        activityModel.setActivityLabel("1");
        activityModel.setActivityName("1");
        activityModel.setActivityDesc("1");
        activityModel.setShowFlag(0);
        activityModel.setActivityRemark("1");
        activityModel.setActivityExpr("1");
        activityModel.setPriority(0);
        activityModel.setTemplateCode("1");
        activityModel.setIncentiveLimitedFlag("1");
        activityModel.setWarmBegin("1");
        activityModel.setWarmEnd("1");
        activityModel.setActivityBegin("1");
        activityModel.setActivityEnd("1");
        activityModel.setPeriodType("1");
        activityModel.setProductSelectionType("1");
        activityModel.setStoreType("1");
        activityModel.setActivityUrl("1");
        activityModel.setActivityStatus("1");
        activityModel.setSponsors("1");
        activityModel.setOpsType("1");
        activityModel.setExclusionKey("1");
        activityModel.setCreateUser("1");
        activityModel.setCreateTime(new Date());
        activityModel.setUpdateUser("1");
        activityModel.setTotalQuantity(0);
        activityModel.setUserLimitMax(0);
        activityModel.setCouponType("1");
        activityModel.setCouponCode("1");
        activityModel.setReserveInventory(0);
        activityModel.setCoolDown("1");
        activityModel.setBackgroundImage("1");
        activityModel.setProductCondition("1");
        activityModel.setRibbonImage("1");
        activityModel.setRibbonPosition("1");
        activityModel.setRibbonText("1");
        activityModel.setCouponCodes(Lists.newArrayList());


        Mockito.when(couponReleaseService.queryReleasesNotReleased()).thenReturn(releaseVOs);

        Mockito.when(couponActivityComponent.findValidActivity(Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(activityModel);
        couponReleaseComponent.couponReleaseTimer();

    }




    @Test
    public void couponReleaseTimer_null(){
        CouponReleaseModel model = new CouponReleaseModel();
        model.setTenantCode("1");
        model.setActivityCode("1");
        List<CouponReleaseModel> releaseVOs = new ArrayList<>();
        releaseVOs.add(model);
        Mockito.when(couponReleaseService.queryReleasesNotReleased()).thenReturn(releaseVOs);
        Mockito.when(couponActivityComponent.findValidActivity(Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(null);
        couponReleaseComponent.couponReleaseTimer();
    }

    @Test
    public void couponReleaseTimer_empty(){
        Mockito.when(couponReleaseService.queryReleasesNotReleased()).thenReturn(new ArrayList<>());
        couponReleaseComponent.couponReleaseTimer();
    }


    @Test
    public void createInnerCoupons(){
        CouponReleaseModel releaseModel = new CouponReleaseModel();
        releaseModel.setReleaseQuantity(1111111111);
        releaseModel.setReleaseCode("1111111");
        releaseModel.setCouponRuleType(null);
        releaseModel.setCouponType("03");
        releaseModel.setCouponCodeLength(12);
        releaseModel.setCouponCodePrefix("11");

        releaseModel.setReleaseQuantity(11111);
        releaseModel.setTenantCode("111111");
        releaseModel.setReleaseCode("1122222");

        ActivityModel activityModel = new ActivityModel();
        activityModel.setActivityCode("1");
        activityModel.setTenantCode("111111111");
        Mockito.when(activityFuncRankService.getRankListByActivityCode(Mockito.any())).thenReturn(new ArrayList<>());
        couponReleaseComponent.createInnerCoupons(releaseModel,activityModel);
    }

    @Test
    public void createInnerCoupons_1(){
        CouponReleaseModel releaseModel = new CouponReleaseModel();
        releaseModel.setReleaseCode("1111111");
        releaseModel.setCouponRuleType(CouponRuleTypeEnum.DIGIT.code());
        releaseModel.setCouponType("03");
        releaseModel.setCouponCodeLength(12);
        releaseModel.setCouponCodePrefix("11");
        releaseModel.setReleaseQuantity(11111);
        releaseModel.setTenantCode("1111111");

        ActivityModel activityModel = new ActivityModel();
        activityModel.setActivityCode("1");
        activityModel.setTenantCode("1111111");
        Mockito.when(activityFuncRankService.getRankListByActivityCode(Mockito.any())).thenReturn(new ArrayList<>());
        couponReleaseComponent.createInnerCoupons(releaseModel,activityModel);
    }

    @Test
    public void createInnerCoupons_2(){
        CouponReleaseModel releaseModel = new CouponReleaseModel();
        releaseModel.setReleaseCode("1111111");
        releaseModel.setCouponRuleType(CouponRuleTypeEnum.DIGIT_LETTER.code());
        releaseModel.setCouponType("03");
        releaseModel.setCouponCodeLength(12);
        releaseModel.setCouponCodePrefix("11");
        releaseModel.setReleaseQuantity(11111);
        ActivityModel activityModel = new ActivityModel();
        activityModel.setActivityCode("1");
        Mockito.when(activityFuncRankService.getRankListByActivityCode(Mockito.any())).thenReturn(new ArrayList<>());
        couponReleaseComponent.createInnerCoupons(releaseModel,activityModel);
    }

    @Test
    public void checkQuantity(){
        ReleaseCouponInDTO dto = new ReleaseCouponInDTO();
        dto.setReleaseQuantity(1);
        ActivityModel activityModel = new ActivityModel();
        activityModel.setActivityCode("1");
        activityModel.setTotalQuantity(4);
        couponReleaseComponent.checkQuantity(dto,activityModel,2L);
    }


    @Test(expected = PromotionException.class)
    public void checkQuantity_more(){
        ReleaseCouponInDTO dto = new ReleaseCouponInDTO();
        dto.setReleaseQuantity(1);
        ActivityModel activityModel = new ActivityModel();
        activityModel.setActivityCode("1");
        activityModel.setTotalQuantity(4);
        couponReleaseComponent.checkQuantity(dto,activityModel,5L);
    }
    @Test
    public void checkQuantity_null_1(){
        ReleaseCouponInDTO dto = new ReleaseCouponInDTO();
        dto.setReleaseQuantity(1);
        ActivityModel activityModel = new ActivityModel();
        activityModel.setActivityCode("1");
        activityModel.setTotalQuantity(4);
        couponReleaseComponent.checkQuantity(dto,activityModel,null);
    }

    @Test(expected = PromotionException.class)
    public void checkQuantity_null_2(){
        ReleaseCouponInDTO dto = new ReleaseCouponInDTO();
        dto.setReleaseQuantity(6);
        ActivityModel activityModel = new ActivityModel();
        activityModel.setActivityCode("1");
        activityModel.setTotalQuantity(4);
        couponReleaseComponent.checkQuantity(dto,activityModel,null);
    }


    @Test
    public void createNewRelease(){
        ReflectionTestUtils.setField(couponReleaseComponent, "releaseQty", 100);

        ReleaseCouponInDTO coupon = new ReleaseCouponInDTO();
        coupon.setReleaseQuantity(1);
        CouponReleaseDomain releaseDomain = new CouponReleaseDomain();
        ActivityModel activityModel = new ActivityModel();
        String releaseCode = "123123";
        couponReleaseComponent.createNewRelease(coupon,releaseDomain,activityModel,releaseCode);
    }

}
