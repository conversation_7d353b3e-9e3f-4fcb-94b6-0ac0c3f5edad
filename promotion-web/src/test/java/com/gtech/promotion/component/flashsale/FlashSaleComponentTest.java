package com.gtech.promotion.component.flashsale;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.gtech.commons.exception.GTechBaseException;
import com.gtech.commons.page.PageData;
import com.gtech.commons.page.PageParam;
import com.gtech.commons.redis.GTechCodeGenerator;
import com.gtech.commons.redis.GTechRedisTemplate;
import com.gtech.commons.result.PageResult;
import com.gtech.commons.utils.DateUtil;
import com.gtech.ecomm.common.page.ResponsePage;
import com.gtech.ecomm.common.result.JsonResult;
import com.gtech.ecomm.order.vo.out.order.OrderProductOut;
import com.gtech.ecomm.order.vo.out.order.OrderQueryOut;
import com.gtech.promotion.PromotionThreadPoolConfig;
import com.gtech.promotion.RedisClient;
import com.gtech.promotion.calc.CalcExecuter;
import com.gtech.promotion.code.activity.ActivityStatusEnum;
import com.gtech.promotion.code.activity.LimitationCodeEnum;
import com.gtech.promotion.code.activity.OrderStatusEnum;
import com.gtech.promotion.code.activity.SelectorProductTypeEnum;
import com.gtech.promotion.code.marketing.ActivityTypeEnum;
import com.gtech.promotion.code.marketing.MarketingGroupStatusEnum;
import com.gtech.promotion.code.marketing.TeamLeaderEnum;
import com.gtech.promotion.component.activity.*;
import com.gtech.promotion.component.marketing.MarketingCacheComponent;
import com.gtech.promotion.component.marketing.MarketingGroupComponent;
import com.gtech.promotion.dao.entity.marketing.MarketingGroupEntity;
import com.gtech.promotion.dao.model.activity.ActivityModel;
import com.gtech.promotion.dao.model.activity.LuckyDrawRuleModel;
import com.gtech.promotion.dao.model.activity.TPromoIncentiveLimitedVO;
import com.gtech.promotion.dao.model.marketing.*;
import com.gtech.promotion.dao.model.marketing.flashsale.*;
import com.gtech.promotion.domain.marketing.flashsale.FlashSaleDomain;
import com.gtech.promotion.dto.cache.ActivityCacheDTO;
import com.gtech.promotion.dto.in.activity.ShoppingCartDTO;
import com.gtech.promotion.dto.in.activity.SingleProductDTO;
import com.gtech.promotion.dto.in.activity.TenantProductDTO;
import com.gtech.promotion.dto.in.flashsale.FlashSaleShoppingCartDto;
import com.gtech.promotion.dto.out.activity.ShoppingCartOutDTO;
import com.gtech.promotion.dto.out.marketing.FlashSaleActivityInfoDTO;
import com.gtech.promotion.dto.out.marketing.FlashSaleActivityPriceDTO;
import com.gtech.promotion.dto.out.marketing.FlashSaleSkuActivityPriceDTO;
import com.gtech.promotion.exception.ErrorCodes;
import com.gtech.promotion.exception.PromotionException;
import com.gtech.promotion.exception.PromotionParamValidateException;
import com.gtech.promotion.feign.OrderFeignClient;
import com.gtech.promotion.feign.PimFeignClient;
import com.gtech.promotion.helper.ActivityRedisHelpler;
import com.gtech.promotion.helper.RedisOpsHelper;
import com.gtech.promotion.pojo.GroupUserContent;
import com.gtech.promotion.service.activity.*;
import com.gtech.promotion.service.flashsale.*;
import com.gtech.promotion.service.marketing.*;
import com.gtech.promotion.vo.bean.ActivityPeriod;
import com.gtech.promotion.vo.bean.IncentiveLimited;
import com.gtech.promotion.vo.bean.ShoppingCartItem;
import com.gtech.promotion.vo.bean.ShoppingCartStore;
import com.gtech.promotion.vo.bean.marketing.MarketingGroup;
import com.gtech.promotion.vo.bean.marketing.MarketingLanguage;
import com.gtech.promotion.vo.bean.marketing.boostsharing.BoostSharing;
import com.gtech.promotion.vo.bean.marketing.flashsale.FlashSaleProduct;
import com.gtech.promotion.vo.bean.marketing.flashsale.FlashSaleQualification;
import com.gtech.promotion.vo.bean.marketing.flashsale.FlashSaleStore;
import com.gtech.promotion.vo.param.activity.CalcShoppingCartParam;
import com.gtech.promotion.vo.param.activity.ConfirmOrderParam;
import com.gtech.promotion.vo.param.activity.QueryMarketingActivityListByProductListParam;
import com.gtech.promotion.vo.param.activity.QueryPromotionOrMarketingNoFilterParam;
import com.gtech.promotion.vo.param.marketing.flashsale.FlashSaleCreateOrderParam;
import com.gtech.promotion.vo.param.marketing.flashsale.FlashSaleQueryListParam;
import com.gtech.promotion.vo.param.marketing.flashsale.SkuParam;
import com.gtech.promotion.vo.result.activity.ActivityGroupCache;
import com.gtech.promotion.vo.result.activity.CalcShoppingCartResult;
import com.gtech.promotion.vo.result.flashpresale.*;
import com.gtech.promotion.vo.result.flashsale.*;
import org.apache.commons.collections4.map.HashedMap;
import org.apache.commons.lang.time.DateUtils;
import org.junit.Assert;
import org.junit.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.redis.core.HashOperations;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.ValueOperations;

import java.math.BigDecimal;
import java.util.*;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
@RunWith(MockitoJUnitRunner.class)
public class FlashSaleComponentTest {

    @InjectMocks
    private FlashSaleComponent flashSaleComponent;

    @Mock
    private GTechCodeGenerator gTechCodeGenerator;

    @Mock
    private MarketingService marketingService;

    @Mock
    private MarketingLanguageService languageService;

    @Mock
    private FlashSaleStoreService flashSaleStoreService;

    @Mock
    private FlashSaleQualificationService flashSaleQualificationService;

    @Mock
    private FlashSaleProductService flashSaleProductService;

    @Mock
    private ActivityPeriodService activityPeriodService;

    @Mock
    private FlashSaleOrderService flashSaleOrderService;

    @Mock
    private FlashSaleOrderDetailService flashSaleOrderDetailService;

    @Mock
    private RedisOpsHelper redisOpsHelper;

    @Mock
    private OperationLogService operationLogService;
    @Mock
    private MarketingCacheComponent marketingCacheComponent;

    @Mock
    private GTechRedisTemplate redisTemplate;
    @Mock
    private ActivityComponentDomain activityComponentDomain;

    @Mock
    private PromotionThreadPoolConfig promotionThreadPoolConfig;

    @Mock
    private MarketingGroupService marketingGroupService;

    @Mock
    private BoostSharingService boostSharingService;

    @Mock
    private MarketingGroupUserService marketingGroupUserService;

    @Mock
    private ActivityCacheDomain activityCacheDomain;

    @Mock
    private GTechCodeGenerator codeGenerator;

    @Mock
    private TPromoIncentiveLimitedService limitedService;

    @Mock
    private ShoppingCartDomain shoppingCartDomain;

    @Mock
    private CalcExecuter calcExecuter;

    @Mock
    private ActivityRedisHelpler activityRedisHelpler;
    @Mock
    private PromoGroupRelationDomain promoGroupRelationDomain;

    @InjectMocks
    private FlashSaleComponent underTest;

    @Mock
    private PromoGroupDomain promoGroupDomain;

    @Mock
    private RedisClient redisClient;

    @Mock
    private MarketingGroupComponent marketingGroupComponent;

    @Mock
    private MarketingGroupCodeService marketingGroupCodeService;


    @Mock
    private ValueOperations<String, String> stringStringValueOperations;
    @Mock
    private HashOperations<String, Object, Object> stringObjectObjectHashOperations;



    @Mock
    private StringRedisTemplate stringRedisTemplate;

    @Mock
    private LuckyDrawRuleService luckyDrawRuleService;

    @Mock
    private ActivityProductDetailService productDetailService;

    @Mock
    private PimFeignClient pimFeignClient;

    @Mock
    private OrderFeignClient orderFeignClient;




    @Mock
    private PrizeService prizeService;

    @Mock
    private MarketingLuckDrawPrizeProductService marketingLuckDrawPrizeProductService;



    public void findByActivityCode(){
        MarketingModel marketingModel = new MarketingModel();
        marketingModel.setSelectProductType(SelectorProductTypeEnum.SELECT_SKU.code());

        Mockito.when(marketingService.findByActivityCode(Mockito.any())).thenReturn(marketingModel);

    }


    @Test
    public void createFlashSale_失败(){
        FlashSaleDomain flashSaleDomain = new FlashSaleDomain();
        when(gTechCodeGenerator.generateCode(any(), any(), any(), any())).thenReturn("1");
        when(marketingService.insert(any())).thenReturn(0);
        when(operationLogService.insertLog(any(), any())).thenReturn(0);
        //when(promoGroupDomain.checkGroupEnabledByTenantCode(any(),any())).thenReturn(true);
        String activityCode = flashSaleComponent.createFlashSale(flashSaleDomain);
        Assert.assertEquals("1", activityCode);
    }

    @Test
    public void createFlashSale_只有商品0(){
        FlashSaleDomain flashSaleDomain = new FlashSaleDomain();
        List<MarketingLanguage> languages = new ArrayList<>();
        flashSaleDomain.setMarketingLanguages(languages);
        List<FlashSaleProduct> products = new ArrayList<>();
        FlashSaleProduct flashSaleProduct = new FlashSaleProduct();

        flashSaleProduct.setLimitFlag(0);
        flashSaleProduct.setMaxPerUserFlag(0);
        products.add(flashSaleProduct);
        flashSaleDomain.setProducts(products);

        when(gTechCodeGenerator.generateCode(any(), any(), any(), any())).thenReturn("1");
        when(marketingService.insert(any())).thenReturn(1);
        when(operationLogService.insertLog(any(), any())).thenReturn(0);
        when(flashSaleProductService.insert(any())).thenReturn(1);
        //when(promoGroupDomain.checkGroupEnabledByTenantCode(any(),any())).thenReturn(true);

        String activityCode = flashSaleComponent.createFlashSale(flashSaleDomain);
        Assert.assertEquals("1", activityCode);
    }


    @Test
    public void createFlashSale_只有商品(){
        FlashSaleDomain flashSaleDomain = new FlashSaleDomain();
        List<MarketingLanguage> languages = new ArrayList<>();
        flashSaleDomain.setMarketingLanguages(languages);
        List<FlashSaleProduct> products = new ArrayList<>();
        FlashSaleProduct flashSaleProduct = new FlashSaleProduct();

        flashSaleProduct.setLimitFlag(1);
        flashSaleProduct.setMaxPerUserFlag(1);
        products.add(flashSaleProduct);
        flashSaleDomain.setProducts(products);

        when(gTechCodeGenerator.generateCode(any(), any(), any(), any())).thenReturn("1");
        when(marketingService.insert(any())).thenReturn(1);
        when(operationLogService.insertLog(any(), any())).thenReturn(0);
        when(flashSaleProductService.insert(any())).thenReturn(1);
//        when(promoGroupDomain.checkGroupEnabledByTenantCode(any(),any())).thenReturn(true);

        String activityCode = flashSaleComponent.createFlashSale(flashSaleDomain);
        Assert.assertEquals("1", activityCode);
    }

    @Test
    public void createFlashSale_import(){
        FlashSaleDomain flashSaleDomain = new FlashSaleDomain();
        List<MarketingLanguage> languages = new ArrayList<>();
        MarketingLanguage language = new MarketingLanguage();
        language.setLanguage("1");
        language.setActivityName("1");
        languages.add(language);
        flashSaleDomain.setMarketingLanguages(languages);
        List<FlashSaleStore> stores = new ArrayList<>();
        stores.add(new FlashSaleStore());
        flashSaleDomain.setFlashSaleStores(stores);
        List<FlashSaleQualification> qualifications = new ArrayList<>();
        FlashSaleQualification e = new FlashSaleQualification();
        ArrayList<String> qualificationValue = new ArrayList<>();
        qualificationValue.add("1");
        e.setQualificationValue(qualificationValue);
        qualifications.add(e);
        flashSaleDomain.setFlashSaleQualifications(qualifications);
        flashSaleDomain.setImportNo("1");

        when(gTechCodeGenerator.generateCode(any(), any(), any(), any())).thenReturn("1");
        when(marketingService.insert(any())).thenReturn(1);
        when(flashSaleStoreService.insert(any())).thenReturn(1);
        when(flashSaleQualificationService.insert(any())).thenReturn(1);
        when(languageService.insert(any())).thenReturn(1);
        when(operationLogService.insertLog(any(), any())).thenReturn(0);
        when(flashSaleProductService.updateActivityCodeAndOrgCode(any(), any(), any())).thenReturn(1);
//        when(promoGroupDomain.checkGroupEnabledByTenantCode(any(),any())).thenReturn(true);

        String activityCode = flashSaleComponent.createFlashSale(flashSaleDomain);
        Assert.assertEquals("1", activityCode);
    }

    @Test
    public void createFlashSale1(){
        FlashSaleDomain flashSaleDomain = new FlashSaleDomain();
        flashSaleDomain.setActivityPeriod(new ActivityPeriod());
        List<MarketingLanguage> languages = new ArrayList<>();
        MarketingLanguage language = new MarketingLanguage();
        language.setLanguage("1");
        languages.add(language);
        flashSaleDomain.setMarketingLanguages(languages);
        List<FlashSaleStore> stores = new ArrayList<>();
        stores.add(new FlashSaleStore());
        flashSaleDomain.setFlashSaleStores(stores);
        List<FlashSaleQualification> qualifications = new ArrayList<>();
        FlashSaleQualification e = new FlashSaleQualification();
        ArrayList<String> qualificationValue = new ArrayList<>();
        qualificationValue.add("1");
        e.setQualificationValue(qualificationValue);
        e.setQualificationValueName(qualificationValue);
        qualifications.add(e);
        flashSaleDomain.setFlashSaleQualifications(qualifications);
        flashSaleDomain.setImportNo("1");
//        when(promoGroupDomain.checkGroupEnabledByTenantCode(any(),any())).thenReturn(true);

        when(gTechCodeGenerator.generateCode(any(), any(), any(), any())).thenReturn("1");
        when(marketingService.insert(any())).thenReturn(1);
        when(flashSaleStoreService.insert(any())).thenReturn(1);
        when(flashSaleQualificationService.insert(any())).thenReturn(1);
        when(operationLogService.insertLog(any(), any())).thenReturn(0);
        when(flashSaleProductService.updateActivityCodeAndOrgCode(any(), any(), any())).thenReturn(1);
        String activityCode = flashSaleComponent.createFlashSale(flashSaleDomain);
        Assert.assertEquals("1", activityCode);
    }


    @Test
    public void createFlashSale7(){
        FlashSaleDomain flashSaleDomain = new FlashSaleDomain();

        List<IncentiveLimited> limitedList = new ArrayList<>();

        IncentiveLimited limited = new IncentiveLimited();
        limited.setLimitationValue(new BigDecimal(1));
        limited.setLimitationCode("01");
        limitedList.add(limited);
        flashSaleDomain.setIncentiveLimitedFlag("00");
        flashSaleDomain.setIncentiveLimiteds(limitedList);

        flashSaleDomain.setActivityPeriod(new ActivityPeriod());
        List<MarketingLanguage> languages = new ArrayList<>();
        MarketingLanguage language = new MarketingLanguage();
        language.setLanguage("1");
        languages.add(language);
        flashSaleDomain.setMarketingLanguages(languages);
        List<FlashSaleStore> stores = new ArrayList<>();
        stores.add(new FlashSaleStore());
        flashSaleDomain.setFlashSaleStores(stores);
        List<FlashSaleQualification> qualifications = new ArrayList<>();
        FlashSaleQualification e = new FlashSaleQualification();
        ArrayList<String> qualificationValue = new ArrayList<>();
        qualificationValue.add("1");
        e.setQualificationValue(qualificationValue);
        e.setQualificationValueName(qualificationValue);
        qualifications.add(e);
        flashSaleDomain.setFlashSaleQualifications(qualifications);
        flashSaleDomain.setImportNo("1");

        when(gTechCodeGenerator.generateCode(any(), any(), any(), any())).thenReturn("1");
        when(marketingService.insert(any())).thenReturn(1);
        when(flashSaleStoreService.insert(any())).thenReturn(1);
        when(flashSaleQualificationService.insert(any())).thenReturn(1);
        when(flashSaleProductService.updateActivityCodeAndOrgCode(any(), any(), any())).thenReturn(0);
//        when(promoGroupDomain.checkGroupEnabledByTenantCode(any(),any())).thenReturn(false);

        MarketingGroup marketingGroup = new MarketingGroup();
        when(marketingService.insert((any()))).thenReturn(1);
        flashSaleDomain.setActivityType("06");
        flashSaleDomain.setMarketingGroup(marketingGroup);
        String activityCode = flashSaleComponent.createFlashSale(flashSaleDomain);
        Assert.assertEquals("1", activityCode);
    }

    @Test
    public void createFlashSale2(){
        FlashSaleDomain flashSaleDomain = new FlashSaleDomain();

        List<IncentiveLimited> limitedList = new ArrayList<>();

        IncentiveLimited limited = new IncentiveLimited();
        limited.setLimitationValue(new BigDecimal(1));
        limited.setLimitationCode("01");
        limitedList.add(limited);
        flashSaleDomain.setIncentiveLimitedFlag("01");
        flashSaleDomain.setIncentiveLimiteds(limitedList);

        flashSaleDomain.setActivityPeriod(new ActivityPeriod());
        List<MarketingLanguage> languages = new ArrayList<>();
        MarketingLanguage language = new MarketingLanguage();
        language.setLanguage("1");
        languages.add(language);
        flashSaleDomain.setMarketingLanguages(languages);
        List<FlashSaleStore> stores = new ArrayList<>();
        stores.add(new FlashSaleStore());
        flashSaleDomain.setFlashSaleStores(stores);
        List<FlashSaleQualification> qualifications = new ArrayList<>();
        FlashSaleQualification e = new FlashSaleQualification();
        ArrayList<String> qualificationValue = new ArrayList<>();
        qualificationValue.add("1");
        e.setQualificationValue(qualificationValue);
        e.setQualificationValueName(qualificationValue);
        qualifications.add(e);
        flashSaleDomain.setFlashSaleQualifications(qualifications);
        flashSaleDomain.setImportNo("1");

        when(gTechCodeGenerator.generateCode(any(), any(), any(), any())).thenReturn("1");
        when(marketingService.insert(any())).thenReturn(1);
        when(flashSaleStoreService.insert(any())).thenReturn(1);
        when(flashSaleQualificationService.insert(any())).thenReturn(1);
        when(flashSaleProductService.updateActivityCodeAndOrgCode(any(), any(), any())).thenReturn(0);
//        when(promoGroupDomain.checkGroupEnabledByTenantCode(any(),any())).thenReturn(true);

        MarketingGroup marketingGroup = new MarketingGroup();
        when(marketingService.insert((any()))).thenReturn(1);
        flashSaleDomain.setActivityType("06");
        flashSaleDomain.setMarketingGroup(marketingGroup);
        String activityCode = flashSaleComponent.createFlashSale(flashSaleDomain);
        Assert.assertEquals("1", activityCode);
    }

    @Test
    public void updateFlashSale(){
        FlashSaleDomain flashSaleDomain = new FlashSaleDomain();
        List<MarketingLanguage> languages = new ArrayList<>();
        flashSaleDomain.setMarketingLanguages(languages);
        List<FlashSaleProduct> products = new ArrayList<>();
        FlashSaleProduct flashSaleProduct = new FlashSaleProduct();
        flashSaleProduct.setLimitFlag(1);
        flashSaleProduct.setMaxPerUserFlag(1);

        products.add(flashSaleProduct);
        flashSaleDomain.setProducts(products);

        when(marketingService.findByActivityCode(any())).thenReturn(new MarketingModel());
        when(languageService.deleteByActivityCode(any())).thenReturn(1);
        when(flashSaleProductService.deleteByActivityCode(any())).thenReturn(1);
        when(flashSaleQualificationService.deleteByActivityCode(any())).thenReturn(1);
        when(flashSaleStoreService.deleteByActivityCode(any())).thenReturn(1);
        when(marketingService.updateByActivityCode(any())).thenReturn(1);
        when(flashSaleProductService.insert(any())).thenReturn(1);
        when(operationLogService.insertLog(any(), any())).thenReturn(0);
        int i = flashSaleComponent.updateFlashSale(flashSaleDomain);
        Assert.assertEquals(1, i);
    }

    @Test
    public void updateFlashSale_失败(){
        FlashSaleDomain flashSaleDomain = new FlashSaleDomain();
        List<MarketingLanguage> languages = new ArrayList<>();
        flashSaleDomain.setMarketingLanguages(languages);
        List<FlashSaleProduct> products = new ArrayList<>();
        products.add(new FlashSaleProduct());
        flashSaleDomain.setProducts(products);
        findByActivityCode();
        when(marketingService.findByActivityCode(any())).thenReturn(new MarketingModel());
        when(languageService.deleteByActivityCode(any())).thenReturn(1);
        when(flashSaleProductService.deleteByActivityCode(any())).thenReturn(1);
        when(flashSaleQualificationService.deleteByActivityCode(any())).thenReturn(1);
        when(flashSaleStoreService.deleteByActivityCode(any())).thenReturn(1);
        when(operationLogService.insertLog(any(), any())).thenReturn(0);
        when(marketingService.updateByActivityCode(any())).thenReturn(0);

        when(limitedService.deleteLimitedByActivityCode(any())).thenReturn(1);


        int i = flashSaleComponent.updateFlashSale(flashSaleDomain);
        Assert.assertEquals(0, i);
    }

    @Test(expected = PromotionException.class)
    public void updateFlashSale1(){
        FlashSaleDomain flashSaleDomain = new FlashSaleDomain();
        List<MarketingLanguage> languages = new ArrayList<>();
        flashSaleDomain.setMarketingLanguages(languages);
        List<FlashSaleProduct> products = new ArrayList<>();
        products.add(new FlashSaleProduct());
        flashSaleDomain.setProducts(products);

        when(marketingService.findByActivityCode(any())).thenReturn(null);
        int i = flashSaleComponent.updateFlashSale(flashSaleDomain);
        Assert.assertEquals(1, i);
    }

    @Test
    public void updateFlashSale2(){
        FlashSaleDomain flashSaleDomain = new FlashSaleDomain();
        List<MarketingLanguage> languages = new ArrayList<>();
        flashSaleDomain.setMarketingLanguages(languages);
        List<FlashSaleProduct> products = new ArrayList<>();
        products.add(new FlashSaleProduct());
        flashSaleDomain.setProducts(products);
        MarketingModel marketingModel = new MarketingModel();
        findByActivityCode();
        flashSaleDomain.setActivityCode("1");
        marketingModel.setActivityStatus("04");
        marketingModel.setActivityEnd(DateUtil.format(DateUtil.addDay(1),DateUtil.FORMAT_YYYYMMDDHHMISS_14));
        when(marketingService.findByActivityCode(any())).thenReturn(marketingModel);
        when(languageService.deleteByActivityCode(any())).thenReturn(1);
        when(flashSaleQualificationService.deleteByActivityCode(any())).thenReturn(1);
        when(marketingService.updateByActivityCode(any())).thenReturn(1);
        when(marketingGroupService.deleteByActivityCode(any())).thenReturn(1);
        when(operationLogService.insertLog(any(), any())).thenReturn(0);
        int i = flashSaleComponent.updateFlashSale(flashSaleDomain);
        Assert.assertEquals(1, i);
    }

    @Test
    public void createMarketingGroup(){
        MarketingGroup marketingModel = new MarketingGroup();
        FlashSaleDomain flashSaleDomain = new FlashSaleDomain();
        flashSaleDomain.setActivityType("06");
        flashSaleDomain.setMarketingGroup(marketingModel);
        flashSaleComponent.createMarketingGroup(flashSaleDomain,"1");

    }

    @Test(expected = PromotionParamValidateException.class)
    public void createMarketingGroup1(){
        MarketingGroup marketingModel = new MarketingGroup();
        FlashSaleDomain flashSaleDomain = new FlashSaleDomain();
        flashSaleDomain.setActivityType("05");
        flashSaleDomain.setMarketingGroup(marketingModel);
        flashSaleComponent.createMarketingGroup(flashSaleDomain,"1");

    }

    @Test
    public void findFlashSale(){
        MarketingModel marketingModel = new MarketingModel();
        marketingModel.setActivityCode("1");
        when(marketingService.findByActivityCode((any()))).thenReturn(marketingModel);
        when(languageService.findListByActivityCode((any()))).thenReturn(null);
        when(flashSaleQualificationService.findListByActivityCode((any()))).thenReturn(null);
        when(flashSaleStoreService.findListByActivityCode((any()))).thenReturn(null);

        List<FlashSaleProductModel> productModels = new ArrayList<>();
        FlashSaleProductModel productModel = new FlashSaleProductModel();
        productModel.setProductCode("1");
        productModels.add(productModel);

        when(flashSaleProductService.findListByActivityCode((any()))).thenReturn(productModels);

        when(activityComponentDomain.getActivityAuditConfig((any()))).thenReturn(null);
        FlashSaleFindResult flashSale = flashSaleComponent.findFlashSale("1");
        Assert.assertEquals("1", flashSale.getActivityCode());

    }

    @Test
    public void findFlashSale2(){
        MarketingModel marketingModel = new MarketingModel();
        marketingModel.setActivityCode("1");
        marketingModel.setIncentiveLimitedFlag("00");
        when(marketingService.findByActivityCode((any()))).thenReturn(marketingModel);
        when(languageService.findListByActivityCode((any()))).thenReturn(null);
        when(flashSaleQualificationService.findListByActivityCode((any()))).thenReturn(null);
        when(flashSaleStoreService.findListByActivityCode((any()))).thenReturn(null);

        List<FlashSaleProductModel> productModels = new ArrayList<>();
        FlashSaleProductModel productModel = new FlashSaleProductModel();
        productModel.setLimitFlag(0);
        productModel.setMaxPerUserFlag(0);
        productModel.setProductCode("");
        productModels.add(productModel);

        when(flashSaleProductService.findListByActivityCode((any()))).thenReturn(productModels);

        when(activityComponentDomain.getActivityAuditConfig((any()))).thenReturn(null);
        FlashSaleFindResult flashSale = flashSaleComponent.findFlashSale("1");
        Assert.assertEquals("1", flashSale.getActivityCode());

    }

    @Test
    public void findFlashSale_is_null(){
        MarketingModel marketingModel = new MarketingModel();
        marketingModel.setActivityCode("1");
        marketingModel.setIncentiveLimitedFlag("00");
        when(marketingService.findByActivityCode((any()))).thenReturn(marketingModel);
        when(languageService.findListByActivityCode((any()))).thenReturn(null);
        when(flashSaleQualificationService.findListByActivityCode((any()))).thenReturn(null);
        when(flashSaleStoreService.findListByActivityCode((any()))).thenReturn(null);

        List<FlashSaleProductModel> productModels = new ArrayList<>();
        FlashSaleProductModel productModel = new FlashSaleProductModel();
        productModel.setLimitFlag(null);
        productModel.setMaxPerUserFlag(null);
        productModel.setProductCode("");
        productModels.add(productModel);

        when(flashSaleProductService.findListByActivityCode((any()))).thenReturn(productModels);

        when(activityComponentDomain.getActivityAuditConfig((any()))).thenReturn(null);
        FlashSaleFindResult flashSale = flashSaleComponent.findFlashSale("1");
        Assert.assertEquals("1", flashSale.getActivityCode());

    }

    @Test
    public void findFlashSale_is_not_null(){
        MarketingModel marketingModel = new MarketingModel();
        marketingModel.setActivityCode("1");
        marketingModel.setIncentiveLimitedFlag("00");
        when(marketingService.findByActivityCode((any()))).thenReturn(marketingModel);
        when(languageService.findListByActivityCode((any()))).thenReturn(null);
        when(flashSaleQualificationService.findListByActivityCode((any()))).thenReturn(null);
        when(flashSaleStoreService.findListByActivityCode((any()))).thenReturn(null);

        List<FlashSaleProductModel> productModels = new ArrayList<>();
        FlashSaleProductModel productModel = new FlashSaleProductModel();
        productModel.setLimitFlag(1);
        productModel.setMaxPerUserFlag(1);
        productModel.setProductCode("");
        productModels.add(productModel);

        when(flashSaleProductService.findListByActivityCode((any()))).thenReturn(productModels);

        when(activityComponentDomain.getActivityAuditConfig((any()))).thenReturn(null);
        FlashSaleFindResult flashSale = flashSaleComponent.findFlashSale("1");
        Assert.assertEquals("1", flashSale.getActivityCode());

    }


    @Test
    public void findFlashSale5(){
        MarketingModel marketingModel = new MarketingModel();
        marketingModel.setActivityCode("1");
        marketingModel.setIncentiveLimitedFlag("01");
        when(marketingService.findByActivityCode((any()))).thenReturn(marketingModel);
        when(languageService.findListByActivityCode((any()))).thenReturn(null);
        when(flashSaleQualificationService.findListByActivityCode((any()))).thenReturn(null);
        when(flashSaleStoreService.findListByActivityCode((any()))).thenReturn(null);
        findByActivityCode();
        List<FlashSaleProductModel> productModels = new ArrayList<>();
        FlashSaleProductModel productModel = new FlashSaleProductModel();
        productModel.setProductCode("");
        productModels.add(productModel);

        when(flashSaleProductService.findListByActivityCode((any()))).thenReturn(productModels);

        when(activityComponentDomain.getActivityAuditConfig((any()))).thenReturn(null);
        FlashSaleFindResult flashSale = flashSaleComponent.findFlashSale("1");
        Assert.assertEquals(null, flashSale.getActivityCode());

    }

    @Test(expected = PromotionException.class)
    public void findFlashSale1(){
        MarketingModel marketingModel = new MarketingModel();
        marketingModel.setActivityCode("1");
        when(marketingService.findByActivityCode((any()))).thenReturn(null);

        FlashSaleFindResult flashSale = flashSaleComponent.findFlashSale("1");
        Assert.assertEquals("1", flashSale.getActivityCode());
    }

    @Test
    public void activitySkuPrice_null(){
        SingleProductDTO product = new SingleProductDTO();
        product.setSkuCode("1");
        Map<String, CacheFlashSaleModel > activityCacheMap = new HashedMap<>();

        FlashSaleSkuActivityPriceDTO flashSaleSkuActivityPriceDTO = flashSaleComponent.activitySkuPrice(product, activityCacheMap);
        Assert.assertEquals(product.getSkuCode(), flashSaleSkuActivityPriceDTO.getSkuCode());
    }

    @Test
    public void activitySkuPrice_no_sku(){
        SingleProductDTO product = new SingleProductDTO();
        product.setSkuCode("1");
        Map<String, CacheFlashSaleModel > activityCacheMap = new HashedMap<>();
        CacheFlashSaleModel model = new CacheFlashSaleModel();
        List<FlashSaleProductModel> productModels = new ArrayList<>();
        FlashSaleProductModel productModel = new FlashSaleProductModel();
        productModel.setSkuCode("2");
        productModels.add(productModel);
        model.setProducts(productModels);
        activityCacheMap.put("1", model);

        FlashSaleProductModel byActivityAndSku = new FlashSaleProductModel();

        FlashSaleSkuActivityPriceDTO flashSaleSkuActivityPriceDTO = flashSaleComponent.activitySkuPrice(product, activityCacheMap);
        Assert.assertEquals(product.getSkuCode(), flashSaleSkuActivityPriceDTO.getSkuCode());
    }

    @Test
    public void activitySkuPrice(){
        SingleProductDTO product = new SingleProductDTO();
        product.setSkuCode("1");
        Map<String, CacheFlashSaleModel > activityCacheMap = new HashedMap<>();
        CacheFlashSaleModel model = new CacheFlashSaleModel();
        List<FlashSaleProductModel> productModels = new ArrayList<>();
        FlashSaleProductModel productModel = new FlashSaleProductModel();
        productModel.setSkuCode("1");
        productModels.add(productModel);
        model.setProducts(productModels);
        activityCacheMap.put("1", model);


        FlashSaleProductModel byActivityAndSku = new FlashSaleProductModel();
        byActivityAndSku.setSkuInventory(1);
        byActivityAndSku.setMaxPerUser(1);

        when(flashSaleProductService.findByActivityAndSku(any(), any())).thenReturn(byActivityAndSku);
        FlashSaleSkuActivityPriceDTO flashSaleSkuActivityPriceDTO = flashSaleComponent.activitySkuPrice(product, activityCacheMap);
        Assert.assertEquals(product.getSkuCode(), flashSaleSkuActivityPriceDTO.getSkuCode());
    }


    @Test
    public void activitySkuPriceList_null(){
        SingleProductDTO product = new SingleProductDTO();
        Map<String, CacheFlashSaleModel > activityCacheMap = new HashedMap<>();

        List<FlashSaleProductModel> flashSaleProductModelList = new ArrayList<>();
        FlashSaleProductModel flashSaleProductModel = new FlashSaleProductModel();
        flashSaleProductModel.setFlashPrice(new BigDecimal(1));
        flashSaleProductModel.setSkuCode("1");
        flashSaleProductModel.setMaxPerUser(1);
        flashSaleProductModel.setSkuInventory(1);
        flashSaleProductModelList.add(flashSaleProductModel);

        flashSaleComponent.activitySkuPriceList(product, activityCacheMap);
        CacheFlashSaleModel cacheFlashSaleModel = new CacheFlashSaleModel();
        cacheFlashSaleModel.setActivityCode("test");
        activityCacheMap.put("1",cacheFlashSaleModel);
        flashSaleComponent.activitySkuPriceList(product, activityCacheMap);

        List<SkuParam> skuList = new ArrayList<>();
        SkuParam skuParam = new SkuParam();
        skuParam.setSkuCode("test");
        skuList.add(skuParam);
        product.setSkuList(skuList);
        Mockito.when(flashSaleProductService.findByActivityAndSkuList(Mockito.any(), Mockito.any())).thenReturn(flashSaleProductModelList);
        flashSaleComponent.activitySkuPriceList(product, activityCacheMap);
    }

    @Test
    public void getActivitiesByProduct_null(){
        Map<String, CacheFlashSaleModel> activityCacheMap = new HashedMap<>();
        TenantProductDTO productDTO = new TenantProductDTO();
        List<FlashSaleActivityInfoDTO> activitiesByProduct = flashSaleComponent.getActivitiesByProduct(activityCacheMap, productDTO);
        Assert.assertEquals(0, activitiesByProduct.size());
    }


    @Test
    public void  getActivitiesByProduct_not_empty(){
        Map<String, CacheFlashSaleModel> activityCacheMap = new HashedMap<>();
        CacheFlashSaleModel model = new CacheFlashSaleModel();
        model.setActivityCode("1");
        activityCacheMap.put("1",model);
        TenantProductDTO productDTO = new TenantProductDTO();
        productDTO.setSkuCode("11111");

        List<FlashSaleProductModel> flashSaleProductModels = new ArrayList<>();
        FlashSaleProductModel productModel1 = new FlashSaleProductModel();
        productModel1.setSkuCode("111121");
        productModel1.setActivityCode("3");
        flashSaleProductModels.add(productModel1);

        FlashSaleProductModel productModel = new FlashSaleProductModel();
        productModel.setSkuCode("11111");
        productModel.setActivityCode("1");
        flashSaleProductModels.add(productModel);
        when(flashSaleProductService.queryProductsByActivityCodesAndSkuCode(any(), any())).thenReturn(flashSaleProductModels);

        List<FlashSaleActivityInfoDTO> activitiesByProduct = flashSaleComponent.getActivitiesByProduct(activityCacheMap, productDTO);
        Assert.assertEquals(1, activitiesByProduct.size());
    }

    @Test
    public void getActivitiesByProduct(){
        Map<String, CacheFlashSaleModel> activityCacheMap = new HashedMap<>();
        TenantProductDTO productDTO = new TenantProductDTO();
        List<FlashSaleActivityInfoDTO> activitiesByProduct = flashSaleComponent.getActivitiesByProduct(activityCacheMap, productDTO);
        Assert.assertEquals(0, activitiesByProduct.size());
    }

    @Test(expected = GTechBaseException.class)
    public void queryProductsByActivityCode_null(){
        String tenantCode = "1";
        String language = "1";
        String activityCode = "1";
        PageParam page = new PageParam();

        when(marketingService.findByActivityCode(any())).thenReturn(null);

        FlashSaleQueryProductListResult result = flashSaleComponent.queryProductsByActivityCode(language, activityCode, page);
        Assert.assertEquals("1", result.getActivityName());
    }

    @Test
    public void queryProductsByActivityCode(){
        String tenantCode = "1";
        String language = "1";
        String activityCode = "1";
        PageParam page = new PageParam();
        MarketingModel marketingModel = new MarketingModel();
        marketingModel.setActivityName("1");
        marketingModel.setActivityStatus(ActivityStatusEnum.EFFECTIVE.code());
        marketingModel.setActivityEnd(DateUtil.format(DateUtil.addDay(new Date(), 1), DateUtil.FORMAT_YYYYMMDDHHMISS_14));

        List<MarketingLanguageModel> languages = new ArrayList<>();
        MarketingLanguageModel languageModel = new MarketingLanguageModel();
        languageModel.setLanguage(language);
        languageModel.setActivityName("1");
        languageModel.setActivityDesc("1");
        languageModel.setActivityLabel("1");
        languageModel.setActivityShortDesc("1");
        languages.add(languageModel);

        PageData<FlashSaleProductModel> flashSaleProductModelPageData = new PageData<>();

        when(marketingService.findByActivityCode(any())).thenReturn(marketingModel);
        when(languageService.findListByActivityCode(any())).thenReturn(languages);
        when(flashSaleProductService.selectPageList(any(), any())).thenReturn(flashSaleProductModelPageData);

        FlashSaleQueryProductListResult result = flashSaleComponent.queryProductsByActivityCode(language, activityCode, page);
        Assert.assertEquals("1", result.getActivityName());
    }

    @Test
    public void checkQuota_error(){
        String activityCode = "1";
        String skuCode = "1";
        Integer quality = 1;
        String memberCode = "1";
//        when(flashSaleProductService.findByActivityAndSku(any(), Mockito.anyString())).thenReturn(null);
        boolean b = flashSaleComponent.checkQuota(activityCode, skuCode, quality, memberCode,null);
        Assert.assertFalse(b);
    }

    @Test
    public void checkQuota_error1(){
        String activityCode = "1";
        String skuCode = "1";
        Integer quality = 1;
        String memberCode = "1";
        FlashSaleProductModel productModel = new FlashSaleProductModel();
        productModel.setSkuInventory(0);
//        when(flashSaleProductService.findByActivityAndSku(any(), Mockito.anyString())).thenReturn(productModel);
        FlashSaleProductModel currentProductModel = new FlashSaleProductModel();
        boolean b = flashSaleComponent.checkQuota(activityCode, skuCode, quality, memberCode,null);
        Assert.assertFalse(b);
    }

    @Test
    public void checkQuota_no_max_per_user(){
        String activityCode = "1";
        String skuCode = "1";
        Integer quality = 1;
        String memberCode = "1";
        FlashSaleProductModel productModel = new FlashSaleProductModel();
        productModel.setSkuInventory(2);
        productModel.setMaxPerUser(0);
//        when(flashSaleProductService.findByActivityAndSku(any(), Mockito.anyString())).thenReturn(productModel);
        FlashSaleProductModel currentProductModel = new FlashSaleProductModel();
        currentProductModel.setSkuInventory(200);
        currentProductModel.setMaxPerUser(10);
        boolean b = flashSaleComponent.checkQuota(activityCode, skuCode, quality, memberCode,currentProductModel);
    }

    @Test
    public void checkQuota(){
        String activityCode = "1";
        String skuCode = "1";
        Integer quality = 1;
        String memberCode = "1";
        FlashSaleProductModel productModel = new FlashSaleProductModel();
        productModel.setSkuInventory(2);
        productModel.setMaxPerUser(2);
//        when(flashSaleProductService.findByActivityAndSku(any(), Mockito.anyString())).thenReturn(productModel);
        FlashSaleProductModel currentProductModel = new FlashSaleProductModel();
        currentProductModel.setSkuInventory(200);
        currentProductModel.setMaxPerUser(10);
        boolean b = flashSaleComponent.checkQuota(activityCode, skuCode, quality, memberCode,currentProductModel);
        Assert.assertTrue(b);
    }

    @Test
    public void checkQuota1(){
        String activityCode = "1";
        String skuCode = "1";
        Integer quality = 1;
        String memberCode = "1";
        FlashSaleProductModel productModel = new FlashSaleProductModel();
        productModel.setSkuInventory(2);
        productModel.setMaxPerUser(2);
//        when(flashSaleProductService.findByActivityAndSku(any(), Mockito.anyString())).thenReturn(productModel);
//        when(redisOpsHelper.getLimitedValue(Mockito.anyString(), any(), any(), any(), any())).thenReturn(null);
        FlashSaleProductModel currentProductModel = new FlashSaleProductModel();
        currentProductModel.setSkuInventory(200);
        currentProductModel.setMaxPerUser(10);
        boolean b = flashSaleComponent.checkQuota(activityCode, skuCode, quality, memberCode,currentProductModel);
        Assert.assertTrue(b);
    }

    @Test(expected = PromotionException.class)
    public void commitOrder00_leader1(){
        FlashSaleCreateOrderParam orderCommitDTO = new FlashSaleCreateOrderParam();
        orderCommitDTO.setPromoDeductedAmount(BigDecimal.ONE);
        List<ShoppingCartStore> cartStoreList = new ArrayList<>();
        ShoppingCartStore shoppingCartStore = new ShoppingCartStore();
        List<ShoppingCartItem> cartItemList = new ArrayList<>();
        ShoppingCartItem item = new ShoppingCartItem();
        item.setSkuCode("1");
        item.setProductCode("1");
        item.setQuantity(1);
        item.setProductPrice(new BigDecimal("123"));
        item.setSelectionFlag("01");
        cartItemList.add(item);
        shoppingCartStore.setCartItemList(cartItemList);
        cartStoreList.add(shoppingCartStore);
        orderCommitDTO.setCartStoreList(cartStoreList);

        List<FlashSaleOrderCalaResult > list = new ArrayList<>();
        FlashSaleOrderCalaResult result = new FlashSaleOrderCalaResult();
        result.setEffectiveFlag(true);
        result.setUserLimitation("1");
        result.setPromoRewardAmount(BigDecimal.ONE);
        List<FlashSaleOrderCalaResult.ShoppingCartItem> items = new ArrayList<>();
        FlashSaleOrderCalaResult.ShoppingCartItem e = new FlashSaleOrderCalaResult.ShoppingCartItem();
        e.setPromoQuantity(1);
        items.add(e);
        result.setShoppingCartItems(items);
        MarketingGroupUserResult marketingGroupUserResult = new MarketingGroupUserResult();
        marketingGroupUserResult.setActivityCode("!");
        marketingGroupUserResult.setGroupSize(3);
        Date date = DateUtil.addDay(1);
        String format = DateUtil.format(date, DateUtil.FORMAT_YYYYMMDDHHMISS_14);

        marketingGroupUserResult.setEffectiveTime(format);
        marketingGroupUserResult.setGroupSize(3);
        marketingGroupUserResult.setEffectiveHour(1);
        marketingGroupUserResult.setTeamLeader(TeamLeaderEnum.MEMBER.code());
        result.setGroupUser(marketingGroupUserResult);
        list.add(result);
        MarketingModel marketingModel = new MarketingModel();
        marketingModel.setActivityName("1");

        marketingModel.setActivityStatus(ActivityStatusEnum.EFFECTIVE.code());
        marketingModel.setActivityEnd(DateUtil.format(DateUtil.addDay(new Date(), 1), DateUtil.FORMAT_YYYYMMDDHHMISS_14));
        marketingModel.setSelectProductType(SelectorProductTypeEnum.SELECT_SKU.code());

        when(flashSaleOrderService.checkLeaderPayOrder(any(),any(),any(),any())).thenReturn(0);

        flashSaleComponent.commitOrder(orderCommitDTO, list);

    }

    @Test
    public void commitOrder00_leader(){
        FlashSaleCreateOrderParam orderCommitDTO = new FlashSaleCreateOrderParam();
        orderCommitDTO.setPromoDeductedAmount(BigDecimal.ONE);
        List<ShoppingCartStore> cartStoreList = new ArrayList<>();
        ShoppingCartStore shoppingCartStore = new ShoppingCartStore();
        List<ShoppingCartItem> cartItemList = new ArrayList<>();
        ShoppingCartItem item = new ShoppingCartItem();
        item.setSkuCode("1");
        item.setProductCode("1");
        item.setQuantity(1);
        item.setProductPrice(new BigDecimal("123"));
        item.setSelectionFlag("01");
        cartItemList.add(item);
        shoppingCartStore.setCartItemList(cartItemList);
        cartStoreList.add(shoppingCartStore);
        orderCommitDTO.setCartStoreList(cartStoreList);

        List<FlashSaleOrderCalaResult > list = new ArrayList<>();
        FlashSaleOrderCalaResult result = new FlashSaleOrderCalaResult();
        result.setEffectiveFlag(true);
        result.setUserLimitation("1");
        result.setPromoRewardAmount(BigDecimal.ONE);
        List<FlashSaleOrderCalaResult.ShoppingCartItem> items = new ArrayList<>();
        FlashSaleOrderCalaResult.ShoppingCartItem e = new FlashSaleOrderCalaResult.ShoppingCartItem();
        e.setPromoQuantity(1);
        items.add(e);
        result.setShoppingCartItems(items);
        MarketingGroupUserResult marketingGroupUserResult = new MarketingGroupUserResult();
        marketingGroupUserResult.setActivityCode("!");
        marketingGroupUserResult.setGroupSize(3);
        Date date = DateUtil.addDay(1);
        String format = DateUtil.format(date, DateUtil.FORMAT_YYYYMMDDHHMISS_14);

        marketingGroupUserResult.setEffectiveTime(format);
        marketingGroupUserResult.setGroupSize(3);
        marketingGroupUserResult.setEffectiveHour(1);
        marketingGroupUserResult.setTeamLeader(TeamLeaderEnum.MEMBER.code());
        result.setGroupUser(marketingGroupUserResult);
        list.add(result);
        MarketingModel marketingModel = new MarketingModel();
        marketingModel.setActivityName("1");
        marketingModel.setActivityType(ActivityTypeEnum.GROUP.code());
        marketingModel.setActivityStatus(ActivityStatusEnum.EFFECTIVE.code());
        marketingModel.setActivityEnd(DateUtil.format(DateUtil.addDay(new Date(), 1), DateUtil.FORMAT_YYYYMMDDHHMISS_14));
        marketingModel.setSelectProductType(SelectorProductTypeEnum.SELECT_SKU.code());
        MarketingModel byActivityCode = new MarketingModel();
        byActivityCode.setActivityType(ActivityTypeEnum.GROUP.code());
        when( marketingService.findByActivityCode(Mockito.any())).thenReturn(byActivityCode);

        when(flashSaleOrderDetailService.insert(any())).thenReturn(1);
        when(flashSaleProductService.dealInventory(any(), any(), any())).thenReturn(1);
        when(marketingService.findByActivityCode(any())).thenReturn(marketingModel);
//        when(redisClient.getString(any())).thenReturn("2");

        when(flashSaleOrderService.checkLeaderPayOrder(any(),any(),any(),any())).thenReturn(1);

        when(marketingGroupUserService.insert(any())).thenReturn(1);
        when(marketingGroupCodeService.deductGroupInventory(any(),any(),any())).thenReturn(1);

        when(marketingGroupUserService.checkGroupProductCode(any(),any(),any(),any(),any())).thenReturn(1);

        flashSaleComponent.commitOrder(orderCommitDTO, list);
    }

    @Test(expected = PromotionException.class)
    public void commitOrder00_leader11(){
        FlashSaleCreateOrderParam orderCommitDTO = new FlashSaleCreateOrderParam();
        orderCommitDTO.setPromoDeductedAmount(BigDecimal.ONE);
        List<ShoppingCartStore> cartStoreList = new ArrayList<>();
        ShoppingCartStore shoppingCartStore = new ShoppingCartStore();
        List<ShoppingCartItem> cartItemList = new ArrayList<>();
        ShoppingCartItem item = new ShoppingCartItem();
        item.setSkuCode("1");
        item.setProductCode("1");
        item.setQuantity(1);
        item.setProductPrice(new BigDecimal("123"));
        item.setSelectionFlag("01");
        cartItemList.add(item);
        shoppingCartStore.setCartItemList(cartItemList);
        cartStoreList.add(shoppingCartStore);
        orderCommitDTO.setCartStoreList(cartStoreList);

        List<FlashSaleOrderCalaResult > list = new ArrayList<>();
        FlashSaleOrderCalaResult result = new FlashSaleOrderCalaResult();
        result.setEffectiveFlag(true);
        result.setUserLimitation("1");
        result.setPromoRewardAmount(BigDecimal.ONE);
        List<FlashSaleOrderCalaResult.ShoppingCartItem> items = new ArrayList<>();
        FlashSaleOrderCalaResult.ShoppingCartItem e = new FlashSaleOrderCalaResult.ShoppingCartItem();
        e.setPromoQuantity(1);
        items.add(e);
        result.setShoppingCartItems(items);
        MarketingGroupUserResult marketingGroupUserResult = new MarketingGroupUserResult();
        marketingGroupUserResult.setActivityCode("!");
        marketingGroupUserResult.setGroupSize(3);
        Date date = DateUtil.addDay(1);
        String format = DateUtil.format(date, DateUtil.FORMAT_YYYYMMDDHHMISS_14);

        marketingGroupUserResult.setEffectiveTime(format);
        marketingGroupUserResult.setGroupSize(3);
        marketingGroupUserResult.setEffectiveHour(1);
        marketingGroupUserResult.setTeamLeader(TeamLeaderEnum.MEMBER.code());
        result.setGroupUser(marketingGroupUserResult);
        list.add(result);
        MarketingModel marketingModel = new MarketingModel();
        marketingModel.setActivityName("1");

        marketingModel.setActivityStatus(ActivityStatusEnum.EFFECTIVE.code());
        marketingModel.setActivityEnd(DateUtil.format(DateUtil.addDay(new Date(), 1), DateUtil.FORMAT_YYYYMMDDHHMISS_14));
        marketingModel.setSelectProductType(SelectorProductTypeEnum.SELECT_SKU.code());

        when(flashSaleOrderService.checkLeaderPayOrder(any(),any(),any(),any())).thenReturn(1);
        when(marketingGroupUserService.checkGroupProductCode(any(),any(),any(),any(),any())).thenReturn(0);

        flashSaleComponent.commitOrder(orderCommitDTO, list);
    }


    @Test(expected = PromotionException.class)
    public void commitOrder00(){
        FlashSaleCreateOrderParam orderCommitDTO = new FlashSaleCreateOrderParam();
        orderCommitDTO.setPromoDeductedAmount(BigDecimal.ONE);
        List<ShoppingCartStore> cartStoreList = new ArrayList<>();
        ShoppingCartStore shoppingCartStore = new ShoppingCartStore();
        List<ShoppingCartItem> cartItemList = new ArrayList<>();
        ShoppingCartItem item = new ShoppingCartItem();
        item.setSkuCode("1");
        item.setProductCode("1");
        item.setQuantity(1);
        item.setProductPrice(new BigDecimal("123"));
        item.setSelectionFlag("01");
        cartItemList.add(item);
        shoppingCartStore.setCartItemList(cartItemList);
        cartStoreList.add(shoppingCartStore);
        orderCommitDTO.setCartStoreList(cartStoreList);

        List<FlashSaleOrderCalaResult > list = new ArrayList<>();
        FlashSaleOrderCalaResult result = new FlashSaleOrderCalaResult();
        result.setEffectiveFlag(true);
        result.setUserLimitation("1");
        result.setPromoRewardAmount(BigDecimal.ONE);
        List<FlashSaleOrderCalaResult.ShoppingCartItem> items = new ArrayList<>();
        FlashSaleOrderCalaResult.ShoppingCartItem e = new FlashSaleOrderCalaResult.ShoppingCartItem();
        e.setPromoQuantity(1);
        items.add(e);
        result.setShoppingCartItems(items);
        MarketingGroupUserResult marketingGroupUserResult = new MarketingGroupUserResult();
        marketingGroupUserResult.setActivityCode("!");
        marketingGroupUserResult.setGroupSize(3);
        marketingGroupUserResult.setAutoGroupFlag(1);
        Date date = DateUtil.addDay(1);
        String format = DateUtil.format(date, DateUtil.FORMAT_YYYYMMDDHHMISS_14);

        marketingGroupUserResult.setEffectiveTime(format);
        marketingGroupUserResult.setGroupSize(3);
        marketingGroupUserResult.setEffectiveHour(1);
        marketingGroupUserResult.setTeamLeader("0");
        result.setGroupUser(marketingGroupUserResult);
        list.add(result);
        MarketingModel marketingModel = new MarketingModel();
        marketingModel.setActivityName("1");
        marketingModel.setActivityStatus(ActivityStatusEnum.EFFECTIVE.code());
        marketingModel.setActivityEnd(DateUtil.format(DateUtil.addDay(new Date(), 1), DateUtil.FORMAT_YYYYMMDDHHMISS_14));
        marketingModel.setSelectProductType(SelectorProductTypeEnum.SELECT_SKU.code());
//        when(flashSaleOrderDetailService.insert(any())).thenReturn(1);
//        when(flashSaleProductService.dealInventory(any(), any(), any())).thenReturn(1);
//        when(marketingService.findByActivityCode(any())).thenReturn(marketingModel);
//        when(redisClient.getString(any())).thenReturn("2");

//        when(marketingGroupUserService.insert(any())).thenReturn(1);
        when(flashSaleOrderService.checkLeaderPayOrder(any(), any(), any(), any())).thenReturn(1);

        flashSaleComponent.commitOrder(orderCommitDTO, list);

    }


    @Test
    public void commitOrder001(){
        FlashSaleCreateOrderParam orderCommitDTO = new FlashSaleCreateOrderParam();
        orderCommitDTO.setPromoDeductedAmount(BigDecimal.ONE);
        List<ShoppingCartStore> cartStoreList = new ArrayList<>();
        ShoppingCartStore shoppingCartStore = new ShoppingCartStore();
        List<ShoppingCartItem> cartItemList = new ArrayList<>();
        ShoppingCartItem item = new ShoppingCartItem();
        item.setSkuCode("1");
        item.setProductCode("1");
        item.setQuantity(1);
        item.setProductPrice(new BigDecimal("123"));
        item.setSelectionFlag("01");
        cartItemList.add(item);
        shoppingCartStore.setCartItemList(cartItemList);
        cartStoreList.add(shoppingCartStore);
        orderCommitDTO.setCartStoreList(cartStoreList);

        List<FlashSaleOrderCalaResult > list = new ArrayList<>();
        FlashSaleOrderCalaResult result = new FlashSaleOrderCalaResult();
        result.setEffectiveFlag(true);
        result.setUserLimitation("1");
        result.setPromoRewardAmount(BigDecimal.ONE);
        List<FlashSaleOrderCalaResult.ShoppingCartItem> items = new ArrayList<>();
        FlashSaleOrderCalaResult.ShoppingCartItem e = new FlashSaleOrderCalaResult.ShoppingCartItem();
        e.setPromoQuantity(1);
        items.add(e);
        result.setShoppingCartItems(items);
        MarketingGroupUserResult marketingGroupUserResult = new MarketingGroupUserResult();
        marketingGroupUserResult.setActivityCode("!");
        marketingGroupUserResult.setGroupSize(3);
        marketingGroupUserResult.setAutoGroupFlag(1);
        marketingGroupUserResult.setTeamLeader("1");
        Date date = DateUtil.addDay(1);
        String format = DateUtil.format(date, DateUtil.FORMAT_YYYYMMDDHHMISS_14);

        marketingGroupUserResult.setEffectiveTime(format);
        marketingGroupUserResult.setEffectiveHour(1);
        result.setGroupUser(marketingGroupUserResult);
        list.add(result);
        MarketingModel marketingModel = new MarketingModel();
        marketingModel.setActivityName("1");
        marketingModel.setActivityStatus(ActivityStatusEnum.EFFECTIVE.code());
        marketingModel.setActivityEnd(DateUtil.format(DateUtil.addDay(new Date(), 1), DateUtil.FORMAT_YYYYMMDDHHMISS_14));
        marketingModel.setSelectProductType(SelectorProductTypeEnum.SELECT_SKU.code());
        marketingModel.setActivityType(ActivityTypeEnum.GROUP.code());
        MarketingModel byActivityCode = new MarketingModel();
        byActivityCode.setActivityType(ActivityTypeEnum.GROUP.code());
        when( marketingService.findByActivityCode(Mockito.any())).thenReturn(byActivityCode);

        when(flashSaleOrderService.insert(any())).thenReturn(1);
        when(flashSaleOrderDetailService.insert(any())).thenReturn(1);
        when(flashSaleProductService.dealInventory(any(), any(), any())).thenReturn(1);
        when(marketingService.findByActivityCode(any())).thenReturn(marketingModel);

        when(marketingGroupUserService.insert(any())).thenReturn(1);
//        when(flashSaleOrderService.checkLeaderPayOrder(any(), any(), any(), any())).thenReturn(1);

        flashSaleComponent.commitOrder(orderCommitDTO, list);
    }

    @Test(expected = PromotionException.class)
    public void commitOrder01(){
        FlashSaleCreateOrderParam orderCommitDTO = new FlashSaleCreateOrderParam();
        orderCommitDTO.setPromoDeductedAmount(BigDecimal.ONE);
        List<ShoppingCartStore> cartStoreList = new ArrayList<>();
        ShoppingCartStore shoppingCartStore = new ShoppingCartStore();
        List<ShoppingCartItem> cartItemList = new ArrayList<>();
        ShoppingCartItem item = new ShoppingCartItem();
        item.setSkuCode("1");
        item.setProductCode("1");
        item.setQuantity(1);
        item.setProductPrice(new BigDecimal("123"));
        item.setSelectionFlag("01");
        cartItemList.add(item);
        shoppingCartStore.setCartItemList(cartItemList);
        cartStoreList.add(shoppingCartStore);
        orderCommitDTO.setCartStoreList(cartStoreList);

        List<FlashSaleOrderCalaResult > list = new ArrayList<>();
        FlashSaleOrderCalaResult result = new FlashSaleOrderCalaResult();
        result.setEffectiveFlag(true);
        result.setUserLimitation("1");
        result.setPromoRewardAmount(BigDecimal.ONE);
        List<FlashSaleOrderCalaResult.ShoppingCartItem> items = new ArrayList<>();
        FlashSaleOrderCalaResult.ShoppingCartItem e = new FlashSaleOrderCalaResult.ShoppingCartItem();
        e.setPromoQuantity(1);
        items.add(e);
        result.setShoppingCartItems(items);
        MarketingGroupUserResult marketingGroupUserResult = new MarketingGroupUserResult();
        marketingGroupUserResult.setActivityCode("!");

        Date date = DateUtil.addDay(-1);
        String format = DateUtil.format(date, DateUtil.FORMAT_YYYYMMDDHHMISS_14);

        marketingGroupUserResult.setEffectiveTime(format);

        marketingGroupUserResult.setEffectiveHour(1);
        result.setGroupUser(marketingGroupUserResult);
        list.add(result);
        MarketingModel marketingModel = new MarketingModel();
        marketingModel.setActivityName("1");
        marketingModel.setActivityStatus(ActivityStatusEnum.EFFECTIVE.code());
        marketingModel.setActivityEnd(DateUtil.format(DateUtil.addDay(new Date(), 1), DateUtil.FORMAT_YYYYMMDDHHMISS_14));
//        when(flashSaleOrderService.checkLeaderPayOrder(any(), any(), any(), any())).thenReturn(1);

        flashSaleComponent.commitOrder(orderCommitDTO, list);
    }

    @Test
    public void commitOrder0(){
        FlashSaleCreateOrderParam orderCommitDTO = new FlashSaleCreateOrderParam();
        orderCommitDTO.setPromoDeductedAmount(BigDecimal.ONE);
        List<ShoppingCartStore> cartStoreList = new ArrayList<>();
        ShoppingCartStore shoppingCartStore = new ShoppingCartStore();
        List<ShoppingCartItem> cartItemList = new ArrayList<>();
        ShoppingCartItem item = new ShoppingCartItem();
        item.setSkuCode("1");
        item.setProductCode("1");
        item.setQuantity(1);
        item.setProductPrice(new BigDecimal("123"));
        item.setSelectionFlag("01");
        cartItemList.add(item);
        shoppingCartStore.setCartItemList(cartItemList);
        cartStoreList.add(shoppingCartStore);
        orderCommitDTO.setCartStoreList(cartStoreList);

        List<FlashSaleOrderCalaResult > list = new ArrayList<>();
        FlashSaleOrderCalaResult result = new FlashSaleOrderCalaResult();
        result.setEffectiveFlag(true);
        result.setUserLimitation("1");
        result.setPromoRewardAmount(BigDecimal.ONE);
        List<FlashSaleOrderCalaResult.ShoppingCartItem> items = new ArrayList<>();
        FlashSaleOrderCalaResult.ShoppingCartItem e = new FlashSaleOrderCalaResult.ShoppingCartItem();
        e.setPromoQuantity(1);
        items.add(e);
        result.setShoppingCartItems(items);
        MarketingGroupUserResult marketingGroupUserResult = new MarketingGroupUserResult();
        marketingGroupUserResult.setActivityCode("!");
        marketingGroupUserResult.setEffectiveTime("1");
        marketingGroupUserResult.setEffectiveHour(1);
        marketingGroupUserResult.setGroupSize(3);
        marketingGroupUserResult.setAutoGroupFlag(0);
        marketingGroupUserResult.setTeamLeader("1");
        Date date = DateUtil.addDay(1);
        String format = DateUtil.format(date, DateUtil.FORMAT_YYYYMMDDHHMISS_14);

        marketingGroupUserResult.setEffectiveTime(format);

        result.setGroupUser(marketingGroupUserResult);
        list.add(result);

        MarketingModel marketingModel = new MarketingModel();
        marketingModel.setActivityName("1");
        marketingModel.setActivityStatus(ActivityStatusEnum.EFFECTIVE.code());
        marketingModel.setActivityEnd(DateUtil.format(DateUtil.addDay(new Date(), 1), DateUtil.FORMAT_YYYYMMDDHHMISS_14));
        marketingModel.setSelectProductType(SelectorProductTypeEnum.SELECT_SKU.code());
        marketingModel.setActivityType(ActivityTypeEnum.GROUP.code());
        MarketingModel byActivityCode = new MarketingModel();
        byActivityCode.setActivityType(ActivityTypeEnum.GROUP.code());
        when( marketingService.findByActivityCode(Mockito.any())).thenReturn(byActivityCode);

        when(marketingService.findByActivityCode(any())).thenReturn(marketingModel);
        when(flashSaleOrderService.insert(any())).thenReturn(1);
        when(flashSaleOrderDetailService.insert(any())).thenReturn(1);
        when(flashSaleProductService.dealInventory(any(), any(), any())).thenReturn(1);
        when(marketingService.findByActivityCode(any())).thenReturn(marketingModel);
//        when(redisOpsHelper.lockRedisData(Mockito.anyList(), any(), Mockito.anyLong(), any())).thenReturn(true);
        when(marketingGroupUserService.insert(any())).thenReturn(1);

//        when(flashSaleOrderService.checkLeaderPayOrder(any(), any(), any(), any())).thenReturn(1);
//        when(flashSaleOrderService.checkLeaderPayOrder(any(), any(), any(), any())).thenReturn(1);


        flashSaleComponent.commitOrder(orderCommitDTO, list);
    }


    @Test
    public void commitOrder4(){
        FlashSaleCreateOrderParam orderCommitDTO = new FlashSaleCreateOrderParam();
        orderCommitDTO.setPromoDeductedAmount(BigDecimal.ONE);
        List<ShoppingCartStore> cartStoreList = new ArrayList<>();
        ShoppingCartStore shoppingCartStore = new ShoppingCartStore();
        List<ShoppingCartItem> cartItemList = new ArrayList<>();
        ShoppingCartItem item = new ShoppingCartItem();
        item.setSkuCode("1");
        item.setProductCode("1");
        item.setQuantity(1);
        item.setProductPrice(new BigDecimal("123"));
        item.setSelectionFlag("01");
        cartItemList.add(item);
        shoppingCartStore.setCartItemList(cartItemList);
        cartStoreList.add(shoppingCartStore);
        orderCommitDTO.setCartStoreList(cartStoreList);

        List<FlashSaleOrderCalaResult > list = new ArrayList<>();
        FlashSaleOrderCalaResult result = new FlashSaleOrderCalaResult();
        result.setEffectiveFlag(true);
        result.setUserLimitation("1");
        result.setPromoRewardAmount(BigDecimal.ONE);
        List<FlashSaleOrderCalaResult.ShoppingCartItem> items = new ArrayList<>();
        FlashSaleOrderCalaResult.ShoppingCartItem e = new FlashSaleOrderCalaResult.ShoppingCartItem();
        e.setPromoQuantity(1);
        items.add(e);
        result.setShoppingCartItems(items);
        MarketingGroupUserResult marketingGroupUserResult = new MarketingGroupUserResult();
        marketingGroupUserResult.setActivityCode("!");
        marketingGroupUserResult.setEffectiveTime("1");
        marketingGroupUserResult.setEffectiveHour(1);
        marketingGroupUserResult.setAutoGroupFlag(1);
        marketingGroupUserResult.setGroupSize(1);
        marketingGroupUserResult.setTeamLeader("0");
        Date date = DateUtil.addDay(1);
        String format = DateUtil.format(date, DateUtil.FORMAT_YYYYMMDDHHMISS_14);

        marketingGroupUserResult.setEffectiveTime(format);

        result.setGroupUser(marketingGroupUserResult);
        list.add(result);

        MarketingModel marketingModel = new MarketingModel();
        marketingModel.setActivityName("1");
        marketingModel.setActivityStatus(ActivityStatusEnum.EFFECTIVE.code());
        marketingModel.setActivityEnd(DateUtil.format(DateUtil.addDay(new Date(), 1), DateUtil.FORMAT_YYYYMMDDHHMISS_14));
        marketingModel.setSelectProductType(SelectorProductTypeEnum.SELECT_SKU.code());
        marketingModel.setActivityType(ActivityTypeEnum.GROUP.code());
        MarketingModel byActivityCode = new MarketingModel();
        byActivityCode.setActivityType(ActivityTypeEnum.GROUP.code());
        when( marketingService.findByActivityCode(Mockito.any())).thenReturn(byActivityCode);

        when(flashSaleOrderService.insert(any())).thenReturn(1);
        when(flashSaleOrderDetailService.insert(any())).thenReturn(1);
        when(flashSaleProductService.dealInventory(any(), any(), any())).thenReturn(1);
        when(marketingService.findByActivityCode(any())).thenReturn(marketingModel);
        when(marketingGroupUserService.insert(any())).thenReturn(1);
        when(flashSaleOrderService.checkLeaderPayOrder(any(), any(), any(), any())).thenReturn(1);
        when(marketingGroupUserService.checkGroupProductCode(any(), any(), any(), any(), any())).thenReturn(1);

        when(marketingGroupCodeService.deductGroupInventory(any(), any(), any())).thenReturn(1);


        flashSaleComponent.commitOrder(orderCommitDTO, list);
    }

    @Test
    public void commitOrder(){
        FlashSaleCreateOrderParam orderCommitDTO = new FlashSaleCreateOrderParam();
        orderCommitDTO.setPromoDeductedAmount(BigDecimal.ONE);
        List<ShoppingCartStore> cartStoreList = new ArrayList<>();
        ShoppingCartStore shoppingCartStore = new ShoppingCartStore();
        List<ShoppingCartItem> cartItemList = new ArrayList<>();
        ShoppingCartItem item = new ShoppingCartItem();
        item.setSkuCode("1");
        item.setProductCode("1");
        item.setQuantity(1);
        item.setProductPrice(new BigDecimal("123"));
        item.setSelectionFlag("01");
        cartItemList.add(item);
        shoppingCartStore.setCartItemList(cartItemList);
        cartStoreList.add(shoppingCartStore);
        orderCommitDTO.setCartStoreList(cartStoreList);

        List<FlashSaleOrderCalaResult > list = new ArrayList<>();
        FlashSaleOrderCalaResult result = new FlashSaleOrderCalaResult();
        result.setEffectiveFlag(true);
        result.setUserLimitation("1");
        result.setPromoRewardAmount(BigDecimal.ONE);
        List<FlashSaleOrderCalaResult.ShoppingCartItem> items = new ArrayList<>();
        FlashSaleOrderCalaResult.ShoppingCartItem e = new FlashSaleOrderCalaResult.ShoppingCartItem();
        e.setPromoQuantity(1);
        items.add(e);
        result.setShoppingCartItems(items);
        list.add(result);

        MarketingModel marketingModel = new MarketingModel();
        marketingModel.setActivityName("1");
        marketingModel.setActivityStatus(ActivityStatusEnum.EFFECTIVE.code());
        marketingModel.setActivityEnd(DateUtil.format(DateUtil.addDay(new Date(), 1), DateUtil.FORMAT_YYYYMMDDHHMISS_14));
        marketingModel.setSelectProductType(SelectorProductTypeEnum.SELECT_SKU.code());
        marketingModel.setActivityType(ActivityTypeEnum.FLASH_SALE.code());
        MarketingModel byActivityCode = new MarketingModel();
        byActivityCode.setActivityType(ActivityTypeEnum.FLASH_SALE.code());
        when( marketingService.findByActivityCode(Mockito.any())).thenReturn(byActivityCode);

        when(flashSaleOrderService.insert(any())).thenReturn(1);
        when(flashSaleOrderDetailService.insert(any())).thenReturn(1);
        when(flashSaleProductService.dealInventory(any(), any(), any())).thenReturn(1);
        when(marketingService.findByActivityCode(any())).thenReturn(marketingModel);
        when(redisOpsHelper.lockRedisData(Mockito.anyList(), any(), Mockito.anyLong(), any())).thenReturn(true);
        flashSaleComponent.commitOrder(orderCommitDTO, list);
    }

    @Test(expected = PromotionException.class)
    public void commitOrder1(){
        FlashSaleCreateOrderParam orderCommitDTO = new FlashSaleCreateOrderParam();
        orderCommitDTO.setPromoDeductedAmount(BigDecimal.ONE);
        List<ShoppingCartStore> cartStoreList = new ArrayList<>();
        ShoppingCartStore shoppingCartStore = new ShoppingCartStore();
        List<ShoppingCartItem> cartItemList = new ArrayList<>();
        ShoppingCartItem item = new ShoppingCartItem();
        item.setSkuCode("1");
        item.setProductCode("1");
        item.setQuantity(1);
        item.setProductPrice(new BigDecimal("123"));
        item.setSelectionFlag("01");
        cartItemList.add(item);
        shoppingCartStore.setCartItemList(cartItemList);
        cartStoreList.add(shoppingCartStore);
        orderCommitDTO.setCartStoreList(cartStoreList);
        List<FlashSaleOrderCalaResult > list = new ArrayList<>();
        FlashSaleOrderCalaResult result = new FlashSaleOrderCalaResult();
        result.setEffectiveFlag(false);
        result.setUserLimitation("1");
        result.setPromoRewardAmount(BigDecimal.ONE);
        List<FlashSaleOrderCalaResult.ShoppingCartItem> items = new ArrayList<>();
        FlashSaleOrderCalaResult.ShoppingCartItem e = new FlashSaleOrderCalaResult.ShoppingCartItem();
        e.setPromoQuantity(1);
        items.add(e);
        result.setShoppingCartItems(items);
        list.add(result);
        MarketingModel marketingModel = new MarketingModel();
        marketingModel.setActivityName("1");
        marketingModel.setActivityStatus(ActivityStatusEnum.EFFECTIVE.code());
        marketingModel.setActivityEnd(DateUtil.format(DateUtil.addDay(new Date(), 1), DateUtil.FORMAT_YYYYMMDDHHMISS_14));
        flashSaleComponent.commitOrder(orderCommitDTO, list);
    }

    @Test(expected = Exception.class)
    public void commitOrder2(){
        FlashSaleCreateOrderParam orderCommitDTO = new FlashSaleCreateOrderParam();
        orderCommitDTO.setPromoDeductedAmount(BigDecimal.ZERO);
        List<ShoppingCartStore> cartStoreList = new ArrayList<>();
        ShoppingCartStore shoppingCartStore = new ShoppingCartStore();
        List<ShoppingCartItem> cartItemList = new ArrayList<>();
        ShoppingCartItem item = new ShoppingCartItem();
        item.setSkuCode("1");
        item.setProductCode("1");
        item.setQuantity(1);
        item.setProductPrice(new BigDecimal("123"));
        item.setSelectionFlag("01");
        cartItemList.add(item);
        shoppingCartStore.setCartItemList(cartItemList);
        cartStoreList.add(shoppingCartStore);
        orderCommitDTO.setCartStoreList(cartStoreList);
        List<FlashSaleOrderCalaResult > list = new ArrayList<>();
        FlashSaleOrderCalaResult result = new FlashSaleOrderCalaResult();
        result.setEffectiveFlag(true);
        result.setUserLimitation("1");
        result.setPromoRewardAmount(BigDecimal.ONE);
        List<FlashSaleOrderCalaResult.ShoppingCartItem> items = new ArrayList<>();
        FlashSaleOrderCalaResult.ShoppingCartItem e = new FlashSaleOrderCalaResult.ShoppingCartItem();
        e.setPromoQuantity(1);
        items.add(e);
        result.setShoppingCartItems(items);
        list.add(result);
        MarketingModel marketingModel = new MarketingModel();
        marketingModel.setActivityName("1");
        marketingModel.setActivityStatus(ActivityStatusEnum.EFFECTIVE.code());
        marketingModel.setActivityEnd(DateUtil.format(DateUtil.addDay(new Date(), 1), DateUtil.FORMAT_YYYYMMDDHHMISS_14));
        flashSaleComponent.commitOrder(orderCommitDTO, list);
    }

    @Test(expected = Exception.class)
    public void commitOrder3(){
        FlashSaleCreateOrderParam orderCommitDTO = new FlashSaleCreateOrderParam();
        orderCommitDTO.setPromoDeductedAmount(BigDecimal.ONE);
        List<ShoppingCartStore> cartStoreList = new ArrayList<>();
        ShoppingCartStore shoppingCartStore = new ShoppingCartStore();
        List<ShoppingCartItem> cartItemList = new ArrayList<>();
        ShoppingCartItem item = new ShoppingCartItem();
        item.setSkuCode("1");
        item.setProductCode("1");
        item.setQuantity(1);
        item.setProductPrice(new BigDecimal("123"));
        item.setSelectionFlag("01");
        cartItemList.add(item);
        shoppingCartStore.setCartItemList(cartItemList);
        cartStoreList.add(shoppingCartStore);
        orderCommitDTO.setCartStoreList(cartStoreList);
        List<FlashSaleOrderCalaResult > list = new ArrayList<>();
        FlashSaleOrderCalaResult result = new FlashSaleOrderCalaResult();
        result.setEffectiveFlag(true);
        result.setUserLimitation("1");
        result.setPromoRewardAmount(BigDecimal.ONE);
        List<FlashSaleOrderCalaResult.ShoppingCartItem> items = new ArrayList<>();
        FlashSaleOrderCalaResult.ShoppingCartItem e = new FlashSaleOrderCalaResult.ShoppingCartItem();
        e.setPromoQuantity(1);
        items.add(e);
        result.setShoppingCartItems(items);
        list.add(result);
        MarketingModel marketingModel = new MarketingModel();
        marketingModel.setActivityName("1");
        marketingModel.setActivityStatus(ActivityStatusEnum.EFFECTIVE.code());
        marketingModel.setActivityEnd(DateUtil.format(DateUtil.addDay(new Date(), 1), DateUtil.FORMAT_YYYYMMDDHHMISS_14));
        marketingModel.setSelectProductType(SelectorProductTypeEnum.SELECT_SKU.code());
        marketingModel.setActivityType(ActivityTypeEnum.GROUP.code());
        when(marketingService.findByActivityCode(any())).thenReturn(marketingModel);
        when(flashSaleOrderService.insert(any())).thenReturn(1);
        when(flashSaleOrderDetailService.insert(any())).thenReturn(1);
        when(flashSaleProductService.dealInventory(any(), any(), any())).thenReturn(0);
        flashSaleComponent.commitOrder(orderCommitDTO, list);
    }

    @Test(expected = Exception.class)
    public void commitOrder5(){
        FlashSaleCreateOrderParam orderCommitDTO = new FlashSaleCreateOrderParam();
        orderCommitDTO.setPromoDeductedAmount(BigDecimal.ONE);
        List<ShoppingCartStore> cartStoreList = new ArrayList<>();
        ShoppingCartStore shoppingCartStore = new ShoppingCartStore();
        List<ShoppingCartItem> cartItemList = new ArrayList<>();
        ShoppingCartItem item = new ShoppingCartItem();
        item.setSkuCode("1");
        item.setProductCode("1");
        item.setQuantity(1);
        item.setProductPrice(new BigDecimal("123"));
        item.setSelectionFlag("01");
        cartItemList.add(item);
        shoppingCartStore.setCartItemList(cartItemList);
        cartStoreList.add(shoppingCartStore);
        orderCommitDTO.setCartStoreList(cartStoreList);
        List<FlashSaleOrderCalaResult > list = new ArrayList<>();
        FlashSaleOrderCalaResult result = new FlashSaleOrderCalaResult();
        result.setEffectiveFlag(true);
        result.setUserLimitation("1");
        result.setPromoRewardAmount(BigDecimal.ONE);
        List<FlashSaleOrderCalaResult.ShoppingCartItem> items = new ArrayList<>();
        FlashSaleOrderCalaResult.ShoppingCartItem e = new FlashSaleOrderCalaResult.ShoppingCartItem();
        e.setPromoQuantity(1);
        items.add(e);
        result.setShoppingCartItems(items);
        list.add(result);
        MarketingModel marketingModel = new MarketingModel();
        marketingModel.setActivityName("1");
        marketingModel.setActivityStatus(ActivityStatusEnum.EFFECTIVE.code());
        marketingModel.setActivityEnd(DateUtil.format(DateUtil.addDay(new Date(), 1), DateUtil.FORMAT_YYYYMMDDHHMISS_14));
        marketingModel.setSelectProductType(SelectorProductTypeEnum.SELECT_SKU.code());
        marketingModel.setActivityType(ActivityTypeEnum.FLASH_SALE.code());
        when(flashSaleOrderService.insert(any())).thenReturn(1);
        when(flashSaleOrderDetailService.insert(any())).thenReturn(1);
        when(flashSaleProductService.dealInventory(any(), any(), any())).thenReturn(1);
        when(marketingService.findByActivityCode(any())).thenReturn(marketingModel);
        when(redisOpsHelper.lockRedisData(Mockito.anyList(), any(), Mockito.anyLong(), any())).thenReturn(false);
        flashSaleComponent.commitOrder(orderCommitDTO, list);
    }

    @Test
    public void rollBackMarketingGroupResource(){
        TPromoIncentiveLimitedVO limitedVO = new TPromoIncentiveLimitedVO();
        limitedVO.setActivityCode("activityCode");
        limitedVO.setLimitationCode(LimitationCodeEnum.ACTIVITY_TOTAL_COUNT.code());
        limitedVO.setLimitationValue(BigDecimal.ONE);
        List<TPromoIncentiveLimitedVO> limitedList = Collections.singletonList(limitedVO);

        FlashSaleOrderModel byOrderNo = new FlashSaleOrderModel();

        FlashSaleOrderDetailModel detailModel = new FlashSaleOrderDetailModel();
        MarketingGroupCodeMode marketingGroupCodeMode = new MarketingGroupCodeMode();
        Date date1 = DateUtils.addHours(new Date(), 2);
        String format1 = DateUtil.format(date1,DateUtil.FORMAT_YYYYMMDDHHMISS_14);
        marketingGroupCodeMode.setEffectiveTime(format1);
        marketingGroupCodeMode.setGroupStatus(MarketingGroupStatusEnum.GROUP_NO_START.code());
        when(marketingGroupCodeService.queryGroupByMarketingGroupCode(Mockito.any(), Mockito.any(),Mockito.any())).thenReturn(marketingGroupCodeMode);

        MarketingModel byActivityCode = new MarketingModel();
        byActivityCode.setActivityStatus("04");
        flashSaleComponent.rollBackMarketingGroupResource("1",byOrderNo,detailModel,byActivityCode);
    }

    @Test(expected = NullPointerException.class)
    public void rollBackMarketingGroupResource1(){
        TPromoIncentiveLimitedVO limitedVO = new TPromoIncentiveLimitedVO();
        limitedVO.setActivityCode("activityCode");
        limitedVO.setLimitationCode(LimitationCodeEnum.ACTIVITY_TOTAL_COUNT.code());
        limitedVO.setLimitationValue(BigDecimal.ONE);
        List<TPromoIncentiveLimitedVO> limitedList = Collections.singletonList(limitedVO);
        when(limitedService.getLimitedListByActivityCode(Mockito.any())).thenReturn(limitedList);
        when(redisTemplate.opsHashHasKey(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(true);


        when(redisOpsHelper.buildIncentiveKey(Mockito.anyString(),Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any())).thenReturn("1");


        FlashSaleOrderModel byOrderNo = new FlashSaleOrderModel();
        FlashSaleOrderDetailModel detailModel = new FlashSaleOrderDetailModel();
        MarketingModel byActivityCode = new MarketingModel();
        byActivityCode.setActivityStatus("07");
        flashSaleComponent.rollBackMarketingGroupResource("1",byOrderNo,detailModel,byActivityCode);
    }

    @Test
    public void rollBackMarketingGroupResource22(){
        TPromoIncentiveLimitedVO limitedVO = new TPromoIncentiveLimitedVO();
        limitedVO.setActivityCode("activityCode");
        limitedVO.setLimitationCode(LimitationCodeEnum.ACTIVITY_TOTAL_COUNT.code());
        limitedVO.setLimitationValue(BigDecimal.ONE);
        List<TPromoIncentiveLimitedVO> limitedList = Collections.singletonList(limitedVO);
        //when(limitedService.getLimitedListByActivityCode(Mockito.any())).thenReturn(limitedList);
        when(redisTemplate.opsHashHasKey(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(false);
        FlashSaleOrderModel byOrderNo = new FlashSaleOrderModel();
        FlashSaleOrderDetailModel detailModel = new FlashSaleOrderDetailModel();

        MarketingGroupCodeMode marketingGroupCodeMode = new MarketingGroupCodeMode();
        Date date1 = DateUtils.addHours(new Date(), 2);
        String format1 = DateUtil.format(date1,DateUtil.FORMAT_YYYYMMDDHHMISS_14);
        marketingGroupCodeMode.setEffectiveTime(format1);
        marketingGroupCodeMode.setGroupStatus(MarketingGroupStatusEnum.GROUP_NO_START.code());
        when(marketingGroupCodeService.queryGroupByMarketingGroupCode(Mockito.any(), Mockito.any(),Mockito.any())).thenReturn(marketingGroupCodeMode);

        MarketingGroupMode marketingGroupMode = new MarketingGroupMode();
        marketingGroupMode.setAutoGroupFlag(1);
        when(marketingGroupService.findMarketingGroupByActivityCode(Mockito.any(), Mockito.any())).thenReturn(marketingGroupMode);


        MarketingModel byActivityCode = new MarketingModel();
        byActivityCode.setActivityStatus("07");

        flashSaleComponent.rollBackMarketingGroupResource("1",byOrderNo,detailModel,byActivityCode);
    }

    @Test
    public void rollBackMarketingGroupResource223(){
        TPromoIncentiveLimitedVO limitedVO = new TPromoIncentiveLimitedVO();
        limitedVO.setActivityCode("activityCode");
        limitedVO.setLimitationCode(LimitationCodeEnum.ACTIVITY_TOTAL_COUNT.code());
        limitedVO.setLimitationValue(BigDecimal.ONE);
        List<TPromoIncentiveLimitedVO> limitedList = Collections.singletonList(limitedVO);
        //when(limitedService.getLimitedListByActivityCode(Mockito.any())).thenReturn(limitedList);
        when(redisTemplate.opsHashHasKey(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(false);
        FlashSaleOrderModel byOrderNo = new FlashSaleOrderModel();
        FlashSaleOrderDetailModel detailModel = new FlashSaleOrderDetailModel();

        MarketingGroupCodeMode marketingGroupCodeMode = new MarketingGroupCodeMode();
        Date date1 = DateUtils.addHours(new Date(), -2);
        String format1 = DateUtil.format(date1,DateUtil.FORMAT_YYYYMMDDHHMISS_14);
        marketingGroupCodeMode.setEffectiveTime(format1);
        marketingGroupCodeMode.setGroupStatus(MarketingGroupStatusEnum.GROUP_NO_START.code());
        when(marketingGroupCodeService.queryGroupByMarketingGroupCode(Mockito.any(), Mockito.any(),Mockito.any())).thenReturn(marketingGroupCodeMode);

        MarketingGroupMode marketingGroupMode = new MarketingGroupMode();
        marketingGroupMode.setAutoGroupFlag(1);
        when(marketingGroupService.findMarketingGroupByActivityCode(Mockito.any(), Mockito.any())).thenReturn(marketingGroupMode);


        MarketingModel byActivityCode = new MarketingModel();
        byActivityCode.setActivityStatus("04");

        flashSaleComponent.rollBackMarketingGroupResource("1",byOrderNo,detailModel,byActivityCode);
    }




    @Test
    public void rollBackMarketingGroupResource224(){
        TPromoIncentiveLimitedVO limitedVO = new TPromoIncentiveLimitedVO();
        limitedVO.setActivityCode("activityCode");
        limitedVO.setLimitationCode(LimitationCodeEnum.ACTIVITY_TOTAL_COUNT.code());
        limitedVO.setLimitationValue(BigDecimal.ONE);
        List<TPromoIncentiveLimitedVO> limitedList = Collections.singletonList(limitedVO);
        //when(limitedService.getLimitedListByActivityCode(Mockito.any())).thenReturn(limitedList);
        when(redisTemplate.opsHashHasKey(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(false);
        FlashSaleOrderModel byOrderNo = new FlashSaleOrderModel();
        FlashSaleOrderDetailModel detailModel = new FlashSaleOrderDetailModel();

        MarketingGroupCodeMode marketingGroupCodeMode = new MarketingGroupCodeMode();
        Date date1 = DateUtils.addHours(new Date(), -2);
        String format1 = DateUtil.format(date1,DateUtil.FORMAT_YYYYMMDDHHMISS_14);
        marketingGroupCodeMode.setEffectiveTime(format1);
        marketingGroupCodeMode.setGroupStatus(MarketingGroupStatusEnum.GROUP_NO_START.code());
        when(marketingGroupCodeService.queryGroupByMarketingGroupCode(Mockito.any(), Mockito.any(),Mockito.any())).thenReturn(marketingGroupCodeMode);

        MarketingGroupMode marketingGroupMode = new MarketingGroupMode();
        marketingGroupMode.setAutoGroupFlag(1);
        when(marketingGroupService.findMarketingGroupByActivityCode(Mockito.any(), Mockito.any())).thenReturn(marketingGroupMode);
        when(marketingGroupUserService.queryGroupMemberNoPay(Mockito.any(), Mockito.any(),Mockito.any())).thenReturn(1);

        MarketingModel byActivityCode = new MarketingModel();
        byActivityCode.setActivityStatus("04");

        flashSaleComponent.rollBackMarketingGroupResource("1",byOrderNo,detailModel,byActivityCode);
    }


    @Test
    public void testRollBackMarketingResource() {
        // Arrange
        String tenantCode = "T001";
        FlashSaleOrderModel byOrderNo = new FlashSaleOrderModel();
        byOrderNo.setActivityCode("ACT001");
        FlashSaleOrderDetailModel detailModel = new FlashSaleOrderDetailModel();
        detailModel.setSkuCode("SKU001");
        detailModel.setSkuQuality(1);

        String key1 = "someKey1";
        String userLimitKey1 = "someUserLimitKey1";

        when(redisOpsHelper.buildIncentiveKey(
                eq(LimitationCodeEnum.USER_TOTAL_COUNT_FLASH_SALE.code()),
                eq(tenantCode),
                anyString(),
                anyString(),
                anyString()
        )).thenReturn(key1);

        when(redisOpsHelper.buildIncentiveKey(
                eq(LimitationCodeEnum.USER_TOTAL_COUNT_FLASH_SALE.code()),
                eq(tenantCode),
                eq(byOrderNo.getActivityCode() + "-" + detailModel.getSkuCode()),
                eq(byOrderNo.getMemberCode()),
                eq(detailModel.getSkuCode())
        )).thenReturn(userLimitKey1);

        when(redisTemplate.opsHashHasKey(anyString(), anyString(), anyString())).thenReturn(true);

        // Act
        flashSaleComponent.rollBackMarketingResource(tenantCode, byOrderNo, detailModel);


    }

    @Test
    public void confirmOrder_null(){
        String tenantCode = "1";
        String orderNo = "1";

        when(flashSaleOrderService.findByOrderNo(Mockito.anyString(), Mockito.anyString())).thenReturn(null);

        int i = flashSaleComponent.confirmOrder(tenantCode, orderNo);
        Assert.assertEquals(1, i);
    }

    @Test
    public void confirmOrder_status(){
        String tenantCode = "1";
        String orderNo = "1";

        FlashSaleOrderModel byOrderNo = new FlashSaleOrderModel();
        byOrderNo.setOrderStatus(OrderStatusEnum.PAID.code());
        when(flashSaleOrderService.findByOrderNo(Mockito.anyString(), Mockito.anyString())).thenReturn(byOrderNo);

        int i = flashSaleComponent.confirmOrder(tenantCode, orderNo);
        Assert.assertEquals(1, i);
    }

    @Test
    public void confirmOrder(){
        String tenantCode = "1";
        String orderNo = "1";

        FlashSaleOrderModel byOrderNo = new FlashSaleOrderModel();
        byOrderNo.setOrderStatus(OrderStatusEnum.UNPAID.code());
        MarketingModel byActivityCode = new MarketingModel();
        byActivityCode.setActivityType(ActivityTypeEnum.GROUP.code());
        when( marketingService.findByActivityCode(Mockito.any())).thenReturn(byActivityCode);

        when(flashSaleOrderService.findByOrderNo(Mockito.anyString(), Mockito.anyString())).thenReturn(byOrderNo);
        when(flashSaleOrderService.updateStatus(Mockito.anyString(), Mockito.anyString(), Mockito.anyString())).thenReturn(1);

        int i = flashSaleComponent.confirmOrder(tenantCode, orderNo);
        Assert.assertEquals(1, i);
    }

    @Test
    public void confirmOrderGroup2(){

        String tenantCode = "1";
        String orderNo = "1";

        FlashSaleOrderModel byOrderNo = new FlashSaleOrderModel();
        byOrderNo.setOrderStatus(OrderStatusEnum.UNPAID.code());
        byOrderNo.setMarketingGroupCode("");
        MarketingModel byActivityCode = new MarketingModel();
        byActivityCode.setActivityType(ActivityTypeEnum.GROUP.code());
        when( marketingService.findByActivityCode(Mockito.any())).thenReturn(byActivityCode);
        when(flashSaleOrderService.findByOrderNo(Mockito.anyString(), Mockito.anyString())).thenReturn(byOrderNo);
        when(flashSaleOrderService.updateStatus(Mockito.anyString(), Mockito.anyString(), Mockito.anyString())).thenReturn(1);

        MarketingGroupMode groupMode = new MarketingGroupMode();
        groupMode.setGroupSize(2);

        int i = flashSaleComponent.confirmOrder(tenantCode, orderNo);
        Assert.assertEquals(1, i);
    }

    @Test
    public void confirmOrderGroup(){

        String tenantCode = "1";
        String orderNo = "1";

        FlashSaleOrderModel byOrderNo = new FlashSaleOrderModel();
        byOrderNo.setOrderStatus(OrderStatusEnum.UNPAID.code());
        byOrderNo.setMarketingGroupCode("111");
        byOrderNo.setMemberCode("111");
        when(flashSaleOrderService.findByOrderNo(Mockito.anyString(), Mockito.anyString())).thenReturn(byOrderNo);
        when(flashSaleOrderService.updateStatus(Mockito.anyString(), Mockito.anyString(), Mockito.anyString())).thenReturn(1);

        MarketingGroupMode groupMode = new MarketingGroupMode();
        groupMode.setGroupSize(2);
        groupMode.setEffectiveHour(1);
        groupMode.setAutoGroupFlag(0);
        when(marketingGroupService.findMarketingGroupByActivityCode(Mockito.any(), Mockito.any())).thenReturn(groupMode);

        Date date = DateUtils.addHours(new Date(), 2);
        String format = DateUtil.format(date,DateUtil.FORMAT_YYYYMMDDHHMISS_14);

        MarketingModel byActivityCode = new MarketingModel();
        byActivityCode.setActivityEnd(format);
        byActivityCode.setActivityType(ActivityTypeEnum.GROUP.code());
        when(marketingService.findByActivityCode(Mockito.any())).thenReturn(byActivityCode);

//        when(marketingGroupUserService.queryGroupUserListByMarketingGroupCode(Mockito.anyString(), Mockito.anyString(),Mockito.anyString())).thenReturn(2);
        MarketingGroupUserMode userMode =new MarketingGroupUserMode();
        userMode.setEffectiveHour(1);
        userMode.setUserCode("111");
        when(marketingGroupUserService.confirmGroupLeaderByMarketingGroupCode(Mockito.anyString(), Mockito.anyString())).thenReturn(userMode);
        GroupUserContent groupUserContent = new GroupUserContent();
//        when(marketingGroupComponent.getGroupUserContent(Mockito.any(), Mockito.any(),Mockito.any())).thenReturn(groupUserContent);

        MarketingGroupCodeMode marketingGroupCodeMode = new MarketingGroupCodeMode();
        Date date1 = DateUtils.addHours(new Date(), 2);
        String format1 = DateUtil.format(date1,DateUtil.FORMAT_YYYYMMDDHHMISS_14);
        marketingGroupCodeMode.setEffectiveTime(format1);
        marketingGroupCodeMode.setGroupStatus(MarketingGroupStatusEnum.GROUP_NO_START.code());
        when(marketingGroupCodeService.queryGroupByMarketingGroupCode(Mockito.any(), Mockito.any(),Mockito.any())).thenReturn(marketingGroupCodeMode);

        int i = flashSaleComponent.confirmOrder(tenantCode, orderNo);
        Assert.assertEquals(1, i);
    }

    @Test
    public void confirmOrderGroup1_not(){

        String tenantCode = "1";
        String orderNo = "1";

        FlashSaleOrderModel byOrderNo = new FlashSaleOrderModel();
        byOrderNo.setOrderStatus(OrderStatusEnum.UNPAID.code());
        byOrderNo.setMarketingGroupCode("111");
        byOrderNo.setMemberCode("111");
        when(flashSaleOrderService.findByOrderNo(Mockito.anyString(), Mockito.anyString())).thenReturn(byOrderNo);
        when(flashSaleOrderService.updateStatus(Mockito.anyString(), Mockito.anyString(), Mockito.anyString())).thenReturn(1);

        MarketingGroupMode groupMode = new MarketingGroupMode();
        groupMode.setGroupSize(1);

        groupMode.setEffectiveHour(2);
        groupMode.setAutoGroupFlag(1);

        Date date = DateUtils.addHours(new Date(), 1);
        String format = DateUtil.format(date,DateUtil.FORMAT_YYYYMMDDHHMISS_14);

        MarketingModel byActivityCode = new MarketingModel();
        byActivityCode.setActivityEnd(format);
        byActivityCode.setActivityType(ActivityTypeEnum.GROUP.code());
        when(marketingService.findByActivityCode(Mockito.any())).thenReturn(byActivityCode);

        when(marketingGroupService.findMarketingGroupByActivityCode(Mockito.any(), Mockito.any())).thenReturn(groupMode);

//        when(marketingGroupUserService.queryGroupUserListByMarketingGroupCode(Mockito.anyString(), Mockito.anyString(),Mockito.anyString())).thenReturn(2);
        MarketingGroupUserMode userMode =new MarketingGroupUserMode();
        userMode.setEffectiveHour(1);
        userMode.setUserCode("111");
        when(marketingGroupUserService.confirmGroupLeaderByMarketingGroupCode(Mockito.anyString(), Mockito.anyString())).thenReturn(userMode);

        MarketingGroupCodeMode marketingGroupCodeMode = new MarketingGroupCodeMode();
        Date date1 = DateUtils.addHours(new Date(), 2);
        String format1 = DateUtil.format(date1,DateUtil.FORMAT_YYYYMMDDHHMISS_14);
        marketingGroupCodeMode.setEffectiveTime(format1);
        marketingGroupCodeMode.setGroupStatus(MarketingGroupStatusEnum.GROUP_NO_START.code());
        when(marketingGroupCodeService.queryGroupByMarketingGroupCode(Mockito.any(), Mockito.any(),Mockito.any())).thenReturn(marketingGroupCodeMode);


        int i = flashSaleComponent.confirmOrder(tenantCode, orderNo);
        Assert.assertEquals(1, i);
    }

    @Test
    public void confirmOrderGroup1(){

        String tenantCode = "1";
        String orderNo = "1";

        FlashSaleOrderModel byOrderNo = new FlashSaleOrderModel();
        byOrderNo.setOrderStatus(OrderStatusEnum.UNPAID.code());
        byOrderNo.setMarketingGroupCode("111");
        byOrderNo.setMemberCode("111");
        when(flashSaleOrderService.findByOrderNo(Mockito.anyString(), Mockito.anyString())).thenReturn(byOrderNo);
        when(flashSaleOrderService.updateStatus(Mockito.anyString(), Mockito.anyString(), Mockito.anyString())).thenReturn(1);

        MarketingGroupMode groupMode = new MarketingGroupMode();
        groupMode.setGroupSize(1);

        groupMode.setEffectiveHour(2);
        groupMode.setAutoGroupFlag(0);

        Date date = DateUtils.addHours(new Date(), 1);
        String format = DateUtil.format(date,DateUtil.FORMAT_YYYYMMDDHHMISS_14);

        MarketingModel byActivityCode = new MarketingModel();
        byActivityCode.setActivityEnd(format);
        byActivityCode.setActivityType(ActivityTypeEnum.GROUP.code());


        when(marketingService.findByActivityCode(Mockito.any())).thenReturn(byActivityCode);

        when(marketingGroupService.findMarketingGroupByActivityCode(Mockito.any(), Mockito.any())).thenReturn(groupMode);

//        when(marketingGroupUserService.queryGroupUserListByMarketingGroupCode(Mockito.anyString(), Mockito.anyString(),Mockito.anyString())).thenReturn(2);
        MarketingGroupUserMode userMode =new MarketingGroupUserMode();
        userMode.setEffectiveHour(1);
        userMode.setUserCode("111");
        when(marketingGroupUserService.confirmGroupLeaderByMarketingGroupCode(Mockito.anyString(), Mockito.anyString())).thenReturn(userMode);

        MarketingGroupCodeMode marketingGroupCodeMode = new MarketingGroupCodeMode();
        Date date1 = DateUtils.addHours(new Date(), 2);
        String format1 = DateUtil.format(date1,DateUtil.FORMAT_YYYYMMDDHHMISS_14);
        marketingGroupCodeMode.setEffectiveTime(format1);
        marketingGroupCodeMode.setGroupStatus(MarketingGroupStatusEnum.GROUP_NO_START.code());
        when(marketingGroupCodeService.queryGroupByMarketingGroupCode(Mockito.any(), Mockito.any(),Mockito.any())).thenReturn(marketingGroupCodeMode);


        int i = flashSaleComponent.confirmOrder(tenantCode, orderNo);
        Assert.assertEquals(1, i);
    }


    @Test
    public void confirmOrderGroup111(){

        String tenantCode = "1";
        String orderNo = "1";

        FlashSaleOrderModel byOrderNo = new FlashSaleOrderModel();
        byOrderNo.setOrderStatus(OrderStatusEnum.UNPAID.code());
        byOrderNo.setMarketingGroupCode("111");
        byOrderNo.setMemberCode("111");
        when(flashSaleOrderService.findByOrderNo(Mockito.anyString(), Mockito.anyString())).thenReturn(byOrderNo);
        when(flashSaleOrderService.updateStatus(Mockito.anyString(), Mockito.anyString(), Mockito.anyString())).thenReturn(1);

        MarketingGroupMode groupMode = new MarketingGroupMode();
        groupMode.setGroupSize(1);

        groupMode.setEffectiveHour(2);
        groupMode.setAutoGroupFlag(0);

        Date date = DateUtils.addHours(new Date(), 1);
        String format = DateUtil.format(date,DateUtil.FORMAT_YYYYMMDDHHMISS_14);

        MarketingModel byActivityCode = new MarketingModel();
        byActivityCode.setActivityEnd(format);
        byActivityCode.setActivityType(ActivityTypeEnum.GROUP.code());
        when(marketingService.findByActivityCode(Mockito.any())).thenReturn(byActivityCode);

        when(marketingGroupService.findMarketingGroupByActivityCode(Mockito.any(), Mockito.any())).thenReturn(groupMode);

//        when(marketingGroupUserService.queryGroupUserListByMarketingGroupCode(Mockito.anyString(), Mockito.anyString(),Mockito.anyString())).thenReturn(2);
        MarketingGroupUserMode userMode =new MarketingGroupUserMode();
        userMode.setEffectiveHour(1);
        userMode.setUserCode("111");
        when(marketingGroupUserService.confirmGroupLeaderByMarketingGroupCode(Mockito.anyString(), Mockito.anyString())).thenReturn(userMode);

        MarketingGroupCodeMode marketingGroupCodeMode = new MarketingGroupCodeMode();
        Date date1 = DateUtils.addHours(new Date(), -1);
        String format1 = DateUtil.format(date1,DateUtil.FORMAT_YYYYMMDDHHMISS_14);
        marketingGroupCodeMode.setEffectiveTime(format1);
        marketingGroupCodeMode.setGroupStatus(MarketingGroupStatusEnum.GROUP_NO_START.code());
        marketingGroupCodeMode.setInventory(0);
        when(marketingGroupCodeService.queryGroupByMarketingGroupCode(Mockito.any(), Mockito.any(),Mockito.any())).thenReturn(marketingGroupCodeMode);


        int i = flashSaleComponent.confirmOrder(tenantCode, orderNo);
        Assert.assertEquals(1, i);
    }

    @Test
    public void confirmOrderGroup12_member(){

        String tenantCode = "1";
        String orderNo = "1";

        FlashSaleOrderModel byOrderNo = new FlashSaleOrderModel();
        byOrderNo.setOrderStatus(OrderStatusEnum.UNPAID.code());
        byOrderNo.setMarketingGroupCode("111");
        byOrderNo.setMemberCode("1121");
        when(flashSaleOrderService.findByOrderNo(Mockito.anyString(), Mockito.anyString())).thenReturn(byOrderNo);
        when(flashSaleOrderService.updateStatus(Mockito.anyString(), Mockito.anyString(), Mockito.anyString())).thenReturn(1);

        MarketingGroupMode groupMode = new MarketingGroupMode();
        groupMode.setGroupSize(1);

        groupMode.setEffectiveHour(2);
        groupMode.setAutoGroupFlag(1);

        Date date = DateUtils.addHours(new Date(), 1);
        String format = DateUtil.format(date,DateUtil.FORMAT_YYYYMMDDHHMISS_14);

        MarketingModel byActivityCode = new MarketingModel();
        byActivityCode.setActivityEnd(format);
        byActivityCode.setActivityType(ActivityTypeEnum.GROUP.code());
        byActivityCode.setActivityStatus("04");

        when(marketingService.findByActivityCode(Mockito.any())).thenReturn(byActivityCode);

        when(marketingGroupService.findMarketingGroupByActivityCode(Mockito.any(), Mockito.any())).thenReturn(groupMode);
        MarketingGroupUserMode userMode =new MarketingGroupUserMode();
        userMode.setEffectiveHour(1);
        userMode.setUserCode("111");

        MarketingGroupCodeMode marketingGroupCodeMode = new MarketingGroupCodeMode();
        marketingGroupCodeMode.setInventory(0);

        marketingGroupCodeMode.setEffectiveTime(format);
        when(marketingGroupCodeService.queryGroupByMarketingGroupCode(Mockito.any(), Mockito.any(),Mockito.any())).thenReturn(marketingGroupCodeMode);

        when(marketingGroupUserService.confirmGroupLeaderByMarketingGroupCode(Mockito.any(), Mockito.any())).thenReturn(userMode);

        int i = flashSaleComponent.confirmOrder(tenantCode, orderNo);
        Assert.assertEquals(1, i);
    }

    @Test
    public void confirmOrderGroup3_member(){

        String tenantCode = "1";
        String orderNo = "1";

        FlashSaleOrderModel byOrderNo = new FlashSaleOrderModel();
        byOrderNo.setOrderStatus(OrderStatusEnum.UNPAID.code());
        byOrderNo.setMarketingGroupCode("111");
        when(flashSaleOrderService.findByOrderNo(Mockito.anyString(), Mockito.anyString())).thenReturn(byOrderNo);
        when(flashSaleOrderService.updateStatus(Mockito.anyString(), Mockito.anyString(), Mockito.anyString())).thenReturn(1);

        MarketingGroupMode groupMode = new MarketingGroupMode();
        groupMode.setGroupSize(1);
        groupMode.setAutoGroupFlag(1);
        when(marketingGroupService.findMarketingGroupByActivityCode(Mockito.any(), Mockito.any())).thenReturn(groupMode);

        MarketingGroupUserMode leader = new MarketingGroupUserMode();

        leader.setUserCode("!1");
        when(marketingGroupUserService.confirmGroupLeaderByMarketingGroupCode(Mockito.anyString(), Mockito.anyString())).thenReturn(leader);

//        when(redisClient.getString(Mockito.anyString())).thenReturn("0");

        when(marketingGroupUserService.queryGroupMemberNoPay(Mockito.any(), Mockito.any(),Mockito.any())).thenReturn(0);
        MarketingGroupCodeMode marketingGroupCodeMode = new MarketingGroupCodeMode();
        marketingGroupCodeMode.setInventory(0);
        Date date1 = DateUtils.addHours(new Date(), -1);
        String effective = DateUtil.format(date1,DateUtil.FORMAT_YYYYMMDDHHMISS_14);
        marketingGroupCodeMode.setEffectiveTime(effective);

        when(marketingGroupCodeService.queryGroupByMarketingGroupCode(Mockito.any(), Mockito.any(),Mockito.any())).thenReturn(marketingGroupCodeMode);

        Date date = DateUtils.addHours(new Date(), 1);
        String format = DateUtil.format(date,DateUtil.FORMAT_YYYYMMDDHHMISS_14);
        MarketingModel byActivityCode = new MarketingModel();
        byActivityCode.setActivityEnd(format);
        byActivityCode.setActivityType(ActivityTypeEnum.GROUP.code());
        byActivityCode.setActivityStatus("04");

        when(marketingService.findByActivityCode(Mockito.any())).thenReturn(byActivityCode);

        int i = flashSaleComponent.confirmOrder(tenantCode, orderNo);
        Assert.assertEquals(1, i);
    }



    @Test
    public void confirmOrderGroup3_member5(){

        String tenantCode = "1";
        String orderNo = "1";

        FlashSaleOrderModel byOrderNo = new FlashSaleOrderModel();
        byOrderNo.setOrderStatus(OrderStatusEnum.UNPAID.code());
        byOrderNo.setMarketingGroupCode("111");
        MarketingModel byActivityCode = new MarketingModel();
        byActivityCode.setActivityType(ActivityTypeEnum.GROUP.code());
        when( marketingService.findByActivityCode(Mockito.any())).thenReturn(byActivityCode);

        when(flashSaleOrderService.findByOrderNo(Mockito.anyString(), Mockito.anyString())).thenReturn(byOrderNo);
        when(flashSaleOrderService.updateStatus(Mockito.anyString(), Mockito.anyString(), Mockito.anyString())).thenReturn(1);

        MarketingGroupMode groupMode = new MarketingGroupMode();
        groupMode.setGroupSize(1);
        groupMode.setAutoGroupFlag(1);
        when(marketingGroupService.findMarketingGroupByActivityCode(Mockito.any(), Mockito.any())).thenReturn(groupMode);

        MarketingGroupUserMode leader = new MarketingGroupUserMode();

        leader.setUserCode("!1");
        when(marketingGroupUserService.confirmGroupLeaderByMarketingGroupCode(Mockito.anyString(), Mockito.anyString())).thenReturn(leader);

//        when(redisClient.getString(Mockito.anyString())).thenReturn("0");

        GroupUserContent groupUserContent = new GroupUserContent();
//        when(marketingGroupComponent.getGroupUserContent(Mockito.any(), Mockito.any(),Mockito.any())).thenReturn(groupUserContent);
        when(marketingGroupUserService.queryGroupMemberNoPay(Mockito.any(), Mockito.any(),Mockito.any())).thenReturn(0);
        MarketingGroupCodeMode marketingGroupCodeMode = new MarketingGroupCodeMode();
        marketingGroupCodeMode.setInventory(0);
        Date date1 = DateUtils.addHours(new Date(), -1);
        String effective = DateUtil.format(date1,DateUtil.FORMAT_YYYYMMDDHHMISS_14);
        marketingGroupCodeMode.setEffectiveTime(effective);

        when(marketingGroupCodeService.queryGroupByMarketingGroupCode(Mockito.any(), Mockito.any(),Mockito.any())).thenReturn(marketingGroupCodeMode);

        Date date = DateUtils.addHours(new Date(), 1);
        String format = DateUtil.format(date,DateUtil.FORMAT_YYYYMMDDHHMISS_14);
        byActivityCode.setActivityEnd(format);

        byActivityCode.setActivityStatus("04");

        when(marketingService.findByActivityCode(Mockito.any())).thenReturn(byActivityCode);

        when(marketingGroupUserService.queryGroupMemberNoPay(Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(2);


        int i = flashSaleComponent.confirmOrder(tenantCode, orderNo);
        Assert.assertEquals(1, i);
    }

    @Test
    public void confirmOrderGroup3_member56(){

        String tenantCode = "1";
        String orderNo = "1";

        FlashSaleOrderModel byOrderNo = new FlashSaleOrderModel();
        byOrderNo.setOrderStatus(OrderStatusEnum.UNPAID.code());
        byOrderNo.setMarketingGroupCode("111");
        when(flashSaleOrderService.findByOrderNo(Mockito.anyString(), Mockito.anyString())).thenReturn(byOrderNo);
        when(flashSaleOrderService.updateStatus(Mockito.anyString(), Mockito.anyString(), Mockito.anyString())).thenReturn(1);

        MarketingGroupMode groupMode = new MarketingGroupMode();
        groupMode.setGroupSize(1);
        groupMode.setAutoGroupFlag(0);
        when(marketingGroupService.findMarketingGroupByActivityCode(Mockito.any(), Mockito.any())).thenReturn(groupMode);

        MarketingGroupUserMode leader = new MarketingGroupUserMode();

        leader.setUserCode("!1");
        when(marketingGroupUserService.confirmGroupLeaderByMarketingGroupCode(Mockito.anyString(), Mockito.anyString())).thenReturn(leader);

        GroupUserContent groupUserContent = new GroupUserContent();
//        when(marketingGroupComponent.getGroupUserContent(Mockito.any(), Mockito.any(),Mockito.any())).thenReturn(groupUserContent);
//        when(marketingGroupUserService.queryGroupMemberNoPay(Mockito.any(), Mockito.any(),Mockito.any())).thenReturn(0);
        MarketingGroupCodeMode marketingGroupCodeMode = new MarketingGroupCodeMode();
        marketingGroupCodeMode.setInventory(0);
        Date date1 = DateUtils.addHours(new Date(), -1);
        String effective = DateUtil.format(date1,DateUtil.FORMAT_YYYYMMDDHHMISS_14);
        marketingGroupCodeMode.setEffectiveTime(effective);

        when(marketingGroupCodeService.queryGroupByMarketingGroupCode(Mockito.any(), Mockito.any(),Mockito.any())).thenReturn(marketingGroupCodeMode);

        Date date = DateUtils.addHours(new Date(), 1);
        String format = DateUtil.format(date,DateUtil.FORMAT_YYYYMMDDHHMISS_14);
        MarketingModel byActivityCode = new MarketingModel();
        byActivityCode.setActivityEnd(format);
        byActivityCode.setActivityType(ActivityTypeEnum.GROUP.code());
        byActivityCode.setActivityStatus("04");

        when(marketingService.findByActivityCode(Mockito.any())).thenReturn(byActivityCode);

//        when(marketingGroupUserService.queryGroupMemberNoPay(Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(2);


        int i = flashSaleComponent.confirmOrder(tenantCode, orderNo);
        Assert.assertEquals(1, i);
    }


    @Test
    public void confirmOrderGroup3_member_07(){

        String tenantCode = "1";
        String orderNo = "1";

        FlashSaleOrderModel byOrderNo = new FlashSaleOrderModel();
        byOrderNo.setOrderStatus(OrderStatusEnum.UNPAID.code());
        byOrderNo.setMarketingGroupCode("111");
        when(flashSaleOrderService.findByOrderNo(Mockito.anyString(), Mockito.anyString())).thenReturn(byOrderNo);
        when(flashSaleOrderService.updateStatus(Mockito.anyString(), Mockito.anyString(), Mockito.anyString())).thenReturn(1);

        MarketingGroupMode groupMode = new MarketingGroupMode();
        groupMode.setGroupSize(1);
        groupMode.setAutoGroupFlag(1);
        when(marketingGroupService.findMarketingGroupByActivityCode(Mockito.any(), Mockito.any())).thenReturn(groupMode);

        MarketingGroupUserMode leader = new MarketingGroupUserMode();

        leader.setUserCode("!1");
        when(marketingGroupUserService.confirmGroupLeaderByMarketingGroupCode(Mockito.anyString(), Mockito.anyString())).thenReturn(leader);

//        when(redisClient.getString(Mockito.anyString())).thenReturn("0");

        when(marketingGroupUserService.queryGroupMemberNoPay(Mockito.any(), Mockito.any(),Mockito.any())).thenReturn(0);

        MarketingGroupCodeMode marketingGroupCodeMode = new MarketingGroupCodeMode();
        marketingGroupCodeMode.setInventory(0);
        marketingGroupCodeMode.setGroupStatus(MarketingGroupStatusEnum.GROUP_PROCESSING.code());

        when(marketingGroupCodeService.queryGroupByMarketingGroupCode(Mockito.any(), Mockito.any(),Mockito.any())).thenReturn(marketingGroupCodeMode);

        Date date = DateUtils.addHours(new Date(), 1);
        String format = DateUtil.format(date,DateUtil.FORMAT_YYYYMMDDHHMISS_14);
        MarketingModel byActivityCode = new MarketingModel();
        byActivityCode.setActivityEnd(format);
        byActivityCode.setActivityType(ActivityTypeEnum.GROUP.code());
        byActivityCode.setActivityStatus("07");

        when(marketingService.findByActivityCode(Mockito.any())).thenReturn(byActivityCode);

        int i = flashSaleComponent.confirmOrder(tenantCode, orderNo);
        Assert.assertEquals(1, i);
    }


    @Test
    public void confirmOrderGroup3_member_07_1(){

        String tenantCode = "1";
        String orderNo = "1";

        FlashSaleOrderModel byOrderNo = new FlashSaleOrderModel();
        byOrderNo.setOrderStatus(OrderStatusEnum.UNPAID.code());
        byOrderNo.setMarketingGroupCode("111");
        when(flashSaleOrderService.findByOrderNo(Mockito.anyString(), Mockito.anyString())).thenReturn(byOrderNo);
        when(flashSaleOrderService.updateStatus(Mockito.anyString(), Mockito.anyString(), Mockito.anyString())).thenReturn(1);

        MarketingGroupMode groupMode = new MarketingGroupMode();
        groupMode.setGroupSize(1);
        groupMode.setAutoGroupFlag(1);
        when(marketingGroupService.findMarketingGroupByActivityCode(Mockito.any(), Mockito.any())).thenReturn(groupMode);

        MarketingGroupUserMode leader = new MarketingGroupUserMode();

        leader.setUserCode("!1");
        when(marketingGroupUserService.confirmGroupLeaderByMarketingGroupCode(Mockito.anyString(), Mockito.anyString())).thenReturn(leader);

//        when(redisClient.getString(Mockito.anyString())).thenReturn("0");

        GroupUserContent groupUserContent = new GroupUserContent();
//        when(marketingGroupComponent.getGroupUserContent(Mockito.any(), Mockito.any(),Mockito.any())).thenReturn(groupUserContent);
        when(marketingGroupUserService.queryGroupMemberNoPay(Mockito.any(), Mockito.any(),Mockito.any())).thenReturn(0);

        MarketingGroupCodeMode marketingGroupCodeMode = new MarketingGroupCodeMode();
        marketingGroupCodeMode.setInventory(0);
        marketingGroupCodeMode.setGroupStatus(MarketingGroupStatusEnum.GROUP_PROCESSING.code());

        when(marketingGroupCodeService.queryGroupByMarketingGroupCode(Mockito.any(), Mockito.any(),Mockito.any())).thenReturn(marketingGroupCodeMode);

        Date date = DateUtils.addHours(new Date(), 1);
        String format = DateUtil.format(date,DateUtil.FORMAT_YYYYMMDDHHMISS_14);
        MarketingModel byActivityCode = new MarketingModel();
        byActivityCode.setActivityEnd(format);
        byActivityCode.setActivityType(ActivityTypeEnum.GROUP.code());
        byActivityCode.setActivityStatus("07");

        when(marketingService.findByActivityCode(Mockito.any())).thenReturn(byActivityCode);

        when(marketingGroupUserService.queryGroupMemberNoPay(Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(2);

        int i = flashSaleComponent.confirmOrder(tenantCode, orderNo);
        Assert.assertEquals(1, i);
    }

    @Test
    public void confirmOrderGroup3_member_07_auto_allow_0_2(){

        String tenantCode = "1";
        String orderNo = "1";

        FlashSaleOrderModel byOrderNo = new FlashSaleOrderModel();
        byOrderNo.setOrderStatus(OrderStatusEnum.UNPAID.code());
        byOrderNo.setMarketingGroupCode("111");
        when(flashSaleOrderService.findByOrderNo(Mockito.anyString(), Mockito.anyString())).thenReturn(byOrderNo);
        when(flashSaleOrderService.updateStatus(Mockito.anyString(), Mockito.anyString(), Mockito.anyString())).thenReturn(1);

        MarketingGroupMode groupMode = new MarketingGroupMode();
        groupMode.setGroupSize(1);
        groupMode.setAutoGroupFlag(0);
        when(marketingGroupService.findMarketingGroupByActivityCode(Mockito.any(), Mockito.any())).thenReturn(groupMode);

        MarketingGroupUserMode leader = new MarketingGroupUserMode();

        leader.setUserCode("!1");
        when(marketingGroupUserService.confirmGroupLeaderByMarketingGroupCode(Mockito.anyString(), Mockito.anyString())).thenReturn(leader);

        GroupUserContent groupUserContent = new GroupUserContent();
//        when(marketingGroupComponent.getGroupUserContent(Mockito.any(), Mockito.any(),Mockito.any())).thenReturn(groupUserContent);
//        when(marketingGroupUserService.queryGroupMemberNoPay(Mockito.any(), Mockito.any(),Mockito.any())).thenReturn(0);

        MarketingGroupCodeMode marketingGroupCodeMode = new MarketingGroupCodeMode();
        marketingGroupCodeMode.setInventory(0);
        marketingGroupCodeMode.setGroupStatus(MarketingGroupStatusEnum.GROUP_PROCESSING.code());

        when(marketingGroupCodeService.queryGroupByMarketingGroupCode(Mockito.any(), Mockito.any(),Mockito.any())).thenReturn(marketingGroupCodeMode);

        Date date = DateUtils.addHours(new Date(), 1);
        String format = DateUtil.format(date,DateUtil.FORMAT_YYYYMMDDHHMISS_14);
        MarketingModel byActivityCode = new MarketingModel();
        byActivityCode.setActivityEnd(format);
        byActivityCode.setActivityType(ActivityTypeEnum.GROUP.code());
        byActivityCode.setActivityStatus("07");

        when(marketingService.findByActivityCode(Mockito.any())).thenReturn(byActivityCode);

//        when(marketingGroupUserService.queryGroupMemberNoPay(Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(2);

        int i = flashSaleComponent.confirmOrder(tenantCode, orderNo);
        Assert.assertEquals(1, i);
    }


    @Test
    public void confirmOrderGroup3_member_07_auto_allow_0_1(){

        String tenantCode = "1";
        String orderNo = "1";

        FlashSaleOrderModel byOrderNo = new FlashSaleOrderModel();
        byOrderNo.setOrderStatus(OrderStatusEnum.UNPAID.code());
        byOrderNo.setMarketingGroupCode("111");
        when(flashSaleOrderService.findByOrderNo(Mockito.anyString(), Mockito.anyString())).thenReturn(byOrderNo);
        when(flashSaleOrderService.updateStatus(Mockito.anyString(), Mockito.anyString(), Mockito.anyString())).thenReturn(1);

        MarketingGroupMode groupMode = new MarketingGroupMode();
        groupMode.setGroupSize(1);
        groupMode.setAutoGroupFlag(0);
        when(marketingGroupService.findMarketingGroupByActivityCode(Mockito.any(), Mockito.any())).thenReturn(groupMode);

        MarketingGroupUserMode leader = new MarketingGroupUserMode();

        leader.setUserCode("!1");
        when(marketingGroupUserService.confirmGroupLeaderByMarketingGroupCode(Mockito.anyString(), Mockito.anyString())).thenReturn(leader);

        GroupUserContent groupUserContent = new GroupUserContent();
//        when(marketingGroupComponent.getGroupUserContent(Mockito.any(), Mockito.any(),Mockito.any())).thenReturn(groupUserContent);
//        when(marketingGroupUserService.queryGroupMemberNoPay(Mockito.any(), Mockito.any(),Mockito.any())).thenReturn(0);

        MarketingGroupCodeMode marketingGroupCodeMode = new MarketingGroupCodeMode();
        marketingGroupCodeMode.setInventory(0);
        marketingGroupCodeMode.setGroupStatus(MarketingGroupStatusEnum.GROUP_PROCESSING.code());

        when(marketingGroupCodeService.queryGroupByMarketingGroupCode(Mockito.any(), Mockito.any(),Mockito.any())).thenReturn(marketingGroupCodeMode);

        Date date = DateUtils.addHours(new Date(), 1);
        String format = DateUtil.format(date,DateUtil.FORMAT_YYYYMMDDHHMISS_14);
        MarketingModel byActivityCode = new MarketingModel();
        byActivityCode.setActivityEnd(format);
        byActivityCode.setActivityType(ActivityTypeEnum.GROUP.code());
        byActivityCode.setActivityStatus("07");

        when(marketingService.findByActivityCode(Mockito.any())).thenReturn(byActivityCode);

//        when(marketingGroupUserService.queryGroupMemberNoPay(Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(1);

        int i = flashSaleComponent.confirmOrder(tenantCode, orderNo);
        Assert.assertEquals(1, i);
    }


    @Test
    public void confirmOrderGroup3_member_07_auto_allow_0_no_Start(){

        String tenantCode = "1";
        String orderNo = "1";

        FlashSaleOrderModel byOrderNo = new FlashSaleOrderModel();
        byOrderNo.setOrderStatus(OrderStatusEnum.UNPAID.code());
        byOrderNo.setMarketingGroupCode("111");
        when(flashSaleOrderService.findByOrderNo(Mockito.anyString(), Mockito.anyString())).thenReturn(byOrderNo);
        when(flashSaleOrderService.updateStatus(Mockito.anyString(), Mockito.anyString(), Mockito.anyString())).thenReturn(1);

        MarketingGroupMode groupMode = new MarketingGroupMode();
        groupMode.setGroupSize(1);
        groupMode.setAutoGroupFlag(0);
        when(marketingGroupService.findMarketingGroupByActivityCode(Mockito.any(), Mockito.any())).thenReturn(groupMode);

        MarketingGroupUserMode leader = new MarketingGroupUserMode();

        leader.setUserCode("!1");
        when(marketingGroupUserService.confirmGroupLeaderByMarketingGroupCode(Mockito.anyString(), Mockito.anyString())).thenReturn(leader);

//        when(marketingGroupUserService.queryGroupMemberNoPay(Mockito.any(), Mockito.any(),Mockito.any())).thenReturn(0);

        MarketingGroupCodeMode marketingGroupCodeMode = new MarketingGroupCodeMode();
        marketingGroupCodeMode.setInventory(0);
        marketingGroupCodeMode.setGroupStatus(MarketingGroupStatusEnum.GROUP_NO_START.code());

        when(marketingGroupCodeService.queryGroupByMarketingGroupCode(Mockito.any(), Mockito.any(),Mockito.any())).thenReturn(marketingGroupCodeMode);

        Date date = DateUtils.addHours(new Date(), 1);
        String format = DateUtil.format(date,DateUtil.FORMAT_YYYYMMDDHHMISS_14);
        MarketingModel byActivityCode = new MarketingModel();
        byActivityCode.setActivityEnd(format);
        byActivityCode.setActivityType(ActivityTypeEnum.GROUP.code());
        byActivityCode.setActivityStatus("07");

        when(marketingService.findByActivityCode(Mockito.any())).thenReturn(byActivityCode);

//        when(marketingGroupUserService.queryGroupMemberNoPay(Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(1);

        int i = flashSaleComponent.confirmOrder(tenantCode, orderNo);
        Assert.assertEquals(1, i);
    }

    @Test(expected = PromotionException.class)
    public void confirmOrderGroup3_member_null(){

        String tenantCode = "1";
        String orderNo = "1";

        FlashSaleOrderModel byOrderNo = new FlashSaleOrderModel();
        byOrderNo.setOrderStatus(OrderStatusEnum.UNPAID.code());
        byOrderNo.setMarketingGroupCode("111");
        when(flashSaleOrderService.findByOrderNo(Mockito.anyString(), Mockito.anyString())).thenReturn(byOrderNo);
        when(flashSaleOrderService.updateStatus(Mockito.anyString(), Mockito.anyString(), Mockito.anyString())).thenReturn(1);

        MarketingGroupMode groupMode = new MarketingGroupMode();
        groupMode.setGroupSize(1);
        groupMode.setAutoGroupFlag(1);
        when(marketingGroupService.findMarketingGroupByActivityCode(Mockito.any(), Mockito.any())).thenReturn(groupMode);

        MarketingGroupUserMode leader = new MarketingGroupUserMode();

        leader.setUserCode("!1");
        when(marketingGroupUserService.confirmGroupLeaderByMarketingGroupCode(Mockito.anyString(), Mockito.anyString())).thenReturn(leader);

//        when(redisClient.getString(Mockito.anyString())).thenReturn("0");

        GroupUserContent groupUserContent = new GroupUserContent();
        MarketingGroupCodeMode marketingGroupCodeMode = new MarketingGroupCodeMode();
        marketingGroupCodeMode.setInventory(0);
        when(marketingGroupCodeService.queryGroupByMarketingGroupCode(Mockito.any(), Mockito.any(),Mockito.any())).thenReturn(null);

        int i = flashSaleComponent.confirmOrder(tenantCode, orderNo);
        Assert.assertEquals(1, i);
    }

    public void confirmOrderGroup3(){

        String tenantCode = "1";
        String orderNo = "1";

        FlashSaleOrderModel byOrderNo = new FlashSaleOrderModel();
        byOrderNo.setOrderStatus(OrderStatusEnum.UNPAID.code());
        byOrderNo.setMarketingGroupCode("111");
        when(flashSaleOrderService.findByOrderNo(Mockito.anyString(), Mockito.anyString())).thenReturn(byOrderNo);
        when(flashSaleOrderService.updateStatus(Mockito.anyString(), Mockito.anyString(), Mockito.anyString())).thenReturn(1);

        MarketingGroupMode groupMode = new MarketingGroupMode();
        groupMode.setGroupSize(1);
        groupMode.setAutoGroupFlag(0);
        when(marketingGroupService.findMarketingGroupByActivityCode(Mockito.any(), Mockito.any())).thenReturn(groupMode);

        MarketingGroupUserMode leader = new MarketingGroupUserMode();

        leader.setUserCode("!1");
        when(marketingGroupUserService.confirmGroupLeaderByMarketingGroupCode(Mockito.anyString(), Mockito.anyString())).thenReturn(leader);

//        when(redisClient.getString(Mockito.anyString())).thenReturn("0");

        when(marketingGroupUserService.queryGroupMemberNoPay(Mockito.any(), Mockito.any(),Mockito.any())).thenReturn(1);

        int i = flashSaleComponent.confirmOrder(tenantCode, orderNo);
        Assert.assertEquals(1, i);
    }

    @Test
    public void cancelOrder_null(){
        String tenantCode = "1";
        String orderNo = "1";

        when(flashSaleOrderService.findByOrderNo(Mockito.anyString(), Mockito.anyString())).thenReturn(null);

        int i = flashSaleComponent.cancelOrder(tenantCode, orderNo);
        Assert.assertEquals(1, i);
    }

    @Test
    public void cancelOrder_status(){
        String tenantCode = "1";
        String orderNo = "1";

        FlashSaleOrderModel byOrderNo = new FlashSaleOrderModel();
        byOrderNo.setOrderStatus(OrderStatusEnum.CANCELED.code());
        when(flashSaleOrderService.findByOrderNo(Mockito.anyString(), Mockito.anyString())).thenReturn(byOrderNo);

        int i = flashSaleComponent.cancelOrder(tenantCode, orderNo);
        Assert.assertEquals(1, i);
    }

    @Test
    public void cancelOrder_no_detail(){
        String tenantCode = "1";
        String orderNo = "1";

        FlashSaleOrderModel byOrderNo = new FlashSaleOrderModel();
        byOrderNo.setOrderStatus(OrderStatusEnum.UNPAID.code());
        byOrderNo.setActivityCode("1");
        when(flashSaleOrderService.findByOrderNo(Mockito.anyString(), Mockito.anyString())).thenReturn(byOrderNo);
        when(flashSaleOrderDetailService.findByOrderNo(Mockito.anyString(), Mockito.anyString())).thenReturn(new ArrayList<>());
        when(flashSaleOrderService.updateStatus(Mockito.anyString(), Mockito.anyString(), Mockito.anyString())).thenReturn(1);
        MarketingModel byActivityCode = new MarketingModel();
        byActivityCode.setSelectProductType(SelectorProductTypeEnum.SELECT_SKU.code());
        when(marketingService.findByActivityCode(Mockito.anyString())).thenReturn(byActivityCode);
        int i = flashSaleComponent.cancelOrder(tenantCode, orderNo);
        Assert.assertEquals(1, i);
    }

    @Test
    public void cancelOrder(){
        String tenantCode = "1";
        String orderNo = "1";

        FlashSaleOrderModel byOrderNo = new FlashSaleOrderModel();
        byOrderNo.setOrderStatus(OrderStatusEnum.UNPAID.code());
        byOrderNo.setActivityCode("1");
        when(flashSaleOrderService.findByOrderNo(Mockito.anyString(), Mockito.anyString())).thenReturn(byOrderNo);
        ArrayList<FlashSaleOrderDetailModel> value = new ArrayList<>();
        FlashSaleOrderDetailModel e = new FlashSaleOrderDetailModel();
        e.setSkuCode("1");
        e.setSkuQuality(1);
        value.add(e);
        when(flashSaleOrderDetailService.findByOrderNo(Mockito.anyString(), Mockito.anyString())).thenReturn(value);
        when(flashSaleProductService.dealInventory(Mockito.anyString(), Mockito.anyString(), Mockito.anyInt())).thenReturn(1);
        when(flashSaleOrderService.updateStatus(Mockito.anyString(), Mockito.anyString(), Mockito.anyString())).thenReturn(1);
        MarketingModel byActivityCode = new MarketingModel();
        byActivityCode.setSelectProductType(SelectorProductTypeEnum.SELECT_SKU.code());
        when(marketingService.findByActivityCode(Mockito.anyString())).thenReturn(byActivityCode);

        int i = flashSaleComponent.cancelOrder(tenantCode, orderNo);
        Assert.assertEquals(1, i);
    }

    @Test
    public void cancelOrder_group(){
        String tenantCode = "1";
        String orderNo = "1";

        FlashSaleOrderModel byOrderNo = new FlashSaleOrderModel();
        byOrderNo.setOrderStatus(OrderStatusEnum.UNPAID.code());
        byOrderNo.setActivityCode("1");
        when(flashSaleOrderService.findByOrderNo(Mockito.anyString(), Mockito.anyString())).thenReturn(byOrderNo);
        ArrayList<FlashSaleOrderDetailModel> value = new ArrayList<>();
        FlashSaleOrderDetailModel e = new FlashSaleOrderDetailModel();
        e.setSkuCode("1");
        e.setSkuQuality(1);
        value.add(e);
        when(flashSaleOrderDetailService.findByOrderNo(Mockito.anyString(), Mockito.anyString())).thenReturn(value);
        when(flashSaleProductService.dealInventory(Mockito.anyString(), Mockito.anyString(), Mockito.anyInt())).thenReturn(1);
        when(flashSaleOrderService.updateStatus(Mockito.anyString(), Mockito.anyString(), Mockito.anyString())).thenReturn(1);
        MarketingModel byActivityCode = new MarketingModel();
        byActivityCode.setSelectProductType(SelectorProductTypeEnum.SELECT_SKU.code());
        when(marketingService.findByActivityCode(Mockito.anyString())).thenReturn(byActivityCode);
        int i = flashSaleComponent.cancelOrder(tenantCode, orderNo);
        Assert.assertEquals(1, i);
    }

    @Test
    public void cancelOrder_group1(){
        String tenantCode = "1";
        String orderNo = "1";

        FlashSaleOrderModel byOrderNo = new FlashSaleOrderModel();
        byOrderNo.setOrderStatus(OrderStatusEnum.UNPAID.code());
        byOrderNo.setActivityCode("1");
        when(flashSaleOrderService.findByOrderNo(Mockito.anyString(), Mockito.anyString())).thenReturn(byOrderNo);
        ArrayList<FlashSaleOrderDetailModel> value = new ArrayList<>();
        FlashSaleOrderDetailModel e = new FlashSaleOrderDetailModel();
        e.setSkuCode("1");
        e.setSkuQuality(1);
        value.add(e);
        when(flashSaleOrderDetailService.findByOrderNo(Mockito.anyString(), Mockito.anyString())).thenReturn(value);
        when(flashSaleOrderService.updateStatus(Mockito.anyString(), Mockito.anyString(), Mockito.anyString())).thenReturn(1);
        //when(redisTemplate.opsHashHasKey(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(true);
        MarketingModel byActivityCode = new MarketingModel();
        byActivityCode.setSelectProductType(SelectorProductTypeEnum.SELECT_SPU.code());
        when(marketingService.findByActivityCode(Mockito.anyString())).thenReturn(byActivityCode);
//        when(flashSaleProductService.dealInventoryBySpu(Mockito.anyString(), Mockito.anyString(), Mockito.anyInt())).thenReturn(1);

        int i = flashSaleComponent.cancelOrder(tenantCode, orderNo);
        Assert.assertEquals(1, i);
    }


    @Test
    public void queryActivityList(){
        FlashSaleQueryListParam param = new FlashSaleQueryListParam();
        PageData<MarketingModel> marketingModelPageData = new PageData<>();
        List<MarketingModel> marketingModels = new ArrayList<>();
        marketingModels.add(new MarketingModel());
        marketingModelPageData.setList(marketingModels);

        when(marketingService.queryMarketingFlashSaleList(any())).thenReturn(marketingModelPageData);
        PageResult<FlashSaleQueryListResult> pageResult = flashSaleComponent.queryActivityList(param);
        Assert.assertEquals(1, pageResult.getData().getList().size());
    }

    @Test
    public void queryActivityList1(){
        FlashSaleQueryListParam param = new FlashSaleQueryListParam();
        param.setLanguage("1");
        PageData<MarketingModel> marketingModelPageData = new PageData<>();
        List<MarketingModel> marketingModels = new ArrayList<>();
        marketingModels.add(new MarketingModel());
        marketingModelPageData.setList(marketingModels);

        when(marketingService.queryMarketingFlashSaleList(any())).thenReturn(marketingModelPageData);
        when(languageService.findListByActivityCode(any())).thenReturn(new ArrayList<>());
        PageResult<FlashSaleQueryListResult> pageResult = flashSaleComponent.queryActivityList(param);
        Assert.assertEquals(1, pageResult.getData().getList().size());
    }

    @Test
    public void queryActivityList2(){
        FlashSaleQueryListParam param = new FlashSaleQueryListParam();
        param.setLanguage("1");
        PageData<MarketingModel> marketingModelPageData = new PageData<>();
        List<MarketingModel> marketingModels = new ArrayList<>();
        marketingModels.add(new MarketingModel());
        marketingModelPageData.setList(marketingModels);
        List<MarketingLanguageModel> languages = new ArrayList<>();
        MarketingLanguageModel model = new MarketingLanguageModel();
        model.setLanguage("2");
        languages.add(model);
        when(marketingService.queryMarketingFlashSaleList(any())).thenReturn(marketingModelPageData);
        when(languageService.findListByActivityCode(any())).thenReturn(languages);
        PageResult<FlashSaleQueryListResult> pageResult = flashSaleComponent.queryActivityList(param);
        Assert.assertEquals(1, pageResult.getData().getList().size());
    }

    @Test
    public void queryActivityList3(){
        FlashSaleQueryListParam param = new FlashSaleQueryListParam();
        param.setLanguage("1");
        PageData<MarketingModel> marketingModelPageData = new PageData<>();
        List<MarketingModel> marketingModels = new ArrayList<>();
        marketingModels.add(new MarketingModel());
        marketingModelPageData.setList(marketingModels);
        List<MarketingLanguageModel> languages = new ArrayList<>();
        MarketingLanguageModel model = new MarketingLanguageModel();
        model.setLanguage("1");
        languages.add(model);
        when(marketingService.queryMarketingFlashSaleList(any())).thenReturn(marketingModelPageData);
        when(languageService.findListByActivityCode(any())).thenReturn(languages);
        PageResult<FlashSaleQueryListResult> pageResult = flashSaleComponent.queryActivityList(param);
        Assert.assertEquals(1, pageResult.getData().getList().size());
    }

    @Test
    public void queryActivityList4(){
        FlashSaleQueryListParam param = new FlashSaleQueryListParam();
        param.setLanguage("1");
        PageData<MarketingModel> marketingModelPageData = new PageData<>();
        List<MarketingModel> marketingModels = new ArrayList<>();
        marketingModelPageData.setList(marketingModels);
        when(marketingService.queryMarketingFlashSaleList(any())).thenReturn(marketingModelPageData);
        PageResult<FlashSaleQueryListResult> pageResult = flashSaleComponent.queryActivityList(param);
        Assert.assertEquals(0, pageResult.getData().getList().size());
    }

    @Test
    public void getProductsByActivityCodesAndProducts(){
        flashSaleComponent.getProductsByActivityCodesAndProducts("1",new HashSet<>(),new ArrayList<>());
    }
//
//    @Test
//    public void releaseUserMax(){
//
//        when(redisTemplate.opsHashIncrement(any(),any(),any(),Mockito.anyLong())).thenReturn(1L);
//        flashSaleComponent.releaseUserMax(null,null,null);
//
//        List<String> userLimitKeyList = new ArrayList<>();
//        userLimitKeyList.add("1");
//        Map<String,Integer> skuQualityMap = new HashMap<>();
//        skuQualityMap.put("1", 2);
//        flashSaleComponent.releaseUserMax("test",userLimitKeyList,skuQualityMap);
//
//        when(redisTemplate.opsHashIncrement(any(),any(),any(),Mockito.anyLong())).thenThrow(new RuntimeException());
//        flashSaleComponent.releaseUserMax("test",userLimitKeyList,skuQualityMap);
//    }

    @Test
    public void setAuditConfig(){
        FlashSaleFindResult flashSaleFindResult = new FlashSaleFindResult();
        flashSaleComponent.setFlashSaleAuditConfig(flashSaleFindResult,null);

        flashSaleComponent.setFlashSaleAuditConfig(flashSaleFindResult,"0");

        flashSaleComponent.setFlashSaleAuditConfig(flashSaleFindResult,"0,1");
    }

    @Test
    public void setFlashSaleAuditConfig(){
        FlashSaleFindResult flashSaleFindResult = new FlashSaleFindResult();
        when(activityComponentDomain.isNeedAudit(any())).thenReturn("1");
        when(activityComponentDomain.isAuditDifferentOperator(any())).thenReturn("1");
        flashSaleComponent.setFlashSaleAuditConfig(flashSaleFindResult,"0");
    }

    @Test
    public void buildMemberInventory(){
        FlashSaleProductModel currentProductModel = new FlashSaleProductModel();
        currentProductModel.setSkuInventory(1);
        currentProductModel.setMaxPerUser(10);
        when(redisOpsHelper.getLimitedValue(Mockito.anyString(),any(),any(),any(),any())).thenReturn(null);
        flashSaleComponent.buildMemberInventory("activity1","sku1","test",currentProductModel);

        currentProductModel.setSkuInventory(3);
        flashSaleComponent.buildMemberInventory("activity1","sku1","test",currentProductModel);

    }


    @Test
    public void calcFlashSaleOrderPrice13(){
        FlashSaleShoppingCartDto param = new FlashSaleShoppingCartDto();
        param.setDomainCode("1");
        param.setTenantCode("1");
        param.setUserCode("1");
        param.setActivityCode("1");
        param.setActivityType(ActivityTypeEnum.GROUP.code());
        List<ShoppingCartStore> cartStoreList = new ArrayList<>();
        ShoppingCartStore shoppingCartStore = new ShoppingCartStore();
        List<ShoppingCartItem> cartItemList = new ArrayList<>();
        ShoppingCartItem item = new ShoppingCartItem();
        item.setSkuCode("1");
        item.setProductCode("1");
        item.setQuantity(1);
        item.setProductPrice(new BigDecimal("123"));
        item.setSelectionFlag("");
        item.setSelectionFlag("01");
        cartItemList.add(item);
        shoppingCartStore.setCartItemList(cartItemList);
        cartStoreList.add(shoppingCartStore);
        param.setCartStoreList(cartStoreList);
        List<FlashSaleActivityInfoDTO> activity = new ArrayList<>();
        FlashSaleActivityInfoDTO dto = new FlashSaleActivityInfoDTO();
        dto.setActivityCode("1");
        activity.add(dto);

        Map<String, CacheFlashSaleModel> flashSaleCacheMap = new HashedMap<>();
        CacheFlashSaleModel flashSaleModel = new CacheFlashSaleModel();
        List<FlashSaleProductModel> productModels = new ArrayList<>();
        FlashSaleProductModel productModel = new FlashSaleProductModel();
        productModel.setSkuCode("1");
        productModel.setFlashPrice(new BigDecimal("12"));
        productModels.add(productModel);
        flashSaleModel.setProducts(productModels);
        List<MarketingLanguageModel> languages11 = new ArrayList<>();

        MarketingLanguageModel marketingLanguageModel11 = new MarketingLanguageModel();
        marketingLanguageModel11.setLanguage("id-ID");
        languages11.add(marketingLanguageModel11);

        MarketingLanguageModel marketingLanguageModel2 = new MarketingLanguageModel();
        marketingLanguageModel2.setLanguage("zh-CN");
        languages11.add(marketingLanguageModel2);

        flashSaleModel.setLanguages(languages11);
        flashSaleCacheMap.put("1", flashSaleModel);

        FlashSaleSkuActivityPriceDTO skuActivityPriceDTO = new FlashSaleSkuActivityPriceDTO();
        List<FlashSaleActivityPriceDTO> activityPriceDTOS = new ArrayList<>();
        FlashSaleActivityPriceDTO priceDTO = new FlashSaleActivityPriceDTO();
        priceDTO.setActivityCode("1");
        activityPriceDTOS.add(priceDTO);
        skuActivityPriceDTO.setActivity(activityPriceDTOS);

        Mockito.when(marketingCacheComponent.filterActivityByProduct(Mockito.any(), Mockito.any(), Mockito.any(),Mockito.any())).thenReturn(flashSaleCacheMap);
////        Mockito.when(flashSaleComponent.checkQuota(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(true);
        Map<String, CacheFlashSaleModel> activityCacheMap = new HashedMap<>();
        CacheFlashSaleModel cacheFlashSaleModel = new CacheFlashSaleModel();
        cacheFlashSaleModel.setActivityCode("test");
        cacheFlashSaleModel.setActivityBegin("20211028");
        cacheFlashSaleModel.setActivityEnd("20211029");
        activityCacheMap.put("test", cacheFlashSaleModel);
        Mockito.when(marketingCacheComponent.getFlashSaleCacheMap(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(activityCacheMap);

        FlashSaleProductModel flashSaleProductModel = new FlashSaleProductModel();
        flashSaleProductModel.setMaxPerUser(12);
        flashSaleProductModel.setSkuInventory(34);
        Mockito.when(flashSaleProductService.findByActivityAndSku(Mockito.any(), Mockito.any())).thenReturn(flashSaleProductModel);
        MarketingModel byActivityCode = new MarketingModel();
        byActivityCode.setActivityCode("11");
        byActivityCode.setSelectProductType("01");
        Date date = DateUtils.addHours(new Date(), 1);
        String format = DateUtil.format(date,DateUtil.FORMAT_YYYYMMDDHHMISS_14);
        byActivityCode.setActivityEnd(format);
        byActivityCode.setActivityType(ActivityTypeEnum.GROUP.code());
        Mockito.when(marketingService.findByActivityCode(Mockito.any())).thenReturn(byActivityCode);

        byActivityCode.setSelectProductType(SelectorProductTypeEnum.SELECT_SKU.code());
        MarketingGroupMode marketingGroup = new MarketingGroupMode();
        marketingGroup.setGroupSize(2);
        marketingGroup.setEffectiveHour(1);
        marketingGroup.setAutoGroupFlag(1);
        Mockito.when(marketingGroupService.findByActivityCode(Mockito.any())).thenReturn(marketingGroup);

        Mockito.when(marketingGroupUserService.existLeaderByGroupCode(Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(false);
        MarketingGroupUserMode leader = new MarketingGroupUserMode();

        Mockito.when(marketingGroupUserService.findGroupLeaderUserByMarketingGroupCode(Mockito.any(),Mockito.any())).thenReturn(leader);

        List<FlashSaleOrderCalaResult> flashSaleOrderCalaResults = flashSaleComponent.handlerShoppingCart(param);
        Assert.assertEquals(1, flashSaleOrderCalaResults.size());
    }

    @Test
    public void calcFlashSaleOrderPrice14(){
        FlashSaleShoppingCartDto param = new FlashSaleShoppingCartDto();
        param.setDomainCode("1");
        param.setTenantCode("1");
        param.setUserCode("1");
        param.setActivityCode("1");
        param.setActivityType(ActivityTypeEnum.GROUP.code());
        List<ShoppingCartStore> cartStoreList = new ArrayList<>();
        ShoppingCartStore shoppingCartStore = new ShoppingCartStore();
        List<ShoppingCartItem> cartItemList = new ArrayList<>();
        ShoppingCartItem item = new ShoppingCartItem();
        item.setSkuCode("1");
        item.setProductCode("1");
        item.setQuantity(1);
        item.setProductPrice(new BigDecimal("123"));
        item.setSelectionFlag("");
        item.setSelectionFlag("01");
        cartItemList.add(item);
        shoppingCartStore.setCartItemList(cartItemList);
        cartStoreList.add(shoppingCartStore);
        param.setCartStoreList(cartStoreList);
        List<FlashSaleActivityInfoDTO> activity = new ArrayList<>();
        FlashSaleActivityInfoDTO dto = new FlashSaleActivityInfoDTO();
        dto.setActivityCode("1");
        activity.add(dto);

        Map<String, CacheFlashSaleModel> flashSaleCacheMap = new HashedMap<>();
        CacheFlashSaleModel flashSaleModel = new CacheFlashSaleModel();
        List<FlashSaleProductModel> productModels = new ArrayList<>();
        FlashSaleProductModel productModel = new FlashSaleProductModel();
        productModel.setSkuCode("1");
        productModel.setFlashPrice(new BigDecimal("12"));
        productModels.add(productModel);
        flashSaleModel.setProducts(productModels);

        List<MarketingLanguageModel> languages11 = new ArrayList<>();

        MarketingLanguageModel marketingLanguageModel11 = new MarketingLanguageModel();
        marketingLanguageModel11.setLanguage("id-ID");
        languages11.add(marketingLanguageModel11);

        MarketingLanguageModel marketingLanguageModel2 = new MarketingLanguageModel();
        marketingLanguageModel2.setLanguage("zh-CN");
        languages11.add(marketingLanguageModel2);

        flashSaleModel.setLanguages(languages11);

        flashSaleCacheMap.put("1", flashSaleModel);

        FlashSaleSkuActivityPriceDTO skuActivityPriceDTO = new FlashSaleSkuActivityPriceDTO();
        List<FlashSaleActivityPriceDTO> activityPriceDTOS = new ArrayList<>();
        FlashSaleActivityPriceDTO priceDTO = new FlashSaleActivityPriceDTO();
        priceDTO.setActivityCode("1");
        activityPriceDTOS.add(priceDTO);
        skuActivityPriceDTO.setActivity(activityPriceDTOS);

        Mockito.when(marketingCacheComponent.filterActivityByProduct(Mockito.any(), Mockito.any(), Mockito.any(),Mockito.any() )).thenReturn(flashSaleCacheMap);
////        Mockito.when(flashSaleComponent.checkQuota(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(true);
        Map<String, CacheFlashSaleModel> activityCacheMap = new HashedMap<>();
        CacheFlashSaleModel cacheFlashSaleModel = new CacheFlashSaleModel();
        cacheFlashSaleModel.setActivityCode("test");
        cacheFlashSaleModel.setActivityBegin("20211028");
        cacheFlashSaleModel.setActivityEnd("20211029");
        activityCacheMap.put("test", cacheFlashSaleModel);
        Mockito.when(marketingCacheComponent.getFlashSaleCacheMap(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(activityCacheMap);

        FlashSaleProductModel flashSaleProductModel = new FlashSaleProductModel();
        flashSaleProductModel.setMaxPerUser(12);
        flashSaleProductModel.setSkuInventory(34);
        Mockito.when(flashSaleProductService.findByActivityAndSku(Mockito.any(), Mockito.any())).thenReturn(flashSaleProductModel);
        MarketingModel byActivityCode = new MarketingModel();
        byActivityCode.setActivityCode("11");
        byActivityCode.setSelectProductType("01");
        Mockito.when(marketingService.findByActivityCode(Mockito.any())).thenReturn(byActivityCode);


        MarketingGroupMode marketingGroup = new MarketingGroupMode();
        marketingGroup.setGroupSize(2);
        marketingGroup.setEffectiveHour(1);
        marketingGroup.setAutoGroupFlag(0);
        Mockito.when(marketingGroupService.findByActivityCode(Mockito.any())).thenReturn(marketingGroup);

        Mockito.when(marketingGroupUserService.existLeaderByGroupCode(Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(false);
        MarketingGroupUserMode leader = new MarketingGroupUserMode();

        Mockito.when(marketingGroupUserService.findGroupLeaderUserByMarketingGroupCode(Mockito.any(),Mockito.any())).thenReturn(leader);
        List<FlashSaleOrderCalaResult> flashSaleOrderCalaResults = flashSaleComponent.handlerShoppingCart(param);
        Assert.assertEquals(1, flashSaleOrderCalaResults.size());
    }

    @Test
    public void calcFlashSaleOrderPrice12(){
        FlashSaleShoppingCartDto param = new FlashSaleShoppingCartDto();
        param.setDomainCode("1");
        param.setTenantCode("1");
        param.setUserCode("1");
        param.setActivityCode("1");
//        param.setActivityType(ActivityTypeEnum.GROUP.code());

        List<ShoppingCartStore> cartStoreList = new ArrayList<>();
        ShoppingCartStore shoppingCartStore = new ShoppingCartStore();
        List<ShoppingCartItem> cartItemList = new ArrayList<>();
        ShoppingCartItem item = new ShoppingCartItem();
        item.setSkuCode("1");
        item.setProductCode("1");
        item.setQuantity(1);
        item.setProductPrice(new BigDecimal("123"));
        item.setSelectionFlag("");
        item.setSelectionFlag("01");
        cartItemList.add(item);
        shoppingCartStore.setCartItemList(cartItemList);
        cartStoreList.add(shoppingCartStore);
        param.setCartStoreList(cartStoreList);
        List<FlashSaleActivityInfoDTO> activity = new ArrayList<>();
        FlashSaleActivityInfoDTO dto = new FlashSaleActivityInfoDTO();
        dto.setActivityCode("1");
        activity.add(dto);

        Map<String, CacheFlashSaleModel> flashSaleCacheMap = new HashedMap<>();
        CacheFlashSaleModel flashSaleModel = new CacheFlashSaleModel();
        List<FlashSaleProductModel> productModels = new ArrayList<>();
        FlashSaleProductModel productModel = new FlashSaleProductModel();
        productModel.setSkuCode("1");
        productModel.setFlashPrice(new BigDecimal("12"));
        productModels.add(productModel);
        flashSaleModel.setProducts(productModels);

        List<MarketingLanguageModel> languages11 = new ArrayList<>();

        MarketingLanguageModel marketingLanguageModel11 = new MarketingLanguageModel();
        marketingLanguageModel11.setLanguage("id-ID");
        languages11.add(marketingLanguageModel11);

        MarketingLanguageModel marketingLanguageModel2 = new MarketingLanguageModel();
        marketingLanguageModel2.setLanguage("zh-CN");
        languages11.add(marketingLanguageModel2);

        flashSaleModel.setLanguages(languages11);

        flashSaleCacheMap.put("1", flashSaleModel);

        FlashSaleSkuActivityPriceDTO skuActivityPriceDTO = new FlashSaleSkuActivityPriceDTO();
        List<FlashSaleActivityPriceDTO> activityPriceDTOS = new ArrayList<>();
        FlashSaleActivityPriceDTO priceDTO = new FlashSaleActivityPriceDTO();
        priceDTO.setActivityCode("1");
        activityPriceDTOS.add(priceDTO);
        skuActivityPriceDTO.setActivity(activityPriceDTOS);

        Mockito.when(marketingCacheComponent.filterActivityByProduct(Mockito.any(), Mockito.any(), Mockito.any(),Mockito.any() )).thenReturn(flashSaleCacheMap);
////        Mockito.when(flashSaleComponent.checkQuota(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(true);
        Map<String, CacheFlashSaleModel> activityCacheMap = new HashedMap<>();
        CacheFlashSaleModel cacheFlashSaleModel = new CacheFlashSaleModel();
        cacheFlashSaleModel.setActivityCode("test");
        cacheFlashSaleModel.setActivityBegin("20211028");
        cacheFlashSaleModel.setActivityEnd("20211029");
        activityCacheMap.put("test", cacheFlashSaleModel);
        Mockito.when(marketingCacheComponent.getFlashSaleCacheMap(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(activityCacheMap);

        FlashSaleProductModel flashSaleProductModel = new FlashSaleProductModel();
        flashSaleProductModel.setMaxPerUser(12);
        flashSaleProductModel.setSkuInventory(34);
        Mockito.when(flashSaleProductService.findByActivityAndSku(Mockito.any(), Mockito.any())).thenReturn(flashSaleProductModel);
        MarketingModel byActivityCode = new MarketingModel();
        byActivityCode.setActivityCode("11");
        byActivityCode.setSelectProductType("01");
        Mockito.when(marketingService.findByActivityCode(Mockito.any())).thenReturn(byActivityCode);

//        Mockito.when(marketingGroupUserService.existLeaderByGroupCode(Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(false);

        List<FlashSaleOrderCalaResult> flashSaleOrderCalaResults = flashSaleComponent.handlerShoppingCart(param);
        Assert.assertEquals(1, flashSaleOrderCalaResults.size());
    }



    @Test
    public void calcFlashSaleOrderPrice1(){
        FlashSaleShoppingCartDto param = new FlashSaleShoppingCartDto();
        param.setDomainCode("1");
        param.setTenantCode("1");
        param.setUserCode("1");
        param.setActivityCode("1");
        List<ShoppingCartStore> cartStoreList = new ArrayList<>();
        ShoppingCartStore shoppingCartStore = new ShoppingCartStore();
        List<ShoppingCartItem> cartItemList = new ArrayList<>();
        ShoppingCartItem item = new ShoppingCartItem();
        item.setSkuCode("1");
        item.setProductCode("1");
        item.setQuantity(1);
        item.setProductPrice(new BigDecimal("123"));
        item.setSelectionFlag("");
        item.setSelectionFlag("01");
        cartItemList.add(item);
        shoppingCartStore.setCartItemList(cartItemList);
        cartStoreList.add(shoppingCartStore);
        param.setCartStoreList(cartStoreList);
        List<FlashSaleActivityInfoDTO> activity = new ArrayList<>();
        FlashSaleActivityInfoDTO dto = new FlashSaleActivityInfoDTO();
        dto.setActivityCode("1");
        activity.add(dto);

        Map<String, CacheFlashSaleModel> flashSaleCacheMap = new HashedMap<>();
        CacheFlashSaleModel flashSaleModel = new CacheFlashSaleModel();


        List<MarketingLanguageModel> languages1 = new ArrayList<>();

        MarketingLanguageModel marketingLanguageModel11 = new MarketingLanguageModel();
        marketingLanguageModel11.setLanguage("id-ID");
        languages1.add(marketingLanguageModel11);

        MarketingLanguageModel marketingLanguageModel2 = new MarketingLanguageModel();
        marketingLanguageModel2.setLanguage("zh-CN");
        languages1.add(marketingLanguageModel2);

        flashSaleModel.setLanguages(languages1);


        List<FlashSaleProductModel> productModels = new ArrayList<>();
        FlashSaleProductModel productModel = new FlashSaleProductModel();
        productModel.setSkuCode("1");
        productModel.setFlashPrice(new BigDecimal("12"));
        productModels.add(productModel);
        flashSaleModel.setProducts(productModels);
        flashSaleCacheMap.put("1", flashSaleModel);

        FlashSaleSkuActivityPriceDTO skuActivityPriceDTO = new FlashSaleSkuActivityPriceDTO();
        List<FlashSaleActivityPriceDTO> activityPriceDTOS = new ArrayList<>();
        FlashSaleActivityPriceDTO priceDTO = new FlashSaleActivityPriceDTO();
        priceDTO.setActivityCode("1");
        activityPriceDTOS.add(priceDTO);
        skuActivityPriceDTO.setActivity(activityPriceDTOS);

        MarketingModel byActivityCode = new MarketingModel();
        byActivityCode.setSelectProductType("01");
        Mockito.when(marketingService.findByActivityCode(Mockito.any())).thenReturn(byActivityCode);


        Mockito.when(marketingCacheComponent.filterActivityByProduct(Mockito.any(), Mockito.any(), Mockito.any(),Mockito.any() )).thenReturn(flashSaleCacheMap);
////        Mockito.when(flashSaleComponent.checkQuota(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(true);
        Map<String, CacheFlashSaleModel> activityCacheMap = new HashedMap<>();
        CacheFlashSaleModel cacheFlashSaleModel = new CacheFlashSaleModel();
        cacheFlashSaleModel.setActivityCode("test");
        cacheFlashSaleModel.setActivityBegin("20211028");
        cacheFlashSaleModel.setActivityEnd("20211029");

        List<MarketingLanguageModel> languages = new ArrayList<>();

        MarketingLanguageModel marketingLanguageModel1 = new MarketingLanguageModel();
        marketingLanguageModel1.setLanguage("id-ID");
        languages.add(marketingLanguageModel1);

        MarketingLanguageModel marketingLanguageModel = new MarketingLanguageModel();
        marketingLanguageModel.setLanguage("zh-CN");
        languages.add(marketingLanguageModel);

        cacheFlashSaleModel.setLanguages(languages);

        activityCacheMap.put("test", cacheFlashSaleModel);
        Mockito.when(marketingCacheComponent.getFlashSaleCacheMap(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(activityCacheMap);
        byActivityCode.setSelectProductType("01");
        Mockito.when(marketingService.findByActivityCode(Mockito.any())).thenReturn(byActivityCode);
        FlashSaleProductModel flashSaleProductModel = new FlashSaleProductModel();
        flashSaleProductModel.setMaxPerUser(12);
        flashSaleProductModel.setSkuInventory(34);
        Mockito.when(flashSaleProductService.findByActivityAndSku(Mockito.any(), Mockito.any())).thenReturn(flashSaleProductModel);
//        Mockito.when(marketingGroupUserService.existLeaderByGroupCode(Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(false);

        List<FlashSaleOrderCalaResult> flashSaleOrderCalaResults = flashSaleComponent.handlerShoppingCart(param);
        Assert.assertEquals(1, flashSaleOrderCalaResults.size());
    }

    @Test
    public void calcFlashSaleOrderPrice2(){
        FlashSaleShoppingCartDto param = new FlashSaleShoppingCartDto();
        param.setDomainCode("1");
        param.setTenantCode("1");
        param.setUserCode("1");
        param.setActivityCode("1");
        param.setDomainCode("1");
        param.setTenantCode("1");
        param.setActivityCode("1");
        param.setActivityType("06");
        List<ShoppingCartStore> cartStoreList = new ArrayList<>();
        ShoppingCartStore shoppingCartStore = new ShoppingCartStore();
        List<ShoppingCartItem> cartItemList = new ArrayList<>();
        ShoppingCartItem item = new ShoppingCartItem();
        item.setSkuCode("1");
        item.setProductCode("1");
        item.setQuantity(1);

        item.setProductPrice(new BigDecimal("123"));
        item.setSelectionFlag("01");
        cartItemList.add(item);
        shoppingCartStore.setCartItemList(cartItemList);
        cartStoreList.add(shoppingCartStore);
        param.setCartStoreList(cartStoreList);
        List<FlashSaleActivityInfoDTO> activity = new ArrayList<>();
        FlashSaleActivityInfoDTO dto = new FlashSaleActivityInfoDTO();
        dto.setActivityCode("1");
        activity.add(dto);

        Map<String, CacheFlashSaleModel> flashSaleCacheMap = new HashedMap<>();
        CacheFlashSaleModel flashSaleModel = new CacheFlashSaleModel();
        List<FlashSaleProductModel> productModels = new ArrayList<>();
        FlashSaleProductModel productModel = new FlashSaleProductModel();
        productModel.setSkuCode("1");
        productModel.setLimitFlag(1);
        productModel.setFlashPrice(new BigDecimal("12"));

        productModel.setFlashPrice(new BigDecimal(1));
        productModels.add(productModel);
        flashSaleModel.setProducts(productModels);

        Date date = DateUtils.addHours(new Date(), -1);
        String effectiveTime = DateUtil.format(date,DateUtil.FORMAT_YYYYMMDDHHMISS_14);
        flashSaleModel.setActivityEnd(effectiveTime);

        List<MarketingLanguageModel> languages11 = new ArrayList<>();

        MarketingLanguageModel marketingLanguageModel11 = new MarketingLanguageModel();
        marketingLanguageModel11.setLanguage("id-ID");
        languages11.add(marketingLanguageModel11);

        MarketingLanguageModel marketingLanguageModel2 = new MarketingLanguageModel();
        marketingLanguageModel2.setLanguage("zh-CN");
        languages11.add(marketingLanguageModel2);

        flashSaleModel.setLanguages(languages11);

        flashSaleCacheMap.put("1", flashSaleModel);

        FlashSaleSkuActivityPriceDTO skuActivityPriceDTO = new FlashSaleSkuActivityPriceDTO();
        List<FlashSaleActivityPriceDTO> activityPriceDTOS = new ArrayList<>();
        FlashSaleActivityPriceDTO priceDTO = new FlashSaleActivityPriceDTO();
        priceDTO.setActivityCode("1");
        activityPriceDTOS.add(priceDTO);
        skuActivityPriceDTO.setActivity(activityPriceDTOS);
        Mockito.when(marketingCacheComponent.filterActivityByProduct(Mockito.any(), Mockito.any(), Mockito.any(),Mockito.any() )).thenReturn(flashSaleCacheMap);
        Map<String, CacheFlashSaleModel> activityCacheMap = new HashedMap<>();
        CacheFlashSaleModel cacheFlashSaleModel = new CacheFlashSaleModel();
        cacheFlashSaleModel.setActivityCode("test");
        cacheFlashSaleModel.setActivityBegin("20211028");
        cacheFlashSaleModel.setActivityEnd("20211029");
        cacheFlashSaleModel.setProducts(productModels);

        List<MarketingLanguageModel> languages1 = new ArrayList<>();

        MarketingLanguageModel marketingLanguageModel131 = new MarketingLanguageModel();
        marketingLanguageModel131.setLanguage("id-ID");
        languages1.add(marketingLanguageModel131);

        MarketingLanguageModel marketingLanguageModel22 = new MarketingLanguageModel();
        marketingLanguageModel22.setLanguage("zh-CN");
        languages1.add(marketingLanguageModel22);

        flashSaleModel.setLanguages(languages1);

        activityCacheMap.put("test", cacheFlashSaleModel);
        Mockito.when(marketingCacheComponent.getFlashSaleCacheMap(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(activityCacheMap);

        FlashSaleProductModel flashSaleProductModel = new FlashSaleProductModel();
        flashSaleProductModel.setMaxPerUser(12);
        flashSaleProductModel.setSkuInventory(34);
        flashSaleProductModel.setMaxPerUserFlag(1);
        flashSaleProductModel.setSkuCode("1");
        flashSaleProductModel.setFlashPrice(new BigDecimal(1));
        flashSaleProductModel.setGroupLeaderPrice(new BigDecimal(100));

        Mockito.when(flashSaleProductService.findByActivityAndSku(Mockito.any(), Mockito.any())).thenReturn(flashSaleProductModel);

        findByActivityCode();
//        Mockito.when(codeGenerator.generateCode(Mockito.any(), Mockito.any(),Mockito.any(),Mockito.any())).thenReturn("11");


        MarketingGroupMode marketingGroup = new MarketingGroupMode();
        marketingGroup.setEffectiveHour(1);
        marketingGroup.setAutoGroupFlag(1);
        Mockito.when(marketingGroupService.findByActivityCode(Mockito.any())).thenReturn(marketingGroup);
//        Mockito.when(flashSaleComponent.checkPerOrderCount(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(true);

        MarketingModel byActivityCode = new MarketingModel();
        byActivityCode.setSelectProductType("01");
        byActivityCode.setGroupCode("11");
        Mockito.when(marketingService.findByActivityCode(Mockito.any())).thenReturn(byActivityCode);
        Mockito.when(marketingGroupUserService.existLeaderByGroupCode(Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(true);

        List<FlashSaleOrderCalaResult> flashSaleOrderCalaResults = flashSaleComponent.handlerShoppingCart(param);
        Assert.assertEquals(1, flashSaleOrderCalaResults.size());

    }

    @Test
    public void calcFlashSaleOrderPrice3(){

        FlashSaleShoppingCartDto param = new FlashSaleShoppingCartDto();
        param.setDomainCode("1");
        param.setActivityType("06");
        param.setTenantCode("1");
        param.setUserCode("1");
        param.setActivityCode("6666666666");
        param.setMarketingGroupCode("11");

        List<ShoppingCartStore> cartStoreList = new ArrayList<>();
        ShoppingCartStore shoppingCartStore = new ShoppingCartStore();
        List<ShoppingCartItem> cartItemList = new ArrayList<>();
        ShoppingCartItem item = new ShoppingCartItem();
        item.setSkuCode("66");
        item.setProductCode("1");
        item.setQuantity(1);
        item.setProductPrice(new BigDecimal("123"));
        item.setSelectionFlag("01");
        cartItemList.add(item);
        shoppingCartStore.setCartItemList(cartItemList);

        cartStoreList.add(shoppingCartStore);

        param.setCartStoreList(cartStoreList);

        List<FlashSaleActivityInfoDTO> activity = new ArrayList<>();
        FlashSaleActivityInfoDTO dto = new FlashSaleActivityInfoDTO();
        dto.setActivityCode("66");
        activity.add(dto);

        FlashSaleSkuActivityPriceDTO skuActivityPriceDTO = new FlashSaleSkuActivityPriceDTO();
        List<FlashSaleActivityPriceDTO> activityPriceDTOS = new ArrayList<>();
        FlashSaleActivityPriceDTO priceDTO = new FlashSaleActivityPriceDTO();
        priceDTO.setActivityCode("6666666666");
        activityPriceDTOS.add(priceDTO);
        skuActivityPriceDTO.setActivity(activityPriceDTOS);

        Map<String, CacheFlashSaleModel> flashSaleCacheMap = new HashedMap<>();
        CacheFlashSaleModel flashSaleModel = new CacheFlashSaleModel();

        List<FlashSaleProductModel> productModels1 = new ArrayList<>();
        FlashSaleProductModel productModel = new FlashSaleProductModel();
        productModel.setSkuCode("66");
        productModel.setLimitFlag(1);
        productModel.setMaxPerUserFlag(1);
        productModel.setFlashPrice(new BigDecimal("12"));
        productModels1.add(productModel);
        flashSaleModel.setProducts(productModels1);
        flashSaleCacheMap.put("6666666666", flashSaleModel);



        List<FlashSaleProductModel> productModels = new ArrayList<>();
        FlashSaleProductModel productModel1 = new FlashSaleProductModel();
        productModel1.setSkuCode("66");
        productModel1.setLimitFlag(1);
        productModel1.setMaxPerUserFlag(1);
        productModel1.setFlashPrice(new BigDecimal("66"));
        productModels.add(productModel1);

        Map<String, CacheFlashSaleModel> activityCacheMap = new HashedMap<>();
        CacheFlashSaleModel cacheFlashSaleModel1 = new CacheFlashSaleModel();
        cacheFlashSaleModel1.setActivityCode("6666666666");
        cacheFlashSaleModel1.setActivityBegin("20211028");
        cacheFlashSaleModel1.setActivityEnd("20211029");

        cacheFlashSaleModel1.setProducts(productModels);
        activityCacheMap.put("test", cacheFlashSaleModel1);

        CacheFlashSaleModel cacheFlashSaleModel = new CacheFlashSaleModel();
        cacheFlashSaleModel.setActivityCode("test");
        cacheFlashSaleModel.setActivityBegin("20211028");
        cacheFlashSaleModel.setActivityEnd("20211029");
        cacheFlashSaleModel.setProducts(productModels);


        List<MarketingLanguageModel> languages11 = new ArrayList<>();

        MarketingLanguageModel marketingLanguageModel11 = new MarketingLanguageModel();
        marketingLanguageModel11.setLanguage("id-ID");
        languages11.add(marketingLanguageModel11);

        MarketingLanguageModel marketingLanguageModel2 = new MarketingLanguageModel();
        marketingLanguageModel2.setLanguage("zh-CN");
        languages11.add(marketingLanguageModel2);

        flashSaleModel.setLanguages(languages11);

        activityCacheMap.put("6666666666", cacheFlashSaleModel);

        Mockito.when(marketingCacheComponent.getFlashSaleCacheMap(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(activityCacheMap);

        Mockito.when(marketingCacheComponent.filterActivityByProduct(Mockito.any(), Mockito.any(), Mockito.any(),Mockito.any() )).thenReturn(flashSaleCacheMap);

        FlashSaleProductModel flashSaleProductModel = new FlashSaleProductModel();
        flashSaleProductModel.setMaxPerUser(12);
        flashSaleProductModel.setSkuInventory(34);
        flashSaleProductModel.setMaxPerUserFlag(0);
        flashSaleProductModel.setGroupLeaderPrice(new BigDecimal(100));
        Mockito.when(flashSaleProductService.findByActivityAndSku(Mockito.any(), Mockito.any())).thenReturn(flashSaleProductModel);
        findByActivityCode();
        MarketingGroupMode marketingGroup = new MarketingGroupMode();
        marketingGroup.setAutoGroupFlag(1);
        marketingGroup.setEffectiveHour(1);
        Mockito.when(marketingGroupService.findByActivityCode(Mockito.any())).thenReturn(marketingGroup);
//        Mockito.when(flashSaleComponent.checkPerOrderCount(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(true);
        MarketingGroupUserMode leader = new MarketingGroupUserMode();
        Date date = DateUtil.addDay(1);
        String format = DateUtil.format(date, DateUtil.FORMAT_YYYYMMDDHHMISS_14);
        leader.setEffectiveTime(format);
        Mockito.when(marketingGroupUserService.findGroupLeaderUserByMarketingGroupCode(Mockito.any(),Mockito.any())).thenReturn(leader);
        Mockito.when(marketingGroupUserService.existLeaderByGroupCode(Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(false);

        List<FlashSaleOrderCalaResult> flashSaleOrderCalaResults = flashSaleComponent.handlerShoppingCart(param);
        Assert.assertEquals(1, flashSaleOrderCalaResults.size());

    }

    @Test
    public void calcFlashSaleOrderPrice8(){

        FlashSaleShoppingCartDto param = new FlashSaleShoppingCartDto();
        param.setDomainCode("1");
        param.setActivityType("06");
        param.setTenantCode("1");
        param.setUserCode("1");
        param.setActivityCode("6666666666");
        param.setMarketingGroupCode("11");

        List<ShoppingCartStore> cartStoreList = new ArrayList<>();
        ShoppingCartStore shoppingCartStore = new ShoppingCartStore();
        List<ShoppingCartItem> cartItemList = new ArrayList<>();
        ShoppingCartItem item = new ShoppingCartItem();
        item.setSkuCode("66");
        item.setProductCode("1");
        item.setQuantity(1);
        item.setProductPrice(new BigDecimal("123"));
        item.setSelectionFlag("01");
        cartItemList.add(item);
        shoppingCartStore.setCartItemList(cartItemList);

        cartStoreList.add(shoppingCartStore);

        param.setCartStoreList(cartStoreList);

        List<FlashSaleActivityInfoDTO> activity = new ArrayList<>();
        FlashSaleActivityInfoDTO dto = new FlashSaleActivityInfoDTO();
        dto.setActivityCode("66");
        activity.add(dto);

        FlashSaleSkuActivityPriceDTO skuActivityPriceDTO = new FlashSaleSkuActivityPriceDTO();
        List<FlashSaleActivityPriceDTO> activityPriceDTOS = new ArrayList<>();
        FlashSaleActivityPriceDTO priceDTO = new FlashSaleActivityPriceDTO();
        priceDTO.setActivityCode("6666666666");
        activityPriceDTOS.add(priceDTO);
        skuActivityPriceDTO.setActivity(activityPriceDTOS);

        Map<String, CacheFlashSaleModel> flashSaleCacheMap = new HashedMap<>();
        CacheFlashSaleModel flashSaleModel = new CacheFlashSaleModel();

        List<FlashSaleProductModel> productModels1 = new ArrayList<>();
        FlashSaleProductModel productModel = new FlashSaleProductModel();
        productModel.setSkuCode("66");
        productModel.setLimitFlag(1);
        productModel.setMaxPerUserFlag(1);
        productModel.setFlashPrice(new BigDecimal("12"));
        productModels1.add(productModel);
        flashSaleModel.setProducts(productModels1);
        flashSaleCacheMap.put("6666666666", flashSaleModel);



        List<FlashSaleProductModel> productModels = new ArrayList<>();
        FlashSaleProductModel productModel1 = new FlashSaleProductModel();
        productModel1.setSkuCode("66");
        productModel1.setLimitFlag(1);
        productModel1.setMaxPerUserFlag(1);
        productModel1.setFlashPrice(new BigDecimal("66"));
        productModels.add(productModel1);

        Map<String, CacheFlashSaleModel> activityCacheMap = new HashedMap<>();
        CacheFlashSaleModel cacheFlashSaleModel1 = new CacheFlashSaleModel();
        cacheFlashSaleModel1.setActivityCode("6666666666");
        cacheFlashSaleModel1.setActivityBegin("20211028");
        cacheFlashSaleModel1.setActivityEnd("20211029");

        cacheFlashSaleModel1.setProducts(productModels);


        List<MarketingLanguageModel> languages11 = new ArrayList<>();

        MarketingLanguageModel marketingLanguageModel11 = new MarketingLanguageModel();
        marketingLanguageModel11.setLanguage("id-ID");
        languages11.add(marketingLanguageModel11);

        MarketingLanguageModel marketingLanguageModel2 = new MarketingLanguageModel();
        marketingLanguageModel2.setLanguage("zh-CN");
        languages11.add(marketingLanguageModel2);

        flashSaleModel.setLanguages(languages11);

        activityCacheMap.put("test", cacheFlashSaleModel1);

        CacheFlashSaleModel cacheFlashSaleModel = new CacheFlashSaleModel();
        cacheFlashSaleModel.setActivityCode("test");
        cacheFlashSaleModel.setActivityBegin("20211028");
        cacheFlashSaleModel.setActivityEnd("20211029");
        cacheFlashSaleModel.setProducts(productModels);
        activityCacheMap.put("6666666666", cacheFlashSaleModel);

        Mockito.when(marketingCacheComponent.getFlashSaleCacheMap(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(activityCacheMap);

        Mockito.when(marketingCacheComponent.filterActivityByProduct(Mockito.any(), Mockito.any(), Mockito.any(),Mockito.any() )).thenReturn(flashSaleCacheMap);

        FlashSaleProductModel flashSaleProductModel = new FlashSaleProductModel();
        flashSaleProductModel.setMaxPerUser(12);
        flashSaleProductModel.setSkuInventory(34);
        flashSaleProductModel.setMaxPerUserFlag(0);
        flashSaleProductModel.setGroupLeaderPrice(new BigDecimal(100));
        Mockito.when(flashSaleProductService.findByActivityAndSku(Mockito.any(), Mockito.any())).thenReturn(flashSaleProductModel);
        MarketingGroupMode marketingGroup = new MarketingGroupMode();
        marketingGroup.setAutoGroupFlag(1);
        marketingGroup.setEffectiveHour(1);
        findByActivityCode();
        Mockito.when(marketingGroupService.findByActivityCode(Mockito.any())).thenReturn(marketingGroup);
//        Mockito.when(flashSaleComponent.checkPerOrderCount(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(true);
        MarketingGroupUserMode leader = new MarketingGroupUserMode();
        Date date = DateUtil.addDay(1);
        String format = DateUtil.format(date, DateUtil.FORMAT_YYYYMMDDHHMISS_14);
        leader.setEffectiveTime(format);
        Mockito.when(marketingGroupUserService.findGroupLeaderUserByMarketingGroupCode(Mockito.any(),Mockito.any())).thenReturn(leader);
        Mockito.when(marketingGroupUserService.existLeaderByGroupCode(Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(false);

        List<FlashSaleOrderCalaResult> flashSaleOrderCalaResults = flashSaleComponent.handlerShoppingCart(param);        Assert.assertEquals(1, flashSaleOrderCalaResults.size());

    }

    @Test(expected = PromotionException.class)
    public void calcFlashSaleOrderPrice4(){

        FlashSaleShoppingCartDto param = new FlashSaleShoppingCartDto();
        param.setDomainCode("1");
        param.setActivityType("06");
        param.setTenantCode("1");
        param.setUserCode("1");
        param.setActivityCode("6666666666");
        param.setMarketingGroupCode("11");

        List<ShoppingCartStore> cartStoreList = new ArrayList<>();
        ShoppingCartStore shoppingCartStore = new ShoppingCartStore();
        List<ShoppingCartItem> cartItemList = new ArrayList<>();
        ShoppingCartItem item1 = new ShoppingCartItem();
        item1.setSkuCode("66");
        item1.setProductCode("1");
        item1.setQuantity(1);
        item1.setProductPrice(new BigDecimal("123"));
        item1.setSelectionFlag("02");
        cartItemList.add(item1);

        ShoppingCartItem item = new ShoppingCartItem();
        item.setSkuCode("66");
        item.setProductCode("1");
        item.setQuantity(1);
        item.setProductPrice(new BigDecimal("123"));
        item.setSelectionFlag("02");
        cartItemList.add(item);
        shoppingCartStore.setCartItemList(cartItemList);

        cartStoreList.add(shoppingCartStore);

        param.setCartStoreList(cartStoreList);

        List<FlashSaleActivityInfoDTO> activity = new ArrayList<>();
        FlashSaleActivityInfoDTO dto = new FlashSaleActivityInfoDTO();
        dto.setActivityCode("66");
        activity.add(dto);

        FlashSaleSkuActivityPriceDTO skuActivityPriceDTO = new FlashSaleSkuActivityPriceDTO();
        List<FlashSaleActivityPriceDTO> activityPriceDTOS = new ArrayList<>();
        FlashSaleActivityPriceDTO priceDTO = new FlashSaleActivityPriceDTO();
        priceDTO.setActivityCode("6666666666");
        activityPriceDTOS.add(priceDTO);
        skuActivityPriceDTO.setActivity(activityPriceDTOS);

        Map<String, CacheFlashSaleModel> flashSaleCacheMap = new HashedMap<>();
        CacheFlashSaleModel flashSaleModel = new CacheFlashSaleModel();

        List<FlashSaleProductModel> productModels1 = new ArrayList<>();
        FlashSaleProductModel productModel = new FlashSaleProductModel();
        productModel.setSkuCode("66");
        productModel.setLimitFlag(1);
        productModel.setMaxPerUserFlag(1);
        productModel.setFlashPrice(new BigDecimal("12"));
        productModels1.add(productModel);
        flashSaleModel.setProducts(productModels1);
        flashSaleModel.setActivityBegin("20211028");
        flashSaleModel.setActivityEnd("20211029");
        flashSaleCacheMap.put("6666666666", flashSaleModel);



        List<FlashSaleProductModel> productModels = new ArrayList<>();
        FlashSaleProductModel productModel1 = new FlashSaleProductModel();
        productModel1.setSkuCode("66");
        productModel1.setLimitFlag(1);
        productModel1.setMaxPerUserFlag(1);
        productModel1.setFlashPrice(new BigDecimal("66"));
        productModels.add(productModel1);

        Map<String, CacheFlashSaleModel> activityCacheMap = new HashedMap<>();
        CacheFlashSaleModel cacheFlashSaleModel1 = new CacheFlashSaleModel();
        cacheFlashSaleModel1.setActivityCode("6666666666");
        cacheFlashSaleModel1.setActivityBegin("20211028");
        cacheFlashSaleModel1.setActivityEnd("20211029");

        cacheFlashSaleModel1.setProducts(productModels);
        activityCacheMap.put("test", cacheFlashSaleModel1);

        CacheFlashSaleModel cacheFlashSaleModel = new CacheFlashSaleModel();
        cacheFlashSaleModel.setActivityCode("test");
        cacheFlashSaleModel.setActivityBegin("20211028");
        cacheFlashSaleModel.setActivityEnd("20211029");
        cacheFlashSaleModel.setProducts(productModels);
        activityCacheMap.put("6666666666", cacheFlashSaleModel);

        Mockito.when(marketingCacheComponent.getFlashSaleCacheMap(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(activityCacheMap);

        Mockito.when(marketingCacheComponent.filterActivityByProduct(Mockito.any(), Mockito.any(), Mockito.any(),Mockito.any() )).thenReturn(flashSaleCacheMap);

        FlashSaleProductModel flashSaleProductModel = new FlashSaleProductModel();
        flashSaleProductModel.setMaxPerUser(12);
        flashSaleProductModel.setSkuInventory(34);
        flashSaleProductModel.setMaxPerUserFlag(0);
        flashSaleProductModel.setGroupLeaderPrice(new BigDecimal(100));
        Mockito.when(flashSaleProductService.findByActivityAndSku(Mockito.any(), Mockito.any())).thenReturn(flashSaleProductModel);
        MarketingGroupMode marketingGroup = new MarketingGroupMode();
        marketingGroup.setAutoGroupFlag(1);
        marketingGroup.setEffectiveHour(1);
        Mockito.when(marketingGroupService.findByActivityCode(Mockito.any())).thenReturn(marketingGroup);
        MarketingModel byActivityCode = new MarketingModel();
        byActivityCode.setSelectProductType("01");
        Mockito.when(marketingService.findByActivityCode(Mockito.any())).thenReturn(byActivityCode);
        Mockito.when(marketingGroupUserService.existLeaderByGroupCode(Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(false);

        List<FlashSaleOrderCalaResult> flashSaleOrderCalaResults = flashSaleComponent.handlerShoppingCart(param);        Assert.assertEquals(1, flashSaleOrderCalaResults.size());

    }


    @Test
    public void calcFlashSaleOrderPrice7(){

        FlashSaleShoppingCartDto param = new FlashSaleShoppingCartDto();
        param.setDomainCode("1");
        param.setActivityType("06");
        param.setTenantCode("1");
        param.setUserCode("1");
        param.setActivityCode("6666666666");
        param.setMarketingGroupCode("11");

        List<ShoppingCartStore> cartStoreList = new ArrayList<>();
        ShoppingCartStore shoppingCartStore = new ShoppingCartStore();
        List<ShoppingCartItem> cartItemList = new ArrayList<>();
        ShoppingCartItem item1 = new ShoppingCartItem();
        item1.setSkuCode("66");
        item1.setProductCode("1");
        item1.setQuantity(1);
        item1.setProductPrice(new BigDecimal("123"));
        item1.setSelectionFlag("02");
        cartItemList.add(item1);

        ShoppingCartItem item = new ShoppingCartItem();
        item.setSkuCode("66");
        item.setProductCode("1");
        item.setQuantity(1);
        item.setProductPrice(new BigDecimal("123"));
        item.setSelectionFlag("02");
        cartItemList.add(item);
        shoppingCartStore.setCartItemList(cartItemList);

        cartStoreList.add(shoppingCartStore);

        param.setCartStoreList(cartStoreList);

        List<FlashSaleActivityInfoDTO> activity = new ArrayList<>();
        FlashSaleActivityInfoDTO dto = new FlashSaleActivityInfoDTO();
        dto.setActivityCode("66");
        activity.add(dto);

        FlashSaleSkuActivityPriceDTO skuActivityPriceDTO = new FlashSaleSkuActivityPriceDTO();
        List<FlashSaleActivityPriceDTO> activityPriceDTOS = new ArrayList<>();
        FlashSaleActivityPriceDTO priceDTO = new FlashSaleActivityPriceDTO();
        priceDTO.setActivityCode("6666666666");
        activityPriceDTOS.add(priceDTO);
        skuActivityPriceDTO.setActivity(activityPriceDTOS);

        Map<String, CacheFlashSaleModel> flashSaleCacheMap = new HashedMap<>();
        CacheFlashSaleModel flashSaleModel = new CacheFlashSaleModel();

        List<FlashSaleProductModel> productModels1 = new ArrayList<>();
        FlashSaleProductModel productModel = new FlashSaleProductModel();
        productModel.setSkuCode("66");
        productModel.setLimitFlag(1);
        productModel.setMaxPerUserFlag(1);
        productModel.setFlashPrice(new BigDecimal("12"));
        productModels1.add(productModel);
        flashSaleModel.setProducts(productModels1);
        flashSaleModel.setActivityBegin("20211028");
        flashSaleModel.setActivityEnd("20211029");
        flashSaleCacheMap.put("6666666666", flashSaleModel);



        List<FlashSaleProductModel> productModels = new ArrayList<>();
        FlashSaleProductModel productModel1 = new FlashSaleProductModel();
        productModel1.setSkuCode("66");
        productModel1.setLimitFlag(1);
        productModel1.setMaxPerUserFlag(1);
        productModel1.setFlashPrice(new BigDecimal("66"));
        productModels.add(productModel1);

        Map<String, CacheFlashSaleModel> activityCacheMap = new HashedMap<>();
        CacheFlashSaleModel cacheFlashSaleModel1 = new CacheFlashSaleModel();
        cacheFlashSaleModel1.setActivityCode("6666666666");
        cacheFlashSaleModel1.setActivityBegin("20211028");
        cacheFlashSaleModel1.setActivityEnd("20211029");

        cacheFlashSaleModel1.setProducts(productModels);


        List<MarketingLanguageModel> languages11 = new ArrayList<>();

        MarketingLanguageModel marketingLanguageModel11 = new MarketingLanguageModel();
        marketingLanguageModel11.setLanguage("id-ID");
        languages11.add(marketingLanguageModel11);

        MarketingLanguageModel marketingLanguageModel2 = new MarketingLanguageModel();
        marketingLanguageModel2.setLanguage("zh-CN");
        languages11.add(marketingLanguageModel2);

        flashSaleModel.setLanguages(languages11);

        activityCacheMap.put("test", cacheFlashSaleModel1);

        CacheFlashSaleModel cacheFlashSaleModel = new CacheFlashSaleModel();
        cacheFlashSaleModel.setActivityCode("test");
        cacheFlashSaleModel.setActivityBegin("20211028");
        cacheFlashSaleModel.setActivityEnd("20211029");
        cacheFlashSaleModel.setProducts(productModels);
        activityCacheMap.put("6666666666", cacheFlashSaleModel);

        Mockito.when(marketingCacheComponent.getFlashSaleCacheMap(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(activityCacheMap);

        Mockito.when(marketingCacheComponent.filterActivityByProduct(Mockito.any(), Mockito.any(), Mockito.any(),Mockito.any() )).thenReturn(flashSaleCacheMap);

        FlashSaleProductModel flashSaleProductModel = new FlashSaleProductModel();
        flashSaleProductModel.setMaxPerUser(12);
        flashSaleProductModel.setSkuInventory(34);
        flashSaleProductModel.setMaxPerUserFlag(0);
        flashSaleProductModel.setGroupLeaderPrice(new BigDecimal(100));
        Mockito.when(flashSaleProductService.findByActivityAndSku(Mockito.any(), Mockito.any())).thenReturn(flashSaleProductModel);
        MarketingGroupMode marketingGroup = new MarketingGroupMode();
        marketingGroup.setAutoGroupFlag(1);
        marketingGroup.setEffectiveHour(1);
        Mockito.when(marketingGroupService.findByActivityCode(Mockito.any())).thenReturn(marketingGroup);
//        Mockito.when(flashSaleComponent.checkPerOrderCount(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(true);
        findByActivityCode();

        MarketingModel byActivityCode = new MarketingModel();
        byActivityCode.setSelectProductType("01");
        byActivityCode.setGroupCode("11");
        Mockito.when(marketingService.findByActivityCode(Mockito.any())).thenReturn(byActivityCode);
        MarketingGroupUserMode leader = new MarketingGroupUserMode();

        Date date = DateUtil.addDay(1);
        String format = DateUtil.format(date, DateUtil.FORMAT_YYYYMMDDHHMISS_14);
        leader.setEffectiveTime(format);
        Mockito.when(marketingGroupUserService.findGroupLeaderUserByMarketingGroupCode(Mockito.any(),Mockito.any())).thenReturn(leader);

        Mockito.when(marketingGroupUserService.existLeaderByGroupCode(Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(false);

        List<FlashSaleOrderCalaResult> flashSaleOrderCalaResults = flashSaleComponent.handlerShoppingCart(param);        Assert.assertEquals(1, flashSaleOrderCalaResults.size());

    }


    @Test
    public void calcFlashSaleOrderPrice5(){

        FlashSaleShoppingCartDto param = new FlashSaleShoppingCartDto();
        param.setDomainCode("1");
        param.setActivityType("06");
        param.setTenantCode("1");
        param.setUserCode("1");
        param.setActivityCode("6666666666");
        param.setMarketingGroupCode("11");

        List<ShoppingCartStore> cartStoreList = new ArrayList<>();
        ShoppingCartStore shoppingCartStore = new ShoppingCartStore();
        List<ShoppingCartItem> cartItemList = new ArrayList<>();
        ShoppingCartItem item1 = new ShoppingCartItem();
        item1.setSkuCode("66");
        item1.setProductCode("1");
        item1.setQuantity(3);
        item1.setProductPrice(new BigDecimal("123"));
        item1.setSelectionFlag("02");
        cartItemList.add(item1);

        ShoppingCartItem item = new ShoppingCartItem();
        item.setSkuCode("66");
        item.setProductCode("1");
        item.setQuantity(3);
        item.setProductPrice(new BigDecimal("123"));
        item.setSelectionFlag("02");
        cartItemList.add(item);
        shoppingCartStore.setCartItemList(cartItemList);

        cartStoreList.add(shoppingCartStore);

        param.setCartStoreList(cartStoreList);

        List<FlashSaleActivityInfoDTO> activity = new ArrayList<>();
        FlashSaleActivityInfoDTO dto = new FlashSaleActivityInfoDTO();
        dto.setActivityCode("66");
        activity.add(dto);

        FlashSaleSkuActivityPriceDTO skuActivityPriceDTO = new FlashSaleSkuActivityPriceDTO();
        List<FlashSaleActivityPriceDTO> activityPriceDTOS = new ArrayList<>();
        FlashSaleActivityPriceDTO priceDTO = new FlashSaleActivityPriceDTO();
        priceDTO.setActivityCode("6666666666");

        activityPriceDTOS.add(priceDTO);
        skuActivityPriceDTO.setActivity(activityPriceDTOS);

        Map<String, CacheFlashSaleModel> flashSaleCacheMap = new HashedMap<>();
        CacheFlashSaleModel flashSaleModel = new CacheFlashSaleModel();
        flashSaleModel.setActivityBegin("20210225181202");
        flashSaleModel.setActivityEnd("20250225181202");
        List<FlashSaleProductModel> productModels1 = new ArrayList<>();
        FlashSaleProductModel productModel = new FlashSaleProductModel();
        productModel.setSkuCode("66");
        productModel.setLimitFlag(1);
        productModel.setMaxPerUserFlag(1);
        productModel.setFlashPrice(new BigDecimal("12"));
        productModel.setSkuQuota(1);
        productModels1.add(productModel);
        flashSaleModel.setProducts(productModels1);
        flashSaleCacheMap.put("6666666666", flashSaleModel);


        List<FlashSaleProductModel> productModels = new ArrayList<>();
        FlashSaleProductModel productModel1 = new FlashSaleProductModel();
        productModel1.setSkuCode("66");
        productModel1.setLimitFlag(1);
        productModel1.setMaxPerUserFlag(1);
        productModel1.setMaxPerUser(1);
        productModel1.setFlashPrice(new BigDecimal("66"));
        productModels.add(productModel1);

        Map<String, CacheFlashSaleModel> activityCacheMap = new HashedMap<>();
        CacheFlashSaleModel cacheFlashSaleModel1 = new CacheFlashSaleModel();
        cacheFlashSaleModel1.setActivityCode("6666666666");

        cacheFlashSaleModel1.setActivityBegin("20210225181202");
        cacheFlashSaleModel1.setActivityEnd("20250225181202");
        cacheFlashSaleModel1.setProducts(productModels);
        activityCacheMap.put("test", cacheFlashSaleModel1);

        CacheFlashSaleModel cacheFlashSaleModel = new CacheFlashSaleModel();
        cacheFlashSaleModel.setActivityCode("test");
        cacheFlashSaleModel.setActivityBegin("20210225181202");
        cacheFlashSaleModel.setActivityEnd("20250225181202");
        cacheFlashSaleModel.setProducts(productModels);
        activityCacheMap.put("6666666666", cacheFlashSaleModel);

        Mockito.when(marketingCacheComponent.getFlashSaleCacheMap(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(activityCacheMap);

        Mockito.when(marketingCacheComponent.filterActivityByProduct(Mockito.any(), Mockito.any(), Mockito.any(),Mockito.any() )).thenReturn(flashSaleCacheMap);
        MarketingModel byActivityCode = new MarketingModel();
        byActivityCode.setSelectProductType("02");
        Mockito.when(marketingService.findByActivityCode(Mockito.any())).thenReturn(byActivityCode);
        FlashSaleProductModel flashSaleProductModel = new FlashSaleProductModel();
        flashSaleProductModel.setMaxPerUser(1);
        flashSaleProductModel.setSkuInventory(34);
        flashSaleProductModel.setMaxPerUserFlag(1);
        flashSaleProductModel.setGroupLeaderPrice(new BigDecimal(100));
        Mockito.when(flashSaleProductService.findByActivityAndSku(Mockito.any(), Mockito.any())).thenReturn(flashSaleProductModel);
        MarketingGroupMode marketingGroup = new MarketingGroupMode();
        marketingGroup.setAutoGroupFlag(1);
        marketingGroup.setEffectiveHour(1);
        Mockito.when(marketingGroupService.findByActivityCode(Mockito.any())).thenReturn(marketingGroup);
        byActivityCode.setSelectProductType("01");
        Mockito.when(marketingService.findByActivityCode(Mockito.any())).thenReturn(byActivityCode);
        Mockito.when(marketingGroupUserService.existLeaderByGroupCode(Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(false);

        List<FlashSaleOrderCalaResult> flashSaleOrderCalaResults = flashSaleComponent.handlerShoppingCart(param);        Assert.assertEquals(0, flashSaleOrderCalaResults.size());

    }

    @Test
    public void calcFlashSaleOrderPrice(){
        FlashSaleShoppingCartDto param = new FlashSaleShoppingCartDto();
        param.setDomainCode("1");
        param.setTenantCode("1");
        param.setUserCode("1");
        param.setActivityCode("1");
        List<ShoppingCartStore> cartStoreList = new ArrayList<>();
        ShoppingCartStore shoppingCartStore = new ShoppingCartStore();
        List<ShoppingCartItem> cartItemList = new ArrayList<>();
        ShoppingCartItem item = new ShoppingCartItem();
        item.setSkuCode("1");
        item.setProductCode("1");
        item.setQuantity(1);
        item.setProductPrice(new BigDecimal("123"));
        item.setSelectionFlag("01");
        cartItemList.add(item);
        shoppingCartStore.setCartItemList(cartItemList);
        cartStoreList.add(shoppingCartStore);
        param.setCartStoreList(cartStoreList);
        List<FlashSaleActivityInfoDTO> activity = new ArrayList<>();
        FlashSaleActivityInfoDTO dto = new FlashSaleActivityInfoDTO();
        dto.setActivityCode("1");
        activity.add(dto);

        Map<String, CacheFlashSaleModel> flashSaleCacheMap = new HashedMap<>();
        CacheFlashSaleModel flashSaleModel = new CacheFlashSaleModel();
        List<FlashSaleProductModel> productModels = new ArrayList<>();
        FlashSaleProductModel productModel = new FlashSaleProductModel();
        productModel.setSkuCode("1");
        productModel.setFlashPrice(new BigDecimal("12"));
        productModels.add(productModel);
        flashSaleModel.setProducts(productModels);
        List<MarketingLanguageModel> languages11 = new ArrayList<>();

        MarketingLanguageModel marketingLanguageModel11 = new MarketingLanguageModel();
        marketingLanguageModel11.setLanguage("id-ID");
        languages11.add(marketingLanguageModel11);

        MarketingLanguageModel marketingLanguageModel2 = new MarketingLanguageModel();
        marketingLanguageModel2.setLanguage("zh-CN");
        languages11.add(marketingLanguageModel2);

        flashSaleModel.setLanguages(languages11);
        flashSaleCacheMap.put("1", flashSaleModel);

        FlashSaleSkuActivityPriceDTO skuActivityPriceDTO = new FlashSaleSkuActivityPriceDTO();
        List<FlashSaleActivityPriceDTO> activityPriceDTOS = new ArrayList<>();
        FlashSaleActivityPriceDTO priceDTO = new FlashSaleActivityPriceDTO();
        priceDTO.setActivityCode("1");
        activityPriceDTOS.add(priceDTO);
        skuActivityPriceDTO.setActivity(activityPriceDTOS);

        Mockito.when(marketingCacheComponent.filterActivityByProduct(Mockito.any(), Mockito.any(), Mockito.any(),Mockito.any() )).thenReturn(flashSaleCacheMap);
//        Mockito.when(flashSaleComponent.getActivitiesByProduct(Mockito.any())).thenReturn(activity);
//        Mockito.when(flashSaleComponent.activitySkuPrice(Mockito.any(), Mockito.any())).thenReturn(skuActivityPriceDTO);
//        FlashSaleProductModel currentProductModel = new FlashSaleProductModel();
//        currentProductModel.setMaxPerUser(100);
//        currentProductModel.setSkuInventory(1000);
//        Mockito.when(flashSaleComponent.checkQuota(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(true);
        Map<String, CacheFlashSaleModel> activityCacheMap = new HashedMap<>();
        CacheFlashSaleModel cacheFlashSaleModel = new CacheFlashSaleModel();
        cacheFlashSaleModel.setActivityCode("test");
        cacheFlashSaleModel.setActivityBegin("20211028");
        cacheFlashSaleModel.setActivityEnd("20211029");
        activityCacheMap.put("test", cacheFlashSaleModel);
        Mockito.when(marketingCacheComponent.getFlashSaleCacheMap(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(activityCacheMap);
        findByActivityCode();
        FlashSaleProductModel flashSaleProductModel = new FlashSaleProductModel();
        flashSaleProductModel.setMaxPerUser(12);
        flashSaleProductModel.setSkuInventory(34);
        Mockito.when(flashSaleProductService.findByActivityAndSku(Mockito.any(), Mockito.any())).thenReturn(flashSaleProductModel);

        MarketingModel byActivityCode = new MarketingModel();
        byActivityCode.setSelectProductType("01");
        Mockito.when(marketingService.findByActivityCode(Mockito.any())).thenReturn(byActivityCode);

        List<FlashSaleOrderCalaResult> flashSaleOrderCalaResults = flashSaleComponent.handlerShoppingCart(param);
        Assert.assertEquals(1, flashSaleOrderCalaResults.size());
    }


    @Test
    public void testPromotionActivityCalculation_withEmptyResultList() {
        FlashSaleShoppingCartDto param = new FlashSaleShoppingCartDto();
        param.setDomainCode("1");
        param.setTenantCode("1");
        param.setUserCode("1");
        param.setActivityCode("1");
        List<ShoppingCartStore> cartStoreList = new ArrayList<>();
        ShoppingCartStore shoppingCartStore = new ShoppingCartStore();
        List<ShoppingCartItem> cartItemList = new ArrayList<>();
        ShoppingCartItem item = new ShoppingCartItem();
        item.setSkuCode("1");
        item.setProductCode("1");
        item.setQuantity(1);
        item.setProductPrice(new BigDecimal("123"));
        item.setSelectionFlag("01");
        cartItemList.add(item);
        shoppingCartStore.setCartItemList(cartItemList);
        cartStoreList.add(shoppingCartStore);
        param.setCartStoreList(cartStoreList);
        List<FlashSaleActivityInfoDTO> activity = new ArrayList<>();
        FlashSaleActivityInfoDTO dto = new FlashSaleActivityInfoDTO();
        dto.setActivityCode("1");
        activity.add(dto);

        CacheFlashSaleModel model = new CacheFlashSaleModel();


        List<FlashSaleOrderCalaResult> list = new ArrayList<>();
        flashSaleComponent.promotionActivityCalculation(list, param, model);

        Assert.assertTrue(list.isEmpty());
    }

    @Test
    public void testPromotionActivityCalculation_withNonEmptyResultList() {
        FlashSaleShoppingCartDto param = new FlashSaleShoppingCartDto();
        param.setDomainCode("1");
        param.setTenantCode("1");
        param.setUserCode("1");
        param.setActivityCode("1");
        List<ShoppingCartStore> cartStoreList = new ArrayList<>();
        ShoppingCartStore shoppingCartStore = new ShoppingCartStore();
        List<ShoppingCartItem> cartItemList = new ArrayList<>();
        ShoppingCartItem item = new ShoppingCartItem();
        item.setSkuCode("1");
        item.setProductCode("1");
        item.setQuantity(1);
        item.setProductPrice(new BigDecimal("123"));
        item.setSelectionFlag("01");
        cartItemList.add(item);
        shoppingCartStore.setCartItemList(cartItemList);
        cartStoreList.add(shoppingCartStore);
        param.setCartStoreList(cartStoreList);
        List<FlashSaleActivityInfoDTO> activity = new ArrayList<>();
        FlashSaleActivityInfoDTO dto = new FlashSaleActivityInfoDTO();
        dto.setActivityCode("1");
        activity.add(dto);

        Map<String, CacheFlashSaleModel> flashSaleCacheMap = new HashedMap<>();
        CacheFlashSaleModel flashSaleModel = new CacheFlashSaleModel();
        List<FlashSaleProductModel> productModels = new ArrayList<>();
        FlashSaleProductModel productModel = new FlashSaleProductModel();
        productModel.setSkuCode("1");
        productModel.setFlashPrice(new BigDecimal("12"));
        productModels.add(productModel);
        flashSaleModel.setProducts(productModels);
        flashSaleCacheMap.put("1", flashSaleModel);

        FlashSaleSkuActivityPriceDTO skuActivityPriceDTO = new FlashSaleSkuActivityPriceDTO();
        List<FlashSaleActivityPriceDTO> activityPriceDTOS = new ArrayList<>();
        FlashSaleActivityPriceDTO priceDTO = new FlashSaleActivityPriceDTO();
        priceDTO.setActivityCode("1");
        activityPriceDTOS.add(priceDTO);
        skuActivityPriceDTO.setActivity(activityPriceDTOS);
        List<CalcShoppingCartResult> mockResultList = Arrays.asList(new CalcShoppingCartResult());
        CacheFlashSaleModel cacheFlashSaleModel = new CacheFlashSaleModel();
        List<FlashSaleOrderCalaResult> list = new ArrayList<>();
        flashSaleComponent.promotionActivityCalculation(list, param, cacheFlashSaleModel);

        Assert.assertTrue(list.isEmpty());
    }

    @Test
    public void testProcessShoppingCart_shouldReturnEarly_dueToEmptyRuleCacheMap() {


        List<FlashSaleOrderCalaResult> list = new ArrayList<>();

        FlashSaleShoppingCartDto param = new FlashSaleShoppingCartDto();
        param.setDomainCode("1");
        param.setTenantCode("1");
        param.setUserCode("1");
        param.setActivityCode("1");
        List<ShoppingCartStore> cartStoreList = new ArrayList<>();
        ShoppingCartStore shoppingCartStore = new ShoppingCartStore();
        List<ShoppingCartItem> cartItemList = new ArrayList<>();
        ShoppingCartItem item = new ShoppingCartItem();
        item.setSkuCode("1");
        item.setProductCode("1");
        item.setQuantity(1);
        item.setProductPrice(new BigDecimal("123"));
        item.setSelectionFlag("01");
        cartItemList.add(item);
        shoppingCartStore.setCartItemList(cartItemList);
        cartStoreList.add(shoppingCartStore);
        param.setCartStoreList(cartStoreList);
        List<FlashSaleActivityInfoDTO> activity = new ArrayList<>();
        FlashSaleActivityInfoDTO dto = new FlashSaleActivityInfoDTO();
        dto.setActivityCode("1");
        activity.add(dto);

        Map<String, CacheFlashSaleModel> flashSaleCacheMap = new HashedMap<>();
        CacheFlashSaleModel flashSaleModel = new CacheFlashSaleModel();
        List<FlashSaleProductModel> productModels = new ArrayList<>();
        FlashSaleProductModel productModel = new FlashSaleProductModel();
        productModel.setSkuCode("1");
        productModel.setFlashPrice(new BigDecimal("12"));
        productModels.add(productModel);
        flashSaleModel.setProducts(productModels);
        flashSaleCacheMap.put("1", flashSaleModel);
        ShoppingCartDTO cartDTO = new ShoppingCartDTO();
        cartDTO.setCouponCodes("someCode");
//        when(activityCacheDomain.getActivityCacheMap(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(Collections.emptyMap());
        CacheFlashSaleModel cacheFlashSaleModel = new CacheFlashSaleModel();
        List<CalcShoppingCartResult> results = flashSaleComponent.processShoppingCart(param, cacheFlashSaleModel, list);

        Assert.assertTrue(results.isEmpty());
    }
    @Test
    public void testCreateCalcShoppingCartParam() {
        // Arrange
        FlashSaleShoppingCartDto flashShoppingCart = new FlashSaleShoppingCartDto();
        flashShoppingCart.setDomainCode("D00001");
        flashShoppingCart.setTenantCode("T00001");
        flashShoppingCart.setLanguage("en");
        flashShoppingCart.setChannelCode(1);
        flashShoppingCart.setUserCode("member123");
        flashShoppingCart.setCouponCodes("coupon1,coupon2");
        flashShoppingCart.setPromotionTime("20230401000000");
        flashShoppingCart.setPostage(BigDecimal.valueOf(10.0));

        ShoppingCartItem cartItem = new ShoppingCartItem();
        cartItem.setSkuCode("skuCode");
        ShoppingCartStore cartStore = new ShoppingCartStore();
        cartStore.setCartItemList(Lists.newArrayList(cartItem));

        FlashSaleShoppingCartDto flashSaleShoppingCartDto = new FlashSaleShoppingCartDto();
        flashSaleShoppingCartDto.setDomainCode("domainCode");
        flashSaleShoppingCartDto.setTenantCode("tenantCode");
        flashSaleShoppingCartDto.setLanguage("language");
        flashSaleShoppingCartDto.setChannelCode(123);
        flashSaleShoppingCartDto.setUserCode("memberCode");
        flashSaleShoppingCartDto.setQualifications(new HashMap<>());
        flashSaleShoppingCartDto.setCouponCodes("");
        flashSaleShoppingCartDto.setCartStoreList(Lists.newArrayList(cartStore));
        flashSaleShoppingCartDto.setPostage(BigDecimal.ZERO);

        FlashSaleOrderCalaResult.ShoppingCartItem shoppingCartItem = new FlashSaleOrderCalaResult.ShoppingCartItem();
        shoppingCartItem.setSkuCode("skuCode");
        shoppingCartItem.setFlashPrice(BigDecimal.ONE);

        FlashSaleOrderCalaResult saleOrderCalaResult = new FlashSaleOrderCalaResult();
        saleOrderCalaResult.setActivityType(ActivityTypeEnum.FLASH_SALE.code());
        saleOrderCalaResult.setShoppingCartItems(Lists.newArrayList(shoppingCartItem));
        List<FlashSaleOrderCalaResult> list = new ArrayList<>();
        list.add(saleOrderCalaResult);

        CalcShoppingCartParam result = flashSaleComponent.createCalcShoppingCartParam(flashSaleShoppingCartDto, list);


    }


    @Test
    public void testActivityIncentiveLimited_0() {
        FlashSaleCreateOrderParam orderCommitDTO = new FlashSaleCreateOrderParam();
        orderCommitDTO.setActivityCode("activityCode");
        orderCommitDTO.setTenantCode("tenantCode");
        orderCommitDTO.setMemberCode("memberCode");
        TPromoIncentiveLimitedVO limitedVO = new TPromoIncentiveLimitedVO();
        limitedVO.setActivityCode("activityCode");
        limitedVO.setLimitationCode(LimitationCodeEnum.USER_TOTAL_COUNT.code());
        limitedVO.setLimitationValue(BigDecimal.ONE);
        List<TPromoIncentiveLimitedVO> limitedList = Collections.singletonList(limitedVO);
        when(limitedService.getLimitedListByActivityCode(Mockito.any())).thenReturn(limitedList);
        when(redisOpsHelper.lockRedisData(Mockito.anyList(), Mockito.any(), Mockito.anyLong(), Mockito.anyString())).thenReturn(true);
        flashSaleComponent.activityIncentiveLimited(orderCommitDTO, 1000L);
    }


    @Test
    public void testActivityIncentiveLimited_1() {
        FlashSaleCreateOrderParam orderCommitDTO = new FlashSaleCreateOrderParam();
        orderCommitDTO.setActivityCode("activityCode");
        orderCommitDTO.setTenantCode("tenantCode");
        orderCommitDTO.setMemberCode("memberCode");
        TPromoIncentiveLimitedVO limitedVO = new TPromoIncentiveLimitedVO();
        limitedVO.setActivityCode("activityCode");
        limitedVO.setLimitationCode(LimitationCodeEnum.ACTIVITY_TOTAL_COUNT.code());
        limitedVO.setLimitationValue(BigDecimal.ONE);
        List<TPromoIncentiveLimitedVO> limitedList = Collections.singletonList(limitedVO);
        when(limitedService.getLimitedListByActivityCode(Mockito.any())).thenReturn(limitedList);
        when(redisOpsHelper.lockRedisData(Mockito.anyList(), Mockito.any(), Mockito.anyLong(), Mockito.anyString())).thenReturn(true);
        flashSaleComponent.activityIncentiveLimited(orderCommitDTO, 1000L);
    }


    @Test
    public void testActivityIncentiveLimited_2() {
        FlashSaleCreateOrderParam orderCommitDTO = new FlashSaleCreateOrderParam();
        orderCommitDTO.setActivityCode("activityCode");
        orderCommitDTO.setTenantCode("tenantCode");
        orderCommitDTO.setMemberCode("memberCode");
        TPromoIncentiveLimitedVO limitedVO = new TPromoIncentiveLimitedVO();
        limitedVO.setActivityCode("activityCode");
        limitedVO.setLimitationCode(LimitationCodeEnum.USER_DAY_COUNT.code());
        limitedVO.setLimitationValue(BigDecimal.ONE);
        List<TPromoIncentiveLimitedVO> limitedList = Collections.singletonList(limitedVO);
        when(limitedService.getLimitedListByActivityCode(Mockito.any())).thenReturn(limitedList);
        when(redisOpsHelper.lockRedisData(Mockito.anyList(), Mockito.any(), Mockito.anyLong(), Mockito.anyString())).thenReturn(true);
        flashSaleComponent.activityIncentiveLimited(orderCommitDTO, 1000L);
    }

    @Test
    public void testActivityIncentiveLimited_3() {
        FlashSaleCreateOrderParam orderCommitDTO = new FlashSaleCreateOrderParam();
        orderCommitDTO.setActivityCode("activityCode");
        orderCommitDTO.setTenantCode("tenantCode");
        orderCommitDTO.setMemberCode("memberCode");
        TPromoIncentiveLimitedVO limitedVO = new TPromoIncentiveLimitedVO();
        limitedVO.setActivityCode("activityCode");
        limitedVO.setLimitationCode(LimitationCodeEnum.ACTIVITY_DAY_COUNT.code());
        limitedVO.setLimitationValue(BigDecimal.ONE);
        List<TPromoIncentiveLimitedVO> limitedList = Collections.singletonList(limitedVO);
        when(limitedService.getLimitedListByActivityCode(Mockito.any())).thenReturn(limitedList);
        //when(redisOpsHelper.lockRedisData(Mockito.anyList(), Mockito.any(), Mockito.anyLong(), Mockito.anyString())).thenReturn(true);
        flashSaleComponent.activityIncentiveLimited(orderCommitDTO, 1000L);
    }


    @Test
    public void updateShoppingCartItem(){
        FlashSaleOrderCalaResult.ShoppingCartItem shoppingCartItem =  new FlashSaleOrderCalaResult.ShoppingCartItem();
        List<CalcShoppingCartResult> resultList = new ArrayList<>();
        BigDecimal currentPromoRewardAmount = new BigDecimal("0");

        shoppingCartItem.setShoppingCartItemActivitys(new ArrayList<>());
        shoppingCartItem.setProductPrice(new BigDecimal("1"));
        shoppingCartItem.setPromoAmount(new BigDecimal("1"));
        CalcShoppingCartResult calcShoppingCartResult = new CalcShoppingCartResult();

        CalcShoppingCartResult.ShoppingCartItem item = new CalcShoppingCartResult.ShoppingCartItem();
        item.setShoppingCartItemActivitys(new ArrayList<>());
        item.setProductPrice(new BigDecimal("1"));
        item.setPromoAmount(new BigDecimal("1"));
        item.setSkuCode("1");
        ArrayList<CalcShoppingCartResult.ShoppingCartItem> list = new ArrayList<>();
        list.add(item);
        calcShoppingCartResult.setShoppingCartItems(list);
        resultList.add(calcShoppingCartResult);

        flashSaleComponent.updateShoppingCartItem(shoppingCartItem,resultList,currentPromoRewardAmount);


    }

    @Test
    public void updateFlashSaleOrderResults(){

        FlashSaleOrderCalaResult.ShoppingCartItem shoppingCartItem =  new FlashSaleOrderCalaResult.ShoppingCartItem();
        List<CalcShoppingCartResult> resultList = new ArrayList<>();
        BigDecimal currentPromoRewardAmount = new BigDecimal("0");

        shoppingCartItem.setShoppingCartItemActivitys(new ArrayList<>());
        shoppingCartItem.setPromoAmount(new BigDecimal("1"));
        CalcShoppingCartResult calcShoppingCartResult = new CalcShoppingCartResult();

        CalcShoppingCartResult.ShoppingCartItem item = new CalcShoppingCartResult.ShoppingCartItem();
        item.setShoppingCartItemActivitys(new ArrayList<>());
        item.setProductPrice(new BigDecimal("1"));
        item.setPromoAmount(new BigDecimal("1"));
        item.setSkuCode("1");
        ArrayList<CalcShoppingCartResult.ShoppingCartItem> list = new ArrayList<>();
        list.add(item);
        calcShoppingCartResult.setShoppingCartItems(list);
        resultList.add(calcShoppingCartResult);

        List<FlashSaleOrderCalaResult> listF = new ArrayList<>();

        FlashSaleOrderCalaResult flashSaleOrderCalaResult = new FlashSaleOrderCalaResult();

        ArrayList<FlashSaleOrderCalaResult.ShoppingCartItem> shoppingCartItems = new ArrayList<>();
        shoppingCartItems.add(shoppingCartItem);
        flashSaleOrderCalaResult.setShoppingCartItems(shoppingCartItems);
        listF.add(flashSaleOrderCalaResult);


        flashSaleComponent.updateFlashSaleOrderResults(listF,resultList);



    }


    @Test
    public void convertToCalcShoppingCartResultList_empty(){

        List<ShoppingCartOutDTO> scoDtoList = new ArrayList<>();

        ShoppingCartOutDTO shoppingCartOutDTO1 = new ShoppingCartOutDTO();
        shoppingCartOutDTO1.setShoppingCartItems(new ArrayList<>());
        shoppingCartOutDTO1.setFailedReason(null);

        ShoppingCartOutDTO shoppingCartOutDTO = new ShoppingCartOutDTO();
        shoppingCartOutDTO.setShoppingCartItems(new ArrayList<>());
        shoppingCartOutDTO.setFailedReason(ErrorCodes.CALC_FUNCTION_TYPE_MATCH_FAILED);
        shoppingCartOutDTO.setShoppingCartItems(new ArrayList<>());

        List<CalcShoppingCartResult> calcShoppingCartResults = flashSaleComponent.convertToCalcShoppingCartResultList(scoDtoList);
        Assert.assertEquals(0,calcShoppingCartResults.size());

    }

    @Test
    public void convertToCalcShoppingCartResultList(){

        List<ShoppingCartOutDTO> scoDtoList = new ArrayList<>();

        ShoppingCartOutDTO shoppingCartOutDTO1 = new ShoppingCartOutDTO();
        shoppingCartOutDTO1.setShoppingCartItems(new ArrayList<>());
        shoppingCartOutDTO1.setFailedReason(null);
        shoppingCartOutDTO1.setShoppingCartItems(new ArrayList<>());


        ShoppingCartOutDTO shoppingCartOutDTO = new ShoppingCartOutDTO();
        shoppingCartOutDTO.setShoppingCartItems(new ArrayList<>());
        shoppingCartOutDTO.setFailedReason(ErrorCodes.CALC_FUNCTION_TYPE_MATCH_FAILED);
        shoppingCartOutDTO.setShoppingCartItems(new ArrayList<>());
        scoDtoList.add(shoppingCartOutDTO);

        List<CalcShoppingCartResult> calcShoppingCartResults = flashSaleComponent.convertToCalcShoppingCartResultList(scoDtoList);
        Assert.assertEquals(1,calcShoppingCartResults.size());

    }


    @Test
    public void processCartActivities(){
        ShoppingCartDTO shoppingCart = new ShoppingCartDTO();
        Map<String, ActivityCacheDTO > ruleCacheMap = new HashMap<>();
        List<ShoppingCartOutDTO> scoDtoList = new ArrayList<>();
        Mockito.when(shoppingCartDomain.queryActivity(shoppingCart, ruleCacheMap,false)).thenReturn(shoppingCart);
        Mockito.when(calcExecuter.calc(Mockito.any(),Mockito.any())).thenReturn(scoDtoList);
        flashSaleComponent.processCartActivities(shoppingCart,ruleCacheMap);
    }


    @Test
    public void shouldReturnEarly_not_empty(){

        Map<String, ActivityCacheDTO> ruleCacheMap = new HashMap<>();
        ShoppingCartDTO shoppingCart = new ShoppingCartDTO();
        ruleCacheMap.put("1",new ActivityCacheDTO());
        boolean b = flashSaleComponent.shouldReturnEarly(ruleCacheMap, shoppingCart);
        Assert.assertFalse(b);

    }

    @Test(expected = PromotionException.class)
    public void shouldReturnEarly1(){

        Map<String, ActivityCacheDTO> ruleCacheMap = new HashMap<>();
        ShoppingCartDTO shoppingCart = new ShoppingCartDTO();
        shoppingCart.setCouponCodes("!1");
        flashSaleComponent.shouldReturnEarly(ruleCacheMap,shoppingCart);

    }

    @Test
    public void shouldReturnEarly(){

        Map<String, ActivityCacheDTO> ruleCacheMap = new HashMap<>();
        ShoppingCartDTO shoppingCart = new ShoppingCartDTO();

        flashSaleComponent.shouldReturnEarly(ruleCacheMap,shoppingCart);

    }


    @Test
    public void fetchRuleCacheMap(){
        ShoppingCartDTO shoppingCart = new ShoppingCartDTO();
        CalcShoppingCartParam param = new CalcShoppingCartParam();
        Map<String, ActivityCacheDTO> cacheMap = new HashMap<>();
        Mockito.when(activityCacheDomain.getActivityCacheMap(Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(cacheMap);
        Mockito.when(activityCacheDomain.filterActivityByCustomCondition(Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(cacheMap);

        flashSaleComponent.fetchRuleCacheMap(shoppingCart,param);

    }

    @Test
    public void processShoppingCart(){
        List<FlashSaleOrderCalaResult> list = new ArrayList<>();
        FlashSaleShoppingCartDto flashShoppingCart = new FlashSaleShoppingCartDto();
        CacheFlashSaleModel cacheFlashSaleModel = new CacheFlashSaleModel();
        cacheFlashSaleModel.setGroupCode("groupCode");
        flashShoppingCart.setUserCode("userCode");
        flashShoppingCart.setDomainCode("domainCode");
        flashShoppingCart.setTenantCode("tenantCode");
        List<ShoppingCartStore> cartStoreList = new ArrayList<>();
        ShoppingCartStore shoppingCartStore = new ShoppingCartStore();
        ShoppingCartItem item = new ShoppingCartItem();
        item.setSelectionFlag("01");
        item.setSkuCode("skuCode");
        item.setProductCode("productCode");
        item.setQuantity(1);
        item.setProductPrice(new BigDecimal("1"));
        shoppingCartStore.setCartItemList(Lists.newArrayList(item));
        cartStoreList.add(shoppingCartStore);
        flashShoppingCart.setCartStoreList(cartStoreList);
        List<CalcShoppingCartResult> calcShoppingCartResults = flashSaleComponent.processShoppingCart(flashShoppingCart, cacheFlashSaleModel, list);

        Assert.assertEquals(0,calcShoppingCartResults.size());

    }


    @Test
    public void processShoppingCart_not_empty(){
        List<FlashSaleOrderCalaResult> list = new ArrayList<>();
        FlashSaleShoppingCartDto flashShoppingCart = new FlashSaleShoppingCartDto();
        CacheFlashSaleModel cacheFlashSaleModel = new CacheFlashSaleModel();
        cacheFlashSaleModel.setGroupCode("6666");
        flashShoppingCart.setUserCode("userCode");
        flashShoppingCart.setDomainCode("domainCode");
        flashShoppingCart.setTenantCode("tenantCode");

        List<ShoppingCartStore> cartStoreList = new ArrayList<>();
        ShoppingCartStore shoppingCartStore = new ShoppingCartStore();
        ShoppingCartItem item = new ShoppingCartItem();
        item.setSelectionFlag("01");
        item.setSkuCode("skuCode");
        item.setProductCode("productCode");
        item.setQuantity(1);
        item.setProductPrice(new BigDecimal("1"));
        shoppingCartStore.setCartItemList(Lists.newArrayList(item));
        cartStoreList.add(shoppingCartStore);
        flashShoppingCart.setCartStoreList(cartStoreList);

        Map<String, ActivityCacheDTO> cacheMap = new HashMap<>();
        ActivityCacheDTO activityCacheDTO = new ActivityCacheDTO();
        ActivityModel activityModel = new ActivityModel();

        activityCacheDTO.setActivityModel(activityModel);
        cacheMap.put("111",activityCacheDTO);

        Mockito.when(activityCacheDomain.getActivityCacheMap(Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(cacheMap);
        Mockito.when(activityCacheDomain.filterActivityByCustomCondition(Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(cacheMap);

        List<ActivityGroupCache> groupCacheList = new ArrayList<>();
        ActivityGroupCache activityGroupCache = new ActivityGroupCache();
        activityGroupCache.setGroupCode("6666");
        groupCacheList.add(activityGroupCache);

        Mockito.when(promoGroupRelationDomain.getTenantGroupCache(Mockito.any())).thenReturn(groupCacheList);
        ShoppingCartDTO shoppingCart = new ShoppingCartDTO();

        Mockito.when(shoppingCartDomain.queryActivity(Mockito.any(),Mockito.any(),Mockito.anyBoolean())).thenReturn(shoppingCart);

        List<CalcShoppingCartResult> calcShoppingCartResults = flashSaleComponent.processShoppingCart(flashShoppingCart, cacheFlashSaleModel, list);

        Assert.assertEquals(0,calcShoppingCartResults.size());

    }

    @Test
    public void processShoppingCart_not_empty_relation(){
        List<FlashSaleOrderCalaResult> list = new ArrayList<>();
        FlashSaleShoppingCartDto flashShoppingCart = new FlashSaleShoppingCartDto();
        CacheFlashSaleModel cacheFlashSaleModel = new CacheFlashSaleModel();
        cacheFlashSaleModel.setGroupCode("6666");
        flashShoppingCart.setUserCode("userCode");
        flashShoppingCart.setDomainCode("domainCode");
        flashShoppingCart.setTenantCode("tenantCode");

        List<ShoppingCartStore> cartStoreList = new ArrayList<>();
        ShoppingCartStore shoppingCartStore = new ShoppingCartStore();
        ShoppingCartItem item = new ShoppingCartItem();
        item.setSelectionFlag("01");
        item.setSkuCode("skuCode");
        item.setProductCode("productCode");
        item.setQuantity(1);
        item.setProductPrice(new BigDecimal("1"));
        shoppingCartStore.setCartItemList(Lists.newArrayList(item));
        cartStoreList.add(shoppingCartStore);
        flashShoppingCart.setCartStoreList(cartStoreList);

        Map<String, ActivityCacheDTO> cacheMap = new HashMap<>();
        ActivityCacheDTO activityCacheDTO = new ActivityCacheDTO();
        ActivityModel activityModel = new ActivityModel();

        activityCacheDTO.setActivityModel(activityModel);
        cacheMap.put("111",activityCacheDTO);

        Mockito.when(activityCacheDomain.getActivityCacheMap(Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(cacheMap);
        Mockito.when(activityCacheDomain.filterActivityByCustomCondition(Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(cacheMap);

        List<ActivityGroupCache> groupCacheList = new ArrayList<>();
        ActivityGroupCache activityGroupCache = new ActivityGroupCache();
        activityGroupCache.setGroupCode("6666");
        List<String> relationList = new ArrayList<>();
        relationList.add("111");
        activityGroupCache.setRelationList(relationList);
        groupCacheList.add(activityGroupCache);

        Mockito.when(promoGroupRelationDomain.getTenantGroupCache(Mockito.any())).thenReturn(groupCacheList);

        ShoppingCartDTO shoppingCart = new ShoppingCartDTO();

        Mockito.when(shoppingCartDomain.queryActivity(Mockito.any(),Mockito.any(),Mockito.anyBoolean())).thenReturn(shoppingCart);

        List<CalcShoppingCartResult> calcShoppingCartResults = flashSaleComponent.processShoppingCart(flashShoppingCart, cacheFlashSaleModel, list);

        Assert.assertEquals(0,calcShoppingCartResults.size());

    }

    @Test
    public void confirmOrder_group(){
        ConfirmOrderParam param = new ConfirmOrderParam();
        param.setDomainCode("1");
        param.setTenantCode("1");
        param.setOrderNo("1");

        FlashSaleOrderModel byOrderNo = new FlashSaleOrderModel();
        byOrderNo.setMarketingGroupCode("1");
        byOrderNo.setActivityCode("1");
        byOrderNo.setMemberCode("11");

        MarketingGroupMode groupMode  = new MarketingGroupMode();
        groupMode.setGroupSize(1);
        groupMode.setEffectiveHour(1);
        groupMode.setAutoGroupFlag(1);
        groupMode.setEffectiveHour(1);
//        when(redisClient.getString(Mockito.anyString())).thenReturn("1");

        Mockito.when(flashSaleOrderService.findByOrderNo(Mockito.any(), Mockito.any())).thenReturn(byOrderNo);
        Mockito.when(marketingGroupService.findMarketingGroupByActivityCode(Mockito.any(), Mockito.any())).thenReturn(groupMode);

        MarketingGroupUserMode leader = new MarketingGroupUserMode();
        leader.setUserCode("11");
        Mockito.when(marketingGroupUserService.confirmGroupLeaderByMarketingGroupCode(Mockito.any(), Mockito.any())).thenReturn(leader);
        MarketingModel byActivityCode = new MarketingModel();
        byActivityCode.setActivityEnd("2033010101");
        Mockito.when(marketingService.findByActivityCode(Mockito.any())).thenReturn(byActivityCode);
        GroupUserContent groupUserContent = new GroupUserContent();
//        when(marketingGroupComponent.getGroupUserContent(Mockito.any(), Mockito.any(),Mockito.any())).thenReturn(groupUserContent);
        MarketingGroupCodeMode marketingGroupCodeMode = new MarketingGroupCodeMode();
        Date date1 = DateUtils.addHours(new Date(), 2);
        String format1 = DateUtil.format(date1,DateUtil.FORMAT_YYYYMMDDHHMISS_14);
        marketingGroupCodeMode.setEffectiveTime(format1);
        marketingGroupCodeMode.setGroupStatus(MarketingGroupStatusEnum.GROUP_NO_START.code());
        byActivityCode.setActivityType(ActivityTypeEnum.GROUP.code());
        when( marketingService.findByActivityCode(Mockito.any())).thenReturn(byActivityCode);
        when(marketingGroupCodeService.queryGroupByMarketingGroupCode(Mockito.anyString(), Mockito.anyString(),Mockito.anyString())).thenReturn(marketingGroupCodeMode);

        flashSaleComponent.confirmOrder("Mockito.any()", "Mockito.any()");
    }

    @Test(expected = PromotionException.class)
    public void confirmOrder_group_leader_null(){
        ConfirmOrderParam param = new ConfirmOrderParam();
        param.setDomainCode("1");
        param.setTenantCode("1");
        param.setOrderNo("1");
        FlashSaleOrderModel byOrderNo = new FlashSaleOrderModel();
        byOrderNo.setMarketingGroupCode("1");
        byOrderNo.setActivityCode("1");
        byOrderNo.setMemberCode("111");
        MarketingGroupMode groupMode  = new MarketingGroupMode();
        groupMode.setGroupSize(1);
        groupMode.setEffectiveHour(2);
        groupMode.setAutoGroupFlag(0);
        MarketingGroupUserMode leader = new MarketingGroupUserMode();
        leader.setUserCode("111");
        Mockito.when(flashSaleOrderService.findByOrderNo(Mockito.any(), Mockito.any())).thenReturn(byOrderNo);
        Mockito.when(marketingGroupService.findMarketingGroupByActivityCode(Mockito.any(), Mockito.any())).thenReturn(groupMode);
        Mockito.when(marketingGroupUserService.confirmGroupLeaderByMarketingGroupCode(Mockito.any(), Mockito.any())).thenReturn(null);
        MarketingModel byActivityCode = new MarketingModel();
        byActivityCode.setActivityEnd("2033010101");
//        when(redisClient.getString(Mockito.anyString())).thenReturn("1");

        MarketingGroupCodeMode marketingGroupCodeMode = new MarketingGroupCodeMode();
        Date date1 = DateUtils.addHours(new Date(), 2);
        String format1 = DateUtil.format(date1,DateUtil.FORMAT_YYYYMMDDHHMISS_14);
        marketingGroupCodeMode.setEffectiveTime(format1);
        marketingGroupCodeMode.setGroupStatus(MarketingGroupStatusEnum.GROUP_NO_START.code());
//        when(marketingGroupCodeService.queryGroupByMarketingGroupCode(Mockito.anyString(), Mockito.anyString(),Mockito.anyString())).thenReturn(marketingGroupCodeMode);

        flashSaleComponent.confirmOrder("Mockito.any()", "Mockito.any()");
    }

    @Test
    public void confirmOrder_group_leader(){
        ConfirmOrderParam param = new ConfirmOrderParam();
        param.setDomainCode("1");
        param.setTenantCode("1");
        param.setOrderNo("1");


        FlashSaleOrderModel byOrderNo = new FlashSaleOrderModel();
        byOrderNo.setMarketingGroupCode("1");
        byOrderNo.setActivityCode("1");
        byOrderNo.setMemberCode("111");

        MarketingGroupMode groupMode  = new MarketingGroupMode();
        groupMode.setGroupSize(1);
        groupMode.setEffectiveHour(2);
        groupMode.setAutoGroupFlag(1);
        MarketingGroupUserMode leader = new MarketingGroupUserMode();
        leader.setUserCode("111");
        MarketingModel byActivityCode = new MarketingModel();
        byActivityCode.setActivityType(ActivityTypeEnum.GROUP.code());
        when( marketingService.findByActivityCode(Mockito.any())).thenReturn(byActivityCode);

        Mockito.when(flashSaleOrderService.findByOrderNo(Mockito.any(), Mockito.any())).thenReturn(byOrderNo);
        Mockito.when(marketingGroupService.findMarketingGroupByActivityCode(Mockito.any(), Mockito.any())).thenReturn(groupMode);
        Mockito.when(marketingGroupUserService.confirmGroupLeaderByMarketingGroupCode(Mockito.any(), Mockito.any())).thenReturn(leader);
        byActivityCode.setActivityEnd("2033010101");
        Mockito.when(marketingService.findByActivityCode(Mockito.any())).thenReturn(byActivityCode);
//        when(redisClient.getString(Mockito.anyString())).thenReturn("1");
        GroupUserContent groupUserContent = new GroupUserContent();
//        when(marketingGroupComponent.getGroupUserContent(Mockito.any(), Mockito.any(),Mockito.any())).thenReturn(groupUserContent);
        MarketingGroupCodeMode marketingGroupCodeMode = new MarketingGroupCodeMode();
        Date date1 = DateUtils.addHours(new Date(), 2);
        String format1 = DateUtil.format(date1,DateUtil.FORMAT_YYYYMMDDHHMISS_14);
        marketingGroupCodeMode.setEffectiveTime(format1);
        marketingGroupCodeMode.setGroupStatus(MarketingGroupStatusEnum.GROUP_NO_START.code());
        when(marketingGroupCodeService.queryGroupByMarketingGroupCode(Mockito.any(), Mockito.any(),Mockito.any())).thenReturn(marketingGroupCodeMode);

        flashSaleComponent.confirmOrder("Mockito.any()", "Mockito.any()");
    }


    @Test
    public void createBoostSharing(){

        FlashSaleDomain flashSaleDomain = new FlashSaleDomain();
        flashSaleDomain.setActivityCode("activityCode");
        flashSaleDomain.setBoostSharing(new BoostSharing());
        String activityCode = "";
        flashSaleComponent.createBoostSharing(flashSaleDomain,activityCode);
    }


    @Test
    public void queryMarketingActivityListByProductList(){

        QueryMarketingActivityListByProductListParam param = new QueryMarketingActivityListByProductListParam();
        param.setTenantCode("1");
        param.setDomainCode("1");

        QueryMarketingActivityListByProductListParam.Product product = new QueryMarketingActivityListByProductListParam.Product();
        product.setProductCode("1");
        product.setSkuCode("1");



        param.setProductList(Lists.newArrayList(product));
        param.setActivityTypeList(new ArrayList<>());


        flashSaleComponent.queryMarketingActivityListByProductList(param);


    }
    @Test
    public void testBuildQueryMemberInventory() {
        // Arrange
        String tenantCode = "T001";
        String memberCode = "M001";
        String skuCode = "S001";

        QueryFlashSaleListByProductResult result1 = new QueryFlashSaleListByProductResult();
        result1.setActivityCode("A001");
        result1.setMaxPerUser(2);
        result1.setSkuInventory(100);

        QueryFlashSaleListByProductResult result2 = new QueryFlashSaleListByProductResult();
        result2.setActivityCode("A002");
        result2.setMaxPerUser(3);
        result2.setSkuInventory(200);

        List<QueryFlashSaleListByProductResult> flashSaleListByProductList = new ArrayList<>();
        flashSaleListByProductList.add(result1);
        flashSaleListByProductList.add(result2);

        // Act
        flashSaleComponent.buildQueryMemberInventory(tenantCode, memberCode, skuCode, flashSaleListByProductList);


    }

    @Test
    public void filterActiveSpuOrSku(){




        QueryMarketingActivityListByProductListParam param = new QueryMarketingActivityListByProductListParam();
        Map<String, CacheFlashSaleModel> activityCacheMap = new HashedMap<>();
        CacheFlashSaleModel flashSaleModel = new CacheFlashSaleModel();
        flashSaleModel.setActivityType(ActivityTypeEnum.LUCKY_DRAW.code());
        flashSaleModel.setActivityName("activityName");
        flashSaleModel.setActivityCode("activityCode");
        activityCacheMap.put("activityCode",flashSaleModel);
        CacheFlashSaleModel flashSaleModel2 = new CacheFlashSaleModel();
        flashSaleModel2.setActivityType(ActivityTypeEnum.BOOST_SHARDING.code());
        flashSaleModel2.setActivityName("activityName");
        flashSaleModel2.setActivityCode("activityCode2");
        flashSaleModel2.setSelectProductType(SelectorProductTypeEnum.SELECT_SPU.code());
        BoostSharingModel boostSharingModel = new BoostSharingModel();
        boostSharingModel.setActivityCode("123");
        flashSaleModel2.setBoostSharingModel(boostSharingModel);
        activityCacheMap.put("activityCode2",flashSaleModel2);

        CacheFlashSaleModel flashSaleModel3 = new CacheFlashSaleModel();
        flashSaleModel3.setActivityType(ActivityTypeEnum.GROUP.code());
        flashSaleModel3.setActivityName("activityName");
        flashSaleModel3.setActivityCode("activityCode3");
        flashSaleModel3.setSelectProductType(SelectorProductTypeEnum.SELECT_SPU.code());
        MarketingGroupMode marketingGroupMode = new MarketingGroupMode();
        marketingGroupMode.setActivityCode("123");
        flashSaleModel3.setMarketingGroupMode(marketingGroupMode);
        activityCacheMap.put("activityCode3",flashSaleModel3);


        List<QueryMarketingActivityListByProductListResult> list = new ArrayList<>();

        QueryMarketingActivityListByProductListResult result = new QueryMarketingActivityListByProductListResult();

        result.setProductCode("productCode");

        list.add(result);

        QueryMarketingActivityListByProductListResult.SkuActivityList skuList = new QueryMarketingActivityListByProductListResult.SkuActivityList();
        skuList.setSkuCode("skuCode");
        skuList.setMarketingActivityList(new ArrayList<>());

        result.setProductCode("productCode");
        result.setSkuList(Lists.newArrayList(skuList));

        list.add(result);




        QueryMarketingActivityListByProductListParam.Product product = new QueryMarketingActivityListByProductListParam.Product();
        product.setSkuCode("skuCode");
        product.setProductCode("productCode");


        param.setProductList(Lists.newArrayList(product));

        List<FlashSaleProductModel> spuOrSkuList = new ArrayList<>();
        FlashSaleProductModel flashSaleProductModel = new FlashSaleProductModel();
        flashSaleProductModel.setSkuCode("skuCode");
        flashSaleProductModel.setProductCode("productCode");
        flashSaleProductModel.setSpuName("1");


        spuOrSkuList.add(flashSaleProductModel);
        Mockito.when(flashSaleProductService.getProductsByActivityCodesAndProducts(Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(spuOrSkuList);


        Mockito.when(flashSaleProductService.getProductsByActivityCodesAndProductsBySpu(Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(spuOrSkuList);

        Map<String, CacheFlashSaleModel> spuMap = new HashMap<>();

        CacheFlashSaleModel cacheFlashSaleModel = new CacheFlashSaleModel();
        List<MarketingLanguageModel> languages = new ArrayList<>();

        MarketingLanguageModel marketingLanguageModel2 = new MarketingLanguageModel();
        marketingLanguageModel2.setLanguage("id-ID");
        languages.add(marketingLanguageModel2);

        cacheFlashSaleModel.setLanguages(languages);
        cacheFlashSaleModel.setSelectProductType(SelectorProductTypeEnum.SELECT_SPU.code());
        cacheFlashSaleModel.setProducts(Lists.newArrayList(flashSaleProductModel));

        MarketingLanguageModel marketingLanguageModel = new MarketingLanguageModel();
        marketingLanguageModel.setLanguage("zh-CN");
        languages.add(marketingLanguageModel);
        spuMap.put("productCode",cacheFlashSaleModel);
        Mockito.when(marketingCacheComponent.filterActivityByProduct(Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(spuMap);

        CacheFlashSaleModel cacheFlashSaleModel2 = new CacheFlashSaleModel();
        cacheFlashSaleModel2.setLanguages(languages);
        cacheFlashSaleModel2.setSelectProductType(SelectorProductTypeEnum.SELECT_SKU.code());
        cacheFlashSaleModel2.setProducts(Lists.newArrayList(flashSaleProductModel));
        spuMap.put("skuCode",cacheFlashSaleModel2);
        Mockito.when(marketingCacheComponent.filterActivityByProduct(Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(spuMap);


        List<MarketingGroupEntity> marketingGroupEntities = new ArrayList<>();

        MarketingGroupEntity marketingGroupEntity = new MarketingGroupEntity();
        marketingGroupEntity.setActivityCode("1");
        marketingGroupEntities.add(marketingGroupEntity);

        Mockito.when(marketingGroupService.selectMarketingGroupList(Mockito.any(),Mockito.anySet())).thenReturn(marketingGroupEntities);
        int size = list.size();
        flashSaleComponent.filterActiveSpuOrSku(param,activityCacheMap,list);
        assert list.size() == size;
    }


    @Test
    public void filterActiveSpuOrSku_NULL(){

        QueryMarketingActivityListByProductListParam param = new QueryMarketingActivityListByProductListParam();
        Map<String, CacheFlashSaleModel> activityCacheMap = new HashedMap<>();
        List<QueryMarketingActivityListByProductListResult> list = new ArrayList<>();

        QueryMarketingActivityListByProductListResult result = new QueryMarketingActivityListByProductListResult();

        result.setProductCode("1");

        list.add(result);

        QueryMarketingActivityListByProductListResult.SkuActivityList skuList = new QueryMarketingActivityListByProductListResult.SkuActivityList();
        skuList.setSkuCode("1");
        skuList.setMarketingActivityList(new ArrayList<>());

        result.setProductCode("1");
        result.setSkuList(Lists.newArrayList(skuList));

        list.add(result);


        QueryMarketingActivityListByProductListParam.Product product = new QueryMarketingActivityListByProductListParam.Product();
        product.setSkuCode("1");
        product.setProductCode("1");


        param.setProductList(Lists.newArrayList(product));

        List<FlashSaleProductModel> spuOrSkuList = new ArrayList<>();
        FlashSaleProductModel flashSaleProductModel = new FlashSaleProductModel();
        flashSaleProductModel.setSkuCode("1");
        flashSaleProductModel.setProductCode("1");
        flashSaleProductModel.setSpuName("1");

        spuOrSkuList.add(flashSaleProductModel);


//        Mockito.when(flashSaleProductService.getProductsByActivityCodesAndProductsBySpu(Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(spuOrSkuList);


        Map<String, CacheFlashSaleModel> spuMap = new HashMap<>();

        CacheFlashSaleModel cacheFlashSaleModel = new CacheFlashSaleModel();
        cacheFlashSaleModel.setSelectProductType("02");
        List<MarketingLanguageModel> languages = new ArrayList<>();

        MarketingLanguageModel marketingLanguageModel2 = new MarketingLanguageModel();
        marketingLanguageModel2.setLanguage("id-ID");
        languages.add(marketingLanguageModel2);
        languages.add(marketingLanguageModel2);

        cacheFlashSaleModel.setLanguages(languages);
        FlashSaleProductModel productModel = new FlashSaleProductModel();
        productModel.setProductCode("1");
        productModel.setSkuCode("1");
        cacheFlashSaleModel.setProducts(Lists.newArrayList(productModel));
        MarketingLanguageModel marketingLanguageModel = new MarketingLanguageModel();
        marketingLanguageModel.setLanguage("zh-CN");
        languages.add(marketingLanguageModel);
        cacheFlashSaleModel.setLanguages(languages);
        languages.add(marketingLanguageModel);

        spuMap.put("1",cacheFlashSaleModel);
//        Mockito.when(marketingCacheComponent.filterActivityByProduct(Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(spuMap);

        param.setLanguage("zh-CN");
        List<MarketingGroupEntity> marketingGroupEntities = new ArrayList<>();

        MarketingGroupEntity marketingGroupEntity = new MarketingGroupEntity();
        marketingGroupEntity.setActivityCode("1");
        marketingGroupEntities.add(marketingGroupEntity);

//        Mockito.when(marketingGroupService.selectMarketingGroupList(Mockito.any(),Mockito.anySet())).thenReturn(marketingGroupEntities);
        int size = list.size();
        flashSaleComponent.filterActiveSpuOrSku(param,activityCacheMap,list);
        assert list.size() == size;
    }




    @Test
    public void getGroupMarketing(){
        List<CacheFlashSaleModel> models = new ArrayList<>();
        CacheFlashSaleModel flashSaleModel1 = new CacheFlashSaleModel();
        flashSaleModel1.setActivityCode("1");
        models.add(flashSaleModel1);
        MarketingGroupEntity flashSaleModel = new MarketingGroupEntity();
        flashSaleModel.setActivityCode("1");
        flashSaleComponent.getGroupMarketing("1",models, Lists.newArrayList(flashSaleModel));

    }


    @Test
    public void getBoostSharing(){
        List<CacheFlashSaleModel> models = new ArrayList<>();
        CacheFlashSaleModel flashSaleModel1 = new CacheFlashSaleModel();
        flashSaleModel1.setActivityCode("1");
        models.add(flashSaleModel1);
        BoostSharingModel boostSharingModels = new BoostSharingModel();
        boostSharingModels.setActivityCode("1");
        flashSaleComponent.getBoostSharing("1",models, Lists.newArrayList(boostSharingModels));


    }


    @Test
    public void testQueryPromotionOrMarketingNoFilter() {
        // Arrange
        QueryPromotionOrMarketingNoFilterParam param = new QueryPromotionOrMarketingNoFilterParam();
        param.setDomainCode("domainCode");
        param.setTenantCode("tenantCode");
        List<QueryPromotionOrMarketingNoFilterParam.Product> products = new ArrayList<>();
        QueryPromotionOrMarketingNoFilterParam.Product product = new QueryPromotionOrMarketingNoFilterParam.Product();
        product.setSkuCode("skuCode");
        product.setProductCode("productCode");
        products.add(product);
        param.setProductList(products);


        List<QueryPromotionOrMarketingNoFilterParam.Product> productList = new ArrayList<>();
        productList.add(new QueryPromotionOrMarketingNoFilterParam.Product());
        Map<String, CacheFlashSaleModel> marketingCacheMap = new HashMap<>();
        CacheFlashSaleModel flashSaleModel03 = new CacheFlashSaleModel();
        flashSaleModel03.setActivityCode("activityCode03");
        flashSaleModel03.setActivityType("03");
        flashSaleModel03.setSelectProductType("01");
        marketingCacheMap.put("marketingCode1", flashSaleModel03);

        CacheFlashSaleModel flashSaleModel04 = new CacheFlashSaleModel();
        flashSaleModel04.setActivityCode("activityCode04");
        flashSaleModel04.setActivityType("04");
        flashSaleModel04.setSelectProductType("02");
        marketingCacheMap.put("marketingCode2", flashSaleModel04);

        CacheFlashSaleModel flashSaleModel05 = new CacheFlashSaleModel();
        flashSaleModel05.setActivityCode("activityCode05");
        flashSaleModel05.setActivityType("05");
        flashSaleModel05.setSelectProductType("01");
        marketingCacheMap.put("marketingCode3", flashSaleModel05);

        CacheFlashSaleModel flashSaleModel06 = new CacheFlashSaleModel();
        flashSaleModel06.setActivityCode("activityCode06");
        flashSaleModel06.setActivityType("06");
        flashSaleModel06.setSelectProductType("01");
        marketingCacheMap.put("marketingCode4", flashSaleModel06);

        CacheFlashSaleModel flashSaleModel07 = new CacheFlashSaleModel();
        flashSaleModel07.setActivityCode("activityCode07");
        flashSaleModel07.setActivityType("07");
        flashSaleModel07.setSelectProductType("01");
        marketingCacheMap.put("marketingCode5", flashSaleModel07);

        CacheFlashSaleModel flashSaleModel0702 = new CacheFlashSaleModel();
        flashSaleModel0702.setActivityCode("activityCode0702");
        flashSaleModel0702.setActivityType("07");
        flashSaleModel0702.setSelectProductType("02");
        marketingCacheMap.put("marketingCode6", flashSaleModel0702);

        Map<String, ActivityCacheDTO> promotionCacheMap = new HashMap<>();
        ActivityCacheDTO activityCacheDTO = new ActivityCacheDTO();
        ActivityModel activityModel = new ActivityModel();
        activityModel.setActivityCode("activityCode");
        activityModel.setActivityType("01");
        activityCacheDTO.setActivityModel(activityModel);
        promotionCacheMap.put("promotionCode6", activityCacheDTO);

        ActivityCacheDTO activityCacheDTO02 = new ActivityCacheDTO();
        ActivityModel activityModel02 = new ActivityModel();
        activityModel02.setActivityCode("activityCode02");
        activityModel02.setActivityType("02");
        activityCacheDTO02.setActivityModel(activityModel02);
        promotionCacheMap.put("promotionCode7", activityCacheDTO02);



        //抽奖
        List<LuckyDrawRuleModel> luckyDrawRuleModelList = new ArrayList<>();
        LuckyDrawRuleModel luckyDrawRuleModel = new LuckyDrawRuleModel();
        luckyDrawRuleModel.setActivityCode("activityCode05");
        luckyDrawRuleModel.setProductCode("productCode");
        luckyDrawRuleModelList.add(luckyDrawRuleModel);

        //
        List<FlashSaleProductModel> flashSaleProductModelList = new ArrayList<>();
        FlashSaleProductModel flashSaleProductModel = new FlashSaleProductModel();
        flashSaleProductModel.setActivityCode("activityCode05");
        flashSaleProductModel.setProductCode("productCode");
        flashSaleProductModelList.add(flashSaleProductModel);

        //activity
        Map<String, ActivityCacheDTO> activityCacheDTOMap = new HashMap<>();
        activityCacheDTOMap.put("activityCode",activityCacheDTO);




        // Set up mock behavior
        Mockito.when(marketingCacheComponent.getFlashSaleCacheMapByActivityTypeList(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(marketingCacheMap);
        Mockito.when(activityCacheDomain.getActivityCacheMap(Mockito.any(), Mockito.any(), Mockito.any(),Mockito.any())).thenReturn(promotionCacheMap);


        Mockito.when(luckyDrawRuleService.queryLuckyDrawRulesByActivityCode(Mockito.any(),Mockito.anyList())).thenReturn(luckyDrawRuleModelList);
        Mockito.when(flashSaleProductService.getProductsByActivityCodesAndProducts(Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(flashSaleProductModelList);
        Mockito.when(flashSaleProductService.getProductsByActivityCodesAndProductsBySpu(Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(flashSaleProductModelList);
        Mockito.when(boostSharingService.getBoostSharingProductsByActivityCodesAndProducts(Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(flashSaleProductModelList);

        Mockito.when(activityCacheDomain.filterActivityByProduct(Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(activityCacheDTOMap);




        // Act
        List<QueryPromotionOrMarketingNoFilterResult> result = flashSaleComponent.queryPromotionOrMarketingNoFilter(param);

        // Assert
        // Verify the result
        Assert.assertNotNull(result);

    }


    @Test
    public void testIsContainsSpuOrSku() {
        // Prepare test data
        Map<String, HashMap<String, List<String>>> activitySpuOrSkuMap = new HashMap<>();
        String code = "abc";
        SelectorProductTypeEnum selectSpu = SelectorProductTypeEnum.SELECT_SPU;
        String product = "product1";

        // Add data to activitySpuOrSkuMap
        HashMap<String, List<String>> spuOrSkuMap = new HashMap<>();
        List<String> productList = new ArrayList<>();
        productList.add(product);
        spuOrSkuMap.put(product, productList);
        activitySpuOrSkuMap.put(code, spuOrSkuMap);

        // Execute the method to be tested
        boolean result = flashSaleComponent.isContainsSpuOrSku(activitySpuOrSkuMap, code, selectSpu, product);


    }

    @Test
    public void testIsContainsSpuOrSku_NotContainingCode() {
        // Prepare test data
        Map<String, HashMap<String, List<String>>> activitySpuOrSkuMap = new HashMap<>();
        String code = "def";
        SelectorProductTypeEnum selectSpu = SelectorProductTypeEnum.SELECT_SKU;
        String product = "product1";

        // Add data to activitySpuOrSkuMap
        HashMap<String, List<String>> spuOrSkuMap = new HashMap<>();
        List<String> productList = new ArrayList<>();
        productList.add(product);
        spuOrSkuMap.put(product, productList);
        activitySpuOrSkuMap.put(code, spuOrSkuMap);

        // Execute the method to be tested
        boolean result = flashSaleComponent.isContainsSpuOrSku(activitySpuOrSkuMap, code, selectSpu, product);

        // Verify the result
        Assert.assertFalse(result);
    }

    @Test
    public void testIsContainsSpuOrSku_NotContainingSpu() {
        // Prepare test data
        Map<String, HashMap<String, List<String>>> activitySpuOrSkuMap = new HashMap<>();
        String code = "abc";
        SelectorProductTypeEnum selectSpu = SelectorProductTypeEnum.SELECT_SPU;
        String product = "product1";

        // Add data to activitySpuOrSkuMap
        HashMap<String, List<String>> spuOrSkuMap = new HashMap<>();
        List<String> productList = new ArrayList<>();
        productList.add(product);
        spuOrSkuMap.put(product, productList);
        activitySpuOrSkuMap.put(code, spuOrSkuMap);

        // Execute the method to be tested
        boolean result = flashSaleComponent.isContainsSpuOrSku(activitySpuOrSkuMap, code, selectSpu, product);

        // Verify the result
        Assert.assertFalse(result);
    }

    @Test
    public void testIsContainsSpuOrSku_NotContainingProduct() {
        // Prepare test data
        Map<String, HashMap<String, List<String>>> activitySpuOrSkuMap = new HashMap<>();
        String code = "abc";
        SelectorProductTypeEnum selectSpu = SelectorProductTypeEnum.SELECT_SKU;
        String product = "product2";

        // Add data to activitySpuOrSkuMap
        HashMap<String, List<String>> spuOrSkuMap = new HashMap<>();
        List<String> productList = new ArrayList<>();
        productList.add(product);
        spuOrSkuMap.put(product, productList);
        activitySpuOrSkuMap.put(code, spuOrSkuMap);

        // Execute the method to be tested
        boolean result = flashSaleComponent.isContainsSpuOrSku(activitySpuOrSkuMap, code, selectSpu, product);

        // Verify the result
        Assert.assertFalse(result);
    }


    @Test
    public void testResultSetPromotionActivity() {
        // 创建测试数据
        Map<String, ActivityCacheDTO> promotionCacheMap = new HashMap<>();
        QueryPromotionOrMarketingNoFilterResult result = new QueryPromotionOrMarketingNoFilterResult();
        String code = "activityCode";

        // 设置promotionCacheMap中的数据
        ActivityCacheDTO promotionQueryResult = new ActivityCacheDTO();
        promotionCacheMap.put(code, promotionQueryResult);

        // 创建FlashSaleComponent对象
        FlashSaleComponent flashSaleComponent = new FlashSaleComponent();

        // 调用被测试方法
        flashSaleComponent.resultSetPromotionActivity(promotionCacheMap, result, code);


    }



    @Test
    public void exportFlashOrPreSaleDetail() {

        ExportFlashPreSaleDto dto = new ExportFlashPreSaleDto();

        List<FlashPreSaleOrderModel> flashSaleOrderModels = new ArrayList<>();

        FlashPreSaleOrderModel mode11 = new FlashPreSaleOrderModel();
        mode11.setDate("20231226");
        mode11.setOrderStatus("01");
        mode11.setPromoAmount(100.0);
        flashSaleOrderModels.add(mode11);

        FlashPreSaleOrderModel model = new FlashPreSaleOrderModel();
        model.setDate("20231226");
        model.setOrderStatus("02");
        model.setPromoAmount(100.0);
        flashSaleOrderModels.add(model);


        Mockito.when(flashSaleOrderService.queryOrderByCondition(Mockito.any())).thenReturn(flashSaleOrderModels);


        JsonResult<ResponsePage<OrderQueryOut>> orderQueryOutJsonResult = new JsonResult<>();

        orderQueryOutJsonResult.setCode("0");
        orderQueryOutJsonResult.setSuccess(Boolean.TRUE);

        ResponsePage<OrderQueryOut> responsePage = new ResponsePage<>();

        List<OrderQueryOut> orderQueryOuts = new ArrayList<>();

        OrderQueryOut orderQueryOut1 = new OrderQueryOut();
        orderQueryOut1.setAmount(BigDecimal.ONE);
        orderQueryOut1.setPayStatus("0");
        orderQueryOuts.add(orderQueryOut1);

        OrderQueryOut orderQueryOut = new OrderQueryOut();
        orderQueryOut.setAmount(BigDecimal.ONE);
        orderQueryOut.setPayStatus("1");
        orderQueryOuts.add(orderQueryOut);

        List<OrderProductOut> orderProductOuts = new ArrayList<>();
        OrderProductOut productOut = new OrderProductOut();
        productOut.setAmount(BigDecimal.ONE);
        productOut.setCount(1);
        orderProductOuts.add(productOut);

        orderQueryOut.setOrderProductOutList(orderProductOuts);
        orderQueryOuts.add(orderQueryOut);

        responsePage.setList(orderQueryOuts);

        orderQueryOutJsonResult.setData(responsePage);

        Mockito.when(orderFeignClient.queryOrderList(Mockito.any())).thenReturn(orderQueryOutJsonResult);

        List<ExportFlashPreSaleResult> results = flashSaleComponent.exportFlashOrPreSaleDetail(dto);
        Assert.assertEquals(1,results.size());


    }


    @Test
    public void exportFlashOrPreSaleProductDetail() {

        ExportFlashPreSaleDto dto = new ExportFlashPreSaleDto();

        List<FlashPreSaleOrderModel> flashSaleOrderModels = new ArrayList<>();

        FlashPreSaleOrderModel mode11 = new FlashPreSaleOrderModel();
        mode11.setDate("20231226");
        mode11.setOrderStatus("01");
        mode11.setOrderId("1");
        mode11.setPromoAmount(100.0);
        flashSaleOrderModels.add(mode11);

        FlashPreSaleOrderModel model = new FlashPreSaleOrderModel();
        model.setDate("20231226");
        model.setOrderStatus("02");
        model.setOrderId("2");

        model.setPromoAmount(100.0);
        flashSaleOrderModels.add(model);


        Mockito.when(flashSaleOrderService.queryOrderByCondition(Mockito.any())).thenReturn(flashSaleOrderModels);

        List<FlashSaleOrderDetailModel> productList = new ArrayList<>();

        FlashSaleOrderDetailModel detailModel1 = new FlashSaleOrderDetailModel();

        detailModel1.setProductCode("111");
        detailModel1.setOrderId("2");
        detailModel1.setSkuQuality(1);
        productList.add(detailModel1);

        FlashSaleOrderDetailModel detailModel = new FlashSaleOrderDetailModel();

        detailModel.setProductCode("111");
        detailModel.setOrderId("1");
        detailModel.setSkuQuality(1);
        productList.add(detailModel);
        Mockito.when(flashSaleOrderDetailService.findByOrderNoList(Mockito.any(),Mockito.any())).thenReturn(productList);
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("data",new JSONArray());
        Mockito.when(pimFeignClient.querySkuWithProductList(Mockito.any())).thenReturn(jsonObject);

        JsonResult<ResponsePage<OrderQueryOut>> orderQueryOutJsonResult = new JsonResult<>();

        orderQueryOutJsonResult.setCode("0");
        orderQueryOutJsonResult.setSuccess(Boolean.TRUE);

        ResponsePage<OrderQueryOut> responsePage = new ResponsePage<>();

        List<OrderQueryOut> orderQueryOuts = new ArrayList<>();

        OrderQueryOut orderQueryOut1 = new OrderQueryOut();
        orderQueryOut1.setAmount(BigDecimal.ONE);
        orderQueryOut1.setPayStatus("0");
        orderQueryOuts.add(orderQueryOut1);

        OrderQueryOut orderQueryOut = new OrderQueryOut();
        orderQueryOut.setAmount(BigDecimal.ONE);
        orderQueryOut.setPayStatus("1");
        orderQueryOuts.add(orderQueryOut);

        List<OrderProductOut> orderProductOuts = new ArrayList<>();
        OrderProductOut productOut = new OrderProductOut();
        productOut.setAmount(BigDecimal.ONE);
        productOut.setCount(1);
        orderProductOuts.add(productOut);

        orderQueryOut.setOrderProductOutList(orderProductOuts);
        orderQueryOuts.add(orderQueryOut);

        responsePage.setList(orderQueryOuts);

        orderQueryOutJsonResult.setData(responsePage);

//        Mockito.when(orderFeignClient.queryOrderList(Mockito.any())).thenReturn(orderQueryOutJsonResult);


        List<ExportFlashPreProductResult> results = flashSaleComponent.exportFlashOrPreSaleProductDetail(dto);
        Assert.assertEquals(0,results.size());


    }


    @Test
    public void exportFlashOrPreSaleProductDetail_not_product_null() {

        ExportFlashPreSaleDto dto = new ExportFlashPreSaleDto();

        List<FlashPreSaleOrderModel> flashSaleOrderModels = new ArrayList<>();

        FlashPreSaleOrderModel mode11 = new FlashPreSaleOrderModel();
        mode11.setDate("20231226");
        mode11.setOrderStatus("01");
        mode11.setOrderId("1");
        mode11.setPromoAmount(100.0);
        flashSaleOrderModels.add(mode11);

        FlashPreSaleOrderModel model = new FlashPreSaleOrderModel();
        model.setDate("20231226");
        model.setOrderStatus("02");
        model.setOrderId("2");

        model.setPromoAmount(100.0);
        flashSaleOrderModels.add(model);


        Mockito.when(flashSaleOrderService.queryOrderByCondition(Mockito.any())).thenReturn(flashSaleOrderModels);

        List<FlashSaleOrderDetailModel> productList = new ArrayList<>();

        FlashSaleOrderDetailModel detailModel1 = new FlashSaleOrderDetailModel();

        detailModel1.setProductCode("111");
        detailModel1.setOrderId("2");
        detailModel1.setSkuQuality(1);
        productList.add(detailModel1);

        FlashSaleOrderDetailModel detailModel = new FlashSaleOrderDetailModel();

        detailModel.setProductCode("111");
        detailModel.setOrderId("1");
        detailModel.setSkuQuality(1);
        productList.add(detailModel);
        Mockito.when(flashSaleOrderDetailService.findByOrderNoList(Mockito.any(),Mockito.any())).thenReturn(productList);
        JSONObject jsonObject = new JSONObject();
        JSONArray jsonArray = new JSONArray();

        JSONObject jsonObject1 = new JSONObject();
        jsonObject1.put("productNo","11");

        jsonObject1.put("productName","11");
        jsonObject1.put("productCode","111");

        jsonArray.add(jsonObject1);

        jsonObject.put("data",jsonArray);
        Mockito.when(pimFeignClient.querySkuWithProductList(Mockito.any())).thenReturn(jsonObject);
        JsonResult<ResponsePage<OrderQueryOut>> orderQueryOutJsonResult = new JsonResult<>();

        orderQueryOutJsonResult.setCode("0");
        orderQueryOutJsonResult.setSuccess(Boolean.TRUE);

        ResponsePage<OrderQueryOut> responsePage = new ResponsePage<>();

        List<OrderQueryOut> orderQueryOuts = new ArrayList<>();

        OrderQueryOut orderQueryOut1 = new OrderQueryOut();
        orderQueryOut1.setAmount(BigDecimal.ONE);
        orderQueryOut1.setPayStatus("0");
        orderQueryOuts.add(orderQueryOut1);

        OrderQueryOut orderQueryOut = new OrderQueryOut();
        orderQueryOut.setAmount(BigDecimal.ONE);
        orderQueryOut.setPayStatus("1");
        orderQueryOuts.add(orderQueryOut);

        List<OrderProductOut> orderProductOuts = new ArrayList<>();
        OrderProductOut productOut = new OrderProductOut();
        productOut.setAmount(BigDecimal.ONE);
        productOut.setCount(1);
        orderProductOuts.add(productOut);

        orderQueryOut.setOrderProductOutList(orderProductOuts);
        orderQueryOuts.add(orderQueryOut);

        responsePage.setList(orderQueryOuts);

        orderQueryOutJsonResult.setData(responsePage);

        Mockito.when(orderFeignClient.queryOrderList(Mockito.any())).thenReturn(orderQueryOutJsonResult);

        List<ExportFlashPreProductResult> results = flashSaleComponent.exportFlashOrPreSaleProductDetail(dto);
        Assert.assertEquals(1,results.size());


    }


    @Test
    public void exportGroupData(){
        ExportGroupDto dto = new ExportGroupDto();
        List<MarketingGroupCodeMode> groupCodeModeList = new ArrayList<>();


        MarketingGroupCodeMode marketingGroupCodeMode1 = new MarketingGroupCodeMode();
        marketingGroupCodeMode1.setDate("20240103");
        marketingGroupCodeMode1.setGroupStatus(MarketingGroupStatusEnum.GROUP_PROCESSING.code());
        groupCodeModeList.add(marketingGroupCodeMode1);

        MarketingGroupCodeMode marketingGroupCodeMode2 = new MarketingGroupCodeMode();
        marketingGroupCodeMode2.setDate("20240102");
        marketingGroupCodeMode2.setGroupStatus(MarketingGroupStatusEnum.GROUP_SUCCESS.code());
        groupCodeModeList.add(marketingGroupCodeMode2);

        MarketingGroupCodeMode marketingGroupCodeMode = new MarketingGroupCodeMode();
        marketingGroupCodeMode.setDate("20240104");
        marketingGroupCodeMode.setGroupStatus(MarketingGroupStatusEnum.GROUP_FAIL.code());

        groupCodeModeList.add(marketingGroupCodeMode);


        Mockito.when(marketingGroupCodeService.queryGroupByActivityCode(Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(groupCodeModeList);

        Mockito.when(marketingGroupUserService.countOfParticipants(Mockito.any())).thenReturn(1);



        List<FlashPreSaleOrderModel> flashSaleOrderModels = new ArrayList<>();

        FlashPreSaleOrderModel mode11 = new FlashPreSaleOrderModel();
        mode11.setDate("20240102");
        mode11.setOrderStatus("01");
        mode11.setPromoAmount(100.0);
        mode11.setOrderId("12312");
        flashSaleOrderModels.add(mode11);

        FlashPreSaleOrderModel model = new FlashPreSaleOrderModel();
        model.setDate("20240104");
        model.setOrderStatus("02");
        model.setPromoAmount(100.0);
        model.setOrderId("22");

        flashSaleOrderModels.add(model);


        Mockito.when(flashSaleOrderService.queryOrderByMarketingGroupCodeList(Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(flashSaleOrderModels);

        JsonResult<ResponsePage<OrderQueryOut>> orderQueryOutJsonResult = new JsonResult<>();

        orderQueryOutJsonResult.setCode("0");
        orderQueryOutJsonResult.setSuccess(Boolean.TRUE);

        ResponsePage<OrderQueryOut> responsePage = new ResponsePage<>();

        List<OrderQueryOut> orderQueryOuts = new ArrayList<>();

        OrderQueryOut orderQueryOut1 = new OrderQueryOut();
        orderQueryOut1.setAmount(BigDecimal.ONE);
        orderQueryOut1.setPayStatus("0");
        orderQueryOuts.add(orderQueryOut1);

        OrderQueryOut orderQueryOut = new OrderQueryOut();
        orderQueryOut.setAmount(BigDecimal.ONE);
        orderQueryOut.setPayStatus("1");
        orderQueryOuts.add(orderQueryOut);

        List<OrderProductOut> orderProductOuts = new ArrayList<>();
        OrderProductOut productOut = new OrderProductOut();
        productOut.setAmount(BigDecimal.ONE);
        productOut.setCount(1);
        orderProductOuts.add(productOut);

        orderQueryOut.setOrderProductOutList(orderProductOuts);
        orderQueryOuts.add(orderQueryOut);

        responsePage.setList(orderQueryOuts);

        orderQueryOutJsonResult.setData(responsePage);

        Mockito.when(orderFeignClient.queryOrderList(Mockito.any())).thenReturn(orderQueryOutJsonResult);

        List<ExportGroupDetailResult> exportGroupDetailResults = flashSaleComponent.exportGroupData(dto);

        Assert.assertEquals(3,exportGroupDetailResults.size());

    }


    @Test
    public void testHandlerShoppingCart() {

        FlashSaleShoppingCartDto shoppingCart = new FlashSaleShoppingCartDto();
        shoppingCart.setDomainCode("1");
        shoppingCart.setTenantCode("1");
        shoppingCart.setUserCode("1");
        shoppingCart.setActivityCode("activityCode");
        shoppingCart.setActivityType("03");
        List<ShoppingCartStore> cartStoreList = new ArrayList<>();
        ShoppingCartStore shoppingCartStore = new ShoppingCartStore();
        List<ShoppingCartItem> cartItemList = new ArrayList<>();
        ShoppingCartItem item = new ShoppingCartItem();
        item.setSkuCode("1");
        item.setProductCode("productCode");
        item.setQuantity(1);
        item.setProductPrice(new BigDecimal("123"));
        item.setSelectionFlag("01");

        cartItemList.add(item);
        shoppingCartStore.setCartItemList(cartItemList);
        cartStoreList.add(shoppingCartStore);
        shoppingCart.setCartStoreList(cartStoreList);
        List<FlashSaleActivityInfoDTO> activity = new ArrayList<>();
        FlashSaleActivityInfoDTO dto = new FlashSaleActivityInfoDTO();
        dto.setActivityCode("1");
        activity.add(dto);

        Map<String, CacheFlashSaleModel> flashSaleCacheMap = new HashedMap<>();
        CacheFlashSaleModel flashSaleModel = new CacheFlashSaleModel();
        flashSaleModel.setActivityCode("6666666666");
        flashSaleModel.setActivityBegin("20210225181202");
        flashSaleModel.setActivityEnd("20250225181202");
        List<FlashSaleProductModel> productModels = new ArrayList<>();
        FlashSaleProductModel productModel = new FlashSaleProductModel();
        productModel.setSkuCode("1");
        productModel.setProductCode("productCode");
        productModel.setFlashPrice(new BigDecimal("12"));
        productModels.add(productModel);
        flashSaleModel.setProducts(productModels);
        List<MarketingLanguageModel> languages11 = new ArrayList<>();
        MarketingLanguageModel marketingLanguageModel11 = new MarketingLanguageModel();
        marketingLanguageModel11.setLanguage("id-ID");
        languages11.add(marketingLanguageModel11);
        MarketingLanguageModel marketingLanguageModel2 = new MarketingLanguageModel();
        marketingLanguageModel2.setLanguage("zh-CN");
        languages11.add(marketingLanguageModel2);
        flashSaleModel.setLanguages(languages11);
        flashSaleCacheMap.put("activityCode", flashSaleModel);






        FlashSaleSkuActivityPriceDTO skuActivityPriceDTO = new FlashSaleSkuActivityPriceDTO();
        List<FlashSaleActivityPriceDTO> activityPriceDTOS = new ArrayList<>();
        FlashSaleActivityPriceDTO priceDTO = new FlashSaleActivityPriceDTO();
        priceDTO.setActivityCode("activityCode");
        activityPriceDTOS.add(priceDTO);
        skuActivityPriceDTO.setActivity(activityPriceDTOS);


        // Mock data
        shoppingCart.setTenantCode("tenantCode");
        shoppingCart.setLanguage("language");
        shoppingCart.setActivityType("03");
        shoppingCart.setMarketingGroupCode("marketingGroupCode");

        List<FlashSaleOrderCalaResult> expectedList = new ArrayList<>();

        // Mock flashSaleCacheMap
        CacheFlashSaleModel cacheFlashSaleModel = new CacheFlashSaleModel();





        // Mock marketingService
        SelectorProductTypeEnum selectProductType = SelectorProductTypeEnum.SELECT_SPU;
        MarketingModel byActivityCode = new MarketingModel();
        byActivityCode.setSelectProductType(selectProductType.code());
        Mockito.when(marketingService.findByActivityCode(Mockito.any())).thenReturn(byActivityCode);

        // Mock prizeService
        List<PrizeModel> listByActivityCode = new ArrayList<>();
        //Mockito.when((prizeService.findListByActivityCode(Mockito.any()))).thenReturn(listByActivityCode);

        // Mock flashSaleProductService
        List<FlashSaleProductModel> products = new ArrayList<>();
        FlashSaleProductModel flashSaleProductModel = new FlashSaleProductModel();
        flashSaleProductModel.setProductCode("productCode");
        flashSaleProductModel.setActivityCode("activityCode");
        flashSaleProductModel.setFlashPrice(BigDecimal.ZERO);
        products.add(flashSaleProductModel);
        //Mockito.when((flashSaleProductService.getProductsByActivityCodesAndProducts(Mockito.any(), Mockito.any(), Mockito.any()))).thenReturn(products);

        // Mock marketingGroupService
        MarketingGroupMode marketingGroup = new MarketingGroupMode();
        //Mockito.when((marketingGroupService.findByActivityCode(Mockito.any()))).thenReturn(marketingGroup);

        // Mock cache
        Mockito.when((marketingCacheComponent.getFlashSaleCacheMap(Mockito.any(), Mockito.any(), Mockito.any()))).thenReturn(flashSaleCacheMap);

        // Mock cacheFilterActivityByProduct
        Mockito.when((marketingCacheComponent.filterActivityByProduct(Mockito.any(), Mockito.any(), Mockito.any(),Mockito.any()))).thenReturn(flashSaleCacheMap);

        // Mock getStringCacheFlashSaleModelMap
        Mockito.when((marketingCacheComponent.getFlashSaleCacheMap(Mockito.any(), Mockito.any(),Mockito.any()))).thenReturn(flashSaleCacheMap);

        // Mock flashSaleProductService
        //Mockito.when((flashSaleProductService.findByActivityAndProduct(Mockito.any(), Mockito.any()))).thenReturn(flashSaleProductModel);
        //Mockito.when((flashSaleProductService.findByActivityAndSku(Mockito.any(), Mockito.any()))).thenReturn(flashSaleProductModel);
        Mockito.when(marketingLuckDrawPrizeProductService.checkLuckDrawPrizeProduct(Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(true);
        // Mock checkQuota
        //Mockito.when(flashSaleComponent.checkQuota(Mockito.anyString(), Mockito.anyString(), Mockito.anyInt(), Mockito.anyString(), Mockito.any(FlashSaleProductModel.class))).thenReturn(true);


        // Call the function to be tested
        List<FlashSaleOrderCalaResult> actualList = flashSaleComponent.handlerShoppingCart(shoppingCart);


    }


}
