package com.gtech.promotion.component.purchaseconstraint;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.gtech.ecomm.order.vo.out.order.OrderProductOut;
import com.gtech.pim.response.CatalogAttributeResponse;
import com.gtech.pim.response.CatalogProductBaseResponse;
import com.gtech.pim.response.CatalogSkuResponse;
import com.gtech.promotion.RedisClient;
import com.gtech.promotion.code.activity.ProductSelectionTypeEnum;
import com.gtech.promotion.code.activity.ProductTypeEnum;
import com.gtech.promotion.code.purchaseconstraint.PcRuleCalculateTypeEnum;
import com.gtech.promotion.code.purchaseconstraint.PurchaseConstraintRuleTimeTypeEnum;
import com.gtech.promotion.code.purchaseconstraint.PurchaseConstraintRuleTypeEnum;
import com.gtech.promotion.component.purchaseconstraint.dto.PcRuleCheckResult;
import com.gtech.promotion.dao.entity.purchaseconstraint.PurchaseConstraintCustomer;
import com.gtech.promotion.dao.model.purchaseconstraint.PcRuleCalculateModel;
import com.gtech.promotion.dao.model.purchaseconstraint.PurchaseConstraintCustomerListMode;
import com.gtech.promotion.dao.model.purchaseconstraint.PurchaseConstraintModel;
import com.gtech.promotion.dto.cache.PurchaseConstraintCacheDTO;
import com.gtech.promotion.dto.in.purchaseconstraint.PcRuleOverlayCheckDTO;
import com.gtech.promotion.feign.CatalogClientConsumer;
import com.gtech.promotion.feign.OrderClientConsumer;
import com.gtech.promotion.feign.response.CustomOrderSkuStatisticsQueryOut;
import com.gtech.promotion.feign.response.MemberPurchaseStatisticsResp;
import com.gtech.promotion.service.purchaseconstraint.PurchaseConstraintCustomerService;
import com.gtech.promotion.service.purchaseconstraint.PurchaseConstraintDetailService;
import com.gtech.promotion.vo.bean.ProductAttribute;
import com.gtech.promotion.vo.bean.ProductDetail;
import com.gtech.promotion.vo.bean.ProductScope;
import com.gtech.promotion.vo.bean.PurchaseConstraintRule;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;
import org.springframework.transaction.support.TransactionTemplate;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@RunWith(MockitoJUnitRunner.class)
public class PcRuleComponentTest {
    @InjectMocks
    private PcRuleComponent pcRuleComponent;
    @Mock
    private PurchaseConstraintComponent pcComponent;
    @Mock
    private RedisClient redisClient;
    @Mock
    private OrderClientConsumer orderClientConsumer;
    @Mock
    private CatalogClientConsumer catalogClientConsumer;
    @Mock
    private PurchaseConstraintCustomerService pcCustomerService;
    @Mock
    private TransactionTemplate transactionTemplate;
    @Mock
    private PurchaseConstraintDetailService pcDetailService;



    @Before
    public void setUp() throws Exception {
        ReflectionTestUtils.setField(pcRuleComponent, "checkOms", true);

    }

    @Test
    public void getPcRuleCheckResultResultTest() {
		pcRuleComponent.getPcRuleCheckResultResult(new PurchaseConstraintCacheDTO(), new PurchaseConstraintRule(), BigDecimal.ZERO, new BigDecimal("0"),
				BigDecimal.ZERO, new PcRuleCheckResult(), new HashMap<Integer, String>());
        for (PurchaseConstraintRuleTypeEnum value : PurchaseConstraintRuleTypeEnum.values()) {
            PurchaseConstraintRule purchaseConstraintRule = new PurchaseConstraintRule();
            purchaseConstraintRule.setPurchaseConstraintRuleType(value.getCode());
            pcRuleComponent.getPcRuleCheckResultResult(new PurchaseConstraintCacheDTO(), purchaseConstraintRule,  BigDecimal.ZERO, new BigDecimal("1"),BigDecimal.ZERO, new PcRuleCheckResult(),new HashMap<Integer, String>());
        }

        Map<Integer, String> ruleTypeMap =  new HashMap<Integer, String>();
		ruleTypeMap.put(0, "0");
		ruleTypeMap.put(1, "1");
		ruleTypeMap.put(2, "2");
		PcRuleCheckResult ruleIncrementResult = new PcRuleCheckResult();
		int i = 0;
		for (PurchaseConstraintRuleTypeEnum value : PurchaseConstraintRuleTypeEnum.values()) {
			PurchaseConstraintRule purchaseConstraintRule = new PurchaseConstraintRule();
			purchaseConstraintRule.setPurchaseConstraintRuleType(value.getCode());
			PurchaseConstraintCacheDTO pc = new PurchaseConstraintCacheDTO();
			pc.setPurchaseConstraintCode(i + "");
			pcRuleComponent.getPcRuleCheckResultResult(pc, purchaseConstraintRule, BigDecimal.ZERO, new BigDecimal("1"),
					BigDecimal.ZERO, ruleIncrementResult, ruleTypeMap);
			i++;
		}

    }

    @Test
    public void customersIncrementTest() {
        pcRuleComponent.customersIncrement(Lists.newArrayList(), Lists.newArrayList());
    }

    @Test
    public void pcRuleCheckResultsForeachTest() {
        CustomOrderSkuStatisticsQueryOut.OrderSkuStatisticsQueryItem queryItem = new CustomOrderSkuStatisticsQueryOut.OrderSkuStatisticsQueryItem();
        queryItem.setReturnAmount(BigDecimal.ZERO);
        queryItem.setCancelAmount(BigDecimal.ZERO);
        queryItem.setAmount(BigDecimal.ZERO);
        queryItem.setReturnCount(1);
        queryItem.setCancelCount(1);
        queryItem.setCount(1);
        CustomOrderSkuStatisticsQueryOut queryOut = new CustomOrderSkuStatisticsQueryOut();
        queryOut.setStatisticsItems(Lists.newArrayList(queryItem));
        Mockito.when(orderClientConsumer.queryCustomOrderSkuStatistics(Mockito.any())).thenReturn(Lists.newArrayList(queryOut));
        PurchaseConstraintCustomer customer = new PurchaseConstraintCustomer();
        customer.setPurchaseConstraintValueUsed(new BigDecimal("0"));
        PcRuleOverlayCheckDTO pcRuleCheckDTO = new PcRuleOverlayCheckDTO();
        pcRuleCheckDTO.setPc(new PurchaseConstraintCacheDTO());
        pcRuleCheckDTO.setIncludeProductCodes(Lists.newArrayList("productCode:001"));
        PcRuleCheckResult pcRuleCheckResult = new PcRuleCheckResult();
        pcRuleCheckResult.setCustomer(customer);
        pcRuleCheckResult.setPcRuleCheckDTO(pcRuleCheckDTO);
        boolean checkOms = true;

        pcRuleComponent.pcRuleCheckResultsForeach(Lists.newArrayList(pcRuleCheckResult), Lists.newArrayList(), Lists.newArrayList());
        customer.setPurchaseConstraintValueUsed(new BigDecimal("1"));
        pcRuleComponent.pcRuleCheckResultsForeach(Lists.newArrayList(pcRuleCheckResult), Lists.newArrayList(), Lists.newArrayList());
    }

    @Test
    public void pcRuleIncrementTest() {
        Mockito.when(pcComponent.checkCommonPurchaseConstraint(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(true);
        Mockito.when(redisClient.setIfAbsent(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(true);
        Mockito.when(orderClientConsumer.queryMemberPurchaseStatistics(Mockito.any())).thenReturn(Lists.newArrayList(new MemberPurchaseStatisticsResp()));


        String productCode = "productCode:001";
        String tenantCode = "tenantCode:001";
        String userCode = "userCode:001";
        String orderCode = "orderCode:001";
        String skuCode = "skuCode:001";
        String returnCode = "returnCode:001";

        List<PurchaseConstraintRule> purchaseConstraintRules = this.getPurchaseConstraintRules();
        Map<String, PurchaseConstraintCacheDTO> pcMap = Arrays.stream(ProductTypeEnum.values())
                .flatMap(fm -> Arrays.stream(ProductSelectionTypeEnum.values()).map(mm -> {
                    PurchaseConstraintCacheDTO pc = new PurchaseConstraintCacheDTO();
                    pc.setPurchaseConstraintRuleList(purchaseConstraintRules);
                    pc.setProductSelectionType(mm.code());
                    pc.setTenantCode(tenantCode);
                    pc.setPurchaseConstraintCode(fm.code() + mm.code());
                    switch (fm) {
                        case ALL:
                        case CUSTOM_SEQ:
                            pc.setPurchaseConstraintName(fm.desc() + ":" + mm.desc());
                            break;
                        case CUSTOM_RANGE:
                            ProductAttribute productAttribute = new ProductAttribute();
                            productAttribute.setAttributeValues("1,2,3");

                            ProductAttribute attributes = new ProductAttribute();
                            attributes.setAttributeValues("1,2,3");
                            attributes.setAttributeCode("001");

                            ProductScope productScope = new ProductScope();
                            productScope.setBrandCode("001");
                            productScope.setAttributes(Lists.newArrayList(attributes));
                            productScope.setSpuAttributes(Lists.newArrayList(productAttribute));
                            productScope.setProductTag("001");
                            pc.setProducts(Lists.newArrayList(productScope));
                            pc.setPurchaseConstraintName(fm.desc() + ":" + mm.desc());
                            break;
                        case CUSTOM_PRODUCT:
                            ProductDetail productDetail = new ProductDetail();
                            productDetail.setSeqNum(1);
                            pc.setProductDetails(Lists.newArrayList(productDetail));
                            break;
                    }
                    return pc;
                })).collect(Collectors.toMap(PurchaseConstraintCacheDTO::getPurchaseConstraintCode, a -> a, (a, b) -> b));


        OrderProductOut orderProductOut = new OrderProductOut();
        orderProductOut.setAmount(new BigDecimal("1"));
        orderProductOut.setSellPrice(new BigDecimal("1"));
        orderProductOut.setCount(1);
        orderProductOut.setProductCode(productCode);


        CatalogSkuResponse catalogSkuResponse = new CatalogSkuResponse();
        catalogSkuResponse.setSkuCode(skuCode);
        catalogSkuResponse.setAttributeValues(Lists.newArrayList(new CatalogAttributeResponse()));

        CatalogProductBaseResponse catalogProductBaseResponse = new CatalogProductBaseResponse();
        catalogProductBaseResponse.setProductCode(productCode);
        catalogProductBaseResponse.setAttributes(Lists.newArrayList(new CatalogAttributeResponse()));
        catalogProductBaseResponse.setSkuList(Lists.newArrayList(catalogSkuResponse));
        Mockito.when(catalogClientConsumer.searchProductAll(Mockito.any()))
                .thenReturn(Lists.newArrayList(catalogProductBaseResponse));


        List<PurchaseConstraintModel> pcModels = new ArrayList<>();
        for (Map.Entry<String, PurchaseConstraintCacheDTO> entry : pcMap.entrySet()) {
            String key = entry.getKey();
            PurchaseConstraintCacheDTO value = entry.getValue();
            PurchaseConstraintModel pcModel = new PurchaseConstraintModel();
            pcModel.setTenantCode(value.getTenantCode());
            pcModel.setPurchaseConstraintName(value.getPurchaseConstraintName());
            pcModel.setPurchaseConstraintCode(key);
            pcModels.add(pcModel);
        }
        Mockito.when(pcComponent.filterPurchaseConstraintByProduct(Mockito.any(), Mockito.any())).thenReturn(Lists.newArrayList(pcMap.values()));


        PcRuleCalculateModel.IncrementProduct productIncrement = new PcRuleCalculateModel.IncrementProduct();
        productIncrement.setSkuCount(1);
        productIncrement.setSellAmount(new BigDecimal("1"));
        productIncrement.setSkuCode(skuCode);
        productIncrement.setProductCode(productCode);
        productIncrement.setPriceSetting(1);
        productIncrement.setHitPcCode("0101");
        productIncrement.setHitPcCodes(Lists.newArrayList("0101"));
        for (PcRuleCalculateTypeEnum value : PcRuleCalculateTypeEnum.values()) {
            PcRuleCalculateModel model = new PcRuleCalculateModel();
            model.setForward(true);
            model.setType(value.getCode());
            model.setUserCode(userCode);
            model.setOrderCode(String.valueOf(System.currentTimeMillis()));
            model.setOrderDate(new Date());
            model.setTenantCode(tenantCode);
            PurchaseConstraintCacheDTO purchaseConstraintCacheDTO = new PurchaseConstraintCacheDTO();
            purchaseConstraintCacheDTO.setPurchaseConstraintCode("0101");
            purchaseConstraintCacheDTO.setPurchaseConstraintRuleList(getPurchaseConstraintRules());

            model.setPcCacheDTOs(Lists.newArrayList(purchaseConstraintCacheDTO));
            model.setIncrementProducts(Lists.newArrayList(productIncrement));
            boolean checkOms = true;

            Map<String,Map<Integer, String>> ruleTypeMap = Maps.newHashMap();
            ruleTypeMap.put(skuCode, null);
            // 正向流程
            pcRuleComponent.pcRuleIncrement(model, ruleTypeMap);
            // 逆向流程
            model.setForward(false);
            pcRuleComponent.pcRuleIncrement(model, ruleTypeMap);
        }


    }


    private List<PurchaseConstraintRule> getPurchaseConstraintRules() {
        return PurchaseConstraintRuleTypeEnum.addUpRuleTypeList().stream()
                .flatMap(fm -> Arrays.stream(PurchaseConstraintRuleTimeTypeEnum.values()).map(m -> {
                    PurchaseConstraintRule purchaseConstraintRule = new PurchaseConstraintRule();
                    switch (m) {
                        case YEARLY:
                            purchaseConstraintRule.setPurchaseConstraintRuleTimeValue("0801");
                            break;
                        case MONTHLY:
                            purchaseConstraintRule.setPurchaseConstraintRuleTimeValue("08");
                            break;
                        case WEEKLY:
                            purchaseConstraintRule.setPurchaseConstraintRuleTimeValue("1");
                            break;
                        default:

                    }
                    purchaseConstraintRule.setPurchaseConstraintRuleType(fm.getCode());
                    purchaseConstraintRule.setPurchaseConstraintRuleTimeType(m.getCode());
                    purchaseConstraintRule.setPurchaseConstraintValue("100");
                    return purchaseConstraintRule;
                })).collect(Collectors.toList());
    }


    @Test
    public void testClearPcRuleCache() {
        // Prepare test data
        PcRuleCalculateModel model = new PcRuleCalculateModel();
        List<PurchaseConstraintCacheDTO> allPcRuleCacheList = new ArrayList<>();
        List<PcRuleCalculateModel.IncrementProduct> incrementProducts = new ArrayList<>();
        Map<String, List<PurchaseConstraintCacheDTO>> skuPcMap = new HashMap<>();


        pcRuleComponent.clearPcRuleCache(model);


    }




    @Test
    public void testGetCustomerMap() {



        PcRuleCalculateModel model = new PcRuleCalculateModel();
        model.setTenantCode("tenantCode");
        model.setUserCode("userCode");

        List incrementProducts = new ArrayList<>();
        PcRuleCalculateModel.IncrementProduct incrementProduct = new PcRuleCalculateModel.IncrementProduct();
        incrementProduct.setSkuCode("skuCode");
        incrementProduct.setProductCode("productCode");
        incrementProducts.add(incrementProduct);

        Map skuPcMap = new HashMap<>();
        List<PurchaseConstraintCacheDTO> pcRuleCacheList = new ArrayList<>();
        PurchaseConstraintCacheDTO pc = new PurchaseConstraintCacheDTO();
        List<PurchaseConstraintRule> pcRuleList = new ArrayList<>();
        PurchaseConstraintRule pcRule = new PurchaseConstraintRule();
        pcRule.setPurchaseConstraintRuleTimeType(PurchaseConstraintRuleTypeEnum.CUSTOMER_MAX_AMOUNT.getCode());
        pcRule.setPurchaseConstraintRuleType(PurchaseConstraintRuleTypeEnum.CUSTOMER_MAX_AMOUNT.getCode());
        pcRule.setPurchaseConstraintRuleTimeValue("1");
        pcRuleList.add(pcRule);
        pc.setPurchaseConstraintRuleList(pcRuleList);
        pc.setPurchaseConstraintCode("purchaseConstraintCode");
        pcRuleCacheList.add(pc);
        skuPcMap.put("skuCode", pcRuleCacheList);

        Map expectedCustomerMap = new HashMap<>();
        PurchaseConstraintCustomer customer = new PurchaseConstraintCustomer();
        customer.setCustomerCode("customerCode");
        expectedCustomerMap.put("customerCode", customer);
        ArrayList arrayList = Lists.newArrayList(expectedCustomerMap.values());
        Mockito.when(pcCustomerService.list(Mockito.any(PurchaseConstraintCustomerListMode.class))).thenReturn(arrayList);
        Map<String, PurchaseConstraintCustomer> actualCustomerMap = pcRuleComponent.getCustomerMap(model, incrementProducts, skuPcMap);

    }

    @Test
    public void testCheckPriceSetting_WithPriceSetting1AndPriceSetting1() {
        // Arrange
        PurchaseConstraintCacheDTO purchaseConstraintCacheDTO = new PurchaseConstraintCacheDTO();
        purchaseConstraintCacheDTO.setPriceSetting(1);
        PcRuleCalculateModel.IncrementProduct product = new PcRuleCalculateModel.IncrementProduct();
        Map<String, PurchaseConstraintCacheDTO> pcMap = new HashMap<>();
        pcMap.put("hitPcCode1", purchaseConstraintCacheDTO);
        product.setHitPcCodes(Lists.newArrayList("hitPcCode1"));
        product.setPriceSetting(1);


        boolean result = pcRuleComponent.checkPriceSetting(product, pcMap);
        Assert.assertTrue(result);
    }




}
