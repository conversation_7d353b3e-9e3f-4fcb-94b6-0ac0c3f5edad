package com.gtech.promotion.component.marketing;

import com.google.common.collect.Lists;
import com.gtech.basic.idm.web.vo.result.QueryOpUserAccountListResult;
import com.gtech.commons.page.PageData;
import com.gtech.commons.redis.GTechCodeGenerator;
import com.gtech.commons.result.PageResult;
import com.gtech.commons.utils.BeanCopyUtils;
import com.gtech.commons.utils.DateUtil;
import com.gtech.promotion.code.activity.ActivityStatusEnum;
import com.gtech.promotion.code.marketing.ActivityTypeEnum;
import com.gtech.promotion.code.marketing.MarketingGroupStatusEnum;
import com.gtech.promotion.code.marketing.PrizeTypeEnum;
import com.gtech.promotion.component.activity.ActivityComponentDomain;
import com.gtech.promotion.component.activity.PromoGroupDomain;
import com.gtech.promotion.component.feign.IdmFeignClientComponent;
import com.gtech.promotion.component.flashsale.DataSyncComponent;
import com.gtech.promotion.dao.model.activity.ActivityModel;
import com.gtech.promotion.dao.model.activity.QualificationModel;
import com.gtech.promotion.dao.model.marketing.*;
import com.gtech.promotion.dao.model.marketing.flashsale.FlashSaleStoreModel;
import com.gtech.promotion.domain.marketing.LuckyDrawDomain;
import com.gtech.promotion.domain.marketing.MarketingDomain;
import com.gtech.promotion.dto.in.marketing.MarketingQueryInDto;
import com.gtech.promotion.exception.PromotionException;
import com.gtech.promotion.helper.ActivityRedisHelpler;
import com.gtech.promotion.service.activity.*;
import com.gtech.promotion.service.flashsale.FlashSaleStoreService;
import com.gtech.promotion.service.impl.activity.ActivityServiceImpl;
import com.gtech.promotion.service.marketing.MarketingGroupCodeService;
import com.gtech.promotion.service.marketing.MarketingGroupService;
import com.gtech.promotion.service.marketing.MarketingGroupUserService;
import com.gtech.promotion.service.marketing.impl.MarketingLanguageServiceImpl;
import com.gtech.promotion.service.marketing.impl.MarketingServiceImpl;
import com.gtech.promotion.service.marketing.impl.PrizeLanguageServiceImpl;
import com.gtech.promotion.service.marketing.impl.PrizeServiceImpl;
import com.gtech.promotion.vo.bean.ActivityPeriod;
import com.gtech.promotion.vo.bean.IncentiveLimited;
import com.gtech.promotion.vo.bean.LuckyDrawRule;
import com.gtech.promotion.vo.bean.Qualification;
import com.gtech.promotion.vo.bean.marketing.MarketingLanguage;
import com.gtech.promotion.vo.bean.marketing.MarketingPrize;
import com.gtech.promotion.vo.bean.marketing.MarketingPrizeLanguage;
import com.gtech.promotion.vo.result.marketing.MarketingFindResult;
import com.gtech.promotion.vo.result.marketing.MarketingQueryResult;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;

@RunWith(MockitoJUnitRunner.class)
public class MarketingComponentTest {

    @InjectMocks
    private MarketingComponent marketingComponent;

    @Mock
    private MarketingServiceImpl marketingService;

    @Mock
    private IdmFeignClientComponent idmFeignClientComponent;

    @Mock
    private MarketingLanguageServiceImpl languageService;

    @Mock
    private PrizeServiceImpl prizeService;

    @Mock
    private PrizeLanguageServiceImpl prizeLanguageService;
    @Mock
    private ActivityPeriodService activityPeriodService;

    @Mock
    private GTechCodeGenerator gTechCodeGenerator;

    @Mock
    private MarketingCacheComponent marketingCacheComponent;

    @Mock
    private ActivityServiceImpl activityService;

    @Mock
    private OperationLogService operationLogService;

    @Mock
    private LuckyDrawRuleService luckyDrawRuleServicel;

    @Mock
    private QualificationService qualificationService;

    @Mock
    private TPromoIncentiveLimitedService incentiveLimitedService;
    @Mock
    private DataSyncComponent dataSyncComponent;

    @Mock
    private ActivityComponentDomain activityComponentDomain;

    @Mock
    private FlashSaleStoreService flashSaleStoreService;

    @Mock
    private PromoGroupDomain promoGroupDomain;


    @Mock
    private MarketingGroupUserService marketingGroupUserService;

    @Mock
    private MarketingGroupCodeService marketingGroupCodeService;

    @Mock
    private MarketingGroupService marketingGroupService;

    @Mock
    private MarketingGroupComponent marketingGroupComponent;

    @Mock
    private ActivityRedisHelpler activityRedisHelpler;

    @Test
    public void createMarketing(){
        LuckyDrawDomain marketingDomain = new LuckyDrawDomain();
        ArrayList<IncentiveLimited> incentiveLimiteds = new ArrayList<>();
        ArrayList<LuckyDrawRule> luckyDrawRules = new ArrayList<>();
        ArrayList<Qualification> qualifications = new ArrayList<>();
        LuckyDrawRule luckyDrawRule = new LuckyDrawRule();
        luckyDrawRule.setAdmissionRules("");
        luckyDrawRule.setProductCode("");
        luckyDrawRule.setBuyingQty(new BigDecimal("0"));
        luckyDrawRule.setEarnTicket(0);
        luckyDrawRule.setMaxGivingPerUser(0);
        Qualification qualification = new Qualification();
        qualification.setQualificationCode("");
        qualification.setQualificationValue(Lists.newArrayList());
        qualification.setQualificationValueName(Lists.newArrayList());
        IncentiveLimited incentiveLimited = new IncentiveLimited();
        incentiveLimited.setLimitationCode("");
        incentiveLimited.setLimitationValue(new BigDecimal("0"));
        incentiveLimiteds.add(incentiveLimited);
        luckyDrawRules.add(luckyDrawRule);
        qualifications.add(qualification);
        marketingDomain.setIncentiveLimitedFlag("01");
        marketingDomain.setIncentiveLimiteds(incentiveLimiteds);
        marketingDomain.setLuckyDrawRules(luckyDrawRules);
        marketingDomain.setQualifications(qualifications);
        List<QualificationModel> qualificationModels = new ArrayList<QualificationModel>();
        Mockito.when(gTechCodeGenerator.generateCode(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any())).thenReturn("1");
        Mockito.when(marketingService.insert(Mockito.any())).thenReturn(0);
        Mockito.when(operationLogService.insertLog(Mockito.any(), Mockito.any())).thenReturn(0);
//        when(promoGroupDomain.checkGroupEnabledByTenantCode(any(),any())).thenReturn(true);

        String marketing = marketingComponent.createMarketing(marketingDomain);
        Assert.assertEquals("1", marketing);
    }

    @Test
    public void createMarketing1(){
        LuckyDrawDomain marketingDomain = new LuckyDrawDomain();
        List<MarketingLanguage> marketingLanguages = new ArrayList<>();
        marketingLanguages.add(new MarketingLanguage());
        marketingDomain.setMarketingLanguages(marketingLanguages);
        List<MarketingPrize> marketingPrizes = new ArrayList<>();

        ArrayList<IncentiveLimited> incentiveLimiteds = new ArrayList<>();
        ArrayList<LuckyDrawRule> luckyDrawRules = new ArrayList<>();
        ArrayList<Qualification> qualifications = new ArrayList<>();
        LuckyDrawRule luckyDrawRule = new LuckyDrawRule();
        luckyDrawRule.setAdmissionRules("");
        luckyDrawRule.setProductCode("");
        luckyDrawRule.setBuyingQty(new BigDecimal("0"));
        luckyDrawRule.setEarnTicket(0);
        luckyDrawRule.setMaxGivingPerUser(0);
        Qualification qualification = new Qualification();
        qualification.setQualificationCode("");
        qualification.setQualificationValue(Collections.singletonList("1"));
        qualification.setQualificationValueName(Collections.singletonList("1"));
        IncentiveLimited incentiveLimited = new IncentiveLimited();
        incentiveLimited.setLimitationCode("");
        incentiveLimited.setLimitationValue(new BigDecimal("0"));
        incentiveLimiteds.add(incentiveLimited);
        luckyDrawRules.add(luckyDrawRule);
        qualifications.add(qualification);
        marketingDomain.setIncentiveLimitedFlag("01");
        marketingDomain.setIncentiveLimiteds(incentiveLimiteds);
        marketingDomain.setLuckyDrawRules(luckyDrawRules);
        marketingDomain.setQualifications(qualifications);
        List<QualificationModel> qualificationModels = new ArrayList<QualificationModel>();

        MarketingPrize prize = new MarketingPrize();
        List<MarketingPrizeLanguage> marketingPrizeLanguages = new ArrayList<>();
        marketingPrizeLanguages.add(new MarketingPrizeLanguage());
        prize.setMarketingPrizeLanguages(marketingPrizeLanguages);
        prize.setPrizeOrder(1);
        marketingPrizes.add(prize);
        marketingDomain.setMarketingPrizes(marketingPrizes);
        ActivityPeriod activityPeriod = new ActivityPeriod();
        marketingDomain.setActivityPeriod(activityPeriod);
        Mockito.when(gTechCodeGenerator.generateCode(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any())).thenReturn("1");
        Mockito.when(marketingService.insert(Mockito.any())).thenReturn(1);
        Mockito.when(prizeService.insert(Mockito.any())).thenReturn(1);
        Mockito.when(operationLogService.insertLog(Mockito.any(), Mockito.any())).thenReturn(0);
//        when(promoGroupDomain.checkGroupEnabledByTenantCode(any(),any())).thenReturn(true);

        String marketing = marketingComponent.createMarketing(marketingDomain);
        Assert.assertEquals("1", marketing);
    }

    @Test
    public void createMarketing2(){
        LuckyDrawDomain marketingDomain = new LuckyDrawDomain();
        List<MarketingLanguage> marketingLanguages = new ArrayList<>();
        marketingLanguages.add(new MarketingLanguage());
        marketingDomain.setMarketingLanguages(marketingLanguages);
        List<MarketingPrize> marketingPrizes = new ArrayList<>();

        ArrayList<IncentiveLimited> incentiveLimiteds = new ArrayList<>();
        ArrayList<LuckyDrawRule> luckyDrawRules = new ArrayList<>();
        ArrayList<Qualification> qualifications = new ArrayList<>();
        LuckyDrawRule luckyDrawRule = new LuckyDrawRule();
        luckyDrawRule.setAdmissionRules("");
        luckyDrawRule.setProductCode("");
        luckyDrawRule.setBuyingQty(new BigDecimal("0"));
        luckyDrawRule.setEarnTicket(0);
        luckyDrawRule.setMaxGivingPerUser(0);
        Qualification qualification = new Qualification();
        qualification.setQualificationCode("");
        qualification.setQualificationValue(Collections.singletonList("1"));
        IncentiveLimited incentiveLimited = new IncentiveLimited();
        incentiveLimited.setLimitationCode("");
        incentiveLimited.setLimitationValue(new BigDecimal("0"));
        incentiveLimiteds.add(incentiveLimited);
        luckyDrawRules.add(luckyDrawRule);
        qualifications.add(qualification);
        marketingDomain.setIncentiveLimitedFlag("01");
        marketingDomain.setIncentiveLimiteds(incentiveLimiteds);
        marketingDomain.setLuckyDrawRules(luckyDrawRules);
        marketingDomain.setQualifications(qualifications);
        List<QualificationModel> qualificationModels = new ArrayList<QualificationModel>();

        MarketingPrize prize = new MarketingPrize();
        List<MarketingPrizeLanguage> marketingPrizeLanguages = new ArrayList<>();
        marketingPrizeLanguages.add(new MarketingPrizeLanguage());
        prize.setMarketingPrizeLanguages(marketingPrizeLanguages);
        prize.setPrizeOrder(1);
        marketingPrizes.add(prize);
        marketingDomain.setMarketingPrizes(marketingPrizes);
        ActivityPeriod activityPeriod = new ActivityPeriod();
        marketingDomain.setActivityPeriod(activityPeriod);
        Mockito.when(gTechCodeGenerator.generateCode(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any())).thenReturn("1");
        Mockito.when(marketingService.insert(Mockito.any())).thenReturn(1);
        Mockito.when(prizeService.insert(Mockito.any())).thenReturn(1);
        Mockito.when(operationLogService.insertLog(Mockito.any(), Mockito.any())).thenReturn(0);
//        when(promoGroupDomain.checkGroupEnabledByTenantCode(any(),any())).thenReturn(true);

        String marketing = marketingComponent.createMarketing(marketingDomain);
        Assert.assertEquals("1", marketing);
    }

    @Test
    public void createMarketing3(){
        LuckyDrawDomain marketingDomain = new LuckyDrawDomain();
        marketingDomain.setLuckyDrawRuleFlag("01");
        List<MarketingLanguage> marketingLanguages = new ArrayList<>();
        marketingLanguages.add(new MarketingLanguage());
        marketingDomain.setMarketingLanguages(marketingLanguages);
        List<MarketingPrize> marketingPrizes = new ArrayList<>();

        ArrayList<IncentiveLimited> incentiveLimiteds = new ArrayList<>();
        ArrayList<LuckyDrawRule> luckyDrawRules = new ArrayList<>();
        ArrayList<Qualification> qualifications = new ArrayList<>();
        LuckyDrawRule luckyDrawRule = new LuckyDrawRule();
        luckyDrawRule.setAdmissionRules("");
        luckyDrawRule.setProductCode("");
        luckyDrawRule.setBuyingQty(new BigDecimal("0"));
        luckyDrawRule.setEarnTicket(0);
        luckyDrawRule.setMaxGivingPerUser(0);
        Qualification qualification = new Qualification();
        qualification.setQualificationCode("");
        qualification.setQualificationValue(Collections.singletonList("1"));
        IncentiveLimited incentiveLimited = new IncentiveLimited();
        incentiveLimited.setLimitationCode("");
        incentiveLimited.setLimitationValue(new BigDecimal("0"));
        incentiveLimiteds.add(incentiveLimited);
        luckyDrawRules.add(luckyDrawRule);
        qualifications.add(qualification);
        marketingDomain.setIncentiveLimiteds(incentiveLimiteds);
        marketingDomain.setLuckyDrawRules(luckyDrawRules);
        marketingDomain.setQualifications(new ArrayList<>());
        List<QualificationModel> qualificationModels = new ArrayList<QualificationModel>();

        MarketingPrize prize = new MarketingPrize();
        List<MarketingPrizeLanguage> marketingPrizeLanguages = new ArrayList<>();
        marketingPrizeLanguages.add(new MarketingPrizeLanguage());
        prize.setMarketingPrizeLanguages(marketingPrizeLanguages);
        prize.setPrizeOrder(1);
        marketingPrizes.add(prize);
        marketingDomain.setMarketingPrizes(marketingPrizes);
        ActivityPeriod activityPeriod = new ActivityPeriod();
        marketingDomain.setActivityPeriod(activityPeriod);
        Mockito.when(gTechCodeGenerator.generateCode(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any())).thenReturn("1");
        Mockito.when(marketingService.insert(Mockito.any())).thenReturn(1);
        Mockito.when(prizeService.insert(Mockito.any())).thenReturn(1);
//        when(promoGroupDomain.checkGroupEnabledByTenantCode(any(),any())).thenReturn(false);

        Mockito.when(operationLogService.insertLog(Mockito.any(), Mockito.any())).thenReturn(0);
        String marketing = marketingComponent.createMarketing(marketingDomain);
        Assert.assertEquals("1", marketing);
    }

    @Test
    public void updateMarketing(){
        LuckyDrawDomain marketingDomain = new LuckyDrawDomain();
        MarketingModel model = new MarketingModel();
        model.setActivityStatus(ActivityStatusEnum.PENDING.code());
        ArrayList<IncentiveLimited> incentiveLimiteds = new ArrayList<>();
        ArrayList<LuckyDrawRule> luckyDrawRules = new ArrayList<>();
        ArrayList<Qualification> qualifications = new ArrayList<>();
        LuckyDrawRule luckyDrawRule = new LuckyDrawRule();
        luckyDrawRule.setAdmissionRules("");
        luckyDrawRule.setProductCode("");
        luckyDrawRule.setBuyingQty(new BigDecimal("0"));
        luckyDrawRule.setEarnTicket(0);
        luckyDrawRule.setMaxGivingPerUser(0);
        Qualification qualification = new Qualification();
        qualification.setQualificationCode("");
        qualification.setQualificationValue(Lists.newArrayList());
        qualification.setQualificationValueName(Lists.newArrayList());
        IncentiveLimited incentiveLimited = new IncentiveLimited();
        incentiveLimited.setLimitationCode("");
        incentiveLimited.setLimitationValue(new BigDecimal("0"));
        incentiveLimiteds.add(incentiveLimited);
        luckyDrawRules.add(luckyDrawRule);
        qualifications.add(qualification);
        marketingDomain.setIncentiveLimitedFlag("01");
        marketingDomain.setIncentiveLimiteds(incentiveLimiteds);
        marketingDomain.setLuckyDrawRules(luckyDrawRules);
        marketingDomain.setQualifications(qualifications);
        List<QualificationModel> qualificationModels = new ArrayList<QualificationModel>();
        Mockito.when(marketingService.findByActivityCode(Mockito.any())).thenReturn(model);
        Mockito.when(languageService.deleteByActivityCode(Mockito.any())).thenReturn(1);
        Mockito.when(prizeService.deleteByActivityCode(Mockito.any())).thenReturn(1);
        Mockito.when(prizeLanguageService.deleteByActivityCode(Mockito.any())).thenReturn(1);
        Mockito.when(marketingService.updateByActivityCode(Mockito.any())).thenReturn(0);
        Mockito.when(operationLogService.insertLog(Mockito.any(), Mockito.any())).thenReturn(0);
        int i = marketingComponent.updateMarketing(marketingDomain);
        Assert.assertEquals(0, i);
    }

    @Test
    public void updateMarketing1(){
        LuckyDrawDomain marketingDomain = new LuckyDrawDomain();
        ArrayList<IncentiveLimited> incentiveLimiteds = new ArrayList<>();
        ArrayList<LuckyDrawRule> luckyDrawRules = new ArrayList<>();
        ArrayList<Qualification> qualifications = new ArrayList<>();
        LuckyDrawRule luckyDrawRule = new LuckyDrawRule();
        luckyDrawRule.setAdmissionRules("");
        luckyDrawRule.setProductCode("");
        luckyDrawRule.setBuyingQty(new BigDecimal("0"));
        luckyDrawRule.setEarnTicket(0);
        luckyDrawRule.setMaxGivingPerUser(0);
        Qualification qualification = new Qualification();
        qualification.setQualificationCode("");
        qualification.setQualificationValue(Lists.newArrayList());
        qualification.setQualificationValueName(Lists.newArrayList());
        IncentiveLimited incentiveLimited = new IncentiveLimited();
        incentiveLimited.setLimitationCode("");
        incentiveLimited.setLimitationValue(new BigDecimal("0"));
        incentiveLimiteds.add(incentiveLimited);
        luckyDrawRules.add(luckyDrawRule);
        qualifications.add(qualification);
        marketingDomain.setIncentiveLimitedFlag("01");
        marketingDomain.setIncentiveLimiteds(incentiveLimiteds);
        marketingDomain.setLuckyDrawRules(luckyDrawRules);
        marketingDomain.setQualifications(qualifications);
        List<QualificationModel> qualificationModels = new ArrayList<QualificationModel>();
        List<MarketingLanguage> marketingLanguages = new ArrayList<>();
        MarketingLanguage e = new MarketingLanguage();
        e.setActivityLabel("1");
        e.setActivityName("1");
        e.setLanguage("zh-cn");
        marketingLanguages.add(e);
        marketingDomain.setMarketingLanguages(marketingLanguages);

        List<MarketingPrize> marketingPrizes = new ArrayList<>();
        MarketingPrize prize = new MarketingPrize();
        List<MarketingPrizeLanguage> marketingPrizeLanguages = new ArrayList<>();
        marketingPrizeLanguages.add(new MarketingPrizeLanguage("1", "zh_cn"));
        prize.setMarketingPrizeLanguages(marketingPrizeLanguages);
        marketingPrizes.add(prize);
        marketingDomain.setMarketingPrizes(marketingPrizes);

        MarketingModel model = new MarketingModel();
        model.setActivityStatus(ActivityStatusEnum.PENDING.code());

        Mockito.when(marketingService.findByActivityCode(Mockito.any())).thenReturn(model);
        Mockito.when(languageService.deleteByActivityCode(Mockito.any())).thenReturn(1);
        Mockito.when(prizeService.deleteByActivityCode(Mockito.any())).thenReturn(1);
        Mockito.when(prizeLanguageService.deleteByActivityCode(Mockito.any())).thenReturn(1);
        Mockito.when(marketingService.updateByActivityCode(Mockito.any())).thenReturn(1);

        Mockito.when(languageService.insert(Mockito.any())).thenReturn(1);
        Mockito.when(prizeService.insert(Mockito.any())).thenReturn(1);
        Mockito.when(prizeLanguageService.insert(Mockito.any())).thenReturn(1);
        Mockito.when(operationLogService.insertLog(Mockito.any(), Mockito.any())).thenReturn(0);
        int i = marketingComponent.updateMarketing(marketingDomain);
        Assert.assertEquals(1, i);
    }

    @Test
    public void updateMarketing2(){
        LuckyDrawDomain marketingDomain = new LuckyDrawDomain();
        ArrayList<IncentiveLimited> incentiveLimiteds = new ArrayList<>();
        ArrayList<LuckyDrawRule> luckyDrawRules = new ArrayList<>();
        ArrayList<Qualification> qualifications = new ArrayList<>();
        LuckyDrawRule luckyDrawRule = new LuckyDrawRule();
        luckyDrawRule.setAdmissionRules("");
        luckyDrawRule.setProductCode("");
        luckyDrawRule.setBuyingQty(new BigDecimal("0"));
        luckyDrawRule.setEarnTicket(0);
        luckyDrawRule.setMaxGivingPerUser(0);
        Qualification qualification = new Qualification();
        qualification.setQualificationCode("");
        qualification.setQualificationValue(Lists.newArrayList());
        qualification.setQualificationValueName(Lists.newArrayList());
        IncentiveLimited incentiveLimited = new IncentiveLimited();
        incentiveLimited.setLimitationCode("");
        incentiveLimited.setLimitationValue(new BigDecimal("0"));
        incentiveLimiteds.add(incentiveLimited);
        luckyDrawRules.add(luckyDrawRule);
        qualifications.add(qualification);
        marketingDomain.setIncentiveLimitedFlag("01");
        marketingDomain.setIncentiveLimiteds(incentiveLimiteds);
        marketingDomain.setLuckyDrawRules(luckyDrawRules);
        marketingDomain.setQualifications(qualifications);
        List<QualificationModel> qualificationModels = new ArrayList<QualificationModel>();
        List<MarketingLanguage> marketingLanguages = new ArrayList<>();
        MarketingLanguage e = new MarketingLanguage();
        e.setActivityName("1");
        e.setLanguage("zh-cn");
        marketingLanguages.add(e);
        marketingDomain.setMarketingLanguages(marketingLanguages);

        List<MarketingPrize> marketingPrizes = new ArrayList<>();
        MarketingPrize prize = new MarketingPrize();
        List<MarketingPrizeLanguage> marketingPrizeLanguages = new ArrayList<>();
        marketingPrizeLanguages.add(new MarketingPrizeLanguage("1", "zh_cn"));
        prize.setMarketingPrizeLanguages(marketingPrizeLanguages);
        marketingPrizes.add(prize);
        marketingDomain.setMarketingPrizes(marketingPrizes);

        MarketingModel model = new MarketingModel();
        model.setActivityStatus(ActivityStatusEnum.PENDING.code());

        Mockito.when(marketingService.findByActivityCode(Mockito.any())).thenReturn(model);
        Mockito.when(languageService.deleteByActivityCode(Mockito.any())).thenReturn(1);
        Mockito.when(prizeService.deleteByActivityCode(Mockito.any())).thenReturn(1);
        Mockito.when(prizeLanguageService.deleteByActivityCode(Mockito.any())).thenReturn(1);
        Mockito.when(marketingService.updateByActivityCode(Mockito.any())).thenReturn(1);

        Mockito.when(languageService.insert(Mockito.any())).thenReturn(1);
        Mockito.when(prizeService.insert(Mockito.any())).thenReturn(1);
        Mockito.when(prizeLanguageService.insert(Mockito.any())).thenReturn(1);
        Mockito.when(operationLogService.insertLog(Mockito.any(), Mockito.any())).thenReturn(0);
        int i = marketingComponent.updateMarketing(marketingDomain);
        Assert.assertEquals(1, i);
    }

    @Test(expected = PromotionException.class)
    public void updateMarketing_检查prize的券活动状态_没有活动(){
        LuckyDrawDomain marketingDomain = new LuckyDrawDomain();
        marketingDomain.setIncentiveLimitedFlag("01");
        marketingDomain.setIncentiveLimiteds(new ArrayList<IncentiveLimited>());
        marketingDomain.setLuckyDrawRules(new ArrayList<LuckyDrawRule>());
        marketingDomain.setQualifications(new ArrayList<Qualification>());
        List<MarketingLanguage> marketingLanguages = new ArrayList<>();
        MarketingLanguage e = new MarketingLanguage();
        e.setActivityName("1");
        e.setLanguage("zh-cn");
        marketingLanguages.add(e);
        marketingDomain.setMarketingLanguages(marketingLanguages);

        List<MarketingPrize> marketingPrizes = new ArrayList<>();
        MarketingPrize prize = new MarketingPrize();
        List<MarketingPrizeLanguage> marketingPrizeLanguages = new ArrayList<>();
        marketingPrizeLanguages.add(new MarketingPrizeLanguage("1", "zh_cn"));
        prize.setMarketingPrizeLanguages(marketingPrizeLanguages);
        prize.setPrizeType(PrizeTypeEnum.COUPON.code());
        marketingPrizes.add(prize);
        marketingDomain.setMarketingPrizes(marketingPrizes);

        MarketingModel model = new MarketingModel();
        model.setActivityStatus(ActivityStatusEnum.PENDING.code());

        Mockito.when(marketingService.findByActivityCode(Mockito.any())).thenReturn(model);

        Mockito.when(languageService.deleteByActivityCode(Mockito.any())).thenReturn(1);
        Mockito.when(prizeService.deleteByActivityCode(Mockito.any())).thenReturn(1);
        Mockito.when(prizeLanguageService.deleteByActivityCode(Mockito.any())).thenReturn(1);
        Mockito.when(marketingService.updateByActivityCode(Mockito.any())).thenReturn(1);
        Mockito.when(languageService.insert(Mockito.any())).thenReturn(1);
        Mockito.when(activityService.findActivityByActivityCode(Mockito.any(), (Mockito.any()))).thenReturn(null);
        marketingComponent.updateMarketing(marketingDomain);
    }

    @Test(expected = PromotionException.class)
    public void updateMarketing_检查prize的券活动状态_活动状态未生效(){
        LuckyDrawDomain marketingDomain = new LuckyDrawDomain();
        marketingDomain.setIncentiveLimitedFlag("01");
        marketingDomain.setIncentiveLimiteds(new ArrayList<IncentiveLimited>());
        marketingDomain.setLuckyDrawRules(new ArrayList<LuckyDrawRule>());
        marketingDomain.setQualifications(new ArrayList<Qualification>());
        List<MarketingLanguage> marketingLanguages = new ArrayList<>();
        MarketingLanguage e = new MarketingLanguage();
        e.setActivityName("1");
        e.setLanguage("zh-cn");
        marketingLanguages.add(e);
        marketingDomain.setMarketingLanguages(marketingLanguages);

        List<MarketingPrize> marketingPrizes = new ArrayList<>();
        MarketingPrize prize = new MarketingPrize();
        List<MarketingPrizeLanguage> marketingPrizeLanguages = new ArrayList<>();
        marketingPrizeLanguages.add(new MarketingPrizeLanguage("1", "zh_cn"));
        prize.setMarketingPrizeLanguages(marketingPrizeLanguages);
        prize.setPrizeType(PrizeTypeEnum.COUPON.code());
        marketingPrizes.add(prize);
        marketingDomain.setMarketingPrizes(marketingPrizes);

        MarketingModel model = new MarketingModel();
        model.setActivityStatus(ActivityStatusEnum.PENDING.code());

        Mockito.when(marketingService.findByActivityCode(Mockito.any())).thenReturn(model);
        Mockito.when(languageService.deleteByActivityCode(Mockito.any())).thenReturn(1);
        Mockito.when(prizeService.deleteByActivityCode(Mockito.any())).thenReturn(1);
        Mockito.when(prizeLanguageService.deleteByActivityCode(Mockito.any())).thenReturn(1);
        Mockito.when(marketingService.updateByActivityCode(Mockito.any())).thenReturn(1);
        Mockito.when(languageService.insert(Mockito.any())).thenReturn(1);
        ActivityModel activityModel = new ActivityModel();
        activityModel.setActivityStatus(ActivityStatusEnum.PENDING.code());
        Mockito.when(activityService.findActivityByActivityCode(Mockito.any(), (Mockito.any()))).thenReturn(activityModel);
        marketingComponent.updateMarketing(marketingDomain);
    }

    @Test
    public void updateMarketing_检查prize的券活动状态_活动状态生效(){
        LuckyDrawDomain marketingDomain = new LuckyDrawDomain();
        ArrayList<IncentiveLimited> incentiveLimiteds = new ArrayList<>();
        ArrayList<LuckyDrawRule> luckyDrawRules = new ArrayList<>();
        ArrayList<Qualification> qualifications = new ArrayList<>();
        LuckyDrawRule luckyDrawRule = new LuckyDrawRule();
        luckyDrawRule.setAdmissionRules("");
        luckyDrawRule.setProductCode("");
        luckyDrawRule.setBuyingQty(new BigDecimal("0"));
        luckyDrawRule.setEarnTicket(0);
        luckyDrawRule.setMaxGivingPerUser(0);
        Qualification qualification = new Qualification();
        qualification.setQualificationCode("");
        qualification.setQualificationValue(Lists.newArrayList());
        qualification.setQualificationValueName(Lists.newArrayList());
        IncentiveLimited incentiveLimited = new IncentiveLimited();
        incentiveLimited.setLimitationCode("");
        incentiveLimited.setLimitationValue(new BigDecimal("0"));
        incentiveLimiteds.add(incentiveLimited);
        luckyDrawRules.add(luckyDrawRule);
        qualifications.add(qualification);
        marketingDomain.setIncentiveLimitedFlag("01");
        marketingDomain.setIncentiveLimiteds(incentiveLimiteds);
        marketingDomain.setLuckyDrawRules(luckyDrawRules);
        marketingDomain.setQualifications(qualifications);
        List<QualificationModel> qualificationModels = new ArrayList<QualificationModel>();
        List<MarketingLanguage> marketingLanguages = new ArrayList<>();
        MarketingLanguage e = new MarketingLanguage();
        e.setActivityName("1");
        e.setLanguage("zh-cn");
        marketingLanguages.add(e);
        marketingDomain.setMarketingLanguages(marketingLanguages);

        List<MarketingPrize> marketingPrizes = new ArrayList<>();
        MarketingPrize prize = new MarketingPrize();
        List<MarketingPrizeLanguage> marketingPrizeLanguages = new ArrayList<>();
        marketingPrizeLanguages.add(new MarketingPrizeLanguage("1", "zh_cn"));
        prize.setMarketingPrizeLanguages(marketingPrizeLanguages);
        prize.setPrizeType(PrizeTypeEnum.COUPON.code());
        prize.setPrizeQuota(10);
        prize.setPrizeName("1");
        marketingPrizes.add(prize);
        marketingDomain.setMarketingPrizes(marketingPrizes);

        MarketingModel model = new MarketingModel();
        model.setActivityStatus(ActivityStatusEnum.EFFECTIVE.code());
        model.setActivityEnd(DateUtil.format(DateUtil.addDay(new Date(), 1), DateUtil.FORMAT_YYYYMMDDHHMISS_14));

        Mockito.when(languageService.deleteByActivityCode(Mockito.any())).thenReturn(1);

        ActivityModel activityModel = new ActivityModel();
        activityModel.setActivityStatus(ActivityStatusEnum.EFFECTIVE.code());
        activityModel.setActivityBegin(DateUtil.format(DateUtil.addDay(-1), DateUtil.FORMAT_YYYYMMDDHHMISS_14));
        activityModel.setActivityEnd(DateUtil.format(DateUtil.addDay(1), DateUtil.FORMAT_YYYYMMDDHHMISS_14));
        Mockito.when(marketingService.findByActivityCode(Mockito.any())).thenReturn(model);
        Mockito.when(operationLogService.insertLog(Mockito.any(), Mockito.any())).thenReturn(0);
        Mockito.when(prizeService.findListByActivityCode(Mockito.any())).thenReturn(BeanCopyUtils.jsonCopyList(marketingPrizes,PrizeModel.class));
        int i = marketingComponent.updateMarketing(marketingDomain);
        Assert.assertEquals(0, i);
    }

    @Test(expected = PromotionException.class)
    public void updateMarketing4(){
        LuckyDrawDomain marketingDomain = new LuckyDrawDomain();
        marketingDomain.setIncentiveLimitedFlag("01");
        marketingDomain.setIncentiveLimiteds(new ArrayList<>());
        marketingDomain.setLuckyDrawRules(new ArrayList<>());
        marketingDomain.setQualifications(new ArrayList<>());
        List<MarketingLanguage> marketingLanguages = new ArrayList<>();
        MarketingLanguage e = new MarketingLanguage();
        e.setActivityName("1");
        e.setLanguage("zh-cn");
        marketingLanguages.add(e);
        marketingDomain.setMarketingLanguages(marketingLanguages);

        List<MarketingPrize> marketingPrizes = new ArrayList<>();
        MarketingPrize prize = new MarketingPrize();
        List<MarketingPrizeLanguage> marketingPrizeLanguages = new ArrayList<>();
        marketingPrizeLanguages.add(new MarketingPrizeLanguage("1", "zh_cn"));
        prize.setMarketingPrizeLanguages(marketingPrizeLanguages);
        prize.setPrizeType(PrizeTypeEnum.COUPON.code());
        prize.setPrizeName("1");
        prize.setPrizeQuota(1);
        marketingPrizes.add(prize);
        marketingDomain.setMarketingPrizes(marketingPrizes);

        MarketingModel model = new MarketingModel();
        model.setActivityStatus(ActivityStatusEnum.EFFECTIVE.code());
        model.setActivityEnd(DateUtil.format(DateUtil.addDay(new Date(), 1), DateUtil.FORMAT_YYYYMMDDHHMISS_14));
        List<PrizeModel> prizeModels = new ArrayList<>();
        PrizeModel prizeModel = new PrizeModel();
        prizeModel.setPrizeName("1");
        prizeModel.setPrizeQuota(2);
        prizeModels.add(prizeModel);
        Mockito.when(marketingService.findByActivityCode(Mockito.any())).thenReturn(model);
        Mockito.when(languageService.deleteByActivityCode(Mockito.any())).thenReturn(1);
        Mockito.when(prizeService.findListByActivityCode(Mockito.any())).thenReturn(prizeModels);
        marketingComponent.updateMarketing(marketingDomain);
    }

    @Test
    public void updateMarketing5(){
        LuckyDrawDomain marketingDomain = new LuckyDrawDomain();
        marketingDomain.setIncentiveLimiteds(new ArrayList<>());
        marketingDomain.setLuckyDrawRules(new ArrayList<>());
        marketingDomain.setQualifications(new ArrayList<>());
        List<MarketingLanguage> marketingLanguages = new ArrayList<>();
        MarketingLanguage e = new MarketingLanguage();
        e.setActivityName("1");
        e.setLanguage("zh-cn");
        marketingLanguages.add(e);
        marketingDomain.setMarketingLanguages(marketingLanguages);
        marketingDomain.setMarketingPrizes(new ArrayList<>());

        MarketingModel model = new MarketingModel();
        model.setActivityStatus(ActivityStatusEnum.EFFECTIVE.code());
        model.setActivityEnd(DateUtil.format(DateUtil.addDay(new Date(), 1), DateUtil.FORMAT_YYYYMMDDHHMISS_14));
        List<PrizeModel> prizeModels = new ArrayList<>();
        PrizeModel prizeModel = new PrizeModel();
        prizeModel.setPrizeName("2");
        prizeModel.setPrizeQuota(2);
        prizeModels.add(prizeModel);
        Mockito.when(marketingService.findByActivityCode(Mockito.any())).thenReturn(model);
        Mockito.when(languageService.deleteByActivityCode(Mockito.any())).thenReturn(1);
        Mockito.when(prizeService.findListByActivityCode(Mockito.any())).thenReturn(prizeModels);
        Mockito.when(prizeService.deleteByActivityCode(Mockito.any())).thenReturn(1);
        Mockito.when(prizeLanguageService.deleteByActivityCode(Mockito.any())).thenReturn(1);
        Mockito.when(marketingService.updateByActivityCode(Mockito.any())).thenReturn(1);
        Mockito.when(languageService.insert(Mockito.any())).thenReturn(1);
        ActivityModel activityModel = new ActivityModel();
        activityModel.setActivityStatus(ActivityStatusEnum.PENDING.code());
        marketingComponent.updateMarketing(marketingDomain);
    }

    @Test
    public void updateMarketingStatus(){
        MarketingDomain marketingDomain = new MarketingDomain();
        marketingDomain.setIncentiveLimiteds(new ArrayList<>());
        marketingDomain.setLuckyDrawRules(new ArrayList<>());
        marketingDomain.setQualifications(new ArrayList<>());
        MarketingModel model = new MarketingModel();
        model.setActivityType(ActivityTypeEnum.LUCKY_DRAW.code());
        Mockito.when(marketingService.updateByActivityCode(Mockito.any())).thenReturn(1);
        Mockito.when(marketingService.findByActivityCode(Mockito.any())).thenReturn(model);
        Mockito.when(activityComponentDomain.getActivityAuditConfig(Mockito.any())).thenReturn(null);
        Mockito.doNothing().when(marketingCacheComponent).updateCacheByStatus(Mockito.any(), Mockito.any());
        Mockito.when(operationLogService.insertLog(Mockito.any(), Mockito.any())).thenReturn(0);
        int i = marketingComponent.updateMarketingStatus(marketingDomain);
        Assert.assertEquals(1, i);
    }

    @Test
    public void updateMarketingStatus1(){
        MarketingDomain marketingDomain = new MarketingDomain();
        marketingDomain.setIncentiveLimiteds(new ArrayList<>());
        marketingDomain.setLuckyDrawRules(new ArrayList<>());
        marketingDomain.setQualifications(new ArrayList<>());
        marketingDomain.setActivityStatus("04");
        MarketingModel model = new MarketingModel();
        model.setTenantCode("1");
        model.setActivityType(ActivityTypeEnum.FLASH_SALE.code());
        model.setActivityEnd(DateUtil.format(DateUtil.addDay(1),DateUtil.FORMAT_YYYYMMDDHHMISS_14));
        model.setActivityBegin(DateUtil.format(new Date(),DateUtil.FORMAT_YYYYMMDDHHMISS_14));
        Mockito.when(marketingService.updateByActivityCode(Mockito.any())).thenReturn(1);
        Mockito.when(marketingService.findByActivityCode(Mockito.any())).thenReturn(model);
        Mockito.doNothing().when(marketingCacheComponent).updateCacheByStatus(Mockito.any(), Mockito.any());
        Mockito.when(operationLogService.insertLog(Mockito.any(), Mockito.any())).thenReturn(0);
        Mockito.when(activityComponentDomain.getActivityAuditConfig(Mockito.any())).thenReturn(null);
        int i = marketingComponent.updateMarketingStatus(marketingDomain);
        Assert.assertEquals(1, i);
    }

    @Test
    public void updateMarketingStatus2(){
        MarketingDomain marketingDomain = new MarketingDomain();
        marketingDomain.setIncentiveLimiteds(new ArrayList<>());
        marketingDomain.setLuckyDrawRules(new ArrayList<>());
        marketingDomain.setQualifications(new ArrayList<>());
        marketingDomain.setActivityStatus("07");
        MarketingModel model = new MarketingModel();
        model.setTenantCode("1");
        model.setActivityType(ActivityTypeEnum.FLASH_SALE.code());
        model.setActivityEnd(DateUtil.format(DateUtil.addDay(1),DateUtil.FORMAT_YYYYMMDDHHMISS_14));
        model.setActivityBegin(DateUtil.format(new Date(),DateUtil.FORMAT_YYYYMMDDHHMISS_14));
        Mockito.when(activityComponentDomain.getActivityAuditConfig(Mockito.any())).thenReturn(null);
        Mockito.when(marketingService.updateByActivityCode(Mockito.any())).thenReturn(1);
        Mockito.when(marketingService.findByActivityCode(Mockito.any())).thenReturn(model);
        Mockito.doNothing().when(marketingCacheComponent).updateCacheByStatus(Mockito.any(), Mockito.any());
        Mockito.when(operationLogService.insertLog(Mockito.any(), Mockito.any())).thenReturn(0);
        int i = marketingComponent.updateMarketingStatus(marketingDomain);
        Assert.assertEquals(1, i);
    }

    @Test
    public void updateMarketingStatus_marketing_group_1_00(){
        MarketingDomain marketingDomain = new MarketingDomain();
        marketingDomain.setIncentiveLimiteds(new ArrayList<>());
        marketingDomain.setLuckyDrawRules(new ArrayList<>());
        marketingDomain.setQualifications(new ArrayList<>());
        marketingDomain.setActivityStatus("07");
        MarketingModel model = new MarketingModel();
        model.setTenantCode("1");
        model.setActivityType(ActivityTypeEnum.FLASH_SALE.code());
        model.setActivityEnd(DateUtil.format(DateUtil.addDay(1),DateUtil.FORMAT_YYYYMMDDHHMISS_14));
        model.setActivityBegin(DateUtil.format(new Date(),DateUtil.FORMAT_YYYYMMDDHHMISS_14));
        model.setActivityType(ActivityTypeEnum.GROUP.code());
        Mockito.when(activityComponentDomain.getActivityAuditConfig(Mockito.any())).thenReturn(null);
        Mockito.when(marketingService.updateByActivityCode(Mockito.any())).thenReturn(1);
        Mockito.when(marketingService.findByActivityCode(Mockito.any())).thenReturn(model);
        Mockito.doNothing().when(marketingCacheComponent).updateCacheByStatus(Mockito.any(), Mockito.any());

        Mockito.when(operationLogService.insertLog(Mockito.any(), Mockito.any())).thenReturn(0);

        MarketingGroupMode marketingGroup = new MarketingGroupMode();
        marketingGroup.setAutoGroupFlag(1);
        Mockito.when(marketingGroupService.findMarketingGroupByActivityCode(Mockito.any(), Mockito.any())).thenReturn(marketingGroup);

        List<MarketingGroupCodeMode> list = new ArrayList<>();
        MarketingGroupCodeMode marketingGroupCodeMode = new MarketingGroupCodeMode();
        marketingGroupCodeMode.setGroupStatus(MarketingGroupStatusEnum.GROUP_NO_START.code());
        list.add(marketingGroupCodeMode);

        Mockito.when(marketingGroupCodeService.queryGroupByActivityCode(Mockito.any(), Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(list);

        int i = marketingComponent.updateMarketingStatus(marketingDomain);
        Assert.assertEquals(1, i);
    }


    @Test
    public void updateMarketingStatus_marketing_group_1_01(){
        MarketingDomain marketingDomain = new MarketingDomain();
        marketingDomain.setIncentiveLimiteds(new ArrayList<>());
        marketingDomain.setLuckyDrawRules(new ArrayList<>());
        marketingDomain.setQualifications(new ArrayList<>());
        marketingDomain.setActivityStatus("07");
        MarketingModel model = new MarketingModel();
        model.setTenantCode("1");
        model.setActivityType(ActivityTypeEnum.FLASH_SALE.code());
        model.setActivityEnd(DateUtil.format(DateUtil.addDay(1),DateUtil.FORMAT_YYYYMMDDHHMISS_14));
        model.setActivityBegin(DateUtil.format(new Date(),DateUtil.FORMAT_YYYYMMDDHHMISS_14));
        model.setActivityType(ActivityTypeEnum.GROUP.code());
        Mockito.when(activityComponentDomain.getActivityAuditConfig(Mockito.any())).thenReturn(null);
        Mockito.when(marketingService.updateByActivityCode(Mockito.any())).thenReturn(1);
        Mockito.when(marketingService.findByActivityCode(Mockito.any())).thenReturn(model);
        Mockito.doNothing().when(marketingCacheComponent).updateCacheByStatus(Mockito.any(), Mockito.any());

        Mockito.when(operationLogService.insertLog(Mockito.any(), Mockito.any())).thenReturn(0);

        MarketingGroupMode marketingGroup = new MarketingGroupMode();
        marketingGroup.setAutoGroupFlag(1);
        Mockito.when(marketingGroupService.findMarketingGroupByActivityCode(Mockito.any(), Mockito.any())).thenReturn(marketingGroup);

        List<MarketingGroupCodeMode> list = new ArrayList<>();
        MarketingGroupCodeMode marketingGroupCodeMode = new MarketingGroupCodeMode();
        marketingGroupCodeMode.setGroupStatus(MarketingGroupStatusEnum.GROUP_PROCESSING.code());
        marketingGroupCodeMode.setInventory(1);
        list.add(marketingGroupCodeMode);

        Mockito.when(marketingGroupCodeService.queryGroupByActivityCode(Mockito.any(), Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(list);

        int i = marketingComponent.updateMarketingStatus(marketingDomain);
        Assert.assertEquals(1, i);
    }



    @Test
    public void updateMarketingStatus_marketing_group_0(){
        MarketingDomain marketingDomain = new MarketingDomain();
        marketingDomain.setIncentiveLimiteds(new ArrayList<>());
        marketingDomain.setLuckyDrawRules(new ArrayList<>());
        marketingDomain.setQualifications(new ArrayList<>());
        marketingDomain.setActivityStatus("07");
        MarketingModel model = new MarketingModel();
        model.setTenantCode("1");
        model.setActivityType(ActivityTypeEnum.FLASH_SALE.code());
        model.setActivityEnd(DateUtil.format(DateUtil.addDay(1),DateUtil.FORMAT_YYYYMMDDHHMISS_14));
        model.setActivityBegin(DateUtil.format(new Date(),DateUtil.FORMAT_YYYYMMDDHHMISS_14));
        model.setActivityType(ActivityTypeEnum.GROUP.code());
        Mockito.when(activityComponentDomain.getActivityAuditConfig(Mockito.any())).thenReturn(null);
        Mockito.when(marketingService.updateByActivityCode(Mockito.any())).thenReturn(1);
        Mockito.when(marketingService.findByActivityCode(Mockito.any())).thenReturn(model);
        Mockito.doNothing().when(marketingCacheComponent).updateCacheByStatus(Mockito.any(), Mockito.any());

        Mockito.when(operationLogService.insertLog(Mockito.any(), Mockito.any())).thenReturn(0);

        MarketingGroupMode marketingGroup = new MarketingGroupMode();
        marketingGroup.setAutoGroupFlag(0);
        Mockito.when(marketingGroupService.findMarketingGroupByActivityCode(Mockito.any(), Mockito.any())).thenReturn(marketingGroup);

        List<MarketingGroupCodeMode> list = new ArrayList<>();
        MarketingGroupCodeMode marketingGroupCodeMode = new MarketingGroupCodeMode();
        marketingGroupCodeMode.setGroupStatus(MarketingGroupStatusEnum.GROUP_PROCESSING.code());
        marketingGroupCodeMode.setInventory(1);
        list.add(marketingGroupCodeMode);

        Mockito.when(marketingGroupCodeService.queryGroupByActivityCode(Mockito.any(), Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(list);

        int i = marketingComponent.updateMarketingStatus(marketingDomain);
        Assert.assertEquals(1, i);
    }



    @Test
    public void findMarketing(){
        MarketingDomain marketingDomain = new MarketingDomain();
        MarketingModel marketingModel = new MarketingModel();
        marketingDomain.setIncentiveLimiteds(new ArrayList<IncentiveLimited>());
        marketingDomain.setLuckyDrawRules(new ArrayList<LuckyDrawRule>());
        marketingDomain.setQualifications(new ArrayList<Qualification>());
        marketingModel.setActivityName("1");
        List<MarketingLanguageModel> marketingLanguageModels = new ArrayList<>();
        List<PrizeModel> prizeModels = new ArrayList<>();
        PrizeModel prizeModel = new PrizeModel();
        prizeModel.setPrizeNo("12");
        prizeModels.add(prizeModel);
        List<PrizeLanguageModel> prizeLanguageModels = new ArrayList<>();
        PrizeLanguageModel prizeLanguageModel1 = new PrizeLanguageModel();
        prizeLanguageModel1.setPrizeNo("13");
        prizeLanguageModels.add(prizeLanguageModel1);
        PrizeLanguageModel prizeLanguageModel2 = new PrizeLanguageModel();
        prizeLanguageModel2.setPrizeNo("12");
        prizeLanguageModels.add(prizeLanguageModel2);
        Mockito.when(marketingService.findByActivityCode(Mockito.any())).thenReturn(marketingModel);
        Mockito.when(languageService.findListByActivityCode(Mockito.any())).thenReturn(marketingLanguageModels);
        Mockito.when(prizeService.findListByActivityCode(Mockito.any())).thenReturn(prizeModels);
        Mockito.when(prizeLanguageService.findListByActivityCode(Mockito.any())).thenReturn(prizeLanguageModels);
        MarketingFindResult marketing = marketingComponent.findMarketing(marketingDomain);
        Assert.assertEquals(marketingModel.getActivityName(), marketing.getActivityName());
    }


    @Test
    public void queryMarketingList(){
        MarketingQueryInDto inDto = new MarketingQueryInDto();

        PageData<MarketingModel> marketingModelPageData = new PageData<>();

        MarketingModel model = new MarketingModel();
        model.setCreateUser("1");
        List<MarketingModel> list = new ArrayList<>();
        list.add(model);
        marketingModelPageData.setList(list);
        marketingModelPageData.setTotal(1L);
        List<QueryOpUserAccountListResult> idmOpUserAccount = new ArrayList<>();
        QueryOpUserAccountListResult opUserAccountListResult = new QueryOpUserAccountListResult();
        opUserAccountListResult.setUserCode("1");
        idmOpUserAccount.add(opUserAccountListResult);
        Mockito.when(idmFeignClientComponent.getIdmOpUserAccount(Mockito.any())).thenReturn(idmOpUserAccount);
        Mockito.when(marketingService.queryMarketingList(Mockito.any())).thenReturn(marketingModelPageData);
        PageResult<MarketingQueryResult> resultPageResult = marketingComponent.queryMarketingList(inDto);
        Assert.assertEquals(marketingModelPageData.getTotal(), resultPageResult.getData().getTotal());
    }


    @Test
    public void queryMarketingList1(){
        MarketingQueryInDto inDto = new MarketingQueryInDto();

        PageData<MarketingModel> marketingModelPageData = new PageData<>();

        MarketingModel model = new MarketingModel();
        model.setCreateUser("1");
        List<MarketingModel> list = new ArrayList<>();
        list.add(model);
        marketingModelPageData.setList(list);
        marketingModelPageData.setTotal(1L);
        List<QueryOpUserAccountListResult> idmOpUserAccount = new ArrayList<>();
        QueryOpUserAccountListResult opUserAccountListResult = new QueryOpUserAccountListResult();
        opUserAccountListResult.setUserCode("1");
        idmOpUserAccount.add(opUserAccountListResult);
        Mockito.when(idmFeignClientComponent.getIdmOpUserAccount(Mockito.any())).thenReturn(idmOpUserAccount);
        Mockito.when(marketingService.queryMarketingList(Mockito.any())).thenReturn(marketingModelPageData);

        List<FlashSaleStoreModel> listByActivityCode = new ArrayList<>();
        FlashSaleStoreModel flashSaleStoreModel = new FlashSaleStoreModel();
        flashSaleStoreModel.setStoreName("1");
        listByActivityCode.add(flashSaleStoreModel);

        Mockito.when(flashSaleStoreService.findListByActivityCode(Mockito.any())).thenReturn(listByActivityCode);

        PageResult<MarketingQueryResult> resultPageResult = marketingComponent.queryMarketingList(inDto);
        Assert.assertEquals(marketingModelPageData.getTotal(), resultPageResult.getData().getTotal());
    }

    @Test(expected = PromotionException.class)
    public void extendMarketing(){

        MarketingDomain marketingDomain = new MarketingDomain();
        marketingDomain.setIncentiveLimiteds(new ArrayList<IncentiveLimited>());
        marketingDomain.setLuckyDrawRules(new ArrayList<LuckyDrawRule>());
        marketingDomain.setQualifications(new ArrayList<Qualification>());
        MarketingModel selectOne = new MarketingModel();
        selectOne.setActivityStatus(ActivityStatusEnum.EFFECTIVE.code());
        selectOne.setActivityEnd(DateUtil.format(DateUtil.addDay(-1), DateUtil.FORMAT_YYYYMMDDHHMISS_14));
        Mockito.when(marketingService.findByActivityCode(Mockito.any())).thenReturn(selectOne);
        int i = marketingComponent.extendMarketing(marketingDomain, "1");
        Assert.assertEquals(1, i);
    }

    @Test(expected = PromotionException.class)
    public void extendMarketing1(){
        MarketingDomain marketingDomain = new MarketingDomain();
        marketingDomain.setIncentiveLimiteds(new ArrayList<IncentiveLimited>());
        marketingDomain.setLuckyDrawRules(new ArrayList<LuckyDrawRule>());
        marketingDomain.setQualifications(new ArrayList<Qualification>());
        MarketingModel selectOne = new MarketingModel();
        selectOne.setActivityStatus(ActivityStatusEnum.END.code());
        Mockito.when(marketingService.findByActivityCode(Mockito.any())).thenReturn(selectOne);
        int i = marketingComponent.extendMarketing(marketingDomain, "1");
        Mockito.when(operationLogService.insertLog(Mockito.any(), Mockito.any())).thenReturn(0);
        Assert.assertEquals(1, i);
    }

    @Test(expected = PromotionException.class)
    public void extendMarketing2(){
        MarketingDomain marketingDomain = new MarketingDomain();
        MarketingModel selectOne = new MarketingModel();
        marketingDomain.setIncentiveLimiteds(new ArrayList<IncentiveLimited>());
        marketingDomain.setLuckyDrawRules(new ArrayList<LuckyDrawRule>());
        marketingDomain.setQualifications(new ArrayList<Qualification>());
        selectOne.setActivityStatus(ActivityStatusEnum.CLOSURE.code());
        Mockito.when(marketingService.findByActivityCode(Mockito.any())).thenReturn(selectOne);
        int i = marketingComponent.extendMarketing(marketingDomain, "1");
        Assert.assertEquals(1, i);
    }

    @Test(expected = PromotionException.class)
    public void extendMarketing3(){
        MarketingDomain marketingDomain = new MarketingDomain();
        marketingDomain.setIncentiveLimiteds(new ArrayList<IncentiveLimited>());
        marketingDomain.setLuckyDrawRules(new ArrayList<LuckyDrawRule>());
        marketingDomain.setQualifications(new ArrayList<Qualification>());
        MarketingModel selectOne = new MarketingModel();
        selectOne.setActivityStatus(ActivityStatusEnum.EFFECTIVE.code());
        selectOne.setActivityEnd(DateUtil.format(DateUtil.addDay(2), DateUtil.FORMAT_YYYYMMDDHHMISS_14));
        Mockito.when(marketingService.findByActivityCode(Mockito.any())).thenReturn(selectOne);
        int i = marketingComponent.extendMarketing(marketingDomain, DateUtil.format(DateUtil.addDay(1), DateUtil.FORMAT_YYYYMMDDHHMISS_14));
        Assert.assertEquals(1, i);
    }

    @Test
    public void extendMarketing_normal(){
        MarketingDomain marketingDomain = new MarketingDomain();
        marketingDomain.setIncentiveLimiteds(new ArrayList<IncentiveLimited>());
        marketingDomain.setLuckyDrawRules(new ArrayList<LuckyDrawRule>());
        marketingDomain.setQualifications(new ArrayList<Qualification>());
        MarketingModel selectOne = new MarketingModel();
        selectOne.setActivityStatus(ActivityStatusEnum.EFFECTIVE.code());
        selectOne.setActivityEnd(DateUtil.format(DateUtil.addDay(1), DateUtil.FORMAT_YYYYMMDDHHMISS_14));
        Mockito.when(marketingService.findByActivityCode(Mockito.any())).thenReturn(selectOne);
        Mockito.when(marketingService.updateByActivityCode(Mockito.any())).thenReturn(1);
        Mockito.when(operationLogService.insertLog(Mockito.any(), Mockito.any())).thenReturn(0);
        int i = marketingComponent.extendMarketing(marketingDomain, DateUtil.format(DateUtil.addDay(2), DateUtil.FORMAT_YYYYMMDDHHMISS_14));
        Assert.assertEquals(1, i);
    }

    @Test
    public void expireMarketing(){
        Mockito.when(marketingService.queryShouldExpireMarketingList()).thenReturn(null);
        Mockito.when(marketingService.expireMarketing()).thenReturn(1);
        int i = marketingComponent.expireMarketing();
        Assert.assertEquals(1, i);
    }

    @Test
    public void expireMarketing1(){
        List<MarketingModel> marketingModels = new ArrayList<>();
        marketingModels.add(new MarketingModel());
        Mockito.when(marketingService.queryShouldExpireMarketingList()).thenReturn(marketingModels);
        Mockito.when(marketingService.expireMarketing()).thenReturn(1);
        int i = marketingComponent.expireMarketing();
        Assert.assertEquals(1, i);
    }
}
