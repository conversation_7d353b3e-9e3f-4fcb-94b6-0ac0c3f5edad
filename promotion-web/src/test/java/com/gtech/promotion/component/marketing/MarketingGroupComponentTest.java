package com.gtech.promotion.component.marketing;

import com.github.pagehelper.PageInfo;
import com.gtech.commons.result.Result;
import com.gtech.member.web.vo.result.QueryMemberListByConditionResult;
import com.gtech.promotion.code.activity.OrderStatusEnum;
import com.gtech.promotion.code.activity.SelectorProductTypeEnum;
import com.gtech.promotion.code.marketing.MarketingGroupStatusEnum;
import com.gtech.promotion.code.marketing.UserGroupStatusEnum;
import com.gtech.promotion.component.flashsale.FlashSaleComponent;
import com.gtech.promotion.dao.model.marketing.MarketingGroupCodeMode;
import com.gtech.promotion.dao.model.marketing.MarketingGroupMode;
import com.gtech.promotion.dao.model.marketing.MarketingGroupUserMode;
import com.gtech.promotion.dao.model.marketing.MarketingModel;
import com.gtech.promotion.dao.model.marketing.flashsale.FlashSaleOrderDetailModel;
import com.gtech.promotion.dao.model.marketing.flashsale.FlashSaleOrderModel;
import com.gtech.promotion.exception.PromotionException;
import com.gtech.promotion.feign.MemberFeignClient;
import com.gtech.promotion.pojo.GroupUserContent;
import com.gtech.promotion.service.flashsale.FlashSaleOrderDetailService;
import com.gtech.promotion.service.flashsale.FlashSaleOrderService;
import com.gtech.promotion.service.marketing.MarketingGroupCodeService;
import com.gtech.promotion.service.marketing.MarketingGroupService;
import com.gtech.promotion.service.marketing.MarketingGroupUserService;
import com.gtech.promotion.service.marketing.MarketingService;
import com.gtech.promotion.vo.param.marketing.MarketingGroupUserListParam;
import com.gtech.promotion.vo.param.marketing.MarketingGroupUserParam;
import com.gtech.promotion.vo.param.marketing.flashsale.FindGroupUserParam;
import com.gtech.promotion.vo.result.flashsale.MarketingGroupUserListResult;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.List;

import static org.mockito.Mockito.when;

/**
 * @Description :
 * <AUTHOR> gaojie.li
 * @Date : 2023/9/6 17:39
 */
@RunWith(MockitoJUnitRunner.class)
public class MarketingGroupComponentTest {

    @InjectMocks
    private MarketingGroupComponent marketingGroupComponent;

    @Mock
    private MarketingGroupUserService marketingGroupUserService;

    @Mock
    private MarketingGroupService marketingGroupService;

    @Mock
    private MarketingGroupCodeService marketingGroupcodeService;

    @Mock
    private MemberFeignClient memberFeignClient;

    @Mock
    private FlashSaleOrderService flashSaleOrderService;
    @Mock
    private FlashSaleOrderDetailService flashSaleOrderDetailService;

    @Mock
    private MarketingService marketingService;

    @Mock
    private FlashSaleComponent flashSaleComponent;


    @Test
    public void queryAllGroupUserList_null(){


        MarketingGroupUserListParam groupMode  = new MarketingGroupUserListParam();
        groupMode.setTenantCode("11");
        groupMode.setMarketingGroupCode("!");
        groupMode.setDomainCode("!1");
        PageInfo<MarketingGroupUserMode> pageInfo = new PageInfo<>();
        List<MarketingGroupUserMode> userModes = new ArrayList<>();
        MarketingGroupUserMode groupUserCode = new MarketingGroupUserMode();
        groupUserCode.setEffectiveHour(1);
        groupUserCode.setGroupStatus(UserGroupStatusEnum.FINISH.code());
        userModes.add(groupUserCode);
        pageInfo.setList(userModes);
        Mockito.when(marketingGroupUserService.queryAllGroupUserListByCondition(Mockito.any())).thenReturn(pageInfo);

        Result<List<QueryMemberListByConditionResult>> listResult = new Result<>();
        List<QueryMemberListByConditionResult> results = new ArrayList<>();
        QueryMemberListByConditionResult result1 = new QueryMemberListByConditionResult();
        result1.setMemberCode("212");

        String json = "{\n" +
                "    \"score\": 0,\n" +
                "    \"nickName\": \"宁可\",\n" +
                "    \"wechatImg\": \"data:image/png;base64,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\",\n" +
                "    \"totalScore\": 0,\n" +
                "    \"isActivated\": true,\n" +
                "    \"blockedStatus\": 0,\n" +
                "    \"annualCostScore\": 0,\n" +
                "    \"isPushInformation\": 1,\n" +
                "    \"hasBoundSportWeapp\": false,\n" +
                "    \"annualAccumulatedScore\": 0\n" +
                "}";

        result1.setExtParams(json);

        results.add(result1);

        QueryMemberListByConditionResult result = new QueryMemberListByConditionResult();
        result.setMemberCode("21");

        result.setExtParams(json);
        results.add(result);
        listResult.setData(results);
        listResult.setCode("0");

        Mockito.when(memberFeignClient.queryMemberListByMemberCode(Mockito.any())).thenReturn(listResult);
        PageInfo<MarketingGroupUserListResult> pageInfo1 = marketingGroupComponent.queryAllGroupUserList(groupMode);
        Assert.assertEquals(1,pageInfo1.getList().size());

    }

    @Test
    public void queryAllGroupUserList(){

        MarketingGroupUserListParam groupMode  = new MarketingGroupUserListParam();
        groupMode.setTenantCode("11");
        groupMode.setMarketingGroupCode("!");
        groupMode.setDomainCode("!1");

        MarketingGroupMode byActivityCode = new MarketingGroupMode();
        byActivityCode.setGroupSize(2);
        PageInfo<MarketingGroupUserMode> pageInfo = new PageInfo<>();
        List<MarketingGroupUserMode> userModes = new ArrayList<>();
        MarketingGroupUserMode groupUserCode = new MarketingGroupUserMode();
        groupUserCode.setEffectiveHour(1);
        groupUserCode.setGroupStatus(UserGroupStatusEnum.FINISH.code());
        userModes.add(groupUserCode);
        pageInfo.setList(userModes);
        Mockito.when(marketingGroupUserService.queryAllGroupUserListByCondition(Mockito.any())).thenReturn(pageInfo);

        MarketingGroupCodeMode marketingGroupCodeMode = new MarketingGroupCodeMode();

        Mockito.when(marketingGroupcodeService.queryGroupByMarketingGroupCode(Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(marketingGroupCodeMode);

        Result<List<QueryMemberListByConditionResult>> listResult = new Result<>();
        List<QueryMemberListByConditionResult> results = new ArrayList<>();
        QueryMemberListByConditionResult result1 = new QueryMemberListByConditionResult();
        result1.setMemberCode("212");

        String json = "{\n" +
                "    \"score\": 0,\n" +
                "    \"nickName\": \"宁可\",\n" +
                "    \"wechatImg\": \"data:image/png;base64,/9j/4AAQSkZJRgABAQAASABIAAD/2wBDAAcFBQYFBAcGBgYIBwcICxILCwoKCxYPEA0SGhYbGhkWGRgcICgiHB4mHhgZIzAkJiorLS4tGyIyNTEsNSgsLSz/2wBDAQcICAsJCxULCxUsHRkdLCwsLCwsLCwsLCwsLCwsLCwsLCwsLCwsLCwsLCwsLCwsLCwsLCwsLCwsLCwsLCwsLCz/wAARCACEAIQDASIAAhEBAxEB/8QAHAABAAEFAQEAAAAAAAAAAAAAAAcBBAUGCAMC/8QAORAAAQMDAwEGAwcCBgMAAAAAAQIDBAAFEQYSITEHEyJBUWEUcYEjMkJSkaGxwdEIFWKC4fAXovH/xAAZAQEBAQEBAQAAAAAAAAAAAAAAAQIDBAX/xAAfEQEBAQACAwADAQAAAAAAAAAAAQIDERIhMSJBUTL/2gAMAwEAAhEDEQA/AOkKUpQKVTzqtBTypSlA8qUqIbR20uWzV8/TmuWIsByO+WWp0VKwwrnjcFElIIwc5x6461VbNqjtb0zpW+/5PJMyZNTgvIhsd53AIyN5yOoPQZNbFp3VFn1XbjNs01EplKtiwAUqbV+VSTgpPzNcr6htd81HvuFvaLz1ykuS3sLCSrev7PqegGAPTirns21TdNL6uRJdKgfiW4c5lXVbalbTu9VIPIPXqOhrlN9tXPTrWlKV1ZKU8qUDNVzVKUFaVSlRCq1Sq0DypSlBQ0qtBQYi7Xd22S2R3IWytJJ5wc58qiPtDtdv1dqxCLc13jsiOBIG0BIwcb1kc+iR5kjggJNSL2gT0QbMXu8ShbCFPeL0A6dR1xjrUHXDtWNveB09Z98i4FLqnnsbdv3QnbwSQEgdePKuXL3Z1HTP9bQNIy7LYQiK4+JSGu7BK/Crkeh4wOny6+uhQ9NSImoEPSw8oOy0rMrZk7yreVFOenU586mDT17nXfSyJl2hJjPqJSUJB5OeoAr0ttuYkxwFBCNqfwny5A/j9q8Xlc307ffqTULDiErTnChkZGD+lfVa3Y7q3At/w9wkBPdk7XFnIxnoT5Y4/wCitjBCkgg5B5Br6OdTU7eezpWlKVpClKUClKUClVpUQpSrefNZt0F6XIUUtMpKlY60Fwa+HHEstKcWrahAyT6GtRX2iwWJxakRJIawk72kd4UZ/Mkc/pnpWZtmoLLqVp1mFMbkEAhxogpcSM4yUnBH6VnO86+Vrxs+oM7cr3c5Hw/2Dv8AljivEtP3dwJ2pV6Y6+hJ9qh56X3cVUgIUUoxlYSSBnoCR611PeokWI+7ClOMOMOKDR74gIVu4CTnjnOK0LtatlosnZ/IgCI01ucQlCWWtiEuZB42jHQeeM8/KlRoGn+068SFMRHS7IZZa7tmG2NiXSeMKIG7pnHPWpqtl8iCY+87JYQvaVu+IZQM4COOvi3Y/wCa5hhSXLZMYlRlqbdaO9DgONvH9jUv6Ziak15Lhzbg0zCt8Z0OtLbZ2reUMYAz1SMdTx6Z8uG+P37dM6/qXLZEclKcbxtW8NygrkIznJI+ZOB51cydX2LSN+temZU1QclNkpU4vd3IA4K1E8biDj3B8qw+tdYxuznS/ejbIusrIjtKOd68cqV/pTx+w4zXNMq5XG6XB25XOWuVPfV3jryupV7egAAAA6AV24OLpnenboIIyOQarXOPZz2zyNPJbtd/3ybYnwoeHK2B/VPt5eXpXQNqu9vvlubn2uYzMiufdcaVkfL2PtXbWbWJe17SlKypSlKCtKUqIVh9Uud1p2QvaFYKOD0+8azFRrrXVikXuFaEqyiU8UgdE7U9ST5nJGB9fKs7/wA1c/Gu3ZLiW21tutkSHW21tOg7RglShxySRtH+0eprErv7emddtrtjSo8mZhl87i5vaKgSUpOSMAZyMcgir3UGorNaGkm4zG+9Q6vYBzsG7k/pWhw9eWlCJN8kNyEXNcYx4ZWjclRVuIOR0PiT18kivDwZtvb0bvUV1/rtvUTEm2R0n4ApKSV8F1WfvfTkgeo59vi9ayuNx7H7Wzc4iZL0vLKpSnPEktLwCU9SopSOc85Oa0GQ9uWRnrzWzRWlar0/YbHare8u5sOvt88JcCvHuyeMcKHtsNfS3J+nmiw0RJtrWrGTOtQuaTnDIQVqBGOUpHCiACcEHOOOcV0lAv8Aa4NtN6VIQbe0wp3vEdMAdAPXjGPXioHu1kh9mjam3ZDc/VTifCUKPdQQR97/AFLx0z88Dz1aFfbjF0zJsAkqXCkvJfUFdUkZyB7E7Tj1T7msePm0y+p9TTNYaikXeacbztZazkNNjokf19SSasoEGVdJqIkJhch9f3UIGT8/Ye9YxhudcZiLdaoj8yWv7rMdBWs++B0HvU99k2iZmmIspUiG8m4SkIW666FKSChbiVITkY8JAOPMHIz5dbuYnpjruvXs+7GUwZDd11EtDroH2URAylJ/MonqfQDp1yeMS1vVBcT8NFdWhR8ZBSEDjqrJz7ZANeTKJRwoyUK9U93wP3zVnfr45ZY0NASHH585iG0EpPRawFkjnogLOfavP5XV7rfXbZ0K3oSrBGRnB6mq14ww58CwHcd53ad2Omcc17V1QpSlBWlKVEW1wU8i3vqYGXAglNQ5rm3OTrB3zCQXoa++BPXaAd3z48vepsqPZzQauEhsAbUuKAHtmlnc6WXpoE7S2l7vptqVPgNqPdJIUFrbycZ5wfX51EOqILkN1qKWO6ZYWCgI+6MZxUh6w1q7oi8sW2fDXJtrjKVsOJCVeykKSeD88g8+dXukTadaQ5Ulz4YtNSAU/DZJCfyqSocevSvBi64/yvx6L1r1EKFWVZqX9NaltOgeyu23htlt/UctqQ1ESocoSZC9yyPy+FHzIwPKri/9jcW6pU7Z5zcF0nBC0ZaVk5BAByk9PLHyrS7z2ca0YUgSLeZbcdpLTS47iVpKR0CU5z1JOMZySa9s5ccn7cbmxqE+e9NmPTZr6nn3llbjizlSlHrVvCbl3O6xYEdlaQ+sBSgPEE58SseQA5rNJ0reUzG2JVulQi4lSguSw4gYHX8OT5DgHkgedSjoiwp0fqaMZ7Coka4Rgw5IwXGVLzkErKUqbPlsUkDng54HTWup6ZSNojTVktuiWJNmtpt/xjCVOoeGXUK/GFqPKiDuHPp08q2ZFoWynMeU42odMdP0qsNxNvaVFlLCUAlSFn7qknr+/wDNZBS0tNKWo+FIJJ964fWnnCeW/FStwYWCpKvmCQf4r5fS2sOSHE5DBSEn3CgT+4H6V7tZ7pJKdhIyU+hPWqsMb0COkkpH31nr/wDT/X5VJ9KyDSSllCT1CQDX3Sld2SlKUFaUpUR8rWlttS1HCUgkn2qN7itcpMlaeHHdxTz5np+9b9dd/wDlUnYMqLZHHp51HkpvfHUC33uCFbPzYOcfSl+LEc9uNvTM0yXUILzja/AQM4wcqOflkVCOlNWS9KzXHWNy23kFC0BRT9R71P3a/AdGipU+KEkIWSpLiyEFCjz0I5z/AGrmliM5ICihJOCBnyycn+Aa83BJcXNdd3qyxO2lO0NF1gKtVwkBIfTtjrP4VDoSeuc1vib4YU5tqVcYyYrKT4fvKUdo6/rXLMZ1+1vAONlxGQrwnp8jUrXGa3coSb1YXzOioQBKChhba9oJKh5efPTrXPl4fG+msb7+ty1ld7tcI8xHdlNsjoC1OE7NwOMKQocg88YFU7Lr1cZKFRXb0u5BlX2fxKMuKAPULB8iOigfLkVH0vU7kizGOp1bkdxv4coKuQE+IfoK9uzu/C29oFkQSExpEhMVSfLC8Af+wSK1xZ1J7TfTpyTEYutucjFbzDbySELR4HGljIO3PQj9Dz1FatfrlG0Xpmx2FV1emPXCSiIy9MWkrU0DuXkgAEbfAOPxJ61smp5r8SyPfCSG4z6ihtL7hG1oqWlO4544CifpWChXPTGqtbMGyREXi4WtotG4jK48NJ9FE4Us4/DknByoCu+XNkBf5s3Use0W1tlbjO1+471eJhtQV3aEj8yiMnOMJ9yK3KMyWGQlWCokqUR6mviJAiwQv4dhttbp3OLSkBTivzKPmKua1MslKUrYUqtKiFKUoKGtPvtpMJ/v2U/YOHy/CfStwxXw60h9pTbiQpChgg+dVUPap0zH1NBbjSE5CV7gdxTgHhQ46gjyPHHSon7Q7Pa7BIgWK1sIaQhPxD5SOVKPhSD8hk/7qnmUhLct1tBylKyB8gagjtOZSzrx/wC1U4pxpDp3ADZkbdo+ic/WmMya7LGiTUNhweEEZBx9elXMG7PWi9t3G1MtxikbVsFRWh5PmlWeSDXhcEDY2MkErzkewry2kda6azL9JWbjs2y4h11pQgvOtqzFWrwJc9UqJ6ex4rBNSkW+5xpMd12Q/FdS8kNpwApKt3U+48qovvD4W8An8R6D6eVe8KCGUblEqKucnzrnOL36W6TN2vXpy7xI2nETW5CbpKRNCUYBbZKR3SFcDqVbsH0Bzg1Nuh9M2nSmlY1ttASplIy48Bgur6KUfqMewFcjJmrkXJEqUrKkpSgKHkEpCU/oAK6+0fcG7lpyPJbWhXepS6Qg52laQog488k5q3HjmM99s7SlKyFKUoFKUoFKUoFeEx/4aE89+RBI+flXvWG1M/3Vq7sHl1YH0HP9KDTySTk9a567UJx/8k3HadyW0tNkfJAP8k10LXMGs5HxeuLy8DkGW4kH2BwP4qqxk2UFiKoZzvII+n/Oa91kKZQfMcGsc4ncpHsrPy4r7dkLbZHdo3LzjnoK3Nf1F4hTSDveWEpHkfOvly5qcyI6No6b1j+B/erJttWdzit7h8z5fOr2M2knONxp5X9HTzVudLZy6tagQrcrAP0Fdc9js9M3Q9tEeMzHYbiJbUlvycQpSSen4sFfmfHXKLySHWnEjocH5Guj/wDDrOQ9pW5wt32saVvxn8K0jHHzQr9axVTFSgqhqIqKUpQKUpQKUpQK1fViz38ZHkEk/wDf0pSqrXq5RuqibrKWeSp1RPzJNKUGOWopeTj8QOfpXqD4QfalKD1jNh1zCs49qySUhCQEjApSgqeRU2/4a0BU7ULhJylthI+pc/sKUoJ9FKUoK086UqIUpSg//9k=\",\n" +
                "    \"totalScore\": 0,\n" +
                "    \"isActivated\": true,\n" +
                "    \"blockedStatus\": 0,\n" +
                "    \"annualCostScore\": 0,\n" +
                "    \"isPushInformation\": 1,\n" +
                "    \"hasBoundSportWeapp\": false,\n" +
                "    \"annualAccumulatedScore\": 0\n" +
                "}";

        result1.setExtParams(json);

        results.add(result1);

        QueryMemberListByConditionResult result = new QueryMemberListByConditionResult();
        result.setMemberCode("21");

        result.setExtParams(json);
        results.add(result);
        listResult.setData(results);
        listResult.setCode("0");

        Mockito.when(memberFeignClient.queryMemberListByMemberCode(Mockito.any())).thenReturn(listResult);
        PageInfo<MarketingGroupUserListResult> pageInfo1 = marketingGroupComponent.queryAllGroupUserList(groupMode);
        Assert.assertEquals(1,pageInfo1.getList().size());

    }

    @Test
    public void marketingGroupUserExpire1(){

        String itemValue = "MARKETING_GROUP_USER_CACHE:TENANTCODE=100016:ACTIVITYCODE=0623090600037630:MARKETINGGROUPCODE=PG20230906162132000001:USERCODE=111-2";


        MarketingGroupMode groupMode  = new MarketingGroupMode();

        groupMode.setAutoGroupFlag(1);

        Mockito.when(marketingGroupService.findMarketingGroupByActivityCode(Mockito.anyString(),Mockito.anyString())).thenReturn(groupMode);

        List<FlashSaleOrderModel> flashSaleOrderModels = new ArrayList<>();
        FlashSaleOrderModel flashSaleOrderModel = new FlashSaleOrderModel();

        flashSaleOrderModel.setOrderStatus(OrderStatusEnum.PAID.code());
        flashSaleOrderModels.add(flashSaleOrderModel);

        Mockito.when(flashSaleOrderService.queryOrderByMarketingGroupCode(Mockito.anyString(),Mockito.anyString(),Mockito.anyString())).thenReturn(flashSaleOrderModels);

        List<FlashSaleOrderDetailModel> detailsByOrderNo = new ArrayList<>();

        FlashSaleOrderDetailModel flashSaleOrderDetailModel = new FlashSaleOrderDetailModel();

        detailsByOrderNo.add(flashSaleOrderDetailModel);
        Mockito.when(flashSaleOrderDetailService.findByOrderNo(Mockito.any(),Mockito.any())).thenReturn(detailsByOrderNo);

        MarketingModel byActivityCode = new MarketingModel();
        byActivityCode.setSelectProductType(SelectorProductTypeEnum.SELECT_SKU.code());
        Mockito.when(marketingService.findByActivityCode(Mockito.any())).thenReturn(byActivityCode);

        MarketingGroupCodeMode marketingGroupCodeMode = new MarketingGroupCodeMode();
        marketingGroupCodeMode.setInventory(1);
        marketingGroupCodeMode.setGroupStatus(MarketingGroupStatusEnum.GROUP_PROCESSING.code());

        Mockito.when(marketingGroupcodeService.queryGroupByMarketingGroupCode(Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(marketingGroupCodeMode);


        marketingGroupComponent.marketingGroupUserExpire(itemValue);
    }


    @Test
    public void marketingGroupUserExpire2(){

        String itemValue = "MARKETING_GROUP_USER_CACHE:TENANTCODE=100016:ACTIVITYCODE=0623090600037630:MARKETINGGROUPCODE=PG20230906162132000001:USERCODE=111-2";


        MarketingGroupMode groupMode  = new MarketingGroupMode();

        groupMode.setAutoGroupFlag(1);

        Mockito.when(marketingGroupService.findMarketingGroupByActivityCode(Mockito.anyString(),Mockito.anyString())).thenReturn(groupMode);

        List<FlashSaleOrderModel> flashSaleOrderModels = new ArrayList<>();
        FlashSaleOrderModel flashSaleOrderModel = new FlashSaleOrderModel();

        flashSaleOrderModel.setOrderStatus(OrderStatusEnum.PAID.code());
        flashSaleOrderModels.add(flashSaleOrderModel);

        Mockito.when(flashSaleOrderService.queryOrderByMarketingGroupCode(Mockito.anyString(),Mockito.anyString(),Mockito.anyString())).thenReturn(flashSaleOrderModels);

        List<FlashSaleOrderDetailModel> detailsByOrderNo = new ArrayList<>();

        FlashSaleOrderDetailModel flashSaleOrderDetailModel = new FlashSaleOrderDetailModel();

        detailsByOrderNo.add(flashSaleOrderDetailModel);
        Mockito.when(flashSaleOrderDetailService.findByOrderNo(Mockito.any(),Mockito.any())).thenReturn(detailsByOrderNo);

        MarketingModel byActivityCode = new MarketingModel();
        byActivityCode.setSelectProductType(SelectorProductTypeEnum.SELECT_SKU.code());
        Mockito.when(marketingService.findByActivityCode(Mockito.any())).thenReturn(byActivityCode);

        MarketingGroupCodeMode marketingGroupCodeMode = new MarketingGroupCodeMode();
        marketingGroupCodeMode.setInventory(3);
        marketingGroupCodeMode.setGroupStatus(MarketingGroupStatusEnum.GROUP_NO_START.code());

        Mockito.when(marketingGroupcodeService.queryGroupByMarketingGroupCode(Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(marketingGroupCodeMode);


        marketingGroupComponent.marketingGroupUserExpire(itemValue);
    }

    @Test
    public void marketingGroupUserExpire_1(){

        String itemValue = "MARKETING_GROUP_USER_CACHE:TENANTCODE=100016:ACTIVITYCODE=0623090600037630:MARKETINGGROUPCODE=PG20230906162132000001:USERCODE=111-2";

        MarketingGroupMode groupMode  = new MarketingGroupMode();

        groupMode.setAutoGroupFlag(0);
        List<FlashSaleOrderModel> flashSaleOrderModels = new ArrayList<>();
        FlashSaleOrderModel flashSaleOrderModel = new FlashSaleOrderModel();

        flashSaleOrderModel.setOrderStatus(OrderStatusEnum.UNPAID.code());
        flashSaleOrderModels.add(flashSaleOrderModel);

        Mockito.when(flashSaleOrderService.queryOrderByMarketingGroupCode(Mockito.anyString(),Mockito.anyString(),Mockito.anyString())).thenReturn(flashSaleOrderModels);

        List<FlashSaleOrderDetailModel> detailsByOrderNo = new ArrayList<>();

        FlashSaleOrderDetailModel flashSaleOrderDetailModel = new FlashSaleOrderDetailModel();

        detailsByOrderNo.add(flashSaleOrderDetailModel);
//        Mockito.when(flashSaleOrderDetailService.findByOrderNo(Mockito.any(),Mockito.any())).thenReturn(detailsByOrderNo);

        MarketingModel byActivityCode = new MarketingModel();
        byActivityCode.setSelectProductType(SelectorProductTypeEnum.SELECT_SPU.code());
//        Mockito.when(marketingService.findByActivityCode(Mockito.any())).thenReturn(byActivityCode);
        Mockito.when(marketingGroupService.findMarketingGroupByActivityCode(Mockito.anyString(),Mockito.anyString())).thenReturn(groupMode);

        MarketingGroupCodeMode marketingGroupCodeMode = new MarketingGroupCodeMode();
        marketingGroupCodeMode.setInventory(3);
        marketingGroupCodeMode.setGroupStatus(MarketingGroupStatusEnum.GROUP_PROCESSING.code());

        Mockito.when(marketingGroupcodeService.queryGroupByMarketingGroupCode(Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(marketingGroupCodeMode);

        marketingGroupComponent.marketingGroupUserExpire(itemValue);
    }


    @Test
    public void marketingGroupUserExpire_2(){

        String itemValue = "MARKETING_GROUP_USER_CACHE:TENANTCODE=100016:ACTIVITYCODE=0623090600037630:MARKETINGGROUPCODE=PG20230906162132000001:USERCODE=111-2";
        MarketingGroupMode groupMode  = new MarketingGroupMode();
        groupMode.setAutoGroupFlag(0);
        Mockito.when(marketingGroupService.findMarketingGroupByActivityCode(Mockito.anyString(),Mockito.anyString())).thenReturn(null);
        marketingGroupComponent.marketingGroupUserExpire(itemValue);
    }

    @Test
    public void marketingGroupUserExpire_0(){

        String itemValue = "MARKETING_GROUP_USER_CACHE:TENANTCODE=100016:ACTIVITYCODE=0623090600037630:MARKETINGGROUPCODE=PG20230906162132000001:USERCODE=111-2";

        MarketingGroupMode groupMode  = new MarketingGroupMode();

        groupMode.setAutoGroupFlag(0);

        Mockito.when(marketingGroupService.findMarketingGroupByActivityCode(Mockito.anyString(),Mockito.anyString())).thenReturn(groupMode);

        MarketingGroupCodeMode marketingGroupCodeMode = new MarketingGroupCodeMode();
        marketingGroupCodeMode.setInventory(3);
        marketingGroupCodeMode.setGroupStatus(MarketingGroupStatusEnum.GROUP_NO_START.code());

        Mockito.when(marketingGroupcodeService.queryGroupByMarketingGroupCode(Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(marketingGroupCodeMode);

        marketingGroupComponent.marketingGroupUserExpire(itemValue);
    }


    @Test
    public void sendGroupMessage(){

        String itemValue = "MARKETING_GROUP_USER_CACHE:TENANTCODE=100016:ACTIVITYCODE=0623090600037630:MARKETINGGROUPCODE=PG20230906162132000001:USERCODE=111-2";
        MarketingGroupMode groupMode  = new MarketingGroupMode();
        groupMode.setAutoGroupFlag(0);
        GroupUserContent groupUserContent = new GroupUserContent();
        groupUserContent.setMarketingGroupCode("1");
        groupUserContent.setMarketingGroupResult("1");
        groupUserContent.setActivityCode("0623090600037630");
        groupUserContent.setTenantCode("100016");
        groupUserContent.setDomainCode("DC0005");
        marketingGroupComponent.sendGroupMessage(groupUserContent);
    }


    @Test(expected = PromotionException.class)
    public void findUserGroupByMarketingGroupAndUserCode_null(){

        FindGroupUserParam param = new FindGroupUserParam();
        param.setTenantCode("!");
        param.setMarketingGroupCode("!");
        param.setUserCode("1");

        FindGroupUserParam groupMode  = new FindGroupUserParam();
        groupMode.setTenantCode("11");
        groupMode.setMarketingGroupCode("!");
        groupMode.setUserCode("!1");
        groupMode.setDomainCode("!1");

        Mockito.when(marketingGroupUserService.findGroupUserCode(Mockito.any())).thenReturn(null);

        marketingGroupComponent.findUserGroupByMarketingGroupAndUserCode(groupMode);
    }

    @Test(expected = PromotionException.class)
    public void findUserGroupByMarketingGroupAndUserCode(){

        FindGroupUserParam groupMode  = new FindGroupUserParam();
        groupMode.setTenantCode("11");
        groupMode.setMarketingGroupCode("!");
        groupMode.setUserCode("!1");
        groupMode.setDomainCode("!1");
        MarketingGroupUserMode groupUserCode = new MarketingGroupUserMode();
        groupUserCode.setEffectiveHour(1);

        Mockito.when(marketingGroupUserService.findGroupUserCode(Mockito.any())).thenReturn(groupUserCode);
        MarketingGroupMode byActivityCode = new MarketingGroupMode();
        byActivityCode.setGroupSize(2);

        marketingGroupComponent.findUserGroupByMarketingGroupAndUserCode(groupMode);
    }

    @Test
    public void findUserGroupByMarketingGroupAndUserCode2(){

        FindGroupUserParam groupMode  = new FindGroupUserParam();
        groupMode.setTenantCode("11");
        groupMode.setMarketingGroupCode("!");
        groupMode.setUserCode("21");
        groupMode.setDomainCode("!1");
        MarketingGroupUserMode groupUserCode = new MarketingGroupUserMode();
        groupUserCode.setEffectiveHour(1);
        groupUserCode.setGroupStatus(UserGroupStatusEnum.FINISH.code());
        Mockito.when(marketingGroupUserService.findGroupUserCode(Mockito.any())).thenReturn(groupUserCode);
        MarketingGroupMode byActivityCode = new MarketingGroupMode();
        byActivityCode.setGroupSize(2);
        Mockito.when(marketingGroupService.findByActivityCode(Mockito.any())).thenReturn(byActivityCode);

        Result<List<QueryMemberListByConditionResult>> listResult = new Result<>();
        List<QueryMemberListByConditionResult> results = new ArrayList<>();
        QueryMemberListByConditionResult result1 = new QueryMemberListByConditionResult();
        result1.setMemberCode("212");
        results.add(result1);

        QueryMemberListByConditionResult result = new QueryMemberListByConditionResult();
        result.setMemberCode("21");
        results.add(result);
        listResult.setData(results);
        listResult.setCode("0");

        Mockito.when(memberFeignClient.queryMemberListByMemberCode(Mockito.any())).thenReturn(listResult);


        MarketingGroupUserListResult userGroupByMarketingGroupAndUserCode = marketingGroupComponent.findUserGroupByMarketingGroupAndUserCode(groupMode);
        Assert.assertNotNull(userGroupByMarketingGroupAndUserCode);
    }

    @Test
    public void findUserGroupByMarketingGroupAndUserCode3(){

        FindGroupUserParam groupMode  = new FindGroupUserParam();
        groupMode.setTenantCode("11");
        groupMode.setMarketingGroupCode("!");
        groupMode.setUserCode("!1");
        groupMode.setDomainCode("!1");
        MarketingGroupUserMode groupUserCode = new MarketingGroupUserMode();
        groupUserCode.setEffectiveHour(1);
        groupUserCode.setGroupStatus(UserGroupStatusEnum.PROCESSING.code());
        Mockito.when(marketingGroupUserService.findGroupUserCode(Mockito.any())).thenReturn(groupUserCode);
        MarketingGroupMode byActivityCode = new MarketingGroupMode();
        byActivityCode.setGroupSize(2);
        Mockito.when(marketingGroupService.findByActivityCode(Mockito.any())).thenReturn(byActivityCode);

        Mockito.when(marketingGroupUserService.queryGroupUserListByMarketingGroupCode(Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(1);


        Result<List<QueryMemberListByConditionResult>> listResult = new Result<>();
        List<QueryMemberListByConditionResult> results = new ArrayList<>();
        QueryMemberListByConditionResult result1 = new QueryMemberListByConditionResult();
        result1.setMemberCode("212");

        String json = "{\n" +
                "    \"score\": 0,\n" +
                "    \"nickName\": \"宁可\",\n" +
                "    \"wechatImg\": \"data:image/png;base64,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\",\n" +
                "    \"totalScore\": 0,\n" +
                "    \"isActivated\": true,\n" +
                "    \"blockedStatus\": 0,\n" +
                "    \"annualCostScore\": 0,\n" +
                "    \"isPushInformation\": 1,\n" +
                "    \"hasBoundSportWeapp\": false,\n" +
                "    \"annualAccumulatedScore\": 0\n" +
                "}";

        result1.setExtParams(json);

        results.add(result1);

        QueryMemberListByConditionResult result = new QueryMemberListByConditionResult();
        result.setMemberCode("21");

        result.setExtParams(json);
        results.add(result);
        listResult.setData(results);
        listResult.setCode("0");

        Mockito.when(memberFeignClient.queryMemberListByMemberCode(Mockito.any())).thenReturn(listResult);

        MarketingGroupUserListResult userGroupByMarketingGroupAndUserCode = marketingGroupComponent.findUserGroupByMarketingGroupAndUserCode(groupMode);
        Assert.assertNotNull(userGroupByMarketingGroupAndUserCode);
    }



    @Test(expected = PromotionException.class)
    public void queryGroupUserList(){

        MarketingGroupUserParam param = new MarketingGroupUserParam();
        param.setDomainCode("!");
        param.setTenantCode("!");
        param.setActivityCode("!");
        List<String> groupStatus = new ArrayList<>();
        groupStatus.add("01");
        groupStatus.add("02");
        List<MarketingGroupUserListResult> results = new ArrayList<>();
        MarketingGroupUserListResult result = new MarketingGroupUserListResult();
        results.add(result);
        when(marketingGroupService.findGroupByActivityCode(Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(null);
        List<MarketingGroupUserListResult> results1 = marketingGroupComponent.queryGroupUserList(param);
        Assert.assertEquals(0, results1.size());
    }

    @Test
    public void queryGroupUserList1(){

        MarketingGroupUserParam param = new MarketingGroupUserParam();
        param.setDomainCode("!");
        param.setTenantCode("!");
        param.setActivityCode("!");
        List<String> groupStatus = new ArrayList<>();
        groupStatus.add("01");
        groupStatus.add("02");
        List<MarketingGroupUserListResult> results = new ArrayList<>();
        MarketingGroupUserListResult result = new MarketingGroupUserListResult();
        results.add(result);

        MarketingGroupMode byActivityCode = new MarketingGroupMode();
        byActivityCode.setGroupSize(1);
        when(marketingGroupService.findGroupByActivityCode(Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(byActivityCode);

        List<MarketingGroupUserMode> userModes = new ArrayList<>();
        MarketingGroupUserMode mode = new MarketingGroupUserMode();
        mode.setMarketingGroupCode("666");
        mode.setGroupStatus(UserGroupStatusEnum.PAID.code());
        mode.setTeamLeader("1");
        userModes.add(mode);
        when(marketingGroupUserService.queryPayGroupUserListByActivityCode(Mockito.any())).thenReturn(userModes);
        when(marketingGroupUserService.queryGroupUserListByActivityCode(Mockito.any())).thenReturn(userModes);
        List<MarketingGroupUserListResult> results1 = marketingGroupComponent.queryGroupUserList(param);
        Assert.assertEquals(1, results1.size());
    }

}
