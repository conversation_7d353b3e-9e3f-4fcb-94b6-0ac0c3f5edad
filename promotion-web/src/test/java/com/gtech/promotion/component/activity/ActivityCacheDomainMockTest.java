package com.gtech.promotion.component.activity;

import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.data.redis.core.ListOperations;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.ValueOperations;

import com.gtech.basic.masterdata.response.JsonResult;
import com.gtech.promotion.dao.entity.activity.ActivityEntity;
import com.gtech.promotion.dao.model.activity.ActivityModel;
import com.gtech.promotion.dao.model.activity.TPromoActivityProductVO;
import com.gtech.promotion.dto.cache.ActivityCacheDTO;
import com.gtech.promotion.dto.cache.ProductSkuDetailDTO;
import com.gtech.promotion.feign.MasterDataFeignClient;
import com.gtech.promotion.service.activity.ActivityProductDetailService;
import com.gtech.promotion.service.activity.ActivityService;
import com.gtech.promotion.service.activity.TPromoIncentiveLimitedService;
import com.gtech.promotion.service.coupon.PromoCouponCodeUserService;
import com.gtech.promotion.vo.bean.ProductAttribute;
import com.gtech.promotion.vo.bean.ProductCodes;
import com.gtech.promotion.vo.bean.ProductDetail;
import com.gtech.promotion.vo.param.activity.MallProductConditionParam;

@RunWith(MockitoJUnitRunner.class)
public class ActivityCacheDomainMockTest {
    @InjectMocks
    private ActivityCacheDomain activityCacheDomain;

    @Mock
    private MasterDataFeignClient masterDataFeignClient;

    @Mock
    private ActivityProductDetailService productDetailService;
	@Mock
	private ActivityService activityService;

	@Mock
	private TPromoIncentiveLimitedService incentiveLimitedService;

	@Mock
	private StringRedisTemplate redisTemplate;

	@Mock
	private ListOperations<String, String> listOps;

	@Mock
	private ValueOperations<String, String> valueOperations;

	@Mock
	private PromoCouponCodeUserService couponCodeUserService;

	@Before
	public void before() {

		when(redisTemplate.opsForList()).thenReturn(listOps);
		when(redisTemplate.opsForValue()).thenReturn(valueOperations);

	}


    @Test
    public void filterBlackProduct(){
        Map<String, ActivityCacheDTO> activityMap = new HashMap<>();
        ActivityCacheDTO activityCacheDTO = new ActivityCacheDTO();
        ActivityModel activityModel = new ActivityModel();
        activityModel.setActivityCode("test");
        activityCacheDTO.setActivityModel(activityModel);
        activityMap.put("test",activityCacheDTO);
        ProductCodes product = new ProductCodes();
        Map<String, List<ProductSkuDetailDTO>> blackProductMap = new HashMap<>();
        activityCacheDomain.filterBlackProduct(activityMap,product,blackProductMap);

        List<ProductSkuDetailDTO> list = new ArrayList<>();
        ProductSkuDetailDTO productSkuDetailDTO = new ProductSkuDetailDTO();
        productSkuDetailDTO.setProductCode("ddd");
        list.add(productSkuDetailDTO);
        blackProductMap.put("test",list);
        activityCacheDomain.filterBlackProduct(activityMap,product,blackProductMap);
    }


    @Test
    public void  filterActivityByCustomCondition(){
        Map<String, ActivityCacheDTO> activityMap =new HashMap<>();
        Map<String,String> customMap =new HashMap<>();
        Map<String, ActivityCacheDTO> map = activityCacheDomain.filterActivityByCustomCondition("1", activityMap, customMap);
        Assert.assertEquals(0,map.size());
    }

    @Test
    public void  filterActivityByCustomCondition1(){
        Map<String, ActivityCacheDTO> activityMap =new HashMap<>();
        Map<String,String> customMap =new HashMap<>();

        JsonResult<String> jsonResult = new JsonResult<>();
        jsonResult.setData("0");
        activityMap.put("1",new ActivityCacheDTO());
        Mockito.when(masterDataFeignClient.getValueValue(Mockito.any(),Mockito.any())).thenReturn(jsonResult);
        Map<String, ActivityCacheDTO> map = activityCacheDomain.filterActivityByCustomCondition("1", activityMap, customMap);
        Assert.assertEquals(1,map.size());
    }

    @Test
    public void  filterActivityByCustomCondition2(){
        Map<String, ActivityCacheDTO> activityMap =new HashMap<>();
        Map<String,String> customMap =new HashMap<>();

        ActivityCacheDTO activityCacheDTO = new ActivityCacheDTO();
        ActivityModel activityModel = new ActivityModel();
        String custom = "[{\"customKey\": \"bank_transfer\", \"customValue\": \"gopay\"}," +
                " {\"customKey\": \"custom\", \"customValue\": \"value11\"}, " +
                "{\"customKey\": \"memberIsFlag\", \"customValue\": \"isNoMember\"}]";
        activityModel.setCustomCondition(custom);
        activityCacheDTO.setActivityModel(activityModel);
        activityMap.put("1",activityCacheDTO);

        JsonResult<String> jsonResult = new JsonResult<>();
        jsonResult.setData("1");

        Mockito.when(masterDataFeignClient.getValueValue(Mockito.any(),Mockito.any())).thenReturn(jsonResult);
        Map<String, ActivityCacheDTO> map = activityCacheDomain.filterActivityByCustomCondition("1", activityMap, customMap);
        Assert.assertEquals(0,map.size());
    }

    @Test
    public void  filterActivityByCustomCondition3(){
        Map<String, ActivityCacheDTO> activityMap =new HashMap<>();
        ActivityCacheDTO activityCacheDTO = new ActivityCacheDTO();

        ActivityModel activityModel =new ActivityModel();

        activityModel.setCustomCondition("");
        activityCacheDTO.setActivityModel(activityModel);

        activityMap.put("123456",activityCacheDTO);
        Map<String,String> customMap =new HashMap<>();
        customMap.put("1","1");

        JsonResult<String> jsonResult = new JsonResult<>();
        jsonResult.setData("1");

        Mockito.when(masterDataFeignClient.getValueValue(Mockito.any(),Mockito.any())).thenReturn(jsonResult);
        Map<String, ActivityCacheDTO> map = activityCacheDomain.filterActivityByCustomCondition("1", activityMap, customMap);
        Assert.assertEquals(1,map.size());
    }

    @Test
    public void  filterActivityByCustomCondition4(){
        Map<String, ActivityCacheDTO> activityMap =new HashMap<>();
        ActivityCacheDTO activityCacheDTO = new ActivityCacheDTO();

        ActivityModel activityModel =new ActivityModel();
        String custom = "[{\"customKey\": \"bank_transfer\", \"customValue\": \"gopay\"}," +
                " {\"customKey\": \"custom\", \"customValue\": \"value11\"}, " +
                "{\"customKey\": \"memberIsFlag\", \"customValue\": \"isNoMember\"}]";
        activityModel.setCustomCondition(custom);
        activityCacheDTO.setActivityModel(activityModel);

        activityMap.put("123456",activityCacheDTO);
        Map<String,String> customMap =new HashMap<>();

        customMap.put("bank_transfer","gopay");

        JsonResult<String> jsonResult = new JsonResult<>();
        jsonResult.setData("1");

        Mockito.when(masterDataFeignClient.getValueValue(Mockito.any(),Mockito.any())).thenReturn(jsonResult);
        Map<String, ActivityCacheDTO> map = activityCacheDomain.filterActivityByCustomCondition("1", activityMap, null);
        Assert.assertEquals(0,map.size());
    }

    @Test
    public void  filterActivityByCustomCondition5(){
        Map<String, ActivityCacheDTO> activityMap =new HashMap<>();
        ActivityCacheDTO activityCacheDTO = new ActivityCacheDTO();

        ActivityModel activityModel =new ActivityModel();
        String custom = "[{\"customKey\": \"bank_transfer\", \"customValue\": \"gopay\"}," +
                " {\"customKey\": \"custom\", \"customValue\": \"value11\"}, " +
                "{\"customKey\": \"memberIsFlag\", \"customValue\": \"isNoMember\"}]";
        activityModel.setCustomCondition(custom);
        activityCacheDTO.setActivityModel(activityModel);

        activityMap.put("123456",activityCacheDTO);
        Map<String,String> customMap =new HashMap<>();

        JsonResult<String> jsonResult = new JsonResult<>();
        jsonResult.setData("1");

        Mockito.when(masterDataFeignClient.getValueValue(Mockito.any(),Mockito.any())).thenReturn(jsonResult);
        Map<String, ActivityCacheDTO> map = activityCacheDomain.filterActivityByCustomCondition("1", activityMap, customMap);
        Assert.assertEquals(0,map.size());
    }


    @Test
    public void  filterActivityByCustomCondition6(){
        Map<String, ActivityCacheDTO> activityMap =new HashMap<>();
        ActivityCacheDTO activityCacheDTO = new ActivityCacheDTO();

        ActivityModel activityModel =new ActivityModel();
        String custom = "[{\"customKey\": \"bank_transfer\", \"customValue\": \"gopay\"}," +
                " {\"customKey\": \"custom\", \"customValue\": \"value11\"}, " +
                "{\"customKey\": \"memberIsFlag\", \"customValue\": \"isNoMember\"}]";
        activityModel.setCustomCondition(custom);
        activityCacheDTO.setActivityModel(activityModel);

        activityMap.put("123456",activityCacheDTO);
        Map<String,String> customMap =new HashMap<>();

        customMap.put("1","1");
        JsonResult<String> jsonResult = new JsonResult<>();
        jsonResult.setData("1");

        Mockito.when(masterDataFeignClient.getValueValue(Mockito.any(),Mockito.any())).thenReturn(jsonResult);
        Map<String, ActivityCacheDTO> map = activityCacheDomain.filterActivityByCustomCondition("1", activityMap, customMap);
        Assert.assertEquals(0,map.size());
    }

    @Test
    public void  filterActivityByCustomCondition8(){
        Map<String, ActivityCacheDTO> activityMap =new HashMap<>();
        ActivityCacheDTO activityCacheDTO = new ActivityCacheDTO();

        ActivityModel activityModel =new ActivityModel();
        String custom = "[{\"customKey\": \"bank_transfer\", \"customValue\": \"gopay\"}," +
                " {\"customKey\": \"custom\", \"customValue\": \"value11\"}, " +
                "{\"customKey\": \"memberIsFlag\", \"customValue\": \"isNoMember\"}]";
        activityModel.setCustomCondition(custom);
        activityCacheDTO.setActivityModel(activityModel);

        activityMap.put("123456",activityCacheDTO);
        Map<String,String> customMap =new HashMap<>();

        customMap.put("bank_transfer","gopay");
        customMap.put("custom","value11");

        JsonResult<String> jsonResult = new JsonResult<>();
        jsonResult.setData("1");

        Mockito.when(masterDataFeignClient.getValueValue(Mockito.any(),Mockito.any())).thenReturn(jsonResult);
        Map<String, ActivityCacheDTO> map = activityCacheDomain.filterActivityByCustomCondition("1", activityMap, customMap);
        Assert.assertEquals(0,map.size());
    }

    @Test
    public void  filterActivityByCustomCondition9(){
        Map<String, ActivityCacheDTO> activityMap =new HashMap<>();
        ActivityCacheDTO activityCacheDTO = new ActivityCacheDTO();

        ActivityModel activityModel =new ActivityModel();
        String custom = "[{\"customKey\": \"bank_transfer\", \"customValue\": \"gopay\"}," +
                " {\"customKey\": \"custom\", \"customValue\": \"value11\"}, " +
                "{\"customKey\": \"memberIsFlag\", \"customValue\": \"isNoMember\"}]";
        activityModel.setCustomCondition(custom);
        activityCacheDTO.setActivityModel(activityModel);

        activityMap.put("123456",activityCacheDTO);
        Map<String,String> customMap =new HashMap<>();

        customMap.put("bank_transfer","gopay");
        customMap.put("custom","value11");
        customMap.put("memberIsFlag","isNoMember");

        JsonResult<String> jsonResult = new JsonResult<>();
        jsonResult.setData("1");

        Mockito.when(masterDataFeignClient.getValueValue(Mockito.any(),Mockito.any())).thenReturn(jsonResult);
        Map<String, ActivityCacheDTO> map = activityCacheDomain.filterActivityByCustomCondition("1", activityMap, customMap);
        Assert.assertEquals(0,map.size());
    }

    @Test
    public void  filterActivityByCustomCondition10(){
        Map<String, ActivityCacheDTO> activityMap =new HashMap<>();
        ActivityCacheDTO activityCacheDTO = new ActivityCacheDTO();

        ActivityModel activityModel =new ActivityModel();
        String custom = "[ {\"customKey\": \"memberIsFlag\", \"customValue\": [\"isNoMember\"]}]";
        activityModel.setCustomCondition(custom);
        activityCacheDTO.setActivityModel(activityModel);

        activityMap.put("123456",activityCacheDTO);
        Map<String,String> customMap =new HashMap<>();

        customMap.put("bank_transfer","gopay");
        customMap.put("custom","value11");
        customMap.put("memberIsFlag","isNoMember");

        JsonResult<String> jsonResult = new JsonResult<>();
        jsonResult.setData("1");

        Mockito.when(masterDataFeignClient.getValueValue(Mockito.any(),Mockito.any())).thenReturn(jsonResult);
        Map<String, ActivityCacheDTO> map = activityCacheDomain.filterActivityByCustomCondition("1", activityMap, customMap);
        Assert.assertEquals(0,map.size());
    }

    @Test
    public void queryActivityByCustomCondition2(){
        Map<String, ActivityCacheDTO> activityMap =new HashMap<>();
        ActivityCacheDTO activityCacheDTO = new ActivityCacheDTO();

        ActivityModel activityModel =new ActivityModel();
        String custom = "[{\"customKey\": \"bank_transfer\", \"customValue\": \"gopay\"}," +
                " {\"customKey\": \"custom\", \"customValue\": \"value11\"}, " +
                "{\"customKey\": \"memberIsFlag\", \"customValue\": \"isNoMember\"}]";
        activityModel.setCustomCondition(custom);
        activityModel.setCustomCondition(null);
        activityCacheDTO.setActivityModel(activityModel);

        activityMap.put("123456",activityCacheDTO);
        Map<String,String> customMap =new HashMap<>();

        customMap.put("bank_transfer","gopay");
        customMap.put("custom","value11");
        customMap.put("memberIsFlag","isNoMember");

        JsonResult<String> jsonResult = new JsonResult<>();
        jsonResult.setData("1");

        Map<String, ActivityCacheDTO> map = activityCacheDomain.queryActivityByCustomCondition(activityMap, customMap);
        Assert.assertEquals(0,map.size());
    }

    @Test
    public void queryActivityByCustomCondition(){
        Map<String, ActivityCacheDTO> activityMap =new HashMap<>();
        ActivityCacheDTO activityCacheDTO = new ActivityCacheDTO();

        ActivityModel activityModel =new ActivityModel();
        String custom = "[{\"customKey\": \"bank_transfer\", \"customValue\": \"gopay\"}," +
                " {\"customKey\": \"custom\", \"customValue\": \"value11\"}, " +
                "{\"customKey\": \"memberIsFlag\", \"customValue\": \"isNoMember\"}]";
        activityModel.setCustomCondition(custom);
        activityCacheDTO.setActivityModel(activityModel);

        activityMap.put("123456",activityCacheDTO);
        Map<String,String> customMap =new HashMap<>();

        customMap.put("bank_transfer","gopay");
        customMap.put("custom","value11");
        customMap.put("memberIsFlag","isNoMember");

        JsonResult<String> jsonResult = new JsonResult<>();
        jsonResult.setData("1");

        Map<String, ActivityCacheDTO> map = activityCacheDomain.queryActivityByCustomCondition(activityMap, customMap);
        Assert.assertEquals(0,map.size());
    }

    @Test
    public void queryActivityByCustomCondition4(){
        Map<String, ActivityCacheDTO> activityMap =new HashMap<>();
        ActivityCacheDTO activityCacheDTO = new ActivityCacheDTO();

        ActivityModel activityModel =new ActivityModel();
        String custom = "[{\"customKey\": \"bank_transfer\", \"customValue\": \"gopay\"}," +
                " {\"customKey\": \"custom\", \"customValue\": \"value11\"}, " +
                "{\"customKey\": \"memberIsFlag\", \"customValue\": \"isNoMember\"}]";
        activityModel.setCustomCondition(custom);
        activityCacheDTO.setActivityModel(activityModel);

        activityMap.put("123456",activityCacheDTO);
        Map<String,String> customMap =new HashMap<>();

        customMap.put("bank_transfer","gopay");
        customMap.put("memberIsFlag","isNoMember");

        JsonResult<String> jsonResult = new JsonResult<>();
        jsonResult.setData("1");

        Map<String, ActivityCacheDTO> map = activityCacheDomain.queryActivityByCustomCondition(activityMap, customMap);
        Assert.assertEquals(0,map.size());
    }

    @Test
    public void queryActivityByCustomCondition1(){
        Map<String, ActivityCacheDTO> activityMap =new HashMap<>();
        ActivityCacheDTO activityCacheDTO = new ActivityCacheDTO();

        ActivityModel activityModel =new ActivityModel();
        String custom = "[{\"customKey\": \"bank_transfer\", \"customValue\": \"gopay\"}," +
                " {\"customKey\": \"custom\", \"customValue\": \"value11\"}, " +
                "{\"customKey\": \"memberIsFlag\", \"customValue\": \"isNoMember\"}]";
        activityModel.setCustomCondition(custom);
        activityCacheDTO.setActivityModel(activityModel);

        Map<String,String> customMap =new HashMap<>();

        customMap.put("bank_transfer","gopay");
        customMap.put("custom","value11");
        customMap.put("memberIsFlag","isNoMember");

        JsonResult<String> jsonResult = new JsonResult<>();
        jsonResult.setData("1");

        Map<String, ActivityCacheDTO> map = activityCacheDomain.queryActivityByCustomCondition(activityMap, customMap);
        Assert.assertEquals(0,map.size());
    }


    @Test
    public void filterMallActivityByProduct(){

        Map<String, ActivityCacheDTO> activityMap = new HashMap<>();

        ActivityCacheDTO activityCacheDTO = new ActivityCacheDTO();

        activityMap.put("123456",activityCacheDTO);

        MallProductConditionParam productCondition = new MallProductConditionParam();
        List<String> productBlackList =new ArrayList<>();
        productCondition.setProductBlackList(productBlackList);

        List<ProductDetail> productDetails = new ArrayList<>();
        ProductDetail productDetail = new ProductDetail();
        productDetail.setType(2);
        productDetail.setProductCode("sp123");
        productDetails.add(productDetail);

        Map<String, ActivityCacheDTO> map = activityCacheDomain.filterMallActivityByProduct(activityMap, productCondition, "01");
        Assert.assertEquals(0,map.size());
    }

    @Test
    public void filterMallActivityByProduct1(){

        Map<String, ActivityCacheDTO> activityMap = new HashMap<>();

        ActivityCacheDTO activityCacheDTO = new ActivityCacheDTO();

        activityMap.put("123456",activityCacheDTO);

        MallProductConditionParam productCondition = new MallProductConditionParam();
        List<String> productBlackList =new ArrayList<>();
        productBlackList.add("sp123");
        productCondition.setProductBlackList(productBlackList);

        List<ProductDetail> productDetails = new ArrayList<>();
        ProductDetail productDetail = new ProductDetail();
        productDetail.setType(2);
        productDetail.setProductCode("sp123");
        productDetails.add(productDetail);

        Mockito.when(productDetailService.getProductSpuSkus(Mockito.anyString())).thenReturn(productDetails);

        Map<String, ActivityCacheDTO> map = activityCacheDomain.filterMallActivityByProduct(activityMap, productCondition, "01");
        Assert.assertEquals(0,map.size());
    }

    @Test
    public void filterMallActivityByProduct2(){

        Map<String, ActivityCacheDTO> activityMap = new HashMap<>();

        ActivityCacheDTO activityCacheDTO = new ActivityCacheDTO();


        List<TPromoActivityProductVO> promoProducts = new ArrayList<>();


        activityCacheDTO.setPromoProducts(promoProducts);

        activityMap.put("123456",activityCacheDTO);

        MallProductConditionParam productCondition = new MallProductConditionParam();
        List<String> productBlackList =new ArrayList<>();
        productBlackList.add("sp12");
        productCondition.setProductBlackList(productBlackList);

        List<ProductDetail> productDetails = new ArrayList<>();
        ProductDetail productDetail = new ProductDetail();
        productDetail.setType(2);
        productDetail.setProductCode("sp123");
        productDetails.add(productDetail);

        Mockito.when(productDetailService.getProductSpuSkus(Mockito.anyString())).thenReturn(productDetails);

        Map<String, ActivityCacheDTO> map = activityCacheDomain.filterMallActivityByProduct(activityMap, productCondition, "01");
        Assert.assertEquals(0,map.size());
    }

    @Test
    public void filterMallActivityByProduct3(){

        Map<String, ActivityCacheDTO> activityMap = new HashMap<>();

        ActivityCacheDTO activityCacheDTO = new ActivityCacheDTO();


        List<TPromoActivityProductVO> promoProducts = new ArrayList<>();

        TPromoActivityProductVO productVO = new TPromoActivityProductVO();
        productVO.setAttrType("02");
        promoProducts.add(productVO);

        activityCacheDTO.setPromoProducts(promoProducts);

        activityMap.put("123456",activityCacheDTO);

        MallProductConditionParam productCondition = new MallProductConditionParam();
        List<String> productBlackList =new ArrayList<>();
        productBlackList.add("sp12");
        productCondition.setProductBlackList(productBlackList);
        productCondition.setAttrType("01");
        List<ProductDetail> productDetails = new ArrayList<>();
        ProductDetail productDetail = new ProductDetail();
        productDetail.setType(2);
        productDetail.setProductCode("sp123");
        productDetails.add(productDetail);

        Mockito.when(productDetailService.getProductSpuSkus(Mockito.anyString())).thenReturn(productDetails);

        Map<String, ActivityCacheDTO> map = activityCacheDomain.filterMallActivityByProduct(activityMap, productCondition, "01");
        Assert.assertEquals(0,map.size());
    }

    @Test
    public void filterMallActivityByProduct4(){

        Map<String, ActivityCacheDTO> activityMap = new HashMap<>();

        ActivityCacheDTO activityCacheDTO = new ActivityCacheDTO();


        List<TPromoActivityProductVO> promoProducts = new ArrayList<>();

        TPromoActivityProductVO productVO = new TPromoActivityProductVO();
        productVO.setAttrType("01");

        productVO.setCategoryCode("111");

        promoProducts.add(productVO);

        activityCacheDTO.setPromoProducts(promoProducts);

        activityMap.put("123456",activityCacheDTO);

        MallProductConditionParam productCondition = new MallProductConditionParam();
        List<String> productBlackList =new ArrayList<>();
        productBlackList.add("sp12");
        productCondition.setProductBlackList(productBlackList);
        productCondition.setAttrType("01");

        List<String> categoryCodes = new ArrayList<>();
        categoryCodes.add("1222");
        productCondition.setCategoryCodes(categoryCodes);

        List<ProductDetail> productDetails = new ArrayList<>();
        ProductDetail productDetail = new ProductDetail();
        productDetail.setType(2);
        productDetail.setProductCode("sp123");
        productDetails.add(productDetail);



        Mockito.when(productDetailService.getProductSpuSkus(Mockito.anyString())).thenReturn(productDetails);

        Map<String, ActivityCacheDTO> map = activityCacheDomain.filterMallActivityByProduct(activityMap, productCondition, "01");
        Assert.assertEquals(0,map.size());
    }

    @Test
    public void filterMallActivityByProduct5(){

        Map<String, ActivityCacheDTO> activityMap = new HashMap<>();

        ActivityCacheDTO activityCacheDTO = new ActivityCacheDTO();


        List<TPromoActivityProductVO> promoProducts = new ArrayList<>();

        TPromoActivityProductVO productVO = new TPromoActivityProductVO();
        productVO.setAttrType("01");

        productVO.setCategoryCode("0000");

        promoProducts.add(productVO);

        activityCacheDTO.setPromoProducts(promoProducts);

        activityMap.put("123456",activityCacheDTO);

        MallProductConditionParam productCondition = new MallProductConditionParam();
        List<String> productBlackList =new ArrayList<>();
        productBlackList.add("sp12");
        productCondition.setProductBlackList(productBlackList);
        productCondition.setAttrType("01");

        List<String> categoryCodes = new ArrayList<>();
        categoryCodes.add("1222");
        productCondition.setCategoryCodes(categoryCodes);

        List<ProductDetail> productDetails = new ArrayList<>();
        ProductDetail productDetail = new ProductDetail();
        productDetail.setType(2);
        productDetail.setProductCode("sp123");
        productDetails.add(productDetail);



        Mockito.when(productDetailService.getProductSpuSkus(Mockito.anyString())).thenReturn(productDetails);

        Map<String, ActivityCacheDTO> map = activityCacheDomain.filterMallActivityByProduct(activityMap, productCondition, "01");
        Assert.assertEquals(1,map.size());
    }

    @Test
    public void filterMallActivityByProduct6(){

        Map<String, ActivityCacheDTO> activityMap = new HashMap<>();

        ActivityCacheDTO activityCacheDTO = new ActivityCacheDTO();


        List<TPromoActivityProductVO> promoProducts = new ArrayList<>();

        TPromoActivityProductVO productVO = new TPromoActivityProductVO();
        productVO.setAttrType("01");

        productVO.setCategoryCode("1222");
        productVO.setBrandCode("0000");
        promoProducts.add(productVO);
        activityCacheDTO.setPromoProducts(promoProducts);

        activityMap.put("123456",activityCacheDTO);

        MallProductConditionParam productCondition = new MallProductConditionParam();
        List<String> productBlackList =new ArrayList<>();
        productBlackList.add("sp12");
        productCondition.setProductBlackList(productBlackList);
        productCondition.setAttrType("01");

        List<String> categoryCodes = new ArrayList<>();
        categoryCodes.add("1222");
        productCondition.setCategoryCodes(categoryCodes);

        List<String> brandCodes = new ArrayList<>();
        brandCodes.add("1");
        productCondition.setBrandCodes(brandCodes);

        List<ProductDetail> productDetails = new ArrayList<>();
        ProductDetail productDetail = new ProductDetail();
        productDetail.setType(2);
        productDetail.setProductCode("sp123");
        productDetails.add(productDetail);


        Mockito.when(productDetailService.getProductSpuSkus(Mockito.anyString())).thenReturn(productDetails);

        Map<String, ActivityCacheDTO> map = activityCacheDomain.filterMallActivityByProduct(activityMap, productCondition, "01");
        Assert.assertEquals(1,map.size());
    }

    @Test
    public void filterMallActivityByProduct7(){

        Map<String, ActivityCacheDTO> activityMap = new HashMap<>();

        ActivityCacheDTO activityCacheDTO = new ActivityCacheDTO();


        List<TPromoActivityProductVO> promoProducts = new ArrayList<>();

        TPromoActivityProductVO productVO = new TPromoActivityProductVO();
        productVO.setAttrType("01");

        productVO.setCategoryCode("1222");
        productVO.setBrandCode("11");
        promoProducts.add(productVO);
        activityCacheDTO.setPromoProducts(promoProducts);

        activityMap.put("123456",activityCacheDTO);

        MallProductConditionParam productCondition = new MallProductConditionParam();
        List<String> productBlackList =new ArrayList<>();
        productBlackList.add("sp12");
        productCondition.setProductBlackList(productBlackList);
        productCondition.setAttrType("01");

        List<String> categoryCodes = new ArrayList<>();
        categoryCodes.add("1222");
        productCondition.setCategoryCodes(categoryCodes);

        List<String> brandCodes = new ArrayList<>();
        brandCodes.add("1");
        productCondition.setBrandCodes(brandCodes);

        List<ProductDetail> productDetails = new ArrayList<>();
        ProductDetail productDetail = new ProductDetail();
        productDetail.setType(2);
        productDetail.setProductCode("sp123");
        productDetails.add(productDetail);


        Mockito.when(productDetailService.getProductSpuSkus(Mockito.anyString())).thenReturn(productDetails);

        Map<String, ActivityCacheDTO> map = activityCacheDomain.filterMallActivityByProduct(activityMap, productCondition, "01");
        Assert.assertEquals(1,map.size());
    }

    @Test
    public void filterMallActivityByProduct8(){

        Map<String, ActivityCacheDTO> activityMap = new HashMap<>();

        ActivityCacheDTO activityCacheDTO = new ActivityCacheDTO();


        List<TPromoActivityProductVO> promoProducts = new ArrayList<>();

        TPromoActivityProductVO productVO = new TPromoActivityProductVO();
        productVO.setAttrType("01");

        productVO.setCategoryCode("1222");
        productVO.setBrandCode("1");
        promoProducts.add(productVO);
        activityCacheDTO.setPromoProducts(promoProducts);

        activityMap.put("123456",activityCacheDTO);

        MallProductConditionParam productCondition = new MallProductConditionParam();
        List<String> productBlackList =new ArrayList<>();
        productBlackList.add("sp12");
        productCondition.setProductBlackList(productBlackList);
        productCondition.setAttrType("01");

        List<String> categoryCodes = new ArrayList<>();
        categoryCodes.add("1222");
        productCondition.setCategoryCodes(categoryCodes);

        List<String> brandCodes = new ArrayList<>();
        brandCodes.add("1");
        productCondition.setBrandCodes(brandCodes);

        List<ProductAttribute> productAttributes = new ArrayList<>();

        productCondition.setAttributes(productAttributes);

        List<ProductDetail> productDetails = new ArrayList<>();
        ProductDetail productDetail = new ProductDetail();
        productDetail.setType(2);
        productDetail.setProductCode("sp123");
        productDetails.add(productDetail);


        Mockito.when(productDetailService.getProductSpuSkus(Mockito.anyString())).thenReturn(productDetails);

        Map<String, ActivityCacheDTO> map = activityCacheDomain.filterMallActivityByProduct(activityMap, productCondition, "01");
        Assert.assertEquals(1,map.size());
    }

    @Test
    public void filterMallActivityByProduct9(){

        Map<String, ActivityCacheDTO> activityMap = new HashMap<>();

        ActivityCacheDTO activityCacheDTO = new ActivityCacheDTO();


        List<TPromoActivityProductVO> promoProducts = new ArrayList<>();

        TPromoActivityProductVO productVO = new TPromoActivityProductVO();
        productVO.setAttrType("01");

        productVO.setCategoryCode("1222");
        productVO.setBrandCode("1");


        List<ProductAttribute> productAttributes = new ArrayList<>();

        ProductAttribute attribute = new ProductAttribute();

        attribute.setAttributeCode("11");
        attribute.setAttributeValues("1");
        productAttributes.add(attribute);

        productVO.setAttributes(productAttributes);

        promoProducts.add(productVO);
        activityCacheDTO.setPromoProducts(promoProducts);

        activityMap.put("123456",activityCacheDTO);

        MallProductConditionParam productCondition = new MallProductConditionParam();
        List<String> productBlackList =new ArrayList<>();
        productBlackList.add("sp12");
        productCondition.setProductBlackList(productBlackList);
        productCondition.setAttrType("01");

        List<String> categoryCodes = new ArrayList<>();
        categoryCodes.add("1222");
        productCondition.setCategoryCodes(categoryCodes);

        List<String> brandCodes = new ArrayList<>();
        brandCodes.add("1");
        productCondition.setBrandCodes(brandCodes);

        List<ProductAttribute> productAttributeList = new ArrayList<>();
        ProductAttribute attribute1 = new ProductAttribute();

        attribute1.setAttributeCode("1");
        attribute1.setAttributeValues("1");
        productAttributeList.add(attribute1);
        productCondition.setAttributes(productAttributeList);

        List<ProductDetail> productDetails = new ArrayList<>();
        ProductDetail productDetail = new ProductDetail();
        productDetail.setType(2);
        productDetail.setProductCode("sp123");
        productDetails.add(productDetail);


        Mockito.when(productDetailService.getProductSpuSkus(Mockito.anyString())).thenReturn(productDetails);

        Map<String, ActivityCacheDTO> map = activityCacheDomain.filterMallActivityByProduct(activityMap, productCondition, "01");
        Assert.assertEquals(1,map.size());
    }
    @Test
    public void filterMallActivityByProduct10(){

        Map<String, ActivityCacheDTO> activityMap = new HashMap<>();

        ActivityCacheDTO activityCacheDTO = new ActivityCacheDTO();


        List<TPromoActivityProductVO> promoProducts = new ArrayList<>();

        TPromoActivityProductVO productVO = new TPromoActivityProductVO();
        productVO.setAttrType("01");

        productVO.setCategoryCode("1222");
        productVO.setBrandCode("1");


        List<ProductAttribute> productAttributes = new ArrayList<>();

        ProductAttribute attribute = new ProductAttribute();

        attribute.setAttributeCode("1");
        attribute.setAttributeValues("1");
        productAttributes.add(attribute);

        productVO.setAttributes(productAttributes);

        promoProducts.add(productVO);
        activityCacheDTO.setPromoProducts(promoProducts);

        activityMap.put("123456",activityCacheDTO);

        MallProductConditionParam productCondition = new MallProductConditionParam();
        List<String> productBlackList =new ArrayList<>();
        productBlackList.add("sp12");
        productCondition.setProductBlackList(productBlackList);
        productCondition.setAttrType("01");

        List<String> categoryCodes = new ArrayList<>();
        categoryCodes.add("1222");
        productCondition.setCategoryCodes(categoryCodes);

        List<String> brandCodes = new ArrayList<>();
        brandCodes.add("1");
        productCondition.setBrandCodes(brandCodes);

        List<ProductAttribute> productAttributeList = new ArrayList<>();
        ProductAttribute attribute1 = new ProductAttribute();

        attribute1.setAttributeCode("1");
        attribute1.setAttributeValues("2,3");
        productAttributeList.add(attribute1);
        productCondition.setAttributes(productAttributeList);

        List<ProductDetail> productDetails = new ArrayList<>();
        ProductDetail productDetail = new ProductDetail();
        productDetail.setType(2);
        productDetail.setProductCode("sp123");
        productDetails.add(productDetail);


        Mockito.when(productDetailService.getProductSpuSkus(Mockito.anyString())).thenReturn(productDetails);

        Map<String, ActivityCacheDTO> map = activityCacheDomain.filterMallActivityByProduct(activityMap, productCondition, "01");
        Assert.assertEquals(1,map.size());
    }


    @Test
    public void filterMallActivityByProduct11(){

        Map<String, ActivityCacheDTO> activityMap = new HashMap<>();

        ActivityCacheDTO activityCacheDTO = new ActivityCacheDTO();


        List<TPromoActivityProductVO> promoProducts = new ArrayList<>();

        TPromoActivityProductVO productVO = new TPromoActivityProductVO();
        productVO.setAttrType("01");

        productVO.setCategoryCode("1222");
        productVO.setBrandCode("1");


        List<ProductAttribute> productAttributes = new ArrayList<>();

        ProductAttribute attribute = new ProductAttribute();

        attribute.setAttributeCode("1");
        attribute.setAttributeValues("1");
        productAttributes.add(attribute);

        productVO.setAttributes(productAttributes);

        promoProducts.add(productVO);
        activityCacheDTO.setPromoProducts(promoProducts);

        activityMap.put("123456",activityCacheDTO);

        MallProductConditionParam productCondition = new MallProductConditionParam();
        List<String> productBlackList =new ArrayList<>();
        productBlackList.add("sp12");
        productCondition.setProductBlackList(productBlackList);
        productCondition.setAttrType("01");

        List<String> categoryCodes = new ArrayList<>();
        categoryCodes.add("1222");
        productCondition.setCategoryCodes(categoryCodes);

        List<String> brandCodes = new ArrayList<>();
        brandCodes.add("1");
        productCondition.setBrandCodes(brandCodes);

        List<ProductAttribute> productAttributeList = new ArrayList<>();
        ProductAttribute attribute1 = new ProductAttribute();

        attribute1.setAttributeCode("1");
        attribute1.setAttributeValues("1,2");
        productAttributeList.add(attribute1);
        productCondition.setAttributes(productAttributeList);

        List<ProductDetail> productDetails = new ArrayList<>();
        ProductDetail productDetail = new ProductDetail();
        productDetail.setType(2);
        productDetail.setProductCode("sp123");
        productDetails.add(productDetail);

        Mockito.when(productDetailService.getProductSpuSkus(Mockito.anyString())).thenReturn(productDetails);

        Map<String, ActivityCacheDTO> map = activityCacheDomain.filterMallActivityByProduct(activityMap, productCondition, "01");
        Assert.assertEquals(1,map.size());
    }

    @Test
    public void filterMallActivityByProduct12(){

        Map<String, ActivityCacheDTO> activityMap = new HashMap<>();

        ActivityCacheDTO activityCacheDTO = new ActivityCacheDTO();


        List<TPromoActivityProductVO> promoProducts = new ArrayList<>();

        TPromoActivityProductVO productVO = new TPromoActivityProductVO();
        productVO.setAttrType("01");

        productVO.setCategoryCode("1222");
        productVO.setBrandCode("1");


        List<ProductAttribute> productAttributes = new ArrayList<>();

        ProductAttribute attribute = new ProductAttribute();

        attribute.setAttributeCode("1");
        attribute.setAttributeValues("1");
        productAttributes.add(attribute);

        productVO.setAttributes(productAttributes);

        promoProducts.add(productVO);
        activityCacheDTO.setPromoProducts(promoProducts);

        activityMap.put("123456",activityCacheDTO);

        MallProductConditionParam productCondition = new MallProductConditionParam();
        List<String> productBlackList =new ArrayList<>();
        productCondition.setProductBlackList(productBlackList);
        productCondition.setAttrType("");

        List<String> categoryCodes = new ArrayList<>();
        productCondition.setCategoryCodes(categoryCodes);

        List<String> brandCodes = new ArrayList<>();
        productCondition.setBrandCodes(brandCodes);

        List<ProductAttribute> productAttributeList = new ArrayList<>();
        ProductAttribute attribute1 = new ProductAttribute();

        attribute1.setAttributeCode("1");
        attribute1.setAttributeValues("1,2");
        productAttributeList.add(attribute1);
        productCondition.setAttributes(productAttributeList);

        List<ProductDetail> productDetails = new ArrayList<>();
        ProductDetail productDetail = new ProductDetail();
        productDetail.setType(2);
        productDetail.setProductCode("sp123");
        productDetails.add(productDetail);

        Map<String, ActivityCacheDTO> map = activityCacheDomain.filterMallActivityByProduct(activityMap, productCondition, "01");
        Assert.assertEquals(1,map.size());
    }


    @Test
    public void filterMallActivityByProduct13(){

        Map<String, ActivityCacheDTO> activityMap = new HashMap<>();

        ActivityCacheDTO activityCacheDTO = new ActivityCacheDTO();


        List<TPromoActivityProductVO> promoProducts = new ArrayList<>();

        TPromoActivityProductVO productVO = new TPromoActivityProductVO();
        productVO.setAttrType("01");

        productVO.setCategoryCode("1222");
        productVO.setBrandCode("1");


        List<ProductAttribute> productAttributes = new ArrayList<>();

        ProductAttribute attribute = new ProductAttribute();

        attribute.setAttributeCode("1");
        attribute.setAttributeValues("1");
        productAttributes.add(attribute);

        productVO.setAttributes(productAttributes);

        promoProducts.add(productVO);
        activityCacheDTO.setPromoProducts(promoProducts);

        activityMap.put("123456",activityCacheDTO);

        MallProductConditionParam productCondition = new MallProductConditionParam();
        List<String> productBlackList =new ArrayList<>();
        productCondition.setProductBlackList(productBlackList);
        productCondition.setAttrType("");

        List<String> categoryCodes = new ArrayList<>();
        productCondition.setCategoryCodes(categoryCodes);

        List<String> brandCodes = new ArrayList<>();
        productCondition.setBrandCodes(brandCodes);

        List<ProductAttribute> productAttributeList = new ArrayList<>();
        ProductAttribute attribute1 = new ProductAttribute();

        attribute1.setAttributeCode("1");
        attribute1.setAttributeValues("1,2");
        productAttributeList.add(attribute1);
        productCondition.setAttributes(productAttributeList);

        List<ProductDetail> productDetails = new ArrayList<>();
        ProductDetail productDetail = new ProductDetail();
        productDetail.setType(1);
        productDetail.setProductCode("sp123");
        productDetails.add(productDetail);

        Mockito.when(productDetailService.getProductSpuSkus(Mockito.anyString())).thenReturn(productDetails);

        Map<String, ActivityCacheDTO> map = activityCacheDomain.filterMallActivityByProduct(activityMap, productCondition, "02");
        Assert.assertEquals(1,map.size());
    }


    @Test
    public void filterMallActivityByProduct14(){

        Map<String, ActivityCacheDTO> activityMap = new HashMap<>();

        ActivityCacheDTO activityCacheDTO = new ActivityCacheDTO();


        List<TPromoActivityProductVO> promoProducts = new ArrayList<>();

        TPromoActivityProductVO productVO = new TPromoActivityProductVO();
        productVO.setAttrType("01");

        productVO.setCategoryCode("1222");
        productVO.setBrandCode("1");


        List<ProductAttribute> productAttributes = new ArrayList<>();

        ProductAttribute attribute = new ProductAttribute();

        attribute.setAttributeCode("1");
        attribute.setAttributeValues("1");
        productAttributes.add(attribute);

        productVO.setAttributes(productAttributes);

        promoProducts.add(productVO);
        activityCacheDTO.setPromoProducts(promoProducts);

        activityMap.put("123456",activityCacheDTO);

        MallProductConditionParam productCondition = new MallProductConditionParam();
        List<String> productBlackList =new ArrayList<>();
        productCondition.setProductBlackList(productBlackList);
        productCondition.setAttrType("");

        List<String> categoryCodes = new ArrayList<>();
        productCondition.setCategoryCodes(categoryCodes);

        List<String> brandCodes = new ArrayList<>();
        productCondition.setBrandCodes(brandCodes);

        List<ProductAttribute> productAttributeList = new ArrayList<>();
        ProductAttribute attribute1 = new ProductAttribute();

        attribute1.setAttributeCode("1");
        attribute1.setAttributeValues("1,2");
        productAttributeList.add(attribute1);
        productCondition.setAttributes(productAttributeList);

        List<ProductDetail> productDetails = new ArrayList<>();
        ProductDetail productDetail = new ProductDetail();
        productDetail.setType(1);
        productDetail.setProductCode("sp123");
        productDetails.add(productDetail);
        List<String> productCodeList = new ArrayList<>();
        productCodeList.add("sp1234");
        productCondition.setProductCode(productCodeList);
        Mockito.when(productDetailService.getProductSpuSkus(Mockito.anyString())).thenReturn(productDetails);

        Map<String, ActivityCacheDTO> map = activityCacheDomain.filterMallActivityByProduct(activityMap, productCondition, "02");
        Assert.assertEquals(0,map.size());
    }

    @Test
    public void filterMallActivityByProduct15(){

        Map<String, ActivityCacheDTO> activityMap = new HashMap<>();

        ActivityCacheDTO activityCacheDTO = new ActivityCacheDTO();


        List<TPromoActivityProductVO> promoProducts = new ArrayList<>();

        TPromoActivityProductVO productVO = new TPromoActivityProductVO();
        productVO.setAttrType("01");

        productVO.setCategoryCode("1222");
        productVO.setBrandCode("1");


        List<ProductAttribute> productAttributes = new ArrayList<>();

        ProductAttribute attribute = new ProductAttribute();

        attribute.setAttributeCode("1");
        attribute.setAttributeValues("1");
        productAttributes.add(attribute);

        productVO.setAttributes(productAttributes);

        promoProducts.add(productVO);
        activityCacheDTO.setPromoProducts(promoProducts);

        activityMap.put("123456",activityCacheDTO);

        MallProductConditionParam productCondition = new MallProductConditionParam();
        List<String> productBlackList =new ArrayList<>();
        productCondition.setProductBlackList(productBlackList);
        productCondition.setAttrType("");

        List<String> categoryCodes = new ArrayList<>();
        productCondition.setCategoryCodes(categoryCodes);

        List<String> brandCodes = new ArrayList<>();
        productCondition.setBrandCodes(brandCodes);

        List<ProductAttribute> productAttributeList = new ArrayList<>();
        ProductAttribute attribute1 = new ProductAttribute();

        attribute1.setAttributeCode("1");
        attribute1.setAttributeValues("1,2");
        productAttributeList.add(attribute1);
        productCondition.setAttributes(productAttributeList);

        List<ProductDetail> productDetails = new ArrayList<>();
        ProductDetail productDetail = new ProductDetail();
        productDetail.setType(1);
        productDetail.setProductCode("sp123");
        productDetail.setSkuCode("1234");
        productDetails.add(productDetail);
        List<String> productCodeList = new ArrayList<>();
        productCodeList.add("sp123");
        productCondition.setProductCode(productCodeList);

        List<String> skuCodes = new ArrayList<>();
        skuCodes.add("12345");
        productCondition.setSkuCodes(skuCodes);
        Mockito.when(productDetailService.getProductSpuSkus(Mockito.anyString())).thenReturn(productDetails);

        Map<String, ActivityCacheDTO> map = activityCacheDomain.filterMallActivityByProduct(activityMap, productCondition, "02");
        Assert.assertEquals(1,map.size());
    }

    @Test
    public void filterMallActivityByProduct16(){

        Map<String, ActivityCacheDTO> activityMap = new HashMap<>();

        ActivityCacheDTO activityCacheDTO = new ActivityCacheDTO();


        List<TPromoActivityProductVO> promoProducts = new ArrayList<>();

        TPromoActivityProductVO productVO = new TPromoActivityProductVO();
        productVO.setAttrType("01");

        productVO.setCategoryCode("1222");
        productVO.setBrandCode("1");


        List<ProductAttribute> productAttributes = new ArrayList<>();

        ProductAttribute attribute = new ProductAttribute();

        attribute.setAttributeCode("1");
        attribute.setAttributeValues("1");
        productAttributes.add(attribute);

        productVO.setAttributes(productAttributes);

        promoProducts.add(productVO);
        activityCacheDTO.setPromoProducts(promoProducts);

        activityMap.put("123456",activityCacheDTO);

        MallProductConditionParam productCondition = new MallProductConditionParam();
        List<String> productBlackList =new ArrayList<>();
        productCondition.setProductBlackList(productBlackList);
        productCondition.setAttrType("");

        List<String> categoryCodes = new ArrayList<>();
        productCondition.setCategoryCodes(categoryCodes);

        List<String> brandCodes = new ArrayList<>();
        productCondition.setBrandCodes(brandCodes);

        List<ProductAttribute> productAttributeList = new ArrayList<>();
        ProductAttribute attribute1 = new ProductAttribute();

        attribute1.setAttributeCode("1");
        attribute1.setAttributeValues("1,2");
        productAttributeList.add(attribute1);
        productCondition.setAttributes(productAttributeList);

        List<ProductDetail> productDetails = new ArrayList<>();
        ProductDetail productDetail = new ProductDetail();
        productDetail.setType(1);
        productDetail.setProductCode("sp123");
        productDetail.setSkuCode("1234");
        productDetails.add(productDetail);
        List<String> productCodeList = new ArrayList<>();
        productCodeList.add("sp123");
        productCondition.setProductCode(productCodeList);

        List<String> skuCodes = new ArrayList<>();
        skuCodes.add("12345");
        skuCodes.add("1234");

        productCondition.setSkuCodes(skuCodes);
        Mockito.when(productDetailService.getProductSpuSkus(Mockito.anyString())).thenReturn(productDetails);

        Map<String, ActivityCacheDTO> map = activityCacheDomain.filterMallActivityByProduct(activityMap, productCondition, "02");
        Assert.assertEquals(1,map.size());
    }


    @Test
    public void filterNoProductListPrice_NO_PRICE(){
        Map<String, ActivityCacheDTO> activityMap = new HashMap<>();
        ActivityCacheDTO activityCacheDTO = new ActivityCacheDTO();
        ActivityModel activityModel = new ActivityModel();
        activityModel.setActivityCode("test");
        activityCacheDTO.setActivityModel(activityModel);
        activityMap.put("test",activityCacheDTO);
        BigDecimal productListPrice = new BigDecimal("100");
        Map<String, ActivityCacheDTO> stringActivityCacheDTOMap = activityCacheDomain.filterNoProductListPrice(activityMap, productListPrice);
        Assert.assertEquals(1,stringActivityCacheDTOMap.size());
    }
    @Test
    public void filterNoProductListPrice_PRICE(){
        Map<String, ActivityCacheDTO> activityMap = new HashMap<>();
        ActivityCacheDTO activityCacheDTO = new ActivityCacheDTO();
        ActivityModel activityModel = new ActivityModel();
        activityModel.setActivityCode("test");
        activityModel.setPriceCondition("1");
        activityCacheDTO.setActivityModel(activityModel);
        activityMap.put("test",activityCacheDTO);
        BigDecimal productListPrice = null;
        Map<String, ActivityCacheDTO> stringActivityCacheDTOMap = activityCacheDomain.filterNoProductListPrice(activityMap, productListPrice);
        Assert.assertEquals(0,stringActivityCacheDTOMap.size());

    }

	@Test
	public void compensationCouponUseLimit() {
		activityCacheDomain.compensationCouponUseLimit();
		ActivityEntity activityEntity = new ActivityEntity();
		activityEntity.setTenantCode("test");
		activityEntity.setActivityCode("code");
		Mockito.when(activityService.queryActivityLimitTenant()).thenReturn(Arrays.asList(activityEntity));
		Map<String, BigDecimal> limitMap = new HashMap<>();
		limitMap.put("code", BigDecimal.TEN);
		Mockito.when(incentiveLimitedService.getLimitedByActivityCodes(Mockito.anyString(), Mockito.anyList(), Mockito.anyString())).thenReturn(limitMap);
		when(valueOperations.get(anyString())).thenReturn("10");
		Mockito.when(couponCodeUserService.queryCouponCountByCodeAndStataus(Mockito.anyString(), Mockito.anyString(), Mockito.anyList())).thenReturn(1);
		activityCacheDomain.compensationCouponUseLimit();
	}
}
