package com.gtech.promotion.component.coupon;

import com.github.pagehelper.PageInfo;
import com.gtech.basic.idm.web.vo.result.QueryOpUserAccountListResult;
import com.gtech.basic.idm.web.vo.result.QueryUserResult;
import com.gtech.commons.exception.GTechBaseException;
import com.gtech.commons.redis.GTechCodeGenerator;
import com.gtech.promotion.code.activity.FuncTypeEnum;
import com.gtech.promotion.code.coupon.CouponStatusEnum;
import com.gtech.promotion.code.coupon.CouponTypeEnum;
import com.gtech.promotion.component.activity.ActivityCacheDomain;
import com.gtech.promotion.component.activity.ActivityComponentDomain;
import com.gtech.promotion.component.activity.ActivityExpireComponentDomain;
import com.gtech.promotion.component.feign.IdmFeignClientComponent;
import com.gtech.promotion.dao.model.activity.*;
import com.gtech.promotion.dao.model.coupon.CountCouponCodeModel;
import com.gtech.promotion.dao.model.coupon.CouponReleaseModel;
import com.gtech.promotion.dao.model.coupon.TPromoCouponInnerCodeVO;
import com.gtech.promotion.domain.coupon.CouponDomain;
import com.gtech.promotion.domain.coupon.CouponReleaseDomain;
import com.gtech.promotion.dto.cache.ActivityCacheDTO;
import com.gtech.promotion.dto.in.coupon.*;
import com.gtech.promotion.dto.out.activity.TPromoActivityOutDTO;
import com.gtech.promotion.dto.out.coupon.*;
import com.gtech.promotion.exception.PromotionException;
import com.gtech.promotion.helper.ActivityRedisHelpler;
import com.gtech.promotion.service.activity.*;
import com.gtech.promotion.service.coupon.PromoCouponActivityService;
import com.gtech.promotion.service.coupon.PromoCouponCodeUserService;
import com.gtech.promotion.service.coupon.PromoCouponInnerCodeService;
import com.gtech.promotion.service.coupon.PromoCouponReleaseService;
import com.gtech.promotion.service.mongo.activity.TPromoProductService;
import com.gtech.promotion.utils.DateUtil;
import com.gtech.promotion.vo.bean.FunctionParam;
import com.gtech.promotion.vo.bean.ProductScope;
import org.apache.tools.ant.util.DateUtils;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.dao.DuplicateKeyException;

import java.util.*;

import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.Silent.class)
public class CouponActivityComponentTest {

    @InjectMocks
    private CouponActivityComponent couponActivityComponent;

    @Mock
    private TPromoProductService tPromoProductService;
    @Mock
    private ActivityComponentDomain activityComponentDomain;
    @Mock
    private OperationLogService operationLogService;
    @Mock
    private PromoCouponActivityService couponActivityService;
    @Mock
    private ActivityCacheDomain activityCacheDomain;
    @Mock
    private PromoCouponReleaseService couponReleaseService;
    @Mock
    private PromoCouponInnerCodeService couponInnerCodeService;
    @Mock
    private ActivityStoreService tPromoActivityStoreService;
    @Mock
    private PromoCouponCodeUserService couponCodeUserService;
    @Mock
    private ActivityService activityService;
    @Mock
    private ActivityExpireComponentDomain activityExpireComponentDomain;
    @Mock
    private ActivityPeriodService activityPeriodService;
    @Mock
    private TemplateService templateService;
    @Mock
    private ActivityFuncRankService promoActivityFuncRankService;
    @Mock
    private QualificationService qualificationService;
    @Mock
    private GiveawayService giveawayService;
    @Mock
    private ActivityFuncParamService promoActivityFuncParamService;
    @Mock
    private ActivityLanguageService languageService;
    @Mock
    private IdmFeignClientComponent idmFeignClientComponent;

    @Mock
    private ActivityRedisHelpler redisService;

    @Mock
    private PromotionGroupService promotionGroupService;
    @Mock
    private GTechCodeGenerator gTechCodeGenerator;


    @Test
    public void queryCouponActivityList3_group() {
        CouponActivityListInDTO inDTO = new CouponActivityListInDTO();
        inDTO.setTagCode("1");
        Map<String, String> tagMap = new HashMap<>();
        tagMap.put("1", "************");
        PageInfo<CouponActivityListOutDTO> promoCouponsDTOs = new PageInfo<>();
        List<CouponActivityListOutDTO> list = new ArrayList<>();
        CouponActivityListOutDTO dto = new CouponActivityListOutDTO();
        dto.setCreateUser("1");
        dto.setCouponType("01");
        dto.setTotalQuantity("1");
        dto.setTemplateCode("************");
        dto.setGroupCode("1");
        list.add(dto);
        promoCouponsDTOs.setList(list);
        List<QueryOpUserAccountListResult> idmOpUserAccount = new ArrayList<>();
        QueryOpUserAccountListResult result = new QueryOpUserAccountListResult();
        result.setUserCode("1");
        result.setFirstName("1");
        result.setLastName("1");
        idmOpUserAccount.add(result);
        List<TPromoActivityStoreVO> storeVOs = new ArrayList<>();
        TPromoActivityStoreVO tPromoActivityStoreVO = new TPromoActivityStoreVO();
        tPromoActivityStoreVO.setOrgCode("01");
        tPromoActivityStoreVO.setChannelCode("1");
        tPromoActivityStoreVO.setChannelName("1");
        tPromoActivityStoreVO.setStoreName("1");
        tPromoActivityStoreVO.setUrl("1");
        storeVOs.add(tPromoActivityStoreVO);

        when(templateService.findTemplateTagAll()).thenReturn(tagMap);
        when(couponActivityService.queryCouponActivityList(Mockito.any())).thenReturn(promoCouponsDTOs);
        PromoGroupVO promoGroupVO = new PromoGroupVO();
        promoGroupVO.setGroupName("!");

        when(promotionGroupService.getGroupByGroupCode(Mockito.any(),Mockito.any())).thenReturn(promoGroupVO);

        when(idmFeignClientComponent.getIdmOpUserAccount(Mockito.any())).thenReturn(idmOpUserAccount);
        when(languageService.findActivityLanguage(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(new ActivityLanguageModel());
        when(tPromoActivityStoreService.getStoresByActivityCode(Mockito.any())).thenReturn(storeVOs);
        when(couponReleaseService.queryReleaseCount111(Mockito.any())).thenReturn(1L);

        PageInfo<CouponActivityListOutDTO> pageInfo = couponActivityComponent.queryCouponActivityList(inDTO);
        Assert.assertEquals(0, pageInfo.getTotal());
    }

    @Test(expected = PromotionException.class)
    public void updateCouponActivity_错误的券类型() {
        UpdateCouponActivityInDTO updateCouponActivityDTO = new UpdateCouponActivityInDTO();
        updateCouponActivityDTO.setCouponType("couponType");
        when(tPromoProductService.getProducts(Mockito.any())).thenReturn(null);
        String s = couponActivityComponent.updateCouponActivity(updateCouponActivityDTO);
    }

    @Test(expected = GTechBaseException.class)
    public void updateCouponActivity_没有couponActivity() {
        UpdateCouponActivityInDTO updateCouponActivityDTO = new UpdateCouponActivityInDTO();
        updateCouponActivityDTO.setCouponType(CouponTypeEnum.PROMOTION_COUPON.code());
        List<ProductScope> preProducts = new ArrayList<>();
        preProducts.add(new ProductScope());
        when(tPromoProductService.getProducts(Mockito.any())).thenReturn(preProducts);
        when(activityComponentDomain.updatePromoActivity(Mockito.any())).thenReturn(1);
        when(operationLogService.insertLog(Mockito.any(), Mockito.any())).thenReturn(1);
        when(couponActivityService.findCouponActivity(Mockito.any(), Mockito.any())).thenReturn(null);
        when(tPromoProductService.insertProducts(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(1);
        String s = couponActivityComponent.updateCouponActivity(updateCouponActivityDTO);
    }

    @Test(expected = PromotionException.class)
    public void updateCouponActivity_投放数量小于0() {
        UpdateCouponActivityInDTO updateCouponActivityDTO = new UpdateCouponActivityInDTO();
        ActivityModel couponActivityModel = new ActivityModel();
        updateCouponActivityDTO.setCouponType(CouponTypeEnum.PROMOTION_COUPON.code());
        updateCouponActivityDTO.setTotalQuantity(-1);
        when(tPromoProductService.getProducts(Mockito.any())).thenReturn(null);
        when(activityComponentDomain.updatePromoActivity(Mockito.any())).thenReturn(1);
        when(operationLogService.insertLog(Mockito.any(), Mockito.any())).thenReturn(1);
        when(couponActivityService.findCouponActivity(Mockito.any(), Mockito.any())).thenReturn(couponActivityModel);
        String s = couponActivityComponent.updateCouponActivity(updateCouponActivityDTO);
    }

    @Test
    public void findEffectiveActivity_null() {
        when(activityService.findEffectiveActivity(Mockito.any(), Mockito.any())).thenReturn(null);
        ActivityModel activityModel = couponActivityComponent.findEffectiveActivity("1", "1", "1");
        Assert.assertEquals(null, activityModel);
    }

    @Test
    public void findEffectiveActivity_not_null() {
        when(activityService.findEffectiveActivity(Mockito.any(), Mockito.any())).thenReturn(new ActivityModel());
        ActivityModel activityModel = couponActivityComponent.findEffectiveActivity("1", "1", "1");
        Assert.assertEquals(null, activityModel);
    }

    @Test
    public void findValidActivity_null() {
        when(activityService.findEffectiveActivity(Mockito.any(), Mockito.any())).thenReturn(null);
        ActivityModel activityModel = couponActivityComponent.findValidActivity("1", "1", "1", null);
        Assert.assertEquals(null, activityModel);
    }

    @Test(expected = PromotionException.class)
    public void findCouponActivityOutDetail() {
        when(activityComponentDomain.findActivityDetail(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(null);
        couponActivityComponent.findCouponActivityOutDetail("1", "1", "1");
    }

    @Test
    public void expireCoupon_false() {
        CouponDomain domain = new CouponDomain();
        domain.setStatus("07");
        when(activityComponentDomain.findActivityDetail(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(null);
        couponActivityComponent.expireCoupon(domain);
    }


    @Test
    public void updateCouponActivity() {
        UpdateCouponActivityInDTO updateCouponActivityDTO = new UpdateCouponActivityInDTO();
        ActivityModel couponActivityModel = new ActivityModel();
        updateCouponActivityDTO.setCouponType(CouponTypeEnum.PROMOTION_COUPON.code());
        updateCouponActivityDTO.setTotalQuantity(1);
        updateCouponActivityDTO.setActivityCode("1");
        when(tPromoProductService.getProducts(Mockito.any())).thenReturn(null);
        when(activityComponentDomain.updatePromoActivity(Mockito.any())).thenReturn(1);
        when(operationLogService.insertLog(Mockito.any(), Mockito.any())).thenReturn(1);
        when(couponActivityService.findCouponActivity(Mockito.any(), Mockito.any())).thenReturn(couponActivityModel);
        when(couponActivityService.updateCouponActivityByActivityCode(Mockito.any())).thenReturn(1);
        String s = couponActivityComponent.updateCouponActivity(updateCouponActivityDTO);
        Assert.assertEquals(updateCouponActivityDTO.getActivityCode(), s);
    }

    @Test
    public void updateCouponActivity_优惠码_空投放() {
        UpdateCouponActivityInDTO updateCouponActivityDTO = new UpdateCouponActivityInDTO();
        ActivityModel couponActivityModel = new ActivityModel();
        updateCouponActivityDTO.setCouponType(CouponTypeEnum.PROMOTION_CODE.code());
        updateCouponActivityDTO.setTotalQuantity(1);
        updateCouponActivityDTO.setUserLimitMax(1);
        updateCouponActivityDTO.setActivityCode("1");
        PageInfo<CouponReleaseDomain> queryReleases = new PageInfo<>();
        when(tPromoProductService.getProducts(Mockito.any())).thenReturn(null);
        when(activityComponentDomain.updatePromoActivity(Mockito.any())).thenReturn(1);
        when(operationLogService.insertLog(Mockito.any(), Mockito.any())).thenReturn(1);
        when(couponActivityService.findCouponActivity(Mockito.any(), Mockito.any())).thenReturn(couponActivityModel);
        when(couponActivityService.updateCouponActivityByActivityCode(Mockito.any())).thenReturn(1);
        when(couponReleaseService.queryReleases(Mockito.any(), Mockito.any())).thenReturn(queryReleases);
        String s = couponActivityComponent.updateCouponActivity(updateCouponActivityDTO);
        Assert.assertEquals(updateCouponActivityDTO.getActivityCode(), s);
    }

    @Test
    public void updateCouponActivity_优惠码_没有券码() {
        UpdateCouponActivityInDTO updateCouponActivityDTO = new UpdateCouponActivityInDTO();
        ActivityModel couponActivityModel = new ActivityModel();
        updateCouponActivityDTO.setCouponType(CouponTypeEnum.PROMOTION_CODE.code());
        updateCouponActivityDTO.setTotalQuantity(1);
        updateCouponActivityDTO.setUserLimitMax(1);
        updateCouponActivityDTO.setActivityCode("1");
        List<CouponReleaseDomain> list = new ArrayList<>();
        list.add(new CouponReleaseDomain());
        PageInfo<CouponReleaseDomain> queryReleases = new PageInfo<>(list);
        when(tPromoProductService.getProducts(Mockito.any())).thenReturn(null);
        when(activityComponentDomain.updatePromoActivity(Mockito.any())).thenReturn(1);
        when(operationLogService.insertLog(Mockito.any(), Mockito.any())).thenReturn(1);
        when(couponActivityService.findCouponActivity(Mockito.any(), Mockito.any())).thenReturn(couponActivityModel);
        when(couponActivityService.updateCouponActivityByActivityCode(Mockito.any())).thenReturn(1);
        when(couponReleaseService.queryReleases(Mockito.any(), Mockito.any())).thenReturn(queryReleases);
        when(couponReleaseService.updateCouponReleaseByReleaseCode(Mockito.any())).thenReturn(1);
        when(couponInnerCodeService.queryInnerCouponByReleaseCode(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(null);
        String s = couponActivityComponent.updateCouponActivity(updateCouponActivityDTO);
        Assert.assertEquals(updateCouponActivityDTO.getActivityCode(), s);
    }

    @Test
    public void updateCouponActivity_优惠码() {
        UpdateCouponActivityInDTO updateCouponActivityDTO = new UpdateCouponActivityInDTO();
        ActivityModel couponActivityModel = new ActivityModel();
        updateCouponActivityDTO.setCouponType(CouponTypeEnum.PROMOTION_CODE.code());
        updateCouponActivityDTO.setTotalQuantity(1);
        updateCouponActivityDTO.setUserLimitMax(null);
        updateCouponActivityDTO.setActivityCode("1");
        List<FunctionParam> functionParams = new ArrayList<>();
        functionParams.add(FunctionParam.builder().functionCode(FuncTypeEnum.IncentiveEnum.REDUCE_AMOUNT.code()).paramValue("50").build());
        functionParams.add(FunctionParam.builder().functionCode(FuncTypeEnum.IncentiveEnum.DISCOUNT.code()).paramValue("50").build());
        updateCouponActivityDTO.setFuncParams(functionParams);
        List<CouponReleaseDomain> list = new ArrayList<>();
        list.add(new CouponReleaseDomain());
        PageInfo<CouponReleaseDomain> queryReleases = new PageInfo<>(list);
        List<TPromoCouponInnerCodeVO> innerCodeVOs = new ArrayList<>();
        innerCodeVOs.add(new TPromoCouponInnerCodeVO());
        when(tPromoProductService.getProducts(Mockito.any())).thenReturn(null);
        when(activityComponentDomain.updatePromoActivity(Mockito.any())).thenReturn(1);
        when(operationLogService.insertLog(Mockito.any(), Mockito.any())).thenReturn(1);
        when(couponActivityService.findCouponActivity(Mockito.any(), Mockito.any())).thenReturn(couponActivityModel);
        when(couponActivityService.updateCouponActivityByActivityCode(Mockito.any())).thenReturn(1);
        when(couponReleaseService.queryReleases(Mockito.any(), Mockito.any())).thenReturn(queryReleases);
        when(couponReleaseService.updateCouponReleaseByReleaseCode(Mockito.any())).thenReturn(1);
        when(couponInnerCodeService.queryInnerCouponByReleaseCode(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(innerCodeVOs);
        when(couponInnerCodeService.updateInnerCoudeById(Mockito.any())).thenReturn(1);
        String s = couponActivityComponent.updateCouponActivity(updateCouponActivityDTO);
        Assert.assertEquals(updateCouponActivityDTO.getActivityCode(), s);
    }

    @Test(expected = PromotionException.class)
    public void updateCouponActivity_优惠码_重复() {
        UpdateCouponActivityInDTO updateCouponActivityDTO = new UpdateCouponActivityInDTO();
        ActivityModel couponActivityModel = new ActivityModel();
        updateCouponActivityDTO.setCouponType(CouponTypeEnum.PROMOTION_CODE.code());
        updateCouponActivityDTO.setTotalQuantity(1);
        updateCouponActivityDTO.setUserLimitMax(1);
        updateCouponActivityDTO.setActivityCode("1");
        List<FunctionParam> functionParams = new ArrayList<>();
        functionParams.add(FunctionParam.builder().functionCode(FuncTypeEnum.IncentiveEnum.REDUCE_AMOUNT.code()).paramValue("50").build());
        functionParams.add(FunctionParam.builder().functionCode(FuncTypeEnum.IncentiveEnum.DISCOUNT.code()).paramValue("50").build());
        updateCouponActivityDTO.setFuncParams(functionParams);
        List<CouponReleaseDomain> list = new ArrayList<>();
        list.add(new CouponReleaseDomain());
        PageInfo<CouponReleaseDomain> queryReleases = new PageInfo<>(list);
        List<TPromoCouponInnerCodeVO> innerCodeVOs = new ArrayList<>();
        innerCodeVOs.add(new TPromoCouponInnerCodeVO());
        when(tPromoProductService.getProducts(Mockito.any())).thenReturn(null);
        when(activityComponentDomain.updatePromoActivity(Mockito.any())).thenReturn(1);
        when(operationLogService.insertLog(Mockito.any(), Mockito.any())).thenReturn(1);
        when(couponActivityService.findCouponActivity(Mockito.any(), Mockito.any())).thenReturn(couponActivityModel);
        when(couponActivityService.updateCouponActivityByActivityCode(Mockito.any())).thenReturn(1);
        when(couponReleaseService.queryReleases(Mockito.any(), Mockito.any())).thenReturn(queryReleases);
        when(couponReleaseService.updateCouponReleaseByReleaseCode(Mockito.any())).thenReturn(1);
        when(couponInnerCodeService.queryInnerCouponByReleaseCode(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(innerCodeVOs);
        when(couponInnerCodeService.updateInnerCoudeById(Mockito.any())).thenThrow(new DuplicateKeyException("test"));
        String s = couponActivityComponent.updateCouponActivity(updateCouponActivityDTO);
    }

    @Test(expected = PromotionException.class)
    public void findCouponActivityOutDetail_null_activity() {
        String tenantCode = "1";
        String activityCode = "1";
        String orgCode = "1";
        List<TPromoActivityStoreVO> storeVOs = new ArrayList<>();
        storeVOs.add(new TPromoActivityStoreVO());
        when(activityComponentDomain.findActivityDetail(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(null);
        when(tPromoActivityStoreService.getStoresByActivityCode(Mockito.any())).thenReturn(storeVOs);

        couponActivityComponent.findCouponActivityOutDetail(tenantCode, activityCode, orgCode);
    }

    @Test(expected = GTechBaseException.class)
    public void findCouponActivityOutDetail_null_coupon_activity() {
        String tenantCode = "1";
        String activityCode = "1";
        String orgCode = "1";
        TPromoActivityOutDTO activityOutDTO = new TPromoActivityOutDTO();
        List<TPromoActivityStoreVO> storeVOs = new ArrayList<>();
        TPromoActivityStoreVO e = new TPromoActivityStoreVO();
        e.setOrgCode(orgCode);
        storeVOs.add(e);

        when(activityComponentDomain.findActivityDetail(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(activityOutDTO);
        when(tPromoActivityStoreService.getStoresByActivityCode(Mockito.any())).thenReturn(storeVOs);
        when(couponActivityService.findCouponActivity(Mockito.any(), Mockito.any())).thenReturn(null);

        couponActivityComponent.findCouponActivityOutDetail(tenantCode, activityCode, orgCode);
    }

    @Test
    public void findCouponActivityOutDetail_优惠券() {
        String tenantCode = "1";
        String activityCode = "1";
        TPromoActivityOutDTO activityOutDTO = new TPromoActivityOutDTO();
        ActivityModel couponActivityModel = new ActivityModel();
        couponActivityModel.setCouponType(CouponTypeEnum.PROMOTION_COUPON.code());

        when(activityComponentDomain.findActivityDetail(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(activityOutDTO);
        when(couponActivityService.findCouponActivity(Mockito.any(), Mockito.any())).thenReturn(couponActivityModel);
        when(couponReleaseService.queryCouponRelease(Mockito.any())).thenReturn(null);
        when(couponCodeUserService.countCouponCode(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(null);

        CouponActivityOutDTO couponActivityOutDetail = couponActivityComponent.findCouponActivityOutDetail(tenantCode, activityCode, null);
        Assert.assertEquals(0, couponActivityOutDetail.getUsedTotal().intValue());
    }

    @Test
    public void findCouponActivityOutDetail_匿名券() {
        String tenantCode = "1";
        String activityCode = "1";
        TPromoActivityOutDTO activityOutDTO = new TPromoActivityOutDTO();
        ActivityModel couponActivityModel = new ActivityModel();
        couponActivityModel.setCouponType(CouponTypeEnum.ANONYMITY_COUPON.code());
        List<CouponReleaseModel> couponReleaseModelList = new ArrayList<>();
        CouponReleaseModel couponReleaseModel = new CouponReleaseModel();
        couponReleaseModel.setReleaseQuantity(1);
        couponReleaseModelList.add(couponReleaseModel);
        List<CountCouponCodeModel> cccModels = new ArrayList<>();
        cccModels.add(new CountCouponCodeModel());

        when(activityComponentDomain.findActivityDetail(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(activityOutDTO);
        when(couponActivityService.findCouponActivity(Mockito.any(), Mockito.any())).thenReturn(couponActivityModel);
        when(couponReleaseService.queryCouponRelease(Mockito.any())).thenReturn(couponReleaseModelList);
        when(couponInnerCodeService.countCouponCode(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(cccModels);

        CouponActivityOutDTO couponActivityOutDetail = couponActivityComponent.findCouponActivityOutDetail(tenantCode, activityCode, null);
        Assert.assertEquals(0, couponActivityOutDetail.getUsedTotal().intValue());
        Assert.assertEquals(1, couponActivityOutDetail.getReleasedQuantity().intValue());
    }

    @Test
    public void findCouponActivityOutDetail_匿名券_投放为空() {
        String tenantCode = "1";
        String activityCode = "1";
        TPromoActivityOutDTO activityOutDTO = new TPromoActivityOutDTO();
        ActivityModel couponActivityModel = new ActivityModel();
        couponActivityModel.setCouponType(CouponTypeEnum.ANONYMITY_COUPON.code());
        List<CouponReleaseModel> couponReleaseModelList = new ArrayList<>();
        CouponReleaseModel couponReleaseModel = new CouponReleaseModel();
        couponReleaseModel.setReleaseQuantity(1);
        couponReleaseModelList.add(couponReleaseModel);
        List<CountCouponCodeModel> cccModels = new ArrayList<>();
        when(activityComponentDomain.findActivityDetail(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(activityOutDTO);
        when(couponActivityService.findCouponActivity(Mockito.any(), Mockito.any())).thenReturn(couponActivityModel);
        when(couponReleaseService.queryCouponRelease(Mockito.any())).thenReturn(couponReleaseModelList);
        when(couponInnerCodeService.countCouponCode(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(cccModels);

        CouponActivityOutDTO couponActivityOutDetail = couponActivityComponent.findCouponActivityOutDetail(tenantCode, activityCode, null);
        Assert.assertEquals(0, couponActivityOutDetail.getUsedTotal().intValue());
        Assert.assertEquals(1, couponActivityOutDetail.getReleasedQuantity().intValue());
    }

    @Test
    public void findCouponActivityOutDetail_优惠码() {
        String tenantCode = "1";
        String activityCode = "1";
        TPromoActivityOutDTO activityOutDTO = new TPromoActivityOutDTO();
        ActivityModel couponActivityModel = new ActivityModel();
        couponActivityModel.setCouponType(CouponTypeEnum.PROMOTION_CODE.code());
        List<CouponReleaseModel> couponReleaseModelList = new ArrayList<>();
        CouponReleaseModel couponReleaseModel = new CouponReleaseModel();
        couponReleaseModel.setReleaseQuantity(1);
        couponReleaseModelList.add(couponReleaseModel);
        List<CountCouponCodeModel> cccModels = new ArrayList<>();
        CountCouponCodeModel countCouponCodeModel = new CountCouponCodeModel();
        countCouponCodeModel.setCouponStatus(CouponStatusEnum.USED.code());
        countCouponCodeModel.setCouponCount(1);
        cccModels.add(countCouponCodeModel);

        when(activityComponentDomain.findActivityDetail(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(activityOutDTO);
        when(couponActivityService.findCouponActivity(Mockito.any(), Mockito.any())).thenReturn(couponActivityModel);
        when(couponReleaseService.queryCouponRelease(Mockito.any())).thenReturn(couponReleaseModelList);
        when(couponInnerCodeService.queryInnerCouponByReleaseCode(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(null);
        when(couponCodeUserService.countCouponCode(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(cccModels);

        CouponActivityOutDTO couponActivityOutDetail = couponActivityComponent.findCouponActivityOutDetail(tenantCode, activityCode, null);
        Assert.assertEquals(1, couponActivityOutDetail.getUsedTotal().intValue());
        Assert.assertEquals(1, couponActivityOutDetail.getReleasedQuantity().intValue());
    }

    @Test
    public void findCouponActivityOutDetail_优惠码_1() {
        String tenantCode = "1";
        String activityCode = "1";
        TPromoActivityOutDTO activityOutDTO = new TPromoActivityOutDTO();
        ActivityModel couponActivityModel = new ActivityModel();
        couponActivityModel.setCouponType(CouponTypeEnum.PROMOTION_CODE.code());
        List<CouponReleaseModel> couponReleaseModelList = new ArrayList<>();
        CouponReleaseModel couponReleaseModel = new CouponReleaseModel();
        couponReleaseModel.setReleaseQuantity(1);
        couponReleaseModelList.add(couponReleaseModel);
        List<CountCouponCodeModel> cccModels = new ArrayList<>();
        CountCouponCodeModel countCouponCodeModel = new CountCouponCodeModel();
        countCouponCodeModel.setCouponStatus(CouponStatusEnum.USED.code());
        countCouponCodeModel.setCouponCount(1);
        cccModels.add(countCouponCodeModel);
        List<TPromoCouponInnerCodeVO> couponInnerCodeVOs = new ArrayList<>();
        TPromoCouponInnerCodeVO tPromoCouponInnerCodeVO = new TPromoCouponInnerCodeVO();
        tPromoCouponInnerCodeVO.setCouponCode("1");
        couponInnerCodeVOs.add(tPromoCouponInnerCodeVO);

        when(activityComponentDomain.findActivityDetail(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(activityOutDTO);
        when(couponActivityService.findCouponActivity(Mockito.any(), Mockito.any())).thenReturn(couponActivityModel);
        when(couponReleaseService.queryCouponRelease(Mockito.any())).thenReturn(couponReleaseModelList);
        when(couponInnerCodeService.queryInnerCouponByReleaseCode(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(couponInnerCodeVOs);
        when(couponCodeUserService.countCouponCode(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(cccModels);

        CouponActivityOutDTO couponActivityOutDetail = couponActivityComponent.findCouponActivityOutDetail(tenantCode, activityCode, null);
        Assert.assertEquals(tPromoCouponInnerCodeVO.getCouponCode(), couponActivityOutDetail.getPromotionCode());
        Assert.assertEquals(1, couponActivityOutDetail.getUsedTotal().intValue());
        Assert.assertEquals(1, couponActivityOutDetail.getReleasedQuantity().intValue());
    }

    @Test
    public void createCouponActivity() {
        CreateCouponActivityDTO dto = new CreateCouponActivityDTO();
        dto.setCouponType("03");
        dto.setPromotionCode("1");
        dto.setTenantCode("1");
        dto.setTotalQuantity(10);
        dto.setTemplateCode("02");
        List<FunctionParam> funcParams = new ArrayList<>();
        FunctionParam paramVO = new FunctionParam();
        paramVO.setParamValue("1");
        paramVO.setFunctionCode("0401");
        funcParams.add(paramVO);
        dto.setFuncParams(funcParams);
        when(activityComponentDomain.createPromoActivity(Mockito.any())).thenReturn("1");
        when(operationLogService.insertLog(Mockito.any(), Mockito.any())).thenReturn(1);
        when(couponActivityService.createCouponActivity(Mockito.any())).thenReturn("1");
        when(couponReleaseService.createCouponRelease(Mockito.any())).thenReturn("1");
        when(couponInnerCodeService.insertPromoCouponInnerCode(Mockito.any())).thenReturn(1);
        Mockito.doNothing().when(activityCacheDomain).putCache(Mockito.any(), Mockito.any());
        String couponActivity = couponActivityComponent.createCouponActivity(dto);
        Assert.assertEquals("1", couponActivity);
    }

    @Test(expected = PromotionException.class)
    public void createCouponActivity_exception() {
        CreateCouponActivityDTO dto = new CreateCouponActivityDTO();
        dto.setCouponType("03");
        dto.setPromotionCode("1");
        dto.setTenantCode("1");
        dto.setTotalQuantity(10);
        dto.setTemplateCode("02");
        List<FunctionParam> funcParams = new ArrayList<>();
        FunctionParam paramVO = new FunctionParam();
        paramVO.setParamValue("1");
        paramVO.setFunctionCode("0401");
        funcParams.add(paramVO);
        dto.setFuncParams(funcParams);
        when(activityComponentDomain.createPromoActivity(Mockito.any())).thenReturn("1");
        when(operationLogService.insertLog(Mockito.any(), Mockito.any())).thenReturn(1);
        when(couponActivityService.createCouponActivity(Mockito.any())).thenReturn("1");
        when(couponReleaseService.createCouponRelease(Mockito.any())).thenReturn("1");
        when(couponInnerCodeService.insertPromoCouponInnerCode(Mockito.any())).thenThrow(new DuplicateKeyException("1"));
        Mockito.doNothing().when(activityCacheDomain).putCache(Mockito.any(), Mockito.any());
        String couponActivity = couponActivityComponent.createCouponActivity(dto);
        Assert.assertEquals("1", couponActivity);
    }

    @Test(expected = PromotionException.class)
    public void createCouponActivity_exception_04() {
        CreateCouponActivityDTO dto = new CreateCouponActivityDTO();
        dto.setCouponType("04");
        dto.setPromotionCode("1");
        dto.setTenantCode("1");
        dto.setTotalQuantity(10);
        dto.setTemplateCode("02");
        List<FunctionParam> funcParams = new ArrayList<>();
        FunctionParam paramVO = new FunctionParam();
        paramVO.setParamValue("1");
        paramVO.setFunctionCode("0401");
        funcParams.add(paramVO);
        dto.setFuncParams(funcParams);
        when(activityComponentDomain.createPromoActivity(Mockito.any())).thenReturn("1");
        when(operationLogService.insertLog(Mockito.any(), Mockito.any())).thenReturn(1);
        when(couponActivityService.createCouponActivity(Mockito.any())).thenReturn("1");
        when(couponReleaseService.createCouponRelease(Mockito.any())).thenReturn("1");
        when(couponInnerCodeService.insertPromoCouponInnerCode(Mockito.any())).thenThrow(new DuplicateKeyException("1"));
        Mockito.doNothing().when(activityCacheDomain).putCache(Mockito.any(), Mockito.any());
        String couponActivity = couponActivityComponent.createCouponActivity(dto);
        Assert.assertEquals("1", couponActivity);
    }


    @Test
    public void createCouponActivity1() {
        CreateCouponActivityDTO dto = new CreateCouponActivityDTO();
        dto.setCouponType("03");
        dto.setPromotionCode("1");
        dto.setTenantCode("1");
        dto.setTotalQuantity(10);
        dto.setTemplateCode("01");
        List<FunctionParam> funcParams = new ArrayList<>();
        FunctionParam paramVO = new FunctionParam();
        paramVO.setParamValue("1");
        paramVO.setFunctionCode("0402");
        funcParams.add(paramVO);
        dto.setFuncParams(funcParams);
        when(activityComponentDomain.createPromoActivity(Mockito.any())).thenReturn("1");
        when(operationLogService.insertLog(Mockito.any(), Mockito.any())).thenReturn(1);
        when(couponActivityService.createCouponActivity(Mockito.any())).thenReturn("1");
        when(couponReleaseService.createCouponRelease(Mockito.any())).thenReturn("1");
        when(couponInnerCodeService.insertPromoCouponInnerCode(Mockito.any())).thenReturn(1);
        Mockito.doNothing().when(activityCacheDomain).putCache(Mockito.any(), Mockito.any());
        String couponActivity = couponActivityComponent.createCouponActivity(dto);
        Assert.assertEquals("1", couponActivity);
    }

    @Test(expected = PromotionException.class)
    public void createCouponActivity2() {
        CreateCouponActivityDTO dto = new CreateCouponActivityDTO();
        dto.setCouponType("03");
        dto.setPromotionCode("1");
        dto.setTenantCode("1");
        dto.setTotalQuantity(10);
        dto.setTemplateCode("01");
        List<FunctionParam> funcParams = new ArrayList<>();
        FunctionParam paramVO = new FunctionParam();
        paramVO.setParamValue("1");
        paramVO.setFunctionCode("0402");
        funcParams.add(paramVO);
        dto.setFuncParams(funcParams);
        when(activityComponentDomain.createPromoActivity(Mockito.any())).thenReturn("1");
        when(operationLogService.insertLog(Mockito.any(), Mockito.any())).thenReturn(1);
        when(couponActivityService.createCouponActivity(Mockito.any())).thenReturn("");
        when(tPromoProductService.deleteProducts(Mockito.any())).thenReturn(1);
        String couponActivity = couponActivityComponent.createCouponActivity(dto);
        Assert.assertEquals("1", couponActivity);
    }

    @Test
    public void findSendCouponValidActivity_null() {
        when(activityService.findEffectiveActivity(Mockito.any(), Mockito.any())).thenReturn(null);
        ActivityModel sendCouponValidActivity = couponActivityComponent.findSendCouponValidActivity("1", "1", "1", new Date());
        Assert.assertNull(sendCouponValidActivity);
    }

    @Test
    public void findSendCouponValidActivity() {

        ActivityModel activityModel = new ActivityModel();
        activityModel.setActivityStatus("01");
        when(activityService.findEffectiveActivity(Mockito.any(), Mockito.any())).thenReturn(activityModel);
        ActivityModel sendCouponValidActivity = couponActivityComponent.findSendCouponValidActivity("1", "1", "1", new Date());
        Assert.assertNull(sendCouponValidActivity);

    }

    @Test
    public void findSendCouponValidActivity_time() {

        ActivityModel activityModel = new ActivityModel();
        activityModel.setActivityEnd("20221110135526");
        when(activityService.findEffectiveActivity(Mockito.any(), Mockito.any())).thenReturn(activityModel);
        ActivityModel sendCouponValidActivity = couponActivityComponent.findSendCouponValidActivity("1", "1", "1", new Date());
        Assert.assertNull(sendCouponValidActivity);

    }

    @Test
    public void findSendCouponValidActivity_effect() {

        ActivityModel activityModel = new ActivityModel();
        activityModel.setActivityEnd("30221110135526");
        activityModel.setActivityStatus("04");
        when(activityService.findEffectiveActivity(Mockito.any(), Mockito.any())).thenReturn(activityModel);
        when(couponActivityService.findEffectiveActivity(Mockito.any(), Mockito.any())).thenReturn(null);

        ActivityModel sendCouponValidActivity = couponActivityComponent.findSendCouponValidActivity("1", "1", "1", new Date());
        Assert.assertNull(sendCouponValidActivity);

    }

    @Test
    public void findEffectiveActivity() {
        when(activityService.findEffectiveActivity(Mockito.any(), Mockito.any())).thenReturn(new ActivityModel());
        ActivityModel effectiveActivity = couponActivityComponent.findEffectiveActivity("1", "1", "1");
        Assert.assertNull(effectiveActivity);
    }

    @Test
    public void findEffectiveActivity1() {
        ActivityModel activityModel = new ActivityModel();
        activityModel.setActivityCode("1");
        when(activityService.findEffectiveActivity(Mockito.any(), Mockito.any())).thenReturn(activityModel);
        when(couponActivityService.findEffectiveActivity(Mockito.any(), Mockito.any())).thenReturn(null);
        ActivityModel effectiveActivity = couponActivityComponent.findEffectiveActivity("1", "1", "1");
        Assert.assertNull(effectiveActivity);
    }

    @Test
    public void findValidActivity() {
        when(activityService.findEffectiveActivity(Mockito.any(), Mockito.any())).thenReturn(null);
        ActivityModel validActivity = couponActivityComponent.findValidActivity("1", "1", "1", new Date());
        Assert.assertNull(validActivity);
    }

    @Test
    public void findValidActivity1() {
        ActivityModel activityModel = new ActivityModel();
        activityModel.setActivityCode("1");
        activityModel.setActivityStatus("04");
        activityModel.setActivityBegin("20210428135655");
        activityModel.setActivityEnd("20220428135655");
        when(activityService.findEffectiveActivity(Mockito.any(), Mockito.any())).thenReturn(activityModel);
        when(couponActivityService.findEffectiveActivity(Mockito.any(), Mockito.any())).thenReturn(null);
        ActivityModel validActivity = couponActivityComponent.findValidActivity("1", "1", "1", new Date());
        Assert.assertNull(validActivity);
    }

    @Test
    public void findValidActivity2() {
        ActivityModel activityModel = new ActivityModel();
        activityModel.setActivityCode("1");
        activityModel.setActivityStatus("04");
        activityModel.setActivityBegin("20210428135655");
        activityModel.setActivityEnd("20300428135655");
        when(activityService.findEffectiveActivity(Mockito.any(), Mockito.any())).thenReturn(activityModel);
        when(couponActivityService.findEffectiveActivity(Mockito.any(), Mockito.any())).thenReturn(activityModel);
        when(activityExpireComponentDomain.expireActivity((ActivityModel) Mockito.any())).thenReturn(1);
        Mockito.doNothing().when(activityComponentDomain).loadActivityLanguage(Mockito.any(), Mockito.any());
        ActivityModel validActivity = couponActivityComponent.findValidActivity("1", "1", "1", new Date());
        Assert.assertNull(validActivity.getActivityName());
    }

    @Test(expected = Exception.class)
    public void findCouponActivityByActivityCode() {
        CouponInfoInDTO couponInfo = new CouponInfoInDTO();
        couponInfo.setTenantCode("1");
        couponInfo.setActivityCode("1");
        couponInfo.setLanguage("1");
        when(activityService.findEffectiveActivity(Mockito.any(), Mockito.any())).thenReturn(null);
        CouponInfoOutDTO outDTO = couponActivityComponent.findCouponActivityByActivityCode(couponInfo);
        Assert.assertNull(outDTO);
    }

    @Test(expected = Exception.class)
    public void findCouponActivityByActivityCode_1() {
        CouponInfoInDTO couponInfo = new CouponInfoInDTO();
        couponInfo.setTenantCode("1");
        couponInfo.setActivityCode("1");
        couponInfo.setLanguage("1");
        couponInfo.setOrgCode("1");
        ActivityModel activityModel = new ActivityModel();
        activityModel.setActivityCode("1");
        activityModel.setActivityStatus("04");
        activityModel.setStoreType("01");
        activityModel.setActivityBegin("20210428135655");
        activityModel.setActivityEnd("20220428135655");
        activityModel.setUserLimitMax(1);
        List<TPromoActivityStoreVO> storeVOs = new ArrayList<>();
        TPromoActivityStoreVO tPromoActivityStoreVO = new TPromoActivityStoreVO();
        tPromoActivityStoreVO.setOrgCode("2");
        storeVOs.add(tPromoActivityStoreVO);

        when(activityService.findEffectiveActivity(Mockito.any(), Mockito.any())).thenReturn(activityModel);
        when(couponActivityService.findEffectiveActivity(Mockito.any(), Mockito.any())).thenReturn(activityModel);
        when(tPromoActivityStoreService.getStoresByActivityCode(Mockito.any())).thenReturn(storeVOs);
        CouponInfoOutDTO outDTO = couponActivityComponent.findCouponActivityByActivityCode(couponInfo);
        Assert.assertNull(outDTO);
    }

    @Test
    public void findCouponActivityByActivityCode1() {
        CouponInfoInDTO couponInfo = new CouponInfoInDTO();
        couponInfo.setTenantCode("1");
        couponInfo.setActivityCode("1");
        couponInfo.setLanguage("1");
        couponInfo.setUserCode("1");
        ActivityModel activityModel = new ActivityModel();
        activityModel.setActivityCode("1");
        activityModel.setActivityStatus("04");
        activityModel.setStoreType("00");
        activityModel.setActivityBegin("20210428135655");
        activityModel.setActivityEnd("20300428135655");
        activityModel.setUserLimitMax(1);
        TemplateModel template = new TemplateModel();
        template.setTemplateCode("************");
        List<CouponReleaseModel> releaseModels = new ArrayList<>();
        CouponReleaseModel model = new CouponReleaseModel();
        model.setReleaseStatus("02");
        model.setValidEndTime("20210428135655");
        model.setValidStartTime("20210428135655");
        model.setReceiveStartTime("20210428135655");
        model.setReceiveEndTime("20210428135655");
        releaseModels.add(model);

        when(activityService.findEffectiveActivity(Mockito.any(), Mockito.any())).thenReturn(activityModel);
        when(couponActivityService.findEffectiveActivity(Mockito.any(), Mockito.any())).thenReturn(activityModel);
        when(activityExpireComponentDomain.expireActivity((ActivityModel) Mockito.any())).thenReturn(1);
        Mockito.doNothing().when(activityComponentDomain).loadActivityLanguage(Mockito.any(), Mockito.any());
        when(activityPeriodService.findPeriod(Mockito.any(), Mockito.any())).thenReturn(new ActivityPeriodModel());
        when(tPromoActivityStoreService.getStoresByActivityCode(Mockito.any())).thenReturn(new ArrayList<>());
        when(templateService.getTemplateByCode(Mockito.any())).thenReturn(template);
        when(promoActivityFuncRankService.getRankListByActivityCode(Mockito.any())).thenReturn(new ArrayList<>());
        when(qualificationService.queryQualifications(Mockito.any(), Mockito.any())).thenReturn(new ArrayList<>());
        when(couponReleaseService.queryCouponRelease(Mockito.any())).thenReturn(releaseModels);

        CouponInfoOutDTO outDTO = couponActivityComponent.findCouponActivityByActivityCode(couponInfo);
        Assert.assertEquals("1", outDTO.getActivityCode());
    }

    @Test
    public void findCouponActivityByActivityCode2() {
        CouponInfoInDTO couponInfo = new CouponInfoInDTO();
        couponInfo.setTenantCode("1");
        couponInfo.setActivityCode("1");
        couponInfo.setLanguage("1");
        couponInfo.setUserCode("1");
        couponInfo.setOrgCode("1");
        ActivityModel activityModel = new ActivityModel();
        activityModel.setActivityCode("1");
        activityModel.setActivityStatus("04");
        activityModel.setStoreType("00");
        activityModel.setActivityBegin("20210428135655");
        activityModel.setActivityEnd("20300428135655");
        activityModel.setUserLimitMax(1);
        TemplateModel template = new TemplateModel();
        template.setTemplateCode("102103104106");
        List<TPromoActivityStoreVO> storeVOs = new ArrayList<>();
        TPromoActivityStoreVO tPromoActivityStoreVO = new TPromoActivityStoreVO();
        tPromoActivityStoreVO.setOrgCode("1");
        tPromoActivityStoreVO.setUrl("1");
        storeVOs.add(tPromoActivityStoreVO);
        List<GiveawayVO> giveawayVOS = new ArrayList<>();
        GiveawayVO giveawayVO = new GiveawayVO();
        giveawayVO.setGiveawayCode("1");
        giveawayVO.setGiveawayName("1");
        giveawayVO.setGiveawayNum(1);
        giveawayVOS.add(giveawayVO);
        List<ActivityFunctionParamRankModel> rankList = new ArrayList<>();
        ActivityFunctionParamRankModel rankVO = new ActivityFunctionParamRankModel();
        rankVO.setId("1");
        rankList.add(rankVO);
        FunctionParamModel functionParamModel = new FunctionParamModel();
        functionParamModel.setParamValue("1");

        when(activityService.findEffectiveActivity(Mockito.any(), Mockito.any())).thenReturn(activityModel);
        when(couponActivityService.findEffectiveActivity(Mockito.any(), Mockito.any())).thenReturn(activityModel);
        when(activityExpireComponentDomain.expireActivity((ActivityModel) Mockito.any())).thenReturn(1);
        Mockito.doNothing().when(activityComponentDomain).loadActivityLanguage(Mockito.any(), Mockito.any());
        when(activityPeriodService.findPeriod(Mockito.any(), Mockito.any())).thenReturn(new ActivityPeriodModel());
        when(tPromoActivityStoreService.getStoresByActivityCode(Mockito.any())).thenReturn(storeVOs);
        when(templateService.getTemplateByCode(Mockito.any())).thenReturn(template);
        when(giveawayService.getGiftListByActivityCode(Mockito.any(), Mockito.any())).thenReturn(giveawayVOS);
        when(promoActivityFuncRankService.getRankListByActivityCode(Mockito.any())).thenReturn(rankList);
        when(promoActivityFuncParamService.getRuleFuncParamListByRankId(Mockito.any())).thenReturn(new ArrayList<>());
        when(promoActivityFuncParamService.getRuleFuncParamListByRankIdAndFunctionCode(Mockito.any(), Mockito.any())).thenReturn(functionParamModel);
        when(qualificationService.queryQualifications(Mockito.any(), Mockito.any())).thenReturn(new ArrayList<>());
        when(couponReleaseService.queryCouponRelease(Mockito.any())).thenReturn(new ArrayList<>());

        CouponInfoOutDTO outDTO = couponActivityComponent.findCouponActivityByActivityCode(couponInfo);
        Assert.assertEquals("1", outDTO.getActivityCode());
    }

    @Test
    public void findCouponActivityByActivityCode3() {
        CouponInfoInDTO couponInfo = new CouponInfoInDTO();
        couponInfo.setTenantCode("1");
        couponInfo.setActivityCode("1");
        couponInfo.setLanguage("1");
        couponInfo.setUserCode("1");
        ActivityModel activityModel = new ActivityModel();
        activityModel.setActivityCode("1");
        activityModel.setActivityStatus("04");
        activityModel.setStoreType("00");
        activityModel.setActivityBegin("20210428135655");
        activityModel.setActivityEnd("20300428135655");
        activityModel.setUserLimitMax(1);
        TemplateModel template = new TemplateModel();
        template.setTemplateCode("************");
        List<CouponReleaseModel> releaseModels = new ArrayList<>();
        CouponReleaseModel model = new CouponReleaseModel();
        model.setReleaseStatus("04");
        model.setValidEndTime("20210428135655");
        model.setValidStartTime("20210428135655");
        model.setReceiveStartTime("20210428135655");
        model.setReceiveEndTime("20210428135655");
        releaseModels.add(model);

        when(activityService.findEffectiveActivity(Mockito.any(), Mockito.any())).thenReturn(activityModel);
        when(couponActivityService.findEffectiveActivity(Mockito.any(), Mockito.any())).thenReturn(activityModel);
        when(activityExpireComponentDomain.expireActivity((ActivityModel) Mockito.any())).thenReturn(1);
        Mockito.doNothing().when(activityComponentDomain).loadActivityLanguage(Mockito.any(), Mockito.any());
        when(activityPeriodService.findPeriod(Mockito.any(), Mockito.any())).thenReturn(new ActivityPeriodModel());
        when(tPromoActivityStoreService.getStoresByActivityCode(Mockito.any())).thenReturn(new ArrayList<>());
        when(templateService.getTemplateByCode(Mockito.any())).thenReturn(template);
        when(promoActivityFuncRankService.getRankListByActivityCode(Mockito.any())).thenReturn(new ArrayList<>());
        when(qualificationService.queryQualifications(Mockito.any(), Mockito.any())).thenReturn(new ArrayList<>());
        when(couponReleaseService.queryCouponRelease(Mockito.any())).thenReturn(releaseModels);

        CouponInfoOutDTO outDTO = couponActivityComponent.findCouponActivityByActivityCode(couponInfo);
        Assert.assertEquals("1", outDTO.getActivityCode());
    }

    @Test
    public void findCouponActivityByActivityCode4() {
        CouponInfoInDTO couponInfo = new CouponInfoInDTO();
        couponInfo.setTenantCode("1");
        couponInfo.setActivityCode("1");
        couponInfo.setLanguage("1");
        couponInfo.setUserCode("1");
        ActivityModel activityModel = new ActivityModel();
        activityModel.setActivityCode("1");
        activityModel.setActivityStatus("04");
        activityModel.setStoreType("00");
        activityModel.setActivityBegin("20210428135655");
        activityModel.setActivityEnd("20300428135655");
        activityModel.setUserLimitMax(1);
        TemplateModel template = new TemplateModel();
        template.setTemplateCode("************");
        List<CouponReleaseModel> releaseModels = new ArrayList<>();
        CouponReleaseModel model = new CouponReleaseModel();
        model.setReleaseStatus("02");
        model.setValidEndTime("20210428135655");
        model.setValidStartTime("20210428135655");
        model.setReceiveStartTime("20220428135655");
        model.setReceiveEndTime("20210428135655");
        releaseModels.add(model);

        when(activityService.findEffectiveActivity(Mockito.any(), Mockito.any())).thenReturn(activityModel);
        when(couponActivityService.findEffectiveActivity(Mockito.any(), Mockito.any())).thenReturn(activityModel);
        when(activityExpireComponentDomain.expireActivity((ActivityModel) Mockito.any())).thenReturn(1);
        Mockito.doNothing().when(activityComponentDomain).loadActivityLanguage(Mockito.any(), Mockito.any());
        when(activityPeriodService.findPeriod(Mockito.any(), Mockito.any())).thenReturn(new ActivityPeriodModel());
        when(tPromoActivityStoreService.getStoresByActivityCode(Mockito.any())).thenReturn(new ArrayList<>());
        when(templateService.getTemplateByCode(Mockito.any())).thenReturn(template);
        when(promoActivityFuncRankService.getRankListByActivityCode(Mockito.any())).thenReturn(new ArrayList<>());
        when(qualificationService.queryQualifications(Mockito.any(), Mockito.any())).thenReturn(new ArrayList<>());
        when(couponReleaseService.queryCouponRelease(Mockito.any())).thenReturn(releaseModels);

        CouponInfoOutDTO outDTO = couponActivityComponent.findCouponActivityByActivityCode(couponInfo);
        Assert.assertEquals("1", outDTO.getActivityCode());
    }

    @Test
    public void findCouponActivityByActivityCode5() {
        CouponInfoInDTO couponInfo = new CouponInfoInDTO();
        couponInfo.setTenantCode("1");
        couponInfo.setActivityCode("1");
        couponInfo.setLanguage("1");
        couponInfo.setUserCode("1");
        ActivityModel activityModel = new ActivityModel();
        activityModel.setActivityCode("1");
        activityModel.setActivityStatus("04");
        activityModel.setStoreType("00");
        activityModel.setActivityBegin("20210428135655");
        activityModel.setActivityEnd("20300428135655");
        activityModel.setUserLimitMax(1);
        TemplateModel template = new TemplateModel();
        template.setTemplateCode("************");
        List<CouponReleaseModel> releaseModels = new ArrayList<>();
        CouponReleaseModel model = new CouponReleaseModel();
        model.setReleaseStatus("02");
        model.setValidEndTime("20210428135655");
        model.setValidStartTime("20210428135655");
        model.setReceiveStartTime("20210428135655");
        model.setReceiveEndTime("20220428135655");
        releaseModels.add(model);

        when(activityService.findEffectiveActivity(Mockito.any(), Mockito.any())).thenReturn(activityModel);
        when(couponActivityService.findEffectiveActivity(Mockito.any(), Mockito.any())).thenReturn(activityModel);
        when(activityExpireComponentDomain.expireActivity((ActivityModel) Mockito.any())).thenReturn(1);
        Mockito.doNothing().when(activityComponentDomain).loadActivityLanguage(Mockito.any(), Mockito.any());
        when(activityPeriodService.findPeriod(Mockito.any(), Mockito.any())).thenReturn(new ActivityPeriodModel());
        when(tPromoActivityStoreService.getStoresByActivityCode(Mockito.any())).thenReturn(new ArrayList<>());
        when(templateService.getTemplateByCode(Mockito.any())).thenReturn(template);
        when(promoActivityFuncRankService.getRankListByActivityCode(Mockito.any())).thenReturn(new ArrayList<>());
        when(qualificationService.queryQualifications(Mockito.any(), Mockito.any())).thenReturn(new ArrayList<>());
        when(couponReleaseService.queryCouponRelease(Mockito.any())).thenReturn(releaseModels);
        when(couponCodeUserService.getUserCouponCountByActivityCode(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(1);

        CouponInfoOutDTO outDTO = couponActivityComponent.findCouponActivityByActivityCode(couponInfo);
        Assert.assertEquals("1", outDTO.getActivityCode());
    }

    @Test
    public void findCouponActivityByActivityCode6() {
        CouponInfoInDTO couponInfo = new CouponInfoInDTO();
        couponInfo.setTenantCode("1");
        couponInfo.setActivityCode("1");
        couponInfo.setLanguage("1");
        couponInfo.setUserCode("1");
        ActivityModel activityModel = new ActivityModel();
        activityModel.setActivityCode("1");
        activityModel.setActivityStatus("04");
        activityModel.setStoreType("00");
        activityModel.setActivityBegin("20210428135655");
        activityModel.setActivityEnd("20300428135655");
        activityModel.setUserLimitMax(0);
        TemplateModel template = new TemplateModel();
        template.setTemplateCode("************");
        List<CouponReleaseModel> releaseModels = new ArrayList<>();
        CouponReleaseModel model = new CouponReleaseModel();
        model.setReleaseStatus("02");
        model.setValidEndTime("20210428135655");
        model.setValidStartTime("20210428135655");
        model.setReceiveStartTime("20210428135655");
        model.setReceiveEndTime("20220428135655");
        model.setReleaseQuantity(2);
        releaseModels.add(model);

        when(activityService.findEffectiveActivity(Mockito.any(), Mockito.any())).thenReturn(activityModel);
        when(couponActivityService.findEffectiveActivity(Mockito.any(), Mockito.any())).thenReturn(activityModel);
        when(activityExpireComponentDomain.expireActivity((ActivityModel) Mockito.any())).thenReturn(1);
        Mockito.doNothing().when(activityComponentDomain).loadActivityLanguage(Mockito.any(), Mockito.any());
        when(activityPeriodService.findPeriod(Mockito.any(), Mockito.any())).thenReturn(new ActivityPeriodModel());
        when(tPromoActivityStoreService.getStoresByActivityCode(Mockito.any())).thenReturn(new ArrayList<>());
        when(templateService.getTemplateByCode(Mockito.any())).thenReturn(template);
        when(promoActivityFuncRankService.getRankListByActivityCode(Mockito.any())).thenReturn(new ArrayList<>());
        when(qualificationService.queryQualifications(Mockito.any(), Mockito.any())).thenReturn(new ArrayList<>());
        when(couponReleaseService.queryCouponRelease(Mockito.any())).thenReturn(releaseModels);
        when(couponCodeUserService.countByReleaseCode(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(1);

        CouponInfoOutDTO outDTO = couponActivityComponent.findCouponActivityByActivityCode(couponInfo);
        Assert.assertEquals("1", outDTO.getActivityCode());
    }

    @Test
    public void findCouponActivityByActivityCode7() {
        CouponInfoInDTO couponInfo = new CouponInfoInDTO();
        couponInfo.setTenantCode("1");
        couponInfo.setActivityCode("1");
        couponInfo.setLanguage("1");
        couponInfo.setUserCode("1");
        ActivityModel activityModel = new ActivityModel();
        activityModel.setActivityCode("1");
        activityModel.setActivityStatus("04");
        activityModel.setStoreType("00");
        activityModel.setActivityBegin("20210428135655");
        activityModel.setActivityEnd("20300428135655");
        activityModel.setUserLimitMax(0);
        TemplateModel template = new TemplateModel();
        template.setTemplateCode("************");
        List<CouponReleaseModel> releaseModels = new ArrayList<>();
        CouponReleaseModel model = new CouponReleaseModel();
        model.setReleaseStatus("02");
        model.setValidEndTime("20210428135655");
        model.setValidStartTime("20210428135655");
        model.setReceiveStartTime("20210428135655");

        Calendar instance = Calendar.getInstance();
        instance.add(Calendar.DATE, 1);
        Date time = instance.getTime();

        String format = DateUtils.format(time, DateUtil.DATETIMESTOREFORMAT);
        model.setReceiveEndTime(format);
        model.setReleaseQuantity(2);
        releaseModels.add(model);

        when(activityService.findEffectiveActivity(Mockito.any(), Mockito.any())).thenReturn(activityModel);
        when(couponActivityService.findEffectiveActivity(Mockito.any(), Mockito.any())).thenReturn(activityModel);
        when(activityExpireComponentDomain.expireActivity((ActivityModel) Mockito.any())).thenReturn(1);
        Mockito.doNothing().when(activityComponentDomain).loadActivityLanguage(Mockito.any(), Mockito.any());
        when(activityPeriodService.findPeriod(Mockito.any(), Mockito.any())).thenReturn(new ActivityPeriodModel());
        when(tPromoActivityStoreService.getStoresByActivityCode(Mockito.any())).thenReturn(new ArrayList<>());
        when(templateService.getTemplateByCode(Mockito.any())).thenReturn(template);
        when(promoActivityFuncRankService.getRankListByActivityCode(Mockito.any())).thenReturn(new ArrayList<>());
        when(qualificationService.queryQualifications(Mockito.any(), Mockito.any())).thenReturn(new ArrayList<>());
        when(couponReleaseService.queryCouponRelease(Mockito.any())).thenReturn(releaseModels);
        when(couponCodeUserService.countByReleaseCode(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(1);

        CouponInfoOutDTO outDTO = couponActivityComponent.findCouponActivityByActivityCode(couponInfo);
        Assert.assertEquals("1", outDTO.getActivityCode());
    }


    @Test
    public void expireCoupon() {
        CouponDomain couponDomain = new CouponDomain();
        couponDomain.setStatus("01");
        couponDomain.setValidEndTime("20210428135655");
        when(couponInnerCodeService.updateCouponStatus(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(1);
        when(couponCodeUserService.updateCouponStatus(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(1);
        couponActivityComponent.expireCoupon(couponDomain);
    }

    @Test
    public void exportCoupon() {
        when(couponInnerCodeService.exportCoupon(Mockito.any())).thenReturn(new ArrayList<>());
        List<ExportCouponOutDTO> dtos = couponActivityComponent.exportCoupon(new ExportCouponInDTO());
        Assert.assertEquals(0, dtos.size());
    }

    @Test
    public void queryCouponReleaseList() {
        when(couponReleaseService.queryCouponRelease(Mockito.any())).thenReturn(new ArrayList<>());
        List<CouponReleaseModel> dtos = couponActivityComponent.queryCouponReleaseList("1");
        Assert.assertEquals(0, dtos.size());
    }

    @Test
    public void closeCoupon() {
        when(couponReleaseService.stopCouponRelease(Mockito.any())).thenReturn(1);
        when(couponInnerCodeService.logicDelete(Mockito.any(), Mockito.any())).thenReturn(1);
        when(couponCodeUserService.logicDelete(Mockito.any(), Mockito.any())).thenReturn(1);
        couponActivityComponent.closeCoupon("1", "1");
    }

    @Test
    public void queryCouponActivityByTenantCode() {
        when(activityService.queryActivityByTenantCode(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(new ArrayList<>());
        List<CouponActivityDTO> dtos = couponActivityComponent.queryCouponActivityByTenantCode("1", "1", null);
        Assert.assertEquals(0, dtos.size());
    }

    @Test
    public void queryCouponActivityByTenantCode1() {
        List<ActivityModel> list = new ArrayList<>();
        ActivityModel activityModel = new ActivityModel();
        activityModel.setActivityCode("1");
        activityModel.setActivityBegin("20220428135655");
        activityModel.setCouponType("01");
        activityModel.setStoreType("01");
        ActivityModel activityModel1 = new ActivityModel();
        activityModel1.setActivityCode("1");
        activityModel1.setActivityBegin("20210428135655");
        activityModel1.setCouponType("02");
        activityModel1.setStoreType("01");
        list.add(activityModel);
        list.add(activityModel1);
        List<TPromoActivityStoreVO> storeVOs = new ArrayList<>();
        TPromoActivityStoreVO tPromoActivityStoreVO = new TPromoActivityStoreVO();
        tPromoActivityStoreVO.setOrgCode("01");
        tPromoActivityStoreVO.setUrl("1");
        storeVOs.add(tPromoActivityStoreVO);

        when(activityService.queryActivityByTenantCode(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(list);
        when(couponActivityService.findCouponActivity(Mockito.any(), Mockito.any())).thenReturn(activityModel);
        when(languageService.findActivityLanguage(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(new ActivityLanguageModel());
        when(tPromoActivityStoreService.getStoresByActivityCode(Mockito.any())).thenReturn(storeVOs);

        List<CouponActivityDTO> dtos = couponActivityComponent.queryCouponActivityByTenantCode("1", "1", "01");
    }

    @Test
    public void queryCouponActivityByTenantCode2() {
        List<ActivityModel> list = new ArrayList<>();
        ActivityModel activityModel = new ActivityModel();
        activityModel.setActivityCode("1");
        activityModel.setActivityBegin("20220428135655");
        activityModel.setCouponType("01");
        activityModel.setStoreType("01");
        ActivityModel activityModel1 = new ActivityModel();
        activityModel1.setActivityCode("1");
        activityModel1.setActivityBegin("20210428135655");
        activityModel1.setCouponType("02");
        activityModel1.setStoreType("00");
        list.add(activityModel);
        list.add(activityModel1);

        when(activityService.queryActivityByTenantCode(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(list);
        when(couponActivityService.findCouponActivity(Mockito.any(), Mockito.any())).thenReturn(activityModel);
        when(languageService.findActivityLanguage(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(new ActivityLanguageModel());

        List<CouponActivityDTO> dtos = couponActivityComponent.queryCouponActivityByTenantCode("1", "1", "01");
        Assert.assertEquals(1, dtos.size());
    }

    @Test
    public void queryCouponActivityList() {
        CouponActivityListInDTO inDTO = new CouponActivityListInDTO();
        inDTO.setTagCode("1");
        Map<String, String> tagMap = new HashMap<>();
        tagMap.put("1", "************");

        when(templateService.findTemplateTagAll()).thenReturn(tagMap);
        when(couponActivityService.queryCouponActivityList(Mockito.any())).thenReturn(new PageInfo<>());

        PageInfo<CouponActivityListOutDTO> pageInfo = couponActivityComponent.queryCouponActivityList(inDTO);
        Assert.assertEquals(0, pageInfo.getTotal());
    }

    @Test
    public void queryCouponActivityList4() {
        CouponActivityListInDTO inDTO = new CouponActivityListInDTO();
        Map<String, String> tagMap = new HashMap<>();
        tagMap.put("1", "************");

        when(templateService.findTemplateTagAll()).thenReturn(tagMap);
        when(couponActivityService.queryCouponActivityList(Mockito.any())).thenReturn(new PageInfo<>());

        PageInfo<CouponActivityListOutDTO> pageInfo = couponActivityComponent.queryCouponActivityList(inDTO);
        Assert.assertEquals(0, pageInfo.getTotal());
    }

    @Test
    public void queryCouponActivityList1() {
        CouponActivityListInDTO inDTO = new CouponActivityListInDTO();

        inDTO.setTagCode("1");
        Map<String, String> tagMap = new HashMap<>();
        tagMap.put("1", "************");
        PageInfo<CouponActivityListOutDTO> promoCouponsDTOs = new PageInfo<>();
        List<CouponActivityListOutDTO> list = new ArrayList<>();
        CouponActivityListOutDTO dto = new CouponActivityListOutDTO();
        dto.setCreateUser("1");
        dto.setCouponType("03");
        dto.setTotalQuantity("1");
        list.add(dto);
        promoCouponsDTOs.setList(list);
        List<QueryOpUserAccountListResult> idmOpUserAccount = new ArrayList<>();
        QueryOpUserAccountListResult result = new QueryOpUserAccountListResult();
        result.setUserCode("1");
        result.setFirstName("1");
        result.setLastName("1");
        idmOpUserAccount.add(result);
        List<TPromoActivityStoreVO> storeVOs = new ArrayList<>();
        TPromoActivityStoreVO tPromoActivityStoreVO = new TPromoActivityStoreVO();
        tPromoActivityStoreVO.setOrgCode("01");
        tPromoActivityStoreVO.setChannelCode("1");
        tPromoActivityStoreVO.setChannelName("1");
        tPromoActivityStoreVO.setStoreName("1");
        tPromoActivityStoreVO.setUrl("1");
        storeVOs.add(tPromoActivityStoreVO);

        when(templateService.findTemplateTagAll()).thenReturn(tagMap);
        when(couponActivityService.queryCouponActivityList(Mockito.any())).thenReturn(promoCouponsDTOs);
        when(idmFeignClientComponent.getIdmOpUserAccount(Mockito.any())).thenReturn(idmOpUserAccount);
        when(languageService.findActivityLanguage(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(new ActivityLanguageModel());
        when(tPromoActivityStoreService.getStoresByActivityCode(Mockito.any())).thenReturn(storeVOs);

        List<QueryUserResult> userAccountList = new ArrayList<>();
        QueryUserResult result1 = new QueryUserResult();
        result1.setFirstName("!");
        result1.setLastName("1");
        result1.setUserCode("1");
        userAccountList.add(result1);
        when(idmFeignClientComponent.queryIdmUserList(Mockito.any(), Mockito.any(), Mockito.anyList())).thenReturn(userAccountList);

        PageInfo<CouponActivityListOutDTO> pageInfo = couponActivityComponent.queryCouponActivityList(inDTO);
        Assert.assertEquals(0, pageInfo.getTotal());
    }

    @Test
    public void queryCouponActivityList2() {
        CouponActivityListInDTO inDTO = new CouponActivityListInDTO();
        inDTO.setTagCode("1");
        Map<String, String> tagMap = new HashMap<>();
        tagMap.put("1", "************");
        PageInfo<CouponActivityListOutDTO> promoCouponsDTOs = new PageInfo<>();
        List<CouponActivityListOutDTO> list = new ArrayList<>();
        CouponActivityListOutDTO dto = new CouponActivityListOutDTO();
        dto.setCreateUser("1");
        dto.setCouponType("01");
        dto.setTotalQuantity("1");
        dto.setTemplateCode("************");
        list.add(dto);
        promoCouponsDTOs.setList(list);
        List<QueryOpUserAccountListResult> idmOpUserAccount = new ArrayList<>();
        QueryOpUserAccountListResult result = new QueryOpUserAccountListResult();
        result.setUserCode("1");
        result.setFirstName("1");
        result.setLastName("1");
        idmOpUserAccount.add(result);
        List<TPromoActivityStoreVO> storeVOs = new ArrayList<>();
        TPromoActivityStoreVO tPromoActivityStoreVO = new TPromoActivityStoreVO();
        tPromoActivityStoreVO.setOrgCode("01");
        tPromoActivityStoreVO.setChannelCode("1");
        tPromoActivityStoreVO.setChannelName("1");
        tPromoActivityStoreVO.setStoreName("1");
        tPromoActivityStoreVO.setUrl("1");
        storeVOs.add(tPromoActivityStoreVO);

        when(templateService.findTemplateTagAll()).thenReturn(tagMap);
        when(couponActivityService.queryCouponActivityList(Mockito.any())).thenReturn(promoCouponsDTOs);
        PromoGroupVO promoGroupVO = new PromoGroupVO();
        promoGroupVO.setGroupName("!");
        when(promotionGroupService.getGroupByGroupCode(Mockito.anyString(),Mockito.anyString())).thenReturn(promoGroupVO);
        when(idmFeignClientComponent.getIdmOpUserAccount(Mockito.any())).thenReturn(idmOpUserAccount);
        when(languageService.findActivityLanguage(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(new ActivityLanguageModel());
        when(tPromoActivityStoreService.getStoresByActivityCode(Mockito.any())).thenReturn(storeVOs);
        when(couponReleaseService.queryReleaseCount111(Mockito.any())).thenReturn(1L);

        PageInfo<CouponActivityListOutDTO> pageInfo = couponActivityComponent.queryCouponActivityList(inDTO);
        Assert.assertEquals(0, pageInfo.getTotal());
    }

    @Test
    public void queryCouponActivityInStore() {
        StoreCouponActivityInDTO store = new StoreCouponActivityInDTO();

        when(activityCacheDomain.getActivityCacheMap(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(new HashMap<>());

        PageInfo<StoreCouponActivityOutDTO> pageInfo = couponActivityComponent.queryCouponActivityInStore(store);
        Assert.assertEquals(0, pageInfo.getTotal());
    }

    @Test
    public void queryCouponActivityInStore1() {
        StoreCouponActivityInDTO store = new StoreCouponActivityInDTO();
        store.setOrgCode("01");
        Map<String, ActivityCacheDTO> activityCacheMap = new HashMap<>();
        ActivityModel activityModel = new ActivityModel();
        activityModel.setCouponType("01");
        activityModel.setActivityBegin("20210428135655");
        activityModel.setShowFlag(1);
        activityModel.setActivityCode("1");
        TemplateModel template = new TemplateModel();
        template.setTemplateCode("02");
        List<FunctionParamModel> paramList = new ArrayList<>();
        FunctionParamModel param = new FunctionParamModel();
        param.setFunctionType("03");
        param.setParamValue("1");
        paramList.add(param);
        ActivityCacheDTO dto = new ActivityCacheDTO();
        dto.setActivityModel(activityModel);
        dto.setPromoTemplate(template);
        dto.setPromoFuncParams(paramList);
        activityCacheMap.put("1", dto);
        List<CouponReleaseDomain> releaseDomains = new ArrayList<>();
        CouponReleaseDomain domain = new CouponReleaseDomain();
        domain.setActivityCode("1");
        domain.setReceiveStartTime("20210428135655");
        domain.setReceiveEndTime("20220428135655");
        domain.setInventory(1);
        releaseDomains.add(domain);
        List<TPromoActivityStoreVO> storeVOs = new ArrayList<>();
        TPromoActivityStoreVO tPromoActivityStoreVO = new TPromoActivityStoreVO();
        tPromoActivityStoreVO.setOrgCode("01");
        tPromoActivityStoreVO.setChannelCode("1");
        tPromoActivityStoreVO.setChannelName("1");
        tPromoActivityStoreVO.setStoreName("1");
        tPromoActivityStoreVO.setUrl("1");
        storeVOs.add(tPromoActivityStoreVO);


        when(activityCacheDomain.getActivityCacheMap(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(activityCacheMap);
		when(couponReleaseService.queryReleasesByActivityCodes(Mockito.any(), Mockito.any(), Mockito.anyBoolean())).thenReturn(releaseDomains);
        when(tPromoActivityStoreService.getStoresByActivityCode(Mockito.any())).thenReturn(storeVOs);

        PageInfo<StoreCouponActivityOutDTO> pageInfo = couponActivityComponent.queryCouponActivityInStore(store);
        Assert.assertEquals(1, pageInfo.getTotal());
    }

    @Test
    public void queryCouponActivityInStore2() {
        StoreCouponActivityInDTO store = new StoreCouponActivityInDTO();
        store.setOrgCode("01");
        Map<String, ActivityCacheDTO> activityCacheMap = new HashMap<>();
        ActivityModel activityModel = new ActivityModel();
        activityModel.setCouponType("01");
        activityModel.setActivityBegin("20210428135655");
        activityModel.setShowFlag(1);
        activityModel.setActivityCode("1");
        TemplateModel template = new TemplateModel();
        template.setTemplateCode("02");
        ActivityCacheDTO dto = new ActivityCacheDTO();
        dto.setActivityModel(activityModel);
        dto.setPromoTemplate(template);
        dto.setPromoFuncParams(new ArrayList<>());
        activityCacheMap.put("1", dto);
        List<TPromoActivityStoreVO> storeVOs = new ArrayList<>();
        TPromoActivityStoreVO tPromoActivityStoreVO = new TPromoActivityStoreVO();
        tPromoActivityStoreVO.setOrgCode("02");
        tPromoActivityStoreVO.setChannelCode("1");
        tPromoActivityStoreVO.setChannelName("1");
        tPromoActivityStoreVO.setStoreName("1");
        tPromoActivityStoreVO.setUrl("1");
        storeVOs.add(tPromoActivityStoreVO);


        when(activityCacheDomain.getActivityCacheMap(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(activityCacheMap);
		when(couponReleaseService.queryReleasesByActivityCodes(Mockito.any(), Mockito.any(), Mockito.anyBoolean())).thenReturn(new ArrayList<>());
        when(tPromoActivityStoreService.getStoresByActivityCode(Mockito.any())).thenReturn(storeVOs);

        PageInfo<StoreCouponActivityOutDTO> pageInfo = couponActivityComponent.queryCouponActivityInStore(store);
        Assert.assertEquals(0, pageInfo.getTotal());
    }

    @Test
    public void queryCouponActivityInStore3() {
        StoreCouponActivityInDTO store = new StoreCouponActivityInDTO();
        store.setOrgCode("01");
        Map<String, ActivityCacheDTO> activityCacheMap = new HashMap<>();
        ActivityModel activityModel = new ActivityModel();
        activityModel.setCouponType("01");
        activityModel.setActivityBegin("20210428135655");
        activityModel.setShowFlag(1);
        activityModel.setActivityCode("1");
        TemplateModel template = new TemplateModel();
        template.setTemplateCode("02");
        ActivityCacheDTO dto = new ActivityCacheDTO();
        dto.setActivityModel(activityModel);
        dto.setPromoTemplate(template);
        dto.setPromoFuncParams(new ArrayList<>());
        activityCacheMap.put("1", dto);
        List<TPromoActivityStoreVO> storeVOs = new ArrayList<>();
        TPromoActivityStoreVO tPromoActivityStoreVO = new TPromoActivityStoreVO();
        tPromoActivityStoreVO.setOrgCode("01");
        tPromoActivityStoreVO.setChannelCode("1");
        tPromoActivityStoreVO.setChannelName("1");
        tPromoActivityStoreVO.setStoreName("1");
        tPromoActivityStoreVO.setUrl("1");
        storeVOs.add(tPromoActivityStoreVO);


        when(activityCacheDomain.getActivityCacheMap(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(activityCacheMap);
		when(couponReleaseService.queryReleasesByActivityCodes(Mockito.any(), Mockito.any(), Mockito.anyBoolean())).thenReturn(new ArrayList<>());
        when(tPromoActivityStoreService.getStoresByActivityCode(Mockito.any())).thenReturn(storeVOs);

        PageInfo<StoreCouponActivityOutDTO> pageInfo = couponActivityComponent.queryCouponActivityInStore(store);
        Assert.assertEquals(1, pageInfo.getTotal());
    }

    @Test
    public void queryCouponActivityInStore4() {
        StoreCouponActivityInDTO store = new StoreCouponActivityInDTO();
        store.setOrgCode("01");
        Map<String, ActivityCacheDTO> activityCacheMap = new HashMap<>();
        ActivityModel activityModel = new ActivityModel();
        activityModel.setCouponType("01");
        activityModel.setActivityBegin("20210428135655");
        activityModel.setShowFlag(1);
        activityModel.setActivityCode("1");
        TemplateModel template = new TemplateModel();
        template.setTemplateCode("02");
        ActivityCacheDTO dto = new ActivityCacheDTO();
        dto.setActivityModel(activityModel);
        dto.setPromoTemplate(template);
        dto.setPromoFuncParams(new ArrayList<>());
        activityCacheMap.put("1", dto);
        List<CouponReleaseDomain> releaseDomains = new ArrayList<>();
        CouponReleaseDomain domain = new CouponReleaseDomain();
        domain.setActivityCode("1");
        domain.setReceiveStartTime("20210428135655");
        domain.setReceiveEndTime("20220428135655");
        domain.setInventory(1);
        domain.setReleaseStatus("04");
        releaseDomains.add(domain);
        List<TPromoActivityStoreVO> storeVOs = new ArrayList<>();
        TPromoActivityStoreVO tPromoActivityStoreVO = new TPromoActivityStoreVO();
        tPromoActivityStoreVO.setOrgCode("01");
        tPromoActivityStoreVO.setChannelCode("1");
        tPromoActivityStoreVO.setChannelName("1");
        tPromoActivityStoreVO.setStoreName("1");
        tPromoActivityStoreVO.setUrl("1");
        storeVOs.add(tPromoActivityStoreVO);


        when(activityCacheDomain.getActivityCacheMap(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(activityCacheMap);
		when(couponReleaseService.queryReleasesByActivityCodes(Mockito.any(), Mockito.any(), Mockito.anyBoolean())).thenReturn(releaseDomains);
        when(tPromoActivityStoreService.getStoresByActivityCode(Mockito.any())).thenReturn(storeVOs);

        PageInfo<StoreCouponActivityOutDTO> pageInfo = couponActivityComponent.queryCouponActivityInStore(store);
        Assert.assertEquals(1, pageInfo.getTotal());
    }

    @Test
    public void queryCouponActivityInStore5() {
        StoreCouponActivityInDTO store = new StoreCouponActivityInDTO();
        store.setOrgCode("01");
        Map<String, ActivityCacheDTO> activityCacheMap = new HashMap<>();
        ActivityModel activityModel = new ActivityModel();
        activityModel.setCouponType("01");
        activityModel.setActivityBegin("20210428135655");
        activityModel.setShowFlag(1);
        activityModel.setActivityCode("1");
        TemplateModel template = new TemplateModel();
        template.setTemplateCode("02");
        ActivityCacheDTO dto = new ActivityCacheDTO();
        dto.setActivityModel(activityModel);
        dto.setPromoTemplate(template);
        dto.setPromoFuncParams(new ArrayList<>());
        activityCacheMap.put("1", dto);
        List<CouponReleaseDomain> releaseDomains = new ArrayList<>();
        CouponReleaseDomain domain = new CouponReleaseDomain();
        domain.setActivityCode("1");
        domain.setReceiveStartTime("20220428135655");
        domain.setReceiveEndTime("20220428135655");
        domain.setInventory(1);
        domain.setReleaseStatus("02");
        releaseDomains.add(domain);
        List<TPromoActivityStoreVO> storeVOs = new ArrayList<>();
        TPromoActivityStoreVO tPromoActivityStoreVO = new TPromoActivityStoreVO();
        tPromoActivityStoreVO.setOrgCode("01");
        tPromoActivityStoreVO.setChannelCode("1");
        tPromoActivityStoreVO.setChannelName("1");
        tPromoActivityStoreVO.setStoreName("1");
        tPromoActivityStoreVO.setUrl("1");
        storeVOs.add(tPromoActivityStoreVO);


        when(activityCacheDomain.getActivityCacheMap(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(activityCacheMap);
		when(couponReleaseService.queryReleasesByActivityCodes(Mockito.any(), Mockito.any(), Mockito.anyBoolean())).thenReturn(releaseDomains);
        when(tPromoActivityStoreService.getStoresByActivityCode(Mockito.any())).thenReturn(storeVOs);

        PageInfo<StoreCouponActivityOutDTO> pageInfo = couponActivityComponent.queryCouponActivityInStore(store);
        Assert.assertEquals(1, pageInfo.getTotal());
    }

    @Test
    public void queryCouponActivityInStore6() {
        StoreCouponActivityInDTO store = new StoreCouponActivityInDTO();
        store.setOrgCode("01");
        Map<String, ActivityCacheDTO> activityCacheMap = new HashMap<>();
        ActivityModel activityModel = new ActivityModel();
        activityModel.setCouponType("01");
        activityModel.setActivityBegin("20210428135655");
        activityModel.setShowFlag(1);
        activityModel.setActivityCode("1");
        TemplateModel template = new TemplateModel();
        template.setTemplateCode("02");
        ActivityCacheDTO dto = new ActivityCacheDTO();
        dto.setActivityModel(activityModel);
        dto.setPromoTemplate(template);
        dto.setPromoFuncParams(new ArrayList<>());
        activityCacheMap.put("1", dto);
        List<CouponReleaseDomain> releaseDomains = new ArrayList<>();
        CouponReleaseDomain domain = new CouponReleaseDomain();
        domain.setActivityCode("1");
        domain.setReceiveStartTime("20210428135655");
        domain.setReceiveEndTime("20210428135655");
        domain.setInventory(1);
        domain.setReleaseStatus("02");
        releaseDomains.add(domain);
        List<TPromoActivityStoreVO> storeVOs = new ArrayList<>();
        TPromoActivityStoreVO tPromoActivityStoreVO = new TPromoActivityStoreVO();
        tPromoActivityStoreVO.setOrgCode("01");
        tPromoActivityStoreVO.setChannelCode("1");
        tPromoActivityStoreVO.setChannelName("1");
        tPromoActivityStoreVO.setStoreName("1");
        tPromoActivityStoreVO.setUrl("1");
        storeVOs.add(tPromoActivityStoreVO);


        when(activityCacheDomain.getActivityCacheMap(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(activityCacheMap);
		when(couponReleaseService.queryReleasesByActivityCodes(Mockito.any(), Mockito.any(), Mockito.anyBoolean())).thenReturn(releaseDomains);
        when(tPromoActivityStoreService.getStoresByActivityCode(Mockito.any())).thenReturn(storeVOs);

        PageInfo<StoreCouponActivityOutDTO> pageInfo = couponActivityComponent.queryCouponActivityInStore(store);
        Assert.assertEquals(1, pageInfo.getTotal());
    }

    @Test
    public void queryCouponActivityInStore7() {
        StoreCouponActivityInDTO store = new StoreCouponActivityInDTO();
        store.setOrgCode("01");
        store.setUserCode("1");
        Map<String, ActivityCacheDTO> activityCacheMap = new HashMap<>();
        ActivityModel activityModel = new ActivityModel();
        activityModel.setCouponType("01");
        activityModel.setActivityBegin("20210428135655");
        activityModel.setShowFlag(1);
        activityModel.setActivityCode("1");
        activityModel.setUserLimitMax(2);
        TemplateModel template = new TemplateModel();
        template.setTemplateCode("02");
        ActivityCacheDTO dto = new ActivityCacheDTO();
        dto.setActivityModel(activityModel);
        dto.setPromoTemplate(template);
        dto.setPromoFuncParams(new ArrayList<>());
        activityCacheMap.put("1", dto);
        List<CouponReleaseDomain> releaseDomains = new ArrayList<>();
        CouponReleaseDomain domain = new CouponReleaseDomain();
        domain.setActivityCode("1");
        domain.setReceiveStartTime("20210428135655");
        domain.setReceiveEndTime("20220428135655");
        domain.setInventory(1);
        domain.setReleaseStatus("02");
        releaseDomains.add(domain);
        List<TPromoActivityStoreVO> storeVOs = new ArrayList<>();
        TPromoActivityStoreVO tPromoActivityStoreVO = new TPromoActivityStoreVO();
        tPromoActivityStoreVO.setOrgCode("01");
        tPromoActivityStoreVO.setChannelCode("1");
        tPromoActivityStoreVO.setChannelName("1");
        tPromoActivityStoreVO.setStoreName("1");
        tPromoActivityStoreVO.setUrl("1");
        storeVOs.add(tPromoActivityStoreVO);


        when(activityCacheDomain.getActivityCacheMap(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(activityCacheMap);
		when(couponReleaseService.queryReleasesByActivityCodes(Mockito.any(), Mockito.any(), Mockito.anyBoolean())).thenReturn(releaseDomains);
        when(tPromoActivityStoreService.getStoresByActivityCode(Mockito.any())).thenReturn(storeVOs);
        when(redisService.getCouponUserCount111(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(2);

        PageInfo<StoreCouponActivityOutDTO> pageInfo = couponActivityComponent.queryCouponActivityInStore(store);
        Assert.assertEquals(1, pageInfo.getTotal());
    }

    @Test
    public void queryCouponActivityByMemberTag() {
        MemberTagCouponActivityInDTO memberTagInDTO = new MemberTagCouponActivityInDTO();
        ActivityCacheDTO activityCacheDTO = new ActivityCacheDTO();
        ActivityModel activityModel = new ActivityModel();
        activityModel.setCouponType(CouponTypeEnum.PROMOTION_COUPON.code());
        activityModel.setActivityBegin("20211212110403");
        activityModel.setActivityCode("test1111");
        activityCacheDTO.setActivityModel(activityModel);
        Map<String, ActivityCacheDTO> activityMap = new HashMap<>();
        activityMap.put("test", activityCacheDTO);
        when(activityCacheDomain.getActivityCacheMap(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(activityMap);
        List<QualificationModel> qualificationModelList = new ArrayList<>();
        when(qualificationService.queryQualificationsByMemberTags(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(qualificationModelList);
        couponActivityComponent.queryCouponActivityByMemberTag(memberTagInDTO);

        QualificationModel qualificationModel = new QualificationModel();
        qualificationModel.setActivityCode("test1111");
        qualificationModelList.add(qualificationModel);
        when(qualificationService.queryQualificationsByMemberTags(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(qualificationModelList);
        couponActivityComponent.queryCouponActivityByMemberTag(memberTagInDTO);
    }


    @Test
    public void findReleaseByCouponCode() {
        CouponReleaseListInDTO dto = new CouponReleaseListInDTO();
        dto.setTenantCode("testTenant");
        dto.setCouponCodeList(Arrays.asList("coupon1", "coupon2"));

        TPromoCouponInnerCodeVO vo = new TPromoCouponInnerCodeVO();
        vo.setReleaseCode("release1");
        when(couponInnerCodeService.getCouponInnerCodeByCodes("testTenant", Arrays.asList("coupon1", "coupon2")))
                .thenReturn(Arrays.asList(vo));

        CouponReleaseDomain domain = new CouponReleaseDomain();
        when(couponReleaseService.queryReleaseByCondition("testTenant", null, Arrays.asList("release1")))
                .thenReturn(Arrays.asList(domain));

        List<CouponReleaseModel> result = couponActivityComponent.findReleaseByCouponCode(dto);
    }

    @Test
    public void findReleaseByCouponCode_NoCouponCode() {
        CouponReleaseListInDTO dto = new CouponReleaseListInDTO();
        dto.setTenantCode("testTenant");
        dto.setCouponCodeList(new ArrayList<>());

        List<CouponReleaseModel> result = couponActivityComponent.findReleaseByCouponCode(dto);
        Assert.assertTrue(result.isEmpty());
    }

    @Test
    public void findReleaseByCouponCode_NoCouponFromInnerCodeService() {
        CouponReleaseListInDTO dto = new CouponReleaseListInDTO();
        dto.setTenantCode("testTenant");
        dto.setCouponCodeList(Arrays.asList("coupon1", "coupon2"));

        when(couponInnerCodeService.getCouponInnerCodeByCodes("testTenant", Arrays.asList("coupon1", "coupon2")))
                .thenReturn(new ArrayList<>());

        List<CouponReleaseModel> result = couponActivityComponent.findReleaseByCouponCode(dto);
        Assert.assertTrue(result.isEmpty());
    }

    @Test
    public void findReleaseByCouponCode_NoReleaseFromReleaseService() {
        CouponReleaseListInDTO dto = new CouponReleaseListInDTO();
        dto.setTenantCode("testTenant");
        dto.setCouponCodeList(Arrays.asList("coupon1", "coupon2"));

        TPromoCouponInnerCodeVO vo = new TPromoCouponInnerCodeVO();
        vo.setReleaseCode("release1");
        when(couponInnerCodeService.getCouponInnerCodeByCodes("testTenant", Arrays.asList("coupon1", "coupon2")))
                .thenReturn(Arrays.asList(vo));

        when(couponReleaseService.queryReleaseByCondition("testTenant", null, Arrays.asList("release1")))
                .thenReturn(new ArrayList<>());

        List<CouponReleaseModel> result = couponActivityComponent.findReleaseByCouponCode(dto);
        Assert.assertTrue(result.isEmpty());
    }


}
