package com.gtech.promotion.component.coupon;

import com.gtech.promotion.code.coupon.CouponFrozenStatusEnum;
import com.gtech.promotion.code.coupon.CouponStatusEnum;
import com.gtech.promotion.code.coupon.CouponTemplateEnum;
import com.gtech.promotion.dao.model.activity.ActivityModel;
import com.gtech.promotion.dao.model.coupon.TPromoCouponCodeUserVO;
import com.gtech.promotion.dao.model.coupon.TPromoCouponInnerCodeVO;
import com.gtech.promotion.domain.coupon.CouponReleaseDomain;
import com.gtech.promotion.dto.cache.ActivityCacheDTO;
import com.gtech.promotion.feign.IdmFeignClient;
import com.gtech.promotion.feign.MemberFeignClient;
import com.gtech.promotion.helper.ActivityRedisHelpler;
import com.gtech.promotion.service.activity.ActivityService;
import com.gtech.promotion.service.coupon.PromoCouponCodeUserService;
import com.gtech.promotion.service.impl.coupon.PromoCouponActivityServiceImpl;
import com.gtech.promotion.service.impl.coupon.PromoCouponInnerCodeServiceImpl;
import com.gtech.promotion.service.impl.coupon.PromoCouponReleaseServiceImpl;
import com.gtech.promotion.utils.Constants;
import com.gtech.promotion.utils.DateUtil;
import com.gtech.promotion.utils.EasyCacheUtil;
import org.apache.commons.lang.time.DateUtils;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.*;

import static org.mockito.ArgumentMatchers.any;

@RunWith(MockitoJUnitRunner.class)
public class IsCanUseTest {
    @InjectMocks
    private CouponCodeUserComponent couponCodeUserComponent;
    @Mock
    private PromoCouponInnerCodeServiceImpl couponInnerCodeService;
    @Mock
    private PromoCouponActivityServiceImpl couponActivityService;
    @Mock
    private ActivityRedisHelpler redisService;
    @Mock
    private PromoCouponReleaseServiceImpl promoCouponReleaseService;
    @Mock
    private PromoCouponCodeUserService couponCodeUserService;
    @Mock
    private MemberFeignClient memberFeignClient;
    @Mock
    private ActivityService activityService;
    @Mock
    private IdmFeignClient idmFeignClient;



    @Test
    public void isCanUse_优惠券_是否领取_1_内存(){


        String couponCodeUserVoKey = "isCanUse_优惠券_是否领取_1_内存:tenantCode:1" + ":" + "1" + ":" + "couponCodeUserVOs";
        List<TPromoCouponCodeUserVO> list = new ArrayList<>();
        TPromoCouponCodeUserVO tPromoCouponCodeUserVO = new TPromoCouponCodeUserVO();
        tPromoCouponCodeUserVO.setCouponCode("1");
        tPromoCouponCodeUserVO.setActivityCode("1");
        tPromoCouponCodeUserVO.setCouponType(CouponTemplateEnum.COUPON.code());
        tPromoCouponCodeUserVO.setStatus(CouponStatusEnum.GRANTED.code());
        tPromoCouponCodeUserVO.setFrozenStatus(CouponFrozenStatusEnum.UN_FROZEN.code());
        tPromoCouponCodeUserVO.setTenantCode("1");
        tPromoCouponCodeUserVO.setUserCode("1");
        tPromoCouponCodeUserVO.setValidStartTime("20210425000000");
        Date date = DateUtils.addDays(new Date(), 1);
        String dateString = DateUtil.getDateString(date, DateUtil.DATETIMESTOREFORMAT);
        tPromoCouponCodeUserVO.setValidEndTime(dateString);
        tPromoCouponCodeUserVO.setUsedRefId(Constants.DEFAULT_USED_REF_ID);
        tPromoCouponCodeUserVO.setActivityCode("111");
        list.add(tPromoCouponCodeUserVO);
        EasyCacheUtil.set(couponCodeUserVoKey,list,2);

        // given
        String tenantCode = "isCanUse_优惠券_是否领取_1_内存:tenantCode:1";
        String couponCode = "1";
        String userCode = "1";
        Map<String, ActivityCacheDTO> activityCacheMap = new HashMap<>();
        ActivityCacheDTO activityCacheDTO = new ActivityCacheDTO();
        ActivityModel activityModel = new ActivityModel();
        activityModel.setTotalQuantity(10);
        activityModel.setActivityCode("111");
        activityCacheDTO.setActivityModel(activityModel);
        activityCacheMap.put("111",activityCacheDTO);
        // when
        TPromoCouponInnerCodeVO couponInnerCode = new TPromoCouponInnerCodeVO();
        couponInnerCode.setStatus(CouponStatusEnum.GRANTED.code());
        couponInnerCode.setCouponType(CouponTemplateEnum.COUPON.code());
        couponInnerCode.setCouponCode("1");
        ActivityModel couponActivity = new ActivityModel();
        CouponReleaseDomain couponReleaseById = new CouponReleaseDomain();
        couponReleaseById.setValidDays(1);
        couponReleaseById.setReceiveStartTime("1");
        couponActivity.setUserLimitMax(0);
        couponInnerCode.setActivityCode("111");
        Mockito.when(couponInnerCodeService.findCouponByCouponCodeOrPassword(Mockito.anyString(), Mockito.anyString())).thenReturn(couponInnerCode);
//        Mockito.when(couponActivityService.findCouponActivity(Mockito.anyString(), any())).thenReturn(couponActivity);
//        Mockito.when(promoCouponReleaseService.findCouponReleaseByReleaseCode(Mockito.any(), Mockito.any())).thenReturn(couponReleaseById);

        TPromoCouponCodeUserVO userVO = new TPromoCouponCodeUserVO();
        userVO.setStatus(CouponStatusEnum.GRANTED.code());
        userVO.setUserCode("12112");

        userVO.setValidStartTime("20210425000000");
        userVO.setValidEndTime(dateString);
        userVO.setUsedRefId(Constants.DEFAULT_USED_REF_ID);
        userVO.setActivityCode("111");
        //Mockito.when(couponCodeUserService.getUserCouponInfo(any(), any(), any())).thenReturn(userVO);
        // then
        String canUse = couponCodeUserComponent.isCanUse(tenantCode, couponCode, userCode, activityCacheMap, true);
        Assert.assertNotNull("111",canUse);
    }


    @Test
    public void isCanUse_优惠券_是否领取_1(){
        // given
        String tenantCode = "isCanUse_优惠券_是否领取_1:tenantCode:1";
        String couponCode = "1";
        String userCode = "1";
        Map<String, ActivityCacheDTO> activityCacheMap = new HashMap<>();
        ActivityCacheDTO activityCacheDTO = new ActivityCacheDTO();
        ActivityModel activityModel = new ActivityModel();
        activityModel.setTotalQuantity(10);
        activityModel.setActivityCode("111");
        activityCacheDTO.setActivityModel(activityModel);
        activityCacheMap.put("111",activityCacheDTO);
        // when
        TPromoCouponInnerCodeVO couponInnerCode = new TPromoCouponInnerCodeVO();
        couponInnerCode.setStatus(CouponStatusEnum.GRANTED.code());
        couponInnerCode.setCouponType(CouponTemplateEnum.COUPON.code());
        couponInnerCode.setActivityCode("111");
        ActivityModel couponActivity = new ActivityModel();
        CouponReleaseDomain couponReleaseById = new CouponReleaseDomain();
        couponReleaseById.setValidDays(1);
        couponReleaseById.setReceiveStartTime("1");
        couponActivity.setUserLimitMax(0);
        Mockito.when(couponInnerCodeService.findCouponByCouponCodeOrPassword(Mockito.anyString(), Mockito.anyString())).thenReturn(couponInnerCode);
//        Mockito.when(couponActivityService.findCouponActivity(Mockito.anyString(), any())).thenReturn(couponActivity);
//        Mockito.when(promoCouponReleaseService.findCouponReleaseByReleaseCode(Mockito.any(), Mockito.any())).thenReturn(couponReleaseById);

        TPromoCouponCodeUserVO userVO = new TPromoCouponCodeUserVO();
        userVO.setStatus(CouponStatusEnum.GRANTED.code());
        userVO.setUserCode("12112");
        Date date = DateUtils.addDays(new Date(), 1);
        String dateString = DateUtil.getDateString(date, DateUtil.DATETIMESTOREFORMAT);
        userVO.setValidStartTime("20210425000000");
        userVO.setValidEndTime(dateString);
        userVO.setUsedRefId(Constants.DEFAULT_USED_REF_ID);
        userVO.setActivityCode("111");
        Mockito.when(couponCodeUserService.getUserCouponInfo(any(), any(), any())).thenReturn(userVO);
        // then
        String canUse = couponCodeUserComponent.isCanUse(tenantCode, couponCode, userCode, activityCacheMap, false);
        Assert.assertNotNull("111",canUse);
    }

    @Test
    public void isCanUse_优惠券_是否领取_2(){
        // given
        String tenantCode = "isCanUse_优惠券_是否领取_2:tenantCode:1";
        String couponCode = "1";
        String userCode = "1";
        Map<String, ActivityCacheDTO> activityCacheMap = new HashMap<>();
        ActivityCacheDTO activityCacheDTO = new ActivityCacheDTO();
        ActivityModel activityModel = new ActivityModel();
        activityModel.setTotalQuantity(10);
        activityModel.setActivityCode("111");
        activityCacheDTO.setActivityModel(activityModel);
        activityCacheMap.put("111",activityCacheDTO);
        // when
        TPromoCouponInnerCodeVO couponInnerCode = new TPromoCouponInnerCodeVO();
        couponInnerCode.setStatus(CouponStatusEnum.GRANTED.code());
        couponInnerCode.setCouponType(CouponTemplateEnum.COUPON.code());
        couponInnerCode.setActivityCode("111");
        ActivityModel couponActivity = new ActivityModel();
        CouponReleaseDomain couponReleaseById = new CouponReleaseDomain();
        couponReleaseById.setValidDays(1);
        couponReleaseById.setReceiveStartTime("1");
        couponActivity.setUserLimitMax(0);
        Mockito.when(couponInnerCodeService.findCouponByCouponCodeOrPassword(Mockito.anyString(), Mockito.anyString())).thenReturn(couponInnerCode);
//        Mockito.when(couponActivityService.findCouponActivity(Mockito.anyString(), any())).thenReturn(couponActivity);

//        Mockito.when(promoCouponReleaseService.findCouponReleaseByReleaseCode(Mockito.any(), Mockito.any())).thenReturn(couponReleaseById);
        TPromoCouponCodeUserVO userVO = new TPromoCouponCodeUserVO();
        userVO.setStatus(CouponStatusEnum.GRANTED.code());
        userVO.setUserCode("12112");
        Date date = DateUtils.addDays(new Date(), 1);
        String dateString = DateUtil.getDateString(date, DateUtil.DATETIMESTOREFORMAT);
        userVO.setValidStartTime("20210425000000");
        userVO.setValidEndTime(dateString);
        userVO.setUsedRefId("121312");
        userVO.setActivityCode("111");
        Mockito.when(couponCodeUserService.getUserCouponInfo(any(), any(), any())).thenReturn(userVO);
        // then
        String canUse = couponCodeUserComponent.isCanUse(tenantCode, couponCode, userCode, activityCacheMap, false);
        Assert.assertNotNull("0",canUse);
    }


    @Test
    public void isCanUse_优惠码(){
        // given
        String tenantCode = "isCanUse_优惠码:tenantCode:1";
        String couponCode = "1";
        String userCode = "1";
        Map<String, ActivityCacheDTO> activityCacheMap = new HashMap<>();
        ActivityCacheDTO activityCacheDTO = new ActivityCacheDTO();
        ActivityModel activityModel = new ActivityModel();
        activityModel.setTotalQuantity(10);
        activityModel.setActivityCode("111");
        activityCacheDTO.setActivityModel(activityModel);
        activityCacheMap.put("111",activityCacheDTO);
        // when
        TPromoCouponInnerCodeVO couponInnerCode = new TPromoCouponInnerCodeVO();
        couponInnerCode.setStatus(CouponStatusEnum.GRANTED.code());
        couponInnerCode.setActivityCode("111");
        ActivityModel couponActivity = new ActivityModel();
        CouponReleaseDomain couponReleaseById = new CouponReleaseDomain();
        couponReleaseById.setValidDays(1);
        couponReleaseById.setReceiveStartTime("1");
        couponActivity.setUserLimitMax(0);
        Mockito.when(couponInnerCodeService.findCouponByCouponCodeOrPassword(Mockito.anyString(), Mockito.anyString())).thenReturn(couponInnerCode);
//        Mockito.when(couponActivityService.findCouponActivity(Mockito.anyString(), any())).thenReturn(couponActivity);
        Mockito.when(redisService.getCouponReleaseLimit(Mockito.anyString(), any(), any())).thenReturn(0);
        Mockito.when(promoCouponReleaseService.findCouponReleaseByReleaseCode(any(), any())).thenReturn(couponReleaseById);
        // then
        String canUse = couponCodeUserComponent.isCanUse(tenantCode, couponCode, userCode, activityCacheMap, false);
        Assert.assertEquals("111",canUse);
    }


}
