package com.gtech.promotion.component.coupon;

import com.gtech.promotion.dao.entity.point.PointTransactionEntity;
import com.gtech.promotion.dao.mapper.point.PointAccountMapper;
import com.gtech.promotion.dao.mapper.point.PointTransactionMapper;
import com.gtech.promotion.service.point.PointCampaignService;
import com.gtech.promotion.service.point.PointTransactionService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021-09-18 10:30
 */
@RunWith(MockitoJUnitRunner.class)
public class RegularDeductionOfExpiredPointsComponentMockTest {
    @InjectMocks
    private RegularDeductionOfExpiredPointsComponent pointsComponent;
    @Mock
    private PointCampaignService pointCampaignService;
    @Mock
    private PointTransactionMapper pointTransactionMapper;
    @Mock
    private PointAccountMapper pointAccountMapper;
    @Mock
    private PointTransactionService pointTransactionService;

    @Test
    public void regularDeductionOfExpiredPoints(){
        pointsComponent.regularDeductionOfExpiredPoints();
    }

    @Test(expected = Exception.class)
    public void regularDeductionOfExpiredPoints2(){
        List<PointTransactionEntity> pointTransactionEntities = new ArrayList<>();
        PointTransactionEntity entity = new PointTransactionEntity();
        entity.setId(1L);
        pointTransactionEntities.add(entity);
        Mockito.when(pointTransactionService.queryPointTransactionEndTime(Mockito.anyLong(),Mockito.anyInt(),Mockito.any())).thenReturn(pointTransactionEntities);
        Mockito.when(pointAccountMapper.updatePoint(Mockito.any())).thenThrow(NullPointerException.class);
        pointsComponent.regularDeductionOfExpiredPoints();
    }
}
