package com.gtech.promotion.feign.response;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class MemberPurchaseStatisticsRespTest {
    @InjectMocks
    private MemberPurchaseStatisticsResp memberPurchaseStatisticsResp;
    @Test
    public void getEfficientQty() {
        memberPurchaseStatisticsResp.getEfficientQty();

    }

    @Test
    public void getEfficientAmount() {
        memberPurchaseStatisticsResp.getEfficientAmount();
    }
}