package com.gtech.promotion.feign;

import com.gtech.ecomm.common.result.JsonResult;
import com.gtech.ecomm.order.vo.in.order.OrderCommonIn;
import com.gtech.promotion.exception.PromotionException;
import com.gtech.promotion.feign.request.CustomOrderSkuStatisticsQuery;
import com.gtech.promotion.feign.request.QueryMemberPurchaseRequest;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class OrderClientConsumerTest {

    @InjectMocks
    private OrderClientConsumer orderClientConsumer;
    @Mock
    private OrderFeignClient orderFeignClient;


    @Test
    public void queryMemberPurchaseStatistics() {
        Assert.assertThrows(PromotionException.class, () -> orderClientConsumer.queryMemberPurchaseStatistics(new QueryMemberPurchaseRequest()));
        Mockito.when(orderFeignClient.queryMemberPurchaseStatistics(Mockito.any())).thenReturn(JsonResult.success());
        orderClientConsumer.queryMemberPurchaseStatistics(new QueryMemberPurchaseRequest());
    }

    @Test
    public void queryCustomOrderSkuStatistics() {
        Assert.assertThrows(PromotionException.class, () -> orderClientConsumer.queryCustomOrderSkuStatistics(new CustomOrderSkuStatisticsQuery()));
        Mockito.when(orderFeignClient.queryCustomOrderSkuStatistics(Mockito.any())).thenReturn(JsonResult.success());
        orderClientConsumer.queryCustomOrderSkuStatistics(new CustomOrderSkuStatisticsQuery());
    }
}
