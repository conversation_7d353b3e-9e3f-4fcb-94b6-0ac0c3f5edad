package com.gtech.promotion.feign;

import com.google.common.collect.Lists;
import com.gtech.pim.client.CatalogClient;
import com.gtech.pim.request.CatalogQueryProductRequest;
import com.gtech.pim.response.CatalogProductBaseResponse;
import com.gtech.pim.response.PageResult;
import com.gtech.pim.response.Result;
import com.gtech.promotion.exception.PromotionException;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class CatalogClientConsumerTest {
    @InjectMocks
    private CatalogClientConsumer catalogClientConsumer;
    @Mock
    private CatalogClient catalogClient;
    @Test
    public void searchProduct() {
        Assert.assertThrows(PromotionException.class, () -> catalogClientConsumer.searchProduct(new CatalogQueryProductRequest()));
        Result<PageResult<CatalogProductBaseResponse>> t = new Result<>();
        PageResult<CatalogProductBaseResponse> data = new PageResult<>();
        data.setList(Lists.newArrayList(new CatalogProductBaseResponse()));
        t.setData(data);
        t.setSuccess(true);
        data.setTotal(1000);
        Mockito.when(catalogClient.searchProducts(Mockito.any())).thenReturn(t);
        catalogClientConsumer.searchProduct(new CatalogQueryProductRequest());
    }

    @Test
    public void searchProductAll() {
        Result<PageResult<CatalogProductBaseResponse>> t = new Result<>();
        PageResult<CatalogProductBaseResponse> data = new PageResult<>();
        data.setList(Lists.newArrayList(new CatalogProductBaseResponse()));
        t.setData(data);
        t.setSuccess(true);
        data.setTotal(1000);
        Mockito.when(catalogClient.searchProducts(Mockito.any())).thenReturn(t);
        catalogClientConsumer.searchProductAll(new CatalogQueryProductRequest());


    }
}
