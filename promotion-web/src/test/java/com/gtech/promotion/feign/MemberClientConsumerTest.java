package com.gtech.promotion.feign;

import com.gtech.member.client.MemberClient;
import com.gtech.member.response.Result;
import com.gtech.member.web.vo.param.QueryTagListParam;
import com.gtech.member.web.vo.result.TagListResult;
import com.gtech.promotion.exception.PromotionCalcException;
import com.gtech.promotion.exception.PromotionException;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.List;

@RunWith(MockitoJUnitRunner.class)
public class MemberClientConsumerTest {
    @InjectMocks
    private MemberClientConsumer memberClientConsumer;
    @Mock
    private MemberClient memberClient;
    @Mock
    private MemberFeignClient memberFeignClient;

    @Test
    public void queryListByMemberCode() {
        Assert.assertThrows(PromotionException.class, () -> memberClientConsumer.queryListByMemberCode(new QueryTagListParam()));
        Mockito.when(memberClient.queryListByMemberCode(Mockito.any())).thenReturn(Result.ok());
        memberClientConsumer.queryListByMemberCode(new QueryTagListParam());
    }


}
