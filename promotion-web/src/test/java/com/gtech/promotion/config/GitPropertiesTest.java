package com.gtech.promotion.config;

import com.gtech.promotion.dto.config.GitProperties;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class GitPropertiesTest {

    @Test
    public void createGitProperties(){
        GitProperties gitProperties = new GitProperties();
        gitProperties.setBranch("test");
        gitProperties.getBranch();
        gitProperties.setBuildTime("1");
        gitProperties.getBuildTime();
        gitProperties.setBuildVersion("v");
        gitProperties.getBuildVersion();
        gitProperties.setCommitIdAbbrev("12");
        gitProperties.getCommitIdAbbrev();
        gitProperties.setCommitIdFull("d");
        gitProperties.getCommitIdFull();
        gitProperties.setCommitMessage("d");
        gitProperties.getCommitMessage();
        gitProperties.setCommitTime("t");
        gitProperties.getCommitTime();
        gitProperties.setCommitUserEmail("e");
        gitProperties.getCommitUserEmail();
        gitProperties.setCommitUserName("n");
        gitProperties.getCommitUserName();
    }
}
