package com.gtech.promotion.util;

import com.gtech.promotion.utils.DateUtil;
import org.junit.Assert;
import org.junit.Test;

import java.util.Calendar;
import java.util.Date;

/**
 * <AUTHOR>
 * @since 2021/8/16 10:29
 */
public class DateUtilTest {

    @Test
    public void getBetweenDays(){
        long betweenDays = DateUtil.getBetweenDays((Date) null, (Date) null);
        Assert.assertEquals(-1,betweenDays);
        long betweenDay = DateUtil.getBetweenDays((String) null, (String) null);
        Assert.assertEquals(-1,betweenDay);
    }

    @Test
    public void getAfterDate(){
        String yyyyMMdd = DateUtil.getAfterDate(new Date(), 1, "yyyyMMdd");
        Assert.assertNotNull(yyyyMMdd);
        String yyyyMMdd1 = DateUtil.getAfterDate("20210816", 1, "yyyyMMdd");
        Assert.assertEquals("20210817",yyyyMMdd1);
    }

    @Test
    public void getAfterMonth(){
        String yyyyMM = DateUtil.getAfterMonth(new Date(), 1, "yyyyMM");
        Assert.assertNotNull(yyyyMM);
        String yyyyMM1 = DateUtil.getAfterMonth("202108", 1, "yyyyMM");
        Assert.assertEquals("202109",yyyyMM1);
    }

    @Test
    public void getAfterHour(){
        String yyyyMM1 = DateUtil.getAfterHour("2021081610", 1, "yyyyMMddHH");
        Assert.assertNotNull(yyyyMM1);
    }

    @Test
    public void getEndDate(){
        String yyyyMMdd = DateUtil.getEndDate("2021-08-16", 1, 1, "yyyy-MM-dd");
        Assert.assertEquals("2021-09-17",yyyyMMdd);
        String yyyyMMdd1 = DateUtil.getEndDate(new Date(), 1, 1, "yyyy-MM-dd");
        Assert.assertNotNull(yyyyMMdd1);
    }

    @Test
    public void getDateToString(){
        Date yyyyMMdd = DateUtil.getDateToString(null, "yyyyMMdd");
        Assert.assertNull(yyyyMMdd);
        Date yyyyMMdd1 = DateUtil.getDateToString("20210816", "yyyyMMdd");
        Assert.assertNotNull(yyyyMMdd1);
        Date yyyyMMdd2 = DateUtil.getDateToString("1", "yyyyMMdd");
        Assert.assertNull(yyyyMMdd2);
    }

    @Test
    public void getDateString(){
        String yyyyMMdd = DateUtil.getDateString((String) null, "yyyyMMdd");
        Assert.assertNotNull(yyyyMMdd);
        String yyyyMMdd1 = DateUtil.getDateString("20210816", "yyyyMMdd");
        Assert.assertNotNull(yyyyMMdd1);
        String yyyyMMdd2 = DateUtil.getDateString("2021-08-16", "yyyy-MM-dd");
        Assert.assertNotNull(yyyyMMdd2);
        String yyyyMMdd3 = DateUtil.getDateString(2021,8,16,"yyyyMMdd");
        Assert.assertNotNull(yyyyMMdd3);
        String yyyyMMdd4 = DateUtil.getDateString("2021","8","16","yyyyMMdd");
        Assert.assertNotNull(yyyyMMdd4);
    }

    @Test
    public void dateToDate(){
        Date yyyyMMdd = DateUtil.dateToDate(new Date(), "yyyyMMdd");
        Assert.assertNotNull(yyyyMMdd);
        Date yyyyMMdd1 = DateUtil.dateToDate(null, "yyyyMMdd");
        Assert.assertNull(yyyyMMdd1);
    }

    @Test
    public void getDateOfString(){
        String yyyyMMdd = DateUtil.getDateOfString(System.currentTimeMillis(), "yyyyMMdd");
        Assert.assertNotNull(yyyyMMdd);
        String yyyyMMdd1 = DateUtil.getDateOfString( null, "yyyyMMdd");
        Assert.assertNotNull(yyyyMMdd1);
    }

    @Test
    public void getSqlDate(){
        java.sql.Date sqlDate = DateUtil.getSqlDate(null);
        Assert.assertNull(sqlDate);
        java.sql.Date yyyyMMdd1 = DateUtil.getSqlDate( "20210816");
        Assert.assertNull(yyyyMMdd1);
    }

    @Test
    public void utilDateToSQLDate(){
        java.sql.Date date = DateUtil.utilDateToSQLDate(new Date());
        Assert.assertNotNull(date);
    }

    @Test
    public void getCalendar(){
        Calendar calendar = DateUtil.getCalendar(new Date());
        Assert.assertNotNull(calendar);
    }

    @Test
    public void parseDateTime(){
        String s = DateUtil.parseDateTime(new Date());
        Assert.assertNotNull(s);
        String s1 = DateUtil.parseDateTime(null);
        Assert.assertNotNull(s1);
    }

    @Test
    public void parsTime(){
        String s = DateUtil.parsTime(new Date());
        Assert.assertNotNull(s);
        String s1 = DateUtil.parsTime(null);
        Assert.assertNotNull(s1);
    }

    @Test
    public void parseDate(){
        String s = DateUtil.parseDate(new Date());
        Assert.assertNotNull(s);
        String s1 = DateUtil.parseDate(null);
        Assert.assertNotNull(s1);
    }

    @Test
    public void firstDate(){
        String s = DateUtil.firstDate();
        Assert.assertNotNull(s);
    }

    @Test
    public void lastDate(){
        String s = DateUtil.lastDate();
        Assert.assertNotNull(s);
    }

    @Test
    public void getUpMouth(){
        Date upMouth = DateUtil.getUpMouth(new Date());
        Assert.assertNotNull(upMouth);
        Date upMouth1 = DateUtil.getUpMouth("2021-08-16");
        Assert.assertNotNull(upMouth1);
    }

    @Test
    public void getMonth(){
        int month = DateUtil.getMonth(new Date());
        int month1 = DateUtil.getMonth("2021-08-16");
    }

    @Test
    public void getDay(){
        int month = DateUtil.getDay(new Date());
        int month1 = DateUtil.getDay("2021-08-16");
    }

    @Test
    public void getWeek(){
        int month = DateUtil.getWeek(new Date());
        int month1 = DateUtil.getWeek("2021-08-16");
    }

    @Test
    public void getYear(){
        int month = DateUtil.getYear("2021-08-16");
    }

    @Test
    public void getHour(){
        int day = DateUtil.getHour(new Date());
        int min = DateUtil.getMin(new Date());
    }

    @Test
    public void checkMax(){
        boolean b = DateUtil.checkMax(new Date(), new Date());
        Assert.assertTrue(b);
        boolean b1 = DateUtil.checkMax(new Date(), null);
        Assert.assertTrue(b1);
        boolean b2 = DateUtil.checkMax(null, null);
        Assert.assertFalse(b2);
    }

    @Test
    public void isWeekend(){
        boolean b = DateUtil.isWeekend(new Date(1631721600L));
        Assert.assertFalse(b);
    }

    @Test
    public void addMinutes(){
        Date date = DateUtil.addMinutes(null, 1);
        Assert.assertNotNull(date);
    }

    @Test
    public void getDiffDay(){
        Long yyyyMMdd = DateUtil.getDiffDay("20210816", "20210617");
        Assert.assertNotNull(yyyyMMdd);
    }

    @Test
    public void roundDate(){
        Calendar cal = Calendar.getInstance();
        Date date = DateUtil.roundDate(cal, 2);
        Assert.assertNotNull(date);
    }

    @Test
    public void getLastDayOfMonth(){
        String lastDayOfMonth = DateUtil.getLastDayOfMonth(2021, 8);
        Assert.assertNotNull(lastDayOfMonth);
        String firstDayOfMonth = DateUtil.getFirstDayOfMonth(2021, 8);
        Assert.assertNotNull(firstDayOfMonth);
    }

    @Test
    public void differHours(){
        long hours = DateUtil.differHours("20210816115255", "20210617115555");
    }
}
