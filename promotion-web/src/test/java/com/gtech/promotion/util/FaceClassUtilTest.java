package com.gtech.promotion.util;

import com.gtech.promotion.code.activity.FuncTypeEnum;
import com.gtech.promotion.dao.model.activity.FunctionParamModel;
import com.gtech.promotion.utils.FaceClassUtil;
import org.junit.Test;

import java.util.ArrayList;
import java.util.List;

public class FaceClassUtilTest {
    @Test
    public void faceUnitAndFaceValue(){
        String rewardType = "02";
        List<FunctionParamModel> paramList = new ArrayList<>();
        FunctionParamModel functionParamModel = new FunctionParamModel();
        functionParamModel.setFunctionCode(FuncTypeEnum.IncentiveEnum.REDUCE_AMOUNT.code());
        functionParamModel.setFunctionName("test");
        functionParamModel.setParamValue("100");
        paramList.add(functionParamModel);
        FaceClassUtil.faceUnitAndFaceValue(rewardType,paramList);

        functionParamModel.setFunctionCode(FuncTypeEnum.IncentiveEnum.DISCOUNT.code());
        FaceClassUtil.faceUnitAndFaceValue(rewardType,paramList);
    }
}
