package com.gtech.promotion.util;

import com.gtech.promotion.callable.PromotionCallable;
import com.gtech.promotion.callable.ThreadPoolUtil;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.TimeUnit;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class ThreadPoolUtilTest {
    @Mock
    ExecutorService executorService;

    @Test
    public void createScheduleExecutor(){
        ThreadPoolUtil.createScheduleExecutor();
    }

    @Test
    public void schedule(){
        PromotionCallable<String> callable = new PromotionCallable<String>() {
            @Override
            public String call() throws Exception {
                return null;
            }
        };
        try {
            ThreadPoolUtil.schedule(callable, 1L, TimeUnit.MILLISECONDS);
        }catch (Exception e){

        }
    }

    @Test
    public void pushTasks(){
        PromotionCallable<String> callable = new PromotionCallable<String>() {
            @Override
            public String call() throws Exception {
                return null;
            }
        };
        List<PromotionCallable<String>> list = new ArrayList<>();
        list.add(callable);
        try {
            ThreadPoolUtil.pushTasks(list);
        }catch (Exception e){

        }
    }
}
