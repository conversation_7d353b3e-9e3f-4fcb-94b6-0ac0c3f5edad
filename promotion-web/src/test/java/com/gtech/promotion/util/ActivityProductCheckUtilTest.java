package com.gtech.promotion.util;

import com.gtech.promotion.utils.ActivityProductCheckUtil;
import com.gtech.promotion.vo.bean.ProductAttribute;
import com.gtech.promotion.vo.bean.ProductCodes;
import org.junit.Test;

import java.util.ArrayList;
import java.util.List;

public class ActivityProductCheckUtilTest {

    @Test
    public void checkProductTest(){
        ProductCodes product = new ProductCodes();

        List<ProductAttribute> attributes = new ArrayList<>();
        ProductAttribute productAttribute = new ProductAttribute();

        attributes.add(productAttribute);
        product.setAttributes(attributes);
        try {
            ActivityProductCheckUtil.checkProduct(product);
        }catch (Exception e){

        }

        product.setCombineSkuCode("test");
        try {
            ActivityProductCheckUtil.checkProduct(product);
        }catch (Exception e){

        }

    }
}
