package com.gtech.promotion.util;

import com.alibaba.druid.sql.dialect.h2.visitor.H2ASTVisitor;
import com.gtech.promotion.code.activity.LangTypeEnum;
import com.gtech.promotion.dto.out.coupon.ManagementDataOutDTO;
import com.gtech.promotion.utils.ExportCouponUtil;
import org.junit.Test;

import java.io.FileOutputStream;
import java.io.OutputStream;
import java.util.*;

public class ExportCouponUtilTest {

    @Test
    public void exportCouponInfoHeaderTest(){
        try {
            OutputStream outputStream = new FileOutputStream("test");
            ExportCouponUtil.exportCouponInfoHeader(outputStream, LangTypeEnum.HK.code());
        }catch (Exception e){

        }
    }

    @Test
    public void exportManagementCouponInfoContentTest(){
        try {
            Map<String, Map<String, String>> hashMap = new HashMap<>();
            List<ManagementDataOutDTO> list = new ArrayList<>();
            OutputStream outputStream = new FileOutputStream("test");

            ManagementDataOutDTO managementDataOutDTO = new ManagementDataOutDTO();
            managementDataOutDTO.setActivityCode("test");
            managementDataOutDTO.setActivityName("test");
            managementDataOutDTO.setCouponCode("test");

            managementDataOutDTO.setReceiveTime(null);
            managementDataOutDTO.setUsedTime(null);
            managementDataOutDTO.setUserCode(null);
            managementDataOutDTO.setUsedRefId(null);
            managementDataOutDTO.setTakeLabel(null);
            list.add(managementDataOutDTO);

            ExportCouponUtil.exportManagementCouponInfoContent(list,outputStream,hashMap);
        }catch (Exception e){

        }

        try {
            Map<String, Map<String, String>> hashMap = new HashMap<>();
            List<ManagementDataOutDTO> list = new ArrayList<>();
            OutputStream outputStream = new FileOutputStream("test");

            ManagementDataOutDTO managementDataOutDTO = new ManagementDataOutDTO();
            managementDataOutDTO.setActivityCode("test");
            managementDataOutDTO.setActivityName("test");
            managementDataOutDTO.setCouponCode("test");

            managementDataOutDTO.setReceiveTime("20181213140606");
            managementDataOutDTO.setUsedTime("20181213140606");
            managementDataOutDTO.setUserCode("test");
            managementDataOutDTO.setUsedRefId("test");
            managementDataOutDTO.setTakeLabel("test");
            list.add(managementDataOutDTO);

            ExportCouponUtil.exportManagementCouponInfoContent(list,outputStream,hashMap);
        }catch (Exception e){

        }
    }
}
