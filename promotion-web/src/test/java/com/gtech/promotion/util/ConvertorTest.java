package com.gtech.promotion.util;

import com.gtech.promotion.utils.Convertor;
import org.junit.Assert;
import org.junit.Test;

/**
 * <AUTHOR>
 * @since 2021/8/16 13:36
 */
public class ConvertorTest {

    @Test
    public void convert(){
        Convertor<String, Integer> convertor = new Convertor<>(String.class,Integer.class);
        Integer convert = convertor.convert("1", 1);
        Assert.assertEquals(1L,(long) convert);
    }

    @Test
    public void convert1(){
        Convertor<String, Integer> convertor = new Convertor<>(String.class,Integer.class);
        Integer convert = convertor.convert(null, 1);
        Assert.assertNull(convert);
    }
}
