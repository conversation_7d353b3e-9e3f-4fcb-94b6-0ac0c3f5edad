package com.gtech.promotion.util;

import com.gtech.commons.utils.DateUtil;
import com.gtech.promotion.code.activity.ActivityTypeEnum;
import com.gtech.promotion.dao.model.activity.ActivityModel;
import com.gtech.promotion.dao.model.activity.ActivityPeriodModel;
import com.gtech.promotion.dao.model.activity.QualificationModel;
import com.gtech.promotion.dao.model.activity.TPromoActivityStoreVO;
import com.gtech.promotion.dto.cache.ActivityCacheDTO;
import com.gtech.promotion.utils.ActivityFilterUtil;
import org.junit.Assert;
import org.junit.Test;

import java.lang.reflect.Constructor;
import java.lang.reflect.InvocationTargetException;
import java.util.*;

public class ActivityFilterUtilTest {

    @Test
    public void filterActivityByQualifications(){
        List<QualificationModel> activityQualifications = new ArrayList<>();
        QualificationModel qualificationModel = new QualificationModel();
        activityQualifications.add(qualificationModel);
        Map<String, List<String>> qualifications = new HashMap<>();
        qualifications.put("test", Arrays.asList("test"));
        try {
            ActivityFilterUtil.filterActivityByQualifications(activityQualifications,qualifications);
        }catch (Exception e){

        }

    }

    @Test
    public void filterActivityByQualifications_1(){
        List<QualificationModel> activityQualifications = new ArrayList<>();
        QualificationModel qualificationModel = new QualificationModel();

        qualificationModel.setQualificationCode("1");
        activityQualifications.add(qualificationModel);

        Map<String, List<String>> qualifications = new HashMap<>();
        qualifications.put("test", Arrays.asList("test"));

        boolean b = ActivityFilterUtil.filterActivityByQualifications(activityQualifications, qualifications);

        Assert.assertFalse(b);


    }

    @Test
    public void filterActivityByQualifications_2(){
        List<QualificationModel> activityQualifications = new ArrayList<>();
        QualificationModel qualificationModel = new QualificationModel();

        qualificationModel.setQualificationCode("test");
        qualificationModel.setQualificationValue("test");
        activityQualifications.add(qualificationModel);

        Map<String, List<String>> qualifications = new HashMap<>();
        qualifications.put("test", Arrays.asList("test"));

        boolean b = ActivityFilterUtil.filterActivityByQualifications(activityQualifications, qualifications);

        Assert.assertTrue(b);

    }


    @Test
    public void filterActivityByQualifications_empty(){
        Map<String, ActivityCacheDTO> activityMap = new HashMap<>();


        Map<String, List<String>> qualifications = new HashMap<>();
        qualifications.put("test", Arrays.asList("test"));

        Map<String, ActivityCacheDTO> stringActivityCacheDTOMap = ActivityFilterUtil.filterActivityByQualifications(activityMap, qualifications);

        Assert.assertEquals(0,stringActivityCacheDTOMap.values().size());

    }

    @Test
    public void filterActivityByQualifications_not_empty(){
        Map<String, ActivityCacheDTO> activityMap = new HashMap<>();

        ActivityCacheDTO activityCacheDTO = new ActivityCacheDTO();

        List<QualificationModel> activityQualifications = new ArrayList<>();
        QualificationModel qualificationModel = new QualificationModel();

        qualificationModel.setQualificationCode("test");
        qualificationModel.setQualificationValue("test");
        activityQualifications.add(qualificationModel);
        activityCacheDTO.setQualificationModels(activityQualifications);

        activityMap.put("1",activityCacheDTO);

        Map<String, List<String>> qualifications = new HashMap<>();
        qualifications.put("test", Arrays.asList("test"));

        Map<String, ActivityCacheDTO> stringActivityCacheDTOMap = ActivityFilterUtil.filterActivityByQualifications(activityMap, qualifications);

        Assert.assertEquals(1,stringActivityCacheDTOMap.values().size());

    }

    @Test
    public void filterActivityByQualifications_not_empty_1(){
        Map<String, ActivityCacheDTO> activityMap = new HashMap<>();

        ActivityCacheDTO activityCacheDTO = new ActivityCacheDTO();

        List<QualificationModel> activityQualifications = new ArrayList<>();
        QualificationModel qualificationModel = new QualificationModel();

        qualificationModel.setQualificationCode("test1");
        qualificationModel.setQualificationValue("test");
        activityQualifications.add(qualificationModel);
        activityCacheDTO.setQualificationModels(activityQualifications);

        activityMap.put("1",activityCacheDTO);

        Map<String, List<String>> qualifications = new HashMap<>();
        qualifications.put("test", Arrays.asList("test"));

        Map<String, ActivityCacheDTO> stringActivityCacheDTOMap = ActivityFilterUtil.filterActivityByQualifications(activityMap, qualifications);

        Assert.assertEquals(0,stringActivityCacheDTOMap.values().size());

    }

    @Test(expected = InvocationTargetException.class)
    public void activityFilterUtil() throws NoSuchMethodException, IllegalAccessException, InvocationTargetException, InstantiationException {
        //获取对象
        Class<?> c = null;
        try {
            c = Class.forName("com.gtech.promotion.utils.ActivityFilterUtil");
        } catch (ClassNotFoundException e) {

        }
        //获取构造方法
        Constructor<?> con = c.getDeclaredConstructor();
        con.setAccessible(true);
        //使用构造方法创建对象，默认无参构造
        con.newInstance();

    }

    @Test
    public void filterActivityByOrgCodes(){

        Map<String, ActivityCacheDTO> activityMap = new HashMap<>();

        ActivityCacheDTO activityCacheDTO = new ActivityCacheDTO();

        List<QualificationModel> activityQualifications = new ArrayList<>();
        QualificationModel qualificationModel = new QualificationModel();
        qualificationModel.setQualificationCode("test1");
        qualificationModel.setQualificationValue("test");
        activityQualifications.add(qualificationModel);
        activityCacheDTO.setQualificationModels(activityQualifications);

        List<TPromoActivityStoreVO> promoChannels = new ArrayList<>();
        TPromoActivityStoreVO activityStoreVO = new TPromoActivityStoreVO();
        activityStoreVO.setOrgCode("1");
        promoChannels.add(activityStoreVO);
        activityCacheDTO.setPromoChannels(promoChannels);

        activityMap.put("1",activityCacheDTO);

        List<String> orgCodes = new ArrayList<>();
        orgCodes.add("1");
        orgCodes.add("2");
        Map<String, ActivityCacheDTO> stringActivityCacheDTOMap = ActivityFilterUtil.filterActivityByOrgCodes(activityMap, orgCodes);

        Assert.assertNotNull(stringActivityCacheDTOMap);
    }

    @Test
    public void filterActivityByOrgCodes_1(){

        Map<String, ActivityCacheDTO> activityMap = new HashMap<>();

        ActivityCacheDTO activityCacheDTO = new ActivityCacheDTO();

        List<TPromoActivityStoreVO> promoChannels = new ArrayList<>();
        TPromoActivityStoreVO activityStoreVO = new TPromoActivityStoreVO();
        activityStoreVO.setOrgCode("5");
        promoChannels.add(activityStoreVO);
        activityCacheDTO.setPromoChannels(promoChannels);

        activityMap.put("1",activityCacheDTO);

        List<String> orgCodes = new ArrayList<>();
        orgCodes.add("1");
        orgCodes.add("2");
        Map<String, ActivityCacheDTO> stringActivityCacheDTOMap = ActivityFilterUtil.filterActivityByOrgCodes(activityMap, orgCodes);

        Assert.assertNotNull(stringActivityCacheDTOMap);
    }

    @Test
    public void filterActivityByActivityType(){

        Map<String, ActivityCacheDTO> activityMap = new HashMap<>();

        ActivityCacheDTO activityCacheDTO = new ActivityCacheDTO();

        List<TPromoActivityStoreVO> promoChannels = new ArrayList<>();
        TPromoActivityStoreVO activityStoreVO = new TPromoActivityStoreVO();
        activityStoreVO.setOrgCode("5");
        promoChannels.add(activityStoreVO);
        activityCacheDTO.setPromoChannels(promoChannels);
        activityMap.put("1",activityCacheDTO);
        ActivityFilterUtil.filterActivityByActivityType(activityMap,null);
    }

    @Test
    public void filterActivityByActivityType_empty(){

        Map<String, ActivityCacheDTO> activityMap = new HashMap<>();

        Map<String, ActivityCacheDTO> stringActivityCacheDTOMap = ActivityFilterUtil.filterActivityByActivityType(activityMap, null);
        Assert.assertEquals(0,stringActivityCacheDTOMap.values().size());

    }

    @Test
    public void filterActivityByActivityType_1(){

        Map<String, ActivityCacheDTO> activityMap = new HashMap<>();

        ActivityCacheDTO activityCacheDTO = new ActivityCacheDTO();

        List<TPromoActivityStoreVO> promoChannels = new ArrayList<>();
        TPromoActivityStoreVO activityStoreVO = new TPromoActivityStoreVO();
        activityStoreVO.setOrgCode("5");
        promoChannels.add(activityStoreVO);
        activityCacheDTO.setPromoChannels(promoChannels);

        ActivityModel activityModel = new ActivityModel();
        activityModel.setActivityType("02");
        activityCacheDTO.setActivityModel(activityModel);

        activityMap.put("1",activityCacheDTO);
        Map<String, ActivityCacheDTO> stringActivityCacheDTOMap = ActivityFilterUtil.filterActivityByActivityType(activityMap, ActivityTypeEnum.COUPON);
        Assert.assertEquals(1,stringActivityCacheDTOMap.values().size());

    }

    @Test
    public void filterActivityByTimeEmpty(){


        Map<String, ActivityCacheDTO> activityMap = new HashMap<>();

        ActivityCacheDTO activityCacheDTO = new ActivityCacheDTO();

        List<TPromoActivityStoreVO> promoChannels = new ArrayList<>();
        TPromoActivityStoreVO activityStoreVO = new TPromoActivityStoreVO();
        activityStoreVO.setOrgCode("5");
        promoChannels.add(activityStoreVO);
        activityCacheDTO.setPromoChannels(promoChannels);

        ActivityModel activityModel = new ActivityModel();
        activityModel.setActivityType("02");
        activityCacheDTO.setActivityModel(activityModel);


        Map<String, ActivityCacheDTO> map = ActivityFilterUtil.filterActivityByTime(activityMap, "20210817141155");

        Assert.assertEquals(0,map.values().size());

    }

    @Test
    public void filterActivityByTimeEmpty_warm(){
        Map<String, ActivityCacheDTO> activityMap = new HashMap<>();
        Map<String, ActivityCacheDTO> map = ActivityFilterUtil.filterActivityByWarmTime(activityMap, "20210817141155",null);
        Assert.assertEquals(0,map.values().size());

    }

    @Test
    public void filterActivityByTimeEmpty_warm_begin(){
        Map<String, ActivityCacheDTO> activityMap = new HashMap<>();
        Map<String, ActivityCacheDTO> map = ActivityFilterUtil.filterActivityByWarmTime(activityMap, "",null);
        Assert.assertEquals(0,map.values().size());

    }


    @Test
    public void filterActivityByWarmTime(){
        Map<String, ActivityCacheDTO> activityMap = new HashMap<>();
        ActivityModel activityModel =new ActivityModel();
        Calendar instance1 = Calendar.getInstance();
        Date time1 = instance1.getTime();
        String activityStartTime = DateUtil.format(time1, DateUtil.FORMAT_YYYYMMDDHHMISS_14);
        instance1.add(Calendar.DATE,-3);
        activityModel.setActivityBegin(activityStartTime);
        ActivityCacheDTO activityCacheDTO = new ActivityCacheDTO();
        List<TPromoActivityStoreVO> promoChannels = new ArrayList<>();
        TPromoActivityStoreVO activityStoreVO = new TPromoActivityStoreVO();
        activityStoreVO.setOrgCode("5");
        promoChannels.add(activityStoreVO);
        activityCacheDTO.setPromoChannels(promoChannels);
        Calendar instance = Calendar.getInstance();
        Date time = instance.getTime();
        instance.add(Calendar.DATE,2);
        String endTime = DateUtil.format(instance.getTime(), DateUtil.FORMAT_YYYYMMDDHHMISS_14);
        activityModel.setActivityType("02");
        activityModel.setActivityEnd(endTime);
        activityCacheDTO.setActivityModel(activityModel);

        activityMap.put("1",activityCacheDTO);

        Map<String, ActivityCacheDTO> map = ActivityFilterUtil.filterActivityByWarmTime(activityMap, "",null);
        Assert.assertEquals(0,map.values().size());

    }

    @Test
    public void filterActivityByWarmTime_warmStartTime(){
        Map<String, ActivityCacheDTO> activityMap = new HashMap<>();
        ActivityModel activityModel =new ActivityModel();
        Calendar instance1 = Calendar.getInstance();
        Date time1 = instance1.getTime();
        String activityStartTime = DateUtil.format(time1, DateUtil.FORMAT_YYYYMMDDHHMISS_14);
        instance1.add(Calendar.DATE,-3);
        activityModel.setActivityBegin(activityStartTime);
        ActivityCacheDTO activityCacheDTO = new ActivityCacheDTO();
        List<TPromoActivityStoreVO> promoChannels = new ArrayList<>();
        TPromoActivityStoreVO activityStoreVO = new TPromoActivityStoreVO();
        activityStoreVO.setOrgCode("5");
        promoChannels.add(activityStoreVO);
        activityCacheDTO.setPromoChannels(promoChannels);
        Calendar instance = Calendar.getInstance();
        instance.add(Calendar.DATE,2);
        String endTime = DateUtil.format(instance.getTime(), DateUtil.FORMAT_YYYYMMDDHHMISS_14);
        activityModel.setActivityType("02");
        activityModel.setActivityEnd(endTime);
        activityModel.setWarmBegin(activityStartTime);
        activityCacheDTO.setActivityModel(activityModel);

        activityMap.put("1",activityCacheDTO);

        Map<String, ActivityCacheDTO> map = ActivityFilterUtil.filterActivityByWarmTime(activityMap, "",activityStartTime);
        Assert.assertEquals(1,map.values().size());

    }

    @Test
    public void filterActivityByWarmTime_warmEndTime(){
        Map<String, ActivityCacheDTO> activityMap = new HashMap<>();
        ActivityModel activityModel =new ActivityModel();
        Calendar instance1 = Calendar.getInstance();
        Date time1 = instance1.getTime();
        String activityStartTime = DateUtil.format(time1, DateUtil.FORMAT_YYYYMMDDHHMISS_14);
        instance1.add(Calendar.DATE,-3);
        activityModel.setActivityBegin(activityStartTime);
        ActivityCacheDTO activityCacheDTO = new ActivityCacheDTO();
        List<TPromoActivityStoreVO> promoChannels = new ArrayList<>();
        TPromoActivityStoreVO activityStoreVO = new TPromoActivityStoreVO();
        activityStoreVO.setOrgCode("5");
        promoChannels.add(activityStoreVO);
        activityCacheDTO.setPromoChannels(promoChannels);
        Calendar instance = Calendar.getInstance();
        instance.add(Calendar.DATE,2);
        String endTime = DateUtil.format(instance.getTime(), DateUtil.FORMAT_YYYYMMDDHHMISS_14);
        activityModel.setActivityType("02");
        activityModel.setActivityEnd(endTime);
        activityModel.setWarmBegin(activityStartTime);
        activityCacheDTO.setActivityModel(activityModel);
        activityMap.put("1",activityCacheDTO);
        Map<String, ActivityCacheDTO> map = ActivityFilterUtil.filterActivityByWarmTime(activityMap, "",activityStartTime);
        Assert.assertEquals(1,map.values().size());

    }

    @Test
    public void filterActivityByWarmTime_warmTime(){
        Map<String, ActivityCacheDTO> activityMap = new HashMap<>();
        ActivityModel activityModel =new ActivityModel();
        Calendar instance1 = Calendar.getInstance();
        Date time1 = instance1.getTime();
        String activityStartTime = DateUtil.format(time1, DateUtil.FORMAT_YYYYMMDDHHMISS_14);
        instance1.add(Calendar.DATE,-3);
        activityModel.setActivityBegin(activityStartTime);
        ActivityCacheDTO activityCacheDTO = new ActivityCacheDTO();
        List<TPromoActivityStoreVO> promoChannels = new ArrayList<>();
        TPromoActivityStoreVO activityStoreVO = new TPromoActivityStoreVO();
        activityStoreVO.setOrgCode("5");
        promoChannels.add(activityStoreVO);
        activityCacheDTO.setPromoChannels(promoChannels);
        Calendar instance = Calendar.getInstance();
        instance.add(Calendar.DATE,2);
        String endTime = DateUtil.format(instance.getTime(), DateUtil.FORMAT_YYYYMMDDHHMISS_14);
        activityModel.setActivityType("02");
        activityModel.setActivityEnd(endTime);
        activityModel.setWarmBegin(activityStartTime);
        activityCacheDTO.setActivityModel(activityModel);
        activityMap.put("1",activityCacheDTO);
        Map<String, ActivityCacheDTO> map = ActivityFilterUtil.filterActivityByWarmTime(activityMap, "",activityStartTime);
        Assert.assertEquals(1,map.values().size());

    }

    @Test
    public void filterActivityByWarmTime_warmTime_empty(){
        Map<String, ActivityCacheDTO> activityMap = new HashMap<>();
        ActivityModel activityModel =new ActivityModel();
        Calendar instance1 = Calendar.getInstance();
        Date time1 = instance1.getTime();
        String activityStartTime = DateUtil.format(time1, DateUtil.FORMAT_YYYYMMDDHHMISS_14);
        instance1.add(Calendar.DATE,-3);
        activityModel.setActivityBegin(activityStartTime);
        ActivityCacheDTO activityCacheDTO = new ActivityCacheDTO();
        List<TPromoActivityStoreVO> promoChannels = new ArrayList<>();
        TPromoActivityStoreVO activityStoreVO = new TPromoActivityStoreVO();
        activityStoreVO.setOrgCode("5");
        promoChannels.add(activityStoreVO);
        activityCacheDTO.setPromoChannels(promoChannels);
        Calendar instance = Calendar.getInstance();
        instance.add(Calendar.DATE,2);
        String endTime = DateUtil.format(instance.getTime(), DateUtil.FORMAT_YYYYMMDDHHMISS_14);
        activityModel.setActivityType("02");
        activityModel.setActivityEnd(endTime);
        activityModel.setWarmBegin(activityStartTime);
        activityCacheDTO.setActivityModel(activityModel);
        activityMap.put("1",activityCacheDTO);
        Map<String, ActivityCacheDTO> map = ActivityFilterUtil.filterActivityByWarmTime(activityMap, "","");
        Assert.assertEquals(1,map.values().size());

    }


    @Test
    public void filterActivityByTime_1(){

        Map<String, ActivityCacheDTO> activityMap = new HashMap<>();
        ActivityModel activityModel =new ActivityModel();
        Calendar instance1 = Calendar.getInstance();
        Date time1 = instance1.getTime();
        String activityStartTime = DateUtil.format(time1, DateUtil.FORMAT_YYYYMMDDHHMISS_14);
        instance1.add(Calendar.DATE,3);
        String activityEndTime = DateUtil.format(instance1.getTime(), DateUtil.FORMAT_YYYYMMDDHHMISS_14);
        activityModel.setActivityBegin(activityStartTime);
        activityModel.setActivityEnd(activityEndTime);
        ActivityCacheDTO activityCacheDTO = new ActivityCacheDTO();
        List<TPromoActivityStoreVO> promoChannels = new ArrayList<>();
        TPromoActivityStoreVO activityStoreVO = new TPromoActivityStoreVO();
        activityStoreVO.setOrgCode("5");
        promoChannels.add(activityStoreVO);
        activityCacheDTO.setPromoChannels(promoChannels);

        activityModel.setActivityType("02");
        activityCacheDTO.setActivityModel(activityModel);

        activityMap.put("1",activityCacheDTO);
        Calendar instance = Calendar.getInstance();
        Date time = instance.getTime();

        String startTime = DateUtil.format(time, DateUtil.FORMAT_YYYYMMDDHHMISS_14);

        instance.add(Calendar.DATE,2);
        String endTime = DateUtil.format(instance.getTime(), DateUtil.FORMAT_YYYYMMDDHHMISS_14);

        ActivityFilterUtil.filterActivityByActivityTime(activityMap, startTime,endTime);

    }



    @Test
    public void filterActivityByTime_periodModel(){


        Map<String, ActivityCacheDTO> activityMap = new HashMap<>();
        ActivityModel activityModel =new ActivityModel();

        Calendar instance1 = Calendar.getInstance();
        Date time1 = instance1.getTime();

        String activityStartTime = DateUtil.format(time1, DateUtil.FORMAT_YYYYMMDDHHMISS_14);

        instance1.add(Calendar.DATE,3);

        String activityEndTime = DateUtil.format(instance1.getTime(), DateUtil.FORMAT_YYYYMMDDHHMISS_14);


        activityModel.setActivityBegin(activityStartTime);
        activityModel.setActivityEnd(activityEndTime);

        ActivityCacheDTO activityCacheDTO = new ActivityCacheDTO();

        List<TPromoActivityStoreVO> promoChannels = new ArrayList<>();
        TPromoActivityStoreVO activityStoreVO = new TPromoActivityStoreVO();
        activityStoreVO.setOrgCode("5");
        promoChannels.add(activityStoreVO);
        activityCacheDTO.setPromoChannels(promoChannels);

        activityModel.setActivityType("02");
        activityCacheDTO.setActivityModel(activityModel);


        ActivityPeriodModel periodModel = new ActivityPeriodModel();

        periodModel.setBeginPeriod(activityStartTime);
        periodModel.setEndPeriod(activityEndTime);


        activityCacheDTO.setPeriodModel(periodModel);


        activityMap.put("1",activityCacheDTO);

        Calendar instance = Calendar.getInstance();
        Date time = instance.getTime();

        String startTime = DateUtil.format(time, DateUtil.FORMAT_YYYYMMDDHHMISS_14);

        instance.add(Calendar.DATE,2);

        String endTime = DateUtil.format(instance.getTime(), DateUtil.FORMAT_YYYYMMDDHHMISS_14);


        ActivityFilterUtil.filterActivityByActivityTime(activityMap, startTime,endTime);

    }

    @Test
    public void filterActivityByTimeStartEmpty_period(){


        Map<String, ActivityCacheDTO> activityMap = new HashMap<>();
        ActivityModel activityModel =new ActivityModel();

        Calendar instance1 = Calendar.getInstance();
        Date time1 = instance1.getTime();

        String activityStartTime = DateUtil.format(time1, DateUtil.FORMAT_YYYYMMDDHHMISS_14);

        instance1.add(Calendar.DATE,3);

        String activityEndTime = DateUtil.format(instance1.getTime(), DateUtil.FORMAT_YYYYMMDDHHMISS_14);

        activityModel.setActivityBegin(activityStartTime);
        activityModel.setActivityEnd(activityEndTime);

        ActivityCacheDTO activityCacheDTO = new ActivityCacheDTO();

        List<TPromoActivityStoreVO> promoChannels = new ArrayList<>();
        TPromoActivityStoreVO activityStoreVO = new TPromoActivityStoreVO();
        activityStoreVO.setOrgCode("5");
        promoChannels.add(activityStoreVO);
        activityCacheDTO.setPromoChannels(promoChannels);

        activityModel.setActivityType("02");
        activityCacheDTO.setActivityModel(activityModel);

        ActivityPeriodModel periodModel = new ActivityPeriodModel();

        periodModel.setBeginPeriod(activityStartTime);
        periodModel.setEndPeriod(activityEndTime);


        activityCacheDTO.setPeriodModel(periodModel);

        activityMap.put("1",activityCacheDTO);

        Calendar instance = Calendar.getInstance();
        Date time = instance.getTime();

        instance.add(Calendar.DATE,2);

        String endTime = DateUtil.format(instance.getTime(), DateUtil.FORMAT_YYYYMMDDHHMISS_14);

        ActivityFilterUtil.filterActivityByActivityTime(activityMap, "",endTime);

    }

    @Test
    public void filterActivityByTimeStartEmpty(){


        Map<String, ActivityCacheDTO> activityMap = new HashMap<>();
        ActivityModel activityModel =new ActivityModel();

        Calendar instance1 = Calendar.getInstance();
        Date time1 = instance1.getTime();

        String activityStartTime = DateUtil.format(time1, DateUtil.FORMAT_YYYYMMDDHHMISS_14);

        instance1.add(Calendar.DATE,3);

        String activityEndTime = DateUtil.format(instance1.getTime(), DateUtil.FORMAT_YYYYMMDDHHMISS_14);

        activityModel.setActivityBegin(activityStartTime);
        activityModel.setActivityEnd(activityEndTime);

        ActivityCacheDTO activityCacheDTO = new ActivityCacheDTO();

        List<TPromoActivityStoreVO> promoChannels = new ArrayList<>();
        TPromoActivityStoreVO activityStoreVO = new TPromoActivityStoreVO();
        activityStoreVO.setOrgCode("5");
        promoChannels.add(activityStoreVO);
        activityCacheDTO.setPromoChannels(promoChannels);

        activityModel.setActivityType("02");
        activityCacheDTO.setActivityModel(activityModel);

        activityMap.put("1",activityCacheDTO);

        Calendar instance = Calendar.getInstance();

        instance.add(Calendar.DATE,2);

        String endTime = DateUtil.format(instance.getTime(), DateUtil.FORMAT_YYYYMMDDHHMISS_14);

        ActivityFilterUtil.filterActivityByActivityTime(activityMap, "",endTime);

    }

    @Test
    public void filterActivityByTimeEndEmpty(){


        Map<String, ActivityCacheDTO> activityMap = new HashMap<>();
        ActivityModel activityModel =new ActivityModel();

        Calendar instance1 = Calendar.getInstance();
        Date time1 = instance1.getTime();

        String activityStartTime = DateUtil.format(time1, DateUtil.FORMAT_YYYYMMDDHHMISS_14);

        instance1.add(Calendar.DATE,3);

        String activityEndTime = DateUtil.format(instance1.getTime(), DateUtil.FORMAT_YYYYMMDDHHMISS_14);

        activityModel.setActivityBegin(activityStartTime);
        activityModel.setActivityEnd(activityEndTime);

        ActivityCacheDTO activityCacheDTO = new ActivityCacheDTO();

        List<TPromoActivityStoreVO> promoChannels = new ArrayList<>();
        TPromoActivityStoreVO activityStoreVO = new TPromoActivityStoreVO();
        activityStoreVO.setOrgCode("5");
        promoChannels.add(activityStoreVO);
        activityCacheDTO.setPromoChannels(promoChannels);

        activityModel.setActivityType("02");
        activityCacheDTO.setActivityModel(activityModel);

        activityMap.put("1",activityCacheDTO);

        Calendar instance = Calendar.getInstance();
        Date time = instance.getTime();

        String startTime = DateUtil.format(time, DateUtil.FORMAT_YYYYMMDDHHMISS_14);

        instance.add(Calendar.DATE,2);

        ActivityFilterUtil.filterActivityByActivityTime(activityMap, startTime,"");

    }

    @Test
    public void filterActivityByTimeEndEmpty_period(){


        Map<String, ActivityCacheDTO> activityMap = new HashMap<>();
        ActivityModel activityModel =new ActivityModel();

        Calendar instance1 = Calendar.getInstance();
        Date time1 = instance1.getTime();

        String activityStartTime = DateUtil.format(time1, DateUtil.FORMAT_YYYYMMDDHHMISS_14);

        instance1.add(Calendar.DATE,3);

        String activityEndTime = DateUtil.format(instance1.getTime(), DateUtil.FORMAT_YYYYMMDDHHMISS_14);

        activityModel.setActivityBegin(activityStartTime);
        activityModel.setActivityEnd(activityEndTime);

        ActivityCacheDTO activityCacheDTO = new ActivityCacheDTO();

        List<TPromoActivityStoreVO> promoChannels = new ArrayList<>();
        TPromoActivityStoreVO activityStoreVO = new TPromoActivityStoreVO();
        activityStoreVO.setOrgCode("5");
        promoChannels.add(activityStoreVO);
        activityCacheDTO.setPromoChannels(promoChannels);

        activityModel.setActivityType("02");
        activityCacheDTO.setActivityModel(activityModel);
        ActivityPeriodModel periodModel = new ActivityPeriodModel();
        periodModel.setBeginPeriod(activityStartTime);
        periodModel.setEndPeriod(activityEndTime);

        activityCacheDTO.setPeriodModel(periodModel);
        activityMap.put("1",activityCacheDTO);

        Calendar instance = Calendar.getInstance();
        Date time = instance.getTime();

        String startTime = DateUtil.format(time, DateUtil.FORMAT_YYYYMMDDHHMISS_14);

        instance.add(Calendar.DATE,2);

        ActivityFilterUtil.filterActivityByActivityTime(activityMap, startTime,"");

    }
}
