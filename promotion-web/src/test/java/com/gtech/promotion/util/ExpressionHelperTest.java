package com.gtech.promotion.util;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import org.junit.Assert;
import org.junit.Test;

import com.alibaba.fastjson.JSON;
import com.gtech.promotion.helper.ActivityCodeComparator;
import com.gtech.promotion.utils.ExpressionHelper;
import com.gtech.promotion.vo.result.activity.ActivityGroupCache;

/**
 * <AUTHOR>
 * @since 2021/8/17 14:23
 */
public class ExpressionHelperTest {

    @Test
    public void isExpiredVersion(){
        boolean version = ExpressionHelper.isExpiredVersion("1_0102020203020415");
        Assert.assertFalse(version);
    }

    @Test
    public void isExpiredVersion1(){
        boolean version = ExpressionHelper.isExpiredVersion("1");
        Assert.assertTrue(version);
    }

    @Test
    public void activityCodeSet2String(){
        Map<String, ActivityCodeComparator> activityCodeComparatorMap = new HashMap<>();
        ActivityCodeComparator activityCodeComparator = new ActivityCodeComparator();
        activityCodeComparator.setActivityCode("1");
        activityCodeComparator.setTemplateCode("0104020103010401");
        activityCodeComparator.setActivityType("02");
        ActivityCodeComparator activityCodeComparator1 = new ActivityCodeComparator();
        activityCodeComparator1.setActivityCode("2");
        activityCodeComparator1.setTemplateCode("0104020103010402");
        activityCodeComparator1.setActivityType("02");
        activityCodeComparatorMap.put("1",activityCodeComparator);
        activityCodeComparatorMap.put("2",activityCodeComparator1);
        String activity = ExpressionHelper.sortActivity(ExpressionHelper.DEF_EXPROSSION, activityCodeComparatorMap);
        Assert.assertNotNull(activity);
    }

    @Test
    public void isExclusionTest(){
        String leftCode = "";
        String rightCode ="";
        String midExpression = "(test)";
        ExpressionHelper.isExclusion(leftCode,rightCode,midExpression);
    }

	@Test
	public void checkActivitySku() {
		boolean tryFlag = false;

		// 2个商品4个活动
		List<String> skuList = Arrays.asList("sku1");
		// 活动所属分组 并且按优先级排序
		List<String> groupList = Arrays.asList("D", "B", "A");

		List<ActivityGroupCache> groupCacheList = getGroupList();
		for (String groupCode : groupList) {
			for (String exclusionKey : skuList) {
				tryFlag = ExpressionHelper.checkActivitySkuWithGroup(exclusionKey, groupCode, "activityCode", groupCacheList, false);
				if (tryFlag) {
					System.out.println(groupCode);
				}
			}
		}
		Assert.assertTrue(true);
	}

	private List<ActivityGroupCache> getGroupList() {
		// 优先级排序
		List<String> groupSettingList = Arrays.asList("D", "C", "B", "A");
		Map<String, List<String>> groupMap = new LinkedHashMap<>();
		for (String string : groupSettingList) {
			groupMap.put(string, new ArrayList<>());
		}
		List<ActivityGroupCache> groupCacheList = new ArrayList<>();
		ActivityGroupCache a = new ActivityGroupCache();
		groupCacheList.add(a);
		a.setGroupCode("A");
		a.setRelationList(Arrays.asList("B", "D"));

		ActivityGroupCache b = new ActivityGroupCache();
		groupCacheList.add(b);
		b.setGroupCode("B");
		b.setRelationList(Arrays.asList("A"));

		ActivityGroupCache c = new ActivityGroupCache();
		groupCacheList.add(c);
		c.setGroupCode("C");

		ActivityGroupCache d = new ActivityGroupCache();
		groupCacheList.add(d);
		d.setGroupCode("D");
		d.setRelationList(Arrays.asList("A"));
		return groupCacheList;
	}


	@Test
	public void isExclusionWithGroup_left(){
		String leftCode = "";

		List<ActivityGroupCache> groupCacheList = new ArrayList<>();
		ActivityGroupCache activityGroupCache = new ActivityGroupCache();
		activityGroupCache.setGroupCode("2");
		activityGroupCache.setPriority(1);

		List<String> list = new ArrayList<>();
		list.add("1");
		list.add("2");
		activityGroupCache.setRelationList(list);
		groupCacheList.add(activityGroupCache);
		boolean exclusionWithGroup = ExpressionHelper.isExclusionWithGroup(leftCode, "1", groupCacheList);
		Assert.assertEquals(false, exclusionWithGroup);
		leftCode = "1";
		exclusionWithGroup = ExpressionHelper.isExclusionWithGroup(leftCode, "", groupCacheList);
		Assert.assertEquals(false,exclusionWithGroup);
		exclusionWithGroup = ExpressionHelper.isExclusionWithGroup(leftCode, "1", groupCacheList);
		Assert.assertEquals(true, exclusionWithGroup);
		leftCode = "2";
		exclusionWithGroup = ExpressionHelper.isExclusionWithGroup(leftCode, "1", groupCacheList);
		Assert.assertEquals(true, exclusionWithGroup);
		leftCode = "3";
		exclusionWithGroup = ExpressionHelper.isExclusionWithGroup(leftCode, "1", groupCacheList);
		Assert.assertEquals(false, exclusionWithGroup);
	}

	@Test
	public void isExclusionWithGroup_rightCode(){
		String leftCode = "1";
		String rightCode ="";

		List<ActivityGroupCache> groupCacheList = new ArrayList<>();
		ActivityGroupCache activityGroupCache = new ActivityGroupCache();
		activityGroupCache.setGroupCode("1");
		activityGroupCache.setPriority(1);

		List<String> list = new ArrayList<>();
		list.add("2");
		list.add("3");
		activityGroupCache.setRelationList(list);
		groupCacheList.add(activityGroupCache);
		boolean exclusionWithGroup = ExpressionHelper.isExclusionWithGroup(leftCode, rightCode, groupCacheList);
		Assert.assertEquals(false,exclusionWithGroup);
	}

	@Test
	public void isExclusionWithGroup_rightCode_1(){
		String leftCode = "1";
		String rightCode ="1";

		List<ActivityGroupCache> groupCacheList = new ArrayList<>();
		ActivityGroupCache activityGroupCache = new ActivityGroupCache();
		activityGroupCache.setGroupCode("1");
		activityGroupCache.setPriority(1);

		List<String> list = new ArrayList<>();
		list.add("2");
		list.add("3");
		activityGroupCache.setRelationList(list);
		boolean exclusionWithGroup = ExpressionHelper.isExclusionWithGroup(leftCode, rightCode, groupCacheList);
		Assert.assertEquals(false,exclusionWithGroup);
	}

	@Test
	public void isExclusionWithGroup(){
		String leftCode = "1:1";
		String rightCode ="1";

		List<ActivityGroupCache> groupCacheList = new ArrayList<>();
		ActivityGroupCache activityGroupCache = new ActivityGroupCache();
		activityGroupCache.setGroupCode("1");
		activityGroupCache.setPriority(1);

		List<String> list = new ArrayList<>();
		list.add("2");
		list.add("3");
		activityGroupCache.setRelationList(list);
		groupCacheList.add(activityGroupCache);
		boolean exclusionWithGroup = ExpressionHelper.isExclusionWithGroup(leftCode,rightCode,groupCacheList);
		Assert.assertEquals(true,exclusionWithGroup);

	}

	@Test
	public void isExclusionWithGroup2(){
		String leftCode = ":1";
		String rightCode ="1";

		List<ActivityGroupCache> groupCacheList = new ArrayList<>();
		ActivityGroupCache activityGroupCache = new ActivityGroupCache();
		activityGroupCache.setGroupCode("1");
		activityGroupCache.setPriority(1);

		List<String> list = new ArrayList<>();
		list.add("2");
		list.add("3");
		activityGroupCache.setRelationList(list);
		groupCacheList.add(activityGroupCache);
		boolean exclusionWithGroup = ExpressionHelper.isExclusionWithGroup(leftCode,rightCode,groupCacheList);
		Assert.assertEquals(false,exclusionWithGroup);

	}

	@Test
	public void isExclusionWithGroup3(){
		String leftCode = "1:1";
		String rightCode ="1";

		List<ActivityGroupCache> groupCacheList = new ArrayList<>();
		ActivityGroupCache activityGroupCache = new ActivityGroupCache();
		activityGroupCache.setGroupCode("4");
		activityGroupCache.setPriority(1);

		List<String> list = new ArrayList<>();
		list.add("2");
		list.add("3");
		list.add("1");
		activityGroupCache.setRelationList(list);
		groupCacheList.add(activityGroupCache);
		boolean exclusionWithGroup = ExpressionHelper.isExclusionWithGroup(leftCode,rightCode,groupCacheList);
		Assert.assertEquals(true,exclusionWithGroup);

	}

	@Test
	public void isExclusionWithGroup4(){
		String leftCode = "1:1";
		String rightCode ="1";

		List<ActivityGroupCache> groupCacheList = new ArrayList<>();
		ActivityGroupCache activityGroupCache = new ActivityGroupCache();
		activityGroupCache.setGroupCode("1");
		activityGroupCache.setPriority(1);

		List<String> list = new ArrayList<>();
		list.add("2");
		list.add("3");
		list.add("1");
		activityGroupCache.setRelationList(new ArrayList<>());
		groupCacheList.add(activityGroupCache);
		boolean exclusionWithGroup = ExpressionHelper.isExclusionWithGroup(leftCode,rightCode,groupCacheList);
		Assert.assertEquals(true,exclusionWithGroup);

	}


	@Test
	public void isExclusionWithGroup5(){
		String leftCode = "1:1";
		String rightCode ="1";

		List<ActivityGroupCache> groupCacheList = new ArrayList<>();
		ActivityGroupCache activityGroupCache = new ActivityGroupCache();
		activityGroupCache.setGroupCode("1");
		activityGroupCache.setPriority(1);

		List<String> list = new ArrayList<>();
		list.add("2");
		list.add("3");
		list.add("1");
		activityGroupCache.setRelationList(list);
		groupCacheList.add(activityGroupCache);
		boolean exclusionWithGroup = ExpressionHelper.isExclusionWithGroup(leftCode,rightCode,groupCacheList);
		Assert.assertEquals(true,exclusionWithGroup);

	}

	@Test
	public void testGroup() {
		String str = "{\"1_0101020103010402\":\"101\",\"1_0102020303020401\":\"103\",\"1_0102020203020413\":\"102\",\"1_0102020203020404\":\"104\",\"1_0101020603020402\":\"105\",\"1_0101020703020406\":\"106\",\"1_0102020203020415\":\"107\",\"2_0104020103010401\":\"201\",\"2_0102020203020401\":\"202\",\"2_0102020203030401\":\"203\",\"2_0101020703020406\":\"204\",\"2_0102020203020405\":\"205\",\"2_0101020603020401\":\"206\"}";
		Map<String, String> map = JSON.parseObject(str, Map.class);
		String exprossion = "[2_0104020103010401,2_0104020103010402] and [1_0101020103010401,1_0101020103010402,1_0101020103010403,1_0101020103010411]  and [1_0101020603020401,1_0101020603020402,1_0102020603020401,1_0102020603020402]  and [1_0102020203020401,1_0102020203030401,1_0102020303020401,1_0102020303030401,1_0102020203020402,1_0102020203030402,1_0102020403020402,1_0102020203020414] and [1_0103020203020401,1_0103020203030401,1_0103020303020401,1_0103020303030401,1_0103020203020402,1_0103020203030402] and [1_0102020203020412,1_0102020203030412,1_0102020203020413,1_0102020203030413] and [1_0102020503020401,1_0102020503020404,1_0102020203020404,1_0102020303020404] and [1_0102020303020407,1_0102020303020408] and [2_0102020203020401,2_0102020203030401,2_0102020303020401,2_0102020303030401,2_0102020203020402,2_0102020203030402,2_0102020403020402,2_0102020203020414] and [2_0103020203020401,2_0103020203030401,2_0103020303020401,2_0103020303030401,2_0103020203020402,2_0103020203030402] and [2_0102020203020412,2_0102020203030412,2_0102020203020413,2_0102020203030413] and [1_0101020703020406,1_0101020703030406,1_0102020203020406,1_0102020203030406,1_0102020303020406,1_0102020303030406,1_0103020203020406,1_0103020203030406] and [1_0102020203020405,1_0102020203030405,1_0102020203020415,1_0102020203030415,1_0103020203020405,1_0103020203030405] and [2_0102020203020405,2_0102020203030405,2_0102020203020415,2_0102020203030415,2_0103020203020405,2_0103020203030405]";
//		String exprossion = ExpressionHelper.DEF_EXPROSSION;
		for (String a : exprossion.split("and")) {
			for (String key : map.keySet()) {
				if (a.contains(key)) {
					for (String code : map.keySet()) {
						if (a.contains(code) && !key.equals(code)) {
							System.out.println(
									"INSERT INTO `promo_group_relation` (`DOMAIN_CODE`, `GROUP_CODE_A`, `GROUP_CODE_B`, `CREATE_TIME`, `UPDATE_TIME`, `LOGIC_DELETE`, `TENANT_CODE`, `CREATE_USER`, `UPDATE_USER`) VALUES ('DC0005', '"
											+ map.get(key) + "', '" + map.get(code) + "', now(),  now(), '0', '100016', NULL, NULL);");
						}
					}
				}
			}
		}
		String[] all = { "101", "102", "103", "104", "105", "106", "107", "108", "201", "202", "203", "204", "205", "206", "207" };
		String[] ma = { "301", "302", "303", "401", "501", "601", "701" };
		for (String a : all) {
			for (String b : ma) {
				System.out.println(
						"INSERT INTO `promo_group_relation` (`DOMAIN_CODE`, `GROUP_CODE_A`, `GROUP_CODE_B`, `CREATE_TIME`, `UPDATE_TIME`, `LOGIC_DELETE`, `TENANT_CODE`, `CREATE_USER`, `UPDATE_USER`) VALUES ('DC0005', '"
								+ a + "', '" + b + "', now(),  now(), '0', '100016', NULL, NULL);");
				System.out.println(
						"INSERT INTO `promo_group_relation` (`DOMAIN_CODE`, `GROUP_CODE_A`, `GROUP_CODE_B`, `CREATE_TIME`, `UPDATE_TIME`, `LOGIC_DELETE`, `TENANT_CODE`, `CREATE_USER`, `UPDATE_USER`) VALUES ('DC0005', '"
								+ b + "', '" + a + "', now(),  now(), '0', '100016', NULL, NULL);");
			}
		}
		for (int i = 0; i < ma.length; i++) {
			for (int j = 0; j < ma.length; j++) {
				if (i == j) {
					continue;
				}
				System.out.println(
						"INSERT INTO `promo_group_relation` (`DOMAIN_CODE`, `GROUP_CODE_A`, `GROUP_CODE_B`, `CREATE_TIME`, `UPDATE_TIME`, `LOGIC_DELETE`, `TENANT_CODE`, `CREATE_USER`, `UPDATE_USER`) VALUES ('DC0005', '"
								+ ma[i] + "', '" + ma[j] + "', now(),  now(), '0', '100016', NULL, NULL);");
			}
		}
	}
}
