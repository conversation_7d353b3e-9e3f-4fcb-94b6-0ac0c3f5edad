package com.gtech.promotion.util;

import com.gtech.promotion.utils.ListUtil;
import org.junit.Test;

import java.util.Arrays;
import java.util.List;

public class ListUtilTest {

    @Test
    public void splitListTest(){
        List<String> stringList = Arrays.asList(
                "1","2","3","4","5","6","7","8","9","10","11","12","13","14","15","16","17","18","19","20",
                "1","2","3","4","5","6","7","8","9","10","11","12","13","14","15","16","17","18","19","20",
                "1","2","3","4","5","6","7","8","9","10","11","12","13","14","15","16","17","18","19","20",
                "1","2","3","4","5","6","7","8","9","10","11","12","13","14","15","16","17","18","19","20",
                "1","2","3","4","5","6","7","8","9","10","11","12","13","14","15","16","17","18","19","20",
                "1","2","3","4","5","6","7","8","9","10","11","12","13","14","15","16","17","18","19","20",
                "1","2","3","4","5","6","7","8","9","10","11","12","13","14","15","16","17","18","19","20",
                "1","2","3","4","5","6","7","8","9","10","11","12","13","14","15","16","17","18","19","20",
                "1","2","3","4","5","6","7","8","9","10","11","12","13","14","15","16","17","18","19","20",
                "1","2","3","4","5","6","7","8","9","10","11","12","13","14","15","16","17","18","19","20"
        );
        List<List<String>> list = ListUtil.splitList(stringList,10);
        int i = 0;
        for(List<String> alist : list){
            for(String a : alist){
                i += 1;
            }
        }
    }
}
