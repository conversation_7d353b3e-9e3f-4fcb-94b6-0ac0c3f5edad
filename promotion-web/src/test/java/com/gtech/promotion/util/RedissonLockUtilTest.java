package com.gtech.promotion.util;

import com.gtech.promotion.exception.PromotionException;
import com.gtech.promotion.utils.RedissonLockUtil;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import org.redisson.RedissonLock;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;

import java.util.concurrent.TimeUnit;

@Slf4j
@RunWith(MockitoJUnitRunner.class)
public class RedissonLockUtilTest {
    @InjectMocks
    private RedissonLockUtil redissonLockUtil;
    @Mock
    private RedissonClient redissonClient;
    @Test
    public void runWithLock() throws InterruptedException {
        // mock redis lock
        RLock lock = Mockito.mock(RedissonLock.class);
        Mockito.doReturn(true).when(lock).tryLock(1, 1, TimeUnit.MILLISECONDS);
        Mockito.doReturn(lock).when(redissonClient).getLock(Mockito.any());

        redissonLockUtil.runWithLock("001",1L,1L,()->{

        });

        Assert.assertThrows(PromotionException.class, () -> {
            redissonLockUtil.runWithLock("001", null, 1L, () -> {
            });
        });

    }
}
