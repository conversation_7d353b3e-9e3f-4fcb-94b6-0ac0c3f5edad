package com.gtech.promotion.util;

import com.gtech.promotion.utils.GenerateUtil;
import com.gtech.promotion.utils.ListUtils;
import org.junit.Assert;
import org.junit.Test;

import java.util.ArrayList;
import java.util.List;

public class ListUtilsTest {

    @Test
    public void getAllSubList() {
        List<String> list = new ArrayList<>();
        list.add("1asdfa");
        list.add("25354tseg");
        list.add("sdfsdf");
        List<String> allSubList = ListUtils.getAllSubList(list);
        Assert.assertEquals(7, allSubList.size());
    }
    @Test
    public void generateUtil() {
    	GenerateUtil.getDate("yyyyMMddhhmmss") ;
    	GenerateUtil.getRandomNum(10) ;
    	GenerateUtil.getGenerateID(3) ;
    	Assert.assertTrue(true);
    }
}
