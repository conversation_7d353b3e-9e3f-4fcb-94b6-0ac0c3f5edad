package com.gtech.promotion.util;

import com.gtech.commons.utils.DateUtil;
import com.gtech.promotion.code.activity.ActivityTypeEnum;
import com.gtech.promotion.dao.model.activity.ActivityModel;
import com.gtech.promotion.dao.model.activity.ActivityPeriodModel;
import com.gtech.promotion.dao.model.marketing.flashsale.CacheFlashSaleModel;
import com.gtech.promotion.dao.model.marketing.flashsale.FlashSaleStoreModel;
import com.gtech.promotion.dto.cache.ActivityCacheDTO;
import com.gtech.promotion.utils.MarketingFilterUtil;
import org.junit.Assert;
import org.junit.Test;

import java.util.*;

/**
 * <AUTHOR>
 * @since 2021/8/17 13:44
 */
public class MarketingFilterUtilTest {

    @Test
    public void filterActivityByQualifications(){
        Map<String, CacheFlashSaleModel> flashSaleCacheMap = new HashMap<>();
        CacheFlashSaleModel model = new CacheFlashSaleModel();
        flashSaleCacheMap.put("1",model);
        Map<String, List<String>> qualifications = new HashMap<>();
        Map<String, CacheFlashSaleModel> map = MarketingFilterUtil.filterActivityByQualifications(flashSaleCacheMap, qualifications);
        Assert.assertNotNull(map);
    }

    @Test
    public void filterActivityByOrgCodes(){
        Map<String, CacheFlashSaleModel> flashSaleCacheMap = new HashMap<>();
        CacheFlashSaleModel model = new CacheFlashSaleModel();
        flashSaleCacheMap.put("1",model);
        List<String> orgCodes = new ArrayList<>();
        orgCodes.add("");
        Map<String, CacheFlashSaleModel> map = MarketingFilterUtil.filterActivityByOrgCodes(flashSaleCacheMap, orgCodes);
        Assert.assertNotNull(map);
    }

    @Test
    public void filterActivityByOrgCodes1(){
        Map<String, CacheFlashSaleModel> flashSaleCacheMap = new HashMap<>();
        CacheFlashSaleModel model = new CacheFlashSaleModel();
        flashSaleCacheMap.put("1",model);
        List<String> orgCodes = new ArrayList<>();
        Map<String, CacheFlashSaleModel> map = MarketingFilterUtil.filterActivityByOrgCodes(flashSaleCacheMap, orgCodes);
        Assert.assertNotNull(map);
    }

    @Test
    public void filterActivityByOrgCodes2(){
        Map<String, CacheFlashSaleModel> flashSaleCacheMap = new HashMap<>();
        CacheFlashSaleModel model = new CacheFlashSaleModel();
        List<FlashSaleStoreModel> stores = new ArrayList<>();
        FlashSaleStoreModel storeModel = new FlashSaleStoreModel();
        storeModel.setOrgCode("1");
        stores.add(storeModel);
        model.setStores(stores);
        flashSaleCacheMap.put("1",model);
        List<String> orgCodes = new ArrayList<>();
        orgCodes.add("1");
        Map<String, CacheFlashSaleModel> map = MarketingFilterUtil.filterActivityByOrgCodes(flashSaleCacheMap, orgCodes);
        Assert.assertNotNull(map);
    }


    @Test
    public void filterActivityByWarmTime(){
        Map<String, CacheFlashSaleModel> flashSaleCacheMap = new HashMap<>();

        Map<String, CacheFlashSaleModel> map = MarketingFilterUtil.filterActivityByWarmTime(flashSaleCacheMap, null,null);
        Assert.assertEquals(0,map.size());
    }


    @Test
    public void filterActivityByWarmTime_periodModel_empty(){
        Map<String, CacheFlashSaleModel> flashSaleCacheMap = new HashMap<>();
        CacheFlashSaleModel model = new CacheFlashSaleModel();

        Calendar instance1 = Calendar.getInstance();
        instance1.add(Calendar.DATE,-1);

        Date time1 = instance1.getTime();
        String activityStartTime = DateUtil.format(time1, DateUtil.FORMAT_YYYYMMDDHHMISS_14);
        instance1.add(Calendar.DATE,2);

        String activityEndTime = DateUtil.format(instance1.getTime(), DateUtil.FORMAT_YYYYMMDDHHMISS_14);
        model.setActivityBegin(activityStartTime);
        model.setActivityEnd(activityEndTime);


        ActivityPeriodModel periodModel = new ActivityPeriodModel();
        periodModel.setBeginPeriod(activityStartTime);
        periodModel.setEndPeriod(activityEndTime);
        periodModel.setIntervalWeek(1);
        model.setActivityPeriod(periodModel);

        flashSaleCacheMap.put("1",model);
        Map<String, CacheFlashSaleModel> map = MarketingFilterUtil.filterActivityByWarmTime(flashSaleCacheMap, null,null);
        Assert.assertEquals(0,map.size());
    }

    @Test
    public void filterActivityByWarmTime_periodModel_null(){
        Map<String, CacheFlashSaleModel> flashSaleCacheMap = new HashMap<>();
        CacheFlashSaleModel model = new CacheFlashSaleModel();
        Calendar instance1 = Calendar.getInstance();
        instance1.add(Calendar.DATE,-1);
        Date time1 = instance1.getTime();
        String activityStartTime = DateUtil.format(time1, DateUtil.FORMAT_YYYYMMDDHHMISS_14);
        instance1.add(Calendar.DATE,2);
        String activityEndTime = DateUtil.format(instance1.getTime(), DateUtil.FORMAT_YYYYMMDDHHMISS_14);
        model.setActivityBegin(activityStartTime);
        model.setActivityEnd(activityEndTime);
        model.setActivityPeriod(null);

        flashSaleCacheMap.put("1",model);
        Map<String, CacheFlashSaleModel> map = MarketingFilterUtil.filterActivityByWarmTime(flashSaleCacheMap, null,activityStartTime);
        Assert.assertEquals(1,map.size());
    }


    @Test
    public void filterActivityByWarmTime_warmBeginFrom_not_empty(){
        Map<String, CacheFlashSaleModel> flashSaleCacheMap = new HashMap<>();
        CacheFlashSaleModel model = new CacheFlashSaleModel();
        Calendar instance1 = Calendar.getInstance();
        instance1.add(Calendar.DATE,-1);
        Date time1 = instance1.getTime();
        String activityStartTime = DateUtil.format(time1, DateUtil.FORMAT_YYYYMMDDHHMISS_14);
        instance1.add(Calendar.DATE,2);
        String activityEndTime = DateUtil.format(instance1.getTime(), DateUtil.FORMAT_YYYYMMDDHHMISS_14);
        model.setActivityBegin(activityStartTime);
        model.setActivityEnd(activityEndTime);
        model.setActivityPeriod(null);
        model.setWarmBegin(activityStartTime);
        flashSaleCacheMap.put("1",model);
        Map<String, CacheFlashSaleModel> map = MarketingFilterUtil.filterActivityByWarmTime(flashSaleCacheMap, activityStartTime,null);
        Assert.assertEquals(1,map.size());
    }

    @Test
    public void filterActivityByWarmTime_warmBeginTo_not_empty(){
        Map<String, CacheFlashSaleModel> flashSaleCacheMap = new HashMap<>();
        CacheFlashSaleModel model = new CacheFlashSaleModel();
        Calendar instance1 = Calendar.getInstance();
        instance1.add(Calendar.DATE,-1);
        Date time1 = instance1.getTime();
        String warmBeginTo = DateUtil.format(time1, DateUtil.FORMAT_YYYYMMDDHHMISS_14);
        instance1.add(Calendar.DATE,2);
        String activityEndTime = DateUtil.format(instance1.getTime(), DateUtil.FORMAT_YYYYMMDDHHMISS_14);
        model.setActivityBegin(warmBeginTo);
        model.setActivityEnd(activityEndTime);
        model.setActivityPeriod(null);
        model.setWarmBegin(warmBeginTo);
        flashSaleCacheMap.put("1",model);
        Map<String, CacheFlashSaleModel> map = MarketingFilterUtil.filterActivityByWarmTime(flashSaleCacheMap, null,warmBeginTo);
        Assert.assertEquals(1,map.size());
    }

    @Test
    public void filterActivityByWarmTime_warmTime_not_empty(){
        Map<String, CacheFlashSaleModel> flashSaleCacheMap = new HashMap<>();
        CacheFlashSaleModel model = new CacheFlashSaleModel();
        Calendar instance1 = Calendar.getInstance();
        instance1.add(Calendar.DATE,-1);
        String warmTime = DateUtil.format(instance1.getTime(), DateUtil.FORMAT_YYYYMMDDHHMISS_14);
        model.setWarmBegin(warmTime);

        instance1.add(Calendar.DATE,1);
        String warmTimeFrom = DateUtil.format(instance1.getTime(), DateUtil.FORMAT_YYYYMMDDHHMISS_14);

        instance1.add(Calendar.DATE,1);
        String warmTimeTo = DateUtil.format(instance1.getTime(), DateUtil.FORMAT_YYYYMMDDHHMISS_14);

        instance1.add(Calendar.DATE,3);
        Date time2 = instance1.getTime();

        String activityStartTime = DateUtil.format(time2, DateUtil.FORMAT_YYYYMMDDHHMISS_14);

        instance1.add(Calendar.DATE,1);
        String activityEndTime = DateUtil.format(instance1.getTime(), DateUtil.FORMAT_YYYYMMDDHHMISS_14);

        model.setActivityBegin(activityStartTime);

        model.setActivityEnd(activityEndTime);


        flashSaleCacheMap.put("1",model);
        Map<String, CacheFlashSaleModel> map = MarketingFilterUtil.filterActivityByWarmTime(flashSaleCacheMap, null,warmTimeFrom);
        Assert.assertEquals(1,map.size());
    }

    @Test
    public void filterActivityByTime(){
        Map<String, CacheFlashSaleModel> flashSaleCacheMap = new HashMap<>();
        CacheFlashSaleModel model = new CacheFlashSaleModel();
        model.setActivityBegin("20210817141155");
        model.setActivityEnd("20220817141155");
        ActivityPeriodModel periodModel = new ActivityPeriodModel();
        periodModel.setBeginPeriod("20210817141155");
        periodModel.setEndPeriod("20220817141155");
        periodModel.setIntervalWeek(1);
        model.setActivityPeriod(periodModel);
        flashSaleCacheMap.put("1",model);
        Map<String, CacheFlashSaleModel> map = MarketingFilterUtil.filterActivityByTime(flashSaleCacheMap, null);
        Assert.assertNotNull(map);

        MarketingFilterUtil.filterActivityByTime(flashSaleCacheMap, "20210817141155");
    }




    @Test
    public void filterActivityByTime1(){
        Map<String, CacheFlashSaleModel> flashSaleCacheMap = new HashMap<>();
        CacheFlashSaleModel model = new CacheFlashSaleModel();
        model.setActivityBegin("20210817141155");
        model.setActivityEnd("20220817141155");
        flashSaleCacheMap.put("1",model);
        Map<String, CacheFlashSaleModel> map = MarketingFilterUtil.filterActivityByTime(flashSaleCacheMap, null);
        Assert.assertNotNull(map);
    }

    @Test
    public void filterActivityByActivityType(){
        Map<String, ActivityCacheDTO> flashSaleCacheMap = new HashMap<>();
        Map<String, ActivityCacheDTO> map = MarketingFilterUtil.filterActivityByActivityType(flashSaleCacheMap, ActivityTypeEnum.ACTIVITY);
        Assert.assertNotNull(map);
    }

    @Test
    public void filterActivityByActivityType1(){
        MarketingFilterUtil.filterActivityByActivityType(null, ActivityTypeEnum.ACTIVITY);


        Map<String, ActivityCacheDTO> flashSaleCacheMap = new HashMap<>();
        ActivityCacheDTO dto = new ActivityCacheDTO();
        flashSaleCacheMap.put("1",dto);
        ActivityModel activity = new ActivityModel();
        dto.setActivityModel(activity);
        activity.setActivityType("01");
        Map<String, ActivityCacheDTO> map = MarketingFilterUtil.filterActivityByActivityType(flashSaleCacheMap, ActivityTypeEnum.ACTIVITY);
        Assert.assertNotNull(map);



    }
}
