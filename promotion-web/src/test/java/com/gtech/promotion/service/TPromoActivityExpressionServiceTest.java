package com.gtech.promotion.service;

import com.gtech.promotion.dao.entity.activity.TPromoActivityExpressionEntity;
import com.gtech.promotion.dao.mapper.activity.TPromoActivityExpressionMapper;
import com.gtech.promotion.helper.ActivityRedisHelpler;
import com.gtech.promotion.service.impl.activity.TPromoActivityExpressionServiceImpl;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.data.redis.core.StringRedisTemplate;
import tk.mybatis.mapper.mapperhelper.EntityHelper;
import tk.mybatis.mapper.mapperhelper.MapperHelper;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * @Description :
 * <AUTHOR> gaojie.li
 * @Date : 2021/8/4 14:48
 */
@RunWith(MockitoJUnitRunner.class)
public class TPromoActivityExpressionServiceTest {
    @InjectMocks
    private TPromoActivityExpressionServiceImpl tPromoActivityExpressionService;

    @Mock
    private TPromoActivityExpressionMapper tPromoActivityExpressionMapper;
    @Mock
    private ActivityRedisHelpler redisService;
//
    @Mock
    private StringRedisTemplate redisTemplate;

    @Before
    public void before(){
        EntityHelper.initEntityNameMap(TPromoActivityExpressionEntity.class, new MapperHelper().getConfig());
    }
    @Test
    public void updateActivityExpression(){
        TPromoActivityExpressionEntity entity = new TPromoActivityExpressionEntity();
        Mockito.when(tPromoActivityExpressionMapper.updateByPrimaryKeySelective(Mockito.any())).thenReturn(1);
        Mockito.doNothing().when(redisService).setExpression(Mockito.any(),Mockito.any());
        Mockito.when(tPromoActivityExpressionMapper.selectOne(Mockito.any())).thenReturn(entity);
        tPromoActivityExpressionService.updateActivityExpression("","");
    }

    @Test
    public void addAllActivityExpression_empty(){
        Set<String> redis = new HashSet<>();
        Mockito.when(redisService.getAll(Mockito.any())).thenReturn(redis);
        tPromoActivityExpressionService.addAllActivityExpression();
    }

    @Test
    public void addAllActivityExpression_not_empty(){
        Set<String> redis = new HashSet<>();
        List<TPromoActivityExpressionEntity> selectAll = new ArrayList<>();
        TPromoActivityExpressionEntity expressionEntity = new TPromoActivityExpressionEntity();
        expressionEntity.setTenantCode("1");
        selectAll.add(expressionEntity);
        Mockito.when(tPromoActivityExpressionMapper.selectAll()).thenReturn(selectAll);
        Mockito.when(redisService.getAll(Mockito.any())).thenReturn(redis);
        Mockito.doNothing().when(redisService).setExpression(Mockito.any(),Mockito.any());
        tPromoActivityExpressionService.addAllActivityExpression();
    }
}


