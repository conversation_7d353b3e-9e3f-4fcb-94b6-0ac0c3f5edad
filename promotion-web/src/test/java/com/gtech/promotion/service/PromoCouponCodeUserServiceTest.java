/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such
 * Confidential Information and shall use it only in accordance with the terms of the license agreement
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.service;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.gtech.commons.page.PageData;
import com.gtech.promotion.code.coupon.CouponStatusEnum;
import com.gtech.promotion.dao.entity.coupon.TPromoCouponCodeUserEntity;
import com.gtech.promotion.dao.entity.coupon.TPromoCouponInnerCodeEntity;
import com.gtech.promotion.dao.mapper.coupon.TPromoCouponCodeUserMapper;
import com.gtech.promotion.dao.model.coupon.QueryUserCouponVO;
import com.gtech.promotion.dao.model.coupon.TPromoCouponCodeUserVO;
import com.gtech.promotion.domain.coupon.CouponDomain;
import com.gtech.promotion.dto.in.activity.ActivityTenantInDTO;
import com.gtech.promotion.dto.in.activity.ManagementDataInDTO;
import com.gtech.promotion.dto.in.coupon.ExportCouponUserDto;
import com.gtech.promotion.dto.in.coupon.QueryCouponUsedInDTO;
import com.gtech.promotion.dto.in.coupon.TCouponCheckAndUseInDTO;
import com.gtech.promotion.page.RequestPage;
import com.gtech.promotion.service.coupon.SqlPublicMethodsService;
import com.gtech.promotion.service.impl.coupon.PromoCouponCodeUserServiceImpl;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import tk.mybatis.mapper.mapperhelper.EntityHelper;
import tk.mybatis.mapper.mapperhelper.MapperHelper;

import java.util.*;

import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class PromoCouponCodeUserServiceTest {

    @InjectMocks
    private PromoCouponCodeUserServiceImpl promoCouponCodeUserService;

    @Mock
    private TPromoCouponCodeUserMapper couponCodeUserDao;

    @Mock
    private SqlPublicMethodsService sql;

    @Before
    public void before(){
        EntityHelper.initEntityNameMap(TPromoCouponCodeUserEntity.class, new MapperHelper().getConfig());
        EntityHelper.initEntityNameMap(TPromoCouponInnerCodeEntity.class, new MapperHelper().getConfig());
    }

    @Test
    public void updatePromotionCode(){
        when(couponCodeUserDao.updateByConditionSelective(Mockito.any(), Mockito.any())).thenReturn(1);
        int i = promoCouponCodeUserService.updatePromotionCode("1", "1", "1");
        Assert.assertEquals(1,i);
    }

    @Test
    public void updateCouponEndTime(){
        when(couponCodeUserDao.updateByConditionSelective(Mockito.any(),Mockito.any())).thenReturn(1);
        int i = promoCouponCodeUserService.updateCouponEndTime("1", "1", "1","1");
        Assert.assertEquals(1,i);
    }


    @Test
    public void expireCouponCode(){
        when(couponCodeUserDao.updateByConditionSelective(Mockito.any(), Mockito.any())).thenReturn(1);
        int i = promoCouponCodeUserService.expireCouponCode();
        Assert.assertEquals(1,i);
    }

    @Test
    public void findCouponUserByActivityCode(){
        when(couponCodeUserDao.selectCountByCondition(Mockito.any())).thenReturn(1);
        int i = promoCouponCodeUserService.findCouponUserByActivityCode("1","1");
        Assert.assertEquals(1,i);
    }

    @Test
    public void countByReleaseCode(){
        when(couponCodeUserDao.selectCount(Mockito.any())).thenReturn(1);
        int i = promoCouponCodeUserService.countByReleaseCode("1","1","1");
        Assert.assertEquals(1,i);
    }
    @Test
    public void getMyCodesByActivityCodes(){
        when(couponCodeUserDao.selectByCondition(Mockito.any())).thenReturn(new ArrayList<>());
        Set<String> activityCodes = new HashSet<>();
        activityCodes.add("1");
        List<String> opsTypeList = new ArrayList<>();
        opsTypeList.add("1");
        List<TPromoCouponCodeUserVO> i = promoCouponCodeUserService.getMyCodesByActivityCodes("1","1",activityCodes,opsTypeList);
        Assert.assertEquals(0,i.size());
    }

    @Test
    public void getMyCodesByActivityCodes1(){
        when(couponCodeUserDao.selectByCondition(Mockito.any())).thenReturn(new ArrayList<>());
        Set<String> activityCodes = new HashSet<>();
        activityCodes.add("1");
        List<String> opsTypeList = new ArrayList<>();
        opsTypeList.add("1");
        List<TPromoCouponCodeUserVO> i = promoCouponCodeUserService.getMyCodesByActivityCodes("1","1",activityCodes,new ArrayList<>());
        Assert.assertEquals(0,i.size());
    }
    @Test
    public void updateCouponStatus(){
        when(couponCodeUserDao.updateByConditionSelective(Mockito.any(),Mockito.any())).thenReturn(1);
        int i = promoCouponCodeUserService.updateCouponStatus("1","1","1",CouponStatusEnum.EXPIRE,"1");
        Assert.assertEquals(1,i);
    }

    @Test
    public void updateCouponStatus1(){
        when(couponCodeUserDao.updateByConditionSelective(Mockito.any(),Mockito.any())).thenReturn(1);
        int i = promoCouponCodeUserService.updateCouponStatus("1","1","1",CouponStatusEnum.UN_GRANT,"");
        Assert.assertEquals(1,i);
    }

    @Test
    public void expireByActivityCode(){
        when(couponCodeUserDao.updateByConditionSelective(Mockito.any(),Mockito.any())).thenReturn(1);
        int i = promoCouponCodeUserService.expireByActivityCode("1","1","1");
        Assert.assertEquals(1,i);
    }

    @Test
    public void expireByActivityCode_tenantCode(){
        int i = promoCouponCodeUserService.expireByActivityCode("","1","1");
        Assert.assertEquals(0,i);
    }

    @Test
    public void expireByActivityCode_tenantCode1(){
        int i = promoCouponCodeUserService.expireByActivityCode("1","","1");
        Assert.assertEquals(0,i);
    }

    @Test
    public void expireByActivityCode_tenantCode2(){
        int i = promoCouponCodeUserService.expireByActivityCode("","","");
        Assert.assertEquals(0,i);
    }

    @Test
    public void updateCouponUserFrozenStatus(){
        when(couponCodeUserDao.updateByConditionSelective(Mockito.any(),Mockito.any())).thenReturn(1);
        int i = promoCouponCodeUserService.updateCouponUserFrozenStatus("1","1");
        Assert.assertEquals(1,i);
    }

    @Test
    public void getCouponCodeUserVO(){
        when(couponCodeUserDao.selectByCondition(Mockito.any())).thenReturn(new ArrayList<>());
        List<TPromoCouponCodeUserVO> i = promoCouponCodeUserService.getCouponCodeUserVO("1","1","1");
        Assert.assertEquals(0,i.size());
    }

    @Test
    public void getAllocateCouponCountYesterday111(){
        when(couponCodeUserDao.selectCountByCondition(Mockito.any())).thenReturn(1);
        Integer i = promoCouponCodeUserService.getAllocateCouponCountYesterday111("1","1");
        Assert.assertEquals(1,i.intValue());
    }

    @Test
    public void usedCouponAmount(){
        when(couponCodeUserDao.selectCountByCondition(Mockito.any())).thenReturn(1);
        List<String> tenantCodes = new ArrayList<>();
        tenantCodes.add("1");
        Integer i = Math.toIntExact(promoCouponCodeUserService.usedCouponAmount("1", "1", tenantCodes));
        Assert.assertEquals(1,i.intValue());
    }

    @Test
    public void usedCouponAmount1(){
        when(couponCodeUserDao.selectCountByCondition(Mockito.any())).thenReturn(1);
        List<String> tenantCodes = new ArrayList<>();
        tenantCodes.add("1");
        int i = Math.toIntExact(promoCouponCodeUserService.usedCouponAmount("", "", new ArrayList<>()));
        Assert.assertEquals(1, i);
    }

    @Test
    public void getReceivedCouponAmount(){
        when(couponCodeUserDao.selectCountByCondition(Mockito.any())).thenReturn(1);
        ActivityTenantInDTO inDTO = new ActivityTenantInDTO();
        inDTO.setTenantCode("1");
        int i = promoCouponCodeUserService.getReceivedCouponAmount(inDTO);
        Assert.assertEquals(1,i);
    }

    @Test
    public void getReceivedCouponAmount1(){
        when(couponCodeUserDao.selectCountByCondition(Mockito.any())).thenReturn(1);
        ActivityTenantInDTO inDTO = new ActivityTenantInDTO();
        inDTO.setTenantCode("1");
        inDTO.setEndTime("1");
        inDTO.setStartTime("1");
        int i = promoCouponCodeUserService.getReceivedCouponAmount(inDTO);
        Assert.assertEquals(1,i);
    }

    @Test
    public void getUsedCouponAmount(){
        when(couponCodeUserDao.selectCountByCondition(Mockito.any())).thenReturn(1);
        ActivityTenantInDTO inDTO = new ActivityTenantInDTO();
        inDTO.setTenantCode("1");
        int i = promoCouponCodeUserService.getUsedCouponAmount(inDTO);
        Assert.assertEquals(1,i);
    }

    @Test
    public void getUsedCouponAmount1(){
        when(couponCodeUserDao.selectCountByCondition(Mockito.any())).thenReturn(1);
        ActivityTenantInDTO inDTO = new ActivityTenantInDTO();
        inDTO.setTenantCode("1");
        inDTO.setEndTime("1");
        inDTO.setStartTime("1");
        int i = promoCouponCodeUserService.getUsedCouponAmount(inDTO);
        Assert.assertEquals(1,i);
    }

    @Test
    public void getUserCouponCode(){
        promoCouponCodeUserService.getUserCouponCode("1","1");
    }

    @Test
    public void queryManagementUserData(){
        when(couponCodeUserDao.selectByCondition(Mockito.any())).thenReturn(new ArrayList<>());
        ManagementDataInDTO inDTO = new ManagementDataInDTO();
        inDTO.setTenantCode("1");
        inDTO.setCouponType("01");
        inDTO.setStatus("01");
        inDTO.setCouponCode("1");
        PageData<TPromoCouponCodeUserVO> pageData = promoCouponCodeUserService.queryManagementUserData(inDTO);
        Assert.assertEquals(0,pageData.getTotal().intValue());
    }

    @Test
    public void updateUserByCouponCode(){
        when(couponCodeUserDao.updateByConditionSelective(Mockito.any(),Mockito.any())).thenReturn(1);
        TCouponCheckAndUseInDTO inDTO = new TCouponCheckAndUseInDTO();
        inDTO.setTenantCode("1");
        int i = promoCouponCodeUserService.updateUserByCouponCode(inDTO);
        Assert.assertEquals(1,i);
    }

    @Test
    public void findUserByCouponCode(){
        TPromoCouponCodeUserEntity entity = new TPromoCouponCodeUserEntity();
        when(couponCodeUserDao.selectOne(Mockito.any())).thenReturn(entity);
        TCouponCheckAndUseInDTO inDTO = new TCouponCheckAndUseInDTO();
        inDTO.setTenantCode("1");
        TPromoCouponCodeUserVO i = promoCouponCodeUserService.findUserByCouponCode(inDTO);
        Assert.assertEquals(null,i.getTenantCode());
    }

    @Test
    public void queryManagementCouponCodeData(){
        when(couponCodeUserDao.selectByCondition(Mockito.any())).thenReturn(new ArrayList<>());
        ManagementDataInDTO inDTO = new ManagementDataInDTO();
        inDTO.setTenantCode("1");
        inDTO.setCouponType("01");
        inDTO.setStatus("01");
        inDTO.setCouponCode("1");
        inDTO.setReceiveStartTime("1");
        inDTO.setReceiveEndTime("1");
        inDTO.setUsedStartTime("1");
        inDTO.setUsedEndTime("1");
        inDTO.setUsedRefId("1");
        inDTO.setUserCode("1");
        inDTO.setIsUseStatus("1");
        PageData<TPromoCouponCodeUserVO> pageData = promoCouponCodeUserService.queryManagementCouponCodeData(inDTO);
        Assert.assertEquals(0,pageData.getTotal().intValue());
    }

    @Test
    public void queryManagementCouponCodeData1(){
        when(couponCodeUserDao.selectByCondition(Mockito.any())).thenReturn(new ArrayList<>());
        ManagementDataInDTO inDTO = new ManagementDataInDTO();
        inDTO.setTenantCode("1");
        inDTO.setCouponType("01");
        inDTO.setStatus("01");
        inDTO.setCouponCode("1");
        inDTO.setReceiveEndTime("1");
        inDTO.setUsedStartTime("1");
        inDTO.setUsedRefId("1");
        inDTO.setUserCode("1");
        inDTO.setIsUseStatus("1");
        PageData<TPromoCouponCodeUserVO> pageData = promoCouponCodeUserService.queryManagementCouponCodeData(inDTO);
        Assert.assertEquals(0,pageData.getTotal().intValue());
    }

    @Test
    public void queryUsedCouponListService(){
        when(couponCodeUserDao.selectByCondition(Mockito.any())).thenReturn(new ArrayList<>());
        QueryCouponUsedInDTO inDTO = new QueryCouponUsedInDTO();
        inDTO.setTenantCode("1");
        inDTO.setBeginTime("2020-02-21 16:40:58");
        inDTO.setEndTime("2021-07-23 11:21:21");
        inDTO.setCouponCode("1");
        inDTO.setUserCode("1");
        List<String> add = new ArrayList<>();
        add.add("01");
        inDTO.setStatus(add);
        PageData<TPromoCouponCodeUserVO> pageData = promoCouponCodeUserService.queryUsedCouponListService(inDTO);
        Assert.assertEquals(0,pageData.getTotal().intValue());
    }

    @Test
    public void queryUsedCouponListService1(){
        when(couponCodeUserDao.selectByCondition(Mockito.any())).thenReturn(new ArrayList<>());
        QueryCouponUsedInDTO inDTO = new QueryCouponUsedInDTO();
        List<String> add = new ArrayList<>();
        add.add("01");
        inDTO.setStatus(add);
        PageData<TPromoCouponCodeUserVO> pageData = promoCouponCodeUserService.queryUsedCouponListService(inDTO);
        Assert.assertEquals(0,pageData.getTotal().intValue());
    }

    @Test
    public void testUpdateUsedRedIdByCodes(){
        // given
        String tenantCode = "8";
        List<String> couponCodes = new ArrayList<>();
        couponCodes.add("1");
        String userCode = "8";
        String usedRefId = "8";
        // when
//        when(couponCodeUserDao.updateByConditionSelective(any(), any())).thenReturn(1);
        // then
        int i = promoCouponCodeUserService.updateUsedRedIdByCodes(tenantCode, couponCodes, userCode, usedRefId, CouponStatusEnum.USED);
        Assert.assertEquals(0, i);
    }

    @Test
    public void testUpdateUsedRedIdByCodes_return_2(){
        // given
        String tenantCode = "8";
        List<String> couponCodes = new ArrayList<>();
        couponCodes.add("1");
        couponCodes.add("2");
        String userCode = "8";
        String usedRefId = "8";
        // when
//        when(couponCodeUserDao.updateByConditionSelective(any(), any())).thenReturn(2);
        // then
        int i = promoCouponCodeUserService.updateUsedRedIdByCodes(tenantCode, couponCodes, userCode, usedRefId, CouponStatusEnum.USED);
        Assert.assertEquals(0, i);
    }

    @Test
    public void testUpdateUsedRedIdByCodes_return_0(){
        // given
        String tenantCode = "8";
        List<String> couponCodes = new ArrayList<>();
        String userCode = "8";
        String usedRefId = "8";
        // then
        int i = promoCouponCodeUserService.updateUsedRedIdByCodes(tenantCode, couponCodes, userCode, usedRefId, CouponStatusEnum.USED);
        Assert.assertEquals(0, i);
    }

    @Test
    public void testClearUsedRefIdByUsedRefId(){
        // given
        String usedRefId = "8";
        String status = "01";
        // when
//        when(couponCodeUserDao.updateByConditionSelective(any(), any())).thenReturn(1);
        // then
        promoCouponCodeUserService.clearUsedRefIdByUsedRefId(usedRefId, status);
    }


    @Test
    public void getCodeUserByUsedRefId(){
        // given
        String usedRefId = "8";
        String status = "01";
        List<TPromoCouponCodeUserVO> codeUserByUsedRefId = promoCouponCodeUserService.getCodeUserByUsedRefId(usedRefId, status);
        Assert.assertEquals(0,codeUserByUsedRefId.size());
    }

    @Test
    public void testUpdateStatusBatch(){
        // given
        String tenantCode = "";
        List<String> couponCodes = new ArrayList<>();
        couponCodes.add("1");
        couponCodes.add("2");
        // when
//        when(couponCodeUserDao.updateByConditionSelective(any(), any())).thenReturn(2);
        // then
        int i = promoCouponCodeUserService.updateStatusBatch(tenantCode, couponCodes, CouponStatusEnum.USED);
        Assert.assertEquals(0, i);
    }

    @Test
    public void testGetUserUsedCouponCountByActivityCode(){
        // given
        String tenantCode = "3";
        String userCode = "3";
        String activityCode = "3";
        // when
//        when(couponCodeUserDao.selectCountByCondition(any())).thenReturn(2);
        // then
        int i = promoCouponCodeUserService.getUserUsedCouponCountByActivityCode(tenantCode, userCode, activityCode);
        Assert.assertEquals(0, i);
    }

    @Test
    public void testDeleteCodeUserByUserId(){
        // given
        String userId = "3";
        // when
//        when(couponCodeUserDao.deleteByPrimaryKey(any())).thenReturn(2);
        // then
        int i = promoCouponCodeUserService.deleteCodeUserByUserId(userId);
        Assert.assertEquals(0, i);
    }

    @Test
    public void testGetUserCouponCode(){
        // given
        String tenantCode = "3";
        String activityId = "3";
        String status = "3";
        String couponCode = "3";
        RequestPage page = new RequestPage();

        List<TPromoCouponCodeUserEntity> list = new ArrayList<>();
        // when
//        when(couponCodeUserDao.select(any())).thenReturn(list);
        // then
        PageInfo<TPromoCouponCodeUserVO> pageInfo = promoCouponCodeUserService.getUserCouponCode(tenantCode, activityId, status, couponCode, page);
        PageHelper.clearPage();
        Assert.assertEquals(0, pageInfo.getTotal());
    }

    @Test
    public void testGetUserCouponCode1(){
        // given
        String tenantCode = "3";
        String activityId = "3";
        String status = "";
        String couponCode = "";
        RequestPage page = new RequestPage();

        List<TPromoCouponCodeUserEntity> list = new ArrayList<>();
        // when
//        when(couponCodeUserDao.select(any())).thenReturn(list);
        // then
        PageInfo<TPromoCouponCodeUserVO> pageInfo = promoCouponCodeUserService.getUserCouponCode(tenantCode, activityId, status, couponCode, page);
        PageHelper.clearPage();
        Assert.assertEquals(0, pageInfo.getTotal());
    }

    @Test
    public void testGetUserCouponInfos(){
        // given
        String tenantCode = "3";
        String activityId = "3";
        String couponCode = "3";
        String status = "3";

        List<TPromoCouponCodeUserEntity> list = new ArrayList<>();

        // when
//        when(couponCodeUserDao.selectByCondition(any())).thenReturn(list);
        // then
        List<TPromoCouponCodeUserVO> userCouponInfos = promoCouponCodeUserService.getUserCouponInfos(tenantCode, activityId, couponCode, status);
        Assert.assertEquals(0, userCouponInfos.size());
    }

    @Test
    public void testGetUserCouponInfos1(){
        // given
        String tenantCode = "3";
        String activityId = "3";
        String couponCode = "";
        String status = "";

        List<TPromoCouponCodeUserEntity> list = new ArrayList<>();

        // when
//        when(couponCodeUserDao.selectByCondition(any())).thenReturn(list);
        // then
        List<TPromoCouponCodeUserVO> userCouponInfos = promoCouponCodeUserService.getUserCouponInfos(tenantCode, activityId, couponCode, status);
        Assert.assertEquals(0, userCouponInfos.size());
    }

    @Test
    public void testFrozenCouponCodeUser(){
        // given
        String tenantCode = "3";
        String couponCode = "3";
        // when
//        when(couponCodeUserDao.updateByConditionSelective(any(), any())).thenReturn(2);
        // then
        int i = promoCouponCodeUserService.frozenCouponCodeUser(tenantCode, couponCode,1,0);
        Assert.assertEquals(0, i);
    }

    @Test
    public void testFrozenCouponCodeUser1(){
        // given
        String tenantCode = "3";
        String couponCode = "3";
        // when
//        when(couponCodeUserDao.updateByConditionSelective(any(), any())).thenReturn(2);
        // then
        int i = promoCouponCodeUserService.frozenCouponCodeUser(tenantCode, couponCode,2,1);
        Assert.assertEquals(0, i);
    }

    @Test
    public void testGetUseCouponCountYesterday111(){
        // given
        String key = "3";
        String id = "3";
        // when
//        when(couponCodeUserDao.selectCountByCondition(any())).thenReturn(2);
        // then
        int i = promoCouponCodeUserService.getUseCouponCountYesterday111(key, id);
        Assert.assertEquals(0, i);
    }

    @Test
    public void testGetAllocateCouponCountToday111(){
        // given
        String tenantCode = "3";
        String id = "3";
        // when
//        when(couponCodeUserDao.selectCountByCondition(any())).thenReturn(2);
        // then
        int i = promoCouponCodeUserService.getAllocateCouponCountToday111(tenantCode, id);
        Assert.assertEquals(0, i);
    }

    @Test
    public void testGetUseCouponCountToday111(){
        // given
        String key = "3";
        String id = "3";
        // when
//        when(couponCodeUserDao.selectCountByCondition(any())).thenReturn(2);
        // then
        int i = promoCouponCodeUserService.getUseCouponCountToday111(key, id);
        Assert.assertEquals(0, i);
    }

    @Test
    public void testUsedCouponAmount(){
        List<String> list = new ArrayList<>();
        list.add("88002221");
        // given
        // when
//        when(couponCodeUserDao.selectCountByCondition(any())).thenReturn(2);
        // then
        long i = promoCouponCodeUserService.usedCouponAmount(list);
        Assert.assertEquals(0L, i);
    }

    @Test
    public void testUsedCouponAmount1(){
        List<String> list = new ArrayList<>();
        list.add("88002221");
        // given
        // when
//        when(couponCodeUserDao.selectCountByCondition(any())).thenReturn(2);
        // then
        long i = promoCouponCodeUserService.usedCouponAmount(new ArrayList<>());
        Assert.assertEquals(0L, i);
    }

    @Test
    public void getUserCouponByUserId(){
        Mockito.when(couponCodeUserDao.selectByCondition(Mockito.any())).thenReturn(new ArrayList<>());
        TPromoCouponCodeUserVO couponCodeUserVO = new TPromoCouponCodeUserVO();
		couponCodeUserVO.setUserCode("1");
		couponCodeUserVO.setTenantCode("1");
		couponCodeUserVO.setStatus("1");
		couponCodeUserVO.setTakeLabel("1");
        PageData<CouponDomain> pageData = promoCouponCodeUserService.getUserCouponByUserId(couponCodeUserVO, new RequestPage(),new ArrayList<>(),"1","1",0);
        Assert.assertEquals(0,pageData.getList().size());
    }

    @Test
    public void getUserCouponByUserId1(){
        TPromoCouponCodeUserVO couponCodeUserVO = new TPromoCouponCodeUserVO();
		couponCodeUserVO.setUserCode("1");
		couponCodeUserVO.setTenantCode("1");
		couponCodeUserVO.setStatus("2");
		couponCodeUserVO.setTakeLabel("1");
        Mockito.when(couponCodeUserDao.selectByCondition(Mockito.any())).thenReturn(new ArrayList<>());
        PageData<CouponDomain> pageData = promoCouponCodeUserService.getUserCouponByUserId(couponCodeUserVO, new RequestPage(),new ArrayList<>(),"1","1",0);
        Assert.assertEquals(0,pageData.getList().size());
    }

    @Test
    public void getUserCouponByUserId2(){
        TPromoCouponCodeUserVO couponCodeUserVO = new TPromoCouponCodeUserVO();
		couponCodeUserVO.setUserCode("1");
		couponCodeUserVO.setTenantCode("1");
		couponCodeUserVO.setStatus("2");
		couponCodeUserVO.setTakeLabel("1");
        List<String> opsList = new ArrayList<>();
        opsList.add("1");
        Mockito.when(couponCodeUserDao.selectByCondition(Mockito.any())).thenReturn(new ArrayList<>());
        PageData<CouponDomain> pageData = promoCouponCodeUserService.getUserCouponByUserId(couponCodeUserVO, new RequestPage(),opsList,"1","1",1);
        Assert.assertEquals(0,pageData.getList().size());
    }

    @Test
    public void getUserCouponByUserId3(){
        TPromoCouponCodeUserVO couponCodeUserVO = new TPromoCouponCodeUserVO();
        couponCodeUserVO.setUserCode("1");
        couponCodeUserVO.setTenantCode("1");
        couponCodeUserVO.setStatus("3");
        couponCodeUserVO.setTakeLabel("1");
        List<String> opsList = new ArrayList<>();
        opsList.add("1");
        Mockito.when(couponCodeUserDao.selectByCondition(Mockito.any())).thenReturn(new ArrayList<>());
        PageData<CouponDomain> pageData = promoCouponCodeUserService.getUserCouponByUserId(couponCodeUserVO, new RequestPage(),opsList,"1","1",1);
        Assert.assertEquals(0,pageData.getList().size());
    }

    @Test
    public void getUserCouponInfo(){
        Mockito.when(couponCodeUserDao.selectOne(Mockito.any())).thenReturn(new TPromoCouponCodeUserEntity());
        TPromoCouponCodeUserVO userCouponInfo = promoCouponCodeUserService.getUserCouponInfo("1", "1", "1");
        Assert.assertNull(userCouponInfo.getCouponCode());
    }

    @Test
    public void getUserCouponInfo1(){
        Mockito.when(couponCodeUserDao.selectOne(Mockito.any())).thenReturn(new TPromoCouponCodeUserEntity());
        TPromoCouponCodeUserVO userCouponInfo = promoCouponCodeUserService.getUserCouponInfo("1", "1", null);
        Assert.assertNull(userCouponInfo.getCouponCode());
    }

    @Test
    public void getUserCouponCountByActivityCode(){
        Mockito.when(couponCodeUserDao.selectCount(Mockito.any())).thenReturn(1);
        int count = promoCouponCodeUserService.getUserCouponCountByActivityCode("1", "1", "1");
        Assert.assertEquals(1,count);
    }

    @Test
    public void insert(){
        List<TPromoCouponCodeUserVO> couponCodeUserList = new ArrayList<>();
        int i = promoCouponCodeUserService.insert(couponCodeUserList);
        Assert.assertEquals(0,i);
    }

    @Test
    public void insert1(){
        List<TPromoCouponCodeUserVO> couponCodeUserList = new ArrayList<>();
        TPromoCouponCodeUserVO userVO = new TPromoCouponCodeUserVO();
        couponCodeUserList.add(userVO);
        Mockito.when(couponCodeUserDao.insertSelective(Mockito.any())).thenReturn(1);
        int i = promoCouponCodeUserService.insert(couponCodeUserList);
        Assert.assertEquals(1,i);
    }

    @Test
    public void insert2(){
        List<TPromoCouponCodeUserVO> couponCodeUserList = new ArrayList<>();
        for (int i = 0; i < 2; i++) {
            TPromoCouponCodeUserVO userVO = new TPromoCouponCodeUserVO();
            couponCodeUserList.add(userVO);
        }
        Mockito.when(couponCodeUserDao.insertList(Mockito.any())).thenReturn(1);
        int i = promoCouponCodeUserService.insert(couponCodeUserList);
        Assert.assertEquals(1,i);
    }

    @Test
    public void insert3(){
        List<TPromoCouponCodeUserVO> couponCodeUserList = new ArrayList<>();
        for (int i = 0; i < 100; i++) {
            TPromoCouponCodeUserVO userVO = new TPromoCouponCodeUserVO();
            couponCodeUserList.add(userVO);
        }
        Mockito.when(couponCodeUserDao.insertList(Mockito.any())).thenReturn(1);
        int i = promoCouponCodeUserService.insert(couponCodeUserList);
        Assert.assertEquals(1,i);
    }
    @Test
    public void logicDelete(){
        Mockito.when(couponCodeUserDao.updateByConditionSelective(Mockito.any(),Mockito.any())).thenReturn(1);
        int i = promoCouponCodeUserService.logicDelete("1", "1");
        Assert.assertEquals(1,i);
    }

    @Test
    public void exportCouponUserCode(){
        promoCouponCodeUserService.exportCouponUserCode(new ExportCouponUserDto());
    }

    @Test
    public void exportCouponOrderCode(){
        promoCouponCodeUserService.exportCouponOrderCode(new ExportCouponUserDto());
    }

    @Test
    public void queryUserCouponByUserId(){
        Mockito.when(couponCodeUserDao.selectByCondition(Mockito.any())).thenReturn(new ArrayList<>());
        TPromoCouponCodeUserVO couponCodeUserVO = new TPromoCouponCodeUserVO();
        couponCodeUserVO.setUserCode("1");
        couponCodeUserVO.setTenantCode("1");
        couponCodeUserVO.setStatus("1");
        couponCodeUserVO.setTakeLabel("1");
        QueryUserCouponVO userCouponVO = new QueryUserCouponVO();
        userCouponVO.setSort(1);
        userCouponVO.setPageCount(10);
        userCouponVO.setPageNo(1);
        userCouponVO.setBeginTime("2011111111");
        userCouponVO.setEndTime("111111111111");
        List<String> ops = new ArrayList<>();
        ops.add("!");
        userCouponVO.setOpsList(ops);
        Mockito.when(couponCodeUserDao.selectByCondition(Mockito.any())).thenReturn(new ArrayList<>());
        PageData<CouponDomain> pageData = promoCouponCodeUserService.queryUserCouponByUserId(couponCodeUserVO, userCouponVO);
        Assert.assertEquals(0,pageData.getList().size());
    }

    @Test
    public void queryUserCouponByUserId1(){
        TPromoCouponCodeUserVO couponCodeUserVO = new TPromoCouponCodeUserVO();
        couponCodeUserVO.setUserCode("1");
        couponCodeUserVO.setTenantCode("1");
        couponCodeUserVO.setStatus("2");
        couponCodeUserVO.setTakeLabel("1");

        QueryUserCouponVO userCouponVO = new QueryUserCouponVO();

        userCouponVO.setSort(1);
        userCouponVO.setPageCount(10);
        userCouponVO.setPageNo(1);
        userCouponVO.setBeginTime("2011111111");
        userCouponVO.setEndTime("111111111111");
        List<String> ops = new ArrayList<>();
        ops.add("!");
        userCouponVO.setOpsList(ops);
        Mockito.when(couponCodeUserDao.selectByCondition(Mockito.any())).thenReturn(new ArrayList<>());
        PageData<CouponDomain> pageData = promoCouponCodeUserService.queryUserCouponByUserId(couponCodeUserVO, userCouponVO);
        Assert.assertEquals(0,pageData.getList().size());
    }

    @Test
    public void queryUserCouponByUserId2(){
        TPromoCouponCodeUserVO couponCodeUserVO = new TPromoCouponCodeUserVO();
        couponCodeUserVO.setUserCode("1");
        couponCodeUserVO.setTenantCode("1");
        couponCodeUserVO.setStatus("3");
        couponCodeUserVO.setTakeLabel("1");
        QueryUserCouponVO userCouponVO = new QueryUserCouponVO();

        userCouponVO.setSort(1);
        userCouponVO.setPageCount(10);
        userCouponVO.setPageNo(1);
        userCouponVO.setBeginTime("2011111111");
        userCouponVO.setEndTime("111111111111");
        List<String> ops = new ArrayList<>();
        ops.add("!");
        userCouponVO.setOpsList(ops);
        Mockito.when(couponCodeUserDao.selectByCondition(Mockito.any())).thenReturn(new ArrayList<>());
        PageData<CouponDomain> pageData = promoCouponCodeUserService.queryUserCouponByUserId(couponCodeUserVO, userCouponVO);
        Assert.assertEquals(0,pageData.getList().size());
    }


    @Test
    public void queryUserCouponByUserId4(){
        TPromoCouponCodeUserVO couponCodeUserVO = new TPromoCouponCodeUserVO();
        couponCodeUserVO.setUserCode("1");
        couponCodeUserVO.setTenantCode("1");
        couponCodeUserVO.setStatus("3");
        couponCodeUserVO.setTakeLabel("");
        QueryUserCouponVO userCouponVO = new QueryUserCouponVO();

        userCouponVO.setSort(1);
        userCouponVO.setPageCount(10);
        userCouponVO.setPageNo(1);
        userCouponVO.setBeginTime("");
        userCouponVO.setEndTime("");
        List<String> ops = new ArrayList<>();
        userCouponVO.setOpsList(ops);
        Mockito.when(couponCodeUserDao.selectByCondition(Mockito.any())).thenReturn(new ArrayList<>());
        PageData<CouponDomain> pageData = promoCouponCodeUserService.queryUserCouponByUserId(couponCodeUserVO, userCouponVO);
        Assert.assertEquals(0,pageData.getList().size());
    }

    @Test
    public void queryUserCouponByUserId_status_empty(){
        Mockito.when(couponCodeUserDao.selectByCondition(Mockito.any())).thenReturn(new ArrayList<>());
        TPromoCouponCodeUserVO couponCodeUserVO = new TPromoCouponCodeUserVO();
        couponCodeUserVO.setUserCode("1");
        couponCodeUserVO.setTenantCode("1");
        couponCodeUserVO.setTakeLabel("1");
        QueryUserCouponVO userCouponVO = new QueryUserCouponVO();
        userCouponVO.setSort(1);
        userCouponVO.setPageCount(10);
        userCouponVO.setPageNo(1);
        userCouponVO.setBeginTime("2011111111");
        userCouponVO.setEndTime("111111111111");
        List<String> ops = new ArrayList<>();
        ops.add("!");
        userCouponVO.setOpsList(ops);
        Mockito.when(couponCodeUserDao.selectByCondition(Mockito.any())).thenReturn(new ArrayList<>());
        PageData<CouponDomain> pageData = promoCouponCodeUserService.queryUserCouponByUserId(couponCodeUserVO, userCouponVO);
        Assert.assertEquals(0,pageData.getList().size());
    }

    @Test
    public void queryUserCouponInfo(){

        promoCouponCodeUserService.queryUserCouponInfo("1", Arrays.asList("11"));
    }

    @Test
    public void updateCouponUserByCode(){

        List<TPromoCouponCodeUserVO> userVO = new ArrayList<>();
        promoCouponCodeUserService.updateCouponUserByCode(userVO);
    }


}
