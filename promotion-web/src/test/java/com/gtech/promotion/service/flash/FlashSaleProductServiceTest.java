package com.gtech.promotion.service.flash;

import com.gtech.promotion.dao.entity.marketing.flashsale.FlashSaleProductEntity;
import com.gtech.promotion.dao.mapper.marketing.flashsale.FlashSaleProductMapper;
import com.gtech.promotion.dao.model.marketing.flashsale.FlashSaleProductModel;
import com.gtech.promotion.service.flashsale.impl.FlashSaleProductServiceImpl;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import tk.mybatis.mapper.mapperhelper.EntityHelper;
import tk.mybatis.mapper.mapperhelper.MapperHelper;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021-09-18 10:50
 */
@RunWith(MockitoJUnitRunner.class)
public class FlashSaleProductServiceTest {
    @InjectMocks
    private FlashSaleProductServiceImpl flashSaleProductService;
    @Mock
    private FlashSaleProductMapper productMapper;

    @Before
    public void before(){
        EntityHelper.initEntityNameMap(FlashSaleProductEntity.class, new MapperHelper().getConfig());
    }

    @Test
    public void getProductsByActivityCodes(){
        flashSaleProductService.getProductsByActivityCodes("1",new ArrayList<>());
    }

    @Test
    public void getProductsByActivityCodesAndProducts(){

        List<String> activityCodes = new ArrayList<>();
        activityCodes.add("!");
        List<String> spuCodes = new ArrayList<>();
        spuCodes.add("!");
        flashSaleProductService.getProductsByActivityCodesAndProducts("1",activityCodes,spuCodes);
    }

    @Test
    public void getProductsByActivityCodesAndProductsBySpu(){

        List<String> activityCodes = new ArrayList<>();
        activityCodes.add("!");
        List<String> spuCodes = new ArrayList<>();
        spuCodes.add("!");
        flashSaleProductService.getProductsByActivityCodesAndProductsBySpu("1",activityCodes,spuCodes);
    }

    @Test
    public void getProductsByActivityCodesAndProductsBySpu_not_empty() {
        List<String> activityCodes = new ArrayList<>();
        activityCodes.add("!");
        List<String> spuCodes = new ArrayList<>();
        spuCodes.add("!");

        flashSaleProductService.getProductsByActivityCodesAndProductsBySpu("1", activityCodes, spuCodes);
    }

    public void getProductsByActivityCodesAndProducts_not_empty(){

        List<String> activityCodes = new ArrayList<>();
        activityCodes.add("1");
        List<String> skuCodes = new ArrayList<>();
        skuCodes.add("1");
        flashSaleProductService.getProductsByActivityCodesAndProducts("1",activityCodes,skuCodes);
    }

    public void getProductsByActivityCodesAndProducts_not_emptyBySpu(){

        List<String> activityCodes = new ArrayList<>();
        activityCodes.add("1");
        List<String> skuCodes = new ArrayList<>();
        skuCodes.add("1");
        flashSaleProductService.getProductsByActivityCodesAndProductsBySpu("1",activityCodes,skuCodes);
    }

    @Test
    public void findByActivityAndSku(){
        flashSaleProductService.findByActivityAndSku("1","1");
    }

    @Test
    public void findByActivityAndProduct(){
        flashSaleProductService.findByActivityAndProduct("1","1");
    }

    @Test
    public void dealInventory(){
        flashSaleProductService.dealInventory("1","1",1);
    }

    @Test
    public void updateActivityCodeAndOrgCode(){
        flashSaleProductService.updateActivityCodeAndOrgCode("1","1","1");
    }

    @Test
    public void dealInventoryBySpu(){
        flashSaleProductService.dealInventoryBySpu("!","1",1);
    }

    @Test
    public void findByActivityAndSkuList(){
        List<FlashSaleProductModel> flashSaleProductModels = flashSaleProductService.findByActivityAndSkuList("1",new ArrayList<>());
        Assert.assertEquals(0,flashSaleProductModels.size());
    }

    @Test
    public void queryProductsByActivityCodesAndSkuCode(){
        List<FlashSaleProductModel> flashSaleProductModels = flashSaleProductService.queryProductsByActivityCodesAndSkuCode(new ArrayList<>(), "1");

        Assert.assertEquals(0,flashSaleProductModels.size());
    }
}
