package com.gtech.promotion.service.activity;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import com.gtech.promotion.dao.entity.activity.TPromoActivityLanguageEntity;
import com.gtech.promotion.dao.entity.activity.TPromoIncentiveLimitedEntity;
import com.gtech.promotion.dao.mapper.activity.LuckyDrawIncentiveLimitedMapper;
import com.gtech.promotion.dao.mapper.activity.TPromoActivityLanguageMapper;
import com.gtech.promotion.dao.mapper.activity.TPromoIncentiveLimitedMapper;
import com.gtech.promotion.dao.model.activity.ActivityLanguageModel;
import com.gtech.promotion.dao.model.activity.ActivityModel;
import com.gtech.promotion.dao.model.activity.TPromoIncentiveLimitedVO;
import com.gtech.promotion.exception.PromotionException;
import com.gtech.promotion.service.impl.activity.TPromoActivityLanguageServiceImpl;
import com.gtech.promotion.service.impl.activity.TPromoIncentiveLimitedServiceImpl;

import tk.mybatis.mapper.mapperhelper.EntityHelper;
import tk.mybatis.mapper.mapperhelper.MapperHelper;

/**
 * @Description :
 * <AUTHOR> gaojie.li
 * @Date : 2021/9/18 14:10
 */
@RunWith(MockitoJUnitRunner.class)
public class TPromoActivityLanguageServiceTest {
    @InjectMocks
    private TPromoActivityLanguageServiceImpl tPromoActivityLanguageService;

    @InjectMocks
    private TPromoIncentiveLimitedServiceImpl tPromoIncentiveLimitedService;

    @Mock
    private TPromoIncentiveLimitedMapper limitedMappper;

    @Mock
    private LuckyDrawIncentiveLimitedMapper luckyDrawIncentiveLimitedMapper;

    @Mock
    private TPromoActivityLanguageMapper languageMapper;

    @Before
    public void before() {
        EntityHelper.initEntityNameMap(TPromoActivityLanguageEntity.class, new MapperHelper().getConfig());
        EntityHelper.initEntityNameMap(TPromoIncentiveLimitedEntity.class, new MapperHelper().getConfig());


    }
    @Test
    public void insertLuckyDrawLimitedList_notEmpty(){
        List<TPromoIncentiveLimitedVO> limitedVOList = new ArrayList<>();
        Integer integer = tPromoIncentiveLimitedService.insertLuckyDrawLimitedList("1", limitedVOList, "1");
        Assert.assertEquals(0,integer.intValue());
    }

    @Test(expected = PromotionException.class)
    public void insertLuckyDrawLimitedList_null_1(){
        List<TPromoIncentiveLimitedVO> limitedVOList = new ArrayList<>();
        TPromoIncentiveLimitedVO vo = new TPromoIncentiveLimitedVO();
        vo.setLimitationValue(null);
        limitedVOList.add(vo);
        Integer integer = tPromoIncentiveLimitedService.insertLuckyDrawLimitedList("1", limitedVOList, "1");
        Assert.assertEquals(0,integer.intValue());
    }

    @Test(expected = PromotionException.class)
    public void insertLuckyDrawLimitedList_null_2(){
        List<TPromoIncentiveLimitedVO> limitedVOList = new ArrayList<>();
        TPromoIncentiveLimitedVO vo = new TPromoIncentiveLimitedVO();
        vo.setLimitationValue(new BigDecimal(-1));
        limitedVOList.add(vo);
        Integer integer = tPromoIncentiveLimitedService.insertLuckyDrawLimitedList("1", limitedVOList, "1");
        Assert.assertEquals(0,integer.intValue());
    }


    @Test(expected = PromotionException.class)
    public void insertLuckyDrawLimitedList_null_3(){
        List<TPromoIncentiveLimitedVO> limitedVOList = new ArrayList<>();
        TPromoIncentiveLimitedVO vo = new TPromoIncentiveLimitedVO();
        String value="111111111111111111111111111111111111";
        vo.setLimitationValue(new BigDecimal(value));
        limitedVOList.add(vo);
        Integer integer = tPromoIncentiveLimitedService.insertLuckyDrawLimitedList("1", limitedVOList, "1");
        Assert.assertEquals(0,integer.intValue());
    }


    @Test(expected = PromotionException.class)
    public void insertLuckyDrawLimitedList_not_code(){
        List<TPromoIncentiveLimitedVO> limitedVOList = new ArrayList<>();
        TPromoIncentiveLimitedVO vo = new TPromoIncentiveLimitedVO();
        vo.setLimitationValue(new BigDecimal(11));
        vo.setLimitationCode("111111");
        limitedVOList.add(vo);
        Integer integer = tPromoIncentiveLimitedService.insertLuckyDrawLimitedList("1", limitedVOList, "1");
        Assert.assertEquals(0,integer.intValue());
    }


    @Test
    public void insertLuckyDrawLimitedList(){
        List<TPromoIncentiveLimitedVO> limitedVOList = new ArrayList<>();
        Integer integer = tPromoIncentiveLimitedService.insertLuckyDrawLimitedList("1", limitedVOList, "1");
        Assert.assertEquals(0,integer.intValue());
    }


    @Test
    public void insertLimitedList111_0(){
        List<TPromoIncentiveLimitedVO> limitedVOList = new ArrayList<>();
        Integer integer = tPromoIncentiveLimitedService.insertLimitedList111("1", limitedVOList, "1");
        Assert.assertEquals(0,integer.intValue());
    }


    @Test(expected = PromotionException.class)
    public void insertLimitedList111_null_1(){
        List<TPromoIncentiveLimitedVO> limitedVOList = new ArrayList<>();
        TPromoIncentiveLimitedVO vo = new TPromoIncentiveLimitedVO();
        vo.setLimitationValue(null);
        limitedVOList.add(vo);
        Integer integer = tPromoIncentiveLimitedService.insertLimitedList111("1", limitedVOList, "1");
        Assert.assertEquals(0,integer.intValue());
    }

    @Test(expected = PromotionException.class)
    public void insertLimitedList111_null_2(){
        List<TPromoIncentiveLimitedVO> limitedVOList = new ArrayList<>();
        TPromoIncentiveLimitedVO vo = new TPromoIncentiveLimitedVO();
        vo.setLimitationValue(new BigDecimal(-1));
        limitedVOList.add(vo);
        Integer integer = tPromoIncentiveLimitedService.insertLimitedList111("1", limitedVOList, "1");
        Assert.assertEquals(0,integer.intValue());
    }


    @Test(expected = PromotionException.class)
    public void insertLimitedList111_null_3(){
        List<TPromoIncentiveLimitedVO> limitedVOList = new ArrayList<>();
        TPromoIncentiveLimitedVO vo = new TPromoIncentiveLimitedVO();
        String value="111111111111111111111111111111111111";
        vo.setLimitationValue(new BigDecimal(value));
        limitedVOList.add(vo);
        Integer integer = tPromoIncentiveLimitedService.insertLimitedList111("1", limitedVOList, "1");
        Assert.assertEquals(0,integer.intValue());
    }


    @Test(expected = PromotionException.class)
    public void insertLimitedList111_not_code(){
        List<TPromoIncentiveLimitedVO> limitedVOList = new ArrayList<>();
        TPromoIncentiveLimitedVO vo = new TPromoIncentiveLimitedVO();
        vo.setLimitationValue(new BigDecimal(11));
        vo.setLimitationCode("111111");
        limitedVOList.add(vo);
        Integer integer = tPromoIncentiveLimitedService.insertLimitedList111("1", limitedVOList, "1");
        Assert.assertEquals(0,integer.intValue());
    }

    @Test
    public void replaceField() {
        ActivityModel activityModel = new ActivityModel();
        activityModel.setTenantCode("1");
        activityModel.setActivityCode("1");
        Mockito.when(languageMapper.selectOne(Mockito.any())).thenReturn(new TPromoActivityLanguageEntity());
        tPromoActivityLanguageService.replaceField(activityModel, "zh-cn");
    }

    @Test
    public void findActivityLanguageNull() {
        ActivityLanguageModel activityLanguage = tPromoActivityLanguageService.findActivityLanguage(null, null, "zh-cn");
        Assert.assertEquals(null, activityLanguage);
    }

    @Test
    public void findActivityLanguage() {
        Mockito.when(languageMapper.selectOne(Mockito.any())).thenReturn(new TPromoActivityLanguageEntity());
        ActivityLanguageModel activityLanguage = tPromoActivityLanguageService.findActivityLanguage("1", "1", "zh-cn");
        Assert.assertEquals(null, activityLanguage.getTenantCode());
    }

    @Test
    public void queryActivityLanguagesByActivityCodes() {
        List<String> activityCodes = new ArrayList<>();
        activityCodes.add("1");
        Mockito.when(languageMapper.selectByCondition(Mockito.any())).thenReturn(new ArrayList<>());
		List<ActivityLanguageModel> activityLanguageModels = tPromoActivityLanguageService.queryActivityLanguagesByActivityCodes("1", null, activityCodes);
        Assert.assertEquals(0, activityLanguageModels.size());
    }

}
