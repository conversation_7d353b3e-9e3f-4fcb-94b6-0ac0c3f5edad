package com.gtech.promotion.service;

import com.gtech.promotion.dao.entity.activity.TPromoActivityStatisticEntity;
import com.gtech.promotion.dao.mapper.activity.TPromoActivityStatisticMapper;
import com.gtech.promotion.dto.in.activity.QueryActivityStatisticInDTO;
import com.gtech.promotion.dto.in.activity.QueryActivityStatisticSumInDTO;
import com.gtech.promotion.dto.in.activity.TPromoActivityStatisticInDTO;
import com.gtech.promotion.dto.out.activity.ActivityStatisticSumQueryOutDTO;
import com.gtech.promotion.service.impl.activity.TPromoActivityStatisticServiceImpl;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import tk.mybatis.mapper.mapperhelper.EntityHelper;
import tk.mybatis.mapper.mapperhelper.MapperHelper;

import java.util.ArrayList;
import java.util.List;

/**
 * @Description :
 * <AUTHOR> gaojie.li
 * @Date : 2021/8/4 15:06
 */
@RunWith(MockitoJUnitRunner.class)
public class TPromoActivityStatisticServiceTest {
    @InjectMocks
    private TPromoActivityStatisticServiceImpl tPromoActivitySettingService;

    @Mock
    private TPromoActivityStatisticMapper tPromoActivityStatisticMapper;

    @Before
    public void before() {
        EntityHelper.initEntityNameMap(TPromoActivityStatisticEntity.class, new MapperHelper().getConfig());
    }

    @Test
    public void queryActivityStatistic(){
        QueryActivityStatisticInDTO dto = new QueryActivityStatisticInDTO();
        dto.setStartTime("20210804");
        dto.setEndTime("20210804");
        Mockito.when(tPromoActivityStatisticMapper.selectByCondition(Mockito.any())).thenReturn(new ArrayList<>());
        List<TPromoActivityStatisticEntity> entities = tPromoActivitySettingService.queryActivityStatistic(dto);
        Assert.assertEquals(0,entities.size());
    }

    @Test
    public void createActivityStatisticBatch(){
        List<TPromoActivityStatisticInDTO> dtos = new ArrayList<>();
        TPromoActivityStatisticInDTO dto = new TPromoActivityStatisticInDTO();
        dtos.add(dto);
        Mockito.when(tPromoActivityStatisticMapper.insertSelective(Mockito.any())).thenReturn(1);
        tPromoActivitySettingService.createActivityStatisticBatch(dtos);
    }

    @Test
    public void queryActivityStatisticSum(){
        ActivityStatisticSumQueryOutDTO outDTO = new ActivityStatisticSumQueryOutDTO();
        QueryActivityStatisticSumInDTO dto = new QueryActivityStatisticSumInDTO();
        Mockito.when(tPromoActivityStatisticMapper.queryActivityStatisticSum(Mockito.any())).thenReturn(outDTO);
        ActivityStatisticSumQueryOutDTO outDTO1 = tPromoActivitySettingService.queryActivityStatisticSum(dto);
        Assert.assertEquals(null,outDTO1.getMemberCount());
    }
}
