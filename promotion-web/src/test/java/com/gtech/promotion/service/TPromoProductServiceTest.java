package com.gtech.promotion.service;

import com.gtech.promotion.dao.mongo.activity.ActivityProductEntity;
import com.gtech.promotion.service.impl.mongo.activity.TPromoProductServiceImpl;
import com.mongodb.client.result.DeleteResult;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.data.mongodb.core.MongoTemplate;

import java.util.ArrayList;
import java.util.List;

@RunWith(MockitoJUnitRunner.class)
public class TPromoProductServiceTest {

    @InjectMocks
    private TPromoProductServiceImpl tPromoProductService;

    @Mock
    private MongoTemplate mongoTemplate;

    @Test
    public void testInsertProducts_null(){
        String tenantCode = "1";
        String activityCode = "1";
        Integer integer = tPromoProductService.insertProducts(null, activityCode, tenantCode);
        Assert.assertEquals(0, integer.intValue());
    }

    @Test
    public void testUpdateActivityId2ActivityCode_null(){
        String tenantCode = "1";
        String activityId = "1";
        String activityCode = "1";
        List<Object> objects = null;
        Mockito.when(mongoTemplate.find(Mockito.any(), Mockito.any())).thenReturn(null);
        tPromoProductService.updateActivityId2ActivityCode(tenantCode, activityId, activityCode);
    }

    @Test
    public void testUpdateActivityId2ActivityCode(){
        String tenantCode = "1";
        String activityId = "1";
        String activityCode = "1";
        List<ActivityProductEntity> list = new ArrayList<>();
        ActivityProductEntity entity = new ActivityProductEntity();
        entity.setTenantCode(tenantCode);
        entity.setActivityCode(activityCode);
        list.add(entity);
        Mockito.when(mongoTemplate.find(Mockito.any(), Mockito.any(ActivityProductEntity.class.getClass()))).thenReturn(list);
        Mockito.when(mongoTemplate.insertAll(Mockito.any())).thenReturn(null);
        tPromoProductService.updateActivityId2ActivityCode(tenantCode, activityId, activityCode);
    }

    @Test
    public void testDeleteProductBySeqNum111(){
        String activityCode = "1";
        int seqNum = 1;
        DeleteResult deleteResult = DeleteResult.acknowledged(1L);
        Mockito.when(mongoTemplate.remove(Mockito.any(), Mockito.any(ActivityProductEntity.class.getClass()))).thenReturn(deleteResult);
        Integer integer = tPromoProductService.deleteProductBySeqNum111(activityCode, seqNum);
        Assert.assertEquals(1, integer.intValue());
    }
}
