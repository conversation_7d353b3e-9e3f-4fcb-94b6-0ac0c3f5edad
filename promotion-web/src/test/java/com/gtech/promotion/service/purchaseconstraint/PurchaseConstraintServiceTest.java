package com.gtech.promotion.service.purchaseconstraint;

import com.github.pagehelper.PageInfo;
import com.gtech.promotion.code.purchaseconstraint.PurchaseConstraintStatusEnum;
import com.gtech.promotion.dao.entity.purchaseconstraint.PurchaseConstraintEntity;
import com.gtech.promotion.dao.mapper.purchaseconstraint.PurchaseConstraintMapper;
import com.gtech.promotion.dao.model.purchaseconstraint.PurchaseConstraintModel;
import com.gtech.promotion.service.purchaseconstraint.impl.PurchaseConstraintServiceImpl;
import com.gtech.promotion.vo.bean.PurchaseConstraintPriority;
import com.gtech.promotion.vo.param.purchaseconstraint.query.QueryPurchaseConstraintListParam;
import org.apache.commons.collections4.CollectionUtils;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import tk.mybatis.mapper.mapperhelper.EntityHelper;
import tk.mybatis.mapper.mapperhelper.MapperHelper;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

@RunWith(MockitoJUnitRunner.class)
public class PurchaseConstraintServiceTest {
    @Before
    public void before() {
        EntityHelper.initEntityNameMap(PurchaseConstraintEntity.class, new MapperHelper().getConfig());
    }


    @InjectMocks
    private PurchaseConstraintServiceImpl purchaseConstraintService;

    @Mock
    private PurchaseConstraintMapper purchaseConstraintMapper;

    @Test
    public void queryList(){
        QueryPurchaseConstraintListParam queryPurchaseConstraintListParam = new QueryPurchaseConstraintListParam();
        queryPurchaseConstraintListParam.setPageNum(1);
        queryPurchaseConstraintListParam.setPageSize(10);
        Mockito.when(purchaseConstraintMapper.queryList(Mockito.any())).thenReturn(Collections.emptyList());
        PageInfo<PurchaseConstraintEntity> pageInfo = purchaseConstraintService.queryList(queryPurchaseConstraintListParam);
        Assert.assertEquals(0, pageInfo.getSize());
    }

    @Test
    public void getPurchaseConstraint(){
        String tenantCode = "111111";
        String purchaseConstraintCode  = "111111";

        PurchaseConstraintEntity purchaseConstraintEntity = new PurchaseConstraintEntity();
        purchaseConstraintEntity.setTenantCode(tenantCode);
        Mockito.when(purchaseConstraintMapper.selectOne(Mockito.any())).thenReturn(purchaseConstraintEntity);
        PurchaseConstraintModel result = purchaseConstraintService.getPurchaseConstraint(tenantCode, purchaseConstraintCode);
        Assert.assertEquals(purchaseConstraintEntity.getTenantCode(), result.getTenantCode());
    }

    @Test
    public void updatePurchaseConstraintStatus(){
        String tenantCode = "111111";
        String purchaseConstraintCode  = "111111";
        String purchaseConstraintStatus = PurchaseConstraintStatusEnum.IN_AUDIT.getCode();
        String operateUser = "11";

        Mockito.when(purchaseConstraintMapper.updateByConditionSelective(Mockito.any(), Mockito.any())).thenReturn(1);
        int result = purchaseConstraintService.updatePurchaseConstraintStatus(tenantCode,purchaseConstraintCode,purchaseConstraintStatus,operateUser);
        Assert.assertEquals(1, result);
    }


    @Test
    public void insert(){
        PurchaseConstraintModel purchaseConstraintModel = new PurchaseConstraintModel();
        purchaseConstraintService.insert(purchaseConstraintModel);
        Assert.assertTrue(Boolean.TRUE);
    }

    @Test
    public void updatePurchaseConstraintById(){
        PurchaseConstraintModel updatePurchaseConstraintModel = new PurchaseConstraintModel();
        Mockito.when(purchaseConstraintMapper.selectByPrimaryKey(Mockito.any())).thenReturn(new PurchaseConstraintEntity());
        int result = purchaseConstraintService.updatePurchaseConstraintById(updatePurchaseConstraintModel);
        Assert.assertEquals(0, result);
    }

    @Test
    public void updatePurchaseConstraintPriority(){
        List<PurchaseConstraintPriority> purchaseConstraintPriorities = new ArrayList<>();
        PurchaseConstraintPriority purchaseConstraintPriority = new PurchaseConstraintPriority();
        purchaseConstraintPriority.setPurchaseConstraintCode("1111");
        purchaseConstraintPriority.setPurchaseConstraintPriority(1);
        purchaseConstraintPriorities.add(purchaseConstraintPriority);

        String tenantCode = "11";
        Mockito.when(purchaseConstraintMapper.updatePurchaseConstraintPriority(Mockito.any(), Mockito.any(), Mockito.any()))
                .thenReturn(purchaseConstraintPriorities.size());
        int result = purchaseConstraintService.updatePurchaseConstraintPriority(tenantCode, purchaseConstraintPriorities);
        Assert.assertEquals(purchaseConstraintPriorities.size(), result);
    }

    @Test
    public void queryEffectivePurchaseConstraint(){
        String tenantCode = "11";
        List<PurchaseConstraintModel> result = purchaseConstraintService.queryEffectivePc(tenantCode);
        Assert.assertTrue(CollectionUtils.isEmpty(result));
    }
}
