package com.gtech.promotion.service.growth;

import com.gtech.commons.redis.GTechCodeGenerator;
import com.gtech.commons.result.PageResult;
import com.gtech.promotion.dao.entity.growth.GrowthAccountEntity;
import com.gtech.promotion.dao.mapper.growth.GrowthAccountMapper;
import com.gtech.promotion.service.growth.impl.GrowthAccountServiceImpl;
import com.gtech.promotion.vo.param.growth.CreateGrowthAccountParam;
import com.gtech.promotion.vo.param.growth.UpdateGrowthParam;
import com.gtech.promotion.vo.param.growth.query.GetGrowthAccountParam;
import com.gtech.promotion.vo.param.growth.query.GrowthAccountUniqueParam;
import com.gtech.promotion.vo.result.growth.GrowthAccountResult;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import tk.mybatis.mapper.mapperhelper.EntityHelper;
import tk.mybatis.mapper.mapperhelper.MapperHelper;

import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

/**
 * @Description :
 * <AUTHOR> gaojie.li
 * @Date : 2021/8/6 11:02
 */
@RunWith(MockitoJUnitRunner.class)
public class GrowthAccountServiceTest {

    @InjectMocks
    private GrowthAccountServiceImpl growthAccountService;

    @Mock
    GrowthAccountMapper growthAccountMapper;

    @Mock
    GrowthTransactionService growthTransactionService;

    @Mock
    GTechCodeGenerator codeGenerator;

    @Mock
    private GrowthAccountSaveService growthAccountSaveService;

    @Before
    public void before() {
        EntityHelper.initEntityNameMap(GrowthAccountEntity.class, new MapperHelper().getConfig());
    }

    @Test
    public void updateGrowthAccountStatus() {
        Mockito.when(growthAccountMapper.updateByExampleSelective(Mockito.any(), Mockito.any())).thenReturn(1);
        GrowthAccountUniqueParam.GrowthAccountStatusUniqueVo grow = new GrowthAccountUniqueParam.GrowthAccountStatusUniqueVo();
        grow.setTenantCode("100001");
        grow.setGrowthAccountCode("code");
        grow.setStatus(1);
        grow.setOldStatus(1);
        int i = growthAccountService.updateGrowthAccountStatus(grow);
        Assert.assertEquals(1, i);
    }

    @Test
    public void updateGrowthAccount() {
        CreateGrowthAccountParam createGrowthAccountParam = new CreateGrowthAccountParam();
        Mockito.when(growthAccountMapper.updateByExampleSelective(Mockito.any(), Mockito.any())).thenReturn(1);
        int i = growthAccountService.updateGrowthAccount(createGrowthAccountParam);
        Assert.assertEquals(1, i);
    }

    @Test
    public void getGrowthAccount() {
        GetGrowthAccountParam createGrowthAccountParam = new GetGrowthAccountParam();
        Mockito.when(growthAccountMapper.selectOne(Mockito.any())).thenReturn(new GrowthAccountEntity());
        GrowthAccountResult growthAccountResult = growthAccountService.getGrowthAccount(createGrowthAccountParam);
        Assert.assertNull(growthAccountResult.getAccountCode());
    }

    @Test
    public void getGrowthAccountByAccount() {
        Mockito.when(growthAccountMapper.selectOne(Mockito.any())).thenReturn(new GrowthAccountEntity());
        GrowthAccountResult growthAccountResult = growthAccountService.getGrowthAccountByAccount("11", "12", 123);
        Assert.assertNull(growthAccountResult.getAccountCode());
    }

    @Test
    public void queryGrowthAccountPage() {
        Mockito.when(growthAccountMapper.query(Mockito.any())).thenReturn(Collections.emptyList());
        Map<String, Object> map = new HashMap<>();
        PageResult<GrowthAccountResult> growthAccountResultPageResult = growthAccountService.queryGrowthAccountPage(map);
        Assert.assertNotNull(growthAccountResultPageResult);
    }

    @Test
    public void updateStatusGrowthAccountModel() {
        GrowthAccountEntity growthAccountEntity = new GrowthAccountEntity();
        growthAccountEntity.setAccountBalance(1);
        Mockito.when(growthAccountMapper.selectOne(Mockito.any())).thenReturn(growthAccountEntity);
        Mockito.when(growthAccountMapper.updateGrowth(Mockito.any())).thenReturn(1);
        UpdateGrowthParam updateGrowthParam = new UpdateGrowthParam();
        updateGrowthParam.setTransactionType(2);
        updateGrowthParam.setTransactionAmount(1);
        int i = growthAccountService.updateGrowth(updateGrowthParam);
        Assert.assertEquals(1,i);
    }

}
