package com.gtech.promotion.service.marketing.impl;

import com.gtech.commons.dao.mapper.GTechBaseMapper;
import com.gtech.promotion.dao.entity.marketing.HelpRecordEntity;
import com.gtech.promotion.dao.mapper.marketing.HelpRecordMapper;
import com.gtech.promotion.dao.model.marketing.HelpRecordModel;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import tk.mybatis.mapper.mapperhelper.EntityHelper;
import tk.mybatis.mapper.mapperhelper.MapperHelper;

import java.util.Arrays;
import java.util.List;

public class HelpRecordServiceImplTest {
    @Mock
    HelpRecordMapper helpRecordMapper;
    @Mock
    GTechBaseMapper<HelpRecordEntity> mapper;
    @InjectMocks
    HelpRecordServiceImpl helpRecordServiceImpl;

    @Before
    public void setUp() {
        MockitoAnnotations.openMocks(this);
        EntityHelper.initEntityNameMap(HelpRecordEntity.class, new MapperHelper().getConfig());
        EntityHelper.initEntityNameMap(HelpRecordModel.class, new MapperHelper().getConfig());
    }

    @Test
    public void testCheckMemberHelpRecord() {
        List<HelpRecordEntity> result = helpRecordServiceImpl.checkMemberHelpRecord("activityCode", "memberCode");
    }

    @Test
    public void testQueryHelpRecord() {
        List<HelpRecordEntity> result = helpRecordServiceImpl.queryHelpRecord(new HelpRecordModel("domainCode", "tenantCode", "orgCode", "activityCode", "sharingRecordCode", "helpRecordCode", "sharingMemberCode", "helpMemberCode", "helpRecordStatus"), Arrays.<String>asList("String"));
    }

    @Test
    public void testInsert() {
        int result = helpRecordServiceImpl.insert(new HelpRecordModel("domainCode", "tenantCode", "orgCode", "activityCode", "sharingRecordCode", "helpRecordCode", "sharingMemberCode", "helpMemberCode", "helpRecordStatus"));
    }

    @Test
    public void testDeleteByActivityCode() {
        int result = helpRecordServiceImpl.deleteByActivityCode("activityCode");
    }

    @Test
    public void testFindByActivityCode() {
        HelpRecordModel result = helpRecordServiceImpl.findByActivityCode("activityCode");
    }

    @Test
    public void testFindListByActivityCode() {
        List<HelpRecordModel> result = helpRecordServiceImpl.findListByActivityCode("activityCode");
    }


}

