package com.gtech.promotion.service.marketing;

import com.gtech.promotion.dao.entity.marketing.PrizeRecordEntity;
import com.gtech.promotion.dao.mapper.marketing.PrizeRecordMapper;
import com.gtech.promotion.dao.model.marketing.PrizeRecordModel;
import com.gtech.promotion.dao.model.marketing.QueryRecordByTicketsModel;
import com.gtech.promotion.service.marketing.impl.PrizeRecordServiceImpl;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import tk.mybatis.mapper.mapperhelper.EntityHelper;
import tk.mybatis.mapper.mapperhelper.MapperHelper;

import java.util.ArrayList;
import java.util.List;

@RunWith(MockitoJUnitRunner.class)
public class PrizeRecordServiceTest {

    @InjectMocks
    private PrizeRecordServiceImpl prizeRecordService;

    @Mock
    private PrizeRecordMapper recordMapper;

    @Before
    public void before(){
        EntityHelper.initEntityNameMap(PrizeRecordEntity.class, new MapperHelper().getConfig());
    }


    @Test
    public void queryLuckyRecordList(){
        QueryRecordByTicketsModel queryRecordByTicketsModel = new QueryRecordByTicketsModel();
        Mockito.when(recordMapper.selectByCondition(Mockito.any())).thenReturn(new ArrayList<>());
        List<PrizeRecordModel> prizeRecordModels = prizeRecordService.queryLuckyRecordList(queryRecordByTicketsModel);
        Assert.assertEquals(0, prizeRecordModels.size());
    }
}
