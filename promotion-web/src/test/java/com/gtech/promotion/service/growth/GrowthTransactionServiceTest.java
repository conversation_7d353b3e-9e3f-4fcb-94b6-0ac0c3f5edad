package com.gtech.promotion.service.growth;

import com.gtech.commons.redis.GTechCodeGenerator;
import com.gtech.commons.result.PageResult;
import com.gtech.promotion.dao.entity.growth.GrowthTransactionEntity;
import com.gtech.promotion.dao.mapper.growth.GrowthTransactionMapper;
import com.gtech.promotion.service.growth.impl.GrowthTransactionServiceImpl;
import com.gtech.promotion.vo.param.growth.GrowthTransactionParam;
import com.gtech.promotion.vo.param.growth.query.GetGrowthTransactionParam;
import com.gtech.promotion.vo.result.growth.GrowthTransactionResult;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.dao.DuplicateKeyException;
import tk.mybatis.mapper.mapperhelper.EntityHelper;
import tk.mybatis.mapper.mapperhelper.MapperHelper;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RunWith(MockitoJUnitRunner.class)
public class GrowthTransactionServiceTest {

    @InjectMocks
    private GrowthTransactionServiceImpl growthTransactionService;

    @Mock
    private GrowthTransactionMapper growthTransactionMapper;
    @Mock
    private GTechCodeGenerator codeGenerator;


    @Before
    public void before() {
        EntityHelper.initEntityNameMap(GrowthTransactionEntity.class, new MapperHelper().getConfig());
    }

    @Test
    public void saveGrowthTransaction() {
        GrowthTransactionParam growthTransactionParam = new GrowthTransactionParam();
        growthTransactionParam.setTransactionSn("12");
        Mockito.when(growthTransactionMapper.insert(Mockito.any())).thenReturn(1);
        String s = growthTransactionService.saveGrowthTransaction(growthTransactionParam);
        Assert.assertEquals("12", s);
    }

    @Test(expected = Exception.class)
    public void saveGrowthTransaction1() {
        String s = growthTransactionService.saveGrowthTransaction(null);
        Assert.assertEquals("12", s);
    }

    @Test
    public void saveGrowthTransaction2() {
        GrowthTransactionParam growthTransactionParam = new GrowthTransactionParam();
        growthTransactionParam.setTransactionSn("12");
        Mockito.when(growthTransactionMapper.insert(Mockito.any())).thenReturn(1);
        String s = growthTransactionService.saveGrowthTransaction(growthTransactionParam);
        Assert.assertEquals("12", s);
    }

    @Test
    public void saveGrowthTransaction3() {
        GrowthTransactionParam growthTransactionParam = new GrowthTransactionParam();
        growthTransactionParam.setTenantCode("1");
        Mockito.when(codeGenerator.generateCode(Mockito.any(),Mockito.any(),Mockito.any(),Mockito.anyLong())).thenReturn("1");
        Mockito.when(growthTransactionMapper.insert(Mockito.any())).thenReturn(1);
        String s = growthTransactionService.saveGrowthTransaction(growthTransactionParam);
        Assert.assertNotNull(s);
    }

    @Test(expected = Exception.class)
    public void saveGrowthTransaction4() {
        GrowthTransactionParam growthTransactionParam = new GrowthTransactionParam();
        growthTransactionParam.setTenantCode("1");
        Mockito.when(codeGenerator.generateCode(Mockito.any(),Mockito.any(),Mockito.any(),Mockito.anyLong())).thenReturn("1");
        Mockito.when(growthTransactionMapper.insert(Mockito.any())).thenThrow(DuplicateKeyException.class);
        String s = growthTransactionService.saveGrowthTransaction(growthTransactionParam);
        Assert.assertNotNull(s);
    }

    @Test(expected = Exception.class)
    public void saveGrowthTransaction5() {
        GrowthTransactionParam growthTransactionParam = new GrowthTransactionParam();
        growthTransactionParam.setTenantCode("1");
        Mockito.when(growthTransactionMapper.insert(Mockito.any())).thenThrow(NullPointerException.class);
        String s = growthTransactionService.saveGrowthTransaction(growthTransactionParam);
        Assert.assertNotNull(s);
    }

    @Test
    public void updateGrowthTransaction() {
        GrowthTransactionParam growthTransactionParam = new GrowthTransactionParam();
        growthTransactionParam.setTransactionSn("12");
        Mockito.when(growthTransactionMapper.selectOne(Mockito.any())).thenReturn(new GrowthTransactionEntity());
        Mockito.when(growthTransactionMapper.updateByPrimaryKeySelective(Mockito.any())).thenReturn(1);
        growthTransactionService.updateGrowthTransaction(growthTransactionParam);
    }

    @Test(expected = Exception.class)
    public void updateGrowthTransaction1() {
        GrowthTransactionParam growthTransactionParam = new GrowthTransactionParam();
        growthTransactionParam.setTransactionSn("12");
        growthTransactionService.updateGrowthTransaction(null);
    }

    @Test(expected = Exception.class)
    public void updateGrowthTransaction2() {
        GrowthTransactionParam growthTransactionParam = new GrowthTransactionParam();
        growthTransactionParam.setTransactionSn("12");
        Mockito.when(growthTransactionMapper.selectOne(Mockito.any())).thenThrow(NullPointerException.class);
        growthTransactionService.updateGrowthTransaction(growthTransactionParam);
    }

    @Test(expected = Exception.class)
    public void updateGrowthTransaction3() {
        GrowthTransactionParam growthTransactionParam = new GrowthTransactionParam();
        growthTransactionService.updateGrowthTransaction(growthTransactionParam);
    }

    @Test(expected = Exception.class)
    public void updateGrowthTransaction4() {
        GrowthTransactionParam growthTransactionParam = new GrowthTransactionParam();
        growthTransactionParam.setTransactionSn("12");
        Mockito.when(growthTransactionMapper.selectOne(Mockito.any())).thenReturn(new GrowthTransactionEntity());
        Mockito.when(growthTransactionMapper.updateByPrimaryKeySelective(Mockito.any())).thenThrow(DuplicateKeyException.class);
        growthTransactionService.updateGrowthTransaction(growthTransactionParam);
    }

    @Test(expected = Exception.class)
    public void updateGrowthTransaction5() {
        GrowthTransactionParam growthTransactionParam = new GrowthTransactionParam();
        growthTransactionParam.setTransactionSn("12");
        Mockito.when(growthTransactionMapper.selectOne(Mockito.any())).thenReturn(new GrowthTransactionEntity());
        Mockito.when(growthTransactionMapper.updateByPrimaryKeySelective(Mockito.any())).thenThrow(NullPointerException.class);
        growthTransactionService.updateGrowthTransaction(growthTransactionParam);
    }

    @Test
    public void getGrowthTransaction() {
        GetGrowthTransactionParam getGrowthTransactionParam = new GetGrowthTransactionParam();
        getGrowthTransactionParam.setTransactionSn("12");
        Mockito.when(growthTransactionMapper.selectOne(Mockito.any())).thenReturn(new GrowthTransactionEntity());
        GrowthTransactionResult growthTransaction = growthTransactionService.getGrowthTransaction(getGrowthTransactionParam);
        Assert.assertNotNull(growthTransaction);
    }

    @Test
    public void getGrowthTransaction1() {
        GetGrowthTransactionParam getGrowthTransactionParam = new GetGrowthTransactionParam();
        getGrowthTransactionParam.setTransactionSn("12");
        Mockito.when(growthTransactionMapper.selectOne(Mockito.any())).thenReturn(null);
        GrowthTransactionResult growthTransaction = growthTransactionService.getGrowthTransaction(getGrowthTransactionParam);
        Assert.assertNull(growthTransaction);
    }

    @Test
    public void queryGrowthTransactionPage() {
        Map<String, Object> map = new HashMap<>();
        List<GrowthTransactionEntity> list = new ArrayList<>();
        list.add(new GrowthTransactionEntity());
        list.add(new GrowthTransactionEntity());
        Mockito.when(growthTransactionMapper.query(Mockito.any())).thenReturn(list);
        PageResult<GrowthTransactionResult> growthTransactionResultPageResult = growthTransactionService.queryGrowthTransactionPage(map);
        Assert.assertNotNull(growthTransactionResultPageResult);
    }

    @Test(expected = Exception.class)
    public void queryGrowthTransactionPage1() {
        Map<String, Object> map = new HashMap<>();
        Mockito.when(growthTransactionMapper.query(Mockito.any())).thenThrow(NullPointerException.class);
        PageResult<GrowthTransactionResult> growthTransactionResultPageResult = growthTransactionService.queryGrowthTransactionPage(map);
        Assert.assertNotNull(growthTransactionResultPageResult);
    }


}
