package com.gtech.promotion.service.marketing;

import com.gtech.promotion.dao.entity.marketing.MarketingGroupEntity;
import com.gtech.promotion.dao.mapper.marketing.MarketingGroupMapper;
import com.gtech.promotion.dao.model.marketing.MarketingGroupMode;
import com.gtech.promotion.service.marketing.impl.MarketingGroupServiceImpl;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import tk.mybatis.mapper.mapperhelper.EntityHelper;
import tk.mybatis.mapper.mapperhelper.MapperHelper;

import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * @Description :
 * <AUTHOR> gaojie.li
 * @Date : 2023/8/7 17:45
 */

@RunWith(MockitoJUnitRunner.class)
public class MarketingGroupServiceTest {

    @InjectMocks
    private MarketingGroupServiceImpl marketingGroupService;

    @Mock
    private MarketingGroupMapper marketingGroupUserMapper;

    @Before
    public void before(){
        EntityHelper.initEntityNameMap(MarketingGroupEntity.class, new MapperHelper().getConfig());
    }


    @Test
    public void findGroupUserCode(){

        Set<String> list = new HashSet<>();
        list.add("!");

        List<MarketingGroupEntity> marketingGroupEntities = marketingGroupService.selectMarketingGroupList("1", list);

        Assert.assertEquals(0, marketingGroupEntities.size());
    }

    @Test
    public void findMarketingGroupByActivityCode(){


        MarketingGroupMode marketingGroupByActivityCode = marketingGroupService.findMarketingGroupByActivityCode("1", "1");

        Assert.assertNull(marketingGroupByActivityCode);
    }

    @Test
    public void findGroupByActivityCode(){


        MarketingGroupMode marketingGroupByActivityCode = marketingGroupService.findGroupByActivityCode("1", "1",1);

        Assert.assertNull(marketingGroupByActivityCode);
    }

    @Test
    public void findGroupByActivityCode_null(){


        MarketingGroupMode marketingGroupByActivityCode = marketingGroupService.findGroupByActivityCode("1", "1",null);

        Assert.assertNull(marketingGroupByActivityCode);
    }
}
