package com.gtech.promotion.service;

import com.gtech.promotion.dao.entity.activity.ActivityScriptEntity;
import com.gtech.promotion.dao.mapper.activity.ActivityScriptMapper;
import com.gtech.promotion.dao.model.activity.ActivityScriptModel;
import com.gtech.promotion.service.common.impl.GroovyServiceImpl;
import org.apache.commons.collections4.map.HashedMap;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import tk.mybatis.mapper.mapperhelper.EntityHelper;
import tk.mybatis.mapper.mapperhelper.MapperHelper;

import java.util.Map;

@RunWith(MockitoJUnitRunner.class)
public class GroovyServiceTest {

    @InjectMocks
    private GroovyServiceImpl groovyService;

    @Mock
    private ActivityScriptMapper activityScriptMapper;

    @Before
    public void before(){
        EntityHelper.initEntityNameMap(ActivityScriptEntity.class, new MapperHelper().getConfig());
    }

    @Test
    public void run_null(){
        Map<String, Object> paramMap = new HashedMap<>();
        boolean run = groovyService.run(null, paramMap);
        Assert.assertTrue(run);
    }

    @Test
    public void run(){
        Map<String, Object> paramMap = new HashedMap<>();
        ActivityScriptModel activityScriptModel = new ActivityScriptModel();
        activityScriptModel.setVersion(1);
        activityScriptModel.setScript("true;");
        boolean run = groovyService.run(activityScriptModel, paramMap);
        boolean run1 = groovyService.run(activityScriptModel, paramMap);
        Assert.assertTrue(run);
        Assert.assertTrue(run1);
    }

    @Test
    public void findByCode(){
        Mockito.when(activityScriptMapper.selectOne(Mockito.any())).thenReturn(null);
        ActivityScriptModel byCode = groovyService.findByCode("1", "1");
        Assert.assertNull(byCode);
    }

}
