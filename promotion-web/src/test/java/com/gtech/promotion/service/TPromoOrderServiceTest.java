package com.gtech.promotion.service;

import com.gtech.promotion.dao.entity.activity.TPromoOrderEntity;
import com.gtech.promotion.dao.mapper.activity.TPromoOrderMapper;
import com.gtech.promotion.dao.model.activity.TPromoOrderVO;
import com.gtech.promotion.dto.in.activity.ActivityTenantInDTO;
import com.gtech.promotion.dto.in.activity.QueryActivityStatisticInDTO;
import com.gtech.promotion.service.impl.activity.TPromoOrderServiceImpl;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import tk.mybatis.mapper.mapperhelper.EntityHelper;
import tk.mybatis.mapper.mapperhelper.MapperHelper;

import java.util.ArrayList;
import java.util.List;

/**
 * @Description :
 * <AUTHOR> gaojie.li
 * @Date : 2021/8/4 15:43
 */
@RunWith(MockitoJUnitRunner.class)
public class TPromoOrderServiceTest {

    @InjectMocks
    private TPromoOrderServiceImpl tPromoOrderService;

    @Mock
    private TPromoOrderMapper tPromoOrderMapper;

    @Before
    public void before() {
        EntityHelper.initEntityNameMap(TPromoOrderEntity.class, new MapperHelper().getConfig());
    }


    @Test
    public void updateOrderStatusById(){
        TPromoOrderVO tPromoOrderVO = new TPromoOrderVO();

        Integer integer = tPromoOrderService.updateOrderStatusById(tPromoOrderVO);
        Assert.assertEquals(0,integer.intValue());
    }


    @Test
    public void updateOrderLogicDelete(){
        Integer integer = tPromoOrderService.updateOrderLogicDelete("1", "1", "!");
        Assert.assertEquals(0,integer.intValue());
    }

    @Test
    public void insertTPromoOrder(){
        TPromoOrderVO tPromoOrderVO = new TPromoOrderVO();
        tPromoOrderVO.setOrderId("11");
        tPromoOrderVO.setId("22");
        String s = tPromoOrderService.insertTPromoOrder(tPromoOrderVO);
        Assert.assertNotNull(s);
    }
    @Test
    public void queryPromoOrderYesterday(){
        Mockito.when(tPromoOrderMapper.selectByCondition(Mockito.any())).thenReturn(new ArrayList<>());
        List<TPromoOrderEntity> tPromoOrderEntities = tPromoOrderService.queryPromoOrderYesterday();
        Assert.assertEquals(0,tPromoOrderEntities.size());
    }

    @Test
    public void queryPromoOrderToday(){
        QueryActivityStatisticInDTO dto = new QueryActivityStatisticInDTO();
        Mockito.when(tPromoOrderMapper.selectByCondition(Mockito.any())).thenReturn(new ArrayList<>());
        List<TPromoOrderEntity> tPromoOrderEntities = tPromoOrderService.queryPromoOrderToday(dto);
        Assert.assertEquals(0,tPromoOrderEntities.size());
    }

    @Test
    public void payOrderAmount(){
        Mockito.when(tPromoOrderMapper.selectCountByCondition(Mockito.any())).thenReturn(1);
        long tPromoOrderEntities = tPromoOrderService.payOrderAmount();
        Assert.assertEquals(1,tPromoOrderEntities);
    }

    @Test
    public void getPayOrderAmount(){
        ActivityTenantInDTO tenant = new ActivityTenantInDTO();
        tenant.setEndTime("1");
        tenant.setStartTime("1");
        Mockito.when(tPromoOrderMapper.selectCountByCondition(Mockito.any())).thenReturn(1);
        long tPromoOrderEntities = tPromoOrderService.getPayOrderAmount(tenant);
        Assert.assertEquals(1,tPromoOrderEntities);
    }

    @Test
    public void getPayOrderAmount1(){
        ActivityTenantInDTO tenant = new ActivityTenantInDTO();
        tenant.setEndTime("");
        tenant.setStartTime("");
        Mockito.when(tPromoOrderMapper.selectCountByCondition(Mockito.any())).thenReturn(1);
        long tPromoOrderEntities = tPromoOrderService.getPayOrderAmount(tenant);
        Assert.assertEquals(1,tPromoOrderEntities);
    }
    @Test
    public void getPayOrderAmount2(){
        ActivityTenantInDTO tenant = new ActivityTenantInDTO();
        tenant.setEndTime("1");
        tenant.setStartTime("");
        Mockito.when(tPromoOrderMapper.selectCountByCondition(Mockito.any())).thenReturn(1);
        long tPromoOrderEntities = tPromoOrderService.getPayOrderAmount(tenant);
        Assert.assertEquals(1,tPromoOrderEntities);
    }
    @Test
    public void getPayOrderAmount3(){

        ActivityTenantInDTO tenant = new ActivityTenantInDTO();
        tenant.setEndTime("");
        tenant.setStartTime("2");
        Mockito.when(tPromoOrderMapper.selectCountByCondition(Mockito.any())).thenReturn(1);
        long tPromoOrderEntities = tPromoOrderService.getPayOrderAmount(tenant);
        Assert.assertEquals(1,tPromoOrderEntities);
    }
}
