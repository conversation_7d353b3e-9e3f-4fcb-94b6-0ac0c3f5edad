package com.gtech.promotion.service.point2;

import com.google.common.collect.Lists;
import com.gtech.basic.masterdata.client.MasterDataClient;
import com.gtech.commons.redis.GTechCodeGenerator;
import com.gtech.promotion.dao.entity.point.PointAccountEntity;
import com.gtech.promotion.dao.entity.point.PointTransactionEntity;
import com.gtech.promotion.dao.mapper.point.PointAccountMapper;
import com.gtech.promotion.dao.mapper.point.PointCampaignMapper;
import com.gtech.promotion.dao.mapper.point.PointTransactionMapper;
import com.gtech.promotion.service.point.PointAccountTempService;
import com.gtech.promotion.service.point.PointCampaignService;
import com.gtech.promotion.service.point.PointTransactionService;
import com.gtech.promotion.service.point.impl.PointAccountServiceImpl;
import com.gtech.promotion.vo.param.point.UpdatePointAccountParam;
import com.gtech.promotion.vo.param.point.UpdatePointParam;
import com.gtech.promotion.vo.param.point.query.GetPointAccountCampaignParam;
import com.gtech.promotion.vo.param.point.query.GetPointAccountParam;
import com.gtech.promotion.vo.param.point.query.PointAccountUniqueParam;
import com.gtech.promotion.vo.result.point.PointAccountCampaignResult;
import com.gtech.promotion.vo.result.point.PointCampaignResult;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import tk.mybatis.mapper.mapperhelper.EntityHelper;
import tk.mybatis.mapper.mapperhelper.MapperHelper;

import java.util.*;

@RunWith(MockitoJUnitRunner.class)
public class PointAccountServiceTest {

    @InjectMocks
    private PointAccountServiceImpl pointAccountService;

    @Mock
    PointAccountMapper pointAccountMapper;

    @Mock
    PointTransactionMapper pointTransactionMapper;

    @Mock
    PointAccountMapper growthAccountMapper;

    @Mock
    PointCampaignService pointCampaignService;

    @Mock
    PointAccountTempService pointAccountTempService;

    @Mock
    PointCampaignMapper pointCampaignMapper;

    @Mock
    PointTransactionService pointTransactionService;

    @Mock
    GTechCodeGenerator codeGenerator;

    @Mock
    MasterDataClient masterDataClient;

    @Before
    public void before() {
        EntityHelper.initEntityNameMap(PointAccountEntity.class, new MapperHelper().getConfig());
        EntityHelper.initEntityNameMap(PointTransactionEntity.class, new MapperHelper().getConfig());
        EntityHelper.initEntityNameMap(PointAccountEntity.class, new MapperHelper().getConfig());
    }

    @Test
    public void updatePointAccountStatus() {
        PointAccountUniqueParam.PointAccountStatusUniqueVo uniqueVo = new PointAccountUniqueParam.PointAccountStatusUniqueVo();
        uniqueVo.setTenantCode("100001");
        uniqueVo.setPointAccountCode("100001");
        Mockito.when(pointAccountMapper.updateByExampleSelective(Mockito.any(), Mockito.any())).thenReturn(1);
        Assert.assertEquals(1, pointAccountService.updatePointAccountStatus(uniqueVo));
    }

    @Test
    public void updatePointAccountStatus1() {
        PointAccountUniqueParam.PointAccountStatusUniqueVo uniqueVo = new PointAccountUniqueParam.PointAccountStatusUniqueVo();
        uniqueVo.setTenantCode("100001");
        Assert.assertEquals(0, pointAccountService.updatePointAccountStatus(uniqueVo));
    }

    @Test(expected = Exception.class)
    public void updatePointAccountStatus2() {
        PointAccountUniqueParam.PointAccountStatusUniqueVo uniqueVo = new PointAccountUniqueParam.PointAccountStatusUniqueVo();
        uniqueVo.setTenantCode("100001");
        uniqueVo.setPointAccountCode("100001");
        Mockito.when(pointAccountMapper.updateByExampleSelective(Mockito.any(), Mockito.any())).thenReturn(0);
        Assert.assertEquals(1, pointAccountService.updatePointAccountStatus(uniqueVo));
    }

    @Test(expected = Exception.class)
    public void updatePointAccountStatus3() {
        PointAccountUniqueParam.PointAccountStatusUniqueVo uniqueVo = new PointAccountUniqueParam.PointAccountStatusUniqueVo();
        uniqueVo.setTenantCode("100001");
        uniqueVo.setPointAccountCode("100001");
        Mockito.when(pointAccountMapper.updateByExampleSelective(Mockito.any(), Mockito.any())).thenThrow(NullPointerException.class);
        Assert.assertEquals(1, pointAccountService.updatePointAccountStatus(uniqueVo));
    }

    @Test
    public void updatePointAccount() {
        UpdatePointAccountParam uniqueVo = new UpdatePointAccountParam();
        uniqueVo.setTenantCode("100001");
        Mockito.when(pointAccountMapper.updateByExampleSelective(Mockito.any(), Mockito.any())).thenReturn(1);
        Assert.assertEquals(1, pointAccountService.updatePointAccount(uniqueVo));
    }

    @Test
    public void getPointAccount() {
        GetPointAccountParam uniqueVo = new GetPointAccountParam();
        uniqueVo.setTenantCode("100001");
        Mockito.when(pointAccountMapper.selectOne(Mockito.any())).thenReturn(new PointAccountEntity());
        Assert.assertNotNull(pointAccountService.getPointAccount(uniqueVo));
    }

    @Test
    public void getPointAccount1() {
        GetPointAccountParam uniqueVo = new GetPointAccountParam();
        uniqueVo.setTenantCode("100001");
        Mockito.when(pointAccountMapper.selectOne(Mockito.any())).thenThrow(NullPointerException.class);
        Assert.assertNull(pointAccountService.getPointAccount(uniqueVo));
    }

    @Test
    public void queryPointAccountPage() {
        Map<String, Object> map = new HashMap<>();
        List<PointAccountEntity> list = new ArrayList<>();
        Mockito.when(pointAccountMapper.query(Mockito.any())).thenReturn(list);
        Assert.assertNotNull(pointAccountService.queryPointAccountPage(map));
    }

    @Test(expected = Exception.class)
    public void queryPointAccountPage1() {
        Map<String, Object> map = new HashMap<>();
        List<PointAccountEntity> list = new ArrayList<>();
        Mockito.when(pointAccountMapper.query(Mockito.any())).thenThrow(NullPointerException.class);
        Assert.assertNull(pointAccountService.queryPointAccountPage(map));
    }

    @Test
    public void updatePoint(){
        UpdatePointParam pointChangeVo = new UpdatePointParam();
        pointChangeVo.setDomainCode("1");
        pointChangeVo.setTenantCode("1");
        pointChangeVo.setCampaignCode("1");
        pointChangeVo.setAccountCode("1");
        pointChangeVo.setAccountType(0);
        pointChangeVo.setTransactionRemarks("1");
        pointChangeVo.setReferOrderNumber("1");
        pointChangeVo.setTransactionType(0);
        pointChangeVo.setTransactionAmount(0);
        pointChangeVo.setCreateUser("1");
        pointChangeVo.setExpiration("1");
        pointChangeVo.setOperation(0);
        PointCampaignResult pointCampaign = new PointCampaignResult();
        pointCampaign.setTenantCode("1");
        pointCampaign.setCampaignCode("1");
        pointCampaign.setProgramName("1");
        pointCampaign.setCampaignTitle("1");
        pointCampaign.setCampaignDesc("1");
        pointCampaign.setSponsor("1");
        pointCampaign.setBeginTime("1");
        pointCampaign.setEndTime("1");
        pointCampaign.setTotalPoints(0);
        pointCampaign.setRemainingPoints(0);
        pointCampaign.setStatus(0);
        pointCampaign.setCreateUser("1");
        pointCampaign.setCreateTime(new Date());
        pointCampaign.setCampaignTitleLanguage("1");
        pointCampaign.setCampaignTitleLanguages(Lists.newArrayList());


        Mockito.when(pointCampaignService.getPointCampaign(Mockito.any())).thenReturn(pointCampaign);
        Mockito.when(pointCampaignService.updatePoint(Mockito.any())).thenReturn(1);
        Mockito.when(pointAccountMapper.updatePoint(Mockito.any())).thenReturn(1);
        pointAccountService.updatePoint(pointChangeVo);

    }

    @Test(expected = Exception.class)
    public void updatePoint2(){
        UpdatePointParam pointChangeVo = new UpdatePointParam();
        pointChangeVo.setDomainCode("1");
        pointChangeVo.setTenantCode("1");
        pointChangeVo.setCampaignCode("1");
        pointChangeVo.setAccountCode("1");
        pointChangeVo.setAccountType(0);
        pointChangeVo.setTransactionRemarks("1");
        pointChangeVo.setReferOrderNumber("1");
        pointChangeVo.setTransactionType(0);
        pointChangeVo.setTransactionAmount(0);
        pointChangeVo.setCreateUser("1");
        pointChangeVo.setOperation(0);
        pointChangeVo.setExpiration("2021-09-09 11:33:23");
        PointCampaignResult pointCampaign = new PointCampaignResult();
        pointCampaign.setTenantCode("1");
        pointCampaign.setCampaignCode("1");
        pointCampaign.setProgramName("1");
        pointCampaign.setCampaignTitle("1");
        pointCampaign.setCampaignDesc("1");
        pointCampaign.setSponsor("1");
        pointCampaign.setBeginTime("1");
        pointCampaign.setEndTime("2021-09-08 11:33:23");
        pointCampaign.setTotalPoints(0);
        pointCampaign.setRemainingPoints(0);
        pointCampaign.setStatus(0);
        pointCampaign.setCreateUser("1");
        pointCampaign.setCreateTime(new Date());
        pointCampaign.setCampaignTitleLanguage("1");
        Mockito.when(pointCampaignService.getPointCampaign(Mockito.any())).thenReturn(pointCampaign);
        pointAccountService.updatePoint(pointChangeVo);
    }

    @Test(expected = Exception.class)
    public void updatePoint1(){
        UpdatePointParam pointChangeVo = new UpdatePointParam();
        pointChangeVo.setDomainCode("1");
        pointChangeVo.setTenantCode("1");
        pointChangeVo.setCampaignCode("1");
        pointChangeVo.setAccountCode("1");
        pointChangeVo.setAccountType(0);
        pointChangeVo.setTransactionRemarks("1");
        pointChangeVo.setReferOrderNumber("1");
        pointChangeVo.setTransactionType(0);
        pointChangeVo.setTransactionAmount(0);
        pointChangeVo.setCreateUser("1");
        pointChangeVo.setOperation(0);
        PointCampaignResult pointCampaign = new PointCampaignResult();
        pointCampaign.setTenantCode("1");
        pointCampaign.setCampaignCode("1");
        pointCampaign.setProgramName("1");
        pointCampaign.setCampaignTitle("1");
        pointCampaign.setCampaignDesc("1");
        pointCampaign.setSponsor("1");
        pointCampaign.setBeginTime("1");
        pointCampaign.setEndTime("1");
        pointCampaign.setTotalPoints(0);
        pointCampaign.setRemainingPoints(0);
        pointCampaign.setStatus(0);
        pointCampaign.setCreateUser("1");
        pointCampaign.setCreateTime(new Date());
        pointCampaign.setCampaignTitleLanguage("1");
        Mockito.when(pointCampaignService.getPointCampaign(Mockito.any())).thenReturn(pointCampaign);
        Mockito.when(pointCampaignService.updatePoint(Mockito.any())).thenReturn(0);
        pointAccountService.updatePoint(pointChangeVo);
    }

    @Test(expected = Exception.class)
    public void updatePoint3(){
        UpdatePointParam pointChangeVo = new UpdatePointParam();
        pointChangeVo.setDomainCode("1");
        pointChangeVo.setTenantCode("1");
        pointChangeVo.setCampaignCode("1");
        pointChangeVo.setAccountCode("1");
        pointChangeVo.setAccountType(0);
        pointChangeVo.setTransactionRemarks("1");
        pointChangeVo.setReferOrderNumber("1");
        pointChangeVo.setTransactionType(0);
        pointChangeVo.setTransactionAmount(0);
        pointChangeVo.setCreateUser("1");
        pointChangeVo.setExpiration("1");
        pointChangeVo.setOperation(0);
        PointCampaignResult pointCampaign = new PointCampaignResult();
        pointCampaign.setTenantCode("1");
        pointCampaign.setCampaignCode("1");
        pointCampaign.setProgramName("1");
        pointCampaign.setCampaignTitle("1");
        pointCampaign.setCampaignDesc("1");
        pointCampaign.setSponsor("1");
        pointCampaign.setBeginTime("1");
        pointCampaign.setEndTime("1");
        pointCampaign.setTotalPoints(0);
        pointCampaign.setRemainingPoints(0);
        pointCampaign.setStatus(0);
        pointCampaign.setCreateUser("1");
        pointCampaign.setCreateTime(new Date());
        pointCampaign.setCampaignTitleLanguage("1");
        pointCampaign.setCampaignTitleLanguages(Lists.newArrayList());
        Mockito.when(pointCampaignService.getPointCampaign(Mockito.any())).thenReturn(pointCampaign);
        Mockito.when(pointCampaignService.updatePoint(Mockito.any())).thenReturn(1);
        Mockito.when(pointAccountMapper.updatePoint(Mockito.any())).thenReturn(0);
        pointAccountService.updatePoint(pointChangeVo);
    }

    @Test
    public void increaseOrDecreasePoint(){
        UpdatePointParam updatePointParam = new UpdatePointParam();
        Mockito.when(pointAccountMapper.updatePoint(Mockito.any())).thenReturn(1);
        pointAccountService.increaseOrDecreasePoint(updatePointParam);
    }

    @Test(expected = Exception.class)
    public void increaseOrDecreasePoint1(){
        UpdatePointParam updatePointParam = new UpdatePointParam();
        Mockito.when(pointAccountMapper.updatePoint(Mockito.any())).thenReturn(0);
        pointAccountService.increaseOrDecreasePoint(updatePointParam);
    }

    @Test
    public void getPointAccountCampaign(){
        GetPointAccountCampaignParam param = new GetPointAccountCampaignParam();
        param.setDomainCode("1");
        param.setTenantCode("1");
        param.setAccountType(0);
        param.setAccountCode("1");
        ArrayList<String> a = Lists.newArrayList();
        a.add("123");
        a.add("123");
        a.add("123");
        a.add("123");
        param.setCampaignCodes(a);
        PointAccountEntity record = new PointAccountEntity();
        record.setId(0L);
        record.setDomainCode("1");
        record.setTenantCode("1");
        record.setPointAccountCode("1");
        record.setAccountCode("1");
        record.setCampaignCode("1");
        record.setAccountType(0);
        record.setAccountDesc("1");
        record.setAccountBalance(0);
        record.setStatus(0);
        record.setExtParams("1");
        record.setCreateUser("1");
        record.setCreateTime(new Date());
        record.setUpdateUser("1");
        record.setUpdateTime(new Date());
        Mockito.when(pointAccountMapper.selectOne(Mockito.any())).thenReturn(record);
        pointAccountService.getPointAccountCampaign(param);
    }

    @Test
    public void getPointAccountCampaign1(){
        GetPointAccountCampaignParam param = new GetPointAccountCampaignParam();
        param.setDomainCode("1");
        param.setTenantCode("1");
        param.setAccountType(0);
        param.setAccountCode("1");
        ArrayList<String> a = Lists.newArrayList();
        a.add("123");
        a.add("123");
        a.add("123");
        a.add("123");
        param.setCampaignCodes(a);
        PointAccountEntity record = new PointAccountEntity();
        record.setId(0L);
        record.setDomainCode("1");
        record.setTenantCode("1");
        record.setPointAccountCode("1");
        record.setAccountCode("1");
        record.setCampaignCode("1");
        record.setAccountType(0);
        record.setAccountDesc("1");
        record.setAccountBalance(0);
        record.setStatus(0);
        record.setExtParams("1");
        record.setCreateUser("1");
        record.setCreateTime(new Date());
        record.setUpdateUser("1");
        record.setUpdateTime(new Date());
        Mockito.when(pointAccountMapper.selectOne(Mockito.any())).thenReturn(null);
        pointAccountService.getPointAccountCampaign(param);
    }

    @Test
    public void getPointAccountCampaign2(){
        GetPointAccountCampaignParam param = new GetPointAccountCampaignParam();
        param.setDomainCode("1");
        param.setTenantCode("1");
        param.setAccountType(0);
        param.setAccountCode("1");
        ArrayList<String> a = Lists.newArrayList();
        a.add("123");
        a.add("123");
        a.add("123");
        a.add("123");
        param.setCampaignCodes(a);
        PointAccountEntity record = new PointAccountEntity();
        record.setId(0L);
        record.setDomainCode("1");
        record.setTenantCode("1");
        record.setPointAccountCode("1");
        record.setAccountCode("1");
        record.setCampaignCode("1");
        record.setAccountType(0);
        record.setAccountDesc("1");
        record.setAccountBalance(0);
        record.setStatus(0);
        record.setExtParams("1");
        record.setCreateUser("1");
        record.setCreateTime(new Date());
        record.setUpdateUser("1");
        record.setUpdateTime(new Date());
        List<PointAccountCampaignResult.CampaignBalance> countPointTransactionCampaign = new ArrayList<>();
        PointAccountCampaignResult.CampaignBalance campaignBalance = new PointAccountCampaignResult.CampaignBalance();
        countPointTransactionCampaign.add(campaignBalance);
        Mockito.when(pointAccountMapper.selectOne(Mockito.any())).thenReturn(record);
        Mockito.when(pointTransactionService.getCountPointTransactionCampaign(Mockito.any(),Mockito.any(),Mockito.anyList())).thenReturn(countPointTransactionCampaign);
        pointAccountService.getPointAccountCampaign(param);
    }

    @Test
    public void getPointAccountCampaign3(){
        GetPointAccountCampaignParam param = new GetPointAccountCampaignParam();
        param.setDomainCode("1");
        param.setTenantCode("1");
        param.setAccountType(0);
        param.setAccountCode("1");
        ArrayList<String> a = Lists.newArrayList();
        a.add("123");
        a.add("123");
        a.add("123");
        a.add("123");
        param.setCampaignCodes(a);
        PointAccountEntity record = new PointAccountEntity();
        record.setId(0L);
        record.setDomainCode("1");
        record.setTenantCode("1");
        record.setPointAccountCode("1");
        record.setAccountCode("1");
        record.setCampaignCode("1");
        record.setAccountType(0);
        record.setAccountDesc("1");
        record.setAccountBalance(2);
        record.setStatus(0);
        record.setExtParams("1");
        record.setCreateUser("1");
        record.setCreateTime(new Date());
        record.setUpdateUser("1");
        record.setUpdateTime(new Date());
        List<PointAccountCampaignResult.CampaignBalance> countPointTransactionCampaign = new ArrayList<>();
        PointAccountCampaignResult.CampaignBalance campaignBalance = new PointAccountCampaignResult.CampaignBalance();
        campaignBalance.setAccountBalance(2);
        countPointTransactionCampaign.add(campaignBalance);
        Mockito.when(pointAccountMapper.selectOne(Mockito.any())).thenReturn(record);
        Mockito.when(pointTransactionService.getCountPointTransactionCampaign(Mockito.any(),Mockito.any(),Mockito.anyList())).thenReturn(countPointTransactionCampaign);
        Mockito.when(pointTransactionMapper.countEffectivePoint(Mockito.any())).thenReturn(1);
        pointAccountService.getPointAccountCampaign(param);
    }

}
