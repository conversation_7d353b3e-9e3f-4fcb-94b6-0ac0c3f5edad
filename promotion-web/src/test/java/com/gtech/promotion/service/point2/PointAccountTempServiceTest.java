package com.gtech.promotion.service.point2;


import com.gtech.commons.redis.GTechCodeGenerator;
import com.gtech.promotion.dao.entity.point.PointAccountEntity;
import com.gtech.promotion.dao.mapper.point.PointAccountMapper;
import com.gtech.promotion.service.point.impl.PointAccountTempServiceImpl;
import com.gtech.promotion.vo.param.point.CreatePointAccountParam;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.dao.DuplicateKeyException;
import tk.mybatis.mapper.mapperhelper.EntityHelper;
import tk.mybatis.mapper.mapperhelper.MapperHelper;

@RunWith(MockitoJUnitRunner.class)
public class PointAccountTempServiceTest {

    @InjectMocks
    private PointAccountTempServiceImpl pointAccountTempService;

    @Mock
    PointAccountMapper pointAccountMapper;

    @Mock
    GTechCodeGenerator codeGenerator;

    @Before
    public void before() {
        EntityHelper.initEntityNameMap(PointAccountEntity.class, new MapperHelper().getConfig());
    }

    @Test
    public void savePointAccount() {
        CreatePointAccountParam uniqueVo = new CreatePointAccountParam();
        uniqueVo.setTenantCode("100001");
        Mockito.when(codeGenerator.generateCode(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any())).thenReturn("1");
        Mockito.when(pointAccountMapper.insert(Mockito.any())).thenReturn(1);
        Assert.assertNotNull(pointAccountTempService.savePointAccount(uniqueVo));
    }

    @Test(expected = Exception.class)
    public void savePointAccount1() {
        CreatePointAccountParam uniqueVo = new CreatePointAccountParam();
        uniqueVo.setDomainCode("1");
        uniqueVo.setAccountBalance(0);
        uniqueVo.setStatus(0);
        uniqueVo.setTenantCode("100001");
        Mockito.when(codeGenerator.generateCode(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any())).thenReturn("1");
        Mockito.when(pointAccountMapper.insert(Mockito.any())).thenThrow(DuplicateKeyException.class);
        Assert.assertNotNull(pointAccountTempService.savePointAccount(uniqueVo));
    }

    @Test(expected = Exception.class)
    public void savePointAccount2() {
        CreatePointAccountParam uniqueVo = new CreatePointAccountParam();
        uniqueVo.setDomainCode("1");
        uniqueVo.setAccountBalance(0);
        uniqueVo.setStatus(0);
        uniqueVo.setTenantCode("100001");
        Mockito.when(codeGenerator.generateCode(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any())).thenReturn("1");
        Mockito.when(pointAccountMapper.insert(Mockito.any())).thenThrow(NullPointerException.class);
        Assert.assertNotNull(pointAccountTempService.savePointAccount(uniqueVo));
    }
}
