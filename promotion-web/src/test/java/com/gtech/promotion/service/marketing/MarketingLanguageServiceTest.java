package com.gtech.promotion.service.marketing;

import com.gtech.promotion.dao.entity.marketing.MarketingLanguageEntity;
import com.gtech.promotion.dao.mapper.marketing.MarketingLanguageMapper;
import com.gtech.promotion.dao.model.marketing.MarketingLanguageModel;
import com.gtech.promotion.service.marketing.impl.MarketingLanguageServiceImpl;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import tk.mybatis.mapper.mapperhelper.EntityHelper;
import tk.mybatis.mapper.mapperhelper.MapperHelper;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2021/3/30 14:33
 */
@RunWith(MockitoJUnitRunner.class)
public class MarketingLanguageServiceTest {

    @InjectMocks
    private MarketingLanguageServiceImpl marketingLanguageService;

    @Mock
    private MarketingLanguageMapper languageMapper;
    @Before
    public void before(){
        EntityHelper.initEntityNameMap(MarketingLanguageEntity.class, new MapperHelper().getConfig());
    }

    @Test
    public void findByLanguage(){
        Mockito.when(languageMapper.selectOne(Mockito.any())).thenReturn(new MarketingLanguageEntity());
        MarketingLanguageModel byLanguage = marketingLanguageService.findByLanguage("1", "1");
        Assert.assertNull(byLanguage.getLanguage());
    }

    @Test
    public void getLanguagesByActivityCodes(){
        List<String> activityCodes = new ArrayList<>();
        activityCodes.add("1");
        Mockito.when(languageMapper.selectByCondition(Mockito.any())).thenReturn(new ArrayList<>());
        List<MarketingLanguageModel> languages = marketingLanguageService.getLanguagesByActivityCodes("1", activityCodes);
        Assert.assertEquals(0,languages.size());
    }
}
