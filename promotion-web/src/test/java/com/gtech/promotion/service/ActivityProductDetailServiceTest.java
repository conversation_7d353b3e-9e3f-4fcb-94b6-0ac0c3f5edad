package com.gtech.promotion.service;

import com.github.pagehelper.PageInfo;
import com.gtech.promotion.dao.entity.activity.ActivityProductDetailEntity;
import com.gtech.promotion.dao.mapper.activity.ActivityProductDetailMapper;
import com.gtech.promotion.dao.model.activity.TPromoActivityProductDetailVO;
import com.gtech.promotion.dto.cache.ProductSkuDetailDTO;
import com.gtech.promotion.service.impl.activity.ActivityProductDetailServiceImpl;
import com.gtech.promotion.vo.bean.ProductDetail;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import tk.mybatis.mapper.mapperhelper.EntityHelper;
import tk.mybatis.mapper.mapperhelper.MapperHelper;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import static org.mockito.Mockito.when;

/**
 * @Description :
 * <AUTHOR> gaojie.li
 * @Date : 2021/8/4 13:44
 */
@RunWith(MockitoJUnitRunner.class)
public class ActivityProductDetailServiceTest {

    @InjectMocks
    private ActivityProductDetailServiceImpl activityProductDetailService;
    @Mock
    private ActivityProductDetailMapper productDetailMapper;

    @Before
    public void before(){
        EntityHelper.initEntityNameMap(ActivityProductDetailEntity.class, new MapperHelper().getConfig());
    }
    @Test
    public void insertProductSku(){
        List<TPromoActivityProductDetailVO> productDetailVO = new ArrayList<>();
        when(productDetailMapper.insertList(Mockito.any())).thenReturn(1);
        Integer integer = activityProductDetailService.insertProductSku(productDetailVO);
        Assert.assertEquals(1,integer.intValue());
    }

    @Test
    public void getProductSkusByActivityCodes(){
        List<String> productDetailVO = new ArrayList<>();
        when(productDetailMapper.selectByCondition(Mockito.any())).thenReturn(new ArrayList<>());
        List<ProductSkuDetailDTO> integer = activityProductDetailService.getProductSkusByActivityCodes("1",productDetailVO);
        Assert.assertEquals(0,integer.size());
    }
    @Test
    public void queryOneGroupByActivityCodes_empty(){
        List<String> productDetailVO = new ArrayList<>();
        List<ProductSkuDetailDTO> integer = activityProductDetailService.queryOneGroupByActivityCodes("1",productDetailVO);
        Assert.assertEquals(0,integer.size());
    }

    @Test
    public void queryOneGroupByActivityCodes_not_empty(){
        List<String> productDetailVO = new ArrayList<>();
        productDetailVO.add("131");
        when(productDetailMapper.queryOneGroupByActivityCodes(Mockito.any(),Mockito.any())).thenReturn(new ArrayList<>());
        List<ProductSkuDetailDTO> integer = activityProductDetailService.queryOneGroupByActivityCodes("1",productDetailVO);
        Assert.assertEquals(0,integer.size());
    }
    @Test
    public void queryListByActivityCodesAndProductCode_empty(){
        Set<String> productDetailVO = new HashSet<>();
        productDetailVO.add("131");
        List<ProductSkuDetailDTO> integer = activityProductDetailService.queryListByActivityCodesAndProductCode(productDetailVO,"1");
        Assert.assertEquals(0,integer.size());
    }

    @Test
    public void queryListByActivityCodesAndProductCodes_empty(){
        Set<String> productDetailVO = new HashSet<>();
        List<String> products = new ArrayList<>();
        productDetailVO.add("131");
        List<ProductSkuDetailDTO> integer = activityProductDetailService.queryListByActivityCodesAndProductCodes(productDetailVO,products);
        Assert.assertEquals(0,integer.size());
    }
    @Test
    public void deleteProductDetails(){
        when(productDetailMapper.delete(Mockito.any())).thenReturn(1);
        Integer integer = activityProductDetailService.deleteProductDetails("",1);
        Assert.assertEquals(1,integer.intValue());
    }

    @Test
    public void queryProductDetails(){
        when(productDetailMapper.selectByCondition(Mockito.any())).thenReturn(new ArrayList<>());
        PageInfo<ProductDetail> integer = activityProductDetailService.queryProductDetails("");
        Assert.assertEquals(0,integer.getList().size());
    }

    @Test
    public void getProductSkus111(){
        when(productDetailMapper.selectByCondition(Mockito.any())).thenReturn(new ArrayList<>());
        List<ProductSkuDetailDTO> integer = activityProductDetailService.getProductSkus111("");
        Assert.assertEquals(0,integer.size());
    }
    @Test
    public void queryProductSpusIn111(){
        List<String > spuCodes=new ArrayList<>();
        when(productDetailMapper.selectByCondition(Mockito.any())).thenReturn(new ArrayList<>());
        List<ProductDetail> integer = activityProductDetailService.queryProductSpusIn111("",spuCodes);
        Assert.assertEquals(0,integer.size());
    }

    @Test
    public void queryProductcombinSpusIn111(){
        List<String > spuCodes=new ArrayList<>();
        when(productDetailMapper.selectByCondition(Mockito.any())).thenReturn(new ArrayList<>());
        List<ProductDetail> integer = activityProductDetailService.queryProductcombinSpusIn111("",spuCodes);
        Assert.assertEquals(0,integer.size());
    }

    @Test
    public void deleteProductSkuBySeqNum(){
        when(productDetailMapper.delete(Mockito.any())).thenReturn(0);
        Integer integer = activityProductDetailService.deleteProductSkuBySeqNum("",1);
        Assert.assertEquals(0,integer.intValue());
    }

    @Test
    public void queryProductSkusBySeqNum111(){
        when(productDetailMapper.selectByCondition(Mockito.any())).thenReturn(new ArrayList<>());
        List<TPromoActivityProductDetailVO> integer = activityProductDetailService.queryProductSkusBySeqNum111("",1);
        Assert.assertEquals(0,integer.size());
    }
    @Test
    public void getSeqNumDistinct111(){
        when(productDetailMapper.getSeqNumDistinct111(Mockito.any())).thenReturn(new ArrayList<>());
        List<Integer> integer = activityProductDetailService.getSeqNumDistinct111("");
        Assert.assertEquals(0,integer.size());
    }

    @Test
    public void copyProductSku111(){
        activityProductDetailService.copyProductSku111("","");
    }
}
