package com.gtech.promotion.service.marketing;

import com.github.pagehelper.PageHelper;
import com.gtech.commons.page.PageData;
import com.gtech.commons.page.PageParam;
import com.gtech.promotion.dao.entity.marketing.PrizeEntity;
import com.gtech.promotion.dao.mapper.marketing.PrizeMapper;
import com.gtech.promotion.dao.model.marketing.BaseModel;
import com.gtech.promotion.dao.model.marketing.PrizeModel;
import com.gtech.promotion.service.marketing.impl.PrizeServiceImpl;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import tk.mybatis.mapper.mapperhelper.EntityHelper;
import tk.mybatis.mapper.mapperhelper.MapperHelper;

import java.util.ArrayList;
import java.util.List;

@RunWith(MockitoJUnitRunner.class)
public class PrizeServiceTest {

    @InjectMocks
    private PrizeServiceImpl prizeService;

    @Mock
    private PrizeMapper prizeMapper;

    @Before
    public void before(){
        EntityHelper.initEntityNameMap(PrizeEntity.class, new MapperHelper().getConfig());
    }

    @Test
    public void deductInventory(){
        PrizeModel prizeModel = new PrizeModel();
        Mockito.when(prizeMapper.deductInventory(Mockito.any())).thenReturn(0);
        int i = prizeService.deductInventory(prizeModel);
        Assert.assertEquals(0, i);
    }

    @Test
    public void insert(){
        PrizeModel prizeModel = new PrizeModel();
        Mockito.when(prizeMapper.insertSelective(Mockito.any())).thenReturn(0);
        int i = prizeService.insert(prizeModel);
        Assert.assertEquals(0, i);
    }


    @Test
    public void deleteByActivityCode(){
        BaseModel baseModel = new BaseModel();
        Mockito.when(prizeMapper.deleteByCondition(Mockito.any())).thenReturn(0);
        int i = prizeService.deleteByActivityCode(baseModel.getActivityCode());
        Assert.assertEquals(0, i);
    }

    @Test
    public void selectListByActivityCode(){
        BaseModel baseModel = new BaseModel();
        Mockito.when(prizeMapper.selectByCondition(Mockito.any())).thenReturn(new ArrayList<>());
        List<PrizeModel> prizeModels = prizeService.findListByActivityCode(baseModel.getActivityCode());
        Assert.assertEquals(0, prizeModels.size());
    }

    @Test
    public void selectOneByActivityCode(){
        BaseModel baseModel = new BaseModel();
        Mockito.when(prizeMapper.selectByCondition(Mockito.any())).thenReturn(new ArrayList<>());
        PrizeModel prizeModel = prizeService.findByActivityCode(baseModel.getActivityCode());
        Assert.assertNull(prizeModel);
    }

    @Test
    public void selectOneByActivityCode1(){
        BaseModel baseModel = new BaseModel();
        ArrayList<PrizeEntity> value = new ArrayList<>();
        value.add(new PrizeEntity());
        Mockito.when(prizeMapper.selectByCondition(Mockito.any())).thenReturn(value);
        PrizeModel prizeModel = prizeService.findByActivityCode(baseModel.getActivityCode());
        Assert.assertNotNull(prizeModel);
        Assert.assertNull(prizeModel.getPrizeCode());
    }

    @Test
    public void selectPageList(){
        PrizeModel prizeModel = new PrizeModel();
        PageParam pageParam = new PageParam();
        ArrayList<PrizeEntity> value = new ArrayList<>();
        value.add(new PrizeEntity());
        Mockito.when(prizeMapper.select(Mockito.any())).thenReturn(value);
        PageData<PrizeModel> prizeModelPageData = prizeService.selectPageList(prizeModel, pageParam);
        PageHelper.clearPage();
        Assert.assertEquals(value.size(), prizeModelPageData.getTotal().intValue());
    }

}
