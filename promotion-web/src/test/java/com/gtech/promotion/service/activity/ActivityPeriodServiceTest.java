package com.gtech.promotion.service.activity;

import com.gtech.promotion.dao.entity.activity.ActivityPeriodEntity;
import com.gtech.promotion.dao.entity.activity.ActivityProductDetailEntity;
import com.gtech.promotion.dao.mapper.activity.ActivityPeriodMapper;
import com.gtech.promotion.dao.mapper.activity.ActivityProductDetailMapper;
import com.gtech.promotion.dao.model.activity.ActivityPeriodModel;
import com.gtech.promotion.dao.model.activity.TPromoActivityProductDetailVO;
import com.gtech.promotion.dto.cache.ProductSkuDetailDTO;
import com.gtech.promotion.service.impl.activity.ActivityPeriodServiceImpl;
import com.gtech.promotion.service.impl.activity.ActivityProductDetailServiceImpl;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import tk.mybatis.mapper.mapperhelper.EntityHelper;
import tk.mybatis.mapper.mapperhelper.MapperHelper;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * @Description :
 * <AUTHOR> gaojie.li
 * @Date : 2021/9/18 11:03
 */
@RunWith(MockitoJUnitRunner.class)
public class ActivityPeriodServiceTest {

    @InjectMocks
    private ActivityPeriodServiceImpl activityPeriodService;

    @InjectMocks
    private ActivityProductDetailServiceImpl activityProductDetailService;

    @Mock
    private ActivityProductDetailMapper productDetailMapper;


    @Mock
    private ActivityPeriodMapper activityPeriodMapper;

    @Before
    public void before(){
        EntityHelper.initEntityNameMap(ActivityPeriodEntity.class, new MapperHelper().getConfig());
        EntityHelper.initEntityNameMap(ActivityProductDetailEntity.class, new MapperHelper().getConfig());

    }

    @Test
    public void insertProductDetail(){

        TPromoActivityProductDetailVO vo = new TPromoActivityProductDetailVO();
        Mockito.when(productDetailMapper.insertSelective(Mockito.any())).thenReturn(1);
        Integer integer = activityProductDetailService.insertProductDetail(vo);
        Assert.assertEquals(1,integer.intValue());
    }

    @Test
    public void queryListByActivityCodesAndProductCode(){
        Set<String> activityCodes = new HashSet<>();
        List<ProductSkuDetailDTO> integer = activityProductDetailService.queryListByActivityCodesAndProductCode(activityCodes,"1");
        Assert.assertEquals(0,integer.size());
    }

    @Test
    public void queryListByActivityCodesAndProductCodes(){
        Set<String> activityCodes = new HashSet<>();
        List<String> productCodes = new ArrayList<>();
        List<ProductSkuDetailDTO> integer = activityProductDetailService.queryListByActivityCodesAndProductCodes(activityCodes,productCodes);
        Assert.assertEquals(0,integer.size());
    }

    @Test
    public void deleteProductDetails_null(){
        Mockito.when(productDetailMapper.delete(Mockito.any())).thenReturn(1);
        Integer integer = activityProductDetailService.deleteProductDetails("1", null);
        Assert.assertEquals(1,integer.intValue());
    }

    @Test
    public void deleteProductDetails(){
        Mockito.when(productDetailMapper.delete(Mockito.any())).thenReturn(1);
        Integer integer = activityProductDetailService.deleteProductDetails("1", 1);
        Assert.assertEquals(1,integer.intValue());
    }



    @Test
    public void createPeriod(){
        ActivityPeriodModel model = new ActivityPeriodModel();
        Mockito.when(activityPeriodMapper.insertSelective(Mockito.any())).thenReturn(1);
        int period = activityPeriodService.createPeriod(model);
        Assert.assertEquals(1,period);
    }

    @Test
    public void queryPeriodByActivityCodes(){
        List<String> activityCodes = new ArrayList<>();
        activityCodes.add("1");
        Mockito.when(activityPeriodMapper.selectByCondition(Mockito.any())).thenReturn(new ArrayList<>());
        List<ActivityPeriodModel> period = activityPeriodService.queryPeriodByActivityCodes("1",activityCodes);
        Assert.assertEquals(0,period.size());
    }
}
