package com.gtech.promotion.service;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.gtech.promotion.code.activity.ActivityStatusEnum;
import com.gtech.promotion.code.activity.ActivityTypeEnum;
import com.gtech.promotion.dao.entity.activity.ActivityEntity;
import com.gtech.promotion.dao.mapper.activity.ActivityMapper;
import com.gtech.promotion.dao.model.activity.ActivityModel;
import com.gtech.promotion.dto.in.activity.GroupQueryListVO;
import com.gtech.promotion.dto.in.activity.TPromoActivityListInDTO;
import com.gtech.promotion.dto.out.activity.GroupActivityVO;
import com.gtech.promotion.page.RequestPage;
import com.gtech.promotion.service.activity.ActivityLanguageService;
import com.gtech.promotion.service.activity.ActivityStoreService;
import com.gtech.promotion.service.impl.activity.ActivityServiceImpl;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import tk.mybatis.mapper.mapperhelper.EntityHelper;
import tk.mybatis.mapper.mapperhelper.MapperHelper;

import java.util.ArrayList;
import java.util.List;

@RunWith(MockitoJUnitRunner.class)
public class ActivityServiceTest {

    @InjectMocks
    private ActivityServiceImpl activityService;

    @Mock
    private ActivityMapper activityMapper;

    @Mock
    private ActivityStoreService storeService;

    @Mock
    private ActivityLanguageService languageService;

    @Before
    public void before(){
        EntityHelper.initEntityNameMap(ActivityEntity.class, new MapperHelper().getConfig());
    }

    @Test
    public void testFindActivity(){
        ActivityEntity entity = new ActivityEntity();
        String activityCode = "1";
        entity.setActivityCode(activityCode);
        Mockito.when(activityMapper.selectOne(Mockito.any())).thenReturn(entity);
        ActivityModel model = activityService.findActivity("2", activityCode, "04");
        Assert.assertEquals(activityCode, model.getActivityCode());
    }

    @Test
    public void testFindActivity_null(){
        ActivityModel model = activityService.findActivity("1", null, null);
        Assert.assertNull(model);
    }

    @Test
    public void testFindEffectiveActivity_null(){
        ActivityModel model = activityService.findEffectiveActivity("1", null);
        Assert.assertNull(model);
    }

    @Test
    public void testQueryEffectiveActivityByTenantCode(){
        String tenantCode = "1";
        List<ActivityEntity> list = new ArrayList<>();
        Mockito.when(activityMapper.selectByCondition(Mockito.any())).thenReturn(list);
        List<ActivityModel> activityModels = activityService.queryEffectiveActivityByTenantCode(tenantCode);
        Assert.assertEquals(list.size(),activityModels.size());
    }

    @Test
    public void testQueryEffectiveActivityByTenantCode_null(){
        String tenantCode = "1";
        List<ActivityModel> activityModels = activityService.queryEffectiveActivityByTenantCode(tenantCode);
        Assert.assertEquals(0,activityModels.size());
    }

    @Test
    public void testQueryEffectiveActivity(){
        List<String> codes = new ArrayList<>();
        codes.add("1");
        List<ActivityEntity> list = new ArrayList<>();
        Mockito.when(activityMapper.selectByCondition(Mockito.any())).thenReturn(list);
        List<ActivityModel> activityEntities = activityService.queryEffectiveActivity("1", codes);
        Assert.assertEquals(list.size(), activityEntities.size());
    }

    @Test
    public void testQueryEffectiveActivity_null(){
        List<ActivityModel> activityEntities = activityService.queryEffectiveActivity("1", null);
        Assert.assertEquals(0, activityEntities.size());
    }

    @Test
    public void testQueryActivityByActivityCodes(){
        List<ActivityEntity> list = new ArrayList<>();
        Mockito.when(activityMapper.selectByCondition(Mockito.any())).thenReturn(list);
        List<ActivityModel> activityEntities = activityService.queryActivityByActivityCodes("1", null);
        Assert.assertEquals(list.size(), activityEntities.size());
    }

    @Test
    public void testQueryAllActivity(){
        TPromoActivityListInDTO inDTO = new TPromoActivityListInDTO();
        inDTO.setOrderByType("02");
        inDTO.setActivityType("01");
        inDTO.setActivityName("1");
        inDTO.setActivityBeginFrom("1");
        inDTO.setActivityBeginTo("1");
        inDTO.setActivityEndFrom("1");
        inDTO.setActivityEndTo("1");
        inDTO.setCreateTimeFrom("1");
        inDTO.setCreateTimeTo("1");
        inDTO.setActivityCode("1");
        inDTO.setOrgCode("1");
        inDTO.setSponsors("1");
        inDTO.setDefaultFlag(true);
        RequestPage page = new RequestPage();
        List<String> templateCodes = new ArrayList<>();

        List<ActivityEntity> list = new ArrayList<>();
        list.add(ActivityEntity.builder().activityCode("1").build());
        List<String> listString = new ArrayList<>();
        listString.add("1");
        Mockito.when(storeService.selectNotByCondition(Mockito.any(), Mockito.any())).thenReturn(listString);
        Mockito.when(activityMapper.selectByCondition(Mockito.any())).thenReturn(list);
        Mockito.doNothing().when(languageService).replaceField(Mockito.any(), Mockito.any());
        PageInfo<ActivityModel> activityModelPageInfo = activityService.queryAllActivity(inDTO, page, templateCodes);
        PageHelper.clearPage();
        Assert.assertEquals(list.size(), activityModelPageInfo.getList().size());
    }

    @Test
    public void testQueryAllActivity_03(){
        // given
        TPromoActivityListInDTO inDTO = new TPromoActivityListInDTO();
        inDTO.setOrderByType("03");
        inDTO.setActivityType("01");
        inDTO.setActivityName("1");
        inDTO.setActivityBeginFrom("1");
        inDTO.setActivityBeginTo("1");
        inDTO.setActivityEndFrom("1");
        inDTO.setActivityEndTo("1");
        inDTO.setCreateTimeFrom("1");
        inDTO.setCreateTimeTo("1");
        inDTO.setActivityCode("1");
        inDTO.setOrgCode("1");
        inDTO.setSponsors("1");
        inDTO.setDefaultFlag(true);

        RequestPage page = new RequestPage();
        List<String> templateCodes = new ArrayList<>();

        List<ActivityEntity> list = new ArrayList<>();
        list.add(ActivityEntity.builder().activityCode("1").build());
        List<String> listString = new ArrayList<>();
        // when
        Mockito.when(storeService.selectNotByCondition(Mockito.any(), Mockito.any())).thenReturn(listString);
        Mockito.when(activityMapper.selectByCondition(Mockito.any())).thenReturn(list);
        Mockito.doNothing().when(languageService).replaceField(Mockito.any(), Mockito.any());
        // then
        PageInfo<ActivityModel> activityModelPageInfo = activityService.queryAllActivity(inDTO, page, templateCodes);
        PageHelper.clearPage();
        Assert.assertEquals(list.size(), activityModelPageInfo.getList().size());
    }

    @Test
    public void testQueryAllActivity_04(){
        TPromoActivityListInDTO inDTO = new TPromoActivityListInDTO();
        inDTO.setOrderByType("04");
        inDTO.setActivityType("01");
        inDTO.setActivityName("1");
        inDTO.setActivityBeginFrom("1");
        inDTO.setActivityBeginTo("1");
        inDTO.setActivityEndFrom("1");
        inDTO.setActivityEndTo("1");
        inDTO.setCreateTimeFrom("1");
        inDTO.setCreateTimeTo("1");
        inDTO.setActivityCode("1");
        inDTO.setOrgCode("1");
        inDTO.setSponsors("1");
        inDTO.setDefaultFlag(true);

        RequestPage page = new RequestPage();
        List<String> templateCodes = new ArrayList<>();

        List<ActivityEntity> list = new ArrayList<>();
        list.add(ActivityEntity.builder().activityCode("1").build());
        List<String> listString = new ArrayList<>();
        Mockito.when(storeService.selectNotByCondition(Mockito.any(), Mockito.any())).thenReturn(listString);
        Mockito.when(activityMapper.selectByCondition(Mockito.any())).thenReturn(list);
        Mockito.doNothing().when(languageService).replaceField(Mockito.any(), Mockito.any());
        PageInfo<ActivityModel> activityModelPageInfo = activityService.queryAllActivity(inDTO, page, templateCodes);
        PageHelper.clearPage();
        Assert.assertEquals(list.size(), activityModelPageInfo.getList().size());
    }

    @Test
    public void testQueryAllActivity_05(){
        TPromoActivityListInDTO inDTO = new TPromoActivityListInDTO();
        inDTO.setOrderByType("05");
        inDTO.setActivityType("01");
        inDTO.setActivityName("1");
        inDTO.setActivityBeginFrom("1");
        inDTO.setActivityBeginTo("1");
        inDTO.setActivityEndFrom("1");
        inDTO.setActivityEndTo("1");
        inDTO.setCreateTimeFrom("1");
        inDTO.setCreateTimeTo("1");
        inDTO.setActivityCode("1");
        inDTO.setOrgCode("1");
        inDTO.setSponsors("1");
        inDTO.setDefaultFlag(true);
        RequestPage page = new RequestPage();
        List<String> templateCodes = new ArrayList<>();

        List<ActivityEntity> list = new ArrayList<>();
        list.add(ActivityEntity.builder().activityCode("1").build());
        List<String> listString = new ArrayList<>();
        Mockito.when(storeService.selectNotByCondition(Mockito.any(), Mockito.any())).thenReturn(listString);
        Mockito.when(activityMapper.selectByCondition(Mockito.any())).thenReturn(list);
        Mockito.doNothing().when(languageService).replaceField(Mockito.any(), Mockito.any());
        PageInfo<ActivityModel> activityModelPageInfo = activityService.queryAllActivity(inDTO, page, templateCodes);
        PageHelper.clearPage();
        Assert.assertEquals(list.size(), activityModelPageInfo.getList().size());
    }

    @Test
    public void testQueryAllActivity_06(){
        TPromoActivityListInDTO inDTO = new TPromoActivityListInDTO();
        inDTO.setOrderByType("06");
        inDTO.setActivityType("01");
        inDTO.setActivityName("1");
        inDTO.setActivityBeginFrom("1");
        inDTO.setActivityBeginTo("1");
        inDTO.setActivityEndFrom("1");
        inDTO.setActivityEndTo("1");
        inDTO.setCreateTimeFrom("1");
        inDTO.setCreateTimeTo("1");
        inDTO.setActivityCode("1");
        inDTO.setOrgCode("1");
        inDTO.setSponsors("1");
        inDTO.setDefaultFlag(true);
        RequestPage page = new RequestPage();
        List<String> templateCodes = new ArrayList<>();

        List<ActivityEntity> list = new ArrayList<>();
        list.add(ActivityEntity.builder().activityCode("1").build());
        List<String> listString = new ArrayList<>();
        Mockito.when(storeService.selectNotByCondition(Mockito.any(), Mockito.any())).thenReturn(listString);
        Mockito.when(activityMapper.selectByCondition(Mockito.any())).thenReturn(list);
        Mockito.doNothing().when(languageService).replaceField(Mockito.any(), Mockito.any());
        PageInfo<ActivityModel> activityModelPageInfo = activityService.queryAllActivity(inDTO, page, templateCodes);
        PageHelper.clearPage();
        Assert.assertEquals(list.size(), activityModelPageInfo.getList().size());
    }

    @Test
    public void testQueryActivityByTenantCode(){
        List<ActivityEntity> list = new ArrayList<>();
        Mockito.when(activityMapper.selectByCondition(Mockito.any())).thenReturn(list);
        List<ActivityModel> activityEntities = activityService.queryActivityByTenantCode("1", ActivityTypeEnum.ACTIVITY, new RequestPage(), ActivityStatusEnum.CLOSURE);
        Assert.assertEquals(list.size(), activityEntities.size());
    }

    @Test
    public void testQueryActivityByTenantCode1(){
        List<ActivityEntity> list = new ArrayList<>();
        Mockito.when(activityMapper.selectByCondition(Mockito.any())).thenReturn(list);
        List<ActivityModel> activityEntities = activityService.queryActivityByTenantCode("1", ActivityTypeEnum.ACTIVITY, new RequestPage(), ActivityStatusEnum.CLOSURE, ActivityStatusEnum.END);
        Assert.assertEquals(list.size(), activityEntities.size());
    }

    @Test
    public void testQueryActivityByTenantCode2(){
        List<ActivityEntity> list = new ArrayList<>();
        Mockito.when(activityMapper.selectByCondition(Mockito.any())).thenReturn(list);
        List<ActivityModel> activityEntities = activityService.queryActivityByTenantCode("1", null, null, null);
        Assert.assertEquals(list.size(), activityEntities.size());
    }

    @Test
    public void testQueryActivityByCodes(){
        List<String> codes = new ArrayList<>();
        codes.add("1");
        List<ActivityEntity> list = new ArrayList<>();
        Mockito.when(activityMapper.selectByCondition(Mockito.any())).thenReturn(list);
        List<ActivityEntity> activityEntities = activityService.queryActivityByCodes("1", codes);
        Assert.assertEquals(list.size(), activityEntities.size());
    }

    @Test
    public void testQueryActivityByTenantCodeAndStatusAndType(){
        List<ActivityEntity> list = new ArrayList<>();
        Mockito.when(activityMapper.selectByCondition(Mockito.any())).thenReturn(list);
        List<ActivityEntity> activityEntities = activityService.queryActivityByTenantCodeAndStatusAndType("1", 1, null);
        Assert.assertEquals(list.size(), activityEntities.size());
    }

    @Test
    public void testEndPromoActivity(){
        Mockito.when(activityMapper.updateByConditionSelective(Mockito.any(), Mockito.any())).thenReturn(1);
        int l = activityService.endPromoActivity();
        Assert.assertEquals(1, l);
    }

    @Test
    public void testDeleteActivity111(){
        Mockito.when(activityMapper.delete(Mockito.any())).thenReturn(1);
        int l = activityService.deleteActivity111("1", "1");
        Assert.assertEquals(1, l);
    }

    @Test
    public void testEffectActivity(){
        Mockito.when(activityMapper.selectCount(Mockito.any())).thenReturn(1);
        long l = activityService.effectActivity();
        Assert.assertEquals(1L, l);
    }

    @Test
    public void testAccumulativeTotal_time(){
        List<String> list = new ArrayList<>();
        list.add("1");
        Mockito.when(activityMapper.selectCountByCondition(Mockito.any())).thenReturn(1);
        long l = activityService.accumulativeTotal("1", "e", list);
        Assert.assertEquals(1L, l);
    }

    @Test
    public void testAccumulativeTotal_time_null(){
        List<String> list = new ArrayList<>();
        Mockito.when(activityMapper.selectCountByCondition(Mockito.any())).thenReturn(1);
        long l = activityService.accumulativeTotal("", "", list);
        Assert.assertEquals(1L, l);
    }

    @Test
    public void testAccumulativeTotal(){
        List<String> list = new ArrayList<>();
        list.add("1");
        Mockito.when(activityMapper.selectCountByCondition(Mockito.any())).thenReturn(1);
        long l = activityService.accumulativeTotal(list);
        Assert.assertEquals(1L, l);
    }

    @Test
    public void testAccumulativeTotal_null_list(){
        List<String> list = new ArrayList<>();
        Mockito.when(activityMapper.selectCountByCondition(Mockito.any())).thenReturn(1);
        long l = activityService.accumulativeTotal(list);
        Assert.assertEquals(1L, l);
    }

    @Test
    public void testQueryEffectActivityByTenantCode(){
        Mockito.when(activityMapper.selectCount(Mockito.any())).thenReturn(1);
        long l = activityService.queryEffectActivityByTenantCode("1");
        Assert.assertEquals(1L, l);
    }

    @Test
    public void testQueryActivityByTenantCodeAndStatusAndTime(){
        List<ActivityEntity> activityEntities = new ArrayList<>();
        Mockito.when(activityMapper.selectByCondition(Mockito.any())).thenReturn(activityEntities);
        List<ActivityModel> activityModels = activityService.queryActivityByTenantCodeAndStatusAndTime("1", "04", "01",null);
        Assert.assertEquals(activityEntities.size(), activityModels.size());
    }

    @Test
    public void expireActivity(){
        Mockito.when(activityMapper.updateByConditionSelective(Mockito.any(), Mockito.any())).thenReturn(1);
        int i = activityService.expireActivity();
        Assert.assertEquals(1, i);
    }

    @Test
    public void queryActivityListUnderGroup(){

        GroupQueryListVO vo = new GroupQueryListVO();

        RequestPage requestPage = new RequestPage();
        requestPage.setPageNo(1);
        requestPage.setPageCount(10);

        List<ActivityEntity> activityEntities = new ArrayList<>();

        Mockito.when(activityMapper.selectByCondition(Mockito.any())).thenReturn(activityEntities);

        PageInfo<GroupActivityVO> pageInfo = activityService.queryActivityListUnderGroup(vo, requestPage);

        Assert.assertEquals(0,pageInfo.getTotal());


    }

    @Test
    public void queryActivityListUnderGroup_not_empty(){

        GroupQueryListVO vo = new GroupQueryListVO();
        vo.setActivityName("!");
        vo.setActivityOrgCode("@");
        vo.setDomainCode("!");
        vo.setTenantCode("!");
        vo.setGroupCode("1");
        vo.setLanguage("en-id");
        List<String> list = new ArrayList<>();
        list.add("02");

        vo.setActivityStatus(list);
        RequestPage requestPage = new RequestPage();
        requestPage.setPageNo(1);
        requestPage.setPageCount(10);

        List<ActivityEntity> activityEntities = new ArrayList<>();
        ActivityEntity activityEntity = new ActivityEntity();
        activityEntity.setGroupCode("!");
        activityEntities.add(activityEntity);

        Mockito.when(activityMapper.selectByCondition(Mockito.any())).thenReturn(activityEntities);

        PageInfo<GroupActivityVO> pageInfo = activityService.queryActivityListUnderGroup(vo, requestPage);

        Assert.assertEquals(1,pageInfo.getTotal());


    }

}
