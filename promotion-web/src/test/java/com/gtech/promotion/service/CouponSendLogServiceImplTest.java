package com.gtech.promotion.service;

import com.github.pagehelper.PageInfo;
import com.gtech.promotion.dao.entity.coupon.PromoCouponSendLogEntity;
import com.gtech.promotion.dao.mapper.coupon.PromoCouponSendLogMapper;
import com.gtech.promotion.dao.model.activity.PromoCouponSendDetailModel;
import com.gtech.promotion.dao.model.activity.PromoCouponSendLogModel;
import com.gtech.promotion.service.coupon.CouponSendDetailService;
import com.gtech.promotion.service.impl.coupon.CouponSendLogServiceImpl;
import com.gtech.promotion.vo.param.coupon.QueryCouponSendLogParam;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import tk.mybatis.mapper.mapperhelper.EntityHelper;
import tk.mybatis.mapper.mapperhelper.MapperHelper;

import java.util.ArrayList;
import java.util.List;

/**
 * @Description :
 * <AUTHOR> gaojie.li
 * @Date : 2023/9/19 11:44
 */
@RunWith(MockitoJUnitRunner.class)
public class CouponSendLogServiceImplTest {

    @InjectMocks
    private CouponSendLogServiceImpl couponSendLogService;
    @Mock
    private PromoCouponSendLogMapper promoCouponSendLogMapper;
    @Mock
    private CouponSendDetailService couponSendDetailService;

    @Before
    public void before(){

        EntityHelper.initEntityNameMap(PromoCouponSendLogEntity.class, new MapperHelper().getConfig());

    }

    @Test
    public void createCouponSendDetail(){

        int couponSendDetail = couponSendLogService.createCouponSendLog(null);
        Assert.assertEquals(0,couponSendDetail);
    }

    @Test
    public void createCouponSendDetail_not_empty(){
        PromoCouponSendLogModel promoCouponSendLogModel = new PromoCouponSendLogModel();

        List<PromoCouponSendDetailModel> list = new ArrayList<>();
        PromoCouponSendDetailModel promoCouponSendDetailModel = new PromoCouponSendDetailModel();
        list.add(promoCouponSendDetailModel);
        promoCouponSendLogModel.setCouponSendDetailModelList(list);
        int couponSendDetail = couponSendLogService.createCouponSendLog(promoCouponSendLogModel);
        Assert.assertEquals(0,couponSendDetail);
    }

    @Test
    public void createCouponSendDetail_not_empty1(){

        PromoCouponSendLogModel promoCouponSendLogModel = new PromoCouponSendLogModel();
        List<PromoCouponSendDetailModel> list = new ArrayList<>();
        promoCouponSendLogModel.setCouponSendDetailModelList(list);
        int couponSendDetail = couponSendLogService.createCouponSendLog(promoCouponSendLogModel);
        Assert.assertEquals(0,couponSendDetail);
    }

    @Test
    public void updateCouponSendLog(){
        PromoCouponSendLogModel promoCouponSendLogModel = new PromoCouponSendLogModel();
        promoCouponSendLogModel.setStatus(1);
        promoCouponSendLogModel.setFailReason("11");
        promoCouponSendLogModel.setCouponQty(11);
        promoCouponSendLogModel.setUserQty(1);
        couponSendLogService.updateCouponSendLog(promoCouponSendLogModel);
    }

    @Test
    public void updateCouponSendLog_is_null(){
        PromoCouponSendLogModel promoCouponSendLogModel = new PromoCouponSendLogModel();

        couponSendLogService.updateCouponSendLog(promoCouponSendLogModel);
    }

    @Test
    public void queryCouponSendLogList(){

        QueryCouponSendLogParam param = new QueryCouponSendLogParam();
        param.setActivityCode("!");
        param.setTenantCode("!1");
        param.setReleaseCode("1");
        PageInfo<PromoCouponSendLogEntity> promoCouponSendLogEntityPageInfo = couponSendLogService.queryCouponSendLogList(param);
        Assert.assertEquals(0,promoCouponSendLogEntityPageInfo.getList().size());
    }


    @Test(expected = NullPointerException.class)
    public void queryCouponSendLogList_empty(){

        QueryCouponSendLogParam param = new QueryCouponSendLogParam();
        Mockito.when(promoCouponSendLogMapper.selectByExample(Mockito.any())).thenThrow(new RuntimeException("1"));

        PageInfo<PromoCouponSendLogEntity> promoCouponSendLogEntityPageInfo = couponSendLogService.queryCouponSendLogList(param);

        Assert.assertEquals(0,promoCouponSendLogEntityPageInfo.getList().size());
    }

}
