package com.gtech.promotion.service.flash;

import com.gtech.promotion.dao.entity.marketing.flashsale.FlashSaleOrderEntity;
import com.gtech.promotion.dao.mapper.marketing.flashsale.FlashSaleOrderMapper;
import com.gtech.promotion.dao.model.marketing.flashsale.FlashPreSaleOrderModel;
import com.gtech.promotion.dao.model.marketing.flashsale.FlashSaleOrderModel;
import com.gtech.promotion.service.flashsale.impl.FlashSaleOrderServiceImpl;
import com.gtech.promotion.vo.result.flashpresale.ExportFlashPreSaleDto;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import tk.mybatis.mapper.mapperhelper.EntityHelper;
import tk.mybatis.mapper.mapperhelper.MapperHelper;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021-09-18 10:45
 */
@RunWith(MockitoJUnitRunner.class)
public class FlashSaleOrderServiceTest {
    @InjectMocks
    private FlashSaleOrderServiceImpl flashSaleOrderDetailService;
    @Mock
    private FlashSaleOrderMapper orderMapper;

    @Before
    public void before(){
        EntityHelper.initEntityNameMap(FlashSaleOrderEntity.class, new MapperHelper().getConfig());
    }

    @Test
    public void findByOrderNo(){
        flashSaleOrderDetailService.findByOrderNo("1","1");
    }

    @Test
    public void updateStatus(){
        flashSaleOrderDetailService.updateStatus("1","1","1");
    }


    @Test
    public void checkLeaderPayOrder(){

        int i = flashSaleOrderDetailService.checkLeaderPayOrder("1", "1", "1", "1");

        Assert.assertEquals(0,i);

    }

    @Test
    public void queryOrderByMarketingGroupCode(){
        List<FlashSaleOrderModel> flashSaleOrderModels = flashSaleOrderDetailService.queryOrderByMarketingGroupCode("1", "1", "1");
        Assert.assertEquals(0,flashSaleOrderModels.size());
    }


    @Test
    public void queryOrderByCondition(){

        ExportFlashPreSaleDto dto = new ExportFlashPreSaleDto();
        dto.setMaxOrderCode("");

        List<FlashPreSaleOrderModel> modelList = flashSaleOrderDetailService.queryOrderByCondition(dto);
        Assert.assertEquals(0,modelList.size());
    }

    @Test
    public void queryOrderByConditionNot_empty(){

        ExportFlashPreSaleDto dto = new ExportFlashPreSaleDto();
        dto.setMaxOrderCode("111");
        List<FlashSaleOrderEntity> flashSaleOrderEntities = new ArrayList<>();
        FlashSaleOrderEntity entity = new FlashSaleOrderEntity();

        entity.setCreateTime(new Date());
        flashSaleOrderEntities.add(entity);

        Mockito.when(orderMapper.selectByCondition(Mockito.any())).thenReturn(flashSaleOrderEntities);

        List<FlashPreSaleOrderModel> modelList = flashSaleOrderDetailService.queryOrderByCondition(dto);
        Assert.assertEquals(1,modelList.size());
    }
}
