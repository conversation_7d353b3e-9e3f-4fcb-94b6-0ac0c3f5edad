package com.gtech.promotion.service;

import com.gtech.promotion.dao.entity.coupon.TPromoCouponCodeUserEntity;
import com.gtech.promotion.dto.in.activity.ManagementDataInDTO;
import com.gtech.promotion.service.impl.coupon.SqlPublicMethodsServiceImpl;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.junit.MockitoJUnitRunner;
import tk.mybatis.mapper.entity.Example;
import tk.mybatis.mapper.mapperhelper.EntityHelper;
import tk.mybatis.mapper.mapperhelper.MapperHelper;

import java.util.ArrayList;
import java.util.List;

/**
 * @Description :
 * <AUTHOR> gaojie.li
 * @Date : 2021/9/22 11:49
 */
@RunWith(MockitoJUnitRunner.class)
public class SqlPublicMethodsServiceTest {

    @InjectMocks
    private SqlPublicMethodsServiceImpl sqlPublicMethodsService;


    @Before
    public void before(){
        EntityHelper.initEntityNameMap(TPromoCouponCodeUserEntity.class, new MapperHelper().getConfig());
    }


    @Test
    public void  sqlSelectCondition_not_empty(){

        ManagementDataInDTO dataInDTO = new ManagementDataInDTO();
        Example example = new Example(TPromoCouponCodeUserEntity.class);
        example.orderBy(TPromoCouponCodeUserEntity.C_ID).desc();
        Example.Criteria criteria = example.createCriteria();
        dataInDTO.setCouponCode("1");
        List<String> couponTypes = new ArrayList<>();
        couponTypes.add("01");
        List<String> couponStatus = new ArrayList<>();
        couponStatus.add("01");
        dataInDTO.setCouponStatus(couponStatus);
        dataInDTO.setCouponTypes(couponTypes);
        sqlPublicMethodsService.sqlSelectCondition(dataInDTO,criteria);
    }

    @Test
    public void  sqlSelectCondition_empty(){
        ManagementDataInDTO dataInDTO = new ManagementDataInDTO();
        Example example = new Example(TPromoCouponCodeUserEntity.class);
        example.orderBy(TPromoCouponCodeUserEntity.C_ID).desc();
        Example.Criteria criteria = example.createCriteria();
        dataInDTO.setCouponCode("");
        List<String> couponTypes = new ArrayList<>();
        List<String> couponStatus = new ArrayList<>();
        dataInDTO.setCouponStatus(couponStatus);
        dataInDTO.setCouponTypes(couponTypes);
        sqlPublicMethodsService.sqlSelectCondition(dataInDTO,criteria);
    }

    @Test
    public void  sqlSelectCouponCodeCondition_1(){
        ManagementDataInDTO dataInDTO = new ManagementDataInDTO();
        Example example = new Example(TPromoCouponCodeUserEntity.class);
        example.orderBy(TPromoCouponCodeUserEntity.C_ID).desc();
        Example.Criteria criteria = example.createCriteria();
        dataInDTO.setCouponCode("1");
        List<String> couponTypes = new ArrayList<>();
        List<String> couponStatus = new ArrayList<>();
        dataInDTO.setCouponStatus(couponStatus);
        dataInDTO.setCouponTypes(couponTypes);
        dataInDTO.setIsUseStatus("1");
        sqlPublicMethodsService.sqlSelectCouponCodeCondition(dataInDTO,criteria);
    }
    @Test
    public void  sqlSelectCouponCodeCondition_0(){
        ManagementDataInDTO dataInDTO = new ManagementDataInDTO();
        Example example = new Example(TPromoCouponCodeUserEntity.class);
        example.orderBy(TPromoCouponCodeUserEntity.C_ID).desc();
        Example.Criteria criteria = example.createCriteria();
        dataInDTO.setCouponCode("");
        List<String> couponTypes = new ArrayList<>();
        List<String> couponStatus = new ArrayList<>();
        dataInDTO.setCouponStatus(couponStatus);
        dataInDTO.setCouponTypes(couponTypes);
        dataInDTO.setIsUseStatus("0");
        sqlPublicMethodsService.sqlSelectCouponCodeCondition(dataInDTO,criteria);
    }

}
