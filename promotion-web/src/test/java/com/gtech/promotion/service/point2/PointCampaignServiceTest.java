package com.gtech.promotion.service.point2;

import com.gtech.commons.redis.GTechCodeGenerator;
import com.gtech.commons.utils.DateUtil;
import com.gtech.promotion.dao.entity.point.PointCampaignEntity;
import com.gtech.promotion.dao.mapper.point.PointCampaignMapper;
import com.gtech.promotion.service.point.impl.PointCampaignServiceImpl;
import com.gtech.promotion.vo.param.point.PointCampaignParam;
import com.gtech.promotion.vo.param.point.UpdatePointParam;
import com.gtech.promotion.vo.param.point.query.PointCampaignUniqueParam;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.dao.DuplicateKeyException;
import tk.mybatis.mapper.mapperhelper.EntityHelper;
import tk.mybatis.mapper.mapperhelper.MapperHelper;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RunWith(MockitoJUnitRunner.class)
public class PointCampaignServiceTest {

    @InjectMocks
    private PointCampaignServiceImpl pointCampaignService;

    @Mock
    PointCampaignMapper pointCampaignMapper;

    @Mock
    GTechCodeGenerator codeGenerator;

    @Before
    public void before() {
        EntityHelper.initEntityNameMap(PointCampaignEntity.class, new MapperHelper().getConfig());
    }

    @Test
    public void savePointCampaign() {
        PointCampaignParam uniqueVo = new PointCampaignParam();
        uniqueVo.setCampaignCode("100001");
        Mockito.when(pointCampaignMapper.insert(Mockito.any())).thenReturn(1);
        Assert.assertNotNull(pointCampaignService.savePointCampaign(uniqueVo));
    }

    @Test(expected = Exception.class)
    public void savePointCampaign1() {
        PointCampaignParam uniqueVo = new PointCampaignParam();
        uniqueVo.setCampaignCode("100001");
        Assert.assertNotNull(pointCampaignService.savePointCampaign(null));
    }

    @Test
    public void savePointCampaign2() {
        PointCampaignParam uniqueVo = new PointCampaignParam();
        Mockito.when(pointCampaignMapper.insert(Mockito.any())).thenReturn(1);
        Assert.assertNull(pointCampaignService.savePointCampaign(uniqueVo));
    }

    @Test(expected = Exception.class)
    public void savePointCampaign3() {
        PointCampaignParam uniqueVo = new PointCampaignParam();
        uniqueVo.setCampaignCode("100001");
        Mockito.when(pointCampaignMapper.insert(Mockito.any())).thenThrow(DuplicateKeyException.class);
        Assert.assertNotNull(pointCampaignService.savePointCampaign(uniqueVo));
    }

    @Test(expected = Exception.class)
    public void savePointCampaign4() {
        PointCampaignParam uniqueVo = new PointCampaignParam();
        uniqueVo.setCampaignCode("100001");
        Mockito.when(pointCampaignMapper.insert(Mockito.any())).thenThrow(NullPointerException.class);
        Assert.assertNotNull(pointCampaignService.savePointCampaign(uniqueVo));
    }

    @Test
    public void updatePointCampaignStatus() {
        PointCampaignUniqueParam.PointCampaignStatusUniqueVo uniqueVo = new PointCampaignUniqueParam.PointCampaignStatusUniqueVo();
        uniqueVo.setCampaignCode("100001");
        uniqueVo.setTenantCode("100001");
        Mockito.when(pointCampaignMapper.updateByExampleSelective(Mockito.any(), Mockito.any())).thenReturn(1);
        pointCampaignService.updatePointCampaignStatus(uniqueVo);
    }

    @Test
    public void updatePointCampaignStatus1() {
        PointCampaignUniqueParam.PointCampaignStatusUniqueVo uniqueVo = new PointCampaignUniqueParam.PointCampaignStatusUniqueVo();
        uniqueVo.setTenantCode("100001");
        pointCampaignService.updatePointCampaignStatus(uniqueVo);
    }

    @Test(expected = Exception.class)
    public void updatePointCampaignStatus2() {
        PointCampaignUniqueParam.PointCampaignStatusUniqueVo uniqueVo = new PointCampaignUniqueParam.PointCampaignStatusUniqueVo();
        uniqueVo.setCampaignCode("100001");
        uniqueVo.setTenantCode("100001");
        Mockito.when(pointCampaignMapper.updateByExampleSelective(Mockito.any(), Mockito.any())).thenThrow(NullPointerException.class);
        pointCampaignService.updatePointCampaignStatus(uniqueVo);
    }

    @Test(expected = Exception.class)
    public void updatePointCampaignStatus3() {
        PointCampaignUniqueParam.PointCampaignStatusUniqueVo uniqueVo = new PointCampaignUniqueParam.PointCampaignStatusUniqueVo();
        uniqueVo.setCampaignCode("100001");
        uniqueVo.setTenantCode("100001");
        Mockito.when(pointCampaignMapper.updateByExampleSelective(Mockito.any(), Mockito.any())).thenReturn(0);
        pointCampaignService.updatePointCampaignStatus(uniqueVo);
    }

    @Test
    public void updatePoint() {
        UpdatePointParam uniqueVo = new UpdatePointParam();
        uniqueVo.setCampaignCode("100001");
        uniqueVo.setTenantCode("100001");
        uniqueVo.setTransactionType(1);
        PointCampaignEntity pointCampaignEntity = new PointCampaignEntity();
        pointCampaignEntity.setStatus(1);
        pointCampaignEntity.setTotalPoints(1);
        pointCampaignEntity.setEndTime(DateUtil.format(DateUtil.addDay(1), DateUtil.FORMAT_YYYYMMDDHHMISS_14));
        Mockito.when(pointCampaignMapper.selectOne(Mockito.any())).thenReturn(pointCampaignEntity);
        Mockito.when(pointCampaignMapper.updatePoint(Mockito.any())).thenReturn(1);
        int i = pointCampaignService.updatePoint(uniqueVo);
        Assert.assertEquals(1, i);
    }

    @Test
    public void updatePoint1() {
        UpdatePointParam uniqueVo = new UpdatePointParam();
        uniqueVo.setCampaignCode("100001");
        uniqueVo.setTenantCode("100001");
        uniqueVo.setTransactionType(1);
        PointCampaignEntity pointCampaignEntity = new PointCampaignEntity();
        pointCampaignEntity.setStatus(0);
        pointCampaignEntity.setTotalPoints(1);
        pointCampaignEntity.setEndTime(DateUtil.format(DateUtil.addDay(1), DateUtil.FORMAT_YYYYMMDDHHMISS_14));
        Mockito.when(pointCampaignMapper.selectOne(Mockito.any())).thenReturn(pointCampaignEntity);
        int i = pointCampaignService.updatePoint(uniqueVo);
        Assert.assertEquals(0, i);
    }

    @Test
    public void updatePoint2() {
        UpdatePointParam uniqueVo = new UpdatePointParam();
        uniqueVo.setCampaignCode("100001");
        uniqueVo.setTenantCode("100001");
        uniqueVo.setTransactionType(1);
        PointCampaignEntity pointCampaignEntity = new PointCampaignEntity();
        pointCampaignEntity.setStatus(1);
        pointCampaignEntity.setTotalPoints(1);
        pointCampaignEntity.setEndTime("2021-09-09 10:42:23");
        Mockito.when(pointCampaignMapper.selectOne(Mockito.any())).thenReturn(pointCampaignEntity);
        int i = pointCampaignService.updatePoint(uniqueVo);
        Assert.assertEquals(0, i);
    }

    @Test
    public void updatePoint3() {
        UpdatePointParam uniqueVo = new UpdatePointParam();
        uniqueVo.setCampaignCode("100001");
        uniqueVo.setTenantCode("100001");
        uniqueVo.setTransactionType(1);
        PointCampaignEntity pointCampaignEntity = new PointCampaignEntity();
        pointCampaignEntity.setStatus(1);
        pointCampaignEntity.setTotalPoints(-1);
        pointCampaignEntity.setEndTime(DateUtil.format(DateUtil.addDay(1), DateUtil.FORMAT_YYYYMMDDHHMISS_14));
        Mockito.when(pointCampaignMapper.selectOne(Mockito.any())).thenReturn(pointCampaignEntity);
        int i = pointCampaignService.updatePoint(uniqueVo);
        Assert.assertEquals(1, i);
    }

    @Test
    public void updatePoint4() {
        UpdatePointParam uniqueVo = new UpdatePointParam();
        uniqueVo.setCampaignCode("100001");
        uniqueVo.setTenantCode("100001");
        uniqueVo.setTransactionType(2);
        PointCampaignEntity pointCampaignEntity = new PointCampaignEntity();
        pointCampaignEntity.setStatus(1);
        pointCampaignEntity.setTotalPoints(1);
        pointCampaignEntity.setEndTime(DateUtil.format(DateUtil.addDay(1), DateUtil.FORMAT_YYYYMMDDHHMISS_14));
        Mockito.when(pointCampaignMapper.selectOne(Mockito.any())).thenReturn(pointCampaignEntity);
        int i = pointCampaignService.updatePoint(uniqueVo);
        Assert.assertEquals(1, i);
    }

    @Test
    public void updatePoint5() {
        UpdatePointParam uniqueVo = new UpdatePointParam();
        uniqueVo.setCampaignCode("100001");
        uniqueVo.setTenantCode("100001");
        uniqueVo.setTransactionType(1);
        PointCampaignEntity pointCampaignEntity = new PointCampaignEntity();
        pointCampaignEntity.setStatus(1);
        pointCampaignEntity.setTotalPoints(1);
        pointCampaignEntity.setEndTime(DateUtil.format(DateUtil.addDay(1), DateUtil.FORMAT_YYYYMMDDHHMISS_14));
        Mockito.when(pointCampaignMapper.selectOne(Mockito.any())).thenReturn(pointCampaignEntity);
        Mockito.when(pointCampaignMapper.updatePoint(Mockito.any())).thenThrow(NullPointerException.class);
        int i = pointCampaignService.updatePoint(uniqueVo);
        Assert.assertEquals(0, i);
    }

    @Test
    public void queryPointCampaignEndTime() {
        UpdatePointParam uniqueVo = new UpdatePointParam();
        uniqueVo.setCampaignCode("100001");
        uniqueVo.setTenantCode("100001");
        uniqueVo.setTransactionType(1);
        PointCampaignEntity pointCampaignEntity = new PointCampaignEntity();
        pointCampaignEntity.setStatus(1);
        pointCampaignEntity.setTotalPoints(1);
        pointCampaignEntity.setEndTime(DateUtil.format(DateUtil.addDay(1), DateUtil.FORMAT_YYYYMMDDHHMISS_14));
        Assert.assertNotNull(pointCampaignService.queryPointCampaignEndTime());
    }

    @Test
    public void queryPointCampaignEndTime1() {
        UpdatePointParam uniqueVo = new UpdatePointParam();
        uniqueVo.setCampaignCode("100001");
        uniqueVo.setTenantCode("100001");
        uniqueVo.setTransactionType(1);
        PointCampaignEntity pointCampaignEntity = new PointCampaignEntity();
        pointCampaignEntity.setStatus(1);
        pointCampaignEntity.setTotalPoints(1);
        pointCampaignEntity.setEndTime(DateUtil.format(DateUtil.addDay(1), DateUtil.FORMAT_YYYYMMDDHHMISS_14));
        List<PointCampaignEntity> pointCampaignEntities = new ArrayList<>();
        pointCampaignEntities.add(pointCampaignEntity);
        Mockito.when(pointCampaignMapper.selectByExample(Mockito.any())).thenReturn(pointCampaignEntities);
        Mockito.when(pointCampaignMapper.updateByPrimaryKeySelective(Mockito.any())).thenReturn(1);
        Assert.assertNotNull(pointCampaignService.queryPointCampaignEndTime());
    }

    @Test
    public void updatePointCampaign() {
        PointCampaignParam uniqueVo = new PointCampaignParam();
        uniqueVo.setCampaignCode("100001");
        uniqueVo.setTenantCode("100001");
        PointCampaignEntity pointCampaignEntity = new PointCampaignEntity();
        pointCampaignEntity.setStatus(1);
        pointCampaignEntity.setTotalPoints(1);
        pointCampaignEntity.setEndTime(DateUtil.format(DateUtil.addDay(1), DateUtil.FORMAT_YYYYMMDDHHMISS_14));
        Mockito.when(pointCampaignMapper.selectOne(Mockito.any())).thenReturn(new PointCampaignEntity());
        Mockito.when(pointCampaignMapper.updateByPrimaryKeySelective(Mockito.any())).thenReturn(1);
        pointCampaignService.updatePointCampaign(uniqueVo);
    }

    @Test(expected = Exception.class)
    public void updatePointCampaign1() {
        PointCampaignParam uniqueVo = new PointCampaignParam();
        uniqueVo.setTenantCode("100001");
        PointCampaignEntity pointCampaignEntity = new PointCampaignEntity();
        pointCampaignEntity.setStatus(1);
        pointCampaignEntity.setTotalPoints(1);
        pointCampaignEntity.setEndTime(DateUtil.format(DateUtil.addDay(1), DateUtil.FORMAT_YYYYMMDDHHMISS_14));
        pointCampaignService.updatePointCampaign(uniqueVo);
    }

    @Test(expected = Exception.class)
    public void updatePointCampaign2() {
        PointCampaignParam uniqueVo = new PointCampaignParam();
        uniqueVo.setCampaignCode("100001");
        uniqueVo.setTenantCode("100001");
        PointCampaignEntity pointCampaignEntity = new PointCampaignEntity();
        pointCampaignEntity.setStatus(1);
        pointCampaignEntity.setTotalPoints(1);
        pointCampaignEntity.setEndTime(DateUtil.format(DateUtil.addDay(1), DateUtil.FORMAT_YYYYMMDDHHMISS_14));
        Mockito.when(pointCampaignMapper.selectOne(Mockito.any())).thenThrow(NullPointerException.class);
        pointCampaignService.updatePointCampaign(uniqueVo);
    }

    @Test(expected = Exception.class)
    public void updatePointCampaign3() {
        PointCampaignParam uniqueVo = new PointCampaignParam();
        uniqueVo.setCampaignCode("100001");
        uniqueVo.setTenantCode("100001");
        PointCampaignEntity pointCampaignEntity = new PointCampaignEntity();
        pointCampaignEntity.setStatus(1);
        pointCampaignEntity.setTotalPoints(1);
        pointCampaignEntity.setEndTime(DateUtil.format(DateUtil.addDay(1), DateUtil.FORMAT_YYYYMMDDHHMISS_14));
        Mockito.when(pointCampaignMapper.selectOne(Mockito.any())).thenReturn(new PointCampaignEntity());
        Mockito.when(pointCampaignMapper.updateByPrimaryKeySelective(Mockito.any())).thenThrow(DuplicateKeyException.class);
        pointCampaignService.updatePointCampaign(uniqueVo);
    }

    @Test(expected = Exception.class)
    public void updatePointCampaign4() {
        PointCampaignParam uniqueVo = new PointCampaignParam();
        uniqueVo.setCampaignCode("100001");
        uniqueVo.setTenantCode("100001");
        PointCampaignEntity pointCampaignEntity = new PointCampaignEntity();
        pointCampaignEntity.setStatus(1);
        pointCampaignEntity.setTotalPoints(1);
        pointCampaignEntity.setEndTime(DateUtil.format(DateUtil.addDay(1), DateUtil.FORMAT_YYYYMMDDHHMISS_14));
        Mockito.when(pointCampaignMapper.selectOne(Mockito.any())).thenReturn(new PointCampaignEntity());
        Mockito.when(pointCampaignMapper.updateByPrimaryKeySelective(Mockito.any())).thenThrow(NullPointerException.class);
        pointCampaignService.updatePointCampaign(uniqueVo);
    }

    @Test
    public void getPointCampaign() {
        PointCampaignUniqueParam uniqueVo = new PointCampaignUniqueParam();
        uniqueVo.setCampaignCode("100001");
        uniqueVo.setTenantCode("100001");
        PointCampaignEntity pointCampaignEntity = new PointCampaignEntity();
        pointCampaignEntity.setStatus(1);
        pointCampaignEntity.setTotalPoints(1);
        pointCampaignEntity.setEndTime(DateUtil.format(DateUtil.addDay(1), DateUtil.FORMAT_YYYYMMDDHHMISS_14));
        Mockito.when(pointCampaignMapper.selectOne(Mockito.any())).thenReturn(new PointCampaignEntity());
        Assert.assertNotNull(pointCampaignService.getPointCampaign(uniqueVo));
    }

    @Test
    public void getPointCampaign1() {
        PointCampaignUniqueParam uniqueVo = new PointCampaignUniqueParam();
        uniqueVo.setCampaignCode("100001");
        uniqueVo.setTenantCode("100001");
        PointCampaignEntity pointCampaignEntity = new PointCampaignEntity();
        pointCampaignEntity.setStatus(1);
        pointCampaignEntity.setTotalPoints(1);
        pointCampaignEntity.setEndTime(DateUtil.format(DateUtil.addDay(1), DateUtil.FORMAT_YYYYMMDDHHMISS_14));
        Mockito.when(pointCampaignMapper.selectOne(Mockito.any())).thenReturn(null);
        Assert.assertNull(pointCampaignService.getPointCampaign(uniqueVo));
    }

    @Test
    public void queryPointCampaignPage() {
        Map<String, Object> map = new HashMap<>();
        Mockito.when(pointCampaignMapper.query(Mockito.any())).thenReturn(new ArrayList<>());
        Assert.assertNotNull(pointCampaignService.queryPointCampaignPage(map));
    }

    @Test(expected =Exception.class)
    public void queryPointCampaignPage1() {
        Map<String, Object> map = new HashMap<>();
        Mockito.when(pointCampaignMapper.query(Mockito.any())).thenThrow(NullPointerException.class);
        Assert.assertNotNull(pointCampaignService.queryPointCampaignPage(map));
    }

}
