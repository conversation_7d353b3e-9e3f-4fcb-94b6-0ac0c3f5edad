package com.gtech.promotion.service.activity;

import com.github.pagehelper.PageInfo;
import com.gtech.promotion.code.activity.ActivityStatusEnum;
import com.gtech.promotion.dao.entity.activity.ActivityEntity;
import com.gtech.promotion.dao.entity.activity.TPromoActivityExpressionEntity;
import com.gtech.promotion.dao.entity.activity.TPromoActivityIncentiveEntity;
import com.gtech.promotion.dao.entity.activity.TemplateEntity;
import com.gtech.promotion.dao.mapper.activity.ActivityMapper;
import com.gtech.promotion.dao.mapper.activity.TPromoActivityExpressionMapper;
import com.gtech.promotion.dao.mapper.activity.TPromoActivityIncentiveMapper;
import com.gtech.promotion.dao.mapper.activity.TemplateMapper;
import com.gtech.promotion.dao.model.activity.ActivityModel;
import com.gtech.promotion.dao.model.activity.TemplateModel;
import com.gtech.promotion.dto.in.activity.GroupBindingActivityVO;
import com.gtech.promotion.dto.in.activity.TPromoActivityListInDTO;
import com.gtech.promotion.dto.in.activity.UpdateExternalActivityInDTO;
import com.gtech.promotion.dto.out.activity.TPromoActivityOutDTO;
import com.gtech.promotion.exception.PromotionException;
import com.gtech.promotion.helper.ActivityRedisHelpler;
import com.gtech.promotion.page.RequestPage;
import com.gtech.promotion.service.impl.activity.ActivityServiceImpl;
import com.gtech.promotion.service.impl.activity.TPromoActivityExpressionServiceImpl;
import com.gtech.promotion.service.impl.activity.TPromoActivityIncentiveServiceImpl;
import com.gtech.promotion.service.impl.activity.TemplateServiceImpl;
import com.gtech.promotion.vo.param.activity.QueryListParam;
import com.gtech.promotion.vo.param.activity.QueryPromoListByStoreParam;
import com.gtech.promotion.vo.result.activity.QueryListResult;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.data.redis.core.StringRedisTemplate;
import tk.mybatis.mapper.mapperhelper.EntityHelper;
import tk.mybatis.mapper.mapperhelper.MapperHelper;

import java.util.ArrayList;
import java.util.List;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

/**
 * @Description :
 * <AUTHOR> gaojie.li
 * @Date : 2021/9/18 11:19
 */
@RunWith(MockitoJUnitRunner.class)
public class ActivityServiceTest {

    @InjectMocks
    private TPromoActivityExpressionServiceImpl promoActivityExpressionService;

    @InjectMocks
    private TPromoActivityIncentiveServiceImpl tPromoActivityIncentiveService;

    @InjectMocks
    private ActivityServiceImpl activityService;

    @InjectMocks
    private TemplateServiceImpl templateService;

    @Mock
    private TPromoActivityIncentiveMapper incentiveMapper;


    //mock的对象不能删除
    @Mock
    private TemplateMapper templateMapper;

    @Mock
    private ActivityMapper activityMapper;

    @Mock
    private ActivityStoreService storeService;//店铺渠道

    @Mock
    private ActivityLanguageService languageService;//店铺渠道

    @Mock
    private TPromoActivityExpressionMapper expressionMapper;

    @Mock
    private ActivityRedisHelpler redisService;

    @Mock
    private StringRedisTemplate redisTemplate;

    @Before
    public void before(){
        EntityHelper.initEntityNameMap(ActivityEntity.class, new MapperHelper().getConfig());
        EntityHelper.initEntityNameMap(TemplateEntity.class, new MapperHelper().getConfig());
        EntityHelper.initEntityNameMap(TPromoActivityIncentiveEntity.class, new MapperHelper().getConfig());
    }


    @Test
    public void getUserActivityTimes111() {
//        Mockito.when(expressionMapper.selectOne(Mockito.any())).thenReturn(null);
        Mockito.when(incentiveMapper.select(Mockito.any())).thenReturn(new ArrayList<>());
        Long userActivityTimes111 = tPromoActivityIncentiveService.getUserActivityTimes111("", "1", "");
        Assert.assertEquals(0,userActivityTimes111.intValue());
    }

    @Test(expected = PromotionException.class)
    public void updateActivityExpression(){
        Mockito.when(expressionMapper.selectOne(Mockito.any())).thenReturn(null);
        promoActivityExpressionService.updateActivityExpression("1","1");
    }

    @Test
    public void getActivityExpressionNull(){
        Mockito.when(redisService.getExpression(Mockito.any())).thenReturn(null);
        Mockito.when(expressionMapper.selectOne(Mockito.any())).thenReturn(null);
        String activityExpression = promoActivityExpressionService.getActivityExpression("1");
        Assert.assertNotNull(activityExpression);
    }

    @Test
    public void getActivityExpressionNotNull(){
        TPromoActivityExpressionEntity expressionEntity = new TPromoActivityExpressionEntity();
        expressionEntity.setExpression("121");
        Mockito.when(redisService.getExpression(Mockito.any())).thenReturn(null);
        Mockito.when(expressionMapper.updateByPrimaryKeySelective(Mockito.any())).thenReturn(1);
        Mockito.when(expressionMapper.selectOne(Mockito.any())).thenReturn(expressionEntity);
        String activityExpression = promoActivityExpressionService.getActivityExpression("1");
        Assert.assertNotNull(activityExpression);

    }

    @Test
    public void getTemplateById(){
        TemplateModel activity = templateService.getTemplateById("1");
        Assert.assertEquals(null,activity);
    }


    @Test
    public void findActivity(){
        ActivityModel activity = activityService.findActivity(null, null, null);
        Assert.assertEquals(null,activity);
    }


    @Test
    public void findEffectiveActivity(){
        ActivityModel activity = activityService.findEffectiveActivity(null, null);
        Assert.assertEquals(null,activity);
    }

    @Test
    public void queryEffectiveActivity(){
        List<String> activityCodes = new ArrayList<>();
        List<ActivityModel> activityModels = activityService.queryEffectiveActivity(null, activityCodes);
        Assert.assertEquals(0,activityModels.size());
    }

    @Test
    public void queryAllActivity(){
        List<String> templateCodes = new ArrayList<>();
        TPromoActivityListInDTO inDTO = new TPromoActivityListInDTO();
        inDTO.setOpsType("1");
        inDTO.setChannelCode("1");
        inDTO.setDefaultFlag(true);
        RequestPage page = new RequestPage();
        PageInfo<ActivityModel> activityModelPageInfo = activityService.queryAllActivity(inDTO, page, templateCodes);
        Assert.assertEquals(0,activityModelPageInfo.getList().size());
    }

    @Test
    public void queryActivityByTenantCode(){
        RequestPage page = new RequestPage();
        List<ActivityModel> activityModelPageInfo = activityService.queryActivityByTenantCode("1", null,page, null);
        Assert.assertEquals(0,activityModelPageInfo.size());
    }

    @Test
    public void queryActivityByTenantCode1(){

        RequestPage page = new RequestPage();
        List<ActivityModel> activityModelPageInfo = activityService.queryActivityByTenantCode("1", null,page, ActivityStatusEnum.CLOSURE);
        Assert.assertEquals(0,activityModelPageInfo.size());
    }

    @Test
    public void queryActivityByTenantCodeAndStatusAndType(){

        List<ActivityEntity> activityEntities = activityService.queryActivityByTenantCodeAndStatusAndType("1", null, null);
        Assert.assertEquals(0,activityEntities.size());
    }

    @Test
    public void queryPromoListByStore(){
        QueryPromoListByStoreParam param = new QueryPromoListByStoreParam();
        PageInfo<TPromoActivityOutDTO> pageInfo = activityService.queryPromoListByStore(param);
        Assert.assertEquals(0,pageInfo.getList().size());
    }

    @Test
    public void queryList(){
        QueryListParam param = new QueryListParam();
        PageInfo<QueryListResult> queryListResultPageInfo = activityService.queryList(param);
        Assert.assertEquals(0,queryListResultPageInfo.getList().size());
    }

    @Test
    public void existPromotionCategory() {
        int i = activityService.queryPromotionCategoryCount("1","1");
        Assert.assertEquals(0, i);
    }

    @Test
    public void updatePromotionCategoryNull() {
        int i = activityService.updatePromotionCategoryNull("1","1");
        Assert.assertEquals(0, i);
    }

    @Test
    public void findActivityNewByActivityCode_null() {
        List<String> list = new ArrayList<>();
        String activityNewByActivityCode = activityService.findActivityNewByActivityCode("1", list);
        Assert.assertEquals(null, activityNewByActivityCode);
    }


    @Test
    public void findActivityNewByActivityCode() {
        List<String> list = new ArrayList<>();

        List<ActivityEntity> activityEntities = new ArrayList<>();
        ActivityEntity activityEntity = new ActivityEntity();
        activityEntity.setActivityCode("1");
        activityEntities.add(activityEntity);
        when(activityMapper.selectByCondition(any())).thenReturn(activityEntities);

        String activityNewByActivityCode = activityService.findActivityNewByActivityCode("1", list);
        Assert.assertEquals("1", activityNewByActivityCode);
    }

    @Test
    public void queryActivityByGroupCode() {

        List<ActivityEntity> activityEntities = new ArrayList<>();

        ActivityEntity activityEntity = new ActivityEntity();
        activityEntity.setActivityCode("1");
        activityEntities.add(activityEntity);

        when(activityMapper.selectByCondition(any())).thenReturn(activityEntities);

        List<ActivityModel> activityModels = activityService.queryActivityByGroupCode("1", "1");
        Assert.assertEquals(1, activityModels.size());
    }

    @Test
    public void bindingActivityToGroup() {

        List<ActivityEntity> activityEntities = new ArrayList<>();

        ActivityEntity activityEntity = new ActivityEntity();
        activityEntity.setActivityCode("1");
        activityEntities.add(activityEntity);

        when(activityMapper.updateByConditionSelective(any(),any())).thenReturn(1);

        GroupBindingActivityVO vo = new GroupBindingActivityVO();
        vo.setGroupCode("1");
        int i = activityService.bindingActivityToGroup(vo);
        Assert.assertEquals(1, i);
    }


    @Test
    public void updateExternalActivityId() {

        when(activityMapper.updateByConditionSelective(any(),any())).thenReturn(1);

        UpdateExternalActivityInDTO vo = new UpdateExternalActivityInDTO();

        int i = activityService.updateExternalActivityId(vo);
        Assert.assertEquals(1, i);
    }
}
