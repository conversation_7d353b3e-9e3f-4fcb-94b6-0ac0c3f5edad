package com.gtech.promotion.service.marketing;

import com.github.pagehelper.PageHelper;
import com.gtech.commons.page.PageData;
import com.gtech.commons.page.PageParam;
import com.gtech.promotion.code.coupon.CouponFrozenStatusEnum;
import com.gtech.promotion.code.marketing.TicketStatusEnum;
import com.gtech.promotion.dao.entity.marketing.TicketEntity;
import com.gtech.promotion.dao.mapper.marketing.TicketMapper;
import com.gtech.promotion.dao.model.marketing.BaseModel;
import com.gtech.promotion.dao.model.marketing.TicketModel;
import com.gtech.promotion.domain.marketing.LuckyDrawMemberChanceDomain;
import com.gtech.promotion.service.marketing.impl.TicketServiceImpl;
import com.gtech.promotion.vo.result.marketing.LuckyDrawMemberChanceResult;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import tk.mybatis.mapper.mapperhelper.EntityHelper;
import tk.mybatis.mapper.mapperhelper.MapperHelper;

import java.util.ArrayList;
import java.util.List;

@RunWith(MockitoJUnitRunner.class)
public class TicketServiceTest {

    @InjectMocks
    private TicketServiceImpl ticketService;

    @Mock
    private TicketMapper ticketMapper;

    @Before
    public void before(){
        EntityHelper.initEntityNameMap(TicketEntity.class, new MapperHelper().getConfig());
    }

    @Test
    public void selectListByMemberCode(){
        BaseModel baseModel = new BaseModel();
        Mockito.when(ticketMapper.selectByCondition(Mockito.any())).thenReturn(new ArrayList<>());
        List<TicketModel> list = ticketService.selectListByMemberCode(baseModel, "", "");
        Assert.assertEquals(0, list.size());
    }

    @Test
    public void queryChanceList(){
        LuckyDrawMemberChanceDomain domain = new LuckyDrawMemberChanceDomain();
        Mockito.when(ticketMapper.queryChanceList(Mockito.any())).thenReturn(new ArrayList<>());
        PageData<LuckyDrawMemberChanceResult> pageData = ticketService.queryChanceList(domain);
        PageHelper.clearPage();
        Assert.assertEquals(0, pageData.getTotal().intValue());
    }

    @Test
    public void queryListByStatus(){
        TicketModel ticketModel = new TicketModel();
        ticketModel.setStatus(TicketStatusEnum.USED.code());
        PageParam pageParam = new PageParam();
        Mockito.when(ticketMapper.selectByCondition(Mockito.any())).thenReturn(new ArrayList<>());
        PageData<TicketModel> ticketModelPageData = ticketService.queryListByStatus(ticketModel, pageParam);
        PageHelper.clearPage();
        Assert.assertEquals(0, ticketModelPageData.getTotal().intValue());
    }

    @Test
    public void queryListByStatus1(){
        TicketModel ticketModel = new TicketModel();
        PageParam pageParam = new PageParam();
        Mockito.when(ticketMapper.selectByCondition(Mockito.any())).thenReturn(new ArrayList<>());
        PageData<TicketModel> ticketModelPageData = ticketService.queryListByStatus(ticketModel, pageParam);
        PageHelper.clearPage();
        Assert.assertEquals(0, ticketModelPageData.getTotal().intValue());
    }

    @Test
    public void updateByTicketCode(){
        TicketModel ticketModel = new TicketModel();
        Mockito.when(ticketMapper.updateByConditionSelective(Mockito.any(), Mockito.any())).thenReturn(1);
        int i = ticketService.updateByTicketCode(ticketModel, "1");
        Assert.assertEquals(1,i);
    }

    @Test
    public void selectCountByMemberCode(){
        BaseModel baseModel = new BaseModel();
        baseModel.setDomainCode("1");
        baseModel.setTenantCode("1");
        baseModel.setActivityCode("1");
        Mockito.when(ticketMapper.selectCountByMemberCode(Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(1);
        Integer integer = ticketService.selectCountByMemberCode(baseModel, "1", "1", "1");
        Assert.assertEquals(1,(long)integer);
    }

    @Test
    public void selectActivityByStatus(){
        BaseModel baseModel = new BaseModel();
        baseModel.setDomainCode("1");
        baseModel.setTenantCode("1");
        baseModel.setActivityCode("1");
        Mockito.when(ticketMapper.selectCount(Mockito.any())).thenReturn(1);
        int i = ticketService.selectActivityByStatus(baseModel, "1");
        Assert.assertEquals(1,i);
    }

    @Test
    public void updateFrozenStatus_no_ticket(){
        LuckyDrawMemberChanceDomain domain = new LuckyDrawMemberChanceDomain();
        domain.setFrozenStatus(CouponFrozenStatusEnum.UN_FROZEN.code());
        Mockito.when(ticketMapper.selectByCondition(Mockito.any())).thenReturn(new ArrayList<>());
        ticketService.updateFrozenStatus(domain);
    }


    @Test
    public void updateFrozenStatus(){
        LuckyDrawMemberChanceDomain domain = new LuckyDrawMemberChanceDomain();
        domain.setQuality(1);
        domain.setFrozenStatus(CouponFrozenStatusEnum.FROZENED.code());
        ArrayList<TicketEntity> ticketEntities = new ArrayList<>();
        ticketEntities.add(new TicketEntity());
        Mockito.when(ticketMapper.selectByCondition(Mockito.any())).thenReturn(ticketEntities);
        ticketService.updateFrozenStatus(domain);
    }

    @Test
    public void updateFrozenStatus_10(){
        LuckyDrawMemberChanceDomain domain = new LuckyDrawMemberChanceDomain();
        domain.setQuality(0);
        domain.setFrozenStatus(CouponFrozenStatusEnum.FROZENED.code());
        ArrayList<TicketEntity> ticketEntities = new ArrayList<>();
        ticketEntities.add(new TicketEntity());
        Mockito.when(ticketMapper.selectByCondition(Mockito.any())).thenReturn(ticketEntities);
        ticketService.updateFrozenStatus(domain);
    }


    @Test
    public void updateFrozenStatus_11(){
        LuckyDrawMemberChanceDomain domain = new LuckyDrawMemberChanceDomain();
        domain.setQuality(1);
        domain.setLogicDelete(1);
        domain.setFrozenStatus(CouponFrozenStatusEnum.FROZENED.code());
        ArrayList<TicketEntity> ticketEntities = new ArrayList<>();
        ticketEntities.add(new TicketEntity());
        Mockito.when(ticketMapper.selectByCondition(Mockito.any())).thenReturn(ticketEntities);
        ticketService.updateFrozenStatus(domain);
    }

    @Test
    public void updateFrozenStatus_12(){
        LuckyDrawMemberChanceDomain domain = new LuckyDrawMemberChanceDomain();
        domain.setQuality(0);
        domain.setLogicDelete(1);
        domain.setFrozenStatus(CouponFrozenStatusEnum.FROZENED.code());
        ArrayList<TicketEntity> ticketEntities = new ArrayList<>();
        ticketEntities.add(new TicketEntity());
        Mockito.when(ticketMapper.selectByCondition(Mockito.any())).thenReturn(ticketEntities);
        ticketService.updateFrozenStatus(domain);
    }

}
