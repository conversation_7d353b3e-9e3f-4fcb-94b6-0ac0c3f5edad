package com.gtech.promotion.service;

import com.gtech.promotion.code.coupon.CouponReleaseStatusEnum;
import com.gtech.promotion.code.coupon.CouponRuleTypeEnum;
import com.gtech.promotion.code.coupon.CouponTypeEnum;
import com.gtech.promotion.dao.entity.coupon.TPromoCouponReleaseEntity;
import com.gtech.promotion.dao.mapper.coupon.TPromoCouponReleaseMapper;
import com.gtech.promotion.dao.model.coupon.CouponReleaseModel;
import com.gtech.promotion.dao.model.coupon.ReleaseCouponVO;
import com.gtech.promotion.domain.coupon.CouponReleaseDomain;
import com.gtech.promotion.domain.coupon.CouponReleaseInventoryDomain;
import com.gtech.promotion.service.impl.coupon.PromoCouponReleaseServiceImpl;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import tk.mybatis.mapper.mapperhelper.EntityHelper;
import tk.mybatis.mapper.mapperhelper.MapperHelper;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

/**
 * @Description :
 * <AUTHOR> gaojie.li
 * @Date : 2021/8/4 10:51
 */
@RunWith(MockitoJUnitRunner.class)
public class PromoCouponReleaseServiceTest {


    @InjectMocks
    private PromoCouponReleaseServiceImpl promoCouponInnerCodeService;

    @Mock
    private TPromoCouponReleaseMapper tPromoCouponReleaseMapper;

    @Before
    public void before() {
        EntityHelper.initEntityNameMap(TPromoCouponReleaseEntity.class, new MapperHelper().getConfig());
    }


    @Test
    public void testQueryCanReceiveReleasesByTime() {
        // Arrange
        String tenantCode = "T001";
        String activityCode = "A001";
        String receiveStartTime = "2023-01-01 00:00:00";
        String receiveEndTime = "2023-01-31 23:59:59";

        TPromoCouponReleaseEntity mockEntity = new TPromoCouponReleaseEntity();
        // Set properties to match the search criteria
        mockEntity.setTenantCode(tenantCode);
        mockEntity.setActivityCode(activityCode);
        mockEntity.setReleaseStatus(CouponReleaseStatusEnum.CREATE_COUPON.code());
        mockEntity.setReceiveEndTime(receiveEndTime);
        mockEntity.setReceiveStartTime(receiveStartTime);

        List<TPromoCouponReleaseEntity> mockEntities = Arrays.asList(mockEntity);

        // Assume BeanCopyUtils.jsonCopyList is correctly implemented and will convert entities to domains
        List<CouponReleaseDomain> expectedDomains = Arrays.asList(new CouponReleaseDomain());

        when(tPromoCouponReleaseMapper.selectByCondition(any())).thenReturn(mockEntities);

        // Act
        List<CouponReleaseDomain> actualDomains = promoCouponInnerCodeService.queryCanReceiveReleasesByTime(tenantCode, activityCode, receiveStartTime, receiveEndTime);


    }



    @Test(expected = NullPointerException.class)
    public void createCouponRelease(){

        CouponReleaseModel domain= new CouponReleaseModel();

        domain.setCouponRuleType(CouponRuleTypeEnum.DIGIT.code());
        domain.setCouponType(CouponTypeEnum.ANONYMITY_COUPON.code());

        TPromoCouponReleaseEntity entity = new TPromoCouponReleaseEntity();

        TPromoCouponReleaseEntity mock = mock(TPromoCouponReleaseEntity.class);
        mock.setId(1L);

        when(tPromoCouponReleaseMapper.insertSelective(Mockito.any())).thenReturn(1);

        String couponRelease = promoCouponInnerCodeService.createCouponRelease(domain);

        Assert.assertEquals(entity.getId(),couponRelease);
    }

    @Test(expected = NullPointerException.class)
    public void createCouponRelease11(){

        CouponReleaseModel domain= new CouponReleaseModel();

        domain.setCouponRuleType("");
        domain.setCouponType(CouponTypeEnum.ANONYMITY_COUPON.code());

        TPromoCouponReleaseEntity entity = new TPromoCouponReleaseEntity();

        TPromoCouponReleaseEntity mock = mock(TPromoCouponReleaseEntity.class);
        mock.setId(1L);

        when(tPromoCouponReleaseMapper.insertSelective(Mockito.any())).thenReturn(1);

        String couponRelease = promoCouponInnerCodeService.createCouponRelease(domain);

        Assert.assertEquals(entity.getId(),couponRelease);
    }

    @Test(expected = NullPointerException.class)
    public void createCouponRelease22(){

        CouponReleaseModel domain= new CouponReleaseModel();

        domain.setCouponRuleType("");
        domain.setCouponType(CouponTypeEnum.PROMOTION_CODE.code());

        TPromoCouponReleaseEntity entity = new TPromoCouponReleaseEntity();

        TPromoCouponReleaseEntity mock = mock(TPromoCouponReleaseEntity.class);
        mock.setId(1L);

        when(tPromoCouponReleaseMapper.insertSelective(Mockito.any())).thenReturn(1);

        String couponRelease = promoCouponInnerCodeService.createCouponRelease(domain);

        Assert.assertEquals(entity.getId(),couponRelease);
    }

    @Test(expected = NullPointerException.class)
    public void createCouponRelease33(){

        CouponReleaseModel domain= new CouponReleaseModel();

        domain.setCouponRuleType("");
        domain.setCouponType(CouponTypeEnum.PROMOTION_COUPON.code());

        TPromoCouponReleaseEntity entity = new TPromoCouponReleaseEntity();

        TPromoCouponReleaseEntity mock = mock(TPromoCouponReleaseEntity.class);
        mock.setId(1L);

        when(tPromoCouponReleaseMapper.insertSelective(Mockito.any())).thenReturn(1);

        String couponRelease = promoCouponInnerCodeService.createCouponRelease(domain);

        Assert.assertEquals(entity.getId(),couponRelease);
    }

    @Test
    public void updateCouponReleaseByReleaseCode() {
        CouponReleaseDomain domain = new CouponReleaseDomain();
        when(tPromoCouponReleaseMapper.updateByConditionSelective(Mockito.any(), Mockito.any())).thenReturn(1);
        int i = promoCouponInnerCodeService.updateCouponReleaseByReleaseCode(domain);
        Assert.assertEquals(1, i);
    }

    @Test
    public void queryCouponRelease() {
        when(tPromoCouponReleaseMapper.select(Mockito.any())).thenReturn(new ArrayList<>());
        List<CouponReleaseModel> i = promoCouponInnerCodeService.queryCouponRelease("12312");
        Assert.assertEquals(0, i.size());
    }

    @Test
    public void queryReleaseCount111() {
        List<TPromoCouponReleaseEntity> entities = new ArrayList<>();
        TPromoCouponReleaseEntity releaseEntity = new TPromoCouponReleaseEntity();
        releaseEntity.setCouponType("02");
        releaseEntity.setReleaseStatus("04");
        releaseEntity.setReleaseType("02");
        TPromoCouponReleaseEntity releaseEntity1 = new TPromoCouponReleaseEntity();
        releaseEntity1.setCouponType("02");
        releaseEntity1.setReleaseStatus("");
        releaseEntity1.setReleaseType("01");
        releaseEntity1.setReleaseQuantity("1");
        entities.add(releaseEntity);
        entities.add(releaseEntity1);
        when(tPromoCouponReleaseMapper.selectByCondition(Mockito.any())).thenReturn(entities);
        Long i = promoCouponInnerCodeService.queryReleaseCount111("12312");
        Assert.assertEquals(1, i.intValue());
    }

    @Test
    public void queryReleasesNotReleased() {
        when(tPromoCouponReleaseMapper.selectByCondition(Mockito.any())).thenReturn(new ArrayList<>());
        List<CouponReleaseModel> i = promoCouponInnerCodeService.queryReleasesNotReleased();
        Assert.assertEquals(0, i.size());
    }

    @Test
    public void stopCouponRelease() {
        when(tPromoCouponReleaseMapper.updateByConditionSelective(Mockito.any(), Mockito.any())).thenReturn(0);
        int i = promoCouponInnerCodeService.stopCouponRelease("1");
        Assert.assertEquals(0, i);
    }


    @Test
    public void deleteCouponRelease() {
        when(tPromoCouponReleaseMapper.delete(Mockito.any())).thenReturn(0);
        int i = promoCouponInnerCodeService.deleteCouponRelease("1", "1");
        Assert.assertEquals(0, i);
    }

    @Test
    public void queryCanReceiveReleases() {
        when(tPromoCouponReleaseMapper.selectByCondition(Mockito.any())).thenReturn(new ArrayList<>());
        List<CouponReleaseDomain> i = promoCouponInnerCodeService.queryCanReceiveReleases("1", "1", "1");
        Assert.assertEquals(0, i.size());
    }

    @Test
    public void queryReleasesByActivityCodes() {
        List<String> activityCodes = new ArrayList<>();
        when(tPromoCouponReleaseMapper.selectByCondition(Mockito.any())).thenReturn(new ArrayList<>());
		List<CouponReleaseDomain> i = promoCouponInnerCodeService.queryReleasesByActivityCodes("1", activityCodes, true);
        Assert.assertEquals(0, i.size());
    }

    @Test
    public void deductInventory_0() {
        List<CouponReleaseDomain> canReleaseDomains = new ArrayList<>();
        CouponReleaseDomain domain = new CouponReleaseDomain();
        domain.setInventory(0);
        canReleaseDomains.add(domain);
        CouponReleaseDomain domain1 = new CouponReleaseDomain();
        domain1.setInventory(1);
        canReleaseDomains.add(domain1);

        TPromoCouponReleaseEntity entity = new TPromoCouponReleaseEntity();
        entity.setReleaseQuantity("2");
        entity.setInventory("1");
        when(tPromoCouponReleaseMapper.selectOne(Mockito.any())).thenReturn(entity);
        List<CouponReleaseInventoryDomain> i = promoCouponInnerCodeService.deductInventory("1", "1", canReleaseDomains, 1);
        Assert.assertEquals(0, i.size());
    }

    @Test
    public void deductInventory_1() {
        List<CouponReleaseDomain> canReleaseDomains = new ArrayList<>();
        CouponReleaseDomain domain = new CouponReleaseDomain();
        domain.setInventory(0);
        canReleaseDomains.add(domain);
        CouponReleaseDomain domain1 = new CouponReleaseDomain();
        domain1.setInventory(1);
        canReleaseDomains.add(domain1);
        TPromoCouponReleaseEntity entity = new TPromoCouponReleaseEntity();
        entity.setReleaseQuantity("2");
        entity.setInventory("1");
        List<CouponReleaseInventoryDomain> i = promoCouponInnerCodeService.deductInventory("1", "1", canReleaseDomains, 0);
        Assert.assertEquals(0, i.size());
    }

    @Test
    public void deductInventory_2() {
        List<CouponReleaseDomain> canReleaseDomains = new ArrayList<>();
        CouponReleaseDomain domain = new CouponReleaseDomain();
        domain.setInventory(0);
        canReleaseDomains.add(domain);
        CouponReleaseDomain domain1 = new CouponReleaseDomain();
        domain1.setInventory(1);
        canReleaseDomains.add(domain1);
        TPromoCouponReleaseEntity entity = new TPromoCouponReleaseEntity();
        entity.setReleaseQuantity("2");
        entity.setInventory("1");
        Mockito.when(tPromoCouponReleaseMapper.deductInventory(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.anyInt())).thenReturn(1);
        List<CouponReleaseInventoryDomain> i = promoCouponInnerCodeService.deductInventory("1", "1", canReleaseDomains, 1);
    }

    @Test
    public void deductInventory_3() {
        List<CouponReleaseDomain> canReleaseDomains = new ArrayList<>();
        CouponReleaseDomain domain = new CouponReleaseDomain();
        domain.setInventory(0);
        canReleaseDomains.add(domain);
        CouponReleaseDomain domain1 = new CouponReleaseDomain();
        domain1.setInventory(1);
        canReleaseDomains.add(domain1);
        TPromoCouponReleaseEntity entity = new TPromoCouponReleaseEntity();
        entity.setReleaseQuantity("2");
        entity.setInventory("1");
        Mockito.when(tPromoCouponReleaseMapper.deductInventory(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.anyInt())).thenReturn(0).thenReturn(1);
        when(tPromoCouponReleaseMapper.selectOne(Mockito.any())).thenReturn(entity);
        List<CouponReleaseInventoryDomain> i = promoCouponInnerCodeService.deductInventory("1", "1", canReleaseDomains, 1);
    }

    @Test
    public void queryCouponReleaseActivityCode() {
        when(tPromoCouponReleaseMapper.selectByCondition(Mockito.any())).thenReturn(new ArrayList<>());
        List<CouponReleaseModel> i = promoCouponInnerCodeService.queryCouponReleaseActivityCode("1", "1", "1", "2");
        Assert.assertEquals(0, i.size());
    }

    @Test
    public void queryCouponReleaseActivityCode1() {
        when(tPromoCouponReleaseMapper.selectByCondition(Mockito.any())).thenReturn(new ArrayList<>());
        List<CouponReleaseModel> i = promoCouponInnerCodeService.queryCouponReleaseActivityCode("1", "", "1", "");
        Assert.assertEquals(0, i.size());
    }

    @Test
    public void findCanReceiveRelease() {
        TPromoCouponReleaseEntity entity = new TPromoCouponReleaseEntity();
        when(tPromoCouponReleaseMapper.selectOneByExample(Mockito.any())).thenReturn(entity);
        CouponReleaseModel canReceiveRelease = promoCouponInnerCodeService.findCanReceiveRelease("1", "1");
        Assert.assertEquals(null, canReceiveRelease.getInventory());
    }

    @Test
    public void findCanUseRelease() {
        TPromoCouponReleaseEntity entity = new TPromoCouponReleaseEntity();
        when(tPromoCouponReleaseMapper.selectOneByExample(Mockito.any())).thenReturn(entity);
        CouponReleaseModel canReceiveRelease = promoCouponInnerCodeService.findCanUseRelease("1", "1");
        Assert.assertEquals(null, canReceiveRelease.getInventory());
    }

    @Test
    public void queryReleaseCouponRecord() {
        when(tPromoCouponReleaseMapper.queryReleaseCouponRecord(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(new ArrayList<>());
        List<ReleaseCouponVO> releaseCouponVOS = promoCouponInnerCodeService.queryReleaseCouponRecord("1", "1", "1", 1, 1);
        Assert.assertEquals(0, releaseCouponVOS.size());
    }

    @Test
    public void queryReleaseByCondition() {
        when(tPromoCouponReleaseMapper.selectByCondition(Mockito.any())).thenReturn(new ArrayList<>());
        List<CouponReleaseDomain> releaseCouponVOS = promoCouponInnerCodeService.queryReleaseByCondition("1", "1", new ArrayList<>());
        Assert.assertEquals(0, releaseCouponVOS.size());
    }

    @Test
    public void queryReleaseByCondition1() {

        List<String> a = new ArrayList<>();
        a.add("11");
        when(tPromoCouponReleaseMapper.selectByCondition(Mockito.any())).thenReturn(new ArrayList<>());
        List<CouponReleaseDomain> releaseCouponVOS = promoCouponInnerCodeService.queryReleaseByCondition("1", "", a);
        Assert.assertEquals(0, releaseCouponVOS.size());
    }

    @Test
    public void queryActivityCodeByReceiveTimeEmpty() {
        List<String> list = promoCouponInnerCodeService.queryActivityCodeByReceiveTime("1", "", "");
        Assert.assertEquals(0, list.size());
    }

    @Test
    public void queryActivityCodeByReceiveTime() {
        when(tPromoCouponReleaseMapper.selectByCondition(Mockito.any())).thenReturn(new ArrayList<>());
        List<String> list = promoCouponInnerCodeService.queryActivityCodeByReceiveTime("1", "1", "1");

        Assert.assertEquals(0, list.size());
    }


    @Test
    public void queryCouponReleaseByReleaseCodes() {

        List<String> list =  new ArrayList<>();
        list.add("1");
        when(tPromoCouponReleaseMapper.selectByCondition(Mockito.any())).thenReturn(new ArrayList<>());

        List<CouponReleaseDomain> releaseDomains = promoCouponInnerCodeService.queryCouponReleaseByReleaseCodes("1", list);

        Assert.assertEquals(0, releaseDomains.size());
    }


    @Test
    public void queryActivityCodeByReceiveTime1() {
        when(tPromoCouponReleaseMapper.selectByCondition(Mockito.any())).thenReturn(new ArrayList<>());
        List<String> list = promoCouponInnerCodeService.queryActivityCodeByReceiveTime("1", "", "1");

        Assert.assertEquals(0, list.size());
    }

    @Test
    public void queryActivityCodeByReceiveTime2() {
        when(tPromoCouponReleaseMapper.selectByCondition(Mockito.any())).thenReturn(new ArrayList<>());
        List<String> list = promoCouponInnerCodeService.queryActivityCodeByReceiveTime("1", "1", "");

        Assert.assertEquals(0, list.size());
    }
}
