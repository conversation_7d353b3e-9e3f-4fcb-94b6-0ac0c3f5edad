package com.gtech.promotion.service.marketing;

import com.github.pagehelper.PageInfo;
import com.gtech.promotion.code.marketing.UserGroupStatusEnum;
import com.gtech.promotion.dao.entity.marketing.MarketingGroupUserEntity;
import com.gtech.promotion.dao.mapper.marketing.MarketingGroupUserMapper;
import com.gtech.promotion.dao.model.marketing.GroupUserCountDto;
import com.gtech.promotion.dao.model.marketing.MarketingGroupUserMode;
import com.gtech.promotion.service.marketing.impl.MarketingGroupUserServiceImpl;
import com.gtech.promotion.utils.DateUtil;
import com.gtech.promotion.vo.param.marketing.MarketingGroupUserListParam;
import com.gtech.promotion.vo.param.marketing.MarketingGroupUserParam;
import com.gtech.promotion.vo.param.marketing.flashsale.FindGroupUserParam;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import tk.mybatis.mapper.mapperhelper.EntityHelper;
import tk.mybatis.mapper.mapperhelper.MapperHelper;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

/**
 * @Description :
 * <AUTHOR> gaojie.li
 * @Date : 2023/8/7 17:45
 */
@RunWith(MockitoJUnitRunner.class)
public class MarketingGroupUserServiceTest {

    @InjectMocks
    private MarketingGroupUserServiceImpl marketingGroupUserService;

    @Mock
    private MarketingGroupUserMapper marketingGroupUserMapper;

    @Before
    public void before(){
        EntityHelper.initEntityNameMap(MarketingGroupUserEntity.class, new MapperHelper().getConfig());
    }

    @Test
    public void checkGroupProductCode(){

        int i = marketingGroupUserService.checkGroupProductCode("!", "1", "1", "1","1");
        Assert.assertEquals(0,i);
    }

    @Test
    public void checkGroupProductCode_empty(){

        int i = marketingGroupUserService.checkGroupProductCode("!", "1", "1", "1","");
        Assert.assertEquals(0,i);
    }


    @Test
    public void checkGroupLeaderProductCode_empty(){

        int i = marketingGroupUserService.checkGroupLeaderProductCode("!", "1", "1", "1","");
        Assert.assertEquals(0,i);
    }


    @Test
    public void checkGroupLeaderProductCode(){

        int i = marketingGroupUserService.checkGroupLeaderProductCode("!", "1", "1", "1","1");
        Assert.assertEquals(0,i);
    }


    @Test
    public void countOfParticipants(){
        GroupUserCountDto dto = new GroupUserCountDto();
        dto.setGroupStatusList(new ArrayList<>());
        int i = marketingGroupUserService.countOfParticipants(dto);
        Assert.assertEquals(0,i);
    }

    @Test
    public void countOfParticipants1(){
        GroupUserCountDto dto = new GroupUserCountDto();
        dto.setGroupStatusList(Arrays.asList("1"));
        int i = marketingGroupUserService.countOfParticipants(dto);
        Assert.assertEquals(0,i);
    }


    @Test
    public void findGroupUserCode(){

        FindGroupUserParam param = new FindGroupUserParam();
        param.setTenantCode("!");
        param.setMarketingGroupCode("!");
        param.setUserCode("1");
        MarketingGroupUserMode groupUserCode = marketingGroupUserService.findGroupUserCode(param);

        Assert.assertEquals(null, groupUserCode);
    }


    @Test
    public void findGroupUserCode_empty(){

        FindGroupUserParam param = new FindGroupUserParam();
        param.setTenantCode("!");
        param.setMarketingGroupCode("!");
        param.setUserCode("1");
        param.setProductCode("!");
        param.setSkuCode("!");


        List<MarketingGroupUserEntity> marketingGroupUserEntities = new ArrayList<>();

        MarketingGroupUserEntity entity =new MarketingGroupUserEntity();
        marketingGroupUserEntities.add(entity);

        Mockito.when(marketingGroupUserMapper.selectByCondition(Mockito.any())).thenReturn(marketingGroupUserEntities);

        MarketingGroupUserMode groupUserCode = marketingGroupUserService.findGroupUserCode(param);

        Assert.assertNotNull(null, groupUserCode);
    }


    @Test
    public void confirmGroupLeaderByMarketingGroupCode(){

        MarketingGroupUserMode groupUserCode = marketingGroupUserService.confirmGroupLeaderByMarketingGroupCode("1","1");

        Assert.assertEquals(null, groupUserCode);
    }


    @Test
    public void confirmGroupLeaderByMarketingGroupCode_not_empty(){

        FindGroupUserParam param = new FindGroupUserParam();
        param.setTenantCode("!");
        param.setMarketingGroupCode("!");
        param.setUserCode("1");
        param.setProductCode("!");
        param.setSkuCode("!");


        List<MarketingGroupUserEntity> marketingGroupUserEntities = new ArrayList<>();

        MarketingGroupUserEntity entity =new MarketingGroupUserEntity();
        marketingGroupUserEntities.add(entity);

        Mockito.when(marketingGroupUserMapper.selectByCondition(Mockito.any())).thenReturn(marketingGroupUserEntities);

        MarketingGroupUserMode groupUserCode = marketingGroupUserService.confirmGroupLeaderByMarketingGroupCode("1","1");

        Assert.assertNotNull(null, groupUserCode);
    }


    @Test
    public void queryGroupUserListByMarketingGroupCode(){

        int userModes = marketingGroupUserService.queryGroupUserListByMarketingGroupCode("1", "1", "1");
        Assert.assertEquals(0,userModes);
    }

    @Test
    public void queryGroupUserListByUserCode(){

        List<MarketingGroupUserMode> userModes = marketingGroupUserService.queryGroupUserListByUserCode("1", "1", "1");
        Assert.assertEquals(0,userModes.size());
    }

    @Test
    public void queryGroupMemberNoPay(){

        int i = marketingGroupUserService.queryGroupMemberNoPay("1", "1", "1");
        Assert.assertEquals(0,i);
    }


    @Test
    public void updateGroupSuccessStatusByUserCode(){

        marketingGroupUserService.updateGroupSuccessStatusByUserCode("1", "1", "1", UserGroupStatusEnum.CANCEL);
    }


    @Test
    public void updateGroupSuccessStatusByMarketingGroupCode(){

        marketingGroupUserService.updateGroupStatusByMarketingGroupCode("1", "1",  UserGroupStatusEnum.CANCEL);
    }

    @Test
    public void updateGroupSuccessStatusByUserCode1(){

        marketingGroupUserService.updateGroupSuccessStatusByUserCode("1", "1", "", UserGroupStatusEnum.CANCEL);
    }

    @Test
    public void findGroupLeaderUserByMarketingGroupCode(){

        MarketingGroupUserMode groupLeaderUserByMarketingGroupCode = marketingGroupUserService.findGroupLeaderUserByMarketingGroupCode("1", "1");

        Assert.assertNull(groupLeaderUserByMarketingGroupCode);

    }

    @Test
    public void findGroupLeaderUserByMarketingGroupCode_not_null(){

        List<MarketingGroupUserEntity> marketingGroupUserEntities = new ArrayList<>();
        MarketingGroupUserEntity entity= new MarketingGroupUserEntity();
        marketingGroupUserEntities.add(entity);

        Mockito.when(marketingGroupUserMapper.selectByCondition(Mockito.any())).thenReturn(marketingGroupUserEntities);
        MarketingGroupUserMode groupLeaderUserByMarketingGroupCode = marketingGroupUserService.findGroupLeaderUserByMarketingGroupCode("1", "1");

        Assert.assertNotNull(groupLeaderUserByMarketingGroupCode);

    }

    @Test
    public void queryAllGroupUserListByCondition_not_empty(){

        MarketingGroupUserListParam groupMode  = new MarketingGroupUserListParam();
        groupMode.setTenantCode("11");
        groupMode.setMarketingGroupCode("!");
        groupMode.setDomainCode("!1");
        groupMode.setMarketingGroupCode("1");
        groupMode.setUserCode("1");
        groupMode.setActivityCode("!");
        groupMode.setSkuCode("1");
        groupMode.setProductCode("!");
        groupMode.setGroupStatusList(Arrays.asList(UserGroupStatusEnum.FINISH.code()));
        PageInfo<MarketingGroupUserMode> pageInfo = marketingGroupUserService.queryAllGroupUserListByCondition(groupMode);

        Assert.assertEquals(0,pageInfo.getList().size());

    }

    @Test
    public void queryAllGroupUserListByCondition_empty(){

        MarketingGroupUserListParam groupMode  = new MarketingGroupUserListParam();
        groupMode.setTenantCode("11");
        groupMode.setDomainCode("!1");

        PageInfo<MarketingGroupUserMode> pageInfo = marketingGroupUserService.queryAllGroupUserListByCondition(groupMode);

        Assert.assertEquals(0,pageInfo.getList().size());

    }

    @Test
    public void queryGroupUserListByActivityCode(){

        MarketingGroupUserParam param =new MarketingGroupUserParam();
        param.setTenantCode("!");
        param.setActivityCode("1");
        param.setUserCode("1");
        param.setProductCode("!");
        param.setSkuCode("!");

        List<String> groupStatus = new ArrayList<>();
        groupStatus.add("01");
        List<MarketingGroupUserMode> userModes = marketingGroupUserService.queryGroupUserListByActivityCode(param);

        Assert.assertEquals(0,userModes.size());

    }

    @Test
    public void queryGroupUserListByActivityCode_empty(){

        MarketingGroupUserParam param =new MarketingGroupUserParam();

        param.setTenantCode("!");
        param.setUserCode("1");
        param.setActivityCode("1");

        List<String> groupStatus = new ArrayList<>();
        groupStatus.add("01");
        List<MarketingGroupUserMode> userModes = marketingGroupUserService.queryGroupUserListByActivityCode(param);

        Assert.assertEquals(0,userModes.size());

    }


    @Test
    public void queryPayGroupUserListByActivityCode(){

        MarketingGroupUserParam param =new MarketingGroupUserParam();
        param.setTenantCode("!");
        param.setActivityCode("1");
        param.setUserCode("1");
        param.setProductCode("!");
        param.setSkuCode("!");

        List<String> groupStatus = new ArrayList<>();
        groupStatus.add("01");
        List<MarketingGroupUserMode> userModes = marketingGroupUserService.queryPayGroupUserListByActivityCode(param);

        Assert.assertEquals(0,userModes.size());

    }

    @Test
    public void queryPayGroupUserListByActivityCode_empty(){

        MarketingGroupUserParam param =new MarketingGroupUserParam();

        param.setTenantCode("!");
        param.setUserCode("1");
        param.setActivityCode("1");

        List<String> groupStatus = new ArrayList<>();
        groupStatus.add("01");
        List<MarketingGroupUserMode> userModes = marketingGroupUserService.queryPayGroupUserListByActivityCode(param);

        Assert.assertEquals(0,userModes.size());

    }

    @Test
    public void updateGroupEffectiveTimeByMarketingGroupCode(){

        String dateString = DateUtil.getDateString(new Date(), DateUtil.DATETIMESTOREFORMAT);
        marketingGroupUserService.updateGroupEffectiveTimeByMarketingGroupCode("!","1","1",dateString);
    }

    @Test
    public void updateGroupCancelStatusProcessing(){

		marketingGroupUserService.updateGroupUserStatus("!", "1", "1", "0");
    }

    @Test
    public void cancelGroupByMarketingGroupAndUserCode(){
        marketingGroupUserService.cancelGroupByMarketingGroupAndUserCode("!","1","!");
    }



    @Test
    public void existLeaderByGroupCode(){
        boolean b = marketingGroupUserService.existLeaderByGroupCode("!", "1", "!", "1");
        Assert.assertTrue(b);
    }


    @Test
    public void existLeaderByGroupCode_not_empty(){
        List<MarketingGroupUserEntity> marketingGroupUserEntities = new ArrayList<>();
        MarketingGroupUserEntity entity = new MarketingGroupUserEntity();

        entity.setTeamLeader("1");
        entity.setUserCode("1111111");
        marketingGroupUserEntities.add(entity);

        Mockito.when(marketingGroupUserMapper.selectByCondition(Mockito.any())).thenReturn(marketingGroupUserEntities);
        boolean b = marketingGroupUserService.existLeaderByGroupCode("!", "1", "!", "1111111");
        Assert.assertTrue(b);

    }

    @Test
    public void existLeaderByGroupCode_empty(){
        List<MarketingGroupUserEntity> marketingGroupUserEntities = new ArrayList<>();
        MarketingGroupUserEntity entity = new MarketingGroupUserEntity();

        entity.setTeamLeader("1");
        entity.setUserCode("2222");
        marketingGroupUserEntities.add(entity);

        Mockito.when(marketingGroupUserMapper.selectByCondition(Mockito.any())).thenReturn(marketingGroupUserEntities);
        boolean b = marketingGroupUserService.existLeaderByGroupCode("!", "1", "!", "1111111");
        Assert.assertFalse(b);

    }


}
