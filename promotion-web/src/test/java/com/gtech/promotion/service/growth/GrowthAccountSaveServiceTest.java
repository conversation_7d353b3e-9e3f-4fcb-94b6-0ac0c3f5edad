package com.gtech.promotion.service.growth;

import com.gtech.commons.redis.GTechCodeGenerator;
import com.gtech.promotion.dao.entity.growth.GrowthAccountEntity;
import com.gtech.promotion.dao.mapper.growth.GrowthAccountMapper;
import com.gtech.promotion.exception.PromotionException;
import com.gtech.promotion.service.growth.impl.GrowthAccountSaveServiceImpl;
import com.gtech.promotion.vo.param.growth.CreateGrowthAccountParam;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.dao.DuplicateKeyException;
import tk.mybatis.mapper.mapperhelper.EntityHelper;
import tk.mybatis.mapper.mapperhelper.MapperHelper;

/**
 * @Description :
 * <AUTHOR> gaojie.li
 * @Date : 2021/8/6 11:05
 */
@RunWith(MockitoJUnitRunner.class)
public class GrowthAccountSaveServiceTest {

    @InjectMocks
    private GrowthAccountSaveServiceImpl growthAccountSaveService;

    @Mock
    GrowthAccountMapper growthAccountMapper;

    @Mock
    GTechCodeGenerator codeGenerator;

    @Before
    public void before() {
        EntityHelper.initEntityNameMap(GrowthAccountEntity.class, new MapperHelper().getConfig());
    }

    @Test
    public void saveGrowthAccount1() {
        CreateGrowthAccountParam createGrowthAccountParam = new CreateGrowthAccountParam();
        Mockito.when(codeGenerator.generateCode(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any())).thenReturn("1");
        Mockito.when(growthAccountMapper.insert(Mockito.any())).thenReturn(1);
        String s = growthAccountSaveService.saveGrowthAccount(createGrowthAccountParam);
        Assert.assertEquals("1", s);
    }

    @Test
    public void saveGrowthAccount2() {
        CreateGrowthAccountParam createGrowthAccountParam = new CreateGrowthAccountParam();
        createGrowthAccountParam.setStatus(1);
        createGrowthAccountParam.setAccountBalance(1);
        Mockito.when(codeGenerator.generateCode(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any())).thenReturn("1");
        Mockito.when(growthAccountMapper.insert(Mockito.any())).thenReturn(1);
        String s = growthAccountSaveService.saveGrowthAccount(createGrowthAccountParam);
        Assert.assertEquals("1", s);
    }

    @Test(expected = PromotionException.class)
    public void saveGrowthAccount3() {
        CreateGrowthAccountParam createGrowthAccountParam = new CreateGrowthAccountParam();
        createGrowthAccountParam.setStatus(1);
        createGrowthAccountParam.setAccountBalance(1);
        Mockito.when(codeGenerator.generateCode(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any())).thenReturn("1");
        Mockito.when(growthAccountMapper.insert(Mockito.any())).thenThrow(new DuplicateKeyException("11"));
        String s = growthAccountSaveService.saveGrowthAccount(createGrowthAccountParam);
        Assert.assertEquals("1", s);
    }


    @Test(expected = PromotionException.class)
    public void saveGrowthAccount4() {
        CreateGrowthAccountParam createGrowthAccountParam = new CreateGrowthAccountParam();
        createGrowthAccountParam.setStatus(1);
        createGrowthAccountParam.setAccountBalance(1);
        Mockito.when(codeGenerator.generateCode(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any())).thenReturn("1");
        Mockito.when(growthAccountMapper.insert(Mockito.any())).thenThrow(new NullPointerException("11"));
        String s = growthAccountSaveService.saveGrowthAccount(createGrowthAccountParam);
        Assert.assertEquals("1", s);
    }

}
