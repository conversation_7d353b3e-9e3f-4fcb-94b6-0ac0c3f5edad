package com.gtech.promotion.service.point;

import com.gtech.commons.redis.GTechCodeGenerator;
import com.gtech.promotion.dao.entity.point.PointAccountEntity;
import com.gtech.promotion.dao.mapper.point.PointAccountMapper;
import com.gtech.promotion.service.point.impl.PointAccountTempServiceImpl;
import com.gtech.promotion.vo.param.point.CreatePointAccountParam;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import tk.mybatis.mapper.mapperhelper.EntityHelper;
import tk.mybatis.mapper.mapperhelper.MapperHelper;

/**
 * @Description :
 * <AUTHOR> gaojie.li
 * @Date : 2021/8/6 14:08
 */
@RunWith(MockitoJUnitRunner.class)
public class PointAccountTempServiceTest {
    @InjectMocks
    private PointAccountTempServiceImpl pointAccountTempService;

    @Mock
    PointAccountMapper pointAccountMapper;

    @Mock
    GTechCodeGenerator codeGenerator;

    @Before
    public void before() {
        EntityHelper.initEntityNameMap(PointAccountEntity.class, new MapperHelper().getConfig());
    }

    @Test
    public void savePointAccount() {
        CreatePointAccountParam uniqueVo = new CreatePointAccountParam();
        uniqueVo.setTenantCode("100001");
        Mockito.when(codeGenerator.generateCode(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any())).thenReturn("1");
        Mockito.when(pointAccountMapper.insert(Mockito.any())).thenReturn(1);
        Assert.assertNotNull(pointAccountTempService.savePointAccount(uniqueVo));
    }

}
