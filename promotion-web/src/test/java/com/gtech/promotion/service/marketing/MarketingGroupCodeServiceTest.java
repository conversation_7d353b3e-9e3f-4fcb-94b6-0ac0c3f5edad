package com.gtech.promotion.service.marketing;

import com.gtech.promotion.dao.entity.marketing.MarketingGroupCodeEntity;
import com.gtech.promotion.dao.mapper.marketing.MarketingGroupCodeMapper;
import com.gtech.promotion.dao.model.marketing.MarketingGroupCodeMode;
import com.gtech.promotion.service.marketing.impl.MarketingGroupCodeServiceImpl;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import tk.mybatis.mapper.mapperhelper.EntityHelper;
import tk.mybatis.mapper.mapperhelper.MapperHelper;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * @Description :
 * <AUTHOR> gaojie.li
 * @Date : 2023/10/18 10:45
 */
@RunWith(MockitoJUnitRunner.class)
public class MarketingGroupCodeServiceTest {

    @InjectMocks
    private MarketingGroupCodeServiceImpl marketingGroupCodeService;

    @Mock
    private MarketingGroupCodeMapper marketingGroupCodeMapper;

    @Before
    public void before() {
        EntityHelper.initEntityNameMap(MarketingGroupCodeEntity.class, new MapperHelper().getConfig());
    }

    @Test
    public void deductGroupInventory() {

        int i = marketingGroupCodeService.deductGroupInventory("!", "1", "1");
        Assert.assertEquals(0, i);
    }

    @Test
    public void addGroupInventory() {

        int i = marketingGroupCodeService.addGroupInventory("!", "1", "1");
        Assert.assertEquals(0, i);
    }

    @Test
    public void queryGroupByMarketingGroupCode() {

        MarketingGroupCodeMode marketingGroupCodeMode = marketingGroupCodeService.queryGroupByMarketingGroupCode("!", "1", "1");
        Assert.assertNull(marketingGroupCodeMode);
    }

    @Test
    public void updateMarketingCodeGroupStatus() {

        int i = marketingGroupCodeService.updateMarketingCodeGroupStatus("!", "1", "1", "01");
        Assert.assertEquals(0, i);
    }



    @Test
    public void queryGroupByActivityCode() {

        List<MarketingGroupCodeMode> list = marketingGroupCodeService.queryGroupByActivityCode("!", "1", Arrays.asList("1"),null);
        Assert.assertEquals(0, list.size());
    }


    @Test
    public void queryGroupByActivityCode_not_empty() {

        List<MarketingGroupCodeEntity> marketingGroupCodeEntities = new ArrayList<>();
        MarketingGroupCodeEntity entity = new MarketingGroupCodeEntity();

        marketingGroupCodeEntities.add(entity);

        Mockito.when(marketingGroupCodeMapper.selectByCondition(Mockito.any())).thenReturn(marketingGroupCodeEntities);


        List<MarketingGroupCodeMode> list = marketingGroupCodeService.queryGroupByActivityCode("!", "1", Arrays.asList("1"),"1");
        Assert.assertEquals(1, list.size());
    }


}
