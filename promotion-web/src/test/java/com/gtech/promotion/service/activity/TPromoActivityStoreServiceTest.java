package com.gtech.promotion.service.activity;

import com.github.pagehelper.PageInfo;
import com.gtech.promotion.dao.entity.activity.TPromoActivityStoreEntity;
import com.gtech.promotion.dao.mapper.activity.TPromoActivityStoreMapper;
import com.gtech.promotion.dao.model.activity.TPromoActivityStoreVO;
import com.gtech.promotion.page.RequestPage;
import com.gtech.promotion.service.impl.activity.TPromoActivityStoreServiceImpl;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import tk.mybatis.mapper.mapperhelper.EntityHelper;
import tk.mybatis.mapper.mapperhelper.MapperHelper;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2021/3/31 10:43
 */
@RunWith(MockitoJUnitRunner.class)
public class TPromoActivityStoreServiceTest {

    @InjectMocks
    private TPromoActivityStoreServiceImpl activityStoreService;

    @Mock
    private TPromoActivityStoreMapper tPromoRuleStoreMapper;

    @Before
    public void before(){
        EntityHelper.initEntityNameMap(TPromoActivityStoreEntity.class, new MapperHelper().getConfig());
    }

    @Test
    public void createStore(){
        TPromoActivityStoreVO storeVO = new TPromoActivityStoreVO();
        Mockito.when(tPromoRuleStoreMapper.insertSelective(Mockito.any())).thenReturn(1);
        activityStoreService.createStore(storeVO);
    }

    @Test
    public void selectNotByCondition(){
        Mockito.when(tPromoRuleStoreMapper.selectNotByCondition(Mockito.any(), Mockito.anyString())).thenReturn(new ArrayList<>());
        List<String> strings = activityStoreService.selectNotByCondition("1", "1");
        Assert.assertEquals(0,strings.size());
    }

    @Test
    public void getStoresByActivityCodes(){
        List<String> activityCodes = new ArrayList<>();
        activityCodes.add("1");
        Mockito.when(tPromoRuleStoreMapper.selectByCondition(Mockito.any())).thenReturn(new ArrayList<>());
        List<TPromoActivityStoreVO> stores = activityStoreService.getStoresByActivityCodes("1", activityCodes);
        Assert.assertEquals(0,stores.size());
    }

    @Test
    public void selectByOrgCode(){
        RequestPage page = new RequestPage();
        Mockito.when(tPromoRuleStoreMapper.selectByCondition(Mockito.any())).thenReturn(new ArrayList<>());
        PageInfo<TPromoActivityStoreVO> pageInfo = activityStoreService.selectByOrgCode("1", "1", page);
        Assert.assertEquals(0,pageInfo.getList().size());
    }
}
