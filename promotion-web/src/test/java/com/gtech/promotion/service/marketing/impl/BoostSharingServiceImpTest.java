package com.gtech.promotion.service.marketing.impl;

import com.gtech.basic.idm.web.vo.result.QueryUserResult;
import com.gtech.commons.result.Result;
import com.gtech.member.web.vo.param.QueryMemberParam;
import com.gtech.member.web.vo.result.QueryMemberListByConditionResult;
import com.gtech.promotion.code.marketing.BoostSharingTypeEnum;
import com.gtech.promotion.component.marketing.MarketingCacheComponent;
import com.gtech.promotion.dao.entity.marketing.BoostSharingEntity;
import com.gtech.promotion.dao.entity.marketing.HelpRecordEntity;
import com.gtech.promotion.dao.entity.marketing.SharingRecordEntity;
import com.gtech.promotion.dao.mapper.marketing.BoostSharingMapper;
import com.gtech.promotion.dao.mapper.marketing.HelpRecordMapper;
import com.gtech.promotion.dao.mapper.marketing.SharingRecordMapper;
import com.gtech.promotion.dao.model.marketing.BoostSharingModel;
import com.gtech.promotion.dao.model.marketing.MarketingModel;
import com.gtech.promotion.dao.model.marketing.flashsale.FlashSaleProductModel;
import com.gtech.promotion.dto.boostsharing.ExportBoostSharingDetailDto;
import com.gtech.promotion.dto.boostsharing.ExportBoostSharingTotalDto;
import com.gtech.promotion.feign.IdmFeignClient;
import com.gtech.promotion.feign.MemberFeignClient;
import com.gtech.promotion.service.marketing.MarketingService;
import com.gtech.promotion.vo.result.boostsharding.ExportBoostSharingTotalResult;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import tk.mybatis.mapper.entity.Example;
import tk.mybatis.mapper.mapperhelper.EntityHelper;
import tk.mybatis.mapper.mapperhelper.MapperHelper;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import static org.mockito.Mockito.when;

/**
 * @Description :
 * <AUTHOR> gaojie.li
 * @Date : 2023/10/23 19:15
 */
@RunWith(MockitoJUnitRunner.class)
public class BoostSharingServiceImpTest {



    @Mock
    private BoostSharingMapper boostSharingMapper;



    @InjectMocks
    BoostSharingServiceImpl boostSharingServiceImp;

    @Mock
    private SharingRecordMapper sharingRecordMapper;
    @Mock
    private HelpRecordMapper helpRecordMapper;
    @Mock
    private MemberFeignClient memberFeignClient;
    @Mock
    private IdmFeignClient idmFeignClient;

    @Mock
    private MarketingCacheComponent marketingCacheComponent;
    @Mock
    private MarketingService marketingService;

    @Before
    public void setUp() {

        EntityHelper.initEntityNameMap(BoostSharingEntity.class, new MapperHelper().getConfig());
        EntityHelper.initEntityNameMap(HelpRecordEntity.class, new MapperHelper().getConfig());
        EntityHelper.initEntityNameMap(SharingRecordEntity.class, new MapperHelper().getConfig());

    }

    @Test
    public void findBoostShardingInfo(){
        BoostSharingModel boostShardingInfo = boostSharingServiceImp.findBoostShardingInfo("!", "1", "1", "1");
        Assert.assertNull(boostShardingInfo);
    }

    @Test
    public void findBoostShardingInfo_org(){
        BoostSharingModel boostShardingInfo = boostSharingServiceImp.findBoostShardingInfo("!", "1", "", "1");
        Assert.assertNull(boostShardingInfo);
    }

    @Test
    public void queryBoostSharingByActivityList(){
        List<BoostSharingModel> boostSharingModels = boostSharingServiceImp.queryBoostSharingByActivityList("!", new ArrayList<>());
        Assert.assertEquals(0,boostSharingModels.size());
    }

    @Test
    public void queryBoostSharingByActivityList_not_empty(){

        List<String> objects = new ArrayList<>();
        objects.add("!");
        List<BoostSharingModel> boostSharingModels = boostSharingServiceImp.queryBoostSharingByActivityList("!", objects);

        Assert.assertEquals(0,boostSharingModels.size());

    }



    @Test
    public void testGetBoostSharingProductsByActivityCodesAndProducts() {
        // Prepare
        String tenantCode = "tenantCode";
        ArrayList<String> activityCodes = new ArrayList<>();
        activityCodes.add("activityCode1");
        activityCodes.add("activityCode2");
        List<String> skuCodeList = new ArrayList<>();
        skuCodeList.add("skuCode1");
        skuCodeList.add("skuCode2");

        BoostSharingEntity boostSharingEntity1 = new BoostSharingEntity();
        boostSharingEntity1.setTenantCode(tenantCode);
        boostSharingEntity1.setActivityCode("activityCode1");
        boostSharingEntity1.setRightOfFirstRefusalProductCode("skuCode1");

        BoostSharingEntity boostSharingEntity2 = new BoostSharingEntity();
        boostSharingEntity2.setTenantCode(tenantCode);
        boostSharingEntity2.setActivityCode("activityCode2");
        boostSharingEntity2.setRightOfFirstRefusalProductCode("skuCode2");

        List<BoostSharingEntity> boostSharingEntities = new ArrayList<>();
        boostSharingEntities.add(boostSharingEntity1);
        boostSharingEntities.add(boostSharingEntity2);

        when(boostSharingMapper.selectByCondition(Mockito.any())).thenReturn(boostSharingEntities);

        // Execute
        List<FlashSaleProductModel> result = boostSharingServiceImp.getBoostSharingProductsByActivityCodesAndProducts(tenantCode, activityCodes, skuCodeList);

        Assert.assertEquals(2,result.size());

    }

    @Test
    public void testGetBoostSharingProductsByActivityCodesAndProducts_activityCodes_empty() {
        // Prepare
        String tenantCode = "tenantCode";
        ArrayList<String> activityCodes = new ArrayList<>();

        List<String> skuCodeList = new ArrayList<>();
        skuCodeList.add("skuCode1");
        skuCodeList.add("skuCode2");

        BoostSharingEntity boostSharingEntity1 = new BoostSharingEntity();
        boostSharingEntity1.setTenantCode(tenantCode);
        boostSharingEntity1.setActivityCode("activityCode1");
        boostSharingEntity1.setRightOfFirstRefusalProductCode("skuCode1");

        BoostSharingEntity boostSharingEntity2 = new BoostSharingEntity();
        boostSharingEntity2.setTenantCode(tenantCode);
        boostSharingEntity2.setActivityCode("activityCode2");
        boostSharingEntity2.setRightOfFirstRefusalProductCode("skuCode2");

        List<BoostSharingEntity> boostSharingEntities = new ArrayList<>();
        boostSharingEntities.add(boostSharingEntity1);
        boostSharingEntities.add(boostSharingEntity2);

        List<FlashSaleProductModel> result = boostSharingServiceImp.getBoostSharingProductsByActivityCodesAndProducts(tenantCode, activityCodes, skuCodeList);

        Assert.assertEquals(0,result.size());

    }

    @Test
    public void testGetBoostSharingProductsByActivityCodesAndProducts_skuCodes_empty() {
        // Prepare
        String tenantCode = "tenantCode";
        ArrayList<String> activityCodes = new ArrayList<>();
        activityCodes.add("activityCode1");
        activityCodes.add("activityCode2");
        List<String> skuCodeList = new ArrayList<>();

        BoostSharingEntity boostSharingEntity1 = new BoostSharingEntity();
        boostSharingEntity1.setTenantCode(tenantCode);
        boostSharingEntity1.setActivityCode("activityCode1");
        boostSharingEntity1.setRightOfFirstRefusalProductCode("skuCode1");

        BoostSharingEntity boostSharingEntity2 = new BoostSharingEntity();
        boostSharingEntity2.setTenantCode(tenantCode);
        boostSharingEntity2.setActivityCode("activityCode2");
        boostSharingEntity2.setRightOfFirstRefusalProductCode("skuCode2");

        List<BoostSharingEntity> boostSharingEntities = new ArrayList<>();
        boostSharingEntities.add(boostSharingEntity1);
        boostSharingEntities.add(boostSharingEntity2);

        List<FlashSaleProductModel> result = boostSharingServiceImp.getBoostSharingProductsByActivityCodesAndProducts(tenantCode, activityCodes, skuCodeList);
        Assert.assertEquals(0,result.size());


    }


    @Test
    public void testExportBoostSharingTotal() {
        // Prepare data
        ExportBoostSharingTotalDto dto = new ExportBoostSharingTotalDto();
        dto.setActivityCode("activityCode");
        dto.setDomainCode("domainCode");
        dto.setTenantCode("tenantCode");

        BoostSharingEntity boostSharingEntity = new BoostSharingEntity();
        boostSharingEntity.setActivityCode(dto.getActivityCode());
        boostSharingEntity.setDomainCode(dto.getDomainCode());
        boostSharingEntity.setTenantCode(dto.getTenantCode());
        boostSharingEntity.setBoostSharingType(BoostSharingTypeEnum.RIGHT_FIRST.code());

        MarketingModel marketingCache = new MarketingModel();
        marketingCache.setActivityName("activityName");

        SharingRecordEntity sharingRecordEntity = new SharingRecordEntity();
        sharingRecordEntity.setNumberOfBoostSharing("10");
        sharingRecordEntity.setActivityStatus("01");

        List<SharingRecordEntity> sharingRecordEntities = new ArrayList<>();
        sharingRecordEntities.add(sharingRecordEntity);

        Example sharingExample = new Example(BoostSharingEntity.class);
        sharingExample.createCriteria()
                .andEqualTo("domainCode", dto.getDomainCode())
                .andEqualTo("tenantCode", dto.getTenantCode())
                .andEqualTo("activityCode", dto.getActivityCode());

        // Mock dependencies
        when(boostSharingMapper.selectOne(Mockito.any())).thenReturn(boostSharingEntity);
        when(marketingCacheComponent.getMarketingCache(Mockito.any(), Mockito.any())).thenReturn(marketingCache);
        when(sharingRecordMapper.selectByCondition(Mockito.any())).thenReturn(sharingRecordEntities);

        // Execute the method
        ExportBoostSharingTotalResult result = boostSharingServiceImp.exportBoostSharingTotal(dto);


    }



    @Test
    public void testExportBoostSharingDetail() {
        // Arrange
        ExportBoostSharingDetailDto
                dto = new ExportBoostSharingDetailDto();
        dto.setDomainCode("exampleDomain");
        dto.setTenantCode("exampleTenant");
        dto.setActivityCode("exampleActivity");

        Example sharingExample = new Example(SharingRecordEntity.class);
        sharingExample.createCriteria()
                .andEqualTo("domainCode", dto.getDomainCode())
                .andEqualTo("tenantCode", dto.getTenantCode())
                .andEqualTo("activityCode", dto.getActivityCode());

        List<SharingRecordEntity> sharingRecordEntities = new ArrayList<>();
        SharingRecordEntity sharingRecordEntity = new SharingRecordEntity();
        sharingRecordEntity.setSharingRecordCode("exampleCode");
        sharingRecordEntity.setSharingMemberCode("exampleMemberCode");
        sharingRecordEntity.setCreateTime(new Date());
        sharingRecordEntities.add(sharingRecordEntity);

        Example helpExample = new Example(HelpRecordEntity.class);
        helpExample.createCriteria()
                .andEqualTo("domainCode", dto.getDomainCode())
                .andEqualTo("tenantCode", dto.getTenantCode())
                .andEqualTo("activityCode", dto.getActivityCode());

        List<HelpRecordEntity> helpRecordEntities = new ArrayList<>();
        HelpRecordEntity helpRecordEntity = new HelpRecordEntity();
        helpRecordEntity.setHelpMemberCode("exampleMemberCode");
        helpRecordEntity.setSharingRecordCode("exampleCode");
        helpRecordEntity.setSharingMemberCode("exampleMemberCode");
        helpRecordEntity.setSharingMemberCode("exampleMemberCode");
        helpRecordEntity.setCreateTime(new Date());
        helpRecordEntities.add(helpRecordEntity);

        List<String> helpMemberCodeList = new ArrayList<>();
        helpMemberCodeList.add("exampleMemberCode");

        List<String> sharingMemberCodeList = new ArrayList<>();
        sharingMemberCodeList.add("exampleMemberCode");

        List<String> memberCodeList = new ArrayList<>();
        memberCodeList.add("exampleMemberCode");

        QueryMemberParam queryMemberParam = new QueryMemberParam();
        queryMemberParam.setMemberCodes(memberCodeList);
        queryMemberParam.setDomainCode(dto.getDomainCode());
        queryMemberParam.setTenantCode(dto.getTenantCode());

        Result<List<QueryMemberListByConditionResult>> listResult = new Result<>();
        listResult.setCode("0");
        List<QueryMemberListByConditionResult> queryMemberListByConditionResults = new ArrayList<>();
        QueryMemberListByConditionResult queryMemberListByConditionResult = new QueryMemberListByConditionResult();
        queryMemberListByConditionResult.setMemberCode("exampleMemberCode");
        queryMemberListByConditionResult.setFirstName("Example");
        queryMemberListByConditionResult.setLastName("User");
        queryMemberListByConditionResult.setMobile("123456789");
        queryMemberListByConditionResults.add(queryMemberListByConditionResult);
        listResult.setData(queryMemberListByConditionResults);
        when(memberFeignClient.queryMemberListByMemberCode(Mockito.any())).thenReturn(listResult);

        when(sharingRecordMapper.selectByCondition(Mockito.any())).thenReturn(sharingRecordEntities);
        when(helpRecordMapper.selectByCondition(Mockito.any())).thenReturn(helpRecordEntities);
        Result<List<QueryUserResult>> result = new Result<>();

        List<QueryUserResult> resultList = new ArrayList<>();

        QueryUserResult userResult = new QueryUserResult();

        resultList.add(userResult);

        result.setData(resultList);

        Mockito.when(idmFeignClient.queryUserList(Mockito.any())).thenReturn(result);
         boostSharingServiceImp.exportBoostSharingDetail(dto);


    }


}
