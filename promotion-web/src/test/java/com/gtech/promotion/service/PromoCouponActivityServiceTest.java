package com.gtech.promotion.service;

import com.github.pagehelper.PageInfo;
import com.gtech.promotion.dao.entity.coupon.TPromoCouponActivityEntity;
import com.gtech.promotion.dao.mapper.coupon.TPromoCouponActivityMapper;
import com.gtech.promotion.dao.model.activity.ActivityModel;
import com.gtech.promotion.dto.in.coupon.CouponActivityListInDTO;
import com.gtech.promotion.dto.out.coupon.CouponActivityListOutDTO;
import com.gtech.promotion.service.impl.coupon.PromoCouponActivityServiceImpl;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import tk.mybatis.mapper.mapperhelper.EntityHelper;
import tk.mybatis.mapper.mapperhelper.MapperHelper;

import java.util.ArrayList;

@RunWith(MockitoJUnitRunner.class)
public class PromoCouponActivityServiceTest {

    @InjectMocks
    private PromoCouponActivityServiceImpl couponActivityService;

    @Mock
    private TPromoCouponActivityMapper couponActivityMapper;

    @Before
    public void before(){
        EntityHelper.initEntityNameMap(TPromoCouponActivityEntity.class, new MapperHelper().getConfig());
    }

    @Test
    public void testReserveCouponQuota(){
        Mockito.when(couponActivityMapper.reserveCouponQuota(Mockito.any(), Mockito.any(), Mockito.anyInt())).thenReturn(1);
        int i = couponActivityService.reserveCouponQuota("1", "1", 0);
        Assert.assertEquals(1, i);
    }

    @Test
    public void testReturnCouponQuota(){
        Mockito.when(couponActivityMapper.returnCouponQuota(Mockito.any(), Mockito.any(), Mockito.anyInt())).thenReturn(1);
        int i = couponActivityService.returnCouponQuota("1", "1", 0);
        Assert.assertEquals(1, i);
    }

    @Test
    public void updateCouponActivityByActivityCode(){
        ActivityModel couponActivityVO = new ActivityModel();
        couponActivityVO.setActivityCode("1");
        couponActivityVO.setLogicDelete(0);
        couponActivityVO.setReserveInventory(0);
//        Mockito.when(couponActivityMapper.updateByConditionSelective(Mockito.any(), Mockito.any())).thenReturn(1);
        try {
            int i = couponActivityService.updateCouponActivityByActivityCode(couponActivityVO);
        }catch (Exception e){
            
        }
    }

    @Test
    public void queryCouponActivityList(){
        CouponActivityListInDTO couponActivityListInDTO = new CouponActivityListInDTO();
        couponActivityListInDTO.setActivityStatus("01");
        couponActivityListInDTO.setCouponType("01");
        couponActivityListInDTO.setTemplateCodes("01");
        couponActivityListInDTO.setOpsType("01");
        couponActivityListInDTO.setChannelCode("01");
        Mockito.when(couponActivityMapper.queryCouponActivityListByQualification(Mockito.any())).thenReturn(new ArrayList<>());
        PageInfo<CouponActivityListOutDTO> pageInfo = couponActivityService.queryCouponActivityList(couponActivityListInDTO);
        Assert.assertEquals(0,pageInfo.getList().size());
    }

    @Test
    public void queryCouponActivityList1(){
        CouponActivityListInDTO couponActivityListInDTO = new CouponActivityListInDTO();
        couponActivityListInDTO.setActivityStatus("01");
        couponActivityListInDTO.setCouponType("01");
        couponActivityListInDTO.setTemplateCodes("01");
        couponActivityListInDTO.setOpsType("01");
        Mockito.when(couponActivityMapper.queryCouponActivityList(Mockito.any())).thenReturn(new ArrayList<>());
        PageInfo<CouponActivityListOutDTO> pageInfo = couponActivityService.queryCouponActivityList(couponActivityListInDTO);
        Assert.assertEquals(0,pageInfo.getList().size());
    }

    @Test
    public void deleteCouponActivity(){
        Mockito.when(couponActivityMapper.delete(Mockito.any())).thenReturn(1);
        int i = couponActivityService.deleteCouponActivity("1", "1");
        Assert.assertEquals(1,i);
    }
}
