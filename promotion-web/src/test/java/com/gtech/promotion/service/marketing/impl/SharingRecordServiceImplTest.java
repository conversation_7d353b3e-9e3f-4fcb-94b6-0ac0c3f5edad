package com.gtech.promotion.service.marketing.impl;

import com.gtech.commons.dao.mapper.GTechBaseMapper;
import com.gtech.promotion.dao.entity.marketing.SharingRecordEntity;
import com.gtech.promotion.dao.mapper.marketing.SharingRecordMapper;
import com.gtech.promotion.dao.model.marketing.BoostSharingModel;
import com.gtech.promotion.dao.model.marketing.SharingRecordModel;
import com.gtech.promotion.dto.in.flashsale.HelpRecordDto;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import tk.mybatis.mapper.mapperhelper.EntityHelper;
import tk.mybatis.mapper.mapperhelper.MapperHelper;

import java.util.Date;
import java.util.List;

public class SharingRecordServiceImplTest {
    @Mock
    SharingRecordMapper sharingRecordMapper;
    @Mock
    GTechBaseMapper<SharingRecordEntity> mapper;

    @InjectMocks
    SharingRecordServiceImpl sharingRecordServiceImpl;


    @Before
    public void setUp() {

        MockitoAnnotations.openMocks(this);
        EntityHelper.initEntityNameMap(SharingRecordEntity.class, new MapperHelper().getConfig());
        EntityHelper.initEntityNameMap(SharingRecordModel.class, new MapperHelper().getConfig());

    }

    @Test
    public void testUpdateSharingRecord() throws Exception {
        int result = sharingRecordServiceImpl.updateSharingRecord(new SharingRecordModel("activityCode", "domainCode", "tenantCode", "orgCode", "sharingRecordCode", "sharingMemberCode", "activityStatus", "numberOfBoostSharing", "numberOfPeopleWhoHaveHelped",new Date()));
    }

    @Test
    public void testFindBysharingRecordCode() throws Exception {
        SharingRecordModel result = sharingRecordServiceImpl.findBysharingRecordCode("sharingRecordCode","sharingMemberCode");
    }

    @Test
    public void testQuerySharingRecord() throws Exception {
        List<SharingRecordEntity> result = sharingRecordServiceImpl.querySharingRecord(new SharingRecordModel("activityCode", "domainCode", "tenantCode", "orgCode", "sharingRecordCode", "sharingMemberCode", "activityStatus", "numberOfBoostSharing", "numberOfPeopleWhoHaveHelped",new Date()));
    }

    @Test
    public void testQuerySharingRecord1(){
        List<SharingRecordEntity> result = sharingRecordServiceImpl.querySharingRecord(new SharingRecordModel("activityCode", "domainCode", "tenantCode", "orgCode", "", "sharingMemberCode", "activityStatus", "numberOfBoostSharing", "numberOfPeopleWhoHaveHelped",new Date()));
    }

    @Test
    public void increaseTheNumberOfPeopleHelping() {

        HelpRecordDto dto= new HelpRecordDto();
        BoostSharingModel boostSharingModel = new BoostSharingModel();
        SharingRecordModel sharingRecordModel = new SharingRecordModel();

        int result = sharingRecordServiceImpl.increaseTheNumberOfPeopleHelping(dto,boostSharingModel,sharingRecordModel);
        Assert.assertEquals(0,result);
    }

    @Test
    public void testInsert() throws Exception {
        int result = sharingRecordServiceImpl.insert(new SharingRecordModel("activityCode", "domainCode", "tenantCode", "orgCode", "sharingRecordCode", "sharingMemberCode", "activityStatus", "numberOfBoostSharing", "numberOfPeopleWhoHaveHelped",new Date()));
    }

    @Test
    public void testDeleteByActivityCode() throws Exception {
        int result = sharingRecordServiceImpl.deleteByActivityCode("activityCode");
    }

    @Test
    public void testFindByActivityCode() throws Exception {
        SharingRecordModel result = sharingRecordServiceImpl.findByActivityCode("activityCode");
    }

    @Test
    public void testFindListByActivityCode() throws Exception {
        List<SharingRecordModel> result = sharingRecordServiceImpl.findListByActivityCode("activityCode");
    }


}

