package com.gtech.promotion.service;

import com.gtech.promotion.dao.entity.coupon.PromoCouponSendDetailEntity;
import com.gtech.promotion.dao.mapper.coupon.PromoCouponSendDetailMapper;
import com.gtech.promotion.dao.model.activity.PromoCouponSendDetailModel;
import com.gtech.promotion.service.impl.coupon.CouponSendDetailServiceImpl;
import com.gtech.promotion.vo.param.coupon.QueryCouponSendDetailParam;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import tk.mybatis.mapper.mapperhelper.EntityHelper;
import tk.mybatis.mapper.mapperhelper.MapperHelper;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * @Description :
 * <AUTHOR> gaojie.li
 * @Date : 2023/9/19 11:34
 */
@RunWith(MockitoJUnitRunner.class)
public class CouponSendDetailServiceImplTest {

    @InjectMocks
    private CouponSendDetailServiceImpl sendDetailService;

    @Mock
    private PromoCouponSendDetailMapper promoCouponSendDetailMapper;

    @Before
    public void before(){

        EntityHelper.initEntityNameMap(PromoCouponSendDetailEntity.class, new MapperHelper().getConfig());

    }
    @Test
    public void createCouponSendDetail(){

        int couponSendDetail = sendDetailService.createCouponSendDetail(null);
        Assert.assertEquals(0,couponSendDetail);
    }


    @Test
    public void createCouponSendDetail1(){
        PromoCouponSendDetailModel model = new PromoCouponSendDetailModel();

        int couponSendDetail = sendDetailService.createCouponSendDetail(model);
        Assert.assertEquals(0,couponSendDetail);
    }

    @Test
    public void createCouponSendDetailList(){
        List<PromoCouponSendDetailModel> promoCouponSendDetailModels = new ArrayList<>();
        sendDetailService.createCouponSendDetailList(promoCouponSendDetailModels);
    }

    @Test
    public void createCouponSendDetailList1(){
        List<PromoCouponSendDetailModel> promoCouponSendDetailModels = new ArrayList<>();

        PromoCouponSendDetailModel model = new PromoCouponSendDetailModel();

        promoCouponSendDetailModels.add(model);

        sendDetailService.createCouponSendDetailList(promoCouponSendDetailModels);
    }

    @Test
    public void queryCouponSendDetailList(){
        QueryCouponSendDetailParam param = new QueryCouponSendDetailParam();
        param.setTenantCode("1");
        param.setActivityCode("!");
        param.setSendBatchNoList(Arrays.asList("11"));
        sendDetailService.queryCouponSendDetailList(param);
    }

    @Test
    public void queryCouponSendDetailLis1t(){
        QueryCouponSendDetailParam param = new QueryCouponSendDetailParam();
        param.setTenantCode("1");
        param.setActivityCode("!");
        param.setSendBatchNoList(Arrays.asList("11"));

        Mockito.when(promoCouponSendDetailMapper.selectByExample(Mockito.any())).thenThrow(new RuntimeException("1"));
        sendDetailService.queryCouponSendDetailList(param);
    }

    @Test
    public void queryCouponSendDetailList_empty(){
        QueryCouponSendDetailParam param = new QueryCouponSendDetailParam();

        sendDetailService.queryCouponSendDetailList(param);
    }


}
