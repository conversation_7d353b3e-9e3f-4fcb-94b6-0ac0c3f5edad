package com.gtech.promotion.service.flash;

import com.gtech.promotion.dao.entity.marketing.flashsale.FlashSaleQualificationEntity;
import com.gtech.promotion.dao.entity.marketing.flashsale.FlashSaleStoreEntity;
import com.gtech.promotion.dao.mapper.marketing.flashsale.FlashSaleStoreMapper;
import com.gtech.promotion.service.flashsale.impl.FlashSaleStoreServiceImpl;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import tk.mybatis.mapper.mapperhelper.EntityHelper;
import tk.mybatis.mapper.mapperhelper.MapperHelper;

import java.util.ArrayList;

/**
 * <AUTHOR>
 * @date 2021-09-18 10:57
 */
@RunWith(MockitoJUnitRunner.class)
public class FlashSaleStoreServiceTest {
    @InjectMocks
    private FlashSaleStoreServiceImpl flashSaleStoreService;
    @Mock
    private FlashSaleStoreMapper storeMapper;

    @Before
    public void before(){
        EntityHelper.initEntityNameMap(FlashSaleStoreEntity.class, new MapperHelper().getConfig());
    }

    @Test
    public void getStoresByActivityCodes(){
        flashSaleStoreService.getStoresByActivityCodes("1",new ArrayList<>());
    }
}
