package com.gtech.promotion.service;

import com.gtech.promotion.dao.entity.activity.QualificationEntity;
import com.gtech.promotion.dao.mapper.activity.LuckyDrawQualificationMapper;
import com.gtech.promotion.dao.mapper.activity.QualificationMapper;
import com.gtech.promotion.dao.model.activity.QualificationModel;
import com.gtech.promotion.service.impl.activity.QualificationServiceImpl;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import tk.mybatis.mapper.mapperhelper.EntityHelper;
import tk.mybatis.mapper.mapperhelper.MapperHelper;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

@RunWith(MockitoJUnitRunner.class)
public class QualificationTest {

    @InjectMocks
    private QualificationServiceImpl qualificationService;

    @Mock
    private QualificationMapper qualificationMapper;

    @Mock
    private LuckyDrawQualificationMapper luckyDrawQualificationMapper;

    @Before
    public void before(){
        EntityHelper.initEntityNameMap(QualificationEntity.class, new MapperHelper().getConfig());
    }


    @Test
    public void createQualifications(){
        List<QualificationModel> models = new ArrayList<>();
        models.add(new QualificationModel());
        Mockito.when(qualificationMapper.insertSelective(Mockito.any())).thenReturn(1);
        int qualifications = qualificationService.createQualifications(models);
        Assert.assertEquals(1, qualifications);
    }

    @Test
    public void deleteQualifications(){
        Mockito.when(qualificationMapper.delete(Mockito.any())).thenReturn(1);
        int qualifications = qualificationService.deleteQualifications("1", "2");
        Assert.assertEquals(1, qualifications);
    }

    @Test
    public void queryQualifications(){
        List<QualificationEntity> entities = new ArrayList<>();
        Mockito.when(qualificationMapper.select(Mockito.any())).thenReturn(entities);
        List<QualificationModel> qualificationModels = qualificationService.queryQualifications("1", "2");
        Assert.assertEquals(entities.size(), qualificationModels.size());
    }

    @Test
    public void queryQualificationsByActivityCodes(){
        List<QualificationEntity> entities = new ArrayList<>();
        Mockito.when(qualificationMapper.selectByCondition(Mockito.any())).thenReturn(entities);
        List<QualificationModel> qualificationModels = qualificationService.queryQualificationsByActivityCodes("1", new ArrayList<>());
        Assert.assertEquals(entities.size(), qualificationModels.size());
    }

    @Test
    public void createLuckyDrawQualifications(){
        List<QualificationModel> qualificationModels = new ArrayList<>();
        QualificationModel model = new QualificationModel();
        qualificationModels.add(model);
        Mockito.when(luckyDrawQualificationMapper.insertSelective(Mockito.any())).thenReturn(1);
        int qualifications = qualificationService.createLuckyDrawQualifications(qualificationModels);
        Assert.assertEquals(1,qualifications);
    }

    @Test
    public void deleteLuckyDrawQualifications(){
        Mockito.when(luckyDrawQualificationMapper.delete(Mockito.any())).thenReturn(1);
        int i = qualificationService.deleteLuckyDrawQualifications("1", "1");
        Assert.assertEquals(1,i);
    }

    @Test
    public void queryLuckyDrawQualifications(){
        Mockito.when(luckyDrawQualificationMapper.select(Mockito.any())).thenReturn(new ArrayList<>());
        List<QualificationModel> models = qualificationService.queryLuckyDrawQualifications("1", "1");
        Assert.assertEquals(0,models.size());
    }

    @Test
    public void queryLuckyDrawQualificationsByActivityCodes(){
        Mockito.when(luckyDrawQualificationMapper.selectByCondition(Mockito.any())).thenReturn(new ArrayList<>());
        List<QualificationModel> models = qualificationService.queryLuckyDrawQualificationsByActivityCodes("1", "1");
        Assert.assertEquals(0,models.size());
    }

    @Test
    public void queryQualificationsForSendCoupon(){
        Mockito.when(qualificationMapper.selectByCondition(Mockito.any())).thenReturn(new ArrayList<>());
        List<QualificationModel> models = qualificationService.queryQualificationsForSendCoupon("1", "1");
        Assert.assertEquals(0,models.size());
    }

    @Test
    public void queryQualificationsByMemberTags(){
        Mockito.when(qualificationMapper.selectByCondition(Mockito.any())).thenReturn(new ArrayList<>());
        qualificationService.queryQualificationsByMemberTags("100001", Arrays.asList("test1"),Arrays.asList("dd"));
    }

    @Test
    public void queryQualificationsByCode(){
        Mockito.when(qualificationMapper.selectByCondition(Mockito.any())).thenReturn(new ArrayList<>());
        qualificationService.queryQualificationsByCode("100001","11","11");
    }
}
