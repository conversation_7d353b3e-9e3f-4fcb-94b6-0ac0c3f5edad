package com.gtech.promotion.service.purchaseconstraint;

import com.gtech.promotion.dao.entity.purchaseconstraint.PurchaseConstraintRuleEntity;
import com.gtech.promotion.dao.mapper.purchaseconstraint.PurchaseConstraintRuleMapper;
import com.gtech.promotion.dao.model.purchaseconstraint.PurchaseConstraintRuleModel;
import com.gtech.promotion.service.purchaseconstraint.impl.PurchaseConstraintRuleServiceImpl;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import tk.mybatis.mapper.mapperhelper.EntityHelper;
import tk.mybatis.mapper.mapperhelper.MapperHelper;

import java.util.ArrayList;
import java.util.List;

@RunWith(MockitoJUnitRunner.class)
public class PurchaseConstraintRuleServiceTest {
    @InjectMocks
    private PurchaseConstraintRuleServiceImpl purchaseConstraintRuleService;

    @Mock
    private PurchaseConstraintRuleMapper purchaseConstraintRuleMapper;

    @Before
    public void before() {
        EntityHelper.initEntityNameMap(PurchaseConstraintRuleEntity.class, new MapperHelper().getConfig());
    }

    @Test
    public void insertList(){
        List<PurchaseConstraintRuleModel> purchaseConstraintRuleModelList = new ArrayList<>();
        int result = purchaseConstraintRuleService.insertList(purchaseConstraintRuleModelList);
        Assert.assertEquals(0, result);
    }


    @Test
    public void deletePurchaseConstraintRule(){
        String tenantCode = "1";
        String purchaseConstraintCode = "2";

        int result = purchaseConstraintRuleService.deletePurchaseConstraintRule(tenantCode, purchaseConstraintCode);
        Assert.assertEquals(0, result);
    }

    @Test
    public void queryPurchaseConstraintRuleByCode(){
        String tenantCode="111";
        String purchaseConstraintCode = "222";
        List<PurchaseConstraintRuleModel> purchaseConstraintRuleModels =
                purchaseConstraintRuleService.queryPurchaseConstraintRuleByCode(tenantCode, purchaseConstraintCode);
        Assert.assertEquals(0, purchaseConstraintRuleModels.size());
    }
}
