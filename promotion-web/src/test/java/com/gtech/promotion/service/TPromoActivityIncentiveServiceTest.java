package com.gtech.promotion.service;

import com.gtech.promotion.dao.entity.activity.TPromoActivityIncentiveEntity;
import com.gtech.promotion.dao.mapper.activity.TPromoActivityIncentiveMapper;
import com.gtech.promotion.dto.in.activity.ActivityTenantInDTO;
import com.gtech.promotion.service.impl.activity.TPromoActivityIncentiveServiceImpl;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import tk.mybatis.mapper.mapperhelper.EntityHelper;
import tk.mybatis.mapper.mapperhelper.MapperHelper;

import java.util.ArrayList;
import java.util.List;

/**
 * @Description :
 * <AUTHOR> gaojie.li
 * @Date : 2021/8/4 14:08
 */
@RunWith(MockitoJUnitRunner.class)
public class TPromoActivityIncentiveServiceTest {
    @InjectMocks
    private TPromoActivityIncentiveServiceImpl tPromoActivityIncentiveService;

    @Mock
    private TPromoActivityIncentiveMapper incentiveMapper;

    @Before
    public void before(){
        EntityHelper.initEntityNameMap(TPromoActivityIncentiveEntity.class, new MapperHelper().getConfig());
    }

    @Test
    public void getUserActivityTimes111(){

        List<TPromoActivityIncentiveEntity> activityIncentiveEntities = new ArrayList<>();
        TPromoActivityIncentiveEntity entity = new TPromoActivityIncentiveEntity();
        entity.setIncentiveTimes(1);
        activityIncentiveEntities.add(entity);
        Mockito.when(incentiveMapper.select(Mockito.any())).thenReturn(activityIncentiveEntities);
        Long times111 = tPromoActivityIncentiveService.getUserActivityTimes111("1", "1", "1");
        Assert.assertEquals(1,times111.intValue());
    }

    @Test
    public void updateOrderIncentiveDeleteStatusByOrderId(){

        Mockito.when(incentiveMapper.updateByConditionSelective(Mockito.any(),Mockito.any())).thenReturn(1);
        Integer times111 = tPromoActivityIncentiveService.updateOrderIncentiveDeleteStatusByOrderId(1L);
        Assert.assertEquals(1,times111.intValue());
    }

    @Test
    public void getListByOrderIds(){

        List<Long> promoOrderIds = new ArrayList<>();
        Mockito.when(incentiveMapper.selectByCondition(Mockito.any())).thenReturn(new ArrayList<>());
        List<TPromoActivityIncentiveEntity> times111 = tPromoActivityIncentiveService.getListByOrderIds(promoOrderIds);
        Assert.assertEquals(0,times111.size());
    }
    @Test
    public void queryDiscountMoneyTotal(){
        Mockito.when(incentiveMapper.queryDiscountMoneyTotal()).thenReturn(0.0);
        double times111 = tPromoActivityIncentiveService.queryDiscountMoneyTotal();
        Assert.assertEquals(0,times111,0.0);
    }

    @Test
    public void getListByActivityCode(){
        Mockito.when(incentiveMapper.selectByCondition(Mockito.any())).thenReturn(new ArrayList<>());
        List<TPromoActivityIncentiveEntity> times111 = tPromoActivityIncentiveService.getListByActivityCode("");
        Assert.assertEquals(0,times111.size());
    }

    @Test
    public void getPayOrderDiscountMoneyTotal(){
        ActivityTenantInDTO dto = new ActivityTenantInDTO();
        Mockito.when(incentiveMapper.getPayOrderMoneyTotalMapper(dto)).thenReturn(0.0);
        double times111 = tPromoActivityIncentiveService.getPayOrderDiscountMoneyTotal(dto);
        Assert.assertEquals(0,times111,0.0);
    }

}
