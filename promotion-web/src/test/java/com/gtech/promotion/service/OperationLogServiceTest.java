package com.gtech.promotion.service;

import com.gtech.commons.redis.GTechCodeGenerator;
import com.gtech.promotion.dao.entity.activity.OperationLogContentEntity;
import com.gtech.promotion.dao.entity.activity.OperationLogEntity;
import com.gtech.promotion.dao.mapper.activity.OperationLogContentMapper;
import com.gtech.promotion.dao.mapper.activity.OperationLogMapper;
import com.gtech.promotion.dao.model.activity.OperationLogModel;
import com.gtech.promotion.dto.in.activity.QueryOperationLogsByActivityCodeDTO;
import com.gtech.promotion.dto.out.activity.QueryOperationLogsByActivityCodeOutDTO;
import com.gtech.promotion.service.impl.activity.OperationLogServiceImpl;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import tk.mybatis.mapper.mapperhelper.EntityHelper;
import tk.mybatis.mapper.mapperhelper.MapperHelper;

import java.util.ArrayList;
import java.util.List;

@RunWith(MockitoJUnitRunner.class)
public class OperationLogServiceTest {

    @InjectMocks
    private OperationLogServiceImpl operationLogService;

    @Mock
    private OperationLogMapper operationLogMapper;

    @Mock
    private OperationLogContentMapper contentMapper;

    @Mock
    private GTechCodeGenerator codeGenerator;

    @Before
    public void before(){
        EntityHelper.initEntityNameMap(OperationLogEntity.class, new MapperHelper().getConfig());
        EntityHelper.initEntityNameMap(OperationLogContentEntity.class, new MapperHelper().getConfig());
    }

    @Test
    public void test_insertLog(){
        OperationLogModel model = new OperationLogModel();
        Mockito.when(codeGenerator.generateCode(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any())).thenReturn("OLs");
        Mockito.when(operationLogMapper.insertSelective(Mockito.any())).thenReturn(1);
        Mockito.when(contentMapper.insertSelective(Mockito.any())).thenReturn(1);
        int i = operationLogService.insertLog(model, "unit test");
        Assert.assertEquals(1, i);
    }

    @Test
    public void queryOperationLogsByActivityCode(){
        QueryOperationLogsByActivityCodeDTO dto = new QueryOperationLogsByActivityCodeDTO();
        List<OperationLogEntity> logEntities = new ArrayList<>();
        OperationLogEntity entity = new OperationLogEntity();
        logEntities.add(entity);
        Mockito.when(operationLogMapper.selectByCondition(Mockito.any())).thenReturn(logEntities);
        List<QueryOperationLogsByActivityCodeOutDTO> logsByActivityCodeOutDTOS = operationLogService.queryOperationLogsByActivityCode(dto);
        Assert.assertEquals(1, logsByActivityCodeOutDTOS.size());
    }
}
