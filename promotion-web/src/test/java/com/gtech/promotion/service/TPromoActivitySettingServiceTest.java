package com.gtech.promotion.service;

import com.gtech.promotion.dao.entity.activity.TPromoActivitySettingEntity;
import com.gtech.promotion.dao.mapper.activity.TPromoActivitySettingMapper;
import com.gtech.promotion.dao.model.activity.TPromoActivitySettingVO;
import com.gtech.promotion.helper.ActivityRedisHelpler;
import com.gtech.promotion.service.impl.activity.TPromoActivitySettingServiceImpl;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.data.redis.core.StringRedisTemplate;
import tk.mybatis.mapper.mapperhelper.EntityHelper;
import tk.mybatis.mapper.mapperhelper.MapperHelper;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * @Description :
 * <AUTHOR> gaojie.li
 * @Date : 2021/8/4 14:24
 */
@RunWith(MockitoJUnitRunner.class)
public class TPromoActivitySettingServiceTest {
    @InjectMocks
    private TPromoActivitySettingServiceImpl tPromoActivitySettingService;

    @Mock
    private TPromoActivitySettingMapper tPromoActivitySettingMapper;

    @Mock
    private ActivityRedisHelpler redisService;
    @Mock
    private StringRedisTemplate redisTemplate;

    @Before
    public void before() {
        EntityHelper.initEntityNameMap(TPromoActivitySettingEntity.class, new MapperHelper().getConfig());
    }

    @Test
    public void deleteSetting() {
        Mockito.when(tPromoActivitySettingMapper.delete(Mockito.any())).thenReturn(1);
        Mockito.when(redisTemplate.delete(Mockito.anyString())).thenReturn(true);
        int i = tPromoActivitySettingService.deleteSetting("", 1);
        Assert.assertEquals(1, i);
    }

    @Test
    public void selectSetting() {
        TPromoActivitySettingEntity entity = new TPromoActivitySettingEntity();
        Mockito.when(tPromoActivitySettingMapper.selectOne(Mockito.any())).thenReturn(entity);
        TPromoActivitySettingVO i = tPromoActivitySettingService.selectSetting("", 1, "");
        Assert.assertEquals(null, i.getSettingType());
    }

    @Test
    public void addActivitySetting() {
        Mockito.when(tPromoActivitySettingMapper.insertSelective(Mockito.any())).thenReturn(1);
        Mockito.doNothing().when(redisService).setActivitySetting(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any());
        tPromoActivitySettingService.addActivitySetting("", 1, ",", "");
    }

    @Test
    public void updateActivitySetting() {
        Mockito.when(tPromoActivitySettingMapper.updateByConditionSelective(Mockito.any(), Mockito.any())).thenReturn(1);
        Mockito.doNothing().when(redisService).setActivitySetting(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any());
        tPromoActivitySettingService.updateActivitySetting("", 1, "", "");
    }

    @Test
    public void addAllActivitySetting() {
        List<TPromoActivitySettingEntity> selectAll = new ArrayList<>();
        Set<String> redis = new HashSet<>();
        TPromoActivitySettingEntity entity = new TPromoActivitySettingEntity();
        selectAll.add(entity);
        Mockito.when(tPromoActivitySettingMapper.selectAll()).thenReturn(selectAll);
        Mockito.when(redisService.getAll(Mockito.any())).thenReturn(redis);
        Mockito.doNothing().when(redisService).setActivitySetting(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any());
        tPromoActivitySettingService.addAllActivitySetting();
    }
}
