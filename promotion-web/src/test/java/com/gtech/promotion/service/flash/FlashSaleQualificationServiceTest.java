package com.gtech.promotion.service.flash;

import com.gtech.promotion.dao.entity.marketing.flashsale.FlashSaleProductEntity;
import com.gtech.promotion.dao.entity.marketing.flashsale.FlashSaleQualificationEntity;
import com.gtech.promotion.dao.mapper.marketing.flashsale.FlashSaleQualificationMapper;
import com.gtech.promotion.service.flashsale.impl.FlashSaleQualificationServiceImpl;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import tk.mybatis.mapper.mapperhelper.EntityHelper;
import tk.mybatis.mapper.mapperhelper.MapperHelper;

import java.util.ArrayList;

/**
 * <AUTHOR>
 * @date 2021-09-18 10:55
 */
@RunWith(MockitoJUnitRunner.class)
public class FlashSaleQualificationServiceTest {
    @InjectMocks
    private FlashSaleQualificationServiceImpl flashSaleQualificationService;
    @Mock
    private FlashSaleQualificationMapper qualificationMapper;

    @Before
    public void before(){
        EntityHelper.initEntityNameMap(FlashSaleQualificationEntity.class, new MapperHelper().getConfig());
    }

    @Test
    public void getQualificationsByActivityCodes(){
        flashSaleQualificationService.getQualificationsByActivityCodes("1",new ArrayList<>());
    }
}
