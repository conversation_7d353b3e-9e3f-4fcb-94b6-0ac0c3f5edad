/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.domain.coupon.activity;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;

/**
 * 测试券活动创建
 */
@RunWith(MockitoJUnitRunner.class)
public class CouponActivityUpdateTest{
    @Test
    public void test(){

    }
//    // 1、有无奖励 限制   incentiveLimitedFlag 00无限制  01有限制
//    // 2、有无渠道 限制   storeType 店铺范围：00-全店铺 01-自定义
//    // 3、有无会员 等级限制   members
//    // 商品范围： productType  00-全商品 01-指定范围 02-指定商品（上传文件）  03-指定多个商品范围
//    // 4、商品：全商品  
//    // 5、商品：指定商品范围
//    // 6、商品：指定商品sku
//    // 7、商品：指定多个商品范围
//    // 8、正反选 productSelectType商品范围正反选：01-正选；02-反选
//
//    @Mock
//    private TPromoActivityDomainImpl activityDomain;
//    
//    @InjectMocks
//    private CouponActivityDomainImpl couponActivityDomain;
//
//    @Mock
//    private TPromoActivityServiceImpl activityService;
//
//    @Mock
//    private TPromoActivityStoreServiceImpl tPromoStoreService;
//
//    @Mock
//    private TPromoProductServiceImpl tPromoProductService;
//
//    @Mock
//    private TPromoProductDetailServiceImpl tPromoProductDetailService;
//
//    @Mock
//    private TPromoIncentiveLimitedServiceImpl tPromoIncentiveLimitedService;
//
//    @Mock
//    private TPromoActivityFuncParamServiceImpl tPromoActivityFuncParamService;
//
//    @Mock
//    private TPromoActivityFuncRankServiceImpl tpromoActivityFuncRankService;
//
//    @Mock
//    private TPromoTemplateServiceImpl tPromoTemplateService;
//
//    @Mock
//    private TPromoActivityGiftServiceImpl tPromoRuleGiftService;
//
//    @Mock
//    private TPromoMemberServiceImpl tPromoMemberService;
//
//    @Mock
//    private TPromoTemplateFunctionServiceImpl tPromoTemplateFunctionService;
//
//    @Mock
//    private ProductDomainImpl productDomain;
//
//    @Mock
//    private ActivityCacheDomainImpl activityCacheDomain;
//
//    @Mock
//    private PromoCouponActivityServiceImpl promoCouponActivityService;
//    @Test
//    public void 券活动_订单满数量打折扣(){
//        //given
//        //创建券活动 订单满数量打折扣模板    满2件打五折
//        Template template = Template.builder().templateEnum(TemplateEnum.T304).condition("2").reward("5").build();
//        // 1、有无奖励 限制   incentiveLimitedFlag 00无限制  01有限制
//        // 2、有无渠道 限制   storeType 店铺范围：00-全店铺 01-自定义
//        // 3、有无会员 等级限制   members
//        // 商品范围： productType  00-全商品 01-指定范围 02-指定商品（上传文件）  03-指定多个商品范围
//        // 4、商品：全商品   
//        // 5、商品：指定商品范围
//        // 6、商品：指定商品sku
//        // 7、商品：指定多个商品范围
//        // 8、正反选 productSelectType商品范围正反选：01-正选；02-反选
//        //given
//        TPromoTemplateVO templateVO = new TPromoTemplateVO();
//        templateVO.setId(TemplateEnum.T304.getId());
//        templateVO.setTemplateCode(template.getTemplateEnum().getCode());
//        TPromoActivityUpdateDTO updateDTO = ActivityFactory.createActivityUpdateDTO(null, template, null, null, IncentiveLimitedFlagEnum.NO, StoreParamTypeEnum.STORE_ALL, ProductSelectionEnum.SELECT, ProductTypeEnum.CUSTOM_PRODUCT);
//        UpdateCouponActivityInDTO updateCouponActivityDTO = ActivityFactory.updateCouponActivityDTO(updateDTO, CouponTypeEnum.PROMOTION_COUPON.code(), "123", 0, "0");
//        //when
////        when(tPromoTemplateService.getTemplateByCode(anyString())).thenReturn(templateVO);
////        when(tPromoTemplateFunctionService.getTemplateFunctionByCode(template.getTemplateEnum().getId(), "0103")).thenReturn(ActivityFactory.createFunctionParam(101L, "0103"));
////        when(tPromoTemplateFunctionService.getTemplateFunctionByCode(template.getTemplateEnum().getId(), "0202")).thenReturn(ActivityFactory.createFunctionParam(101L, "0202"));
////        when(tPromoTemplateFunctionService.getTemplateFunctionByCode(template.getTemplateEnum().getId(), "0302")).thenReturn(ActivityFactory.createFunctionParam(101L, "0302"));
////        when(tPromoTemplateFunctionService.getTemplateFunctionByCode(template.getTemplateEnum().getId(), "0402")).thenReturn(ActivityFactory.createFunctionParam(101L, "0402"));
//        TPromoActivityVO activityVo = new TPromoActivityVO();
//        activityVo.setActivityStatus("01");
////        when(activityService.findActivityByTenantCodeAndActivityId(anyString(),anyString())).thenReturn(activityVo);
////        when(activityService.updatePromoActivity(any(TPromoActivityVO.class))).thenReturn(new TPromoActivityEntity());
////        when(productDomain.createProductSku(anyString(), anyString(), anyString())).thenReturn(null);
////        when(tPromoActivityFuncParamService.saveTPromoRuleFuncParam(anyString(), anyString(), any())).thenReturn(1);
//        when(tPromoProductService.getProducts(anyString())).thenReturn(new ArrayList<>());
////        when(tPromoTemplateService.getTemplateByCode(anyString())).thenReturn(templateVO);
//        TPromoActivityVO tPromoActivityVO = new TPromoActivityVO();
//        tPromoActivityVO.setId("1");
//        when(activityDomain.updatePromoActivity(any(TPromoActivityUpdateDTO.class))).thenReturn(tPromoActivityVO);
//        TPromoCouponActivityVO updateCouponActivityVO = ActivityFactory.updateCouponActivityVO(updateCouponActivityDTO);
//        when(promoCouponActivityService.findCouponActivity(anyString())).thenReturn(updateCouponActivityVO);
//        when(promoCouponActivityService.updateCouponActivityByActivityId(any(TPromoCouponActivityVO.class))).thenReturn(updateCouponActivityVO.getId());
////        Mockito.doNothing().when(activityCacheDomain).putCache(anyString(), anyString());
//        String updateCouponActivity = couponActivityDomain.updateCouponActivity(updateCouponActivityDTO);
//        assertNotNull(updateCouponActivity);
//        
//    }
}
