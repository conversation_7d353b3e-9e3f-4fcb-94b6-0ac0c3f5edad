/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.domain.impl.activity;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.gtech.promotion.calc.TemplateEnum;
import com.gtech.promotion.code.activity.ActivityTypeEnum;
import com.gtech.promotion.code.activity.IncentiveLimitedFlagEnum;
import com.gtech.promotion.code.activity.QualificationCodeEnum;
import com.gtech.promotion.code.activity.StoreParamTypeEnum;
import com.gtech.promotion.component.activity.*;
import com.gtech.promotion.dao.model.activity.*;
import com.gtech.promotion.dto.cache.ActivityCacheDTO;
import com.gtech.promotion.dto.cache.ProductSkuDetailDTO;
import com.gtech.promotion.dto.in.activity.SingleProductDTO;
import com.gtech.promotion.dto.in.activity.TPromoActivityListMallInDTO;
import com.gtech.promotion.dto.out.activity.SkuActivityPriceDTO;
import com.gtech.promotion.dto.out.activity.TPromoActivityListMallOutDTO;
import com.gtech.promotion.helper.ActivityHelper;
import com.gtech.promotion.helper.TemplateHelper;
import com.gtech.promotion.helper.bean.Template;
import com.gtech.promotion.helper.factory.ActivityFactory;
import com.gtech.promotion.service.activity.*;
import com.gtech.promotion.service.impl.activity.ActivityServiceImpl;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

/**
 * 活动服务类测试
 */
public class ActivityDomainTest{

    @InjectMocks
    private ActivityComponentDomain activityComponentDomain;

    @Mock
    private ActivityServiceImpl activityService;

    @Mock
    private ActivityStoreService tPromoStoreService;

    @Mock
    private QualificationService qualificationService;

    @Mock
    private ActivityLanguageService languageService;//语言

    @Mock
    private TemplateHelper templateHelper;

    @Mock
    private TemplateDomain templateDomain;

    @Mock
    private ActivityProductDetailService activityProductDetailService;

    @Mock
    private ActivityCacheDomain activityCacheDomain;

    @Mock
    private PromoGroupDomain promoGroupDomain;

    @Mock
    private PromotionGroupService promotionGroupService;


    @Mock
    private ActivityPriceComponentDomain activityPriceComponentDomain;


    @Before
    public void setup(){
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void queryMallAllActivityTest_匹配到(){
        Template template = Template.builder().templateEnum(TemplateEnum.T0102).reward("2").build();
        ActivityModel activityVO = ActivityFactory.createActivityVO(template, IncentiveLimitedFlagEnum.YES, StoreParamTypeEnum.STORE_CUSTOM, ActivityTypeEnum.ACTIVITY);
        List<ActivityModel> list = new ArrayList<>();
        list.add(activityVO);
        List<TPromoActivityStoreVO> stores = new ArrayList<>();
        TPromoActivityStoreVO storeVO = new TPromoActivityStoreVO();
        storeVO.setOrgCode(ActivityHelper.orgCode);
        storeVO.setChannelCode(ActivityHelper.channelCode);
        stores.add(storeVO);
        List<QualificationModel> qualificationModels = new ArrayList<>();
        QualificationModel qualificationModel = new QualificationModel();
        qualificationModel.setQualificationCode(QualificationCodeEnum.MEMBER_TAG.code());
        qualificationModel.setQualificationValue("1");
        qualificationModels.add(qualificationModel);
        when(activityService.queryActivityByTenantCodeAndStatusAndTime(any(), any(), any(), any())).thenReturn(list);
        when(tPromoStoreService.getStoresByActivityCode(any())).thenReturn(stores);
        when(qualificationService.queryQualifications(any(), any())).thenReturn(qualificationModels);
        when(templateHelper.templateCode2TagCode(anyString())).thenReturn("01");
        
        TPromoActivityListMallInDTO inDTO = new TPromoActivityListMallInDTO();
        inDTO.setTenantCode(ActivityHelper.tenantCode);
        inDTO.setChannelCode(ActivityHelper.channelCode);
        inDTO.setOrgCode(ActivityHelper.orgCode);
        List<String> memTagCodes = new ArrayList<>();
        memTagCodes.add("1");
        inDTO.setMemberTagCodes(memTagCodes);
        List<TPromoActivityListMallOutDTO> createProductSku = activityComponentDomain.queryMallAllActivity(inDTO);
        Assert.assertTrue(createProductSku.size() > 0);
    }

    @Test
    public void queryMallAllActivityTest_匹配不到(){
        Template template = Template.builder().templateEnum(TemplateEnum.T0102).reward("2").build();
        ActivityModel activityVO = ActivityFactory.createActivityVO(template, IncentiveLimitedFlagEnum.YES, StoreParamTypeEnum.STORE_CUSTOM, ActivityTypeEnum.ACTIVITY);
        List<ActivityModel> list = new ArrayList<>();
        list.add(activityVO);
        List<TPromoActivityStoreVO> stores = new ArrayList<>();
        TPromoActivityStoreVO storeVO = new TPromoActivityStoreVO();
        storeVO.setOrgCode(ActivityHelper.orgCode);
        storeVO.setChannelCode(ActivityHelper.channelCode);
        stores.add(storeVO);
        List<QualificationModel> qualificationModels = new ArrayList<>();
        QualificationModel qualificationModel = new QualificationModel();
        qualificationModel.setQualificationCode(QualificationCodeEnum.MEMBER_TAG.code());
        qualificationModel.setQualificationValue("1");
        qualificationModels.add(qualificationModel);
        when(activityService.queryActivityByTenantCodeAndStatusAndTime(any(), any(), any(), any())).thenReturn(list);
        when(tPromoStoreService.getStoresByActivityCode(any())).thenReturn(stores);
        when(qualificationService.queryQualifications(any(), any())).thenReturn(qualificationModels);
        TPromoActivityListMallInDTO inDTO = new TPromoActivityListMallInDTO();
        inDTO.setTenantCode(ActivityHelper.tenantCode);
        inDTO.setChannelCode(ActivityHelper.channelCode);
        inDTO.setOrgCode(ActivityHelper.orgCode);
        List<String> memTagCodes = new ArrayList<>();
        memTagCodes.add("2");
        inDTO.setMemberTagCodes(memTagCodes);
        List<TPromoActivityListMallOutDTO> createProductSku = activityComponentDomain.queryMallAllActivity(inDTO);
        Assert.assertEquals(0, createProductSku.size());
    }

    @Test
    public void activitySkuPrice(){

        SingleProductDTO product = new SingleProductDTO();

//        String map = "{\"10102198613986791906\":{\"activityModel\":{\"activityBegin\":\"20231220000000\",\"activityCode\":\"10102198613986791906\",\"activityEnd\":\"20231221235959\",\"" +
//                "activityName\":\"单品sp特价\",\"activityStatus\":\"04\",\"activityType\":\"01\",\"activityUrl\":\"\",\"backgroundImage\":\"\",\"coolDown\":\"\",\"" +
//                "couponActivity\":false,\"createTime\":1703061398000,\"createUser\":\"10230227619262\",\"domainCode\":\"DC0005\",\"extendParams\":{},\"externalActivityId\":\"\"," +
//                "\"groupCode\":\"108\",\"id\":581141,\"incentiveLimitedFlag\":\"00\",\"itemScopeType\":1,\"logicDelete\":0,\"needToDoExpire\":false,\"opsType\":\"108\",\"orgCode\":" +
//                "\"10244962\",\"periodType\":\"00\",\"priceCondition\":\"0\",\"priority\":999,\"productSelectionType\":\"01\",\"promotionCategory\":\"\",\"promotionCategoryName\":\"\"," +
//                "\"ribbonImage\":\"\",\"ribbonPosition\":\"\",\"ribbonText\":\"\",\"showFlag\":1,\"sponsors\":\"default\",\"storeType\":\"01\",\"templateCode\":\"0101020103010411\",\"" +
//                "tenantCode\":\"100016\",\"updateUser\":\"10230227619262\",\"warmBegin\":\"\",\"warmEnd\":\"\"},\"giveaways\":[],\"groupCacheList\":[{\"groupCode\":\"302\",\"priority\":22,\"relationList\":[\"101\",\"102\",\"103\",\"104\",\"105\",\"106\",\"107\",\"108\",\"201\",\"202\",\"203\",\"204\",\"205\",\"206\",\"207\"]},{\"groupCode\":\"303\",\"priority\":21,\"relationList\":[\"101\",\"102\",\"103\",\"104\",\"105\",\"106\",\"107\",\"108\",\"201\",\"202\",\"203\",\"204\",\"205\",\"206\",\"207\"]},{\"groupCode\":\"701\",\"priority\":20,\"relationList\":[\"101\",\"102\",\"103\",\"104\",\"105\",\"106\",\"107\",\"108\",\"201\",\"202\",\"203\",\"204\",\"205\",\"206\",\"207\"]},{\"groupCode\":\"601\",\"priority\":19,\"relationList\":[\"101\",\"102\",\"103\",\"104\",\"105\",\"106\",\"107\",\"108\",\"201\",\"202\",\"203\",\"204\",\"205\",\"206\",\"207\"]},{\"groupCode\":\"501\",\"priority\":18,\"relationList\":[\"101\",\"102\",\"103\",\"104\",\"105\",\"106\",\"107\",\"108\",\"201\",\"202\",\"203\",\"204\",\"205\",\"206\",\"207\"]},{\"groupCode\":\"401\",\"priority\":17,\"relationList\":[\"101\",\"102\",\"103\",\"104\",\"105\",\"106\",\"107\",\"108\",\"201\",\"202\",\"203\",\"204\",\"205\",\"206\",\"207\"]},{\"groupCode\":\"301\",\"priority\":16,\"relationList\":[\"101\",\"102\",\"103\",\"104\",\"105\",\"106\",\"107\",\"108\",\"201\",\"202\",\"203\",\"204\",\"205\",\"206\",\"207\"]},{\"groupCode\":\"207\",\"priority\":15,\"relationList\":[\"301\",\"302\",\"303\",\"401\",\"501\",\"601\",\"701\"]},{\"groupCode\":\"206\",\"priority\":14,\"relationList\":[\"301\",\"302\",\"303\",\"401\",\"501\",\"601\",\"701\"]},{\"groupCode\":\"205\",\"priority\":13,\"relationList\":[\"301\",\"302\",\"303\",\"401\",\"501\",\"601\",\"701\"]},{\"groupCode\":\"204\",\"priority\":12,\"relationList\":[\"301\",\"302\",\"303\",\"401\",\"501\",\"601\",\"701\"]},{\"groupCode\":\"203\",\"priority\":11,\"relationList\":[\"301\",\"302\",\"303\",\"401\",\"501\",\"601\",\"701\"]},{\"groupCode\":\"202\",\"priority\":10,\"relationList\":[\"301\",\"302\",\"303\",\"401\",\"501\",\"601\",\"701\"]},{\"groupCode\":\"201\",\"priority\":9,\"relationList\":[\"301\",\"302\",\"303\",\"401\",\"501\",\"601\",\"701\"]},{\"groupCode\":\"106\",\"priority\":8,\"relationList\":[\"301\",\"302\",\"303\",\"401\",\"501\",\"601\",\"701\"]},{\"groupCode\":\"105\",\"priority\":7,\"relationList\":[\"301\",\"302\",\"303\",\"401\",\"501\",\"601\",\"701\"]},{\"groupCode\":\"104\",\"priority\":6,\"relationList\":[\"301\",\"302\",\"303\",\"401\",\"501\",\"601\",\"701\"]},{\"groupCode\":\"103\",\"priority\":5,\"relationList\":[\"301\",\"302\",\"303\",\"401\",\"501\",\"601\",\"701\"]},{\"groupCode\":\"102\",\"priority\":4,\"relationList\":[\"301\",\"302\",\"303\",\"401\",\"501\",\"601\",\"701\"]},{\"groupCode\":\"107\",\"priority\":3,\"relationList\":[\"301\",\"302\",\"303\",\"401\",\"501\",\"601\",\"701\"]},{\"groupCode\":\"108\",\"priority\":2,\"relationList\":[\"301\",\"302\",\"303\",\"401\",\"501\",\"601\",\"701\"]},{\"groupCode\":\"101\",\"priority\":1,\"relationList\":[\"301\",\"302\",\"303\",\"401\",\"501\",\"601\",\"701\"]}],\"incentiveLimiteds\":[],\"languageMap\":{\"en-US\":{\"activityCode\":\"10102198613986791906\",\"activityDesc\":\"\",\"activityLabel\":\"\",\"activityName\":\"\",\"id\":1273664,\"language\":\"en-US\",\"tenantCode\":\"100016\"},\"id-ID\":{\"activityCode\":\"10102198613986791906\",\"activityDesc\":\"\",\"activityLabel\":\"\",\"activityName\":\"\",\"id\":1273665,\"language\":\"id-ID\",\"tenantCode\":\"100016\"},\"zh-CN\":{\"activityCode\":\"10102198613986791906\",\"activityDesc\":\"\",\"activityLabel\":\"\",\"activityName\":\"\",\"id\":1273663,\"language\":\"zh-CN\",\"tenantCode\":\"100016\"}},\"promoChannels\":[{\"activityCode\":\"10102198613986791906\",\"channelCode\":\"0000\",\"channelName\":\"0000\",\"id\":\"556485\",\"orgCode\":\"10244962\",\"storeName\":\"demo\",\"tenantCode\":\"100016\"}],\"promoFuncParams\":[{\"functionCode\":\"0101\",\"functionName\":\"F0101\",\"functionType\":\"01\",\"id\":\"2705139\",\"paramType\":\"01\",\"rankId\":\"676300\",\"tenantCode\":\"100016\"},{\"functionCode\":\"0201\",\"functionName\":\"F0201\",\"functionType\":\"02\",\"id\":\"2705140\",\"paramType\":\"01\",\"rankId\":\"676300\",\"tenantCode\":\"100016\"},{\"functionCode\":\"0301\",\"functionName\":\"F0301\",\"functionType\":\"03\",\"id\":\"2705141\",\"paramType\":\"01\",\"rankId\":\"676300\",\"tenantCode\":\"100016\"},{\"functionCode\":\"0411\",\"functionName\":\"F0411\",\"functionType\":\"04\",\"id\":\"2705142\",\"paramType\":\"01\",\"rankId\":\"676300\",\"tenantCode\":\"100016\"}],\"promoFuncRanks\":[{\"activityCode\":\"10102198613986791906\",\"id\":\"676300\",\"rankParam\":1,\"templateCode\":\"0101020103010411\",\"tenantCode\":\"100016\"}],\"promoProductCombines\":[],\"promoProductDetails\":{\"SP231207213937\":[{\"activityCode\":\"10102198613986791906\",\"productCode\":\"SP231207213937\",\"promoPrice\":200.000,\"seqNum\":1,\"type\":1}]},\"promoProducts\":[],\"promoTemplate\":{\"tagCode\":\"01\",\"templateCode\":\"0101020103010411\",\"templateDesc\":\"单品无条件设为每件不同特价\",\"templateName\":\"单品无条件设为每件不同特价\"},\"promoTemplateFunctions\":[{\"functionCode\":\"0101\",\"functionDesc\":\"单品\",\"functionName\":\"F0101\",\"functionType\":\"01\"},{\"functionCode\":\"0201\",\"functionDesc\":\"无\",\"functionName\":\"F0201\",\"functionType\":\"02\"},{\"functionCode\":\"0301\",\"functionDesc\":\"None\",\"functionName\":\"F0301\",\"functionType\":\"03\"},{\"functionCode\":\"0411\",\"functionDesc\":\"每件不同特价\",\"functionName\":\"F0411\",\"functionType\":\"04\"}],\"qualificationModels\":[],\"version\":\"20200310\"},\"10102198404747544114\":{\"activityModel\":{\"activityBegin\":\"20231220000000\",\"activityCode\":\"10102198404747544114\",\"activityEnd\":\"20231223235959\",\"activityName\":\"打折促销222222222\",\"activityStatus\":\"04\",\"activityType\":\"01\",\"activityUrl\":\"\",\"backgroundImage\":\"\",\"coolDown\":\"\",\"couponActivity\":false,\"createTime\":1703040474000,\"createUser\":\"10230227619262\",\"domainCode\":\"DC0005\",\"extendParams\":{},\"externalActivityId\":\"\",\"groupCode\":\"101\",\"id\":581109,\"incentiveLimitedFlag\":\"00\",\"itemScopeType\":1,\"logicDelete\":0,\"needToDoExpire\":false,\"opsType\":\"101\",\"orgCode\":\"10244962\",\"periodType\":\"00\",\"priceCondition\":\"0\",\"priority\":999,\"productSelectionType\":\"01\",\"promotionCategory\":\"\",\"promotionCategoryName\":\"\",\"ribbonImage\":\"\",\"ribbonPosition\":\"\",\"ribbonText\":\"\",\"showFlag\":1,\"sponsors\":\"default\",\"storeType\":\"01\",\"templateCode\":\"0101020103010401\",\"tenantCode\":\"100016\",\"updateUser\":\"10230227619262\",\"warmBegin\":\"\",\"warmEnd\":\"\"},\"giveaways\":[],\"groupCacheList\":[{\"$ref\":\"$.10102198613986791906.groupCacheList[0]\"},{\"$ref\":\"$.10102198613986791906.groupCacheList[1]\"},{\"$ref\":\"$.10102198613986791906.groupCacheList[2]\"},{\"$ref\":\"$.10102198613986791906.groupCacheList[3]\"},{\"$ref\":\"$.10102198613986791906.groupCacheList[4]\"},{\"$ref\":\"$.10102198613986791906.groupCacheList[5]\"},{\"$ref\":\"$.10102198613986791906.groupCacheList[6]\"},{\"$ref\":\"$.10102198613986791906.groupCacheList[7]\"},{\"$ref\":\"$.10102198613986791906.groupCacheList[8]\"},{\"$ref\":\"$.10102198613986791906.groupCacheList[9]\"},{\"$ref\":\"$.10102198613986791906.groupCacheList[10]\"},{\"$ref\":\"$.10102198613986791906.groupCacheList[11]\"},{\"$ref\":\"$.10102198613986791906.groupCacheList[12]\"},{\"$ref\":\"$.10102198613986791906.groupCacheList[13]\"},{\"$ref\":\"$.10102198613986791906.groupCacheList[14]\"},{\"$ref\":\"$.10102198613986791906.groupCacheList[15]\"},{\"$ref\":\"$.10102198613986791906.groupCacheList[16]\"},{\"$ref\":\"$.10102198613986791906.groupCacheList[17]\"},{\"$ref\":\"$.10102198613986791906.groupCacheList[18]\"},{\"$ref\":\"$.10102198613986791906.groupCacheList[19]\"},{\"$ref\":\"$.10102198613986791906.groupCacheList[20]\"},{\"$ref\":\"$.10102198613986791906.groupCacheList[21]\"}],\"incentiveLimiteds\":[],\"languageMap\":{\"en-US\":{\"activityCode\":\"10102198404747544114\",\"activityDesc\":\"\",\"activityLabel\":\"\",\"activityName\":\"\",\"id\":1273592,\"language\":\"en-US\",\"tenantCode\":\"100016\"},\"id-ID\":{\"activityCode\":\"10102198404747544114\",\"activityDesc\":\"\",\"activityLabel\":\"\",\"activityName\":\"\",\"id\":1273593,\"language\":\"id-ID\",\"tenantCode\":\"100016\"},\"zh-CN\":{\"activityCode\":\"10102198404747544114\",\"activityDesc\":\"\",\"activityLabel\":\"\",\"activityName\":\"\",\"id\":1273591,\"language\":\"zh-CN\",\"tenantCode\":\"100016\"}},\"promoChannels\":[{\"activityCode\":\"10102198404747544114\",\"channelCode\":\"0000\",\"channelName\":\"0000\",\"id\":\"556473\",\"orgCode\":\"10244962\",\"storeName\":\"demo\",\"tenantCode\":\"100016\"}],\"promoFuncParams\":[{\"functionCode\":\"0101\",\"functionName\":\"F0101\",\"functionType\":\"01\",\"id\":\"2705011\",\"paramType\":\"01\",\"rankId\":\"676268\",\"tenantCode\":\"100016\"},{\"functionCode\":\"0201\",\"functionName\":\"F0201\",\"functionType\":\"02\",\"id\":\"2705012\",\"paramType\":\"01\",\"rankId\":\"676268\",\"tenantCode\":\"100016\"},{\"functionCode\":\"0301\",\"functionName\":\"F0301\",\"functionType\":\"03\",\"id\":\"2705013\",\"paramType\":\"01\",\"rankId\":\"676268\",\"tenantCode\":\"100016\"},{\"functionCode\":\"0401\",\"functionName\":\"F0401\",\"functionType\":\"04\",\"id\":\"2705014\",\"paramType\":\"02\",\"paramUnit\":\"01\",\"paramValue\":\"20\",\"rankId\":\"676268\",\"tenantCode\":\"100016\"}],\"promoFuncRanks\":[{\"activityCode\":\"10102198404747544114\",\"id\":\"676268\",\"rankParam\":1,\"templateCode\":\"0101020103010401\",\"tenantCode\":\"100016\"}],\"promoProductCombines\":[],\"promoProductDetails\":{\"SP231207213937\":[{\"activityCode\":\"10102198404747544114\",\"orgCode\":\"10244962\",\"productCode\":\"SP231207213937\",\"seqNum\":1,\"skuCode\":\"SK23120700000226\",\"type\":1}]},\"promoProducts\":[],\"promoTemplate\":{\"tagCode\":\"01\",\"templateCode\":\"0101020103010401\",\"templateDesc\":\"单个商品无条件减金额\",\"templateName\":\"单品无条件减金额\"},\"promoTemplateFunctions\":[{\"functionCode\":\"0101\",\"functionDesc\":\"单品\",\"functionName\":\"F0101\",\"functionType\":\"01\"},{\"functionCode\":\"0201\",\"functionDesc\":\"无\",\"functionName\":\"F0201\",\"functionType\":\"02\"},{\"functionCode\":\"0301\",\"functionDesc\":\"None\",\"functionName\":\"F0301\",\"functionType\":\"03\"},{\"functionCode\":\"0401\",\"functionDesc\":\"减金额\",\"functionName\":\"F0401\",\"functionType\":\"04\"}],\"qualificationModels\":[],\"version\":\"20200310\"},\"10102198384953190176\":{\"activityModel\":{\"activityBegin\":\"20231220000000\",\"activityCode\":\"10102198384953190176\",\"activityEnd\":\"20231223235959\",\"activityName\":\"测试计算价格\",\"activityStatus\":\"04\",\"activityType\":\"01\",\"activityUrl\":\"\",\"backgroundImage\":\"\",\"coolDown\":\"\",\"couponActivity\":false,\"createTime\":1703038495000,\"createUser\":\"10230227619262\",\"domainCode\":\"DC0005\",\"extendParams\":{},\"externalActivityId\":\"\",\"groupCode\":\"101\",\"id\":581105,\"incentiveLimitedFlag\":\"00\",\"itemScopeType\":1,\"logicDelete\":0,\"needToDoExpire\":false,\"opsType\":\"101\",\"orgCode\":\"10244962\",\"periodType\":\"00\",\"priceCondition\":\"0\",\"priority\":999,\"productSelectionType\":\"01\",\"promotionCategory\":\"\",\"promotionCategoryName\":\"\",\"ribbonImage\":\"\",\"ribbonPosition\":\"\",\"ribbonText\":\"\",\"showFlag\":1,\"sponsors\":\"default\",\"storeType\":\"01\",\"templateCode\":\"0101020103010401\",\"tenantCode\":\"100016\",\"updateUser\":\"10230227619262\",\"warmBegin\":\"\",\"warmEnd\":\"\"},\"giveaways\":[],\"groupCacheList\":[{\"$ref\":\"$.10102198613986791906.groupCacheList[0]\"},{\"$ref\":\"$.10102198613986791906.groupCacheList[1]\"},{\"$ref\":\"$.10102198613986791906.groupCacheList[2]\"},{\"$ref\":\"$.10102198613986791906.groupCacheList[3]\"},{\"$ref\":\"$.10102198613986791906.groupCacheList[4]\"},{\"$ref\":\"$.10102198613986791906.groupCacheList[5]\"},{\"$ref\":\"$.10102198613986791906.groupCacheList[6]\"},{\"$ref\":\"$.10102198613986791906.groupCacheList[7]\"},{\"$ref\":\"$.10102198613986791906.groupCacheList[8]\"},{\"$ref\":\"$.10102198613986791906.groupCacheList[9]\"},{\"$ref\":\"$.10102198613986791906.groupCacheList[10]\"},{\"$ref\":\"$.10102198613986791906.groupCacheList[11]\"},{\"$ref\":\"$.10102198613986791906.groupCacheList[12]\"},{\"$ref\":\"$.10102198613986791906.groupCacheList[13]\"},{\"$ref\":\"$.10102198613986791906.groupCacheList[14]\"},{\"$ref\":\"$.10102198613986791906.groupCacheList[15]\"},{\"$ref\":\"$.10102198613986791906.groupCacheList[16]\"},{\"$ref\":\"$.10102198613986791906.groupCacheList[17]\"},{\"$ref\":\"$.10102198613986791906.groupCacheList[18]\"},{\"$ref\":\"$.10102198613986791906.groupCacheList[19]\"},{\"$ref\":\"$.10102198613986791906.groupCacheList[20]\"},{\"$ref\":\"$.10102198613986791906.groupCacheList[21]\"}],\"incentiveLimiteds\":[],\"languageMap\":{\"en-US\":{\"activityCode\":\"10102198384953190176\",\"activityDesc\":\"\",\"activityLabel\":\"\",\"activityName\":\"\",\"id\":1273582,\"language\":\"en-US\",\"tenantCode\":\"100016\"},\"id-ID\":{\"activityCode\":\"10102198384953190176\",\"activityDesc\":\"\",\"activityLabel\":\"\",\"activityName\":\"\",\"id\":1273583,\"language\":\"id-ID\",\"tenantCode\":\"100016\"},\"zh-CN\":{\"activityCode\":\"10102198384953190176\",\"activityDesc\":\"\",\"activityLabel\":\"\",\"activityName\":\"\",\"id\":1273581,\"language\":\"zh-CN\",\"tenantCode\":\"100016\"}},\"promoChannels\":[{\"activityCode\":\"10102198384953190176\",\"channelCode\":\"0000\",\"channelName\":\"0000\",\"id\":\"556469\",\"orgCode\":\"10244962\",\"storeName\":\"demo\",\"tenantCode\":\"100016\"}],\"promoFuncParams\":[{\"functionCode\":\"0101\",\"functionName\":\"F0101\",\"functionType\":\"01\",\"id\":\"2704991\",\"paramType\":\"01\",\"rankId\":\"676263\",\"tenantCode\":\"100016\"},{\"functionCode\":\"0201\",\"functionName\":\"F0201\",\"functionType\":\"02\",\"id\":\"2704992\",\"paramType\":\"01\",\"rankId\":\"676263\",\"tenantCode\":\"100016\"},{\"functionCode\":\"0301\",\"functionName\":\"F0301\",\"functionType\":\"03\",\"id\":\"2704993\",\"paramType\":\"01\",\"rankId\":\"676263\",\"tenantCode\":\"100016\"},{\"functionCode\":\"0401\",\"functionName\":\"F0401\",\"functionType\":\"04\",\"id\":\"2704994\",\"paramType\":\"02\",\"paramUnit\":\"01\",\"paramValue\":\"10\",\"rankId\":\"676263\",\"tenantCode\":\"100016\"}],\"promoFuncRanks\":[{\"activityCode\":\"10102198384953190176\",\"id\":\"676263\",\"rankParam\":1,\"templateCode\":\"0101020103010401\",\"tenantCode\":\"100016\"}],\"promoProductCombines\":[],\"promoProductDetails\":{\"SP231207213937\":[{\"activityCode\":\"10102198384953190176\",\"orgCode\":\"10244962\",\"productCode\":\"SP231207213937\",\"seqNum\":1,\"skuCode\":\"SK23120700000226\",\"type\":1}]},\"promoProducts\":[],\"promoTemplate\":{\"tagCode\":\"01\",\"templateCode\":\"0101020103010401\",\"templateDesc\":\"单个商品无条件减金额\",\"templateName\":\"单品无条件减金额\"},\"promoTemplateFunctions\":[{\"functionCode\":\"0101\",\"functionDesc\":\"单品\",\"functionName\":\"F0101\",\"functionType\":\"01\"},{\"functionCode\":\"0201\",\"functionDesc\":\"无\",\"functionName\":\"F0201\",\"functionType\":\"02\"},{\"functionCode\":\"0301\",\"functionDesc\":\"None\",\"functionName\":\"F0301\",\"functionType\":\"03\"},{\"functionCode\":\"0401\",\"functionDesc\":\"减金额\",\"functionName\":\"F0401\",\"functionType\":\"04\"}],\"qualificationModels\":[],\"version\":\"20200310\"},\"10102198400631198631\":{\"activityModel\":{\"activityBegin\":\"20231220000000\",\"activityCode\":\"10102198400631198631\",\"activityEnd\":\"20231223235959\",\"activityName\":\"单品特价1122\",\"activityStatus\":\"04\",\"activityType\":\"01\",\"activityUrl\":\"\",\"backgroundImage\":\"\",\"coolDown\":\"\",\"couponActivity\":false,\"createTime\":1703040063000,\"createUser\":\"10230227619262\",\"domainCode\":\"DC0005\",\"extendParams\":{},\"externalActivityId\":\"\",\"groupCode\":\"108\",\"id\":581108,\"incentiveLimitedFlag\":\"00\",\"itemScopeType\":1,\"logicDelete\":0,\"needToDoExpire\":false,\"opsType\":\"108\",\"orgCode\":\"10244962\",\"periodType\":\"00\",\"priceCondition\":\"0\",\"priority\":999,\"productSelectionType\":\"01\",\"promotionCategory\":\"\",\"promotionCategoryName\":\"\",\"ribbonImage\":\"\",\"ribbonPosition\":\"\",\"ribbonText\":\"\",\"showFlag\":1,\"sponsors\":\"default\",\"storeType\":\"01\",\"templateCode\":\"0101020103010411\",\"tenantCode\":\"100016\",\"updateUser\":\"10230227619262\",\"warmBegin\":\"\",\"warmEnd\":\"\"},\"giveaways\":[],\"groupCacheList\":[{\"$ref\":\"$.10102198613986791906.groupCacheList[0]\"},{\"$ref\":\"$.10102198613986791906.groupCacheList[1]\"},{\"$ref\":\"$.10102198613986791906.groupCacheList[2]\"},{\"$ref\":\"$.10102198613986791906.groupCacheList[3]\"},{\"$ref\":\"$.10102198613986791906.groupCacheList[4]\"},{\"$ref\":\"$.10102198613986791906.groupCacheList[5]\"},{\"$ref\":\"$.10102198613986791906.groupCacheList[6]\"},{\"$ref\":\"$.10102198613986791906.groupCacheList[7]\"},{\"$ref\":\"$.10102198613986791906.groupCacheList[8]\"},{\"$ref\":\"$.10102198613986791906.groupCacheList[9]\"},{\"$ref\":\"$.10102198613986791906.groupCacheList[10]\"},{\"$ref\":\"$.10102198613986791906.groupCacheList[11]\"},{\"$ref\":\"$.10102198613986791906.groupCacheList[12]\"},{\"$ref\":\"$.10102198613986791906.groupCacheList[13]\"},{\"$ref\":\"$.10102198613986791906.groupCacheList[14]\"},{\"$ref\":\"$.10102198613986791906.groupCacheList[15]\"},{\"$ref\":\"$.10102198613986791906.groupCacheList[16]\"},{\"$ref\":\"$.10102198613986791906.groupCacheList[17]\"},{\"$ref\":\"$.10102198613986791906.groupCacheList[18]\"},{\"$ref\":\"$.10102198613986791906.groupCacheList[19]\"},{\"$ref\":\"$.10102198613986791906.groupCacheList[20]\"},{\"$ref\":\"$.10102198613986791906.groupCacheList[21]\"}],\"incentiveLimiteds\":[],\"languageMap\":{\"en-US\":{\"activityCode\":\"10102198400631198631\",\"activityDesc\":\"\",\"activityLabel\":\"\",\"activityName\":\"\",\"id\":1273589,\"language\":\"en-US\",\"tenantCode\":\"100016\"},\"id-ID\":{\"activityCode\":\"10102198400631198631\",\"activityDesc\":\"\",\"activityLabel\":\"\",\"activityName\":\"\",\"id\":1273590,\"language\":\"id-ID\",\"tenantCode\":\"100016\"},\"zh-CN\":{\"activityCode\":\"10102198400631198631\",\"activityDesc\":\"\",\"activityLabel\":\"\",\"activityName\":\"\",\"id\":1273588,\"language\":\"zh-CN\",\"tenantCode\":\"100016\"}},\"promoChannels\":[{\"activityCode\":\"10102198400631198631\",\"channelCode\":\"0000\",\"channelName\":\"0000\",\"id\":\"556472\",\"orgCode\":\"10244962\",\"storeName\":\"demo\",\"tenantCode\":\"100016\"}],\"promoFuncParams\":[{\"functionCode\":\"0101\",\"functionName\":\"F0101\",\"functionType\":\"01\",\"id\":\"2705007\",\"paramType\":\"01\",\"rankId\":\"676267\",\"tenantCode\":\"100016\"},{\"functionCode\":\"0201\",\"functionName\":\"F0201\",\"functionType\":\"02\",\"id\":\"2705008\",\"paramType\":\"01\",\"rankId\":\"676267\",\"tenantCode\":\"100016\"},{\"functionCode\":\"0301\",\"functionName\":\"F0301\",\"functionType\":\"03\",\"id\":\"2705009\",\"paramType\":\"01\",\"rankId\":\"676267\",\"tenantCode\":\"100016\"},{\"functionCode\":\"0411\",\"functionName\":\"F0411\",\"functionType\":\"04\",\"id\":\"2705010\",\"paramType\":\"01\",\"rankId\":\"676267\",\"tenantCode\":\"100016\"}],\"promoFuncRanks\":[{\"activityCode\":\"10102198400631198631\",\"id\":\"676267\",\"rankParam\":1,\"templateCode\":\"0101020103010411\",\"tenantCode\":\"100016\"}],\"promoProductCombines\":[],\"promoProductDetails\":{\"SP231207213937\":[{\"activityCode\":\"10102198400631198631\",\"productCode\":\"SP231207213937\",\"promoPrice\":100.000,\"seqNum\":1,\"skuCode\":\"SK23120700000226\",\"type\":1}]},\"promoProducts\":[],\"promoTemplate\":{\"tagCode\":\"01\",\"templateCode\":\"0101020103010411\",\"templateDesc\":\"单品无条件设为每件不同特价\",\"templateName\":\"单品无条件设为每件不同特价\"},\"promoTemplateFunctions\":[{\"functionCode\":\"0101\",\"functionDesc\":\"单品\",\"functionName\":\"F0101\",\"functionType\":\"01\"},{\"functionCode\":\"0201\",\"functionDesc\":\"无\",\"functionName\":\"F0201\",\"functionType\":\"02\"},{\"functionCode\":\"0301\",\"functionDesc\":\"None\",\"functionName\":\"F0301\",\"functionType\":\"03\"}," +
//                "{\"functionCode\":\"0411\",\"functionDesc\":\"每件不同特价\",\"functionName\"" +
//                ":\"F0411\",\"functionType\":\"04\"}],\"qualificationModels\":[],\"version\":\"20200310\"}}";


        ActivityCacheDTO activityCacheDTO = new ActivityCacheDTO();
        ActivityModel activityModel = new ActivityModel();
        activityModel.setTemplateCode("0101020103010411");
        activityModel.setActivityCode("10102198613986791906");
        activityCacheDTO.setActivityModel(activityModel);
        List<FunctionParamModel> funcParamList = new ArrayList<>();

        activityCacheDTO.setPromoFuncParams(funcParamList);
        Map<String, ActivityCacheDTO> activityCacheMap = new HashMap<>();

        activityCacheMap.put("10102198613986791906",activityCacheDTO);

        String singletemplateCodes = "0101020103010401,0101020103010402,0101020103010403,0101020103010411";

        when(templateDomain.findTemplateTagByTagId(any())).thenReturn(singletemplateCodes);

        String prodcutDetails= "[{\"activityCode\":\"10102198384953190176\",\"orgCode\":\"10244962\",\"productCode\":\"SP231207213937\",\"seqNum\":1,\"skuCode\":\"SK23120700000226\",\"type\":1}," +
                "{\"activityCode\":\"10102198400631198631\",\"productCode\":\"SP231207213937\",\"promoPrice\":100.000,\"seqNum\":1,\"skuCode\":\"SK23120700000226\",\"type\":1}," +
                "{\"activityCode\":\"10102198404747544114\",\"orgCode\":\"10244962\",\"productCode\":\"SP231207213937\",\"seqNum\":1,\"skuCode\":\"SK23120700000226\",\"type\":1}," +
                "{\"activityCode\":\"10102198613986791906\",\"productCode\":\"SP231207213937\",\"promoPrice\":200.000,\"seqNum\":1,\"type\":1}]";

        List<ProductSkuDetailDTO> productSkuDetailDTOList = JSONArray.parseArray(prodcutDetails, ProductSkuDetailDTO.class);

        when(activityProductDetailService.queryListByActivityCodesAndProductCode(any(),any())).thenReturn(productSkuDetailDTOList);

        when(activityCacheDomain.filterActivityByProduct(any(),any(),any())).thenReturn(activityCacheMap);

        when(promoGroupDomain.checkGroupEnabledByTenantCode(any(),any())).thenReturn(true);
        PromoGroupVO promoGroupVO = new PromoGroupVO();
        when(promotionGroupService.getGroupByGroupCode(any(),any())).thenReturn(promoGroupVO);

        when(activityPriceComponentDomain.getTenantPrecision(any())).thenReturn(2);

        SkuActivityPriceDTO skuActivityPriceDTO = activityComponentDomain.activitySkuPrice(product, activityCacheMap);

        Assert.assertNotNull(skuActivityPriceDTO);

    }

    @Test
    public void activitySkuPrice_false(){

        SingleProductDTO product = new SingleProductDTO();

        ActivityCacheDTO activityCacheDTO = new ActivityCacheDTO();
        ActivityModel activityModel = new ActivityModel();
        activityModel.setTemplateCode("0101020103010411");
        activityModel.setActivityCode("10102198613986791906");
        activityCacheDTO.setActivityModel(activityModel);

        Map<String, ActivityCacheDTO> activityCacheMap = new HashMap<>();
        List<FunctionParamModel> funcParamList = new ArrayList<>();
        activityCacheDTO.setPromoFuncParams(funcParamList);

        activityCacheMap.put("10102198613986791906",activityCacheDTO);

        String singletemplateCodes = "0101020103010401,0101020103010402,0101020103010403,0101020103010411";

        when(templateDomain.findTemplateTagByTagId(any())).thenReturn(singletemplateCodes);


        String prodcutDetails= "[{\"activityCode\":\"10102198384953190176\",\"orgCode\":\"10244962\",\"productCode\":\"SP231207213937\",\"seqNum\":1,\"skuCode\":\"SK23120700000226\",\"type\":1}," +
                "{\"activityCode\":\"10102198400631198631\",\"productCode\":\"SP231207213937\",\"promoPrice\":100.000,\"seqNum\":1,\"skuCode\":\"SK23120700000226\",\"type\":1}," +
                "{\"activityCode\":\"10102198404747544114\",\"orgCode\":\"10244962\",\"productCode\":\"SP231207213937\",\"seqNum\":1,\"skuCode\":\"SK23120700000226\",\"type\":1}," +
                "{\"activityCode\":\"10102198613986791906\",\"productCode\":\"SP231207213937\",\"promoPrice\":200.000,\"seqNum\":1,\"type\":1}]";

        List<ProductSkuDetailDTO> productSkuDetailDTOList = JSONArray.parseArray(prodcutDetails, ProductSkuDetailDTO.class);

        when(activityProductDetailService.queryListByActivityCodesAndProductCode(any(),any())).thenReturn(productSkuDetailDTOList);

        when(activityCacheDomain.filterActivityByProduct(any(),any(),any())).thenReturn(activityCacheMap);

        when(promoGroupDomain.checkGroupEnabledByTenantCode(any(),any())).thenReturn(false);
        PromoGroupVO promoGroupVO = new PromoGroupVO();
        when(promotionGroupService.getGroupByGroupCode(any(),any())).thenReturn(promoGroupVO);

        when(activityPriceComponentDomain.getTenantPrecision(any())).thenReturn(2);
        SkuActivityPriceDTO skuActivityPriceDTO = activityComponentDomain.activitySkuPrice(product, activityCacheMap);

        Assert.assertNotNull(skuActivityPriceDTO);

    }

    @Test
    public void activitySkuPrice_empty(){

        SingleProductDTO product = new SingleProductDTO();

        String map = "{\"10102198613986791906\":{\"activityModel\":{\"activityBegin\":\"20231220000000\",\"activityCode\":\"10102198613986791906\",\"activityEnd\":\"20231221235959\",\"activityName\":\"单品sp特价\",\"activityStatus\":\"04\",\"activityType\":\"01\",\"activityUrl\":\"\",\"backgroundImage\":\"\",\"coolDown\":\"\",\"couponActivity\":false,\"createTime\":1703061398000,\"createUser\":\"10230227619262\",\"domainCode\":\"DC0005\",\"extendParams\":{},\"externalActivityId\":\"\",\"groupCode\":\"108\",\"id\":581141,\"incentiveLimitedFlag\":\"00\",\"itemScopeType\":1,\"logicDelete\":0,\"needToDoExpire\":false,\"opsType\":\"108\",\"orgCode\":\"10244962\",\"periodType\":\"00\",\"priceCondition\":\"0\",\"priority\":999,\"productSelectionType\":\"01\",\"promotionCategory\":\"\",\"promotionCategoryName\":\"\",\"ribbonImage\":\"\",\"ribbonPosition\":\"\",\"ribbonText\":\"\",\"showFlag\":1,\"sponsors\":\"default\",\"storeType\":\"01\",\"templateCode\":\"0101020103010411\",\"tenantCode\":\"100016\",\"updateUser\":\"10230227619262\",\"warmBegin\":\"\",\"warmEnd\":\"\"},\"giveaways\":[],\"groupCacheList\":[{\"groupCode\":\"302\",\"priority\":22,\"relationList\":[\"101\",\"102\",\"103\",\"104\",\"105\",\"106\",\"107\",\"108\",\"201\",\"202\",\"203\",\"204\",\"205\",\"206\",\"207\"]},{\"groupCode\":\"303\",\"priority\":21,\"relationList\":[\"101\",\"102\",\"103\",\"104\",\"105\",\"106\",\"107\",\"108\",\"201\",\"202\",\"203\",\"204\",\"205\",\"206\",\"207\"]},{\"groupCode\":\"701\",\"priority\":20,\"relationList\":[\"101\",\"102\",\"103\",\"104\",\"105\",\"106\",\"107\",\"108\",\"201\",\"202\",\"203\",\"204\",\"205\",\"206\",\"207\"]},{\"groupCode\":\"601\",\"priority\":19,\"relationList\":[\"101\",\"102\",\"103\",\"104\",\"105\",\"106\",\"107\",\"108\",\"201\",\"202\",\"203\",\"204\",\"205\",\"206\",\"207\"]},{\"groupCode\":\"501\",\"priority\":18,\"relationList\":[\"101\",\"102\",\"103\",\"104\",\"105\",\"106\",\"107\",\"108\",\"201\",\"202\",\"203\",\"204\",\"205\",\"206\",\"207\"]},{\"groupCode\":\"401\",\"priority\":17,\"relationList\":[\"101\",\"102\",\"103\",\"104\",\"105\",\"106\",\"107\",\"108\",\"201\",\"202\",\"203\",\"204\",\"205\",\"206\",\"207\"]},{\"groupCode\":\"301\",\"priority\":16,\"relationList\":[\"101\",\"102\",\"103\",\"104\",\"105\",\"106\",\"107\",\"108\",\"201\",\"202\",\"203\",\"204\",\"205\",\"206\",\"207\"]},{\"groupCode\":\"207\",\"priority\":15,\"relationList\":[\"301\",\"302\",\"303\",\"401\",\"501\",\"601\",\"701\"]},{\"groupCode\":\"206\",\"priority\":14,\"relationList\":[\"301\",\"302\",\"303\",\"401\",\"501\",\"601\",\"701\"]},{\"groupCode\":\"205\",\"priority\":13,\"relationList\":[\"301\",\"302\",\"303\",\"401\",\"501\",\"601\",\"701\"]},{\"groupCode\":\"204\",\"priority\":12,\"relationList\":[\"301\",\"302\",\"303\",\"401\",\"501\",\"601\",\"701\"]},{\"groupCode\":\"203\",\"priority\":11,\"relationList\":[\"301\",\"302\",\"303\",\"401\",\"501\",\"601\",\"701\"]},{\"groupCode\":\"202\",\"priority\":10,\"relationList\":[\"301\",\"302\",\"303\",\"401\",\"501\",\"601\",\"701\"]},{\"groupCode\":\"201\",\"priority\":9,\"relationList\":[\"301\",\"302\",\"303\",\"401\",\"501\",\"601\",\"701\"]},{\"groupCode\":\"106\",\"priority\":8,\"relationList\":[\"301\",\"302\",\"303\",\"401\",\"501\",\"601\",\"701\"]},{\"groupCode\":\"105\",\"priority\":7,\"relationList\":[\"301\",\"302\",\"303\",\"401\",\"501\",\"601\",\"701\"]},{\"groupCode\":\"104\",\"priority\":6,\"relationList\":[\"301\",\"302\",\"303\",\"401\",\"501\",\"601\",\"701\"]},{\"groupCode\":\"103\",\"priority\":5,\"relationList\":[\"301\",\"302\",\"303\",\"401\",\"501\",\"601\",\"701\"]},{\"groupCode\":\"102\",\"priority\":4,\"relationList\":[\"301\",\"302\",\"303\",\"401\",\"501\",\"601\",\"701\"]},{\"groupCode\":\"107\",\"priority\":3,\"relationList\":[\"301\",\"302\",\"303\",\"401\",\"501\",\"601\",\"701\"]},{\"groupCode\":\"108\",\"priority\":2,\"relationList\":[\"301\",\"302\",\"303\",\"401\",\"501\",\"601\",\"701\"]},{\"groupCode\":\"101\",\"priority\":1,\"relationList\":[\"301\",\"302\",\"303\",\"401\",\"501\",\"601\",\"701\"]}],\"incentiveLimiteds\":[],\"languageMap\":{\"en-US\":{\"activityCode\":\"10102198613986791906\",\"activityDesc\":\"\",\"activityLabel\":\"\",\"activityName\":\"\",\"id\":1273664,\"language\":\"en-US\",\"tenantCode\":\"100016\"},\"id-ID\":{\"activityCode\":\"10102198613986791906\",\"activityDesc\":\"\",\"activityLabel\":\"\",\"activityName\":\"\",\"id\":1273665,\"language\":\"id-ID\",\"tenantCode\":\"100016\"},\"zh-CN\":{\"activityCode\":\"10102198613986791906\",\"activityDesc\":\"\",\"activityLabel\":\"\",\"activityName\":\"\",\"id\":1273663,\"language\":\"zh-CN\",\"tenantCode\":\"100016\"}},\"promoChannels\":[{\"activityCode\":\"10102198613986791906\",\"channelCode\":\"0000\",\"channelName\":\"0000\",\"id\":\"556485\",\"orgCode\":\"10244962\",\"storeName\":\"demo\",\"tenantCode\":\"100016\"}],\"promoFuncParams\":[{\"functionCode\":\"0101\",\"functionName\":\"F0101\",\"functionType\":\"01\",\"id\":\"2705139\",\"paramType\":\"01\",\"rankId\":\"676300\",\"tenantCode\":\"100016\"},{\"functionCode\":\"0201\",\"functionName\":\"F0201\",\"functionType\":\"02\",\"id\":\"2705140\",\"paramType\":\"01\",\"rankId\":\"676300\",\"tenantCode\":\"100016\"},{\"functionCode\":\"0301\",\"functionName\":\"F0301\",\"functionType\":\"03\",\"id\":\"2705141\",\"paramType\":\"01\",\"rankId\":\"676300\",\"tenantCode\":\"100016\"},{\"functionCode\":\"0411\",\"functionName\":\"F0411\",\"functionType\":\"04\",\"id\":\"2705142\",\"paramType\":\"01\",\"rankId\":\"676300\",\"tenantCode\":\"100016\"}],\"promoFuncRanks\":[{\"activityCode\":\"10102198613986791906\",\"id\":\"676300\",\"rankParam\":1,\"templateCode\":\"0101020103010411\",\"tenantCode\":\"100016\"}],\"promoProductCombines\":[],\"promoProductDetails\":{\"SP231207213937\":[{\"activityCode\":\"10102198613986791906\",\"productCode\":\"SP231207213937\",\"promoPrice\":200.000,\"seqNum\":1,\"type\":1}]},\"promoProducts\":[],\"promoTemplate\":{\"tagCode\":\"01\",\"templateCode\":\"0101020103010411\",\"templateDesc\":\"单品无条件设为每件不同特价\",\"templateName\":\"单品无条件设为每件不同特价\"},\"promoTemplateFunctions\":[{\"functionCode\":\"0101\",\"functionDesc\":\"单品\",\"functionName\":\"F0101\",\"functionType\":\"01\"},{\"functionCode\":\"0201\",\"functionDesc\":\"无\",\"functionName\":\"F0201\",\"functionType\":\"02\"},{\"functionCode\":\"0301\",\"functionDesc\":\"None\",\"functionName\":\"F0301\",\"functionType\":\"03\"},{\"functionCode\":\"0411\",\"functionDesc\":\"每件不同特价\",\"functionName\":\"F0411\",\"functionType\":\"04\"}],\"qualificationModels\":[],\"version\":\"20200310\"},\"10102198404747544114\":{\"activityModel\":{\"activityBegin\":\"20231220000000\",\"activityCode\":\"10102198404747544114\",\"activityEnd\":\"20231223235959\",\"activityName\":\"打折促销222222222\",\"activityStatus\":\"04\",\"activityType\":\"01\",\"activityUrl\":\"\",\"backgroundImage\":\"\",\"coolDown\":\"\",\"couponActivity\":false,\"createTime\":1703040474000,\"createUser\":\"10230227619262\",\"domainCode\":\"DC0005\",\"extendParams\":{},\"externalActivityId\":\"\",\"groupCode\":\"101\",\"id\":581109,\"incentiveLimitedFlag\":\"00\",\"itemScopeType\":1,\"logicDelete\":0,\"needToDoExpire\":false,\"opsType\":\"101\",\"orgCode\":\"10244962\",\"periodType\":\"00\",\"priceCondition\":\"0\",\"priority\":999,\"productSelectionType\":\"01\",\"promotionCategory\":\"\",\"promotionCategoryName\":\"\",\"ribbonImage\":\"\",\"ribbonPosition\":\"\",\"ribbonText\":\"\",\"showFlag\":1,\"sponsors\":\"default\",\"storeType\":\"01\",\"templateCode\":\"0101020103010401\",\"tenantCode\":\"100016\",\"updateUser\":\"10230227619262\",\"warmBegin\":\"\",\"warmEnd\":\"\"},\"giveaways\":[],\"groupCacheList\":[{\"$ref\":\"$.10102198613986791906.groupCacheList[0]\"},{\"$ref\":\"$.10102198613986791906.groupCacheList[1]\"},{\"$ref\":\"$.10102198613986791906.groupCacheList[2]\"},{\"$ref\":\"$.10102198613986791906.groupCacheList[3]\"},{\"$ref\":\"$.10102198613986791906.groupCacheList[4]\"},{\"$ref\":\"$.10102198613986791906.groupCacheList[5]\"},{\"$ref\":\"$.10102198613986791906.groupCacheList[6]\"},{\"$ref\":\"$.10102198613986791906.groupCacheList[7]\"},{\"$ref\":\"$.10102198613986791906.groupCacheList[8]\"},{\"$ref\":\"$.10102198613986791906.groupCacheList[9]\"},{\"$ref\":\"$.10102198613986791906.groupCacheList[10]\"},{\"$ref\":\"$.10102198613986791906.groupCacheList[11]\"},{\"$ref\":\"$.10102198613986791906.groupCacheList[12]\"},{\"$ref\":\"$.10102198613986791906.groupCacheList[13]\"},{\"$ref\":\"$.10102198613986791906.groupCacheList[14]\"},{\"$ref\":\"$.10102198613986791906.groupCacheList[15]\"},{\"$ref\":\"$.10102198613986791906.groupCacheList[16]\"},{\"$ref\":\"$.10102198613986791906.groupCacheList[17]\"},{\"$ref\":\"$.10102198613986791906.groupCacheList[18]\"},{\"$ref\":\"$.10102198613986791906.groupCacheList[19]\"},{\"$ref\":\"$.10102198613986791906.groupCacheList[20]\"},{\"$ref\":\"$.10102198613986791906.groupCacheList[21]\"}],\"incentiveLimiteds\":[],\"languageMap\":{\"en-US\":{\"activityCode\":\"10102198404747544114\",\"activityDesc\":\"\",\"activityLabel\":\"\",\"activityName\":\"\",\"id\":1273592,\"language\":\"en-US\",\"tenantCode\":\"100016\"},\"id-ID\":{\"activityCode\":\"10102198404747544114\",\"activityDesc\":\"\",\"activityLabel\":\"\",\"activityName\":\"\",\"id\":1273593,\"language\":\"id-ID\",\"tenantCode\":\"100016\"},\"zh-CN\":{\"activityCode\":\"10102198404747544114\",\"activityDesc\":\"\",\"activityLabel\":\"\",\"activityName\":\"\",\"id\":1273591,\"language\":\"zh-CN\",\"tenantCode\":\"100016\"}},\"promoChannels\":[{\"activityCode\":\"10102198404747544114\",\"channelCode\":\"0000\",\"channelName\":\"0000\",\"id\":\"556473\",\"orgCode\":\"10244962\",\"storeName\":\"demo\",\"tenantCode\":\"100016\"}],\"promoFuncParams\":[{\"functionCode\":\"0101\",\"functionName\":\"F0101\",\"functionType\":\"01\",\"id\":\"2705011\",\"paramType\":\"01\",\"rankId\":\"676268\",\"tenantCode\":\"100016\"},{\"functionCode\":\"0201\",\"functionName\":\"F0201\",\"functionType\":\"02\",\"id\":\"2705012\",\"paramType\":\"01\",\"rankId\":\"676268\",\"tenantCode\":\"100016\"},{\"functionCode\":\"0301\",\"functionName\":\"F0301\",\"functionType\":\"03\",\"id\":\"2705013\",\"paramType\":\"01\",\"rankId\":\"676268\",\"tenantCode\":\"100016\"},{\"functionCode\":\"0401\",\"functionName\":\"F0401\",\"functionType\":\"04\",\"id\":\"2705014\",\"paramType\":\"02\",\"paramUnit\":\"01\",\"paramValue\":\"20\",\"rankId\":\"676268\",\"tenantCode\":\"100016\"}],\"promoFuncRanks\":[{\"activityCode\":\"10102198404747544114\",\"id\":\"676268\",\"rankParam\":1,\"templateCode\":\"0101020103010401\",\"tenantCode\":\"100016\"}],\"promoProductCombines\":[],\"promoProductDetails\":{\"SP231207213937\":[{\"activityCode\":\"10102198404747544114\",\"orgCode\":\"10244962\",\"productCode\":\"SP231207213937\",\"seqNum\":1,\"skuCode\":\"SK23120700000226\",\"type\":1}]},\"promoProducts\":[],\"promoTemplate\":{\"tagCode\":\"01\",\"templateCode\":\"0101020103010401\",\"templateDesc\":\"单个商品无条件减金额\",\"templateName\":\"单品无条件减金额\"},\"promoTemplateFunctions\":[{\"functionCode\":\"0101\",\"functionDesc\":\"单品\",\"functionName\":\"F0101\",\"functionType\":\"01\"},{\"functionCode\":\"0201\",\"functionDesc\":\"无\",\"functionName\":\"F0201\",\"functionType\":\"02\"},{\"functionCode\":\"0301\",\"functionDesc\":\"None\",\"functionName\":\"F0301\",\"functionType\":\"03\"},{\"functionCode\":\"0401\",\"functionDesc\":\"减金额\",\"functionName\":\"F0401\",\"functionType\":\"04\"}],\"qualificationModels\":[],\"version\":\"20200310\"},\"10102198384953190176\":{\"activityModel\":{\"activityBegin\":\"20231220000000\",\"activityCode\":\"10102198384953190176\",\"activityEnd\":\"20231223235959\",\"activityName\":\"测试计算价格\",\"activityStatus\":\"04\",\"activityType\":\"01\",\"activityUrl\":\"\",\"backgroundImage\":\"\",\"coolDown\":\"\",\"couponActivity\":false,\"createTime\":1703038495000,\"createUser\":\"10230227619262\",\"domainCode\":\"DC0005\",\"extendParams\":{},\"externalActivityId\":\"\",\"groupCode\":\"101\",\"id\":581105,\"incentiveLimitedFlag\":\"00\",\"itemScopeType\":1,\"logicDelete\":0,\"needToDoExpire\":false,\"opsType\":\"101\",\"orgCode\":\"10244962\",\"periodType\":\"00\",\"priceCondition\":\"0\",\"priority\":999,\"productSelectionType\":\"01\",\"promotionCategory\":\"\",\"promotionCategoryName\":\"\",\"ribbonImage\":\"\",\"ribbonPosition\":\"\",\"ribbonText\":\"\",\"showFlag\":1,\"sponsors\":\"default\",\"storeType\":\"01\",\"templateCode\":\"0101020103010401\",\"tenantCode\":\"100016\",\"updateUser\":\"10230227619262\",\"warmBegin\":\"\",\"warmEnd\":\"\"},\"giveaways\":[],\"groupCacheList\":[{\"$ref\":\"$.10102198613986791906.groupCacheList[0]\"},{\"$ref\":\"$.10102198613986791906.groupCacheList[1]\"},{\"$ref\":\"$.10102198613986791906.groupCacheList[2]\"},{\"$ref\":\"$.10102198613986791906.groupCacheList[3]\"},{\"$ref\":\"$.10102198613986791906.groupCacheList[4]\"},{\"$ref\":\"$.10102198613986791906.groupCacheList[5]\"},{\"$ref\":\"$.10102198613986791906.groupCacheList[6]\"},{\"$ref\":\"$.10102198613986791906.groupCacheList[7]\"},{\"$ref\":\"$.10102198613986791906.groupCacheList[8]\"},{\"$ref\":\"$.10102198613986791906.groupCacheList[9]\"},{\"$ref\":\"$.10102198613986791906.groupCacheList[10]\"},{\"$ref\":\"$.10102198613986791906.groupCacheList[11]\"},{\"$ref\":\"$.10102198613986791906.groupCacheList[12]\"},{\"$ref\":\"$.10102198613986791906.groupCacheList[13]\"},{\"$ref\":\"$.10102198613986791906.groupCacheList[14]\"},{\"$ref\":\"$.10102198613986791906.groupCacheList[15]\"},{\"$ref\":\"$.10102198613986791906.groupCacheList[16]\"},{\"$ref\":\"$.10102198613986791906.groupCacheList[17]\"},{\"$ref\":\"$.10102198613986791906.groupCacheList[18]\"},{\"$ref\":\"$.10102198613986791906.groupCacheList[19]\"},{\"$ref\":\"$.10102198613986791906.groupCacheList[20]\"},{\"$ref\":\"$.10102198613986791906.groupCacheList[21]\"}],\"incentiveLimiteds\":[],\"languageMap\":{\"en-US\":{\"activityCode\":\"10102198384953190176\",\"activityDesc\":\"\",\"activityLabel\":\"\",\"activityName\":\"\",\"id\":1273582,\"language\":\"en-US\",\"tenantCode\":\"100016\"},\"id-ID\":{\"activityCode\":\"10102198384953190176\",\"activityDesc\":\"\",\"activityLabel\":\"\",\"activityName\":\"\",\"id\":1273583,\"language\":\"id-ID\",\"tenantCode\":\"100016\"},\"zh-CN\":{\"activityCode\":\"10102198384953190176\",\"activityDesc\":\"\",\"activityLabel\":\"\",\"activityName\":\"\",\"id\":1273581,\"language\":\"zh-CN\",\"tenantCode\":\"100016\"}},\"promoChannels\":[{\"activityCode\":\"10102198384953190176\",\"channelCode\":\"0000\",\"channelName\":\"0000\",\"id\":\"556469\",\"orgCode\":\"10244962\",\"storeName\":\"demo\",\"tenantCode\":\"100016\"}],\"promoFuncParams\":[{\"functionCode\":\"0101\",\"functionName\":\"F0101\",\"functionType\":\"01\",\"id\":\"2704991\",\"paramType\":\"01\",\"rankId\":\"676263\",\"tenantCode\":\"100016\"},{\"functionCode\":\"0201\",\"functionName\":\"F0201\",\"functionType\":\"02\",\"id\":\"2704992\",\"paramType\":\"01\",\"rankId\":\"676263\",\"tenantCode\":\"100016\"},{\"functionCode\":\"0301\",\"functionName\":\"F0301\",\"functionType\":\"03\",\"id\":\"2704993\",\"paramType\":\"01\",\"rankId\":\"676263\",\"tenantCode\":\"100016\"},{\"functionCode\":\"0401\",\"functionName\":\"F0401\",\"functionType\":\"04\",\"id\":\"2704994\",\"paramType\":\"02\",\"paramUnit\":\"01\",\"paramValue\":\"10\",\"rankId\":\"676263\",\"tenantCode\":\"100016\"}],\"promoFuncRanks\":[{\"activityCode\":\"10102198384953190176\",\"id\":\"676263\",\"rankParam\":1,\"templateCode\":\"0101020103010401\",\"tenantCode\":\"100016\"}],\"promoProductCombines\":[],\"promoProductDetails\":{\"SP231207213937\":[{\"activityCode\":\"10102198384953190176\",\"orgCode\":\"10244962\",\"productCode\":\"SP231207213937\",\"seqNum\":1,\"skuCode\":\"SK23120700000226\",\"type\":1}]},\"promoProducts\":[],\"promoTemplate\":{\"tagCode\":\"01\",\"templateCode\":\"0101020103010401\",\"templateDesc\":\"单个商品无条件减金额\",\"templateName\":\"单品无条件减金额\"},\"promoTemplateFunctions\":[{\"functionCode\":\"0101\",\"functionDesc\":\"单品\",\"functionName\":\"F0101\",\"functionType\":\"01\"},{\"functionCode\":\"0201\",\"functionDesc\":\"无\",\"functionName\":\"F0201\",\"functionType\":\"02\"},{\"functionCode\":\"0301\",\"functionDesc\":\"None\",\"functionName\":\"F0301\",\"functionType\":\"03\"},{\"functionCode\":\"0401\",\"functionDesc\":\"减金额\",\"functionName\":\"F0401\",\"functionType\":\"04\"}],\"qualificationModels\":[],\"version\":\"20200310\"},\"10102198400631198631\":{\"activityModel\":{\"activityBegin\":\"20231220000000\",\"activityCode\":\"10102198400631198631\",\"activityEnd\":\"20231223235959\",\"activityName\":\"单品特价1122\",\"activityStatus\":\"04\",\"activityType\":\"01\",\"activityUrl\":\"\",\"backgroundImage\":\"\",\"coolDown\":\"\",\"couponActivity\":false,\"createTime\":1703040063000,\"createUser\":\"10230227619262\",\"domainCode\":\"DC0005\",\"extendParams\":{},\"externalActivityId\":\"\",\"groupCode\":\"108\",\"id\":581108,\"incentiveLimitedFlag\":\"00\",\"itemScopeType\":1,\"logicDelete\":0,\"needToDoExpire\":false,\"opsType\":\"108\",\"orgCode\":\"10244962\",\"periodType\":\"00\",\"priceCondition\":\"0\",\"priority\":999,\"productSelectionType\":\"01\",\"promotionCategory\":\"\",\"promotionCategoryName\":\"\",\"ribbonImage\":\"\",\"ribbonPosition\":\"\",\"ribbonText\":\"\",\"showFlag\":1,\"sponsors\":\"default\",\"storeType\":\"01\",\"templateCode\":\"0101020103010411\",\"tenantCode\":\"100016\",\"updateUser\":\"10230227619262\",\"warmBegin\":\"\",\"warmEnd\":\"\"},\"giveaways\":[],\"groupCacheList\":[{\"$ref\":\"$.10102198613986791906.groupCacheList[0]\"},{\"$ref\":\"$.10102198613986791906.groupCacheList[1]\"},{\"$ref\":\"$.10102198613986791906.groupCacheList[2]\"},{\"$ref\":\"$.10102198613986791906.groupCacheList[3]\"},{\"$ref\":\"$.10102198613986791906.groupCacheList[4]\"},{\"$ref\":\"$.10102198613986791906.groupCacheList[5]\"},{\"$ref\":\"$.10102198613986791906.groupCacheList[6]\"},{\"$ref\":\"$.10102198613986791906.groupCacheList[7]\"},{\"$ref\":\"$.10102198613986791906.groupCacheList[8]\"},{\"$ref\":\"$.10102198613986791906.groupCacheList[9]\"},{\"$ref\":\"$.10102198613986791906.groupCacheList[10]\"},{\"$ref\":\"$.10102198613986791906.groupCacheList[11]\"},{\"$ref\":\"$.10102198613986791906.groupCacheList[12]\"},{\"$ref\":\"$.10102198613986791906.groupCacheList[13]\"},{\"$ref\":\"$.10102198613986791906.groupCacheList[14]\"},{\"$ref\":\"$.10102198613986791906.groupCacheList[15]\"},{\"$ref\":\"$.10102198613986791906.groupCacheList[16]\"},{\"$ref\":\"$.10102198613986791906.groupCacheList[17]\"},{\"$ref\":\"$.10102198613986791906.groupCacheList[18]\"},{\"$ref\":\"$.10102198613986791906.groupCacheList[19]\"},{\"$ref\":\"$.10102198613986791906.groupCacheList[20]\"},{\"$ref\":\"$.10102198613986791906.groupCacheList[21]\"}],\"incentiveLimiteds\":[],\"languageMap\":{\"en-US\":{\"activityCode\":\"10102198400631198631\",\"activityDesc\":\"\",\"activityLabel\":\"\",\"activityName\":\"\",\"id\":1273589,\"language\":\"en-US\",\"tenantCode\":\"100016\"},\"id-ID\":{\"activityCode\":\"10102198400631198631\",\"activityDesc\":\"\",\"activityLabel\":\"\",\"activityName\":\"\",\"id\":1273590,\"language\":\"id-ID\",\"tenantCode\":\"100016\"},\"zh-CN\":{\"activityCode\":\"10102198400631198631\",\"activityDesc\":\"\",\"activityLabel\":\"\",\"activityName\":\"\",\"id\":1273588,\"language\":\"zh-CN\",\"tenantCode\":\"100016\"}},\"promoChannels\":[{\"activityCode\":\"10102198400631198631\",\"channelCode\":\"0000\",\"channelName\":\"0000\",\"id\":\"556472\",\"orgCode\":\"10244962\",\"storeName\":\"demo\",\"tenantCode\":\"100016\"}],\"promoFuncParams\":[{\"functionCode\":\"0101\",\"functionName\":\"F0101\",\"functionType\":\"01\",\"id\":\"2705007\",\"paramType\":\"01\",\"rankId\":\"676267\",\"tenantCode\":\"100016\"},{\"functionCode\":\"0201\",\"functionName\":\"F0201\",\"functionType\":\"02\",\"id\":\"2705008\",\"paramType\":\"01\",\"rankId\":\"676267\",\"tenantCode\":\"100016\"},{\"functionCode\":\"0301\",\"functionName\":\"F0301\",\"functionType\":\"03\",\"id\":\"2705009\",\"paramType\":\"01\",\"rankId\":\"676267\",\"tenantCode\":\"100016\"},{\"functionCode\":\"0411\",\"functionName\":\"F0411\",\"functionType\":\"04\",\"id\":\"2705010\",\"paramType\":\"01\",\"rankId\":\"676267\",\"tenantCode\":\"100016\"}],\"promoFuncRanks\":[{\"activityCode\":\"10102198400631198631\",\"id\":\"676267\",\"rankParam\":1,\"templateCode\":\"0101020103010411\",\"tenantCode\":\"100016\"}],\"promoProductCombines\":[],\"promoProductDetails\":{\"SP231207213937\":[{\"activityCode\":\"10102198400631198631\",\"productCode\":\"SP231207213937\",\"promoPrice\":100.000,\"seqNum\":1,\"skuCode\":\"SK23120700000226\",\"type\":1}]},\"promoProducts\":[],\"promoTemplate\":{\"tagCode\":\"01\",\"templateCode\":\"0101020103010411\",\"templateDesc\":\"单品无条件设为每件不同特价\",\"templateName\":\"单品无条件设为每件不同特价\"},\"promoTemplateFunctions\":[{\"functionCode\":\"0101\",\"functionDesc\":\"单品\",\"functionName\":\"F0101\",\"functionType\":\"01\"},{\"functionCode\":\"0201\",\"functionDesc\":\"无\",\"functionName\":\"F0201\",\"functionType\":\"02\"},{\"functionCode\":\"0301\",\"functionDesc\":\"None\",\"functionName\":\"F0301\",\"functionType\":\"03\"}," +
                "{\"functionCode\":\"0411\",\"functionDesc\":\"每件不同特价\",\"functionName\":\"F0411\",\"functionType\":\"04\"}],\"qualificationModels\":[],\"version\":\"20200310\"}}";


        Map<String,ActivityCacheDTO> map1 = JSONObject.parseObject(map, Map.class);

        Map<String, ActivityCacheDTO> activityCacheMap = new HashMap<>();

        activityCacheMap.putAll(map1);

        String singletemplateCodes = "0101020103010401,0101020103010402,0101020103010403,0101020103010411";

        when(templateDomain.findTemplateTagByTagId(any())).thenReturn(singletemplateCodes);


        String prodcutDetails= "[{\"activityCode\":\"10102198384953190176\",\"orgCode\":\"10244962\",\"productCode\":\"SP231207213937\",\"seqNum\":1,\"skuCode\":\"SK23120700000226\",\"type\":1}," +
                "{\"activityCode\":\"10102198400631198631\",\"productCode\":\"SP231207213937\",\"promoPrice\":100.000,\"seqNum\":1,\"skuCode\":\"SK23120700000226\",\"type\":1}," +
                "{\"activityCode\":\"10102198404747544114\",\"orgCode\":\"10244962\",\"productCode\":\"SP231207213937\",\"seqNum\":1,\"skuCode\":\"SK23120700000226\",\"type\":1}," +
                "{\"activityCode\":\"10102198613986791906\",\"productCode\":\"SP231207213937\",\"promoPrice\":200.000,\"seqNum\":1,\"type\":1}]";

        List<ProductSkuDetailDTO> productSkuDetailDTOList = JSONArray.parseArray(prodcutDetails, ProductSkuDetailDTO.class);

        when(activityProductDetailService.queryListByActivityCodesAndProductCode(any(),any())).thenReturn(productSkuDetailDTOList);

        when(activityCacheDomain.filterActivityByProduct(any(),any(),any())).thenReturn(new HashMap<>());

        SkuActivityPriceDTO skuActivityPriceDTO = activityComponentDomain.activitySkuPrice(product, new HashMap<>());

        Assert.assertNull(skuActivityPriceDTO.getSkuCode());

    }
}
