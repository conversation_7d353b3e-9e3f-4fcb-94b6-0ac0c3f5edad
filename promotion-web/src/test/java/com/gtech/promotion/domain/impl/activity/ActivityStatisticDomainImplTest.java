/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.domain.impl.activity;

import static org.junit.Assert.assertNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.data.mongodb.core.MongoTemplate;

import com.alibaba.fastjson.JSONObject;
import com.gtech.commons.utils.DateUtil;
import com.gtech.promotion.code.activity.ActivityTypeEnum;
import com.gtech.promotion.component.activity.ActivityStatisticDomain;
import com.gtech.promotion.dao.entity.activity.TPromoActivityIncentiveEntity;
import com.gtech.promotion.dao.entity.activity.TPromoActivityStatisticEntity;
import com.gtech.promotion.dao.entity.activity.TPromoOrderEntity;
import com.gtech.promotion.dao.model.activity.ActivityModel;
import com.gtech.promotion.dao.mongo.activity.OrderDetailEntity;
import com.gtech.promotion.dto.in.activity.QueryActivityStatisticInDTO;
import com.gtech.promotion.dto.in.activity.QueryActivityStatisticSumInDTO;
import com.gtech.promotion.dto.in.activity.StartAndEndTimeInDTO;
import com.gtech.promotion.dto.out.activity.ActivityDataAnalyzeOutDto;
import com.gtech.promotion.dto.out.activity.ActivityStatisticQueryOutDTO;
import com.gtech.promotion.dto.out.activity.ActivityStatisticSumQueryOutDTO;
import com.gtech.promotion.dto.out.activity.ActivityTenantOutDTO;
import com.gtech.promotion.service.activity.TPromoActivityIncentiveService;
import com.gtech.promotion.service.activity.ActivityService;
import com.gtech.promotion.service.activity.TPromoActivityStatisticService;
import com.gtech.promotion.service.activity.TPromoOrderService;
import com.gtech.promotion.service.coupon.PromoCouponCodeUserService;

@RunWith(MockitoJUnitRunner.class)
public class ActivityStatisticDomainImplTest {

    @InjectMocks
    private ActivityStatisticDomain statisticDomain;

    @Mock
    private TPromoActivityStatisticService statisticService;
    
    @Mock
    private TPromoOrderService orderService;

    @Mock
    private TPromoActivityIncentiveService incentiveService;

    @Mock
    private ActivityService activityService;

    @Mock
    private MongoTemplate mongoTemplate;


    @Mock
    private PromoCouponCodeUserService couponCodeUserService;

    @Test
    public void 查询活动统计数据_今天之后的数据为空_1() {
        //given
        QueryActivityStatisticInDTO paramDTO = new QueryActivityStatisticInDTO();
        paramDTO.setStartTime(DateUtil.format(DateUtil.addDay(new Date(), -1), DateUtil.FORMAT_YYYYMMDD));
        paramDTO.setEndTime(DateUtil.format(DateUtil.addDay(new Date(), 1), DateUtil.FORMAT_YYYYMMDD));

        List<TPromoActivityStatisticEntity> list = new ArrayList<>();
        TPromoActivityStatisticEntity activityStatisticEntity = new TPromoActivityStatisticEntity();
        activityStatisticEntity.setStatisticDate(DateUtil.format(new Date(), DateUtil.FORMAT_YYYYMMDD));
        list.add(activityStatisticEntity);

        ActivityModel activity = new ActivityModel();
        List<TPromoOrderEntity> orderEntities = new ArrayList<>();
        //when
        when(statisticService.queryActivityStatistic(any())).thenReturn(list);
        when(activityService.findActivityByActivityCode(any(),any())).thenReturn(activity);
        when(orderService.queryPromoOrderToday(any())).thenReturn(orderEntities);
        //then
        List<ActivityStatisticQueryOutDTO> result = statisticDomain.queryActivityStatistic(paramDTO);
        Assert.assertEquals(3, result.size());
        Assert.assertEquals(paramDTO.getStartTime(), result.get(0).getStatisticDate());
    }

    @Test
    public void 查询活动统计数据_今天之后的数据为空_2() {
        //given
        QueryActivityStatisticInDTO paramDTO = new QueryActivityStatisticInDTO();
        paramDTO.setStartTime(DateUtil.format(DateUtil.addDay(new Date(), -1), DateUtil.FORMAT_YYYYMMDD));
        paramDTO.setEndTime(DateUtil.format(DateUtil.addDay(new Date(), 1), DateUtil.FORMAT_YYYYMMDD));

        List<TPromoActivityStatisticEntity> list = new ArrayList<>();
        TPromoActivityStatisticEntity activityStatisticEntity = new TPromoActivityStatisticEntity();
        activityStatisticEntity.setStatisticDate(DateUtil.format(new Date(), DateUtil.FORMAT_YYYYMMDD));
        list.add(activityStatisticEntity);

        ActivityModel activity = new ActivityModel();
        List<TPromoOrderEntity> orderEntities = new ArrayList<>();
        orderEntities.add(new TPromoOrderEntity());

        List<TPromoActivityIncentiveEntity> incentiveList = new ArrayList<>();
        //when
        when(statisticService.queryActivityStatistic(any())).thenReturn(list);
        when(activityService.findActivityByActivityCode(any(),any())).thenReturn(activity);
        when(orderService.queryPromoOrderToday(any())).thenReturn(orderEntities);
        when(incentiveService.getListByOrderIds(any())).thenReturn(incentiveList);
        //then
        List<ActivityStatisticQueryOutDTO> result = statisticDomain.queryActivityStatistic(paramDTO);
        Assert.assertEquals(3, result.size());
        Assert.assertEquals(paramDTO.getStartTime(), result.get(0).getStatisticDate());
    }

    @Test
    public void 查询活动统计数据() {
        //given
        QueryActivityStatisticInDTO paramDTO = new QueryActivityStatisticInDTO();
        paramDTO.setStartTime(DateUtil.format(DateUtil.addDay(new Date(), -1), DateUtil.FORMAT_YYYYMMDD));
        paramDTO.setEndTime(DateUtil.format(DateUtil.addDay(new Date(), 1), DateUtil.FORMAT_YYYYMMDD));

        List<TPromoActivityStatisticEntity> list = new ArrayList<>();
        TPromoActivityStatisticEntity activityStatisticEntity = new TPromoActivityStatisticEntity();
        activityStatisticEntity.setStatisticDate(DateUtil.format(new Date(), DateUtil.FORMAT_YYYYMMDD));
        list.add(activityStatisticEntity);

        String activityCode = "1";
        ActivityModel activity = new ActivityModel();
        activity.setActivityCode("1");
        activity.setActivityType(ActivityTypeEnum.COUPON.code());

        List<TPromoOrderEntity> orderEntities = new ArrayList<>();
        orderEntities.add(new TPromoOrderEntity());

        List<TPromoActivityIncentiveEntity> incentiveList = new ArrayList<>();
        TPromoActivityIncentiveEntity incentiveEntity = new TPromoActivityIncentiveEntity();
        incentiveEntity.setActivityCode(activityCode);
        incentiveEntity.setIncentiveAmount(new BigDecimal(123));
        incentiveEntity.setPromoOrderId("21312");
        incentiveList.add(incentiveEntity);

        List<Object> entityList = new ArrayList<>();
        OrderDetailEntity detailVO = new OrderDetailEntity();
        detailVO.setProductAmount(new BigDecimal(12313));
        entityList.add(detailVO);
        //when
        when(statisticService.queryActivityStatistic(any())).thenReturn(list);
        when(activityService.findActivityByActivityCode(any(),any())).thenReturn(activity);
        when(orderService.queryPromoOrderToday(any())).thenReturn(orderEntities);
        when(incentiveService.getListByOrderIds(any())).thenReturn(incentiveList);
        when(mongoTemplate.find(any(), any())).thenReturn(entityList);
        when(couponCodeUserService.getAllocateCouponCountToday111(any(), any())).thenReturn(1);
        when(couponCodeUserService.getUseCouponCountToday111(any(), any())).thenReturn(1);
        //then
        List<ActivityStatisticQueryOutDTO> result = statisticDomain.queryActivityStatistic(paramDTO);
        Assert.assertEquals(3, result.size());
        Assert.assertEquals(paramDTO.getStartTime(), result.get(0).getStatisticDate());
    }

    @Test
    public void 查询活动统计数据总数_历史统计数据为空_今日数据不为空() {
        //given
        ActivityStatisticSumQueryOutDTO outDTO = new ActivityStatisticSumQueryOutDTO();
        QueryActivityStatisticSumInDTO paramDTO = new QueryActivityStatisticSumInDTO();
        List<TPromoOrderEntity> orderEntities = new ArrayList<>();
        TPromoOrderEntity entity = new TPromoOrderEntity();
        orderEntities.add(entity);
        ActivityModel activity = new ActivityModel();
        activity.setActivityCode("111");
        activity.setActivityType(ActivityTypeEnum.COUPON.code());
        List<TPromoActivityIncentiveEntity> list = new ArrayList<>();
        TPromoActivityIncentiveEntity incentiveEntity = new TPromoActivityIncentiveEntity();
        incentiveEntity.setIncentiveAmount(new BigDecimal(12));
        incentiveEntity.setPromoOrderId("12");
        incentiveEntity.setActivityCode("111");
        incentiveEntity.setUserCode("1111");
        incentiveEntity.setPromoOrderId("9090");
        list.add(incentiveEntity);
        List<Object> entityList = new ArrayList<>();
        OrderDetailEntity detailEntity = new OrderDetailEntity();
        detailEntity.setProductAmount(new BigDecimal(20));

        //when
        when(statisticService.queryActivityStatisticSum(paramDTO)).thenReturn(null);
        when(activityService.findActivityByActivityCode(any(),any())).thenReturn(activity);
        when(orderService.queryPromoOrderToday(any())).thenReturn(orderEntities);
        when(incentiveService.getListByOrderIds(any())).thenReturn(list);
        when(incentiveService.getListByActivityCode(any())).thenReturn(list);
        when(mongoTemplate.find(any(), any())).thenReturn(entityList);
        when(couponCodeUserService.getAllocateCouponCountToday111(any(), any())).thenReturn(1);
        when(couponCodeUserService.getUseCouponCountToday111(any(), any())).thenReturn(1);
        //then
        outDTO = statisticDomain.queryActivityStatisticSum(paramDTO);

        System.out.println(JSONObject.toJSONString(outDTO));

        Assert.assertTrue(outDTO.getMemberCount() == null);
        Assert.assertTrue(outDTO.getOrderAmount().equals(new BigDecimal(-12)));
        Assert.assertTrue(outDTO.getOrderCount() == 1);
        Assert.assertTrue(outDTO.getReduceAmount().equals(new BigDecimal(12)));

    }

    @Test
    public void 查询活动统计数据总数_历史统计数据不为空_今日数据不为空() {
        //given
        QueryActivityStatisticSumInDTO paramDTO = new QueryActivityStatisticSumInDTO();
        ActivityStatisticSumQueryOutDTO sumQueryOutDTO = new ActivityStatisticSumQueryOutDTO();
        sumQueryOutDTO.setMemberCount(1);
        sumQueryOutDTO.setReduceAmount(new BigDecimal(10));
        sumQueryOutDTO.setOrderAmount(new BigDecimal(20));
        sumQueryOutDTO.setOrderCount(1);
        List<TPromoOrderEntity> orderEntities = new ArrayList<>();
        TPromoOrderEntity entity = new TPromoOrderEntity();
        orderEntities.add(entity);
        ActivityModel activity = new ActivityModel();
        activity.setActivityCode("9");
        activity.setActivityType(ActivityTypeEnum.COUPON.code());
        List<TPromoActivityIncentiveEntity> list = new ArrayList<>();
        TPromoActivityIncentiveEntity incentiveEntity = new TPromoActivityIncentiveEntity();
        incentiveEntity.setIncentiveAmount(new BigDecimal(12));
        incentiveEntity.setPromoOrderId("12");
        incentiveEntity.setActivityCode("9");
        list.add(incentiveEntity);
        List<Object> entityList = new ArrayList<>();
        OrderDetailEntity detailEntity = new OrderDetailEntity();
        detailEntity.setProductAmount(new BigDecimal(20));

        //when
        when(statisticService.queryActivityStatisticSum(paramDTO)).thenReturn(sumQueryOutDTO);
        when(activityService.findActivityByActivityCode(any(),any())).thenReturn(activity);
        when(orderService.queryPromoOrderToday(any())).thenReturn(orderEntities);
        when(incentiveService.getListByOrderIds(any())).thenReturn(list);
        when(incentiveService.getListByActivityCode(any())).thenReturn(list);
        when(mongoTemplate.find(any(), any())).thenReturn(entityList);
        when(couponCodeUserService.getAllocateCouponCountToday111(any(), any())).thenReturn(1);
        when(couponCodeUserService.getUseCouponCountToday111(any(), any())).thenReturn(1);
        //then
        sumQueryOutDTO = statisticDomain.queryActivityStatisticSum(paramDTO);

        Assert.assertTrue(sumQueryOutDTO.getMemberCount() == 1);
        Assert.assertTrue(sumQueryOutDTO.getOrderAmount().equals(new BigDecimal(8)));
        Assert.assertTrue(sumQueryOutDTO.getOrderCount() == 2);
        Assert.assertTrue(sumQueryOutDTO.getReduceAmount().equals(new BigDecimal(22)));

    }

    @Test
    public void 查询活动统计数据总数_此活动下今日没奖励_奖励表为空() {
       //given
        QueryActivityStatisticSumInDTO paramDTO = new QueryActivityStatisticSumInDTO();
        ActivityStatisticSumQueryOutDTO sumQueryOutDTO = new ActivityStatisticSumQueryOutDTO();
        List<TPromoOrderEntity> orderEntities = new ArrayList<>();
        TPromoOrderEntity entity = new TPromoOrderEntity();
        orderEntities.add(entity);
        ActivityModel activity = new ActivityModel();
        List<TPromoActivityIncentiveEntity> list = new ArrayList<>();
        //when
        when(statisticService.queryActivityStatisticSum(paramDTO)).thenReturn(sumQueryOutDTO);
        when(activityService.findActivityByActivityCode(any(),any())).thenReturn(activity);
        when(orderService.queryPromoOrderToday(any())).thenReturn(orderEntities);
        when(incentiveService.getListByOrderIds(any())).thenReturn(list);
        when(incentiveService.getListByActivityCode(any())).thenReturn(list);
        //then
        sumQueryOutDTO = statisticDomain.queryActivityStatisticSum(paramDTO);

        assertNull(sumQueryOutDTO);

    }

    public void 查询活动统计数据总数_今日订单表为空_奖励表为空() {
        //given
        QueryActivityStatisticSumInDTO paramDTO = new QueryActivityStatisticSumInDTO();
        ActivityStatisticSumQueryOutDTO sumQueryOutDTO = new ActivityStatisticSumQueryOutDTO();
        List<TPromoOrderEntity> orderEntities = new ArrayList<>();
        ActivityModel activity = new ActivityModel();
        List<TPromoActivityIncentiveEntity> list = new ArrayList<>();
        //when
        when(statisticService.queryActivityStatisticSum(paramDTO)).thenReturn(sumQueryOutDTO);
        when(activityService.findActivityByActivityCode(any(),any())).thenReturn(activity);
        when(orderService.queryPromoOrderToday(any())).thenReturn(orderEntities);
        when(incentiveService.getListByActivityCode(any())).thenReturn(list);
        //then
        sumQueryOutDTO = statisticDomain.queryActivityStatisticSum(paramDTO);

        assertNull(sumQueryOutDTO);

    }
    
    @Test
    public void 促销数据汇总() {
        //given
        List<String> list = new ArrayList<>();
        list.add("88002221");
        StartAndEndTimeInDTO startAndEndTime = new StartAndEndTimeInDTO();
        //when
//        when(activityService.accumulativeTotal(list)).thenReturn(10000000L);
//        when(couponCodeUserService.usedCouponAmount(list)).thenReturn(10000000L);
        //then
        ActivityDataAnalyzeOutDto analyze = statisticDomain.dataOfAnalyze(startAndEndTime);
        Assert.assertNotNull(analyze);
    }
    
    @Test
    public void 促销活动券_订单_优惠金额_数据统计() {
        when(couponCodeUserService.getReceivedCouponAmount(any())).thenReturn(100);
        when(couponCodeUserService.getReceivedCouponAmount(any())).thenReturn(100);
        when(couponCodeUserService.getUsedCouponAmount(any())).thenReturn(100);
        when(orderService.getPayOrderAmount(any())).thenReturn(100);
        when(incentiveService.getPayOrderDiscountMoneyTotal(any())).thenReturn(100.0);
        ActivityTenantOutDTO data = statisticDomain.queryActivityTenantData(any());
        Assert.assertNotNull(data);
    }
    
    @Test
    public void 促销活动进行中数据统计() {
        when(activityService.queryEffectActivityByTenantCode(any())).thenReturn(100L);
        Long total = statisticDomain.queryActivityActiveTotal(any());
        Assert.assertEquals(new Long(100), total);
    }
}
