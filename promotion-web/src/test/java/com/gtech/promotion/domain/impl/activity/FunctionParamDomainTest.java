/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.domain.impl.activity;

import static org.junit.Assert.assertEquals;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import com.gtech.promotion.component.activity.FunctionParamDomain;
import com.gtech.promotion.dao.model.activity.FunctionParamModel;
import com.gtech.promotion.dao.model.activity.ActivityFunctionParamRankModel;
import com.gtech.promotion.dto.in.coupon.ConditionAndFace;
import com.gtech.promotion.service.impl.activity.ActivityFuncParamServiceImpl;
import com.gtech.promotion.service.impl.activity.ActivityFuncRankServiceImpl;

/**
 * <功能描述>
 */
@RunWith(MockitoJUnitRunner.class)
public class FunctionParamDomainTest {

    @InjectMocks
    private FunctionParamDomain functionParamDomain;

    @Mock
    private ActivityFuncRankServiceImpl promoActivityFuncRankService;

    @Mock
    private ActivityFuncParamServiceImpl promoActivityFuncParamService;

    @Test
    public void 获取条件值和面值_满1件减10元(){
        // given
        List<ActivityFunctionParamRankModel> rankList = new ArrayList<>();
        ActivityFunctionParamRankModel vo = new ActivityFunctionParamRankModel();
        vo.setId("1");
        rankList.add(vo);

        List<FunctionParamModel> params = new ArrayList<>();
        FunctionParamModel paramVO1 = new FunctionParamModel();
        paramVO1.setRankId("1");
        paramVO1.setFunctionType("01");
        paramVO1.setFunctionCode("0102");
        paramVO1.setFunctionName("商品范围");
        paramVO1.setParamType("01");
        params.add(paramVO1);
        FunctionParamModel paramVO2 = new FunctionParamModel();
        paramVO2.setRankId("1");
        paramVO2.setFunctionType("02");
        paramVO2.setFunctionCode("0202");
        paramVO2.setFunctionName("满");
        paramVO2.setParamType("01");
        params.add(paramVO2);
        FunctionParamModel paramVO3 = new FunctionParamModel();
        paramVO3.setRankId("1");
        paramVO3.setFunctionType("03");
        paramVO3.setFunctionCode("0302");
        paramVO3.setFunctionName("数量");
        paramVO3.setParamType("02");
        paramVO3.setParamValue("1");
        paramVO3.setParamUnit("02");
        params.add(paramVO3);
        FunctionParamModel paramVO4 = new FunctionParamModel();
        paramVO4.setRankId("1");
        paramVO4.setFunctionType("04");
        paramVO4.setFunctionCode("0401");
        paramVO4.setFunctionName("减金额");
        paramVO4.setParamType("02");
        paramVO4.setParamValue("10");
        paramVO4.setParamUnit("01");
        params.add(paramVO4);

        // when
        Mockito.when(promoActivityFuncRankService.getRankListByActivityCode("1")).thenReturn(rankList);
        Mockito.when(promoActivityFuncParamService.getRuleFuncParamListByRankId("1")).thenReturn(params);

        final ConditionAndFace conditionAndFace = functionParamDomain.findConditionAndFaceByActivityCode("1");
        // then
        assertEquals("02", conditionAndFace.getConditionUnit());
        assertEquals(new BigDecimal(1), conditionAndFace.getConditionValue());
        assertEquals("01", conditionAndFace.getFaceUnit());
        assertEquals(new BigDecimal(10), conditionAndFace.getFaceValue());
    }

    @Test
    public void 获取条件值和面值_满10元打8折(){
        // given
        List<ActivityFunctionParamRankModel> rankList = new ArrayList<>();
        ActivityFunctionParamRankModel vo = new ActivityFunctionParamRankModel();
        vo.setId("1");
        rankList.add(vo);

        List<FunctionParamModel> params = new ArrayList<>();
        FunctionParamModel paramVO1 = new FunctionParamModel();
        paramVO1.setRankId("1");
        paramVO1.setFunctionType("01");
        paramVO1.setFunctionCode("0102");
        paramVO1.setFunctionName("商品范围");
        paramVO1.setParamType("01");
        params.add(paramVO1);
        FunctionParamModel paramVO2 = new FunctionParamModel();
        paramVO2.setRankId("1");
        paramVO2.setFunctionType("02");
        paramVO2.setFunctionCode("0202");
        paramVO2.setFunctionName("满");
        paramVO2.setParamType("01");
        params.add(paramVO2);
        FunctionParamModel paramVO3 = new FunctionParamModel();
        paramVO3.setRankId("1");
        paramVO3.setFunctionType("03");
        paramVO3.setFunctionCode("0303");
        paramVO3.setFunctionName("金额");
        paramVO3.setParamType("02");
        paramVO3.setParamValue("10");
        paramVO3.setParamUnit("01");
        params.add(paramVO3);
        FunctionParamModel paramVO4 = new FunctionParamModel();
        paramVO4.setRankId("1");
        paramVO4.setFunctionType("04");
        paramVO4.setFunctionCode("0402");
        paramVO4.setFunctionName("打折扣");
        paramVO4.setParamType("02");
        paramVO4.setParamValue("0.80");
        paramVO4.setParamUnit("03");
        params.add(paramVO4);

        // when
        Mockito.when(promoActivityFuncRankService.getRankListByActivityCode("1")).thenReturn(rankList);
        Mockito.when(promoActivityFuncParamService.getRuleFuncParamListByRankId("1")).thenReturn(params);

        final ConditionAndFace conditionAndFace = functionParamDomain.findConditionAndFaceByActivityCode("1");
        // then
        assertEquals("01", conditionAndFace.getConditionUnit());
        assertEquals(new BigDecimal(10), conditionAndFace.getConditionValue());
        assertEquals("02", conditionAndFace.getFaceUnit());
        assertEquals(0, new BigDecimal(8).compareTo(conditionAndFace.getFaceValue()));
    }
}
