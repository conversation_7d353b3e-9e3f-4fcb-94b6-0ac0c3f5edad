/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.domain.impl.activity;

import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

import java.util.HashSet;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.springframework.data.redis.core.HashOperations;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.ValueOperations;

import com.gtech.commons.result.Result;
import com.gtech.promotion.component.activity.ProductDomain;
import com.gtech.promotion.helper.factory.ProductFactory;
import com.gtech.promotion.service.impl.activity.ActivityProductDetailServiceImpl;

/**
 * 商品服务类测试
 */
//@RunWith(MockitoJUnitRunner.class)
public class ProductDomainTest{

    @InjectMocks
    private ProductDomain ProductDomain;

    @Mock
    private StringRedisTemplate redisTemplate;

    @Mock
    private ActivityProductDetailServiceImpl productDetailService;

    @Before
    public void setup(){
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void createProductSku没有skuToken(){
        Result<Object> createProductSku = ProductDomain.createProductSku("", "", "");
        Assert.assertTrue(createProductSku.isSuccess());
    }

    @Test
    public void createProductSku有skuToken异常捕捉(){

        Result<Object> createProductSku = ProductDomain.createProductSku("", "", "1");
        Assert.assertTrue(createProductSku.isSuccess());
    }

    @Test
    @SuppressWarnings("unchecked")
    public void createProductSku有skuToken查不到数据(){
        HashOperations<String, String, String> hashOperations = Mockito.mock(HashOperations.class);
        ValueOperations<String, String> valueOperations = Mockito.mock(ValueOperations.class);

        when(redisTemplate.<String, String> opsForHash()).thenReturn(hashOperations);
        when(hashOperations.keys(anyString())).thenReturn(new HashSet<>());
        when(redisTemplate.opsForValue()).thenReturn(valueOperations);
        when(valueOperations.get(anyString())).thenReturn("");
        when(hashOperations.get(anyString(), anyString())).thenReturn("");

        Result<Object> createProductSku = ProductDomain.createProductSku("", "", "1");
        Assert.assertTrue(createProductSku.isSuccess());
    }

    @Test
    @SuppressWarnings("unchecked")
    public void createProductSku有skuToken(){
        String jsonString = ProductFactory.initProductDetail();

        HashOperations<String, String, String> hashOperations = Mockito.mock(HashOperations.class);
        ValueOperations<String, String> valueOperations = Mockito.mock(ValueOperations.class);

        when(redisTemplate.<String, String> opsForHash()).thenReturn(hashOperations);
        when(hashOperations.keys(anyString())).thenReturn(new HashSet<>());
        when(redisTemplate.opsForValue()).thenReturn(valueOperations);
        when(valueOperations.get(anyString())).thenReturn("1");
        when(hashOperations.get(anyString(), anyString())).thenReturn(jsonString);

        Result<Object> createProductSku = ProductDomain.createProductSku("", "", "1");
        Assert.assertTrue(createProductSku.isSuccess());
    }

}
