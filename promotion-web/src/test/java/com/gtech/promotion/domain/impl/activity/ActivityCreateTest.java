/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.domain.impl.activity;

import com.gtech.basic.masterdata.response.JsonResult;
import com.gtech.promotion.calc.TemplateEnum;
import com.gtech.promotion.code.activity.*;
import com.gtech.promotion.component.activity.ActivityCacheDomain;
import com.gtech.promotion.component.activity.ActivityComponentDomain;
import com.gtech.promotion.component.activity.ProductDomain;
import com.gtech.promotion.component.activity.PromoGroupDomain;
import com.gtech.promotion.dao.model.activity.ActivityModel;
import com.gtech.promotion.domain.activity.ActivityDomain;
import com.gtech.promotion.dto.out.product.ProductDetailInDTO;
import com.gtech.promotion.exception.PromotionException;
import com.gtech.promotion.feign.MasterDataFeignClient;
import com.gtech.promotion.helper.bean.Template;
import com.gtech.promotion.helper.factory.ActivityFactory;
import com.gtech.promotion.helper.factory.CacheActivityFactory;
import com.gtech.promotion.service.activity.*;
import com.gtech.promotion.service.impl.activity.*;
import com.gtech.promotion.service.impl.mongo.activity.TPromoProductServiceImpl;
import com.gtech.promotion.vo.bean.*;
import org.apache.commons.lang.StringUtils;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.List;

import static org.junit.Assert.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class ActivityCreateTest{

    // 1、有无限制
    // 2、有无渠道
    // 3、有无会员
    // 4、商品：全商品
    // 5、商品：指定商品范围
    // 6、商品：指定多个商品范围
    // 7、正选
    // 8、反选
    // 异常：

    //    单品无条件减金额
    //    单品无条件打折扣
    //    单品无条件设为每件特价
    //    商品范围满数量减金额
    //    商品范围满金额减金额
    //    商品范围每满数量减金额 --
    //    商品范围每满金额减金额 --
    //    订单满数量减金额
    //    订单满金额减金额
    //    商品范围满数量打折扣
    //    商品范围满金额打折扣
    //    商品范围第数量打折扣
    //    订单满数量打折扣
    //    订单满金额打折扣
    //    商品范围满数量设为总计特价
    //    商品范围每满数量设为总计特价
    //    商品范围满数量送赠品
    //    商品范围满金额送赠品
    //    商品范围每满数量送赠品
    //    商品范围每满金额送赠品
    //    订单满数量送赠品
    //    订单满金额送赠品
    //    订单满数量包邮
    //    订单满金额包邮
    //    多组商品范围各选1件设为总计特价
    //    商品范围每满数量送同类商品
    //    商品范围每满数量送不同类商品

    @InjectMocks
    private ActivityComponentDomain activityComponentDomain;

    @Mock
    private ActivityCacheDomain activityCacheDomain;

    @Mock
    private ActivityServiceImpl activityService;

    @Mock
    private TPromoActivityStoreServiceImpl tPromoStoreService;

    @Mock
    private TPromoProductServiceImpl tPromoProductService;

    @Mock
    private ActivityProductDetailServiceImpl activityProductDetailService;

    @Mock
    private TPromoIncentiveLimitedServiceImpl incentiveLimitedService;

    @Mock
    private ActivityFuncParamServiceImpl activityFuncParamService;

    @Mock
    private ActivityFuncRankService activityFuncRankService;

    @Mock
    private TemplateServiceImpl templateService;

    @Mock
    private ProductDomain productDomain;

    @Mock
    private GiveawayServiceImpl tPromoRuleGiftService;

    @Mock
    private QualificationService qualificationService;

    @Mock
    private ActivityLanguageService languageService;

    @Mock
    private OperationLogService operationLogService;

    @Mock
    private ActivityPeriodService activityPeriodService;

    @Mock
    private MasterDataFeignClient masterDataFeignClient;

    @Mock
    private PromotionGroupService promotionGroupService;

    @Mock
    private PromoGroupDomain promoGroupDomain;
    @Test
    public void group_activity_create(){
        // given
        // 创建活动模板
        Template template = Template.builder().templateEnum(TemplateEnum.T0101).reward("1").build();
        ActivityDomain createDTO = ActivityFactory.createActivityCreateDTO(null, template, null, IncentiveLimitedFlagEnum.NO, StoreParamTypeEnum.STORE_ALL,
                ProductSelectionEnum.SELECT, ProductTypeEnum.ALL);

        when(templateService.getTemplateByCode(anyString())).thenReturn(CacheActivityFactory.createTemplate(template.getTemplateEnum()));
        when(activityService.createPromoActivity(any(ActivityModel.class))).thenReturn("1");
        when(activityFuncParamService.saveTPromoRuleFuncParam(anyString(), anyString(), any())).thenReturn(1);
        when(operationLogService.insertLog(any(), any())).thenReturn(1);

        JsonResult<String> result = new JsonResult<>();
        result.setSuccess(true);
        result.setData("1");
//        when(masterDataFeignClient.getValueValue(anyString(), anyString())).thenReturn(result);

//        when(promotionGroupService.getGroupCountGroupType(anyString(), anyString())).thenReturn(1);

        createDTO.setOpsType("01");
        String activityCode = activityComponentDomain.createPromoActivity(createDTO);

        // then
        assertTrue(StringUtils.isNotBlank(activityCode));
    }

    @Test(expected = PromotionException.class)
    public void group_activity_create_1(){
        // given
        // 创建活动模板
        Template template = Template.builder().templateEnum(TemplateEnum.T0101).reward("1").build();
        ActivityDomain createDTO = ActivityFactory.createActivityCreateDTO(null, template, null, IncentiveLimitedFlagEnum.NO, StoreParamTypeEnum.STORE_ALL,
                ProductSelectionEnum.SELECT, ProductTypeEnum.ALL);

        JsonResult<String> result = new JsonResult<>();
        result.setSuccess(true);
        result.setData("1");
//        when(masterDataFeignClient.getValueValue(anyString(), anyString())).thenReturn(result);

//        when(promotionGroupService.getGroupCountGroupType(anyString(), anyString())).thenReturn(0);

        createDTO.setOpsType("01");
        String activityCode = activityComponentDomain.createPromoActivity(createDTO);

        // then
        assertTrue(StringUtils.isNotBlank(activityCode));
    }

    @Test
    public void group_activity_create_exception(){
        // given
        // 创建活动模板
        Template template = Template.builder().templateEnum(TemplateEnum.T0101).reward("1").build();
        ActivityDomain createDTO = ActivityFactory.createActivityCreateDTO(null, template, null, IncentiveLimitedFlagEnum.NO, StoreParamTypeEnum.STORE_ALL,
                ProductSelectionEnum.SELECT, ProductTypeEnum.ALL);

        when(templateService.getTemplateByCode(anyString())).thenReturn(CacheActivityFactory.createTemplate(template.getTemplateEnum()));
        when(activityService.createPromoActivity(any(ActivityModel.class))).thenReturn("1");
        when(activityFuncParamService.saveTPromoRuleFuncParam(anyString(), anyString(), any())).thenReturn(1);
        when(operationLogService.insertLog(any(), any())).thenReturn(1);

        // when
        String activityCode = activityComponentDomain.createPromoActivity(createDTO);

        // then
        assertTrue(StringUtils.isNotBlank(activityCode));
    }



    @Test
    public void T0010单品减金额_条件全无(){
        // given
        // 创建活动模板
        Template template = Template.builder().templateEnum(TemplateEnum.T0101).reward("1").build();
        ActivityDomain createDTO = ActivityFactory.createActivityCreateDTO(null, template, null, IncentiveLimitedFlagEnum.NO, StoreParamTypeEnum.STORE_ALL,
            ProductSelectionEnum.SELECT, ProductTypeEnum.ALL);

        when(templateService.getTemplateByCode(anyString())).thenReturn(CacheActivityFactory.createTemplate(template.getTemplateEnum()));
        when(activityService.createPromoActivity(any(ActivityModel.class))).thenReturn("1");
        when(activityFuncParamService.saveTPromoRuleFuncParam(anyString(), anyString(), any())).thenReturn(1);
        when(operationLogService.insertLog(any(), any())).thenReturn(1);

        // when
        String activityCode = activityComponentDomain.createPromoActivity(createDTO);

        // then
        assertTrue(StringUtils.isNotBlank(activityCode));
    }

    @Test
    public void T0010单品减金额_限制周期(){
        // given
        // 创建活动模板
        Template template = Template.builder().templateEnum(TemplateEnum.T0101).reward("1").build();
        ActivityDomain createDTO = ActivityFactory.createActivityCreateDTO(null, template, null, IncentiveLimitedFlagEnum.NO, StoreParamTypeEnum.STORE_ALL,
            ProductSelectionEnum.SELECT, ProductTypeEnum.ALL);
        createDTO.setActivityPeriod(new ActivityPeriod("0 0 12 * * ? *", "0 0 13 * * ? *", null));
        when(templateService.getTemplateByCode(anyString())).thenReturn(CacheActivityFactory.createTemplate(template.getTemplateEnum()));
        when(activityService.createPromoActivity(any(ActivityModel.class))).thenReturn("1");
        when(activityFuncParamService.saveTPromoRuleFuncParam(anyString(), anyString(), any())).thenReturn(1);
        when(operationLogService.insertLog(any(), any())).thenReturn(1);

        // when
        String activityCode = activityComponentDomain.createPromoActivity(createDTO);

        // then
        assertTrue(StringUtils.isNotBlank(activityCode));
    }

    @Test
    public void T0020单品打折扣_仅有限制(){
        // given
        // 创建活动模板
        Template template = Template.builder().templateEnum(TemplateEnum.T0102).reward("0.2").build();
        // 创建活动限制
        List<IncentiveLimited> limiteds = ActivityFactory.createLimiteds(LimitationCodeEnum.ACTIVITY_TOTAL_COUNT.code(), 2);
        ActivityDomain createDTO = ActivityDomain.builder().incentiveLimiteds(limiteds).build();
        ActivityDomain activityCreateDTO = ActivityFactory.createActivityCreateDTO(createDTO, template, null, IncentiveLimitedFlagEnum.YES,
            StoreParamTypeEnum.STORE_ALL, ProductSelectionEnum.SELECT, ProductTypeEnum.ALL);
        when(templateService.getTemplateByCode(anyString())).thenReturn(CacheActivityFactory.createTemplate(template.getTemplateEnum()));
        when(activityService.createPromoActivity(any(ActivityModel.class))).thenReturn("1");
        when(activityFuncParamService.saveTPromoRuleFuncParam(anyString(), anyString(), any())).thenReturn(1);
        when(incentiveLimitedService.insertLimitedList111(anyString(), any(), anyString())).thenReturn(1);
        when(operationLogService.insertLog(any(), any())).thenReturn(1);
        // when
        String activityCode = activityComponentDomain.createPromoActivity(activityCreateDTO);

        // then
        assertTrue(StringUtils.isNotBlank(activityCode));
    }

    @Test
    public void T0030单品特价_仅有限制和渠道(){
        // given
        // 创建活动模板
        Template template = Template.builder().templateEnum(TemplateEnum.T0103).reward("2").build();
        // 创建活动限制
        List<IncentiveLimited> limiteds = ActivityFactory.createLimiteds(LimitationCodeEnum.ACTIVITY_TOTAL_COUNT.code(), 3);
        List<ActivityStore> channelStores = ActivityFactory.createChannelStores(2);
        ActivityDomain createDTO = ActivityDomain.builder().incentiveLimiteds(limiteds).channelStores(channelStores).build();
        ActivityDomain activityCreateDTO = ActivityFactory.createActivityCreateDTO(createDTO, template, null, IncentiveLimitedFlagEnum.YES,
            StoreParamTypeEnum.STORE_CUSTOM, ProductSelectionEnum.SELECT, ProductTypeEnum.ALL);
        when(templateService.getTemplateByCode(anyString())).thenReturn(CacheActivityFactory.createTemplate(template.getTemplateEnum()));
        when(activityService.createPromoActivity(any(ActivityModel.class))).thenReturn("1");
        when(activityFuncParamService.saveTPromoRuleFuncParam(anyString(), anyString(), any())).thenReturn(1);
        when(incentiveLimitedService.insertLimitedList111(anyString(), any(), anyString())).thenReturn(1);
        when(operationLogService.insertLog(any(), any())).thenReturn(1);
        // when
        String activityCode = activityComponentDomain.createPromoActivity(activityCreateDTO);

        // then
        assertTrue(StringUtils.isNotBlank(activityCode));
    }

    @Test
    public void T0040满数量减金额_有限制和渠道和会员(){

        // 创建活动模板
        Template template = Template.builder().templateEnum(TemplateEnum.T0201).condition("3").reward("2").build();
        // 创建活动限制
        List<IncentiveLimited> limiteds = ActivityFactory.createLimiteds(LimitationCodeEnum.ACTIVITY_TOTAL_COUNT.code(), 3);
        List<ActivityStore> channelStores = ActivityFactory.createChannelStores(2);
        List<Qualification> qualifications = ActivityFactory.createQualifications(2);
        ActivityDomain createDTO = ActivityDomain.builder().incentiveLimiteds(limiteds).channelStores(channelStores).build();
        ActivityDomain activityCreateDTO = ActivityFactory.createActivityCreateDTO(createDTO, template, qualifications, IncentiveLimitedFlagEnum.YES,
            StoreParamTypeEnum.STORE_CUSTOM, ProductSelectionEnum.SELECT, ProductTypeEnum.ALL);
        when(templateService.getTemplateByCode(anyString())).thenReturn(CacheActivityFactory.createTemplate(template.getTemplateEnum()));
        when(activityService.createPromoActivity(any(ActivityModel.class))).thenReturn("1");
        when(qualificationService.createQualifications(any())).thenReturn(1);
        when(activityFuncParamService.saveTPromoRuleFuncParam(anyString(), anyString(), any())).thenReturn(1);
        when(incentiveLimitedService.insertLimitedList111(anyString(), any(), anyString())).thenReturn(1);
        when(operationLogService.insertLog(any(), any())).thenReturn(1);
        // when
        String activityCode = activityComponentDomain.createPromoActivity(activityCreateDTO);

        // then
        assertTrue(StringUtils.isNotBlank(activityCode));
    }

    @Test
    public void T0050满金额减金额_有限制和渠道和会员指定商品(){

        // 创建活动模板
        Template template = Template.builder().templateEnum(TemplateEnum.T0202).condition("3").reward("2").build();
        // 创建活动限制
        List<IncentiveLimited> limiteds = ActivityFactory.createLimiteds(LimitationCodeEnum.ACTIVITY_TOTAL_COUNT.code(), 3);
        List<ActivityStore> channelStores = ActivityFactory.createChannelStores(2);
        List<Qualification> qualifications = ActivityFactory.createQualifications(2);
        List<ProductScope> products = ActivityFactory.createProducts(1);
        ActivityDomain createDTO = ActivityDomain.builder().incentiveLimiteds(limiteds).channelStores(channelStores).products(products).build();
        ActivityDomain activityCreateDTO = ActivityFactory.createActivityCreateDTO(createDTO, template, qualifications, IncentiveLimitedFlagEnum.YES,
            StoreParamTypeEnum.STORE_CUSTOM, ProductSelectionEnum.SELECT, ProductTypeEnum.CUSTOM_PRODUCT);
        when(templateService.getTemplateByCode(anyString())).thenReturn(CacheActivityFactory.createTemplate(template.getTemplateEnum()));
        when(activityService.createPromoActivity(any(ActivityModel.class))).thenReturn("1");
        when(qualificationService.createQualifications(any())).thenReturn(1);
        when(activityFuncParamService.saveTPromoRuleFuncParam(anyString(), anyString(), any())).thenReturn(1);
        when(incentiveLimitedService.insertLimitedList111(anyString(), any(), anyString())).thenReturn(1);
        when(operationLogService.insertLog(any(), any())).thenReturn(1);
        // when
        String activityCode = activityComponentDomain.createPromoActivity(activityCreateDTO);

        // then
        assertTrue(StringUtils.isNotBlank(activityCode));
    }

    @Test
    public void T0060每满数量减金额_有限制和渠道和会员指定商品接口(){

        // 创建活动模板
        Template template = Template.builder().templateEnum(TemplateEnum.T0203).condition("3").reward("2").build();
        // 创建活动限制
        List<IncentiveLimited> limiteds = ActivityFactory.createLimiteds(LimitationCodeEnum.ACTIVITY_TOTAL_COUNT.code(), 3);
        List<ActivityStore> channelStores = ActivityFactory.createChannelStores(2);
        List<Qualification> qualifications = ActivityFactory.createQualifications(2);
        List<ProductScope> products = ActivityFactory.createProducts(1);
        List<ProductDetailInDTO> productDetails = ActivityFactory.createProductDetails(1);
        ActivityDomain createDTO = ActivityDomain.builder().incentiveLimiteds(limiteds).channelStores(channelStores).products(products).productDetails(productDetails).build();
        ActivityDomain activityCreateDTO = ActivityFactory.createActivityCreateDTO(createDTO, template, qualifications, IncentiveLimitedFlagEnum.YES,
            StoreParamTypeEnum.STORE_CUSTOM, ProductSelectionEnum.SELECT, ProductTypeEnum.CUSTOM_PRODUCT);
        when(templateService.getTemplateByCode(anyString())).thenReturn(CacheActivityFactory.createTemplate(template.getTemplateEnum()));
        when(activityService.createPromoActivity(any(ActivityModel.class))).thenReturn("1");
        when(qualificationService.createQualifications(any())).thenReturn(1);
        when(activityFuncParamService.saveTPromoRuleFuncParam(anyString(), anyString(), any())).thenReturn(1);
        when(incentiveLimitedService.insertLimitedList111(anyString(), any(), anyString())).thenReturn(1);
        when(operationLogService.insertLog(any(), any())).thenReturn(1);
        // when
        String activityCode = activityComponentDomain.createPromoActivity(activityCreateDTO);

        // then
        assertTrue(StringUtils.isNotBlank(activityCode));
    }

    @Test
    public void T0070每满金额减金额_有限制和会员指定商品反选(){

        // 创建活动模板
        Template template = Template.builder().templateEnum(TemplateEnum.T0204).condition("3").reward("2").build();
        // 创建活动限制
        List<IncentiveLimited> limiteds = ActivityFactory.createLimiteds(LimitationCodeEnum.USER_TOTAL_COUNT.code(), 3);
        List<ProductScope> products = ActivityFactory.createProducts(1);
        List<Qualification> qualifications = ActivityFactory.createQualifications(2);
        ActivityDomain createDTO = ActivityDomain.builder().incentiveLimiteds(limiteds).products(products).skuToken("1110").build();
        ActivityDomain activityCreateDTO = ActivityFactory.createActivityCreateDTO(createDTO, template, qualifications, IncentiveLimitedFlagEnum.YES,
            StoreParamTypeEnum.STORE_ALL, ProductSelectionEnum.INVERT_SELECT, ProductTypeEnum.CUSTOM_PRODUCT);
        when(templateService.getTemplateByCode(anyString())).thenReturn(CacheActivityFactory.createTemplate(template.getTemplateEnum()));
        when(activityService.createPromoActivity(any(ActivityModel.class))).thenReturn("1");
        when(qualificationService.createQualifications(any())).thenReturn(1);
        when(productDomain.createProductSku(anyString(), anyString(), anyString())).thenReturn(null);
        when(activityFuncParamService.saveTPromoRuleFuncParam(anyString(), anyString(), any())).thenReturn(1);
        when(incentiveLimitedService.insertLimitedList111(anyString(), any(), anyString())).thenReturn(1);
        when(operationLogService.insertLog(any(), any())).thenReturn(1);
        // when
        String activityCode = activityComponentDomain.createPromoActivity(activityCreateDTO);

        // then
        assertTrue(StringUtils.isNotBlank(activityCode));
    }

    @Test
    public void T0080订单满数量减金额_有限制和渠道指定商品反选(){

        // 创建活动模板
        Template template = Template.builder().templateEnum(TemplateEnum.T0205).condition("3").reward("2").build();
        // 创建活动限制
        List<IncentiveLimited> limiteds = ActivityFactory.createLimiteds(LimitationCodeEnum.USER_TOTAL_COUNT.code(), 3);
        List<ProductScope> products = ActivityFactory.createProducts(1);
        List<ActivityStore> channelStores = ActivityFactory.createChannelStores(1);
        ActivityDomain createDTO = ActivityDomain.builder().incentiveLimiteds(limiteds).channelStores(channelStores).products(products).build();
        ActivityDomain activityCreateDTO = ActivityFactory.createActivityCreateDTO(createDTO, template, null,  IncentiveLimitedFlagEnum.YES,
            StoreParamTypeEnum.STORE_CUSTOM, ProductSelectionEnum.INVERT_SELECT, ProductTypeEnum.CUSTOM_PRODUCT);
        when(templateService.getTemplateByCode(anyString())).thenReturn(CacheActivityFactory.createTemplate(template.getTemplateEnum()));
        when(activityService.createPromoActivity(any(ActivityModel.class))).thenReturn("1");
        when(activityFuncParamService.saveTPromoRuleFuncParam(anyString(), anyString(), any())).thenReturn(1);
        when(incentiveLimitedService.insertLimitedList111(anyString(), any(), anyString())).thenReturn(1);
        when(operationLogService.insertLog(any(), any())).thenReturn(1);
        // when
        String activityCode = activityComponentDomain.createPromoActivity(activityCreateDTO);

        // then
        assertTrue(StringUtils.isNotBlank(activityCode));
    }

    @Test
    public void T0090订单满金额减金额_有限制和指定商品反选(){

        // 创建活动模板
        Template template = Template.builder().templateEnum(TemplateEnum.T0206).condition("3").reward("2").build();
        // 创建活动限制
        List<IncentiveLimited> limiteds = ActivityFactory.createLimiteds(LimitationCodeEnum.USER_TOTAL_COUNT.code(), 3);
        ActivityDomain createDTO = ActivityDomain.builder().incentiveLimiteds(limiteds).build();
        ActivityDomain activityCreateDTO = ActivityFactory.createActivityCreateDTO(createDTO, template, null, IncentiveLimitedFlagEnum.YES,
            StoreParamTypeEnum.STORE_ALL, ProductSelectionEnum.INVERT_SELECT, ProductTypeEnum.CUSTOM_PRODUCT);
        when(templateService.getTemplateByCode(anyString())).thenReturn(CacheActivityFactory.createTemplate(template.getTemplateEnum()));
        when(activityService.createPromoActivity(any(ActivityModel.class))).thenReturn("1");
        when(activityFuncParamService.saveTPromoRuleFuncParam(anyString(), anyString(), any())).thenReturn(1);
        when(incentiveLimitedService.insertLimitedList111(anyString(), any(), anyString())).thenReturn(1);
        when(operationLogService.insertLog(any(), any())).thenReturn(1);
        // when
        String activityCode = activityComponentDomain.createPromoActivity(activityCreateDTO);

        // then
        assertTrue(StringUtils.isNotBlank(activityCode));
    }

    @Test
    public void T0100满数量打折扣_指定商品反选(){

        // 创建活动模板
        Template template = Template.builder().templateEnum(TemplateEnum.T0301).condition("3").reward("0.2").build();
        List<ProductScope> products = ActivityFactory.createProducts(1);
        ActivityDomain createDTO = ActivityDomain.builder().products(products).build();
        ActivityDomain activityCreateDTO = ActivityFactory.createActivityCreateDTO(createDTO, template, null, IncentiveLimitedFlagEnum.NO,
            StoreParamTypeEnum.STORE_ALL, ProductSelectionEnum.INVERT_SELECT, ProductTypeEnum.CUSTOM_PRODUCT);
        when(templateService.getTemplateByCode(anyString())).thenReturn(CacheActivityFactory.createTemplate(template.getTemplateEnum()));
        when(activityService.createPromoActivity(any(ActivityModel.class))).thenReturn("1");
        when(activityFuncParamService.saveTPromoRuleFuncParam(anyString(), anyString(), any())).thenReturn(1);
        when(operationLogService.insertLog(any(), any())).thenReturn(1);
        // when
        String activityCode = activityComponentDomain.createPromoActivity(activityCreateDTO);

        // then
        assertTrue(StringUtils.isNotBlank(activityCode));
    }

    @Test
    public void T0110满金额打折扣(){

        // 创建活动模板
        Template template = Template.builder().templateEnum(TemplateEnum.T0302).condition("3").reward("0.2").build();
        ActivityDomain activityCreateDTO = ActivityFactory.createActivityCreateDTO(null, template, null, IncentiveLimitedFlagEnum.NO,
            StoreParamTypeEnum.STORE_ALL, ProductSelectionEnum.SELECT, ProductTypeEnum.ALL);
        when(templateService.getTemplateByCode(anyString())).thenReturn(CacheActivityFactory.createTemplate(template.getTemplateEnum()));
        when(activityService.createPromoActivity(any(ActivityModel.class))).thenReturn("1");
        when(activityFuncParamService.saveTPromoRuleFuncParam(anyString(), anyString(), any())).thenReturn(1);
        when(operationLogService.insertLog(any(), any())).thenReturn(1);
        // when
        String activityCode = activityComponentDomain.createPromoActivity(activityCreateDTO);

        assertTrue(StringUtils.isNotBlank(activityCode));
    }

    @Test
    public void T0120第数量打折扣(){

        // 创建活动模板
        Template template = Template.builder().templateEnum(TemplateEnum.T0303).condition("3").reward("0.2").build();
        ActivityDomain activityCreateDTO = ActivityFactory.createActivityCreateDTO(null, template, null, IncentiveLimitedFlagEnum.NO,
            StoreParamTypeEnum.STORE_ALL, ProductSelectionEnum.SELECT, ProductTypeEnum.ALL);
        when(templateService.getTemplateByCode(anyString())).thenReturn(CacheActivityFactory.createTemplate(template.getTemplateEnum()));
        when(activityService.createPromoActivity(any(ActivityModel.class))).thenReturn("1");
        when(activityFuncParamService.saveTPromoRuleFuncParam(anyString(), anyString(), any())).thenReturn(1);
        when(operationLogService.insertLog(any(), any())).thenReturn(1);
        // when
        String activityCode = activityComponentDomain.createPromoActivity(activityCreateDTO);

        // then
        assertTrue(StringUtils.isNotBlank(activityCode));
    }

    @Test
    public void T0130订单满数量打折扣(){

        // 创建活动模板
        Template template = Template.builder().templateEnum(TemplateEnum.T0304).condition("3").reward("0.2").build();
        ActivityDomain activityCreateDTO = ActivityFactory.createActivityCreateDTO(null, template, null, IncentiveLimitedFlagEnum.NO,
            StoreParamTypeEnum.STORE_ALL, ProductSelectionEnum.SELECT, ProductTypeEnum.ALL);
        when(templateService.getTemplateByCode(anyString())).thenReturn(CacheActivityFactory.createTemplate(template.getTemplateEnum()));
        when(activityService.createPromoActivity(any(ActivityModel.class))).thenReturn("1");
        when(activityFuncParamService.saveTPromoRuleFuncParam(anyString(), anyString(), any())).thenReturn(1);
        when(operationLogService.insertLog(any(), any())).thenReturn(1);
        // when
        String activityCode = activityComponentDomain.createPromoActivity(activityCreateDTO);

        // then
        assertTrue(StringUtils.isNotBlank(activityCode));
    }

    @Test
    public void T0140订单满金额打折扣(){

        // 创建活动模板
        Template template = Template.builder().templateEnum(TemplateEnum.T0305).condition("3").reward("0.2").build();
        ActivityDomain activityCreateDTO = ActivityFactory.createActivityCreateDTO(null, template, null, IncentiveLimitedFlagEnum.NO, StoreParamTypeEnum.STORE_ALL, ProductSelectionEnum.SELECT, ProductTypeEnum.ALL);
        when(templateService.getTemplateByCode(anyString())).thenReturn(CacheActivityFactory.createTemplate(template.getTemplateEnum()));
        when(activityService.createPromoActivity(any(ActivityModel.class))).thenReturn("1");
        when(activityFuncParamService.saveTPromoRuleFuncParam(anyString(), anyString(), any())).thenReturn(1);
        when(operationLogService.insertLog(any(), any())).thenReturn(1);
        // when
        String activityCode = activityComponentDomain.createPromoActivity(activityCreateDTO);

        // then
        assertTrue(StringUtils.isNotBlank(activityCode));
    }

    @Test
    public void T0150满数量设为总计特价(){

        // 创建活动模板
        Template template = Template.builder().templateEnum(TemplateEnum.T0401).condition("3").reward("2").build();
        ActivityDomain activityCreateDTO = ActivityFactory.createActivityCreateDTO(null, template, null, IncentiveLimitedFlagEnum.NO, StoreParamTypeEnum.STORE_ALL, ProductSelectionEnum.SELECT, ProductTypeEnum.ALL);
        when(templateService.getTemplateByCode(anyString())).thenReturn(CacheActivityFactory.createTemplate(template.getTemplateEnum()));
        when(activityService.createPromoActivity(any(ActivityModel.class))).thenReturn("1");
        when(activityFuncParamService.saveTPromoRuleFuncParam(anyString(), anyString(), any())).thenReturn(1);
        when(operationLogService.insertLog(any(), any())).thenReturn(1);
        // when
        String activityCode = activityComponentDomain.createPromoActivity(activityCreateDTO);

        // then
        assertTrue(StringUtils.isNotBlank(activityCode));
    }

    @Test
    public void T0160每满数量设为总计特价(){

        // 创建活动模板
        Template template = Template.builder().templateEnum(TemplateEnum.T0402).condition("3").reward("2").build();
        ActivityDomain activityCreateDTO = ActivityFactory.createActivityCreateDTO(null, template, null, IncentiveLimitedFlagEnum.NO, StoreParamTypeEnum.STORE_ALL, ProductSelectionEnum.SELECT, ProductTypeEnum.ALL);
        when(templateService.getTemplateByCode(anyString())).thenReturn(CacheActivityFactory.createTemplate(template.getTemplateEnum()));
        when(activityService.createPromoActivity(any(ActivityModel.class))).thenReturn("1");
        when(activityFuncParamService.saveTPromoRuleFuncParam(anyString(), anyString(), any())).thenReturn(1);
        when(operationLogService.insertLog(any(), any())).thenReturn(1);
        // when
        String activityCode = activityComponentDomain.createPromoActivity(activityCreateDTO);

        // then
        assertTrue(StringUtils.isNotBlank(activityCode));
    }

    @Test
    public void T0170订单满数量包邮(){
        // given
        // 创建活动模板
        Template template = Template.builder().templateEnum(TemplateEnum.T0501).condition("3").reward("2").build();
        ActivityDomain activityCreateDTO = ActivityFactory.createActivityCreateDTO(null, template, null, IncentiveLimitedFlagEnum.NO, StoreParamTypeEnum.STORE_ALL, ProductSelectionEnum.SELECT, ProductTypeEnum.ALL);
        when(templateService.getTemplateByCode(anyString())).thenReturn(CacheActivityFactory.createTemplate(template.getTemplateEnum()));
        when(activityService.createPromoActivity(any(ActivityModel.class))).thenReturn("1");
        when(activityFuncParamService.saveTPromoRuleFuncParam(anyString(), anyString(), any())).thenReturn(1);
        when(operationLogService.insertLog(any(), any())).thenReturn(1);
        // when
        String activityCode = activityComponentDomain.createPromoActivity(activityCreateDTO);
 
        // then
        assertTrue(StringUtils.isNotBlank(activityCode));
    }

    @Test
    public void T0180订单满金额包邮(){

        // 创建活动模板
        Template template = Template.builder().templateEnum(TemplateEnum.T0502).condition("3").reward("2").build();
        ActivityDomain activityCreateDTO = ActivityFactory.createActivityCreateDTO(null, template, null, IncentiveLimitedFlagEnum.NO, StoreParamTypeEnum.STORE_ALL, ProductSelectionEnum.SELECT, ProductTypeEnum.ALL);
        when(templateService.getTemplateByCode(anyString())).thenReturn(CacheActivityFactory.createTemplate(template.getTemplateEnum()));
        when(activityService.createPromoActivity(any(ActivityModel.class))).thenReturn("1");
        when(activityFuncParamService.saveTPromoRuleFuncParam(anyString(), anyString(), any())).thenReturn(1);
        when(operationLogService.insertLog(any(), any())).thenReturn(1);
        // when
        String activityCode = activityComponentDomain.createPromoActivity(activityCreateDTO);

        // then
        assertTrue(StringUtils.isNotBlank(activityCode));
    }

    @Test
    public void T0190订单满数量送赠品(){
        // given
        // 创建活动模板
        Template template = Template.builder().templateEnum(TemplateEnum.T0601).condition("3").reward("2").build();
        ActivityDomain createDTO = ActivityDomain.builder().giveaways(ActivityFactory.createGiveaways(2)).build();
        ActivityDomain activityCreateDTO = ActivityFactory.createActivityCreateDTO(createDTO, template, null, IncentiveLimitedFlagEnum.NO, StoreParamTypeEnum.STORE_ALL, ProductSelectionEnum.SELECT, ProductTypeEnum.ALL);
        when(templateService.getTemplateByCode(anyString())).thenReturn(CacheActivityFactory.createTemplate(template.getTemplateEnum()));
        when(activityService.createPromoActivity(any(ActivityModel.class))).thenReturn("1");
        when(activityFuncParamService.saveTPromoRuleFuncParam(anyString(), anyString(), any())).thenReturn(1);
        when(operationLogService.insertLog(any(), any())).thenReturn(1);
        // when
        String activityCode = activityComponentDomain.createPromoActivity(activityCreateDTO);

        // then
        assertTrue(StringUtils.isNotBlank(activityCode));
    }

    @Test
    public void T0200订单满金额送赠品(){

        // 创建活动模板
        Template template = Template.builder().templateEnum(TemplateEnum.T0602).condition("3").reward("2").build();
        ActivityDomain createDTO = ActivityDomain.builder().giveaways(ActivityFactory.createGiveaways(2)).build();
        ActivityDomain activityCreateDTO = ActivityFactory.createActivityCreateDTO(createDTO, template, null, IncentiveLimitedFlagEnum.NO, StoreParamTypeEnum.STORE_ALL, ProductSelectionEnum.SELECT, ProductTypeEnum.ALL);
        when(templateService.getTemplateByCode(anyString())).thenReturn(CacheActivityFactory.createTemplate(template.getTemplateEnum()));
        when(activityService.createPromoActivity(any(ActivityModel.class))).thenReturn("1");
        when(activityFuncParamService.saveTPromoRuleFuncParam(anyString(), anyString(), any())).thenReturn(1);
        when(operationLogService.insertLog(any(), any())).thenReturn(1);
        // when
        String activityCode = activityComponentDomain.createPromoActivity(activityCreateDTO);

        // then
        assertTrue(StringUtils.isNotBlank(activityCode));
    }

    @Test
    public void T0210满数量送赠品(){
        // given
        // 创建活动模板
        Template template = Template.builder().templateEnum(TemplateEnum.T0603).condition("3").reward("1").build();
        ActivityDomain createDTO = ActivityDomain.builder().giveaways(ActivityFactory.createGiveaways(2)).build();
        ActivityDomain activityCreateDTO = ActivityFactory.createActivityCreateDTO(createDTO, template, null,  IncentiveLimitedFlagEnum.NO, StoreParamTypeEnum.STORE_ALL, ProductSelectionEnum.SELECT, ProductTypeEnum.ALL);
        when(templateService.getTemplateByCode(anyString())).thenReturn(CacheActivityFactory.createTemplate(template.getTemplateEnum()));
        when(activityService.createPromoActivity(any(ActivityModel.class))).thenReturn("1");
        when(activityFuncParamService.saveTPromoRuleFuncParam(anyString(), anyString(), any())).thenReturn(1);
        when(operationLogService.insertLog(any(), any())).thenReturn(1);
        // when
        String activityCode = activityComponentDomain.createPromoActivity(activityCreateDTO);

        // then
        assertTrue(StringUtils.isNotBlank(activityCode));
    }

    @Test
    public void T0220满金额送赠品(){

        // 创建活动模板
        Template template = Template.builder().templateEnum(TemplateEnum.T0604).condition("3").reward("2").build();
        ActivityDomain createDTO = ActivityDomain.builder().giveaways(ActivityFactory.createGiveaways(2)).build();
        ActivityDomain activityCreateDTO = ActivityFactory.createActivityCreateDTO(createDTO, template, null,  IncentiveLimitedFlagEnum.NO, StoreParamTypeEnum.STORE_ALL, ProductSelectionEnum.SELECT, ProductTypeEnum.ALL);
        when(templateService.getTemplateByCode(anyString())).thenReturn(CacheActivityFactory.createTemplate(template.getTemplateEnum()));
        when(activityService.createPromoActivity(any(ActivityModel.class))).thenReturn("1");
        when(operationLogService.insertLog(any(), any())).thenReturn(1);
        // when
        String activityCode = activityComponentDomain.createPromoActivity(activityCreateDTO);

        // then
        assertTrue(StringUtils.isNotBlank(activityCode));
    }

    @Test
    public void T0230每满数量送赠品(){

        // 创建活动模板
        Template template = Template.builder().templateEnum(TemplateEnum.T0605).condition("3").reward("2").build();
        ActivityDomain createDTO = ActivityDomain.builder().giveaways(ActivityFactory.createGiveaways(2)).build();
        ActivityDomain activityCreateDTO = ActivityFactory.createActivityCreateDTO(createDTO, template, null, IncentiveLimitedFlagEnum.NO, StoreParamTypeEnum.STORE_ALL, ProductSelectionEnum.SELECT, ProductTypeEnum.ALL);
        when(templateService.getTemplateByCode(anyString())).thenReturn(CacheActivityFactory.createTemplate(template.getTemplateEnum()));
        when(activityService.createPromoActivity(any(ActivityModel.class))).thenReturn("1");
        when(activityFuncParamService.saveTPromoRuleFuncParam(anyString(), anyString(), any())).thenReturn(1);
        when(operationLogService.insertLog(any(), any())).thenReturn(1);
        // when
        String activityCode = activityComponentDomain.createPromoActivity(activityCreateDTO);

        // then
        assertTrue(StringUtils.isNotBlank(activityCode));
    }

    @Test
    public void T0240每满金额送赠品(){

        // 创建活动模板
        Template template = Template.builder().templateEnum(TemplateEnum.T0606).condition("3").reward("2").build();
        ActivityDomain createDTO = ActivityDomain.builder().giveaways(ActivityFactory.createGiveaways(2)).build();
        ActivityDomain activityCreateDTO = ActivityFactory.createActivityCreateDTO(createDTO, template, null, IncentiveLimitedFlagEnum.NO, StoreParamTypeEnum.STORE_ALL, ProductSelectionEnum.SELECT, ProductTypeEnum.ALL);
        when(templateService.getTemplateByCode(anyString())).thenReturn(CacheActivityFactory.createTemplate(template.getTemplateEnum()));
        when(activityService.createPromoActivity(any(ActivityModel.class))).thenReturn("1");
        when(activityFuncParamService.saveTPromoRuleFuncParam(anyString(), anyString(), any())).thenReturn(1);
        when(operationLogService.insertLog(any(), any())).thenReturn(1);
        // when
        String activityCode = activityComponentDomain.createPromoActivity(activityCreateDTO);

        // then
        assertTrue(StringUtils.isNotBlank(activityCode));
    }

    @Test
    public void T0250多组商品范围各选1件设为总计特价(){

        // 创建活动模板
        Template template = Template.builder().templateEnum(TemplateEnum.T0701).condition("3").reward("2").build();
        ActivityDomain createDTO = ActivityDomain.builder().products(ActivityFactory.createProducts(3)).build();
        ActivityDomain activityCreateDTO = ActivityFactory.createActivityCreateDTO(createDTO, template, null, IncentiveLimitedFlagEnum.NO, StoreParamTypeEnum.STORE_ALL, ProductSelectionEnum.SELECT, ProductTypeEnum.ALL);
        when(templateService.getTemplateByCode(anyString())).thenReturn(CacheActivityFactory.createTemplate(template.getTemplateEnum()));
        when(activityService.createPromoActivity(any(ActivityModel.class))).thenReturn("1");
        when(activityFuncParamService.saveTPromoRuleFuncParam(anyString(), anyString(), any())).thenReturn(1);
        when(operationLogService.insertLog(any(), any())).thenReturn(1);
        // when
        String activityCode = activityComponentDomain.createPromoActivity(activityCreateDTO);

        // then
        assertTrue(StringUtils.isNotBlank(activityCode));
    }

    @Test
    public void T0260每满数量送同类商品(){

        // 创建活动模板
        Template template = Template.builder().templateEnum(TemplateEnum.T0801).condition("3").reward("2").build();
        ActivityDomain createDTO = ActivityDomain.builder().products(ActivityFactory.createProducts(1)).build();
        ActivityDomain activityCreateDTO = ActivityFactory.createActivityCreateDTO(createDTO, template, null, IncentiveLimitedFlagEnum.NO, StoreParamTypeEnum.STORE_ALL, ProductSelectionEnum.SELECT, ProductTypeEnum.ALL);
        when(templateService.getTemplateByCode(anyString())).thenReturn(CacheActivityFactory.createTemplate(template.getTemplateEnum()));
        when(activityService.createPromoActivity(any(ActivityModel.class))).thenReturn("1");
        when(activityFuncParamService.saveTPromoRuleFuncParam(anyString(), anyString(), any())).thenReturn(1);
        when(operationLogService.insertLog(any(), any())).thenReturn(1);
        // when
        String activityCode = activityComponentDomain.createPromoActivity(activityCreateDTO);

        // then
        assertTrue(StringUtils.isNotBlank(activityCode));
    }

    @Test
    public void T0270每满数量送不同类商品(){
        // given
        // 创建活动模板
        Template template = Template.builder().templateEnum(TemplateEnum.T0802).condition("3").reward("2").build();
        ActivityDomain createDTO = ActivityDomain.builder().products(ActivityFactory.createProducts(3)).build();
        ActivityDomain activityCreateDTO = ActivityFactory.createActivityCreateDTO(createDTO, template, null, IncentiveLimitedFlagEnum.NO, StoreParamTypeEnum.STORE_ALL, ProductSelectionEnum.SELECT, ProductTypeEnum.ALL);
        when(templateService.getTemplateByCode(anyString())).thenReturn(CacheActivityFactory.createTemplate(template.getTemplateEnum()));
        when(activityService.createPromoActivity(any(ActivityModel.class))).thenReturn("1");
        when(activityFuncParamService.saveTPromoRuleFuncParam(anyString(), anyString(), any())).thenReturn(1);
        when(operationLogService.insertLog(any(), any())).thenReturn(1);
        // when
        String activityCode = activityComponentDomain.createPromoActivity(activityCreateDTO);

        // then
        assertTrue(StringUtils.isNotBlank(activityCode));
    }

    @Test
    public void T0280多件商品设置不同的促销价(){
        // given
        // 创建活动模板
        Template template = Template.builder().templateEnum(TemplateEnum.T0104).build();
        ActivityDomain createDTO = ActivityDomain.builder().products(ActivityFactory.createProducts(3)).build();
        ActivityDomain activityCreateDTO = ActivityFactory.createActivityCreateDTO(createDTO, template, null, IncentiveLimitedFlagEnum.NO, StoreParamTypeEnum.STORE_ALL, ProductSelectionEnum.SELECT, ProductTypeEnum.ALL);
        when(templateService.getTemplateByCode(anyString())).thenReturn(CacheActivityFactory.createTemplate(template.getTemplateEnum()));
        when(activityService.createPromoActivity(any(ActivityModel.class))).thenReturn("1");
        when(activityFuncParamService.saveTPromoRuleFuncParam(anyString(), anyString(), any())).thenReturn(1);
        when(operationLogService.insertLog(any(), any())).thenReturn(1);
        // when
        String activityCode = activityComponentDomain.createPromoActivity(activityCreateDTO);

        // then
        assertTrue(StringUtils.isNotBlank(activityCode));
    }

    @Test
    public void T0290商品范围满金额每件减金额(){

        // 创建活动模板
        Template template = Template.builder().templateEnum(TemplateEnum.T0209).condition("2").reward("3").build();
        ActivityDomain createDTO = ActivityDomain.builder().products(ActivityFactory.createProducts(3)).build();
        ActivityDomain activityCreateDTO = ActivityFactory.createActivityCreateDTO(createDTO, template, null, IncentiveLimitedFlagEnum.NO, StoreParamTypeEnum.STORE_ALL, ProductSelectionEnum.SELECT, ProductTypeEnum.ALL);
        when(templateService.getTemplateByCode(anyString())).thenReturn(CacheActivityFactory.createTemplate(template.getTemplateEnum()));
        when(activityService.createPromoActivity(any(ActivityModel.class))).thenReturn("1");
        when(activityFuncParamService.saveTPromoRuleFuncParam(anyString(), anyString(), any())).thenReturn(1);
        when(operationLogService.insertLog(any(), any())).thenReturn(1);
        // when
        String activityCode = activityComponentDomain.createPromoActivity(activityCreateDTO);

        // then
        assertTrue(StringUtils.isNotBlank(activityCode));
    }

    @Test
    public void T0300买A优惠B减金额(){

        // 创建活动模板
        Template template = Template.builder().templateEnum(TemplateEnum.T1001).condition("2").reward("3").build();
        ActivityDomain createDTO = ActivityDomain.builder().products(ActivityFactory.createProducts(3)).build();
        ActivityDomain activityCreateDTO = ActivityFactory.createActivityCreateDTO(createDTO, template, null, IncentiveLimitedFlagEnum.NO, StoreParamTypeEnum.STORE_ALL, ProductSelectionEnum.SELECT, ProductTypeEnum.ALL);
        when(templateService.getTemplateByCode(anyString())).thenReturn(CacheActivityFactory.createTemplate(template.getTemplateEnum()));
        when(activityService.createPromoActivity(any(ActivityModel.class))).thenReturn("1");
        when(activityFuncParamService.saveTPromoRuleFuncParam(anyString(), anyString(), any())).thenReturn(1);
        when(operationLogService.insertLog(any(), any())).thenReturn(1);
        // when
        String activityCode = activityComponentDomain.createPromoActivity(activityCreateDTO);

        // then
        assertTrue(StringUtils.isNotBlank(activityCode));
    }
    
    @Test
    public void T1004买A优惠B打折扣(){

        // 创建活动模板
        Template template = Template.builder().templateEnum(TemplateEnum.T1004).condition("2").reward("3").build();
        ActivityDomain createDTO = ActivityDomain.builder().products(ActivityFactory.createProducts(3)).build();
        ActivityDomain activityCreateDTO = ActivityFactory.createActivityCreateDTO(createDTO, template, null, IncentiveLimitedFlagEnum.NO, StoreParamTypeEnum.STORE_ALL, ProductSelectionEnum.SELECT, ProductTypeEnum.ALL);
        when(templateService.getTemplateByCode(anyString())).thenReturn(CacheActivityFactory.createTemplate(template.getTemplateEnum()));
        when(activityService.createPromoActivity(any(ActivityModel.class))).thenReturn("1");
        when(activityFuncParamService.saveTPromoRuleFuncParam(anyString(), anyString(), any())).thenReturn(1);
        when(operationLogService.insertLog(any(), any())).thenReturn(1);
        // when
        String activityCode = activityComponentDomain.createPromoActivity(activityCreateDTO);

        // then
        assertTrue(StringUtils.isNotBlank(activityCode));
    }
}
