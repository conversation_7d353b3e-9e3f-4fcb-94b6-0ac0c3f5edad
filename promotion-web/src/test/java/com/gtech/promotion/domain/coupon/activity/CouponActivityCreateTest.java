/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.domain.coupon.activity;

import com.gtech.promotion.dao.entity.activity.GiveawayEntity;
import com.gtech.promotion.dao.mapper.activity.GiveawayMapper;
import com.gtech.promotion.dao.model.activity.GiveawayVO;
import com.gtech.promotion.service.impl.activity.GiveawayServiceImpl;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import tk.mybatis.mapper.mapperhelper.EntityHelper;
import tk.mybatis.mapper.mapperhelper.MapperHelper;

import java.util.ArrayList;
import java.util.List;

/**
 * 测试券活动创建
 */
@RunWith(MockitoJUnitRunner.class)
public class CouponActivityCreateTest{

    @InjectMocks
    private GiveawayServiceImpl giveawayService;

    @Mock
    private GiveawayMapper giveawayMapper;


    @Before
    public void before(){
        EntityHelper.initEntityNameMap(GiveawayEntity.class, new MapperHelper().getConfig());
    }
    @Test
    public void deleteGiveaway(){

        Mockito.when(giveawayMapper.delete(Mockito.any())).thenReturn(1);
        giveawayService.deleteGiveaway("1","!");
    }
    @Test
    public void getGiftListByActivityCodes(){

        List<String> activityCodes = new ArrayList<>();
        activityCodes.add("1");
        Mockito.when(giveawayMapper.selectByCondition(Mockito.any())).thenReturn(new ArrayList<>());
        List<GiveawayVO> giftListByActivityCodes = giveawayService.getGiftListByActivityCodes("1", activityCodes);
        Assert.assertEquals(0,giftListByActivityCodes.size());
    }



//    // 1、有无奖励 限制   incentiveLimitedFlag 00无限制  01有限制
//    // 2、有无渠道 限制   storeType 店铺范围：00-全店铺 01-自定义
//    // 3、有无会员 等级限制   members
//    // 商品范围： productType  00-全商品 01-指定范围 02-指定商品（上传文件）  03-指定多个商品范围
//    // 4、商品：全商品
//    // 5、商品：指定商品范围
//    // 6、商品：指定商品sku
//    // 7、商品：指定多个商品范围
//    // 8、正反选 productSelectType商品范围正反选：01-正选；02-反选
//
//    @Mock
//    private TPromoActivityDomainImpl activityDomain;
//
//    @InjectMocks
//    private CouponActivityDomainImpl couponActivityDomain;
//
//    @Mock
//    private TPromoActivityServiceImpl activityService;
//
//    @Mock
//    private TPromoActivityFuncParamServiceImpl tPromoActivityFuncParamService;
//
//    @Mock
//    private TPromoTemplateServiceImpl tPromoTemplateService;
//
//    @Mock
//    private TPromoTemplateFunctionServiceImpl tPromoTemplateFunctionService;
//
//    @Mock
//    private ProductDomainImpl productDomain;
//
//    @Mock
//    private ActivityCacheDomainImpl activityCacheDomain;
//
//    @Mock
//    private PromoCouponActivityServiceImpl promoCouponActivityService;
//    @Test
//    public void 券活动_订单满数量打折扣(){
//        //given
//        //创建券活动 订单满数量打折扣模板    满2件打五折
//        Template template = Template.builder().templateEnum(TemplateEnum.T304).condition("2").reward("5").build();
//        //构建全商品
//        List<ActivityProduct> activityProducts = new ArrayList<ActivityProduct>();
//        activityProducts.add(ActivityProduct.builder().tenantCode("111").build());
//        //商品信息
//        List<ActivityProductDetail> activityProductDetails = Lists.newArrayList();
//        activityProductDetails.add(ActivityProductDetail.builder().tenantCode("111").skuCode("skuCode1").skuName("skuName1").productCode("productCode1").spuName("spuName1").build());
//        // 1、有无奖励 限制   incentiveLimitedFlag 00无限制  01有限制
//        // 2、有无渠道 限制   storeType 店铺范围：00-全店铺 01-自定义
//        // 3、有无会员 等级限制   members
//        // 商品范围： productType  00-全商品 01-指定范围 02-指定商品（上传文件）  03-指定多个商品范围
//        // 4、商品：全商品
//        // 5、商品：指定商品范围
//        // 6、商品：指定商品sku
//        // 7、商品：指定多个商品范围
//        // 8、正反选 productSelectType商品范围正反选：01-正选；02-反选
//        TPromoActivityCreateDTO createDTO = new TPromoActivityCreateDTO();
//        TPromoActivityCreateDTO createActivityCreateDTO = ActivityFactory.createActivityCreateDTO(createDTO, template, null, null, IncentiveLimitedFlagEnum.NO, StoreParamTypeEnum.STORE_ALL_MERC, ProductSelectionEnum.SELECT, ProductTypeEnum.ALL);
//        TPromoTemplateVO templateVO = new TPromoTemplateVO();
//        templateVO.setId(TemplateEnum.T304.getId());
//        templateVO.setTemplateCode(template.getTemplateEnum().getCode());
//        //given
//        CreateCouponActivityDTO createCouponActivityDTO = ActivityFactory.createCouponActivityDTO(createActivityCreateDTO, "1", CouponTypeEnum.PROMOTION_COUPON.code(), null, 0, "0");
//        //when mock掉的方法需要返回一个对应的结果 0103 0202 0302 0402
//        when(activityDomain.createPromoActivity(any(TPromoActivityCreateDTO.class))).thenReturn("1");
//        when(promoCouponActivityService.createCouponActivity(any(TPromoCouponActivityVO.class))).thenReturn("2");
//        String createCouponActivity2 = couponActivityDomain.createCouponActivity(createCouponActivityDTO);
//        // then
//        Assert.assertEquals("1", createCouponActivity2);
//    }
}
