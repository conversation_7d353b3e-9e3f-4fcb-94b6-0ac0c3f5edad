/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.domain.impl.activity;

import com.google.common.collect.Lists;
import com.gtech.promotion.calc.TemplateEnum;
import com.gtech.promotion.code.activity.ActivityTypeEnum;
import com.gtech.promotion.code.activity.ProductSelectionEnum;
import com.gtech.promotion.component.activity.ActivityCacheDomain;
import com.gtech.promotion.component.activity.PromoGroupRelationDomain;
import com.gtech.promotion.component.activity.ShoppingCartDomain;
import com.gtech.promotion.component.boostsharing.BoostSharingComponent;
import com.gtech.promotion.component.coupon.CouponCodeUserComponent;
import com.gtech.promotion.dao.model.coupon.PromoPassVO;
import com.gtech.promotion.dao.model.coupon.TPromoCouponInnerCodeVO;
import com.gtech.promotion.dto.cache.ActivityCacheDTO;
import com.gtech.promotion.dto.in.activity.ShoppingCartDTO;
import com.gtech.promotion.dto.in.activity.ShoppingCartItem;
import com.gtech.promotion.exception.PromotionException;
import com.gtech.promotion.exception.PromotionParamValidateException;
import com.gtech.promotion.helper.ActivityRedisHelpler;
import com.gtech.promotion.helper.TemplateHelper;
import com.gtech.promotion.helper.bean.ActivityProductDetail;
import com.gtech.promotion.helper.bean.Template;
import com.gtech.promotion.helper.factory.CacheActivityFactory;
import com.gtech.promotion.helper.factory.ShoppingCartFactory;
import com.gtech.promotion.service.activity.ActivityProductDetailService;
import com.gtech.promotion.service.activity.ActivityService;
import com.gtech.promotion.service.activity.TPromoActivityExpressionService;
import com.gtech.promotion.service.coupon.PromoCouponInnerCodeService;
import com.gtech.promotion.utils.ExpressionHelper;
import com.gtech.promotion.vo.bean.ProductAttribute;
import com.gtech.promotion.vo.bean.ProductCodes;
import com.gtech.promotion.vo.bean.ProductScope;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;

import java.math.BigDecimal;
import java.util.*;

import static org.junit.Assert.assertTrue;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class ShoppingCartDomainImplTest {

    // 根据购物车商品筛选可参加的活动
    // 正常的

    // 1、只有属性满足
    // 1、只有分类+品牌满足
    // 1、只有分类+属性满足
    // 1、只有品牌+属性满足
    // 1、指定sku活动满足

    // 3、促销和券活动
    // 4、正选
    // 5、反选

    // 6、多活动叠加
    // 7、多活动互斥
    // 8、多活动优先级排序

    // 异常：
    // 商品列表不能为空
    // 租户编码不能为空

    //    单品无条件减金额
    //    单品无条件打折扣
    //    单品无条件设为每件特价
    //    商品范围满数量减金额
    //    商品范围满金额减金额
    //    商品范围每满数量减金额 --
    //    商品范围每满金额减金额 --
    //    订单满数量减金额
    //    订单满金额减金额
    //    商品范围满数量打折扣
    //    商品范围满金额打折扣
    //    商品范围第数量打折扣
    //    订单满数量打折扣
    //    订单满金额打折扣
    //    商品范围满数量设为总计特价
    //    商品范围每满数量设为总计特价
    //    商品范围满数量送赠品
    //    商品范围满金额送赠品
    //    商品范围每满数量送赠品
    //    商品范围每满金额送赠品
    //    订单满数量送赠品
    //    订单满金额送赠品
    //    订单满数量包邮
    //    订单满金额包邮
    //    多组商品范围各选1件设为总计特价
    //    商品范围每满数量送同类商品
    //    商品范围每满数量送不同类商品

    /**
     * 如果活动为空，直接返回原有的购物车
     */

    @InjectMocks
    private ShoppingCartDomain shoppingCartDomain;

    @Mock
    private ActivityCacheDomain activityCacheDomain;
    @Mock
    private ActivityProductDetailService productDetailService;

    @Mock
    private CouponCodeUserComponent couponCodeUserDomain;
    @Mock
    private CouponCodeUserComponent couponCodeUserComponent;
    @Mock
    private TemplateHelper templateHelper;

    @Mock
    private TPromoActivityExpressionService expressionService;

    @Mock
    private ActivityRedisHelpler redisService;

    @Mock
    private PromoCouponInnerCodeService couponInnerCodeService;

    @Mock
    private ActivityService activityService;

    @Mock
    private BoostSharingComponent boostSharingComponent;
    @Before
    public void testBefor() {

        when(redisService.getActivitySetting(anyString(), anyString())).thenReturn(new HashMap<>());
        when(templateHelper.templateCode2TagCode(anyString())).thenReturn("01");
        ReflectionTestUtils.setField(shoppingCartDomain, "groupBlackList", Lists.newArrayList("tenantCode"));
    }

    @Mock
    private PromoGroupRelationDomain promoGroupRelationDomain;
    @Test
    public void 单品减金额_指定分类() {

        // given
        String categoryCode = "01";
        // 创建购物车商品项列表
        List<ShoppingCartItem> shoppingCartItems = new ArrayList<>();
        shoppingCartItems.add(ShoppingCartFactory.createShoppingCartItemByCategory(categoryCode, new BigDecimal(10), 1));
        // 创建购物车
        ShoppingCartDTO shoppingCart = ShoppingCartFactory.createShoppingCart(null, shoppingCartItems, null);

        Map<String, ActivityCacheDTO> map = new HashMap<>();
        // 创建活动范围
        List<ProductScope> activityProductList = new ArrayList<>();
        activityProductList.add(ProductScope.builder().categoryCode(categoryCode).build());
        // 创建活动模板
        Template template = Template.builder().templateEnum(TemplateEnum.T0101).reward("1").build();
        // 创建活动
        ActivityCacheDTO activity = CacheActivityFactory.createActivity(ActivityTypeEnum.ACTIVITY, template, ProductSelectionEnum.SELECT, activityProductList, null, null);
        map.put(activity.getActivityModel().getActivityCode(), activity);
//        when(promoGroupRelationDomain.filterActivityByCoupon(any(), any())).thenReturn(map);
        // when
        when(activityCacheDomain.filterActivityByProduct(anyMap(), any(ProductCodes.class), any())).thenReturn(map);
        when(activityCacheDomain.filterNoProductListPrice(anyMap(), Mockito.any())).thenReturn(map);
        
        when(expressionService.getActivityExpression(anyString())).thenReturn(ExpressionHelper.DEF_EXPROSSION);
        shoppingCartDomain.queryActivity(shoppingCart, map,false);

        // then
        assertTrue(shoppingCart.getActivityExpr().contains(activity.getActivityModel().getActivityCode()));
    }

    @Test
    public void 单品折扣_指定品牌() {

        // given
        String brandCode = "01";
        // 创建购物车商品项列表
        List<ShoppingCartItem> shoppingCartItems = new ArrayList<>();
        shoppingCartItems.add(ShoppingCartFactory.createShoppingCartItemByBrand(brandCode, new BigDecimal(10), 1));
        // 创建购物车
        ShoppingCartDTO shoppingCart = ShoppingCartFactory.createShoppingCart(null, shoppingCartItems, null);

        Map<String, ActivityCacheDTO> map = new HashMap<>();
        // 创建活动范围
        List<ProductScope> activityProductList = new ArrayList<>();
        activityProductList.add(ProductScope.builder().brandCode(brandCode).build());
        // 创建活动模板
        Template template = Template.builder().templateEnum(TemplateEnum.T0102).reward("5").build();
        // 创建活动
        ActivityCacheDTO activity = CacheActivityFactory.createActivity(ActivityTypeEnum.ACTIVITY, template, ProductSelectionEnum.SELECT, activityProductList, null, null);
        map.put(activity.getActivityModel().getActivityCode(), activity);
//        when(promoGroupRelationDomain.filterActivityByCoupon(any(), any())).thenReturn(map);
        // when
        when(activityCacheDomain.filterActivityByProduct(anyMap(), any(ProductCodes.class), any())).thenReturn(map);
        when(activityCacheDomain.filterNoProductListPrice(anyMap(), Mockito.any())).thenReturn(map);
        
        when(expressionService.getActivityExpression(anyString())).thenReturn(ExpressionHelper.DEF_EXPROSSION);
        shoppingCartDomain.queryActivity(shoppingCart, map,false);

        // then
        assertTrue(shoppingCart.getActivityExpr().contains(activity.getActivityModel().getActivityCode()));
    }

    @Test
    public void 单品特价_指定sku() {

        // given
        String productCode = "01";
        String skuCode = "01";
        // 创建购物车商品项列表
        List<ShoppingCartItem> shoppingCartItems = new ArrayList<>();
        shoppingCartItems.add(ShoppingCartFactory.createShoppingCartItemBySku(productCode, skuCode, new BigDecimal(10), 1));
        // 创建购物车
        ShoppingCartDTO shoppingCart = ShoppingCartFactory.createShoppingCart(null, shoppingCartItems, null);

        Map<String, ActivityCacheDTO> map = new HashMap<>();
        // 创建活动范围
        List<ActivityProductDetail> details = new ArrayList<>();
        details.add(ActivityProductDetail.builder().productCode(productCode).skuCode(skuCode).build());
        // 创建活动模板
        Template template = Template.builder().templateEnum(TemplateEnum.T0103).reward("5").build();
        // 创建活动
        ActivityCacheDTO activity = CacheActivityFactory.createActivity(ActivityTypeEnum.ACTIVITY, template, ProductSelectionEnum.SELECT, null, details, null);
        map.put(activity.getActivityModel().getActivityCode(), activity);
//        when(promoGroupRelationDomain.filterActivityByCoupon(any(), any())).thenReturn(map);
        // when
        when(activityCacheDomain.filterActivityByProduct(anyMap(), any(ProductCodes.class), any())).thenReturn(map);
        when(activityCacheDomain.filterNoProductListPrice(anyMap(), Mockito.any())).thenReturn(map);
        
        when(expressionService.getActivityExpression(anyString())).thenReturn(ExpressionHelper.DEF_EXPROSSION);
        shoppingCartDomain.queryActivity(shoppingCart, map,false);

        // then
        assertTrue(shoppingCart.getActivityExpr().contains(activity.getActivityModel().getActivityCode()));
    }

    @Test
    public void 商品范围满数量减金额_券_指定属性() {

        // given
        // 创建购物车商品项列表
        List<ShoppingCartItem> shoppingCartItems = new ArrayList<>();
        // 创建属性
        shoppingCartItems.add(ShoppingCartFactory.createShoppingCartItemByAttribute(
            Arrays.asList(ProductAttribute.builder().attributeCode("01").attributeValues("02").build()), new BigDecimal(10), 2));
        // 创建购物车
        ShoppingCartDTO shoppingCart = ShoppingCartFactory.createShoppingCart("888", shoppingCartItems, null);
        Map<String, ActivityCacheDTO> map = new HashMap<>();
        // 创建活动范围
        List<ProductScope> products = new ArrayList<>();
        products.add(ProductScope.builder().attributes(Arrays.asList(ProductAttribute.builder().attributeCode("01").attributeValues("02").build())).build());
        // 创建活动模板
        Template template = Template.builder().templateEnum(TemplateEnum.T0201).condition("2").build();
        // 创建活动
        ActivityCacheDTO activity = CacheActivityFactory.createActivity(ActivityTypeEnum.COUPON, template, ProductSelectionEnum.SELECT, products, null, null);
        map.put(activity.getActivityModel().getActivityCode(), activity);
//        when(promoGroupRelationDomain.filterActivityByCoupon(any(), any())).thenReturn(map);
        // when
//        when(couponCodeUserDomain.isCanUse(anyString(), anyString(), anyString(), Mockito.anyMap())).thenReturn(activity.getActivityModel().getActivityCode());
        when(activityCacheDomain.filterActivityByProduct(anyMap(), any(ProductCodes.class), any())).thenReturn(map);
        when(activityCacheDomain.filterNoProductListPrice(anyMap(), Mockito.any())).thenReturn(map);
        
        when(expressionService.getActivityExpression(anyString())).thenReturn(ExpressionHelper.DEF_EXPROSSION);

        TPromoCouponInnerCodeVO innerCodeVO = new TPromoCouponInnerCodeVO();

        when(couponInnerCodeService.findActivityCodeByCouponCode(anyString(), anyString())).thenReturn(innerCodeVO);
        Mockito.when(couponCodeUserComponent.isCanUse(Mockito.any(), Mockito.any(),
                Mockito.any(),Mockito.any(), Mockito.anyBoolean())).thenReturn(activity.getActivityModel().getActivityCode());
        shoppingCartDomain.queryActivity(shoppingCart, map,false);

        // then
        assertTrue(shoppingCart.getActivityExpr().contains(activity.getActivityModel().getActivityCode()));
    }

    @Test(expected = PromotionParamValidateException.class)
    public void promoPassword_null() {

        // given
        // 创建购物车商品项列表
        List<ShoppingCartItem> shoppingCartItems = new ArrayList<>();
        // 创建属性
        shoppingCartItems.add(ShoppingCartFactory.createShoppingCartItemByAttribute(
                Arrays.asList(ProductAttribute.builder().attributeCode("01").attributeValues("02").build()), new BigDecimal(10), 2));
        // 创建购物车
        ShoppingCartDTO shoppingCart = ShoppingCartFactory.createShoppingCart("888", shoppingCartItems, null);
        Map<String, ActivityCacheDTO> map = new HashMap<>();
        // 创建活动范围
        List<ProductScope> products = new ArrayList<>();
        products.add(ProductScope.builder().attributes(Arrays.asList(ProductAttribute.builder().attributeCode("01").attributeValues("02").build())).build());
        // 创建活动模板
        Template template = Template.builder().templateEnum(TemplateEnum.T0201).condition("2").build();
        // 创建活动
        ActivityCacheDTO activity = CacheActivityFactory.createActivity(ActivityTypeEnum.COUPON, template, ProductSelectionEnum.SELECT, products, null, null);
        map.put(activity.getActivityModel().getActivityCode(), activity);
//        when(promoGroupRelationDomain.filterActivityByCoupon(any(), any())).thenReturn(map);
        // when
        when(activityCacheDomain.filterBlackProduct(Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(map);
        when(activityCacheDomain.filterActivityByProduct(Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(map);
        when(activityCacheDomain.filterNoProductListPrice(Mockito.any(),Mockito.any())).thenReturn(map);
        when(couponInnerCodeService.findActivityCodeByCouponCode(anyString(), anyString())).thenReturn(null);
        List<PromoPassVO> promoPassVOS = new ArrayList<>();
        PromoPassVO promoPassVO = new PromoPassVO();
        promoPassVO.setActivityCode(activity.getActivityModel().getActivityCode());
        promoPassVO.setCouponCode("1");
        promoPassVO.setPromoPassword("1");
        promoPassVOS.add(promoPassVO);
        when(couponInnerCodeService.findCouponCodeByPassword(anyString(), anyString())).thenReturn(promoPassVOS);
        when(activityService.findActivityNewByActivityCode(anyString(), any())).thenReturn(activity.getActivityModel().getActivityCode());

        Mockito.when(couponCodeUserComponent.isCanUse(Mockito.any(), Mockito.any(),
                Mockito.any(),Mockito.any(), Mockito.anyBoolean())).thenReturn(activity.getActivityModel().getActivityCode());
        shoppingCartDomain.queryActivity(shoppingCart, map,false);

        // then
        assertTrue(shoppingCart.getActivityExpr().contains(activity.getActivityModel().getActivityCode()));
    }

    @Test(expected = PromotionParamValidateException.class)
    public void promoPassword() {

        // given
        // 创建购物车商品项列表
        List<ShoppingCartItem> shoppingCartItems = new ArrayList<>();
        // 创建属性
        shoppingCartItems.add(ShoppingCartFactory.createShoppingCartItemByAttribute(
                Arrays.asList(ProductAttribute.builder().attributeCode("01").attributeValues("02").build()), new BigDecimal(10), 2));
        // 创建购物车
        ShoppingCartDTO shoppingCart = ShoppingCartFactory.createShoppingCart("888", shoppingCartItems, null);
        Map<String, ActivityCacheDTO> map = new HashMap<>();
        // 创建活动范围
        List<ProductScope> products = new ArrayList<>();
        products.add(ProductScope.builder().attributes(Arrays.asList(ProductAttribute.builder().attributeCode("01").attributeValues("02").build())).build());
        // 创建活动模板
        Template template = Template.builder().templateEnum(TemplateEnum.T0201).condition("2").build();
        // 创建活动
        ActivityCacheDTO activity = CacheActivityFactory.createActivity(ActivityTypeEnum.COUPON, template, ProductSelectionEnum.SELECT, products, null, null);
        map.put(activity.getActivityModel().getActivityCode(), activity);

        // when
//        when(promoGroupRelationDomain.filterActivityByCoupon(any(), any())).thenReturn(map);
//        when(couponInnerCodeService.findActivityCodeByCouponCode(anyString(), anyString())).thenReturn(null);
        when(activityCacheDomain.filterBlackProduct(Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(map);
        when(activityCacheDomain.filterActivityByProduct(Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(map);
        when(activityCacheDomain.filterNoProductListPrice(Mockito.any(),Mockito.any())).thenReturn(map);
        List<PromoPassVO> promoPassVOS = new ArrayList<>();
        PromoPassVO promoPassVO = new PromoPassVO();
        promoPassVO.setActivityCode("1");
        promoPassVO.setCouponCode("1");
        promoPassVO.setPromoPassword("1");
        promoPassVOS.add(promoPassVO);
        when(couponInnerCodeService.findCouponCodeByPassword(anyString(), anyString())).thenReturn(promoPassVOS);

        when(activityService.findActivityNewByActivityCode(anyString(), anyList())).thenReturn("1");

        Mockito.when(couponCodeUserComponent.isCanUse(Mockito.any(), Mockito.any(),
                Mockito.any(),Mockito.any(), Mockito.anyBoolean())).thenReturn(activity.getActivityModel().getActivityCode());
        shoppingCartDomain.queryActivity(shoppingCart, map,false);

        // then
        assertTrue(shoppingCart.getActivityExpr().contains(activity.getActivityModel().getActivityCode()));
    }


    @Test
    public void promoPassword11() {

        // given
        // 创建购物车商品项列表
        List<ShoppingCartItem> shoppingCartItems = new ArrayList<>();
        // 创建属性
        shoppingCartItems.add(ShoppingCartFactory.createShoppingCartItemByAttribute(
                Arrays.asList(ProductAttribute.builder().attributeCode("01").attributeValues("02").build()), new BigDecimal(10), 2));
        // 创建购物车
        ShoppingCartDTO shoppingCart = ShoppingCartFactory.createShoppingCart("888", shoppingCartItems, null);
        Map<String, ActivityCacheDTO> map = new HashMap<>();
        // 创建活动范围
        List<ProductScope> products = new ArrayList<>();
        products.add(ProductScope.builder().attributes(Arrays.asList(ProductAttribute.builder().attributeCode("01").attributeValues("02").build())).build());
        // 创建活动模板
        Template template = Template.builder().templateEnum(TemplateEnum.T0201).condition("2").build();
        // 创建活动
        ActivityCacheDTO activity = CacheActivityFactory.createActivity(ActivityTypeEnum.COUPON, template, ProductSelectionEnum.SELECT, products, null, null);
        map.put(activity.getActivityModel().getActivityCode(), activity);
//        when(promoGroupRelationDomain.filterActivityByCoupon(any(), any())).thenReturn(map);
        // when
//        when(couponCodeUserDomain.isCanUse(anyString(), anyString(), anyString(), anyMap())).thenReturn(activity.getActivityModel().getActivityCode());
        when(activityCacheDomain.filterActivityByProduct(anyMap(), any(ProductCodes.class), any())).thenReturn(map);
        when(expressionService.getActivityExpression(anyString())).thenReturn(ExpressionHelper.DEF_EXPROSSION);


        //when(couponInnerCodeService.findActivityCodeByCouponCode(anyString(), anyString())).thenReturn(null);

        List<PromoPassVO> promoPassVOS = new ArrayList<>();
        PromoPassVO promoPassVO = new PromoPassVO();
        promoPassVO.setActivityCode("1");
        promoPassVO.setCouponCode("1");
        promoPassVO.setPromoPassword("1");
        promoPassVOS.add(promoPassVO);
        when(couponInnerCodeService.findCouponCodeByPassword(anyString(), anyString())).thenReturn(promoPassVOS);

        when(activityService.findActivityNewByActivityCode(anyString(), anyList())).thenReturn("1");
        when(activityCacheDomain.filterNoProductListPrice(anyMap(), Mockito.any())).thenReturn(map);

        Mockito.when(couponCodeUserComponent.isCanUse(Mockito.any(), Mockito.any(),
                Mockito.any(),Mockito.any(), Mockito.anyBoolean())).thenReturn(activity.getActivityModel().getActivityCode());
        shoppingCartDomain.queryActivity(shoppingCart, map,false);

        // then
        assertTrue(shoppingCart.getActivityExpr().contains(activity.getActivityModel().getActivityCode()));
    }



    @Test(expected = PromotionParamValidateException.class)
    public void promoPassword12() {

        // given
        // 创建购物车商品项列表
        List<ShoppingCartItem> shoppingCartItems = new ArrayList<>();
        // 创建属性
        shoppingCartItems.add(ShoppingCartFactory.createShoppingCartItemByAttribute(
                Arrays.asList(ProductAttribute.builder().attributeCode("01").attributeValues("02").build()), new BigDecimal(10), 2));
        // 创建购物车
        ShoppingCartDTO shoppingCart = ShoppingCartFactory.createShoppingCart("888", shoppingCartItems, null);
        Map<String, ActivityCacheDTO> map = new HashMap<>();
        // 创建活动范围
        List<ProductScope> products = new ArrayList<>();
        products.add(ProductScope.builder().attributes(Arrays.asList(ProductAttribute.builder().attributeCode("01").attributeValues("02").build())).build());
        // 创建活动模板
        Template template = Template.builder().templateEnum(TemplateEnum.T0201).condition("2").build();
        // 创建活动
        ActivityCacheDTO activity = CacheActivityFactory.createActivity(ActivityTypeEnum.COUPON, template, ProductSelectionEnum.SELECT, products, null, null);
        map.put(activity.getActivityModel().getActivityCode(), activity);
//        when(promoGroupRelationDomain.filterActivityByCoupon(any(), any())).thenReturn(map);
        // when
        when(activityCacheDomain.filterBlackProduct(Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(map);
        when(activityCacheDomain.filterActivityByProduct(Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(map);
        when(activityCacheDomain.filterNoProductListPrice(Mockito.any(),Mockito.any())).thenReturn(map);

        when(couponInnerCodeService.findActivityCodeByCouponCode(anyString(), anyString())).thenReturn(null);

        List<PromoPassVO> promoPassVOS = new ArrayList<>();
        PromoPassVO promoPassVO = new PromoPassVO();
        promoPassVO.setActivityCode("1");
        promoPassVO.setCouponCode("1");
        promoPassVO.setPromoPassword("1");
        promoPassVOS.add(promoPassVO);

        when(couponInnerCodeService.findCouponCodeByPassword(anyString(), anyString())).thenReturn(promoPassVOS);
        when(activityService.findActivityNewByActivityCode(Mockito.any(),Mockito.any())).thenReturn("1");


        Mockito.when(couponCodeUserComponent.isCanUse(Mockito.any(), Mockito.any(),
                Mockito.any(),Mockito.any(), Mockito.anyBoolean())).thenReturn(activity.getActivityModel().getActivityCode());
        shoppingCartDomain.queryActivity(shoppingCart, map,false);

        // then
        assertTrue(shoppingCart.getActivityExpr().contains(activity.getActivityModel().getActivityCode()));
    }


    @Test
    public void 商品范围满金额减金额_指定分类() {

        // given
        // 创建购物车商品项列表
        List<ShoppingCartItem> shoppingCartItems = new ArrayList<>();
        shoppingCartItems.add(ShoppingCartFactory.createShoppingCartItemByCategory("0001", new BigDecimal(2), 3));
        // 创建购物车
        ShoppingCartDTO shoppingCart = ShoppingCartFactory.createShoppingCart(null, shoppingCartItems, null);

        Map<String, ActivityCacheDTO> map = new HashMap<>();
        // 创建活动范围
        List<ProductScope> products = new ArrayList<>();
        products.add(ProductScope.builder().categoryCode("0001").build());
        // 创建活动模板
        Template template = Template.builder().templateEnum(TemplateEnum.T0202).condition("1").reward("3").build();
        // 创建活动
        ActivityCacheDTO activity = CacheActivityFactory.createActivity(ActivityTypeEnum.ACTIVITY, template, ProductSelectionEnum.SELECT, products, null, null);
        map.put(activity.getActivityModel().getActivityCode(), activity);

//        when(promoGroupRelationDomain.filterActivityByCoupon(any(), any())).thenReturn(map);
        // when
        when(activityCacheDomain.filterActivityByProduct(anyMap(), any(ProductCodes.class), any())).thenReturn(map);
        when(activityCacheDomain.filterNoProductListPrice(anyMap(), Mockito.any())).thenReturn(map);
        
        
        when(expressionService.getActivityExpression(anyString())).thenReturn(ExpressionHelper.DEF_EXPROSSION);
        shoppingCartDomain.queryActivity(shoppingCart, map,false);

        // then
        assertTrue(shoppingCart.getActivityExpr().contains(activity.getActivityModel().getActivityCode()));
    }

    @Test
    public void 商品范围每满数量减金额_指定品牌() {

        // given
        // 创建购物车商品项列表
        List<ShoppingCartItem> shoppingCartItems = new ArrayList<>();
        shoppingCartItems.add(ShoppingCartFactory.createShoppingCartItemByBrand("0012", new BigDecimal(10), 1));
        // 创建购物车
        ShoppingCartDTO shoppingCart = ShoppingCartFactory.createShoppingCart(null, shoppingCartItems, null);

        Map<String, ActivityCacheDTO> map = new HashMap<>();
        // 创建活动范围
        List<ProductScope> products = new ArrayList<>();
        products.add(ProductScope.builder().brandCode("0012").build());
        // 创建活动模板
        Template template = Template.builder().templateEnum(TemplateEnum.T0203).condition("1").reward("3").build();
        // 创建活动
        ActivityCacheDTO activity = CacheActivityFactory.createActivity(ActivityTypeEnum.ACTIVITY, template, ProductSelectionEnum.SELECT, products, null, null);
        map.put(activity.getActivityModel().getActivityCode(), activity);
//        when(promoGroupRelationDomain.filterActivityByCoupon(any(), any())).thenReturn(map);
        // when
        when(activityCacheDomain.filterActivityByProduct(anyMap(), any(ProductCodes.class), any())).thenReturn(map);
        when(activityCacheDomain.filterNoProductListPrice(anyMap(), Mockito.any())).thenReturn(map);
        
        when(expressionService.getActivityExpression(anyString())).thenReturn(ExpressionHelper.DEF_EXPROSSION);
        shoppingCartDomain.queryActivity(shoppingCart, map,false);

        // then
        assertTrue(shoppingCart.getActivityExpr().contains(activity.getActivityModel().getActivityCode()));
    }

    @Test
    public void 商品范围每满金额减金额_指定sku() {

        // given
        // 创建购物车商品项列表
        List<ShoppingCartItem> shoppingCartItems = new ArrayList<>();
        shoppingCartItems.add(ShoppingCartFactory.createShoppingCartItemBySku("01", "02", new BigDecimal(10), 1));
        // 创建购物车
        ShoppingCartDTO shoppingCart = ShoppingCartFactory.createShoppingCart(null, shoppingCartItems, null);

        Map<String, ActivityCacheDTO> map = new HashMap<>();
        // 创建活动范围
        List<ActivityProductDetail> productDetails = new ArrayList<>();
        productDetails.add(ActivityProductDetail.builder().productCode("01").skuCode("02").build());
        // 创建活动模板
        Template template = Template.builder().templateEnum(TemplateEnum.T0204).condition("1").reward("3").build();
        // 创建活动
        ActivityCacheDTO activity = CacheActivityFactory.createActivity(ActivityTypeEnum.ACTIVITY, template, ProductSelectionEnum.SELECT, null, productDetails, null);
        map.put(activity.getActivityModel().getActivityCode(), activity);
//        when(promoGroupRelationDomain.filterActivityByCoupon(any(), any())).thenReturn(map);
        // when
        when(activityCacheDomain.filterActivityByProduct(anyMap(), any(ProductCodes.class), any())).thenReturn(map);
        when(activityCacheDomain.filterNoProductListPrice(anyMap(), Mockito.any())).thenReturn(map);
        
        when(expressionService.getActivityExpression(anyString())).thenReturn(ExpressionHelper.DEF_EXPROSSION);
        shoppingCartDomain.queryActivity(shoppingCart, map,false);

        // then
        assertTrue(shoppingCart.getActivityExpr().contains(activity.getActivityModel().getActivityCode()));
    }

    @Test
    public void 订单满数量减金额_指定sku() {

        // given
        // 创建购物车商品项列表
        List<ShoppingCartItem> shoppingCartItems = new ArrayList<>();
        shoppingCartItems.add(ShoppingCartFactory.createShoppingCartItemBySku("01", "02", new BigDecimal(11), 2));
        // 创建购物车
        ShoppingCartDTO shoppingCart = ShoppingCartFactory.createShoppingCart(null, shoppingCartItems, null);

        Map<String, ActivityCacheDTO> map = new HashMap<>();
        // 创建活动范围
        List<ActivityProductDetail> productDetails = new ArrayList<>();
        productDetails.add(ActivityProductDetail.builder().productCode("01").skuCode("02").build());
        // 创建活动模板
        Template template = Template.builder().templateEnum(TemplateEnum.T0205).condition("2").reward("3").build();
        // 创建活动
        ActivityCacheDTO activity = CacheActivityFactory.createActivity(ActivityTypeEnum.ACTIVITY, template, ProductSelectionEnum.SELECT, null, productDetails, null);
        map.put(activity.getActivityModel().getActivityCode(), activity);
//        when(promoGroupRelationDomain.filterActivityByCoupon(any(), any())).thenReturn(map);
        // when
        when(activityCacheDomain.filterActivityByProduct(anyMap(), any(ProductCodes.class), any())).thenReturn(map);
        when(activityCacheDomain.filterNoProductListPrice(anyMap(), Mockito.any())).thenReturn(map);
        
        when(expressionService.getActivityExpression(anyString())).thenReturn(ExpressionHelper.DEF_EXPROSSION);
        shoppingCartDomain.queryActivity(shoppingCart, map,false);

        // then
        assertTrue(shoppingCart.getActivityExpr().contains(activity.getActivityModel().getActivityCode()));
    }

    @Test
    public void 订单满金额减金额_指定sku() {

        // given
        // 创建购物车商品项列表
        List<ShoppingCartItem> shoppingCartItems = new ArrayList<>();
        shoppingCartItems.add(ShoppingCartFactory.createShoppingCartItemBySku("01", "02", new BigDecimal(11), 2));
        // 创建购物车
        ShoppingCartDTO shoppingCart = ShoppingCartFactory.createShoppingCart(null, shoppingCartItems, null);

        Map<String, ActivityCacheDTO> map = new HashMap<>();
        // 创建活动范围
        List<ActivityProductDetail> productDetails = new ArrayList<>();
        productDetails.add(ActivityProductDetail.builder().productCode("01").skuCode("02").build());
        // 创建活动模板
        Template template = Template.builder().templateEnum(TemplateEnum.T0206).condition("2").reward("3").build();
        // 创建活动
        ActivityCacheDTO activity = CacheActivityFactory.createActivity(ActivityTypeEnum.ACTIVITY, template, ProductSelectionEnum.SELECT, null, productDetails, null);
        map.put(activity.getActivityModel().getActivityCode(), activity);
//        when(promoGroupRelationDomain.filterActivityByCoupon(any(), any())).thenReturn(map);
        // when
        when(activityCacheDomain.filterActivityByProduct(anyMap(), any(ProductCodes.class), any())).thenReturn(map);
        when(activityCacheDomain.filterNoProductListPrice(anyMap(), Mockito.any())).thenReturn(map);
        
        when(expressionService.getActivityExpression(anyString())).thenReturn(ExpressionHelper.DEF_EXPROSSION);
        shoppingCartDomain.queryActivity(shoppingCart, map,false);

        // then
        assertTrue(shoppingCart.getActivityExpr().contains(activity.getActivityModel().getActivityCode()));
    }

    @Test
    public void 商品范围满数量打折扣_指定sku() {

        // given
        // 创建购物车商品项列表
        List<ShoppingCartItem> shoppingCartItems = new ArrayList<>();
        shoppingCartItems.add(ShoppingCartFactory.createShoppingCartItemBySku("01", "02", new BigDecimal(11), 2));
        // 创建购物车
        ShoppingCartDTO shoppingCart = ShoppingCartFactory.createShoppingCart(null, shoppingCartItems, null);

        Map<String, ActivityCacheDTO> map = new HashMap<>();
        // 创建活动范围
        List<ActivityProductDetail> productDetails = new ArrayList<>();
        productDetails.add(ActivityProductDetail.builder().productCode("01").skuCode("02").build());
        // 创建活动模板
        Template template = Template.builder().templateEnum(TemplateEnum.T0301).condition("2").reward("3").build();
        // 创建活动
        ActivityCacheDTO activity = CacheActivityFactory.createActivity(ActivityTypeEnum.ACTIVITY, template, ProductSelectionEnum.SELECT, null, productDetails, null);
        map.put(activity.getActivityModel().getActivityCode(), activity);
//        when(promoGroupRelationDomain.filterActivityByCoupon(any(), any())).thenReturn(map);
        // when
        when(activityCacheDomain.filterActivityByProduct(anyMap(), any(ProductCodes.class), any())).thenReturn(map);
        when(activityCacheDomain.filterNoProductListPrice(anyMap(), Mockito.any())).thenReturn(map);
        
        when(expressionService.getActivityExpression(anyString())).thenReturn(ExpressionHelper.DEF_EXPROSSION);
        shoppingCartDomain.queryActivity(shoppingCart, map,false);

        // then
        assertTrue(shoppingCart.getActivityExpr().contains(activity.getActivityModel().getActivityCode()));
    }

    @Test
    public void 商品范围满金额打折扣_指定sku() {

        // given
        // 创建购物车商品项列表
        List<ShoppingCartItem> shoppingCartItems = new ArrayList<>();
        shoppingCartItems.add(ShoppingCartFactory.createShoppingCartItemBySku("01", "02", new BigDecimal(11), 2));
        // 创建购物车
        ShoppingCartDTO shoppingCart = ShoppingCartFactory.createShoppingCart(null, shoppingCartItems, null);

        Map<String, ActivityCacheDTO> map = new HashMap<>();
        // 创建活动范围
        List<ActivityProductDetail> productDetails = new ArrayList<>();
        productDetails.add(ActivityProductDetail.builder().productCode("01").skuCode("02").build());
        // 创建活动模板
        Template template = Template.builder().templateEnum(TemplateEnum.T0302).condition("2").reward("3").build();
        // 创建活动
        ActivityCacheDTO activity = CacheActivityFactory.createActivity(ActivityTypeEnum.ACTIVITY, template, ProductSelectionEnum.SELECT, null, productDetails, null);
        map.put(activity.getActivityModel().getActivityCode(), activity);
//        when(promoGroupRelationDomain.filterActivityByCoupon(any(), any())).thenReturn(map);
        // when
        when(activityCacheDomain.filterActivityByProduct(anyMap(), any(ProductCodes.class), any())).thenReturn(map);
        when(activityCacheDomain.filterNoProductListPrice(anyMap(), Mockito.any())).thenReturn(map);
        
        when(expressionService.getActivityExpression(anyString())).thenReturn(ExpressionHelper.DEF_EXPROSSION);
        shoppingCartDomain.queryActivity(shoppingCart, map,false);

        // then
        assertTrue(shoppingCart.getActivityExpr().contains(activity.getActivityModel().getActivityCode()));
    }

    @Test
    public void 商品范围第数量打折扣_指定sku() {

        // given
        // 创建购物车商品项列表
        List<ShoppingCartItem> shoppingCartItems = new ArrayList<>();
        shoppingCartItems.add(ShoppingCartFactory.createShoppingCartItemBySku("01", "02", new BigDecimal(11), 2));
        // 创建购物车
        ShoppingCartDTO shoppingCart = ShoppingCartFactory.createShoppingCart(null, shoppingCartItems, null);

        Map<String, ActivityCacheDTO> map = new HashMap<>();
        // 创建活动范围
        List<ActivityProductDetail> productDetails = new ArrayList<>();
        productDetails.add(ActivityProductDetail.builder().productCode("01").skuCode("02").build());
        // 创建活动模板
        Template template = Template.builder().templateEnum(TemplateEnum.T0303).condition("2").reward("3").build();
        // 创建活动
        ActivityCacheDTO activity = CacheActivityFactory.createActivity(ActivityTypeEnum.ACTIVITY, template, ProductSelectionEnum.SELECT, null, productDetails, null);
        map.put(activity.getActivityModel().getActivityCode(), activity);
//        when(promoGroupRelationDomain.filterActivityByCoupon(any(), any())).thenReturn(map);
        // when
        when(activityCacheDomain.filterActivityByProduct(anyMap(), any(ProductCodes.class), any())).thenReturn(map);
        when(activityCacheDomain.filterNoProductListPrice(anyMap(), Mockito.any())).thenReturn(map);
        
        when(expressionService.getActivityExpression(anyString())).thenReturn(ExpressionHelper.DEF_EXPROSSION);
        shoppingCartDomain.queryActivity(shoppingCart, map,false);

        // then
        assertTrue(shoppingCart.getActivityExpr().contains(activity.getActivityModel().getActivityCode()));
    }

    @Test
    public void 订单满数量打折扣_指定sku() {

        // given
        // 创建购物车商品项列表
        List<ShoppingCartItem> shoppingCartItems = new ArrayList<>();
        shoppingCartItems.add(ShoppingCartFactory.createShoppingCartItemBySku("01", "02", new BigDecimal(11), 2));
        // 创建购物车
        ShoppingCartDTO shoppingCart = ShoppingCartFactory.createShoppingCart(null, shoppingCartItems, null);

        Map<String, ActivityCacheDTO> map = new HashMap<>();
        // 创建活动范围
        List<ActivityProductDetail> productDetails = new ArrayList<>();
        productDetails.add(ActivityProductDetail.builder().productCode("01").skuCode("02").build());
        // 创建活动模板
        Template template = Template.builder().templateEnum(TemplateEnum.T0304).condition("2").reward("3").build();
        // 创建活动
        ActivityCacheDTO activity = CacheActivityFactory.createActivity(ActivityTypeEnum.ACTIVITY, template, ProductSelectionEnum.SELECT, null, productDetails, null);
        map.put(activity.getActivityModel().getActivityCode(), activity);
//        when(promoGroupRelationDomain.filterActivityByCoupon(any(), any())).thenReturn(map);
        // when
        when(activityCacheDomain.filterActivityByProduct(anyMap(), any(ProductCodes.class), any())).thenReturn(map);
        when(activityCacheDomain.filterNoProductListPrice(anyMap(), Mockito.any())).thenReturn(map);
        
        when(expressionService.getActivityExpression(anyString())).thenReturn(ExpressionHelper.DEF_EXPROSSION);

//        Mockito.when(couponCodeUserComponent.isCanUse(Mockito.any(), Mockito.any(),
 //               Mockito.any(),Mockito.any())).thenReturn(activity.getActivityModel().getActivityCode());


        shoppingCartDomain.queryActivity(shoppingCart, map,false);

        // then
        assertTrue(shoppingCart.getActivityExpr().contains(activity.getActivityModel().getActivityCode()));
    }

    @Test
    public void 订单满金额打折扣_指定品牌() {

        // given
        // 创建购物车商品项列表
        List<ShoppingCartItem> shoppingCartItems = new ArrayList<>();
        shoppingCartItems.add(ShoppingCartFactory.createShoppingCartItemByBrand("0012", new BigDecimal(10), 1));
        // 创建购物车
        ShoppingCartDTO shoppingCart = ShoppingCartFactory.createShoppingCart(null, shoppingCartItems, null);

        Map<String, ActivityCacheDTO> map = new HashMap<>();
        // 创建活动范围
        List<ProductScope> products = new ArrayList<>();
        products.add(ProductScope.builder().brandCode("0012").build());
        // 创建活动模板
        Template template = Template.builder().templateEnum(TemplateEnum.T0305).condition("2").reward("3").build();
        // 创建活动
        ActivityCacheDTO activity = CacheActivityFactory.createActivity(ActivityTypeEnum.ACTIVITY, template, ProductSelectionEnum.SELECT, products, null, null);
        map.put(activity.getActivityModel().getActivityCode(), activity);
//        when(promoGroupRelationDomain.filterActivityByCoupon(any(), any())).thenReturn(map);
        // when
        when(activityCacheDomain.filterActivityByProduct(anyMap(), any(ProductCodes.class), any())).thenReturn(map);
        when(activityCacheDomain.filterNoProductListPrice(anyMap(), Mockito.any())).thenReturn(map);
        
        when(expressionService.getActivityExpression(anyString())).thenReturn(ExpressionHelper.DEF_EXPROSSION);
        shoppingCartDomain.queryActivity(shoppingCart, map,false);

        // then
        assertTrue(shoppingCart.getActivityExpr().contains(activity.getActivityModel().getActivityCode()));
    }

    @Test
    public void 商品范围满数量设为总计特价_指定指定品牌() {

        // given
        // 创建购物车商品项列表
        List<ShoppingCartItem> shoppingCartItems = new ArrayList<>();
        shoppingCartItems.add(ShoppingCartFactory.createShoppingCartItemByBrand("0012", new BigDecimal(10), 1));
        // 创建购物车
        ShoppingCartDTO shoppingCart = ShoppingCartFactory.createShoppingCart(null, shoppingCartItems, null);

        Map<String, ActivityCacheDTO> map = new HashMap<>();
        // 创建活动范围
        List<ProductScope> products = new ArrayList<>();
        products.add(ProductScope.builder().brandCode("0012").build());
        // 创建活动模板
        Template template = Template.builder().templateEnum(TemplateEnum.T0401).condition("2").reward("3").build();
        // 创建活动
        ActivityCacheDTO activity = CacheActivityFactory.createActivity(ActivityTypeEnum.ACTIVITY, template, ProductSelectionEnum.SELECT, products, null, null);
        map.put(activity.getActivityModel().getActivityCode(), activity);
//        when(promoGroupRelationDomain.filterActivityByCoupon(any(), any())).thenReturn(map);
        // when
        when(activityCacheDomain.filterActivityByProduct(anyMap(), any(ProductCodes.class), any())).thenReturn(map);
        when(activityCacheDomain.filterNoProductListPrice(anyMap(), Mockito.any())).thenReturn(map);
        
        when(expressionService.getActivityExpression(anyString())).thenReturn(ExpressionHelper.DEF_EXPROSSION);
        shoppingCartDomain.queryActivity(shoppingCart, map,false);

        // then
        assertTrue(shoppingCart.getActivityExpr().contains(activity.getActivityModel().getActivityCode()));
    }

    @Test
    public void 商品范围每满数量设为总计特价_指定指定品牌() {

        // given
        // 创建购物车商品项列表
        List<ShoppingCartItem> shoppingCartItems = new ArrayList<>();
        shoppingCartItems.add(ShoppingCartFactory.createShoppingCartItemByBrand("0012", new BigDecimal(10), 1));
        // 创建购物车
        ShoppingCartDTO shoppingCart = ShoppingCartFactory.createShoppingCart(null, shoppingCartItems, null);

        Map<String, ActivityCacheDTO> map = new HashMap<>();
        // 创建活动范围
        List<ProductScope> products = new ArrayList<>();
        products.add(ProductScope.builder().brandCode("0012").build());
        // 创建活动模板
        Template template = Template.builder().templateEnum(TemplateEnum.T0402).condition("2").reward("3").build();
        // 创建活动
        ActivityCacheDTO activity = CacheActivityFactory.createActivity(ActivityTypeEnum.ACTIVITY, template, ProductSelectionEnum.SELECT, products, null, null);
        map.put(activity.getActivityModel().getActivityCode(), activity);
//        when(promoGroupRelationDomain.filterActivityByCoupon(any(), any())).thenReturn(map);
        // when
        when(activityCacheDomain.filterActivityByProduct(anyMap(), any(ProductCodes.class), any())).thenReturn(map);
        when(activityCacheDomain.filterNoProductListPrice(anyMap(), Mockito.any())).thenReturn(map);
        
        when(expressionService.getActivityExpression(anyString())).thenReturn(ExpressionHelper.DEF_EXPROSSION);
        shoppingCartDomain.queryActivity(shoppingCart, map,false);

        // then
        assertTrue(shoppingCart.getActivityExpr().contains(activity.getActivityModel().getActivityCode()));
    }

    @Test
    public void 订单满数量包邮_指定指定品牌() {

        // given
        // 创建购物车商品项列表
        List<ShoppingCartItem> shoppingCartItems = new ArrayList<>();
        shoppingCartItems.add(ShoppingCartFactory.createShoppingCartItemByBrand("0012", new BigDecimal(10), 1));
        // 创建购物车
        ShoppingCartDTO shoppingCart = ShoppingCartFactory.createShoppingCart(null, shoppingCartItems, null);

        Map<String, ActivityCacheDTO> map = new HashMap<>();
        // 创建活动范围
        List<ProductScope> products = new ArrayList<>();
        products.add(ProductScope.builder().brandCode("0012").build());
        // 创建活动模板
        Template template = Template.builder().templateEnum(TemplateEnum.T0501).condition("2").reward("3").build();
        // 创建活动
        ActivityCacheDTO activity = CacheActivityFactory.createActivity(ActivityTypeEnum.ACTIVITY, template, ProductSelectionEnum.SELECT, products, null, null);
        map.put(activity.getActivityModel().getActivityCode(), activity);
//        when(promoGroupRelationDomain.filterActivityByCoupon(any(), any())).thenReturn(map);
        // when
        when(activityCacheDomain.filterActivityByProduct(anyMap(), any(ProductCodes.class), any())).thenReturn(map);
        when(activityCacheDomain.filterNoProductListPrice(anyMap(), Mockito.any())).thenReturn(map);
        
        when(expressionService.getActivityExpression(anyString())).thenReturn(ExpressionHelper.DEF_EXPROSSION);
        shoppingCartDomain.queryActivity(shoppingCart, map,false);

        // then
        assertTrue(shoppingCart.getActivityExpr().contains(activity.getActivityModel().getActivityCode()));
    }

    @Test
    public void 订单满金额包邮_指定分类() {

        // given
        // 创建购物车商品项列表
        List<ShoppingCartItem> shoppingCartItems = new ArrayList<>();
        shoppingCartItems.add(ShoppingCartFactory.createShoppingCartItemByCategory("0001", new BigDecimal(2), 3));
        // 创建购物车
        ShoppingCartDTO shoppingCart = ShoppingCartFactory.createShoppingCart(null, shoppingCartItems, null);

        Map<String, ActivityCacheDTO> map = new HashMap<>();
        // 创建活动范围
        List<ProductScope> products = new ArrayList<>();
        products.add(ProductScope.builder().categoryCode("0001").build());
        // 创建活动模板
        Template template = Template.builder().templateEnum(TemplateEnum.T0502).condition("2").reward("3").build();
        // 创建活动
        ActivityCacheDTO activity = CacheActivityFactory.createActivity(ActivityTypeEnum.ACTIVITY, template, ProductSelectionEnum.SELECT, products, null, null);
        map.put(activity.getActivityModel().getActivityCode(), activity);
//        when(promoGroupRelationDomain.filterActivityByCoupon(any(), any())).thenReturn(map);
        // when
        when(activityCacheDomain.filterActivityByProduct(anyMap(), any(ProductCodes.class), any())).thenReturn(map);
        when(activityCacheDomain.filterNoProductListPrice(anyMap(), Mockito.any())).thenReturn(map);
        
        when(expressionService.getActivityExpression(anyString())).thenReturn(ExpressionHelper.DEF_EXPROSSION);
        shoppingCartDomain.queryActivity(shoppingCart, map,false);

        // then
        assertTrue(shoppingCart.getActivityExpr().contains(activity.getActivityModel().getActivityCode()));
    }

    @Test
    public void 订单满数量送赠品_指定分类() {

        // given
        // 创建购物车商品项列表
        List<ShoppingCartItem> shoppingCartItems = new ArrayList<>();
        shoppingCartItems.add(ShoppingCartFactory.createShoppingCartItemByCategory("0001", new BigDecimal(2), 3));
        // 创建购物车
        ShoppingCartDTO shoppingCart = ShoppingCartFactory.createShoppingCart(null, shoppingCartItems, null);

        Map<String, ActivityCacheDTO> map = new HashMap<>();
        // 创建活动范围
        List<ProductScope> products = new ArrayList<>();
        products.add(ProductScope.builder().categoryCode("0001").build());
        // 创建活动模板
        Template template = Template.builder().templateEnum(TemplateEnum.T0601).condition("2").reward("3").build();
        // 创建活动
        ActivityCacheDTO activity = CacheActivityFactory.createActivity(ActivityTypeEnum.ACTIVITY, template, ProductSelectionEnum.SELECT, products, null, null);
        map.put(activity.getActivityModel().getActivityCode(), activity);
//        when(promoGroupRelationDomain.filterActivityByCoupon(any(), any())).thenReturn(map);
        // when
        when(activityCacheDomain.filterActivityByProduct(anyMap(), any(ProductCodes.class), any())).thenReturn(map);
        when(activityCacheDomain.filterNoProductListPrice(anyMap(), Mockito.any())).thenReturn(map);
        
        when(expressionService.getActivityExpression(anyString())).thenReturn(ExpressionHelper.DEF_EXPROSSION);
        shoppingCartDomain.queryActivity(shoppingCart, map,false);

        // then
        assertTrue(shoppingCart.getActivityExpr().contains(activity.getActivityModel().getActivityCode()));
    }

    @Test
    public void 订单满金额送赠品_指定分类() {

        // given
        // 创建购物车商品项列表
        List<ShoppingCartItem> shoppingCartItems = new ArrayList<>();
        shoppingCartItems.add(ShoppingCartFactory.createShoppingCartItemByCategory("0001", new BigDecimal(2), 3));
        // 创建购物车
        ShoppingCartDTO shoppingCart = ShoppingCartFactory.createShoppingCart(null, shoppingCartItems, null);

        Map<String, ActivityCacheDTO> map = new HashMap<>();
        // 创建活动范围
        List<ProductScope> products = new ArrayList<>();
        products.add(ProductScope.builder().categoryCode("0001").build());
        // 创建活动模板
        Template template = Template.builder().templateEnum(TemplateEnum.T0602).condition("2").reward("3").build();
        // 创建活动
        ActivityCacheDTO activity = CacheActivityFactory.createActivity(ActivityTypeEnum.ACTIVITY, template, ProductSelectionEnum.SELECT, products, null, null);
        map.put(activity.getActivityModel().getActivityCode(), activity);
//        when(promoGroupRelationDomain.filterActivityByCoupon(any(), any())).thenReturn(map);
        // when
        when(activityCacheDomain.filterActivityByProduct(anyMap(), any(ProductCodes.class), any())).thenReturn(map);
        when(activityCacheDomain.filterNoProductListPrice(anyMap(), Mockito.any())).thenReturn(map);
        
        when(expressionService.getActivityExpression(anyString())).thenReturn(ExpressionHelper.DEF_EXPROSSION);
        shoppingCartDomain.queryActivity(shoppingCart, map,false);

        // then
        assertTrue(shoppingCart.getActivityExpr().contains(activity.getActivityModel().getActivityCode()));
    }

    @Test
    public void 商品范围满数量送赠品_指定分类() {

        // given
        // 创建购物车商品项列表
        List<ShoppingCartItem> shoppingCartItems = new ArrayList<>();
        shoppingCartItems.add(ShoppingCartFactory.createShoppingCartItemByCategory("0001", new BigDecimal(2), 3));
        // 创建购物车
        ShoppingCartDTO shoppingCart = ShoppingCartFactory.createShoppingCart(null, shoppingCartItems, null);

        Map<String, ActivityCacheDTO> map = new HashMap<>();
        // 创建活动范围
        List<ProductScope> products = new ArrayList<>();
        products.add(ProductScope.builder().categoryCode("0001").build());
        // 创建活动模板
        Template template = Template.builder().templateEnum(TemplateEnum.T0603).condition("2").reward("3").build();
        // 创建活动
        ActivityCacheDTO activity = CacheActivityFactory.createActivity(ActivityTypeEnum.ACTIVITY, template, ProductSelectionEnum.SELECT, products, null, null);
        map.put(activity.getActivityModel().getActivityCode(), activity);

        // when
        //when(promoGroupRelationDomain.filterActivityByCoupon(any(), any())).thenReturn(map);
        when(productDetailService.queryListByActivityCodesAndProductCodes(any(), any())).thenReturn(new ArrayList<>());
        when(activityCacheDomain.filterActivityByProduct(anyMap(), any(ProductCodes.class), any())).thenReturn(map);
        when(activityCacheDomain.filterNoProductListPrice(anyMap(), Mockito.any())).thenReturn(map);
        
        
        when(expressionService.getActivityExpression(anyString())).thenReturn(ExpressionHelper.DEF_EXPROSSION);
        shoppingCartDomain.queryActivity(shoppingCart, map,false);

        // then
        assertTrue(shoppingCart.getActivityExpr().contains(activity.getActivityModel().getActivityCode()));
    }

    @Test
    public void 商品范围满金额送赠品_指定分类() {

        // given
        // 创建购物车商品项列表
        List<ShoppingCartItem> shoppingCartItems = new ArrayList<>();
        shoppingCartItems.add(ShoppingCartFactory.createShoppingCartItemByCategory("0001", new BigDecimal(2), 3));
        // 创建购物车
        ShoppingCartDTO shoppingCart = ShoppingCartFactory.createShoppingCart(null, shoppingCartItems, null);

        Map<String, ActivityCacheDTO> map = new HashMap<>();
        // 创建活动范围
        List<ProductScope> products = new ArrayList<>();
        products.add(ProductScope.builder().categoryCode("0001").build());
        // 创建活动模板
        Template template = Template.builder().templateEnum(TemplateEnum.T0604).condition("2").reward("3").build();
        // 创建活动
        ActivityCacheDTO activity = CacheActivityFactory.createActivity(ActivityTypeEnum.ACTIVITY, template, ProductSelectionEnum.SELECT, products, null, null);
        map.put(activity.getActivityModel().getActivityCode(), activity);
//        when(promoGroupRelationDomain.filterActivityByCoupon(any(), any())).thenReturn(map);
        // when
        when(productDetailService.queryListByActivityCodesAndProductCodes(any(), any())).thenReturn(new ArrayList<>());
        when(activityCacheDomain.filterActivityByProduct(anyMap(), any(ProductCodes.class), any())).thenReturn(map);
        when(activityCacheDomain.filterNoProductListPrice(anyMap(), Mockito.any())).thenReturn(map);
        
        
        when(expressionService.getActivityExpression(anyString())).thenReturn(ExpressionHelper.DEF_EXPROSSION);
        shoppingCartDomain.queryActivity(shoppingCart, map,false);

        // then
        assertTrue(shoppingCart.getActivityExpr().contains(activity.getActivityModel().getActivityCode()));
    }

    @Test
    public void 商品范围每满数量送赠品_指定属性() {

        // given
        // 创建购物车商品项列表
        List<ShoppingCartItem> shoppingCartItems = new ArrayList<>();
        shoppingCartItems.add(ShoppingCartFactory.createShoppingCartItemByAttribute(
            Arrays.asList(ProductAttribute.builder().attributeCode("01").attributeValues("02").build()), new BigDecimal(10), 2));
        // 创建购物车
        ShoppingCartDTO shoppingCart = ShoppingCartFactory.createShoppingCart(null, shoppingCartItems, null);

        Map<String, ActivityCacheDTO> map = new HashMap<>();
        // 创建活动范围
        List<ProductScope> products = new ArrayList<>();
        products.add(ProductScope.builder().attributes(Arrays.asList(ProductAttribute.builder().attributeCode("01").attributeValues("02").build())).build());
        // 创建活动模板
        Template template = Template.builder().templateEnum(TemplateEnum.T0605).condition("2").reward("3").build();
        // 创建活动
        ActivityCacheDTO activity = CacheActivityFactory.createActivity(ActivityTypeEnum.ACTIVITY, template, ProductSelectionEnum.SELECT, products, null, null);
        map.put(activity.getActivityModel().getActivityCode(), activity);
//        when(promoGroupRelationDomain.filterActivityByCoupon(any(), any())).thenReturn(map);
        // when
        when(productDetailService.queryListByActivityCodesAndProductCodes(any(), any())).thenReturn(new ArrayList<>());
        when(activityCacheDomain.filterActivityByProduct(anyMap(), any(ProductCodes.class), any())).thenReturn(map);
        when(activityCacheDomain.filterNoProductListPrice(anyMap(), Mockito.any())).thenReturn(map);
        
        
        when(expressionService.getActivityExpression(anyString())).thenReturn(ExpressionHelper.DEF_EXPROSSION);
        shoppingCartDomain.queryActivity(shoppingCart, map,false);

        // then
        assertTrue(shoppingCart.getActivityExpr().contains(activity.getActivityModel().getActivityCode()));
    }

    @Test
    public void 商品范围每满金额送赠品_指定属性() {

        // given
        // 创建购物车商品项列表
        List<ShoppingCartItem> shoppingCartItems = new ArrayList<>();
        shoppingCartItems.add(ShoppingCartFactory.createShoppingCartItemByAttribute(
            Arrays.asList(ProductAttribute.builder().attributeCode("01").attributeValues("02").build()), new BigDecimal(10), 2));
        // 创建购物车
        ShoppingCartDTO shoppingCart = ShoppingCartFactory.createShoppingCart(null, shoppingCartItems, null);

        Map<String, ActivityCacheDTO> map = new HashMap<>();
        // 创建活动范围
        List<ProductScope> products = new ArrayList<>();
        products.add(ProductScope.builder().attributes(Arrays.asList(ProductAttribute.builder().attributeCode("01").attributeValues("02").build())).build());
        // 创建活动模板
        Template template = Template.builder().templateEnum(TemplateEnum.T0606).condition("2").reward("3").build();
        // 创建活动
        ActivityCacheDTO activity = CacheActivityFactory.createActivity(ActivityTypeEnum.ACTIVITY, template, ProductSelectionEnum.SELECT, products, null, null);
        map.put(activity.getActivityModel().getActivityCode(), activity);
//        when(promoGroupRelationDomain.filterActivityByCoupon(any(), any())).thenReturn(map);
        // when
        when(productDetailService.queryListByActivityCodesAndProductCodes(any(), any())).thenReturn(new ArrayList<>());
        when(activityCacheDomain.filterActivityByProduct(anyMap(), any(ProductCodes.class), any())).thenReturn(map);
        when(activityCacheDomain.filterNoProductListPrice(anyMap(), Mockito.any())).thenReturn(map);
        
        
        when(expressionService.getActivityExpression(anyString())).thenReturn(ExpressionHelper.DEF_EXPROSSION);
        shoppingCartDomain.queryActivity(shoppingCart, map,false);

        // then
        assertTrue(shoppingCart.getActivityExpr().contains(activity.getActivityModel().getActivityCode()));
    }

    @Test
    public void 多组商品范围各选1件设为总计特价_指定属性() {

        // given
        // 创建购物车商品项列表
        List<ShoppingCartItem> shoppingCartItems = new ArrayList<>();
        shoppingCartItems.add(ShoppingCartFactory.createShoppingCartItemByAttribute(
            Arrays.asList(ProductAttribute.builder().attributeCode("01").attributeValues("02").build()), new BigDecimal(10), 2));
        // 创建购物车
        ShoppingCartDTO shoppingCart = ShoppingCartFactory.createShoppingCart(null, shoppingCartItems, null);

        Map<String, ActivityCacheDTO> map = new HashMap<>();
        // 创建活动范围
        List<ProductScope> products = new ArrayList<>();
        products.add(ProductScope.builder().attributes(Arrays.asList(ProductAttribute.builder().attributeCode("01").attributeValues("02").build())).build());
        // 创建活动模板
        Template template = Template.builder().templateEnum(TemplateEnum.T0701).condition("2").reward("3").build();
        // 创建活动
        ActivityCacheDTO activity = CacheActivityFactory.createActivity(ActivityTypeEnum.ACTIVITY, template, ProductSelectionEnum.SELECT, products, null, null);
        map.put(activity.getActivityModel().getActivityCode(), activity);
//        when(promoGroupRelationDomain.filterActivityByCoupon(any(), any())).thenReturn(map);
        // when
        when(productDetailService.queryListByActivityCodesAndProductCodes(any(), any())).thenReturn(new ArrayList<>());
        when(activityCacheDomain.filterActivityByProduct(anyMap(), any(ProductCodes.class), any())).thenReturn(map);
        when(activityCacheDomain.filterNoProductListPrice(anyMap(), Mockito.any())).thenReturn(map);
        
        
        when(expressionService.getActivityExpression(anyString())).thenReturn(ExpressionHelper.DEF_EXPROSSION);
        shoppingCartDomain.queryActivity(shoppingCart, map,false);

        // then
        assertTrue(shoppingCart.getActivityExpr().contains(activity.getActivityModel().getActivityCode()));
    }

    @Test
    public void 商品范围每满数量送同类商品_指定属性() {

        // given
        // 创建购物车商品项列表
        List<ShoppingCartItem> shoppingCartItems = new ArrayList<>();
        shoppingCartItems.add(ShoppingCartFactory.createShoppingCartItemByAttribute(
            Arrays.asList(ProductAttribute.builder().attributeCode("01").attributeValues("02").build()), new BigDecimal(10), 2));
        // 创建购物车
        ShoppingCartDTO shoppingCart = ShoppingCartFactory.createShoppingCart(null, shoppingCartItems, null);

        Map<String, ActivityCacheDTO> map = new HashMap<>();
        // 创建活动范围
        List<ProductScope> products = new ArrayList<>();
        products.add(ProductScope.builder().attributes(Arrays.asList(ProductAttribute.builder().attributeCode("01").attributeValues("02").build())).build());
        // 创建活动模板
        Template template = Template.builder().templateEnum(TemplateEnum.T0801).condition("2").reward("3").build();
        // 创建活动
        ActivityCacheDTO activity = CacheActivityFactory.createActivity(ActivityTypeEnum.ACTIVITY, template, ProductSelectionEnum.SELECT, products, null, null);
        map.put(activity.getActivityModel().getActivityCode(), activity);
//        when(promoGroupRelationDomain.filterActivityByCoupon(any(), any())).thenReturn(map);
        // when
        when(productDetailService.queryListByActivityCodesAndProductCodes(any(), any())).thenReturn(new ArrayList<>());
        when(activityCacheDomain.filterActivityByProduct(anyMap(), any(ProductCodes.class), any())).thenReturn(map);
        when(activityCacheDomain.filterNoProductListPrice(anyMap(), Mockito.any())).thenReturn(map);
        
        
        when(expressionService.getActivityExpression(anyString())).thenReturn(ExpressionHelper.DEF_EXPROSSION);
        shoppingCartDomain.queryActivity(shoppingCart, map,false);

        // then
        assertTrue(shoppingCart.getActivityExpr().contains(activity.getActivityModel().getActivityCode()));
    }

    @Test
    public void 商品范围每满数量送不同类商品_指定属性() {

        // given
        // 创建购物车商品项列表
        List<ShoppingCartItem> shoppingCartItems = new ArrayList<>();
        shoppingCartItems.add(ShoppingCartFactory.createShoppingCartItemByAttribute(
            Arrays.asList(ProductAttribute.builder().attributeCode("01").attributeValues("02").build()), new BigDecimal(10), 2));
        // 创建购物车
        ShoppingCartDTO shoppingCart = ShoppingCartFactory.createShoppingCart(null, shoppingCartItems, null);

        Map<String, ActivityCacheDTO> map = new HashMap<>();
        // 创建活动范围
        List<ProductScope> products = new ArrayList<>();
        products.add(ProductScope.builder().attributes(Arrays.asList(ProductAttribute.builder().attributeCode("01").attributeValues("02").build())).build());
        // 创建活动模板
        Template template = Template.builder().templateEnum(TemplateEnum.T0802).condition("2").reward("3").build();
        // 创建活动
        ActivityCacheDTO activity = CacheActivityFactory.createActivity(ActivityTypeEnum.ACTIVITY, template, ProductSelectionEnum.SELECT, products, null, null);
        map.put(activity.getActivityModel().getActivityCode(), activity);
//        when(promoGroupRelationDomain.filterActivityByCoupon(any(), any())).thenReturn(map);
        // when
        when(activityCacheDomain.filterActivityByProduct(anyMap(), any(ProductCodes.class), any())).thenReturn(map);
        when(activityCacheDomain.filterNoProductListPrice(anyMap(), Mockito.any())).thenReturn(map);
        
        when(expressionService.getActivityExpression(anyString())).thenReturn(ExpressionHelper.DEF_EXPROSSION);
        shoppingCartDomain.queryActivity(shoppingCart, map,false);

        // then
        assertTrue(shoppingCart.getActivityExpr().contains(activity.getActivityModel().getActivityCode()));
    }

    @Test(expected = PromotionException.class)
    public void filterCoupon() {

        // given
        List<PromoPassVO> promoPassVOS = new ArrayList<>();
        // when
        when(couponInnerCodeService.findCouponCodeByPassword(anyString(),anyString())).thenReturn(promoPassVOS);


        String s = shoppingCartDomain.filterCoupon("1", "1");

        // then

    }

    @Test(expected = PromotionException.class)
    public void filterCoupon1() {

        // given
        List<PromoPassVO> promoPassVOS = new ArrayList<>();

        PromoPassVO promoPassVO = new PromoPassVO();
        promoPassVO.setActivityCode("1");
        promoPassVO.setCouponCode("1");
        promoPassVOS.add(promoPassVO);
        // when
        when(couponInnerCodeService.findCouponCodeByPassword(anyString(),anyString())).thenReturn(promoPassVOS);

        when(activityService.findActivityNewByActivityCode(anyString(),anyList())).thenReturn(null);


        String s = shoppingCartDomain.filterCoupon("1", "1");
        Assert.assertEquals(null,s);

        // then

    }

    @Test
    public void filterCoupon2() {

        // given
        List<PromoPassVO> promoPassVOS = new ArrayList<>();

        PromoPassVO promoPassVO = new PromoPassVO();
        promoPassVO.setActivityCode("1");
        promoPassVO.setCouponCode("1");
        promoPassVOS.add(promoPassVO);
        // when
        when(couponInnerCodeService.findCouponCodeByPassword(anyString(),anyString())).thenReturn(promoPassVOS);

        when(activityService.findActivityNewByActivityCode(anyString(),anyList())).thenReturn("null");


        String s = shoppingCartDomain.filterCoupon("1", "1");
        Assert.assertEquals(null,s);

        // then

    }
}
