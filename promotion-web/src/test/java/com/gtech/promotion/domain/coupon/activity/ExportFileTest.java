/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.domain.coupon.activity;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.ObjectOutputStream;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.junit.Before;
import org.junit.FixMethodOrder;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.junit.runners.MethodSorters;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.transaction.annotation.Transactional;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.gtech.commons.page.PageData;
import com.gtech.commons.utils.DateUtil;
import com.gtech.promotion.PromotionWebApplication;
import com.gtech.promotion.calc.TemplateEnum;
import com.gtech.promotion.component.coupon.CouponInnerCodeComponent;
import com.gtech.promotion.component.impl.feign.BasicClientDomainImpl;
import com.gtech.promotion.dao.model.activity.ActivityModel;
import com.gtech.promotion.dao.model.activity.TPromoActivityStoreVO;
import com.gtech.promotion.dao.model.activity.TemplateModel;
import com.gtech.promotion.dao.model.coupon.CouponReleaseModel;
import com.gtech.promotion.dao.model.coupon.TPromoCouponCodeUserVO;
import com.gtech.promotion.dao.model.coupon.TPromoCouponInnerCodeVO;
import com.gtech.promotion.domain.coupon.CouponReleaseDomain;
import com.gtech.promotion.dto.in.activity.ManagementDataInDTO;
import com.gtech.promotion.dto.in.coupon.TCouponListQueryDTO;
import com.gtech.promotion.dto.out.coupon.CouponInfoImportOutDTO;
import com.gtech.promotion.helper.BaseControllerTests;
import com.gtech.promotion.helper.bean.Template;
import com.gtech.promotion.helper.factory.ActivityFactory;
import com.gtech.promotion.service.impl.activity.ActivityServiceImpl;
import com.gtech.promotion.service.impl.coupon.PromoCouponCodeUserServiceImpl;
import com.gtech.promotion.service.impl.coupon.PromoCouponInnerCodeServiceImpl;
import com.gtech.promotion.service.impl.coupon.PromoCouponReleaseServiceImpl;

/**
 * 测试券活动创建
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = { PromotionWebApplication.class })
@FixMethodOrder(MethodSorters.NAME_ASCENDING)
@ActiveProfiles("dev")
@Transactional
public class ExportFileTest extends BaseControllerTests {

    @Test
    public void test000setup() {

        super.setup();
    }

    // 1、有无奖励 限制   incentiveLimitedFlag 00无限制  01有限制
    // 2、有无渠道 限制   storeType 店铺范围：00-全店铺 01-自定义
    // 3、有无会员 等级限制   members
    // 商品范围： productType  00-全商品 01-指定范围 02-指定商品（上传文件）  03-指定多个商品范围
    // 4、商品：全商品  
    // 5、商品：指定商品范围
    // 6、商品：指定商品sku
    // 7、商品：指定多个商品范围
    // 8、正反选 productSelectType商品范围正反选：01-正选；02-反选

    @InjectMocks
    private CouponInnerCodeComponent couponInnerCodeComponent;

    @Autowired
    private CouponInnerCodeComponent couponInnerCodeComponent1;

    @Mock
    private PromoCouponReleaseServiceImpl promoCouponReleaseService;

    @Mock
    private PromoCouponCodeUserServiceImpl promoCouponCodeUserService;

    @Mock
    private ActivityServiceImpl activityService;

    @Mock
    private PromoCouponInnerCodeServiceImpl promoCouponInnerCodeService;

    @Mock
    private BasicClientDomainImpl basicClientDomain;

    Map<String, String> map = new HashMap<>();
    Map<String, Map<String, String>> hashMap = new HashMap<>();

    @Before
    public void befor(){
        MockitoAnnotations.initMocks(this);
        map.put("01", "未冻结");
        
        map.put("01", "券各类状态");
        hashMap.put("couponType", map);
        hashMap.put("couponTakeLabel", map);
        hashMap.put("couponStatus", map);
        hashMap.put("couponFrozenStatus", map);
    }

    @Test
    public void test100导出_优惠_匿名券(){
        //given
        TCouponListQueryDTO tCouponListQueryDTO = ActivityFactory.exportFile();
        List<CouponInfoImportOutDTO> couponInfoImportOutDTOs = ActivityFactory.couponInfoImportOutDTOs();
        Template template = Template.builder().templateEnum(TemplateEnum.T0304).condition("2").reward("5").build();
        TemplateModel templateVO = new TemplateModel();
        templateVO.setTemplateCode(template.getTemplateEnum().code());
        ActivityModel tPromoActivityVO = new ActivityModel();
        tCouponListQueryDTO.setActivityCode("1");
        tPromoActivityVO.setActivityBegin("20180611170850");
        Date date = DateUtil.addDay(new Date(), 5);
        String string = DateUtil.format(date, DateUtil.FORMAT_YYYYMMDDHHMISS_14);
        tPromoActivityVO.setActivityEnd(string);
        List<ActivityModel> arrayList = new ArrayList<ActivityModel>();
        arrayList.add(tPromoActivityVO);
        ActivityModel activityModel = ActivityFactory.createCouponActivityVO111("01", "1");
        activityModel.setUserLimitMax(2);
        List<TPromoActivityStoreVO> storeVOs = new ArrayList<TPromoActivityStoreVO>();
        TPromoActivityStoreVO storeVO = new TPromoActivityStoreVO();
        storeVO.setOrgCode("1");
        storeVOs.add(storeVO);
        ArrayList<CouponReleaseModel> releaseVOs = new ArrayList<CouponReleaseModel>();
        CouponReleaseModel releaseVO = new CouponReleaseModel();
        Date release = DateUtil.addDay(new Date(), 1);
        String dateString = DateUtil.format(release, DateUtil.FORMAT_YYYYMMDDHHMISS_14);
        releaseVO.setReceiveEndTime(dateString);
        releaseVO.setReceiveStartTime("20180611170850");
        releaseVO.setReleaseQuantity(5);
        releaseVO.setReleaseType("01");
        releaseVO.setReleaseCode("1");
        releaseVOs.add(releaseVO);
        //when
        when(activityService.findActivityByActivityCode(anyString(), anyString())).thenReturn(tPromoActivityVO);
        when(promoCouponReleaseService.queryCouponRelease(anyString())).thenReturn(releaseVOs);//投放集合
        when(promoCouponInnerCodeService.selectCouponCodeList(any(), any(), any())).thenReturn(couponInfoImportOutDTOs).thenReturn(Collections.emptyList());//导出数据集合

        // then
        try (ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream(); ObjectOutputStream objectOutputStream = new ObjectOutputStream(byteArrayOutputStream)){
            couponInnerCodeComponent.exportCouponInfos(tCouponListQueryDTO, objectOutputStream, map, map);
        }catch (IOException e){
            e.printStackTrace();
        }
        PageHelper.clearPage();
    }

    @Test
    public void test100导出优惠码(){
        //given
        TCouponListQueryDTO tCouponListQueryDTO = ActivityFactory.exportFile();
        Template template = Template.builder().templateEnum(TemplateEnum.T0304).condition("2").reward("5").build();
        TemplateModel templateVO = new TemplateModel();
        templateVO.setTemplateCode(template.getTemplateEnum().code());
        ActivityModel tPromoActivityVO = new ActivityModel();
        tCouponListQueryDTO.setActivityCode("1");
        tPromoActivityVO.setActivityBegin("20180611170850");
        Date date = DateUtil.addDay(new Date(), 5);
        String string = DateUtil.format(date, DateUtil.FORMAT_YYYYMMDDHHMISS_14);
        tPromoActivityVO.setActivityEnd(string);
        List<ActivityModel> arrayList = new ArrayList<ActivityModel>();
        arrayList.add(tPromoActivityVO);
        ActivityModel activityModel = ActivityFactory.createCouponActivityVO111("03", "1");
        activityModel.setUserLimitMax(2);
        List<TPromoActivityStoreVO> storeVOs = new ArrayList<TPromoActivityStoreVO>();
        TPromoActivityStoreVO storeVO = new TPromoActivityStoreVO();
        storeVO.setOrgCode("1");
        storeVOs.add(storeVO);
        ArrayList<CouponReleaseModel> releaseVOs = new ArrayList<CouponReleaseModel>();
        CouponReleaseModel releaseVO = new CouponReleaseModel();
        Date release = DateUtil.addDay(new Date(), 1);
        String dateString = DateUtil.format(release, DateUtil.FORMAT_YYYYMMDDHHMISS_14);
        releaseVO.setReceiveEndTime(dateString);
        releaseVO.setReceiveStartTime("20180611170850");
        releaseVO.setReleaseQuantity(5);
        releaseVO.setReleaseType("01");
        releaseVO.setReleaseCode("1");
        releaseVOs.add(releaseVO);
        PageInfo<TPromoCouponInnerCodeVO> couponInnerCode = ActivityFactory.couponInnerCode();
        List<TPromoCouponCodeUserVO> couponCodeUserVOs = ActivityFactory.couponCodeUserVOs();
        //when
        when(activityService.findActivityByActivityCode(anyString(), anyString())).thenReturn(tPromoActivityVO);
        when(promoCouponReleaseService.queryCouponRelease(anyString())).thenReturn(releaseVOs);//投放集合
        when(promoCouponInnerCodeService.selectCouponList(any(), any(), any(), any(), any(), any(), any())).thenReturn(couponInnerCode);
        when(promoCouponCodeUserService.getUserCouponInfos(any(), any(), any(), any())).thenReturn(couponCodeUserVOs).thenReturn(new ArrayList<>());

        try (ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream(); ObjectOutputStream objectOutputStream = new ObjectOutputStream(byteArrayOutputStream)){
            couponInnerCodeComponent.exportDiscountCodeInfos(tCouponListQueryDTO, objectOutputStream, map, map);
        }catch (IOException e){
            e.printStackTrace();
        }
        PageHelper.clearPage();
    }

    
    @Test
    public void test100根据查询条件导出优惠码_未使用(){
        //given
        ManagementDataInDTO  dataInDTO = new ManagementDataInDTO();
        dataInDTO.setTenantCode("55555");
        ActivityModel tPromoActivityVO = new ActivityModel();
        tPromoActivityVO.setTenantCode("55555");
        tPromoActivityVO.setActivityBegin("20180611170850");
        ArrayList<CouponReleaseDomain> releaseVOs = new ArrayList<>();
        CouponReleaseDomain releaseVO = new CouponReleaseDomain();
        Date release = DateUtil.addDay(new Date(), 1);
        String dateString = DateUtil.format(release, DateUtil.FORMAT_YYYYMMDDHHMISS_14);
        releaseVO.setReceiveEndTime(dateString);
        releaseVO.setReceiveStartTime("20180611170850");
        releaseVO.setReleaseTime("20180611170850");
        releaseVO.setReleaseQuantity(5);
        releaseVO.setReleaseType("01");
        releaseVOs.add(releaseVO);
        List<TPromoCouponInnerCodeVO> innerCodeVOs = new ArrayList<>();
        TPromoCouponInnerCodeVO vo = new TPromoCouponInnerCodeVO();
        vo.setTenantCode("55555");
        vo.setCouponCode("123das");
        vo.setCouponType("03");
        vo.setFrozenStatus("01");
        vo.setStatus("01");
        vo.setReleaseCode("1");
        innerCodeVOs.add(vo);
        PageData<TPromoCouponInnerCodeVO> list = new PageData<>(innerCodeVOs, 1L);
        //when
        when(promoCouponReleaseService.findCouponReleaseByReleaseCode(anyString(), anyString())).thenReturn(releaseVO);
        when(promoCouponInnerCodeService.findCouponCodeData(any())).thenReturn(list);
        //then
        try (ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream(); 
                        ObjectOutputStream objectOutputStream = new ObjectOutputStream(byteArrayOutputStream)){
            couponInnerCodeComponent.managementCouponCode(dataInDTO,tPromoActivityVO);
        }catch (IOException e){
            e.printStackTrace();
        }
        PageHelper.clearPage();
    }
    
    @Test
    public void test100根据查询条件导出优惠码_已使用(){
        //given
        ManagementDataInDTO  dataInDTO = new ManagementDataInDTO();
        dataInDTO.setTenantCode("55555");
        dataInDTO.setIsUseStatus("1");
        ActivityModel tPromoActivityVO = new ActivityModel();
        tPromoActivityVO.setTenantCode("55555");
        tPromoActivityVO.setActivityBegin("20180611170850");
        ArrayList<CouponReleaseDomain> releaseVOs = new ArrayList<>();
        CouponReleaseDomain releaseVO = new CouponReleaseDomain();
        Date release = DateUtil.addDay(new Date(), 1);
        String dateString = DateUtil.format(release, DateUtil.FORMAT_YYYYMMDDHHMISS_14);
        releaseVO.setReceiveEndTime(dateString);
        releaseVO.setReceiveStartTime("20180611170850");
        releaseVO.setReleaseTime("20180611170850");
        releaseVO.setReleaseQuantity(5);
        releaseVO.setReleaseType("01");
        releaseVOs.add(releaseVO);
        List<TPromoCouponCodeUserVO> userVOs = new ArrayList<>();
        TPromoCouponCodeUserVO vo = new TPromoCouponCodeUserVO();
        vo.setTenantCode("55555");
        vo.setCouponCode("123das");
        vo.setCouponType("03");
        vo.setFrozenStatus("01");
        vo.setStatus("01");
        vo.setReleaseCode("1");
        userVOs.add(vo);

        PageData<TPromoCouponCodeUserVO> list = new PageData<>(userVOs, 1L);
        //when
        when(promoCouponReleaseService.findCouponReleaseByReleaseCode(anyString(), anyString())).thenReturn(releaseVO);
        when(promoCouponCodeUserService.queryManagementCouponCodeData(any())).thenReturn(list);

        //then
        try (ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream(); 
                        ObjectOutputStream objectOutputStream = new ObjectOutputStream(byteArrayOutputStream)){
            couponInnerCodeComponent.managementCouponCode(dataInDTO,tPromoActivityVO);
        }catch (IOException e){
            e.printStackTrace();
        }
        PageHelper.clearPage();
    }
    
    @Test
    public void test100租户编码寄条件导出_优惠券_匿名券(){
        //given
        ManagementDataInDTO inDTO = new ManagementDataInDTO();
        inDTO.setTenantCode("100001");
        inDTO.setPageNo(1);
        inDTO.setPageCount(10);
        inDTO.setCouponTypes(Arrays.asList("01","02"));
        inDTO.setActivityCode("20200999185570537678N");

        // then
        try (ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream(); 
                        ObjectOutputStream objectOutputStream = new ObjectOutputStream(byteArrayOutputStream)){
            couponInnerCodeComponent1.exportManagementCodeInfos(inDTO,objectOutputStream,hashMap);
        }catch (IOException e){
            e.printStackTrace();
        }
        PageHelper.clearPage();
    }
    
}
