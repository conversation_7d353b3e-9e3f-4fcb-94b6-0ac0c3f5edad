/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion;

import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.test.context.junit4.SpringRunner;

import com.gtech.promotion.exception.ErrorCodes;
import com.gtech.promotion.exception.PromotionParamValidateException;
import com.gtech.promotion.helper.CodeHelper;
import com.gtech.promotion.helper.FunctionHelper;
import com.gtech.promotion.helper.TestUtils;
import com.gtech.promotion.utils.NumberUtil;

/**
 * CommonTest
 *
 * <AUTHOR>
 * @Date 2019-11-18
 */
@RunWith(SpringRunner.class)
public class CommonTest {

    @Test
    public void testEntity(){
        Assert.assertTrue(NumberUtil.checkDiscount("0.00"));
        Assert.assertTrue(NumberUtil.checkDiscount("1.00"));

        Assert.assertTrue(!NumberUtil.checkDiscount("-0.01"));
        Assert.assertTrue(!NumberUtil.checkDiscount("1.01"));
        Assert.assertTrue(!NumberUtil.checkDiscount("0.231"));

        Assert.assertTrue(NumberUtil.checkMoney("0.01", null));
        Assert.assertTrue(NumberUtil.checkMoney("99.99", null));
        Assert.assertTrue(!NumberUtil.checkMoney("0.00", null));
        Assert.assertTrue(!NumberUtil.checkMoney("11.231", null));
        
        TestUtils.testEntity("com.gtech.promotion.dto");
        TestUtils.testEntity("com.gtech.promotion.vo");
        TestUtils.testEntity("com.gtech.promotion.entity");
        TestUtils.testEntity("com.gtech.promotion.mongo");

        for(int i = 95; i < 105; i++) {
            System.err.println(CodeHelper.newActivityCode("" + i, "1"));
        }
    }

    @Test
    public void testFunctionHelper() {

        try {
            FunctionHelper.validateFunctionCode("", "0401");
        } catch (PromotionParamValidateException e) {
            Assert.assertTrue(ErrorCodes.PARAM_ERROR_TEMPLATE_CODE.toExceptionCode().equals(e.getCode()));
        }
        try {
            FunctionHelper.validateFunctionCode("010102010301040", "0401");
        } catch (PromotionParamValidateException e) {
            Assert.assertTrue(ErrorCodes.PARAM_ERROR_TEMPLATE_CODE.toExceptionCode().equals(e.getCode()));
        }
        try {
            FunctionHelper.validateFunctionCode("0101020103010401", "0402");
        } catch (PromotionParamValidateException e) {
            Assert.assertTrue(ErrorCodes.PARAM_ERROR_FUNCTION_CODE.toExceptionCode().equals(e.getCode()));
        }
        FunctionHelper.validateFunctionCode("0101020103010401", "0401");
    }
}
