package com.gtech.promotion.controller.flashsale;

import com.alibaba.fastjson.JSON;
import com.gtech.commons.result.Result;
import com.gtech.commons.utils.BeanCopyUtils;
import com.gtech.promotion.component.flashsale.DataSyncComponent;
import com.gtech.promotion.component.flashsale.FlashSaleComponent;
import com.gtech.promotion.domain.marketing.flashsale.FlashSaleDomain;
import com.gtech.promotion.dto.in.flashsale.QueryFlashSalePriceByProductInDto;
import com.gtech.promotion.vo.bean.marketing.flashsale.FlashSaleProduct;
import com.gtech.promotion.vo.param.marketing.flashsale.FlashSaleCreateParam;
import com.gtech.promotion.vo.param.marketing.flashsale.FlashSaleFindParam;
import com.gtech.promotion.vo.param.marketing.flashsale.FlashSaleUpdateParam;
import com.gtech.promotion.vo.param.marketing.flashsale.QueryFlashSalePriceByProductSyncParam;
import com.gtech.promotion.vo.result.flashsale.FlashSaleFindResult;
import com.gtech.promotion.vo.result.flashsale.QueryFlashSalePriceByProductResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.io.Serializable;
import java.util.List;

/**
 * 秒杀
 */
@Slf4j
@RestController
@Api(value = "Marketing Flash Sale API",tags = { "Marketing Flash Sale API" })
public class FlashSaleController {

    @Autowired
    private FlashSaleComponent flashSaleComponent;
    @Autowired
    private DataSyncComponent dataSyncComponent;

    @ApiOperation(value = "Create flash sale activity",notes = "Create flash sale activity")
    @PostMapping("/marketing/flashSale/create")
    public Result<String> createFlashSale(@RequestBody FlashSaleCreateParam param){
        log.info("创建秒杀活动:{{}}", JSON.toJSONString(param));
        param.validate();

        checkProduct(param.getProducts());

        FlashSaleDomain flashSaleDomain = BeanCopyUtils.jsonCopyBean(param, FlashSaleDomain.class);

        return Result.ok(flashSaleComponent.createFlashSale(flashSaleDomain));
    }

    @ApiOperation(value = "Update flash sale activity",notes = "Update flash sale activity")
    @PostMapping("/marketing/flashSale/update")
    public Result<Serializable> updateFlashSale(@RequestBody FlashSaleUpdateParam param){
        log.info("更新秒杀活动:{{}}", JSON.toJSONString(param));

        param.validate();

        checkProduct(param.getProducts());

        FlashSaleDomain flashSaleDomain = BeanCopyUtils.jsonCopyBean(param, FlashSaleDomain.class);
        flashSaleComponent.updateFlashSale(flashSaleDomain);
        return Result.ok();
    }

    public void checkProduct(List<FlashSaleProduct> products2) {
        List<FlashSaleProduct> products = products2;
        if (CollectionUtils.isNotEmpty(products)) {
            for (FlashSaleProduct product : products) {
                product.validate();
            }
        }
    }

    @ApiOperation(value = "Find flash sale activity information",notes = "Find flash sale activity information")
    @PostMapping("/marketing/flashSale/find")
    public Result<FlashSaleFindResult> findFlashSale(@RequestBody FlashSaleFindParam param){
        param.validate();
        FlashSaleDomain flashSaleDomain = BeanCopyUtils.jsonCopyBean(param, FlashSaleDomain.class);
        FlashSaleFindResult flashSale = flashSaleComponent.findFlashSale(flashSaleDomain.getActivityCode());
        return Result.ok(flashSale);
    }

    @ApiOperation(value = "Find flash sale activity information",notes = "Find flash sale activity information")
    @PostMapping("/marketing/flashSale/price/sync")
    public Result<List<QueryFlashSalePriceByProductResult>> queryFlashSalePriceByProductSync(@RequestBody QueryFlashSalePriceByProductSyncParam param){
        param.validate();

        QueryFlashSalePriceByProductInDto queryFlashSalePriceByProductInDto = BeanCopyUtils.jsonCopyBean(param, QueryFlashSalePriceByProductInDto.class);
        return Result.ok(BeanCopyUtils.jsonCopyList(dataSyncComponent.queryFlashSalePriceByProduct(queryFlashSalePriceByProductInDto), QueryFlashSalePriceByProductResult.class));
    }

    //定时同步秒杀价格到pim
    @ApiOperation(value = "Timing synchronizes the second kill price to PIM",notes = "Timing synchronizes the second kill price to PIM")
    @PostMapping(value = "/marketing/flashSale/price/sync/timer")
    public Result<Serializable> syncPriceTimer() {
        dataSyncComponent.syncPriceTimer();
        return Result.ok();
    }
}
