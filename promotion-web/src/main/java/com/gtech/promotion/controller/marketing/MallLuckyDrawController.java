package com.gtech.promotion.controller.marketing;

import com.alibaba.fastjson.JSONObject;
import com.gtech.commons.page.PageData;
import com.gtech.commons.result.PageResult;
import com.gtech.commons.result.Result;
import com.gtech.commons.utils.BeanCopyUtils;
import com.gtech.promotion.component.marketing.LuckyDrawComponent;
import com.gtech.promotion.domain.marketing.*;
import com.gtech.promotion.vo.param.marketing.*;
import com.gtech.promotion.vo.result.coupon.ActivityParticipateInResult;
import com.gtech.promotion.vo.result.marketing.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Slf4j
@RestController
@Api(value = "Mall Marketing Lucky Draw API",tags = { "Mall Marketing Lucky Draw API" })
public class MallLuckyDrawController {

    @Autowired
    private LuckyDrawComponent luckyDrawComponent;

    @ApiOperation(value = "Find lucky draw detail information",notes = "Find lucky draw detail information")
    @PostMapping("/marketing/luckyDraw/detail")
    public Result<LuckyDrawDetailResult> getLuckyDrawDetail(@RequestBody LuckyDrawDetailParam param){
        param.validate();

        LuckyDrawDetailDomain luckyDrawDetailDomain = BeanCopyUtils.jsonCopyBean(param, LuckyDrawDetailDomain.class);
        return Result.ok(luckyDrawComponent.getLuckyDrawDetail(luckyDrawDetailDomain));
    }

    @ApiOperation(value = "Do lucky draw detail",notes = "Do lucky draw detail")
    @PostMapping("/marketing/luckyDraw/draw")
    public Result<LuckyDrawResult> draw(@RequestBody LuckyDrawParam param){
        param.validate();

        LuckyDrawDoDomain luckyDrawDoDomain = BeanCopyUtils.jsonCopyBean(param, LuckyDrawDoDomain.class);
        return Result.ok(luckyDrawComponent.getLuckyDrawResult(luckyDrawDoDomain));
    }

    @ApiOperation(value = "Query lucky draw chance list",notes = "Query lucky draw chance list")
    @PostMapping("/marketing/luckyDraw/chance/query")
    public PageResult<LuckyDrawMemberChanceResult> queryChanceList(@RequestBody LuckyDrawMemberChanceParam param){
        param.validate();

        LuckyDrawMemberChanceDomain domain = BeanCopyUtils.jsonCopyBean(param, LuckyDrawMemberChanceDomain.class);
        PageData<LuckyDrawMemberChanceResult> pageResult = luckyDrawComponent.queryChanceList(domain);
        return PageResult.ok(pageResult.getList(), pageResult.getTotal());
    }

    @ApiOperation(value = "Query lucky draw record list",notes = "Query lucky draw record list")
    @PostMapping("/marketing/luckyDraw/record/query")
    public PageResult<LuckyDrawMemberRecordResult> queryLuckyRecordList(@RequestBody LuckyDrawMemberRecordParam param){
        param.validate();

        LuckyDrawMemberRecordDomain domain = BeanCopyUtils.jsonCopyBean(param, LuckyDrawMemberRecordDomain.class);
        PageData<LuckyDrawMemberRecordResult> pageResult = luckyDrawComponent.queryLuckyRecordList(domain);
        return PageResult.ok(pageResult.getList(), pageResult.getTotal());
    }


    @ApiOperation(value = "Send ticket by product",notes = "Send ticket by product")
    @PostMapping("/marketing/luckyDraw/sendTicketByProduct")
    public Result<List<ActivityParticipateInResult>> sendTicketByProduct(@RequestBody JudgeQualificationParam param){

        log.info("校验抽奖资格参数:{{}}", JSONObject.toJSONString(param));
        param.validate();

        JudgeQualificationDomain domain = BeanCopyUtils.jsonCopyBean(param, JudgeQualificationDomain.class);

        List<ActivityParticipateInResult> activityParticipateInResults = luckyDrawComponent.judgeQualification(domain);
        return Result.ok(activityParticipateInResults);
    }


    @ApiOperation(value = "Eligible activities",notes = "Query eligible activities")
    @PostMapping("/marketing/luckyDraw/eligibleActivities")
    public Result<List<ActivityParticipateInResult>> eligibleActivities(@RequestBody EligibleActivitiesParam param){

        log.info("校验抽奖资格参数:{{}}", JSONObject.toJSONString(param));
        param.validate();

        JudgeQualificationDomain domain = BeanCopyUtils.jsonCopyBean(param, JudgeQualificationDomain.class);

        List<ActivityParticipateInResult> activityParticipateInResults = luckyDrawComponent.eligibleActivities(domain);

        return Result.ok(activityParticipateInResults);
    }

    @ApiOperation(value = "Query member lucky draw frequency",notes = "Query member lucky draw frequency")
    @PostMapping("/marketing/luckyDraw/queryMemberLuckyDrawFrequency")
    public Result<QueryMemberLuckyDrawFrequencyResult> queryMemberLuckyDrawFrequency(@RequestBody QueryMemberLuckyDrawFrequencyParam param){

        log.info("查询用户抽奖次数参数:{{}}", JSONObject.toJSONString(param));
        param.validate();

        QueryMemberLuckyDrawFrequencyDomain domain = BeanCopyUtils.jsonCopyBean(param,QueryMemberLuckyDrawFrequencyDomain.class);

        QueryMemberLuckyDrawFrequencyResult result = luckyDrawComponent.queryMemberLuckyDrawFrequency(domain);

        return Result.ok(result);
    }


    @ApiOperation(value = "Update frozen status",notes = "Update frozen status")
    @PostMapping("/marketing/luckyDraw/updateFrozenStatus")
    public Result updateFrozenStatus(@RequestBody UpdateFrozenStatusParam param){
        param.validate();
        LuckyDrawMemberChanceDomain domain = BeanCopyUtils.jsonCopyBean(param, LuckyDrawMemberChanceDomain.class);
        luckyDrawComponent.updateFrozenStatus(domain);
        return Result.ok();
    }



}
