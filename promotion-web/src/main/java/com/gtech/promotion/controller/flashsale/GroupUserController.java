package com.gtech.promotion.controller.flashsale;

import com.github.pagehelper.PageInfo;
import com.gtech.commons.result.PageResult;
import com.gtech.commons.result.Result;
import com.gtech.commons.utils.BeanCopyUtils;
import com.gtech.promotion.component.marketing.MarketingGroupComponent;
import com.gtech.promotion.vo.param.marketing.MarketingGroupUserListParam;
import com.gtech.promotion.vo.param.marketing.MarketingGroupUserParam;
import com.gtech.promotion.vo.param.marketing.flashsale.FindGroupUserParam;
import com.gtech.promotion.vo.result.flashsale.MarketingGroupUserListResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 秒杀
 */
@Slf4j
@RestController
@Api(value = "Marketing Group User API",tags = { "Marketing Group User API" })
public class GroupUserController {


    @Autowired
    private MarketingGroupComponent marketingGroupComponent;


    @ApiOperation(value = "Query group user list",notes = "Query group user list")
    @PostMapping("/marketing/all/group/user/list")
    public PageResult<MarketingGroupUserListResult> queryAllGroupUserList(@RequestBody MarketingGroupUserListParam param){

        param.validate();

        PageInfo<MarketingGroupUserListResult> pageInfo = marketingGroupComponent.queryAllGroupUserList(param);

        return PageResult.ok(BeanCopyUtils.jsonCopyList(pageInfo.getList(), MarketingGroupUserListResult.class), pageInfo.getTotal());

    }


    /**
     * 查询团长已支付的拼团列表（进行中的）
     * @param param
     * @return
     */
    @ApiOperation(value = "List of ongoing groups",notes = "List of ongoing groups")
    @PostMapping("/marketing/group/user/list")
    public Result<List<MarketingGroupUserListResult>> queryGroupUserList(@RequestBody MarketingGroupUserParam param){

        param.validate();

        return Result.ok(marketingGroupComponent.queryGroupUserList(param));
    }


    /**
     * 用户查询拼团情况
     * @param param
     * @return
     */
    @ApiOperation(value = "Find group by user.",notes = "Find group by user.")
    @PostMapping("/marketing/group/user/detail")
    public Result<MarketingGroupUserListResult> findUserGroupByMarketingGroupAndUserCode(@RequestBody FindGroupUserParam param){

        param.validate();

        return Result.ok(marketingGroupComponent.findUserGroupByMarketingGroupAndUserCode(param));
    }


}
