/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.controller.coupon;

import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.gtech.commons.exception.ErrorCode;
import com.gtech.commons.exception.Exceptions;
import com.gtech.commons.page.PageData;
import com.gtech.commons.result.PageResult;
import com.gtech.commons.result.Result;
import com.gtech.commons.utils.BeanCopyUtils;
import com.gtech.commons.utils.StringUtil;
import com.gtech.promotion.code.CodeHelper;
import com.gtech.promotion.component.activity.QualificationDomain;
import com.gtech.promotion.component.coupon.*;
import com.gtech.promotion.controller.BaseController;
import com.gtech.promotion.dao.entity.coupon.PromoCouponSendDetailEntity;
import com.gtech.promotion.dao.entity.coupon.PromoCouponSendLogEntity;
import com.gtech.promotion.dao.model.activity.PromoCouponSendDetailModel;
import com.gtech.promotion.dao.model.coupon.CouponSendDetailVO;
import com.gtech.promotion.dao.model.coupon.CouponSendLogVO;
import com.gtech.promotion.domain.coupon.CouponDomain;
import com.gtech.promotion.dto.in.activity.ShoppingCartDTO;
import com.gtech.promotion.dto.in.activity.TenantProductDTO;
import com.gtech.promotion.dto.in.coupon.*;
import com.gtech.promotion.dto.out.activity.ShoppingCartOutDTO;
import com.gtech.promotion.dto.out.coupon.CouponActivityProductDTO;
import com.gtech.promotion.dto.out.coupon.CouponInfoDTO;
import com.gtech.promotion.dto.out.coupon.CouponInfoOutDTO;
import com.gtech.promotion.dto.out.coupon.StoreCouponActivityOutDTO;
import com.gtech.promotion.exception.ErrorCodes;
import com.gtech.promotion.service.coupon.CouponSendDetailService;
import com.gtech.promotion.service.coupon.CouponSendLogService;
import com.gtech.promotion.service.coupon.PromoCouponReleaseService;
import com.gtech.promotion.utils.ActivityProductCheckUtil;
import com.gtech.promotion.vo.param.coupon.*;
import com.gtech.promotion.vo.result.activity.CalcShoppingCartResult;
import com.gtech.promotion.vo.result.coupon.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.*;
import java.util.stream.Collectors;

/**
 * Mall coupon screening query use and receive.
 *
 */
@Slf4j
@Api(value = "Mall Coupon Api", tags = { "Mall Coupon API" })
@RestController
public class MallCouponController extends BaseController {

	@Autowired
	private FilterCouponComponent filterCouponDomain;

	@Autowired
	private CouponInnerCodeComponent couponInnerCodeDomain;

	@Autowired
	private CouponActivityComponent couponActivityDomain;

	@Autowired
	private CouponCodeUserComponent couponUserCodeDomain;

	@Autowired
	private PromoCouponReleaseService couponReleaseService;

	@Autowired
	private ReceiveCouponBatchComponent receiveCouponBatchDomain;

	@Autowired
	private QualificationDomain qualificationDomain;

	@Autowired
	private CouponReserveComponent couponReserveComponent;

	@Autowired
	private CouponSendLogService couponSendLogService;

	@Autowired
	private CouponSendDetailService couponSendDetailService;

	@Autowired
	private CouponCodeUserComponent couponCodeUserComponent;

	/***
	 * 自动选优惠券
	 */
	@ApiOperation(value = "Auto choose coupons by shopping cart", notes = "Return shopping cart results")
	@PostMapping(value = "/coupon/chooseCouponsByCart")
	public Result<List<CalcShoppingCartResult>> chooseCouponsByCart(@RequestBody FilterCouponsByCartParam param) {

		log.info("MallCouponController::chooseCouponsByCart: {}", JSONObject.toJSONString(param));

		param.validate();

		ShoppingCartDTO shoppingCart = BeanCopyUtils.jsonCopyBean(param, ShoppingCartDTO.class);

		shoppingCart.setUserCode(param.getMemberCode());

		List<ShoppingCartOutDTO> shoppingCartOutDTOS = filterCouponDomain.chooseCouponsByCart(shoppingCart);
		if (CollectionUtils.isEmpty(shoppingCartOutDTOS)) {
			return Result.ok();
		}
		List<CalcShoppingCartResult> resultList = new ArrayList<>();
		for (ShoppingCartOutDTO sco : shoppingCartOutDTOS) {
			CalcShoppingCartResult scResult = BeanCopyUtils.jsonCopyBean(sco, CalcShoppingCartResult.class);
			ErrorCode failedReason = sco.getFailedReason();
			if (null != failedReason) {
				scResult.setFalseCode(failedReason.getCode());
				scResult.setFalseReason(failedReason.getMessage());
			}
			resultList.add(scResult);
		}
		return Result.ok(resultList);
	}

	/***
	 * 筛选优惠券
	 */
	@ApiOperation(value = "Filter coupons by shopping cart", notes = "Return coupon and activity information")
	@PostMapping(value = "/coupon/filterCouponsByCart")
	public Result<List<FilterCouponsByCartResult>> filterCouponsByCart(@RequestBody FilterCouponsByCartParam param) {

		log.info("MallCouponController::filterCouponsByCart: {}", JSONObject.toJSONString(param));

		param.validate();

		ShoppingCartDTO shoppingCart = BeanCopyUtils.jsonCopyBean(param, ShoppingCartDTO.class);

		shoppingCart.setUserCode(param.getMemberCode());

		List<CouponInfoDTO> filterCoupon = filterCouponDomain.filterCoupon(shoppingCart);

		return Result.ok(BeanCopyUtils.jsonCopyList(filterCoupon, FilterCouponsByCartResult.class));
	}

	/**
	 * 根据商品查询优惠券活动列表
	 */
	@ApiOperation(value = "Query coupon activity list by product", notes = "Return a list of coupon activity based on items")
	@PostMapping(value = "/coupon/queryCouponActivityListByProduct")
	public Result<List<QueryCouponActivityListByProductResult>> queryCouponActivityListByProduct(
			@RequestBody QueryCouponActivityListByProductParam param) {

		log.info("MallCouponController::queryCouponActivityListByProduct：{}", JSONObject.toJSONString(param));
		param.validate();
		TenantProductDTO tenantProductDTO = BeanCopyUtils.jsonCopyBean(param, TenantProductDTO.class);

		if (StringUtil.isNotEmpty(param.getOrgCode())){
			tenantProductDTO.setOrgCodes(Arrays.asList(param.getOrgCode()));
		}

		ActivityProductCheckUtil.checkProductCombine(tenantProductDTO);// 商品校验


		//和现有逻辑冲突 会出现 "orgCodes": "[\"\"]",
		/*if (StringUtil.isNotEmpty(param.getOrgCode())){
			tenantProductDTO.setOrgCodes(Arrays.asList(param.getOrgCode()));
		}
		tenantProductDTO.setOrgCodes(CodeHelper.getOrgCodes(tenantProductDTO.getOrgCodes()));*/

		QueryCouponActivityListByProductListParam queryCouponActivityListByProductListParam = BeanCopyUtils.jsonCopyBean(tenantProductDTO, QueryCouponActivityListByProductListParam.class);

		QueryCouponActivityListByProductListParam.Product product = BeanCopyUtils.jsonCopyBean(tenantProductDTO, QueryCouponActivityListByProductListParam.Product.class);
		queryCouponActivityListByProductListParam.setProductList(Lists.newArrayList(product));


		List<QueryCouponActivityListByProductListResult> couponActivityProductDTOList = filterCouponDomain
				.searchActivityByProductList(queryCouponActivityListByProductListParam);

		List<CouponActivityProductDTO> couponActivityProductDTOS = filterCouponDomain.convertCouponActivityProductDTO(couponActivityProductDTOList);

		return Result.ok(BeanCopyUtils.jsonCopyList(couponActivityProductDTOS, QueryCouponActivityListByProductResult.class));
	}



	/**
	 * 根据批量商品查询优惠券活动列表
	 */
	@ApiOperation(value = "Query coupon activity list by product list", notes = "Return a list of coupon activity based on items")
	@PostMapping(value = "/coupon/queryCouponActivityListByProductList")
	public Result<List<QueryCouponActivityListByProductListResult>> queryCouponActivityListByProductList(
			@RequestBody QueryCouponActivityListByProductListParam param) {

		log.info("MallCouponController::queryCouponActivityListByProductList：{}", JSONObject.toJSONString(param));
		param.validate();

		List<QueryCouponActivityListByProductListResult> couponActivityProductDTOList = filterCouponDomain
				.searchActivityByProductList(param);


		return Result.ok(BeanCopyUtils.jsonCopyList(couponActivityProductDTOList, QueryCouponActivityListByProductListResult.class));
	}


	@ApiOperation(value = "Query coupon activity list by store", notes = "Return coupon activity list according to store")
	@PostMapping(value = "/coupon/queryCouponActivityListByStore")
	public PageResult<QueryCouponActivityListByStoreResult> queryCouponActivityListByStore(
			@RequestBody QueryCouponActivityListByStoreParam param) {

		log.info("MallCouponController::queryCouponActivityListByStore： {}", JSONObject.toJSONString(param));

		param.validate();

		StoreCouponActivityInDTO dto = BeanCopyUtils.jsonCopyBean(param, StoreCouponActivityInDTO.class);

		dto.setOrgCode(CodeHelper.getOrgCode(dto.getOrgCode()));

		PageInfo<StoreCouponActivityOutDTO> list = couponActivityDomain.queryCouponActivityInStore(dto);

		return PageResult.ok(BeanCopyUtils.jsonCopyList(list.getList(), QueryCouponActivityListByStoreResult.class),
				list.getTotal());
	}

	/**
	 * Query coupon list by user.
	 */
	@ApiOperation(value = "Query coupon list by user.", notes = "Return a list of coupon activity based on user")
	@PostMapping(value = "/coupon/queryCouponListByUser")
	public PageResult<QueryCouponListByUserResult> queryCouponListByUser(
			@RequestBody QueryCouponListByUserParam param) {

		log.info("MallCouponController#queryCouponListByUser:{{}}", JSONObject.toJSONString(param));

		param.validate();

		PageData<CouponDomain> pageList = filterCouponDomain.queryUserCouponList(param);

		return PageResult.ok(BeanCopyUtils.jsonCopyList(pageList.getList(), QueryCouponListByUserResult.class),
				pageList.getTotal());
	}

	@ApiOperation(value = "Find a coupon activity detail information", notes = "Return coupon detail")
	@PostMapping(value = "/coupon/findCouponDetail")
	public Result<FindCouponDetailResult> findCouponDetail(@RequestBody FindCouponDetailParam param) {

		log.info("MallCouponController#findCouponDetail:{{}}", JSONObject.toJSONString(param));

		param.validate();

		CouponInfoInDTO couponInfoInDTO = BeanCopyUtils.jsonCopyBean(param, CouponInfoInDTO.class);

		couponInfoInDTO.setOrgCode(CodeHelper.getOrgCode(couponInfoInDTO.getOrgCode()));

		CouponInfoOutDTO infoOutDTO = couponActivityDomain.findCouponActivityByActivityCode(couponInfoInDTO);

		return Result.ok(BeanCopyUtils.jsonCopyBean(infoOutDTO, FindCouponDetailResult.class));
	}

	/**
	 * 查询会员某一张优惠券券码信息
	 *
	 * @return 券码信息
	 */
	@ApiOperation(value = "Find a user coupon base information", notes = "Return coupon information")
	@PostMapping(value = "/coupon/findUserCoupon")
	public Result<FindUserCouponResult> findUserCoupon(@RequestBody FindUserCouponParam param) {

		log.info("MallCouponController#findUserCoupon:{{}}", JSONObject.toJSONString(param));

		param.validate();

		TCouponQueryOrApplyDTO tCouponQueryOrApplyDTO = BeanCopyUtils.jsonCopyBean(param, TCouponQueryOrApplyDTO.class);

		tCouponQueryOrApplyDTO.setOrgCode(CodeHelper.getOrgCode(tCouponQueryOrApplyDTO.getOrgCode()));

		CouponDomain couponDomain = couponInnerCodeDomain.getCouponInfo(tCouponQueryOrApplyDTO);

		return Result.ok(BeanCopyUtils.jsonCopyBean(couponDomain, FindUserCouponResult.class));
	}

	/**
	 * 核销券码
	 * 
	 * @param param
	 */
	@ApiOperation(value = "Verify a coupon code", notes = "Returns the result of voucher verification.")
	@PostMapping(value = "/coupon/verifyCouponCode")
	public Result<Integer> verifyCouponCode(@RequestBody VerifyCouponCodeParam param) {

		log.info("MallCouponController#verifyCouponCode:{{}}", JSONObject.toJSONString(param));

		param.validate();

		TCouponCheckAndUseInDTO checkAndUseInDTO = BeanCopyUtils.jsonCopyBean(param, TCouponCheckAndUseInDTO.class);

		return Result.ok(couponUserCodeDomain.queryCheckAndUseCouponCode(checkAndUseInDTO));
	}

	/**
	 * 会员领取优惠券
	 */
	@ApiOperation(value = "Send a coupon to a user", notes = "Return coupon code")
	@PostMapping(value = "/coupon/sendCouponToUser")
	public Result<String> sendCouponToUser(@RequestBody SendCouponToUserParam param) {

		log.info("MallCouponController#sendCouponToUser:{{}}", JSONObject.toJSONString(param));

		param.validate();

		Map<String, String> userCouponMap = couponCodeUserComponent.sendCouponToUserByUserCode(param);

		return Result.ok(userCouponMap.get(param.getUserCode()));
	}



	public void checkUserLimit(List<PromoCouponSendDetailModel> couponSendDetailModelList) {
		if (CollectionUtils.isEmpty(couponSendDetailModelList)) {
			return;
		}

		PromoCouponSendDetailModel promoCouponSendDetailModel = couponSendDetailModelList.get(0);
		if (null != promoCouponSendDetailModel && !StringUtil.isEmpty(promoCouponSendDetailModel.getFailReason())) {
			log.info("send coupon fail!failReason:{}", promoCouponSendDetailModel.getFailReason());
			if (promoCouponSendDetailModel.getFailReason().contains("day")) {
				throw Exceptions.fail(ErrorCodes.USER_MAX_TIME_PER_DAY_EXCEEDED);
			} else {
				throw Exceptions.fail(ErrorCodes.USER_MAX_TIME_EXCEEDED);
			}
		}

	}

	/**
	 * 批量会员领取优惠券
	 */
	@ApiOperation(value = "Send coupon to user list", notes = "Returns the result of member's voucher collection")
	@PostMapping(value = "/coupon/sendCouponToUserList")
	public Result<List<SendCouponToUserListResult>> sendCouponToUserList(@RequestBody SendCouponToUserListParam param) {

		log.info("MallCouponController#sendCouponToUserList:{{}}", JSONObject.toJSONString(param));

		param.validate();

		ReceiveCouponBatchDTO receiveCouponBatchDTO = BeanCopyUtils.jsonCopyBean(param, ReceiveCouponBatchDTO.class);

		return Result.ok(BeanCopyUtils.jsonCopyList(receiveCouponBatchDomain.receiveCouponBatch(receiveCouponBatchDTO),
				SendCouponToUserListResult.class));
	}

	/**
	 * 批量发送匿名券
	 */
	@ApiOperation(value = "Send coupon", notes = "Returns the sent coupon code")
	@PostMapping(value = "/coupon/sendAnonymousCoupon")
	public Result<List<SendAnonymousCouponResult>> sendAnonymousCoupon(@RequestBody SendAnonymousCoupon param) {
		log.info("MallCouponController#sendAnonymousCoupon:{{}}", JSONObject.toJSONString(param));
		param.validate();
		SendAnonymousCouponBatchDTO receiveCouponBatchDTO = BeanCopyUtils.jsonCopyBean(param, SendAnonymousCouponBatchDTO.class);
		List<SendAnonymousCouponResult> results = receiveCouponBatchDomain.sendAnonymousCoupon(receiveCouponBatchDTO);
		return Result.ok(results);
	}

	/**
	 * 批量会员领取优惠券
	 */
	@ApiOperation(value = "Send coupon to user list", notes = "Returns the result of member's voucher collection")
	@PostMapping(value = "/coupon/sendCoupon")
	public Result<List<SendCouponToUserListResult>> sendCoupon(@RequestBody SendCouponParam param) {
		log.info("MallCouponController#sendCoupon:{{}}", JSONObject.toJSONString(param));
		String errorMessage = null;
		try {
			param.validate();
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			log.error("param validate error.");
			errorMessage = e.getMessage();
		}
		ReceiveCouponBatchDTO receiveCouponBatchDTO = BeanCopyUtils.jsonCopyBean(param, ReceiveCouponBatchDTO.class);
		receiveCouponBatchDTO.setErrorMessage(errorMessage);
		receiveCouponBatchDomain.receiveCouponBatchAsync(receiveCouponBatchDTO);
		return Result.ok(null);
	}

	// 锁定预留券码库存
	@ApiOperation(value = "Reserve coupon quota", notes = "Reserve coupon quota")
	@PostMapping(value = "/coupon/quota/reserve")
	public Result<Object> reserveCouponQuota(@RequestBody ReserveCouponQuotaParam param) {
		param.validate();

		ReserveCouponQuotaDto reserveCouponQuotaDto = BeanCopyUtils.jsonCopyBean(param, ReserveCouponQuotaDto.class);
		couponReserveComponent.reserveCouponQuota(reserveCouponQuotaDto);
		return Result.ok();
	}

	// 解锁预留券码库存
	@ApiOperation(value = "Return coupon quota", notes = "Return coupon quota")
	@PostMapping(value = "/coupon/quota/return")
	public Result<Object> returnCouponQuota(@RequestBody ReserveCouponQuotaParam param) {
		param.validate();

		ReserveCouponQuotaDto reserveCouponQuotaDto = BeanCopyUtils.jsonCopyBean(param, ReserveCouponQuotaDto.class);
		couponReserveComponent.returnCouponQuota(reserveCouponQuotaDto);
		return Result.ok();
	}

	// 发放预留库存的券码
	@ApiOperation(value = "Release coupon quota", notes = "Release coupon quota")
	@PostMapping(value = "/coupon/quota/send")
	public Result<Object> releaseCouponQuota(@RequestBody ReserveCouponQuotaParam param) {
		param.validate();

		ReserveCouponQuotaDto reserveCouponQuotaDto = BeanCopyUtils.jsonCopyBean(param, ReserveCouponQuotaDto.class);
		couponReserveComponent.releaseCouponQuota(reserveCouponQuotaDto);
		return Result.ok();
	}

	/**
	 * Check your coupon usage history.
	 */
	@ApiOperation(value = "Check your coupon usage history.", notes = "Return coupon usage history")
	@PostMapping(value = "/coupon/queryUsedCouponList")
	public PageResult<QueryCouponUsedListResult> queryUsedCouponList(@RequestBody QueryCouponUsedListParam param) {
		log.info("queryUsedCouponList:{{}}", JSONObject.toJSONString(param));
		param.validate();
		QueryCouponUsedInDTO dto = BeanCopyUtils.jsonCopyBean(param, QueryCouponUsedInDTO.class);
		PageData<QueryCouponUsedListResult> pageList = couponUserCodeDomain.queryUsedCouponList(dto);
		return PageResult.ok(BeanCopyUtils.jsonCopyList(pageList.getList(), QueryCouponUsedListResult.class),
				pageList.getTotal());
	}

	@ApiOperation(value = "query send coupon log list", notes = "query send coupon log list")
	@PostMapping(value = "/coupon/querySendCouponLogList")
	public PageResult<CouponSendLogVO> querySendCouponLogList(
			@RequestBody QueryCouponSendLogParam queryCouponSendLogParam) {
		queryCouponSendLogParam.validate();
		PageInfo<PromoCouponSendLogEntity> couponSendLogEntityPageInfo = couponSendLogService
				.queryCouponSendLogList(queryCouponSendLogParam);
		List<CouponSendLogVO> couponSendLogVOList = new ArrayList<>();

		if (CollectionUtils.isNotEmpty(couponSendLogEntityPageInfo.getList())) {
			List<String> sendBatchNoList = couponSendLogEntityPageInfo.getList().stream()
					.map(PromoCouponSendLogEntity::getSendBatchNo).collect(Collectors.toList());
			QueryCouponSendDetailParam queryCouponSendDetailParam = new QueryCouponSendDetailParam();
			queryCouponSendDetailParam.setActivityCode(queryCouponSendLogParam.getActivityCode());
			queryCouponSendDetailParam.setTenantCode(queryCouponSendLogParam.getTenantCode());
			queryCouponSendDetailParam.setSendBatchNoList(sendBatchNoList);
			List<PromoCouponSendDetailEntity> sendDetailEntityList = couponSendDetailService
					.queryCouponSendDetailList(queryCouponSendDetailParam);
			Map<String, List<PromoCouponSendDetailEntity>> sendBatchMap = new HashMap<>();
			if (CollectionUtils.isNotEmpty(sendDetailEntityList)) {
				sendBatchMap = sendDetailEntityList.stream()
						.collect(Collectors.groupingBy(PromoCouponSendDetailEntity::getBatchNo));
			}
			for (PromoCouponSendLogEntity couponSendLogEntity : couponSendLogEntityPageInfo.getList()) {
				CouponSendLogVO couponSendLogVO = new CouponSendLogVO();
				BeanCopyUtils.copyProperties(couponSendLogEntity, couponSendLogVO);
				List<PromoCouponSendDetailEntity> couponSendDetailEntityList = sendBatchMap
						.get(couponSendLogEntity.getSendBatchNo());
				if (CollectionUtils.isNotEmpty(couponSendDetailEntityList)) {
					List<CouponSendDetailVO> couponSendDetailList = new ArrayList<>();
					for (PromoCouponSendDetailEntity couponSendDetailEntity : couponSendDetailEntityList) {
						CouponSendDetailVO couponSendDetailVO = new CouponSendDetailVO();
						BeanCopyUtils.copyProperties(couponSendDetailEntity, couponSendDetailVO);
						couponSendDetailList.add(couponSendDetailVO);
					}
					couponSendLogVO.setCouponSendDetailList(couponSendDetailList);
					couponSendLogVO.setFailCount(couponSendDetailEntityList.size());
				} else {
					couponSendLogVO.setFailCount(0);
				}
				couponSendLogVOList.add(couponSendLogVO);
			}
		}
		return PageResult.ok(BeanCopyUtils.jsonCopyList(couponSendLogVOList, CouponSendLogVO.class),
				couponSendLogEntityPageInfo.getTotal());

	}
}
