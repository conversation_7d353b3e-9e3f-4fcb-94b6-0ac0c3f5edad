package com.gtech.promotion.controller.marketing;

import com.gtech.commons.result.PageResult;
import com.gtech.commons.result.Result;
import com.gtech.commons.utils.BeanCopyUtils;
import com.gtech.commons.utils.StringUtil;
import com.gtech.member.web.vo.param.QueryMemberCodesParam;
import com.gtech.member.web.vo.param.TagQueryMemberParam;
import com.gtech.member.web.vo.result.QueryMemberCodesResult;
import com.gtech.member.web.vo.result.TagMemberListResult;
import com.gtech.promotion.checker.marketing.MarketingChecker;
import com.gtech.promotion.component.marketing.TicketComponent;
import com.gtech.promotion.domain.marketing.TicketReleaseDomain;
import com.gtech.promotion.domain.marketing.TicketSendDomain;
import com.gtech.promotion.exception.Check;
import com.gtech.promotion.feign.MemberFeignClient;
import com.gtech.promotion.vo.param.marketing.TicketReleaseParam;
import com.gtech.promotion.vo.param.marketing.TicketReleaseQueryParam;
import com.gtech.promotion.vo.param.marketing.TicketSendParam;
import com.gtech.promotion.vo.result.marketing.TicketReleaseQueryResult;
import com.gtech.promotion.vo.result.marketing.TicketSendResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;

@Slf4j
@RestController
@Api(value = "Marketing Ticket API",tags = { "Marketing Ticket API" })
public class TicketController {

    @Autowired
    private TicketComponent ticketComponent;

    @Autowired
    private MemberFeignClient memberFeignClient;

    @ApiOperation(value = "Create ticket release",notes = "Create ticket release")
    @PostMapping("/marketing/ticket/release")
    public Result<String> release(@RequestBody TicketReleaseParam param){
        param.validate();

        TicketReleaseDomain ticketReleaseDomain = BeanCopyUtils.jsonCopyBean(param, TicketReleaseDomain.class);
        return Result.ok(ticketComponent.release(ticketReleaseDomain));
    }

    @ApiOperation(value = "Querying ticket the release history",notes = "Querying ticket the release history")
    @PostMapping("/marketing/ticket/release/query")
    public PageResult<TicketReleaseQueryResult> queryRelease(@RequestBody TicketReleaseQueryParam param){
        param.validate();

        TicketReleaseDomain ticketReleaseDomain = BeanCopyUtils.jsonCopyBean(param, TicketReleaseDomain.class);
        return ticketComponent.queryRelease(ticketReleaseDomain, param);
    }

    @ApiOperation(value = "Send ticket to user",notes = "Send ticket to user")
    @PostMapping("/marketing/ticket/send")
    public Result<Integer> sendTicket(@RequestBody TicketSendParam param){
        param.validate();

        TicketSendDomain ticketSendDomain = BeanCopyUtils.jsonCopyBean(param, TicketSendDomain.class);
        if (CollectionUtils.isEmpty(param.getMemberCodes())){
            List<String> memberCodes = new ArrayList<>();
            if (!CollectionUtils.isEmpty(param.getMemberMobiles())){
                QueryMemberCodesParam queryMemberCodesParam = new QueryMemberCodesParam();
                queryMemberCodesParam.setDomainCode(param.getDomainCode());
                queryMemberCodesParam.setTenantCode(param.getTenantCode());
                queryMemberCodesParam.setMobiles(StringUtil.list2String(param.getMemberMobiles()));
                PageResult<QueryMemberCodesResult> resultPageResult = memberFeignClient.queryMemberCodesByMobiles(queryMemberCodesParam);
                if (!CollectionUtils.isEmpty(resultPageResult.getData().getList())){
                    resultPageResult.getData().getList().forEach(x->memberCodes.add(x.getMemberCode()));
                }
            }else {
                TagQueryMemberParam tagQueryMemberParam = new TagQueryMemberParam();
                tagQueryMemberParam.setDomainCode(param.getDomainCode());
                tagQueryMemberParam.setTenantCode(param.getTenantCode());
                tagQueryMemberParam.setTagCodeList(param.getMemberTagCodes());
                tagQueryMemberParam.setPageSize(1000);
                PageResult<TagMemberListResult> resultPageResult = memberFeignClient.queryMemberCodesByTagCodes(tagQueryMemberParam);
                if (!CollectionUtils.isEmpty(resultPageResult.getData().getList())){
                    resultPageResult.getData().getList().forEach(x->memberCodes.add(x.getMemberCode()));
                }
            }
            Check.check(CollectionUtils.isEmpty(memberCodes), MarketingChecker.TICKET_SEND_NO_MEMBER_FIND);
            ticketSendDomain.setMemberCodes(memberCodes);
        }
        List<TicketSendResult> data = ticketComponent.sendTicket(ticketSendDomain);
        return Result.ok(data.size());
    }
}
