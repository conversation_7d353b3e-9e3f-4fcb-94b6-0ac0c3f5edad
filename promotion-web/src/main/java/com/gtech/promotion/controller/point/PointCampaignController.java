package com.gtech.promotion.controller.point;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.google.common.collect.ImmutableMap;
import com.gtech.commons.result.PageResult;
import com.gtech.commons.result.Result;
import com.gtech.promotion.service.point.PointCampaignService;
import com.gtech.promotion.vo.param.point.PointCampaignParam;
import com.gtech.promotion.vo.param.point.query.PointCampaignUniqueParam;
import com.gtech.promotion.vo.param.point.query.PointCampaignUniqueParam.PointCampaignStatusUniqueVo;
import com.gtech.promotion.vo.param.point.query.QueryPointCampaignParam;
import com.gtech.promotion.vo.result.point.PointCampaignResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import javax.annotation.Resource;
import java.util.Map;

@RequestMapping(value = "/point")
@RestController
@Slf4j
@ApiIgnore
@Api(value = "Point Campaign API", tags = "Point Campaign API")
public class PointCampaignController {
    @Resource
	private PointCampaignService pointCampaignService;

	/**
	 * @Description: 获取积分池列表
	 * @Date: 2019/4/15 19:52
	 * 
	 */
	@SuppressWarnings("unchecked")
	@ApiOperation(value = "queryPointCampaignList")
	@PostMapping(value = "/queryPointCampaignList")
	public PageResult<PointCampaignResult> queryPointCampaignList(@RequestBody @Validated QueryPointCampaignParam pointCampaignQueryVo) {
		log.info("promotion_point:queryPointCampaignList:{{}}",JSON.toJSONString(pointCampaignQueryVo));

		//check参数
		Map<String, Object> paramMap = JSON.parseObject(JSON.toJSONString(pointCampaignQueryVo), Map.class);
		return pointCampaignService.queryPointCampaignPage(paramMap);
	}

	/**
	 * @Description: 获取积分池详情
	 * @Date: 2019/4/16 17:52
	 * 
	 */
	@ApiOperation(value = "getPointCampaign")
	@PostMapping(value = "/getPointCampaign")
	public Result<PointCampaignResult> getPointCampaign(@RequestBody @Validated PointCampaignUniqueParam campaignUniqueVo) {
		log.info("promotion_point:getPointCampaign:{{}}",JSON.toJSONString(campaignUniqueVo));

		return Result.ok(pointCampaignService.getPointCampaign(campaignUniqueVo));
	}

	/**
	 * @Description: 修改积分池状态
	 * @Date: 2019/4/16 17:52
	 * 
	 */
	@ApiOperation(value = "updatePointCampaignStatus")
	@PostMapping(value = "/updatePointCampaignStatus")
	public Result<Void> updatePointCampaignStatus(@RequestBody @Validated PointCampaignStatusUniqueVo campaignStatusUniqueVo) {
		log.info("promotion_point:updatePointCampaignStatus:{{}}",JSON.toJSONString(campaignStatusUniqueVo));

		pointCampaignService.updatePointCampaignStatus(campaignStatusUniqueVo);
		return Result.ok();
	}

	/**
	 * @Description:新建积分池
	 * @Date: 2019/4/16 19:05
	 * 
	 */
	@ApiOperation(value = "createPointCampaign")
	@PostMapping(value = "/createPointCampaign")
	public Result<Map<String, String>> createPointCampaign(@RequestBody @Validated PointCampaignParam pointCampaignVo) {
		log.info("promotion_point:createPointCampaign:{{}}",JSON.toJSONString(pointCampaignVo));
		pointCampaignVo.setCampaignTitleLanguage(JSONArray.toJSONString(pointCampaignVo.getLanguageList()));

		String pointCampaignCode = pointCampaignService.savePointCampaign(pointCampaignVo);
		return Result.ok(ImmutableMap.of("campaignCode", pointCampaignCode));
	}

	/**
	 * @Description: 编辑积分池
	 * @Date: 2019/4/17 16:17
	 * 
	 */
	@ApiOperation(value = "editPointCampaign")
	@PostMapping(value = "/editPointCampaign")
	public Result<Void> editPointCampaign(@RequestBody @Validated PointCampaignParam pointCampaignVo){
		log.info("promotion_point:editPointCampaign:{{}}",JSON.toJSONString(pointCampaignVo));
		pointCampaignVo.setCampaignTitleLanguage(JSONArray.toJSONString(pointCampaignVo.getLanguageList()));
		pointCampaignService.updatePointCampaign(pointCampaignVo);
		return Result.ok();
	}

}
