/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * <PERSON>ECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.controller.activity;

import com.alibaba.fastjson.JSONObject;
import com.gtech.commons.result.Result;
import com.gtech.commons.utils.StringUtil;
import com.gtech.promotion.checker.activity.ActivityStatisticChecker;
import com.gtech.promotion.component.activity.ActivityStatisticDomain;
import com.gtech.promotion.controller.BaseController;
import com.gtech.promotion.dto.in.activity.*;
import com.gtech.promotion.dto.out.activity.*;
import com.gtech.promotion.exception.Check;
import com.gtech.promotion.utils.DateValidUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import java.util.List;

@Slf4j
@Api(value = "Activity Statistic API",tags = {"Activity Statistic API"})
@ApiIgnore
@RestController
public class ActivityStatisticController extends BaseController{

    @Autowired
    private ActivityStatisticDomain statisticDomain;

    @ApiOperation(value = "创建活动统计数据记录")
    @PostMapping(value = "/activity/statistic/create")
    public Result<String> createActivityStatistic(){

        statisticDomain.createActivityStatistic();

        return Result.ok("调用成功");
    }

    @ApiOperation(value = "查询活动统计每天数据记录")
    @PostMapping(value = "/activity/statistic/query")
    public Result<List<ActivityStatisticQueryOutDTO>> queryActivityStatistic(@RequestBody QueryActivityStatisticInDTO paramDTO){

        log.info("查询活动统计每天数据记录入参: {}", JSONObject.toJSONString(paramDTO));

        paramDTO.checkParams();

        return Result.ok(statisticDomain.queryActivityStatistic(paramDTO));

    }

    @ApiOperation(value = "查询活动统计数据总数")
    @PostMapping(value = "/activity/statistic/sum/query")
    public Result<ActivityStatisticSumQueryOutDTO> queryActivityStatisticSum(@RequestBody QueryActivityStatisticSumInDTO paramDTO){

        log.info("查询活动统计数据总数入参: {}", JSONObject.toJSONString(paramDTO));

        paramDTO.checkParams();

        return Result.ok(statisticDomain.queryActivityStatisticSum(paramDTO));

    }

    @ApiOperation(value = "促销数据汇总")
    @PostMapping(value = "/promotion/data")
    public Result<ActivityDataAnalyzeOutDto> summarizationOfata(@RequestBody StartAndEndTimeInDTO startAndEndTime){
        startAndEndTime.checkTime();
        return Result.ok(statisticDomain.dataOfAnalyze(startAndEndTime));
    }

    @ApiOperation(value = "促销进行中的活动总数(包含券活动),1.15.0版本新增接口")
    @PostMapping(value = "activity/active/data")
    public Result<ActivityTotalOutDto> queryActivityActiveTotal(@RequestBody ActivityActiveInDTO activeDTO){
        ActivityTotalOutDto activityTotalOutDto = new ActivityTotalOutDto();
        Check.check(StringUtil.isBlank(activeDTO.getTenantCode()), ActivityStatisticChecker.NOT_NULL_TENANT_CODE);
        Long activeTotal = statisticDomain.queryActivityActiveTotal(activeDTO.getTenantCode());
        activityTotalOutDto.setActivityTotal(activeTotal);
        return Result.ok(activityTotalOutDto);
    }

    @ApiOperation(value = "促销租户数据统计,1.15.0版本新增接口")
    @PostMapping(value = "activity/tenant/data")
    public Result<ActivityTenantOutDTO> queryActivityTenantData(@RequestBody ActivityTenantInDTO tenantDTO){
        checkStartAndEndTime(tenantDTO);
        return Result.ok(statisticDomain.queryActivityTenantData(tenantDTO));
    }

    private void checkStartAndEndTime(ActivityTenantInDTO tenantDTO){
        Check.check(StringUtil.isBlank(tenantDTO.getTenantCode()), ActivityStatisticChecker.NOT_NULL_TENANT_CODE);
        Check.check(StringUtil.isBlank(tenantDTO.getStartTime()) && StringUtil.isNotBlank(tenantDTO.getEndTime()), ActivityStatisticChecker.NOT_NULL_START_TIME);
        Check.check(StringUtil.isNotBlank(tenantDTO.getStartTime()) && StringUtil.isBlank(tenantDTO.getEndTime()), ActivityStatisticChecker.NOT_NULL_END_TIME);
        if (StringUtil.isNotBlank(tenantDTO.getStartTime()) && StringUtil.isNotBlank(tenantDTO.getEndTime())){
            Check.check(!DateValidUtil.isValidDate(tenantDTO.getStartTime()), ActivityStatisticChecker.NO_TIME_FORTMAT);
            Check.check(!DateValidUtil.isValidDate(tenantDTO.getEndTime()), ActivityStatisticChecker.NO_TIME_FORTMAT);
            Check.check(tenantDTO.getStartTime().compareTo(tenantDTO.getEndTime()) > 0, ActivityStatisticChecker.START_TIME_LOWER_END_TIME);
        }
    }



}
