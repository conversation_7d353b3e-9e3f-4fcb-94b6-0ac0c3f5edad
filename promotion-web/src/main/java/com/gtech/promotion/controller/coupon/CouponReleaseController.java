/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * <PERSON>ECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.controller.coupon;

import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.gtech.commons.result.PageResult;
import com.gtech.commons.result.Result;
import com.gtech.commons.utils.BeanCopyUtils;
import com.gtech.promotion.checker.coupon.CouponActivityChecker;
import com.gtech.promotion.code.coupon.CouponReleaseSourceEnum;
import com.gtech.promotion.component.coupon.CouponReleaseComponent;
import com.gtech.promotion.domain.coupon.CouponReleaseDomain;
import com.gtech.promotion.dto.in.coupon.ReleaseCouponInDTO;
import com.gtech.promotion.exception.Check;
import com.gtech.promotion.exception.ErrorCodes;
import com.gtech.promotion.exception.PromotionException;
import com.gtech.promotion.page.RequestPage;
import com.gtech.promotion.vo.param.coupon.*;
import com.gtech.promotion.vo.result.coupon.FindCouponReleaseResult;
import com.gtech.promotion.vo.result.coupon.QueryCouponReleaseResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;
import tk.mybatis.mapper.util.StringUtil;

import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

/**
 * 券投放controller类
 */
@RestController
@Slf4j
@Api(value = "Coupon Release API",tags = { "Coupon Release API" })
public class CouponReleaseController {

    @Autowired
    private CouponReleaseComponent couponReleaseComponent;

    @ApiOperation(value = "Create a coupon release and send coupon",notes = "if success is return release code.")
    @PostMapping(value = "/coupon/createReleaseAndSendCoupon")
    public Result<String> createUaReleaseAndSendCoupon(@RequestBody UaCreateSendCouponParam uaCreateCouponParam) {

        uaCreateCouponParam.validate();

        ReleaseCouponInDTO releaseDTO = BeanCopyUtils.jsonCopyBean(uaCreateCouponParam, ReleaseCouponInDTO.class);

        Map<String, String>   outCouponMap = new HashMap<>();

        for (Iterator<String> iterator = releaseDTO.getCouponCodes().iterator(); iterator.hasNext();) {
            String code = iterator.next();
            if (StringUtil.isEmpty(code)) {
                iterator.remove();
            }
        }


        checkCouponCodeError(releaseDTO.getCouponCodes(), outCouponMap);

        if (CollectionUtils.isEmpty(releaseDTO.getCouponCodes())) {
            throw new PromotionException(ErrorCodes.EXPORT_COUPON.getCode(),ErrorCodes.EXPORT_COUPON.getMessage());
        }

        releaseDTO.setReleaseSource(CouponReleaseSourceEnum.EXPORT.code());

        return Result.ok(couponReleaseComponent.createUaReleaseAndSendCoupon(releaseDTO, outCouponMap));
    }

    /**
     * Create a coupon release
     */
    @ApiOperation(value = "Create a coupon release",notes = "Return release code")
    @PostMapping(value = "/coupon/createCouponRelease")
    public Result<String> createCouponRelease(@RequestBody CreateCouponReleaseParam param) {
        log.info("CouponReleaseController#createCouponRelease: {{}}", JSONObject.toJSONString(param));
        param.validate();
        ReleaseCouponInDTO releaseDTO = BeanCopyUtils.jsonCopyBean(param, ReleaseCouponInDTO.class);
        releaseDTO.setReleaseSource(CouponReleaseSourceEnum.SYSTEM_CREATE.code());

        Map<String, String> outCouponMap = null;
        if (!CollectionUtils.isEmpty(releaseDTO.getCouponCodes())){
			for (Iterator<String> iterator = releaseDTO.getCouponCodes().iterator(); iterator.hasNext();) {
				String code = iterator.next();
				if (StringUtil.isEmpty(code)) {
					iterator.remove();
				}
			}
            outCouponMap = new HashMap<>();
            checkCouponCodeError(releaseDTO.getCouponCodes(), outCouponMap);
            releaseDTO.setReleaseQuantity(outCouponMap.size());
            releaseDTO.setReleaseSource(CouponReleaseSourceEnum.EXPORT.code());
        }

        return Result.ok(couponReleaseComponent.releaseCoupon(releaseDTO, outCouponMap,null));
    }


    /**
     * Find a coupon release detail
     */
    @ApiOperation(value = "Find a coupon release detail",notes = "Return coupon release details")
    @PostMapping(value = "/coupon/findCouponRelease")
    public Result<FindCouponReleaseResult> findCouponRelease(@RequestBody FindCouponReleaseParam param) {

        log.info("CouponReleaseController#findCouponRelease: {{}}", JSONObject.toJSONString(param));

        param.validate();

        CouponReleaseDomain releaseDomain = this.couponReleaseComponent.findCouponReleaseByReleaseCode(param.getTenantCode(), param.getReleaseCode());

        return Result.ok(BeanCopyUtils.jsonCopyBean(releaseDomain, FindCouponReleaseResult.class));
    }

    /**
     * Query coupon release history for a coupon activity
     */
    @ApiOperation(value = "Query coupon release history for a coupon activity",notes = "Returns the ticket placement list")
    @PostMapping(value = "/coupon/queryCouponRelease")
    public PageResult<QueryCouponReleaseResult> queryCouponRelease(@RequestBody QueryCouponReleaseParam param) {
        log.info("CouponReleaseController#queryCouponRelease: {{}}", JSONObject.toJSONString(param));
        param.validate();
        RequestPage page = new RequestPage(param.getPageNo(), param.getPageCount());

        // 查询活动信息
        CouponReleaseDomain releaseDomain = new CouponReleaseDomain();
        releaseDomain.setTenantCode(param.getTenantCode());
        releaseDomain.setActivityCode(param.getActivityCode());
        releaseDomain.setReleaseStatus(null);
        PageInfo<CouponReleaseDomain> releaseDomainPage = this.couponReleaseComponent.queryCouponRelease(releaseDomain, page);
        List<QueryCouponReleaseResult> releaseResultList = BeanCopyUtils.jsonCopyList(releaseDomainPage.getList(), QueryCouponReleaseResult.class);
        return PageResult.ok(releaseResultList, releaseDomainPage.getTotal());
    }

    /**
     * Cancel a coupon release
     */
    @ApiOperation(value = "Cancel a coupon release",notes = "Successful cancellation of coupon distribution")
    @PostMapping(value = "/coupon/cancelCouponRelease")
    public Result<Object> cancelCouponRelease(@RequestBody CancelCouponReleaseParam param) {

        log.info("CouponReleaseController#cancelCouponRelease: {{}}", JSONObject.toJSONString(param));

        param.validate();

        couponReleaseComponent.cancelReleaseCoupon111(param.getTenantCode(), param.getReleaseCode());
        return Result.ok();
    }

    private void checkCouponCodeError(List<String> couponCodeList, Map<String, String> outCouponMap) {

        for (String couponCode : couponCodeList) {
			Check.check(!couponCode.matches("[0-9a-zA-Z]{1,32}"), CouponActivityChecker.COUPON_CODE_TOO_LONG, couponCode);
            Check.check(couponCode.indexOf(' ') != -1, CouponActivityChecker.DUPLICATE_COUPON_CODE_BLANK);
            Check.check(outCouponMap.containsKey(couponCode), CouponActivityChecker.DUPLICATE_COUPON_CODE, couponCode);
            outCouponMap.put(couponCode, couponCode);
        }
    }

    /**
     * 定时投放券码
     * 
     * @return 是否成功
     */
    @ApiOperation(value = "Timing of the coupon code",notes = "Timing of the coupon code")
    @ApiIgnore
    @PostMapping(value = "/coupon/release/timer/create")
    public Result<Object> couponReleaseTimer() {

        couponReleaseComponent.couponReleaseTimer();
        return Result.ok();
    }



}
