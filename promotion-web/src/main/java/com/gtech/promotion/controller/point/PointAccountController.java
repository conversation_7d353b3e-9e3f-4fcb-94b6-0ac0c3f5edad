package com.gtech.promotion.controller.point;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.ImmutableMap;
import com.gtech.commons.result.PageResult;
import com.gtech.commons.result.Result;
import com.gtech.promotion.service.point.PointAccountService;
import com.gtech.promotion.service.point.PointAccountTempService;
import com.gtech.promotion.vo.param.point.CreatePointAccountParam;
import com.gtech.promotion.vo.param.point.UpdatePointAccountParam;
import com.gtech.promotion.vo.param.point.UpdatePointParam;
import com.gtech.promotion.vo.param.point.query.GetPointAccountCampaignParam;
import com.gtech.promotion.vo.param.point.query.GetPointAccountParam;
import com.gtech.promotion.vo.param.point.query.PointAccountUniqueParam.PointAccountStatusUniqueVo;
import com.gtech.promotion.vo.param.point.query.QueryPointAccountParam;
import com.gtech.promotion.vo.result.point.PointAccountCampaignResult;
import com.gtech.promotion.vo.result.point.PointAccountResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import javax.annotation.Resource;
import java.util.Map;

@RequestMapping(value = "/point")
@RestController
@Api(value = "Point Account API", tags = "Point Account API")
@Slf4j
@ApiIgnore
public class PointAccountController {
    @Resource
	private PointAccountService pointAccountService;
	@Resource
	private PointAccountTempService pointAccountTempService;

	/**
	 * 获取积分账户列表
	 */
	@SuppressWarnings("unchecked")
	@ApiOperation(value = "Paging query points")
	@PostMapping(value = "/queryPointAccountList")
	public PageResult<PointAccountResult> queryPointAccountList(@RequestBody @Validated QueryPointAccountParam pointAccountQueryVo) {
		log.info("promotion_point:queryPointAccountList:{{}}",JSON.toJSONString(pointAccountQueryVo));
		Map<String, Object> paramMap = JSON.parseObject(JSON.toJSONString(pointAccountQueryVo), Map.class);
		return pointAccountService.queryPointAccountPage(paramMap);
	}

	/**
	 * 获取积分账户详情
	 */
	@ApiOperation(value = "get point account by point account code")
	@PostMapping(value = "/getPointAccount")
	public Result<PointAccountResult> getPointAccount(@RequestBody @Validated GetPointAccountParam param) {
		log.info("promotion_point:getPointAccount:{{}}",JSON.toJSONString(param));
	    return Result.ok(pointAccountService.getPointAccount(param));
	}

	/**
	 *  获取积分账户详情（对应积分池）
	 */
	@ApiOperation(value = "get point account by point account code and campaign code")
	@PostMapping(value = "/campaign/getPointAccount")
	public Result<PointAccountCampaignResult> getPointAccountCampaign(@RequestBody @Validated GetPointAccountCampaignParam param) {
		log.info("promotion_point:getPointAccountCampaign:{{}}",JSON.toJSONString(param));
		return Result.ok(pointAccountService.getPointAccountCampaign(param));
	}


	/**
	 *  修改积分账户状态
	 *
	 */
	@ApiOperation(value = "update point account status")
	@PostMapping(value = "/updatePointAccountStatus")
	public Result<Void> updatePointAccountStatus(@RequestBody @Validated PointAccountStatusUniqueVo accountStatusUniqueVo) {
		log.info("promotion_point:updatePointAccountStatus:{{}}",JSON.toJSONString(accountStatusUniqueVo));
		pointAccountService.updatePointAccountStatus(accountStatusUniqueVo);
		return Result.ok();
	}

	/**
	 * 新建积分账户
	 */
	@ApiOperation(value = "create point account")
	@PostMapping(value = "/createPointAccount")
	public Result<Map<String, String>> createPointAccount(@RequestBody @Validated CreatePointAccountParam pointAccountVo) {
		log.info("promotion_point:createPointAccount:{{}}",JSON.toJSONString(pointAccountVo));

		String pointAccountCode = pointAccountTempService.savePointAccount(pointAccountVo);
		return Result.ok(ImmutableMap.of("pointAccountCode", pointAccountCode));
	}

	/**:
	 * 编辑积分账户
	 */
	@ApiOperation(value = "edit point account")
	@PostMapping(value = "/editPointAccount")
	public Result<Void> editPointAccount(@RequestBody @Validated UpdatePointAccountParam pointAccountVo) {
		log.info("promotion_point:editPointAccount:{{}}",JSON.toJSONString(pointAccountVo));

		pointAccountService.updatePointAccount(pointAccountVo);
		return Result.ok();
	}

	/**
	 * 积分变更（这里其实应该是从积分池分派积分给account）
	 */
	@ApiOperation(value = "Points change")
	@PostMapping(value = "/updatePoint")
	public Result<Void> changePoint(@RequestBody UpdatePointParam param) {
		log.info("promotion_point:changePoint:{{}}",JSON.toJSONString(param));

	    param.validate();

	    pointAccountService.updatePoint(param);

	    return Result.ok();
	}

	/**
	 * 积分变更(这里是对account积分的简单增减操作)
	 */
	@ApiOperation(value = "Increase or decrease Points")
	@PostMapping(value = "/increaseOrDecreasePoint")
	public Result<Void> increaseOrDecreasePoint(@RequestBody UpdatePointParam param) {
		log.info("promotion_point:increaseOrDecreasePoint:{{}}",JSON.toJSONString(param));

		param.validate();

		pointAccountService.increaseOrDecreasePoint(param);

		return Result.ok();
	}
}
