/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.controller.activity;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.ExecutorService;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.gtech.commons.exception.ErrorCode;
import com.gtech.commons.result.PageResult;
import com.gtech.commons.result.Result;
import com.gtech.commons.utils.BeanCopyUtils;
import com.gtech.commons.utils.CheckUtils;
import com.gtech.commons.utils.StringUtil;
import com.gtech.promotion.calc.CalcExecuter;
import com.gtech.promotion.calc.model.CalcShoppingCart;
import com.gtech.promotion.checker.activity.TPromoProductChecker;
import com.gtech.promotion.checker.coupon.CouponErrorChecker;
import com.gtech.promotion.code.CodeHelper;
import com.gtech.promotion.code.activity.ActivityStoreEnum;
import com.gtech.promotion.code.activity.ActivityTypeEnum;
import com.gtech.promotion.code.activity.OpsTypeEnum;
import com.gtech.promotion.code.activity.StoreTypeEnum;
import com.gtech.promotion.component.activity.ActivityCacheDomain;
import com.gtech.promotion.component.activity.ActivityComponentDomain;
import com.gtech.promotion.component.activity.ActivityQueryDomain;
import com.gtech.promotion.component.activity.ProductDomain;
import com.gtech.promotion.component.activity.ShoppingCartDomain;
import com.gtech.promotion.dao.model.activity.ActivityFunctionParamRankModel;
import com.gtech.promotion.dao.model.activity.ActivityLanguageModel;
import com.gtech.promotion.dao.model.activity.ActivityModel;
import com.gtech.promotion.dao.model.activity.FunctionParamModel;
import com.gtech.promotion.dao.model.activity.TPromoActivityStoreVO;
import com.gtech.promotion.dto.cache.ActivityCacheDTO;
import com.gtech.promotion.dto.cache.ProductSkuDetailDTO;
import com.gtech.promotion.dto.in.activity.ShoppingCartActivity;
import com.gtech.promotion.dto.in.activity.ShoppingCartDTO;
import com.gtech.promotion.dto.in.activity.ShoppingCartItem;
import com.gtech.promotion.dto.in.activity.SingleProductDTO;
import com.gtech.promotion.dto.in.activity.TenantProductDTO;
import com.gtech.promotion.dto.out.activity.ActivityInfoDTO;
import com.gtech.promotion.dto.out.activity.ActivityPriceDTO;
import com.gtech.promotion.dto.out.activity.ActivityProductOutDTO;
import com.gtech.promotion.dto.out.activity.ShoppingCartOutDTO;
import com.gtech.promotion.dto.out.activity.SkuActivityPriceDTO;
import com.gtech.promotion.dto.out.activity.TPromoActivityOutDTO;
import com.gtech.promotion.exception.Check;
import com.gtech.promotion.exception.ErrorCodes;
import com.gtech.promotion.exception.PromotionException;
import com.gtech.promotion.page.RequestPage;
import com.gtech.promotion.service.activity.ActivityFuncParamService;
import com.gtech.promotion.service.activity.ActivityFuncRankService;
import com.gtech.promotion.service.activity.ActivityProductDetailService;
import com.gtech.promotion.service.activity.ActivityStoreService;
import com.gtech.promotion.service.coupon.PromoCouponReleaseService;
import com.gtech.promotion.utils.ActivityFilterUtil;
import com.gtech.promotion.utils.ActivityProductCheckUtil;
import com.gtech.promotion.vo.bean.FunctionParam;
import com.gtech.promotion.vo.bean.GiveawaySettings;
import com.gtech.promotion.vo.bean.IncentiveLimited;
import com.gtech.promotion.vo.bean.Qualification;
import com.gtech.promotion.vo.param.activity.*;
import com.gtech.promotion.vo.result.activity.ActivityItemResult;
import com.gtech.promotion.vo.result.activity.CalcShoppingCartResult;
import com.gtech.promotion.vo.result.activity.CalcSkuPromotionPriceResult;
import com.gtech.promotion.vo.result.activity.DiscountPriceResult;
import com.gtech.promotion.vo.result.activity.QueryActivityByProductListResult;
import com.gtech.promotion.vo.result.activity.QueryActivityListByProductListResult;
import com.gtech.promotion.vo.result.activity.QueryActivityListByProductResult;
import com.gtech.promotion.vo.result.activity.QueryProductListByActivityResult;
import com.gtech.promotion.vo.result.activity.QueryPromoListByStoreResult;
import com.gtech.promotion.vo.result.flashsale.MarketingLanguageResult;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import springfox.documentation.annotations.ApiIgnore;

/**
 * 
 *Shopping mall activities and goods query and shopping cart calculation
 * <AUTHOR>
 * @Date 2020-02-12
 */
@Slf4j
@Api(value = "Mall Activity API",tags = {"Mall Activity API"})
@RestController
public class MallActivityController {

    @Autowired
    private ActivityCacheDomain activityCacheDomain;

    @Autowired
    private ActivityQueryDomain queryDomain;

    @Autowired
    private CalcExecuter calcExecuter;

    @Autowired
    private ProductDomain productDomain;

    @Autowired
    private ShoppingCartDomain shoppingCartDomain;

    @Autowired
    private ActivityComponentDomain tPromoActivityDomain;
    @Autowired
    private ActivityProductDetailService productDetailService;

    @Autowired
    @Qualifier("queryActivityListByProductListThreadPool")
    private ExecutorService promotionThreadPoolConfig;


    @Autowired
    private ActivityStoreService activityStoreService;

    @Autowired
    private PromoCouponReleaseService promoCouponReleaseService;

    @Autowired
    private ActivityFuncRankService activityFuncRankService;

    @Autowired
    private ActivityFuncParamService activityFuncParamService;


    /**
     * 单品活动价格计算： 根据商品信息查询并计算其参与的单品促销价格
     */
    @ApiOperation(value = "(PDP) Calculate single product promotion price",notes = "(PDP) Calculate single product promotion price") //店铺待定
    @PostMapping(value = "/activity/calcSkuPromotionPrice")
    public Result<CalcSkuPromotionPriceResult> calcSkuPromotionPrice(@RequestBody CalcSkuPromotionPriceParam param) {

        log.info("促销商品信息:{}", JSONObject.toJSONString(param));

        // Parameter validation.
        param.validate();

        SingleProductDTO product = BeanCopyUtils.jsonCopyBean(param, SingleProductDTO.class);

        ActivityProductCheckUtil.checkProductCombine(product);
        product.setOrgCodes(CodeHelper.getOrgCodes(product.getOrgCodes()));

        Map<String, ActivityCacheDTO> activityCacheMap = activityCacheDomain.getActivityCacheMap(product.getTenantCode(), param.getLanguage(), 2, ActivityTypeEnum.ACTIVITY);

        activityCacheMap = ActivityFilterUtil.filterActivityByOrgCodes(activityCacheMap, product.getOrgCodes());
        activityCacheMap = ActivityFilterUtil.filterActivityByQualifications(activityCacheMap, product.getQualifications());

        SkuActivityPriceDTO skuActivityPriceDTO = tPromoActivityDomain.activitySkuPrice(product, activityCacheMap);

        if (null == skuActivityPriceDTO || CollectionUtils.isEmpty(skuActivityPriceDTO.getActivity())) {
            return Result.ok();
        }

        ActivityPriceDTO activityPriceDTO = skuActivityPriceDTO.getActivity().get(0);

        return Result.ok(BeanCopyUtils.jsonCopyBean(activityPriceDTO, CalcSkuPromotionPriceResult.class));
    }

    @ApiOperation(value = "(PDP) Query activity list by product",notes = "(PDP) Query activity list by product") //店铺待定
    @PostMapping("/activity/queryActivityListByProduct")
    public Result<List<QueryActivityListByProductResult>> queryActivityListByProduct(@RequestBody QueryActivityListByProductParam param){

        log.info("促销根据商品查询活动列表入参:{}", JSONObject.toJSONString(param));

        param.validate();

        TenantProductDTO product = BeanCopyUtils.jsonCopyBean(param, TenantProductDTO.class);

        if (StringUtils.isNotBlank(param.getOrgCodes())) {
            product.setOrgCodes(Arrays.asList(param.getOrgCodes().split(",")));
        } else {
            product.setOrgCodes(Arrays.asList(ActivityStoreEnum.ALL.code()));
        }

        ActivityProductCheckUtil.checkProductCombine(product);

        //该商户已审核成功的活动
        Map<String, ActivityCacheDTO> activityCacheMap = activityCacheDomain.getActivityCacheMap(product.getTenantCode(), product.getLanguage(), 2, ActivityTypeEnum.ACTIVITY);

        // 过滤符合的活动
        Map<String, ActivityCacheDTO> activityCacheMap1 = ActivityFilterUtil.filterActivityByActivityType(activityCacheMap, ActivityTypeEnum.ACTIVITY);
        Map<String, ActivityCacheDTO> activityCacheMap2 = ActivityFilterUtil.filterActivityByTime(activityCacheMap1, null);
        Map<String, ActivityCacheDTO> activityCacheMap3 = ActivityFilterUtil.filterActivityByQualifications(activityCacheMap2, product.getQualifications());
        List<ProductSkuDetailDTO> productSkuDetailDTOS = productDetailService.queryListByActivityCodesAndProductCode(activityCacheMap3.keySet(), product.getProductCode());
        return Result.ok(BeanCopyUtils.jsonCopyList(queryDomain.getActivityByProduct(product, activityCacheMap3, productSkuDetailDTOS), QueryActivityListByProductResult.class));
    }

    @ApiOperation(value = "(PLP) Query activity list by product list") //店铺待定
    @PostMapping("/activity/queryActivityListByProductList")
    public Result<List<QueryActivityListByProductListResult>> queryActivityListByProductList(@RequestBody QueryActivityListByProductListParam param){

        log.info("促销根据批量商品查询活动列表入参:{}", JSONObject.toJSONString(param));

        param.validate();

        //该商户已审核成功的活动
        Map<String, ActivityCacheDTO> activityCacheMap = activityCacheDomain.getActivityCacheMap(param.getTenantCode(), param.getLanguage(), 2, ActivityTypeEnum.ACTIVITY);

        // 过滤符合的活动
        activityCacheMap = ActivityFilterUtil.filterActivityByActivityType(activityCacheMap, ActivityTypeEnum.ACTIVITY);
        //可根据预热时间过滤，以及兼容之前时间过滤
        activityCacheMap = ActivityFilterUtil.filterActivityByWarmTime(activityCacheMap, null,param.getWarmBeginFrom());
        activityCacheMap = ActivityFilterUtil.filterActivityByQualifications(activityCacheMap, param.getQualifications());

        List<QueryActivityListByProductListResult> list = new CopyOnWriteArrayList<>();
        List<TenantProductDTO> list1 = new ArrayList<>();
        List<String> productCodes = new ArrayList<>();
        for (QueryActivityListByProductListParam.Product product : param.getProducts()) {
            TenantProductDTO productDTO = BeanCopyUtils.jsonCopyBean(product, TenantProductDTO.class);
            ActivityProductCheckUtil.checkProductCombine(productDTO);
            if (StringUtils.isNotBlank(product.getOrgCodes())) {
                productDTO.setOrgCodes(Arrays.asList(product.getOrgCodes().split(",")));
            } else {
                productDTO.setOrgCodes(Collections.singletonList(ActivityStoreEnum.ALL.code()));
            }
            list1.add(productDTO);
            if (!productCodes.contains(productDTO.getProductCode())){
                productCodes.add(productDTO.getProductCode());
            }
        }
        List<ProductSkuDetailDTO> productSkuDetailDTOS = productDetailService.queryListByActivityCodesAndProductCodes(activityCacheMap.keySet(), productCodes);


        //CountDownLatch countDownLatch = new CountDownLatch(list1.size());

        for (TenantProductDTO productDTO : list1) {
            queryActivityListByProductListThread(activityCacheMap, list, productSkuDetailDTOS, productDTO);
        }
        /*try {
            countDownLatch.await();
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }*/

        //给打折促销类型的活动添加优惠规则信息
        funcParam(activityCacheMap, list);


        return Result.ok(list);
    }

    public void funcParam(Map<String, ActivityCacheDTO> activityCacheMap, List<QueryActivityListByProductListResult> list) {
        for (QueryActivityListByProductListResult productListResult : list) {
            if (null == productListResult){
                //防止因线程问题产生的空指针
                continue;
            }
            if (CollectionUtils.isEmpty(productListResult.getActivityList())) {
                continue;
            }
            List<QueryActivityListByProductResult> activityList = productListResult.getActivityList();
            for (QueryActivityListByProductResult activity1 : activityList) {
                ActivityCacheDTO activityCacheDTO = activityCacheMap.get(activity1.getActivityCode());
                if(activityCacheDTO != null && MapUtils.isNotEmpty(activityCacheDTO.getLanguageMap())) {
                    List<ActivityLanguageModel> languageList = new ArrayList<>(activityCacheDTO.getLanguageMap().values());
                    activity1.setLanguageResults(BeanCopyUtils.jsonCopyList(languageList, MarketingLanguageResult.class));
                }
            }

            List<QueryActivityListByProductResult> ops101ActivityList = activityList.stream()
                    .filter(x -> x.getOpsType().equals(OpsTypeEnum.OPS_101.code())).collect(Collectors.toList());
            if(CollectionUtils.isEmpty(ops101ActivityList)){
                continue;
            }
            for (QueryActivityListByProductResult activity : ops101ActivityList) {
                ActivityCacheDTO activityCacheDTO = activityCacheMap.get(activity.getActivityCode());
                List<ActivityFunctionParamRankModel> rankModelList = activityCacheDTO.getPromoFuncRanks();
                List<FunctionParamModel> promoFuncParams = activityCacheDTO.getPromoFuncParams();
                setRankAndFunctionParam(rankModelList, promoFuncParams);
                activity.setFuncParams(BeanCopyUtils.jsonCopyList(promoFuncParams, FunctionParam.class));
            }
        }
    }

    public void queryActivityListByProductListThread(Map<String, ActivityCacheDTO> copyMap, List<QueryActivityListByProductListResult> list, List<ProductSkuDetailDTO> productSkuDetailDTOS,  TenantProductDTO productDTO) {


        Map<String, List<ProductSkuDetailDTO>> activitySkuMap = productSkuDetailDTOS.stream().collect(Collectors.groupingBy(ProductSkuDetailDTO::getActivityCode));

        //promotionThreadPoolConfig.execute(() -> {
            //try {
                queryActivityListByProductList(copyMap, list, productSkuDetailDTOS, productDTO, activitySkuMap);
           // } catch (Exception e) {
             //   log.error("queryActivityListByProductList:{}", e.getMessage());
            //} finally {
              //  countDownLatch.countDown();
           // }
        //});
    }

    public void queryActivityListByProductList(Map<String, ActivityCacheDTO> copyMap, List<QueryActivityListByProductListResult> list, List<ProductSkuDetailDTO> productSkuDetailDTOS, TenantProductDTO productDTO, Map<String, List<ProductSkuDetailDTO>> activitySkuMap) {
        List<ActivityInfoDTO> activityByProduct = queryDomain.getActivityByProduct(productDTO, copyMap, productSkuDetailDTOS);

        List<QueryActivityListByProductResult> queryActivityListByProductResults = BeanCopyUtils.jsonCopyList(activityByProduct, QueryActivityListByProductResult.class);

        QueryActivityListByProductListResult listResult = new QueryActivityListByProductListResult();
        listResult.setProductCode(productDTO.getProductCode());
        listResult.setSkuCode(productDTO.getSkuCode());
        listResult.setCombineSkuCode(productDTO.getCombineSkuCode());

        BigDecimal promoPrice;
        for (QueryActivityListByProductResult result : queryActivityListByProductResults) {
            List<ProductSkuDetailDTO> productSkuDetailDTOS1 = activitySkuMap.get(result.getActivityCode());

            if (CollectionUtils.isNotEmpty(productSkuDetailDTOS1)){

                Map<String, BigDecimal> skuMap = productSkuDetailDTOS1.stream().filter(x -> StringUtil.isNotEmpty(x.getSkuCode()) && null != x.getPromoPrice()).collect(Collectors.toMap(ProductSkuDetailDTO::getSkuCode, ProductSkuDetailDTO::getPromoPrice));
                Map<String, BigDecimal> spuMap = productSkuDetailDTOS1.stream().filter(x -> StringUtil.isEmpty(x.getSkuCode()) && null != x.getPromoPrice()).collect(Collectors.toMap(ProductSkuDetailDTO::getProductCode, ProductSkuDetailDTO::getPromoPrice));
                promoPrice = skuMap.get(productDTO.getSkuCode());
                if (null == promoPrice) {
                    promoPrice = spuMap.get(productDTO.getProductCode());
                }
                if (null != promoPrice) {
                    result.setPromoPrice(String.valueOf(promoPrice));
                }
            }
        }

        listResult.setActivityList(queryActivityListByProductResults);
        list.add(listResult);
    }


    @ApiOperation(value = "Query product list by activity",notes = "Query product list by activity")
    @PostMapping("/activity/queryProductListByActivity")
    public Result<QueryProductListByActivityResult> queryProductListByActivity(@RequestBody QueryProductListByActivityParam param){

        log.info("促销根据活动编码查询商品信息:{{}}", JSONObject.toJSONString(param));
        param.validate();
        RequestPage page = new RequestPage(param.getPageNum(), param.getPageSize());
        Integer seqNum = param.getSeqNum();
        seqNum = seqNum == null ? 1 : seqNum;

        ActivityProductOutDTO activityProductOutDTO = productDomain.queryActivityProductByCode(param.getTenantCode(), param.getLanguage(), param.getActivityCode(), seqNum, page);

        return Result.ok(BeanCopyUtils.jsonCopyBean(activityProductOutDTO, QueryProductListByActivityResult.class));
    }

    @ApiOperation(value = "Filter coupon",notes = "Filter coupon codes")
    @PostMapping("/activity/filterCoupon")
    public Result<List<String>> filterCoupon(@RequestBody FilterCouponParam param){

        param.validate();

        return Result.ok(shoppingCartDomain.filterCouponDomain(param.getTenantCode(), param.getCouponCodes()));
    }

    @ApiOperation(value = "Calc shopping cart",notes = "Calc shopping cart")
    @PostMapping(value = "/activity/calcShoppingCart")
    public Result<List<CalcShoppingCartResult>> calcShoppingCart(@RequestBody CalcShoppingCartParam param) {

        log.info("促销购物车入参：{}", JSONObject.toJSONString(param));
        param.validate();
        ShoppingCartDTO shoppingCart = BeanCopyUtils.jsonCopyBean(param, ShoppingCartDTO.class);
        shoppingCart.setUserCode(param.getMemberCode());
        //获取规则缓存
        Map<String, ActivityCacheDTO> cacheMap = activityCacheDomain.getActivityCacheMap(shoppingCart.getTenantCode(), param.getLanguage(), 2, null);
        Map<String, ActivityCacheDTO> ruleCacheMap = activityCacheDomain.filterActivityByCustomCondition(shoppingCart.getTenantCode(), cacheMap, param.getCustomConditionMap());

        if (MapUtils.isEmpty(ruleCacheMap)) {
            String couponCodes = shoppingCart.getCouponCodes();
            if (StringUtil.isNotBlank(couponCodes)) {
                Check.check(true, CouponErrorChecker.NO_EFFECTIVE);
            }
            return Result.ok(new ArrayList<>());
        }
        // 查询
        shoppingCart = shoppingCartDomain.queryActivity(shoppingCart, ruleCacheMap,false);

        // 计算
        List<ShoppingCartOutDTO> scoDtoList = calcExecuter.calc(new CalcShoppingCart(shoppingCart), ruleCacheMap);
        if (CollectionUtils.isEmpty(scoDtoList)) {
            log.info("scoDtoList is null");
            Result.ok();
        }

        List<CalcShoppingCartResult> resultList = new ArrayList<>();
        for(ShoppingCartOutDTO sco : scoDtoList) {

            CalcShoppingCartResult scResult = BeanCopyUtils.jsonCopyBean(sco, CalcShoppingCartResult.class);
            scResult.setExtendParams(sco.getExtendParams());
            scResult.setGiveawaySettings(JSONObject.toJavaObject(sco.getExtendParams(), GiveawaySettings.class));
            ErrorCode failedReason = sco.getFailedReason();
            if (null != failedReason) {
                scResult.setFalseReason(failedReason.getMessage());
                scResult.setFalseCode(failedReason.getCode());
            }

            ActivityCacheDTO activityCacheDTO = ruleCacheMap.get(sco.getActivityCode());
            if(activityCacheDTO != null && MapUtils.isNotEmpty(activityCacheDTO.getLanguageMap())) {
                List<ActivityLanguageModel> languageList = new ArrayList<>(activityCacheDTO.getLanguageMap().values());
                scResult.setLanguageResults(BeanCopyUtils.jsonCopyList(languageList, MarketingLanguageResult.class));
            }

            resultList.add(scResult);
        }

        return Result.ok(resultList);
    }

    @ApiOperation(value = "Query activity list by online store",notes = "Query activity list by online store")
    @PostMapping("/activity/queryPromoListByStore")
    public PageResult<QueryPromoListByStoreResult> queryPromoListByStore(@RequestBody QueryPromoListByStoreParam param){

        log.info("根据店铺编码查询活动列表:{{}}", JSONObject.toJSONString(param));
        param.validate();
        PageInfo<TPromoActivityOutDTO> activityOutDTOs = tPromoActivityDomain.queryPromoListByStore(param);
        return PageResult.ok(BeanCopyUtils.jsonCopyList(activityOutDTOs.getList(), QueryPromoListByStoreResult.class), activityOutDTOs.getTotal());
    }

    @ApiOperation(value = "Query activities based on shopping cart items",notes = "Query activities based on shopping cart items")
    @PostMapping(value = "/activity/queryActivityByShoppingCartProduct")
    public Result<List<QueryActivityByProductListResult>> queryActivityByShoppingCartProduct(@RequestBody CalcShoppingCartParam param) {
        log.info("根据购物车商品查询活动信息入参：{}", JSONObject.toJSONString(param));
        param.validate();
        ShoppingCartDTO shoppingCart = BeanCopyUtils.jsonCopyBean(param, ShoppingCartDTO.class);
        shoppingCart.setUserCode(param.getMemberCode());
        //获取规则缓存
        Map<String, ActivityCacheDTO> ruleCacheMap = activityCacheDomain.getActivityCacheMap(shoppingCart.getTenantCode(), param.getLanguage(), 2, null);
        if (MapUtils.isEmpty(ruleCacheMap)) {
            return Result.ok(new ArrayList<>());
        }
        // 查询
        shoppingCart = shoppingCartDomain.queryActivityByShoppingCartProduct(shoppingCart, ruleCacheMap);
        //活动编码-活动实体
        Map<String,ActivityModel> activityMap = new HashMap<>();
        //活动编码对应所属sku
        Map<String,List<ActivityItemResult>> skuMap = new HashMap<>();
        for (ShoppingCartItem shoppingCartItem : shoppingCart.getPromoProducts()) {
            List<ShoppingCartActivity> activityList = shoppingCartItem.getUsedActivitys();
            if (CollectionUtils.isNotEmpty(activityList)) {
                setActivityRelationProduct(activityMap, skuMap, shoppingCartItem, activityList);
            }
        }
        //出参结果封装
        List<QueryActivityByProductListResult> results = Lists.newArrayList();
        //活动遍历
        for (Map.Entry<String, List<ActivityItemResult>> stringListEntry : skuMap.entrySet()) {
            String activityCode = stringListEntry.getKey();
            QueryActivityByProductListResult result = new QueryActivityByProductListResult();
            ActivityModel activityModel = activityMap.get(activityCode);
            List<ActivityItemResult> activityItems = skuMap.get(activityCode);
            BeanCopyUtils.copyProps(activityModel,result);
            result.setActivityItems(activityItems);

            ActivityCacheDTO activityCacheDTO = ruleCacheMap.get(activityCode);

            List<ActivityFunctionParamRankModel> rankModelList = activityCacheDTO.getPromoFuncRanks();
            List<FunctionParamModel> promoFuncParams = activityCacheDTO.getPromoFuncParams();
            setRankAndFunctionParam(rankModelList, promoFuncParams);
            result.setFuncParams(BeanCopyUtils.jsonCopyList(promoFuncParams, FunctionParam.class));
            result.setLimitedList(BeanCopyUtils.jsonCopyList(activityCacheDTO.getIncentiveLimiteds(), IncentiveLimited.class));
            result.setQualifications(BeanCopyUtils.jsonCopyList(activityCacheDTO.getQualificationModels(), Qualification.class));
            //赠品
            result.setGiveaways(activityCacheDTO.getGiveaways());
            results.add(result);
        }
        return Result.ok(results);
    }

    public void setRankAndFunctionParam(List<ActivityFunctionParamRankModel> rankModelList, List<FunctionParamModel> promoFuncParams) {
        for (ActivityFunctionParamRankModel rankModel : rankModelList) {
            for (FunctionParamModel promoFuncParam : promoFuncParams) {
                if (rankModel.getId().equals(promoFuncParam.getRankId())){
                    promoFuncParam.setRankParam(rankModel.getRankParam());
                }
            }
        }
    }

    public void setActivityRelationProduct(Map<String, ActivityModel> activityMap, Map<String, List<ActivityItemResult>> skuMap, ShoppingCartItem shoppingCartItem, List<ShoppingCartActivity> activityList) {
        //遍历商品下活动
        for (ShoppingCartActivity usedActivity : activityList) {
            if (skuMap.containsKey(usedActivity.getActivityCode())){
                List<ActivityItemResult> skuProducts = new ArrayList<>();
                List<ActivityItemResult> activityItemList = skuMap.get(usedActivity.getActivityCode());
                ActivityItemResult activityItem = new ActivityItemResult();
                activityItem.setSkuCode(shoppingCartItem.getSkuCode());
                activityItem.setProductCode(shoppingCartItem.getProductCode());
                skuProducts.add(activityItem);
                skuProducts.addAll(activityItemList);
                //填充活动对应多个sku
                skuMap.put(usedActivity.getActivityCode(),skuProducts);
            }else {
                ActivityItemResult activityItem = new ActivityItemResult();
                //存储活动对应商品
                List<ActivityItemResult> activityItems = Lists.newArrayList();
                activityItem.setSkuCode(shoppingCartItem.getSkuCode());
                activityItem.setProductCode(shoppingCartItem.getProductCode());
                activityItems.add(activityItem);
                skuMap.put(usedActivity.getActivityCode(),activityItems);
            }
            //对应每一个活动实体
            if (!activityMap.containsKey(usedActivity.getActivityCode())){
                ActivityModel activityModel = usedActivity.getActivityModel();
                activityMap.put(usedActivity.getActivityCode(),activityModel);
            }
        }
    }

    @ApiOperation(value = "Query activity discount price",notes = "Query activity discount price")
    @PostMapping("/activity/queryAfterDiscountPrice")
    public Result<DiscountPriceResult> queryAfterDiscountPrice(@RequestBody QueryAfterDiscountPriceParam param){
        log.info("start queryAfterDiscountPrice!param:{}", JSONObject.toJSONString(param));
        CheckUtils.isNotBlank(param.getTenantCode(), ErrorCodes.PARAM_EMPTY, "tenantCode");
        if(CollectionUtils.isEmpty(param.getItemList())){
            throw new PromotionException(TPromoProductChecker.NULL_SKU);
        }
        for(QueryAfterDiscountItemParam item : param.getItemList()){
            if(null == item.getSalePrice()){
                throw new PromotionException(TPromoProductChecker.ERROR_SALE_PRICE_EMPTY);
            }
            if(StringUtil.isEmpty(item.getSkuCode())){
                throw new PromotionException(TPromoProductChecker.NOT_NULL_SKU_CODE);
            }
            if(StringUtil.isEmpty(item.getProductCode())){
                throw new PromotionException(TPromoProductChecker.NOT_NULL_PRODUCT_CODE);
            }
        }
        return tPromoActivityDomain.queryAfterDiscountPrice(param);
    }



	/**
	 * 根据商品列表和活动列表进行匹配，返回活动及其匹配的商品
	 * @param param 包含商品列表和活动列表的参数
	 * @return 活动列表，每个活动下挂参与该活动的商品
	 */
	@ApiOperation(value = "Match products to activities", notes = "Match products to activities and return activities with matched products")
	@PostMapping(value = "/activity/matchProductsToActivities")
	public Result<List<QueryActivityByProductListResult>> matchProductsToActivities(@RequestBody MatchProductsToActivitiesParam param) {
		log.info("商品活动匹配入参：{}", JSONObject.toJSONString(param));
		param.validate();

		// 1. 构建购物车对象（直接从参数转换）
		ShoppingCartDTO shoppingCart = BeanCopyUtils.jsonCopyBean(param, ShoppingCartDTO.class);
		shoppingCart.setUserCode(param.getMemberCode());

		// 2. 获取活动缓存，如果指定了活动列表则进行过滤
		Map<String, ActivityCacheDTO> ruleCacheMap = getFilteredActivityCache(param);
		if (MapUtils.isEmpty(ruleCacheMap)) {
			return Result.ok(new ArrayList<>());
		}

		// 3. 执行商品活动匹配
		shoppingCart = shoppingCartDomain.queryActivityByShoppingCartProduct(shoppingCart, ruleCacheMap);

		// 4. 组织返回结果：以活动为主体，商品为子项
		List<QueryActivityByProductListResult> results = buildActivityProductResults(shoppingCart, ruleCacheMap);

		return Result.ok(results);
	}



	/**
	 * 获取过滤后的活动缓存
	 */
	private Map<String, ActivityCacheDTO> getFilteredActivityCache(MatchProductsToActivitiesParam param) {
		// 获取所有活动缓存
		Map<String, ActivityCacheDTO> allActivityCache = activityCacheDomain.getActivityCacheMap(
			param.getTenantCode(), param.getLanguage(), 2, null);

		// 如果指定了活动列表，则进行过滤
		if (CollectionUtils.isNotEmpty(param.getActivityCodes())) {
			Map<String, ActivityCacheDTO> filteredCache = new HashMap<>();
			for (String activityCode : param.getActivityCodes()) {
				if (allActivityCache.containsKey(activityCode)) {
					filteredCache.put(activityCode, allActivityCache.get(activityCode));
				}
			}
			return filteredCache;
		}

		return allActivityCache;
	}

	/**
	 * 构建活动商品匹配结果
	 */
	private List<QueryActivityByProductListResult> buildActivityProductResults(
			ShoppingCartDTO shoppingCart, Map<String, ActivityCacheDTO> ruleCacheMap) {

		// 活动编码-活动实体
		Map<String, ActivityModel> activityMap = new HashMap<>();
		// 活动编码对应所属sku
		Map<String, List<ActivityItemResult>> skuMap = new HashMap<>();

		// 遍历商品，收集活动和商品的关系
		for (ShoppingCartItem shoppingCartItem : shoppingCart.getPromoProducts()) {
			List<ShoppingCartActivity> activityList = shoppingCartItem.getUsedActivitys();
			if (CollectionUtils.isNotEmpty(activityList)) {
				setActivityRelationProduct(activityMap, skuMap, shoppingCartItem, activityList);
			}
		}

		// 构建返回结果
		List<QueryActivityByProductListResult> results = new ArrayList<>();
		for (Map.Entry<String, List<ActivityItemResult>> entry : skuMap.entrySet()) {
			String activityCode = entry.getKey();
			QueryActivityByProductListResult result = new QueryActivityByProductListResult();

			// 设置活动基本信息
			ActivityModel activityModel = activityMap.get(activityCode);
			BeanCopyUtils.copyProps(activityModel, result);
			result.setActivityItems(entry.getValue());

			// 设置活动详细信息
			ActivityCacheDTO activityCacheDTO = ruleCacheMap.get(activityCode);
			if (activityCacheDTO != null) {
				List<ActivityFunctionParamRankModel> rankModelList = activityCacheDTO.getPromoFuncRanks();
				List<FunctionParamModel> promoFuncParams = activityCacheDTO.getPromoFuncParams();
				setRankAndFunctionParam(rankModelList, promoFuncParams);
				result.setFuncParams(BeanCopyUtils.jsonCopyList(promoFuncParams, FunctionParam.class));
				result.setLimitedList(BeanCopyUtils.jsonCopyList(activityCacheDTO.getIncentiveLimiteds(), IncentiveLimited.class));
				result.setQualifications(BeanCopyUtils.jsonCopyList(activityCacheDTO.getQualificationModels(), Qualification.class));
				result.setGiveaways(activityCacheDTO.getGiveaways());
			}

			results.add(result);
		}

		return results;
	}






    //QueryActivityMallParam
    @ApiOperation(value = "Query mall activity list")
    @PostMapping("/activity/queryMallActivityListByCondition")
    public Result<List<ActivityInfoDTO>> queryMallActivityListByCondition(@RequestBody QueryActivityMallParam param){

        log.info("商城根据促销条件查询状态为04的活动列表:{}", JSONObject.toJSONString(param));

        param.validate();
        Map<String, ActivityCacheDTO> activityCacheMap;
        //该商户已审核成功的活动
        activityCacheMap = getStringActivityCacheDTOMap(param);

        //自定义条件过滤
        activityCacheMap = activityCacheDomain.queryActivityByCustomCondition(activityCacheMap,param.getCustomConditionMap());

        if (StringUtils.isNotBlank(param.getReceiveStartTime()) || StringUtils.isNotBlank(param.getReceiveEndTime())){
            activityCacheMap= filterActivityCouponByReceiveTime(activityCacheMap, param);
        }
        //活动时间过滤符合的活动,没有按当前时间过滤
        if (StringUtils.isNotBlank(param.getActivityStartTime()) || StringUtils.isNotBlank(param.getActivityEndTime())){
            activityCacheMap = ActivityFilterUtil.filterActivityByActivityTime(activityCacheMap, param.getActivityStartTime(),param.getActivityEndTime());

        }else {
            activityCacheMap = ActivityFilterUtil.filterActivityByTime(activityCacheMap, null);
        }

        if (null != param.getQualifications() && !param.getQualifications().isEmpty()){
            activityCacheMap = ActivityFilterUtil.filterActivityByQualifications(activityCacheMap, param.getQualifications());
        }

        if (null != param.getProductCondition()){
            activityCacheMap =  activityCacheDomain.filterMallActivityByProduct(activityCacheMap, param.getProductCondition(),param.getConditionProductType());
        }

        List<ActivityInfoDTO> activityInfoDTOS = queryDomain.cacheConvertActivity(activityCacheMap);

        return Result.ok(activityInfoDTOS);
    }

	@ApiIgnore
	@PostMapping("/activity/compensationUseLimit")
	public void compensationUseLimit() {
		activityCacheDomain.compensationCouponUseLimit();
	}

    public Map<String, ActivityCacheDTO> getStringActivityCacheDTOMap(@RequestBody QueryActivityMallParam param) {
        Map<String, ActivityCacheDTO> activityCacheMap;
        if (ActivityTypeEnum.ACTIVITY.code().equals(param.getActivityType())){
             activityCacheMap = activityCacheDomain.getActivityCacheMap(param.getTenantCode(), param.getLanguage(), 2, ActivityTypeEnum.ACTIVITY);
            activityCacheMap = filterActivityByCondition(activityCacheMap, ActivityTypeEnum.ACTIVITY,param);
        }else if (ActivityTypeEnum.COUPON.code().equals(param.getActivityType())){
            activityCacheMap = activityCacheDomain.getActivityCacheMap(param.getTenantCode(), param.getLanguage(), 2, ActivityTypeEnum.COUPON);
            activityCacheMap = filterActivityByCondition(activityCacheMap, ActivityTypeEnum.COUPON,param);
        }else {
           activityCacheMap = activityCacheDomain.getActivityCacheMap(param.getTenantCode(), param.getLanguage(), 2, null);
            activityCacheMap = filterActivityByCondition(activityCacheMap, null,param);
        }
        return activityCacheMap;
    }

    public  Map<String, ActivityCacheDTO> filterActivityByCondition(Map<String, ActivityCacheDTO> activityMap, ActivityTypeEnum activityType, QueryActivityMallParam param) {

        Map<String, ActivityCacheDTO> newCaches = new HashMap<>();
        if (MapUtils.isEmpty(activityMap)) {
            return activityMap;
        }
        for(Map.Entry<String, ActivityCacheDTO> e : activityMap.entrySet()) {
            ActivityModel activity = e.getValue().getActivityModel();
            if (null == activityType && (activity.getActivityType().equals(com.gtech.promotion.code.marketing.ActivityTypeEnum.LUCKY_DRAW.code())
                    || activity.getActivityType().equals(com.gtech.promotion.code.marketing.ActivityTypeEnum.FLASH_SALE.code())
                    || activity.getActivityType().equals(com.gtech.promotion.code.marketing.ActivityTypeEnum.PRE_SALE.code()))) {
                continue;
            }

            //活动条件过滤.店铺
            if (filterByActivityCondition(param, activity)) continue;
            newCaches.put(e.getKey(), e.getValue());

        }
        return newCaches;
    }

    public boolean filterByActivityCondition(QueryActivityMallParam param, ActivityModel activity) {
        if (StringUtils.isNotBlank(param.getActivityName()) && !activity.getActivityName().contains(param.getActivityName())) {

            return true;
        }
        if (StringUtils.isNotBlank(param.getActivityCode()) && !activity.getActivityCode().equals(param.getActivityCode())) {

            return true;

        }
        //促销所属orgCode
        if (StringUtils.isNotBlank(param.getActivityOrgCode()) && !activity.getOrgCode().equals(param.getActivityOrgCode())) {

            return true;

        }

        if (activity.getStoreType().equals(StoreTypeEnum.CUSTOM.code()) && StringUtils.isNotBlank(param.getStoreOrgCode())) {
            List<TPromoActivityStoreVO> storesByActivityCode = activityStoreService.getStoresByActivityCode(activity.getActivityCode());
            boolean flag = storesByActivityCode.stream().anyMatch(y -> y.getOrgCode().equals(param.getStoreOrgCode()));
            if (!flag) {
                return  true;
            }
        }

        return false;
    }

    public  Map<String, ActivityCacheDTO> filterActivityCouponByReceiveTime(Map<String, ActivityCacheDTO> activityMap, QueryActivityMallParam param) {
        if (MapUtils.isEmpty(activityMap)) {
            return activityMap;
        }

        List<String> list = promoCouponReleaseService.queryActivityCodeByReceiveTime(param.getTenantCode(), param.getReceiveStartTime(), param.getActivityEndTime());

        Map<String, ActivityCacheDTO> newCaches = new HashMap<>();
        for (Map.Entry<String, ActivityCacheDTO> e : activityMap.entrySet()) {
            String value = e.getKey();
            if (list.contains(value)){
                newCaches.put(e.getKey(), e.getValue());
            }
        }
        return newCaches;
    }

}
