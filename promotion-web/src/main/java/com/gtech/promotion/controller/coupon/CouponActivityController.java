/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 * <p>
 * This software is the confidential and proprietary information of GTech. You shall not disclose such
 * Confidential Information and shall use it only in accordance with the terms of the license agreement
 * you entered into with GTech.
 * <p>
 * <PERSON><PERSON><PERSON> MAKES NO REPRESENTATIONS OR WA<PERSON><PERSON>NTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.controller.coupon;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.gtech.basic.masterdata.response.JsonResult;
import com.gtech.commons.result.PageResult;
import com.gtech.commons.result.Result;
import com.gtech.commons.utils.BeanCopyUtils;
import com.gtech.commons.utils.CheckUtils;
import com.gtech.commons.utils.StringUtil;
import com.gtech.promotion.checker.SystemChecker;
import com.gtech.promotion.checker.activity.TPromoActivityChecker;
import com.gtech.promotion.checker.coupon.CouponActivityChecker;
import com.gtech.promotion.checker.coupon.CouponCodeUserChecker;
import com.gtech.promotion.checker.coupon.CouponExportChecker;
import com.gtech.promotion.code.CodeHelper;
import com.gtech.promotion.code.activity.ActivityStatusEnum;
import com.gtech.promotion.code.activity.TurnOnAndOffEnum;
import com.gtech.promotion.code.coupon.CouponStatusEnum;
import com.gtech.promotion.code.coupon.CouponTypeEnum;
import com.gtech.promotion.component.coupon.CouponActivityComponent;
import com.gtech.promotion.controller.BaseController;
import com.gtech.promotion.dao.model.activity.ActivityLanguageModel;
import com.gtech.promotion.dao.model.activity.ActivityModel;
import com.gtech.promotion.dao.model.coupon.CouponReleaseModel;
import com.gtech.promotion.dao.model.coupon.TPromoCouponInnerCodeVO;
import com.gtech.promotion.dto.in.activity.TenantCodeInDTO;
import com.gtech.promotion.dto.in.coupon.*;
import com.gtech.promotion.dto.out.coupon.*;
import com.gtech.promotion.exception.Check;
import com.gtech.promotion.exception.ErrorCodes;
import com.gtech.promotion.feign.MasterDataFeignClient;
import com.gtech.promotion.service.activity.ActivityLanguageService;
import com.gtech.promotion.service.activity.ActivityService;
import com.gtech.promotion.service.coupon.PromoCouponCodeUserService;
import com.gtech.promotion.service.coupon.PromoCouponInnerCodeService;
import com.gtech.promotion.vo.param.activity.CustomCondition;
import com.gtech.promotion.vo.param.activity.FindActivityParam;
import com.gtech.promotion.vo.param.coupon.*;
import com.gtech.promotion.vo.result.coupon.ExportCouponResult;
import com.gtech.promotion.vo.result.coupon.QueryCouponActivityListResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 优惠券活动接口
 *
 */
@Api(value = "Coupon Activity API",tags ={"Coupon Activity API"})
@RestController
@Slf4j
public class CouponActivityController extends BaseController {

    @Autowired
    private CouponActivityComponent couponActivityDomain;

    @Autowired
    private PromoCouponInnerCodeService promoCouponInnerCodeService;

    @Autowired
    private PromoCouponCodeUserService couponCodeUserService;

    @Autowired
    private ActivityService activityService;

    @Autowired
    private ActivityLanguageService activityLanguageService;

    @Autowired
    private MasterDataFeignClient masterDataFeignClient;

    public void checkCustomCondition(List<CustomCondition> customConditions, String tenantCode) {
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(customConditions)) {
            Set<String> set = new HashSet<>();
            JsonResult<String> masterResult = masterDataFeignClient.getValueValue(tenantCode, "TENANT_CUSTOM_CONDITION");
            if (null != masterResult && org.apache.commons.lang3.StringUtils.isNotBlank(masterResult.getData()) && TurnOnAndOffEnum.TURN_ON.code().equals(masterResult.getData())) {
                for (CustomCondition customCondition : customConditions) {

                    if (set.contains(customCondition.getCustomKey())) {
                        CheckUtils.isTrue(false, ErrorCodes.PROMO_DUPLICATE, "customKey");
                    }
                    set.add(customCondition.getCustomKey());
                    if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(customCondition.getCustomValueList())){
                        customCondition.setCustomValue(JSON.toJSONString(customCondition.getCustomValueList()));
                    }
                }
            }
        }
    }

    /**
     * 创建优惠券活动
     */
    @ApiOperation(value = "Create a new coupon activity", notes = "Return coupon activity code")
    @PostMapping(value = "/coupon/createCouponActivity")
    public Result<String> createCouponActivity(@RequestBody CreateCouponActivityParam param) {
        log.info("创建优惠券活动入参:{{}}", JSONObject.toJSONString(param));
        param.validate();
        checkCustomCondition(param.getCustomConditions(), param.getTenantCode());

        CreateCouponActivityDTO activityDTO = BeanCopyUtils.jsonCopyBean(param, CreateCouponActivityDTO.class);
        activityDTO.setCreateUser(param.getOperateUser());
        String activityId = couponActivityDomain.createCouponActivity(activityDTO);

        return Result.ok(activityId);
    }

    /**
     * 更新优惠券活动
     */
    @ApiOperation(value = "Update an exist coupon activity", notes = "Return coupon activity code")
    @PostMapping(value = "/coupon/updateCouponActivity")
    public Result<String> updateCouponActivity(@RequestBody UpdateCouponActivityParam param) {
        log.info("更新优惠券活动入参:{{}}", JSONObject.toJSONString(param));
        param.validate();
        checkCustomCondition(param.getCustomConditions(), param.getTenantCode());

        UpdateCouponActivityInDTO updateDTO = BeanCopyUtils.jsonCopyBean(param, UpdateCouponActivityInDTO.class);
        updateDTO.setUpdateUser(param.getOperateUser());
        String result = couponActivityDomain.updateCouponActivity(updateDTO);
        return Result.ok(result);
    }

    /**
     * 查询券活动列表
     *
     * @param param 查询券活动列表 入参实体
     */
    @ApiOperation(value = "Query coupon activity list", notes = "Returns a list of coupon activitys for the specified criteria")
    @PostMapping(value = "/coupon/queryCouponActivityList")
    public PageResult<QueryCouponActivityListResult> queryCouponActivityList(@RequestBody QueryCouponActivityListParam param) {
        log.info("查询券活动列表入参:{{}}", JSONObject.toJSONString(param));
        param.validate();
        CouponActivityListInDTO dtoParam = BeanCopyUtils.jsonCopyBean(param, CouponActivityListInDTO.class);
        dtoParam.setOrgCode(CodeHelper.getOrgCode(param.getOrgCode()));
        dtoParam.setPageCount(param.getPageSize());
        dtoParam.setPageNo(param.getPageNum());
        PageInfo<CouponActivityListOutDTO> couponActivityList = couponActivityDomain.queryCouponActivityList(dtoParam);
        List<QueryCouponActivityListResult> resultList = BeanCopyUtils.jsonCopyList(couponActivityList.getList(), QueryCouponActivityListResult.class);
        return PageResult.ok(resultList, couponActivityList.getTotal());
    }

    /**
     * 查询券活动列表
     *
     * @param request 查询券活动列表 入参实体
     */
    @ApiOperation(value = "Query coupon activity list by qualification")
    @PostMapping(value = "/coupon/queryCouponActivityListByQualification")
    public PageResult<QueryCouponActivityListResult> queryCouponActivityListByQualification(@RequestBody QueryCouponActivityListByMemberTagParam request) {
        log.info("start queryCouponActivityListByQualification:{{}}", JSONObject.toJSONString(request));
        request.validate();
        CouponActivityListInDTO dtoParam = BeanCopyUtils.jsonCopyBean(request, CouponActivityListInDTO.class);
        dtoParam.setOrgCode(CodeHelper.getOrgCode(request.getOrgCode()));
        dtoParam.setPageNo(request.getPageNum());
        dtoParam.setPageCount(request.getPageSize());
        PageInfo<CouponActivityListOutDTO> couponActivityList = couponActivityDomain.queryCouponActivityList(dtoParam);
        List<QueryCouponActivityListResult> resultList = BeanCopyUtils.jsonCopyList(couponActivityList.getList(), QueryCouponActivityListResult.class);
        return PageResult.ok(resultList, couponActivityList.getTotal());
    }

    /**
     * 查詢优惠券活动详情
     *
     * @param param 查询券活动详情入参实体
     */
    @ApiOperation(value = "Find a coupon activity detail", notes = "Return a coupon activity detail")
    @PostMapping(value = "/coupon/findCouponActivity")
    public Result<CouponActivityOutDTO> findCouponActivity(@RequestBody FindActivityParam param) {
        log.info("查询券活动详情入参:{{}}", JSONObject.toJSONString(param));
        param.validate();
        CouponActivityOutDTO couponActivityDTO = couponActivityDomain.findCouponActivityOutDetail(param.getTenantCode(),
                param.getActivityCode(), CodeHelper.getOrgCode(param.getOrgCode()));
        return Result.ok(couponActivityDTO);
    }

    @ApiOperation(value = "查询优惠码")
    @ApiIgnore
    @PostMapping(value = "/couponCode/find")
    public Result<CouponActivityCodeOutDTO> queryCouponActivity(@RequestBody CouponActivityInDTO couponActivityInDTO) {
        log.info("查询优惠码入参:{{}}", JSONObject.toJSONString(couponActivityInDTO));
        CheckUtils.isNotBlank(couponActivityInDTO.getTenantCode(), ErrorCodes.PARAM_EMPTY, "tenantCode");
        Check.check(StringUtil.isBlank(couponActivityInDTO.getPromotionCode()), CouponActivityChecker.NULL_PROMOTION_CODE);
        TPromoCouponInnerCodeVO couponCodeVo = promoCouponInnerCodeService.findCouponByCouponCode(couponActivityInDTO.getTenantCode(), couponActivityInDTO.getPromotionCode());
        CouponActivityCodeOutDTO outDTO = new CouponActivityCodeOutDTO();
        if (null != couponCodeVo) {
            ActivityModel activityVO = findActivityByActivityCode(couponActivityInDTO.getTenantCode(), couponActivityInDTO.getLanguage(), couponCodeVo.getActivityCode());
            outDTO.setActivityCode(activityVO.getActivityCode());
            outDTO.setStatus(activityVO.getActivityStatus());
            outDTO.setPromoPassword(couponCodeVo.getPromoPassword());
            if (ActivityStatusEnum.END.equalsCode(activityVO.getActivityStatus()) || ActivityStatusEnum.CLOSURE.equalsCode(activityVO.getActivityStatus())) {
                outDTO.setPromotionCode(couponActivityInDTO.getPromotionCode() + "_" + activityVO.getActivityCode());
            }
        }
        return Result.ok(outDTO);
    }

    private ActivityModel findActivityByActivityCode(String tenantCode, String language, String activityCode) {
        ActivityModel activityVO = activityService.findActivity(tenantCode, activityCode, null);
        Check.check(null == activityVO, TPromoActivityChecker.NOT_NULL);
        //多语言列表
        ActivityLanguageModel activityLanguage = activityLanguageService.findActivityLanguage(activityVO.getTenantCode(), activityVO.getActivityCode(), language);
        if (null != activityLanguage) {
            activityVO.setLanguage(activityLanguage);
        }

        return activityVO;
    }

    /**
     *
     * @param updateDTO
     */
    @ApiOperation(value = "更新优惠码活动的码")
    @ApiIgnore
    @PostMapping(value = "/couponCode/update")
    public Result<String> updateCouponCode(@RequestBody CouponActivityInDTO updateDTO) {
        log.info("更新优惠码活动的码入参:{{}}", JSONObject.toJSONString(updateDTO));
        Check.check(updateDTO == null, SystemChecker.NULL_VO);
        CheckUtils.isNotBlank(updateDTO.getTenantCode(), ErrorCodes.PARAM_EMPTY, "tenantCode");
        String promotionCode = updateDTO.getPromotionCode();
        Check.check(StringUtil.isBlank(promotionCode), CouponActivityChecker.NULL_PROMOTION_CODE);
        TPromoCouponInnerCodeVO innerCodeVO = new TPromoCouponInnerCodeVO();
        innerCodeVO.setTenantCode(updateDTO.getTenantCode());
        innerCodeVO.setCouponCode(promotionCode);
        int num = promoCouponInnerCodeService.updateInnerCodeByCode(updateDTO.getTenantCode(), promotionCode.split("_")[0], promotionCode);
        couponCodeUserService.updatePromotionCode(updateDTO.getTenantCode(), promotionCode.split("_")[0], promotionCode);
        return Result.ok(num > 0 ? "更新成功" : "更新失败");
    }

    /**
     * 查询已经生效的优惠券活动列表
     * @param dto 请求参数
     */
    @ApiOperation(value = "查询已经生效的优惠券活动列表")
    @ApiIgnore
    @PostMapping(value = "/coupon/effective/query")
    public Result<List<CouponActivityDTO>> queryCouponActivityByTenantCode(@RequestBody TenantCodeInDTO dto) {
        log.info("查询已经生效的优惠券活动列表:{{}}", JSONObject.toJSONString(dto));
        Check.check(StringUtil.isBlank(dto.getTenantCode()), CouponCodeUserChecker.NOT_NULL_MERCCODE);
        List<CouponActivityDTO> list = couponActivityDomain.queryCouponActivityByTenantCode(dto.getTenantCode(), dto.getLanguage(),
                CodeHelper.getOrgCode(dto.getOrgCode()));
        return Result.ok(list);
    }

    @ApiOperation(value = "Only anonymous coupons are exported", notes = "Only anonymous coupons are exported.")
    @PostMapping(value = "/coupon/export/query")
    public Result<List<ExportCouponResult>> queryExportCoupon(@RequestBody ExportCouponParam exportCouponParam) {
        log.info("导出券码:{{}}", JSONObject.toJSONString(exportCouponParam));
        exportCouponParam.validate();
        List<CouponReleaseModel> couponReleaseModels = couponActivityDomain.queryCouponReleaseList(exportCouponParam.getActivityCode());
        Check.check(CollectionUtils.isEmpty(couponReleaseModels), CouponExportChecker.NO_RELEASE);
        Check.check(!couponReleaseModels.get(0).getCouponType().equals(CouponTypeEnum.ANONYMITY_COUPON.code()), CouponExportChecker.NOT_ANONYMITY_COUPON);

        Map<String, List<CouponReleaseModel>> releaseMap = couponReleaseModels.stream().collect(Collectors.groupingBy(CouponReleaseModel::getReleaseCode));
        ExportCouponInDTO exportCouponInDTO = BeanCopyUtils.jsonCopyBean(exportCouponParam, ExportCouponInDTO.class);
        List<ExportCouponOutDTO> list = couponActivityDomain.exportCoupon(exportCouponInDTO);
        List<ExportCouponResult> results = new ArrayList<>();
        for (ExportCouponOutDTO exportCouponOutDTO : list) {
            CouponReleaseModel couponReleaseModel = releaseMap.get(exportCouponOutDTO.getReleaseCode()).get(0);
            if ( null != couponReleaseModel.getValidDays() && couponReleaseModel.getCouponType().equals(CouponTypeEnum.ANONYMITY_COUPON.code())){
                continue;
            }
            ExportCouponResult result = new ExportCouponResult();
            result.setValidEndDate(couponReleaseModel.getValidEndTime());
            result.setValidStartDate(couponReleaseModel.getValidStartTime());
            result.setCouponCode(exportCouponOutDTO.getCouponCode());
            result.setId(exportCouponOutDTO.getId());
            if (StringUtil.isNotEmpty(exportCouponOutDTO.getValidStartTime()) && exportCouponOutDTO.getStatus().equals(CouponStatusEnum.UN_GRANT.code())){
                result.setStatus(CouponStatusEnum.getByCode("02").desc());
            }else {
                result.setStatus(CouponStatusEnum.getByCode(exportCouponOutDTO.getStatus()).desc());
            }
            results.add(result);
        }

        return Result.ok(results);
    }

}
