package com.gtech.promotion.controller.marketing;

import com.alibaba.fastjson.JSON;
import com.gtech.commons.result.Result;
import com.gtech.commons.utils.BeanCopyUtils;
import com.gtech.promotion.component.marketing.LuckyDrawComponent;
import com.gtech.promotion.component.marketing.MarketingComponent;
import com.gtech.promotion.domain.marketing.LuckyDrawDomain;
import com.gtech.promotion.domain.marketing.MarketingDomain;
import com.gtech.promotion.dto.in.marketing.ExportLuckyDrawDto;
import com.gtech.promotion.vo.param.marketing.ExportLuckyDrawParam;
import com.gtech.promotion.vo.param.marketing.LuckyDrawCreateParam;
import com.gtech.promotion.vo.param.marketing.LuckyDrawUpdateParam;
import com.gtech.promotion.vo.param.marketing.MarketingFindParam;
import com.gtech.promotion.vo.result.marketing.ExportLuckyDrawResult;
import com.gtech.promotion.vo.result.marketing.ExportLuckyDrawResultTotal;
import com.gtech.promotion.vo.result.marketing.LuckyDrawFindResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.io.Serializable;
import java.util.List;

@Slf4j
@RestController
@Api(value = "Marketing Lucky Draw API",tags = { "Marketing Lucky Draw API" })
public class LuckyDrawController {

    @Autowired
    private MarketingComponent marketingComponent;

    @Autowired
    private LuckyDrawComponent LuckyDrawComponent;

    @ApiOperation(value = "Create activity to lucky draw",notes = "Create activity to lucky draw")
    @PostMapping("/marketing/luckyDraw/create")
    public Result<String> create(@RequestBody LuckyDrawCreateParam param){
        log.info("创建抽奖活动:{{}}", JSON.toJSONString(param));
        param.validate();
        LuckyDrawDomain marketingDomain = BeanCopyUtils.jsonCopyBean(param, LuckyDrawDomain.class);
        return Result.ok(marketingComponent.createMarketing(marketingDomain));
    }

    @ApiOperation(value = "Update activity to lucky draw",notes = "Update activity to lucky draw")
    @PostMapping("/marketing/luckyDraw/update")
    public Result<Serializable> update(@RequestBody LuckyDrawUpdateParam param){
        log.info("更新抽奖活动:{{}}", JSON.toJSONString(param));
        param.validate();
        LuckyDrawDomain marketingDomain = BeanCopyUtils.jsonCopyBean(param, LuckyDrawDomain.class);
        marketingComponent.updateMarketing(marketingDomain);
        return Result.ok();
    }

    @ApiOperation(value = "For details of lucky draw",notes = "For details of lucky draw")
    @PostMapping("/marketing/luckyDraw/find")
    public Result<LuckyDrawFindResult> find(@RequestBody MarketingFindParam param){
        param.validate();
        MarketingDomain marketingDomain = BeanCopyUtils.jsonCopyBean(param, MarketingDomain.class);
        LuckyDrawFindResult marketingFindResult = marketingComponent.findMarketing(marketingDomain);
        return Result.ok(marketingFindResult);
    }


    @ApiOperation(value = "Export details of lucky draw",notes = "Export details of lucky draw")
    @PostMapping("/marketing/luckyDraw/exportLuckyDraw")
    public Result<List<ExportLuckyDrawResult>> exportLuckyDraw(@RequestBody ExportLuckyDrawParam param){
        param.validate();
        ExportLuckyDrawDto exportLuckyDrawDto = BeanCopyUtils.jsonCopyBean(param, ExportLuckyDrawDto.class);
        List<ExportLuckyDrawResult> exportLuckyDraw = LuckyDrawComponent.exportLuckyDraw(exportLuckyDrawDto);
        return Result.ok(exportLuckyDraw);
    }


    @ApiOperation(value = "Export details of lucky draw total",notes = "Export details of lucky draw")
    @PostMapping("/marketing/luckyDraw/exportLuckyDrawTotal")
    public Result<List<ExportLuckyDrawResultTotal>> exportLuckyDrawTotal(@RequestBody ExportLuckyDrawParam param){
        param.validate();
        ExportLuckyDrawDto exportLuckyDrawDto = BeanCopyUtils.jsonCopyBean(param, ExportLuckyDrawDto.class);
        List<ExportLuckyDrawResultTotal> exportLuckyDraw = LuckyDrawComponent.exportLuckyDrawTotal(exportLuckyDrawDto);
        return Result.ok(exportLuckyDraw);
    }



}
