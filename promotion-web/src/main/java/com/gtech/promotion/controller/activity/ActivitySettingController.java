/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * <PERSON>ECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.controller.activity;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import com.gtech.commons.result.Result;
import com.gtech.commons.utils.StringUtil;
import com.gtech.promotion.checker.activity.ActivityExpressionChecker;
import com.gtech.promotion.checker.activity.ActivitySettingChecker;
import com.gtech.promotion.code.activity.SettingTypeEnum;
import com.gtech.promotion.dto.in.activity.TpromoActivityExpressionDTO;
import com.gtech.promotion.dto.in.activity.TpromoActivitySettingDTO;
import com.gtech.promotion.exception.Check;
import com.gtech.promotion.service.activity.TPromoActivityExpressionService;
import com.gtech.promotion.service.activity.TPromoActivitySettingService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import springfox.documentation.annotations.ApiIgnore;

@Api(value = "Activity Setting API",tags = { "Activity Setting API" })
@ApiIgnore
@RestController
public class ActivitySettingController{

    @Autowired
    private TPromoActivityExpressionService expressionService;

    @Autowired
    private TPromoActivitySettingService settingService;

    @ApiOperation(value = "Add and modify activity expressions",notes = "Add and modify activity expressions")
    @ApiIgnore
    @PostMapping(value = "/activity/expression")
    public Result<Object> activityExpression(@RequestBody TpromoActivityExpressionDTO expressionDTO){
        String tenantCode = expressionDTO.getTenantCode();
        String expression = expressionDTO.getExpression();
        Check.check(StringUtil.isBlank(tenantCode), ActivityExpressionChecker.NOT_NULL_TENANTCODE);
        Check.check(StringUtil.isBlank(expression), ActivityExpressionChecker.NOT_NULL_EXPRESSION);
        if ("0".equals(tenantCode) && "0".equals(expression)){//将数据库中所有表达式同步到redis中
            expressionService.addAllActivityExpression();
        }else{
            if (expressionService.selectExpression(tenantCode) == null){
                expressionService.addActivityExpression(tenantCode, expression);
            }else{
                expressionService.updateActivityExpression(tenantCode, expression);
            }
        }
        return Result.ok();
    }

    @ApiOperation(value = "Add and modify active configuration items",notes = "Add and modify active configuration items")
    @PostMapping(value = "/activity/setting")
    public Result<Object> activitySetting(@RequestBody TpromoActivitySettingDTO setting){
        if (SettingTypeEnum.ALL.equalsCode(setting.getSettingType() + "")){//将数据库所有配置同步到redis
            settingService.addAllActivitySetting();
        }else{
            String tenantCode = setting.getTenantCode();
            Integer settingType = setting.getSettingType();
            String settingCode = setting.getSettingCode();
            String settingValue = setting.getSettingValue();
            Check.check(StringUtil.isBlank(tenantCode), ActivitySettingChecker.NOT_NULL_TENANTCODE);
            Check.check(null == settingType, ActivitySettingChecker.NOT_NULL_SETTINGTYPE);
            Check.check(StringUtil.isBlank(settingCode), ActivitySettingChecker.NOT_NULL_SETTINGCODE);
            Check.check(StringUtil.isBlank(settingValue), ActivitySettingChecker.NOT_NULL_SETTINGVALUE);
            Check.check(!SettingTypeEnum.exist(settingType.toString()), ActivitySettingChecker.ERROR_SETTINGTYPE);
            //租户的购物车计算 不同模板商品根据价格升序降序配置
            Check.check(!SettingTypeEnum.PRICE_SORT.equalsCode(settingType.toString()), ActivitySettingChecker.ERROR_SETTINGCODE);
            Check.check(SettingTypeEnum.PRICE_SORT.equalsCode(settingType.toString()) && !SettingTypeEnum.PriceSortEnum.exist(settingValue), ActivitySettingChecker.ERROR_SETTINGVALUE);
            if (settingService.selectSetting(tenantCode, settingType, settingCode) == null){
                settingService.addActivitySetting(tenantCode, settingType, settingCode, settingValue);
            }else{
                settingService.updateActivitySetting(tenantCode, settingType, settingCode, settingValue);
            }
        }
        return Result.ok();
    }
}
