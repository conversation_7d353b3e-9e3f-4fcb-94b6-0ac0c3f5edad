package com.gtech.promotion.controller.marketing;

import com.gtech.commons.result.PageResult;
import com.gtech.commons.result.Result;
import com.gtech.commons.utils.BeanCopyUtils;
import com.gtech.promotion.component.marketing.MarketingComponent;
import com.gtech.promotion.domain.marketing.MarketingDomain;
import com.gtech.promotion.dto.in.marketing.MarketingQueryInDto;
import com.gtech.promotion.vo.param.marketing.MarketingExtendParam;
import com.gtech.promotion.vo.param.marketing.MarketingQueryParam;
import com.gtech.promotion.vo.param.marketing.MarketingUpdateStatusParam;
import com.gtech.promotion.vo.result.marketing.MarketingQueryResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.io.Serializable;

@Slf4j
@Api(value = "Marketing Activity API", tags = { "Marketing Activity API" })
@RestController
public class MarketingController {

	@Autowired
	private MarketingComponent marketingComponent;

	@ApiOperation(value = "Change marketing campaign status", notes = "Change marketing campaign status")
	@PostMapping("/marketing/activity/status/update")
	public Result<Serializable> updateStatus(@RequestBody MarketingUpdateStatusParam param) {
		param.validate();

		MarketingDomain marketingDomain = BeanCopyUtils.jsonCopyBean(param, MarketingDomain.class);
		marketingComponent.updateMarketingStatus(marketingDomain);
		return Result.ok();
	}

	@ApiOperation(value = "Query the list of marketing campaigns", notes = "Query the list of marketing campaigns")
	@PostMapping("/marketing/activity/query")
	public PageResult<MarketingQueryResult> query(@RequestBody MarketingQueryParam param) {
		param.validate();
		MarketingQueryInDto marketingQueryInDto = BeanCopyUtils.jsonCopyBean(param, MarketingQueryInDto.class);
		return marketingComponent.queryMarketingList(marketingQueryInDto);
	}

	@ApiOperation(value = "Marketing campaign postponement", notes = "Marketing campaign postponement")
	@PostMapping(value = "/marketing/activity/extend")
	public Result<Serializable> extendMarketing(@RequestBody MarketingExtendParam param) {
		log.info("活动结束时间延期参数:{{}}", param);
		param.validate();
		MarketingDomain marketingDomain = BeanCopyUtils.jsonCopyBean(param, MarketingDomain.class);
		marketingComponent.extendMarketing(marketingDomain, param.getEndTime());
		return Result.ok();
	}

	@ApiOperation(value = "Close expired activities periodically", notes = "Close expired activities periodically")
	@PostMapping(value = "/marketing/activity/expire")
	public Result<Serializable> expireMarketing() {
		marketingComponent.expireMarketing();
		return Result.ok();
	}
}
