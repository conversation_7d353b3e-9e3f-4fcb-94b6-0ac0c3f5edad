/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * <PERSON>ECH MAKES NO REPRESENTATIONS OR WA<PERSON>RANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.controller.coupon;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.gtech.commons.page.PageData;
import com.gtech.commons.result.PageResult;
import com.gtech.commons.result.Result;
import com.gtech.commons.utils.BeanCopyUtils;
import com.gtech.commons.utils.CheckUtils;
import com.gtech.commons.utils.StringUtil;
import com.gtech.promotion.checker.SystemChecker;
import com.gtech.promotion.checker.activity.TPromoActivityChecker;
import com.gtech.promotion.checker.coupon.*;
import com.gtech.promotion.code.CodeHelper;
import com.gtech.promotion.code.coupon.CouponStatusEnum;
import com.gtech.promotion.code.coupon.CouponTypeEnum;
import com.gtech.promotion.component.coupon.CouponActivityComponent;
import com.gtech.promotion.component.coupon.CouponInnerCodeComponent;
import com.gtech.promotion.component.coupon.FilterCouponComponent;
import com.gtech.promotion.component.feign.BasicClientDomain;
import com.gtech.promotion.controller.BaseController;
import com.gtech.promotion.dao.model.activity.ActivityModel;
import com.gtech.promotion.dao.model.coupon.CouponReleaseModel;
import com.gtech.promotion.dao.model.coupon.TPromoCouponCodeUserVO;
import com.gtech.promotion.dao.model.coupon.TPromoCouponInnerCodeVO;
import com.gtech.promotion.domain.coupon.CouponReleaseDomain;
import com.gtech.promotion.dto.in.activity.CouponDataInDTO;
import com.gtech.promotion.dto.in.activity.CouponQuantityDTO;
import com.gtech.promotion.dto.in.activity.FrozenCouponCodeInDTO;
import com.gtech.promotion.dto.in.activity.ManagementDataInDTO;
import com.gtech.promotion.dto.in.coupon.CouponActivityListInDTO;
import com.gtech.promotion.dto.in.coupon.CouponReleaseListInDTO;
import com.gtech.promotion.dto.in.coupon.ExportCouponRelationDto;
import com.gtech.promotion.dto.in.coupon.TCouponListQueryDTO;
import com.gtech.promotion.dto.out.coupon.CouponActivityListOutDTO;
import com.gtech.promotion.dto.out.coupon.CouponInfoQueryOutDTO;
import com.gtech.promotion.dto.out.coupon.CouponReleaseOutDto;
import com.gtech.promotion.dto.out.coupon.ManagementDataOutDTO;
import com.gtech.promotion.exception.Check;
import com.gtech.promotion.exception.ErrorCodes;
import com.gtech.promotion.exception.PromotionException;
import com.gtech.promotion.page.RequestPage;
import com.gtech.promotion.service.coupon.PromoCouponActivityService;
import com.gtech.promotion.service.coupon.PromoCouponInnerCodeService;
import com.gtech.promotion.service.coupon.PromoCouponReleaseService;
import com.gtech.promotion.utils.DateValidUtil;
import com.gtech.promotion.utils.ExportCouponUtil;
import com.gtech.promotion.vo.param.activity.GetCouponActivityParam;
import com.gtech.promotion.vo.param.activity.GetCouponReleaseParam;
import com.gtech.promotion.vo.param.activity.UpdateCouponParam;
import com.gtech.promotion.vo.param.coupon.ExportCouponRelationParam;
import com.gtech.promotion.vo.result.coupon.ExportCouponRelationResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.OutputStream;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 券码controller
 * 
 */
@RestController
@Slf4j
@Api(value = "Coupon Inner Code API",tags = { "Coupon Inner Code API"} )
public class CouponInnerCodeController extends BaseController{

    @Autowired
    private CouponInnerCodeComponent couponInnerCodeDomain;

    @Autowired
    private FilterCouponComponent filterCouponDomain;

    @Autowired
    private PromoCouponActivityService promoCouponActivityService;

    @Autowired
    private BasicClientDomain basicClientDomain;

    @Autowired
    private CouponActivityComponent couponActivityDomain;

    @Autowired
    private PromoCouponInnerCodeService promoCouponInnerCodeService;

    @Autowired
    private PromoCouponReleaseService promoCouponReleaseService;

    private static final  String CONTENT_TYPE = "application/octet-stream;charset=UTF-8";
                  
    private static final  String HEADER = "Content-Disposition";
                  
    private static final  String FILE = "attachment;fileName=coupon_code.csv";
                  
    private static final  String COUPON_STATUS = "couponStatus";
                  
    private static final  String COUPON_FROZEN_STATUS = "couponFrozenStatus";
                  
    private static final  String COUPON_TYPE = "couponType";
                  
    private static final  String COUPON_TAKE_LABEL = "couponTakeLabel";

    /**
     * 查询券码列表
     * 
     * @param tCouponListQueryDTO 入参
     * @return 券码列表
     */
    @ApiOperation(value = "Query the coupon code list",notes = "Return the coupon detail list")
    @PostMapping(value = "/coupon/code/query")
    public PageResult<CouponInfoQueryOutDTO> queryCouponListInfo(@RequestBody TCouponListQueryDTO tCouponListQueryDTO){

        log.info("查询券活动的券码列表参数:{{}}", JSONObject.toJSONString(tCouponListQueryDTO));
        Check.check(null == tCouponListQueryDTO, SystemChecker.NULL_VO);
        Check.check(StringUtil.isBlank(tCouponListQueryDTO.getActivityCode()), TPromoActivityChecker.NOT_NULL_ACTIVITY_CODE);
        Check.check(StringUtil.isBlank(tCouponListQueryDTO.getTenantCode()), CouponQueryChecker.NOT_NULL_TENANTCODE);
        RequestPage page = new RequestPage(tCouponListQueryDTO.getPageNo(), tCouponListQueryDTO.getPageCount());
        tCouponListQueryDTO.setOrgCode(CodeHelper.getOrgCode(tCouponListQueryDTO.getOrgCode()));
        
        PageInfo<CouponInfoQueryOutDTO> pageList = couponInnerCodeDomain.queryCouponListInfo(tCouponListQueryDTO, page);
        
        return PageResult.ok(pageList.getList(), pageList.getTotal());
    }


    /**
     * 导出券
     * 
     * @param dataInDTO 查询条件（入参）
     * @param response 响应
     * @throws
     */
    @ApiOperation(value = "Export the coupon code",notes = "Export the coupon code")
    @GetMapping(value = "/coupon/code/export")
    @ApiIgnore
    public void exportFile(TCouponListQueryDTO dataInDTO,HttpServletResponse response,HttpServletRequest request){
        log.info("导出匿名券文件参数:{{}}", JSONObject.toJSONString(dataInDTO));
        Check.check(null == dataInDTO, SystemChecker.NULL_VO);
        Check.check(StringUtil.isBlank(dataInDTO.getTenantCode()), CouponExportChecker.NOT_NULL_TENANTCODE);
        Check.check(StringUtil.isBlank(dataInDTO.getActivityCode()), CouponExportChecker.NOT_NULL_ACTIVITYCODE);
        ActivityModel activityModel = promoCouponActivityService.findCouponActivity(dataInDTO.getTenantCode(), dataInDTO.getActivityCode());
        CheckUtils.isNotNull(activityModel, ErrorCodes.VALIDATE_COUPON_ACTIVITY_EXIST);

        //优惠码导出
        try (OutputStream outputStream = response.getOutputStream()){
            response.setContentType(CONTENT_TYPE);
            response.setHeader(HEADER, FILE);
            Map<String, String> couponStatus = basicClientDomain.getLanguages(COUPON_STATUS);
            Map<String, String> frozenStatus = basicClientDomain.getLanguages(COUPON_FROZEN_STATUS);
            ExportCouponUtil.exportCouponInfoHeader(outputStream, dataInDTO.getLanguage());
            if (CouponTypeEnum.PROMOTION_CODE.code().equals(activityModel.getCouponType())){//NOSONAR
                couponInnerCodeDomain.exportDiscountCodeInfos(dataInDTO, outputStream, couponStatus, frozenStatus);//NOSONAR
            }else{//NOSONAR
                //优惠券 匿名券导出
                couponInnerCodeDomain.exportCouponInfos(dataInDTO, outputStream, couponStatus, frozenStatus);//NOSONAR
            }
        }catch (Exception e){
            log.error("优惠码导出失败", e);
            throw new PromotionException(SystemChecker.ERROR_STREAM);
        }

    }

    @ApiOperation(value = "Export coupon codes according to specified conditions",notes = "Export coupon codes according to specified conditions.1.16.0.version")
    @GetMapping(value = "/export/management/code/data")
    @ApiIgnore
    public void exportManagementCouponCodeData(ManagementDataInDTO dataInDTO,HttpServletResponse response,HttpServletRequest request){
        log.info("按租户编码以及查询条件导出券码数据:{{}}", JSONObject.toJSONString(dataInDTO));
        Check.check(StringUtil.isBlank(dataInDTO.getTenantCode()), CouponQueryListChecker.NOT_TENANT_CODE);
        managementCheckTime(dataInDTO);
        try (OutputStream outputStream = response.getOutputStream()){
            response.setContentType(CONTENT_TYPE);
            response.setHeader(HEADER, FILE);
            Map<String, Map<String, String>> hashMap = getLanguages();

            ExportCouponUtil.exportManagementCouponInfoHeader(outputStream);
            couponInnerCodeDomain.exportManagementCodeInfos(dataInDTO, outputStream, hashMap);
        }catch (Exception e){
            log.error("券导出失败", e);
            throw new PromotionException(SystemChecker.ERROR_STREAM);
        }
    }

    private Map<String, Map<String, String>> getLanguages(){
        Map<String, String> couponStatus = basicClientDomain.getLanguages(COUPON_STATUS);
        Map<String, String> frozenStatus = basicClientDomain.getLanguages(COUPON_FROZEN_STATUS);
        Map<String, String> couponTypes = basicClientDomain.getLanguages(COUPON_TYPE);
        Map<String, String> couponTakeLabels = basicClientDomain.getLanguages(COUPON_TAKE_LABEL);
        Map<String, Map<String, String>> hashMap = new HashMap<>();
        hashMap.put(COUPON_STATUS, couponStatus);
        hashMap.put(COUPON_FROZEN_STATUS, frozenStatus);
        hashMap.put(COUPON_TYPE, couponTypes);
        hashMap.put(COUPON_TAKE_LABEL, couponTakeLabels);
        return hashMap;
    }

    @ApiOperation(value = "Export coupon codes according to specified conditions.",notes = "Export coupon codes according to specified conditions.1.18.0.version")
    @GetMapping(value = "/export/coupon/code/data")
    @ApiIgnore
    public void exportCouponCodeData(CouponDataInDTO dataInDTO,HttpServletResponse response,HttpServletRequest request){
        log.info("按租户编码以及查询条件导出券码数据:{{}}", JSONObject.toJSONString(dataInDTO));
        Check.check(StringUtil.isBlank(dataInDTO.getTenantCode()), CouponQueryListChecker.NOT_TENANT_CODE);
        try (OutputStream outputStream = response.getOutputStream()){
            response.setContentType(CONTENT_TYPE);
            response.setHeader(HEADER, FILE);
            ExportCouponUtil.exportManagementCouponInfoHeader(outputStream);
            List<ManagementDataOutDTO> list = new ArrayList<>();
            RequestPage page = new RequestPage(1, 1000);
            while (true){
                dataInDTO.setPageNo(page.getPageNo());
                dataInDTO.setPageCount(page.getPageCount());
                list = managementPromoCouponCodeData(dataInDTO).getData().getList();//调用优惠码管理,1.18.0新增接口
                if (CollectionUtils.isEmpty(list)){
                    break;
                }
                page.setPageNo(page.getPageNo() + 1);

                Map<String, Map<String, String>> hashMap = getLanguages();
                ExportCouponUtil.exportManagementCouponInfoContent(list, outputStream, hashMap);
                list.clear();
            }
        }catch (Exception e){//NOSONAR
            log.error("券导出失败", e);//NOSONAR
            throw new PromotionException(SystemChecker.ERROR_STREAM);//NOSONAR
        }
    }

    @ApiOperation(value = "Management coupon data",notes = "Return coupon data.1.16.0.version")
    @PostMapping(value = "/management/coupon/data")
    @ApiIgnore
    public PageResult<ManagementDataOutDTO> managementData(@RequestBody ManagementDataInDTO dataInDTO){
        log.info("租户券码数据管理:{{}}", JSONObject.toJSONString(dataInDTO));
        Check.check(StringUtil.isBlank(dataInDTO.getTenantCode()), CouponQueryListChecker.NOT_TENANT_CODE);
        managementCheckTime(dataInDTO);
        setCouponTypeAndStatus(dataInDTO);
        dataInDTO.setType("01");
        PageData<ManagementDataOutDTO> pageInfo = couponInnerCodeDomain.getPageInfo(dataInDTO);

        return PageResult.ok(pageInfo.getList(), pageInfo.getTotal());
    }

    @ApiOperation(value = "Management promotion coupon code data",notes = "Return promotion coupon code data.1.18.0.version")
    @PostMapping(value = "/coupon/code/management/data")
    @ApiIgnore
    public PageResult<ManagementDataOutDTO> managementPromoCouponCodeData(@RequestBody CouponDataInDTO inDTO){
        log.info("租户优惠码数据管理:{{}}", JSONObject.toJSONString(inDTO));
        ManagementDataInDTO dataInDTO = BeanCopyUtils.jsonCopyBean(inDTO, ManagementDataInDTO.class);
        Check.check(StringUtil.isBlank(dataInDTO.getTenantCode()), CouponQueryListChecker.NOT_TENANT_CODE);
        managementCheckTime(dataInDTO);
        setCouponTypeAndStatus(dataInDTO);
        dataInDTO.setType("03");
        if (!("1".equals(dataInDTO.getIsUseStatus()) || "0".equals(dataInDTO.getIsUseStatus()))){//NOSONAR
            dataInDTO.setIsUseStatus("1");//NOSONAR
        }
        PageData<ManagementDataOutDTO> pageInfo = couponInnerCodeDomain.getPageInfo(dataInDTO);
        return PageResult.ok(pageInfo.getList(), pageInfo.getTotal());
    }

    /**
     * 冻结券码
     * 
     * @param frozenDTO
     */
    @ApiOperation(value = "Frozen coupon code",notes = "Returns whether the freezing result is successful")
    @PostMapping(value = "/frozen/coupon/code")
    public Result<Object> frozenCouponCode(@RequestBody FrozenCouponCodeInDTO frozenDTO){
        log.info("冻结某一张券码:{{}}", JSONObject.toJSONString(frozenDTO));
        Check.check(frozenDTO == null, SystemChecker.NULL_VO);
        Check.check(StringUtil.isBlank(frozenDTO.getTenantCode()), CouponCodeUserChecker.NOT_NULL_MERCCODE);
        Check.check(StringUtil.isBlank(frozenDTO.getCouponCode()), CouponCodeUserChecker.COUPON_CODE_NOT_NULL);
        CheckUtils.isNotNull(frozenDTO.getFrozenStatus(),ErrorCodes.PARAM_EMPTY, "frozenStatus");
        filterCouponDomain.frozenCouponCode(frozenDTO);
        return Result.ok();
    }

    private void managementCheckTime(ManagementDataInDTO dataInDTO){

        if ((StringUtil.isNotBlank(dataInDTO.getReceiveStartTime()) && StringUtil.isBlank(dataInDTO.getReceiveEndTime())) || (StringUtil.isBlank(dataInDTO.getReceiveStartTime()) && StringUtil.isNotBlank(dataInDTO.getReceiveEndTime()))){//NOSONAR
            Check.check(true, CouponQueryListChecker.RECEIVE_TIME);//NOSONAR
        }
        if ((StringUtil.isNotBlank(dataInDTO.getReleaseStartTime()) && StringUtil.isBlank(dataInDTO.getReleaseEndTime())) || (StringUtil.isBlank(dataInDTO.getReleaseStartTime()) && StringUtil.isNotBlank(dataInDTO.getReleaseEndTime()))){//NOSONAR
            Check.check(true, CouponQueryListChecker.RELEASE_TIME);//NOSONAR
        }
        if ((StringUtil.isNotBlank(dataInDTO.getUsedStartTime()) && StringUtil.isBlank(dataInDTO.getUsedEndTime())) || (StringUtil.isBlank(dataInDTO.getUsedStartTime()) && StringUtil.isNotBlank(dataInDTO.getUsedEndTime()))){//NOSONAR
            Check.check(true, CouponQueryListChecker.USED_TIME);//NOSONAR
        }
        if (StringUtil.isNotBlank(dataInDTO.getReleaseStartTime())){//NOSONAR
            Check.check(!DateValidUtil.isValidDate(dataInDTO.getReleaseStartTime()), TPromoActivityChecker.ERROR_TIME_FORMAT);//NOSONAR
            Check.check(!DateValidUtil.isValidDate(dataInDTO.getReleaseEndTime()), TPromoActivityChecker.ERROR_TIME_FORMAT);//NOSONAR
        }
        if (StringUtil.isNotBlank(dataInDTO.getUsedStartTime())){//NOSONAR
            Check.check(!DateValidUtil.isValidDate(dataInDTO.getUsedStartTime()), TPromoActivityChecker.ERROR_TIME_FORMAT);//NOSONAR
            Check.check(!DateValidUtil.isValidDate(dataInDTO.getUsedEndTime()), TPromoActivityChecker.ERROR_TIME_FORMAT);//NOSONAR
        }
        if (StringUtil.isNotBlank(dataInDTO.getReceiveStartTime())){
            Check.check(!DateValidUtil.isValidDate(dataInDTO.getReceiveStartTime()), TPromoActivityChecker.ERROR_TIME_FORMAT);//NOSONAR
            Check.check(!DateValidUtil.isValidDate(dataInDTO.getReceiveEndTime()), TPromoActivityChecker.ERROR_TIME_FORMAT);//NOSONAR
        }

    }

    private void setCouponTypeAndStatus(ManagementDataInDTO dataInDTO){
        if (StringUtil.isNotBlank(dataInDTO.getStatus())){
            List<String> statusList = new ArrayList<>();
            String[] couponStatus = dataInDTO.getStatus().split(",");
            if (couponStatus != null && couponStatus.length > 0){
                for (int i = 0; i < couponStatus.length; i++){
                    String status = couponStatus[i];
                    Check.check(!CouponStatusEnum.exist(status), CouponActivityChecker.ERROR_STATUS);
                    statusList.add(status);
                }
            }
            dataInDTO.setCouponStatus(statusList);
        }

        if (StringUtil.isNotBlank(dataInDTO.getCouponType())){
            List<String> coupons = new ArrayList<>();
            String[] couponTypes = dataInDTO.getCouponType().split(",");
            if (couponTypes != null && couponTypes.length > 0){//NOSONAR
                for (int i = 0; i < couponTypes.length; i++){
                    String type = couponTypes[i];
                    boolean exist = CouponTypeEnum.exist(type);
                    Check.check(!exist, CouponActivityChecker.NOT_COUPON_TYPE_EXIST);//NOSONAR
                    coupons.add(type);
                }
            }
            dataInDTO.setCouponTypes(coupons);
        }
    }

    @ApiOperation(value = "Query existing promotion activity",notes = "Return existing promotion activity")
    @PostMapping(value = "/coupon/findActivityByCouponCode")
    public PageResult<CouponActivityListOutDTO> findActivityByCouponCode(@RequestBody GetCouponActivityParam param) {
        log.info("查询优惠券活动{}", JSON.toJSONString(param));
        param.validate();
        CouponActivityListInDTO dto = BeanCopyUtils.jsonCopyBean(param, CouponActivityListInDTO.class);
        dto.setOrgCode(CodeHelper.getOrgCode(param.getOrgCode()));
        List<TPromoCouponInnerCodeVO> voList = couponInnerCodeDomain.findActivityCodeByCouponCode(param.getTenantCode(), param.getCouponCode());
        if (CollectionUtils.isEmpty(voList)){
            return PageResult.ok(BeanCopyUtils.jsonCopyList(new ArrayList<>(), CouponActivityListOutDTO.class), 0L);
        }

        dto.setPageCount(voList.size());
        dto.setPageNo(1);
        PageInfo<CouponActivityListOutDTO> pageInfoList = couponActivityDomain.queryCouponActivityList(dto);

        //获取releaseCode集合
        List<String> releaseCodes = voList.stream().map(TPromoCouponInnerCodeVO::getReleaseCode).distinct().collect(Collectors.toList());

        //获取券码集合
        List<String> couponList = voList.stream().map(TPromoCouponInnerCodeVO::getCouponCode).collect(Collectors.toList());
        List<TPromoCouponCodeUserVO> userVOS = couponInnerCodeDomain.queryActivityByCouponCodes(param.getTenantCode(), couponList);

        List<CouponReleaseDomain> releaseDomains = promoCouponReleaseService.queryCouponReleaseByReleaseCodes(param.getTenantCode(), releaseCodes);


        Map<String, String> couponReleaseCodeMap = voList.stream().collect(Collectors.toMap(TPromoCouponInnerCodeVO::getCouponCode, TPromoCouponInnerCodeVO::getReleaseCode));


        //投放编码对应的投放时间
        Map<String, String> releaseTimeMap = releaseDomains.stream().collect(Collectors.toMap(CouponReleaseDomain::getReleaseCode, CouponReleaseDomain::getReleaseTime));

        Map<String, List<TPromoCouponCodeUserVO>> couponUserMap = userVOS.stream().collect(Collectors.groupingBy(TPromoCouponCodeUserVO::getCouponCode));

        List<CouponActivityListOutDTO> list = pageInfoList.getList();
        for (CouponActivityListOutDTO couponActivityListOutDTO : list) {
            List<TPromoCouponCodeUserVO> userVOS1 = couponUserMap.get(couponActivityListOutDTO.getCouponCode());
            if (!CollectionUtils.isEmpty(userVOS1)){
                TPromoCouponCodeUserVO userVO = userVOS1.get(0);
                couponActivityListOutDTO.setUsedTime(userVO.getUsedTime());
                couponActivityListOutDTO.setUsedRefId(userVO.getUsedRefId());
                couponActivityListOutDTO.setReceivedTime(userVO.getReceivedTime());
                couponActivityListOutDTO.setValidEndTime(userVO.getValidEndTime());
                couponActivityListOutDTO.setValidStartTime(userVO.getValidStartTime());
                couponActivityListOutDTO.setUserCode(userVO.getUserCode());

            }
            String releaseCode = couponReleaseCodeMap.get(couponActivityListOutDTO.getCouponCode());
            couponActivityListOutDTO.setReleaseTime(releaseTimeMap.get(releaseCode));
        }

        return PageResult.ok(BeanCopyUtils.jsonCopyList(pageInfoList.getList(), CouponActivityListOutDTO.class), pageInfoList.getTotal());
    }


    @ApiOperation(value = "Query release list",notes = "Query and send batch information according to the coupon number")
    @PostMapping(value = "/coupon/findReleaseByCouponCode")
    public Result<List<CouponReleaseOutDto>> findReleaseByCouponCode(@RequestBody GetCouponReleaseParam param) {
        log.info("查询优惠券发放批次{}", JSON.toJSONString(param));
        param.validate();
        CouponReleaseListInDTO dto = BeanCopyUtils.jsonCopyBean(param, CouponReleaseListInDTO.class);
        List<CouponReleaseModel> releaseModelList = couponActivityDomain.findReleaseByCouponCode(dto);
        return Result.ok(BeanCopyUtils.jsonCopyList(releaseModelList, CouponReleaseOutDto.class));
    }



    @ApiOperation(value = "get coupon quantity",notes = "get coupon quantity")
    @PostMapping(value = "/coupon/getCouponQuantity")
    public Result<Integer> getCouponQuantity(@RequestBody CouponQuantityDTO couponQuantityDTO){
        log.info("getCouponQuantity{}", JSON.toJSONString(couponQuantityDTO));
        couponQuantityDTO.validate();
        Integer quantity = promoCouponInnerCodeService.getCouponQuantity(couponQuantityDTO);
        return Result.ok(quantity);
    }

    /**
     * 更新券状态以及可用时间
     * @param param
     */
    @ApiOperation(value = "Update coupon ",notes = "Update coupon")
    @PostMapping(value = "/update/updateCouponByCode")
    public Result<String> updateCouponByCode(@RequestBody UpdateCouponParam param){

        param.validate();
        log.info("updateCouponByCode:{{}}", JSONObject.toJSONString(param));

        filterCouponDomain.updateCouponCode(param);
        return Result.ok();
    }

    @ApiOperation(value = "Export coupon relation info",notes = "Export coupon relation info")
    @PostMapping(value = "/export/couponRelationInfo")
    public Result<List<ExportCouponRelationResult>> exportCouponRelationInfo(@RequestBody ExportCouponRelationParam param){

        param.validate();
        log.info("导出券以及相关信息：{}", JSONObject.toJSONString(param));
        ExportCouponRelationDto dto = BeanCopyUtils.jsonCopyBean(param, ExportCouponRelationDto.class);
        List<ExportCouponRelationResult> results = couponInnerCodeDomain.exportCouponRelationInfo(dto);
        results.removeAll(Collections.singleton(null));
        return Result.ok(results);
    }

}
