package com.gtech.promotion.controller.purchaseconstraint;

import cn.hutool.core.map.MapUtil;
import com.alibaba.fastjson.JSON;
import com.gtech.commons.result.Result;
import com.gtech.commons.utils.BeanCopyUtils;
import com.gtech.promotion.component.purchaseconstraint.PurchaseConstraintComponent;
import com.gtech.promotion.dto.in.purchaseconstraint.CalAvailableQtyInDto;
import com.gtech.promotion.dto.in.purchaseconstraint.CheckPurchaseConstraintInDto;
import com.gtech.promotion.vo.bean.ProductAttribute;
import com.gtech.promotion.vo.param.purchaseconstraint.CalAvailableQtyByProductListParam;
import com.gtech.promotion.vo.param.purchaseconstraint.CalAvailableQtyParam;
import com.gtech.promotion.vo.param.purchaseconstraint.CheckPurchaseConstraintParam;
import com.gtech.promotion.vo.result.purchaseconstraint.CalAvailableQtyByProductListResult;
import com.gtech.promotion.vo.result.purchaseconstraint.CalAvailableQtyResult;
import com.gtech.promotion.vo.result.purchaseconstraint.CheckPurchaseConstraintResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Api(value = "Mall Purchase Constraint API", tags = { "Mall Purchase Constraint API" })
@RestController
@RequestMapping("/purchaseConstraint")
public class MallPurchaseConstraintController {

    @Autowired
    private PurchaseConstraintComponent pcComponent;


    /**
     * 限购校验
     * 适用于创建订单，购物车，结算
     * @param param
     * @return
     */
    @ApiOperation(value = "Check purchase constraint", notes = "Check purchase constraint.")
    @PostMapping(value = "/checkPurchaseConstraint")
    public Result<CheckPurchaseConstraintResult> checkPurchaseConstraint(@RequestBody CheckPurchaseConstraintParam param){
        log.info("checkPurchaseConstraint param : {}", JSON.toJSONString(param));
        param.validate();
        CheckPurchaseConstraintInDto checkPcInDto = BeanCopyUtils.jsonCopyBean(param, CheckPurchaseConstraintInDto.class);
        CheckPurchaseConstraintResult checkPurchaseConstraintResult = pcComponent.checkPurchaseConstraint(checkPcInDto);
        return Result.ok(checkPurchaseConstraintResult);
    }

    /**
     * 计算每个SKU限购可购买数量
     * 支持一个spu下有多个sku
     * @param param
     * @return Result data值： -1 不限制 0 不可购买  大于0 可购买的数量
     */
    @ApiOperation(value = "Calculate product available buy quantity through purchase constraint"
            , notes = "Calculate product available buy quantity through purchase constraint.")
    @PostMapping(value = "/calAvailableQty")
    public Result<List<CalAvailableQtyResult>> calAvailableQty(@RequestBody CalAvailableQtyParam param) {
        log.info("calAvailableQty param : {}", JSON.toJSONString(param));
        param.validate();
        CalAvailableQtyInDto calAvailableQtyInDto = BeanCopyUtils.jsonCopyBean(param, CalAvailableQtyInDto.class);
        Map<String, List<ProductAttribute>> skuAttributeMap = param.getSkuAttributeMap();
        if (MapUtil.isNotEmpty(skuAttributeMap)) {
            calAvailableQtyInDto.setIgnoreProductScopeFilter(Boolean.TRUE);
        }
        List<CalAvailableQtyResult> calAvailableQtyResultList = pcComponent.calAvailableQty(calAvailableQtyInDto);
        return Result.ok(calAvailableQtyResultList);
    }



    @ApiOperation(value = "Calculate product available buy quantity through purchase constraint"
            , notes = "Calculate product available buy quantity through purchase constraint.")
    @PostMapping(value = "/calAvailableQtyByProductList")
    public Result<List<CalAvailableQtyByProductListResult>> calAvailableQtyByProductList(@RequestBody CalAvailableQtyByProductListParam param) {
        log.info("calAvailableQtyByProductList param : {}", JSON.toJSONString(param));
        param.validate();
        CalAvailableQtyInDto calAvailableQtyInDto = BeanCopyUtils.jsonCopyBean(param, CalAvailableQtyInDto.class);
        Map<String, List<ProductAttribute>> skuAttributeMap = param.getSkuAttributeMap();
        if (MapUtil.isNotEmpty(skuAttributeMap)) {
            calAvailableQtyInDto.setIgnoreProductScopeFilter(Boolean.TRUE);
        }
        List<CalAvailableQtyResult> calAvailableQtyResultList = pcComponent.calAvailableQty(calAvailableQtyInDto);

        Map<String, List<CalAvailableQtyResult>> collect = calAvailableQtyResultList.stream().collect(Collectors.groupingBy(CalAvailableQtyResult::getSkuCode));
        List<CalAvailableQtyByProductListResult> resultList =new ArrayList<>();
        param.getCalAvailableQtyProductList().forEach(product -> {
            List<CalAvailableQtyResult> calAvailableQtyResults = collect.get(product.getSkuCode());
            calAvailableQtyResults = calAvailableQtyResults.stream().filter(x -> null != x.getPurchaseConstraint()).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(calAvailableQtyResults)) {
                return;
            }
            CalAvailableQtyByProductListResult result = new CalAvailableQtyByProductListResult();
            result.setSkuCode(product.getSkuCode());
            result.setSpuCode(product.getProductCode());
            result.setCalAvailableQtyResultList(calAvailableQtyResults);
            resultList.add(result);
        });

        return Result.ok(resultList);
    }

}
