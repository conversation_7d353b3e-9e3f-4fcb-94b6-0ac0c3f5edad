/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.controller;

import java.util.Date;

import javax.servlet.http.HttpServletRequest;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.gtech.commons.utils.DateUtil;
import com.gtech.promotion.dto.config.GitProperties;

import springfox.documentation.annotations.ApiIgnore;

/**
 * Demo Controller 
 * 
 * <AUTHOR>
 * @Date 2019年9月23日下午5:31:01
 */
@RestController
@RequestMapping(value = "/demo")
@EnableConfigurationProperties(GitProperties.class)
@ApiIgnore
public class DemoController {

    @Value("${demo.info.project.artifactId:idm-web}")
    private String projectArtifactId;

    @Value("${demo.info.project.name:idm-web}")
    private String projectName;

    @Value("${info.build.version:1.0.0}")
    private String projectVersion;

    @Autowired
    private GitProperties gitProperties;

    private static Date startTime = new Date();

    @GetMapping(value = "/info")
    @PostMapping(value = "/info")
    public String info(HttpServletRequest request) {

        String splitLine = "<tr><td align='right'>------</td><td>------</td></tr>";
        StringBuilder context = new StringBuilder(2048);
        context.append("<html>");
        context.append("<head>");
        context.append("<style>");
        context.append("TD.title {padding-right:10; text-align:right; background:#f5f5f5;}");
        context.append("TD.value {padding-left:10;}");
        context.append("</style>");
        context.append("</head>");
        context.append("<body>");
        context.append("<table cellPadding='0' cellSpacing='0'><tbody>");

        context.append("<tr><td width='30%' align='right'></td><td width='70%' align='left'></td></tr>");
        this.addLine(context, "Project artifact id", projectArtifactId);
        this.addLine(context, "Project name", projectName);
        this.addLine(context, "Project version", projectVersion);
        this.addLine(context, "Project start time", DateUtil.format(startTime, DateUtil.FORMAT_YYYYMMDDHHMISS_2));

        context.append(splitLine);
        this.addLine(context, "Build version", gitProperties.getBuildVersion());
        this.addLine(context, "Build time", gitProperties.getBuildTime());

        context.append(splitLine);
        this.addLine(context, "Git branch", gitProperties.getBranch());
        this.addLine(context, "Git closest tag name", gitProperties.getTagName());

        context.append(splitLine);
        this.addLine(context, "Git commit id full", gitProperties.getCommitIdFull());
        this.addLine(context, "Git commit id abbrev", gitProperties.getCommitIdAbbrev());
        this.addLine(context, "Git commit message", gitProperties.getCommitMessage());
        this.addLine(context, "Git commit time", gitProperties.getCommitTime());
        this.addLine(context, "Git commit user name", gitProperties.getCommitUserName());
        this.addLine(context, "Git commit user email", gitProperties.getCommitUserEmail());

        context.append("</tbody></table>");
        context.append("</body></html>");

        return context.toString();
    }

    private void addLine(StringBuilder context, String title, String value) {

        context.append("<tr><td class='title'>" + title + ":</td>");
        context.append("<td class='value'>" + value + "</td></tr>");
    }
}
