package com.gtech.promotion.controller.flashsale;

import com.alibaba.fastjson.JSON;
import com.gtech.commons.redis.GTechCodeGenerator;
import com.gtech.commons.result.PageResult;
import com.gtech.commons.result.Result;
import com.gtech.commons.utils.BeanCopyUtils;
import com.gtech.commons.utils.StringUtil;
import com.gtech.promotion.checker.activity.TPromoOrderChecker;
import com.gtech.promotion.checker.flashsale.FlashSaleChecker;
import com.gtech.promotion.code.CodeHelper;
import com.gtech.promotion.code.activity.ActivityStoreEnum;
import com.gtech.promotion.code.activity.SelectorProductTypeEnum;
import com.gtech.promotion.code.marketing.ActivityTypeEnum;
import com.gtech.promotion.component.boostsharing.BoostSharingComponent;
import com.gtech.promotion.component.flashsale.FlashSaleComponent;
import com.gtech.promotion.component.marketing.MarketingCacheComponent;
import com.gtech.promotion.dao.entity.marketing.MarketingGroupEntity;
import com.gtech.promotion.dao.model.marketing.MarketingGroupMode;
import com.gtech.promotion.dao.model.marketing.flashsale.CacheFlashSaleModel;
import com.gtech.promotion.dao.model.marketing.flashsale.FlashSaleProductModel;
import com.gtech.promotion.dto.boostsharing.ExportBoostSharingDetailDto;
import com.gtech.promotion.dto.boostsharing.ExportBoostSharingTotalDto;
import com.gtech.promotion.dto.boostsharing.FilterNoRightOfFirstRefusalDto;
import com.gtech.promotion.dto.in.activity.SingleProductDTO;
import com.gtech.promotion.dto.in.activity.TenantProductDTO;
import com.gtech.promotion.dto.in.flashsale.*;
import com.gtech.promotion.dto.out.marketing.FlashSaleActivityPriceDTO;
import com.gtech.promotion.dto.out.marketing.FlashSaleSkuActivityPriceDTO;
import com.gtech.promotion.exception.Check;
import com.gtech.promotion.helper.ActivityRedisHelpler;
import com.gtech.promotion.page.RequestPage;
import com.gtech.promotion.service.marketing.MarketingGroupCodeService;
import com.gtech.promotion.service.marketing.MarketingGroupService;
import com.gtech.promotion.utils.ActivityProductCheckUtil;
import com.gtech.promotion.utils.MarketingFilterUtil;
import com.gtech.promotion.vo.bean.ProductCodes;
import com.gtech.promotion.vo.param.activity.*;
import com.gtech.promotion.vo.param.marketing.*;
import com.gtech.promotion.vo.param.marketing.boostsharding.ExportBoostSharingDetailParam;
import com.gtech.promotion.vo.param.marketing.boostsharding.ExportBoostSharingTotalParam;
import com.gtech.promotion.vo.param.marketing.flashsale.*;
import com.gtech.promotion.vo.result.boostsharding.ExportBoostSharingDetailResult;
import com.gtech.promotion.vo.result.boostsharding.ExportBoostSharingTotalResult;
import com.gtech.promotion.vo.result.flashpresale.*;
import com.gtech.promotion.vo.result.flashsale.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.map.HashedMap;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.web.bind.annotation.*;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@RestController
@Api(value = "Mall Marketing Flash Sale API", tags = {"Mall Marketing Flash Sale API"})
public class MallFlashSaleController {

    @Autowired
    private MarketingCacheComponent marketingCacheComponent;

    @Autowired
    private FlashSaleComponent flashSaleComponent;

    @Autowired
    private MarketingGroupService marketingGroupService;


    @Autowired
    private BoostSharingComponent boostSharingComponent;


    @Autowired
    private GTechCodeGenerator codeGenerator;

    @Autowired
    private ActivityRedisHelpler activityRedisHelpler;

    @Autowired
    private MarketingGroupCodeService marketingGroupCodeService;

    private static final String LOG_WARN = "{}-{}-{}-{}";

    /**
     * 单品活动价格计算： 根据商品信息查询并计算其参与的单品促销价格
     */
    @ApiOperation(value = "(PDP) Calculate single product flash sale price", notes = "Product detail page call this api, get the flash sale price for this product")
    //店铺待定
    @PostMapping(value = "/marketing/flashSale/calSkuFlashSalePrice")
    public Result<CalcFlashSaleSkuPromotionPriceResult> calSkuFlashSalePrice(@RequestBody FlashSalePdpPriceParam param) {

        log.info("flashSale Sku Promotion Price商品信息:{}", JSON.toJSONString(param));
        // Parameter validation.
        param.validate();

        SingleProductDTO product = BeanCopyUtils.jsonCopyBean(param, SingleProductDTO.class);
        ActivityProductCheckUtil.checkProductCombine(product);
        product.setOrgCodes(CodeHelper.getOrgCodes(product.getOrgCodes()));
        Map<String, CacheFlashSaleModel> flashSaleCacheMap = marketingCacheComponent.getFlashSaleCacheMap(param.getTenantCode(), param.getLanguage(), param.getActivityType());

        flashSaleCacheMap = MarketingFilterUtil.filterActivityByOrgCodes(flashSaleCacheMap, product.getOrgCodes());
        flashSaleCacheMap = MarketingFilterUtil.filterActivityByQualifications(flashSaleCacheMap, product.getQualifications());
        flashSaleCacheMap = MarketingFilterUtil.filterActivityByTime(flashSaleCacheMap, null);
        List<FlashSaleProductModel> products = flashSaleComponent.getProductsByActivityCodesAndProducts(param.getTenantCode(), Collections.singleton(param.getActivityCode()), Collections.singletonList(product.getSkuCode()));
        flashSaleCacheMap = marketingCacheComponent.filterActivityByProduct(flashSaleCacheMap, product, products, SelectorProductTypeEnum.SELECT_SKU.code() );
        flashSaleCacheMap = getStringCacheFlashSaleModelMap(flashSaleCacheMap, param.getActivityCode());
        FlashSaleSkuActivityPriceDTO skuActivityPriceDTO = flashSaleComponent.activitySkuPrice(product, flashSaleCacheMap);

        if (null == skuActivityPriceDTO || CollectionUtils.isEmpty(skuActivityPriceDTO.getActivity())) {
            return Result.ok();
        }

        FlashSaleActivityPriceDTO activityPriceDTO = skuActivityPriceDTO.getActivity().get(0);

        return Result.ok(BeanCopyUtils.jsonCopyBean(activityPriceDTO, CalcFlashSaleSkuPromotionPriceResult.class));
    }


    /**
     * 单品活动价格计算： 根据商品信息查询并计算其参与的单品促销价格(多sku)
     */
    @ApiOperation(value = "(PDP) Calculate single product flash sale price", notes = "Product detail page call this api, get the flash sale price for this product")
    //店铺待定
    @PostMapping(value = "/marketing/flashSale/calSkuListFlashSalePrice")
    public Result<List<CalcFlashSaleSkuPromotionPriceResult>> calSkuListFlashSalePrice(@RequestBody FlashSaleNewPdpPriceParam param) {
        log.info("start calSkuListFlashSalePrice!商品信息:{}", JSON.toJSONString(param));
        param.validate();

        SingleProductDTO product = BeanCopyUtils.jsonCopyBean(param, SingleProductDTO.class);
        ActivityProductCheckUtil.checkProductCombine(product);
        product.setOrgCodes(CodeHelper.getOrgCodes(product.getOrgCodes()));
        Map<String, CacheFlashSaleModel> flashSaleCacheMap = marketingCacheComponent.getFlashSaleCacheMap(param.getTenantCode(), param.getLanguage(), param.getActivityType());

        flashSaleCacheMap = MarketingFilterUtil.filterActivityByOrgCodes(flashSaleCacheMap, product.getOrgCodes());
        flashSaleCacheMap = MarketingFilterUtil.filterActivityByQualifications(flashSaleCacheMap, product.getQualifications());
        flashSaleCacheMap = MarketingFilterUtil.filterActivityByTime(flashSaleCacheMap, null);
        List<FlashSaleProductModel> products = flashSaleComponent.getProductsByActivityCodesAndProducts(param.getTenantCode(), Collections.singleton(param.getActivityCode()), product.getSkuList().stream().map(SkuParam::getSkuCode).collect(Collectors.toList()));
        flashSaleCacheMap = marketingCacheComponent.filterActivityByProduct(flashSaleCacheMap, product, products,SelectorProductTypeEnum.SELECT_SKU.code() );
        flashSaleCacheMap = getStringCacheFlashSaleModelMap(flashSaleCacheMap, param.getActivityCode());
        List<FlashSaleSkuActivityPriceDTO> skuActivityPriceDTOList = flashSaleComponent.activitySkuPriceList(product, flashSaleCacheMap);

        if (CollectionUtils.isEmpty(skuActivityPriceDTOList)) {
            return Result.ok();
        }
        List<FlashSaleActivityPriceDTO> activityList = new ArrayList<>();
        for (FlashSaleSkuActivityPriceDTO flashSaleSkuActivityPriceDTO : skuActivityPriceDTOList) {
            if (!CollectionUtils.isEmpty(flashSaleSkuActivityPriceDTO.getActivity())) {
                activityList.addAll(flashSaleSkuActivityPriceDTO.getActivity());
            }
        }
        if (CollectionUtils.isEmpty(activityList)) {
            return Result.ok();
        }

        List<CalcFlashSaleSkuPromotionPriceResult> calcFlashSaleSkuPromotionPriceResultList = BeanCopyUtils.jsonCopyList(activityList, CalcFlashSaleSkuPromotionPriceResult.class);
        return Result.ok(calcFlashSaleSkuPromotionPriceResultList);
    }

    private Map<String, CacheFlashSaleModel> getStringCacheFlashSaleModelMap(Map<String, CacheFlashSaleModel> flashSaleCacheMap, String activityCode) {
        Check.check(!flashSaleCacheMap.containsKey(activityCode), FlashSaleChecker.SKU_NOT_MATCH_ACTIVITY);
        Map<String, CacheFlashSaleModel> tempMap = new HashedMap<>();
        tempMap.put(activityCode, flashSaleCacheMap.get(activityCode));
        flashSaleCacheMap = tempMap;
        return flashSaleCacheMap;
    }

    @ApiOperation(value = "(PDP) Query activity list by product", notes = "(PDP) Query activity list by product") //店铺待定
    @PostMapping("/marketing/flashSale/queryActivityListByProduct")
    public Result<List<QueryFlashSaleListByProductResult>> queryActivityListByProduct(@RequestBody QueryActivityListByProductParam param) {

        log.info("flashSale 根据商品查询活动列表入参:{}", JSON.toJSONString(param));

        param.validate();

        TenantProductDTO product = BeanCopyUtils.jsonCopyBean(param, TenantProductDTO.class);

        if (StringUtils.isNotBlank(param.getOrgCodes())) {
            product.setOrgCodes(Arrays.asList(param.getOrgCodes().split(",")));
        } else {
            product.setOrgCodes(Collections.singletonList(ActivityStoreEnum.ALL.code()));
        }

        ActivityProductCheckUtil.checkProductCombine(product);

        String tenantCode = product.getTenantCode();

        //该商户已审核成功的活动
        Map<String, CacheFlashSaleModel> activityCacheMap = marketingCacheComponent.getFlashSaleCacheMap(tenantCode, param.getLanguage(), param.getActivityType());

        // 过滤符合的活动
        activityCacheMap = MarketingFilterUtil.filterActivityByTime(activityCacheMap, null);
        activityCacheMap = MarketingFilterUtil.filterActivityByOrgCodes(activityCacheMap, product.getOrgCodes());
        activityCacheMap = MarketingFilterUtil.filterActivityByQualifications(activityCacheMap, product.getQualifications());
        List<FlashSaleProductModel> products = flashSaleComponent.getProductsByActivityCodesAndProducts(param.getTenantCode(), activityCacheMap.keySet(), Collections.singletonList(product.getSkuCode()));
        activityCacheMap = marketingCacheComponent.filterActivityByProduct(activityCacheMap, product, products, SelectorProductTypeEnum.SELECT_SKU.code());
        List<QueryFlashSaleListByProductResult> flashSaleListByProductList = BeanCopyUtils.jsonCopyList(flashSaleComponent.getActivitiesByProduct(activityCacheMap, product), QueryFlashSaleListByProductResult.class);
        if (!StringUtil.isEmpty(param.getMemberCode())) {
            log.info("进入memberCode查询memberInventory逻辑！");
            flashSaleComponent.buildQueryMemberInventory(tenantCode, param.getMemberCode(), param.getSkuCode(), flashSaleListByProductList);
        }
        return Result.ok(flashSaleListByProductList);
    }

    @ApiOperation(value = "(PLP) Query activity list by product list", notes = "(PLP) Query activity list by product list")
    //店铺待定
    @PostMapping("/marketing/flashSale/queryActivityListByProductList")
    public Result<List<QueryFlashSaleListByProductListResult>> queryActivityListByProductList(@RequestBody QueryActivityListByProductListParam param) {

        log.info("flashSale 根据批量商品查询活动列表入参:{}", JSON.toJSONString(param));

        param.validate();

        String tenantCode = param.getTenantCode();

        //该商户已审核成功的活动
        Map<String, CacheFlashSaleModel> activityCacheMap = marketingCacheComponent.getFlashSaleCacheMap(tenantCode, param.getLanguage(), param.getActivityType());

        // 过滤符合的活动
        activityCacheMap = MarketingFilterUtil.filterActivityByTime(activityCacheMap, null);
        activityCacheMap = MarketingFilterUtil.filterActivityByQualifications(activityCacheMap, param.getQualifications());
        List<String> skuCodes = new ArrayList<>();
        for (QueryActivityListByProductListParam.Product product : param.getProducts()) {
            skuCodes.add(product.getSkuCode());
        }
        List<MarketingGroupEntity> marketingGroupEntities = marketingGroupService.selectMarketingGroupList(param.getTenantCode(), activityCacheMap.keySet());
        if (!CollectionUtils.isEmpty(marketingGroupEntities)) {
            for (MarketingGroupEntity marketingGroupEntity : marketingGroupEntities) {
                if (activityCacheMap.get(marketingGroupEntity.getActivityCode()) != null) {
                    MarketingGroupMode marketingGroupMode = new MarketingGroupMode();
                    BeanUtils.copyProperties(marketingGroupEntity, marketingGroupMode);
                    activityCacheMap.get(marketingGroupEntity.getActivityCode()).setMarketingGroupMode(marketingGroupMode);
                }
            }
        }
        List<FlashSaleProductModel> products = flashSaleComponent.getProductsByActivityCodesAndProducts(param.getTenantCode(), activityCacheMap.keySet(), skuCodes);
        List<QueryFlashSaleListByProductListResult> list = new ArrayList<>();
        for (QueryActivityListByProductListParam.Product product : param.getProducts()) {
            Map<String, CacheFlashSaleModel> copyMap = new HashedMap<>();
            copyMap.putAll(activityCacheMap);
            ProductCodes productCodes = BeanCopyUtils.jsonCopyBean(product, ProductCodes.class);
            List<String> orgCodes;
            if (StringUtils.isNotBlank(product.getOrgCodes())) {
                orgCodes = Arrays.asList(product.getOrgCodes().split(","));
            } else {
                orgCodes = Collections.singletonList(ActivityStoreEnum.ALL.code());
            }
            ActivityProductCheckUtil.checkProductCombine(productCodes);

            copyMap = MarketingFilterUtil.filterActivityByOrgCodes(copyMap, orgCodes);

            copyMap = marketingCacheComponent.filterActivityByProduct(copyMap, productCodes, products,SelectorProductTypeEnum.SELECT_SKU.code() );
            List<QueryFlashSaleListByProductResult> queryFlashSaleListByProductResults = BeanCopyUtils.jsonCopyList(flashSaleComponent.getActivitiesByProduct(copyMap, productCodes), QueryFlashSaleListByProductResult.class);
            QueryFlashSaleListByProductListResult listResult = new QueryFlashSaleListByProductListResult();
            listResult.setSkuCode(productCodes.getSkuCode());
            listResult.setProductCode(productCodes.getProductCode());
            listResult.setCombineSkuCode(productCodes.getCombineSkuCode());
            listResult.setFlashSaleList(queryFlashSaleListByProductResults);
            list.add(listResult);
        }

        return Result.ok(list);
    }



    @ApiOperation(value = "(PLP) Query marketing activity list.", notes = "(PLP) Query marketing activity list")
    //店铺待定
    @PostMapping("/marketing/flashSale/queryMarketingActivityListByProductList")
    public Result<List<QueryMarketingActivityListByProductListResult>> queryMarketingActivityListByProductList(@RequestBody QueryMarketingActivityListByProductListParam param) {

        log.info("根据批量商品查询营销活动:{}", JSON.toJSONString(param));

        param.validate();

        List<QueryMarketingActivityListByProductListResult> queryMarketingActivityListByProductListResults = flashSaleComponent.queryMarketingActivityListByProductList(param);

        return Result.ok(queryMarketingActivityListByProductListResults);
    }



    @ApiOperation(value = "(PLP) Query promotion and marketing activity list no filter.", notes = "(PLP) Query promotion and marketing activity list no filter")
    //店铺待定
    @PostMapping("/marketing/flashSale/queryPromotionOrMarketingNoFilter")
    public Result<List<QueryPromotionOrMarketingNoFilterResult>> queryPromotionOrMarketingNoFilter(@RequestBody QueryPromotionOrMarketingNoFilterParam param) {

        log.info("根据批量商品查询营销活动:{}", JSON.toJSONString(param));

        param.validate();

        List<QueryPromotionOrMarketingNoFilterResult> queryPromotionOrMarketingNoFilter = flashSaleComponent.queryPromotionOrMarketingNoFilter(param);

        return Result.ok(queryPromotionOrMarketingNoFilter);
    }





    @ApiOperation(value = "Query flash sale activity list", notes = "Query flash sale activity list")
    @PostMapping("/marketing/flashSale/queryActivityList")
    public PageResult<FlashSaleQueryListResult> queryFlashSaleActivityList(@RequestBody FlashSaleQueryListParam param) {
        log.info("flashSale 查询活动列表:{{}}", JSON.toJSONString(param));
        param.validate();

        return flashSaleComponent.queryActivityList(param);
    }

    @ApiOperation(value = "Query product list by activity", notes = "Query product list by activity")
    @PostMapping("/marketing/flashSale/queryProductListByActivity")
    public Result<FlashSaleQueryProductListResult> queryProductListByActivity(@RequestBody FlashSaleQueryProductListParam param) {
        log.info("flashSale 根据活动编码查询商品信息:{{}}", JSON.toJSONString(param));
        param.validate();

        return Result.ok(flashSaleComponent.queryProductsByActivityCode(param.getLanguage(), param.getActivityCode(), param));
    }

    //营销购物车计算
    @ApiOperation(value = "Calculate flash sale order price", notes = "Calculate flash sale order price") //店铺待定
    @PostMapping(value = "/marketing/flashSale/calcFlashSaleOrderPrice")
    public Result<List<FlashSaleOrderCalaResult>> calcFlashSaleOrderPrice(@RequestBody FlashSaleOrderCalaParam param) {

        log.info("flashSale Sku Promotion calcShoppingCart:{}", JSON.toJSONString(param));
        // Parameter validation.
        param.validate();

        FlashSaleShoppingCartDto shoppingCart = BeanCopyUtils.jsonCopyBean(param, FlashSaleShoppingCartDto.class);

        shoppingCart.setUserCode(param.getMemberCode());

        List<FlashSaleOrderCalaResult> list = flashSaleComponent.handlerShoppingCart(shoppingCart);
        return Result.ok(list);
    }

    @ApiOperation(value = "Get marketing group code.", notes = "Get marketing group code.")
    @GetMapping("/marketing/flashSale/getMarketingGroupCode")
    public Result<String> getMarketingGroupCode(@RequestParam("tenantCode") String tenantCode) {

        return Result.ok(codeGenerator.generateCode(tenantCode, "marketingGroupCode", "PG[D:yyyyMMddHHmmss][SM:%06d]", 1L));

    }




    @ApiOperation(value = "Create flash sale order", notes = "Create flash sale order")
    @PostMapping("/marketing/flashSale/createOrder")
    public Result<String> createOrder(@RequestBody FlashSaleCreateOrderParam param) {
        log.info("flashSale 创建订单:{{}}", JSON.toJSONString(param));
        param.validate();

        FlashSaleShoppingCartDto shoppingCart = BeanCopyUtils.jsonCopyBean(param, FlashSaleShoppingCartDto.class);
        shoppingCart.setUserCode(param.getMemberCode());

        List<FlashSaleOrderCalaResult> list = flashSaleComponent.handlerShoppingCart(shoppingCart);

        //如果没有赠品没有且促销的扣减总金额为0
        if (param.getPromoDeductedAmount().doubleValue() == 0 && ActivityTypeEnum.FLASH_SALE.code().equals(param.getActivityType())) {
            log.info("No reward information, just ignore it.");
            return Result.ok();
        }

        try {
            flashSaleComponent.commitOrder(param, list);
        } catch (DuplicateKeyException e) {
            Check.check(ActivityTypeEnum.GROUP.code().equals(param.getActivityType()), TPromoOrderChecker.EXIST_ORDER_DUPLICATED);
            log.error("createOrder duplicated!:{}", param.getOrderNo());
            return Result.ok();
        } catch (Exception e) {

            log.error(LOG_WARN, param.getTenantCode(), 10149903, param.getTenantCode() + param.getOrderNo(), e.getMessage());
            throw e;
        }


        return Result.ok();


    }

    @ApiOperation(value = "Cancel flash sale order", notes = "Cancel flash sale order")
    @PostMapping(value = "/marketing/flashSale/cancelOrder")
    public Result<Object> cancelOrder(@RequestBody CancelOrderParam param) {

        log.info("flashSale cancelOrder: {}", JSON.toJSONString(param));
        param.validate();

        try {
            flashSaleComponent.cancelOrder(param.getTenantCode(), param.getOrderNo());
        } catch (Exception e) {
            log.warn(LOG_WARN, param.getTenantCode(), 10149905, param.getTenantCode() + param.getOrderNo(), e.getMessage());
            throw e;
        }
        return Result.ok();
    }

    @ApiOperation(value = "Confirm flash sale order", notes = "Confirm flash sale order")
    @PostMapping(value = "/marketing/flashSale/confirmOrder")
    public Result<Object> confirmOrder(@RequestBody ConfirmOrderParam param) {

        log.info("flashSale confirmOrder: {}", JSON.toJSONString(param));
        param.validate();

        try {
            flashSaleComponent.confirmOrder(param.getTenantCode(), param.getOrderNo());
        } catch (Exception e) {
            log.warn(LOG_WARN, param.getTenantCode(), 10149904, param.getTenantCode() + param.getOrderNo(), e.getMessage());
            throw e;
        }
        return Result.ok();
    }


    @ApiOperation(value = "Create sharing record", notes = "Create sharing record")
    @PostMapping(value = "/marketing/boostSharing/createSharingRecord")
    public Result<String> createSharingRecord(@RequestBody CreateSharingRecordParam param) {
        log.info("createBoostSharing: {}", JSON.toJSONString(param));
        param.validate();
        String sharingRecordCode = boostSharingComponent.createBoostSharing(BeanCopyUtils.jsonCopyBean(param, SharingRecordDto.class));
        return Result.ok(sharingRecordCode);
    }




    @ApiOperation(value = "Create help record", notes = "Create help record")
    @PostMapping(value = "/marketing/boostSharing/createHelpRecord")
    public Result<CreateHelpRecordResult> createHelpRecord(@RequestBody CreateHelpRecordParam param) {
        log.info("createHelpRecord: {}", JSON.toJSONString(param));
        param.validate();
        CreateHelpRecordResult  result = boostSharingComponent.createHelpRecord(BeanCopyUtils.jsonCopyBean(param, HelpRecordDto.class));
        return Result.ok(result);
    }



    @ApiOperation(value = "Query sharing information", notes = "Query sharing information")
    @PostMapping(value = "/marketing/boostSharing/querySharingInformation")
    public PageResult<QuerySharingInformationResult> querySharingInformation(@RequestBody QuerySharingInformationParam param) {
        log.info("querySharingInformation: {}", JSON.toJSONString(param));
        param.validate();
        RequestPage page = new RequestPage(param.getPageNum(), param.getPageSize());

        return boostSharingComponent.querySharingInformation(BeanCopyUtils.jsonCopyBean(param, QuerySharingInformationDto.class),page);
    }

    @ApiOperation(value = "Issuance of pre-emptive rights.", notes = "Issuance of pre-emptive rights.")
    @PostMapping(value = "/marketing/boostSharing/issuanceOfPreEmptiveRights")
    public Result<String> issuanceOfPreEmptiveRights(@RequestBody IssuanceOfPreEmptiveRightsParam param) {
        log.info("issuanceOfPreEmptiveRights: {}", JSON.toJSONString(param));
        param.validate();
        boostSharingComponent.issuanceOfPreEmptiveRights(BeanCopyUtils.jsonCopyBean(param, IssuanceOfPreEmptiveRightsDto.class));
        return Result.ok();
    }


    @ApiOperation(value = "Write off of pre emptive rights.", notes = "Write off of pre emptive rights.")
    @PostMapping(value = "/marketing/boostSharing/writeOffOfPreEmptiveRights")
    public Result<String> writeOffOfPreEmptiveRights(@RequestBody WriteOffOfPreEmptiveRightsParam param) {
        log.info("writeOffOfPreEmptiveRights: {}", JSON.toJSONString(param));
        param.validate();
        boostSharingComponent.writeOffOfPreEmptiveRights(BeanCopyUtils.jsonCopyBean(param, WriteOffOfPreEmptiveRightsDto.class));
        return Result.ok();
    }


    @ApiOperation(value = "Query right of first refusal.", notes = "Query right of first refusal.")
    @PostMapping(value = "/marketing/boostSharing/queryRightOfFirstRefusal")
    public Result<List<QueryRightOfFirstRefusalResult>> queryRightOfFirstRefusal(@RequestBody QueryRightOfFirstRefusalParam param) {
        log.info("queryRightOfFirstRefusal: {}", JSON.toJSONString(param));
        param.validate();
        List<QueryRightOfFirstRefusalResult> rightOfFirstRefusalEntities = boostSharingComponent.queryRightOfFirstRefusal(BeanCopyUtils.jsonCopyBean(param, WriteOffOfPreEmptiveRightsDto.class));
        return Result.ok(rightOfFirstRefusalEntities);
    }


    @ApiOperation(value = "Query assistance information.", notes = "Query assistance information.")
    @PostMapping(value = "/marketing/boostSharing/queryHelpRecord")
    public Result<List<HelpRecordResult>> queryHelpRecord(@RequestBody QueryAssistanceInformationParam param) {
        log.info("queryAssistanceInformation: {}", JSON.toJSONString(param));
        param.validate();
        QuerySharingInformationDto querySharingInformationDto = BeanCopyUtils.jsonCopyBean(param, QuerySharingInformationDto.class);
        querySharingInformationDto.setHelpMemberCode(param.getMemberCode());
        List<HelpRecordResult> helpRecordResults = boostSharingComponent.queryHelpRecord(querySharingInformationDto);
        return Result.ok(helpRecordResults);
    }



    @ApiOperation(value = "Filter no right of first refusal.", notes = "Filter no right of first refusal.")
    @PostMapping(value = "/marketing/boostSharing/filterNoRightOfFirstRefusal")
    public Result<String> filterNoRightOfFirstRefusal(@RequestBody FilterNoRightOfFirstRefusalParam param) {
        log.info("filterNoRightOfFirstRefusal: {}", JSON.toJSONString(param));
        param.validate();
        boostSharingComponent.filterNoRightOfFirstRefusal(BeanCopyUtils.jsonCopyBean(param, FilterNoRightOfFirstRefusalDto.class));
        return Result.ok();
    }

    @ApiOperation(value = "Export boost sharing total.", notes = "Export boost sharing total.")
    @PostMapping(value = "/marketing/boostSharing/exportBoostSharingTotal")
    public Result<ExportBoostSharingTotalResult> exportBoostSharingTotal(@RequestBody ExportBoostSharingTotalParam param){
        param.validate();
       return Result.ok(boostSharingComponent.exportBoostSharingTotal(BeanCopyUtils.jsonCopyBean(param, ExportBoostSharingTotalDto.class)));
    }

    @ApiOperation(value = "Export boost sharing detail.", notes = "Export boost sharing detail.")
    @PostMapping(value = "/marketing/boostSharing/exportBoostSharingDetail")
    public Result<List<ExportBoostSharingDetailResult>> exportBoostSharingDetail(@RequestBody ExportBoostSharingDetailParam param){
        param.validate();
        List<ExportBoostSharingDetailResult> exportBoostSharingDetailResults = boostSharingComponent.exportBoostSharingDetail(BeanCopyUtils.jsonCopyBean(param, ExportBoostSharingDetailDto.class));
        return Result.ok(exportBoostSharingDetailResults);
    }


    @ApiOperation(value = "Export flash or pre sale detail.", notes = "Export flash or pre sale detail.")
    @PostMapping(value = "/marketing/export/exportFlashOrPreSaleDetail")
    public Result<List<ExportFlashPreSaleResult>> exportFlashOrPreSaleDetail(@RequestBody ExportFlashPreSaleParam param){
        param.validate();

        ExportFlashPreSaleDto dto = BeanCopyUtils.jsonCopyBean(param,ExportFlashPreSaleDto.class);
        List<ExportFlashPreSaleResult> results = flashSaleComponent.exportFlashOrPreSaleDetail(dto);

        return Result.ok(results);
    }

    @ApiOperation(value = "Export flash or pre sale product detail.", notes = "Export flash or pre sale product detail.")
    @PostMapping(value = "/marketing/export/exportFlashOrPreSaleProductDetail")
    public Result<List<ExportFlashPreProductResult>> exportFlashOrPreSaleProductDetail(@RequestBody ExportFlashPreSaleParam param){
        param.validate();

        ExportFlashPreSaleDto dto = BeanCopyUtils.jsonCopyBean(param,ExportFlashPreSaleDto.class);
        List<ExportFlashPreProductResult> results = flashSaleComponent.exportFlashOrPreSaleProductDetail(dto);

        return Result.ok(results);
    }

    @ApiOperation(value = "Export group data.", notes = "Export group data.")
    @PostMapping(value = "/marketing/export/exportGroupData")
    public Result<List<ExportGroupDetailResult>> exportGroupData(@RequestBody ExportGroupParam param){

        param.validate();

        ExportGroupDto dto = BeanCopyUtils.jsonCopyBean(param,ExportGroupDto.class);
        List<ExportGroupDetailResult> results = flashSaleComponent.exportGroupData(dto);

        return Result.ok(results);
    }
}
