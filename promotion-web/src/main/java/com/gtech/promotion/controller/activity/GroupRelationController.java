package com.gtech.promotion.controller.activity;

import com.alibaba.fastjson.JSON;
import com.gtech.commons.result.Result;
import com.gtech.promotion.component.activity.PromoGroupRelationDomain;
import com.gtech.promotion.vo.param.activity.ActivityGroupRelationQueryParam;
import com.gtech.promotion.vo.param.activity.GroupSettingRelationParam;
import com.gtech.promotion.vo.result.activity.ActivityGroupRelationResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @Description :
 * <AUTHOR> gaojie.li
 * @Date : 2023/7/12 10:06
 */
@Slf4j
@Api(value = "Promotion Group Relation API", tags = { "Promotion Group Relation API" })
@RestController
public class GroupRelationController {

    @Autowired
    private PromoGroupRelationDomain promoGroupRelationDomain;


    @ApiOperation(value = "查询活动分组叠加互斥关系列表")
    @PostMapping(value = "/activity/queryGroupRelation")
    public Result<List<ActivityGroupRelationResult>> queryAllActivityGroupRelation(@RequestBody ActivityGroupRelationQueryParam queryParam){

        queryParam.validate();

        log.info("queryAllActivityGroupRelation:{}", JSON.toJSONString(queryParam));

        return Result.ok(promoGroupRelationDomain.queryAllActivityGroupRelation(queryParam));
    }

    @ApiOperation(value = "设置活动分组叠加互斥关系")
    @PostMapping(value = "/activity/group/relation")
    public Result<Integer> settingRelationActivityGroup(@RequestBody GroupSettingRelationParam relationParam){

        relationParam.validate();
        log.info("settingRelationActivityGroup:{}", JSON.toJSONString(relationParam));

        return Result.ok(promoGroupRelationDomain.settingActivityGroupRelation(relationParam));
    }




}
