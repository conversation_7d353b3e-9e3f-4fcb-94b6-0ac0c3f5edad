package com.gtech.promotion.controller.purchaseconstraint;

import com.alibaba.fastjson.JSON;
import com.gtech.commons.result.PageResult;
import com.gtech.commons.result.Result;
import com.gtech.commons.utils.BeanCopyUtils;
import com.gtech.promotion.code.purchaseconstraint.PurchaseConstraintStatusEnum;
import com.gtech.promotion.component.purchaseconstraint.PcRuleComponent;
import com.gtech.promotion.component.purchaseconstraint.PurchaseConstraintCacheComponent;
import com.gtech.promotion.component.purchaseconstraint.PurchaseConstraintComponent;
import com.gtech.promotion.controller.BaseController;
import com.gtech.promotion.dao.model.purchaseconstraint.PcRuleCalculateModel;
import com.gtech.promotion.domain.purchaseconstraint.PurchaseConstraintDomain;
import com.gtech.promotion.dto.in.purchaseconstraint.FindPurchaseConstraintInDto;
import com.gtech.promotion.dto.in.purchaseconstraint.UpdatePurchaseConstraintInDto;
import com.gtech.promotion.dto.in.purchaseconstraint.UpdatePurchaseConstraintPriorityInDto;
import com.gtech.promotion.dto.in.purchaseconstraint.UpdatePurchaseConstraintStatusInDTO;
import com.gtech.promotion.dto.out.purchaseconstraint.FindPurchaseConstraintOutDto;
import com.gtech.promotion.utils.Constants;
import com.gtech.promotion.utils.ParamValidateUtil;
import com.gtech.promotion.vo.bean.PurchaseConstraintPriority;
import com.gtech.promotion.vo.param.purchaseconstraint.*;
import com.gtech.promotion.vo.param.purchaseconstraint.query.FindPurchaseConstraintParam;
import com.gtech.promotion.vo.param.purchaseconstraint.query.QueryPurchaseConstraintListParam;
import com.gtech.promotion.vo.result.purchaseconstraint.FindPurchaseConstraintResult;
import com.gtech.promotion.vo.result.purchaseconstraint.QueryPurchaseConstraintListResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 限购 控制层
 */
@Slf4j
@Api(value = "Purchase Constraint API", tags = { "Purchase Constraint API" })
@RequestMapping(value = "/purchaseConstraint")
@RestController
public class PurchaseConstraintController extends BaseController {

	public static final String UPDATE_SUCCESS = "update success";
	@Autowired
	private PurchaseConstraintComponent purchaseConstraintComponent;

	@Autowired
	private PurchaseConstraintCacheComponent purchaseConstraintCacheComponent;

	@Autowired
	private PcRuleComponent pcRuleComponent;


	/**
	 * 访问的场景
	 * 创单时候限购check时候直接调用factory.pcRuleIncrement<br/>
	 * trade mq监听订单状态变更; orderStatus in(61买家取消,62超时取消)<br/>
	 * trade mq监听订单状态变更; returnCode not blank && returnMode in (41取消订单 11退货 51已发货仅退款) && returnStatus equals 41: 仓库已收到
	 *
	 */
	@ApiOperation(value = "Purchase constraint rule increment", notes = "Purchase constraint rule increment.")
	@PostMapping("/pcRuleIncrement")
	public Result<Void> pcRuleIncrement(@Validated @RequestBody PcRuleIncrementRequest request) {
		log.info("pcRuleIncrement param : {}", JSON.toJSONString(request));
		PcRuleCalculateModel model = BeanCopyUtils.jsonCopyBean(request, PcRuleCalculateModel.class);
		pcRuleComponent.pcRuleIncrement(model, null);
		return Result.ok();
	}

	@ApiOperation(value = "Purchase constraint rule decrement", notes = "Purchase constraint rule decrement.")
	@PostMapping("/pcRuleDecrement")
	public Result<Void> pcRuleDecrement(@Validated @RequestBody PcRuleDecrementRequest request) {
		log.info("pcRuleDecrement param : {}", JSON.toJSONString(request));
		PcRuleCalculateModel model = BeanCopyUtils.jsonCopyBean(request, PcRuleCalculateModel.class);
		pcRuleComponent.pcRuleIncrement(model, null);
		return Result.ok();
	}

	@ApiOperation(value = "Create a purchase constraint", notes = "Create a purchase constraint.")
	@PostMapping(value = "/create")
	public Result<String> createPurchaseConstraint(@RequestBody CreatePurchaseConstraintParam param) {
		log.info("createPurchaseConstraint param : {}", JSON.toJSONString(param));
		param.validate();
		ParamValidateUtil.checkCustomCondition(param.getCustomConditionsList(), param.getTenantCode());
		ParamValidateUtil.checkCustomCondition(param.getCustomRulesList(), param.getTenantCode());
		PurchaseConstraintDomain purchaseConstraintDomain = BeanCopyUtils.jsonCopyBean(param, PurchaseConstraintDomain.class);
		if (StringUtils.isEmpty(param.getOrgCode())) {
			purchaseConstraintDomain.setOrgCode(Constants.DEFAULT_ORG);
		}
		return Result.ok(purchaseConstraintComponent.createPurchaseConstraint(purchaseConstraintDomain));
	}


	@ApiOperation(value = "Query purchase constraint list", notes = "Query purchase constraint list")
	@PostMapping(value = "/query")
	public PageResult<QueryPurchaseConstraintListResult> queryPurchaseConstraintList(@RequestBody QueryPurchaseConstraintListParam param) {
		log.info("queryPurchaseConstraintList param :{}", JSON.toJSONString(param));
		param.validate();
		return purchaseConstraintComponent.queryPurchaseConstraintList(param);
	}


	@ApiOperation(value = "Update status for an existing purchase constraint", notes = "Update status for an existing purchase constraint.")
	@PostMapping(value = "/status/update")
	public Result<String> updatePurchaseConstraintStatus(@RequestBody UpdatePurchaseConstraintStatusParam param) {
		log.info("updatePurchaseConstraintStatus param：{}", param);
		param.validate();
		UpdatePurchaseConstraintStatusInDTO updateActivityStatusInDTO =
								BeanCopyUtils.jsonCopyBean(param, UpdatePurchaseConstraintStatusInDTO.class);
		purchaseConstraintComponent.updatePurchaseConstraintStatus(updateActivityStatusInDTO);
		if(PurchaseConstraintStatusEnum.EFFECTIVE.code()
				.equalsIgnoreCase(param.getPurchaseConstraintStatus())){
			purchaseConstraintCacheComponent.putPurchaseConstraintCache(param.getTenantCode(), param.getPurchaseConstraintCode());
		}else if(PurchaseConstraintStatusEnum.END.code()
				.equalsIgnoreCase(param.getPurchaseConstraintStatus())){
			purchaseConstraintCacheComponent.delPurchaseConstraintCache(param.getTenantCode(), param.getPurchaseConstraintCode());
		}
		return Result.ok(UPDATE_SUCCESS);
	}

	@ApiOperation(value = "Find purchase constraint detail", notes = "Find purchase constraint detail")
	@PostMapping(value = "/get")
	public Result<FindPurchaseConstraintResult> findPurchaseConstraint(@RequestBody FindPurchaseConstraintParam param) {
		log.info("getPurchaseConstraint param :{}", JSON.toJSONString(param));
		param.validate();
		FindPurchaseConstraintInDto findPurchaseConstraintInDto =
										BeanCopyUtils.jsonCopyBean(param, FindPurchaseConstraintInDto.class);

		FindPurchaseConstraintOutDto findPurchaseConstraintOutDto =
										purchaseConstraintComponent.findPurchaseConstraint(findPurchaseConstraintInDto);

		return Result.ok(BeanCopyUtils.jsonCopyBean(findPurchaseConstraintOutDto, FindPurchaseConstraintResult.class));
	}

	@ApiOperation(value = "Update purchase constraint", notes = "Update purchase constraint")
	@PostMapping(value = "/update")
	public Result<String> updatePurchaseConstraint(@RequestBody UpdatePurchaseConstraintParam param) {
		log.info("updatePurchaseConstraint param :{}", JSON.toJSONString(param));
		param.validate();
		ParamValidateUtil.checkCustomCondition(param.getCustomConditionsList(), param.getTenantCode());
		ParamValidateUtil.checkCustomCondition(param.getCustomRulesList(), param.getTenantCode());
		UpdatePurchaseConstraintInDto updatePurchaseConstraintInDto =
				BeanCopyUtils.jsonCopyBean(param, UpdatePurchaseConstraintInDto.class);
		purchaseConstraintComponent.updatePurchaseConstraint(updatePurchaseConstraintInDto);

		purchaseConstraintCacheComponent.putPurchaseConstraintCache(param.getTenantCode(), param.getPurchaseConstraintCode());

		return Result.ok(UPDATE_SUCCESS);
	}

	@ApiOperation(value = "Update purchase constraint priority", notes = "Update purchase constraint priority")
	@PostMapping(value = "/priority/update")
	public Result<String> updatePurchaseConstraintPriority(@RequestBody UpdatePurchaseConstraintPriorityParam param) {
		log.info("updatePurchaseConstraintPriority param :{}", JSON.toJSONString(param));
		param.validate();
		UpdatePurchaseConstraintPriorityInDto updatePurchaseConstraintInDto =
				BeanCopyUtils.jsonCopyBean(param, UpdatePurchaseConstraintPriorityInDto.class);

		purchaseConstraintComponent.updatePurchaseConstraintPriority(updatePurchaseConstraintInDto);

		List<PurchaseConstraintPriority> purchaseConstraintPriorities = param.getPurchaseConstraintPriorityList();
		for(PurchaseConstraintPriority purchaseConstraintPriority : purchaseConstraintPriorities) {
			purchaseConstraintCacheComponent.putPurchaseConstraintCache(param.getTenantCode(),
															purchaseConstraintPriority.getPurchaseConstraintCode());
		}

		return Result.ok(UPDATE_SUCCESS);
	}

}