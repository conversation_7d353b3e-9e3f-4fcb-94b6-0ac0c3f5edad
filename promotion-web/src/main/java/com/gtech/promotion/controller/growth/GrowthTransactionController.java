package com.gtech.promotion.controller.growth;

import com.alibaba.fastjson.JSON;
import com.gtech.commons.result.PageResult;
import com.gtech.commons.result.Result;
import com.gtech.commons.utils.DateUtil;
import com.gtech.promotion.service.growth.GrowthTransactionService;
import com.gtech.promotion.vo.param.growth.query.GetGrowthTransactionParam;
import com.gtech.promotion.vo.param.growth.query.QueryGrowthTransactionParam;
import com.gtech.promotion.vo.result.growth.GrowthTransactionResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import javax.annotation.Resource;
import java.util.Map;

@RequestMapping(value = "/growth")
@RestController
@ApiIgnore
@Api(value = "Growth Transaction API", tags = "Growth Transaction API")
public class GrowthTransactionController {
    @Resource
	private GrowthTransactionService growthTransactionService;

	/**
	 * 获取成长值流水列表
	 */
	@SuppressWarnings("unchecked")
	@ApiOperation(value = "queryGrowthTransactionList")
	@PostMapping(value = "/queryGrowthTransactionList")
	public PageResult<GrowthTransactionResult> queryGrowthTransactionList(@RequestBody QueryGrowthTransactionParam param) {
	    
	    param.validate();

	    //check参数
		Map<String, Object> paramMap = JSON.parseObject(JSON.toJSONString(param), Map.class);
		PageResult<GrowthTransactionResult> resultPageResult = growthTransactionService.queryGrowthTransactionPage(paramMap);
		for (GrowthTransactionResult result : resultPageResult.getData().getList()) {
			result.setCreateTimeString(DateUtil.format(result.getCreateTime(),DateUtil.FORMAT_YYYYMMDDHHMISS));
            result.setUpdateTimeString(DateUtil.format(result.getUpdateTime(),DateUtil.FORMAT_YYYYMMDDHHMISS));
		}
		return resultPageResult;
	}

	/**
	 * 获取成长值流水详情
	 */
	@ApiOperation(value = "getGrowthTransaction")
	@PostMapping(value = "/getGrowthTransaction")
	public Result<GrowthTransactionResult> getGrowthTransaction(@RequestBody GetGrowthTransactionParam param) {
	    
	    param.validate();
		GrowthTransactionResult growthTransaction = growthTransactionService.getGrowthTransaction(param);
		growthTransaction.setCreateTimeString(DateUtil.format(growthTransaction.getCreateTime(),DateUtil.FORMAT_YYYYMMDDHHMISS));
		growthTransaction.setUpdateTimeString(DateUtil.format(growthTransaction.getUpdateTime(),DateUtil.FORMAT_YYYYMMDDHHMISS));
	    return Result.ok(growthTransaction);
	}

}
