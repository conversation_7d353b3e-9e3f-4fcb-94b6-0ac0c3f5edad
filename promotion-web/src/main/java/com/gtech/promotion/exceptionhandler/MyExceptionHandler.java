/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.exceptionhandler;

import com.gtech.commons.exception.GTechBaseException;
import com.gtech.commons.result.Result;
import com.gtech.commons.utils.StringUtil;
import com.gtech.promotion.checker.SystemChecker;
import com.gtech.promotion.exception.PromotionException;
import io.sentry.Sentry;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.TypeMismatchException;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.validation.BindException;
import org.springframework.validation.ObjectError;
import org.springframework.web.HttpRequestMethodNotSupportedException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartException;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * 异常处理类
 */
@Slf4j
@ControllerAdvice
public class MyExceptionHandler {


    public static final String CUSTOM_ERROR_MESSAGE = "处理自定义项目异常：{}出错请求信息：uri:{}";

    public static final String HTTP_ERROR_MESSAGE = "处理http请求方式不支持异常：{}出错请求信息：uri:{}";

    /**
     * 处理自定义项目异常
     *
     * @param e 异常
     * @return result
     */
    @ExceptionHandler(PromotionException.class)
    @ResponseBody
    public Result<Object> processNapoleanException(HttpServletRequest request, PromotionException e) {

        String uri = getURI(request);
        log.error(CUSTOM_ERROR_MESSAGE, uri, e);

        return Result.failed(e.getCode(), e.getMessage());
    }

    /**
     * 处理自定义项目异常
     *
     * @param e 异常
     * @return result
     */
    @ExceptionHandler(GTechBaseException.class)
    @ResponseBody
    public Result<Object> processNapoleanException(HttpServletRequest request, GTechBaseException e) {

        String uri = getURI(request);
        log.error(CUSTOM_ERROR_MESSAGE, uri, e);
        String message = e.getMessage();
        if(message.contains("({0})")){
            message = message.replace("({0})", "");
        }
        return Result.failed(e.getCode(), message);
    }

    @ExceptionHandler(HttpMessageNotReadableException.class)
    @ResponseBody
    public Result<Object> processHttpMessageNotReadableException(HttpServletRequest request, HttpMessageNotReadableException e) {

        String uri = getURI(request);
        if (e.getMessage().contains("field")) {
            String field = e.getMessage().substring(e.getMessage().indexOf("field :") + 7, e.getMessage().indexOf(';'));
            log.error("非法参数{},异常：{},出错请求信息：uri:{}", field, uri, e);
            return Result.failed(SystemChecker.ILLEGAL_FORMAT_PARAM_U.getCode(), field + SystemChecker.ILLEGAL_FORMAT_PARAM_U.getMessage());
        }
        log.error(CUSTOM_ERROR_MESSAGE, uri, e);
        return Result.failed(SystemChecker.NULL_VO_OR_ILLEGAL_FORMAT_PARAM.getCode(), SystemChecker.NULL_VO_OR_ILLEGAL_FORMAT_PARAM.getMessage());
    }

    @ExceptionHandler(NullPointerException.class)
    @ResponseBody
    public Result<Object> processNullPointerException(HttpServletRequest request, NullPointerException e) {

        String uri = getURI(request);
        log.error(CUSTOM_ERROR_MESSAGE, uri, e);
        return Result.failed(SystemChecker.NULL_POINTER.getCode(), SystemChecker.NULL_POINTER.getMessage());
    }

    /**
     * 处理http请求方式不支持异常
     *
     * @return result
     */
    @ExceptionHandler({ HttpRequestMethodNotSupportedException.class, MultipartException.class })
    @ResponseBody
    public Result<Object> processMethodNotSupportedException(HttpServletRequest request, HttpRequestMethodNotSupportedException e) {

        String uri = getURI(request);
        log.error(HTTP_ERROR_MESSAGE, uri, e);
        return Result.failed(SystemChecker.HTTP_METHOD_ILLEGAL.getCode(), SystemChecker.HTTP_METHOD_ILLEGAL.getMessage());
    }

    /**
     * 数据库字段异常
     * Cause: com.mysql.jdbc.MysqlDataTruncation: Data truncation: Data too long for column 'ACTIVITY_LABEL' at row 1 //varchar
     * Duplicate entry '3001-12-12' for key 'UIDX_PROMO_ACTIVITY_CHANNEL'
     * Cause: com.mysql.jdbc.MysqlDataTruncation: Data truncation: Out of range value for column 'ACTIVITY_CODE' at row 1 //bigint
     * 
     * @return result
     */
    @ExceptionHandler(DataIntegrityViolationException.class)
    @ResponseBody
    public Result<Object> processDataIntegrityViolationExceptionException(HttpServletRequest request, DataIntegrityViolationException e) {

        String message = e.getMessage();
        String uri = getURI(request);
        if (message.contains("Data too long for column") || message.contains("Out of range value")) {
            log.error("处理数据库字段长度超出异常：{}出错请求信息：uri:{}", uri, e);
            return Result.failed(SystemChecker.DATA_TOOLONG.getCode(), tooLangMessage(message) + SystemChecker.DATA_TOOLONG.getMessage());
        } else if (message.contains("Duplicate entry")) {
            log.error("处理数据库字段重复异常：{}出错请求信息：uri:{}", uri, e);
            return Result.failed(SystemChecker.DUPLICATE_KEY.getCode(), SystemChecker.DUPLICATE_KEY.getMessage());
        } else if (message.contains("' cannot be null") || message.contains("doesn't have a default value")) {
            log.error("处理数据库字段不能为空异常：{}出错请求信息：uri:{}", uri, e);
            return Result.failed(SystemChecker.NOT_NULL_PROMO.getCode(), SystemChecker.NOT_NULL_PROMO.getMessage());
        }
        log.error("违法数据库完整性：{}出错请求信息：uri:{}", uri, e);
        return Result.failed(SystemChecker.DATA_INTEGRITY_VIOLATION.getCode(), SystemChecker.DATA_INTEGRITY_VIOLATION.getMessage());
    }

    /**
     * 处理转换异常异常
     *
     * @return result
     */
    @ExceptionHandler(TypeMismatchException.class)
    @ResponseBody
    public Result<Object> processTypeMismatchException(HttpServletRequest request, TypeMismatchException e) {

        String uri = getURI(request);
        log.error(HTTP_ERROR_MESSAGE, uri, e);
        return Result.failed(SystemChecker.ILLEGAL_FORMAT_PARAM.getCode(), SystemChecker.ILLEGAL_FORMAT_PARAM.getMessage());
    }

    /**
     * 处理springmvc接收参数类型异常
     *
     * @return result
     */
    @ExceptionHandler(BindException.class)
    @ResponseBody
    public Result<Object> processBindException(HttpServletRequest request, BindException e) {

        String uri = getURI(request);
        log.error("处理springmvc接收参数类型异常：{}出错请求信息：uri:{}", uri, e);
        return Result.failed(SystemChecker.ILLEGAL_FORMAT_PARAM.getCode(), SystemChecker.ILLEGAL_FORMAT_PARAM.getMessage());
    }

    /**
     * 处理非法参数异常，比如：数字传字符串等
     *
     * @return result
     */
    @ExceptionHandler(IllegalArgumentException.class)
    @ResponseBody
    public Result<Object> processIllegalArgumentException(HttpServletRequest request, IllegalArgumentException e) {

        String uri = getURI(request);
        log.error("处理非法参数异常：{}出错请求信息：uri:{}", uri, e);
        return Result.failed(SystemChecker.ILLEGAL_FORMAT_PARAM.getCode(), SystemChecker.ILLEGAL_FORMAT_PARAM.getMessage());
    }

	/***
	 * <p>
	 * 服务端参数验证
	 * </p>
	 * 
	 * @date 2018/9/3 15:55
	 * @params [ex]
	 */
	@ResponseBody
	@ExceptionHandler(value = { MethodArgumentNotValidException.class })
	public Result<Object> argumentNotValidHandler(MethodArgumentNotValidException ex) {
        List<ObjectError> errors = ex.getBindingResult().getAllErrors();
        StringBuilder errorMsg = new StringBuilder();
        for (ObjectError error : errors) {
            errorMsg.append(error.getDefaultMessage()).append(";");
        }
        log.info("服务端参数验证未通过：{}", errorMsg);

		return Result.failed(SystemChecker.NULL_VO_OR_ILLEGAL_FORMAT_PARAM.getCode(), ex.getBindingResult().getFieldError().getDefaultMessage());
	}

    /**
     * 处理其他异常
     *
     * @param e
     *            异常
     * @return result
     */
    @ExceptionHandler(Exception.class)
    @ResponseBody
    public Result<Object> processOtherException(HttpServletRequest request, Exception e) {

        String uri = getURI(request);
        log.error("系统未知异常{},出错请求信息：uri:{}", uri, e);
        Sentry.captureException(e);

        return Result.failed(SystemChecker.UNKNOWN_ERROR.getCode(), SystemChecker.UNKNOWN_ERROR.getMessage());
    }

    private static String getURI(HttpServletRequest request) {

        return request.getRequestURI() + (StringUtil.isBlank(request.getQueryString()) ? "" : "?" + request.getQueryString());
    }

    private String tooLangMessage(String message) {

        String column = "";
        try {
            String[] split = message.split("column '");
            column = split[1].split("' at row")[0];
            column = column.replaceAll("_", "");
            column = column.toLowerCase();
        } catch (Exception e) {
            log.error("字段长度超出异常，截取字段名出错：{}", e);
            return SystemChecker.DATA_TOOLONG.getMessage();
        }
        return column;
    }

}
