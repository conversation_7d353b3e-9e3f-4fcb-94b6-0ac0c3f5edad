package com.gtech.promotion.config;

import com.alibaba.fastjson.JSON;
import io.swagger.annotations.ApiOperation;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.util.Optional;


@Component
@Aspect
@Slf4j
public class ControllerMethodParamLog {
    @Pointcut("execution(* com.gtech.promotion.controller..*(..))")
    public void aspect() {

    }

    @Around("aspect()")
    public Object before(ProceedingJoinPoint point) throws Throwable {
        //获取方法
        ServletRequestAttributes sra = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        HttpServletRequest request = sra.getRequest();
        //访问路径
        String requestPath = request.getRequestURI();
        String className = point.getSignature().getDeclaringType().getSimpleName();
        String methodName = point.getSignature().getName();
        MethodSignature signature = (MethodSignature) point.getSignature();
        String urlDes = Optional.ofNullable(signature.getMethod().getAnnotation(ApiOperation.class))
                .map(ApiOperation::value)
                .orElse(null);
        RequestInfo requestInfo = new RequestInfo();
        requestInfo.setRequestURI(requestPath);
        requestInfo.setClassName(className);
        requestInfo.setMethodName(methodName);
        requestInfo.setApiOperation(urlDes);
        requestInfo.setParameterNames(getArgs(point));

        //请求路径,类名,方法名
        log.debug("Request Info {}", JSON.toJSONString(requestInfo));
        Object proceed = point.proceed();
        log.debug("Response Info {}", JSON.toJSONString(proceed));
        return proceed;
    }

    private String getArgs(JoinPoint point) {
        String[] parameterNames = ((MethodSignature) point.getSignature()).getParameterNames();
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < parameterNames.length; i++) {
            sb.append(parameterNames[i])
                    .append(":")
                    .append(JSON.toJSONString(point.getArgs()[i]))
                    .append(";");
        }
        return sb.toString();
    }

    @Data
    static class RequestInfo {
        private String requestURI;
        private String className;
        private String methodName;
        private String apiOperation;
        private String parameterNames;

    }
}
