/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * <PERSON>ECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.config;

import com.gtech.promotion.callable.ThreadPoolUtil;
import com.gtech.promotion.component.activity.TemplateDomain;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * 项目启动初始化redis缓存
 */
@Slf4j
@Component
public class InitConfig implements CommandLineRunner{


    @Autowired
    private TemplateDomain templateDomain;

    @Value("${app.maxThread:50}")
    private int maxThread;

    @Override
    public void run(String...args) throws Exception{

        log.info("init start ... ");

        Map<String, String> tagMap = templateDomain.findTemplateTagAll();
        log.info("加载模板标签缓存:{}",tagMap);

        log.info("初始化线程池:{}",maxThread);
        ThreadPoolUtil.createExecutor(maxThread);

        log.info("init end");
    }

}
