# Logger configuration
logging.level.com.gtech.promotion = debug
server.port=8087

# Database configuration
#spring.datasource.url = jdbc:mysql://*************:3306/dev_promotion_db?useUnicode=true&characterEncoding=utf8&useSSL=false
#spring.datasource.username = gtech-dev
#spring.datasource.password = gtech-dev

spring.shardingsphere.datasource.names=master,read
#stç¯å¢ä¿é
#spring.shardingsphere.datasource.master.type=com.alibaba.druid.pool.DruidDataSource
#spring.shardingsphere.datasource.master.driver-class-name=com.mysql.jdbc.Driver
#spring.shardingsphere.datasource.master.url=****************************************************************************************************************************************************************
#spring.shardingsphere.datasource.master.username=oms_staging
#spring.shardingsphere.datasource.master.password=Gtech@123
#spring.shardingsphere.datasource.master.maxActive=100
#
#spring.shardingsphere.datasource.read.type=com.alibaba.druid.pool.DruidDataSource
#spring.shardingsphere.datasource.read.driver-class-name=com.mysql.jdbc.Driver
#spring.shardingsphere.datasource.read.url=****************************************************************************************************************************************************************
#spring.shardingsphere.datasource.read.username=oms_staging
#spring.shardingsphere.datasource.read.password=Gtech@123
#spring.shardingsphere.datasource.read.maxActive=100
#
#spring.main.allow-bean-definition-overriding=true
#spring.shardingsphere.masterslave.name=staging_promotion_db
#spring.shardingsphere.masterslave.master-data-source-name=master
#spring.shardingsphere.masterslave.slave-data-source-names=read



spring.shardingsphere.datasource.master.type=com.alibaba.druid.pool.DruidDataSource
spring.shardingsphere.datasource.master.driver-class-name=com.mysql.jdbc.Driver
spring.shardingsphere.datasource.master.url=****************************************************************************************************************************
spring.shardingsphere.datasource.master.username=gtech-dev
spring.shardingsphere.datasource.master.password=gtech-dev
spring.shardingsphere.datasource.master.maxActive=100

spring.shardingsphere.datasource.read.type=com.alibaba.druid.pool.DruidDataSource
spring.shardingsphere.datasource.read.driver-class-name=com.mysql.jdbc.Driver
spring.shardingsphere.datasource.read.url=****************************************************************************************************************************
spring.shardingsphere.datasource.read.username=gtech-dev
spring.shardingsphere.datasource.read.password=gtech-dev
spring.shardingsphere.datasource.read.maxActive=100

spring.main.allow-bean-definition-overriding=true
spring.shardingsphere.masterslave.name=dev_promotion_db
spring.shardingsphere.masterslave.master-data-source-name=master
spring.shardingsphere.masterslave.slave-data-source-names=read
#spring.shardingsphere.props.sql.show = true

spring.data.mongodb.host=*************
spring.data.mongodb.port=27017
spring.data.mongodb.database=dev_promotion_mongo
spring.data.mongodb.username=promotion
spring.data.mongodb.password=promotion

#spring.data.mongodb.host=************
#spring.data.mongodb.port=27017
#spring.data.mongodb.database=promotion
#spring.data.mongodb.username=promotion
#spring.data.mongodb.password=promotion

eureka.client.serviceUrl.defaultZone = http://eureka-dev.gtech.asia/eureka/
eureka.instance.lease-expiration-duration-in-seconds = 20
eureka.instance.lease-renewal-interval-in-seconds = 10

# REDIS (CasabaRedisProperties)
# Redisæ°æ®åºç´¢å¼ï¼é»è®¤ä¸º0ï¼
spring.redis.database=3
# Redisæå¡å¨å°å
spring.redis.host=*************
# Redisæå¡å¨è¿æ¥ç«¯å£
spring.redis.port=6379
# Redisæå¡å¨è¿æ¥å¯ç ï¼é»è®¤ä¸ºç©ºï¼
#spring.redis.password=
# è¿æ¥è¶æ¶æ¶é´ï¼æ¯«ç§ï¼
#spring.redis.timeout=0

# è¿æ¥æ± æå¤§è¿æ¥æ°ï¼ä½¿ç¨è´å¼è¡¨ç¤ºæ²¡æéå¶ï¼
spring.redis.lettuce.pool.max-active=8
# è¿æ¥æ± æå¤§é»å¡ç­å¾æ¶é´ï¼ä½¿ç¨è´å¼è¡¨ç¤ºæ²¡æéå¶ï¼
spring.redis.lettuce.pool.max-wait=-1
# è¿æ¥æ± ä¸­çæå¤§ç©ºé²è¿æ¥
spring.redis.lettuce.pool.max-idle=8
# è¿æ¥æ± ä¸­çæå°ç©ºé²è¿æ¥
spring.redis.lettuce.pool.min-idle=0

titan.gateway.url = http://gateway-dev.gtech.asia/api/

# å®æ¶ä»»å¡
#task:
#  baseUrl: http://***********:1507/api/task/task
#  addTaskUrl: ${task.baseUrl}/schedule_task/
#  addLogUrl: ${task.baseUrl}/task_log/
#  customerCode: unex-task
#  httpRequestMethod: 3
#  flag: 1

#è¯¥åºç¨æ¯å¦å¯ç¨çäº§è
rocketmq.producer.isOnOff = on
#åéåä¸ç±»æ¶æ¯çè®¾ç½®ä¸ºåä¸ä¸ªgroupï¼ä¿è¯å¯ä¸,é»è®¤ä¸éè¦è®¾ç½®ï¼rocketmqä¼ä½¿ç¨ip@pid(pidä»£è¡¨jvmåå­)ä½ä¸ºå¯ä¸æ ç¤º
rocketmq.producer.groupName = ${spring.application.name}
#mqçnameserverå°å
rocketmq.producer.namesrvAddr=*************:9876
#æ¶æ¯æå¤§é¿åº¦ é»è®¤1024*4(4M)
rocketmq.producer.maxMessageSize=4096000
#åéæ¶æ¯è¶æ¶æ¶é´,é»è®¤3000
rocketmq.producer.sendMsgTimeout=3000
#åéæ¶æ¯å¤±è´¥éè¯æ¬¡æ°ï¼é»è®¤2
rocketmq.producer.retryTimesWhenSendFailed=2
###consumer
##è¯¥åºç¨æ¯å¦å¯ç¨æ¶è´¹è
rocketmq.consumer.isOnOff=on
rocketmq.consumer.groupName=${spring.application.name}
#mqçnameserverå°å
rocketmq.consumer.namesrvAddr=*************:9876
#è¯¥æ¶è´¹èè®¢éçä¸»é¢åtags("*"å·è¡¨ç¤ºè®¢éè¯¥ä¸»é¢ä¸ææçtags),æ ¼å¼ï¼topic~tag1||tag2||tag3;topic2~*;
rocketmq.consumer.topics=PromotionTopic~*;
rocketmq.consumer.consumeThreadMin=20
rocketmq.consumer.consumeThreadMax=64
#è®¾ç½®ä¸æ¬¡æ¶è´¹æ¶æ¯çæ¡æ°ï¼é»è®¤ä¸º1æ¡
rocketmq.consumer.consumeMessageBatchMaxSize=1

pcrule.check.oms = false

promotion.group.black.list = 100008