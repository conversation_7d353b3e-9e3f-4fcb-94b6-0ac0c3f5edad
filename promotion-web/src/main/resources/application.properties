spring.profiles.active = @profile.active@
spring.session.store-type = REDIS
spring.application.name = promotion
spring.main.allow-bean-definition-overriding=true
spring.main.allow-circular-references=true
spring.mvc.pathmatch.matching-strategy= ant_path_matcher
apollo.bootstrap.enabled=true

spring.http.encoding.force=true
spring.servlet.multipart.enabled=true
spring.servlet.multipart.max-file-size=5MB
spring.servlet.multipart.max-request-size=10MB
spring.boot.admin.client.instance.prefer-ip=true

server.port= 8080
server.servlet.context-path= /promotion
server.connection-timeout = 10000

server.tomcat.accept-count = 500
server.tomcat.max-connections = 500
server.tomcat.max-threads = 500

eureka.client.register-with-eureka=true
eureka.instance.preferIpAddress=true
eureka.instance.instance-id=${spring.cloud.client.ip-address}:${server.port}
eureka.instance.health-check-url=${server.servlet.context-path}/actuator/health

app.version=@project.version@
info.build.version=@project.version@

demo.info.project.artifactId= @project.artifactId@
demo.info.name= @project.name@

demo.info.basedir=@project.basedir@
demo.info.outputDirectory=@project.build.outputDirectory@

# Whether to open Swagger access
gtech.common.security.enableSwagger = true

# Database configuration
#spring.datasource.type = com.alibaba.druid.pool.DruidDataSource
#spring.datasource.driver-class-name = com.mysql.jdbc.Driver

# mybatis configuration
mybatis.configuration.map-underscore-to-camel-case = true
mybatis.type-aliases-package=com.gtech.promotion.dao.entity
mybatis.mapper-locations=classpath:mapper/*.xml

# mapper config
mapper.mappers=com.gtech.commons.dao.mapper.GTechBaseMapper
mapper.not-empty=false
mapper.identity=MYSQL
# Query criteria must be set to delete
mapper.safe-delete=true
# Query criteria must be set to update
mapper.safe-update=true

# pagehelper config
pagehelper.helper-dialect=mysql
pagehelper.support-methods-arguments=true
pagehelper.params=count=countSql

ribbon.ReadTimeout=3000

# Spring Boot Actuator health check
management.endpoint.shutdown.enabled=true
management.endpoints.web.exposure.include=*
management.endpoints.web.exposure.exclude=heapdump
management.health.db.enabled = false

# Logger configuration
logging.file.name = logs/svc-titan-promotion.log
logging.config = classpath:logback-spring.xml
feign.client.config.default.loggerLevel= full
logging.pattern.level = trace_id=%mdc{trace_id} span_id=%mdc{span_id} trace_flags=%mdc{trace_flags} %5p