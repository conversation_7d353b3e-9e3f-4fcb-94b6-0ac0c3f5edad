package com.gtech.promotion.checker.purchaseconstraint;

import com.gtech.promotion.checker.Checker;
import com.gtech.promotion.utils.Constants;

public enum PurchaseConstraint<PERSON><PERSON><PERSON><PERSON><PERSON> implements Checker {
    FIRST_REFUSAL_VALID_FAIL("21", "No right of first refusal."),

    ORDER_MAX_QTY_PER_SKU_VALID_FAIL("22", "Limit the purchase of {0} item(s) per size."),
    ORDER_MAX_AMOUNT_PER_SKU_VALID_FAIL("23", "The total purchase amount for each size must not exceed {0}CNY."),
    ORDER_MAX_AMOUNT_VALID_FAIL("24", "The total amount per order must not exceed {0}CNY."),
    ORDER_MAX_QTY_VALID_FAIL("25", "Limit the purchase of {0} piece(s) per order."),
    CUSTOMER_MAX_QTY_PER_PRODUCT_VALID_FAIL("26", "Each person is limited to purchasing {0} piece(s) of each product."),
	CUSTOMER_MAX_QTY_ALL_PRODUCTS_VALID_FAIL("27", "{0} your {1} purchases have reached the limit."),
	CUSTOMER_MAX_QTY_ALL_PRODUCTS_VALID_FAIL1("271", "your {0} year {1} purchases have reached the limit."),
	CUSTOMER_MAX_QTY_ALL_PRODUCTS_VALID_FAIL2("272", "your {0} month {1} purchases have reached the limit."),
	CUSTOMER_MAX_QTY_ALL_PRODUCTS_VALID_FAIL3("273", "your {0} week {1} purchases have reached the limit."),
	CUSTOMER_MAX_AMOUNT_VALID_FAIL("28", "{0} your {1} purchases have reached the limit."),
	CUSTOMER_MAX_AMOUNT_VALID_FAIL1("281", "your {0} year {1}  purchases have reached the limit."),
	CUSTOMER_MAX_AMOUNT_VALID_FAIL2("282", "your {0} month {1} purchases have reached the limit."),
	CUSTOMER_MAX_AMOUNT_VALID_FAIL3("283", "your {0} week {1} purchases have reached the limit."),
    CUSTOMER_RULE_VALID_FAIL("29", "Custom rule valid fail."),
    ;

    private String code;

    private String message;

    PurchaseConstraintRuleChecker(String code, String message){
        this.code = Constants.ErrorCodePrefix.PURCHASE_CONSTRAINT_RULE_CHECKER.getCodePrefix() + code;
        this.message = message;
    }

	public static String getMessageByCode(String code) {
		for (PurchaseConstraintRuleChecker checker : PurchaseConstraintRuleChecker.values()) {
			if (checker.getCode().equals(code)) {
				return checker.getMessage();
			}
		}
		return null;
	}

    @Override
    public String getCode(){
        return code;
    }

    @Override
    public String getMessage(){
        return message;
    }

}
