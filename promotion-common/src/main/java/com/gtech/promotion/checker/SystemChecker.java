/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * <PERSON>ECH MAKES NO REPRESENTATIONS OR <PERSON><PERSON><PERSON>NTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.checker;

import com.gtech.promotion.utils.Constants;

/**
 * 系统异常规则
 */
public enum SystemChecker implements Checker {
	DUPLICATE_KEY("01", "This record already exists"),
	UNKNOWN_ERROR("02", "System unknown error"), 
	NULL_VO("03", "Parameter object is empty"),
	ILLEGAL_FORMAT_PARAM("04", "Wrong parameter type"), 
	HTTP_METHOD_ILLEGAL("05", "HTTP incorrect request"),
	CAN_NOT_INSTANCE_ERROR("07", "This class cannot be instantiated"),
	NULL_VO_OR_ILLEGAL_FORMAT_PARAM("08","Parameter object is empty or parameter type is wrong"),
	CALC_ERROR("10","System calculation timed out, please try again later"),
	SCHEDULE_ERROR("11","Timed task failed"),
	REDIS_ERROR("12","Cache service exception"),
	NULL_POINTER("13","Internal System Error"),//空指针异常
	DATA_TOOLONG("14","Field length exceeds"),
	NOT_NULL_PROMO("15","Field cannot be empty"),
	TOLONG_STRING("21","String length cannot exceed {0}"),
	ILLEGAL_FORMAT_PARAM_U("22",": Parameter type error"),
	ERROR_STREAM("23","IO异常"), 
	DATA_INTEGRITY_VIOLATION("24","Illegal database integrity"),
	NULL_UPDATE("26", "Not updated to data"),
	LOCK_FAIL("27", "System busy try again later "),
	;
    private String code;
	private String message;

	SystemChecker(String code, String message) {
		this.code = Constants.ErrorCodePrefix.SYSTEM.getCodePrefix() + code;
		this.message = message;
	}

	@Override
	public String getCode() {
		return code;
	}

	@Override
	public String getMessage() {
		return message;
	}

}
