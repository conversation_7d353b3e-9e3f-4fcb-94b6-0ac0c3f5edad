/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.checker.activity;

import com.gtech.promotion.checker.Checker;
import com.gtech.promotion.utils.Constants;

/**
 * TPromoActivityStoreService的异常枚举
 */
public enum TPromoProductChecker implements Checker{

    NOT_NULL("01","促销商品信息不能为空"),
    NULL_SKU_TOKEN("05","商品详情数据为空"),
    AT_LEAST_TWO_PRODUCT_SEQ("06","请选择至少2个商品池"),
    TWO_PRODUCT_SEQ("07","请选择2个商品池"),
    ONE_PRODUCT_SEQ("08","只能选择1个商品池"),
    NOT_NULL_PRODUCT_SEQ("10","商品池序号不能为空"),
    NOT_MORE_THAN_FIVE_PRODUCT_SEQ("11","请选择最多5个商品池序号"),
    REPEAT_PRODUCT_TAG("12","商品属性信息重复"),
    ERROR_PRODUCT_SELECT_SEQ("13","反选不能指定多个商品范围"),
    ERROR_PRODUCT_SELECT_ALL("14","反选不能为全部商品"),
    NOT_NULL_SKU_CODE("15","商品sku编码不能为空"),
    NULL_SKU("16","商品数据为空"),
    TOO_BIG_SKUNUM("17","商品池序号超出范围"),
    ERROR_PRODUCT_SELECT_EACH_PRICE("18","反选不能为每个不同特价活动"),
    ERROR_PRODUCT_TYPE_EACH_PRICE("19","每个不同特价活动商品范围只能是指定商品"),
    ERROR_ACTIVITY_TYPE_EACH_PRICE("20","商品文件模板类型错误"),
    ERROR_PRODUCT_SCOPE("21","商品范围重合"),
    ERROR_SALE_PRICE_EMPTY("22","销售价不能为空"),
    NOT_NULL_PRODUCT_CODE("23","商品product编码不能为空"),

    LEADER_PRICE("24","团长价不能大于拼团价"),

    FLASH_PRICE("25","拼团价(秒杀价或预售价)不能大于销售价"),

    SALE_PRICE("26","销售价不能大于市场价"),

    NO_LEADER_PRICE("27","不应该填写团长价，请检查对应参数."),

    NO_NULL_LIST_PRICE("28","市场价不能为空"),

    NO_NULL_SALE_PRICE("29","销售价不能为空"),

    NO_NULL_FLASH_PRICE("30","拼团价(秒杀价或预售价)不能为空"),

    NO_NULL_LEADER_PRICE("31","团长价不能为空"),
    NO_RIGHT_OF_FIRST_REFUSAL("32","没有优先购买权."),
    INSUFFICIENT_NUMBER_OF_PREEMPTIVE_RIGHTS("33","优先购买权数量不够."),

    NOT_EXIST_PRODUCT("34","商品不存在."),


    ;

    private String code;

    private String message;

    TPromoProductChecker(String code, String message){
        this.code = Constants.ErrorCodePrefix.PRODUCT_CHECKER.getCodePrefix() + code;
        this.message = message;
    }

    @Override
    public String getCode(){
        return code;
    }

    @Override
    public String getMessage(){
        return message;
    }

}
