/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * <PERSON>ECH MAKES NO REPRESENTATIONS OR <PERSON><PERSON><PERSON>NTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.checker.coupon;

import com.gtech.promotion.checker.Checker;
import com.gtech.promotion.utils.Constants;

/**   
 */
public enum CouponAllocateChecker implements Checker{
	
	NOT_NULL_MERCCODE("01","The Parameter tenantCode cannot be empty."),
	OVER_ALLOCATE_TOPLIMIT("08","No coupon left"),
	NOT_EXISTS_OUTERCOUPON("16","Invalid coupon code"),
	FROZENED("18","Coupon is frozen"),
	COUPON_EXPIRED("21","Coupon code expired"),
	ERROR_QUALIFICATION("30","Qualification check failed"),
    ERROR_LIMIT("34","Exceed coupon quantity limit"),
    ERROR_MEMBER_LABEL_LEVEL("36","当前会员标签不符合要求"),
    NOT_USER_COUPONS("37","The Parameter coupon code and member code cannot be both empty."),
    USER_NULL("38","The Parameter member code cannot be empty."),
    NOT_NULL_COUPON_CODE("40","The Parameter coupon code cannot be empty."),
    NOT_USER_NULL("41","The Parameter member code cannot be empty."),
    NOT_DISCOUNT_CODE("42","Discount type not supported"),
    NO_RELEASE("45","No coupons were put in."),
    START_TIME("46","Valid start time must later than activity begin time"),
    END_TIME("47","Valid end time must earlier than activity end time"),
    VALID_TIME("48","The Parameter valid time cannot be empty."),
    START_END_TIME("49","Valid start time must earlier than valid end time"),
    LOCK_FAIL("50","System busy try again later "),
	;

	private String code;
	private String message;
	
	
	CouponAllocateChecker(String code, String message) {
		this.code = Constants.ErrorCodePrefix.COUPON_RECEIVE_CHECKER.getCodePrefix() + code;
        this.message = message;		
	}

	@Override
	public String getCode() {
		
		return code;
	}

	@Override
	public String getMessage() {
	
		return message;
	}

}
