/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * <PERSON>ECH MAKES NO REPRESENTATIONS OR <PERSON><PERSON><PERSON>NTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.checker.marketing;

import com.gtech.promotion.checker.Checker;

public enum MarketingChecker implements Checker {
	ACTIVITY_NOT_EXIST("10143001","Activity not exist"),
	TICKET_NOT_ENOUGH("10143002","Ticket not enough"),
	TICKET_NOT_OWN("10143003","Ticket error"),
	TICKET_ALREADY_USED("10143007","Ticket already used"),
    PROBABILITY_TOTAL_100("10143004","Total probability can not large than 100%"),
    TICKET_NO_RELEASE("10143005","Must release ticket first"),
    TICKET_NO_INVENTORY("10143006","Ticket inventory not enough"),
    TICKET_SEND_NO_MEMBER("10143008","Member memberCode/tagCode/mobile list must have one"),
    TICKET_SEND_NO_MEMBER_FIND("10143009","Member tagCode/mobile list find no member"),
    ACTIVITY_NOT_EFFECTIVE("10143010","Activity not effective"),
    PRIZE_ACTIVITY_NOT_EXIST("10143011","Prize Activity not exist"),
    PRIZE_ACTIVITY_NOT_EFFECTIVE("10143012","Prize Activity not effective"),
    PARTICIPATION_LIMIT_REACHED("10143013","Participation limit reached"),
    PRIZE_QUOTA("10143014","Prize quota can not less than original quota"),

    LEADER_NO_PAID("101430115","团长未支付不可查询"),

    NO_MARKETING_GROUP("101430116","拼团不存在"),

    NO_MARKETING_GROUP_USER("101430117","该用户目前没有符合的拼团"),
    THE_USER_HAS_ALREADY_PARTICIPATED_IN_THIS_ACTIVITY("101430118","This user has already participated in this activity."),
    THE_RIGHT_OF_FIRST_REFUSAL_HAS_ALREADY_BEEN_USED("101430119","This right of first refusal has already been used."),
    FAILED_TO_USE_THE_RIGHT_OF_FIRST_REFUSAL("101430120","Failed to use the right of first refusal."),
    THE_ACTIVITY_INFORMATION_ERROR("101430121","Activity information error."),
    THE_SHARING_RECORD_DOES_NOT_EXIST("101430122","The sharing record does not exist."),
    ACTIVITY_STATUS_IS_ABNORMAL("101430123","Activity status is abnormal."),
    SHARERS_AND_FACILITATORS_CANNOT_BE_THE_SAME("101430124","Sharers and facilitators cannot be the same."),
    ACTIVITY_CONDITIONS_DO_NOT_MATCH("101430125","Activity conditions do not match."),
    SHARING_HAS_BEEN_COMPLETED("101430126","Sharing has been completed."),
    USER_INFORMATION_ABNORMALITY("101430127","User information abnormality."),
    ACTIVITY_NOT_START("10143128","Activity not start"),
    ACTIVITY_END("10143129","Activity end"),
    ;

	private final String code;
    private final String message;

    MarketingChecker(String code, String message){
        this.code = code;
        this.message = message;
    }

    @Override
    public String getCode() {
        return code;
    }

    @Override
    public String getMessage() {
        return message;
    }
}
