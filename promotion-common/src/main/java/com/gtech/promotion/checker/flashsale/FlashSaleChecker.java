package com.gtech.promotion.checker.flashsale;

import com.gtech.promotion.checker.Checker;

public enum FlashSaleChecker implements Checker {

    ACTIVITY_NOT_EXIST("flashsale.01","Activity not exist"),
    SKU_QUOTA_ZERO("flashsale.02","Sku quota must bigger than 0."),
    SKU_MAX_PER_USER_ZERO("flashsale.03","Sku max per user must higher than 0."),
    SKU_FLASH_PRICE_ZERO("flashsale.04","Sku campaign price must higher than 0."),
    SKU_FLASH_PRICE_BIG("flashsale.05","Sku campaign price must lower than sale price."),
    SKU_OR_UPLOAD("flashsale.06","Products or importNo must have one ."),
    NO_IMPORT_SKU("flashsale.07","Cannot find any sku by importNo ."),
    SKU_NOT_MATCH_ACTIVITY("flashsale.08","The product not match campaign activity ."),

    SKU_GROUP_LEADER_PRICE_BIG("flashSale.09","Group leader price at least lower than sale price."),
    SKU_GROUP_LEADER_PRICE_ZERO("flashSale.10","Group leader price must higher than 0."),

    SKU_QUANTITY("flashSale.11","Exceeding sku purchase limit quantity."),
    LIMITED_TYPE("flashsale.12","参数类型不存在${0} ."),


    ;

    private final String code;
    private final String message;

    FlashSaleChecker(String code, String message){
        this.code = code;
        this.message = message;
    }

    @Override
    public String getCode() {
        return code;
    }

    @Override
    public String getMessage() {
        return message;
    }

}
