/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * <PERSON>ECH MAKES NO REPRESENTATIONS OR WA<PERSON><PERSON>NTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.checker.activity;

import com.gtech.promotion.checker.Checker;
import com.gtech.promotion.utils.Constants;


/**   
 * 会员接口的校验
 */
public enum MemberChecker implements Checker{

    NOT_NULL_MEMBERCODE("01","会员编码不能为空"),
    NOT_NULL_MEMBERNAME("02","会员名称不能为空"),
    ;

    private String code;

    private String message;

    MemberChecker(String code, String message){
        this.code = Constants.ErrorCodePrefix.MEMBER_CHECKER.getCodePrefix() + code;
        this.message = message;
    }

    @Override
    public String getCode(){
        return code;
    }

    @Override
    public String getMessage(){
        return message;
    }

}
