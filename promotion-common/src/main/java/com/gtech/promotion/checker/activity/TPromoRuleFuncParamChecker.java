/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.checker.activity;

import com.gtech.promotion.checker.Checker;
import com.gtech.promotion.utils.Constants;

/**
 * 模板函数参数校验规则
 */
public enum TPromoRuleFuncParamChecker implements Checker{

    NOT_NULL("01","模板参数不能为空"),
    NOT_NULL_PARAM_VALUE("12","参数类型为数值时参数值不能为空"),
    ILLEGAL_EACH_FULL("20","参数条件只有为满或第时，才能有多个层级"),
    ILLEGAL_GIFT_LIMITMAX("21","赠品总条数小于赠送最大限制数"),
    DUPLICATE_RANKPARAM("22","层级参数rankParam重复"),
    ERROR_FUNCPARAM("23","模板函数错误"),
    ERROR_RANKPARAM("25","每个层级必须是4个函数"),
    ;

    private String code;

    private String message;

    TPromoRuleFuncParamChecker(String code, String message){
        this.code = Constants.ErrorCodePrefix.ACTIVITY_FUNC_PARAM.getCodePrefix() + code;
        this.message = message;
    }

    @Override
    public String getCode(){
        return code;
    }

    @Override
    public String getMessage(){
        return message;
    }

}
