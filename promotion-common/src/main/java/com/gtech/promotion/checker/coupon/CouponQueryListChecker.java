/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.checker.coupon;

import com.gtech.promotion.checker.Checker;
import com.gtech.promotion.utils.Constants;

/**
 */
public enum CouponQueryListChecker implements Checker{
    
    RECEIVE_TIME("01","领取开始和领取结束时间 必须同时出现"), 
    RELEASE_TIME("02","投放开始和投放结束时间 必须同时出现"), 
    USED_TIME("03","使用开始和使用结束时间 必须同时出现"),
    NOT_TENANT_CODE("04","租户编码不能为空"),
    EXCEPTION_STARTTIME("06","起始时间异常"), 
    EXCEPTION_ENDTIME("07","结束时间异常"),

    
    ;

    private String code;

    private String message;

    CouponQueryListChecker(String code, String message){
        this.code = Constants.ErrorCodePrefix.COUPON_QUERY_LIST_CHECKER.getCodePrefix() + code;
        this.message = message;
    }

    @Override
    public String getCode(){

        return code;
    }

    @Override
    public String getMessage(){

        return message;
    }
}
