/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.checker.activity;

import com.gtech.promotion.checker.Checker;
import com.gtech.promotion.utils.Constants;

/**   
 * 活动设置项检查
 */
public enum ActivitySettingChecker implements Checker {
    NOT_NULL_TENANTCODE("01","租户编码不能为空"),
    NOT_NULL_SETTINGTYPE("02","设置项类型不能为空"),
    NOT_NULL_SETTINGCODE("03","设置项编码不能为空"),
    NOT_NULL_SETTINGVALUE("04","设置项值不能为空"),
    ERROR_SETTINGTYPE("05","设置项类型不存在"),
    ERROR_SETTINGCODE("06","改类型设置项不存在该编码"),
    ERROR_SETTINGVALUE("07","改类型设置项不存在该值"),

    ;
    private String code;
    private String message;

    ActivitySettingChecker(String code, String message){
        this.code = Constants.ErrorCodePrefix.ACTIVITY_SETTING_CHECKER.getCodePrefix() + code;
        this.message = message;
    }

    @Override
    public String getCode() {
        return code;
    }

    @Override
    public String getMessage() {
        return message;
    }
}
