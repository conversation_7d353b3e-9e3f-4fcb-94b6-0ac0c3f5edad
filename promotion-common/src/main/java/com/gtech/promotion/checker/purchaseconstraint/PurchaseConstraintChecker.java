package com.gtech.promotion.checker.purchaseconstraint;

import com.gtech.promotion.checker.Checker;
import com.gtech.promotion.utils.Constants;

public enum PurchaseConstraint<PERSON><PERSON><PERSON> implements Checker {
    NOT_NULL_PURCHASE_CONSTRAINT_CODE("01","限购编码不能为空"),
    NOT_NULL_PURCHASE_CONSTRAINT_STATUS("02","状态不能为空"),
    NOT_EXIST_PURCHASE_CONSTRAINT("03","限购信息不存在"),
    END_TIME_NOW("04", "限购时间已经结束"),
    NOT_NULL_TENANT_CODE("08","租户编码不能为空"),
    END_TIME_LITTLE_THAN_START_TIME("09","Purchase constraint end time little than start time"),
    PURCHASE_CONSTRAINT_RULE_TIME_INVALID("10","purchase constraint rule time set error"),
    TENANT_NOT_NEED_AUDIT("48","The tenant does not need to be audited."),
    CREATE_AUDIT_NEED_DIFFERENT("49","The auditor and the creator cannot be the same person."),
    CREATE_AUDIT_COMMIT_NEED_DIFFERENT("50","The auditor and the creator and submitter cannot be the same person."),
    NOT_EMPTY_PURCHASE_CONSTRAINT_PRIORITY("51","Purchase constraint priority cannot be empty."),
    PURCHASE_CONSTRAINT_PRIORITY_UPDATE_NOT_MATCH("52", "Update purchase constraint priority number does not match."),
    INVALID_ORDER_ITEM("53", "Invalid order item."),
    INCREMENT_ERROR("54", "Increment error."),
    OMS_ERROR("55", "oms error."),
    MEMBER_ERROR("56", "member error."),
    PIM_ERROR("57", "pim error."),
    USER_CODE_NOT_EMPTY("58", "用户编码不能为空"),
    FORWARD_NOT_EMPTY("59", "forward不能为空"),
    TYPE_NOT_EMPTY("60", "type不能为空"),
    ORDER_CODE_NOT_EMPTY("61", "orderCode 不能为空"),
    INCREMENT_PRODUCTS_NOT_EMPTY("63", "incrementProducts 不能为空"),
    ;

    private String code;

    private String message;

    PurchaseConstraintChecker(String code, String message){
        this.code = Constants.ErrorCodePrefix.PURCHASE_CONSTRAINT_CHECKER.getCodePrefix() + code;
        this.message = message;
    }

    @Override
    public String getCode(){
        return code;
    }

    @Override
    public String getMessage(){
        return message;
    }
}
