/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * <PERSON>ECH MAKES NO REPRESENTATIONS OR <PERSON><PERSON><PERSON>NTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.checker.activity;

import com.gtech.promotion.checker.Checker;
import com.gtech.promotion.utils.Constants;

/**
 * Giveaway的异常枚举
 *
 */
public enum GiveawayChecker implements Checker{
    
    NULL_LIST("01","must have giveaways"),
    NOT_NULL_SKU_CODE("02","The Parameter sku code cannot be empty."),
    NOT_NULL_SKU_NAME("03","The Parameter sku name cannot be empty."),
    TOO_LONG_SKU_NAME("04","too long (> 256) giveaway name"),
    ERROR_SKUNUM("05","giveaway num invalid"),
    DUPLICATE_SKU_CODE("06","The same type of gift code cannot be repeated"),
    NULL_RANK_LIST("07","must have rank giveaways"),
    ACTIVITY_STATUS_NOT_MATCH("08","该赠品活动,状态存在错误")
    ,
;
    private String code;

    private String message;

    GiveawayChecker(String code, String message){
        this.code = Constants.ErrorCodePrefix.GIFT_CHECKER.getCodePrefix() + code;
        this.message = message;
    }

    @Override
    public String getCode(){
        return code;
    }

    @Override
    public String getMessage(){
        return message;
    }

}
