/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * <PERSON>ECH MAKES NO REPRESENTATIONS OR <PERSON><PERSON><PERSON>NTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.checker.coupon;

import com.gtech.promotion.checker.Checker;
import com.gtech.promotion.utils.Constants;

/**   
 */
public enum CouponCodeUser<PERSON>hecker implements Checker {

    NOT_NULL_MERCCODE("01","The Parameter tenantCode cannot be empty."),
    COUPON_CANNOT_LOCK("04","Coupon can not be locked."),
    COUPON_FROZEN("06","The coupon has been frozen."),
    COUPON_CODE_NOT_NULL("08","The Parameter coupon code cannot be empty."),
    COUPON_CODE_NOT_EXISTS("09","The coupon does not exist."),
    NOT_RECEIVE_TIME("17","The coupon is not in the collection period."),
    NOT_USABLE_TIME("18","Coupon is not available now"),

    EXPIRE_TIME("19","券码过期，不可启用"),
    ;

    private String code;
    private String message;

    CouponCodeUserChecker(String code, String message){
        this.code = Constants.ErrorCodePrefix.COUPON_CODE_USER.getCodePrefix() + code;
        this.message = message;
    }

    @Override
    public String getCode() {
        return code;
    }

    @Override
    public String getMessage() {
        return message;
    }
}
