/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.checker.activity;

import com.gtech.promotion.checker.Checker;
import com.gtech.promotion.utils.Constants;

/**
 * TPromoActivityStoreService的异常枚举
 */
public enum TPromoProductDetail<PERSON><PERSON><PERSON> implements Checker{
    NOT_NULL_ACTIVITY_CODE("01","ActivityCode can not be empty"),
    NOT_NULL_TENANT_CODE("02","系统商户编码不能为空"),
    NO_FILE_SELECTED("03","未选择任何文件"),
    NOT_SUPPORTED("04","该文件类型暂不支持,请选择正确的xlsx结尾的文件"),
    NOT_NULL_PRODUCT_CODE("05","SPU编码和套装编码不能都为空:第${0}行"),
    ERROR_ACTIVITY_TENANT_CODE("06","该系统商户无此活动"),
    NULL_FILE_CONTENT("07","文件内容为空"),
    NOT_NULL_SEQ_NUM("08","商品池序号不能为空"),
    NOT_NULL_FIRST_ROW("09","文件第一行不能为空"),
    ERROR_COMBINE("11","SPU编码和套装编码不能一起传:第${0}行"),
    NO_PRODUCT_SKU_PRICE("12","商品价格不能为空：第${0}行"),
    NOT_NULL_PRICE_FLAG("13","是否带价格标示不能为空"),
    ERROR_PRICE("14","商品价格不合法：第${0}行"),
    ;
    

    private String code;

    private String message;

    TPromoProductDetailChecker(String code, String message){
        this.code = Constants.ErrorCodePrefix.PRODUCT_DETAIL.getCodePrefix() + code;
        this.message = message;
    }

    @Override
    public String getCode(){
        return code;
    }

    @Override
    public String getMessage(){
        return message;
    }

}
