/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.checker.activity;

import com.gtech.promotion.checker.Checker;
import com.gtech.promotion.utils.Constants;

/**   
 * 互斥叠加表达式检查
 */
public enum ActivityExpressionChecker implements Checker {
    NOT_NULL("01","该商户下不存在表达式"),
    NOT_NULL_TENANTCODE("02","系统商户编码不能为空"),
    NOT_NULL_EXPRESSION("03","表达式不能为空"),
    ;
    private String code;
    private String message;

    ActivityExpressionChecker(String code, String message){
        this.code = Constants.ErrorCodePrefix.ACTIVITY_COMP_PARAM.getCodePrefix() + code;
        this.message = message;
    }

    @Override
    public String getCode() {
        return code;
    }

    @Override
    public String getMessage() {
        return message;
    }
}
