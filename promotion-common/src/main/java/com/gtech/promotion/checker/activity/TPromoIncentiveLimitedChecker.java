/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.checker.activity;

import com.gtech.promotion.checker.Checker;
import com.gtech.promotion.utils.Constants;

/**   
 * TPromoIncentiveLimitedChecker
 */
public enum TPromoIncentiveLimited<PERSON>hecker implements Checker{

    NOT_NULL_LIMITATION_LIST("03","限制标志为01时，限制列表不能为空"),
    NOT_NULL_LIMITATION_CODE("04","限制条件编码不能为空"),
    NOT_REPEAT_LIMITATION_CODE("10","限制条件编码 不能重复 插入"),
    NOT_NULL_LIMITATION_VALUE("11","限制条件值不能为空或者不能小于0"),
    ERROR_LIMITED_SIZE("12","限制条件值不能超过16个字符"),
    ERROR_LIMITED_CODE("13","限制条件编码不存在"),
    ;

    private String code;
    private String message;

    TPromoIncentiveLimitedChecker(String code, String message) {
        this.code = Constants.ErrorCodePrefix.INCENTIVE_LIMITED.getCodePrefix() + code;
        this.message = message;
    }

    @Override
    public String getCode() {
        return code;
    }

    @Override
    public String getMessage() {
        return message;
    }

    @Override
    public String toString() {
        return "TPromoIncentiveLimitedRule{" +
                "brandCode=" + code + '\'' +
                ", message='" + message + '\'' +
                '}';
    }
}
