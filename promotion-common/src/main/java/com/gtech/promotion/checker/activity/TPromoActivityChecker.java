/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.checker.activity;

import com.gtech.promotion.checker.Checker;
import com.gtech.promotion.utils.Constants;

/**
 * 活动规则参数校验
 */
public enum TPromoActivityChecker implements Checker{

    NOT_NULL("01","此活动不存在"),
    END_TIME_NOW("02","The end date is over."),
    NOT_NULL_ACTIVITY_CODE("04","活动编码不能为空"),
    NOT_NULL_TENANT_CODE("08","租户编码不能为空"),

    ERROR_TIME_FORMAT("15","time format should be yyyyMMddHHmmss."),
    NULL_ACTIVITY_ENTITY("23","当前用户下没有该活动"),
    NOT_ACTIVITY_STATUS_TYPE("25","活动状态类型不存在"),
    NOT_NULL_ACTIVITY_STATUS("27","状态不能为空"),
    ERROR_DELETESTATUS("36","状态是待提交或已拒绝的活动才可以删除"),
    ERROR_DELETE("37","删除活动失败"),
    END_TIME_BEFORE_ACTIVITY_END_TIME("38","The new end date must after the current end date."),
    ACTIVITY_END("39","活动已结束"),
    ACTIVITY_NOT_EFFECTIVE("40","The activity status is not effective."),
    ACTIVITY_BEGIN_END_TIME("41","activityBegin must be earlier than activityEnd."),
    ACTIVITY_END_BEGIN_TIME("42","activityEnd must be later than current time."),
    REDIS_TIME_LIMIT("43","activityEnd must be earlier than ${0}."),
    WARM_BEGIN_TIME("44","warmBegin must be earlier than activityBegin."),
    LATER_ACTIVITY_BEGIN("45","coolDown must be later than activityBegin."),
    EARLIER_ACTIVITY_END("46","coolDown must be earlier than activityEnd."),
    RANK("47","rankParam must be between 1 and 99."),
    TENANT_NOT_NEED_AUDIT("48","The tenant does not need to be audited."),
    CREATE_AUDIT_NEED_DIFFERENT("49","The auditor and the creator cannot be the same person."),
    CREATE_AUDIT_COMMIT_NEED_DIFFERENT("50","The auditor and the creator and submitter cannot be the same person."),
    SUPPORT_ANONYMOUS("51","Only anonymous coupon activities are supported"),
    RELEASE_EMPTY("52","Release coupon is empty."),

    COUPON_INVENTORY("53","The total quantity of coupons is less than the customers.total:${0}"),

    NOT_RELEASE_INVENTORY("54","The delivery batch does not exist. Please enter the correct delivery batch."),
    NOT_NULL_OR_NO_EFFECTIVE("55","Activity does not exist or has no active activity"),
    NO_COUPON("56","There are no valid vouchers."),
    NOT_NULL_DOMAIN_CODE("57","domainCode not empty"),

    INIT_GROUP("58","The grouping code does not exist. Please initialize the grouping."),

    RECEIVE_PROCESS("59","该批次正在发放券,请稍后。"),

    TURN_ON_GROUP("60","使用分组功能，请先开启分组开关."),

    DISABLE_GROUP("61","营销系统变量(Disable_Overlay_Mutex_Array)配置存在有误."),





    ;


    private String code;

    private String message;

    TPromoActivityChecker(String code, String message){
        this.code = Constants.ErrorCodePrefix.RULE_CHECKER.getCodePrefix() + code;
        this.message = message;
    }

    @Override
    public String getCode(){
        return code;
    }

    @Override
    public String getMessage(){
        return message;
    }

}
