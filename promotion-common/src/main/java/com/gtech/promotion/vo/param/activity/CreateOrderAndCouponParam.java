package com.gtech.promotion.vo.param.activity;

import com.gtech.commons.utils.CheckUtils;
import com.gtech.promotion.exception.ErrorCodes;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * @Description :
 * <AUTHOR> gaojie.li
 * @Date : 2021/6/10 14:31
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("CreateOrderAndCouponRequest")
public class CreateOrderAndCouponParam implements Serializable {

    private static final long serialVersionUID = 4716502037850178064L;

    @ApiModelProperty(value = "used coupon codes in order (Separated by commas)",required = true)
    private String couponCodes;
    @ApiModelProperty(value = "Order number.",required = true)
    private String orderNo;
    @ApiModelProperty(value = "Tenant code.",required = true)
    private String tenantCode;
    @ApiModelProperty(value = "Domain code.",required = true)
    private String domainCode;
    @ApiModelProperty(value = "Member code.",required = true)
    private String memberCode;
    // Language id.
    private String language;
    @ApiModelProperty(value = "Activity related Goods",required = true)
    private List<ActivityRelationProduct> activityRelationProducts;

    public void validate() {
        CheckUtils.isNotBlank(this.couponCodes, ErrorCodes.PARAM_EMPTY, "couponCodes");
        CheckUtils.isNotBlank(this.orderNo, ErrorCodes.PARAM_EMPTY, "orderNo");
        CheckUtils.isNotBlank(this.tenantCode, ErrorCodes.PARAM_EMPTY, "tenantCode");
        CheckUtils.isNotBlank(this.domainCode, ErrorCodes.PARAM_EMPTY, "domainCode");
        CheckUtils.isNotBlank(this.memberCode, ErrorCodes.PARAM_EMPTY, "memberCode");


    }
}
