package com.gtech.promotion.vo.param.purchaseconstraint;

import com.gtech.commons.utils.StringUtil;
import com.gtech.promotion.api.ApiConstants;
import com.gtech.promotion.checker.purchaseconstraint.PurchaseConstraintChecker;
import com.gtech.promotion.exception.Check;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

@Data
@ToString
@ApiModel("UpdatePurchaseConstraintStatusRequest")
public class UpdatePurchaseConstraintStatusParam {
    @ApiModelProperty(value="Tenant code",required=true)
    private String tenantCode;

    @ApiModelProperty(value="Activity code",required=true)
    private String purchaseConstraintCode;

    @ApiModelProperty( value="orgCode.", example="default",required=true)
    private String orgCode;

    @ApiModelProperty(value= ApiConstants.PURCHASE_CONSTRAINT_STATUS,required=true)
    private String purchaseConstraintStatus;

    @ApiModelProperty(value = "User code of the operator.", example="U00001", required = true)
    private String operateUser;

    @ApiModelProperty(value = "User last name of the operator.")
    private String operateLastName;

    @ApiModelProperty(value = "User first name of the operator.")
    private String operateFirstName;

    public void validate(){
        Check.check(StringUtil.isBlank(tenantCode), PurchaseConstraintChecker.NOT_NULL_TENANT_CODE);
        Check.check(StringUtil.isBlank(purchaseConstraintCode), PurchaseConstraintChecker.NOT_NULL_PURCHASE_CONSTRAINT_CODE);
        Check.check(StringUtil.isBlank(purchaseConstraintStatus), PurchaseConstraintChecker.NOT_NULL_PURCHASE_CONSTRAINT_STATUS);
    }
}
