package com.gtech.promotion.vo.param.point.query;

import java.io.Serializable;

import com.gtech.commons.page.PageParam;
import com.gtech.commons.utils.CheckUtils;
import com.gtech.promotion.exception.ErrorCodes;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString
public class QueryPointTransactionParam extends PageParam implements Serializable {

    private static final long serialVersionUID = 3654772322482930636L;

    @ApiModelProperty(value = "Domain code", required = true)
	private String domainCode;

	@ApiModelProperty(value = "Tenant code", required = true)
	private String tenantCode;

	@ApiModelProperty(value = "Transaction serial number.")
    private String transactionSn;

	@ApiModelProperty(value = "Point account code. (UserCode or OrgCode)")
	private String accountCode;

	@ApiModelProperty(value = "Point account type. (1-User 2-Organization)")
	private Integer accountType;

	@ApiModelProperty(value = "Point campaign code.")
    private String campaignCode;

	@ApiModelProperty(value = "Point transaction type. (1-Increase points 2-Deduct points)")
	private Integer transactionType;

	@ApiModelProperty(value = "Transaction date. (yyyyMMddHHmmss)")
    private Long transactionDate;

	@ApiModelProperty(value = "Refer transaction serial number.")
    private String referTransactionSn;

	@ApiModelProperty(value = "Refer order number.")
    private String referOrderNumber;

	@ApiModelProperty(value = "begin time.")
	private String beginDateFrom;

	@ApiModelProperty(value = "end time.")
	private String endDateTo;

    /**
     * Parameter validation.
     */
    public void validate() {

        CheckUtils.isNotBlank(this.domainCode, ErrorCodes.PARAM_EMPTY, "domainCode");
        CheckUtils.isNotBlank(this.tenantCode, ErrorCodes.PARAM_EMPTY, "tenantCode");
    }
}