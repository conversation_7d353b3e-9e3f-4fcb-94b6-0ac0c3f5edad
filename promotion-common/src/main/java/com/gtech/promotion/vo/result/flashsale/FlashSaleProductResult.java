package com.gtech.promotion.vo.result.flashsale;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class FlashSaleProductResult implements Serializable {

    private static final long serialVersionUID = -3195419710820815987L;

    @ApiModelProperty(value = "Product code.")
    private String productCode;


    @ApiModelProperty(value = "Sku code.")
    private String skuCode;

    @ApiModelProperty(value = "Sku name.")
    private String skuName;

    @ApiModelProperty(value = "Sku quota.")
    private Integer skuQuota;

    @ApiModelProperty(value = "Sku inventory.")
    private Integer skuInventory;

    @ApiModelProperty(value = "Max flash sale times per user.")
    private Integer maxPerUser;

    @ApiModelProperty(value = "Sku list price.")
    private BigDecimal listPrice;

    @ApiModelProperty(value = "Sku sale price.")
    private BigDecimal salePrice;

    @ApiModelProperty(value = "Sku flash sale price.")
    private BigDecimal flashPrice;
}
