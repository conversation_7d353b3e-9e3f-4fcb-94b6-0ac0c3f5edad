package com.gtech.promotion.vo.param.activity;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * @Description :
 * <AUTHOR> gaojie.li
 * @Date : 2021/7/19 14:36
 */
@NoArgsConstructor
@AllArgsConstructor
@Data
public class CustomCondition implements Serializable {


    private static final long serialVersionUID = 4960584768476488626L;

    @ApiModelProperty(value = "Custom key.")
    private String customKey;

    @ApiModelProperty(value = "custom value. This field is only used by the mall and has been discarded")
    private String customValue;


    @ApiModelProperty(value = "Custom value list.")
    private List<String> customValueList;

}
