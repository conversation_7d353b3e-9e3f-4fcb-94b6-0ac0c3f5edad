package com.gtech.promotion.vo.bean.marketing;

import com.gtech.commons.utils.CheckUtils;
import com.gtech.promotion.code.marketing.SwitchEnum;
import com.gtech.promotion.exception.ErrorCodes;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @Description :
 * <AUTHOR> gaojie.li
 * @Date : 2023/6/5 15:35
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("MarketingGroupRequest")
public class MarketingGroup implements Serializable {

    private static final long serialVersionUID = 5655284599496413706L;

    /**
     * 时效字段
     */
    @ApiModelProperty(value = "effective hour", required = true)
    private Integer effectiveHour;

    @ApiModelProperty(value = "show leader price. 0不展示 1展示 . 0-no 1-yes default 0")
    private Integer showLeaderPrice;

    /***
     * 成团人数
     */
    @ApiModelProperty(value = "group size", required = true)
    private Integer groupSize;


    /**
     * 允许自动成团
     * 默认开， 0开 1关闭
     */
    @ApiModelProperty(value = "auto group flag;0-yes 1-no;default 0")
    private Integer autoGroupFlag;


    @ApiModelProperty(value = "Promotional activities that the group leader can participate. Json")
    private String leaderBenefits;

    @ApiModelProperty(value = "是否封闭团，1是0不是，默认0")
    private Integer closeFlag;

    public void validate(){

        CheckUtils.isTrue(null != effectiveHour, ErrorCodes.PARAM_EMPTY, "effectiveHour");
        CheckUtils.isTrue(null != groupSize, ErrorCodes.PARAM_EMPTY, "groupSize");

        CheckUtils.isTrue(effectiveHour.intValue() > 0, ErrorCodes.DATA_INTEGER, "effectiveHour");
        CheckUtils.isTrue(groupSize.intValue() > 1, ErrorCodes.DATA_GROUP_SIZE_INTEGER, "groupSize");

        if (null != autoGroupFlag){
            CheckUtils.isTrue(SwitchEnum.exist(autoGroupFlag.toString()), ErrorCodes.SWITCH, "autoGroupFlag");
        }

        if (null != showLeaderPrice){
            CheckUtils.isTrue(SwitchEnum.exist(showLeaderPrice.toString()), ErrorCodes.SWITCH, "showLeaderPrice");
        }
    }

}
