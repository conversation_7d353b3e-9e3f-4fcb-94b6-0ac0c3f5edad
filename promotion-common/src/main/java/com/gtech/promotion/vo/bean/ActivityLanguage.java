/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WA<PERSON>RANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.vo.bean;

import com.gtech.commons.utils.CheckUtils;
import com.gtech.promotion.exception.ErrorCodes;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.io.Serializable;
import java.util.List;


/**
 * ActivityLanguage
 *
 * <AUTHOR>
 * @Date 2020-02-10
 */
@Getter
@Setter
@ToString
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("ActivityLanguage")
public class ActivityLanguage implements Serializable {

    private static final long serialVersionUID = -2603722998351779238L;

    @ApiModelProperty(value = "Activity name.",required = false)
    private String activityName;

    @ApiModelProperty(value = "Activity label.",required = false)
    private String activityLabel;

    @ApiModelProperty(value = "Activity description.",required = false)
    private String activityDesc;

    @ApiModelProperty(value = "Ext descriptions.")
    private List<ExtDescription> extDescriptions;

    public void setActivityShortDesc(String activityShortDesc) {

        if (null == this.activityRemark) {
            this.activityRemark = activityShortDesc;
        }
    }

    public String getActivityShortDesc() {

        return this.activityRemark;
    }

    @ApiModelProperty(value = "Activity remark.",required = false)
    private String activityRemark;
    public void setActivityRemark(String activityRemark) {

        if (null == this.activityRemark) {
            this.activityRemark = activityRemark;
        }
    }

    @ApiModelProperty(value = "Language id. (en-US/id-ID/zh-CN/...)",required = true)
    private String language;

    /**
     * Parameter validation.
     */
    public void validate() {

        CheckUtils.isNotBlank(this.language, ErrorCodes.PARAM_EMPTY, "language");
    }
}
