/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.vo.result.coupon;

import com.gtech.promotion.api.ApiConstants;
import com.gtech.promotion.vo.bean.ActivityPeriod;
import com.gtech.promotion.vo.bean.CouponReleaseTime;
import com.gtech.promotion.vo.bean.Giveaway;
import com.gtech.promotion.vo.bean.Qualification;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**   
 * 
 */
@Getter
@Setter
@ToString
@ApiModel("QueryCouponActivityListByProductResponse")
public class QueryCouponActivityListByProductResult implements Serializable {

    private static final long serialVersionUID = 3038237219322919383L;

    @ApiModelProperty(ApiConstants.ACTIVITY_TYPE)
    private String activityType;//

    @ApiModelProperty("Activity code")
    private String activityCode;//

    @ApiModelProperty("Activity name")
    private String activityName;//

    @ApiModelProperty(value = "Group code.")
    private String groupCode;

    @ApiModelProperty(value = "Custom conditions.")
    private String customCondition;

    @ApiModelProperty("Activity desc")
    private String activityDesc;//

    @ApiModelProperty("Activity remark")
    private String activityRemark;//

    @ApiModelProperty("Activity label")
    private String activityLabel;//

    @ApiModelProperty(ApiConstants.ACTIVITY_SORT)
    private String activitySort;//

    @ApiModelProperty("Activity begin time")
    private String activityBegin;//

    @ApiModelProperty("Activity end time")
    private String activityEnd;//

    @ApiModelProperty("Warm begin time")
    private String warmBegin;//

    @ApiModelProperty(value = "Create time.")
    private Date createTime;

    @ApiModelProperty(value = "Qualification list")
    private List<Qualification> qualifications;//

    /**
     * @deprecated
     */
    @ApiModelProperty(value = "Deprecated. Member level codes. Separated by commas.")
    @Deprecated
    private String memberLevelCodes; //NOSONAR

    /**
     * @deprecated
     */
    @ApiModelProperty(value = "Deprecated. Member label codes. Separated by commas.")
    @Deprecated
    private String memberLabelCodes; //NOSONAR

    @ApiModelProperty(value = ApiConstants.PROMO_SCOPE)
    private String promoScope;//

    @ApiModelProperty("Warm end time")
    private String warmEnd;//

    @ApiModelProperty("Activity URL")
    private String activityUrl;//

    @ApiModelProperty(ApiConstants.PRODUCT_SELECTION_TYPE)
    private String productSelectionType;

    //赠品赠送最大限制数量
    @ApiModelProperty("Maximum number of gifts")
    private String giftLimitMax;//

    @ApiModelProperty("Giveaways")
    private List<Giveaway> giveaways;

    //商品池序号
    @ApiModelProperty("Seq num")
    private String seqNum;

    ////////////////////////////////////////////////////////////////////////////////////////////////
    ////////////////////////////////////////////////////////////////////////////////////////////////
    @ApiModelProperty(value = "Coupon face value")
    private BigDecimal faceValue;

    @ApiModelProperty(ApiConstants.FACE_UNIT)
    private String faceUnit;

    @ApiModelProperty(ApiConstants.COUPON_TYPE)
    private String couponType;

    @ApiModelProperty(value = "Each limit get")
    private Integer userLimitMax;

    @ApiModelProperty("Reward type")
    private String rewardType;

    //条件值单位 01：元  02：件
    @ApiModelProperty(ApiConstants.CONDITION_UNIT)
    private String conditionUnit;

    @ApiModelProperty("Condition value")
    private BigDecimal conditionValue;

    //券活动的券可用时间
    @ApiModelProperty("Coupon activity coupon available time")
    private List<CouponReleaseTime> couponValidTime;

    @ApiModelProperty(value = "Activity cool down time.")
    private String coolDown;

    @ApiModelProperty(value = "Activity background image.")
    private String backgroundImage;

    @ApiModelProperty(value = "Activity period.")
    private ActivityPeriod activityPeriod;

    @ApiModelProperty(value = "External activity id")
    private String externalActivityId;
}
