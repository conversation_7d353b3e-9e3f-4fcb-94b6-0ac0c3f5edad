/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * <PERSON>ECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.vo.result.coupon;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;

/**   
 * 
 */
@Getter
@Setter
@ToString
@ApiModel("QueryCouponActivityListByProductListResponse")
public class QueryCouponActivityListByProductListResult implements Serializable {

    private static final long serialVersionUID = 3038237219322919383L;


    @ApiModelProperty("Product code")
    private String productCode;

    @ApiModelProperty("Product activity list")
    private List<QueryCouponActivityListByProductResult> productActivityList;

    @ApiModelProperty("Sku list")
    private List<SkuActivity> skuList;


    @Data
    public static class SkuActivity implements Serializable{


        private static final long serialVersionUID = 7693395687370543111L;

        @ApiModelProperty("Sku code")
        private String skuCode;

        @ApiModelProperty("Sku activity list")
        private List<QueryCouponActivityListByProductResult> skuActivityList;

    }




}
