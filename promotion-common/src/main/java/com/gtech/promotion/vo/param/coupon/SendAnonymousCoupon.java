package com.gtech.promotion.vo.param.coupon;

import com.gtech.commons.utils.CheckUtils;
import com.gtech.promotion.exception.ErrorCodes;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @Description :
 * <AUTHOR> gaojie.li
 * @Date : 2022/11/7 11:12
 */
@Data
public class SendAnonymousCoupon implements Serializable {

    private static final long serialVersionUID = 2609214005996389574L;

    @ApiModelProperty(value = "Domain code.",example = "D00001",required = true)
    private String domainCode;

    @ApiModelProperty(value = "Tenant code", required = true)
    private String tenantCode;

    @ApiModelProperty(value = "Coupon activity code.", required = true)
    private String activityCode;

    @ApiModelProperty(value = "How many coupon codes are obtained this time.", required = true)
    private Integer sendTotal;

    @ApiModelProperty(value = "1:The voucher is valid after being collected 2:Fixed effective time. default 1", required = true)
    private Integer receiveType = 1;

    @ApiModelProperty(value = "Coupon activity release code.")
    private String releaseCode;


    public void validate() {

        CheckUtils.isNotBlank(this.domainCode, ErrorCodes.PARAM_EMPTY, "domainCode");
        CheckUtils.isNotBlank(this.tenantCode, ErrorCodes.PARAM_EMPTY, "tenantCode");
        CheckUtils.isNotBlank(this.activityCode, ErrorCodes.PARAM_EMPTY, "activityCode");
        CheckUtils.isNotNull(this.sendTotal, ErrorCodes.PARAM_EMPTY, "sendTotal");
        CheckUtils.isNotNull(this.receiveType, ErrorCodes.PARAM_EMPTY, "receiveType");

        CheckUtils.isTrue(this.sendTotal > 0 && this.sendTotal <= 2000, ErrorCodes.PARAM_SPECIFICATION_ERROR, "receiveCount must greater than 0 and less than 2000.");
    }

}
