package com.gtech.promotion.vo.param.point.query;

import java.io.Serializable;

import com.gtech.commons.utils.CheckUtils;
import com.gtech.promotion.exception.ErrorCodes;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString
public class GetPointTransactionParam implements Serializable {

    private static final long serialVersionUID = -8634618856461794319L;

    @ApiModelProperty(value = "Domain code",required = true)
    private String domainCode;

    @ApiModelProperty(value = "tenantCode",required = true)
    private String tenantCode;

    @ApiModelProperty(value = "transactionSn",required = true)
    private String transactionSn;

    /**
     * Parameter validation.
     */
    public void validate() {

        CheckUtils.isNotBlank(this.domainCode, ErrorCodes.PARAM_EMPTY, "domainCode");
        CheckUtils.isNotBlank(this.tenantCode, ErrorCodes.PARAM_EMPTY, "tenantCode");
        CheckUtils.isNotBlank(this.transactionSn, ErrorCodes.PARAM_EMPTY, "transactionSn");
    }

}
