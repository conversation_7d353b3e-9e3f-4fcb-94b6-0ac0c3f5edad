package com.gtech.promotion.vo.param.point.query;

import java.io.Serializable;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
public class PointCampaignUniqueParam implements Serializable{

	/**
	 * 
	 */
	private static final long serialVersionUID = 5825657452236955730L;

	/**
	 * 积分代码
	 */
	@ApiModelProperty(value = "campaignCode", required = true)
	@NotEmpty(message = "campaignCode can not be empty")
	private String campaignCode;

	@ApiModelProperty(value = "tenantCode", required = true)
	@NotEmpty(message = "tenantCode can not be empty")
	private String tenantCode;

	
	@EqualsAndHashCode(callSuper=false)
	@Data
	public static class PointCampaignStatusUniqueVo extends PointCampaignUniqueParam {
		/**
		 * 
		 */
		private static final long serialVersionUID = -8612717153118651895L;
		@ApiModelProperty(value = "status", required = true)
		@NotNull(message = "status can not be null")
		private Integer status;
		@ApiModelProperty(value = "oldStatus", required = true)
		@NotNull(message = "oldStatus can not be null")
		private Integer oldStatus;
	}

    /**
     * Parameter validation.
     */
    public void validate() {
		// to do something
    }
}
