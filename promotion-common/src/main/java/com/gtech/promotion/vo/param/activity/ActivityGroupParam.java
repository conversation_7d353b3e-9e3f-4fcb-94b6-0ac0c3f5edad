package com.gtech.promotion.vo.param.activity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @Description :
 * <AUTHOR> gaojie.li
 * @Date : 2023/7/16 17:05
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("ActivityGroupRequest")
public class ActivityGroupParam implements Serializable {

    private static final long serialVersionUID = 4065948526627383588L;

    @ApiModelProperty(value = "分组编码", example = "1231209", required = true)
    private String groupCode;

}
