package com.gtech.promotion.vo.param.marketing;

import com.gtech.commons.utils.CheckUtils;
import com.gtech.promotion.exception.ErrorCodes;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.io.Serializable;

@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
public class MarketingUpdateParam extends MarketingCreateParam implements Serializable {

    private static final long serialVersionUID = -7230634988536186461L;
    @ApiModelProperty(value = "activity code.",example = "12556568987",required = true)
    private String activityCode;

    @Override
    public void validate() {

        CheckUtils.isNotBlank(this.activityCode, ErrorCodes.PARAM_EMPTY, "activityCode");
        super.validate();
    }
}
