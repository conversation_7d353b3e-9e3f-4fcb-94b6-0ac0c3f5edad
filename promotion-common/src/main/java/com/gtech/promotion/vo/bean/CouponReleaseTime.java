/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * <PERSON>ECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.vo.bean;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.io.Serializable;
import java.util.Date;

/**
 *
 */
@Getter
@Setter
@ToString
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "CouponReleaseTime")
public class CouponReleaseTime implements Serializable {

    private static final long serialVersionUID = 1765533312128635042L;

    @ApiModelProperty(value = "Valid start time(yyyyMMddhhmmss)")
    private String validStartTime;

    @ApiModelProperty(value = "Valid end time(yyyyMMddhhmmss)")
    private String validEndTime;

    @ApiModelProperty(value = "Valid days")
    private Integer validDays;

    @ApiModelProperty(value = "Inventory")
    private Integer inventory;

    @ApiModelProperty(value = "Create time")
    private Date createTime;


    // Activity code.
    @ApiModelProperty(value = "Activity code")
    private String activityCode;

    // Coupon type: 01-优惠券 02-匿名券 03-优惠码
    @ApiModelProperty(value = "Coupon type. 01-优惠券 02-匿名券 03-优惠码")
    private String couponType;


    // Coupon release code.
    @ApiModelProperty(value = "Release code")
    private String releaseCode;

    // Coupon release status: 01-Waiting released 02-Already released 03-Imported 04-Cancel released
    @ApiModelProperty(value = "Release status.01-Waiting released 02-Already released 03-Imported 04-Cancel released")
    private String releaseStatus;

    // Release coupon quantity: releaseQuantity fixed 1 while couponType=03
    @ApiModelProperty(value = "Release quantity. releaseQuantity fixed 1 while couponType=03")
    private Integer releaseQuantity;

    // Coupon release source: 01-System generated 02-Imported
    @ApiModelProperty(value = "Release source.01-System generated 02-Imported")
    private String releaseSource;

    // Receive coupon start time: yyyyMMddhhmmss
    @ApiModelProperty(value = "Receive start time.yyyyMMddhhmmss")
    private String receiveStartTime;

    // Receive coupon end time: yyyyMMddhhmmss
    @ApiModelProperty(value = "Receive end time.yyyyMMddhhmmss")
    private String receiveEndTime;

    // Appointment time of coupon release: yyyyMMddhhmmss
    @ApiModelProperty(value = "Release time.yyyyMMddhhmmss")
    private String releaseTime;

    // Coupon release type: 01-immediately, 02-appointment
    @ApiModelProperty(value = "Release type.01-immediately, 02-appointment")
    private String releaseType;

}
