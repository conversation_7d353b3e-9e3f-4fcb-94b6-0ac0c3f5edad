package com.gtech.promotion.vo.param.point;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

@Data
public class UpdatePointAccountParam {

	/**
	 * Domain code.
	 */
	@ApiModelProperty(value = "Domain code", required = true)
	@NotEmpty(message = "domainCode can not be empty")
	private String domainCode;

	/**
	 * Tenant code.
	 */
	@ApiModelProperty(value = "Tenant code", required = true)
	@NotEmpty(message = "tenantCode can not be empty")
	private String tenantCode;

	/**
	 * account code. (UserCode or OrgCode)
	 */
	@ApiModelProperty(value = "account code. (UserCode or OrgCode)", required = true)
	@NotEmpty(message = "account code can not be empty")
	private String accountCode;

	/**
	 * account code. (UserCode or OrgCode)
	 */
	@ApiModelProperty(value = "campaign code.", required = true)
	@NotEmpty(message = "campaign code can not be empty")
	private String campaignCode;

	/**
	 * Point account type. (1-User 2-Organization)
	 */
	@ApiModelProperty(value = "Point account type. (1-User 2-Organization)", required = true)
	@NotNull(message = "Point account type can not be null")
	private Integer accountType;

	/**
	 * Point account description.
	 */
	@ApiModelProperty(value = "Point account description.")
	private String accountDesc;

	/**
	 * Account balance
	 */
	@ApiModelProperty(value = "Account balance.")
	private Integer accountBalance;

	/**
	 * Point account status.(0-Inactive 1-Active)
	 */
	@ApiModelProperty(value = "Point account status.(0-Inactive 1-Active)")
	private Integer status;

	/**
	 * Extends parameters. (JSON String)
	 */
	@ApiModelProperty(value = "Extends parameters. (JSON String)")
	private String extParams;

    /**
     * Parameter validation.
     */
    public void validate() {
		// to do something
    }
}