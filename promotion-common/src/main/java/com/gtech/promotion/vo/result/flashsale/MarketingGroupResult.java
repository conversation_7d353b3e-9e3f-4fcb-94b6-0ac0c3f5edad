package com.gtech.promotion.vo.result.flashsale;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @Description :
 * <AUTHOR> gaojie.li
 * @Date : 2023/6/5 15:35
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("MarketingGroupResponse")
public class MarketingGroupResult implements Serializable {

    private static final long serialVersionUID = -7657694076972099327L;



    @ApiModelProperty(value = "show leader price.", required = true)
    private Integer showLeaderPrice;
    /**
     * 时效字段
     */
    @ApiModelProperty(value = "effective hour.", required = true)
    private Integer effectiveHour;

    /***
     * 成团人数
     */
    @ApiModelProperty(value = "group size.", required = true)
    private Integer groupSize;


    /**
     * 允许自动成团
     * 默认开， 0开 1关闭
     */
    @ApiModelProperty(value = "auto group flag;0-yes 1-no;default 0", required = true)
    private Integer autoGroupFlag;


    @ApiModelProperty(value = "Promotional activities that the group leader can participate. Json")
    private String leaderBenefits;

    @ApiModelProperty(value = "是否封闭团，1是0不是，默认0")
    private Integer closeFlag;





}
