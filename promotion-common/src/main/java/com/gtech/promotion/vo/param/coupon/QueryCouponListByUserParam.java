/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * <PERSON>ECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.vo.param.coupon;

import com.gtech.commons.page.PageParam;
import com.gtech.commons.utils.CheckUtils;
import com.gtech.promotion.exception.ErrorCodes;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.io.Serializable;
import java.util.List;

/**
 * QueryCouponListByUserParam
 */
@Getter
@Setter
@ToString
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("QueryCouponListByUserRequest")
public class QueryCouponListByUserParam extends PageParam implements Serializable{

    private static final long serialVersionUID = -4572784345175239134L;

    @ApiModelProperty(value = "Domain code.",example = "D00001",required = true)
    private String domainCode;

    @ApiModelProperty(value = "Tenant code", required = true)
    private String tenantCode;

    @ApiModelProperty(value = "Language id")
    private String language;

    @ApiModelProperty(value = "User code", required = true)
    private String userCode;

    @ApiModelProperty(value = "Coupon status: 1-Useable, 2-Used, 3-Expired. Empty means query all coupons.")
    private String status;

    @ApiModelProperty(value = "是否显示冻结券码， true 是, false 否")
    private boolean flag = false;

    @ApiModelProperty(value = "Take coupon label: 01-Purchase 02-OPS 99-Others")
    private String takeLabel;

    @ApiModelProperty(value="Store organization code.")
    private String orgCode;

    @ApiModelProperty(value="Channel code.")
    private String channelCode;

    //自定义选择查优惠券的 打折券，满减券，满减码，兑换券 其中的几种券
    @ApiModelProperty(value="OPS Type List.201-product Discount,202-order discount(coupon),203- order discount(code),204- voucher")
    private List<String> opsTypeList;

    @ApiModelProperty(value="Received Begin Time.20210425103227",notes = "20210425103227")
    private String beginTime;

    @ApiModelProperty(value="Received End Time.20210425103227",notes = "20210425103227")
    private String endTime;

    @ApiModelProperty(value="Sort by received time.",notes = "1")
    private Integer sortByReceivedTime;

    public void setPageNo(int pageNo) {
        super.setPageNum(pageNo);
    }
    public void setPageCount(int pageCount) {
        super.setPageSize(pageCount);
    }

    /**
     * Parameter validation.
     */
    public void validate() {

        CheckUtils.isNotBlank(this.domainCode, ErrorCodes.PARAM_EMPTY, "domainCode");
        CheckUtils.isNotBlank(this.tenantCode, ErrorCodes.PARAM_EMPTY, "tenantCode");
        CheckUtils.isNotBlank(this.userCode, ErrorCodes.PARAM_EMPTY, "userCode");
    }

}
  
