package com.gtech.promotion.vo.result.flashpresale;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @Description :
 * <AUTHOR> gaojie.li
 * @Date : 2023/12/29 15:35
 */
@Data
public class ExportGroupDetailResult implements Serializable {

    private static final long serialVersionUID = -7972082031210083204L;

    private String date;

    //拼团发起人数
    private Integer ofInitiators;

    //参与拼团人数
    private Integer ofParticipants;

    //拼团成功数
    private Integer ofSuccessfulGroups;

    //拼团失败数
    private Integer ofFailingGroups;

    //新开团数
    private Integer ofNewGroups;

    //订单总数
    private Integer totalOfOrders;

    //订单总金额
    private BigDecimal totalAmountOfOrders;


    private String maxGroupCode;


}
