/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.vo.result.flashsale;

import com.gtech.promotion.api.ApiConstants;
import com.gtech.promotion.vo.bean.ActivityPeriod;
import com.gtech.promotion.vo.result.activity.CalcShoppingCartResult;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 购物车计算出参
 *
 */
@Getter
@Setter
@ToString
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("FlashSaleOrderCalaResponse")
public class FlashSaleOrderCalaResult implements Serializable {

    private static final long serialVersionUID = 4564161427611589661L;

    @ApiModelProperty(value = "Activity code.")
    private String activityCode;

    @ApiModelProperty(value = "Activity name.")
    private String activityName;

    @ApiModelProperty(value = "Activity end.")
    private String activityEnd;

    @ApiModelProperty(value = "Activity begin.")
    private String activityBegin;


    @ApiModelProperty(value = "Activity label.")
    private String activityLabel;

    @ApiModelProperty(value = "Languages activity.")
    List<MarketingLanguageResult> languageResults;

    @ApiModelProperty(value = "Activity type：04-flash sale")
    private String activityType;

    @ApiModelProperty(value = "Activity URL.")
    private String activityUrl;

    @ApiModelProperty(value = "Promotion match success flag.")
    private Boolean effectiveFlag;

    //活动减免总金额
    @ApiModelProperty(value = "Total amount of activity reduction")
    private BigDecimal promoRewardAmount;

    //单人限购次数
    @ApiModelProperty(value = "Single person limited number of purchases")
    private String userLimitation;

    //商品列表
    @ApiModelProperty(value = "List of items")
    private List<ShoppingCartItem> shoppingCartItems;

    @ApiModelProperty(value = "Activity period.")
    private ActivityPeriod activityPeriod;

    @ApiModelProperty(value = "pre sale pay type")
    private String preSalePayType;

    @ApiModelProperty(value = "shipping time")
    private String shippingTime;

    @ApiModelProperty(value = "import no")
    private String importNo;

    @ApiModelProperty(value = "Group user")
    MarketingGroupUserResult groupUser;

    @Getter
    @Setter
    @ToString
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @ApiModel("FlashSaleOrderCalaResult#ShoppingCartItem")
    public static class ShoppingCartItem implements Serializable {

        private static final long serialVersionUID = -6064917222102183332L;

        @ApiModelProperty(value = "Product sku code.")
        private String skuCode;

        @ApiModelProperty(value = "Product code.")
        private String productCode;

        //套装商品sku编码
        @ApiModelProperty(value = "Combine sku code")
        private String combineSkuCode;

//        商品原单价
        @ApiModelProperty(value = "Prodcut price")
        private BigDecimal productPrice;

        //购买数量（该sku的总数量）
        @ApiModelProperty(value = "Quantity purchased (total number of skUs)")
        private Integer quantity;

        //参与促销数量（参与当前活动的数量）
        @ApiModelProperty(value = "Number of promotions participated (number of current campaigns participated)")
        private Integer promoQuantity;

        //促销后总价（参与所有活动后价格）
        @ApiModelProperty(value = "Total price after promotion (price after participating in all activities)")
        private BigDecimal promoAmount;

        //当前商品参与的促销活动列表
        @ApiModelProperty(value = "List of promotions that the current item participates in")
        private List<ShoppingCartItemActivity> shoppingCartItemActivitys;


        //当前活动参与的促销活动列表
        @ApiModelProperty(value = "List of promotions that the current item participates in")
        private List<CalcShoppingCartResult.ShoppingCartItemActivity> promoShoppingCartItemActivitys;

        @ApiModelProperty(value = "sku inventory")
        private Integer skuInventory;

        @ApiModelProperty(value = "max per user")
        private Integer maxPerUser;

        @ApiModelProperty(value = "member inventory")
        private Integer memberInventory;

        @ApiModelProperty(value = "Flash price")
        private BigDecimal flashPrice;
    }

    @Getter
    @Setter
    @ToString
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @ApiModel("FlashSaleOrderCalaResult#ShoppingCartItemActivity")
    public static class ShoppingCartItemActivity implements Serializable {

        private static final long serialVersionUID = -348441808556220208L;

        @ApiModelProperty(value = "Activity code")
        private String activityCode;

        //是否满足活动
        @ApiModelProperty(value = "Whether the activity is effective")
        private Boolean effectiveFlag;

        @ApiModelProperty(value = "Activity name")
        private String activityName;

        @ApiModelProperty(value = "Activity label")
        private String activityLabel;

        @ApiModelProperty(value = ApiConstants.ACTIVITY_TYPE)
        private String activityType;

        //参加活动计算前的商品项总价
        @ApiModelProperty(value = "The total price of the commodity item before the calculation of the activity")
        private BigDecimal beforeAmount;//

        //参加活动计算后的商品项总价
        @ApiModelProperty(value = "The total price of the commodity item after the calculation of the activity")
        private BigDecimal afterAmount;//

    }
}
