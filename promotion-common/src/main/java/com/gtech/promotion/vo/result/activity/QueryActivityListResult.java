/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.vo.result.activity;

import com.gtech.promotion.vo.bean.ActivityStore;
import com.gtech.promotion.vo.bean.ExtImage;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Getter
@Setter
@ToString
@ApiModel("QueryActivityListResponse")
public class QueryActivityListResult implements Serializable {

    private static final long serialVersionUID = 770738332844799423L;

    private String orgCode;
    @ApiModelProperty(value = "Activity code")
    private String activityCode;

    @ApiModelProperty(value = "Activity name")
    private String activityName;

    @ApiModelProperty(value = "External activity id")
    private String externalActivityId;

    @ApiModelProperty(value = "Group name")
    private String groupName;

    @ApiModelProperty(value = "Promotion category")
    private String promotionCategory;

    @ApiModelProperty(value = "Activity description")
    private String activityDesc;

    @ApiModelProperty(value = "Activity remark")
    private String activityRemark;

    @ApiModelProperty(value = "Activity label")
    private String activityLabel;

    @ApiModelProperty(value = "Activity type：01-Activity 02-Coupon")
    private String activityType;

    @ApiModelProperty(value = "Activity sponsors")
    private String sponsors;

//    @ApiModelProperty(value = "Activity tag codes. Separated by commas. 01-单品、02-满减、03-满折、04-特价、05-包邮、06-赠品、07-捆绑、08-满送、10-买A优惠B")

    @ApiModelProperty(value = "Activity tag codes. Separated by commas. 01--Simple promotion、02-Full reduction、03-Full discount、04-special price、05-free shipping、6-giveaway、7-package、8-limited free、10-Buy A get B discount 11-prodict discount 20-promotion code")
    private String tagCode;

    @ApiModelProperty(value = "Product selection type: 01-Selection, 02-Invert Selection")
    private String productSelectionType;

    @ApiModelProperty(value = "Product item scope type: 1-All scope 2-By spu in scope")
    private String itemScopeType;

    @ApiModelProperty(value = "Activity channel & store list.")
    private List<ActivityStore> stores;

    @ApiModelProperty(value = "Activity start time. (yyyyMMddHHmmss)")
    private String activityBegin;

    @ApiModelProperty(value = "Activity end time. (yyyyMMddHHmmss)")
    private String activityEnd;

    @ApiModelProperty(value = "Activity warm start time. (yyyyMMddHHmmss)")
    private String warmBegin;

    @ApiModelProperty(value = "Activity warm end time. (yyyyMMddHHmmss)")
    private String warmEnd;

    @ApiModelProperty(value = "Activity status")
    private String activityStatus;

    @ApiModelProperty(value = "Template code")
    private String templateCode;

    @ApiModelProperty(value = "Activity create user code")
    private String createUser;

    @ApiModelProperty(value = "Activity create time")
    private Date createTime;
    @ApiModelProperty(value = "Activity create user first name")
    private String createUserFirstName;
    @ApiModelProperty(value = "Activity create user last name")
    private String createUserLastName;

    @ApiModelProperty(value = "ops create Activity type")
    private String opsType;

    @ApiModelProperty(value = "Activity priority.(1-100, default is 999)")
    private Integer priority;

    @ApiModelProperty(value = "Need Audit. 0:no 1:yes")
    private String needAudit;

    @ApiModelProperty(value = "Need Different Operator.  0:no 1:yes")
    private String needDifferentOperator;

    @ApiModelProperty(value = "audit user")
    private String auditUser;

    @ApiModelProperty(value = "commit user")
    private String commitUser;

    @ApiModelProperty(value = "Ext images")
    private List<ExtImage> extImages;
}
