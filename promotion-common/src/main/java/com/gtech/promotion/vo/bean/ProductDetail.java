/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * <PERSON>ECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.vo.bean;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import org.apache.commons.collections4.CollectionUtils;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Iterator;
import java.util.List;

/**
 * 促销商品明细
 */
@Getter
@Setter
@ToString
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("ProductDetail")
public class ProductDetail implements Serializable {

    // serialVersionUID
    private static final long serialVersionUID = -334582634295972064L;

    @ApiModelProperty(value = "Product seqence number.",example = "1",required = true)
    private Integer seqNum;

    @ApiModelProperty(value = "Product(Spu) code.")
    private String productCode;

    @ApiModelProperty(value = "Product(Spu) name.")
    private String spuName;

    @ApiModelProperty(value = "Whitelist, 1-white; 2 - black")
    private Integer type;

    @ApiModelProperty(value = "Sku code")
    private String skuCode;

    @ApiModelProperty(value = "Sku name.")
    private String skuName;

    @ApiModelProperty(value = "Promotion price.")
    private String promoPrice;

    @ApiModelProperty(value = "Org code")
    private String orgCode;

    @ApiModelProperty(value = "Org name")
    private String orgName;

    @ApiModelProperty(value = "Product number")
    private String productNumber;

    @ApiModelProperty(value = "Product url")
    private String productUrl;

    @ApiModelProperty(value = "Sku number")
    private String skuNumber;

    @ApiModelProperty(value = "Sku url")
    private String skuUrl;

    @ApiModelProperty(value = "Price type. 1 -special price ; 2 -discount ; default 1")
    private String priceType = "1";

    @ApiModelProperty(value = "Sku url")
    private String priceDiscount;

    public static void removeBySeq(List<ProductDetail> list, int seqNum) {
        
        if (CollectionUtils.isEmpty(list)) {
            return;
        }

        Iterator<ProductDetail> iterator = list.iterator();
        while(iterator.hasNext()) {
            if (iterator.next().getSeqNum() == seqNum) {
                iterator.remove();
            }
        }
    }

    public static List<ProductDetail> getProductScopeBySeq(List<ProductDetail> list, int seqNum) {
        
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        }

        List<ProductDetail> filterList = new ArrayList<>();
        Iterator<ProductDetail> iterator = list.iterator();
        while(iterator.hasNext()) {
            ProductDetail ps = iterator.next();
            if (ps.getSeqNum() == seqNum) {
                filterList.add(ps);
            }
        }

        return filterList;
    }
}
