package com.gtech.promotion.vo.param.purchaseconstraint.query;

import com.gtech.commons.utils.StringUtil;
import com.gtech.promotion.checker.purchaseconstraint.PurchaseConstraintChecker;
import com.gtech.promotion.exception.Check;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;

@Data
@ToString
@ApiModel("FindPurchaseConstraintRequest")
public class FindPurchaseConstraintParam implements Serializable {

    @ApiModelProperty(value = "Tenant code", required = true)
    @NotEmpty(message = "tenantCode can not be empty")
    private String tenantCode;

    private String orgCode;

    @ApiModelProperty(value = "Domain code.",example = "D00001",required = true)
    private String domainCode;

    @ApiModelProperty(value = "Activity code",required = true)
    private String purchaseConstraintCode;


    public void validate(){
        Check.check(StringUtil.isBlank(tenantCode), PurchaseConstraintChecker.NOT_NULL_TENANT_CODE);
        Check.check(StringUtil.isBlank(purchaseConstraintCode), PurchaseConstraintChecker.NOT_NULL_PURCHASE_CONSTRAINT_CODE);
    }
}
