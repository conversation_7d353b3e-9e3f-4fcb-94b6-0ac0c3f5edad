package com.gtech.promotion.vo.param.activity;

import java.io.Serializable;

import com.gtech.commons.utils.CheckUtils;
import com.gtech.promotion.exception.ErrorCodes;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Description :
 * <AUTHOR> gaojie.li
 * @Date : 2021/9/17 10:25
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("GetCouponActivityRequest")
public class GetCouponActivityParam implements Serializable {

    @ApiModelProperty(value = "Domain code",required = true)
    private String domainCode;

    @ApiModelProperty(value = "Tenant code",required = true)
    private String tenantCode;

    @ApiModelProperty(value = "Language id.")
    private String language;

    @ApiModelProperty(value = "Coupon code.",required = true)
    private String couponCode;

    @ApiModelProperty(value = "Organization code.")
    private String orgCode;

	@ApiModelProperty(value = "defaultFlag default = true")
	private Boolean defaultFlag = true;

    public void validate() {

        CheckUtils.isNotBlank(this.domainCode, ErrorCodes.PARAM_EMPTY, "domainCode");
        CheckUtils.isNotBlank(this.tenantCode, ErrorCodes.PARAM_EMPTY, "tenantCode");
        CheckUtils.isNotBlank(this.couponCode, ErrorCodes.PARAM_EMPTY, "couponCode");

    }

}
