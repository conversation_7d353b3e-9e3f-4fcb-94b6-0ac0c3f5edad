package com.gtech.promotion.vo.param.marketing;

import com.gtech.commons.page.PageParam;
import com.gtech.commons.utils.CheckUtils;
import com.gtech.promotion.exception.ErrorCodes;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("QuerySharingInformationRequest")
public class QuerySharingInformationParam extends PageParam implements Serializable {
    private static final long serialVersionUID = 6850383347651539206L;


    @ApiModelProperty(value = "domainCode", required = true)
    private String domainCode;
    @ApiModelProperty(value = "tenantCode", required = true)
    private String tenantCode;
    @ApiModelProperty(value = "orgCode", required = true)
    private String orgCode;
    @ApiModelProperty(value = "activityCode")
    private String activityCode;
    @ApiModelProperty(value = "sharingMemberCode", required = true)
    private String sharingMemberCode;
    @ApiModelProperty(value = "sharingRecordCode")
    private String sharingRecordCode;
    @ApiModelProperty(value = "activityStatus")
    private String activityStatus;

    public void validate() {
        CheckUtils.isNotBlank(this.domainCode, ErrorCodes.PARAM_EMPTY, "domainCode");
        CheckUtils.isNotBlank(this.tenantCode, ErrorCodes.PARAM_EMPTY, "tenantCode");
        CheckUtils.isNotBlank(this.orgCode, ErrorCodes.PARAM_EMPTY, "orgCode");
        CheckUtils.isNotBlank(this.sharingMemberCode, ErrorCodes.PARAM_EMPTY, "sharingMemberCode");

    }

}
