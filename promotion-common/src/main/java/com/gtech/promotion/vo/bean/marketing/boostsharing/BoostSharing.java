package com.gtech.promotion.vo.bean.marketing.boostsharing;

import com.gtech.commons.utils.CheckUtils;
import com.gtech.promotion.code.marketing.BoostSharingTypeEnum;
import com.gtech.promotion.exception.ErrorCodes;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("BoostSharingRequest")
public class BoostSharing implements Serializable {
    private static final long serialVersionUID = 7642308573959924716L;

    @ApiModelProperty(value = "boost sharing type")
    private String boostSharingType;

    @ApiModelProperty(value = "attract new customers. 0-no 1-yes default 0")
    private String attractNewCustomers;

    @ApiModelProperty(value = "number of boost sharing")
    private String numberOfBoostSharing;

    /**-----------------分享人奖励----------------------*/
    /**-----------------分享优惠券----------------------*/
    @ApiModelProperty(value = "share to get coupon activity code")
    private String shareToGetCouponActivityCode;
    @ApiModelProperty(value = "share to get coupon activity name")
    private String shareToGetCouponActivityName;
    /**-----------------分享优惠券----------------------*/
    /**-----------------分享商品-----------------*/
    @ApiModelProperty(value = "share to get right of first refusal product code")
    private String rightOfFirstRefusalProductCode;
    @ApiModelProperty(value = "share to get right of first refusal product no")
    private String rightOfFirstRefusalProductNo;
    @ApiModelProperty(value = "share to get right of first refusal product name")
    private String rightOfFirstRefusalProductName;
    @ApiModelProperty(value = "right of first refusal start time")
    private String rightOfFirstRefusalStartTime;
    @ApiModelProperty(value = "right of first refusal end time")
    private String rightOfFirstRefusalEndTime;
    @ApiModelProperty(value = "lucky draw activity code")
    private String luckyDrawActivityCode;
    @ApiModelProperty(value = "lucky draw activity name")
    private String luckyDrawActivityName;
    /**-----------------分享商品-----------------*/
    /**-----------------分享人奖励----------------------*/


    /**-----------------助力人奖励----------------------*/
    @ApiModelProperty(value = "help to get coupon activity code")
    private String helpToGetCouponActivityCode;

    @ApiModelProperty(value = "help to get coupon activity name")
    private String helpToGetCouponActivityName;

    /**-----------------助力人奖励----------------------*/


    @ApiModelProperty(value = "Redirect link")
    private String redirectLink;
    @ApiModelProperty(value = "Image for the event page")
    private String eventPageImage;
    @ApiModelProperty(value = "Back ground image for the event page")
    private String eventBackGroundPageImage;
    @ApiModelProperty(value = "Event page link")
    private String eventPageLink;
    @ApiModelProperty(value = "Image for the mini-program")
    private String miniProgramImage;
    @ApiModelProperty(value = "Mini-program share copy")
    private String miniProgramShareCopy;
    @ApiModelProperty(value = "Image for the poster")
    private String posterImage;
    @ApiModelProperty(value = "Image for the popup")
    private String popupImage;
    @ApiModelProperty(value = "Popup share copy")
    private String popupShareCopy;



    public void validate(){
        //检查boostSharingType attractNewCustomers numberOfBoostSharing 为必填项
        CheckUtils.isNotBlank(boostSharingType , ErrorCodes.PARAM_EMPTY, "boostSharingType");
        CheckUtils.isNotBlank(attractNewCustomers , ErrorCodes.PARAM_EMPTY, "attractNewCustomers");
        CheckUtils.isNotBlank(numberOfBoostSharing , ErrorCodes.PARAM_EMPTY, "numberOfBoostSharing");
        CheckUtils.isNotBlank(redirectLink , ErrorCodes.PARAM_EMPTY, "redirectLink");
        if(boostSharingType.equals(BoostSharingTypeEnum.RIGHT_FIRST.code())){
            CheckUtils.isNotBlank(rightOfFirstRefusalStartTime , ErrorCodes.PARAM_EMPTY, "rightOfFirstRefusalStartTime");
            CheckUtils.isNotBlank(rightOfFirstRefusalEndTime , ErrorCodes.PARAM_EMPTY, "rightOfFirstRefusalEndTime");
        }
    }


}
