package com.gtech.promotion.vo.param.growth;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

import org.hibernate.validator.constraints.Range;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class UpdateGrowthAccountParam {

	/**
	 * Domain code.
	 */
	@ApiModelProperty(value = "Domain code", required = true)
	@NotEmpty(message = "domainCode can not be empty")
	private String domainCode;

	/**
	 * Tenant code.
	 */
	@ApiModelProperty(value = "Tenant code", required = true)
	@NotEmpty(message = "tenantCode can not be empty")
	private String tenantCode;

	/**
	 * account code. (UserCode or OrgCode)
	 */
	@ApiModelProperty(value = "account code. (UserCode or OrgCode)", required = true)
	@NotEmpty(message = "accountCode can not be empty")
	private String accountCode;

	/**
	 * Growth account type. (1-User 2-Organization)
	 */
	@ApiModelProperty(value = "Growth account type. (1-User 2-Organization)", required = true)
	@Range(min = 1, max = 2, message = "accountType is error")
	@NotNull(message = "accountType can not be null")
	private Integer accountType;

	/**
	 * Growth account description.
	 */
	@ApiModelProperty(value = "Growth account description.")
	private String accountDesc;

	/**
	 * Latest account points.
	 */
	@ApiModelProperty(value = "Latest account points.", required = true)
	@NotNull(message = "accountBalance can not be null")
	private Integer accountBalance;

	/**
	 * Growth account status.(0-Inactive 1-Active)
	 */
	@ApiModelProperty(value = "Growth account status.(0-Inactive 1-Active)")
	private Integer status;

	/**
	 * Extends parameters. (JSON String)
	 */
	@ApiModelProperty(value = "Extends parameters. (JSON String)")
	private String extParams;

    /**
     * Parameter validation.
     */
    public void validate() {
		// to do something
    }
}