package com.gtech.promotion.vo.param.activity;

import com.gtech.commons.utils.CheckUtils;
import com.gtech.promotion.exception.ErrorCodes;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class QueryOperationLogsByActivityCodeParam implements Serializable {

    private static final long serialVersionUID = -8138955119593074171L;
    @ApiModelProperty(value = "Tenant code",required = true)
    private String tenantCode;

    @ApiModelProperty(value = "Activity code",required = true)
    private String activityCode;

    public void validate() {
        CheckUtils.isNotBlank(this.tenantCode, ErrorCodes.PARAM_EMPTY, "tenantCode");
        CheckUtils.isNotBlank(this.activityCode, ErrorCodes.PARAM_EMPTY, "activityCode");
    }
}
