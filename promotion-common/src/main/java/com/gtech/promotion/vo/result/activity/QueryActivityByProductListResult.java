package com.gtech.promotion.vo.result.activity;

import com.gtech.promotion.api.ApiConstants;
import com.gtech.promotion.vo.bean.FunctionParam;
import com.gtech.promotion.vo.bean.Giveaway;
import com.gtech.promotion.vo.bean.IncentiveLimited;
import com.gtech.promotion.vo.bean.Qualification;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.io.Serializable;
import java.util.List;

/**
 * @Description :
 * <AUTHOR> 李高杰
 * @Date : 2021/5/25 14:35
 */
@Getter
@Setter
@ToString
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("QueryActivityByProductListResponse")
public class QueryActivityByProductListResult implements Serializable {

    private static final long serialVersionUID = 464282405725113782L;

    //当前促销活动参与的商品列表
    @ApiModelProperty(value = "List of items participating in the current promotion")
    private List<ActivityItemResult> activityItems;

    //活动赞助商
    @ApiModelProperty(value = "Sponsors")
    private String sponsors;

    private String activityCode;

    @ApiModelProperty(value = "Activity name")
    private String activityName;

    @ApiModelProperty(value = "Custom condition")
    private String customCondition;

    @ApiModelProperty(value = "Activity label")
    private String activityLabel;

    //OPS活动类型，对应ops中的创建活动，1对1，促销101开始，券201开始
    @ApiModelProperty(value = "OPS activity type, corresponding to the OPS creation activity, 1 to 1, promotion 101 starts, coupon 201 starts")
    private String opsType;

    @ApiModelProperty(value = "Product item scope type: 1-All scope 2-By spu in scope")
    private Integer itemScopeType;

    @ApiModelProperty(value = ApiConstants.ACTIVITY_TYPE)
    private String activityType;

    @ApiModelProperty(value = "Parameters list of the activity template function.")
    private List<FunctionParam> funcParams;

    //赠品赠送最大限制数量
    @ApiModelProperty(value = "Maximum number of gifts")
    private String giftLimitMax;//

    @ApiModelProperty(value = "Giveaways")
    private List<Giveaway> giveaways;

    @ApiModelProperty(value = "Limited condition")
    private List<IncentiveLimited> limitedList;

    @ApiModelProperty(value = "Qualification list. Empty means not limited.")
    private List<Qualification> qualifications;

}




