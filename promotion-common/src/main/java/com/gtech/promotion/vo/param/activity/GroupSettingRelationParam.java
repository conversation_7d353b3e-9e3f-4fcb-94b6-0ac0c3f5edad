package com.gtech.promotion.vo.param.activity;

import com.gtech.commons.utils.CheckUtils;
import com.gtech.promotion.exception.ErrorCodes;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;

import java.io.Serializable;
import java.util.List;

/**
 * @Description :
 * <AUTHOR> gaojie.li
 * @Date : 2023/6/14 15:57
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("GroupSettingRelationRequest")
public class GroupSettingRelationParam implements Serializable {

    private static final long serialVersionUID = -8243330757181516562L;

    @ApiModelProperty(value = "Tenant code", example = "880001", required = true)
    private String tenantCode;

    @ApiModelProperty(value = "Domain code", example = "880001", required = true)
    private String domainCode;

    @ApiModelProperty(value = "互斥叠加关系列表",  required = true)
    private List<GroupRelationParam> relations;


    public void validate(){

        CheckUtils.isNotBlank(this.tenantCode, ErrorCodes.PARAM_EMPTY, "tenantCode");

        CheckUtils.isNotBlank(this.domainCode, ErrorCodes.PARAM_EMPTY, "domainCode");

        CheckUtils.isTrue(CollectionUtils.isNotEmpty(relations),ErrorCodes.PARAM_EMPTY, "relations");

        for (GroupRelationParam  relation : relations) {

            CheckUtils.isNotBlank(relation.getGroupCodeA(), ErrorCodes.PARAM_EMPTY, relation.getGroupCodeA());
            CheckUtils.isNotBlank(relation.getGroupCodeB(), ErrorCodes.PARAM_EMPTY, relation.getGroupCodeB());

        }
    }
}
