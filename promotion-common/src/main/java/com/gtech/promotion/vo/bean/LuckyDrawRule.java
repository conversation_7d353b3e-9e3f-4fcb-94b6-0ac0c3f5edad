package com.gtech.promotion.vo.bean;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Date 2021/1/25 18:24
 */
@Getter
@Setter
@ToString
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("LuckyDrawRules")
public class LuckyDrawRule implements Serializable {
    private static final long serialVersionUID = -1872274404633461913L;


    @ApiModelProperty(value = "Restriction type 01- By quantity of goods 02- by amount of goods.")
    private String admissionRules;

    @ApiModelProperty(value = "SPU.")
    private String productCode;

    @ApiModelProperty(value = "SUP NAME.")
    private String productName;

    @ApiModelProperty(value = "Price or quantity.")
    private BigDecimal buyingQty;

    @ApiModelProperty(value = "The ticket number.")
    private Integer earnTicket;

    @ApiModelProperty(value = "Maximum quantity available to users.")
    private Integer maxGivingPerUser;


}
