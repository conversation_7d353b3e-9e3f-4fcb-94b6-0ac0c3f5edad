package com.gtech.promotion.vo.param.activity;

import com.gtech.promotion.code.PromotionConstants;
import com.gtech.promotion.vo.bean.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * @Description :
 * <AUTHOR> gaojie.li
 * @Date : 2021/7/20 14:46
 */
@NoArgsConstructor
@AllArgsConstructor
@Data
@ApiModel("WinTogetherRequest")
public class WinTogetherParam implements Serializable {

    @ApiModelProperty( value="Domain code.", example="DC0001",required=true)
    private String domainCode;

    @ApiModelProperty( value="Tenant code.", example="TC0001",required=true)
    private String tenantCode;

    @ApiModelProperty( value="orgCode.", example="default",required=true)
    private String orgCode;

    @ApiModelProperty(value = "Activity Name",required = true)
    private String activityName;

    @ApiModelProperty(value = "Activity type：01-Activity 02-Coupon",required = true)
    private String activityType;

    @ApiModelProperty("Activity sponsors")
    private String sponsors;

    @ApiModelProperty(value = "Show flag: 1-show, 2-hidden")
    private Integer showFlag;

    @ApiModelProperty(value = "Activity warm-up begin time.")
    private String warmBegin;

    @ApiModelProperty(value = "Activity warm-up end time. is warmBegin not blank, warmEnd same as activityBegin")
    private String warmEnd;

    @ApiModelProperty(value = "Activity begin time.",required = true)
    private String activityBegin;

    @ApiModelProperty(value = "Activity end time.",required = true)
    private String activityEnd;

    @ApiModelProperty("product in activity sort method: 01-price asc 02-price desc")
    private String productSort;

    @ApiModelProperty(value = "Activity url.")
    private String activityUrl;

    @ApiModelProperty(value = "Custom conditions.")
    private List<CustomCondition> customConditions;

    @ApiModelProperty(value = "Activity priority.(1-100, default is 999)")
    private Integer priority = PromotionConstants.DEF_PRIORITY;
    public void setPriority(Integer priority) {
        this.priority = (null == priority ? PromotionConstants.DEF_PRIORITY: priority);
    }

    @ApiModelProperty(value = "Incentive limited mark: 00-unlimited 01-limited")
    private String incentiveLimitedFlag;

    @ApiModelProperty(value = "Incentive limited list. It can't be empty while incentiveLimitedFlag is '01'.")
    private List<IncentiveLimited> incentiveLimiteds;

    @ApiModelProperty("Deprecated. Member level list. Empty means not limited.")
    @Deprecated
    private List<MemberLevel> memberLevels; //NOSONAR

    @ApiModelProperty("Deprecated. Member label list. Empty means not limited.")
    @Deprecated
    private List<MemberLabel> memberLabels; //NOSONAR

    @ApiModelProperty(value = "Qualification list. Empty means not limited.")
    private List<Qualification> qualifications;

    @ApiModelProperty(value = "Store limited type: 00-Unlimited, 01-Specified stores.",required = true)
    private String storeType;

    @ApiModelProperty(value = "Specified store list while storeType equals 01.")
    private List<ActivityStore> channelStores;

    @ApiModelProperty(value = "Multilingual attributes.")
    private List<ActivityLanguage> activityLanguages;

    @ApiModelProperty(value = "Activity period.")
    private ActivityPeriod activityPeriod;

    @ApiModelProperty(value = "User code of the operator.", example="U00001")
    private String operateUser;

    @ApiModelProperty(value = "User last name of the operator.")
    private String operateLastName;

    @ApiModelProperty(value = "User first name of the operator.")
    private String operateFirstName;

    @ApiModelProperty(value = "Activity cool down time.")
    private String coolDown;

    @ApiModelProperty(value = "Activity background image.")
    private String backgroundImage;

    @ApiModelProperty(value = "Ribbon image.")
    private String ribbonImage;

    @ApiModelProperty(value = "Position of Ribbon image .")
    private String ribbonPosition;

    @ApiModelProperty(value = "Ribbon text.")
    private String ribbonText;

    @ApiModelProperty(value = "Different parameters for each activity.",required = true)
    private List<WinTogetherDifferentParam> activityParams;

}
