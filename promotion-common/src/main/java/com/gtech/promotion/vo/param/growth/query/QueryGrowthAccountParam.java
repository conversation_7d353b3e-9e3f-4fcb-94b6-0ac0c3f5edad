package com.gtech.promotion.vo.param.growth.query;

import com.gtech.commons.page.PageParam;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;

@Getter
@Setter
@ToString
public class QueryGrowthAccountParam extends PageParam implements Serializable {

    private static final long serialVersionUID = 615960045268030335L;

    @ApiModelProperty(value = "Domain code")
	private String domainCode;

	@ApiModelProperty(value = "Tenant code", required = true)
	@NotEmpty(message = "tenantCode can not be empty")
	private String tenantCode;

	@ApiModelProperty(value = "Growth account code.")
	private String growthAccountCode;

	/**
	 * account code. (UserCode or OrgCode)
	 */
	@ApiModelProperty(value = "account code. (UserCode or OrgCode)")
	private String accountCode;

	/**
	 * Growth account type. (1-User 2-Organization)
	 */
	@ApiModelProperty(value = "Growth account type. (1-User 2-Organization)")
	@Range(min = 1, max = 2, message = "accountType is error")
	private Integer accountType;

	/**
	 * Growth account description.
	 */
	@ApiModelProperty(value = "Growth account description.")
	private String accountDesc;

	/**
	 * Latest account points.
	 */
	@ApiModelProperty(value = "Latest account points.")
	private Integer accountBalance;

	/**
	 * Growth account status.(0-Inactive 1-Active)
	 */
	@ApiModelProperty(value = "Growth account status.(0-Inactive 1-Active)")
	private Integer status;

	/**
	 * Extends parameters. (JSON String)
	 */
	@ApiModelProperty(value = "Extends parameters. (JSON String)")
	private String extParams;

	@ApiModelProperty(value = "createBeginTime.")
	private String createBeginTime;

	@ApiModelProperty(value = "createEndTime.")
	private String createEndTime;

	@ApiModelProperty(value = "updateBeginTime.")
	private String updateBeginTime;

	@ApiModelProperty(value = "updateEndTime.")
	private String updateEndTime;

    /**
     * Parameter validation.
     */
    public void validate() {
		// to do something
    }
}