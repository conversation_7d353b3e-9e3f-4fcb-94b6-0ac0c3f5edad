/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.vo.param.coupon;

import com.gtech.commons.utils.CheckUtils;
import com.gtech.commons.utils.DateUtil;
import com.gtech.commons.utils.StringUtil;
import com.gtech.promotion.api.ApiConstants;
import com.gtech.promotion.code.coupon.CouponRuleTypeEnum;
import com.gtech.promotion.code.coupon.ReleaseTimeSameActivityEnum;
import com.gtech.promotion.code.coupon.ReleaseTypeEnum;
import com.gtech.promotion.exception.ErrorCodes;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.util.List;

/**
 * ReleaseCouponParam
 *
 * <AUTHOR>
 * @Date 2020-02-17
 */
@Getter
@Setter
@ToString
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("CreateCouponReleaseRequest")
public class CreateCouponReleaseParam implements Serializable {


    private static final long serialVersionUID = 5651680028586806619L;

    @ApiModelProperty(value = "Domain code.",example = "D00001",required = true)
    private String domainCode;

    @ApiModelProperty(value = "Tenant code",required = true)
    private String tenantCode;

    @ApiModelProperty(value = "Activity code.",required = true)
    private String activityCode;

    @ApiModelProperty(value = "Receive coupon start time: yyyyMMddhhmmss",required = true)
    private String receiveStart;

    @ApiModelProperty(value = "Receive coupon end time: yyyyMMddhhmmss",required = true)
    private String receiveEnd;

    @ApiModelProperty(value = "Validity days from received date.",required = false)
    private Integer validDays;

    @ApiModelProperty(value = "Validity date begin: yyyyMMddhhmmss",required = false)
    private String validStart;

    @ApiModelProperty(value = "Validity date end: yyyyMMddhhmmss",required = false)
    private String validEnd;

    @ApiModelProperty(value = "Appointment time of coupon release: yyyyMMddhhmmss, required while releaseType=02",required = false)
    private String releaseTime;

    @ApiModelProperty(value = ApiConstants.RELEASE_TYPE,required = true)
    private String releaseType;

    @ApiModelProperty(value = "Release coupon quantity: releaseQuantity fixed 1 while couponType=03",required = true)
    private Integer releaseQuantity;

    @ApiModelProperty(name = "couponCodePrefix",value = "The prefix for auto coupon codes generation")
    private String couponCodePrefix;

    @ApiModelProperty(name = "couponRuleType， default 01",value = "券码生成规则 01 数字 02 数字和字母")
    private String couponRuleType;


    @ApiModelProperty(name = "couponCodeLength", value = "The coupon code length, default 18 length, not contain couponCodePrefix")
	private Integer couponCodeLength;

    @ApiModelProperty(name = "couponCodes",value = "The import coupon codes")
    private List<String> couponCodes;

    // 时间同步活动时间 0-不同步 1-同步
    @ApiModelProperty(name = "timeSameActivity",value = "The time same as activity: 1- same 0- no")
    private String timeSameActivity;

    // 领取时间同步活动时间 0-不同步 1-同步
    @ApiModelProperty(name = "receiveTimeSameActivity",value = "The time same as activity: 1- same 0- no")
    private String receiveTimeSameActivity;

    @ApiModelProperty(name = "promotionType",value = "The promotion type")
    private String promotionType;

    @ApiModelProperty(name = "remark",value = "The remark")
    private String remark;


    /**
     * Parameter validation.
     */
    public void validate() {

        CheckUtils.isNotBlank(this.domainCode, ErrorCodes.PARAM_EMPTY, "domainCode");
        CheckUtils.isNotBlank(this.tenantCode, ErrorCodes.PARAM_EMPTY, "tenantCode");
        CheckUtils.isNotBlank(this.activityCode, ErrorCodes.PARAM_EMPTY, "activityCode");
        checkReceiveTime();

        if (StringUtil.isNotEmpty(couponRuleType)) {
            CheckUtils.isTrue(CouponRuleTypeEnum.exist(couponRuleType), ErrorCodes.NOT_EXIST, couponRuleType);

        }

        if (ReleaseTimeSameActivityEnum.SAME_NOT.getCode().equals(timeSameActivity)){
            if (StringUtils.isNotBlank(this.validStart) && StringUtils.isNotBlank(this.validEnd)) {
                long begin = DateUtil.parseDate(this.validStart, DateUtil.FORMAT_YYYYMMDDHHMISS_14).getTime();
                long end = DateUtil.parseDate(this.validEnd, DateUtil.FORMAT_YYYYMMDDHHMISS_14).getTime();
                CheckUtils.isTrue(begin < end, ErrorCodes.PARAM_SPECIFICATION_ERROR, "Date validStart must precede date validEnd");
            } else {
                CheckUtils.isNotNull(validDays, ErrorCodes.PARAM_SPECIFICATION_ERROR, "Date validStart & validEnd or validDays must be specified one");
            }
        }
        CheckUtils.isTrue(this.releaseQuantity != null && this.releaseQuantity >= 1, ErrorCodes.PARAM_SPECIFICATION_ERROR, "releaseQuantity must be greater than 0");
        if (ReleaseTypeEnum.APPOINTMENT.equalsCode(releaseType)) {
            CheckUtils.isNotBlank(this.releaseTime, ErrorCodes.PARAM_EMPTY, "releaseTime");
        }

        if (StringUtil.isNotBlank(couponCodePrefix)){
            CheckUtils.isTrue(couponCodePrefix.matches("[0-9a-zA-Z]{1,8}"), ErrorCodes.PARAM_SPECIFICATION_ERROR, "couponCodePrefix must be upper and lower case letters or numbers, up to 8 digits");
        }

        checkCouponCode();

    }

    public void checkCouponCode() {
        if (couponCodeLength != null) {
            CheckUtils.isTrue(couponCodeLength >= 6 && couponCodeLength <= 20, ErrorCodes.PARAM_SPECIFICATION_ERROR, "couponCodeLength min 6, max 20");
        }

        if (!"03".equals(releaseType) && releaseQuantity != null) {
            couponCodeLength = this.couponCodeLength == null ? 18 : this.couponCodeLength;
            CheckUtils.isTrue(releaseQuantity.toString().length() <= couponCodeLength, ErrorCodes.PARAM_SPECIFICATION_ERROR,
                    "Release quantity is greater than the coupo code length.");
        }
    }

    public void checkReceiveTime() {
        if (ReleaseTimeSameActivityEnum.SAME_NOT.getCode().equals(receiveTimeSameActivity)) {
            CheckUtils.isNotBlank(this.receiveStart, ErrorCodes.PARAM_EMPTY, "receiveStart");
            CheckUtils.isNotBlank(this.receiveEnd, ErrorCodes.PARAM_EMPTY, "receiveEnd");
            if (StringUtils.isNotBlank(this.receiveStart) && StringUtils.isNotBlank(this.receiveEnd)) {
                long begin = DateUtil.parseDate(this.receiveStart, DateUtil.FORMAT_YYYYMMDDHHMISS_14).getTime();
                long end = DateUtil.parseDate(this.receiveEnd, DateUtil.FORMAT_YYYYMMDDHHMISS_14).getTime();
                CheckUtils.isTrue(begin < end, ErrorCodes.PARAM_SPECIFICATION_ERROR, "Date receiveStart must precede date receiveEnd");
            }
        }
    }

}
