package com.gtech.promotion.vo.param.growth;

import com.gtech.commons.utils.CheckUtils;
import com.gtech.promotion.code.growth.GrowthAccountTypeEnum;
import com.gtech.promotion.code.growth.GrowthTransactionTypeEnum;
import com.gtech.promotion.exception.ErrorCodes;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.util.Date;

@Getter
@Setter
@ToString
public class UpdateGrowthParam implements Serializable {

    private static final long serialVersionUID = -3771092723454580580L;

    @ApiModelProperty(value = "Domain code", required = true)
	private String domainCode;

	@ApiModelProperty(value = "Tenant code", required = true)
	private String tenantCode;

	@ApiModelProperty(value = "account code. (UserCode or OrgCode)", required = true)
	private String accountCode;

	@ApiModelProperty(value = "Growth account type. (1-User 2-Organization)", required = true)
	private Integer accountType;

	@ApiModelProperty(value = "Growth change remark.")
	private String transactionRemarks;

	@ApiModelProperty(value = "Growth transaction type. (1-Increase growths 2-Deduct growths)", required = true)
	private Integer transactionType;

	@ApiModelProperty(value = "transaction amount.", required = true)
	private Integer transactionAmount;

    @ApiModelProperty(value = "refer order number.")
    private String referOrderNumber;

    @ApiModelProperty(value = "refer transaction sn.")
    private String referTransactionSn;

    @ApiModelProperty(value = "create time")
    private Date transactionCreateTime;

    @ApiModelProperty(value = "create user")
    private String transactionCreateUser;

    @ApiModelProperty(value = "Origin",example = "1-导入的数据；2-...")
    private Integer origin;

    /**
     * Parameter validation.
     */
    public void validate() {

        // Parameter validation
        CheckUtils.isNotBlank(this.domainCode, ErrorCodes.PARAM_EMPTY, "domainCode");
        CheckUtils.isNotBlank(this.tenantCode, ErrorCodes.PARAM_EMPTY, "tenantCode");
        CheckUtils.isNotBlank(this.accountCode, ErrorCodes.PARAM_EMPTY, "accountCode");
        CheckUtils.isNotNull(this.accountType, ErrorCodes.PARAM_EMPTY, "accountType");
        CheckUtils.isNotNull(this.transactionType, ErrorCodes.PARAM_EMPTY, "transactionType");
        CheckUtils.isNotNull(this.transactionAmount, ErrorCodes.PARAM_EMPTY, "transactionAmount");

        CheckUtils.isTrue(GrowthAccountTypeEnum.exist(this.accountType), ErrorCodes.PARAM_ERROR, "accountType");
        CheckUtils.isTrue(GrowthTransactionTypeEnum.exist(this.transactionType), ErrorCodes.PARAM_ERROR, "transactionType");
        CheckUtils.isTrue(this.transactionAmount >= 0, ErrorCodes.PARAM_ERROR, "transactionAmount");
    }

}