package com.gtech.promotion.vo.param.point.query;

import javax.validation.constraints.NotEmpty;

import com.gtech.commons.page.PageParam;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class QueryPointCampaignParam extends PageParam {

	/**
	 * Domain code.
	 */
	@ApiModelProperty(value = "Domain code")
	private String domainCode;

	/**
	 * Tenant code.
	 */
	@ApiModelProperty(value = "Tenant code", required = true)
	@NotEmpty(message = "tenantCode can not be empty")
	private String tenantCode;

    /**
     * campaign code
     */
	@ApiModelProperty(value = "campaign code")
    private String campaignCode;

    /**
     * campaign title
     */
	@ApiModelProperty(value = "campaign title")
    private String campaignTitle;

    /**
     * Campaingn sponsor information.
     */
	@ApiModelProperty(value = "Campaingn sponsor information.")
    private String sponsor;

    /**
     * Activity total points.
     */
	@ApiModelProperty(value = "Activity total points.")
    private Integer totalPoints;

    /**
     * Activity remaining points.
     */
	@ApiModelProperty(value = "Activity remaining points.")
    private Integer remainingPoints;

    /**
     * campaignstatus.( 0-inactive 1-active)
     */
	@ApiModelProperty(value = "campaignstatus.( 0-inactive 1-active)")
	private Integer status;

    /**
     * Parameter validation.
     */
    public void validate() {
		// to do something

    }
}