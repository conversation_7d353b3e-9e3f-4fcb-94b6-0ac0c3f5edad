package com.gtech.promotion.vo.result.activity;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class DiscountPriceItemResult implements Serializable {

    private static final long serialVersionUID = 6222469763407213883L;

    @ApiModelProperty(value = "sku code.")
    private String skuCode;

    @ApiModelProperty(value = "discount price.")
    private BigDecimal discountPrice;

//    @ApiModelProperty(value = "discount price.")
//    private Boolean isTriggerPromotion = false
//
    @ApiModelProperty(value = "activity code.")
    private String activityCode;
}
