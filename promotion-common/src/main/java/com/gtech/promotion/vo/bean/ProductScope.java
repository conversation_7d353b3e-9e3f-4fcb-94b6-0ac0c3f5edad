/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * <PERSON>ECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.vo.bean;

import com.gtech.promotion.code.PromotionConstants;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Iterator;
import java.util.List;

/**
 * 促销商品信息
 * 
 */
@Getter
@Setter
@ToString
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("ProductScope")
public class ProductScope implements Serializable{

    private static final long serialVersionUID = 2209609760172402672L;

    @ApiModelProperty(value = "Seq num",required = true)
    @Builder.Default
    private Integer seqNum = 1;

    @ApiModelProperty(value = "Product category code, separated by comma.",required = true)
    @Builder.Default
    private String categoryCode = PromotionConstants.UNLIMITED;
    public void setCategoryCode(String categoryCode) {

        this.categoryCode = StringUtils.isBlank(categoryCode) ? PromotionConstants.UNLIMITED : categoryCode;
    }

    @ApiModelProperty(value = "Product category name, separated by comma.",required = true)
    @Builder.Default
    private String categoryName = PromotionConstants.UNLIMITED_DESC;

    @ApiModelProperty(value = "Product brand code, separated by comma.",required = true)
    @Builder.Default
    private String brandCode = PromotionConstants.UNLIMITED;
    public void setBrandCode(String brandCode) {

        this.brandCode = StringUtils.isBlank(brandCode) ? PromotionConstants.UNLIMITED : brandCode;
    }

    @ApiModelProperty(value = "Product brand name, separated by comma.",required = true)
    @Builder.Default
    private String brandName = PromotionConstants.UNLIMITED_DESC;

    @ApiModelProperty(value = "Product attribute list. Empty means unlimited.",required = true)
    private List<ProductAttribute> attributes;

    @ApiModelProperty(value = "org code.",required = true)
    private String orgCode;

    @ApiModelProperty(value = "attrType.")
    private String attrType;

    @ApiModelProperty(value = "product tag.")
    private String productTag;

    @ApiModelProperty(value = "Spu attributes.")
    private List<ProductAttribute> spuAttributes;


    public void setAttributes(List<ProductAttribute> attributes) {

        if (CollectionUtils.isEmpty(attributes)) {
            this.attributes = null;
        } else {
            for(ProductAttribute a : attributes) {
                addAttribute(a);
            }
        }
    }
    public void addAttribute(ProductAttribute attribute) {
        
        if (null == attribute || this.isUnlimited(attribute.getAttributeCode())) {
            return;
        }
        if (null == this.attributes) {
            this.attributes = new ArrayList<>();
        }
        this.attributes.add(attribute);
    }

    private boolean isUnlimited(String code) {

        return StringUtils.isBlank(code) || PromotionConstants.UNLIMITED.equals(code);
    }

    /**
     * Parameter validation.
     */
    public void validate() {
        // No codes
    }

    // 是否是全商品
    public boolean isAllProduct(){

        if (!PromotionConstants.UNLIMITED.equals(this.categoryCode)) {
            return false;
        }

        if (!PromotionConstants.UNLIMITED.equals(this.brandCode)) {
            return false;
        }

        if (CollectionUtils.isNotEmpty(attributes)) {
            for(ProductAttribute attr : attributes) {
                if (!isUnlimited(attr.getAttributeCode())) {
                    return false;
                }
            }
        }

        return true;
    }

    public static void removeBySeq(List<ProductScope> list, int seqNum) {
        
        if (CollectionUtils.isEmpty(list)) {
            return;
        }

        Iterator<ProductScope> iterator = list.iterator();
        while(iterator.hasNext()) {
            if (iterator.next().getSeqNum() == seqNum) {
                iterator.remove();
            }
        }
    }

    public static List<ProductScope> getProductScopeBySeq(List<ProductScope> list, int seqNum) {

        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        }

        List<ProductScope> filterList = new ArrayList<>();
        Iterator<ProductScope> iterator = list.iterator();
        while(iterator.hasNext()) {
            ProductScope ps = iterator.next();
            if (ps.getSeqNum() == seqNum) {
                filterList.add(ps);
            }
        }

        return filterList;
    }
}
