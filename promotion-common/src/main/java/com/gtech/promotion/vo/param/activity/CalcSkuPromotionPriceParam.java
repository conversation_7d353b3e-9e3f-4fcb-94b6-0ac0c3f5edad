/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * <PERSON>ECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.vo.param.activity;

import com.gtech.commons.utils.CheckUtils;
import com.gtech.promotion.api.ApiConstants;
import com.gtech.promotion.exception.ErrorCodes;
import com.gtech.promotion.vo.bean.ProductAttribute;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 
 *
 * <AUTHOR>
 * @Date 2019-11-23
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("CalcSkuPromotionPriceRequest")
public class CalcSkuPromotionPriceParam {

    @ApiModelProperty(value = "Domain code.",example = "DC0001",required = true)
    private String domainCode;

    @ApiModelProperty(value = "Tenant code.",example = "TC0001",required = true)
    private String tenantCode;

    @ApiModelProperty(value = "Language id")
    private String language;

    @ApiModelProperty(value = "Store orgnization code.",example = "O00001")
    private String orgCode;

    /**
     * @deprecated
     */
    @ApiModelProperty("Deprecated. Member level codes list.")
    @Deprecated
    private List<String> memberLevelCodes; //NOSONAR

    /**
     * @deprecated
     */
    @ApiModelProperty("Deprecated. Member label codes list.")
    @Deprecated
    private List<String> memberLabelCodes; //NOSONAR

    @ApiModelProperty(value = ApiConstants.QUALIFICATIONS)
    private Map<String, List<String>> qualifications;

    @ApiModelProperty(value = "Channel code.")
    private String channelCode;

    @ApiModelProperty(value = "Product category code list.")
    private List<String> categoryCodes;

    @ApiModelProperty(value = "Product brand code.")
    private String brandCode;

    @ApiModelProperty(value = "Product code.")
    private String productCode;

    @ApiModelProperty(value = "Product sku code.")
    private String skuCode;

    @ApiModelProperty(name = "combineSkuCode",value = ApiConstants.COMBINESKUCODE)
    private String combineSkuCode;

    @ApiModelProperty(value = "Product attribute information list.")
    private List<ProductAttribute> attributes;

    @ApiModelProperty(value = "Product price.",required = true)
    private BigDecimal price;

    /**
     * Parameter validation.
     */
    public void validate() {

        CheckUtils.isNotBlank(this.domainCode, ErrorCodes.PARAM_EMPTY, "domainCode");
        CheckUtils.isNotBlank(this.tenantCode, ErrorCodes.PARAM_EMPTY, "tenantCode");
        CheckUtils.isNotNull(this.price, ErrorCodes.PARAM_EMPTY, "price");
    }
}
