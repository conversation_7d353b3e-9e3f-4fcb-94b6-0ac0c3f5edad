/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * <PERSON>ECH MAKES NO REPRESENTATIONS OR WA<PERSON><PERSON>NTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.vo.result.marketing;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("LuckyDrawMemberRecordResponse")
public class LuckyDrawMemberRecordResult implements Serializable {

    private static final long serialVersionUID = -4016623400786783705L;

    @ApiModelProperty(value = "Ticket code",required = true)
    private String ticketCode;

    @ApiModelProperty(value = "Status 02-no lucky; 03-lucky")
    private String status;

    @ApiModelProperty(value = "Prize list")
    private List<Prize> prizes;

    @ApiModelProperty(value = "Use time", required = true)
    private String useTime;

    @Data
    public static class Prize implements Serializable{

        private static final long serialVersionUID = -2739251667125724232L;

        @ApiModelProperty(value = "Prize no", required = true)
        private String prizeNo;

        @ApiModelProperty(value = "Prize code", required = true)
        private String prizeCode;

        @ApiModelProperty(value = "Prize name", required = true)
        private String prizeName;

        @ApiModelProperty(value = "Prize image")
        private String prizeImage;

        @ApiModelProperty(value = "Prize number per prize. eg. (coupon * 2)")
        private Integer prizeNum;
        // 奖品类型，01-券
        @ApiModelProperty(value = "Prize type. 00-no prize, 01-Coupon", required = true)
        private String prizeType;

    }
}
