package com.gtech.promotion.vo.param.purchaseconstraint;

import com.gtech.commons.utils.StringUtil;
import com.gtech.promotion.checker.purchaseconstraint.PurchaseConstraintChecker;
import com.gtech.promotion.exception.Check;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.io.Serializable;

@Data
@ToString
@ApiModel("UpdatePurchaseConstraintRequest")
@NoArgsConstructor
@AllArgsConstructor
public class UpdatePurchaseConstraintParam extends CreatePurchaseConstraintParam implements Serializable {
    @ApiModelProperty(value="Update purchase constraint code", required=true)
    private String purchaseConstraintCode;

    @Override
    public void validate(){
        Check.check(StringUtil.isBlank(purchaseConstraintCode), PurchaseConstraintChecker.NOT_NULL_PURCHASE_CONSTRAINT_CODE);

        super.validate();
    }
}
