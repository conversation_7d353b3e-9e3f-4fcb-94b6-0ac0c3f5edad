package com.gtech.promotion.vo.result.marketing;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class TicketSendResult {

    @ApiModelProperty(value = "Member user code.", required = true)
    private String memberCode;

    @ApiModelProperty(value = "Success ticket list.", required = true)
    private List<String> ticketCodes;

    public static TicketSendResult ok(String memberCode, List<String> ticketCodes){
        return new TicketSendResult(memberCode, ticketCodes);
    }
}
