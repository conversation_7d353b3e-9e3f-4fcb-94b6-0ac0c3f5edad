/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * <PERSON>ECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.vo.bean;

import java.io.Serializable;

import org.apache.commons.lang3.StringUtils;

import com.gtech.promotion.api.ApiConstants;
import com.gtech.promotion.code.PromotionConstants;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * Product attribute-values define for activity.
 *
 * <AUTHOR>
 * @Date 2020-05-08
 */
@ToString
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("ProductAttribute")
public class ProductAttribute implements Serializable {

    private static final long serialVersionUID = 3346411062340208026L;

    @Getter
    @ApiModelProperty(value = "Product attribute code",required = true)
    private String attributeCode;
    public void setAttributeCode(String attributeCode) {

        this.attributeCode = StringUtils.isBlank(attributeCode) ? PromotionConstants.UNLIMITED : attributeCode;
    }

    @Getter
    @ApiModelProperty(value = "Product attribute value, separated by comma.",required = true)
    private String attributeValues;
    public void setAttributeValues(String attributeValues) {

        this.attributeValues = StringUtils.isBlank(attributeValues) ? PromotionConstants.UNLIMITED : attributeValues;
    }

    @Getter
    @Setter
    @ApiModelProperty(value = ApiConstants.OPERATOR,example = "==",required = true)
    private String operator;

}