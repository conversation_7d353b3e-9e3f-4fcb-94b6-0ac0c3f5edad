/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * <PERSON>ECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.vo.param.marketing.flashsale;

import com.gtech.commons.utils.CheckUtils;
import com.gtech.promotion.checker.activity.TPromoOrderChecker;
import com.gtech.promotion.exception.Check;
import com.gtech.promotion.exception.ErrorCodes;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.math.BigDecimal;

/**
 * 提交锁定订单dto
 *
 */
@Getter
@Setter
@ToString
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("FlashSaleCreateOrderRequest")
public class FlashSaleCreateOrderParam extends FlashSaleOrderCalaParam {

    private static final long serialVersionUID = 1685537888696132216L;

    @ApiModelProperty(value = "促销扣减总金额",required = true)
    private BigDecimal promoDeductedAmount;//

    @ApiModelProperty(value = "Order number.",required = true)
    private String orderNo;

    /**
     * Parameter validation.
     */
    @Override
    public void validate() {
        
        super.validate();
        
        CheckUtils.isNotBlank(this.orderNo, ErrorCodes.PARAM_EMPTY, "orderNo");

        CheckUtils.isNotNull(this.promoDeductedAmount, ErrorCodes.PARAM_EMPTY, "promoDeductedAmount");

        String deductedAmount = this.promoDeductedAmount.toString();
        Check.check(deductedAmount.contains(".") && deductedAmount.substring(deductedAmount.indexOf('.') + 1, deductedAmount.length()).length() > 2,
            TPromoOrderChecker.ERROR_PRICE_LENGTH);
    }
}
