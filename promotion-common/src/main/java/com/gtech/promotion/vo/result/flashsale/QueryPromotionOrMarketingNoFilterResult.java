package com.gtech.promotion.vo.result.flashsale;

import com.gtech.promotion.vo.result.activity.QueryActivityListByProductResult;
import com.gtech.promotion.vo.result.marketing.MarketingQueryResult;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@ApiModel(value = "QueryPromotionOrMarketingNoFilterResponse")
@Data
public class QueryPromotionOrMarketingNoFilterResult implements Serializable {


    private String productCode;
    private String skuCode;
    private static final long serialVersionUID = 5977688253811179639L;
    @ApiModelProperty(value = "Product marketing activity list.")
    private List<MarketingQueryResult> marketingQueryResults;

    @ApiModelProperty(value = "Product promotion activity list.")
    private List<QueryActivityListByProductResult> activityListResults;



}
