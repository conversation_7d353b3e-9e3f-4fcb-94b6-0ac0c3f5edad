package com.gtech.promotion.vo.param.marketing;

import com.gtech.commons.utils.CheckUtils;
import com.gtech.promotion.exception.ErrorCodes;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("QueryRightOfFirstRefusalRequest")
public class QueryRightOfFirstRefusalParam {


    @ApiModelProperty(value = "DomainCode", required = true)
    private String domainCode;
    @ApiModelProperty(value = "TenantCode", required = true)
    private String tenantCode;
    @ApiModelProperty(value = "OrgCode", required = true)
    private String orgCode;
    @ApiModelProperty(value = "ActivityCode", required = true)
    private String activityCode;
    @ApiModelProperty(value = "memberCode", required = true)
    private String memberCode;
    @ApiModelProperty(value = "RightOfFirstRefusalCode")
    private String rightOfFirstRefusalCode;
    @ApiModelProperty(value = "RightOfFirstRefusalProductCode")
    private String rightOfFirstRefusalProductCode;

    public void validate() {
        CheckUtils.isNotBlank(this.domainCode, ErrorCodes.PARAM_EMPTY, "domainCode");
        CheckUtils.isNotBlank(this.tenantCode, ErrorCodes.PARAM_EMPTY, "tenantCode");
        CheckUtils.isNotBlank(this.orgCode, ErrorCodes.PARAM_EMPTY, "orgCode");
        CheckUtils.isNotBlank(this.activityCode, ErrorCodes.PARAM_EMPTY, "activityCode");
        CheckUtils.isNotBlank(this.memberCode, ErrorCodes.PARAM_EMPTY, "memberCode");
    }

}
