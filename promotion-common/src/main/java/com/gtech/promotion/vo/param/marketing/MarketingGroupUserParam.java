package com.gtech.promotion.vo.param.marketing;

import com.gtech.commons.utils.CheckUtils;
import com.gtech.promotion.checker.activity.TPromoOrderChecker;
import com.gtech.promotion.code.marketing.CloseFlagEnum;
import com.gtech.promotion.exception.Check;
import com.gtech.promotion.exception.ErrorCodes;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;


@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("MarketingGroupUserRequest")
public class MarketingGroupUserParam  implements Serializable {

    private static final long serialVersionUID = -800806958545225667L;

    @ApiModelProperty(value = "Domain code.",example = "D00001",required = true)
    private String domainCode;

    @ApiModelProperty(value = "Tenant code.",required = true)
    private String tenantCode;

    @ApiModelProperty(value = "activity code.",example = "12556568987",required = true)
    private String activityCode;

    @ApiModelProperty(value = "user code.",example = "12556568987")
    private String userCode;

    @ApiModelProperty(value = "Close flag. 封闭团 1 是 0否, 不传查所有",example = "1")
    private Integer closeFlag;

    @ApiModelProperty(value = "product code.",example = "1")
    private String productCode;

    @ApiModelProperty(value = "sku code.",example = "1")
    private String skuCode;

    public void validate() {

        CheckUtils.isNotBlank(this.domainCode, ErrorCodes.PARAM_EMPTY, "domainCode");
        CheckUtils.isNotBlank(this.tenantCode, ErrorCodes.PARAM_EMPTY, "tenantCode");
        CheckUtils.isNotBlank(this.activityCode, ErrorCodes.PARAM_EMPTY, "activityCode");

        if (null != closeFlag){
            Check.check(!CloseFlagEnum.exist(String.valueOf(closeFlag)), TPromoOrderChecker.NO_CLOSE_FLAG);
        }

    }


}