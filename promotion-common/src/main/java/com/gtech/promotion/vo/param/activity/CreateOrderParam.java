/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * <PERSON>ECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.vo.param.activity;

import com.gtech.commons.utils.CheckUtils;
import com.gtech.promotion.api.ApiConstants;
import com.gtech.promotion.checker.activity.TPromoOrderChecker;
import com.gtech.promotion.exception.Check;
import com.gtech.promotion.exception.ErrorCodes;
import com.gtech.promotion.vo.bean.Giveaway;
import com.gtech.promotion.vo.param.ShoppingCartParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 提交锁定订单dto
 *
 */
@Getter
@Setter
@ToString
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("CreateOrderRequest")
public class CreateOrderParam extends ShoppingCartParam {

    private static final long serialVersionUID = 1685537888696132216L;

    @ApiModelProperty(value="Total discount amount (including promoRewardPostage)",required=true)
    private BigDecimal promoDeductedAmount;//

    @ApiModelProperty(value = "Promotion giveaway ")
    private List<PromoGiveaway> promoGiveaways;//

    @ApiModelProperty(value = "Order number.",required = true)
    private String orderNo;

    @ApiModelProperty(value = "Deprecated." + ApiConstants.FREE_POSTAGE,required = true)
    private Integer freePostage;//

    @ApiModelProperty(value = "Total amount of postage deduction")
    private BigDecimal promoRewardPostage = BigDecimal.ZERO;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @ApiModel("CreateOrderParam#PromoGiveaway")
    public static class PromoGiveaway implements Serializable {

        private static final long serialVersionUID = 9009522069231110446L;

        @ApiModelProperty(value = "Activity code",required = true)
        private String activityCode;

        @ApiModelProperty(value = "Giveaways list",required = true)
        private List<Giveaway> giveaways;
    }

    /**
     * Parameter validation.
     */
    @Override
    public void validate() {
        
        super.validate();
        
        CheckUtils.isNotBlank(this.orderNo, ErrorCodes.PARAM_EMPTY, "orderNo");

        CheckUtils.isNotNull(this.promoDeductedAmount, ErrorCodes.PARAM_EMPTY, "promoDeductedAmount");
        CheckUtils.isNotNull(this.freePostage, ErrorCodes.PARAM_EMPTY, "freePostage");
 
        String deductedAmount = this.promoDeductedAmount.toString();
        Check.check(deductedAmount.contains(".") && deductedAmount.substring(deductedAmount.indexOf('.') + 1, deductedAmount.length()).length() > 2,
            TPromoOrderChecker.ERROR_PRICE_LENGTH);
    }
}
