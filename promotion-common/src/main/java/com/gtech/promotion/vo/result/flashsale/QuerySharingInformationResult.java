package com.gtech.promotion.vo.result.flashsale;

import com.gtech.promotion.vo.param.marketing.flashsale.HelpRecordResult;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.util.List;

@Data
@ApiModel("QuerySharingInformationResponse")
public class QuerySharingInformationResult {


    @ApiModelProperty(value = "domainCode")
    private String domainCode;

    @ApiModelProperty(value = "tenantCode")
    private String tenantCode;
    @ApiModelProperty(value = "orgCode")
    private String orgCode;
    @ApiModelProperty(value = "activityCode")
    private String activityCode;
    @ApiModelProperty(value = "sharingRecordCode")
    private String sharingRecordCode;
    @ApiModelProperty(value = "sharingMemberCode")
    private String sharingMemberCode;
    @ApiModelProperty(value = "numberOfPeopleWhoHaveHelped")
    private String numberOfPeopleWhoHaveHelped;
    @ApiModelProperty(value = "numberOfBoostSharing")
    private String numberOfBoostSharing;
    @ApiModelProperty(value = "activityStatus")
    private String activityStatus;
    @ApiModelProperty(value = "helpRecordResultList")
    private List<HelpRecordResult> helpRecordResultList;

}
