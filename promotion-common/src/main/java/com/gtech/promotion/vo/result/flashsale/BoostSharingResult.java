package com.gtech.promotion.vo.result.flashsale;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("BoostSharingResponse")
public class BoostSharingResult implements Serializable {

    private static final long serialVersionUID = -7487980040749957188L;


    @ApiModelProperty(value = "boost sharing type")
    private String boostSharingType;

    @ApiModelProperty(value = "attract new customers. 0-no 1-yes default 0")
    private String attractNewCustomers;

    @ApiModelProperty(value = "number of boost sharing")
    private String numberOfBoostSharing;

    /**-----------------分享人奖励----------------------*/

    @ApiModelProperty(value = "share to get coupon activity code")
    private String shareToGetCouponActivityCode;

    @ApiModelProperty(value = "share to get coupon activity name")
    private String shareToGetCouponActivityName;

    @ApiModelProperty(value = "share to get right of first refusal product code")
    private String rightOfFirstRefusalProductCode;
    @ApiModelProperty(value = "share to get right of first refusal product no")
    private String rightOfFirstRefusalProductNo;

    @ApiModelProperty(value = "share to get right of first refusal product name")
    private String rightOfFirstRefusalProductName;

    @ApiModelProperty(value = "right of first refusal start time")
    private String rightOfFirstRefusalStartTime;

    @ApiModelProperty(value = "right of first refusal end time")
    private String rightOfFirstRefusalEndTime;

    @ApiModelProperty(value = "lucky draw activity code")
    private String luckyDrawActivityCode;

    @ApiModelProperty(value = "lucky draw activity name")
    private String luckyDrawActivityName;
    /**-----------------分享人奖励----------------------*/


    /**-----------------助力人奖励----------------------*/
    @ApiModelProperty(value = "help to get coupon activity code")
    private String helpToGetCouponActivityCode;

    @ApiModelProperty(value = "help to get coupon activity name")
    private String helpToGetCouponActivityName;

    /**-----------------助力人奖励----------------------*/




    @ApiModelProperty(value = "Redirect link")
    private String redirectLink;
    @ApiModelProperty(value = "Image for the event page")
    private String eventPageImage;
    @ApiModelProperty(value = "Back ground image for the event page")
    private String eventBackGroundPageImage;
    @ApiModelProperty(value = "Event page link")
    private String eventPageLink;
    @ApiModelProperty(value = "Image for the mini-program")
    private String miniProgramImage;
    @ApiModelProperty(value = "Mini-program share copy")
    private String miniProgramShareCopy;
    @ApiModelProperty(value = "Image for the poster")
    private String posterImage;
    @ApiModelProperty(value = "Image for the popup")
    private String popupImage;
    @ApiModelProperty(value = "Popup share copy")
    private String popupShareCopy;


}
