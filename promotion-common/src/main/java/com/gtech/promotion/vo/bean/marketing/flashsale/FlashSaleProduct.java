package com.gtech.promotion.vo.bean.marketing.flashsale;

import java.io.Serializable;
import java.math.BigDecimal;

import com.gtech.commons.utils.CheckUtils;
import com.gtech.promotion.checker.flashsale.FlashSaleChecker;
import com.gtech.promotion.code.marketing.SwitchEnum;
import com.gtech.promotion.exception.Check;
import com.gtech.promotion.exception.ErrorCodes;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("FlashSaleProduct")
public class FlashSaleProduct implements Serializable {

    private static final long serialVersionUID = 2150433716565524116L;


    @ApiModelProperty(value = "Sku code.")
    private String skuCode;

    @ApiModelProperty(value = "Sku name.")
    private String skuName;

    @ApiModelProperty(value = "Product code.")
    private String productCode;

    @ApiModelProperty(value = "Spu name.")
    private String spuName;

    @ApiModelProperty(value = "Sku quota.", required = true)
    private Integer skuQuota;

    @ApiModelProperty(value = "Max flash sale times per user.")
    private Integer maxPerUser;


    @ApiModelProperty(value = "Sku list price.", required = true)
    private BigDecimal listPrice;

    @ApiModelProperty(value = "Sku sale price.", required = true)
    private BigDecimal salePrice;

    @ApiModelProperty(value = "Sku flash sale price.", required = true)
    private BigDecimal flashPrice;

    @ApiModelProperty(value = "Group leader price. Required fields for group joining activities.")
    private BigDecimal groupLeaderPrice;

    @ApiModelProperty(value = "Json format.")
    private String extendParam;

    @ApiModelProperty(value = "Limit flag. This field indicates whether to limit the inventory quantity, 1 is limited, 0 is not limited, and the default is limited.")
    private Integer limitFlag = 1;

    @ApiModelProperty(value = "Max flash sale times per user. 1 is limited, 0 is not limited, and the default is limited.")
    private Integer maxPerUserFlag =1;



    public void validate() {

        CheckUtils.isTrue(null != this.listPrice, ErrorCodes.PARAM_EMPTY, "listPrice");
        CheckUtils.isTrue(null != this.salePrice, ErrorCodes.PARAM_EMPTY, "salePrice");
        CheckUtils.isTrue(null != this.flashPrice, ErrorCodes.PARAM_EMPTY, "flashPrice");

        CheckUtils.isTrue(SwitchEnum.exist(limitFlag.toString()), ErrorCodes.SWITCH, "limitFlag");
        CheckUtils.isTrue(SwitchEnum.exist(maxPerUserFlag.toString()), ErrorCodes.SWITCH, "maxPerUserFlag");
		if (1 == limitFlag.intValue()) {
            CheckUtils.isTrue(null != this.skuQuota, ErrorCodes.PARAM_EMPTY, "skuQuota");
            Check.check(0 >= this.skuQuota, FlashSaleChecker.SKU_QUOTA_ZERO);
        }
		if (1 == maxPerUserFlag.intValue()) {

            CheckUtils.isTrue(null != this.maxPerUser, ErrorCodes.PARAM_EMPTY, "skuQuota");
            Check.check(0 >= this.maxPerUser, FlashSaleChecker.SKU_MAX_PER_USER_ZERO);
        }

        Check.check(flashPrice.compareTo(BigDecimal.ZERO) <= 0, FlashSaleChecker.SKU_FLASH_PRICE_ZERO);
        Check.check(flashPrice.compareTo(salePrice) > 0, FlashSaleChecker.SKU_FLASH_PRICE_BIG);
    }


}
