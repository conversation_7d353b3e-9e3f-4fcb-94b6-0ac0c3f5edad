/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.vo.bean;

import com.gtech.commons.utils.CheckUtils;
import com.gtech.promotion.exception.ErrorCodes;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.io.Serializable;
import java.math.BigDecimal;


/**
 * 
 *
 * <AUTHOR>
 * @Date 2020-02-11
 */
@Getter
@Setter
@ToString
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("IncentiveLimited")
public class IncentiveLimited implements Serializable{

    private static final long serialVersionUID = -6767115817247361671L;
    // 01-活动限制总次数 02-活动限制总金额 03-活动限制单日总次数 04-活动限制单日总金额 05-每人限制总次数 06-每人限制总金额；07-每人限制每天总次数；08-每人限制每天总金额; 09-活动限制单日总订单数 21-活动限制每单优惠金额; 22-每人每周限制总次数  " +
    //            "23-每人每周限制总金额; 24-每人每月限制总次数 25-每人每月限制总金额 35-用户领券限制次数

    @ApiModelProperty(value = "Limitation code: 01-Total number of activity limits " +
            "02-Activity limit total amount 03-Limit the total number of activities per day 04-Activities limit the total amount of a day" +
            " 05-Limit the total number of times per person 06-Limit the total amount per person；" +
            "07-Limit the total number of times per person per day；08-Limit the total amount per person per day; 09-Activities limit the total number of orders per day" +
            " 21-Activity limits the amount of offers per order; 22-Limit the total number of times per person per week  " +
            "23-Limit the total amount per person per week; 24-Limit the total number of times per person per month 25-Limit the total amount per person per month " +
            "35-Limit the number of times users can receive coupons  ",required = true)
    private String limitationCode;

    @ApiModelProperty(value = "Limitation value",required = true)
    private BigDecimal limitationValue;

    /**
     * Parameter validation.
     */
    public void validate() {

        CheckUtils.isNotBlank(this.limitationCode, ErrorCodes.PARAM_EMPTY, "limitationCode");
        CheckUtils.isNotNull(this.limitationValue, ErrorCodes.PARAM_EMPTY, "limitationValue");
    }
}