package com.gtech.promotion.vo.param.purchaseconstraint;

import com.gtech.commons.utils.CheckUtils;
import com.gtech.promotion.exception.ErrorCodes;
import com.gtech.promotion.vo.bean.ProductAttribute;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.math.BigDecimal;
import java.util.List;

@Data
@ToString
@NoArgsConstructor
@AllArgsConstructor
@Builder
@ApiModel("CheckPurchaseConstraintProductParam")
public class CheckPurchaseConstraintProductParam {

    private List<String> categories;// 此list为categoryPath 格式A1>B1>C 如果以后需要时 新增List<String> categoryCodes
    @ApiModelProperty(value = "Product category code list.")
    private List<String> categoryCodes;
    @ApiModelProperty(value = "Product category name.")
    private List<String> categoryNames;
    @ApiModelProperty(value = "Product brand code.")
    private String brandCode;
    @ApiModelProperty(value = "Product brand name.")
    private String brandName;
    @ApiModelProperty(value = "SKU attribute information list.")
    private List<ProductAttribute> attributes; // sku属性
    @ApiModelProperty(value = "Product code.")
    private String productCode;
    @ApiModelProperty(value = "Product name.")
    private String productName;
    @ApiModelProperty(value = "Product sku code.")
    private String skuCode;
    @ApiModelProperty(value = "Product sku name.")
    private String skuName;
    @ApiModelProperty(value = "Product promotion price.")
    private BigDecimal promotionPrice; // 此商品行折后单价
    @ApiModelProperty(value = "Product promotion amount.")
    private BigDecimal promotionAmount; // 此商品行折后总价
    @ApiModelProperty(value = "Product quantity.",required = true)
    private Integer quantity;
    @ApiModelProperty(value = "Product selected.")
    private Integer selected;

    @ApiModelProperty(value = "Product main product number.")
    private String mainProductNo;

    @ApiModelProperty(value = "Product attribute information list.")
    private List<ProductAttribute> spuAttributes; // spu属性，用于核算促销价时，处理通过spu属性标记的活动

    @ApiModelProperty(value = "Product tag code.")
    private String productTag;
    /**
     * 高级价格限购标识，默认0，非高级价格限购
     */
    @ApiModelProperty(value = "price setting")
    private Integer priceSetting;

    public void validate(){
        CheckUtils.isNotBlank(this.skuCode, ErrorCodes.PARAM_EMPTY, "skuCode");
        CheckUtils.isNotBlank(this.productCode, ErrorCodes.PARAM_EMPTY, "productCode");
        CheckUtils.isNotNull(this.quantity, ErrorCodes.PARAM_EMPTY, "quantity");
        CheckUtils.isNotNull(this.promotionAmount, ErrorCodes.PARAM_EMPTY, "promotionAmount");
        CheckUtils.isNotNull(this.promotionPrice, ErrorCodes.PARAM_EMPTY, "promotionPrice");
        CheckUtils.isNotNull(this.priceSetting, ErrorCodes.PARAM_EMPTY, "priceSetting");
    }
}
