/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * <PERSON>ECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.vo.bean;

import com.gtech.commons.utils.CheckUtils;
import com.gtech.promotion.exception.ErrorCodes;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;

/**
 * 渠道店铺对象
 */
@Getter
@Setter
@ToString
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("ActivityStore")
public class ActivityStore implements Serializable {

    // serialVersionUID
    private static final long serialVersionUID = 1630054597943356910L;

    @ApiModelProperty(value = "Channel code",required = false)
    private String channelCode;

    @ApiModelProperty(value = "Channel name",required = false)
    private String channelName;

    @ApiModelProperty(value = "Store orgnization code",required = true)
    private String orgCode;

    @ApiModelProperty(value = "Store name",required = true)
    private String storeName;

    @ApiModelProperty(value = "Active link",required = false)
    private String url;

    /**
     * Parameter validation.
     */
    public void validate() {

        CheckUtils.isNotBlank(this.orgCode, ErrorCodes.PARAM_EMPTY, "orgCode");
        CheckUtils.isNotBlank(this.storeName, ErrorCodes.PARAM_EMPTY, "storeName");

        if (StringUtils.isBlank(this.channelCode)) {
            this.channelCode = "0000";
            this.channelName = "ALL";
        }
    }
}
