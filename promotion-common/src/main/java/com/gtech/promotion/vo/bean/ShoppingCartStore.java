/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.vo.bean;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

import org.springframework.util.CollectionUtils;

import com.gtech.commons.utils.CheckUtils;
import com.gtech.commons.utils.StringUtil;
import com.gtech.promotion.exception.ErrorCodes;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;


/**
 * 
 *
 * <AUTHOR>
 * @Date 2020-02-13
 */
@Getter
@Setter
@ToString
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("ShoppingCartStore")
public class ShoppingCartStore implements Serializable {

    private static final long serialVersionUID = 4867317622255593407L;

    @ApiModelProperty(value = "Store orgnization code.",example = "OC001")
    private String orgCode;

    @ApiModelProperty(value = "Store name.",example = "SName001")
    private String storeName;

    @ApiModelProperty(value = "Shoping cart item list.",required = true)
    private List<ShoppingCartItem> cartItemList;

    /**
     * Parameter validation.
     */
    public void validate() {

        CheckUtils.isTrue(!CollectionUtils.isEmpty(this.cartItemList), ErrorCodes.PARAM_EMPTY, "cartItemList");

        // 合并相同的商品
        List<String> skuCodes = new ArrayList<>();
        List<ShoppingCartItem> cartItems = new ArrayList<>();
        for (ShoppingCartItem cartItem : this.cartItemList) {

            cartItem.validate();

            checkProduct(cartItem);

            if (StringUtil.isNotBlank(cartItem.getCombineSkuCode())) {
                cartItem.setSkuCode(cartItem.getCombineSkuCode());
            }
            if (skuCodes.contains(cartItem.getSkuCode())) {
                for (ShoppingCartItem cartItem1 : this.cartItemList) {
                    if (cartItem1.getSkuCode().equals(cartItem.getSkuCode())) {
                        cartItem1.setQuantity(cartItem1.getQuantity() + cartItem.getQuantity());
                    }
                }
            } else {
                skuCodes.add(cartItem.getSkuCode());
                cartItems.add(cartItem);
            }
        }

        this.cartItemList = cartItems;
    }

    // 商品属性校验：购物车sku编码不能也为空
    private static void checkProduct(ShoppingCartItem cartItem) {

        checkProductCombine(cartItem);
        if (StringUtil.isBlank(cartItem.getCombineSkuCode())) {
            CheckUtils.isNotBlank(cartItem.getSkuCode(), ErrorCodes.PARAM_EMPTY, "skuCode");
        }
    }

    private static void checkProductCombine(ShoppingCartItem product) {

        if (StringUtil.isBlank(product.getCombineSkuCode())) {
            //根据商品匹配活动 属性编码和值只要有一个为空，则不合法，删掉，不抛错
            if (!CollectionUtils.isEmpty(product.getAttributes())) {
                Iterator<ProductAttribute> iterator = product.getAttributes().iterator();
                while (iterator.hasNext()) {
                    ProductAttribute attributelDTO = iterator.next();
                    if (StringUtil.isBlank(attributelDTO.getAttributeCode()) || StringUtil.isBlank(attributelDTO.getAttributeValues())) {
                        iterator.remove();
                    }
                }
            }
        } else {
            product.setAttributes(null);
            product.setCategoryCodes(null);
            product.setBrandCode(null);
            product.setProductCode(null);
            product.setSkuCode(null);
        }
    }
}
