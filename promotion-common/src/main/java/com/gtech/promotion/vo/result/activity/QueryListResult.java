/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * <PERSON>ECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.vo.result.activity;

import com.gtech.promotion.api.ApiConstants;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.util.Date;

@Getter
@Setter
@ToString
@ApiModel("QueryListResponse")
public class QueryListResult implements Serializable {

    private static final long serialVersionUID = 2912028981758700L;
    @ApiModelProperty(value = "Activity code")
    private String activityCode;

    @ApiModelProperty(value = "Activity name")
    private String activityName;

    @ApiModelProperty(value = "Activity type：01-Activity 02-Coupon 03-lucky draw 04-flash sale")
    private String activityType;

    @ApiModelProperty(value = "Activity sponsors")
    private String sponsors;

    @ApiModelProperty(value = "Activity start time. (yyyyMMddHHmmss)")
    private String activityBegin;

    @ApiModelProperty(value = "Activity end time. (yyyyMMddHHmmss)")
    private String activityEnd;

    @ApiModelProperty(value = ApiConstants.ACTIVITY_STATUS)
    private String activityStatus;

    @ApiModelProperty(value = "Activity create time")
    private Date createTime;

    @ApiModelProperty(value = "ops create Activity type")
    private String opsType;

}
