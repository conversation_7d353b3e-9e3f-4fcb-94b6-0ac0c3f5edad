package com.gtech.promotion.vo.param.activity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

/**
 * 商品活动匹配参数
 * 继承购物车参数，添加活动列表过滤功能
 * 
 * <AUTHOR>
 * @date 2025-01-23
 */
@Getter
@Setter
@ToString(callSuper = true)
@ApiModel("MatchProductsToActivitiesRequest")
public class MatchProductsToActivitiesParam extends CalcShoppingCartParam {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "Activity codes to filter (optional, if empty will match all activities)")
    private List<String> activityCodes;

    @Override
    public void validate() {
        super.validate();
    }
}
