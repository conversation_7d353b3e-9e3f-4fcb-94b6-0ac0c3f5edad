package com.gtech.promotion.vo.bean;

import com.gtech.commons.utils.CheckUtils;
import com.gtech.promotion.exception.ErrorCodes;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2020/12/29 15:43
 */
@Getter
@Setter
@ToString
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("CampaignTitleLanguage")
public class CampaignTitleLanguage implements Serializable{
    private static final long serialVersionUID = -7126036449958827262L;


    @ApiModelProperty(value = "Campaign name.",required = false)
    private String campaignTitle;

    @ApiModelProperty(value = "Language id. (en-US/id-ID/zh-CN/...)",required = true)
    private String language;

    /**
     * Parameter validation.
     */
    public void validate() {

        CheckUtils.isNotBlank(this.language, ErrorCodes.PARAM_EMPTY, "language");
    }

}
