package com.gtech.promotion.vo.param.activity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @Description :
 * <AUTHOR> gaojie.li
 * @Date : 2023/7/13 10:08
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("GroupRelationRequest")
public class GroupRelationParam implements Serializable {

    private static final long serialVersionUID = -2707946353439468265L;

    @ApiModelProperty(value = "分组编码A", example = "1231209", required = true)
    private String groupCodeA;

    @ApiModelProperty(value = "分组编码B", example = "1231209", required = true)
    private String groupCodeB;

    @ApiModelProperty(value = "分组关系 1:叠加 2：互斥", example = "1", required = true)
    private Integer relation;
}
