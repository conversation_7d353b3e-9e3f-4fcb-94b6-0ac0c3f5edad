/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.vo.result.flashsale;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;

/**
 * 根据商品查询促销列表出参实体(出参)
 */
@Getter
@Setter
@ToString
@EqualsAndHashCode(callSuper = false)
@ApiModel("QueryFlashSaleListByProductListResponse")
public class QueryFlashSaleListByProductListResult implements Serializable {

    private static final long serialVersionUID = 321205282953756990L;
    @ApiModelProperty(value = "Product code.")
    private String productCode;

    @ApiModelProperty(value = "Product sku code.")
    private String skuCode;

    @ApiModelProperty(value = "Combine product sku code.")
    private String combineSkuCode;

    private List<QueryFlashSaleListByProductResult> flashSaleList;

}
