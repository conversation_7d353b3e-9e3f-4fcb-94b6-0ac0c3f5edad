package com.gtech.promotion.vo.param.activity;

import com.gtech.commons.utils.CheckUtils;
import com.gtech.promotion.exception.ErrorCodes;
import com.gtech.promotion.vo.bean.ProductDetail;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021/1/26 10:31
 * @Version 1.0
 */

@Data
@ToString
@ApiModel("UpdateActivityProductDetailBlackListRequest")
public class UpdateActivityProductDetailBlackListParam implements Serializable {
    private static final long serialVersionUID = 2392377942215729581L;

    @ApiModelProperty( value="Tenant code.", example="TC0001",required=true)
    private String tenantCode;

    @ApiModelProperty( value="orgCode.", example="default",required=true)
    private String orgCode;

    @ApiModelProperty(value = "List of blacklisted product details")
    private List<ProductDetail> productDetailBlackList;

    /**
     * Parameter validation.
     */
    public void validate() {
        CheckUtils.isNotBlank(this.tenantCode, ErrorCodes.PARAM_EMPTY, "tenantCode");
    }
}
