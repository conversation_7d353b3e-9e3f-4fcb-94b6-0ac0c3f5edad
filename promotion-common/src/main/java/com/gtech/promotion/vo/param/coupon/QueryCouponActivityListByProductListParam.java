/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.vo.param.coupon;

import com.gtech.commons.utils.CheckUtils;
import com.gtech.promotion.api.ApiConstants;
import com.gtech.promotion.exception.ErrorCodes;
import com.gtech.promotion.vo.bean.ProductAttribute;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * 根据商品查询促销列表(入参)
 * 商户编码和商品信息
 * 
 */
@Getter
@Setter
@ToString
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("QueryCouponActivityListByProductListRequest")
public class QueryCouponActivityListByProductListParam implements Serializable {

    private static final long serialVersionUID = -891450636157095042L;

    @ApiModelProperty( value="Domain code.", example="D00001",required=true)
    private String domainCode;

    @ApiModelProperty(value = "Tenant code.",required = true)
    private String tenantCode;

    @ApiModelProperty(value = "Language id.")
    private String language;

    @ApiModelProperty(value = "Channel code.")
    private String channelCode;

    @ApiModelProperty(value = "Organization code.")
    private String orgCode;

    @ApiModelProperty(value = ApiConstants.QUALIFICATIONS)
    private Map<String, List<String>> qualifications;

    /**
     * @deprecated
     */
    @ApiModelProperty(value = "Deprecated. Member level code.",required = false)
    @Deprecated
    private String memberLevelCode; //NOSONAR

    /**
     * @deprecated
     */
    @ApiModelProperty(value = "Deprecated. Member label codes.",required = false)
    @Deprecated
    private String memberLabelCodes; //NOSONAR

    @ApiModelProperty(value = "product list", example = "[123,123]")
    private List<Product> productList;




    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Product implements Serializable {

        private static final long serialVersionUID = 7643926171938415849L;

        @ApiModelProperty(value = "Store orgnization codes. Separated by commas. Empty value for all stores.")
        private String orgCodes;

        @ApiModelProperty(value = "Product category code list.")
        private List<String> categoryCodes;

        @ApiModelProperty(value = "Product brand code. If combineSkuCode is empty, brandCode can not be empty")
        private String brandCode;

        @ApiModelProperty(value = "Product code. If combineSkuCode is empty, productCode can not be empty")
        private String productCode;

        @ApiModelProperty(value = "Product sku code.")
        private String skuCode;

        @ApiModelProperty(value = "Combine product sku code. If productCode, skuCode, brandCode, categoryCodes and attributes are all empty, combineSkuCode can not be empty")
        private String combineSkuCode;

        @ApiModelProperty(value = "Product tag code.")
        private String productTag;

        @ApiModelProperty(value = "Product attribute information list.")
        private List<ProductAttribute> spuAttributes;

        @ApiModelProperty(value = "Attribute")
        private List<ProductAttribute> attributes;

        public void validate() {

            boolean singleProduct = StringUtils.isNotBlank(this.productCode) || StringUtils.isNotBlank(this.skuCode)
                    || CollectionUtils.isNotEmpty(this.categoryCodes) || CollectionUtils.isNotEmpty(this.attributes);
            boolean combineProduct = StringUtils.isNotBlank(this.combineSkuCode);
            CheckUtils.isTrue((singleProduct && !combineProduct) || (!singleProduct && combineProduct) , ErrorCodes.PARAM_SPECIFICATION_ERROR,
                    "If productCode, skuCode, categoryCodes and attributes are all empty, combineSkuCode can not be empty. "
                            + "One of the productCode, skuCode, categoryCode or attributes is not empty, combineSkuCode must be empty.");

        }
    }

    /**
     * Parameter validation.
     */
    public void validate() {

        CheckUtils.isNotBlank(this.domainCode, ErrorCodes.PARAM_EMPTY, "domainCode");
        CheckUtils.isNotBlank(this.tenantCode, ErrorCodes.PARAM_EMPTY, "tenantCode");


        CheckUtils.isTrue(CollectionUtils.isNotEmpty(this.productList), ErrorCodes.PARAM_EMPTY, "productList");

        for (Product product : productList) {
            product.validate();
        }

    }

}
