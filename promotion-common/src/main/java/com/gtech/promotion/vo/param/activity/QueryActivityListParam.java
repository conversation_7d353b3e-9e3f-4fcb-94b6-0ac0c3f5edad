/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * <PERSON>ECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.vo.param.activity;

import com.gtech.commons.page.PageParam;
import com.gtech.commons.utils.CheckUtils;
import com.gtech.commons.utils.StringUtil;
import com.gtech.promotion.api.ApiConstants;
import com.gtech.promotion.exception.ErrorCodes;
import com.gtech.promotion.utils.DateValidUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;

/**
 * QueryActivityListParam
 *
 * <AUTHOR>
 * @Date 2020-02-14
 */
@Getter
@Setter
@ToString
@ApiModel("QueryActivityListRequest")
public class QueryActivityListParam extends PageParam implements Serializable {

    private static final long serialVersionUID = -291316796684135355L;

    @ApiModelProperty(value = "Domain code",required = true)
    private String domainCode;

    @ApiModelProperty(value = "Tenant code",required = true)
    private String tenantCode;

    @ApiModelProperty( value="orgCode.", example="default",required=true)
    private String orgCode;

    @ApiModelProperty( value="activity org code.", example="default",required=true)
    private String activityOrgCode;

    @ApiModelProperty(value = "Language id.")
    private String language;

    @ApiModelProperty(value = "Activity code.")
    private String activityCode;

    @ApiModelProperty(value = "Activity type：01-Activity 02-Coupon")
    private String activityType;
//    @ApiModelProperty(value = "Activity tag codes. Separated by commas. 01-单品、02-满减、03-满折、04-特价、05-包邮、06-赠品、07-package、08-满送、10-买A优惠B 11-prodict discount 20-promotion code")
    @ApiModelProperty(value = "Activity tag codes. Separated by commas. 01--Simple promotion、02-Full reduction、03-Full discount、04-special price、05-free shipping、6-giveaway、7-package、8-limited free、10-Buy A get B discount 11-prodict discount 20-promotion code")
    private String tagCode;

    @ApiModelProperty(value = "Activity name.")
    private String activityName;

    @ApiModelProperty(value = "Activity begin time from.")
    private String activityBeginFrom;

    @ApiModelProperty(value = "Activity begin time to.")
    private String activityBeginTo;

    @ApiModelProperty(value = "Activity end time from.")
    private String activityEndFrom;

    @ApiModelProperty(value = "Activity end time to.")
    private String activityEndTo;

    @ApiModelProperty(value = "Activity create time from.")
    private String createTimeFrom;

    @ApiModelProperty(value = "Activity create time to.")
    private String createTimeTo;

    @ApiModelProperty(value = "Activity status: split by comma.")
    private String activityStatus;

    @ApiModelProperty(value = ApiConstants.ORDERBY_TYPE)
    private String orderByType;

    @ApiModelProperty(value = "Activity sponsors.")
    private String sponsors;

    @ApiModelProperty(value = "ops create Activity type")
    private String opsType;

    @ApiModelProperty(value = "warm begin time from")
    private String warmBeginFrom;

    @ApiModelProperty(value = "warm begin time to")
    private String warmBeginTo;

    @ApiModelProperty(value = "warm end time from")
    private String warmEndFrom;

    @ApiModelProperty(value = "warm end time to")
    private String warmEndTo;

    @ApiModelProperty(value = "defaultFlag default = false")
    private Boolean defaultFlag = true;


    /**
     * Parameter validation.
     */
    public void validate() {

        CheckUtils.isNotBlank(this.domainCode, ErrorCodes.PARAM_EMPTY, "domainCode");
        CheckUtils.isNotBlank(this.tenantCode, ErrorCodes.PARAM_EMPTY, "tenantCode");

        // 时间查询条件验证
        CheckUtils.isTrue(StringUtil.isBlank(this.activityBeginFrom) || DateValidUtil.isValidDate(this.activityBeginFrom), ErrorCodes.PARAM_ERROR, "activityBeginFrom");
        CheckUtils.isTrue(StringUtil.isBlank(this.activityBeginTo) || DateValidUtil.isValidDate(this.activityBeginTo), ErrorCodes.PARAM_ERROR, "activityBeginTo");
        CheckUtils.isTrue(StringUtil.isBlank(this.activityEndFrom) || DateValidUtil.isValidDate(this.activityEndFrom), ErrorCodes.PARAM_ERROR, "activityEndFrom");
        CheckUtils.isTrue(StringUtil.isBlank(this.activityEndTo) || DateValidUtil.isValidDate(this.activityEndTo), ErrorCodes.PARAM_ERROR, "activityEndTo");

        if (StringUtil.isNotBlank(this.activityBeginFrom) && StringUtil.isNotBlank(this.activityBeginTo)) {
            CheckUtils.isTrue(this.activityBeginFrom.compareTo(this.activityBeginTo) < 0, ErrorCodes.PARAM_SPECIFICATION_ERROR,
                "activityBeginFrom must be earlier than activityBeginTo");
        }
        if (StringUtil.isNotBlank(this.activityEndFrom) && StringUtil.isNotBlank(this.activityEndTo)) {
            CheckUtils.isTrue(this.activityEndFrom.compareTo(this.activityEndTo) < 0, ErrorCodes.PARAM_SPECIFICATION_ERROR,
                "activityEndFrom must be earlier than activityEndTo");
        }
        if (StringUtil.isNotBlank(this.createTimeFrom) && StringUtil.isNotBlank(this.createTimeTo)) {
            CheckUtils.isTrue(this.createTimeFrom.compareTo(this.createTimeTo) < 0, ErrorCodes.PARAM_SPECIFICATION_ERROR,
                "createTimeFrom must be earlier than createTimeTo");
        }
    }
}
