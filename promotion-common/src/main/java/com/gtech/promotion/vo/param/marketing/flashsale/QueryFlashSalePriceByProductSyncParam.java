package com.gtech.promotion.vo.param.marketing.flashsale;

import com.gtech.commons.utils.CheckUtils;
import com.gtech.promotion.exception.ErrorCodes;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;

import java.util.List;

@Data
public class QueryFlashSalePriceByProductSyncParam {

    private String tenantCode;
    private List<Product> products;

    @Data
    public static class Product{
        private String productCode;
        private String skuCode;
    }

    public void validate(){
        CheckUtils.isNotBlank(this.tenantCode, ErrorCodes.PARAM_EMPTY, "tenantCode");
        CheckUtils.isTrue(CollectionUtils.isNotEmpty(this.products), ErrorCodes.PARAM_EMPTY, "products");
        for (Product product : products) {
            CheckUtils.isNotBlank(product.getProductCode(), ErrorCodes.PARAM_EMPTY, "productCode");
            CheckUtils.isNotBlank(product.getSkuCode(), ErrorCodes.PARAM_EMPTY, "skuCode");
        }
    }
}
