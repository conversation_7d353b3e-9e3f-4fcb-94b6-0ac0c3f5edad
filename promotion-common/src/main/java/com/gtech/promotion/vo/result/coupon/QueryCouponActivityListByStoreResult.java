/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.vo.result.coupon;

import com.gtech.promotion.api.ApiConstants;
import com.gtech.promotion.vo.bean.ActivityPeriod;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 店铺下所有生效的优惠券活动列表出参
 */
@Getter
@Setter
@ToString
@ApiModel("QueryCouponActivityListByStoreResponse")
// com.gtech.promotion.vo.result.coupon.QueryCouponActivityListByStoreResult
public class QueryCouponActivityListByStoreResult implements Serializable{

    private static final long serialVersionUID = -6043576185958844061L;

    @ApiModelProperty(value = "Activity name.")
    private String activityName;
    
    @ApiModelProperty(value = "Activity code.")
    private String activityCode;

    @ApiModelProperty(value = "Activity label")
    private String activityLabel;

    @ApiModelProperty(value = "Activity desc")
    private String activityDesc;

    @ApiModelProperty(value = "Activity remark")
    private String activityRemark;

    @ApiModelProperty(value = "Coupon face value")
    private BigDecimal faceValue;

    @ApiModelProperty(ApiConstants.FACE_UNIT)
    private String faceUnit;

    // conditionUnit:条件值单位 01：金额  02：数量   
    @ApiModelProperty(ApiConstants.CONDITION_UNIT)
    private String conditionUnit;

    @ApiModelProperty(value = "Condition value")
    private BigDecimal conditionValue;

    @ApiModelProperty(value = "Activity URL")
    private String activityUrl;

    // rewardType:奖励类型：减金额(01)、打折扣(02)、单件固定金额(03)、组合固定金额(04)、包邮(05)、送赠品(06)、买A送A(07)、买A送B(08)    
    @ApiModelProperty(value = ApiConstants.REWARD_TYPE)
    private String rewardType;

    @ApiModelProperty("Receive start time")
    private String startReceiveTime;

    @ApiModelProperty("Receive end time")
    private String endReceiveTime;

    @ApiModelProperty(value = ApiConstants.RECEIVE_STATUS_STORE)
    private int receiveStatus;

    @ApiModelProperty("Each limit get")
    private Integer userLimitMax;

    @ApiModelProperty(value = "Activity warm-up begin time.")
    private String warmBegin;

    @ApiModelProperty(value = "Activity warm-up end time.")
    private String warmEnd;

    @ApiModelProperty(value = "Activity cool down time.")
    private String coolDown;

    @ApiModelProperty(value = "Activity background image.")
    private String backgroundImage;

    @ApiModelProperty(value = "Activity period.")
    private ActivityPeriod activityPeriod;
}
