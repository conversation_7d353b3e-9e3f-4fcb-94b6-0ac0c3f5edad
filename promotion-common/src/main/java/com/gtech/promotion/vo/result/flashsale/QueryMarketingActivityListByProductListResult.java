package com.gtech.promotion.vo.result.flashsale;

import com.gtech.promotion.vo.bean.ActivityPeriod;
import com.gtech.promotion.vo.bean.Qualification;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Getter
@Setter
@ToString
@EqualsAndHashCode(callSuper = false)
@ApiModel("QueryMarketingActivityListByProductListResponse")
public class QueryMarketingActivityListByProductListResult implements Serializable {
    private static final long serialVersionUID = -5090022225149904710L;


    @ApiModelProperty(value = "Product code")
    private String productCode;

    @ApiModelProperty(value = "Product marketing activity list.")
    private List<MarketingActivity> marketingActivityList;

    @ApiModelProperty(value = "Sku list")
    private List<SkuActivityList> skuList;




    @Getter
    @Setter
    @ToString
    @EqualsAndHashCode(callSuper = false)
    @ApiModel("SkuActivityListResponse")
    public static class SkuActivityList implements Serializable {
        private static final long serialVersionUID = 1182921258705624479L;
        @ApiModelProperty(value = "Sku code")
        private String skuCode;
        @ApiModelProperty(value = "Sku marketing activity list.")
        private List<MarketingActivity> marketingActivityList;
    }



    @Data
    @ApiModel("MarketingActivity")
    public static class MarketingActivity implements Serializable {

        private static final long serialVersionUID = -3773839186816010111L;
        @ApiModelProperty(value = "Activity type：01-Activity 02-Coupon 03-Lucky draw 04-Flash sale")
        private String activityType;

        @ApiModelProperty(value = "Activity name.")
        private String activityName;

        @ApiModelProperty(value = "Activity status.")
        private String activityStatus;

        @ApiModelProperty(value = "Activity code.")
        private String activityCode;

        @ApiModelProperty(value = "Group code.")
        private String groupCode;

        @ApiModelProperty("Activity begin time")
        private String activityBegin;//

        @ApiModelProperty("Activity end time")
        private String activityEnd;//

        @ApiModelProperty(value = "Activity warm-up begin time.")
        private String warmBegin;

        @ApiModelProperty(value = "Activity cool down time.")
        private String coolDown;

        @ApiModelProperty(value = "Ops create activity type")
        private String opsType;

        // Member level codes list.
        @ApiModelProperty(value = "Qualification list.")
        private List<Qualification> qualifications;

        @ApiModelProperty("Warm end time")
        private String warmEnd;//

        @ApiModelProperty("Activity URL")
        private String activityUrl;//

        @ApiModelProperty("Activity label")
        private String activityLabel;//

        @ApiModelProperty("Language results. 营销多语言信息")
        private  List<MarketingLanguageResult> languageResults;

        @ApiModelProperty(value = "Activity background image.")
        private String backgroundImage;

        @ApiModelProperty(value = "Ribbon image")
        private String ribbonImage;

        @ApiModelProperty(value = "Ribbon position")
        private String ribbonPosition;

        @ApiModelProperty(value = "Ribbon text")
        private String ribbonText;

        @ApiModelProperty(value = "Sponsors")
        private String sponsors;

        @ApiModelProperty(value = "Flash sale price")
        private BigDecimal flashPrice;

        @ApiModelProperty(value = "sku inventory")
        private Integer skuInventory;

        @ApiModelProperty(value = "max pre user")
        private Integer maxPerUser;

        @ApiModelProperty(value = "Activity period.")
        private ActivityPeriod activityPeriod;

        @ApiModelProperty(value = "pre sale pay type")
        private String preSalePayType;

        @ApiModelProperty(value = "shipping time")
        private String shippingTime;

        @ApiModelProperty(value = "import no")
        private String importNo;

        @ApiModelProperty(value = "member inventory")
        private Integer memberInventory;

        @ApiModelProperty(value = "Marketing group.")
        private MarketingGroupResult marketingGroup;

        @ApiModelProperty(value = "BoostSharing.")
        private BoostSharingResult boostSharing;

        @ApiModelProperty(value = "Product info.")
        private List<ProductPrice> products;

        @ApiModelProperty(value = "Create time.")
        private Date createTime;


    }

    @Data
    @ApiModel("ProductPrice")
    public static class ProductPrice implements Serializable {


        private static final long serialVersionUID = 5211971623987383793L;

        @ApiModelProperty(value = "Sku code")
        private String skuCode;
        @ApiModelProperty(value = "Sku name")
        private String skuName;
        @ApiModelProperty(value = "Sku quota")
        private Integer skuQuota;
        @ApiModelProperty(value = "Sku inventory")
        private Integer skuInventory;
        @ApiModelProperty(value = "Max per user")
        private Integer maxPerUser;
        @ApiModelProperty(value = "List price")
        private BigDecimal listPrice;
        @ApiModelProperty(value = "Sale price")
        private BigDecimal salePrice;
        @ApiModelProperty(value = "Flash price")
        private BigDecimal flashPrice;
        @ApiModelProperty(value = "Group leader price")
        private BigDecimal groupLeaderPrice;
        @ApiModelProperty(value = "Limit flag")
        private Integer limitFlag;
        @ApiModelProperty(value = "Product code")
        private String productCode;
        @ApiModelProperty(value = "Sku name")
        private String spuName;
        @ApiModelProperty(value = "Extent param")
        private String extendParam;

        //1 有限制 0 无限制
        @ApiModelProperty(value = "Max per user flag")
        private Integer maxPerUserFlag;

    }







}
