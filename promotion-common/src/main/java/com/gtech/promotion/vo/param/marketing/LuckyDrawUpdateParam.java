package com.gtech.promotion.vo.param.marketing;

import com.gtech.commons.utils.CheckUtils;
import com.gtech.promotion.exception.ErrorCodes;
import com.gtech.promotion.vo.bean.Qualification;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
public class LuckyDrawUpdateParam extends LuckyDrawCreateParam implements Serializable {

    private static final long serialVersionUID = 3719148526019900368L;

    @ApiModelProperty(value = "activity code.",example = "12556568987",required = true)
    private String activityCode;

    @ApiModelProperty(value = "List of eligibility (blank indicates unlimited)",required = true)
    private List<Qualification> qualifications;

    @Override
    public void validate() {

        CheckUtils.isNotBlank(this.activityCode, ErrorCodes.PARAM_EMPTY, "activityCode");
        super.validate();
    }
}
