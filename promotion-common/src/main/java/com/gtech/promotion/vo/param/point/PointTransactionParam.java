package com.gtech.promotion.vo.param.point;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;

@Data
public class PointTransactionParam {

	/**
	 * Domain code.
	 */
	@ApiModelProperty(value = "Domain code", required = true)
	@NotEmpty(message = "domainCode can not be empty")
	private String domainCode;

	/**
	 * Tenant code.
	 */
	@ApiModelProperty(value = "Tenant code", required = true)
	@NotEmpty(message = "tenantCode can not be empty")
	private String tenantCode;

    /**
     * Transaction serial number.
     */
	@ApiModelProperty(value = "Transaction serial number.")
    private String transactionSn;

    /**
     * Point account code. (UserCode or OrgCode)
     */
	@ApiModelProperty(value = "Point account code. (UserCode or OrgCode)")
	private String accountCode;

    /**
     * Point account type. (1-User 2-Organization)
     */
	@ApiModelProperty(value = "Point account type. (1-User 2-Organization)")
	private Integer accountType;

    /**
     * Point campaign code.
     */
	@ApiModelProperty(value = "Point campaign code.")
    private String campaignCode;

    /**
     * Point transaction type. (1-Increase points 2-Deduct points)
     */
	@ApiModelProperty(value = "Point transaction type. (1-Increase points 2-Deduct points)")
	private Integer transactionType;

    /**
     * Point transaction remarks.
     */
	@ApiModelProperty(value = "Point transaction remarks.")
    private String transactionRemarks;

    /**
     * Point spent/earned in the transaction.
     */
	@ApiModelProperty(value = "Point spent/earned in the transaction.")
    private Integer transactionAmount;

    /**
     * Transaction date. (yyyyMMddHHmmss)
     */
	@ApiModelProperty(value = "Transaction date. (yyyyMMddHHmmss)")
    private Long transactionDate;

    /**
     * Refer transaction serial number.
     */
	@ApiModelProperty(value = "Refer transaction serial number.")
    private String referTransactionSn;

    /**
     * Refer order number.
     */
	@ApiModelProperty(value = "Refer order number.")
    private String referOrderNumber;

	/**
     * Refer order number.
     */
	@ApiModelProperty(value = "create user.")
    private String createUser;

	/**
     * expiration
     */
	@ApiModelProperty(value = "expiration.")
    private String expiration;


	/**
     * balance.
     */
	@ApiModelProperty(value = "balance.")
    private Integer balance;

	/**
     * balance.
     */
	@ApiModelProperty(value = "operation.")
    private Integer operation;





    /**
     * Parameter validation.
     */
    public void validate() {
		// to do something
    }
}