package com.gtech.promotion.vo.result.flashsale;

import com.gtech.promotion.vo.bean.ExtImage;
import com.gtech.promotion.vo.bean.marketing.flashsale.FlashSaleProduct;
import com.gtech.promotion.vo.bean.marketing.flashsale.FlashSaleQualification;
import com.gtech.promotion.vo.bean.marketing.flashsale.FlashSaleStore;
import com.gtech.promotion.vo.result.marketing.MarketingFindResult;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class FlashSaleFindResult extends MarketingFindResult implements Serializable {

    private static final long serialVersionUID = -3247527630839068512L;

    @ApiModelProperty(value = "Ribbon image.")
    private String ribbonImage;

    @ApiModelProperty(value = "Position of ribbon image .")
    private String ribbonPosition;

    @ApiModelProperty(value = "Ribbon text.")
    private String ribbonText;

    @ApiModelProperty(value = "pre sale pay type")
    private String preSalePayType;

    @ApiModelProperty(value = "shipping time")
    private String shippingTime;

    @ApiModelProperty(value = "import no")
    private String importNo;

    @ApiModelProperty(value = "Qualification list. Empty means not limited.")
    private List<FlashSaleQualification> flashSaleQualifications;

    @ApiModelProperty(value = "Specified store list.")
    private List<FlashSaleStore> flashSaleStores;

    @ApiModelProperty(value = "product list.",required = true)
    private List<FlashSaleProduct> products;

    @ApiModelProperty(value = "Need Audit")
    private String needAudit = "0";

    @ApiModelProperty(value = "Need Different Operator")
    private String needDifferentOperator = "0";

    @ApiModelProperty(value = "audit user")
    private String auditUser;

    @ApiModelProperty(value = "commit user")
    private String commitUser;

    @ApiModelProperty(value = "Marketing group.")
    private MarketingGroupResult marketingGroup;

    @ApiModelProperty(value = "BoostSharing.")
    private BoostSharingResult boostSharing;

    @ApiModelProperty(value = "ExtImages.")
    private List<ExtImage> extImages;
}
