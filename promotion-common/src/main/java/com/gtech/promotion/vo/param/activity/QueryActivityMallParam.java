/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.vo.param.activity;

import com.gtech.commons.utils.CheckUtils;
import com.gtech.promotion.api.ApiConstants;
import com.gtech.promotion.exception.ErrorCodes;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("QueryActivityMallRequest")
public class QueryActivityMallParam implements Serializable {

    private static final long serialVersionUID = -1725470234584353314L;

    @ApiModelProperty(hidden = true)
    private String orgCode;

    @ApiModelProperty( value="Domain code.", example="DC0001",required=true)
    private String domainCode;

    @ApiModelProperty( value="Tenant code.", example="TC0001",required=true)
    private String tenantCode;

    @ApiModelProperty(value = "Language id")
    private String language;

    @ApiModelProperty(value = ApiConstants.QUALIFICATIONS)
    private Map<String, List<String>> qualifications;


    @ApiModelProperty( value="Store org code.", example="default")
    private String storeOrgCode;

    @ApiModelProperty( value="activity org code.", example="default")
    private String activityOrgCode;

    @ApiModelProperty(value = "Activity code.")
    private String activityCode;

    @ApiModelProperty(value = "activity type", example = "01")
    private String activityType;

    @ApiModelProperty(value = "Activity name.")
    private String activityName;

    @ApiModelProperty(value = "Activity start time")
    private String activityStartTime;
    @ApiModelProperty(value = "Activity end time.")
    private String activityEndTime;


    @ApiModelProperty(value = "Receive start time")
    private String receiveStartTime;
    @ApiModelProperty(value = "Receive end time.")
    private String receiveEndTime;


    @ApiModelProperty(value = "Custom conditions",example = " {\n" +
            "        \"credit_card\": \"356536\",\n" +
            "        \"bank_transfer\": \"bri_va\"\n" +
            "    }")
    private Map<String,String> customConditionMap;

    @ApiModelProperty(value = "Product type. 01 根据条件 02 根据（商品或sku）,productCondition有值，则为必填，默认01,",example = "01")
    private String conditionProductType ="01";

    @ApiModelProperty( value="Product condition.")
    private MallProductConditionParam productCondition;



    public void validate() {

        CheckUtils.isNotBlank(this.domainCode, ErrorCodes.PARAM_EMPTY, "domainCode");
        CheckUtils.isNotBlank(this.tenantCode, ErrorCodes.PARAM_EMPTY, "tenantCode");
        CheckUtils.isNotBlank(this.tenantCode, ErrorCodes.PARAM_EMPTY, "conditionProductType");



    }

}
