package com.gtech.promotion.vo.result.marketing;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(value = "ExportLuckyDrawResponse")
public class ExportLuckyDrawResult {

    /*抽奖时间
    抽奖人会员账号
    抽奖人昵称
    抽奖人手机号
    抽奖结果（包括券号/商品编码)*/
    @ApiModelProperty(value = "drawTime", name = "drawTime", example = "2020-01-01 00:00:00")
    private String drawTime;
    @ApiModelProperty(value = "memberCode", name = "memberCode", example = "memberCode")
    private String memberCode;
    @ApiModelProperty(value = "memberName", name = "memberName", example = "memberName")
    private String memberName;
    @ApiModelProperty(value = "memberMobile", name = "memberMobile", example = "memberMobile")
    private String memberMobile;
    @ApiModelProperty(value = "drawResult", name = "drawResult", example = "drawResult")
    private String drawResult;




}
