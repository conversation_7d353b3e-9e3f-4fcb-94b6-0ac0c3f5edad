/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.vo.param.coupon;

import com.gtech.commons.utils.CheckUtils;
import com.gtech.promotion.exception.ErrorCodes;
import com.gtech.promotion.vo.bean.Qualification;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import org.springframework.util.CollectionUtils;

import java.io.Serializable;
import java.util.List;

/**
 * 查询券活动详情-对外入参
 */
@Getter
@Setter
@ToString
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("FindCouponDetailRequest")
public class FindCouponDetailParam implements Serializable {

    private static final long serialVersionUID = -1766956312871643462L;

    @ApiModelProperty(value = "Domain code.",example = "D00001",required = true)
    private String domainCode;

    @ApiModelProperty(value = "Tenant code.",required = true)
    private String tenantCode;

    @ApiModelProperty(value = "Language id.")
    private String language;

    @ApiModelProperty(value = "Activity code.",required = true)
    private String activityCode;

    @ApiModelProperty(value = "Member code.",required = false)
    private String userCode;

    @ApiModelProperty(value = "Qualification list",required = false)
    private List<Qualification> qualifications;

    /**
     * @deprecated
     */
    @ApiModelProperty(value = "Deprecated. Member level code.",required = false)
    @Deprecated
    private String memberLevelCode; //NOSONAR

    /**
     * @deprecated
     */
    @ApiModelProperty(value = "Deprecated. Member label codes.",required = false)
    @Deprecated
    private String memberLabelCodes; //NOSONAR

    @ApiModelProperty(value = "Store organization code. (00-unlimited)",required = false)
    private String orgCode;

    @ApiModelProperty(value = "渠道编码",required = false)
    private String channelCode;

    /**
     * Parameter validation.
     */
    public void validate() {

        CheckUtils.isNotBlank(this.domainCode, ErrorCodes.PARAM_EMPTY, "domainCode");
        CheckUtils.isNotBlank(this.tenantCode, ErrorCodes.PARAM_EMPTY, "tenantCode");
        CheckUtils.isNotBlank(this.activityCode, ErrorCodes.PARAM_EMPTY, "activityCode");
        if (!CollectionUtils.isEmpty(qualifications)){
            for (Qualification qualification : qualifications) {
                qualification.validate();
            }
        }
    }
}
