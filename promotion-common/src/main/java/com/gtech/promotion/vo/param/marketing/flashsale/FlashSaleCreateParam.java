package com.gtech.promotion.vo.param.marketing.flashsale;

import com.gtech.commons.utils.StringUtil;
import com.gtech.promotion.checker.activity.TPromoIncentiveLimitedChecker;
import com.gtech.promotion.checker.activity.TPromoProductChecker;
import com.gtech.promotion.checker.flashsale.FlashSaleChecker;
import com.gtech.promotion.code.activity.IncentiveLimitedFlagEnum;
import com.gtech.promotion.exception.Check;
import com.gtech.promotion.vo.bean.ExtImage;
import com.gtech.promotion.vo.bean.IncentiveLimited;
import com.gtech.promotion.vo.bean.marketing.MarketingGroup;
import com.gtech.promotion.vo.bean.marketing.boostsharing.BoostSharing;
import com.gtech.promotion.vo.bean.marketing.flashsale.FlashSaleProduct;
import com.gtech.promotion.vo.bean.marketing.flashsale.FlashSaleQualification;
import com.gtech.promotion.vo.bean.marketing.flashsale.FlashSaleStore;
import com.gtech.promotion.vo.param.marketing.MarketingCreateParam;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
public class FlashSaleCreateParam extends MarketingCreateParam implements Serializable {

    private static final long serialVersionUID = -204585152273212073L;

    @ApiModelProperty(value = "Ribbon image.")
    private String ribbonImage;

    @ApiModelProperty(value = "Position of Ribbon image .")
    private String ribbonPosition;

    @ApiModelProperty(value = "Ribbon text.")
    private String ribbonText;

    @ApiModelProperty(value = "Upload sku importNo.")
    private String importNo;

    @ApiModelProperty(value = "1:full pay  2:partial pay")
    private String preSalePayType;

    @ApiModelProperty(value = "shipping time")
    private String shippingTime;

    @ApiModelProperty(value = "Qualification list. Empty means not limited.")
    private List<FlashSaleQualification> flashSaleQualifications;


    @ApiModelProperty(value = "Specified store list.")
    private List<FlashSaleStore> flashSaleStores;


    @ApiModelProperty(value = "product list.")
    private List<FlashSaleProduct> products;

    @ApiModelProperty(value = "Marketing group.")
    private MarketingGroup marketingGroup;

    @ApiModelProperty(value = "BoostSharing.")
    private BoostSharing boostSharing;

    @ApiModelProperty(value = "incentive Limited flag. 00 不限 01限制, 默认 00")
    private String incentiveLimitedFlag;


    @ApiModelProperty(value = "Incentive limited list. It can't be empty while incentiveLimitedFlag is '01'.")
    private List<IncentiveLimited> incentiveLimiteds;

    @ApiModelProperty(value = "ExtImage")
    private List<ExtImage> extImages;
    @Override
    public void validate() {

        super.validate();
        if (boostSharing == null) {
            Check.check(StringUtil.isBlank(importNo) && CollectionUtils.isEmpty(products), FlashSaleChecker.SKU_OR_UPLOAD);
        }else {
            boostSharing.validate();
        }

        if (!CollectionUtils.isEmpty(flashSaleQualifications)) {
            for (FlashSaleQualification flashSaleQualification : flashSaleQualifications) {
                flashSaleQualification.validate();
            }
        }

        if (!CollectionUtils.isEmpty(flashSaleStores)) {
            for (FlashSaleStore flashSaleStore : flashSaleStores) {
                flashSaleStore.validate();
            }
        }

        if (null != marketingGroup) {

            marketingGroup.validate();
        }

        if (null != boostSharing) {

            boostSharing.validate();
        }

        if (StringUtil.isEmpty(incentiveLimitedFlag)) {
            incentiveLimitedFlag = "00";
        }
        //限制通过
        Check.check(!IncentiveLimitedFlagEnum.exist(incentiveLimitedFlag), FlashSaleChecker.LIMITED_TYPE, incentiveLimitedFlag);

        if (IncentiveLimitedFlagEnum.YES.code().equals(incentiveLimitedFlag)) {

            Check.check(CollectionUtils.isEmpty(this.incentiveLimiteds), TPromoIncentiveLimitedChecker.NOT_NULL_LIMITATION_LIST);
            for (IncentiveLimited limited : this.incentiveLimiteds) {
                limited.validate();
            }
        }

//        营销价格 设置
//        市场价>= 销售价>=拼团价 >= 团长价
//        市场价>= 销售价>=秒杀价（预售价）
        if (CollectionUtils.isNotEmpty(products)) {

            for (FlashSaleProduct product : products) {

                BigDecimal listPrice = product.getListPrice();
                BigDecimal salePrice = product.getSalePrice();
                BigDecimal flashPrice = product.getFlashPrice();
                Check.check(null == listPrice, TPromoProductChecker.NO_NULL_LIST_PRICE);
                Check.check(null == salePrice, TPromoProductChecker.NO_NULL_SALE_PRICE);
                Check.check(null == flashPrice, TPromoProductChecker.NO_NULL_FLASH_PRICE);
                BigDecimal groupLeaderPrice = product.getGroupLeaderPrice();
//                Check.check(salePrice.compareTo(listPrice) > 0, TPromoProductChecker.SALE_PRICE);
                Check.check(flashPrice.compareTo(salePrice) > 0, TPromoProductChecker.FLASH_PRICE);

                if (null != marketingGroup) {
                    //1展示 0不展示
                    Integer showLeaderPrice = marketingGroup.getShowLeaderPrice();
                    if (null != marketingGroup.getShowLeaderPrice() && 1 == showLeaderPrice.intValue()) {
                        Check.check(null == groupLeaderPrice, TPromoProductChecker.NO_NULL_LEADER_PRICE);
                        Check.check(groupLeaderPrice.compareTo(flashPrice) > 0, TPromoProductChecker.LEADER_PRICE);

                    } else {
                        Check.check(null != groupLeaderPrice && groupLeaderPrice.floatValue() > 0, TPromoProductChecker.NO_LEADER_PRICE);
                    }
                }else {
                    Check.check(null != groupLeaderPrice, TPromoProductChecker.NO_LEADER_PRICE);
                }
            }
        }

        if (StringUtil.isBlank(this.getGroupCode())){
            this.setGroupCode(this.getOpsType());
        }

    }
}