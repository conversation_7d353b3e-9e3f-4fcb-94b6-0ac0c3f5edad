/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.vo.result.coupon;

import com.gtech.promotion.api.ApiConstants;
import com.gtech.promotion.vo.bean.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 查询券活动详情-对外出参
 */
@Getter
@Setter
@ToString
@ApiModel("FindCouponDetailResponse")
public class FindCouponDetailResult implements Serializable {

    private static final long serialVersionUID = -668129261285291856L;

    @ApiModelProperty("Activity code.")
    private String activityCode;

    @ApiModelProperty("Activity name")
    private String activityName;

    @ApiModelProperty("Activity desc")
    private String activityDesc;

    @ApiModelProperty("Activity remark")
    private String activityRemark;

    @ApiModelProperty("Activity label")
    private String activityLabel;

    @ApiModelProperty(ApiConstants.COUPON_TYPE)
    private String couponType;

    //优惠券面值
    @ApiModelProperty("Coupon face value")
    private BigDecimal faceValue;

    @ApiModelProperty(ApiConstants.FACE_UNIT)
    private String faceUnit;

    //条件值单位 01：金额  02：件
    @ApiModelProperty(ApiConstants.CONDITION_UNIT)
    private String conditionUnit;

    @ApiModelProperty("Condition value")
    private BigDecimal conditionValue;

    @ApiModelProperty("Activity URL")
    private String activityUrl;

    @ApiModelProperty("Each limit get")
    private Integer userLimitMax;

    //奖励类型：减金额(01)、打折扣(02)、单件固定金额(03)、组合固定金额(04)、包邮(05)、送赠品(06)、买A送A(07)、买A送B(08)
    @ApiModelProperty(value = ApiConstants.REWARD_TYPE)
    private String rewardType;

    @ApiModelProperty("Start receive time")
    private String startReceiveTime;

    @ApiModelProperty("End receive time")
    private String endReceiveTime;

//    @ApiModelProperty("领取状态：1：可领取；2：未开始；3：已结束；4：已领完；5：已被限领；6：会员等级不符合要求；7：活动已关闭")
    @ApiModelProperty("领取状态：1-available；2-has not started；3-is over；4-none；5-restricted；6-member-level doesn't match；7：closed")
    private int receiveStatus;

    @ApiModelProperty(ApiConstants.PRODUCT_SELECTION_TYPE)
    private String productSelectionType;

    @ApiModelProperty("Activity giveaway list.")
    private List<Giveaway> giveaways;

    @ApiModelProperty("Qualifications")
    private List<Qualification> qualifications;

    /**
     * @deprecated
     */
    @ApiModelProperty("Deprecated. List of Membership Levels")
    @Deprecated
    private List<MemberLevel> memberLevels; //NOSONAR

    /**
     * @deprecated
     */
    @ApiModelProperty("Deprecated. Membership Tag List")
    @Deprecated
    private List<MemberLabel> memberLabels; //NOSONAR

    //券活动的券可用时间
    @ApiModelProperty("Coupon activity coupon available time")
    private List<CouponReleaseTime> couponValidTime;

    @ApiModelProperty(value = "Activity warm-up begin time.")
    private String warmBegin;

    @ApiModelProperty(value = "Activity warm-up end time.")
    private String warmEnd;

    @ApiModelProperty(value = "Activity cool down time.")
    private String coolDown;

    @ApiModelProperty(value = "Activity background image.")
    private String backgroundImage;

    @ApiModelProperty(value = "Activity period.")
    private ActivityPeriod activityPeriod;
}
