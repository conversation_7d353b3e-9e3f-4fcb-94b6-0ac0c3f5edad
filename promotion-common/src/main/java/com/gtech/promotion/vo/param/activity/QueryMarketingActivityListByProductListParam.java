package com.gtech.promotion.vo.param.activity;


import com.gtech.commons.utils.CheckUtils;
import com.gtech.promotion.api.ApiConstants;
import com.gtech.promotion.code.marketing.ActivityTypeEnum;
import com.gtech.promotion.exception.ErrorCodes;
import com.gtech.promotion.vo.bean.ProductAttribute;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * 根据商品查询促销列表
 * 支持SPUList 和 SKUList 查询
 *  或者ActivityTypeList
 */

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("QueryMarketingActivityListByProductListRequest")
public class QueryMarketingActivityListByProductListParam implements Serializable {


    private static final long serialVersionUID = 617256235121327047L;



    @ApiModelProperty( value="Domain code.", example="DC0001",required=true)
    private String domainCode;

    @ApiModelProperty( value="Tenant code.", example="TC0001",required=true)
    private String tenantCode;
    @ApiModelProperty(value = "Language id")
    private String language;

    @ApiModelProperty(value = ApiConstants.QUALIFICATIONS)
    private Map<String, List<String>> qualifications;

    @ApiModelProperty(value = "activity type", example = "[04]")
    private List<String> activityTypeList;

    @ApiModelProperty(value = "product list", example = "[123,123]")
    private List<Product> productList;

    @ApiModelProperty(value = "warm begin time from")
    private String warmBeginFrom;



    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Product implements Serializable {

        private static final long serialVersionUID = 7643926171938415849L;

        @ApiModelProperty(value = "Store orgnization codes. Separated by commas. Empty value for all stores.")
        private String orgCodes;

        @ApiModelProperty(value = "Product category code list.")
        private List<String> categoryCodes;

        @ApiModelProperty(value = "Product brand code. If combineSkuCode is empty, brandCode can not be empty")
        private String brandCode;

        @ApiModelProperty(value = "Product code. If combineSkuCode is empty, productCode can not be empty")
        private String productCode;

        @ApiModelProperty(value = "Product sku code.")
        private String skuCode;

        @ApiModelProperty(value = "Combine product sku code. If productCode, skuCode, brandCode, categoryCodes and attributes are all empty, combineSkuCode can not be empty")
        private String combineSkuCode;

        @ApiModelProperty(value = "Product tag code.")
        private String productTag;

        @ApiModelProperty(value = "Product attribute information list.")
        private List<ProductAttribute> spuAttributes;

        @ApiModelProperty(value = "Attribute")
        private List<ProductAttribute> attributes;

        public void validate() {

            boolean singleProduct = StringUtils.isNotBlank(this.productCode) || StringUtils.isNotBlank(this.skuCode)
                    || CollectionUtils.isNotEmpty(this.categoryCodes) || CollectionUtils.isNotEmpty(this.attributes);
            boolean combineProduct = StringUtils.isNotBlank(this.combineSkuCode);
            CheckUtils.isTrue((singleProduct && !combineProduct) || (!singleProduct && combineProduct) , ErrorCodes.PARAM_SPECIFICATION_ERROR,
                    "If productCode, skuCode, categoryCodes and attributes are all empty, combineSkuCode can not be empty. "
                            + "One of the productCode, skuCode, categoryCode or attributes is not empty, combineSkuCode must be empty.");

        }
    }

    public void validate() {

        CheckUtils.isNotBlank(this.domainCode, ErrorCodes.PARAM_EMPTY, "domainCode");
        CheckUtils.isNotBlank(this.tenantCode, ErrorCodes.PARAM_EMPTY, "tenantCode");
        CheckUtils.isTrue(CollectionUtils.isNotEmpty(productList), ErrorCodes.PARAM_EMPTY, "productList");
        if (CollectionUtils.isEmpty(this.activityTypeList)) {
            activityTypeList = ActivityTypeEnum.getCodes();
        }
        for (QueryMarketingActivityListByProductListParam.Product product : productList) {
            product.validate();
        }

    }


}
