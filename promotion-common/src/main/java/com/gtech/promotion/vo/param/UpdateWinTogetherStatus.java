package com.gtech.promotion.vo.param;

import com.gtech.commons.utils.CheckUtils;
import com.gtech.promotion.api.ApiConstants;
import com.gtech.promotion.exception.ErrorCodes;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;

import java.io.Serializable;
import java.util.List;

/**
 * @Description :
 * <AUTHOR> gaojie.li
 * @Date : 2021/7/21 10:33
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class UpdateWinTogetherStatus implements Serializable {

    @ApiModelProperty( value="Domain code.", example="DC0001",required=true)
    private String domainCode;

    @ApiModelProperty( value="Tenant code.", example="TC0001",required=true)
    private String tenantCode;

    @ApiModelProperty( value="orgCode.", example="default",required=true)
    private String orgCode;

    @ApiModelProperty(value= ApiConstants.ACTIVITY_STATUS,required=true)
    private String activityStatus;

    @ApiModelProperty(value = "User code of the operator.", example="U00001", required = true)
    private String operateUser;

    @ApiModelProperty( value="activityCodes.",required=true)
    private List<String> activityCodes;

    public void validate(){
        CheckUtils.isNotBlank(this.domainCode, ErrorCodes.PARAM_EMPTY, "domainCode");
        CheckUtils.isNotBlank(this.tenantCode, ErrorCodes.PARAM_EMPTY, "tenantCode");
        CheckUtils.isNotBlank(this.activityStatus, ErrorCodes.PARAM_EMPTY, "status");
        CheckUtils.isNotBlank(this.operateUser, ErrorCodes.PARAM_EMPTY, "operateUser");
        CheckUtils.isTrue(CollectionUtils.isNotEmpty(this.activityCodes), ErrorCodes.PARAM_EMPTY, "activityCodes");
    }
}
