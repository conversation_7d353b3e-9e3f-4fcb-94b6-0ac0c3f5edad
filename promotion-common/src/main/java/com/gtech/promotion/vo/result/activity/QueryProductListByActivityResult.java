/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * <PERSON>ECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.vo.result.activity;

import com.gtech.promotion.api.ApiConstants;
import com.gtech.promotion.vo.bean.ActivityPeriod;
import com.gtech.promotion.vo.bean.Giveaway;
import com.gtech.promotion.vo.bean.ProductDetail;
import com.gtech.promotion.vo.bean.ProductScope;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 根据活动查商品 出参
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("QueryProductListByActivityResponse")
public class QueryProductListByActivityResult {

    @ApiModelProperty(value = ApiConstants.ACTIVITY_TYPE)
    private String activityType;

    @ApiModelProperty(value = "Activity name")
    private String activityName;

    @ApiModelProperty(value = "Activity label")
    private String activityLabel;

    @ApiModelProperty(value = "Activity desc")
    private String activityDesc;

    @ApiModelProperty(value = "Activity remark")
    private String activityRemark;

    @ApiModelProperty(value = ApiConstants.ACTIVITY_SORT)
    private String activitySort;//

    @ApiModelProperty(value = "Activity begin")
    private String activityBegin;

    @ApiModelProperty(value = "Activity end")
    private String activityEnd;

    @ApiModelProperty(value = ApiConstants.PRODUCT_SELECTION_TYPE)
    private String productSelectionType;

    @ApiModelProperty(value = "Product item scope type: 1-All scope 2-By spu in scope")
    private String itemScopeType;

    /**
     * @deprecated Will be deleted on 2020.08.18.
     */
    @Deprecated
    private String productType; // NOSONAR

    //商品信息列表
    @ApiModelProperty(value = "Product list")
    private List<ProductScope> products;

    //商品池数量
    @ApiModelProperty(value = "Seq count")
    private Integer seqCount;

//    商品SPU总记录数
    @ApiModelProperty(value = "Total records of commodity SPUs")
    private Long spuSkuTotal;

    //商品明细列表
    @ApiModelProperty(value = "Product detail list")
    private List<ProductDetail> productDetils;

    //赠品赠送最大限制数量
    @ApiModelProperty(value = "Maximum number of gifts")
    private String giftLimitMax;

    @ApiModelProperty(value = "Giveaways")
    private List<Giveaway> giveaways;

    @ApiModelProperty(value = "Activity warm-up begin time.")
    private String warmBegin;

    @ApiModelProperty(value = "Activity cool down time.")
    private String coolDown;

    @ApiModelProperty(value = "ops create Activity type")
    private String opsType;

    @ApiModelProperty(value = "Ribbon image.")
    private String ribbonImage;

    @ApiModelProperty(value = "Position of Ribbon image .")
    private String ribbonPosition;

    @ApiModelProperty(value = "Ribbon text.")
    private String ribbonText;

    @ApiModelProperty(value = "Activity period.")
    private ActivityPeriod activityPeriod;
}
