package com.gtech.promotion.vo.result.activity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * @Description :
 * <AUTHOR> gaojie.li
 * @Date : 2023/7/12 13:36
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("ActivityGroupRelationResponse")
public class ActivityGroupRelationResult implements Serializable {

    private static final long serialVersionUID = 6382703290001777839L;

    @ApiModelProperty(value = "租户编码", example = "100001")
    private String tenantCode;

    @ApiModelProperty(value = "Domain code",example = "DC0001")
    private String domainCode;

    @ApiModelProperty(value = "分组编码", example = "1231209")
    private String groupCode;

    @ApiModelProperty(value = "分组名称", example = "自定义满减分组")
    private String groupName;

    @ApiModelProperty(value = "优先级，数字越大越优先", example = "20")
    private Integer priority;

    @ApiModelProperty(value = "分组类型, 01:默认分组 02：自定义分组", example = "02")
    private String type;

    @ApiModelProperty(value = "互斥的分组编码列表")
    private List<ActivityGroupExclusionResult> relations;
}
