package com.gtech.promotion.vo.result.marketing;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(value = "ExportLuckyDrawTotalResponse")
public class ExportLuckyDrawResultTotal {

    /*奖品
    券号/商品编码
    商品名
    奖品总数
    已抽中数
    剩余数量*/
    @ApiModelProperty(value = "prize", name = "prize", example = "prize")
    private String prize;
    @ApiModelProperty(value = "prize code", name = "prize", example = "prize")
    private String prizeCode;
    @ApiModelProperty(value = "Product name", name = "prize", example = "prize")
    private String productName;
    @ApiModelProperty(value = "Prize num", name = "prize", example = "prize")
    private Integer prizeNum;
    @ApiModelProperty(value = "prize drawn PRIZE_NUM-PRIZE_INVENTORY", name = "prize", example = "prize")
    private Integer prizeDrawn;
    @ApiModelProperty(value = "prize", name = "prize", example = "prize")
    private Integer prizeInventory;




}
