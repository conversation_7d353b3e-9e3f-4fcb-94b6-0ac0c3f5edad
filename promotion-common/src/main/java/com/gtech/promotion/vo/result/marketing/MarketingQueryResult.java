package com.gtech.promotion.vo.result.marketing;

import com.gtech.promotion.api.ApiConstants;
import com.gtech.promotion.vo.bean.ActivityStore;
import com.gtech.promotion.vo.bean.ExtImage;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class MarketingQueryResult implements Serializable {

    private static final long serialVersionUID = -3283799111316112799L;

    private String orgCode;
    private String groupCode;

    @ApiModelProperty(value = "activity code.",example = "12556568987",required = true)
    private String activityCode;

    // 活动类型: 01-大转盘 02-砸金蛋
    @ApiModelProperty(value = "Activity type.01-Slyder Adventures 02-Egg frenzy",required = true)
    private String activityType;

    @ApiModelProperty(value= ApiConstants.ACTIVITY_STATUS)
    private String activityStatus;

    @ApiModelProperty(value = "Activity name",required = true)
    private String activityName;

    @ApiModelProperty(value = "Activity begin",required = true)
    private String activityBegin;

    @ApiModelProperty(value = "Activity end",required = true)
    private String activityEnd;

    @ApiModelProperty(value = "Activity url")
    private String activityUrl;

    @ApiModelProperty(value = "Activity sponsors",required = true)
    private String sponsors;

    @ApiModelProperty(value = "Activity create user code")
    private String createUser;
    @ApiModelProperty(value = "Activity create user first name")
    private String createUserFirstName;
    @ApiModelProperty(value = "Activity create user last name")
    private String createUserLastName;
    @ApiModelProperty(value = "Need Audit. 0:no 1:yes")
    private String needAudit;
    @ApiModelProperty(value = "Need Different Operator.  0:no 1:yes")
    private String needDifferentOperator;
    @ApiModelProperty(value = "audit user")
    private String auditUser;
    @ApiModelProperty(value = "commit user")
    private String commitUser;
    @ApiModelProperty(value = "warmBegin")
    private String warmBegin;
    @ApiModelProperty(value = "warmEnd")
    private String warmEnd;

    @ApiModelProperty(value = "Activity channel & store list.")
    private List<ActivityStore> stores;

    @ApiModelProperty(value = "Activity create time")
    private Date createTime;

    @ApiModelProperty(value = "ExtImages")
    private List<ExtImage> extImages;

}
