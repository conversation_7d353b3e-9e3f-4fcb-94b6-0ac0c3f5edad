package com.gtech.promotion.vo.param.growth.query;

import com.gtech.commons.page.PageParam;
import com.gtech.commons.utils.CheckUtils;
import com.gtech.promotion.exception.ErrorCodes;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;

@Getter
@Setter
@ToString
public class QueryGrowthTransactionParam extends PageParam implements Serializable {

    private static final long serialVersionUID = -1370905104549635718L;

    @ApiModelProperty(value = "Domain code",required = true)
    private String domainCode;

    @ApiModelProperty(value = "Tenant code",required = true)
    private String tenantCode;

    @ApiModelProperty(value = "Transaction serial number.")
    private String transactionSn;

    @ApiModelProperty(value = "Growth account code. (UserCode or OrgCode)")
    private String accountCode;

    @ApiModelProperty(value = "Growth account type. (1-User 2-Organization)")
    private Integer accountType;

    @ApiModelProperty(value = "Growth transaction type. (1-Increase points 2-Deduct points)")
    private Integer transactionType;

    @ApiModelProperty(value = " Transaction date. (yyyyMMddHHmmss)")
    private Long transactionDate;

    @ApiModelProperty(value = "Refer transaction serial number.")
    private String referTransactionSn;

    @ApiModelProperty(value = "Refer order number.")
    private String referOrderNumber;

    @ApiModelProperty(value = "Begin time(yyyy-MM-dd HH:mm:ss)")
    private String beginTime;

    @ApiModelProperty(value = "End time (yyyy-MM-dd HH:mm:ss)")
    private String endTime;

    @ApiModelProperty(value = "Origin")
    private Integer origin;
    /**
     * Parameter validation.
     */
    public void validate() {

        CheckUtils.isNotBlank(this.domainCode, ErrorCodes.PARAM_EMPTY, "domainCode");
        CheckUtils.isNotBlank(this.tenantCode, ErrorCodes.PARAM_EMPTY, "tenantCode");
    }

}