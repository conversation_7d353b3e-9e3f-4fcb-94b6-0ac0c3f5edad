package com.gtech.promotion.vo.param.marketing.flashsale;

import com.gtech.commons.utils.CheckUtils;
import com.gtech.commons.utils.StringUtil;
import com.gtech.promotion.api.ApiConstants;
import com.gtech.promotion.code.marketing.ActivityTypeEnum;
import com.gtech.promotion.exception.ErrorCodes;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class FlashSalePdpPriceParam implements Serializable {

    private static final long serialVersionUID = -30955046084273807L;

    @ApiModelProperty(value = "Domain code.",example = "DC0001",required = true)
    private String domainCode;

    @ApiModelProperty(value = "Tenant code.",example = "TC0001",required = true)
    private String tenantCode;

    @ApiModelProperty(value = "Language id")
    private String language;

    @ApiModelProperty(value = "Store orgnization code.",example = "O00001")
    private String orgCode;

    @ApiModelProperty(value = ApiConstants.QUALIFICATIONS)
    private Map<String, List<String>> qualifications;

    @ApiModelProperty(value = "Product sku code.")
    private String skuCode;

    @ApiModelProperty(value = "Flash sale activity code.", example = "87234943952324", required = true)
    private String activityCode;

    @ApiModelProperty(value = "activity type", example = "04")
    private String activityType = ActivityTypeEnum.FLASH_SALE.code();

    @ApiModelProperty(name = "combineSkuCode",value = ApiConstants.COMBINESKUCODE)
    private String combineSkuCode;

    public void validate() {

        CheckUtils.isNotBlank(this.domainCode, ErrorCodes.PARAM_EMPTY, "domainCode");
        CheckUtils.isNotBlank(this.tenantCode, ErrorCodes.PARAM_EMPTY, "tenantCode");
        CheckUtils.isNotBlank(this.activityCode, ErrorCodes.PARAM_EMPTY, "activityCode");
        CheckUtils.isTrue(StringUtil.isNotBlank(this.skuCode) || StringUtil.isNotBlank(this.combineSkuCode), ErrorCodes.PARAM_LEAST_ONE, "skuCode\\combineSkuCode");
    }
}
