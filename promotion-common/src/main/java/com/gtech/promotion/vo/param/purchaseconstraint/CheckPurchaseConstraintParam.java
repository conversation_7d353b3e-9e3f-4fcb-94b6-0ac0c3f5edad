package com.gtech.promotion.vo.param.purchaseconstraint;

import com.gtech.commons.utils.CheckUtils;
import com.gtech.promotion.api.ApiConstants;
import com.gtech.promotion.exception.ErrorCodes;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import javax.validation.constraints.NotEmpty;
import java.util.List;
import java.util.Map;


@Data
@ToString
@NoArgsConstructor
@AllArgsConstructor
@Builder
@ApiModel("CheckPurchaseConstraintRequest")
public class CheckPurchaseConstraintParam {
    @ApiModelProperty(value = "Tenant code", required = true)
    @NotEmpty(message = "tenantCode can not be empty")
    private String tenantCode;

    @ApiModelProperty(value = "Domain code", required = true)
    private String domainCode;

    @ApiModelProperty( value="orgCode.", example="default",required=true)
    private String orgCode;

    @ApiModelProperty(value = "Member Code",required = true)
    private String memberCode;

    @ApiModelProperty(value = "Channel code")
    private String channelCode;

    @ApiModelProperty(value = ApiConstants.QUALIFICATIONS)
    private Map<String, List<String>> qualifications;

    @ApiModelProperty(value = "Check purchase constraint products")
    private List<CheckPurchaseConstraintProductParam> checkPurchaseConstraintProducts;

    @ApiModelProperty(value = "Tourist flag")
    private Integer touristFlag;
    @ApiModelProperty(value = "Order code",notes = "Order code.")
    private String orderCode;
    @ApiModelProperty(value = "Use Increment",notes = "default false.")
    private Boolean useIncrement;
    @ApiModelProperty(value = "Rule Type List",notes = "default false.")
    private List<Integer> pcRuleTypeList;
    @ApiModelProperty(value = "Custom Map")
    private Map<String, String> customMap;

    public void validate(){
        CheckUtils.isNotBlank(this.domainCode, ErrorCodes.PARAM_EMPTY, "domainCode");
        CheckUtils.isNotBlank(this.tenantCode, ErrorCodes.PARAM_EMPTY, "tenantCode");
        CheckUtils.isNotBlank(this.memberCode, ErrorCodes.PARAM_EMPTY, "memberCode");
        CheckUtils.isNotNull(this.touristFlag, ErrorCodes.PARAM_EMPTY, "touristFlag");
        for(CheckPurchaseConstraintProductParam productParam : checkPurchaseConstraintProducts){
            productParam.validate();
        }
    }
}
