package com.gtech.promotion.vo.result.growth;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.util.Date;

@Getter
@Setter
@ToString
public class GrowthAccountResult implements Serializable {

    private static final long serialVersionUID = -604272648921423898L;

    @ApiModelProperty(value = "Tenant code")
    private String tenantCode;

    @ApiModelProperty(value = "Growth account code.")
    private String growthAccountCode;

    @ApiModelProperty(value = "account code. (UserCode or OrgCode)",required = true)
    private String accountCode;

    @ApiModelProperty(value = "Growth account type. (1-User 2-Organization)",required = true)
    private Integer accountType;

    @ApiModelProperty(value = "Growth account description.")
    private String accountDesc;

    @ApiModelProperty(value = "Latest account points.",required = true)
    private String accountBalance;

    @ApiModelProperty(value = "Account grade.")
    private Integer accountGrade;

    @ApiModelProperty(value = "Growth account status.(0-Inactive 1-Active)")
    private Integer status;

    @ApiModelProperty(value = "Extends parameters. (JSON String)")
    private String extParams;

    @ApiModelProperty(value = "valid Begin Time")
    private Date validBeginTime;

    @ApiModelProperty(value = "valid Begin Time")
    private String validBeginTimeString;

    @ApiModelProperty(value = "valid Begin Time")
    private Date validEndTime;

    @ApiModelProperty(value = "valid Begin Time")
    private String validEndTimeString;

    @ApiModelProperty(value = "Create Time")
    private Date createTime;

    @ApiModelProperty(value = "Create Time")
    private String createTimeString;

    @ApiModelProperty(value = "Create User")
    private String createUser;

    @ApiModelProperty(value = "Create Time")
    private Date updateTime;

    @ApiModelProperty(value = "Update Time")
    private String updateTimeString;

    @ApiModelProperty(value = "Update User")
    private String updateUser;


}