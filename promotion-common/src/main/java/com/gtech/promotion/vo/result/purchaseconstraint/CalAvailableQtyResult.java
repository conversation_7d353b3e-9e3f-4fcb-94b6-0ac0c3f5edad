package com.gtech.promotion.vo.result.purchaseconstraint;

import io.swagger.annotations.ApiModel;
import lombok.*;

import java.util.ArrayList;

/**
 * 查询限购可用数量返回结果
 */
@Data
@ToString
@NoArgsConstructor
@AllArgsConstructor
@Builder
@ApiModel("CalAvailableQtyResponse")
public class CalAvailableQtyResult {

    public static final Integer UN_LIMIT = -1;

    public static final Integer NO_QTY = 0;

    /**
     * sku编码
     */
    private String skuCode;
    private String spuCode;

    /**
     * 是否命中限购
     */
    private Boolean hitPurchaseConstraint;

    /**
     * 命中限购后的可购买数量
     * -1 无限制
     * 0 不可购买
     * >0 可购买数量
     */
    private Integer availableQty;

    /**
     * 是否是优先购买验证失败,默认false
     */
    private Boolean firstRefusalValidFail;

    /**
     * 命中的限购详细信息
     */
    private FindPurchaseConstraintResult purchaseConstraint;

    /**
     * 重写获取purchaseConstraint属性，清理掉暂时不需要的数据
     * @return
     */
    public FindPurchaseConstraintResult getPurchaseConstraint(){
        if(null != purchaseConstraint){
            purchaseConstraint.setProductDetailBlackList(new ArrayList<>());
            purchaseConstraint.setActivityPeriod(null);
            purchaseConstraint.setChannelStores(new ArrayList<>());
            purchaseConstraint.setProductDetailBlackList(new ArrayList<>());
            if(!firstRefusalValidFail) {
                purchaseConstraint.setQualifications(new ArrayList<>());
            }
            return purchaseConstraint;
        }
        return null;
    }
}
