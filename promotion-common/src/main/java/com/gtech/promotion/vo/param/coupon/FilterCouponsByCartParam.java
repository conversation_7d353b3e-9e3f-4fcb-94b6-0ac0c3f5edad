/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.vo.param.coupon;

import com.gtech.promotion.vo.param.ShoppingCartParam;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;


/**
 *
 * <AUTHOR>
 * @Date 2020-02-08
 */
@Getter
@Setter
@ToString
@ApiModel("FilterCouponsByCartRequest")
public class FilterCouponsByCartParam extends ShoppingCartParam {

    private static final long serialVersionUID = -6002613093977959795L;

    //自定义选择查优惠券的 打折券，满减券，满减码，兑换券 其中的几种券
    @ApiModelProperty(value="OPS Type List.201-product Discount,202-order discount(coupon),203- order discount(code),204- voucher")
    private List<String> opsTypeList;
}
