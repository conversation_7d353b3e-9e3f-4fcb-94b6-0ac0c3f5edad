package com.gtech.promotion.vo.result.flashsale;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class ActivityLanguageResult {

    @ApiModelProperty(value = "Activity name")
    private String activityName;

    @ApiModelProperty(value = "Activity Label")
    private String activityLabel;

    @ApiModelProperty(value = "Activity Desc")
    private String activityDesc;

    @ApiModelProperty(value = "Activity Short Desc")
    private String activityShortDesc;
}
