package com.gtech.promotion.vo.param.activity;

import com.gtech.commons.page.PageParam;
import com.gtech.commons.utils.CheckUtils;
import com.gtech.promotion.api.ApiConstants;
import com.gtech.promotion.code.activity.ActivityStatusEnum;
import com.gtech.promotion.exception.ErrorCodes;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("QueryPromoListByStoreRequest")
public class QueryPromoListByStoreParam extends PageParam {

    @ApiModelProperty(value = "Domain code.",example = "D00001",required = true)
    private String domainCode;

    @ApiModelProperty(value = "Tenant code.",example = "T00001",required = true)
    private String tenantCode;

    @ApiModelProperty(value = "Store orgnization code.",example = "OC001",required = true)
    private String orgCode;

    @ApiModelProperty(value = "Language id",required = true)
    private String language;

    @ApiModelProperty(value = ApiConstants.ACTIVITY_STATUS + ", default 04")
    private String activityStatus = ActivityStatusEnum.EFFECTIVE.code();

    @ApiModelProperty(value = ApiConstants.ACTIVITY_TYPE)
    private String activityType;

    public void validate() {
        CheckUtils.isNotBlank(this.domainCode, ErrorCodes.PARAM_EMPTY, "domainCode");
        CheckUtils.isNotBlank(this.tenantCode, ErrorCodes.PARAM_EMPTY, "tenantCode");
        CheckUtils.isNotBlank(this.orgCode, ErrorCodes.PARAM_EMPTY, "orgCode");
        CheckUtils.isNotBlank(this.language, ErrorCodes.PARAM_EMPTY, "language");
    }
}
