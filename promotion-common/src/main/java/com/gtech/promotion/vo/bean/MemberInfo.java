/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * <PERSON>ECH MAKES NO REPRESENTATIONS OR WA<PERSON>RANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.vo.bean;

import com.gtech.commons.utils.CheckUtils;
import com.gtech.promotion.exception.ErrorCodes;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import org.springframework.util.CollectionUtils;

import java.io.Serializable;
import java.util.List;


/**
 * MemberInfo
 *
 * <AUTHOR>
 * @Date 2020-02-21
 */
@Getter
@Setter
@ToString
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("MemberInfo")
public class MemberInfo implements Serializable {

    private static final long serialVersionUID = 6076081377498728398L;

    @ApiModelProperty(value = "Member user code.", required = true)
    private String userCode;

    @ApiModelProperty(value = "Qualification list")
    private List<Qualification> qualifications;

    /**
     * @deprecated
     */
    @ApiModelProperty(value = "Deprecated. Member level code.",required = false)
    @Deprecated
    private String memberLevelCode; //NOSONAR

    /**
     * @deprecated
     */
    @ApiModelProperty(value = "Deprecated. Member label codes.",required = false)
    @Deprecated
    private String memberLabelCodes; //NOSONAR

    /**
     * Parameter validation.
     */
    public void validate() {

        CheckUtils.isNotBlank(this.userCode, ErrorCodes.PARAM_EMPTY, "userCode");
        if (!CollectionUtils.isEmpty(qualifications)){
            for (Qualification qualification : qualifications) {
                qualification.validate();
            }
        }
    }
}
