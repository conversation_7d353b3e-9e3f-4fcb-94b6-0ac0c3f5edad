package com.gtech.promotion.vo.param.marketing;

import com.gtech.commons.utils.CheckUtils;
import com.gtech.promotion.exception.ErrorCodes;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;

import java.io.Serializable;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("FilterNoRightOfFirstRefusalRequest")
public class FilterNoRightOfFirstRefusalParam {


    @ApiModelProperty(value = "Domain code",required = true)
    String domainCode;
    @ApiModelProperty(value = "Tenant code",required = true)
    String tenantCode;
    @ApiModelProperty(value = "User code",required = true)
    String userCode;
    @ApiModelProperty(value = "Language")
    String language;
    @ApiModelProperty(value = "CartItems",required = true)
    List<CartItem> cartItems;
    @ApiModelProperty(value = "Order ID")
    String orderId;
    @ApiModelProperty(value = "Write Off")
    Boolean writeOff = false;


    @Data
    @ApiModel("FilterNoRightOfFirstRefusalRequest#CartItem")
    public static class CartItem implements Serializable {
        private static final long serialVersionUID = 7302691737560617650L;
        @ApiModelProperty(value = "Product sku code.")
        private String skuCode;

        @ApiModelProperty(value = "Product code.")
        private String productCode;

        @ApiModelProperty(value = "Org code.")
        private String orgCode;

        //购买数量（该sku的总数量）
        @ApiModelProperty(value = "Quantity purchased (total number of skUs)")
        private Integer quantity;


    }

    public void validate() {
        CheckUtils.isNotBlank(this.domainCode, ErrorCodes.PARAM_EMPTY, "domainCode");
        CheckUtils.isNotBlank(this.tenantCode, ErrorCodes.PARAM_EMPTY, "tenantCode");
        CheckUtils.isNotBlank(this.userCode, ErrorCodes.PARAM_EMPTY, "userCode");
        CheckUtils.isTrue(CollectionUtils.isNotEmpty(cartItems), ErrorCodes.PARAM_EMPTY, "cartItems");
    }


}
