package com.gtech.promotion.vo.bean;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;
@Data
@ApiModel("GiveawaySettings")
public class GiveawaySettings {
    /**
     * {
     *   "giveawayPoolEnable": "{{giveawayPoolEnable}}",
     *   // 是否启用赠品池 1是 2否
     *   "giveawayRules": [
     *     // 赠送规则
     *     {
     *       "giveawayMethod": "{{giveawayMethod}}",
     *       // 赠送方式 1全部 2 部分
     *       "giveawayChooseQty": "{{giveawayChooseQty}}",
     *       // 可选数量 giveawayMethod等于2 必填
     *       "giveawaySortRule": "{{giveawaySortRule}}",
     *       // 赠品排序规则,越小靠前 1有序 2随机
     *       "rankParam": 1
     *       // 层级
     *     }
     *   ]
     * }
     */
    @ApiModelProperty(value = "是否启用赠品池 1是 2否")
    private String giveawayPoolEnable;
    @ApiModelProperty(value = "赠送规则")
    private List<GiveawayRule> giveawayRules;



}
