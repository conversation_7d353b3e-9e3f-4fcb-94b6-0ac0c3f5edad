package com.gtech.promotion.vo.result.purchaseconstraint;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

@Data
@ToString
@NoArgsConstructor
@AllArgsConstructor
@Builder
@ApiModel("CheckPurchaseConstraintResponse")
public class CheckPurchaseConstraintProductResult {
    /**
     * sku编码
     */
    @ApiModelProperty(value = "Product sku code.")
    private String skuCode;

    /**
     * spu编码
     */
    @ApiModelProperty(value = "Product code.")
    private String productCode;

    /**
     * 主商品编码
     */
    @ApiModelProperty(value = "Main product no.")
    private String mainProductNo;
}
