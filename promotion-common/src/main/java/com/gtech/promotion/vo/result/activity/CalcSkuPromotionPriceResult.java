/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.vo.result.activity;

import com.gtech.promotion.vo.bean.ActivityPeriod;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 单品活动活动价出参对象
 */
@Getter
@Setter
@ToString
@ApiModel("CalcSkuPromotionPriceResponse")
public class CalcSkuPromotionPriceResult implements Serializable {

    private static final long serialVersionUID = 4935889240530230220L;

    @ApiModelProperty(value = "Activity code.", example = "32424234234")
    private String activityCode;

    @ApiModelProperty(value = "Activity name.", example = "name")
    private String activityName;

    @ApiModelProperty("Activity start time. (yyyyMMddHHmmss)")
    private String activityStartTime;

    @ApiModelProperty("Activity end time. (yyyyMMddHHmmss)")
    private String activityEndTime;

    //活动价
    @ApiModelProperty(value = "Promotion price", example = "80.00")
    private BigDecimal promotionPrice;

    @ApiModelProperty(value = "Activity warm-up begin time.")
    private String warmBegin;

    @ApiModelProperty(value = "Activity cool down time.")
    private String coolDown;

    @ApiModelProperty(value = "ops create Activity type")
    private String opsType;

    @ApiModelProperty(value = "Ribbon image.")
    private String ribbonImage;

    @ApiModelProperty(value = "Position of Ribbon image .")
    private String ribbonPosition;

    @ApiModelProperty(value = "Ribbon text.")
    private String ribbonText;

    @ApiModelProperty(value = "Activity period.")
    private ActivityPeriod activityPeriod;
}