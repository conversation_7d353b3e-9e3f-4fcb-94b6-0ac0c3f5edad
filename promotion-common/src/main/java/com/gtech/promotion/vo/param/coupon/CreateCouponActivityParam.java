/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * <PERSON>ECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.vo.param.coupon;

import com.gtech.commons.utils.CheckUtils;
import com.gtech.commons.utils.StringUtil;
import com.gtech.promotion.code.coupon.CouponTypeEnum;
import com.gtech.promotion.exception.ErrorCodes;
import com.gtech.promotion.utils.QuantityCheckUtil;
import com.gtech.promotion.vo.param.activity.CreatePromoActivityParam;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 
 *
 * <AUTHOR>
 * @Date 2020-02-11
 */
@Getter
@Setter
@ToString
@ApiModel("CreateCouponActivityRequest")
public class CreateCouponActivityParam extends CreatePromoActivityParam {

    private static final long serialVersionUID = -2448815901876754472L;
    public static final String PROMOTION_CODE = "promotionCode";

    @ApiModelProperty(value = "Coupon type: 01-General coupon 02-Anonymous coupon 03-Single fixed promotion code.",required = true)
    private String couponType;

    @ApiModelProperty(value = "Single user limited max. 0 means unlimited")
    private Integer userLimitMax;

    @ApiModelProperty(value = "Single user day limited max. 0 means unlimited")
    private Integer userLimitMaxDay;

    @ApiModelProperty(value = "Activity total quantity: 0 means unlimited",required = true)
    private String totalQuantity;

    @ApiModelProperty(value = "Single promotional code while couponType=03.")
    private String promotionCode;

    @ApiModelProperty(value = "Promo password.")
    private String promoPassword;

    /**
     * Parameter validation.
     */
    @Override
    public void validate() {

        CheckUtils.isNotBlank(this.couponType, ErrorCodes.PARAM_EMPTY, "couponType");
        if (couponType.equals(CouponTypeEnum.PROMOTION_CODE.code())){
            CheckUtils.isNotBlank(this.promotionCode, ErrorCodes.PARAM_EMPTY, PROMOTION_CODE);
            CheckUtils.isTrue(this.promotionCode.length() <= 32, ErrorCodes.PARAM_LENGTH, PROMOTION_CODE);
            if (StringUtil.isNotEmpty(promoPassword)){
                CheckUtils.isTrue(!promoPassword.contains(","), ErrorCodes.PROMO_PASSWORD, "promoPassword");
            }
            CheckUtils.isTrue(!promotionCode.contains(","), ErrorCodes.PROMO_PASSWORD, PROMOTION_CODE);
        }
        CheckUtils.isNotBlank(this.totalQuantity, ErrorCodes.PARAM_EMPTY, "totalQuantity");
        
        if (StringUtil.isBlank(totalQuantity)){
            this.totalQuantity = "0";
        }

        QuantityCheckUtil.checkParamInDTO(totalQuantity, this.userLimitMax);
        QuantityCheckUtil.checkParam(totalQuantity, this.couponType);

        super.validate();
    }
}
