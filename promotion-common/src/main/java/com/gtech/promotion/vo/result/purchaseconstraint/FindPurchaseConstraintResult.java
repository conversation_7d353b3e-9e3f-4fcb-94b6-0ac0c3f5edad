package com.gtech.promotion.vo.result.purchaseconstraint;

import com.gtech.commons.utils.BeanCopyUtils;
import com.gtech.commons.utils.StringUtil;
import com.gtech.promotion.api.ApiConstants;
import com.gtech.promotion.code.PromotionConstants;
import com.gtech.promotion.code.activity.ProductTypeEnum;
import com.gtech.promotion.vo.bean.*;
import com.gtech.promotion.vo.param.activity.CustomCondition;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;
import org.apache.commons.collections4.CollectionUtils;

import java.util.Date;
import java.util.List;

@Data
@ToString
@ApiModel("FindPurchaseConstraintResponse")
public class FindPurchaseConstraintResult {

    /**
     * 租户号
     */
    @ApiModelProperty(value = "Tenant code")
    private String tenantCode;

    /**
     * 业务域
     */
    @ApiModelProperty(value = "Domain code")
    private String domainCode;

    /**
     * 组织编号
     */
    @ApiModelProperty(value = "Org code")
    private String orgCode;

    /**
     * 限购Code
     */
    @ApiModelProperty(value = "Purchase constraint code")
    private String purchaseConstraintCode;

    /**
     * 限购名称
     */
    @ApiModelProperty(value = "Purchase constraint name")
    private String purchaseConstraintName;

    /**
     * 限购开始时间
     */
    @ApiModelProperty(value = "Purchase constraint start time")
    private Date purchaseConstraintStartTime;

    /**
     * 限购结束时间
     */
    @ApiModelProperty(value = "Purchase constraint end time")
    private Date purchaseConstraintEndTime;

    /**
     * 状态
     * @see com.gtech.promotion.code.purchaseconstraint.PurchaseConstraintStatusEnum
     */
    @ApiModelProperty(value = "Purchase constraint status")
    private String purchaseConstraintStatus;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "Purchase constraint create time")
    private Date createTime;

    /**
     * 更新时间
     */
    @ApiModelProperty(value = "Purchase constraint update time")
    private Date updateTime;

    /**
     * 创建人
     */
    @ApiModelProperty(value = "Purchase constraint create user")
    private String createUser;

    /**
     * 更新人
     */
    @ApiModelProperty(value = "Purchase constraint update user")
    private String updateUser;

    /**
     * 周期类型：00-全时段 01-自定义
     */
    @ApiModelProperty(value = "Purchase constraint period type")
    private String periodType;

    /**
     * 商品范围正反选：01-正选；02-反选
     */
    @ApiModelProperty(value = "Purchase constraint product selection type")
    private String productSelectionType;

    /**
     * Product item scope type: 1-All scope 2-By spu in scope
     */
    @ApiModelProperty(value = "Purchase constraint product item scope type")
    private Integer itemScopeType;

    /**
     * 店铺范围：00-全店铺 01-自定义
     */
    @ApiModelProperty(value = "Purchase constraint product store type")
    private String storeType;

    /**
     * 审核人
     */
    @ApiModelProperty(value = "Purchase constraint product audit user")
    private String auditUser;

    /**
     * 是否开启会员优先可见
     */
    @ApiModelProperty(value = "Purchase constraint customer right first refusal")
    private Integer firstRefusal;

    /**
     * 限购渠道及店铺
     */
    @ApiModelProperty(value = "Purchase constraint chanel store list")
    private List<ActivityStore> channelStores;

    /**
     * 商品范围列表
     */
    @ApiModelProperty(value = "Purchase constraint product list")
    private List<ProductScope> products;

    /**
     * 商品明细列表
     */
    @ApiModelProperty(value = "Purchase constraint product detail list")
    private List<ProductDetail> productDetails;

    /**
     * 商品黑名单列表
     */
    @ApiModelProperty(value = "Purchase constraint product detail black list")
    private List<ProductDetail> productDetailBlackList;

    /**
     * Qualification list. Empty means not limited.
     */
    @ApiModelProperty(value = "Qualification list. Empty means not limited.")
    private List<Qualification> qualifications;


    /**
     * Activity period.
     */
    @ApiModelProperty(value = "Activity period.")
    private ActivityPeriod activityPeriod;

    /**
     * 限购规则列表
     */
    @ApiModelProperty(value = "Purchase constraint rule list.")
    private List<PurchaseConstraintRule> purchaseConstraintRuleList;

    /**
     * 描述
     */
    @ApiModelProperty(value = "Purchase constraint description.")
    private String description;

    /**
     * 优先级
     */
    @ApiModelProperty(value = "Purchase constraint priority.")
    private Integer priority;

    /**
     * 高级价格限购标识，默认0，非高级价格限购
     */
    @ApiModelProperty(value = "Price setting.", example = "0")
    private Integer priceSetting;


    @ApiModelProperty(value = "Custom conditions.")
    private String customCondition;
    @ApiModelProperty(value = "Custom conditions.")
    private String customRule;

    @ApiModelProperty(value = "Custom conditions list.")
    private List<CustomCondition> customConditionsList;

    @ApiModelProperty(value = "Custom rule list.")
    private List<CustomCondition> customRulesList;

    @ApiModelProperty(ApiConstants.PRODUCT_TYPE)
    public String getConditionProductType() {

        if (CollectionUtils.isNotEmpty(ProductDetail.getProductScopeBySeq(BeanCopyUtils.jsonCopyList(productDetails, ProductDetail.class), 1))) {
            return ProductTypeEnum.CUSTOM_PRODUCT.code();
        } else {
            List<ProductScope> productScopeBySeq = ProductScope.getProductScopeBySeq(products, 1);
            if (CollectionUtils.isNotEmpty(productScopeBySeq)) {
                if (productScopeBySeq.size() == 1
                        && CollectionUtils.isEmpty(productScopeBySeq.get(0).getAttributes())
                        && CollectionUtils.isEmpty(productScopeBySeq.get(0).getSpuAttributes())
                        && PromotionConstants.UNLIMITED.equals(productScopeBySeq.get(0).getBrandCode())
                        && PromotionConstants.UNLIMITED.equals(productScopeBySeq.get(0).getCategoryCode())
                        && StringUtil.isBlank(productScopeBySeq.get(0).getProductTag())
                ){
                    return ProductTypeEnum.ALL.code();
                }
                return ProductTypeEnum.CUSTOM_RANGE.code();
            } else {
                return ProductTypeEnum.ALL.code();
            }
        }
    }
}
