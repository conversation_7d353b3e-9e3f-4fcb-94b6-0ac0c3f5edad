package com.gtech.promotion.vo.param.activity;

import com.gtech.promotion.api.ApiConstants;
import com.gtech.promotion.vo.bean.Giveaway;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * @Description :
 * <AUTHOR> gaojie.li
 * @Date : 2021/6/15 11:04
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("ItemRelationActivityRequest")
public class ItemRelationActivity implements Serializable {


    @ApiModelProperty(value = "Activity code")
    private String activityCode;

    @ApiModelProperty(value = "Whether the activity meets")
    private Boolean effectiveFlag;

    @ApiModelProperty(value = "Coupon code")
    private String couponCode;

    @ApiModelProperty(value = ApiConstants.PROMO_SCOPE)
    private String promoScope;//

    @ApiModelProperty(value = "Activity name")
    private String activityName;

    @ApiModelProperty(value = "Activity label")
    private String activityLabel;

    @ApiModelProperty(value = ApiConstants.ACTIVITY_TYPE)
    private String activityType;

    @ApiModelProperty(value = "The total price of the commodity item before the calculation of the activity")
    private BigDecimal beforeAmount;//

    @ApiModelProperty(value = "The total price of the commodity item after the calculation of the activity")
    private BigDecimal afterAmount;//

    @ApiModelProperty(value = "Maximum number of gifts")
    private String giftLimitMax;//

    @ApiModelProperty(value = "Giveaways list")
    private List<Giveaway> giveaways;//

}
