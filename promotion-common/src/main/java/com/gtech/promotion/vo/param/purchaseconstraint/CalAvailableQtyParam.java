package com.gtech.promotion.vo.param.purchaseconstraint;

import com.gtech.commons.utils.CheckUtils;
import com.gtech.promotion.api.ApiConstants;
import com.gtech.promotion.exception.ErrorCodes;
import com.gtech.promotion.vo.bean.ProductAttribute;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import javax.validation.constraints.NotEmpty;
import java.util.List;
import java.util.Map;

@Data
@ToString
@NoArgsConstructor
@AllArgsConstructor
@Builder
@ApiModel("CalAvailableQtyRequest")
public class CalAvailableQtyParam {

    @ApiModelProperty(value = "Tenant code", required = true)
    @NotEmpty(message = "tenantCode can not be empty")
    private String tenantCode;

    @ApiModelProperty(value = "Domain code", required = true)
    private String domainCode;

    @ApiModelProperty( value="orgCode.", example="default",required=true)
    private String orgCode;

    @ApiModelProperty( value="Member code.", example="default")
    private String memberCode;

    @ApiModelProperty(value = ApiConstants.QUALIFICATIONS)
    private Map<String, List<String>> qualifications;

    @ApiModelProperty(value = "Tourist flag",example = "0")
    private Integer touristFlag;

    @Deprecated
    @ApiModelProperty(value = "Calculate available qty product info")
    private CalAvailableQtyProductParam calAvailableQtyProduct;

    @ApiModelProperty(value = "SKU product map", notes = "key=skuCode, value=product list")
    private Map<String, CalAvailableQtyProductParam> skuProductMap;

    @ApiModelProperty(value = "SKU attribute map", notes = "key=skuCode, value=sku attribute list")
    private Map<String, List<ProductAttribute>> skuAttributeMap;

    @ApiModelProperty(value = "Custom Map")
    private Map<String, String> customMap;


    public void validate(){
        CheckUtils.isNotBlank(this.domainCode, ErrorCodes.PARAM_EMPTY, "domainCode");
        CheckUtils.isNotBlank(this.tenantCode, ErrorCodes.PARAM_EMPTY, "tenantCode");
        CheckUtils.isNotBlank(this.orgCode, ErrorCodes.PARAM_EMPTY, "orgCode");
        //CheckUtils.isNotBlank(this.memberCode, ErrorCodes.PARAM_EMPTY, "memberCode");
        if (null == skuAttributeMap){
            CheckUtils.isNotNull(calAvailableQtyProduct, ErrorCodes.PARAM_EMPTY, "calAvailableQtyProduct");
            calAvailableQtyProduct.validate();
        }
    }
}
