/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WA<PERSON><PERSON>NTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.vo.result.marketing;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.io.Serializable;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "LuckyDrawMemberChanceResponse")
public class LuckyDrawMemberChanceResult implements Serializable {

    private static final long serialVersionUID = -8542819779549054619L;
    @ApiModelProperty(value = "Activity code.",required = true)
    private String activityCode;

    @ApiModelProperty(value = "Activity name.",required = true)
    private String activityName;

    @ApiModelProperty(value = "Remain participate times",required = true)
    private String times;

    @ApiModelProperty(value = "Activity begin time",required = true)
    private String activityBegin;

    @ApiModelProperty(value = "Activity end time", required = true)
    private String activityEnd;

    @ApiModelProperty(value = "Activity sponsors",required = true)
    private String sponsors;

    @ApiModelProperty(value = "Frozen times",required = true)
    private String frozenCount;

}
