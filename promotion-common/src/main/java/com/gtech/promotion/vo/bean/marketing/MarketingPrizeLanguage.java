package com.gtech.promotion.vo.bean.marketing;

import com.gtech.commons.utils.CheckUtils;
import com.gtech.promotion.exception.ErrorCodes;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("MarketingPrizeLanguage")
public class MarketingPrizeLanguage implements Serializable {

    private static final long serialVersionUID = 1775772879231464956L;

    @ApiModelProperty(value = "prize display name")
    private String prizeName;

    @ApiModelProperty(value = "Language id. (en-US/id-ID/zh-CN/...)", required = true)
    private String language;

    public void validate() {
        CheckUtils.isNotBlank(this.language, ErrorCodes.PARAM_EMPTY, "language");
    }
}
