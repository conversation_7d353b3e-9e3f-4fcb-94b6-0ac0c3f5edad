/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * <PERSON>ECH MAKES NO REPRESENTATIONS OR WA<PERSON>RANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.vo.result.coupon;

import java.io.Serializable;

import com.gtech.commons.result.Result;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;


/**
 * SendCouponToUserResult
 *
 * <AUTHOR>
 * @Date 2020-02-21
 */
@Getter
@Setter
@ToString
@ApiModel("SendCouponToUserListResponse")
public class SendCouponToUserListResult implements Serializable {

    private static final long serialVersionUID = 3752638172895274952L;

    @ApiModelProperty(value = "Member user code.", required = true)
    private String userCode;

    @ApiModelProperty(value = "Result code. 0 for no errors.", required = true)
    private String code;

    @ApiModelProperty(value = "Successed coupons. Separated by comma.")
    private String couponCode;

    public boolean isSuccess() {

        return Result.DEFAULT_SUCCESS_CODE.equals(code);
    }
}
