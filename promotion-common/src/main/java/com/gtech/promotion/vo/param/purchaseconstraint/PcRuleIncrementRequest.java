package com.gtech.promotion.vo.param.purchaseconstraint;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
public class PcRuleIncrementRequest {

    private String domainCode;
    @NotBlank(message = "tenantCode not empty")
    private String tenantCode;
    @NotBlank(message = "userCode not empty")
    private String userCode;
    private String orgCode;
    /**
     * true: 正向流程
     * false: 逆向流程(事务回滚)
     */
    private Boolean forward = true;
    /**
     * 使用增量
     * 默认true
     */
    private Boolean useIncrement = true;
    /**
     * 订单号
     */
    private String orderCode;
    /**
     * 订单状态
     */
    private String orderStatus;
    /**
     * 退货单号
     */
    private String returnCode;
    /**
     * 订单日期
     */
    private Date orderDate;
    /**
     * 增量商品数量
     * 优先,其次根据订单号/退货号查询
     */
    private List<ProductIncrement> productIncrementList;


    @Data
    public static class ProductIncrement {
        @NotBlank(message = "product not empty")
        private String productCode;
        
        @NotBlank(message = "skuCode not empty")
        private String skuCode;

        /**
         * sku 单价;
         */
        private BigDecimal amount;

        /**
         * sku维度的数量;
         */
        private Integer count;
    }

}
