package com.gtech.promotion.vo.param.purchaseconstraint.query;

import com.gtech.commons.page.OrderItem;
import com.gtech.commons.page.PageParam;
import com.gtech.commons.utils.DateUtil;
import com.gtech.commons.utils.StringUtil;
import com.gtech.promotion.checker.purchaseconstraint.PurchaseConstraintChecker;
import com.gtech.promotion.exception.Check;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;
import org.apache.commons.collections4.CollectionUtils;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;

@Data
@ToString
@ApiModel("QueryPurchaseConstraintListRequest")
public class QueryPurchaseConstraintListParam extends PageParam implements Serializable {
    @ApiModelProperty(value = "Tenant code", required = true)
    @NotEmpty(message = "tenantCode can not be empty")
    private String tenantCode;

    @ApiModelProperty(value = "Domain code",required = true)
    private String domainCode;

    @ApiModelProperty( value="orgCode.", example="default")
    private String orgCode;

    @ApiModelProperty(value="Purchase constraint status", example="0")
    private String purchaseConstraintStatus;

    @ApiModelProperty( value="Purchase constraint name.", example="xx")
    private String purchaseConstraintName;

    @ApiModelProperty( value="Purchase constraint begin from.", example="20221010101010")
    private String purchaseConstraintBeginFrom;

    @ApiModelProperty( value="Purchase constraint begin to.", example="20221010101010")
    private String purchaseConstraintBeginTo;

    @ApiModelProperty( value="Purchase constraint end from.", example="20221010101010")
    private String purchaseConstraintEndFrom;

    @ApiModelProperty( value="Purchase constraint end to.", example="20221010101010")
    private String purchaseConstraintEndTo;

    @ApiModelProperty( value="Purchase constraint code.", example="20221010101010")
    private String purchaseConstraintCode;
    @ApiModelProperty(value = "defaultFlag default = true")
    private Boolean defaultFlag = true;


    @Override
    public List<OrderItem> getOrderItems() {
        List<OrderItem> orderItems =  super.getOrderItems();
        if(CollectionUtils.isNotEmpty(orderItems)){
            Optional<OrderItem> orderItemOptional = orderItems.stream()
                    .filter(orderItem -> orderItem.getItem().trim().equalsIgnoreCase("CREATE_TIME"))
                    .findFirst();
            if(!orderItemOptional.isPresent()){
                OrderItem createTimeOrderItem = new OrderItem();
                createTimeOrderItem.setItem("CREATE_TIME");
                createTimeOrderItem.setType("DESC");
                orderItems.add(createTimeOrderItem);
            }
        }
        return orderItems;
    }

    public void validate(){
        Check.check(StringUtil.isBlank(tenantCode), PurchaseConstraintChecker.NOT_NULL_TENANT_CODE);

        Check.check(StringUtil.isNotBlank(purchaseConstraintBeginFrom)
                        && StringUtil.isNotBlank(purchaseConstraintBeginTo)
                        && DateUtil.parseDate(purchaseConstraintBeginTo, DateUtil.FORMAT_YYYYMMDDHHMISS_14).getTime()
                                < DateUtil.parseDate(purchaseConstraintBeginFrom, DateUtil.FORMAT_YYYYMMDDHHMISS_14).getTime(),
                PurchaseConstraintChecker.END_TIME_LITTLE_THAN_START_TIME);

        Check.check(StringUtil.isNotBlank(purchaseConstraintEndFrom)
                        && StringUtil.isNotBlank(purchaseConstraintEndTo)
                        && DateUtil.parseDate(purchaseConstraintEndTo, DateUtil.FORMAT_YYYYMMDDHHMISS_14).getTime()
                        < DateUtil.parseDate(purchaseConstraintEndFrom, DateUtil.FORMAT_YYYYMMDDHHMISS_14).getTime(),
                PurchaseConstraintChecker.END_TIME_LITTLE_THAN_START_TIME);

        if(CollectionUtils.isNotEmpty(this.getOrderItems())){
            List<String> sqlInjectList = Arrays.asList("insert", "update", "delete", "truncate", "select");
            String orderItemStr = this.getOrderItems().toString();
            for(String sqlInject : sqlInjectList){
                Check.check(orderItemStr.indexOf(sqlInject) != -1, PurchaseConstraintChecker.INVALID_ORDER_ITEM);
            }
        }
    }
}
