package com.gtech.promotion.vo.param.growth.query;

import java.io.Serializable;

import com.gtech.commons.utils.CheckUtils;
import com.gtech.promotion.exception.ErrorCodes;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString
public class GetGrowthTransactionParam implements Serializable {

    private static final long serialVersionUID = 2753338266317810360L;

    @ApiModelProperty(value = "Domain code",required = true)
    private String domainCode;

    @ApiModelProperty(value = "Tenant code",required = true)
    private String tenantCode;

    @ApiModelProperty(value = "Transaction sn",required = true)
    private String transactionSn;

    /**
     * Parameter validation.
     */
    public void validate() {

        CheckUtils.isNotBlank(this.domainCode, ErrorCodes.PARAM_EMPTY, "domainCode");
        CheckUtils.isNotBlank(this.tenantCode, ErrorCodes.PARAM_EMPTY, "tenantCode");
        CheckUtils.isNotBlank(this.transactionSn, ErrorCodes.PARAM_EMPTY, "transactionSn");
    }

}
