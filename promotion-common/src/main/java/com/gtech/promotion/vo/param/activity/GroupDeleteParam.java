package com.gtech.promotion.vo.param.activity;

import com.gtech.commons.utils.CheckUtils;
import com.gtech.promotion.exception.ErrorCodes;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @Description :
 * <AUTHOR> gaojie.li
 * @Date : 2023/6/14 9:56
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("GroupDeleteRequest")
public class GroupDeleteParam implements Serializable {

    private static final long serialVersionUID = 6158694111465674684L;

    @ApiModelProperty(value = "Tenant code", example = "880001", required = true)
    private String tenantCode;

    @ApiModelProperty(value = "Domain code",required = true)
    private String domainCode;

    @ApiModelProperty(value = "Group code", example = "123", required = true)
    private String groupCode;

    @ApiModelProperty(value = "Operator user", example = "12344",required = true)
    private String operatorUser;

    public void checkParam(){

        CheckUtils.isNotBlank(this.tenantCode, ErrorCodes.PARAM_EMPTY, "tenantCode");
        CheckUtils.isNotBlank(this.domainCode, ErrorCodes.PARAM_EMPTY, "domainCode");

        CheckUtils.isNotBlank(this.groupCode, ErrorCodes.PARAM_EMPTY, "groupCode");
        CheckUtils.isNotBlank(this.operatorUser, ErrorCodes.PARAM_EMPTY, "operatorUser");

    }

}
