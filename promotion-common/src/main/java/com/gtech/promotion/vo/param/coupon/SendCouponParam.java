/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * <PERSON>ECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.vo.param.coupon;

import java.io.Serializable;
import java.util.List;

import org.apache.commons.lang3.StringUtils;

import com.gtech.commons.utils.CheckUtils;
import com.gtech.promotion.api.ApiConstants;
import com.gtech.promotion.exception.ErrorCodes;
import com.gtech.promotion.exception.PromotionException;
import com.gtech.promotion.vo.bean.MemberInfo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;


/**
 * 
 *
 * <AUTHOR>
 * @Date 2020-02-21
 */
@Getter
@Setter
@ToString
@ApiModel("SendCouponRequest")
public class SendCouponParam implements Serializable {

    private static final long serialVersionUID = 5421959503093179924L;

    @ApiModelProperty(value = "Domain code.",example = "D00001",required = true)
    private String domainCode;

    @ApiModelProperty(value = "Tenant code", required = true)
    private String tenantCode;

    @ApiModelProperty(value = "Language id")
    private String language;
    
    @ApiModelProperty(value="Channel code.")
    private String channelCode;

    @ApiModelProperty(value = "Store organization code.")
    private String orgCode;

    @ApiModelProperty(value = "Coupon activity code.", required = true)
    private String activityCode;

    @ApiModelProperty(value = "Coupon activity release code.")
    private String releaseCode;

    @ApiModelProperty(value = ApiConstants.TAKE_LABEL, required = true)
    private String takeLabel;

    @ApiModelProperty(value = "Receive count for per member.", required = true)
    private Integer receiveCount;
    
    @ApiModelProperty(value = "Member user information list.", required = true)
    private List<MemberInfo> userList;

	@ApiModelProperty(value = "user codes, split by comma. (userAccounts or userCodes or userMobiles or userTags must be specified one)", required = true)
    private String userCodes;

	@ApiModelProperty(value = "user mobiles, split by comma. (userAccounts or userCodes or userMobiles or userTags must be specified one)", required = true)
    private String userMobiles;

	@ApiModelProperty(value = "User accounts, split by comma. (userAccounts or userCodes or userMobiles or userTags must be specified one)", required = true)
    private String userAccounts;

	@ApiModelProperty(value = "User tags, split by comma. (userAccounts or userCodes or userMobiles or userTags must be specified one)", required = true)
	private String userTags;

    @ApiModelProperty(value = "is all user, 0:no 1:yes default 0", required = true)
    private String isAllUser = "0";

    @ApiModelProperty(value = "operator code", required = true)
    private String operatorCode;
    /**
     * Parameter validation.
     */
    public void validate() {

        CheckUtils.isNotBlank(this.domainCode, ErrorCodes.PARAM_EMPTY, "domainCode");
        CheckUtils.isNotBlank(this.tenantCode, ErrorCodes.PARAM_EMPTY, "tenantCode");
        CheckUtils.isNotBlank(this.activityCode, ErrorCodes.PARAM_EMPTY, "activityCode");
        CheckUtils.isNotBlank(this.takeLabel, ErrorCodes.PARAM_EMPTY, "takeLabel");
        CheckUtils.isNotNull(this.receiveCount, ErrorCodes.PARAM_EMPTY, "receiveCount");
        CheckUtils.isNotNull(this.operatorCode, ErrorCodes.PARAM_EMPTY, "operatorCode");
        CheckUtils.isNotNull(this.releaseCode, ErrorCodes.PARAM_EMPTY, "releaseCode");

		if ("0".equals(isAllUser) && StringUtils.isEmpty(userCodes) && StringUtils.isEmpty(userMobiles) && StringUtils.isEmpty(userAccounts)
				&& StringUtils.isEmpty(userTags)) {
                throw new PromotionException(ErrorCodes.USER_CODE_MOBILE_ACCOUNT_MUST_ONE.getCode(),ErrorCodes.USER_CODE_MOBILE_ACCOUNT_MUST_ONE.getMessage());
        }


        CheckUtils.isTrue(this.receiveCount > 0 && this.receiveCount < 100, ErrorCodes.PARAM_SPECIFICATION_ERROR, "receiveCount must greater than 0 and less than 100.");
    }
}
