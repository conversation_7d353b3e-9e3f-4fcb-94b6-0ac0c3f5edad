package com.gtech.promotion.vo.result.marketing;

import com.gtech.promotion.api.ApiConstants;
import com.gtech.promotion.vo.bean.*;
import com.gtech.promotion.vo.bean.marketing.MarketingLanguage;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class MarketingFindResult implements Serializable {

    private static final long serialVersionUID = -3283799111316112799L;


    @ApiModelProperty(value = "Domain code.",example = "D00001",required = true)
    private String domainCode;

    @ApiModelProperty(value = "Tenant code.",required = true)
    private String tenantCode;

    @ApiModelProperty(value = "Org code.",required = true)
    private String orgCode;

    @ApiModelProperty(value = "Group code.")
    private String groupCode;

    @ApiModelProperty(value = "Activity code.",example = "12556568987",required = true)
    private String activityCode;

    @ApiModelProperty(value = "Activity type. 03-lucky draw 04-flash sale",required = true)
    private String activityType;

    @ApiModelProperty(value = "Activity ops type. 401-flash sale",required = true)
    private String opsType;

    @ApiModelProperty(value= ApiConstants.ACTIVITY_STATUS)
    private String activityStatus;

    @ApiModelProperty(value = "Activity name",required = true)
    private String activityName;

    @ApiModelProperty(value = "Activity begin",required = true)
    private String activityBegin;

    @ApiModelProperty(value = "Activity end",required = true)
    private String activityEnd;

    @ApiModelProperty(value = "Activity url")
    private String activityUrl;

    @ApiModelProperty(value = "Activity sponsors",required = true)
    private String sponsors;

    @ApiModelProperty(value = "Activity background image.")
    private String backgroundImage;

    @ApiModelProperty(value = "Multilingual attributes.",required = true)
    private List<MarketingLanguage> marketingLanguages;

    @ApiModelProperty(value = "Activity warm-up begin time.")
    private String warmBegin;

    @ApiModelProperty(value = "Activity warm-up end time.")
    private String warmEnd;

    @ApiModelProperty(value = "Activity cool down time.")
    private String coolDown;

    @ApiModelProperty(value = "Activity period.")
    private ActivityPeriod activityPeriod;

    //参与资格列表（为空表示不限）
    @ApiModelProperty(value = "List of eligibility (blank indicates unlimited)",required = true)
    private List<Qualification> qualifications;

    @ApiModelProperty(value = "sku 01 ,spu 02. default 01")
    private String selectProductType;


    @ApiModelProperty(value = "Incentive limited list. It can't be empty while incentiveLimitedFlag is '01'.",required = true)
    private List<IncentiveLimited> incentiveLimiteds;

    //参与规则列表
    @ApiModelProperty(value = "Rule list",required = true)
    private List<LuckyDrawRule> luckyDrawRules;
//    奖励标识 00 无限制  01 有限制
    @ApiModelProperty(value = "Bonus sign 00 unlimited 01 limited")
    private String incentiveLimitedFlag;

    //规则标识 00 无限制  01 有限制
    @ApiModelProperty(value = "Rule ID 00 Restricted 01 Restricted")
    private String luckyDrawRuleFlag;

    @ApiModelProperty(value = "extImages.")
    private List<ExtImage> extImages;
}
