package com.gtech.promotion.vo.param.purchaseconstraint;

import com.gtech.commons.utils.CheckUtils;
import com.gtech.commons.utils.Joiner;
import com.gtech.commons.utils.StringUtil;
import com.gtech.promotion.api.ApiConstants;
import com.gtech.promotion.checker.purchaseconstraint.PurchaseConstraintChecker;
import com.gtech.promotion.checker.activity.TPromoProductChecker;
import com.gtech.promotion.code.PromotionConstants;
import com.gtech.promotion.code.activity.ItemScopeTypeEnum;
import com.gtech.promotion.code.activity.ProductSelectionEnum;
import com.gtech.promotion.code.activity.StoreParamTypeEnum;
import com.gtech.promotion.exception.Check;
import com.gtech.promotion.exception.ErrorCodes;
import com.gtech.promotion.vo.bean.*;
import com.gtech.promotion.vo.param.activity.CustomCondition;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.*;
import java.util.stream.Collectors;

@Data
@Builder
@ToString
@ApiModel("CreatePurchaseConstraintRequest")
@NoArgsConstructor
@AllArgsConstructor
public class CreatePurchaseConstraintParam implements Serializable {
    @ApiModelProperty(value = "Tenant code", required = true)
    @NotEmpty(message = "tenantCode can not be empty")
    private String tenantCode;

    @ApiModelProperty(value = "Domain code",required = true)
    private String domainCode;

    @ApiModelProperty( value="orgCode.", example="default",required=true)
    private String orgCode;

    @ApiModelProperty(value = "Purchase constraint Name",required = true)
    private String purchaseConstraintName;

    @ApiModelProperty(value = "Purchase constraint start time.")
    private Date purchaseConstraintStartTime;

    @ApiModelProperty(value = "Activity end time.")
    private Date purchaseConstraintEndTime;

    @ApiModelProperty(value = "Unique identification of goods to be uploaded (this parameter must be filled when uploading files) ")
    private String skuToken;

    @Builder.Default
    @ApiModelProperty(value = ApiConstants.PRODUCT_SELECTION_TYPE)
    private String productSelectionType = ProductSelectionEnum.SELECT.code();
    public void setProductSelectionType(String productSelectionType) {
        this.productSelectionType = StringUtils.isBlank(productSelectionType) ? ProductSelectionEnum.SELECT.code() : productSelectionType;
    }

    @Builder.Default
    @ApiModelProperty(value = "Product item scope type: 1-All scope 2-By spu in scope")
    private String itemScopeType = ItemScopeTypeEnum.ALL_SCOPE.code();
    public void setItemScopeType(String itemScopeType) {
        this.itemScopeType = StringUtils.isBlank(itemScopeType) ? ItemScopeTypeEnum.ALL_SCOPE.code() : itemScopeType;
    }

    @ApiModelProperty(value = ApiConstants.PRODUCT_TYPE,required = true)
    private String conditionProductType;
    public void setConditionProductType(String conditionProductType) {
        this.conditionProductType = conditionProductType;
    }

    @ApiModelProperty(value = "Product scope list. seqNum equals 1 means condition product. seqNum equals 2 means incentive product.")
    private List<ProductScope> products;

    @ApiModelProperty(value = "Product detail list. seqNum equals 1 means condition product. seqNum equals 2 means incentive product.")
    private List<ProductDetail> productDetails;

    @ApiModelProperty(value = "Product detail list. seqNum equals 1 means condition product. seqNum equals 2 means incentive product.")
    private List<ProductDetail> productDetailBlackList;

    @ApiModelProperty(value = "Qualification list. Empty means not limited.")
    private List<Qualification> qualifications;

    @ApiModelProperty(value = "Store limited type: 00-Unlimited, 01-Specified stores.",required = true)
    private String storeType;

    @ApiModelProperty(value = "Specified store list while storeType equals 01.")
    private List<ActivityStore> channelStores;

    @ApiModelProperty(value = "Period type.")
    private String periodType;

    @ApiModelProperty(value = "Activity period.")
    private ActivityPeriod activityPeriod;

    @ApiModelProperty(value = "User code of the operator.", example="U00001")
    private String operateUser;

    @ApiModelProperty(value = "User last name of the operator.")
    private String operateLastName;

    @ApiModelProperty(value = "User first name of the operator.")
    private String operateFirstName;

    @ApiModelProperty(value = "Right of first refusal.", example = "0")
    private Integer firstRefusal;

    @ApiModelProperty(value = "Purchase constraint rule list.", example = "0")
    private List<PurchaseConstraintRule> purchaseConstraintRules;

    @ApiModelProperty(value = "Purchase constraint description.", example = "xx")
    private String description;

    @ApiModelProperty(value = "Purchase constraint priority.", example = "1")
    private Integer priority;

    /**
     * 高级价格限购标识，默认0，非高级价格限购
     */
    @ApiModelProperty(value = "Price setting.", example = "0")
    private Integer priceSetting;


    @ApiModelProperty(value = "Custom conditions.")
    private List<CustomCondition> customConditionsList;

    @ApiModelProperty(value = "Custom rule.")
    private List<CustomCondition> customRulesList;


    public void setPriority(Integer priority) {
        this.priority = (null == priority ? PromotionConstants.DEF_PRIORITY: priority);
    }

    public void validate(){
        CheckUtils.isNotBlank(this.domainCode, ErrorCodes.PARAM_EMPTY, "domainCode");
        CheckUtils.isNotBlank(this.tenantCode, ErrorCodes.PARAM_EMPTY, "tenantCode");
        CheckUtils.isNotNull(this.firstRefusal, ErrorCodes.PARAM_EMPTY, "firstRefusal");
        CheckUtils.isNotBlank(this.purchaseConstraintName, ErrorCodes.PARAM_EMPTY, "purchaseConstraintName");
        Check.check(null != purchaseConstraintStartTime
                                && null != purchaseConstraintEndTime
                                && purchaseConstraintEndTime.getTime() <= purchaseConstraintStartTime.getTime(),
                            PurchaseConstraintChecker.END_TIME_LITTLE_THAN_START_TIME);


        if (CollectionUtils.isNotEmpty(this.qualifications)){
            for (Qualification qualification : qualifications) {
                qualification.validate();
            }
        }

        // Validate the store information
        checkChannelStores();

        // validate product
        this.checkProduct();

        // validate purchase constraint rule
        if(CollectionUtils.isNotEmpty(this.purchaseConstraintRules)){
            for(PurchaseConstraintRule purchaseConstraintRule : purchaseConstraintRules){
                purchaseConstraintRule.validate();
            }
        }
    }

    public void checkChannelStores() {
        this.setStoreType(StringUtil.isBlank(this.getStoreType()) ? "00" : this.getStoreType());
        if (StoreParamTypeEnum.STORE_CUSTOM.equalsCode(this.getStoreType())) {
            CheckUtils.isTrue(CollectionUtils.isNotEmpty(this.channelStores), ErrorCodes.PARAM_EMPTY, "channelStores");
            for (ActivityStore e : this.channelStores) {
                e.validate();
            }
        }
    }

    public boolean isAllProducts() {
        return CollectionUtils.isEmpty(products) && CollectionUtils.isEmpty(productDetails);
    }

    // 商品校验
    private void checkProduct(){

        //反选时
        if (ProductSelectionEnum.INVERT_SELECT.equalsCode(this.productSelectionType)){
            Check.check(this.isAllProducts(), TPromoProductChecker.ERROR_PRODUCT_SELECT_ALL);//不能反选全部商品
        }

        if (this.isAllProducts()) {
            return;
        }
        boolean seq1 = false;
        if (CollectionUtils.isNotEmpty(this.products)) {
            this.checkProductScopes(products);
            seq1 = products.stream().anyMatch(x->x.getSeqNum() == 1);
        }
        if (CollectionUtils.isNotEmpty(this.productDetails)) {
            this.skuToken = null;
            seq1 = seq1 || productDetails.stream().anyMatch(x -> null != x.getSeqNum() && 1 == x.getSeqNum());
        }

        if (!seq1){ // 如果没有商品池1， 添加一个全分类的商品池1
            if (null == products){
                products = new ArrayList<>();
            }
            products.add(ProductScope.builder().categoryCode(PromotionConstants.UNLIMITED).build());
        }
    }

    /**
     * 各商品池不能有一摸一样的商品范围
     */
    public void checkProductScopes(List<ProductScope> products){

        HashSet<String> seqNumProducts = new HashSet<>();
        for (ProductScope product : products){

            addIfNullProduct(product);

            String[] categoryCode = product.getCategoryCode().split(",");
            String[] brandCode = product.getBrandCode().split(",");
            String[] attributeCodes = new String[0];

            List<ProductAttribute> productAttributes = product.getAttributes();
            if (CollectionUtils.isNotEmpty(productAttributes)) {
                attributeCodes = productAttributes.stream().map(m -> (m.getAttributeCode() + ":" + m.getAttributeValues())).collect(Collectors.toList()).toArray(attributeCodes);
            }
            Arrays.sort(categoryCode);
            Arrays.sort(brandCode);
            Arrays.sort(attributeCodes);

            Joiner joiner = Joiner.on(",");
            seqNumProducts.add(joiner.appendTo(joiner.appendTo(joiner.appendTo(new StringBuilder(), categoryCode), brandCode), attributeCodes).toString());
        }
        Check.check(products.size() > seqNumProducts.size(), TPromoProductChecker.ERROR_PRODUCT_SCOPE);
    }

    private void addIfNullProduct(ProductScope product){
        if (StringUtil.isBlank(product.getCategoryCode())){
            product.setCategoryCode(PromotionConstants.UNLIMITED);
        }
        if (StringUtil.isBlank(product.getBrandCode())){
            product.setBrandCode(PromotionConstants.UNLIMITED);
        }
        List<ProductAttribute> productAttributes = product.getAttributes();
        if (CollectionUtils.isNotEmpty(productAttributes)) {
            productAttributes.forEach(attr -> {
                if (StringUtil.isBlank(attr.getAttributeCode())) {
                    attr.setAttributeCode(PromotionConstants.UNLIMITED);
                }
                if (StringUtil.isBlank(attr.getAttributeValues())) {
                    attr.setAttributeValues(PromotionConstants.UNLIMITED);
                }
            });
        }
    }
}
