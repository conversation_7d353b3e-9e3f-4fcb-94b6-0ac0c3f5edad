package com.gtech.promotion.vo.param.coupon;

import java.util.List;

import com.gtech.commons.utils.CheckUtils;
import com.gtech.promotion.api.ApiConstants;
import com.gtech.promotion.exception.ErrorCodes;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Description :
 * <AUTHOR> gaojie.li
 * @Date : 2023/8/25 17:18
 */
@Data
@ApiModel("UaCreateSendCouponRequest")
public class UaCreateSendCouponParam extends CreateCouponReleaseParam{


    private static final long serialVersionUID = 1213625490161810353L;

    @ApiModelProperty(value = ApiConstants.TAKE_LABEL, required = true)
    private String takeLabel;

    @ApiModelProperty(value = "user codes", required = true)
    private String userCode;

    @ApiModelProperty(name = "status，参数不传 默认为 02 ",value = "券状态 01 未领取， 02 领取 03 已使用 04 已锁定 05 过期")
    private String status;

    @ApiModelProperty(name = "Check flag time",value = "true 校验时间，false 不校验")
    private boolean checkFlagTime;

	@Override
    public void validate(){

        super.validate();
        CheckUtils.isNotBlank(this.takeLabel, ErrorCodes.PARAM_EMPTY, "takeLabel");
        CheckUtils.isNotBlank(this.userCode, ErrorCodes.PARAM_EMPTY, "userCode");
        List<String> couponCodes = super.getCouponCodes();
        CheckUtils.isTrue(null != couponCodes, ErrorCodes.PARAM_EMPTY, "couponCodes");

    }

}
