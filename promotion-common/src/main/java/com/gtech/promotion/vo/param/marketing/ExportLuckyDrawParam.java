package com.gtech.promotion.vo.param.marketing;

import com.gtech.commons.page.PageParam;
import com.gtech.commons.utils.CheckUtils;
import com.gtech.promotion.exception.ErrorCodes;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@ApiModel(value = "ExportLuckyDrawRequest")
public class ExportLuckyDrawParam extends PageParam implements Serializable {
    private static final long serialVersionUID = -7137605380796655311L;

    @ApiModelProperty(value = "Domain code",required = true)
    private String domainCode;
    @ApiModelProperty(value = "Tenant code",required = true)
    private String tenantCode;

    @ApiModelProperty(value = "Activity code",required = true)
    private String activityCode;
    @ApiModelProperty(value = "Activity code",required = true)
    private String language;
    public void validate() {
        CheckUtils.isNotBlank(this.domainCode, ErrorCodes.PARAM_EMPTY, "domainCode");
        CheckUtils.isNotBlank(this.tenantCode, ErrorCodes.PARAM_EMPTY, "tenantCode");
        CheckUtils.isNotBlank(this.activityCode, ErrorCodes.PARAM_EMPTY, "activityCode");
        CheckUtils.isNotBlank(this.language, ErrorCodes.PARAM_EMPTY, "language");

    }

}
