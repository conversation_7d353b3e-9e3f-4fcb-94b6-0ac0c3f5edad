package com.gtech.promotion.vo.param.activity;

import com.gtech.commons.utils.CheckUtils;
import com.gtech.commons.utils.DateUtil;
import com.gtech.promotion.exception.ErrorCodes;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("ActivityExtendRequest")
public class ActivityExtendParam implements Serializable {

    private static final long serialVersionUID = -6657572891128838569L;

    @ApiModelProperty(value = "Tenant code",required = true)
    private String tenantCode;

    @ApiModelProperty(value = "Activity code",required = true)
    private String activityCode;

    @ApiModelProperty(value = "Activity end time (format:yyyyMMddHHmmss)",required = true)
    private String endTime;

    @ApiModelProperty(value = "User code of the operator.", example="U00001", required = true)
    private String operateUser;

    @ApiModelProperty(value = "User last name of the operator.")
    private String operateLastName;

    @ApiModelProperty(value = "User first name of the operator.")
    private String operateFirstName;

    public void validate() {
        CheckUtils.isNotBlank(this.tenantCode, ErrorCodes.PARAM_EMPTY, "tenantCode");
        CheckUtils.isNotBlank(this.activityCode, ErrorCodes.PARAM_EMPTY, "activityCode");
        CheckUtils.isNotBlank(this.endTime, ErrorCodes.PARAM_EMPTY, "endTime");
        CheckUtils.isNotBlank(this.operateUser, ErrorCodes.PARAM_EMPTY, "operateUser");
        CheckUtils.isTrue(DateUtil.validate(this.endTime, DateUtil.FORMAT_YYYYMMDDHHMISS_14), ErrorCodes.PARAM_ERROR, "endTime");
    }
}
