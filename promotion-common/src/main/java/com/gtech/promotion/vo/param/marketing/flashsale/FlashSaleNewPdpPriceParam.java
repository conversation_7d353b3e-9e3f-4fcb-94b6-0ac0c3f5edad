package com.gtech.promotion.vo.param.marketing.flashsale;

import com.gtech.commons.utils.CheckUtils;
import com.gtech.promotion.api.ApiConstants;
import com.gtech.promotion.code.marketing.ActivityTypeEnum;
import com.gtech.promotion.exception.ErrorCodes;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class FlashSaleNewPdpPriceParam implements Serializable {
    private static final long serialVersionUID = -30955046084273807L;

    @ApiModelProperty(value = "Domain code.",example = "DC0001",required = true)
    private String domainCode;

    @ApiModelProperty(value = "Tenant code.",example = "TC0001",required = true)
    private String tenantCode;

    @ApiModelProperty(value = "Language id")
    private String language;

    @ApiModelProperty(value = "Store orgnization code.",example = "O00001")
    private String orgCode;

    @ApiModelProperty(value = ApiConstants.QUALIFICATIONS)
    private Map<String, List<String>> qualifications;

    @ApiModelProperty(value = "sku list",example = "O00001")
    private List<SkuParam> skuList;//NOSONAR

    @ApiModelProperty(value = "Flash sale activity code.", example = "87234943952324", required = true)
    private String activityCode;

    @ApiModelProperty(value = "activity type", example = "04")
    private String activityType = ActivityTypeEnum.FLASH_SALE.code();

    public void validate() {

        CheckUtils.isNotBlank(this.domainCode, ErrorCodes.PARAM_EMPTY, "domainCode");
        CheckUtils.isNotBlank(this.tenantCode, ErrorCodes.PARAM_EMPTY, "tenantCode");
        CheckUtils.isNotBlank(this.activityCode, ErrorCodes.PARAM_EMPTY, "activityCode");
        CheckUtils.isTrue(CollectionUtils.isNotEmpty(this.skuList), ErrorCodes.PARAM_LEAST_ONE, "skuCode\\combineSkuCode");
        List<String> skuCodeList = new ArrayList<>();
        for(SkuParam skuParam : skuList){
            if(!StringUtils.isEmpty(skuParam.getSkuCode())){
                skuCodeList.add(skuParam.getSkuCode());
            }
        }
        CheckUtils.isTrue(CollectionUtils.isNotEmpty(skuCodeList), ErrorCodes.PARAM_LEAST_ONE, "skuCode list");
    }
}
