package com.gtech.promotion.vo.result.flashsale;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @Description :
 * <AUTHOR> gaojie.li
 * @Date : 2023/8/8 13:38
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("MarketingGroupUserResponse")
public class MarketingGroupUserResult extends MarketingGroupResult implements Serializable {

    private static final long serialVersionUID = -4259245642435603969L;

    @ApiModelProperty(value = "activity code.",example = "12556568987",required = true)
    private String activityCode;

    /**
     * 拼团业务编码
     */
    @ApiModelProperty(value = "Marketing group code.",example = "12556568987",required = true)
    private String marketingGroupCode;

    /**
     * 会员编码
     */
    @ApiModelProperty(value = "User code.",example = "12556568987",required = true)
    private String userCode;

    /**
     * 是否团长 1 是， 0 否
     */
    @ApiModelProperty(value = "Team leader.",example = "12556568987",required = true)
    private String teamLeader;

    /**
     * 有效截止时间
     */
    @ApiModelProperty(value = "Effective time.",example = "20200810154932",required = true)
    private String effectiveTime;


    /**
     * org编码
     */
    @ApiModelProperty(value = "Org code.",example = "12556568987",required = true)
    private String orgCode;

    /**
     * 拼团状态 01进行中，02 拼团结束
     */
    @ApiModelProperty(value = "Group status.",example = "12556568987",required = true)
    private String groupStatus;

    @ApiModelProperty(hidden = true)
    private String leaderUserCode;

    private Integer closeFlag;

    @ApiModelProperty(hidden = true)
    private String productCode;

    @ApiModelProperty(hidden = true)
    private String skuCode;

    @ApiModelProperty(hidden = true)
    private String selectProductType;






}
