package com.gtech.promotion.vo.result.coupon;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class ExportCouponResult implements Serializable {

    private static final long serialVersionUID = -178214235464360330L;

    @ApiModelProperty(value = "id")
    private String id;

    @ApiModelProperty(value = "Coupon code")
    private String couponCode;

    @ApiModelProperty(value = "Coupon code start use date")
    private String validStartDate;

    @ApiModelProperty(value = "Coupon code end use date")
    private String validEndDate;

    @ApiModelProperty(value = "Coupon code status: 01-unreceived,02-unused,03-used,04-locked,05-expired")
    private String status;
}
