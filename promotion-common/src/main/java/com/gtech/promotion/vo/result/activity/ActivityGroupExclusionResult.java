package com.gtech.promotion.vo.result.activity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @Description :
 * <AUTHOR> gaojie.li
 * @Date : 2023/7/12 13:37
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("ActivityGroupExclusionResponse")
public class ActivityGroupExclusionResult implements Serializable {

    private static final long serialVersionUID = -1359534884338446518L;

    @ApiModelProperty(value = "分组编码", example = "1231209")
    private String groupCode;
}
