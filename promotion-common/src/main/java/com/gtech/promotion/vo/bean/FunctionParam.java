/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * <PERSON>ECH MAKES NO REPRESENTATIONS OR WA<PERSON>RANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.vo.bean;

import com.gtech.commons.utils.CheckUtils;
import com.gtech.promotion.api.ApiConstants;
import com.gtech.promotion.exception.ErrorCodes;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.io.Serializable;


/**
 * 
 *
 * <AUTHOR>
 * @Date 2020-02-11
 */
@Getter
@Setter
@ToString
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("FunctionRequest")
public class FunctionParam implements Serializable {

    private static final long serialVersionUID = -6415054630977506830L;

    @ApiModelProperty(value = ApiConstants.FUNCTION_TYPE,required = true)
    private String functionType;

    @ApiModelProperty(value = "Function code",required = true)
    private String functionCode;

    @ApiModelProperty(value = ApiConstants.PARAM_TYPE,required = true)
    private String paramType;

    @ApiModelProperty(value = "Param value",required = false)
    private String paramValue;

    @ApiModelProperty(value = ApiConstants.PARAM_UNIT,required = false)
    private String paramUnit;

    @ApiModelProperty(value = "Level (numbers 1-99)",required = true)
    private Integer rankParam;

    /**
     * Parameter validation.
     */
    public void validate() {

        CheckUtils.isNotBlank(this.functionType, ErrorCodes.PARAM_EMPTY, "functionType");
        CheckUtils.isNotBlank(this.functionCode, ErrorCodes.PARAM_EMPTY, "functionCode");
        CheckUtils.isNotBlank(this.paramType, ErrorCodes.PARAM_EMPTY, "paramType");
        CheckUtils.isNotNull(this.rankParam, ErrorCodes.PARAM_EMPTY, "rankParam");
    }

}
