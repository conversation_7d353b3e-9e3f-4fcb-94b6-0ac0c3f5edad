package com.gtech.promotion.vo.param.marketing;

import com.gtech.commons.utils.CheckUtils;
import com.gtech.promotion.exception.ErrorCodes;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2021/2/25 14:22
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("QueryMemberLuckyDrawFrequencyRequest")
public class QueryMemberLuckyDrawFrequencyParam implements Serializable {

    private static final long serialVersionUID = -8476687449467905323L;

    @ApiModelProperty(value = "Domain code.",example = "D00001",required = true)
    private String domainCode;

    @ApiModelProperty(value = "Tenant code.",example = "T00001",required = true)
    private String tenantCode;

    @ApiModelProperty(value = "Store organization code. (Blank value means unlimited.)")
    private String orgCode;

    @ApiModelProperty(value = "Activity code",required = true)
    private String activityCode;

    @ApiModelProperty(value = "Member code", required = true)
    private String memberCode;


    /**
     * Parameter validation.
     */
    public void validate() {

        CheckUtils.isNotBlank(this.domainCode, ErrorCodes.PARAM_EMPTY, "domainCode");
        CheckUtils.isNotBlank(this.tenantCode, ErrorCodes.PARAM_EMPTY, "tenantCode");
        CheckUtils.isNotBlank(this.activityCode, ErrorCodes.PARAM_EMPTY, "activityCode");
        CheckUtils.isNotBlank(this.memberCode, ErrorCodes.PARAM_EMPTY, "memberCode");
    }
}
