package com.gtech.promotion.vo.param.growth.query;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Data
public class GrowthAccountUniqueParam implements Serializable{

	/**
	 * 
	 */
	private static final long serialVersionUID = 5825657452236955730L;

    /**
     * Domain code.
     */
    @ApiModelProperty(value = "Domain code", required = true)
    @NotEmpty(message = "domainCode can not be empty")
    private String domainCode;

    @ApiModelProperty(value = "tenantCode", required = true)
    @NotEmpty(message = "tenantCode can not be empty")
    private String tenantCode;

	@ApiModelProperty(value = "growthAccountCode", required = true)
	@NotEmpty(message = "growthAccountCode can not be empty")
	private String growthAccountCode;

	@EqualsAndHashCode(callSuper=false)
	@Data
	public static class GrowthAccountStatusUniqueVo extends GrowthAccountUniqueParam {
		/**
		 * 
		 */
		private static final long serialVersionUID = -8612717153118651895L;
		@ApiModelProperty(value = "status", required = true)
		@NotNull(message = "status can not be null")
		private Integer status;
		@ApiModelProperty(value = "oldStatus", required = true)
		@NotNull(message = "oldStatus can not be null")
		private Integer oldStatus;

		private String updateUser;
	}

    /**
     * Parameter validation.
     */
    public void validate() {
		// to do something
    }
}
