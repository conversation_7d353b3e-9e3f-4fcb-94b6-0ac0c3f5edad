package com.gtech.promotion.vo.param.coupon;

import com.gtech.commons.page.PageParam;
import com.gtech.commons.utils.CheckUtils;
import com.gtech.promotion.exception.ErrorCodes;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * @Description :
 * <AUTHOR> gaojie.li
 * @Date : 2021/7/5 10:39
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "QueryCouponUsedListRequest")
public class QueryCouponUsedListParam extends PageParam implements Serializable {

    private static final long serialVersionUID = -5066908595557951188L;

    @ApiModelProperty(value = "Domain code.",example = "D00001",required = true)
    private String domainCode;

    @ApiModelProperty(value = "Tenant code", required = true)
    private String tenantCode;

    @ApiModelProperty(value = "Coupon code")
    private String couponCode;

    //01-未发放 02-已发放 03-已使用 04-已锁定 05-已过期
    @ApiModelProperty(value = "Coupon status: 01-Did not receive 02-Have to receive 03-Used 04-Locked 05-Expired. Empty means query all status.")
    private List<String> status;

    @ApiModelProperty(value="Create Begin Time.2020-06-15 15:06:59",notes = "2020-06-15 15:06:59")
    private String beginTime;

    @ApiModelProperty(value="Create End Time.2020-06-15 15:06:59",notes = "2020-06-15 15:06:59")
    private String endTime;

    @ApiModelProperty(value="Update Begin Time.2020-06-15 15:06:59",notes = "2020-06-15 15:06:59")
    private String updateBeginTime;

    @ApiModelProperty(value="Update End Time.2020-06-15 15:06:59",notes = "2020-06-15 15:06:59")
    private String updateEndTime;

    @ApiModelProperty(value = "Mobile.")
    private String mobile ;

    @ApiModelProperty(value = "User email.")
    private String email;

    @ApiModelProperty(value = "User code.")
    private String userCode;

    /**
     * Parameter validation.
     */
    public void validate() {
        CheckUtils.isNotBlank(this.domainCode, ErrorCodes.PARAM_EMPTY, "domainCode");
        CheckUtils.isNotBlank(this.tenantCode, ErrorCodes.PARAM_EMPTY, "tenantCode");
    }


}
