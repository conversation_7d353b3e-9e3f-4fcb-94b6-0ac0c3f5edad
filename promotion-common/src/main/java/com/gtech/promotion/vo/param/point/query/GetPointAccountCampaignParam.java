package com.gtech.promotion.vo.param.point.query;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

@Getter
@Setter
@ToString
public class GetPointAccountCampaignParam implements Serializable {

    private static final long serialVersionUID = -2865703311905599622L;

    @ApiModelProperty(value = "Domain code", required = true)
    @NotEmpty(message = "domainCode can not be empty")
    private String domainCode;

    @ApiModelProperty(value = "tenantCode", required = true)
    @NotEmpty(message = "tenantCode can not be empty")
    private String tenantCode;

    @ApiModelProperty(value = "Point account type. (1-User 2-Organization)", required = true)
    @NotNull(message = "Point account type can not be null")
    private Integer accountType;

    @ApiModelProperty(value = "account code. (UserCode or OrgCode)", required = true)
    @NotEmpty(message = "account code can not be empty")
    private String accountCode;

    @ApiModelProperty(value = "campaign code list", required = true)
    @NotEmpty(message = "campaign code list can not be empty")
    private List<String> campaignCodes;

    /**
     * Parameter validation.
     */
    public void validate() {
        // to do something
    }

}
