package com.gtech.promotion.vo.param.coupon;

import com.gtech.commons.utils.CheckUtils;
import com.gtech.promotion.exception.ErrorCodes;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @Description :
 * <AUTHOR> gaojie.li
 * @Date : 2023/12/22 14:14
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("ExportCouponRelationRequest")
public class ExportCouponRelationParam implements Serializable {

    private static final long serialVersionUID = -2467452044437357493L;

    @ApiModelProperty(value = "Domain code.",example = "D00001",required = true)
    private String domainCode;

    @ApiModelProperty(value = "Tenant code",required = true)
    private String tenantCode;

    @ApiModelProperty(value = "Activity code.",required = true)
    private String activityCode;

    @ApiModelProperty(value = "Release code.")
    private String releaseCode;

    @ApiModelProperty(value = "Max coupon code")
    private String maxCouponCode;


    public void validate() {
        CheckUtils.isNotBlank(this.domainCode, ErrorCodes.PARAM_EMPTY, "domainCode");
        CheckUtils.isNotBlank(this.tenantCode, ErrorCodes.PARAM_EMPTY, "tenantCode");
        CheckUtils.isNotBlank(this.activityCode, ErrorCodes.PARAM_EMPTY, "activityCode");
    }

}
