package com.gtech.promotion.vo.result.point;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;

@Getter
@Setter
@ToString
public class PointAccountResult implements Serializable {

    private static final long serialVersionUID = 3493808812488306983L;

    @ApiModelProperty(value = "Tenant code")
    private String tenantCode;

    @ApiModelProperty(value = "Point account code.")
    private String pointAccountCode;

    @ApiModelProperty(value = "account code. (UserCode or OrgCode)")
    private String accountCode;

    @ApiModelProperty(value = "campaign code.")
    private String campaignCode;

    @ApiModelProperty(value = "Point account type. (1-User 2-Organization)")
    private Integer accountType;

    @ApiModelProperty(value = "Point account description.")
    private String accountDesc;

    @ApiModelProperty(value = "Account balance")
    private Integer accountBalance;

    @ApiModelProperty(value = "Point account status.(0-Inactive 1-Active)")
    private Integer status;

    @ApiModelProperty(value = "Extends parameters. (JSON String)")
    private String extParams;

    @ApiModelProperty(value = "expiring point")
    private Integer expiringPoint;

    @ApiModelProperty(value = "expiring days")
    private Long expiringDays;
}