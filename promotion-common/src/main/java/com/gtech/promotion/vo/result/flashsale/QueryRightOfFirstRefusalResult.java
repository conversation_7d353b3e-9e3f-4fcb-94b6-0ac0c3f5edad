package com.gtech.promotion.vo.result.flashsale;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel("QueryRightOfFirstRefusalResponse")
public class QueryRightOfFirstRefusalResult {

    @ApiModelProperty(value = "domainCode")
    private String domainCode;
    @ApiModelProperty(value = "tenantCode")
    private String tenantCode;
    @ApiModelProperty(value = "orgCode")
    private String orgCode;
    @ApiModelProperty(value = "activityCode")
    private String activityCode;
    @ApiModelProperty(value = "memberCode")
    private String memberCode;
    @ApiModelProperty(value = "rightOfFirstRefusalCode")
    private String rightOfFirstRefusalCode;
    @ApiModelProperty(value = "rightOfFirstRefusalProductCode")
    private String rightOfFirstRefusalProductCode;
    @ApiModelProperty(value = "rightOfFirstRefusalStatus")
    private String rightOfFirstRefusalStatus;

}
