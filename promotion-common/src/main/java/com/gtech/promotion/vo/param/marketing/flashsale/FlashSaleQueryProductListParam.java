/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.vo.param.marketing.flashsale;

import com.gtech.commons.page.PageParam;
import com.gtech.commons.utils.CheckUtils;
import com.gtech.promotion.exception.ErrorCodes;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

@Data
@EqualsAndHashCode(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("FlashSaleQueryProductListRequest")
public class FlashSaleQueryProductListParam extends PageParam {

    @ApiModelProperty(value = "Domain code.",example = "D00001",required = true)
    private String domainCode;

    @ApiModelProperty(value = "Tenant code.",example = "T00001",required = true)
    private String tenantCode;

    @ApiModelProperty(value = "Language id")
    private String language;

    @ApiModelProperty(value = "Activity code",required = true)
    private String activityCode;

    /**
     * Parameter validation.
     */
    public void validate() {

        CheckUtils.isNotBlank(this.domainCode, ErrorCodes.PARAM_EMPTY, "domainCode");
        CheckUtils.isNotBlank(this.tenantCode, ErrorCodes.PARAM_EMPTY, "tenantCode");
        CheckUtils.isNotBlank(this.activityCode, ErrorCodes.PARAM_EMPTY, "activityCode");
    }

}
