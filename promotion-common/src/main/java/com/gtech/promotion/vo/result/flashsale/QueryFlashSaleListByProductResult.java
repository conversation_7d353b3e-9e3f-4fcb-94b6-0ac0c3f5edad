/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.vo.result.flashsale;

import com.gtech.promotion.vo.bean.ActivityPeriod;
import com.gtech.promotion.vo.bean.Qualification;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 根据商品查询促销列表出参实体(出参)
 */
@Getter
@Setter
@ToString
@EqualsAndHashCode(callSuper = false)
@ApiModel("QueryFlashSaleListByProductResponse")
public class QueryFlashSaleListByProductResult extends ActivityLanguageResult implements Serializable {

    private static final long serialVersionUID = -5772096217070334884L;
    @ApiModelProperty(value = "Activity type：01-Activity 02-Coupon 03-Lucky draw 04-Flash sale")
    private String activityType;

    @ApiModelProperty(value = "Activity code.")
    private String activityCode;

    @ApiModelProperty("Activity begin time")
    private String activityBegin;//

    @ApiModelProperty("Activity end time")
    private String activityEnd;//

    @ApiModelProperty(value = "Activity warm-up begin time.")
    private String warmBegin;

    @ApiModelProperty(value = "Activity cool down time.")
    private String coolDown;

    @ApiModelProperty(value = "Ops create activity type")
    private String opsType;

    // Member level codes list.
    @ApiModelProperty(value = "Qualification list.")
    private List<Qualification> qualifications;

    @ApiModelProperty("Warm end time")
    private String warmEnd;//

    @ApiModelProperty("Activity URL")
    private String activityUrl;//

    @ApiModelProperty(value = "Activity background image.")
    private String backgroundImage;

    @ApiModelProperty(value = "Ribbon image")
    private String ribbonImage;

    @ApiModelProperty(value = "Ribbon position")
    private String ribbonPosition;

    @ApiModelProperty(value = "Ribbon text")
    private String ribbonText;

    @ApiModelProperty(value = "Sponsors")
    private String sponsors;

    @ApiModelProperty(value = "Flash sale price")
    private BigDecimal flashPrice;

    @ApiModelProperty(value = "sku inventory")
    private Integer skuInventory;

    @ApiModelProperty(value = "max pre user")
    private Integer maxPerUser;

    @ApiModelProperty(value = "Activity period.")
    private ActivityPeriod activityPeriod;

    @ApiModelProperty(value = "pre sale pay type")
    private String preSalePayType;

    @ApiModelProperty(value = "shipping time")
    private String shippingTime;

    @ApiModelProperty(value = "import no")
    private String importNo;

    @ApiModelProperty(value = "member inventory")
    private Integer memberInventory;

    //拼团活动字段
    @ApiModelProperty(value = "Group leader price")
    private BigDecimal groupLeaderPrice;
    @ApiModelProperty("时效字段")
    private Integer effectiveHour;
    @ApiModelProperty("成团人数")
    private Integer groupSize;
    @ApiModelProperty("单数限制（单/人）")
    private Integer orderCount;
    @ApiModelProperty("允许自动成团，默认开， 0开 1关闭")
    private Integer autoGroupFlag;
    @ApiModelProperty("允许超员成团，0开 1关闭")
    private Integer allowGroupFlag;
    @ApiModelProperty("团长福利")
    private String leaderBenefits;
    @ApiModelProperty("单数限制 1限制，0单数字段没有意义")
    private Integer orderCountFlag;
}
