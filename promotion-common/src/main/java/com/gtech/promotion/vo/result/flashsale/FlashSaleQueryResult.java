package com.gtech.promotion.vo.result.flashsale;

import com.gtech.promotion.api.ApiConstants;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class FlashSaleQueryResult implements Serializable {

    private static final long serialVersionUID = 3046742133936120483L;

    @ApiModelProperty(value = "activity code.",example = "12556568987",required = true)
    private String activityCode;

    @ApiModelProperty(value = "Activity type.04-Flash sale")
    private String activityType;

    @ApiModelProperty(value = "Activity ops type. 401-Flash sale",required = true)
    private String opsType;

    @ApiModelProperty(value= ApiConstants.ACTIVITY_STATUS)
    private String activityStatus;

    @ApiModelProperty(value = "Activity name",required = true)
    private String activityName;

    @ApiModelProperty(value = "Activity begin",required = true)
    private String activityBegin;

    @ApiModelProperty(value = "Activity end",required = true)
    private String activityEnd;

    @ApiModelProperty(value = "Activity url")
    private String activityUrl;

    @ApiModelProperty(value = "Activity sponsors",required = true)
    private String sponsors;

}
