package com.gtech.promotion.vo.result.boostsharding;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@ApiModel(value = "ExportBoostSharingTotalResponse")
@Data
public class ExportBoostSharingTotalResult {

    /*活动名称
        活动时间
        活动类型
        数据统计时间
        当前发起活动人数
        发起活动成功数
        发起活动未成功数
        助力人数*/
    @ApiModelProperty(value = "Activity name")
    private String activityName;
    @ApiModelProperty(value = "Activity time")
    private String activityTime;
    @ApiModelProperty(value = "Activity type")
    private String activityType;
    @ApiModelProperty(value = "Data statistics time")
    private String dataStatisticsTime;
    @ApiModelProperty(value = "Current activity number")
    private String currentActivityNumber;
    @ApiModelProperty(value = "Activity success number")
    private String activitySuccessNumber;
    @ApiModelProperty(value = "Activity unsuccessful number")
    private String activityUnsuccessfulNumber;
    @ApiModelProperty(value = "Boost number")
    private String boostNumber;


}
