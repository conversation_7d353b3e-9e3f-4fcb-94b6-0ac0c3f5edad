package com.gtech.promotion.vo.result.marketing;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class LuckyDrawPrizeResult implements Serializable {

    private static final long serialVersionUID = -5315546778145837005L;
    @ApiModelProperty(value = "Prize no", required = true)
    private String prizeNo;

    @ApiModelProperty(value = "Prize code", required = true)
    private String prizeCode;

    @ApiModelProperty(value = "Prize name", required = true)
    private String prizeName;

    @ApiModelProperty(value = "Prize image")
    private String prizeImage;

    @ApiModelProperty(value = "Prize order", required = true)
    private Integer prizeOrder;

    @ApiModelProperty(value = "Prize number per prize. eg. (coupon * 2)")
    private Integer prizeNum;
    // 奖品类型，01-券
    @ApiModelProperty(value = "Prize type. 00-no prize, 01-coupon", required = true)
    private String prizeType;

}
