package com.gtech.promotion.vo.param.coupon;

import com.gtech.commons.utils.CheckUtils;
import com.gtech.promotion.exception.ErrorCodes;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(description = "ReserveCouponQuotaRequest")
public class ReserveCouponQuotaParam implements Serializable {

    private static final long serialVersionUID = -7632335231696958650L;
    @ApiModelProperty( value="Domain code.", example="DC0001",required=true)
    private String domainCode;

    @ApiModelProperty(value = "Tenant code", required = true)
    private String tenantCode;

    @ApiModelProperty(value = "Order number.",required = true)
    private String orderNo;

    /**
     * Parameter validation.
     */
    public void validate() {

        CheckUtils.isNotBlank(this.domainCode, ErrorCodes.PARAM_EMPTY, "domainCode");
        CheckUtils.isNotBlank(this.tenantCode, ErrorCodes.PARAM_EMPTY, "tenantCode");
        CheckUtils.isNotBlank(this.orderNo, ErrorCodes.PARAM_EMPTY, "orderNo");
    }
}
