package com.gtech.promotion.vo.param.marketing;

import com.gtech.commons.utils.CheckUtils;
import com.gtech.promotion.exception.ErrorCodes;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.util.CollectionUtils;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2021/4/26 14:32
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("JudgeQualificationRequest")
public class EligibleActivitiesParam implements Serializable {
    private static final long serialVersionUID = 5573724159233899187L;


    @ApiModelProperty(value = "domain Code",required = true)
    private String domainCode;

    @ApiModelProperty(value = "tenant code Code",required = true)
    private String tenantCode;

    @ApiModelProperty(value = "member code",required = true)
    private String memberCode;

    @ApiModelProperty(value = "product code list",required = true)
    private List<String> productCodes;

    @ApiModelProperty(value = "org")
    String orgCode;

    @ApiModelProperty(value = "product amount",required = true)
    private BigDecimal amount;

    @ApiModelProperty(value = "product money",required = true)
    private BigDecimal quantity;

    @ApiModelProperty(value = "member qualifications")
    private Map<String, List<String>> qualifications;

    /**
     * Parameter validation.
     */
    public void validate() {

        CheckUtils.isTrue(!CollectionUtils.isEmpty(productCodes), ErrorCodes.PARAM_EMPTY, "productCodes");
        CheckUtils.isNotBlank(this.domainCode,ErrorCodes.PARAM_EMPTY,"domainCode");
        CheckUtils.isNotBlank(this.tenantCode,ErrorCodes.PARAM_EMPTY,"tenantCode");
        CheckUtils.isNotBlank(this.memberCode,ErrorCodes.PARAM_EMPTY,"memberCode");
        CheckUtils.isTrue(null!=this.amount,ErrorCodes.PARAM_EMPTY,"amount");
        CheckUtils.isTrue(null!=this.quantity,ErrorCodes.PARAM_EMPTY,"quantity");

    }


}
