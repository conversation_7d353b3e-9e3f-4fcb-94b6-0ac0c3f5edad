package com.gtech.promotion.vo.result.purchaseconstraint;

import com.gtech.promotion.vo.param.activity.CustomCondition;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.util.Date;
import java.util.List;

@Data
@ToString
@ApiModel("QueryPurchaseConstraintListResponse")
public class QueryPurchaseConstraintListResult {
    /**
     * 租户号
     */
    @ApiModelProperty(value = "Tenant code")
    private String tenantCode;

    /**
     * 业务域
     */
    @ApiModelProperty(value = "Domain code")
    private String domainCode;

    /**
     * 组织编号
     */
    @ApiModelProperty(value = "Org code")
    private String orgCode;

    /**
     * 限购Code
     */
    @ApiModelProperty(value = "Purchase constraint code")
    private String purchaseConstraintCode;

    /**
     * 限购名称
     */
    @ApiModelProperty(value = "Purchase constraint name")
    private String purchaseConstraintName;

    /**
     * 限购开始时间
     */
    @ApiModelProperty(value = "Purchase constraint start time")
    private Date purchaseConstraintStartTime;

    /**
     * 限购结束时间
     */
    @ApiModelProperty(value = "Purchase constraint end time")
    private Date purchaseConstraintEndTime;

    /**
     * 状态
     * @see com.gtech.promotion.code.purchaseconstraint.PurchaseConstraintStatusEnum
     */
    @ApiModelProperty(value = "Purchase constraint status")
    private String purchaseConstraintStatus;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "Purchase constraint create time")
    private Date createTime;

    /**
     * 更新时间
     */
    @ApiModelProperty(value = "Purchase constraint update time")
    private Date updateTime;

    /**
     * 创建人
     */
    @ApiModelProperty(value = "Purchase constraint create user")
    private String createUser;

    /**
     * 更新人
     */
    @ApiModelProperty(value = "Purchase constraint update user")
    private String updateUser;

    /**
     * 周期类型：00-全时段 01-自定义
     */
    @ApiModelProperty(value = "Purchase constraint period type")
    private String periodType;

    /**
     * 商品范围正反选：01-正选；02-反选
     */
    @ApiModelProperty(value = "Purchase constraint product selection type")
    private String productSelectionType;

    /**
     * Product item scope type: 1-All scope 2-By spu in scope
     */
    @ApiModelProperty(value = "Purchase constraint product item scope type")
    private Integer itemScopeType;

    /**
     * 店铺范围：00-全店铺 01-自定义
     */
    @ApiModelProperty(value = "Purchase constraint product store type")
    private String storeType;

    /**
     * 审核人
     */
    @ApiModelProperty(value = "Purchase constraint product audit user")
    private String auditUser;

    /**
     * 是否开启会员优先可见
     */
    @ApiModelProperty(value = "Purchase constraint customer right first refusal")
    private Integer firstRefusal;

    /**
     * 创建人名
     */
    @ApiModelProperty(value = "Purchase constraint create user first name")
    private String createUserFirstName;

    /**
     * 创建人姓
     */
    @ApiModelProperty(value = "Purchase constraint create user last name")
    private String createUserLastName;

    /**
     * 限购优先级
     */
    @ApiModelProperty(value = "Purchase constraint priority")
    private Integer priority;


    @ApiModelProperty(value = "Custom conditions.")
    private List<CustomCondition> customConditions;
}
