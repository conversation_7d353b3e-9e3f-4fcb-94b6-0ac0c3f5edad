/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * <PERSON>ECH MAKES NO REPRESENTATIONS OR <PERSON><PERSON><PERSON>NTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.vo.result.marketing;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("LuckyDrawResponse")
public class LuckyDrawResult implements Serializable {

    public static final String LUCKY = "00";
    public static final String NO_LUCKY = "01";
    public static final String NO_INVENTORY = "02";

    private static final long serialVersionUID = -2865221960273984445L;

    @ApiModelProperty(value = "Prize list", required = true)
    private List<Prize> prizes;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @ApiModel("LuckyDrawResult#Prize")
    public static class Prize implements Serializable{
        private static final long serialVersionUID = -137460528719755951L;
        //00-中奖 01-未中奖（中空奖品也算中奖）02-奖品没有库存
        @ApiModelProperty(value = "Prize code . 00- Winning 01- Not winning (hollow prizes count as winning prizes) 02- No prizes in stock", required = true)
        private String resultCode;

        @ApiModelProperty(value = "Prize no", required = true)
        private String prizeNo;

        @ApiModelProperty(value = "Prize code", required = true)
        private String prizeCode;

        @ApiModelProperty(value = "Prize name", required = true)
        private String prizeName;

        @ApiModelProperty(value = "Prize image")
        private String prizeImage;

        @ApiModelProperty(value = "Prize order", required = true)
        private Integer prizeOrder;

        @ApiModelProperty(value = "Prize number per prize. eg. (coupon * 2)")
        private Integer prizeNum;
        // 奖品类型，01-券
        @ApiModelProperty(value = "Prize type. 00-no prize, 01-coupon", required = true)
        private String prizeType;
    }
}
