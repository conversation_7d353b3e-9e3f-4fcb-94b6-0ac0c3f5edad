package com.gtech.promotion.vo.param.activity;

import com.gtech.commons.utils.CheckUtils;
import com.gtech.promotion.checker.activity.TPromoActivityChecker;
import com.gtech.promotion.code.activity.ActivityStatusEnum;
import com.gtech.promotion.exception.Check;
import com.gtech.promotion.exception.ErrorCodes;
import com.gtech.promotion.page.RequestPage;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;

import java.io.Serializable;
import java.util.List;

/**
 * @Description :
 * <AUTHOR> gaojie.li
 * @Date : 2023/7/16 9:57
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("GroupQueryListRequest")
public class GroupQueryListParam extends RequestPage implements Serializable {


    private static final long serialVersionUID = -4670761390963851456L;

    @ApiModelProperty(value = "租户编码", example = "880001", required = true)
    private String tenantCode;

    @ApiModelProperty(value = "Domain code", required = true)
    private String domainCode;

    @ApiModelProperty(value = "activity org code")
    private String activityOrgCode;

    @ApiModelProperty(value = "Group code")
    private String groupCode;

    @ApiModelProperty(value = "Activity name", example = "活动名称")
    private String activityName;

    @ApiModelProperty(value = "Activity status", example = "123")
    private List<String> activityStatus;

    @ApiModelProperty(value = "Language id")
    private String language;


    public void checkParam() {

        CheckUtils.isNotBlank(this.domainCode, ErrorCodes.PARAM_EMPTY, "domainCode");
        CheckUtils.isNotBlank(this.tenantCode, ErrorCodes.PARAM_EMPTY, "tenantCode");

        if (CollectionUtils.isNotEmpty(activityStatus)) {

            for (String status : activityStatus) {

                Check.check(!ActivityStatusEnum.exist(status), TPromoActivityChecker.NOT_ACTIVITY_STATUS_TYPE);

            }
        }
    }

}
