package com.gtech.promotion.vo.bean;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@ApiModel("GiveawayRule")
@Data
public class GiveawayRule {
    /**
     *
     *     // 赠送规则
     *     {
     *       "giveawayMethod": "{{giveawayMethod}}",
     *       // 赠送方式 1全部 2 部分
     *       "giveawayChooseQty": "{{giveawayChooseQty}}",
     *       // 可选数量 giveawayMethod等于2 必填
     *       "giveawaySortRule": "{{giveawaySortRule}}",
     *       // 赠品排序规则,越小靠前 1有序 2随机
     *       "rankParam": 1
     *       // 层级
     *     }
     *
     */


    @ApiModelProperty(value = "赠送方式 1全部 2 部分")
    private String giveawayMethod;
    @ApiModelProperty(value = "可选数量 giveawayMethod等于2 必填")
    private String giveawayChooseQty;
    @ApiModelProperty(value = "赠品排序规则,越小靠前 1有序 2随机")
    private String giveawaySortRule;
    @ApiModelProperty(value = "层级")
    private Integer rankParam;



}
