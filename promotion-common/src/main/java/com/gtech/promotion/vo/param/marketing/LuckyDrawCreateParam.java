package com.gtech.promotion.vo.param.marketing;

import com.gtech.commons.utils.CheckUtils;
import com.gtech.promotion.checker.marketing.MarketingChecker;
import com.gtech.promotion.code.marketing.PrizeTypeEnum;
import com.gtech.promotion.exception.Check;
import com.gtech.promotion.exception.ErrorCodes;
import com.gtech.promotion.vo.bean.IncentiveLimited;
import com.gtech.promotion.vo.bean.LuckyDrawRule;
import com.gtech.promotion.vo.bean.Qualification;
import com.gtech.promotion.vo.bean.marketing.MarketingPrize;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.springframework.util.CollectionUtils;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
public class LuckyDrawCreateParam extends MarketingCreateParam implements Serializable {

    private static final BigDecimal HUNDRED = new BigDecimal("100");
    private static final long serialVersionUID = 2990674549722727433L;

    @ApiModelProperty(value = "Prize list.",required = true)
    private List<MarketingPrize> marketingPrizes;

    @ApiModelProperty(value = "List of eligibility (blank indicates unlimited)")
    private List<Qualification> qualifications;

    @ApiModelProperty(value = "Incentive limited list. It can't be empty while incentiveLimitedFlag is '01'.")
    private List<IncentiveLimited> incentiveLimiteds;

    @ApiModelProperty(value = "List of Participation Rules")
    private List<LuckyDrawRule> luckyDrawRules;

    @Override
    public void validate() {
        super.validate();
        CheckUtils.isTrue(!CollectionUtils.isEmpty(marketingPrizes), ErrorCodes.PARAM_EMPTY, "marketingPrizes");

        BigDecimal totalProbability = BigDecimal.ZERO;
        int emptyPrizeCount = 0;
        for (MarketingPrize marketingPrize : marketingPrizes) {
            marketingPrize.validate();
            if (PrizeTypeEnum.DEFAULT.equalsCode(marketingPrize.getPrizeType())){
                emptyPrizeCount ++;
            }
            totalProbability = totalProbability.add(marketingPrize.getPrizeProbability());
        }

        Check.check(totalProbability.compareTo(HUNDRED) > 0, MarketingChecker.PROBABILITY_TOTAL_100);

        // 分摊空奖品的概率
        if (emptyPrizeCount > 0) {
            emptyPrizeCount(totalProbability, emptyPrizeCount);
        }
        if(CollectionUtils.isEmpty(incentiveLimiteds)){
            setIncentiveLimitedFlag("00");
        }else {
            setIncentiveLimitedFlag("01");
        }
        if(CollectionUtils.isEmpty(luckyDrawRules)){
            setLuckyDrawRuleFlag("00");
        }else {
            setLuckyDrawRuleFlag("01");
        }
    }

    public void emptyPrizeCount(BigDecimal totalProbability, int emptyPrizeCount) {
        BigDecimal remainTotalProbability = HUNDRED.subtract(totalProbability);
        BigDecimal divide = remainTotalProbability.divide(new BigDecimal(emptyPrizeCount), 2, BigDecimal.ROUND_DOWN);
        for (int i = 0; i < marketingPrizes.size(); i++) {
            MarketingPrize marketingPrize = marketingPrizes.get(i);
            if (PrizeTypeEnum.DEFAULT.equalsCode(marketingPrize.getPrizeType())){
                emptyPrizeCount --;
                if (emptyPrizeCount <= 0){
                    marketingPrize.setPrizeProbability(remainTotalProbability);
                }else {
                    marketingPrize.setPrizeProbability(divide);
                    remainTotalProbability = remainTotalProbability.subtract(divide);
                }
            }
        }
    }
}
