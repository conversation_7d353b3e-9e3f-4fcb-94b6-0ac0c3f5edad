package com.gtech.promotion.vo.result.activity;

import com.gtech.promotion.api.ApiConstants;
import com.gtech.promotion.vo.bean.*;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class QueryPromoListByStoreResult implements Serializable {

    private static final long serialVersionUID = 703322487140029493L;
    @ApiModelProperty(value = "Activity code.",example = "23")
    private String activityCode;

    @ApiModelProperty(value = "Activity Name")
    private String activityName;

    @ApiModelProperty("Activity sponsors")
    private String sponsors;

    @ApiModelProperty(value = "Activity type：01-Activity 02-Coupon")
    private String activityType;

    @ApiModelProperty(value = "Activity label.")
    private String activityLabel;

    @ApiModelProperty(value = "Activity description.")
    private String activityDesc;

    @ApiModelProperty(value = "Activity remark.")
    private String activityRemark;

    @ApiModelProperty(value = "Activity begin time.")
    private String activityBegin;

    @ApiModelProperty(value = "Activity end time.")
    private String activityEnd;

    @ApiModelProperty(value = "Activity warm-up begin time.")
    private String warmBegin;

    @ApiModelProperty(value = "Activity status.")
    private String activityStatus;

    @ApiModelProperty(value = "Activity url.")
    private String activityUrl;

    @ApiModelProperty(value = "Giving-gift list of the activity.")
    private List<Giveaway> giveaways;

    @ApiModelProperty(value = "Product scope list. seqNum equals 1 means condition product. seqNum equals 2 means incentive product.")
    private List<ProductScope> products;

    @ApiModelProperty(value = "Product detail list. seqNum equals 1 means condition product. seqNum equals 2 means incentive product.")
    private List<ProductDetail> productDetils;

    @ApiModelProperty(value = "Qualification list. Empty means not limited.")
    private List<Qualification> qualifications;

    @ApiModelProperty(value = "Ops create Activity type")
    private String opsType;

    @ApiModelProperty(value = "Activity cool down time.")
    private String coolDown;

    @ApiModelProperty(value = "Promotion code.")
    private String promotionCode;

    @ApiModelProperty(ApiConstants.PRODUCT_SELECTION_TYPE)
    private String productSelectionType;

    @ApiModelProperty(value = "Activity background image.")
    private String backgroundImage;

    @ApiModelProperty(value = "Ribbon image.")
    private String ribbonImage;

    @ApiModelProperty(value = "Position of ribbon image .")
    private String ribbonPosition;

    @ApiModelProperty(value = "Ribbon text.")
    private String ribbonText;

    @ApiModelProperty(value = "Activity period.")
    private ActivityPeriod activityPeriod;
}
