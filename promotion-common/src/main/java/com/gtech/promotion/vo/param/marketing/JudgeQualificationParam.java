package com.gtech.promotion.vo.param.marketing;

import com.gtech.commons.utils.CheckUtils;
import com.gtech.commons.utils.StringUtil;
import com.gtech.promotion.code.coupon.CouponFrozenStatusEnum;
import com.gtech.promotion.exception.ErrorCodes;
import com.gtech.promotion.vo.bean.ProductCodeAndQuantity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.util.CollectionUtils;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2021/1/28 10:09
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("JudgeQualificationRequest")
public class JudgeQualificationParam implements Serializable {

    private static final long serialVersionUID = 6831494271217635804L;

    @ApiModelProperty(value = "domain Code",required = true)
    String domainCode;

    @ApiModelProperty(value = "tenant code Code",required = true)
    String tenantCode;

    @ApiModelProperty(value = "member code",required = true)
    String memberCode;

    @ApiModelProperty(value = "product code list",required = true)
    private List<String> productCodes;

    @ApiModelProperty(value = "product and amount")
    private List<ProductCodeAndQuantity> productCodeAndQuantity;


    @ApiModelProperty(value = "org")
    String orgCode;

    //amount 价格
    @ApiModelProperty(value = "product amount",required = true)
    BigDecimal amount;

    //quantity 数量
    @ApiModelProperty(value = "product money",required = true)
    BigDecimal quantity;

    @ApiModelProperty(value = "frozen status 01-UnFrozen 02-Frozen")
    String frozenStatus;

    @ApiModelProperty(value = "member qualifications")
    private Map<String, List<String>> qualifications;

    /**
     * Parameter validation.
     */
    public void validate() {

        CheckUtils.isTrue(!CollectionUtils.isEmpty(productCodes), ErrorCodes.PARAM_EMPTY, "productCodes");
        CheckUtils.isNotBlank(this.domainCode,ErrorCodes.PARAM_EMPTY,"domainCode");
        CheckUtils.isNotBlank(this.tenantCode,ErrorCodes.PARAM_EMPTY,"tenantCode");
        CheckUtils.isNotBlank(this.memberCode,ErrorCodes.PARAM_EMPTY,"memberCode");
        CheckUtils.isTrue(null!=this.amount,ErrorCodes.PARAM_EMPTY,"amount");
        CheckUtils.isTrue(null!=this.quantity,ErrorCodes.PARAM_EMPTY,"quantity");
        if (StringUtil.isNotBlank(frozenStatus)){
            CheckUtils.isTrue(CouponFrozenStatusEnum.exist(frozenStatus), ErrorCodes.PARAM_ERROR, "frozenStatus");
        }

    }





}
