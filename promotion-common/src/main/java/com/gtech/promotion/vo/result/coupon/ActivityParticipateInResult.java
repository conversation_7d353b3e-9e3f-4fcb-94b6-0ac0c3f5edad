package com.gtech.promotion.vo.result.coupon;

import com.alibaba.fastjson.annotation.JSONField;
import com.gtech.commons.utils.DateUtil;
import com.gtech.promotion.code.activity.ActivityStatusEnum;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2021/4/23 10:01
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ActivityParticipateInResult implements Serializable {
    private static final long serialVersionUID = -7899707426844889196L;

    // Domain code.
    private String domainCode;

    // Tenant code
    private String tenantCode;

    // org code.
    private String orgCode;

    private String activityCode;

    //送券数量
    private Integer earnTicket;

    private String activityType;
    private String opsType;
    private String activityName;
    private String activityBegin;
    private String activityEnd;
    // 活动状态：01-待提交 02-审核中 03-已拒绝 04-已生效 05-已结束 06-暂停中 07-已终止
    private String activityStatus;
    private String activityUrl;
    private String sponsors;
    private String backgroundImage;
    private String ribbonImage;
    private String ribbonPosition;
    private String ribbonText;
    private String coolDown;
    private String warmBegin;
    private String warmEnd;
    private String createUser;
    private String updateUser;
    private String createUserFirstName;
    private String createUserLastName;
    private String syncPriceStatus;
    private String incentiveLimitedFlag;
    private String luckyDrawRuleFlag;

    /**
     * 判断是否需要做过期处理
     */
    @JSONField(serialize = false)
    public boolean isNeedToDoExpire() {

        return ActivityStatusEnum.EFFECTIVE.equalsCode(this.activityStatus)
                && DateUtil.parseDate(this.activityEnd, DateUtil.FORMAT_YYYYMMDDHHMISS_14).getTime() <= System.currentTimeMillis();
    }









}
