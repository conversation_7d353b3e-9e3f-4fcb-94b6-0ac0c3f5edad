package com.gtech.promotion.vo.result.flashsale;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;

@Getter
@Setter
@ToString
@ApiModel("CreateHelpRecordResponse")
public class CreateHelpRecordResult implements Serializable {
    private static final long serialVersionUID = -2613595478434828513L;

    @ApiModelProperty(value = "activityCode")
    private String activityCode;
    @ApiModelProperty(value = "domainCode")
    private String domainCode;
    @ApiModelProperty(value = "tenantCode")
    private String tenantCode;
    @ApiModelProperty(value = "orgCode")
    private String orgCode;
    @ApiModelProperty(value = "sharingRecordCode")
    private String sharingRecordCode;
    @ApiModelProperty(value = "sharingMemberCode")
    private String sharingMemberCode;
    @ApiModelProperty(value = "helpRecordCode")
    private String helpRecordCode;
    @ApiModelProperty(value = "helpRecordStatus")
    private String helpRecordStatus;
    @ApiModelProperty(value = "helpMemberCode")
    private String helpMemberCode;
    @ApiModelProperty(value = "sharingRecordStatus")
    private String sharingRecordStatus;

    @ApiModelProperty(value = "boostSharingRewardsResult")
    private BoostSharingRewardsResult boostSharingRewardsResult;

}
