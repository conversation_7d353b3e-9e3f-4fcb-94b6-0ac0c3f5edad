/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.vo.param;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONException;
import com.gtech.commons.utils.CheckUtils;
import com.gtech.promotion.api.ApiConstants;
import com.gtech.promotion.exception.ErrorCodes;
import com.gtech.promotion.vo.bean.ShoppingCartStore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import org.springframework.util.CollectionUtils;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 购物车查询计算的参数实体 属性有”租户信息“和”订单活动列表“以及“商品列表，商品里面包含其所属活动” 等
 */
@Getter
@Setter
@ToString
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("ShoppingCartRequest")
public class ShoppingCartParam implements Serializable {

    private static final long serialVersionUID = 2986124484100533156L;

    @ApiModelProperty( value="Domain code.", example="D00001",required=true)
    private String domainCode;

    @ApiModelProperty( value="Tenant code.", example="T00001",required=true)
    private String tenantCode;

    @ApiModelProperty(value = "Language id")
    private String language;

    @ApiModelProperty(value = "Deprecated. 渠道编码",required = false)
    private Integer channelCode;

    @ApiModelProperty(value = "Member Code",required = true)
    private String memberCode;

    @ApiModelProperty(value = ApiConstants.QUALIFICATIONS)
    private Map<String, List<String>> qualifications;

    @ApiModelProperty(value = "Custom conditions",example = " {\n" +
            "        \"credit_card\": \"356536\",\n" +
            "        \"bank_transfer\": \"bri_va\"\n" +
            "    }")
    private Map<String,String> customConditionMap;
    /**
     * @deprecated
     */
    @ApiModelProperty(value = "Deprecated. 会员等级编码",required = false)
    @Deprecated
    private String memberLevelCode; //NOSONAR

    /**
     * @deprecated
     */
    @ApiModelProperty(value = "Deprecated. 会员标签编码（多个用逗号隔开）",required = false)
    @Deprecated
    private String memberLabelCode; //NOSONAR

    @ApiModelProperty(value = "used coupon codes in order (Separated by commas)",required = false)
    private String couponCodes;

    @ApiModelProperty(value = "Promotion time.(yyyyMMddHHmmss)",required = false)
    private String promotionTime;

    @ApiModelProperty(value = "Cart item list by store.",required = true)
    private List<ShoppingCartStore> cartStoreList;

    @ApiModelProperty(value = "postage")
    private BigDecimal postage;

    /**
     * Parameter validation.
     */
    public void validate() {

        CheckUtils.isNotBlank(this.domainCode, ErrorCodes.PARAM_EMPTY, "domainCode");
        CheckUtils.isNotBlank(this.tenantCode, ErrorCodes.PARAM_EMPTY, "tenantCode");
        CheckUtils.isNotBlank(this.memberCode, ErrorCodes.PARAM_EMPTY, "memberCode");

        CheckUtils.isTrue(!CollectionUtils.isEmpty(this.cartStoreList), ErrorCodes.PARAM_EMPTY, "cartStoreList");
        for(ShoppingCartStore cs : this.cartStoreList) {
            cs.validate();
        }
        if (null == postage){
            postage = BigDecimal.ZERO;
        }
        if (!CollectionUtils.isEmpty(customConditionMap)){
            Set<Map.Entry<String, String>> keyMap = customConditionMap.entrySet();

            for (Map.Entry<String, String> keySet : keyMap) {
                String key = keySet.getKey();
                String value = keySet.getValue();
                CheckUtils.isNotBlank(key, ErrorCodes.CUSTOM_KEY, "customConditionMap");
                try {
                    JSONArray.parseArray(value).toJavaList(String.class);
                }catch (JSONException exception){
                    CheckUtils.isTrue(false, ErrorCodes.CUSTOM_KEY_VALUE, "customConditionMap");
                }
            }
        }
    }

}
