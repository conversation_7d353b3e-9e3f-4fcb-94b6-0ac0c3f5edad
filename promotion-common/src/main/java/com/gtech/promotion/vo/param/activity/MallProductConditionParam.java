package com.gtech.promotion.vo.param.activity;

import com.gtech.promotion.vo.bean.ProductAttribute;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * @Description :
 * <AUTHOR> gaojie.li
 * @Date : 2023/5/17 15:11
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class MallProductConditionParam implements Serializable {

    @ApiModelProperty( value="Product black list.")
    private List<String> productBlackList;


    @ApiModelProperty(value = "Product code list.conditionProductType =02 时入参")
    private List<String> productCode;

    @ApiModelProperty(value = "Sku code list. conditionProductType =02 时入参")

    private List<String> skuCodes;

    @ApiModelProperty(value = "Product category code list. conditionProductType =01 时入参")
    private List<String> categoryCodes;


    @ApiModelProperty(value = "Product brand code list. conditionProductType =01 时入参")
    private List<String>  brandCodes;


    @ApiModelProperty(value = "01 sku属性 02 商品属性.conditionProductType =01 时入参")
    private String attrType;

    @ApiModelProperty(value = "属性. attrType类型 对应的属性; conditionProductType =01 时入参")
    private List<ProductAttribute> attributes;

}
