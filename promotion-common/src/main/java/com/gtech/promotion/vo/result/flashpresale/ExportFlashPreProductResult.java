package com.gtech.promotion.vo.result.flashpresale;

import lombok.Data;

import java.io.Serializable;

/**
 * @Description :
 * <AUTHOR> gaojie.li
 * @Date : 2023/12/27 10:32
 */
@Data
public class ExportFlashPreProductResult implements Serializable {


    private static final long serialVersionUID = 7450231581827744626L;

    private String timeOfStatistics;


    private String productNumber;


    private String productName;


    private Integer ofPaidProducts;


    private Long ofPaidOrders;


    private Double amountOfPaidProducts;

    private String maxOrderCode;

}
