package com.gtech.promotion.vo.param.activity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @Description :
 * <AUTHOR> gaojie.li
 * @Date : 2023/9/21 20:24
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("UpdateUserCouponRequest")
public class UpdateUserCouponParam implements Serializable {


    @ApiModelProperty(value = "Coupon code.",required = true)
    private String couponCode;

    @ApiModelProperty(value = "Status.",required = true)
    private String status;

    @ApiModelProperty(value = "Valid start time.")
    private String validStartTime;

    @ApiModelProperty(value = "Valid end time.")
    private String validEndTime;
}
