package com.gtech.promotion.vo.param.purchaseconstraint;

import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
public class PcRuleDecrementRequest {

    @NotBlank(message = "tenantCode not empty")
    private String tenantCode;
    @NotBlank(message = "userCode not empty")
    private String userCode;
    /**
     * 订单号
     */
    private String orderCode;
    /**
     * 订单状态
     */
    private String orderStatus;
    /**
     * 退货单号
     */
    private String returnCode;
    /**
     * 订单日期
     */
    private Date orderDate;

    private Boolean forward;

    /**
     * 计算类型
     * @see com.gtech.promotion.code.purchaseconstraint.PcRuleCalculateTypeEnum
     */
    private String type;
    /**
     * 增量商品数量
     */
    @NotNull
    @Size(min = 1, max = 1000)
    private List<@Valid IncrementProduct> incrementProducts;

    @Data
    public static class IncrementProduct {
        @NotBlank(message = "product not empty")
        private String productCode;
        @NotBlank(message = "skuCode not empty")
        private String skuCode;
        // sku折扣总价
        @NotNull
        private BigDecimal sellAmount;
        // sku总数
        @NotNull
        private Integer skuCount;
    }

}
