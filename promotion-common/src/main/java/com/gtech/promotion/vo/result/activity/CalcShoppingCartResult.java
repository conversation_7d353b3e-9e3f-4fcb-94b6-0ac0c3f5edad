/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 * <p>
 * This software is the confidential and proprietary information of GTech. You shall not disclose such
 * Confidential Information and shall use it only in accordance with the terms of the license agreement
 * you entered into with GTech.
 * <p>
 * <PERSON><PERSON><PERSON> MAKES NO REPRESENTATIONS OR WAR<PERSON>NTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.vo.result.activity;

import com.alibaba.fastjson.JSONObject;
import com.gtech.promotion.api.ApiConstants;
import com.gtech.promotion.vo.bean.Giveaway;
import com.gtech.promotion.vo.bean.GiveawaySettings;
import com.gtech.promotion.vo.param.activity.CustomCondition;
import com.gtech.promotion.vo.result.flashsale.MarketingLanguageResult;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 购物车计算出参
 *
 */
@Getter
@Setter
@ToString
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("CalcShoppingCartResponse")
public class CalcShoppingCartResult implements Serializable {

    private static final long serialVersionUID = 4564161427611589661L;

    @ApiModelProperty(value = "Activity code.")
    private String activityCode;

    @ApiModelProperty(value = "Activity name.")
    private String activityName;

    @ApiModelProperty(value = "Ops type.")
    private String opsType;

    @ApiModelProperty(value = "Promotion category.")
    private String promotionCategory;

    private String promotionCategoryName;

    // 开始时间：yyyyMMddhhmmss
    @ApiModelProperty(value = "活动开始时间")
    private String activityBegin;

    // 结束时间：yyyyMMddhhmmss
    @ApiModelProperty(value = "活动结束时间")
    private String activityEnd;

    // 活动描述
    @ApiModelProperty(value = "活动描述")
    private String activityDesc;

    // 活动备注
    @ApiModelProperty(value = "活动备注")
    private String activityRemark;

    @ApiModelProperty(value = "Coupon code.")
    private String couponCode;

    @ApiModelProperty(value = "Promotion scope: 01-Single product , 02-Product scope , 03-Order")
    private String promoScope;

    @ApiModelProperty(value = "Activity label.")
    private String activityLabel;

    @ApiModelProperty(value = "Activity type：01-Activity 02-Coupon")
    private String activityType;

    @ApiModelProperty(value = "Activity URL.")
    private String activityUrl;

    @ApiModelProperty(value = "Product selection type: 01-Selection, 02-Invert Selection")
    private String productSelectionType;

    @ApiModelProperty(value = "giveaway limit max")
    private String giveawayLimitMax;

    @ApiModelProperty(value = "Promotion match success flag.")
    private Boolean effectiveFlag;

    @ApiModelProperty(value = "Promotion match failed reason reason message.")
    private String falseReason;

    @ApiModelProperty(value = "Promotion match failed reason reason code.")
    private String falseCode;

    @ApiModelProperty(value = "The number of commodity that the promotion campaign lacks.")
    private String needMoreAmount;

    @ApiModelProperty(value = "Unit for needMoreAmount. 01-Amount, 02-Quantity")
    private String needMoreUnit;

    @ApiModelProperty(value = "Total discount amount (including promoRewardPostage) ")
    private BigDecimal promoRewardAmount;

    @ApiModelProperty(value = "Total amount of postage deduction")
    @Builder.Default
    private BigDecimal promoRewardPostage = BigDecimal.ZERO;

    @ApiModelProperty(value = ApiConstants.REWARD_TYPE)
    private String rewardType;

    @ApiModelProperty(value = "Single person limited number of purchases")
    private String userLimitation;

    @ApiModelProperty(value = " Activity sponsors")
    private String sponsors;

    private JSONObject extendParams;

    @ApiModelProperty(value = "Giveaway settings")
    private GiveawaySettings giveawaySettings;

    @ApiModelProperty(value = "Giveaway list")
    private List<Giveaway> giveaways;

    @ApiModelProperty(value = "List of items")
    private List<ShoppingCartItem> shoppingCartItems;

    @ApiModelProperty(value = "Ribbon image.")
    private String ribbonImage;

    @ApiModelProperty(value = "Position of Ribbon image .")
    private String ribbonPosition;

    @ApiModelProperty(value = "Ribbon text.")
    private String ribbonText;


    private List<CustomCondition> customConditionsList;

    @ApiModelProperty(value = "Languages activity.")
    private List<MarketingLanguageResult> languageResults;

    @Getter
    @Setter
    @ToString
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @ApiModel("CalcShoppingCartResult#ShoppingCartItem")
    public static class ShoppingCartItem implements Serializable {

        private static final long serialVersionUID = -6064917222102183332L;

        @ApiModelProperty(value = "Product sku code.")
        private String skuCode;

        //套装商品sku编码
        @ApiModelProperty(value = "Combine sku code")
        private String combineSkuCode;

        @ApiModelProperty(value = ApiConstants.SELECT_FLAG)
        private String selectionFlag;

        //商品原单价
        @ApiModelProperty(value = "Product price")
        private BigDecimal productPrice;

        //购买数量（该sku的总数量）
        @ApiModelProperty(value = "Quantity purchased (total number of skUs)")
        private Integer quantity;

        //参与促销数量（参与当前活动的数量）
        @ApiModelProperty(value = "Number of promotions participated (number of current campaigns participated)")
        private Integer promoQuantity;

        //促销后总价（参与所有活动后价格）
        @ApiModelProperty(value = "Total price after promotion (price after participating in all activities)")
        private BigDecimal promoAmount;

        //当前商品参与的促销活动列表
        @ApiModelProperty(value = "List of promotions that the current item participates in")
        private List<ShoppingCartItemActivity> shoppingCartItemActivitys;
    }

    @Getter
    @Setter
    @ToString
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @ApiModel("CalcShoppingCartResult#ShoppingCartItemActivity")
    public static class ShoppingCartItemActivity implements Serializable {

        private static final long serialVersionUID = -348441808556220208L;

        @ApiModelProperty(value = "Activity code")
        private String activityCode;

        @ApiModelProperty(value = "Price condition")
        private String priceCondition;

        //是否满足活动
        @ApiModelProperty(value = "Effective flag activity")
        private Boolean effectiveFlag;

        @ApiModelProperty(value = "Coupon code")
        private String couponCode;

        @ApiModelProperty(value = ApiConstants.PROMO_SCOPE)
        private String promoScope;//

        @ApiModelProperty(value = "Activity name")
        private String activityName;

        @ApiModelProperty(value = "Ops type.")
        private String opsType;

        @ApiModelProperty(value = "Promotion category")
        private String promotionCategory;

        // 开始时间：yyyyMMddhhmmss
        @ApiModelProperty(value = "活动开始时间")
        private String activityBegin;

        // 结束时间：yyyyMMddhhmmss
        @ApiModelProperty(value = "活动结束时间")
        private String activityEnd;

        // 活动描述
        @ApiModelProperty(value = "活动描述")
        private String activityDesc;

        // 活动备注
        @ApiModelProperty(value = "活动备注")
        private String activityRemark;

        @ApiModelProperty(value = "Activity label")
        private String activityLabel;

        @ApiModelProperty(value = ApiConstants.ACTIVITY_TYPE)
        private String activityType;

        //指定特价
        @ApiModelProperty(value = "指定特价")
        private BigDecimal promoPrice;//

        //参加活动计算前的商品项总价
        @ApiModelProperty(value = "The total price of the commodity item before the calculation of the activity")
        private BigDecimal beforeAmount;//

        //参加活动计算后的商品项总价
        @ApiModelProperty(value = "Total commodity price calculated after participating in the activity")
        private BigDecimal afterAmount;//

        //赠品赠送最大限制数量
        @ApiModelProperty(value = "Maximum number of gifts")
        private String giftLimitMax;//

        @ApiModelProperty(value = "Giveaways")
        private List<Giveaway> giveaways;//
    }
}
