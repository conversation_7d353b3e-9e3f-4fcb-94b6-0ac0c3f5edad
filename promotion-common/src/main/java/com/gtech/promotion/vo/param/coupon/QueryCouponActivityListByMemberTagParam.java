/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.vo.param.coupon;

import java.util.List;

import com.gtech.promotion.vo.param.activity.QueryActivityListParam;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * QueryCouponActivityListParam
 *
 * <AUTHOR>
 * @Date 2020-02-19
 */
@Getter
@Setter
@ToString
@ApiModel("QueryCouponActivityListByMemberTagParam")
public class QueryCouponActivityListByMemberTagParam extends QueryActivityListParam {

    private static final long serialVersionUID = 6557720702225554072L;

    @ApiModelProperty(value = "Coupon type: 01-Coupon 02-Anonymous stamps 03-Coupon code")
    private String couponType;

    @ApiModelProperty(value = "Coupon code")
    private String couponCode;

    @ApiModelProperty(value = "Member tag list")
    private List<String> memberTagList;

}
