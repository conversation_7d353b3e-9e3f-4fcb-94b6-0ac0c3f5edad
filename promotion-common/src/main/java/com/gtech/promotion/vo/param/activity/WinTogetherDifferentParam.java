package com.gtech.promotion.vo.param.activity;

import com.gtech.promotion.api.ApiConstants;
import com.gtech.promotion.code.activity.ItemScopeTypeEnum;
import com.gtech.promotion.code.activity.ProductSelectionEnum;
import com.gtech.promotion.vo.bean.FunctionParam;
import com.gtech.promotion.vo.bean.Giveaway;
import com.gtech.promotion.vo.bean.ProductDetail;
import com.gtech.promotion.vo.bean.ProductScope;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.util.List;

/**
 * @Description :
 * <AUTHOR> gaojie.li
 * @Date : 2021/7/20 15:45
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@ApiModel("WinTogetherDifferentRequest")
public class WinTogetherDifferentParam implements Serializable {

    private static final long serialVersionUID = -2413446906858439106L;

    @ApiModelProperty(value = "Activity code.", required = true, example = "23")
    private String activityCode;

    @ApiModelProperty("ops create type")
    private String opsType;

    @ApiModelProperty(value = "Giving-gift list of the activity.")
    private List<Giveaway> giveaways;

    @ApiModelProperty(value = "Parameters list of the activity template function.", required = true)
    private List<FunctionParam> funcParams;
    @ApiModelProperty(value = "Template code.", required = true)
    private String templateCode;

    @ApiModelProperty(value = "Unique identification of goods to be uploaded (this parameter must be filled when uploading files) ")
    private String skuToken;

    @ApiModelProperty(value = ApiConstants.PRODUCT_CONDITION)
    private String productCondition;

    @Builder.Default
    @ApiModelProperty(value = ApiConstants.PRODUCT_SELECTION_TYPE)
    private String productSelectionType = ProductSelectionEnum.SELECT.code();

    public void setProductSelectionType(String productSelectionType) {
        this.productSelectionType = StringUtils.isBlank(productSelectionType) ? ProductSelectionEnum.SELECT.code() : productSelectionType;
    }

    @Builder.Default
    @ApiModelProperty(value = "Product item scope type: 1-All scope 2-By spu in scope")
    private String itemScopeType = ItemScopeTypeEnum.ALL_SCOPE.code();

    public void setItemScopeType(String itemScopeType) {
        this.itemScopeType = StringUtils.isBlank(itemScopeType) ? ItemScopeTypeEnum.ALL_SCOPE.code() : itemScopeType;
    }

    @ApiModelProperty(value = ApiConstants.PRODUCT_TYPE, required = true)
    private String conditionProductType;

    public void setConditionProductType(String conditionProductType) {
        this.conditionProductType = conditionProductType;
    }

    @ApiModelProperty(value = ApiConstants.PRODUCT_TYPE)
    private String incentiveProductType;

    @ApiModelProperty(value = "Product scope list. seqNum equals 1 means condition product. seqNum equals 2 means incentive product.")
    private List<ProductScope> products;

    @ApiModelProperty(value = "Product detail list. seqNum equals 1 means condition product. seqNum equals 2 means incentive product.")
    private List<ProductDetail> productDetails;

    @ApiModelProperty(value = "Product detail list. seqNum equals 1 means condition product. seqNum equals 2 means incentive product.")
    private List<ProductDetail> productDetailBlackList;

}