package com.gtech.promotion.vo.param.activity;

import com.gtech.commons.utils.CheckUtils;
import com.gtech.promotion.exception.ErrorCodes;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;

import java.io.Serializable;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("GetCouponReleaseRequest")
public class GetCouponReleaseParam implements Serializable {

    private static final long serialVersionUID = -2586696505749933650L;
    @ApiModelProperty(value = "Domain code",required = true)
    private String domainCode;

    @ApiModelProperty(value = "Tenant code",required = true)
    private String tenantCode;

    @ApiModelProperty(value = "Coupon code list.",required = true)
    private List<String> couponCodeList;

    public void validate() {

        CheckUtils.isNotBlank(this.domainCode, ErrorCodes.PARAM_EMPTY, "domainCode");
        CheckUtils.isNotBlank(this.tenantCode, ErrorCodes.PARAM_EMPTY, "tenantCode");
        CheckUtils.isTrue(CollectionUtils.isNotEmpty(this.couponCodeList), ErrorCodes.PARAM_EMPTY, "couponCodeList");

    }

}
