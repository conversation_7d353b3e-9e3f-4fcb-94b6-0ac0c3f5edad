package com.gtech.promotion.vo.param.point;

import com.gtech.commons.utils.CheckUtils;
import com.gtech.promotion.code.point.PointAccountTypeEnum;
import com.gtech.promotion.code.point.PointTransactionTypeEnum;
import com.gtech.promotion.exception.ErrorCodes;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;

@Getter
@Setter
@ToString
public class UpdatePointParam implements Serializable {

    private static final long serialVersionUID = -4604832588696584783L;

    @ApiModelProperty(value = "Domain code", required = true)
	private String domainCode;

	@ApiModelProperty(value = "Tenant code", required = true)
	private String tenantCode;

	@ApiModelProperty(value = "campaign code", required = true)
	private String campaignCode;

	@ApiModelProperty(value = "account code. (UserCode or OrgCode)", required = true)
	private String accountCode;

	@ApiModelProperty(value = "Point account type. (1-User 2-Organization)", required = true)
	private Integer accountType;

	@ApiModelProperty(value = "Point change remark.")
	private String transactionRemarks;

	@ApiModelProperty(value = "refer order number.")
	private String referOrderNumber;

	@ApiModelProperty(value = "Point change type. (1-Increase points 2-Deduct points)", required = true)
	private Integer transactionType;

	@ApiModelProperty(value = "transaction amount.", required = true)
	private Integer transactionAmount;

	@ApiModelProperty(value = "create user.")
	private String createUser;

	@ApiModelProperty(value = "expiration.")
	private String expiration;

	@ApiModelProperty(value = "operation(1. Earn 2. Expire 3. Redeem 4. Adjust).",required = true)
	private Integer operation;


    /**
     * Parameter validation.
     */
    public void validate() {

        CheckUtils.isNotBlank(this.domainCode, ErrorCodes.PARAM_EMPTY, "domainCode");
        CheckUtils.isNotBlank(this.tenantCode, ErrorCodes.PARAM_EMPTY, "tenantCode");
        CheckUtils.isNotBlank(this.accountCode, ErrorCodes.PARAM_EMPTY, "accountCode");
        CheckUtils.isNotNull(this.accountType, ErrorCodes.PARAM_EMPTY, "accountType");
        CheckUtils.isNotNull(this.transactionType, ErrorCodes.PARAM_EMPTY, "transactionType");
        CheckUtils.isNotNull(this.transactionAmount, ErrorCodes.PARAM_EMPTY, "transactionAmount");
        CheckUtils.isNotNull(this.operation, ErrorCodes.PARAM_EMPTY, "operation");

        CheckUtils.isTrue(PointAccountTypeEnum.exist(this.accountType), ErrorCodes.PARAM_ERROR, "accountType");
        CheckUtils.isTrue(PointTransactionTypeEnum.exist(this.transactionType), ErrorCodes.PARAM_ERROR, "transactionType");
        CheckUtils.isTrue(this.transactionAmount >= 0, ErrorCodes.PARAM_EMPTY, "transactionAmount");
    }

}