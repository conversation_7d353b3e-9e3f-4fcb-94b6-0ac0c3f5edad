package com.gtech.promotion.vo.bean.marketing.flashsale;

import com.gtech.commons.utils.CheckUtils;
import com.gtech.promotion.exception.ErrorCodes;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.util.CollectionUtils;

import java.io.Serializable;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("FlashSaleQualification")
public class FlashSaleQualification implements Serializable {

    private static final long serialVersionUID = 1376245758028629056L;
    @ApiModelProperty(value = "qualification code",required = true)
    private String qualificationCode;

    @ApiModelProperty(value = "qualification Value list",required = true)
    private List<String> qualificationValue;

    @ApiModelProperty(value = "qualification Value name list")
    private List<String> qualificationValueName;

    @ApiModelProperty(value = "Is exclude, 02 按会员标签排除")
    private String isExclude;

    public void validate() {
        CheckUtils.isNotBlank(this.qualificationCode, ErrorCodes.PARAM_EMPTY, "qualificationCode");
        CheckUtils.isTrue(!CollectionUtils.isEmpty(this.qualificationValue), ErrorCodes.PARAM_EMPTY, "qualificationValue");
    }
}
