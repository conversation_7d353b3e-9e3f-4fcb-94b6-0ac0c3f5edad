package com.gtech.promotion.vo.result.flashsale;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel("BoostSharingRewardsResponse")
public class BoostSharingRewardsResult {


    @ApiModelProperty(value = "rewardType")
    private String rewardType;
    @ApiModelProperty(value = "rewardName")
    private String rewardActivityName;
    @ApiModelProperty(value = "rewardCode")
    private String rewardActivityCode;






}
