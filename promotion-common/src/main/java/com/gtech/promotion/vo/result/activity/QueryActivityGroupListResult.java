/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.vo.result.activity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;

@Getter
@Setter
@ToString
@ApiModel("QueryActivityGroupListResponse")
public class QueryActivityGroupListResult implements Serializable {

    private static final long serialVersionUID = -6633936839520366221L;

    @ApiModelProperty(value = "Tenant code")
    private String tenantCode;

    @ApiModelProperty(value = "Activity code")
    private String activityCode;

    @ApiModelProperty(value = "Activity name")
    private String activityName;


    @ApiModelProperty(value = "Group name")
    private String groupName;


    @ApiModelProperty(value = "Group code")
    private String groupCode;


    @ApiModelProperty(value = "Activity org code")
    private String activityOrgCode;

    @ApiModelProperty(value = "Activity start time. (yyyyMMddHHmmss)")
    private String activityBegin;

    @ApiModelProperty(value = "Activity end time. (yyyyMMddHHmmss)")
    private String activityEnd;

    @ApiModelProperty(value = "Activity status")
    private String activityStatus;

    @ApiModelProperty(value = "Priority")
    private Integer priority;




}
