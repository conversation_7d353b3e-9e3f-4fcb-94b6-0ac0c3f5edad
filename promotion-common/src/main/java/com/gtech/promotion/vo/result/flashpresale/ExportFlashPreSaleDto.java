package com.gtech.promotion.vo.result.flashpresale;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @Description :
 * <AUTHOR> gaojie.li
 * @Date : 2023/12/26 14:03
 */
@Data
public class ExportFlashPreSaleDto implements Serializable {


    private static final long serialVersionUID = -4942439569309903668L;

    @ApiModelProperty(value = "Domain code.",example = "D00001",required = true)
    private String domainCode;

    @ApiModelProperty(value = "Tenant code",required = true)
    private String tenantCode;

    @ApiModelProperty(value = "Activity code.",required = true)
    private String activityCode;

    @ApiModelProperty(value = "query from max id.")
    private String maxOrderCode;

    @ApiModelProperty(value = "order status.")
    private String orderStatus;


}
