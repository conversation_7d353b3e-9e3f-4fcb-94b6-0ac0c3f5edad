package com.gtech.promotion.vo.result.marketing;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class TicketReleaseQueryResult implements Serializable {

    private static final long serialVersionUID = -2604809697972922522L;
    @ApiModelProperty(value = "Ticket release code.",example = "32300001",required = true)
    private String releaseCode;

    @ApiModelProperty(value="Activity code",required=true)
    private String activityCode;

    @ApiModelProperty(value="Quality",required=true)
    private Long quality;

    @ApiModelProperty(value="Inventory",required=true)
    private Long inventory;

    @ApiModelProperty(value="Used",required=true)
    private Long used;

    @ApiModelProperty(value="Create Time",required=true)
    private String createTime;

    @ApiModelProperty(value="Create user")
    private String createUser;
}
