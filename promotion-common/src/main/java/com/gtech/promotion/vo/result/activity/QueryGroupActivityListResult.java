package com.gtech.promotion.vo.result.activity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.io.Serializable;

/**
 * @Description :
 * <AUTHOR> gaojie.li
 * @Date : 2023/7/16 10:14
 */
@Getter
@Setter
@ToString
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("QueryGroupActivityListResponse")
public class QueryGroupActivityListResult implements Serializable {

    @ApiModelProperty(value = "Tenant code")
    private String tenantCode;

    @ApiModelProperty(value = "Domain code")
    private String domainCode;

    @ApiModelProperty(value = "Group code")
    private String groupCode;

    @ApiModelProperty(value = "Activity code")
    private String activityCode;

    @ApiModelProperty(value = "Activity name")
    private String activityName;

    @ApiModelProperty(value = "Group name")
    private String groupName;

    @ApiModelProperty(value = "Activity begin")
    private String activityBegin;

    /**
     * Activity end time
     * yyyyMMddHHmmss
     */
    @ApiModelProperty(value = "Activity end")
    private String activityEnd;

    @ApiModelProperty(value = "Activity status")
    private String activityStatus;
}
