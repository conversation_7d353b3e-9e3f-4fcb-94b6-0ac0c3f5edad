package com.gtech.promotion.vo.bean;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.gtech.commons.utils.CheckUtils;
import com.gtech.commons.utils.StringUtil;
import com.gtech.promotion.checker.purchaseconstraint.PurchaseConstraintChecker;
import com.gtech.promotion.code.purchaseconstraint.PurchaseConstraintRuleTimeTypeEnum;
import com.gtech.promotion.exception.Check;
import com.gtech.promotion.exception.ErrorCodes;
import com.gtech.promotion.utils.DateUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.io.Serializable;
import java.util.Arrays;
import java.util.List;

/**
 * 限购规则
 */
@Data
@ToString
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("PurchaseConstraintRule")
public class PurchaseConstraintRule implements Serializable {
    /**
     * 限购条件类型
     * 0:CUSTOMER_MAX_QTY_PER_PRODUCT,1:CUSTOMER_MAX_QTY_ALL_PRODUCTS,2:CUSTOMER_MAX_AMOUNT,
     * 3:ORDER_MAX_QTY_PER_SKU,4:ORDER_MAX_QUANTITY,5:ORDER_MAX_AMOUNT_PER_SKU,6:ORDER_MAX_AMOUNT
     * @see com.gtech.promotion.code.purchaseconstraint.PurchaseConstraintRuleTypeEnum
     */
    @ApiModelProperty(value = "Purchase constraint rule type.",required = true)
    private Integer purchaseConstraintRuleType;

    /**
     * 限购条件值 可能是金额也可能是数量
     */
    @ApiModelProperty(value = "Purchase constraint rule value.",required = true)
    private String purchaseConstraintValue;

    /**
     * 限购条件统计时间类型  1: YEARLY, 2:MONTHLY, 3:WEEKLY
     * @see PurchaseConstraintRuleTimeTypeEnum
     */
    @ApiModelProperty(value = "Purchase constraint rule time type.")
    private Integer purchaseConstraintRuleTimeType;

    /**
     * 限购条件统计时间值 YEARLY: MMdd, MONTHLY: dd, WEEKLY: 0-6(0代表周日)
     */
    @ApiModelProperty(value = "Purchase constraint rule time value.")
    private String purchaseConstraintRuleTimeValue;

    public void validate(){
        CheckUtils.isNotNull(this.purchaseConstraintRuleType, ErrorCodes.PARAM_EMPTY, "purchaseConstraintRuleType");
        CheckUtils.isNotBlank(this.purchaseConstraintValue, ErrorCodes.PARAM_EMPTY, "purchaseConstraintValue");
        Check.check(checkRuleTime(),PurchaseConstraintChecker.PURCHASE_CONSTRAINT_RULE_TIME_INVALID);
    }

    private boolean checkRuleTime() {
        if((null != purchaseConstraintRuleTimeType
                && StringUtil.isBlank(purchaseConstraintRuleTimeValue))
                            || (null == purchaseConstraintRuleTimeType
                && StringUtil.isNotBlank(purchaseConstraintRuleTimeValue))){
            return true;
        }

        if(null != purchaseConstraintRuleTimeType){
            if(!PurchaseConstraintRuleTimeTypeEnum.exist(purchaseConstraintRuleTimeType)){
                return true;
            }
            if(PurchaseConstraintRuleTimeTypeEnum.YEARLY.getCode().equals(purchaseConstraintRuleTimeType)){
                return null == DateUtil.isDate(purchaseConstraintRuleTimeValue, DateUtil.DATEMDSTOREFORMAT);
            }else if(PurchaseConstraintRuleTimeTypeEnum.MONTHLY.getCode().equals(purchaseConstraintRuleTimeType)){
                return null == DateUtil.isDate(purchaseConstraintRuleTimeValue, DateUtil.DD);
            }else if(PurchaseConstraintRuleTimeTypeEnum.WEEKLY.getCode().equals(purchaseConstraintRuleTimeType)){
                List<String> week = Arrays.asList("0", "1", "2", "3", "4", "5", "6");
                return !week.contains(purchaseConstraintRuleTimeValue);
            }else {
                return true;
            }
        }

        return false;
    }
}
