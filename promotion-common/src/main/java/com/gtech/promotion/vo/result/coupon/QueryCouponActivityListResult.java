/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.vo.result.coupon;

import com.gtech.promotion.vo.result.activity.QueryActivityListResult;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;


/**
 * QueryCouponActivityListResult
 *
 * <AUTHOR>
 * @Date 2020-02-19
 */
@Getter
@Setter
@ToString
@ApiModel("QueryCouponActivityListResponse")
public class QueryCouponActivityListResult extends QueryActivityListResult {

    private static final long serialVersionUID = 6399215685299513238L;

    @ApiModelProperty(value = "Coupon type: 01-General coupon 02-Anonymous coupon 03-Single fixed promotion code.")
    private String couponType;

    @ApiModelProperty(value = "Activity total quantity: 0 means unlimited, fixed 1 while couponType='03'")
    private String totalQuantity;

    @ApiModelProperty(value = "Already released quantity.")
    private String releaseQuantity;

    @ApiModelProperty(value = "Single user limited max. 0 means unlimited")
    private String userLimitMax;

    @ApiModelProperty(value = "Single user limited max. 0 means unlimited")
    private String userLimitMaxDay;
}
