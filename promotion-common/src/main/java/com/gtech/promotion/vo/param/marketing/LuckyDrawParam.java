/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * <PERSON>ECH MAKES NO REPRESENTATIONS OR <PERSON><PERSON><PERSON>NTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.vo.param.marketing;

import com.gtech.commons.utils.CheckUtils;
import com.gtech.promotion.exception.ErrorCodes;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("LuckyDrawRequest")
public class LuckyDrawParam implements Serializable {

    private static final long serialVersionUID = -4181132442175225725L;
    @ApiModelProperty(value = "Domain code.",example = "D00001",required = true)
    private String domainCode;

    @ApiModelProperty(value = "Tenant code.",example = "T00001",required = true)
    private String tenantCode;

    @ApiModelProperty(value = "Store organization code. (Blank value means unlimited.)",required = true)
    private String orgCode;

    @ApiModelProperty(value = "Activity code",required = true)
    private String activityCode;

    @ApiModelProperty(value = "Member code", required = true)
    private String memberCode;

    @ApiModelProperty(value = "Draw quality", required = true)
    private Integer quality;

    @ApiModelProperty(value = "Language id. (en-US/id-ID/zh-CN/...)", required = true)
    private String language;

    /**
     * Parameter validation.
     */
    public void validate() {

        CheckUtils.isNotBlank(this.domainCode, ErrorCodes.PARAM_EMPTY, "domainCode");
        CheckUtils.isNotBlank(this.tenantCode, ErrorCodes.PARAM_EMPTY, "tenantCode");
        CheckUtils.isNotBlank(this.orgCode, ErrorCodes.PARAM_EMPTY, "orgCode");
        CheckUtils.isNotBlank(this.activityCode, ErrorCodes.PARAM_EMPTY, "activityCode");
        CheckUtils.isNotBlank(this.memberCode, ErrorCodes.PARAM_EMPTY, "memberCode");
        CheckUtils.isNotBlank(this.language, ErrorCodes.PARAM_EMPTY, "language");
        CheckUtils.isTrue(null != quality, ErrorCodes.PARAM_EMPTY, "quality");
        CheckUtils.isTrue(0 < quality, ErrorCodes.PARAM_ERROR, "quality");
    }
}
