package com.gtech.promotion.vo.param.activity;

import com.gtech.commons.utils.CheckUtils;
import com.gtech.commons.utils.StringUtil;
import com.gtech.promotion.code.marketing.DeleteEnum;
import com.gtech.promotion.exception.ErrorCodes;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @Description :
 * <AUTHOR> gaojie.li
 * @Date : 2023/6/14 13:47
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("GroupQueryRequest")
public class GroupQueryParam  implements Serializable {

    private static final long serialVersionUID = -9074510168209768378L;

    @ApiModelProperty(value = "租户编码", example = "880001", required = true)
    private String tenantCode;

    @ApiModelProperty(value = "Domain code",required = true)
    private String domainCode;

    @ApiModelProperty(value = "Group name", example = "自定义满减分组")
    private String groupName;

    @ApiModelProperty(value = "Group code", example = "123")
    private String groupCode;

    @ApiModelProperty(value = "Logic delete. 0:no delete ,1:yes", example = "0")
    private String logicDelete;

    public void checkParam(){

        CheckUtils.isNotBlank(this.tenantCode, ErrorCodes.PARAM_EMPTY, "tenantCode");
        CheckUtils.isNotBlank(this.domainCode, ErrorCodes.PARAM_EMPTY, "domainCode");

        if (StringUtil.isNotEmpty(logicDelete)){
            CheckUtils.isTrue(DeleteEnum.exist(logicDelete), ErrorCodes.SWITCH, "logicDelete");

        }
    }

}
