package com.gtech.promotion.vo.param.marketing;

import java.io.Serializable;
import java.util.List;

import org.apache.commons.collections4.CollectionUtils;

import com.gtech.commons.utils.CheckUtils;
import com.gtech.promotion.code.marketing.UserGroupStatusEnum;
import com.gtech.promotion.exception.ErrorCodes;
import com.gtech.promotion.page.RequestPage;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Description :
 * <AUTHOR> gaojie.li
 * @Date : 2023/9/15 13:56
 */
@Data
@ApiModel("MarketingGroupUserListRequest")
public class MarketingGroupUserListParam extends RequestPage implements Serializable {

    @ApiModelProperty(value = "Domain code.",example = "D00001",required = true)
    private String domainCode;

    @ApiModelProperty(value = "Tenant code.",required = true)
    private String tenantCode;

    @ApiModelProperty(value = "activity code.",example = "12556568987")
    private String activityCode;

    @ApiModelProperty(value = "Marketing group code.",example = "12556568987")
    private String marketingGroupCode;

    @ApiModelProperty(value = "Group status list. 01 进行中 02 拼团成功 03 取消，04已支付 ；空查询所有状态",example = "12556568987")
    private List<String> groupStatusList;

    @ApiModelProperty(value = "user code.",example = "12556568987")
    private String userCode;

    @ApiModelProperty(value = "product code.",example = "1")
    private String productCode;

    @ApiModelProperty(value = "sku code.",example = "1")
    private String skuCode;


    public void validate() {

        CheckUtils.isNotBlank(this.domainCode, ErrorCodes.PARAM_EMPTY, "domainCode");
        CheckUtils.isNotBlank(this.tenantCode, ErrorCodes.PARAM_EMPTY, "tenantCode");

        if (CollectionUtils.isNotEmpty(groupStatusList)) {
			for (String status : groupStatusList) {
				CheckUtils.isTrue(UserGroupStatusEnum.exist(status), ErrorCodes.GROUP_STATUS, "status");
            }
        }
    }
}
