package com.gtech.promotion.vo.result.point;import com.alibaba.fastjson.JSON;
import com.gtech.commons.utils.BeanCopyUtils;
import com.gtech.commons.utils.StringUtil;
import com.gtech.promotion.vo.bean.CampaignTitleLanguage;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.util.Collections;
import java.util.Date;
import java.util.List;

@Getter
@Setter
@ToString
public class PointCampaignResult implements Serializable {

    private static final long serialVersionUID = -3718377891361157728L;

    /**
     * Tenant code.
     */
    @ApiModelProperty(value = "Tenant code")
    private String tenantCode;

    /**
     * campaign code
     */
	@ApiModelProperty(value = "campaign code")
    private String campaignCode;


    /**
     * program name
     */
    @ApiModelProperty(value = "program name")
    private String programName;

    /**
     * campaign title
     */
	@ApiModelProperty(value = "campaign title")
    private String campaignTitle;

    /**
     * campaign desc
     */
	@ApiModelProperty(value = "campaign desc")
    private String campaignDesc;

    /**
     * Campaingn sponsor information.
     */
	@ApiModelProperty(value = "Campaingn sponsor information.")
    private String sponsor;

    /**
     * Point campaign begin time.
     */
	@ApiModelProperty(value = "Point campaign begin time.")
    private String beginTime;

    /**
     * Point campaign end time.
     */
	@ApiModelProperty(value = "Point campaign end time.")
    private String endTime;

    /**
     * Activity total points.
     */
	@ApiModelProperty(value = "Activity total points.")
    private Integer totalPoints;

    /**
     * Activity remaining points.
     */
	@ApiModelProperty(value = "Activity remaining points.")
    private Integer remainingPoints;

    /**
     * campaignstatus.( 0-inactive 1-active)
     */
	@ApiModelProperty(value = "campaignstatus.( 0-inactive 1-active)")
	private Integer status;


    /**
     * create user.
     */
    @ApiModelProperty(value = "create user")
    private String createUser;

    /**
     * create time
     */
    @ApiModelProperty(value = "create date. (yyyyMMddHHmmss)")
    private Date createTime;

    /**
     * campaignTitleLanguage
     */
    @ApiModelProperty(value = "campaignTitleLanguage")
    private String campaignTitleLanguage;

    @ApiModelProperty(value = "RankName by language")
    private List<CampaignTitleLanguage> campaignTitleLanguages;




    public List<CampaignTitleLanguage> getCampaignTitleLanguages(){

        if (StringUtil.isNotBlank(campaignTitleLanguage)){
            campaignTitleLanguages = BeanCopyUtils.jsonCopyList(JSON.parseArray(campaignTitleLanguage), CampaignTitleLanguage.class);
        }else{
            campaignTitleLanguages = Collections.emptyList();
        }
        return campaignTitleLanguages;
    }





}