/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.vo.result.activity;

import com.alibaba.fastjson.JSONObject;
import com.gtech.commons.utils.BeanCopyUtils;
import com.gtech.commons.utils.StringUtil;
import com.gtech.promotion.api.ApiConstants;
import com.gtech.promotion.code.PromotionConstants;
import com.gtech.promotion.code.activity.ProductTypeEnum;
import com.gtech.promotion.vo.bean.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.apache.commons.collections4.CollectionUtils;

import java.io.Serializable;
import java.util.List;

/**
 * FindActivityResult
 *
 * <AUTHOR>
 * @Date 2020-05-20
 */
@Getter
@Setter
@ToString
@ApiModel("FindActivityResponse")
public class FindActivityResult implements Serializable {

    private static final long serialVersionUID = -1777664971818491480L;
    private String orgCode;

    @ApiModelProperty(value = "Activity code.",example = "23")
    private String activityCode;

    @ApiModelProperty(value = "Promotion category")
    private String promotionCategory;

    @ApiModelProperty(value = "External activity id")
    private String externalActivityId;


    @ApiModelProperty(value = "Promotion category name")
    private String promotionCategoryName;

    @ApiModelProperty(value = "Activity Name")
    private String activityName;
    
    @ApiModelProperty(value = "Activity Type")
    private String activityType;

    @ApiModelProperty("Activity sponsors")
    private String sponsors;

    @ApiModelProperty(ApiConstants.ACTIVITY_TAGCODE)
    private String tagCode;

    @ApiModelProperty(value = "Activity label.")
    private String activityLabel;

    @ApiModelProperty(value = "Activity description.")
    private String activityDesc;

    @ApiModelProperty(value = "Custom conditions.")
    private String customCondition;
    @ApiModelProperty(value = "Price conditions.")
    private String priceCondition;
    @ApiModelProperty(value = "Activity remark.")
    private String activityRemark;

    @ApiModelProperty(value = "Multilingual attributes.")
    private List<ActivityLanguage> activityLanguages;

    @ApiModelProperty(value = "Activity begin time.")
    private String activityBegin;

    @ApiModelProperty(value = "Activity end time.")
    private String activityEnd;

    @ApiModelProperty(value = "Activity warm-up begin time.")
    private String warmBegin;

    @ApiModelProperty(value = "Activity warm-up end time.")
    private String warmEnd;

    @ApiModelProperty(value = "Activity period type: 00-Full time 01-Custmize")
    private String periodType;

    @ApiModelProperty(value = "Activity status.")
    private String activityStatus;

    @ApiModelProperty(value = "Activity url.")
    private String activityUrl;

    @ApiModelProperty(value = "Incentive limited mark: 00-unlimited 01-limited")
    private String incentiveLimitedFlag;

    @ApiModelProperty(value = "Incentive limited list. It can't be empty while incentiveLimitedFlag is '01'.")
    private List<IncentiveLimited> incentiveLimiteds;

    @ApiModelProperty(value = "Product item scope type: 1-All scope 2-By spu in scope")
    private String itemScopeType;

    @ApiModelProperty(value = "Store limited type: 00-Unlimited, 01-Specified stores.")
    private String storeType;

    @ApiModelProperty(value = "Activity priority.(1-100, default is 999)")
    private Integer priority;
    
    @ApiModelProperty(value = "Show flag: 1-show, 2-hidden")
    private Integer showFlag;
    
    @ApiModelProperty(value = "Specified store list while storeType equals 01.")
    private List<ActivityStore> channelStores;

    @ApiModelProperty(value = "Giving-gift list of the activity.")
    private List<Giveaway> giveaways;

    @ApiModelProperty(value = "Product scope list. seqNum equals 1 means condition product. seqNum equals 2 means incentive product.")
    private List<ProductScope> products;

    @ApiModelProperty(value = "Product detail list. seqNum equals 1 means condition product. seqNum equals 2 means incentive product.")
    private List<ProductDetail> productDetils;

    //商品黑名单列表
    @ApiModelProperty("Product blacklist")
    private List<ProductDetail> productDetilBlackList;

    @ApiModelProperty(value = "Qualification list. Empty means not limited.")
    private List<Qualification> qualifications;

    @ApiModelProperty(value = "ops create Activity type")
    private String opsType;

    @ApiModelProperty(value = "Activity cool down time.")
    private String coolDown;
    /**
     * @deprecated
     */
    @ApiModelProperty(value = "Deprecated. Member level list. Empty means not limited.")
    @Deprecated
    private List<MemberLevel> memberLevels; //NOSONAR

    /**
     * @deprecated
     */
    @ApiModelProperty(value = "Deprecated. Member label list. Empty means not limited.")
    @Deprecated
    private List<MemberLabel> memberLabels; //NOSONAR

    @ApiModelProperty(value = "Template code.")
    private String templateCode;

    @ApiModelProperty(value = "promotion code.")
    private String promotionCode;

    @ApiModelProperty(value = "promo password.")
    private String promoPassword;

    @ApiModelProperty("Coupon type: 01-General coupon 02-Anonymous coupon 03-Single fixed promotion code.")
    private String couponType;

    @ApiModelProperty(value = "Ribbon image.")
    private String ribbonImage;

    @ApiModelProperty(value = "Position of Ribbon image .")
    private String ribbonPosition;

    @ApiModelProperty(value = "Ribbon text.")
    private String ribbonText;

    @ApiModelProperty(value = "Activity period.")
    private ActivityPeriod activityPeriod;

    @ApiModelProperty(value = "Ext images")
    private List<ExtImage> extImages;

    @ApiModelProperty(ApiConstants.PRODUCT_TYPE)
    public String getConditionProductType() {

        if (CollectionUtils.isNotEmpty(ProductDetail.getProductScopeBySeq(BeanCopyUtils.jsonCopyList(productDetils, ProductDetail.class), 1))) {
            return ProductTypeEnum.CUSTOM_PRODUCT.code();
        } else {
            List<ProductScope> productScopeBySeq = ProductScope.getProductScopeBySeq(products, 1);
            if (CollectionUtils.isNotEmpty(productScopeBySeq)) {
                if (productScopeBySeq.size() == 1 && CollectionUtils.isEmpty(productScopeBySeq.get(0).getAttributes()) && CollectionUtils.isEmpty(productScopeBySeq.get(0).getSpuAttributes())
                    && PromotionConstants.UNLIMITED.equals(productScopeBySeq.get(0).getBrandCode())
                    && PromotionConstants.UNLIMITED.equals(productScopeBySeq.get(0).getCategoryCode())
                        && StringUtil.isEmpty(productScopeBySeq.get(0).getProductTag())){
                    return ProductTypeEnum.ALL.code();
                }
                return ProductTypeEnum.CUSTOM_RANGE.code();
            } else {
                return ProductTypeEnum.ALL.code();
            }
        }
    }

    @ApiModelProperty(ApiConstants.PRODUCT_TYPE)
    public String getIncentiveProductType() {

        if (CollectionUtils.isNotEmpty(ProductScope.getProductScopeBySeq(products, 2))) {
            return ProductTypeEnum.CUSTOM_RANGE.code();
        } else if (CollectionUtils.isNotEmpty(ProductDetail.getProductScopeBySeq(BeanCopyUtils.jsonCopyList(productDetils, ProductDetail.class), 2))) {
            return ProductTypeEnum.CUSTOM_PRODUCT.code();
        } else {
            return ProductTypeEnum.ALL.code();
        }
    }

    @ApiModelProperty(ApiConstants.PRODUCT_SELECTION_TYPE)
    private String productSelectionType;

    @ApiModelProperty(value = "Parameters list of the activity template function.")
    private List<FunctionParam> funcParams;

    @ApiModelProperty("Activity create time. (yyyyMMddHHmmss)")
    private String createTime;

    @ApiModelProperty(value = "Activity background image.")
    private String backgroundImage;

    @ApiModelProperty(value = ApiConstants.PRODUCT_CONDITION)
    private String productCondition;

    @ApiModelProperty(value = "Need Audit. 0:no 1:yes")
    private String needAudit;

    @ApiModelProperty(value = "Need Different Operator.  0:no 1:yes")
    private String needDifferentOperator;

    @ApiModelProperty(value = "audit user")
    private String auditUser;

    @ApiModelProperty(value = "commit user")
    private String commitUser;

    @ApiModelProperty(value = "coupon activity user limit max ")
    private  Integer userLimitMax;

    @ApiModelProperty(value = "coupon activity user limit max  day")
    private  Integer userLimitMaxDay;

    @ApiModelProperty(value = "{\n" +
            "  \"giveawayPoolEnable\": \"{{giveawayPoolEnable}}\", // 是否启用赠品池 1是 2否\n" +
            "  \"giveawayRules\": [\n" +
            "    {\n" +
            "      \"giveawayMethod\": \"{{giveawayMethod}}\", // 赠送方式 1全部 2 部分\n" +
            "      \"giveawayChooseQty\": \"{{giveawayChooseQty}}\", // 可选数量\n" +
            "      \"giveawaySortRule\": \"{{giveawaySortRule}}\", // 赠品排序规则 1有序 2随机\n" +
            "      \"rankParam\": 1 // 层级\n" +
            "    }\n" +
            "  ]\n" +
            "}")
    private JSONObject extendParams;
}
