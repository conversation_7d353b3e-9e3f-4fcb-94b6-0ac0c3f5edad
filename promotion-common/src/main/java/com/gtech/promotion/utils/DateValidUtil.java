/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.utils;

import com.gtech.promotion.checker.SystemChecker;
import com.gtech.promotion.exception.PromotionException;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Date;

public class DateValidUtil{
    
    private DateValidUtil(){
        throw new PromotionException(SystemChecker.CAN_NOT_INSTANCE_ERROR);
    }

    public static final String YYYYMMDDHHMMSS = "yyyyMMddHHmmss";

    /**
     * 获取当前日期的默认格式的字符串
     * @return 当前日期字符串
     */
    public static String getNowFormatDate(){
        return getNowFormatDate(YYYYMMDDHHMMSS);
    }
    
    /**
     * 获取当前日期的指定格式的字符串
     * @param pattern 指定格式
     * @return 当前日期字符串
     */
    public static String getNowFormatDate(String pattern){
        DateFormat df = new SimpleDateFormat(pattern);
        return df.format(new Date());
    }

    public static boolean endIsGreateBegin(String beginTime,String endTime){
        boolean flag = false;
        Long beginTimeL = Long.valueOf(beginTime);
        Long endTimeL = Long.valueOf(endTime);
        if (endTimeL > beginTimeL){
            flag = true;
        }
        return flag;
    }

    /**
     * 校验时间格式
     *
     * @param str
     *            时间
     * @return 正确true 错误false
     * @see
     * @since
     */
    public static boolean isValidDate(String str){
        SimpleDateFormat sdf = new SimpleDateFormat(YYYYMMDDHHMMSS);
        try{
            Date date = sdf.parse(str);
            return str.equals(sdf.format(date));
        }catch (Exception e){
            return false;
        }
    }

}
