/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * <PERSON>ECH MAKES NO REPRESENTATIONS OR WA<PERSON><PERSON>NTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.utils;

import com.gtech.promotion.checker.SystemChecker;
import com.gtech.promotion.exception.PromotionException;

/**
 * 全局常量定义
 */
public abstract class MarketingConstants {

    private MarketingConstants() {
        throw new PromotionException(SystemChecker.CAN_NOT_INSTANCE_ERROR);
    }
    public static final String APP_KEY = "MKT";

    public static final String MKT_CODE_TEMPLATE_EXP = "30[D:yyMMdd][SA:%08d]";

    public static final String PRIZE_CODE_DEFAULT = "default";
    public static final String PRIZE_NAME_DEFAULT = "Undefined";

    public static final String MARKETING_TICKET_CODE_LOCK_KEY = "MARKETING_TICKET_CODE_LOCK_KEY";

    public static final String MARKETING_ACTIVITY_CACHE = "MARKETING_ACTIVITY_CACHE";
    public static final String MARKETING_ACTIVITY_CACHE_LEVEL_TWO = "MARKETING_ACTIVITY_CACHE_LEVEL_TWO";

    public static final String MARKETING_GROUP_USER_CACHE = "MARKETING_GROUP_USER_CACHE";

}
