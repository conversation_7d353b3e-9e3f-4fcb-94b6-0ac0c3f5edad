/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.utils;

import java.lang.reflect.Constructor;
import java.util.Objects;

import org.springframework.beans.BeanUtils;

import lombok.extern.slf4j.Slf4j;

/**
 * 类型转换器
 */
@Slf4j
public class Convertor<S, T> {


    private Class<T> targetClass;

    public Convertor(Class<S> sourceClass, Class<T> targetClass){//NOSONAR
        this.targetClass = targetClass;
    }

    /**
     * 类型转换, 目标对象有属性需要保留
     *
     * @param s 源对象
     * @param t 目标对象
     * @param skipProperties 忽略的属性
     * @return 目标对象
     */
    public T convert(S s,T t,String...skipProperties){
        if (Objects.isNull(s)){
            return null;
        }
        try{
            Constructor<T> constructor = targetClass.getConstructor();
            if (!constructor.isAccessible()){
                constructor.setAccessible(true);
            }
            BeanUtils.copyProperties(s, t, skipProperties);
        }catch (NoSuchMethodException e){
            log.error("类型转换出错：", e);
        }
        return t;
    }

}
