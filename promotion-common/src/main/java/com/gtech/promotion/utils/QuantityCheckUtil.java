/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.utils;

import com.gtech.promotion.checker.SystemChecker;
import com.gtech.promotion.checker.coupon.CouponActivityChecker;
import com.gtech.promotion.code.coupon.CouponTypeEnum;
import com.gtech.promotion.exception.Check;
import com.gtech.promotion.exception.PromotionException;

/**
 * 校验工具类
 * 
 */
public class QuantityCheckUtil{

    private QuantityCheckUtil(){
        throw new PromotionException(SystemChecker.CAN_NOT_INSTANCE_ERROR);
    }

    /**
     * 校验参数是否合法
     * 
     * @param totalQuantity 数量
     * @param couponType 券类型
     */
    public static void checkParam(String totalQuantity,String couponType){
        if (!"0".equals(totalQuantity)){
            long parseInt = 0;
            try {
                parseInt = Long.parseLong(totalQuantity);
            }catch (NumberFormatException e){
                Check.check(true, CouponActivityChecker.QUANTITY);
            }
            if (CouponTypeEnum.PROMOTION_CODE.equalsCode(couponType)){
                Check.check(parseInt < 1, CouponActivityChecker.RELEASE_QUANTITY_USED_LIMIT);
            }else{
                Check.check(parseInt < 1, CouponActivityChecker.RELEASE_QUANTITY);
            }
        }
    }
    
    public static void checkParamInDTO(String totalQuantity, Integer userLimitMax){
        if (null != userLimitMax){
            Check.check(userLimitMax < 0 , CouponActivityChecker.NOT_LIMIT_USERLIMITMAX);
            if (!"0".equals(totalQuantity)){
                Check.check(userLimitMax > Long.parseLong(totalQuantity), CouponActivityChecker.NOT_LIMIT_USERLIMITMAX);
            }
        }
    }
}
