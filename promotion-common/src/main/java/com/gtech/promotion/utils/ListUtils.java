package com.gtech.promotion.utils;

import com.gtech.promotion.checker.SystemChecker;
import com.gtech.promotion.exception.PromotionException;

import java.util.ArrayList;
import java.util.List;

public class ListUtils {

    private ListUtils() {
        throw new PromotionException(SystemChecker.CAN_NOT_INSTANCE_ERROR);
    }
    /**
     * 获取所有组合情况
     * @param list
     * @return
     */
    public static List<String> getAllSubList(List<String> list) {
        List<String> allList = new ArrayList<>();
        int size = 1 << list.size();// 2^n次方
        for (int mark = 0; mark < size; mark++) {
            StringBuilder stringBuilder = new StringBuilder();
            for (int i = 0; i < list.size(); i++) {
                /* 重要：这句是最重要的代码:是 0-7 分别与运算 1，2，4
                 * 000 001 010 011 100 101 110 111
                 *
                 */
                if ((mark & (1 << i)) != 0) {
                    stringBuilder.append(list.get(i)).append(",");
                }
            }
            if (stringBuilder.length() > 0) {
                allList.add(stringBuilder.deleteCharAt(stringBuilder.length() - 1).toString());
            }
        }
        return allList;
    }
}
