/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.utils;

import com.gtech.promotion.checker.SystemChecker;
import com.gtech.promotion.exception.PromotionException;

/**   
 * 字符串截取
 */
public class TemplateCodeSubstringUtil{

    private TemplateCodeSubstringUtil(){
        throw new PromotionException(SystemChecker.CAN_NOT_INSTANCE_ERROR);
    }

    /**   
     * 截取模板编码  条件参数编码
     * @param templateCode
     * @return   返回截取后的字符串
     */
    public static String subStringTemplateCodeBegin8End12(String templateCode){
        return templateCode.substring(8, 12);
    }
    
    /**   
     * 截取模板编码  条件参数编码
     * @param templateCode
     * @return   返回截取后的字符串
     */
    public static String subStringTemplateCodeBegin12End16(String templateCode){
        return templateCode.substring(12, 16);
    }
    /**   
     * 截取模板编码  条件参数编码 
     * @param templateCode
     * @return   返回截取后的字符串
     */
    public static String subStringTemplateCodeBegin4End8(String templateCode){
        return templateCode.substring(4, 8);
    }
   
    /**   
     *截取模板编码  条件参数编码  
     * @param templateCode
     * @return  
     */
    public static String subStringTemplateCodeBegin2End4(String templateCode){
        return templateCode.substring(2, 4);
    }
    /**   
     * 截取模板编码 获取奖励值类型编码
     * @param templateCode
     * @return  奖励值类型编码
     */
    public static String getRewardType(String templateCode){
        return templateCode.substring(templateCode.length() - 2);
    }
}
