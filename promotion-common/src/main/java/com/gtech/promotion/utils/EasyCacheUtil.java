package com.gtech.promotion.utils;

import lombok.extern.slf4j.Slf4j;
import net.jodah.expiringmap.ExpiringMap;

import java.util.concurrent.TimeUnit;

@Slf4j
public class EasyCacheUtil {
	
	private EasyCacheUtil() {
		
	}
	
	private static ExpiringMap<String, Object> map = ExpiringMap.builder().variableExpiration().build();

	/**
	 * 不过期
	 * 
	 * @param key
	 * @param value
	 */
	public static void set(String key, Object value) {
		map.put(key, value);
		log.debug("expiringmap size :{}", map.keySet().size());
	}

	/**
	 * 设置过期时间(秒)
	 * 
	 * @param key
	 * @param value
	 * @param duration
	 */
	public static void set(String key, Object value, long duration) {
		try {
			set(key, value, duration, TimeUnit.SECONDS);
		} catch (Exception e) {
			log.error(e.getMessage(), e);
		}
	}

	/**
	 * 设置过期时间
	 * 
	 * @param key
	 * @param value
	 * @param duration
	 * @param timeUnit
	 */
	public static void set(String key, Object value, long duration, TimeUnit timeUnit) {
		map.setExpiration(duration, timeUnit);
		set(key, value);
	}

	public static Object get(String key) {
		return map.get(key);
	}

	public static Object remove(String key) {
		return map.remove(key);
	}
}
