package com.gtech.promotion.utils;

import com.cronutils.model.Cron;
import com.cronutils.model.definition.CronConstraintsFactory;
import com.cronutils.model.definition.CronDefinition;
import com.cronutils.model.definition.CronDefinitionBuilder;
import com.cronutils.model.time.ExecutionTime;
import com.cronutils.parser.CronParser;
import com.gtech.commons.utils.DateUtil;
import com.gtech.promotion.checker.SystemChecker;
import com.gtech.promotion.exception.PromotionException;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Optional;

public class CronUtil {

    private CronUtil() {
        throw new PromotionException(SystemChecker.CAN_NOT_INSTANCE_ERROR);
    }

    private static final CronDefinition cronDefinition = CronDefinitionBuilder.defineCron()
            .withSeconds().withValidRange(0, 59).and()
            .withMinutes().withValidRange(0, 59).and()
            .withHours().withValidRange(0, 23).and()
            .withDayOfMonth().withValidRange(1, 31).supportsL().supportsW().supportsLW().supportsQuestionMark().and()
            .withMonth().withValidRange(1, 12).and()
            .withDayOfWeek().withValidRange(1, 7).withMondayDoWValue(2).supportsHash().supportsL().supportsQuestionMark().withStrictRange().and()
            .withYear().withValidRange(1970, 2099).withStrictRange().optional().and()
            .withCronValidation(CronConstraintsFactory.ensureEitherDayOfWeekOrDayOfMonth())
            .instance();
    private static final CronParser parser = new CronParser(cronDefinition);

    public static boolean validate(String expression){
        try {
            Cron parse = parser.parse(expression);
            parse.validate();
        } catch (IllegalArgumentException e) {
            return false;
        }
        return true;
    }

    private static boolean checkDateByIntervalWeek(ZonedDateTime now, ExecutionTime executionTime, ExecutionTime executionTime2, Integer intervalWeek, String activityBegin, String activityEnd){
        ZonedDateTime min = ZonedDateTime.of(LocalDateTime.MIN, ZoneId.systemDefault());
        ZonedDateTime start = ZonedDateTime.of(LocalDateTime.parse(activityBegin, DateTimeFormatter.ofPattern(DateUtil.FORMAT_YYYYMMDDHHMISS_14)), ZoneId.systemDefault());
        ZonedDateTime end = ZonedDateTime.of(LocalDateTime.parse(activityEnd, DateTimeFormatter.ofPattern(DateUtil.FORMAT_YYYYMMDDHHMISS_14)), ZoneId.systemDefault());
        ZonedDateTime f = executionTime.nextExecution(start).orElse(min);
        ZonedDateTime e = executionTime2.nextExecution(f.plusNanos(1L)).orElse(min);
        if (f.isBefore(now) && e.isAfter(now)){
            return true;
        }
        start = f;
        while (start.isBefore(end) && !start.isEqual(min)){
            ZonedDateTime x = executionTime.nextExecution(start).orElse(min);
            ZonedDateTime x2 = executionTime2.nextExecution(x.plusNanos(1L)).orElse(min);

            int value = f.getDayOfWeek().getValue() % 7;
            ZonedDateTime weekEnd = f.plusDays(7L - value).withHour(0).withMinute(0).withSecond(0).withNano(0);
            ZonedDateTime weekStart = f.minusDays(value).withHour(0).withMinute(0).withSecond(0).withNano(0);
            if (weekStart.plusWeeks(intervalWeek + 1L).isBefore(x) && weekEnd.plusWeeks(intervalWeek + 1L).isAfter(x)){
                if (checkDateTime(x,x2,now)){
                    return true;
                }
                f = x;
            }
            if (weekStart.isBefore(x) && weekEnd.isAfter(x) && checkDateTime(x,x2,now)) {
                return true;
            }
            start = x;
        }
        return false;
    }

    public static boolean checkDateTime(ZonedDateTime x,ZonedDateTime x2,ZonedDateTime now){
        return x.isBefore(now) && x2.isAfter(now);
    }

    public static boolean checkDate(String beginPeriod, String endPeriod, Integer intervalWeek, String activityBegin, String activityEnd, String time) {
        try {
            Cron parseBegin = parser.parse(beginPeriod);
            ExecutionTime beginExecution = ExecutionTime.forCron(parseBegin);
            ExecutionTime endExecution = ExecutionTime.forCron(parser.parse(endPeriod));
            ZonedDateTime now = ZonedDateTime.of(LocalDateTime.parse(time, DateTimeFormatter.ofPattern(DateUtil.FORMAT_YYYYMMDDHHMISS_14)), ZoneId.systemDefault());
            if (null != intervalWeek && intervalWeek > 0) { // 每隔几周
                return checkDateByIntervalWeek(now, beginExecution, endExecution, intervalWeek, activityBegin, activityEnd);
            }

            ZonedDateTime start = now.plusNanos(1L);
            ZonedDateTime end = now.minusNanos(1L);

            Optional<ZonedDateTime> z1 = beginExecution.lastExecution(start);
            Optional<ZonedDateTime> z2 = endExecution.lastExecution(end);
            return (z1.isPresent() && !z2.isPresent()) || (z1.isPresent() && z2.isPresent() && z1.get().isAfter(z2.get()));
        } catch (Exception e) {
            return false;
        }
    }
}
