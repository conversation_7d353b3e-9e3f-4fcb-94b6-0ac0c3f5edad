/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR <PERSON><PERSON><PERSON>NTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.api;

/**
 * <功能描述>
 */
public class ApiConstants{


    private ApiConstants(){
    }


    public static final String ACTIVITY_TYPE = "Activity type：01-Activity 02-Coupon 03-lucky draw 04-flash sale";

    public static final String PRODUCT_CONDITION = "Product condition: 01-(Buy A get B discount, B must lower price than A)";

    public static final String PRODUCT_SELECTION_TYPE = "Product selection type: 01-Selection, 02-Invert Selection";

    public static final String PRODUCT_TYPE = "Product scope type: 00-All product 01-Product scope 02-Specify products 03-Multi product scope";

    public static final String INCENTIVE_LIMITED_FLAG = "Incentive limit mark: 00-unlimited 01-limited";

    public static final String ACTIVITY_STATUS = "Activity status: 01-draft 04-submitted 05-completed 07-Terminated";

    public static final String PURCHASE_CONSTRAINT_STATUS = "Purchase constraint status: 01-draft 04-submitted 05-completed 07-Terminated";

    public static final String COMBINESKUCODE = "Combine product sku code. If productCode, skuCode, categoryCode and attributes are all empty, combineSkuCode can not be empty. One of the productCode, skuCode, categoryCode or attributes is not empty, combineSkuCode must be empty.";

    public static final String FREE_POSTAGE = "0-Pay for shipment,1:Free shipping";//0:不包邮,1:包邮

    public static final String SANDBOX_TEST = "00-non-sandbox ,01-sandbox";//是否为沙盒模拟测试，00=非沙盒，01=沙盒

    public static final String SELECT_FLAG = "00-unchecked 01-check";//是否勾选，00=非勾选，01=勾选

    public static final String PROMO_SCOPE = "Promotion scope: 01-specific product, 02-range of products , 03-order";

//    public static final String REWARD_TYPE = "Incentive type: 01-减金额 02-打折扣 03-单件固定金额 04-组合固定金额 05-包邮 06-送赠品 07-买A送A 08-买A送B 12-买A减价B 13-买A打折B"

    public static final String REWARD_TYPE = "Incentive type: 01-specific amount discount" +
            " 02-percent off 03-special price 04-package price 05-free shipping 06-giveaway 07-Buy A get A free 08-Buy A get B free" +
            "12-Buy A get B discounted 13-13-Buy A get B percentage off";

    public static final String COUPON_STATUS = "Coupon status:01-Did not receive 02-Have to receive 03-Used 04-Locked 05-Expired.";

    public static final String TAKE_LABEL = "Take coupon label: 01-Purchase 02-OPS 03-giveaway promotion 99-Others";

    public static final String COUPON_TYPE = "Coupon type: 01-General coupon 02-Anonymous coupon 03-Single fixed promotion code.";

    public static final String FACE_UNIT = "Coupon face value unit: 01-amount 02-discount";

    public static final String CONDITION_UNIT = "Coupon use condition value unit: 01-amount 02-quantity";

    public static final String ORDERBY_TYPE = "Sort order：01-Descending order by creation time;02-Ascending by creation time;" +
            "03:Descending order by activity start time;04-Ascending by activity start time" +
            ";05-Descending order by activity end time;06-Ascending by activity end time";
//    public static final String ORDERBY_TYPE = "排序方式：01-根据创建时间降序;02-根据创建时间升序;03-根据活动开始时间降序;04-根据活动开始时间升序;05-根据活动结束时间降序;06-根据活动结束时间升序"

    public static final String RELEASE_TYPE = "Coupon release type: 01-immediately, 02-appointment";

    public static final String NEEDMOREUNIT = "01-currency unit 02-piece";// 数值单位：01元；02件（捆绑和满送活动此值为空）

    public static final String FROZEN_STATUS = "Frozen status：01-Not frozen 02-Frozen";

    // 04-特价、05-包邮、06-赠品、07-捆绑、08-满送
    public static final String ACTIVITY_SORT = "Activity sort: 01-Simple promotion, 02-Order discount, 04-special price、05-free shipping、6-giveaway、7-package、8-limited free、10-Buy A get B discount";

    public static final String ACTIVITY_TAGCODE = "Activity tag codes. Separated by commas. 01-Simple promotion, 02-Order discount, 04-special price、05-free shipping、06-giveaway、07-package、08-limited free、10-Buy A get B discount";

    public static final String RECEIVE_STATUS = "Receive status：1-available；2-has not started；3-is over；4-none；5-restricted；6-member-level doesn't match；7：closed";
    
//    public static final String RECEIVE_STATUS_STORE = "领取状态：1：可领取；2：未开始；3：已结束；4：已领完；5：已被限领；6：会员等级不符合要求"
    public static final String RECEIVE_STATUS_STORE = "Receive status：1-available；2-has not started；3-is over；4-none；5-restricted；6-member-level doesn't match";

    public static final String PRICE_TYPE = "Price Type,01:Sale price,02:Tag price";

//    public static final String FUNCTION_TYPE = "函数类型：01-促销范围 02-促销条件 03-条件参数 04-促销奖励"
    public static final String FUNCTION_TYPE = "Function type：01-promotion range 02-promotion condition 03-promotion parameters 04-rewards";

    public static final String PARAM_TYPE = "Param type: 01-empty 02-number 03-JSON";

//    public static final String PARAM_UNIT = "参数单位: 01-元 02-件 03-折"
    public static final String PARAM_UNIT = "01-currency unit: 02-piece 03-percent off";

    public static final String OPERATOR = "Promotion condition operator.(==,!=,>,<,>=,<=)";

    public static final String GROUP_INNER_RELATION = "Inner relations in the same qualification group. (OR, AND)";

    public static final String QUALIFICATIONS = "Check some condition whether or not eligible for active promotion, for example {\"memberTagCode\":[\"313\",\"3435\"],\"isMember\":[\"1\"],\"channelCode\":[\"123\",\"1234\"],\"paymentMethod\":[\"applePay\"]}";
}
