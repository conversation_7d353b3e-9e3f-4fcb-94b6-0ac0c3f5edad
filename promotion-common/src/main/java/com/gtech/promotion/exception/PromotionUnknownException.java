/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * <PERSON>ECH MAKES NO REPRESENTATIONS OR <PERSON><PERSON><PERSON><PERSON>IES ABOUT THE SUITABILITY OF THE SOFTWARE, <PERSON>ITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * <PERSON>ECH MAKES NO REPRESENTATIONS OR <PERSON><PERSON><PERSON><PERSON>IES ABOUT THE SUITABILITY OF THE SOFTWARE, <PERSON><PERSON><PERSON><PERSON> EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.exception;

import com.gtech.commons.exception.ErrorCode;
import com.gtech.commons.exception.GTechBaseException;


/**
 * IDMUnknownException
 *
 * <AUTHOR>
 * @Date 2019-09-29 10:23
 */
public class PromotionUnknownException extends GTechBaseException {

    // serialVersionUID
    private static final long serialVersionUID = 179003011399576127L;

    /**
     * Constructor without parameters.
     */
    public PromotionUnknownException() {

        super();
    }

    /**
     * Constructor with Throwable.
     */
    public PromotionUnknownException(Throwable cause) {

        super(cause);
    }

    /**
     * Constructor with message and Throwable.
     */
    public PromotionUnknownException(String message, Throwable cause, Object...args) {

        super(message, cause, args);
    }

    /**
     *Constructor with code and message.
     */
    public PromotionUnknownException(String code, String message, Object...args) {

        super(code, message, args);
    }

    /**
     * Constructor with code, message and Throwable.
     */
    public PromotionUnknownException(String code, String message, Throwable cause, Object...args) {

        super(code, message, cause, args);
    }

    /**
     * Constructor with ErrorCode.
     */
    public PromotionUnknownException(ErrorCode errorInfo, Object...args) {

        super(errorInfo, args);
    }

    /**
     * Constructor with ErrorCode and Throwable.
     */
    public PromotionUnknownException(ErrorCode errorInfo, Throwable cause, Object...args) {

        super(errorInfo, cause, args);
    }

}
