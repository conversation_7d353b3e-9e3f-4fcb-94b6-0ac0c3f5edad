/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.exception;

import com.gtech.promotion.checker.Checker;
import com.gtech.promotion.checker.SystemChecker;

/**
 * 判断条件的工具类，多用于参数校验
 */
public class Check{

    private Check(){
        throw new PromotionException(SystemChecker.CAN_NOT_INSTANCE_ERROR);
    }

    /**
     * 检查是否符合判断条件，如果符合，抛出异常
     * 
     * @param condition
     *            判断条件
     * @param rule
     *            异常规则
     * @param replaceStr
     *            替换异常提示的字符串
     */
    public static void check(boolean condition,Checker rule,String...replaceStr){
        if (condition){
            String message = rule.getMessage();
            if (null != replaceStr && replaceStr.length > 0){
                for (int i = 0; i < replaceStr.length; i++){
                    String replace = "${" + i + "}";
                    message = message.replace(replace, replaceStr[i]);
                }
            }
            throw new PromotionException(rule.getCode(), message);
        }
    }

}
