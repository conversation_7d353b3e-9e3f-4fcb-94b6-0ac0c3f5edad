/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * <PERSON>ECH MAKES NO REPRESENTATIONS OR <PERSON><PERSON><PERSON><PERSON>IES ABOUT THE SUITABILITY OF THE SOFTWARE, <PERSON>ITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * <PERSON>ECH MAKES NO REPRESENTATIONS OR <PERSON><PERSON><PERSON><PERSON>IES ABOUT THE SUITABILITY OF THE SOFTWARE, <PERSON><PERSON><PERSON><PERSON> EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.exception;

import com.gtech.commons.exception.ErrorCode;
import com.gtech.commons.exception.Exceptions;
import com.gtech.promotion.utils.Constants;

/**
 * Store error code definition
 *
 * <AUTHOR>
 */
public abstract class ErrorCodes {

    protected ErrorCodes() {
    }


    public static final ErrorCode ERROR_UNKNOWN = Exceptions.errorCode(PromotionUnknownException.class,
            Constants.ERR_CODE_PREFIX, "0201", "An unknown error.");

    public static final ErrorCode PARAM_EMPTY = Exceptions.errorCode(PromotionParamValidateException.class,
            Constants.ERR_CODE_PREFIX, "0202", "The Parameter ({0}) cannot be empty.");

    public static final ErrorCode PARAM_LENGTH = Exceptions.errorCode(PromotionParamValidateException.class,
            Constants.ERR_CODE_PREFIX, "0203", "The Parameter ({0}) too long.");

    public static final ErrorCode PARAM_ERROR = Exceptions.errorCode(PromotionParamValidateException.class,
            Constants.ERR_CODE_PREFIX, "0204", "Parameter ({0}) specification error.");

    public static final ErrorCode PARAM_ERROR_TEMPLATE_CODE = Exceptions.errorCode(PromotionParamValidateException.class,
            Constants.ERR_CODE_PREFIX, "0205", "TemplateCode ({0}) specification error.");

    public static final ErrorCode PARAM_ERROR_FUNCTION_CODE = Exceptions.errorCode(PromotionParamValidateException.class,
            Constants.ERR_CODE_PREFIX, "0206", "FunctionCode ({0}) of templateCode ({1}) specification error.");

    public static final ErrorCode PARAM_DUPLICATED = Exceptions.errorCode(PromotionParamValidateException.class,
            Constants.ERR_CODE_PREFIX, "0207", "Do not use same parameter ({0}).");

    public static final ErrorCode PARAM_LEAST_ONE = Exceptions.errorCode(PromotionParamValidateException.class,
            Constants.ERR_CODE_PREFIX, "0208", "At least one parameter ({0}) needs to be specified.");

    public static final ErrorCode PARAM_SPECIFICATION_ERROR = Exceptions.errorCode(PromotionParamValidateException.class,
            Constants.ERR_CODE_PREFIX, "0209", "{0}.");

    public static final ErrorCode TEMPLATE_EXPRESSION_ERROR = Exceptions.errorCode(PromotionParamValidateException.class,
            Constants.ERR_CODE_PREFIX, "0210", "Template expression specification error.");

    public static final ErrorCode VALIDATE_ACTIVITY_EXIST = Exceptions.errorCode(PromotionParamValidateException.class,
            Constants.ERR_CODE_PREFIX, "0211", "No effective activity was found. ({0})");

    public static final ErrorCode VALIDATE_COUPON_ACTIVITY_EXIST = Exceptions.errorCode(PromotionParamValidateException.class,
            Constants.ERR_CODE_PREFIX, "0212", "No effective coupon activity was found. ({0})");

    public static final ErrorCode VALIDATE_COUPON_CODE_EXIST = Exceptions.errorCode(PromotionParamValidateException.class,
            Constants.ERR_CODE_PREFIX, "0213", "No effective coupon code was found. ({0})");

    public static final ErrorCode COUPON_ACTIVITY_MATCH_FAILED = Exceptions.errorCode(PromotionCouponException.class,
            Constants.ERR_CODE_PREFIX, "0214", "No effective coupon activity has been matched.");

    public static final ErrorCode COUPON_INVENTORY_NOT_ENOUGH = Exceptions.errorCode(PromotionCouponException.class,
            Constants.ERR_CODE_PREFIX, "0215", "No enough coupon inventory.");


    public static final ErrorCode COUPON_RECEIVE_MEMBER_QUALIFICATION_FAILED = Exceptions.errorCode(PromotionCouponException.class,
            Constants.ERR_CODE_PREFIX, "0216", "Qualification match failed.");

    public static final ErrorCode COUPON_RECEIVE_MEMBER_LABEL_FAILED = Exceptions.errorCode(PromotionCouponException.class,
            Constants.ERR_CODE_PREFIX, "0217", "User label match failed.");

    public static final ErrorCode COUPON_USE_MORE_THAN_ONE_FAIL = Exceptions.errorCode(PromotionCouponException.class,
            Constants.ERR_CODE_PREFIX, "0218", "Activity cannot use more than one coupon.");

    public static final ErrorCode COUPON_USE_FAILED = Exceptions.errorCode(PromotionCouponException.class,
            Constants.ERR_CODE_PREFIX, "0219", "Coupon can not be used.");

    public static final ErrorCode COUPON_USE_FAILED_P = Exceptions.errorCode(PromotionCouponException.class,
            Constants.ERR_CODE_PREFIX, "0220", "Coupon can not be used. ({0})");

    public static final ErrorCode COUPON_VALID_START_TIME_FAIL = Exceptions.errorCode(PromotionCouponException.class,
            Constants.ERR_CODE_PREFIX, "0221", "The using time of the coupon {0} has not arrived.");

    public static final ErrorCode COUPON_VALID_END_TIME_FAIL = Exceptions.errorCode(PromotionCouponException.class,
            Constants.ERR_CODE_PREFIX, "0222", "The coupon {0} has already expired.");

    public static final ErrorCode COUPON_VALID_COUPON_FROZEN = Exceptions.errorCode(PromotionCouponException.class,
            Constants.ERR_CODE_PREFIX, "0223", "The coupon {0} has already frozen.");

    public static final ErrorCode COUPON_FILTER_EFFECTIVE_FAILED = Exceptions.errorCode(PromotionCouponException.class,
            Constants.ERR_CODE_PREFIX, "0224", "Coupon activity is not effective. ({0})");

    public static final ErrorCode COUPON_FILTER_MEMBER_FAILED = Exceptions.errorCode(PromotionCouponException.class,
            Constants.ERR_CODE_PREFIX, "0225", "The qualification of membership verification failed. ({0})");

    public static final ErrorCode CALC_ACTIVITY_NOT_FOUND = Exceptions.errorCode(PromotionCalcException.class,
            Constants.ERR_CODE_PREFIX, "0226", "No specific promotion activity was found: ({0})");

    public static final ErrorCode CALC_TEMPLATE_NOT_FOUND = Exceptions.errorCode(PromotionCalcException.class,
            Constants.ERR_CODE_PREFIX, "0227", "No specific promotion template was found. ({0})");

    public static final ErrorCode CALC_TEMPLATE_FUNCTION_NOT_FOUND = Exceptions.errorCode(PromotionCalcException.class,
            Constants.ERR_CODE_PREFIX, "0228", "No specific promotion template function was found. ({0})");

    public static final ErrorCode CALC_FUNCTION_DUPLICATED = Exceptions.errorCode(PromotionCalcException.class,
            Constants.ERR_CODE_PREFIX, "0229", "CalcFunction ({0}) already registered..");

    public static final ErrorCode CALC_VARIABLE_EMPTY = Exceptions.errorCode(PromotionCalcException.class,
            Constants.ERR_CODE_PREFIX, "0230", "{0} is empty..");

    public static final ErrorCode CALC_FUNCTION_TYPE_MATCH_FAILED = Exceptions.errorCode(PromotionCalcException.class,
            Constants.ERR_CODE_PREFIX, "0231", "{0} match failed.");

    public static final ErrorCode ACTIVITY_LIMIT_ACTIVITY_TOTAL_COUNT = Exceptions.errorCode(PromotionCalcException.class,
            Constants.ERR_CODE_PREFIX, "0232", "Exceeding the activity max count limit.");

    public static final ErrorCode ACTIVITY_LIMIT_ACTIVITY_TOTAL_AMOUNT = Exceptions.errorCode(PromotionCalcException.class,
            Constants.ERR_CODE_PREFIX, "0233", "Exceeding the activity max amount limit.");
    
    public static final ErrorCode ACTIVITY_LIMIT_USER_TOTAL_COUNT = Exceptions.errorCode(PromotionCalcException.class,
            Constants.ERR_CODE_PREFIX, "0234", "Exceeding the user max count limit.");

    public static final ErrorCode ACTIVITY_LIMIT_USER_TOTAL_AMOUNT = Exceptions.errorCode(PromotionCalcException.class,
            Constants.ERR_CODE_PREFIX, "0235", "Exceeding the user max amount limit.");

    public static final ErrorCode ACTIVITY_LIMIT_CONDITION_QUANTITY = Exceptions.errorCode(PromotionCalcException.class,
            Constants.ERR_CODE_PREFIX, "0236", "The quantity of commodity is not enough.");

    public static final ErrorCode ACTIVITY_LIMIT_CONDITION_AMOUNT = Exceptions.errorCode(PromotionCalcException.class,
            Constants.ERR_CODE_PREFIX, "0237", "The amount of commodity is not enough.");

    public static final ErrorCode ACTIVITY_LIMIT_CONDITION_DAY_ORDER = Exceptions.errorCode(PromotionCalcException.class,
            Constants.ERR_CODE_PREFIX, "0238", "The order of commodity is not enough.");

    public static final ErrorCode ACTIVITY_PARAM_ERROR = Exceptions.errorCode(PromotionCalcException.class,
            Constants.ERR_CODE_PREFIX, "0239", "Activity parameter configuration error.");

    public static final ErrorCode LOCK_PROMOTION_RESOURCES_FAILED_ACTIVITY_TOTAL_COUNT = Exceptions.errorCode(PromotionOrderException.class,
            Constants.ERR_CODE_PREFIX, "0240", "Lock promotion resources failed: activity-total-count");

    public static final ErrorCode LOCK_PROMOTION_RESOURCES_FAILED_ACTIVITY_TOTAL_AMOUNT = Exceptions.errorCode(PromotionOrderException.class,
            Constants.ERR_CODE_PREFIX, "0241", "Lock promotion resources failed: activity-total-amount");

    public static final ErrorCode LOCK_PROMOTION_RESOURCES_FAILED_USER_SKU_TOTAL_TOTAL = Exceptions.errorCode(PromotionOrderException.class,
            Constants.ERR_CODE_PREFIX, "0242", "Lock promotion resources failed: user-sku-count");
    public static final ErrorCode LOCK_PROMOTION_RESOURCES_FAILED_SKU_TOTAL_TOTAL = Exceptions.errorCode(PromotionOrderException.class,
            Constants.ERR_CODE_PREFIX, "0243", "Lock promotion resources failed: sku-count");

    public static final ErrorCode LOCK_PROMOTION_RESOURCES_FAILED_USER_TOTAL_COUNT = Exceptions.errorCode(PromotionOrderException.class,
            Constants.ERR_CODE_PREFIX, "0244", "Lock promotion resources failed: user-total-count");

    public static final ErrorCode LOCK_PROMOTION_RESOURCES_FAILED_USER_TOTAL_AMOUNT = Exceptions.errorCode(PromotionOrderException.class,
            Constants.ERR_CODE_PREFIX, "0245", "Lock promotion resources failed: user-total-amount");

    public static final ErrorCode CREATE_ORDER_DEDUCTED_AMOUNT_ERROR = Exceptions.errorCode(PromotionOrderException.class,
            Constants.ERR_CODE_PREFIX, "0246", "The total amount of promotion deduction is incorrect, so ({0}) should be deducted.");

    public static final ErrorCode CREATE_ORDER_DEDUCTED_POSTAGE_ERROR = Exceptions.errorCode(PromotionOrderException.class,
            Constants.ERR_CODE_PREFIX, "0247", "The total amount of postage deduction is incorrect, so ({0}) should be deducted.");

    public static final ErrorCode FLASH_SALE_INVENTORY_NOT_ENOUGH = Exceptions.errorCode(PromotionCouponException.class,
            Constants.ERR_CODE_PREFIX, "0248", "No enough flash sale inventory.");

    public static final ErrorCode USER_CODE_MOBILE_ACCOUNT_MUST_ONE = Exceptions.errorCode(PromotionCouponException.class,
			Constants.ERR_CODE_PREFIX, "0249", "userAccounts or userCodes or userMobiles or userTags must be specified one.");

    public static final ErrorCode USER_MAX_TIME_EXCEEDED = Exceptions.errorCode(PromotionCouponException.class,
            Constants.ERR_CODE_PREFIX, "0250", "The maximum times have been exceeded.");

    public static final ErrorCode USER_MAX_TIME_PER_DAY_EXCEEDED = Exceptions.errorCode(PromotionCouponException.class,
            Constants.ERR_CODE_PREFIX, "0251", "The maximum times per day have been exceeded.");

    public static final ErrorCode CUSTOM_CONDITION = Exceptions.errorCode(PromotionParamValidateException.class,
            Constants.ERR_CODE_PREFIX, "0252", "The custom conditional switch is faulty. Check related configurations {0}");

    public static final ErrorCode SWITCH = Exceptions.errorCode(PromotionParamValidateException.class,
            Constants.ERR_CODE_PREFIX, "0253", "The value of the parameter {0} can only be 0 or 1.");


    public static final ErrorCode ACTIVITY_TYPE_GROUP = Exceptions.errorCode(PromotionParamValidateException.class,
            Constants.ERR_CODE_PREFIX, "0254", "Activity type should be 06.");

    public static final ErrorCode DATA_INTEGER = Exceptions.errorCode(PromotionParamValidateException.class,
            Constants.ERR_CODE_PREFIX, "0255", "The Parameter ({0}) value must be a positive integer.");

    public static final ErrorCode DATA_GROUP_SIZE_INTEGER = Exceptions.errorCode(PromotionParamValidateException.class,
            Constants.ERR_CODE_PREFIX, "0256", "The Parameter ({0}) value must be a positive integer and at least greater than 2.");

    public static final ErrorCode PROMO_PASSWORD = Exceptions.errorCode(PromotionParamValidateException.class,
            Constants.ERR_CODE_PREFIX, "0257", "The Parameter ({0}) value cannot contain commas.");

    public static final ErrorCode PROMO_DUPLICATE = Exceptions.errorCode(PromotionParamValidateException.class,
            Constants.ERR_CODE_PREFIX, "0258", "The Parameter ({0})  duplicate keys.");

    public static final ErrorCode CUSTOM_KEY = Exceptions.errorCode(PromotionParamValidateException.class,
            Constants.ERR_CODE_PREFIX, "0259", "The key in the ({0}) parameter cannot be empty .");

    public static final ErrorCode CUSTOM_KEY_VALUE = Exceptions.errorCode(PromotionParamValidateException.class,
            Constants.ERR_CODE_PREFIX, "0260", "The key in the ({0})  parameter, the corresponding value is an array.");

    public static final ErrorCode GROUP_ACTIVITY_EFFECTIVE = Exceptions.errorCode(PromotionParamValidateException.class,
            Constants.ERR_CODE_PREFIX, "0261", "There are effective activities under the group and deletion of the group is not allowed.");

    public static final ErrorCode GROUP_CODE_INTEGER = Exceptions.errorCode(PromotionParamValidateException.class,
            Constants.ERR_CODE_PREFIX, "0262", "The value of the parameter {0} only be an integer");

    public static final ErrorCode PARAM_GROUP_TYPE = Exceptions.errorCode(PromotionParamValidateException.class,
            Constants.ERR_CODE_PREFIX, "0263", "Group type ({0}) does not exist.");

    public static final ErrorCode PARAM_GROUP_NOT_EXIST = Exceptions.errorCode(PromotionParamValidateException.class,
            Constants.ERR_CODE_PREFIX, "0264", "Group does not exist.");

    public static final ErrorCode DEFAULT_GROUP = Exceptions.errorCode(PromotionParamValidateException.class,
            Constants.ERR_CODE_PREFIX, "0265", "Default group cannot be deleted.");

    public static final ErrorCode GROUP_STATUS = Exceptions.errorCode(PromotionParamValidateException.class,
            Constants.ERR_CODE_PREFIX, "0266", "Group status ({0}) does not exist.");

    public static final ErrorCode LOCK_PROMOTION_RESOURCES_FAILED_USER_ORDER_COUNT = Exceptions.errorCode(PromotionOrderException.class,
            Constants.ERR_CODE_PREFIX, "0267", "Lock promotion resources failed,the user has reached the maximum order limit.");

    public static final ErrorCode EXPORT_COUPON = Exceptions.errorCode(PromotionParamValidateException.class,
            Constants.ERR_CODE_PREFIX, "0268", "导入外部券码使用，couponCodes 入参不能为空.");

    public static final ErrorCode ERROR_BOOST_SHARDING_TYPE = Exceptions.errorCode(PromotionParamValidateException.class,
            Constants.ERR_CODE_PREFIX, "0269", "Wrong boost sharing type parameter.");
    public static final ErrorCode ERROR_LOCK_RIGHT_OF_FIRST_REFUSAL = Exceptions.errorCode(PromotionParamValidateException.class,
            Constants.ERR_CODE_PREFIX, "0270", "Error locking right of first refusal.");

    public static final ErrorCode NOT_EXIST = Exceptions.errorCode(PromotionParamValidateException.class,
            Constants.ERR_CODE_PREFIX, "0271", "{0} 类型不存在.");

    public static final ErrorCode NO_ORG_CODE_ACTIVITY_EXIST = Exceptions.errorCode(PromotionParamValidateException.class,
            Constants.ERR_CODE_PREFIX, "0272", "输入的参数orgCode:{0}， 无对应的活动 ");


    public static final ErrorCode ACTIVITY_LIMIT_ACTIVITY_DAY_TOTAL_COUNT = Exceptions.errorCode(PromotionCalcException.class,
            Constants.ERR_CODE_PREFIX, "0273", "Exceeding the day activity max count limit.");


    public static final ErrorCode ACTIVITY_LIMIT_ACTIVITY_WEEK_TOTAL_COUNT = Exceptions.errorCode(PromotionCalcException.class,
            Constants.ERR_CODE_PREFIX, "0274", "Exceeding the week activity max count limit.");

    public static final ErrorCode ACTIVITY_LIMIT_ACTIVITY_MONTH_TOTAL_COUNT = Exceptions.errorCode(PromotionCalcException.class,
            Constants.ERR_CODE_PREFIX, "0275", "Exceeding the month activity max count limit.");
}
