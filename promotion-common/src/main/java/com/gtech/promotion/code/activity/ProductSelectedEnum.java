package com.gtech.promotion.code.activity;

import java.util.Objects;

public enum ProductSelectedEnum {
    SELECT(1,"选中"),
    NO_SELECT(0,"未选中"),
    ;

    ProductSelectedEnum(Integer code, String desc){
        this.code = code;
        this.desc = desc;
    }

    private final Integer code;
    private final String desc;


    /**
     * 判断是否是合法
     *
     * @param code 类型code
     * @return true：合法
     */
    public static boolean exist(String code){
        ProductSelectionEnum[] values = ProductSelectionEnum.values();
        for (ProductSelectionEnum type : values){
            if (type.code().equals(code)){
                return true;
            }
        }
        return false;
    }

    public Integer code(){
        return code;
    }

    public String desc(){
        return desc;
    }

    public boolean equalsCode(Integer code) {

        return Objects.equals(this.code(), code);
    }

}
