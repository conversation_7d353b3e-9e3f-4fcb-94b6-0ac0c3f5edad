package com.gtech.promotion.code.coupon;

public enum CouponReleaseStatusEnum {

    NOT_CREATE_COUPON("01","未生成券码"), CREATE_COUPON("02","已生成券码"), OUTER_IMPORTANT("03","外部导入"), STOP_RELEASE("04","投放已中止");

    CouponReleaseStatusEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    private String code;

    private String desc;

    public String code() {

        return code;
    }

    public String desc() {

        return desc;
    }

    public boolean equalsCode(String code) {

        return this.code().equals(code);
    }
}
