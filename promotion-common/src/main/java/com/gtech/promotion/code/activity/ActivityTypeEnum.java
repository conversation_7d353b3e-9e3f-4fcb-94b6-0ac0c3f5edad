/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.code.activity;

import com.gtech.commons.code.IEnum;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 促销类型
 */
public enum ActivityTypeEnum implements IEnum {

    ACTIVITY("01","活动"), COUPON("02","券");

    ActivityTypeEnum(String code, String desc){
        this.code = code;
        this.desc = desc;
    }

    private final String code;

    private final String desc;

    /**
     * 判断是否是合法的类型
     * 
     * @param code
     *            类型code
     * @return true：合法
     */
    public static boolean exist(String code){
        ActivityTypeEnum[] values = ActivityTypeEnum.values();
        for (ActivityTypeEnum type : values){
            if (type.code().equals(code)){
                return true;
            }
        }
        return false;
    }

    public static boolean isActivity(String code){
        return ACTIVITY.code.equals(code);
    }
    
    public static boolean isCoupon(String code){
        return COUPON.code.equals(code);
    }

    public static List<String> getCodes() {
        //获取ActivityTypeEnum所有code
        ActivityTypeEnum[] values = ActivityTypeEnum.values();
        return Arrays.stream(values).map(ActivityTypeEnum::code).collect(Collectors.toList());
    }


    public String code(){
        return code;
    }

    public String desc(){
        return desc;
    }

    public boolean equalsCode(String code) {

        return this.code().equals(code);
    }

}
