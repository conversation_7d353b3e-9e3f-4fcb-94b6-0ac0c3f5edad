package com.gtech.promotion.code.marketing;

import com.gtech.commons.code.IEnum;
import com.gtech.commons.utils.EnumUtil;

public enum MarketingGroupStatusEnum implements IEnum {

    //拼团状态
    GROUP_NO_START("00", "未开始"),
    GROUP_PROCESSING("01", "进行中"),
    GROUP_SUCCESS("02", "拼团成功结束"),
    GROUP_FAIL("03", "拼团失败"),

    ;

    MarketingGroupStatusEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    private String code;

    private String desc;

    @Override
    public String code() {

        return code;
    }

    @Override
    public String desc() {

        return desc;
    }

    @Override
    public boolean equalsCode(String code) {

        return this.code().equals(code);
    }

    /**
     * Check whether the enumeration code is valid
     */
    public static boolean exist(String code) {

        return EnumUtil.code2Enum(MarketingGroupStatusEnum.class, code) != null;
    }

}
