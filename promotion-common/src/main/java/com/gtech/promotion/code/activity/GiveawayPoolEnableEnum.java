package com.gtech.promotion.code.activity;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 促销满赠 启用线程池
 * 1:启用 2:禁用
 */
@Getter
@AllArgsConstructor
public enum GiveawayPoolEnableEnum {

    ENABLE("1","启用"),
    DISABLE("2","禁用"),
    ;


    private final String code;

    private final String desc;


    public static boolean exist(String code){
        GiveawayPoolEnableEnum[] values = GiveawayPoolEnableEnum.values();
        for (GiveawayPoolEnableEnum type : values){
            if (type.getCode().equals(code)){
                return true;
            }
        }
        return false;
    }


    public boolean equalsCode(String code) {

        return this.getCode().equals(code);
    }

}
