/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WA<PERSON>RANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.code.coupon;

import com.gtech.promotion.checker.coupon.CouponErrorChecker;

/**
 *    
 */
public enum CouponStatusEnum{

    UN_GRANT("01","unreceived"),
    GRANTED("02","unused"),
    USED("03","used"),
    LOCKED("04","locked"),
    EXPIRE("05","expired");

    CouponStatusEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    private String code;

    private String desc;

    public static CouponStatusEnum getByCode(String code) {

        CouponStatusEnum[] values = CouponStatusEnum.values();
        for (CouponStatusEnum value : values) {
            if (value.code().equals(code)) {
                return value;
            }
        }
        return null;
    }

    public String code() {

        return code;
    }

    public String desc() {

        return desc;
    }

    public boolean equalsCode(String code) {

        return this.code().equals(code);
    }

    /**
     * 判断是否是合法的类型
     *
     * @param code
     *            类型code
     * @return true：合法
     */
    public static boolean exist(String code) {

        CouponStatusEnum[] values = CouponStatusEnum.values();
        for (CouponStatusEnum type : values) {
            if (type.code().equals(code)) {
                return true;
            }
        }
        return false;
    }

    public static CouponErrorChecker getError(String code){

        switch (code) {
            case "01":
                return CouponErrorChecker.THE_VOUCHER_WAS_NOT_RECEIVED;
            case "02":
                return CouponErrorChecker.THE_VOUCHER_IS_NOT_USED;
            case "03":
                return CouponErrorChecker.THE_COUPON_HAS_BEEN_USED;
            case "04":
                return CouponErrorChecker.THE_COUPON_IS_LOCKED;
            case "05":
                return CouponErrorChecker.THE_VOUCHER_HAS_EXPIRED;
            default:
                return CouponErrorChecker.ERROR;
        }



    }


}
