package com.gtech.promotion.code.activity;

import com.gtech.commons.code.IEnum;

public enum OpsTypeEnum implements IEnum {

    OPS_101("101","打折促销"),
    OPS_102("102","买A优惠B"),
    OPS_103("103","订单满减满折"),
    OPS_104("104","一口价"),
    OPS_105("105","买多优惠"),
    OPS_106("106","满赠促销"),
    OPS_107("107","运费促销"),
    OPS_108("108","单品特价促销"),
    OPS_201("201","打折券"),
    OPS_202("202","满减满折券"),
    OPS_203("203","满减满折码"),
    OPS_204("204","实物券"),
    OPS_205("205","运费促销"),
    OPS_206("206","买多优惠券"),
	OPS_207("207", "满赠促销券"),
    OPS_208("208","单品特价券"),
    ;

    OpsTypeEnum(String code, String desc){
        this.code = code;
        this.desc = desc;
    }

    private final String code;

    private final String desc;

    public String code(){
        return code;
    }

    public String desc(){
        return desc;
    }

    public boolean equalsCode(String code) {

        return this.code().equals(code);
    }

    /**
     * 判断是否是合法的类型
     *
     * @param code
     *            类型code
     * @return true：合法
     */
    public static boolean exist(String code){
        OpsTypeEnum[] values = OpsTypeEnum.values();
        for (OpsTypeEnum type : values){
            if (type.code().equals(code)){
                return true;
            }
        }
        return false;
    }

}
