/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR <PERSON><PERSON><PERSON>NTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.code.marketing;

import com.gtech.commons.code.IEnum;
import com.gtech.commons.utils.EnumUtil;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

public enum ActivityTypeEnum implements IEnum{

    LUCKY_DRAW("03","lucky draw"),
    FLASH_SALE("04","flash sale"),
    PRE_SALE("05","pre-sale"),
    GROUP("06","marketing-group"),
    BOOST_SHARDING("07","boost sharing");

    ActivityTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    private String code;

    private String desc;

    @Override
    public String code() {

        return code;
    }

    @Override
    public String desc() {

        return desc;
    }

    @Override
    public boolean equalsCode(String code) {

        return this.code().equals(code);
    }

    /**
     * Check whether the enumeration code is valid
     */
    public static boolean exist(String code){

        return EnumUtil.code2Enum(ActivityTypeEnum.class, code) != null;
    }

    public static List<String> getCodes() {
        //获取ActivityTypeEnum所有code
        ActivityTypeEnum[] values = ActivityTypeEnum.values();
        return Arrays.stream(values).map(ActivityTypeEnum::code).collect(Collectors.toList());
    }

}
