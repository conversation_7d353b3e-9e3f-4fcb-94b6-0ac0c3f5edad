package com.gtech.promotion.code.marketing;

import com.gtech.commons.code.IEnum;
import com.gtech.commons.utils.EnumUtil;

public enum UserGroupStatusEnum implements IEnum{

//    用户参与拼团状态 01进行中，02 拼团结束
    PROCESSING("01","进行中"),
    FINISH("02","拼团成功"),
    CANCEL("03","取消拼团"),
    PAID("04","已支付"),

    ;

    UserGroupStatusEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    private String code;

    private String desc;

    @Override
    public String code() {

        return code;
    }

    @Override
    public String desc() {

        return desc;
    }

    @Override
    public boolean equalsCode(String code) {

        return this.code().equals(code);
    }

    /**
     * Check whether the enumeration code is valid
     */
    public static boolean exist(String code){

        return EnumUtil.code2Enum(UserGroupStatusEnum.class, code) != null;
    }

}
