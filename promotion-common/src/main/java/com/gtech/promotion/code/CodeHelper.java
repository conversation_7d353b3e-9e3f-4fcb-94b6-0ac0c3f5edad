/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.code;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import org.apache.commons.collections4.CollectionUtils;

import com.gtech.promotion.code.activity.ActivityStoreEnum;

/**
 * 
 *
 * <AUTHOR>
 * @Date 2020-02-07
 */
public class CodeHelper {

    private CodeHelper() {
        // No codes
    }

    // 全店铺
    private static final String ALL_STORE_CODE = "all";

    // 不限店铺
    private static final String ANY_STORE_CODE = "any";

    public static String getOrgCode(String orgCode) {

        if (null == orgCode) {

            return ActivityStoreEnum.ALL.code();

        } else if (ALL_STORE_CODE.equalsIgnoreCase(orgCode)) {

            return ActivityStoreEnum.ALL.code();

        } else if (ANY_STORE_CODE.equalsIgnoreCase(orgCode)) {

            return ActivityStoreEnum.ANY.code();

        } else {

            return orgCode;
        }
    }

    public static List<String> getOrgCodes(List<String> orgCodes) {

        if (CollectionUtils.isEmpty(orgCodes)) {

            return Arrays.asList(ActivityStoreEnum.ALL.code());
        }

        List<String> newOrgCodes = new ArrayList<>();
        for (String e : orgCodes) {
            newOrgCodes.add(getOrgCode(e));
        }

        return newOrgCodes;
    }

}
