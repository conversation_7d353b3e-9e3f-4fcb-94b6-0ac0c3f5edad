/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WA<PERSON>RANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.code.marketing;

import com.gtech.commons.code.IEnum;
import com.gtech.commons.utils.EnumUtil;

public enum PrizeTypeEnum implements IEnum{

    DEFAULT("00","no prize","未中奖"),
    COUPON("01","coupon","优惠券"),
    RIGHT_OF_FIRST_REFUSAL("02","right of first refusal","优先购买权"),
    PRODUCT("03","product","商品"),
    ;

    PrizeTypeEnum(String code, String desc,String language) {
        this.code = code;
        this.desc = desc;
        this.language = language;
    }

    private String code;

    private String desc;

    private String language;

    @Override
    public String code() {

        return code;
    }

    public String language() {

        return language;
    }

    @Override
    public String desc() {

        return desc;
    }

    @Override
    public boolean equalsCode(String code) {

        return this.code().equals(code);
    }

    /**
     * Check whether the enumeration code is valid
     */
    public static boolean exist(String code){

        return EnumUtil.code2Enum(PrizeTypeEnum.class, code) != null;
    }

    public static PrizeTypeEnum getEnumByCode(String code){

        PrizeTypeEnum[] values = PrizeTypeEnum.values();
        for (PrizeTypeEnum value : values) {
            if (value.code().equals(code)) {
                return value;
            }
        }
        return null;
    }



}
