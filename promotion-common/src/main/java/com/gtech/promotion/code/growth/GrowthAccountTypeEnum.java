/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR <PERSON><PERSON><PERSON>NTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.code.growth;

import com.gtech.commons.code.IEnum;
import com.gtech.commons.utils.EnumUtil;

/**
 * PointTransactionTypeEnum
 *
 * <AUTHOR>
 * @Date 2020-04-07
 */
public enum GrowthAccountTypeEnum implements IEnum{

    USER(1,"User account"),
    ORG(2,"Organization account");

    GrowthAccountTypeEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    private int code;

    private String desc;

    public int number() {

        return this.code;
    }

    public boolean equalsCode(int code) {

        return this.code == code;
    }

    @Override
    public String code() {

        return String.valueOf(code);
    }

    @Override
    public String desc() {

        return desc;
    }

    @Override
    public boolean equalsCode(String code) {

        return this.code().equals(code);
    }

    /**
     * Check whether the enumeration code is valid
     */
    public static boolean exist(String code){

        return EnumUtil.code2Enum(GrowthAccountTypeEnum.class, code) != null;
    }

    /**
     * Check whether the enumeration code is valid
     */
    public static boolean exist(int code){

        return EnumUtil.code2Enum(GrowthAccountTypeEnum.class, String.valueOf(code)) != null;
    }

}
