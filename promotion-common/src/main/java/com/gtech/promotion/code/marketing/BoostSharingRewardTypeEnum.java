package com.gtech.promotion.code.marketing;

public enum BoostSharingRewardTypeEnum  {
    COUPON("01","优惠券"),
    RIGHT_OF_FIRST_REFUSAL("02","优先购买权"),
    RAFFLE_OPPORTUNITIES("03","抽奖机会"),
    ;

    private String code;
    private String desc;
    BoostSharingRewardTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }
    public String code() {

        return code;
    }

    public String desc() {

        return desc;
    }


}
