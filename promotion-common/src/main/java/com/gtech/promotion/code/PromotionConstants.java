/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * <PERSON>ECH MAKES NO REPRESENTATIONS OR <PERSON><PERSON><PERSON>NTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.code;

/**
 * PromotionConstants
 *
 * <AUTHOR>
 */
public class PromotionConstants {

    private PromotionConstants() {
        // Empty
    }

    public static final String APP_KEY = "PRO";

    public static final int MAX_PAGE_SIZE_1000 = 1000;
    public static final int MAX_PAGE_SIZE_100 = 100;
    public static final int DEFAULT_PAGE_SIZE_10 = 10;

    public static final int LIMITE_1000 = 1000;
    public static final int LIMITE_100 = 100;

    public static final String DEFAULT_ORG_CODE = "default";

    public static final int DEF_PRIORITY = 999;
    
    public static final String UNLIMITED = "0000";
    public static final String UNLIMITED_DESC = "UNLIMITED";
    public static final String ATTERIBUTE_TYPE = "01";

}
