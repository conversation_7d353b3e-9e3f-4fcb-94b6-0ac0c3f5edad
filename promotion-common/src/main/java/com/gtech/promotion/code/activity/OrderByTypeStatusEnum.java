/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.code.activity;

import com.gtech.commons.code.IEnum;

/**   
 * 排序类型
 */
public enum OrderByTypeStatusEnum implements IEnum {
    
  CREATE_TIME_DESC("01","根据创建时间降序"),
  CREATE_TIME_ASC("02","根据创建时间升序"),
  ACTIVITY_BINGIN_DESC("03","根据活动开始时间降序"), 
  ACTIVITY_BINGIN_ASC("04","根据活动开始时间升序"), 
  ACTIVITY_END_DESC("05","根据活动结束时间降序"),
  ACTIVITY_END_ASC("06","根据活动结束时间升序");

    OrderByTypeStatusEnum(String code, String desc){
        this.code = code;
        this.desc = desc;
    }

    private final String code;

    private final String desc;

    public String code(){
        return code;
    }

    public String desc(){
        return desc;
    }

    public boolean equalsCode(String code) {

        return this.code().equals(code);
    }

    /**
     * 判断是否是合法的类型
     * 
     * @param code
     *            类型code
     * @return true：合法
     */
    public static boolean exist(String code){
        OrderByTypeStatusEnum[] values = OrderByTypeStatusEnum.values();
        for (OrderByTypeStatusEnum type : values){
            if (type.code().equals(code)){
                return true;
            }
        }
        return false;
    }

}
