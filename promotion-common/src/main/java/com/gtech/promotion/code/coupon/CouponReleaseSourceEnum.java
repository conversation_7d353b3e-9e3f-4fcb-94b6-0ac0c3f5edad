package com.gtech.promotion.code.coupon;

public enum CouponReleaseSourceEnum {

    // 01-系统生成 02-外部导入
    SYSTEM_CREATE("01","系统生成"), EXPORT("02","外部导入");

    CouponReleaseSourceEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    private String code;

    private String desc;

    public String code() {

        return code;
    }

    public String desc() {

        return desc;
    }

    public boolean equalsCode(String code) {

        return this.code().equals(code);
    }
}
