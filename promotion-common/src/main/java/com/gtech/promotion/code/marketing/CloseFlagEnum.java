package com.gtech.promotion.code.marketing;

import com.gtech.commons.code.IEnum;
import com.gtech.commons.utils.EnumUtil;

public enum CloseFlagEnum implements IEnum{

    CLOSE_FLAG_NO("0","封闭团关闭"),
    CLOSE_FLAG_YES("1","封闭团开启");

    CloseFlagEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    private String code;

    private String desc;

    @Override
    public String code() {

        return code;
    }

    @Override
    public String desc() {

        return desc;
    }

    @Override
    public boolean equalsCode(String code) {

        return this.code().equals(code);
    }

    /**
     * Check whether the enumeration code is valid
     */
    public static boolean exist(String code){

        return EnumUtil.code2Enum(CloseFlagEnum.class, code) != null;
    }

}
