package com.gtech.promotion.code.point;

import com.gtech.commons.code.IEnum;
import com.gtech.commons.utils.EnumUtil;

/**
 * <AUTHOR>
 * @Date 2020/12/28 16:57
 */
public enum PointStatusTypeEnum implements IEnum {
    INACTIVE(0,"inactive"),
    ACTIVE(1,"active");

    PointStatusTypeEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    private int code;

    private String desc;

    public int number() {

        return this.code;
    }

    public boolean equalsCode(int code) {

        return this.code == code;
    }

    @Override
    public String code() {

        return String.valueOf(code);
    }

    @Override
    public String desc() {

        return desc;
    }

    @Override
    public boolean equalsCode(String code) {

        return this.code().equals(code);
    }

    /**
     * Check whether the enumeration code is valid
     */
    public static boolean exist(String code){

        return EnumUtil.code2Enum(PointAccountTypeEnum.class, code) != null;
    }

    /**
     * Check whether the enumeration code is valid
     */
    public static boolean exist(int code){

        return EnumUtil.code2Enum(PointAccountTypeEnum.class, String.valueOf(code)) != null;
    }
}
