/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.code.activity;

/**
 * 活动店铺类型
 */
public enum OperationTypeEnum {

    CREATION("01","创建活动"),
    EDITION("02","编辑活动"),
    ACTIVATION("03","激活活动"),
    TERMINATION("04","终止活动"),
    COMPLETION("05","完成活动"),
    EXTENSION("06","延期"),
    EDITION_PRODUCT("07","修改商品"),
    APPROVAL("08","审核"),
    PRIORITY("09","修改优先级"),
    ;

    OperationTypeEnum(String code, String desc){
        this.code = code;
        this.desc = desc;
    }

    private String code;

    private String desc;

    /**
     * 判断是否是合法的活动店铺类型
     * 
     * @param code
     *            类型code
     * @return true：合法
     */
    public static boolean exist(String code){
        OperationTypeEnum[] values = OperationTypeEnum.values();
        for (OperationTypeEnum type : values){
            if (type.code().equals(code)){
                return true;
            }
        }
        return false;
    }

    public String code(){
        return code;
    }

    public String desc(){
        return desc;
    }

    public boolean equalsCode(String code) {

        return this.code().equals(code);
    }

}
