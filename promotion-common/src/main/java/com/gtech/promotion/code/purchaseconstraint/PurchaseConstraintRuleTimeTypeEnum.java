package com.gtech.promotion.code.purchaseconstraint;

import java.time.LocalDate;
import java.time.temporal.WeekFields;
import java.util.Arrays;
import java.util.Locale;
import java.util.Map;

import com.google.common.collect.Maps;

import lombok.Getter;

/**
 * 限购规则条件设置时间类型
 * 1: YEARLY, 2:MONTHLY, 3:WEEKLY
 */
@Getter
public enum PurchaseConstraintRuleTimeTypeEnum {

    YEARLY(1, "周期年"),
    MONTHLY(2, "周期月"),
    WEEKLY(3, "周期周"),
    ;

    PurchaseConstraintRuleTimeTypeEnum(Integer code, String desc){
        this.code = code;
        this.desc = desc;
    }

    private final Integer code;

    private final String desc;
    private static final Map<Integer, PurchaseConstraintRuleTimeTypeEnum> PURCHASE_CONSTRAINT_RULE_TIME_TYPE_MAP = Maps.newHashMap();

    static {
        Arrays.stream(values()).forEach(val -> PURCHASE_CONSTRAINT_RULE_TIME_TYPE_MAP.put(val.getCode(), val));
    }

    public static Map<Integer, PurchaseConstraintRuleTimeTypeEnum> getMap() {
        return PURCHASE_CONSTRAINT_RULE_TIME_TYPE_MAP;
    }
    public static PurchaseConstraintRuleTimeTypeEnum getByCode(Integer code) {
        return PURCHASE_CONSTRAINT_RULE_TIME_TYPE_MAP.get(code);
    }
    public static boolean exist(Integer code){
        PurchaseConstraintRuleTimeTypeEnum[] values = PurchaseConstraintRuleTimeTypeEnum.values();
        for (PurchaseConstraintRuleTimeTypeEnum type : values){
            if (type.getCode().equals(code)){
                return true;
            }
        }
        return false;
    }

	public static String getErrorMessageVal(Integer code) {
        PurchaseConstraintRuleTimeTypeEnum timeTypeEnum = getByCode(code);
        if(null == timeTypeEnum){
			return "";
        }
		LocalDate date = LocalDate.now(); // 获取当前日期
        switch (timeTypeEnum){
		case YEARLY:
			return date.getYear() + "";
		case MONTHLY:
			return date.getMonthValue() + "";
		case WEEKLY:
			return date.get(WeekFields.of(Locale.getDefault()).weekOfWeekBasedYear()) + "";
		default:
			return "";
        }
    }
}
