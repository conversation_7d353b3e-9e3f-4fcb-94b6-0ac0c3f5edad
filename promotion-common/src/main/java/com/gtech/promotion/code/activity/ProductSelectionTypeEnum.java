
package com.gtech.promotion.code.activity;

import com.gtech.commons.code.IEnum;

/**
 * 商品选择类型  01:正选 02:反选
 */
public enum ProductSelectionTypeEnum implements IEnum {

    INCLUDE("01", "正选"),
    EXCLUDE("02", "反选")
    ;

    ProductSelectionTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    private final String code;

    private final String desc;

    /**
     * 判断是否是合法的活动状态
     */
    public static boolean exist(String code) {

        ProductSelectionTypeEnum[] values = ProductSelectionTypeEnum.values();
        for (ProductSelectionTypeEnum type : values) {
            if (type.code().equals(code)) {
                return true;
            }
        }
        return false;
    }
@Override
    public String code() {

        return code;
    }
    @Override
    public String desc() {

        return desc;
    }
    @Override
    public boolean equalsCode(String code) {

        return this.code().equals(code);
    }

}
