/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.code.activity;

import com.gtech.commons.code.IEnum;

/**
 * 活动作用商品范围类型
 */
public enum ItemScopeTypeEnum implements IEnum{

    ALL_SCOPE(1,"All scope"),
    BY_SPU(2,"By spu in scope"),
    ;

    ItemScopeTypeEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    private final int code;

    private final String desc;

    /**
     * 判断是否是合法的活动状态
     */
    public static boolean exist(String code) {

        ItemScopeTypeEnum[] values = ItemScopeTypeEnum.values();
        for (ItemScopeTypeEnum type : values) {
            if (type.code().equals(code)) {
                return true;
            }
        }
        return false;
    }

    public boolean equalsCode(int code) {

        return this.code == code;
    }

    public int number() {

        return this.code;
    }

    @Override
    public String code() {

        return String.valueOf(code);
    }

    @Override
    public String desc() {

        return desc;
    }

    @Override
    public boolean equalsCode(String code) {

        return this.code().equals(code);
    }

}
