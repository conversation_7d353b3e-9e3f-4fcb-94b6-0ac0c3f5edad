package com.gtech.promotion.code.marketing;

import com.gtech.commons.code.IEnum;
import com.gtech.commons.utils.EnumUtil;

public enum BoostSharingRewardEnum implements IEnum {
    COUPON_FOR_HELPERS_ONLY("01","助力人优惠券"),
    SHARE_PEOPLE_AND_HELP_PEOPLE_COUPONS("02","分享人优惠券"),
    SHARER_RIGHT_OF_FIRST_REFUSAL("03","分享人优先购买权"),
    SHARER_LUCKY_DRAW_BONUS("04","分享人幸运抽奖"),

            ;

    BoostSharingRewardEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    private String code;

    private String desc;

    @Override
    public String code() {

        return code;
    }

    @Override
    public String desc() {

        return desc;
    }

    @Override
    public boolean equalsCode(String code) {

        return this.code().equals(code);
    }

    public static BoostSharingRewardEnum defaultReward(BoostSharingTypeEnum type){

        if (type.equals(BoostSharingTypeEnum.COUPON)) {
            return SHARE_PEOPLE_AND_HELP_PEOPLE_COUPONS;
        }else if(type.equals(BoostSharingTypeEnum.RIGHT_FIRST)){
            return SHARER_RIGHT_OF_FIRST_REFUSAL;
        }


        return COUPON_FOR_HELPERS_ONLY;
    }

    /**
     * Check whether the enumeration code is valid
     */
    public static boolean exist(String code){

        return EnumUtil.code2Enum(BoostSharingRewardEnum.class, code) != null;
    }
}
