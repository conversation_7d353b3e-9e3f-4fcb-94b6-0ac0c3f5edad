package com.gtech.promotion.code.marketing;

import com.gtech.commons.code.IEnum;
import com.gtech.commons.utils.EnumUtil;
import com.gtech.promotion.exception.ErrorCodes;
import com.gtech.promotion.exception.PromotionException;

public enum BoostSharingTypeEnum implements IEnum {
    COUPON("0","优惠券"),
    RIGHT_FIRST("1","优先购买权"),

            ;

    BoostSharingTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    private String code;

    private String desc;

    @Override
    public String code() {

        return code;
    }

    @Override
    public String desc() {

        return desc;
    }

    @Override
    public boolean equalsCode(String code) {

        return this.code().equals(code);
    }

    /**
     * Check whether the enumeration code is valid
     */
    public static boolean exist(String code){

        return EnumUtil.code2Enum(BoostSharingTypeEnum.class, code) != null;
    }


    public static BoostSharingTypeEnum getEnumByCode(String code){

        BoostSharingTypeEnum[] values = BoostSharingTypeEnum.values();
        for (BoostSharingTypeEnum value : values) {
            if (value.code().equals(code)) {
                return value;
            }
        }
        throw new PromotionException(ErrorCodes.ERROR_BOOST_SHARDING_TYPE.getCode(),ErrorCodes.ERROR_BOOST_SHARDING_TYPE.getMessage());
    }

}
