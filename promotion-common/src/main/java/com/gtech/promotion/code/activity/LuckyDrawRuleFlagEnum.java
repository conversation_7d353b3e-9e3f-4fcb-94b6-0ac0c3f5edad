package com.gtech.promotion.code.activity;
import com.gtech.commons.code.IEnum;
/**
 * 奖励规则限制
 * <AUTHOR>
 * @Date 2021/1/27 14:34
 */
public enum LuckyDrawRuleFlagEnum implements IEnum {

    NO("00","无限制"),
    YES("01","有限制")
    ;

    LuckyDrawRuleFlagEnum(String code, String desc){
        this.code = code;
        this.desc = desc;
    }

    private final String code;
    private final String desc;

    public String code(){
        return code;
    }

    public String desc(){
        return desc;
    }

    public boolean equalsCode(String code) {

        return this.code().equals(code);
    }

    /**
     * 判断是否是合法的类型
     *
     * @param code
     *            类型code
     * @return true：合法
     */
    public static boolean exist(String code){
        IncentiveLimitedFlagEnum[] values = IncentiveLimitedFlagEnum.values();
        for (IncentiveLimitedFlagEnum type : values){
            if (type.code().equals(code)){
                return true;
            }
        }
        return false;
    }

}
