/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.page;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;


/**   
 * RequestPage
 */
@ApiModel("RequestPage")
public class RequestPage implements Serializable {
    
    private static final long serialVersionUID = -516638748653675613L;
    
    private static final int ZERO = 0;
    //默认页码
    private static final int DEFAULT_PAGE_NO = 1;
    //默认页面记录数
    private static final int DEFAULT_PAGE_COUNT = 10;
    //默认最大页面记录数
    private static final int DEFAULT_MAX_PAGE_COUNT = 100;

    @ApiModelProperty(value="页码", example="1", required=false)
    private Integer pageNo;
    
    @ApiModelProperty(value="每页显示条数", example="10", required=false)
    private Integer pageCount;
    
    public RequestPage() {
        this.pageNo = DEFAULT_PAGE_NO;
        this.pageCount = DEFAULT_PAGE_COUNT;
    }
    public RequestPage(int pageNo, int pageCount) {
        this.pageNo = pageNo;
        if (pageCount <= 0){
            pageCount = DEFAULT_PAGE_COUNT;
        }
        this.pageCount = pageCount;
    }
    
    public Integer getPageNo(){
        return pageNo;
    }
    
    public void setPageNo(Integer pageNo){
        if(null == pageNo || pageNo <= ZERO || pageNo > Integer.MAX_VALUE / pageCount) {
            pageNo = DEFAULT_PAGE_NO;
        }
        this.pageNo = pageNo;
    }
        
    public Integer getPageCount(){
        return pageCount;
    }
    
    public void setPageCount(Integer pageCount){
        if(null == pageCount || pageCount <= ZERO ) {
            pageCount = DEFAULT_PAGE_COUNT;
        }
        if (pageCount > DEFAULT_MAX_PAGE_COUNT){
            pageCount = DEFAULT_MAX_PAGE_COUNT;
        }
        this.pageCount = pageCount;
    }

    @Override
    public String toString(){
        return "RequestPage [pageNo=" + pageNo + ", pageCount=" + pageCount + "]";
    }
    
}
