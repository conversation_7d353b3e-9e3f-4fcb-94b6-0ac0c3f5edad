INSERT  INTO `promo_template`(`ID`,`TEMPLATE_CODE`,`TEMPLATE_NAME`,`TEMPLATE_DESC`,`TAG_CODE`,`LOCALE`,`UPDATE_USER`,`UPDATE_TIME`,`CREATE_USER`,`CREATE_TIME`,`LOGIC_DELETE`) VALUES
(607,'0101020703020406','单品满数量送优惠券','单品满数量送优惠券','06','zh-CN',NULL,CURRENT_TIMESTAMP(),NULL,CURRENT_TIMESTAMP(),0);

INSERT  INTO `promo_template_function`(`TEMPLATE_ID`,TEMPLATE_CODE,`FUNCTION_TYPE`,`FUNCTION_CODE`,`FUNCTION_NAME`,`UPDATE_USER`,`UPDATE_TIME`,`CREATE_USER`,`CREATE_TIME`,`LOGIC_DELETE`) VALUES
(607,'0101020703020406','01','0101','单品',NULL,CURRENT_TIMESTAMP(),NULL,CURRENT_TIMESTAMP(),0),
(607,'0101020703020406','02','0207','单品满',NULL,CURRENT_TIMESTAMP(),NULL,CURRENT_TIMESTAMP(),0),
(607,'0101020703020406','03','0302','数量',NULL,CURRENT_TIMESTAMP(),NULL,CURRENT_TIMESTAMP(),0),
(607,'0101020703020406','04','0406','送赠品',NULL,CURRENT_TIMESTAMP(),NULL,CURRENT_TIMESTAMP(),0);

INSERT  INTO `promo_template`(`ID`,`TEMPLATE_CODE`,`TEMPLATE_NAME`,`TEMPLATE_DESC`,`TAG_CODE`,`LOCALE`,`UPDATE_USER`,`UPDATE_TIME`,`CREATE_USER`,`CREATE_TIME`,`LOGIC_DELETE`) VALUES
(608,'0101020703030406','单品满金额送优惠券','单品满金额送优惠券','06','zh-CN',NULL,CURRENT_TIMESTAMP(),NULL,CURRENT_TIMESTAMP(),0);

INSERT  INTO `promo_template_function`(`TEMPLATE_ID`,TEMPLATE_CODE,`FUNCTION_TYPE`,`FUNCTION_CODE`,`FUNCTION_NAME`,`UPDATE_USER`,`UPDATE_TIME`,`CREATE_USER`,`CREATE_TIME`,`LOGIC_DELETE`) VALUES
(608,'0101020703030406','01','0101','单品',NULL,CURRENT_TIMESTAMP(),NULL,CURRENT_TIMESTAMP(),0),
(608,'0101020703030406','02','0207','单品满',NULL,CURRENT_TIMESTAMP(),NULL,CURRENT_TIMESTAMP(),0),
(608,'0101020703030406','03','0303','金额',NULL,CURRENT_TIMESTAMP(),NULL,CURRENT_TIMESTAMP(),0),
(608,'0101020703030406','04','0406','送赠品',NULL,CURRENT_TIMESTAMP(),NULL,CURRENT_TIMESTAMP(),0);

ALTER TABLE promo_incentive_limited
  CHANGE `LIMITATION_CODE` `LIMITATION_CODE` CHAR(2) CHARSET utf8 COLLATE utf8_general_ci NOT NULL   COMMENT '限制条件编码：01-活动限制总次数 02-活动限制总金额 03-活动限制单日总次数 04-活动限制单日总金额 05-每人限制总次数 06-每人限制总金额；07-每人限制每天总次数；08-每人限制每天总金额; 09-活动限制单日总订单数 21-活动限制每单优惠金额';

ALTER TABLE `promo_coupon_activity`
    ADD COLUMN `RESERVE_INVENTORY` BIGINT(20) DEFAULT 0  NOT NULL   COMMENT '预留库存（用于送券活动）' AFTER `USER_LIMIT_MAX`;

CREATE TABLE `promo_giveaway` (
  `ID` BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `TENANT_CODE` VARCHAR(32) NOT NULL COMMENT '租户编码',
  `ACTIVITY_CODE` VARCHAR(40) NOT NULL COMMENT 'ACTIVITY CODE',
  `GIVEAWAY_CODE` VARCHAR(40) NOT NULL COMMENT 'SKU编码',
  `GIVEAWAY_NAME` VARCHAR(100) NOT NULL COMMENT 'SKU名称',
  `GIVEAWAY_NUM` INT(11) NOT NULL COMMENT 'SKU数量',
  `GIVEAWAY_TYPE` TINYINT(1) NOT NULL DEFAULT '1' COMMENT '赠品类型1:商品2:券',
  `UPDATE_USER` VARCHAR(32) DEFAULT NULL COMMENT '更新者',
  `UPDATE_TIME` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `CREATE_USER` VARCHAR(32) DEFAULT NULL COMMENT '创建者',
  `CREATE_TIME` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `LOGIC_DELETE` TINYINT(1) NOT NULL DEFAULT '0' COMMENT '逻辑删除：0-未删除 1-已删除',
  PRIMARY KEY (`ID`),
  UNIQUE KEY `uidx_giveaway_activity` (`ACTIVITY_CODE`,`GIVEAWAY_CODE`)
) ENGINE=INNODB DEFAULT CHARSET=utf8 COMMENT='促销活动赠品表';



CREATE TABLE `promo_coupon_reserve` (
  `ID` BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `TENANT_CODE` VARCHAR(32) NOT NULL COMMENT '租户编码',
  `ACTIVITY_CODE` VARCHAR(40) NOT NULL COMMENT 'ACTIVITY CODE',
  `QUOTA` INT(11) NOT NULL COMMENT '预留额度',
  `ORDER_NO` VARCHAR(32) NOT NULL COMMENT '订单编号',
  `MEMBER_CODE` VARCHAR(32) NOT NULL COMMENT 'member code',
  `STATUS` TINYINT(1) NOT NULL DEFAULT '1' COMMENT '状态:锁定库存 2:解锁库存 3：已发券',
  `UPDATE_USER` VARCHAR(32) DEFAULT NULL COMMENT '更新者',
  `UPDATE_TIME` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `CREATE_USER` VARCHAR(32) DEFAULT NULL COMMENT '创建者',
  `CREATE_TIME` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `LOGIC_DELETE` TINYINT(1) NOT NULL DEFAULT '0' COMMENT '逻辑删除：0-未删除 1-已删除',
  PRIMARY KEY (`ID`),
  UNIQUE KEY `uidx_reserve_tenant_order_activity` (`TENANT_CODE`, `ORDER_NO`, `ACTIVITY_CODE`)
) ENGINE=INNODB DEFAULT CHARSET=utf8 COMMENT='促销券码预留额度表';

UPDATE promo_activity_expression SET EXPRESSION = '[2_0104020103010401,2_0104020103010402] and [1_0101020103010401,1_0101020603020401,1_0101020103010402,1_0101020603020402,1_0101020103010403,1_0101020103010411]  and [1_0102020203020401,1_0102020203030401,1_0102020303020401,1_0102020303030401,1_0102020203020402,1_0102020203030402,1_0102020403020402,1_0102020203020414] and [1_0103020203020401,1_0103020203030401,1_0103020303020401,1_0103020303030401,1_0103020203020402,1_0103020203030402] and [1_0102020203020412,1_0102020203030412,1_0102020203020413,1_0102020203030413] and [1_0102020503020401,1_0102020503020404,1_0102020203020404,1_0102020303020404] and [1_0101020703020406,1_0101020703030406,1_0102020203020406,1_0102020203030406,1_0102020303020406,1_0102020303030406,1_0103020203020406,1_0103020203030406] and [1_0102020203020405,1_0102020203030405,1_0103020203020405,1_0103020203030405] and [1_0102020303020407,1_0102020303020408] and [2_0102020203020401,2_0102020203030401,2_0102020303020401,2_0102020303030401,2_0102020203020402,2_0102020203030402,2_0102020403020402,2_0102020203020414] and [2_0103020203020401,2_0103020203030401,2_0103020303020401,2_0103020303030401,2_0103020203020402,2_0103020203030402] and [2_0102020203020412,2_0102020203030412,2_0102020203020413,2_0102020203030413] and [1_0102020203020405,1_0102020203030405,1_0103020203020405,1_0103020203030405]'