
drop table `promo_activity_gift`;
drop table `promo_activity_gift_coupon`;


ALTER TABLE `promo_activity` ADD COLUMN `OPS_TYPE` CHAR(3) NULL comment 'OPS活动类型，对应ops中的创建活动，1对1，促销101开始，券201开始';
ALTER TABLE `promo_activity` ADD COLUMN `COOL_DOWN` CHAR(14) NULL COMMENT '预冷时间yyyyMMddhhmmss';

ALTER TABLE `promo_activity_product_detail`
    ADD COLUMN `ORG_CODE` VARCHAR(40) NULL   COMMENT 'org编码' AFTER `SKU_NAME`,
    ADD COLUMN `ORG_NAME` VARCHAR(100) NULL   COMMENT 'org名称' AFTER `ORG_CODE`;


INSERT  INTO `promo_template`(`ID`,`TEMPLATE_CODE`,`TEMPLATE_NAME`,`TEMPLATE_DESC`,`TAG_CODE`,`LOCALE`,`UPDATE_USER`,`UPDATE_TIME`,`CREATE_USER`,`CREATE_TIME`,`LOGIC_DELETE`) VALUES
(1203,'0102020603020402','商品范围第数量打折扣','多个商品第几件，打折扣','12','zh-CN',NULL,CURRENT_TIMESTAMP(),NULL,CURRENT_TIMESTAMP(),0);

INSERT  INTO `promo_template_function`(`TEMPLATE_ID`,TEMPLATE_CODE,`FUNCTION_TYPE`,`FUNCTION_CODE`,`FUNCTION_NAME`,`UPDATE_USER`,`UPDATE_TIME`,`CREATE_USER`,`CREATE_TIME`,`LOGIC_DELETE`) VALUES
(1203,'0102020603020402','01','0102','商品范围',NULL,CURRENT_TIMESTAMP(),NULL,CURRENT_TIMESTAMP(),0),
(1203,'0102020603020402','02','0206','第',NULL,CURRENT_TIMESTAMP(),NULL,CURRENT_TIMESTAMP(),0),
(1203,'0102020603020402','03','0302','数量',NULL,CURRENT_TIMESTAMP(),NULL,CURRENT_TIMESTAMP(),0),
(1203,'0102020603020402','04','0402','打折扣',NULL,CURRENT_TIMESTAMP(),NULL,CURRENT_TIMESTAMP(),0);

INSERT  INTO `promo_template`(`ID`,`TEMPLATE_CODE`,`TEMPLATE_NAME`,`TEMPLATE_DESC`,`TAG_CODE`,`LOCALE`,`UPDATE_USER`,`UPDATE_TIME`,`CREATE_USER`,`CREATE_TIME`,`LOGIC_DELETE`) VALUES
(1204,'0102020603020401','商品范围第数量减金额','多个商品第几件，减金额','12','zh-CN',NULL,CURRENT_TIMESTAMP(),NULL,CURRENT_TIMESTAMP(),0);

INSERT  INTO `promo_template_function`(`TEMPLATE_ID`,TEMPLATE_CODE,`FUNCTION_TYPE`,`FUNCTION_CODE`,`FUNCTION_NAME`,`UPDATE_USER`,`UPDATE_TIME`,`CREATE_USER`,`CREATE_TIME`,`LOGIC_DELETE`) VALUES
(1204,'0102020603020401','01','0102','商品范围',NULL,CURRENT_TIMESTAMP(),NULL,CURRENT_TIMESTAMP(),0),
(1204,'0102020603020401','02','0206','第',NULL,CURRENT_TIMESTAMP(),NULL,CURRENT_TIMESTAMP(),0),
(1204,'0102020603020401','03','0302','数量',NULL,CURRENT_TIMESTAMP(),NULL,CURRENT_TIMESTAMP(),0),
(1204,'0102020603020401','04','0401','减金额',NULL,CURRENT_TIMESTAMP(),NULL,CURRENT_TIMESTAMP(),0);

-- 以下UPDATE语句，上线前执行， 更新历史数据， 上线后需要再执行一遍，以更新增量数据
UPDATE promo_activity SET OPS_TYPE = '101' WHERE ACTIVITY_TYPE = '01' AND TEMPLATE_CODE IN ('0101020103010401','0101020103010402','0101020103010403','0101020103010411') AND OPS_TYPE IS NULL;
UPDATE promo_activity SET OPS_TYPE = '102' WHERE ACTIVITY_TYPE = '01' AND TEMPLATE_CODE IN ('0102020203020412','0102020203030412','0102020203020413','0102020203030413') AND OPS_TYPE IS NULL;
UPDATE promo_activity SET OPS_TYPE = '103' WHERE ACTIVITY_TYPE = '01' AND TEMPLATE_CODE IN ('0102020203020401','0102020203030401','0102020303020401','0102020303030401','0103020203020401','0103020203030401','0103020303020401','0103020303030401','0102020203020414','0102020203020402','0102020203030402') AND OPS_TYPE IS NULL;
UPDATE promo_activity SET OPS_TYPE = '104' WHERE ACTIVITY_TYPE = '01' AND TEMPLATE_CODE IN ('0102020203020404','0102020303020404') AND OPS_TYPE IS NULL;
UPDATE promo_activity SET OPS_TYPE = '105' WHERE ACTIVITY_TYPE = '01' AND TEMPLATE_CODE IN ('0101020603020402','0101020603020401') AND OPS_TYPE IS NULL;
UPDATE promo_activity SET OPS_TYPE = '106' WHERE ACTIVITY_TYPE = '01' AND TEMPLATE_CODE IN ('0103020203020406','0103020203030406','0102020203020406','0102020203030406','0102020303020406','0102020303030406','0101020703020406','0101020703030406') AND OPS_TYPE IS NULL;


UPDATE promo_activity SET OPS_TYPE = '203' WHERE ACTIVITY_TYPE = '02' AND activity_code IN (SELECT activity_code FROM promo_coupon_activity WHERE coupon_type = '03') AND OPS_TYPE IS NULL;
UPDATE promo_activity SET OPS_TYPE = '201' WHERE ACTIVITY_TYPE = '02' AND TEMPLATE_CODE IN ('0104020103010401','0104020103010402') AND OPS_TYPE IS NULL;
UPDATE promo_activity SET OPS_TYPE = '202' WHERE ACTIVITY_TYPE = '02' AND TEMPLATE_CODE IN ('0102020203020401','0102020203030401','0102020303020401','0102020303030401','0103020203020401','0103020203030401','0103020303020401','0103020303030401','0102020203020414','0102020203020402','0102020203030402','0102020403020402','0103020203020402','0103020203030402') AND OPS_TYPE IS NULL;
UPDATE promo_activity SET OPS_TYPE = '204' WHERE ACTIVITY_TYPE = '02' AND TEMPLATE_CODE IN ('0102020203020412','0102020203030412','0102020203020413','0102020203030413') AND OPS_TYPE IS NULL;

-- masterdata
INSERT INTO `t_masterdata_dd` (`DD_CODE`, `DD_VALUE`, `DD_TEXT`, `DD_TYPE`, `DD_ORDER`, `DD_REMARK`, `TENANT_CODE`, `STATE`, `UPDATE_TIME`, `UPDATE_USER`, `CREATE_TIME`, `CREATE_USER`) VALUES ('PROMOTION_SPONSOR', 'Gulf', 'Gulf', NULL, '0', NULL, '100001', '0', CURRENT_TIMESTAMP(), NULL, CURRENT_TIMESTAMP(), NULL);
INSERT INTO `t_masterdata_dd` (`DD_CODE`, `DD_VALUE`, `DD_TEXT`, `DD_TYPE`, `DD_ORDER`, `DD_REMARK`, `TENANT_CODE`, `STATE`, `UPDATE_TIME`, `UPDATE_USER`, `CREATE_TIME`, `CREATE_USER`) VALUES ('PROMOTION_SPONSOR', 'PT GT', 'PT GT', NULL, '0', NULL, '100001', '0', CURRENT_TIMESTAMP(), NULL, CURRENT_TIMESTAMP(), NULL);
INSERT INTO `t_masterdata_dd` (`DD_CODE`, `DD_VALUE`, `DD_TEXT`, `DD_TYPE`, `DD_ORDER`, `DD_REMARK`, `TENANT_CODE`, `STATE`, `UPDATE_TIME`, `UPDATE_USER`, `CREATE_TIME`, `CREATE_USER`) VALUES ('PROMOTION_SPONSOR', 'Speedwork', 'Speedwork', NULL, '0', NULL, '100001', '0', CURRENT_TIMESTAMP(), NULL, CURRENT_TIMESTAMP(), NULL);
INSERT INTO `t_masterdata_dd` (`DD_CODE`, `DD_VALUE`, `DD_TEXT`, `DD_TYPE`, `DD_ORDER`, `DD_REMARK`, `TENANT_CODE`, `STATE`, `UPDATE_TIME`, `UPDATE_USER`, `CREATE_TIME`, `CREATE_USER`) VALUES ('PROMOTION_SPONSOR', 'Petronas', 'Petronas', NULL, '0', NULL, '100001', '0', CURRENT_TIMESTAMP(), NULL, CURRENT_TIMESTAMP(), NULL);
INSERT INTO `t_masterdata_dd` (`DD_CODE`, `DD_VALUE`, `DD_TEXT`, `DD_TYPE`, `DD_ORDER`, `DD_REMARK`, `TENANT_CODE`, `STATE`, `UPDATE_TIME`, `UPDATE_USER`, `CREATE_TIME`, `CREATE_USER`) VALUES ('PROMOTION_SPONSOR', 'Test', 'Test', NULL, '0', NULL, '100001', '0', CURRENT_TIMESTAMP(), NULL, CURRENT_TIMESTAMP(), NULL);
INSERT INTO `t_masterdata_dd` (`DD_CODE`, `DD_VALUE`, `DD_TEXT`, `DD_TYPE`, `DD_ORDER`, `DD_REMARK`, `TENANT_CODE`, `STATE`, `UPDATE_TIME`, `UPDATE_USER`, `CREATE_TIME`, `CREATE_USER`) VALUES ('PROMOTION_SPONSOR', 'Giti Tire Malaysia', 'Giti Tire Malaysia', NULL, '0', NULL, '100002', '0', CURRENT_TIMESTAMP(), NULL, CURRENT_TIMESTAMP(), NULL);


-- idm
INSERT INTO `idm_resource`(`domain_code`, `app_code`, `resource_parent`, `resource_code`, `resource_name`, `resource_type`, `resource_class`, `access_type`, `status`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`, `position_code`) VALUES ( 'SYSTEM_DEFAULT', 'PROMOTION', 'root', 'queryOrganizationTree', 'queryOrganizationTree', 3, 1, 1, 1, NULL, '2020-02-19 20:17:57', NULL, '2020-02-19 20:17:57', 0, NULL);
INSERT INTO `idm_resource_detail`(`domain_code`, `app_code`, `resource_code`, `resource_url`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`) VALUES ( 'SYSTEM_DEFAULT', 'PROMOTION', 'queryOrganizationTree', 'queryOrganizationTree', NULL, '2020-02-19 20:18:53', NULL, '2020-02-19 20:18:53', 0);
