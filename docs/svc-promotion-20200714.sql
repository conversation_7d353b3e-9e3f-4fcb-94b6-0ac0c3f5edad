CREATE TABLE `MARKETING` (
 `ID` BIGINT(20) NOT NULL AUTO_INCREMENT,
 `DOMAIN_CODE` VARCHAR(32) NOT NULL COMMENT '系统域编码，系统拥有者编码',
 `TENANT_CODE` VARCHAR(32) NOT NULL COMMENT '系统租户编码，系统拥有者编码',
 `ORG_CODE` VARCHAR(32) NOT NULL COMMENT '系统ORG编码',
 `ACTIVITY_CODE` VARCHAR(40) NOT NULL COMMENT '活动编码',
 `ACTIVITY_TYPE` CHAR(3) NOT NULL COMMENT '活动类型: 101-大转盘 102-砸金蛋',
 `ACTIVITY_NAME` VARCHAR(256) NOT NULL COMMENT 'Promotion activity name.',
 `ACTIVITY_BEGIN` CHAR(14) NOT NULL COMMENT '开始时间：yyyyMMddhhmmss',
 `ACTIVITY_END` CHAR(14) NOT NULL COMMENT '结束时间：yyyyMMddhhmmss',
 `ACTIVITY_STATUS` CHAR(2) NOT NULL DEFAULT '01' COMMENT '活动状态：01-待提交 02-审核中 03-已拒绝 04-已生效 05-已结束 06-暂停中 07-已终止',
 `ACTIVITY_URL` VARCHAR(255) DEFAULT NULL COMMENT '活动URL',
 `SPONSORS` VARCHAR(100) DEFAULT NULL COMMENT 'Activity sponsors',
 `UPDATE_USER` VARCHAR(32) DEFAULT NULL COMMENT '更新者',
 `UPDATE_TIME` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
 `CREATE_USER` VARCHAR(32) DEFAULT NULL COMMENT '创建者',
 `CREATE_TIME` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
 `LOGIC_DELETE` TINYINT(1) NOT NULL DEFAULT '0' COMMENT '逻辑删除：0-未删除 1-已删除',
 PRIMARY KEY (`ID`),
 UNIQUE KEY `UIDX_MARKETING_ACTIVITY` (`ACTIVITY_CODE`)
) ENGINE=INNODB DEFAULT CHARSET=utf8 COMMENT='营销活动表';

CREATE TABLE `MARKETING_LANGUAGE` (
   `ID` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
   `DOMAIN_CODE` VARCHAR(32) NOT NULL COMMENT '系统域编码，系统拥有者编码',
   `TENANT_CODE` VARCHAR(32) NOT NULL COMMENT '系统租户编码，系统拥有者编码',
   `ORG_CODE` VARCHAR(32) NOT NULL COMMENT '系统ORG编码',
   `ACTIVITY_CODE` varchar(40) NOT NULL COMMENT '活动编码',
   `ACTIVITY_LABEL` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '活动标签',
   `ACTIVITY_NAME` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '活动名称',
   `ACTIVITY_DESC` varchar(1000) DEFAULT NULL COMMENT '活动描述',
   `ACTIVITY_SHORT_DESC` varchar(1000) DEFAULT NULL COMMENT '活动描述2',
   `LANGUAGE` varchar(10) NOT NULL COMMENT '语言类型',
   `UPDATE_USER` VARCHAR(32) DEFAULT NULL COMMENT '更新者',
   `UPDATE_TIME` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
   `CREATE_USER` VARCHAR(32) DEFAULT NULL COMMENT '创建者',
   `CREATE_TIME` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
   `LOGIC_DELETE` TINYINT(1) NOT NULL DEFAULT '0' COMMENT '逻辑删除：0-未删除 1-已删除',
   PRIMARY KEY (`ID`),
   UNIQUE KEY `uidx_marketing_lang` (`ACTIVITY_CODE`,`LANGUAGE`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='营销活动多语言表';

CREATE TABLE `MARKETING_PRIZE` (
  `ID` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
  `DOMAIN_CODE` VARCHAR(32) NOT NULL COMMENT '系统域编码，系统拥有者编码',
  `TENANT_CODE` VARCHAR(32) NOT NULL COMMENT '系统租户编码，系统拥有者编码',
  `ORG_CODE` VARCHAR(32) NOT NULL COMMENT '系统ORG编码',
  `ACTIVITY_CODE` varchar(40) NOT NULL COMMENT '活动编码',
  `PRIZE_NO` varchar(32) NOT NULL COMMENT '奖品编号（系统生成，全局唯一）',
  `PRIZE_CODE` varchar(32) NOT NULL COMMENT '奖品编码',
  `PRIZE_NAME` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '奖品名称',
  `PRIZE_IMAGE` varchar(256) DEFAULT NULL COMMENT '奖品图片',
  `PRIZE_ORDER` INT(11) DEFAULT 1 NOT NULL COMMENT '奖品排序',
  `PRIZE_QUOTA` INT(11) DEFAULT 1 NOT NULL COMMENT '奖品最大中奖次数',
  `PRIZE_TYPE` char(2) DEFAULT '01' NOT NULL COMMENT '奖品类型，01-券',
  `PRIZE_INVENTORY` INT(11) NOT NULL COMMENT '奖品库存（奖品最大中奖次数剩余）',
  `PRIZE_NUM` INT(11) DEFAULT 1 NOT NULL COMMENT '每份奖品包含的数量',
  `PRIZE_PROBABILITY` DECIMAL(5, 2) NOT NULL COMMENT '奖品概率',
  `UPDATE_USER` VARCHAR(32) DEFAULT NULL COMMENT '更新者',
  `UPDATE_TIME` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `CREATE_USER` VARCHAR(32) DEFAULT NULL COMMENT '创建者',
  `CREATE_TIME` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `LOGIC_DELETE` TINYINT(1) NOT NULL DEFAULT '0' COMMENT '逻辑删除：0-未删除 1-已删除',
  PRIMARY KEY (`ID`),
  UNIQUE KEY `uidx_marketing_prize` (`PRIZE_NO`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='营销活动奖品表';

CREATE TABLE `MARKETING_PRIZE_LANGUAGE` (
  `ID` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
  `DOMAIN_CODE` VARCHAR(32) NOT NULL COMMENT '系统域编码，系统拥有者编码',
  `TENANT_CODE` VARCHAR(32) NOT NULL COMMENT '系统租户编码，系统拥有者编码',
  `ORG_CODE` VARCHAR(32) NOT NULL COMMENT '系统ORG编码',
  `ACTIVITY_CODE` varchar(40) NOT NULL COMMENT '活动编码',
  `PRIZE_NO` varchar(32) NOT NULL COMMENT '奖品编号（系统生成，全局唯一）',
  `PRIZE_NAME` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '奖品名称',
  `LANGUAGE` varchar(10) NOT NULL COMMENT '语言类型',
  `UPDATE_USER` VARCHAR(32) DEFAULT NULL COMMENT '更新者',
  `UPDATE_TIME` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `CREATE_USER` VARCHAR(32) DEFAULT NULL COMMENT '创建者',
  `CREATE_TIME` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `LOGIC_DELETE` TINYINT(1) NOT NULL DEFAULT '0' COMMENT '逻辑删除：0-未删除 1-已删除',
  PRIMARY KEY (`ID`),
  UNIQUE KEY `uidx_marketing_prize_lang` (`PRIZE_NO`,`LANGUAGE`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='营销活动奖品多语言表';

CREATE TABLE `MARKETING_PRIZE_RECORD` (
    `ID` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
    `DOMAIN_CODE` VARCHAR(32) NOT NULL COMMENT '系统域编码，系统拥有者编码',
    `TENANT_CODE` VARCHAR(32) NOT NULL COMMENT '系统租户编码，系统拥有者编码',
    `ORG_CODE` VARCHAR(32) NOT NULL COMMENT '系统ORG编码',
    `ACTIVITY_CODE` varchar(40) NOT NULL COMMENT '活动编码',
    `MEMBER_CODE` varchar(32) NOT NULL COMMENT '用户编码',
    `TICKET_CODE` varchar(32) NOT NULL COMMENT '票据编号',
    `PRIZE_NO` varchar(32) NOT NULL COMMENT '奖品编号（系统生成，全局唯一）',
    `STATUS` char(2) NOT NULL COMMENT '领取状态，01-未领取 02-已领取 03-领取失败',
    `UPDATE_USER` VARCHAR(32) DEFAULT NULL COMMENT '更新者',
    `UPDATE_TIME` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `CREATE_USER` VARCHAR(32) DEFAULT NULL COMMENT '创建者',
    `CREATE_TIME` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `LOGIC_DELETE` TINYINT(1) NOT NULL DEFAULT '0' COMMENT '逻辑删除：0-未删除 1-已删除',
    PRIMARY KEY (`ID`),
    UNIQUE KEY `uidx_marketing_prize_record` (`TICKET_CODE`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='营销活动中奖记录表';

CREATE TABLE `MARKETING_TICKET_RELEASE` (
  `ID` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
  `DOMAIN_CODE` VARCHAR(32) NOT NULL COMMENT '系统域编码，系统拥有者编码',
  `TENANT_CODE` VARCHAR(32) NOT NULL COMMENT '系统租户编码，系统拥有者编码',
  `ORG_CODE` VARCHAR(32) NOT NULL COMMENT '系统ORG编码',
  `ACTIVITY_CODE` varchar(40) NOT NULL COMMENT '活动编码',
  `RELEASE_CODE` varchar(32) NOT NULL COMMENT '用户编码',
  `QUALITY` INT(11) NOT NULL COMMENT '总数量',
  `INVENTORY` INT(11) NOT NULL DEFAULT 0 COMMENT '库存数量',
  `USED` INT(11) NOT NULL DEFAULT 0 COMMENT '使用数量',
  `UPDATE_USER` VARCHAR(32) DEFAULT NULL COMMENT '更新者',
  `UPDATE_TIME` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `CREATE_USER` VARCHAR(32) DEFAULT NULL COMMENT '创建者',
  `CREATE_TIME` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `LOGIC_DELETE` TINYINT(1) NOT NULL DEFAULT '0' COMMENT '逻辑删除：0-未删除 1-已删除',
  PRIMARY KEY (`ID`),
  UNIQUE KEY `uidx_marketing_ticket_release` (`RELEASE_CODE`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='营销活动票据投放表';

CREATE TABLE `MARKETING_TICKET` (
    `ID` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
    `DOMAIN_CODE` VARCHAR(32) NOT NULL COMMENT '系统域编码，系统拥有者编码',
    `TENANT_CODE` VARCHAR(32) NOT NULL COMMENT '系统租户编码，系统拥有者编码',
    `ORG_CODE` VARCHAR(32) NOT NULL COMMENT '系统ORG编码',
    `ACTIVITY_CODE` varchar(40) NOT NULL COMMENT '活动编码',
    `RELEASE_CODE` varchar(32) NOT NULL COMMENT '用户编码',
    `TICKET_CODE` varchar(32) NOT NULL COMMENT '票据编号',
    `MEMBER_CODE` varchar(32) NOT NULL COMMENT '会员编号',
    `STATUS` CHAR(2) NOT NULL COMMENT '状态：01-已发放 02-已使用（未中奖）03-已中奖',
    `USE_TIME` varchar(14) DEFAULT NULL COMMENT '使用时间：yyyyMMddhhmmss',
    `LUCKY_TIME` varchar(14) DEFAULT NULL COMMENT '中奖时间：yyyyMMddhhmmss',
    `UPDATE_USER` VARCHAR(32) DEFAULT NULL COMMENT '更新者',
    `UPDATE_TIME` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `CREATE_USER` VARCHAR(32) DEFAULT NULL COMMENT '创建者',
    `CREATE_TIME` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `LOGIC_DELETE` TINYINT(1) NOT NULL DEFAULT '0' COMMENT '逻辑删除：0-未删除 1-已删除',
    PRIMARY KEY (`ID`),
    UNIQUE KEY `uidx_marketing_ticket_member` (`TICKET_CODE`,`MEMBER_CODE`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='营销活动票据表';



CREATE TABLE `promo_qualification` (
 `ID` BIGINT(20) NOT NULL AUTO_INCREMENT,
 `DOMAIN_CODE` VARCHAR(32) NOT NULL COMMENT '系统域编码，系统拥有者编码',
 `TENANT_CODE` VARCHAR(32) NOT NULL COMMENT '系统租户编码，系统拥有者编码',
 `ORG_CODE` VARCHAR(32) NOT NULL COMMENT '系统ORG编码',
 `ACTIVITY_CODE` VARCHAR(40) NOT NULL COMMENT '活动编码',
 `QUALIFICATION_CODE` VARCHAR(32) NOT NULL COMMENT '资格编码',
 `QUALIFICATION_VALUE` VARCHAR(100) NOT NULL COMMENT '资格值',
 `RELATION` CHAR(2) NOT NULL COMMENT '资格关系，01-OR 02-AND',
 `UPDATE_USER` VARCHAR(32) DEFAULT NULL COMMENT '更新者',
 `UPDATE_TIME` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
 `CREATE_USER` VARCHAR(32) DEFAULT NULL COMMENT '创建者',
 `CREATE_TIME` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
 `LOGIC_DELETE` TINYINT(1) NOT NULL DEFAULT '0' COMMENT '逻辑删除：0-未删除 1-已删除',
 PRIMARY KEY (`ID`),
 UNIQUE KEY `UIDX_ACTIVITY_QUALIFICATION` (`ACTIVITY_CODE`, QUALIFICATION_CODE, QUALIFICATION_VALUE)
) ENGINE=INNODB DEFAULT CHARSET=utf8 COMMENT='促销活动参与资格表';

INSERT INTO `idm_resource`(`domain_code`, `app_code`, `resource_parent`, `resource_code`, `resource_name`, `resource_type`, `resource_class`, `access_type`, `status`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`, `position_code`) VALUES ( 'SYSTEM_DEFAULT', 'PROMOTION', 'root', 'getMarketing', 'getMarketing', 3, 1, 1, 1, NULL, '2020-02-19 20:17:57', NULL, '2020-02-19 20:17:57', 0, NULL);
INSERT INTO `idm_resource`(`domain_code`, `app_code`, `resource_parent`, `resource_code`, `resource_name`, `resource_type`, `resource_class`, `access_type`, `status`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`, `position_code`) VALUES ( 'SYSTEM_DEFAULT', 'PROMOTION', 'root', 'queryMarketingList', 'queryMarketingList', 3, 1, 1, 1, NULL, '2020-02-19 20:17:57', NULL, '2020-02-19 20:17:57', 0, NULL);
INSERT INTO `idm_resource`(`domain_code`, `app_code`, `resource_parent`, `resource_code`, `resource_name`, `resource_type`, `resource_class`, `access_type`, `status`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`, `position_code`) VALUES ( 'SYSTEM_DEFAULT', 'PROMOTION', 'root', 'createMarketing', 'createMarketing', 3, 1, 1, 1, NULL, '2020-02-19 20:17:57', NULL, '2020-02-19 20:17:57', 0, NULL);
INSERT INTO `idm_resource`(`domain_code`, `app_code`, `resource_parent`, `resource_code`, `resource_name`, `resource_type`, `resource_class`, `access_type`, `status`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`, `position_code`) VALUES ( 'SYSTEM_DEFAULT', 'PROMOTION', 'root', 'updateMarketing', 'updateMarketing', 3, 1, 1, 1, NULL, '2020-02-19 20:17:57', NULL, '2020-02-19 20:17:57', 0, NULL);
INSERT INTO `idm_resource`(`domain_code`, `app_code`, `resource_parent`, `resource_code`, `resource_name`, `resource_type`, `resource_class`, `access_type`, `status`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`, `position_code`) VALUES ( 'SYSTEM_DEFAULT', 'PROMOTION', 'root', 'updateMarketingStatus', 'updateMarketingStatus', 3, 1, 1, 1, NULL, '2020-02-19 20:17:57', NULL, '2020-02-19 20:17:57', 0, NULL);
INSERT INTO `idm_resource`(`domain_code`, `app_code`, `resource_parent`, `resource_code`, `resource_name`, `resource_type`, `resource_class`, `access_type`, `status`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`, `position_code`) VALUES ( 'SYSTEM_DEFAULT', 'PROMOTION', 'root', 'marketingExtend', 'marketingExtend', 3, 1, 1, 1, NULL, '2020-02-19 20:17:57', NULL, '2020-02-19 20:17:57', 0, NULL);
INSERT INTO `idm_resource_detail`(`domain_code`, `app_code`, `resource_code`, `resource_url`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`) VALUES ( 'SYSTEM_DEFAULT', 'PROMOTION', 'getMarketing', 'getMarketing', NULL, '2020-02-19 20:18:53', NULL, '2020-02-19 20:18:53', 0);
INSERT INTO `idm_resource_detail`(`domain_code`, `app_code`, `resource_code`, `resource_url`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`) VALUES ( 'SYSTEM_DEFAULT', 'PROMOTION', 'queryMarketingList', 'queryMarketingList', NULL, '2020-02-19 20:18:53', NULL, '2020-02-19 20:18:53', 0);
INSERT INTO `idm_resource_detail`(`domain_code`, `app_code`, `resource_code`, `resource_url`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`) VALUES ( 'SYSTEM_DEFAULT', 'PROMOTION', 'createMarketing', 'createMarketing', NULL, '2020-02-19 20:18:53', NULL, '2020-02-19 20:18:53', 0);
INSERT INTO `idm_resource_detail`(`domain_code`, `app_code`, `resource_code`, `resource_url`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`) VALUES ( 'SYSTEM_DEFAULT', 'PROMOTION', 'updateMarketing', 'updateMarketing', NULL, '2020-02-19 20:18:53', NULL, '2020-02-19 20:18:53', 0);
INSERT INTO `idm_resource_detail`(`domain_code`, `app_code`, `resource_code`, `resource_url`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`) VALUES ( 'SYSTEM_DEFAULT', 'PROMOTION', 'updateMarketingStatus', 'updateMarketingStatus', NULL, '2020-02-19 20:18:53', NULL, '2020-02-19 20:18:53', 0);
INSERT INTO `idm_resource_detail`(`domain_code`, `app_code`, `resource_code`, `resource_url`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`) VALUES ( 'SYSTEM_DEFAULT', 'PROMOTION', 'marketingExtend', 'marketingExtend', NULL, '2020-02-19 20:18:53', NULL, '2020-02-19 20:18:53', 0);

INSERT INTO `idm_resource`(`domain_code`, `app_code`, `resource_parent`, `resource_code`, `resource_name`, `resource_type`, `resource_class`, `access_type`, `status`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`, `position_code`) VALUES ( 'SYSTEM_DEFAULT', 'PROMOTION', 'root', 'queryTagList', 'queryTagList', 3, 1, 1, 1, NULL, '2020-02-19 20:17:57', NULL, '2020-02-19 20:17:57', 0, NULL);
INSERT INTO `idm_resource_detail`(`domain_code`, `app_code`, `resource_code`, `resource_url`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`) VALUES ( 'SYSTEM_DEFAULT', 'PROMOTION', 'queryTagList', 'queryTagList', NULL, '2020-02-19 20:18:53', NULL, '2020-02-19 20:18:53', 0);
INSERT INTO `idm_resource`(`domain_code`, `app_code`, `resource_parent`, `resource_code`, `resource_name`, `resource_type`, `resource_class`, `access_type`, `status`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`, `position_code`) VALUES ( 'SYSTEM_DEFAULT', 'PROMOTION', 'root', 'getMediaSignature', 'getMediaSignature', 3, 1, 1, 1, NULL, '2020-02-19 20:17:57', NULL, '2020-02-19 20:17:57', 0, NULL);
INSERT INTO `idm_resource_detail`(`domain_code`, `app_code`, `resource_code`, `resource_url`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`) VALUES ( 'SYSTEM_DEFAULT', 'PROMOTION', 'getMediaSignature', 'getMediaSignature', NULL, '2020-02-19 20:18:53', NULL, '2020-02-19 20:18:53', 0);

INSERT INTO `idm_resource`(`domain_code`, `app_code`, `resource_parent`, `resource_code`, `resource_name`, `resource_type`, `resource_class`, `access_type`, `status`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`, `position_code`) VALUES ( 'SYSTEM_DEFAULT', 'PROMOTION', 'root', 'queryTicketReleaseList', 'queryTicketReleaseList', 3, 1, 1, 1, NULL, '2020-02-19 20:17:57', NULL, '2020-02-19 20:17:57', 0, NULL);
INSERT INTO `idm_resource_detail`(`domain_code`, `app_code`, `resource_code`, `resource_url`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`) VALUES ( 'SYSTEM_DEFAULT', 'PROMOTION', 'queryTicketReleaseList', 'queryTicketReleaseList', NULL, '2020-02-19 20:18:53', NULL, '2020-02-19 20:18:53', 0);
INSERT INTO `idm_resource`(`domain_code`, `app_code`, `resource_parent`, `resource_code`, `resource_name`, `resource_type`, `resource_class`, `access_type`, `status`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`, `position_code`) VALUES ( 'SYSTEM_DEFAULT', 'PROMOTION', 'root', 'createTicket', 'createTicket', 3, 1, 1, 1, NULL, '2020-02-19 20:17:57', NULL, '2020-02-19 20:17:57', 0, NULL);
INSERT INTO `idm_resource_detail`(`domain_code`, `app_code`, `resource_code`, `resource_url`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`) VALUES ( 'SYSTEM_DEFAULT', 'PROMOTION', 'createTicket', 'createTicket', NULL, '2020-02-19 20:18:53', NULL, '2020-02-19 20:18:53', 0);
INSERT INTO `idm_resource`(`domain_code`, `app_code`, `resource_parent`, `resource_code`, `resource_name`, `resource_type`, `resource_class`, `access_type`, `status`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`, `position_code`) VALUES ( 'SYSTEM_DEFAULT', 'PROMOTION', 'root', 'sendTicket', 'sendTicket', 3, 1, 1, 1, NULL, '2020-02-19 20:17:57', NULL, '2020-02-19 20:17:57', 0, NULL);
INSERT INTO `idm_resource_detail`(`domain_code`, `app_code`, `resource_code`, `resource_url`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`) VALUES ( 'SYSTEM_DEFAULT', 'PROMOTION', 'sendTicket', 'sendTicket', NULL, '2020-02-19 20:18:53', NULL, '2020-02-19 20:18:53', 0);
