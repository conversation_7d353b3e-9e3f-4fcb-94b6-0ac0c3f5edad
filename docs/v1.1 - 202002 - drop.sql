-- -------------------------------------------------------------------------------------------------
-- -------------------------------------------------------------------------------------------------
-- 删除不用的字段
ALTER TABLE promo_activity DROP COLUMN ACTIVITY_RULE_DESC;
ALTER TABLE promo_activity DROP COLUMN OUTER_ACTIVITY_CODE;
ALTER TABLE promo_activity DROP COLUMN TEMPLATE_ID;

DROP INDEX uidx_promo_activity_channel ON promo_activity_channel;
ALTER TABLE promo_activity_channel DROP COLUMN ACTIVITY_ID;

DROP INDEX uidx_promo_activity_func_rank ON promo_activity_func_rank;
ALTER TABLE promo_activity_func_rank DROP COLUMN ACTIVITY_ID;

DROP INDEX uidx_promo_activity_gift ON promo_activity_gift;
ALTER TABLE promo_activity_gift DROP COLUMN ACTIVITY_ID;

ALTER TABLE promo_activity_gift_Coupon DROP COLUMN ACTIVITY_ID;
ALTER TABLE promo_activity_gift_coupon DROP COLUMN COUPON_ACTIVITY_ID;
ALTER TABLE promo_activity_gift_coupon DROP COLUMN COUPON_ACTIVITY_CODE;

DROP INDEX uidx_promo_activity_incentive ON promo_activity_incentive;
ALTER TABLE promo_activity_incentive DROP COLUMN ACTIVITY_ID;

ALTER TABLE promo_activity_language DROP COLUMN ACTIVITY_RULE_DESC;
ALTER TABLE promo_activity_language DROP COLUMN LANG_NAME;
ALTER TABLE promo_activity_language DROP COLUMN LOGIC_DELETE;
ALTER TABLE promo_activity_language DROP COLUMN UPDATE_USER;
ALTER TABLE promo_activity_language DROP COLUMN CREATE_USER;

ALTER TABLE promo_activity_member DROP COLUMN ACTIVITY_ID;

DROP INDEX uniq_promo_activity_product_detail ON promo_activity_product_detail;
ALTER TABLE promo_activity_product_detail DROP COLUMN ACTIVITY_ID;

DROP INDEX uniq_promo_activity_channel ON promo_activity_store;
ALTER TABLE promo_activity_store DROP COLUMN ACTIVITY_ID;

DROP INDEX uidx_promo_coupon_activity ON promo_coupon_activity;
ALTER TABLE promo_coupon_activity DROP COLUMN ACTIVITY_ID;

DROP INDEX idx_coupon_user_release_id ON promo_coupon_code_user;
ALTER TABLE promo_coupon_code_user DROP COLUMN ACTIVITY_ID;
ALTER TABLE promo_coupon_code_user DROP COLUMN RELEASE_ID;

DROP INDEX idx_inner_code_activity_id ON promo_coupon_inner_code;
DROP INDEX idx_release_id ON promo_coupon_inner_code;
DROP INDEX idx_promo_coupon_inner_code ON promo_coupon_inner_code;
DROP INDEX idx_inner_code_tenant_code ON promo_coupon_inner_code;
ALTER TABLE promo_coupon_inner_code DROP COLUMN ACTIVITY_ID;
ALTER TABLE promo_coupon_inner_code DROP COLUMN RELEASE_ID;

DROP INDEX idx_release_activity_id ON promo_coupon_release;
ALTER TABLE promo_coupon_release DROP COLUMN ACTIVITY_ID;
ALTER TABLE promo_coupon_release DROP COLUMN COUPON_ACTIVITY_ID;

DROP INDEX uidx_promo_incentive_limited ON promo_incentive_limited;
ALTER TABLE promo_incentive_limited DROP COLUMN ACTIVITY_ID;


