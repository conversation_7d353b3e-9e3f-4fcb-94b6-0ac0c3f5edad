<project xmlns="http://maven.apache.org/POM/4.0.0"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>


	<groupId>com.gtech.promotion</groupId>
	<artifactId>promotion-pom</artifactId>
	<packaging>pom</packaging>
	<version>${promotion.version}</version>
	<name>promotion</name>
	<url>http://www.gtech.asia</url>

	<modules>
		<module>promotion-common</module>
		<module>promotion-web</module>
		<module>promotion-service</module>
		<module>promotion-client</module>
	</modules>

	<properties>
		<promotion.version>2.0.4-SNAPSHOT</promotion.version>
		<project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
		<project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
		<profile.active>dev</profile.active>
		<java.version>1.8</java.version>
		<maven-compiler-plugin.version>3.8.1</maven-compiler-plugin.version>
		<maven-source-plugin.version>3.1.0</maven-source-plugin.version>
		<maven-assembly-plugin.version>3.1.1</maven-assembly-plugin.version>
		<docker-maven-plugin.version>1.2.0</docker-maven-plugin.version>
		<git-commit-id-plugin.version>3.0.0</git-commit-id-plugin.version>
		<maven-jar-plugin.version>3.1.1</maven-jar-plugin.version>

		<spring-cloud.version>2021.0.3</spring-cloud.version>
		<spring-boot.version>2.7.1</spring-boot.version>
		<apollo.version>1.4.0</apollo.version>
		<sharding-jdbc-spring.version>4.1.1</sharding-jdbc-spring.version>
		<mockito-all.version>1.10.19</mockito-all.version>
		<poi-ooxml.version>4.1.1</poi-ooxml.version>
		<commons-collections4.version>4.4</commons-collections4.version>
		<mybatis-spring-boot-starter.version>2.2.0</mybatis-spring-boot-starter.version>
		<mapper-spring-boot-starter.version>2.1.5</mapper-spring-boot-starter.version>
		<pagehelper-spring-boot-starter.version>1.4.3</pagehelper-spring-boot-starter.version>
		<hibernate-validator.version>6.0.17.Final</hibernate-validator.version>
		<springfox-swagger2.version>2.7.0</springfox-swagger2.version>
		<fastjson.version>1.2.83</fastjson.version>
		<cron-utils.version>9.1.3</cron-utils.version>
		<groovy-all.version>3.0.6</groovy-all.version>
		<druid.version>1.1.18</druid.version>
		<rocketmq-client.version>3.2.6</rocketmq-client.version>

		<gtech-commons.version>1.1.5-RELEASE</gtech-commons.version>
		<member-common.version>1.3.1-SNAPSHOT</member-common.version>
		<member-client.version>1.3.1-SNAPSHOT</member-client.version>
		<filecloud-api-feign.version>1.6.3-SNAPSHOT</filecloud-api-feign.version>
		<pim-client.version>0.0.2-SNAPSHOT</pim-client.version>
		<masterdata-client.version>0.0.1-SNAPSHOT</masterdata-client.version>
		<idm-common.version>1.2.1-SNAPSHOT</idm-common.version>

	</properties>


	<dependencyManagement>
		<dependencies>

			<dependency>
				<groupId>com.gtech.promotion</groupId>
				<artifactId>promotion-common</artifactId>
				<version>${promotion.version}</version>
			</dependency>

			<dependency>
				<groupId>com.gtech.promotion</groupId>
				<artifactId>promotion-service</artifactId>
				<version>${promotion.version}</version>
			</dependency>

			<dependency>
				<groupId>org.springframework.cloud</groupId>
				<artifactId>spring-cloud-dependencies</artifactId>
				<version>${spring-cloud.version}</version>
				<type>pom</type>
				<scope>import</scope>
			</dependency>

			<dependency>
				<groupId>org.springframework.boot</groupId>
				<artifactId>spring-boot-starter-parent</artifactId>
				<version>${spring-boot.version}</version>
				<type>pom</type>
				<scope>import</scope>
			</dependency>
			<dependency>
				<groupId>de.codecentric</groupId>
				<artifactId>spring-boot-admin-starter-client</artifactId>
				<version>${spring-boot.version}</version>
			</dependency>


			<dependency>
				<groupId>com.ctrip.framework.apollo</groupId>
				<artifactId>apollo-client</artifactId>
				<version>${apollo.version}</version>
			</dependency>

			<dependency>
				<groupId>org.apache.shardingsphere</groupId>
				<artifactId>sharding-jdbc-spring-boot-starter</artifactId>
				<version>${sharding-jdbc-spring.version}</version>
			</dependency>

			<dependency>
				<groupId>org.apache.shardingsphere</groupId>
				<artifactId>sharding-jdbc-spring-namespace</artifactId>
				<version>${sharding-jdbc-spring.version}</version>
			</dependency>
			<dependency>
				<groupId>org.mockito</groupId>
				<artifactId>mockito-all</artifactId>
				<version>${mockito-all.version}</version>
			</dependency>
			<dependency>
				<groupId>org.apache.poi</groupId>
				<artifactId>poi-ooxml</artifactId>
				<version>${poi-ooxml.version}</version>
			</dependency>

			<dependency>
				<groupId>org.apache.commons</groupId>
				<artifactId>commons-collections4</artifactId>
				<version>${commons-collections4.version}</version>
			</dependency>

			<dependency>
				<groupId>org.mybatis.spring.boot</groupId>
				<artifactId>mybatis-spring-boot-starter</artifactId>
				<version>${mybatis-spring-boot-starter.version}</version>
			</dependency>
			<dependency>
				<groupId>tk.mybatis</groupId>
				<artifactId>mapper-spring-boot-starter</artifactId>
				<version>${mapper-spring-boot-starter.version}</version>
				<exclusions>
					<exclusion>
						<artifactId>mybatis</artifactId>
						<groupId>org.mybatis</groupId>
					</exclusion>
					<exclusion>
						<artifactId>mybatis-spring</artifactId>
						<groupId>org.mybatis</groupId>
					</exclusion>
				</exclusions>
			</dependency>
			<dependency>
				<groupId>com.github.pagehelper</groupId>
				<artifactId>pagehelper-spring-boot-starter</artifactId>
				<version>${pagehelper-spring-boot-starter.version}</version>
			</dependency>

			<dependency>
				<groupId>org.hibernate</groupId>
				<artifactId>hibernate-validator</artifactId>
				<version>${hibernate-validator.version}</version>
			</dependency>
			<dependency>
				<groupId>io.springfox</groupId>
				<artifactId>springfox-swagger2</artifactId>
				<version>${springfox-swagger2.version}</version>
			</dependency>
			<dependency>
				<groupId>io.springfox</groupId>
				<artifactId>springfox-swagger-ui</artifactId>
				<version>${springfox-swagger2.version}</version>
			</dependency>

			<dependency>
				<groupId>com.alibaba</groupId>
				<artifactId>fastjson</artifactId>
				<version>${fastjson.version}</version>
			</dependency>
			<dependency>
				<groupId>com.cronutils</groupId>
				<artifactId>cron-utils</artifactId>
				<version>${cron-utils.version}</version>
			</dependency>

			<dependency>
				<groupId>org.codehaus.groovy</groupId>
				<artifactId>groovy-all</artifactId>
				<version>${groovy-all.version}</version>
				<type>pom</type>
				<exclusions>
					<exclusion>
						<groupId>org.codehaus.groovy</groupId>
						<artifactId>groovy-testng</artifactId>
					</exclusion>
				</exclusions>
			</dependency>

			<dependency>
				<groupId>com.alibaba</groupId>
				<artifactId>druid</artifactId>
				<version>${druid.version}</version>
			</dependency>


			<dependency>
				<groupId>com.alibaba.rocketmq</groupId>
				<artifactId>rocketmq-client</artifactId>
				<version>${rocketmq-client.version}</version>
			</dependency>

			<dependency>
				<groupId>com.gtech.commons</groupId>
				<artifactId>gtech-commons</artifactId>
				<version>${gtech-commons.version}</version>
			</dependency>
			<dependency>
				<groupId>com.gtech.commons</groupId>
				<artifactId>gtech-commons-basic</artifactId>
				<version>${gtech-commons.version}</version>
			</dependency>
			<dependency>
				<groupId>com.gtech.member</groupId>
				<artifactId>member-common</artifactId>
				<version>${member-common.version}</version>
			</dependency>
			<dependency>
				<groupId>com.gtech.member</groupId>
				<artifactId>member-client</artifactId>
				<version>${member-client.version}</version>
			</dependency>
			<dependency>
				<groupId>com.gtech.basic</groupId>
				<artifactId>filecloud-api-feign</artifactId>
				<version>${filecloud-api-feign.version}</version>
			</dependency>
			<dependency>
				<groupId>com.gtech</groupId>
				<artifactId>pim-client</artifactId>
				<version>${pim-client.version}</version>
			</dependency>
			<dependency>
				<groupId>com.gtech.basic</groupId>
				<artifactId>masterdata-client</artifactId>
				<version>${masterdata-client.version}</version>
			</dependency>
			<dependency>
				<groupId>com.gtech.basic</groupId>
				<artifactId>idm-common</artifactId>
				<version>${idm-common.version}</version>
				<exclusions>
					<exclusion>
						<groupId>com.gtech.commons</groupId>
						<artifactId>gtech-commons-basic</artifactId>
					</exclusion>
					<exclusion>
						<groupId>org.apache.commons</groupId>
						<artifactId>commons-lang3</artifactId>
					</exclusion>
					<exclusion>
						<groupId>org.apache.commons</groupId>
						<artifactId>commons-collections4</artifactId>
					</exclusion>
					<exclusion>
						<groupId>com.alibaba</groupId>
						<artifactId>fastjson</artifactId>
					</exclusion>
					<exclusion>
						<groupId>org.projectlombok</groupId>
						<artifactId>lombok</artifactId>
					</exclusion>
				</exclusions>
			</dependency>
		</dependencies>
	</dependencyManagement>
	<distributionManagement>
		<repository>
			<id>maven-releases</id>
			<url>https://nexus.gtech.asia/repository/maven-releases/</url>
		</repository>
		<snapshotRepository>
			<id>maven-snapshots</id>
			<url>https://nexus.gtech.asia/repository/maven-snapshots/</url>
		</snapshotRepository>
	</distributionManagement>
	<profiles>
		<profile>
			<id>dev</id>
			<properties>
				<profile.active>dev</profile.active>
			</properties>
			<activation>
				<activeByDefault>true</activeByDefault>
			</activation>
		</profile>

	</profiles>
	<build>
		<resources>
			<resource>
				<directory>src/main/resources</directory>
				<filtering>true</filtering>
				<includes>
					<include>application.yml</include>
					<include>*.properties</include>
				</includes>
			</resource>
			<resource>
				<directory>src/main/resources</directory>
				<filtering>false</filtering>
				<excludes>
					<exclude>application.yml</exclude>
					<exclude>*.properties</exclude>
				</excludes>
			</resource>
		</resources>
		<plugins>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-compiler-plugin</artifactId>
				<version>${maven-compiler-plugin.version}</version>
				<configuration>
					<source>${java.version}</source>
					<target>${java.version}</target>
				</configuration>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-source-plugin</artifactId>
				<version>${maven-source-plugin.version}</version>
				<executions>
					<execution>
						<id>attach-sources</id>
						<goals>
							<goal>jar</goal>
						</goals>
					</execution>
				</executions>
			</plugin>
		</plugins>
	</build>

</project>
